import App from './App'

// #ifndef VUE3
import Vue from 'vue'
Vue.config.productionTip = false

import xwy_api from './utils/api/xwy_api.js'
Vue.prototype.xwy_api = xwy_api
import utils from './utils/utils.js'
Vue.prototype._utils = utils
import $uni from './utils/uni-api.js'
Vue.prototype.$uni = $uni
import $wx from './utils/wx-api.js'
Vue.prototype.$wx = $wx
import xwy_config from './config/config.js'
Vue.prototype.xwy_config = xwy_config
import login from './utils/api/login.js'
Vue.prototype.$login = login

// 利口答题全区引入
import xwyLib from "./pages/likou_dati/common/js/public.js"
import base64 from "./pages/likou_dati/common/js/base64.js"
import imgImport from "./pages/likou_dati/common/js/imgImport.js"

Vue.prototype.xwyLib = xwyLib
Vue.prototype.base64 = base64
Vue.prototype.imgImport = imgImport

App.mpType = 'app'
const app = new Vue({
    ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif

import pickerTime from './components/picker-time.vue'
Vue.component('picker-time', pickerTime)
