<template>
    <view v-if="show" class="loading-ani">
        <text
            v-for="(item, index) in colorList"
            :key="index"
            :style="{
                background: item,
                width: width,
                height: width,
                margin: '0 ' + margin,
                animation: `loading ${time}s infinite alternate`,
                animationDelay: delay * index + 's'
            }"
        ></text>
    </view>
</template>

<script>
export default {
    name: "loadAni",
    props: {
        show: {
            default: true,
        },
        colorList: { // 点的颜色，需要多少个点就放多少种颜色
            default: ['#007DB6', '#008FB2', '#009B9E']
        },
        width: { // 点的直径
            type: String,
            default: '10px'
        },
        margin: { // 点的左右间距
            type: String,
            default: '3px'
        },
        time: { // 每个点的动画时间(秒)
            type: Number,
            default: 0.6
        },
        delay: { // 每个点之间动画的延迟(秒),第一个点不用延迟，立即执行。如: 0.3 第二个点延迟0.3秒执行，第三个点延迟0.6秒执行...
            type: Number,
            default: 0.3
        }
    },
    data() {
        return {
            display: false
        }
    },
    mounted() {
        this.display = this.show
    },
    methods: {
        open() {
            this.display = true
        },
        close() {
            this.display = false
        }
    }
}
</script>

<style>
.loading-ani text {
    display: inline-block;
    vertical-align: middle;
    border-radius: 50%;
}

@keyframes loading {
    0% {
        opacity: 0;
        transform: scale(1);
    }
    100% {
        opacity: 1;
        transform: scale(1.2);
    }
}
</style>
