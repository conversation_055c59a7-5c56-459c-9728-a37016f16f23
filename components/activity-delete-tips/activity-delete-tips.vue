<template>
    <view class="activity-delete-tips text-center">
        <view @click="copyId">
            <uni-icons type="info" size="100" color="#ff9900"/>
            <view class="color-content">{{ error }}</view>
<!--            <view v-if="activeId" class="color-sub font14">活动ID: {{ activeId }}</view>-->
        </view>
        
        <view class="flex-all-center pt15">
            <navigator class="p5 color-sub font12" url="/pages/user/user">个人中心</navigator>
        </view>
    </view>
</template>

<script>
export default {
    name: "activity-delete-tips",
    props: {
        activeId: {
            type: String,
            default: ''
        },
        error: {
            type: String,
            default: '活动已结束或已删除'
        }
    },
    methods: {
        copyId() {
            this.activeId && this.$uni.setClipboardData(this.activeId)
        }
    }
}
</script>

<style lang="scss">
.activity-delete-tips {
    width: 100vw;
    height: 100vh;
    position: fixed;
    z-index: 99999;
}
</style>
