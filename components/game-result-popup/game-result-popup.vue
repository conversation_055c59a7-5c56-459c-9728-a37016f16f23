<template>
    <view>
        <uni-popup ref="resultPopup" type="top" :is-mask-click="false"
                   mask-background-color="rgba(0,0,0,0.8)">

            <view class="text-center" style="padding-top: 10vh;">

                <image
                    :src="'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/guessing_songs/' + (resultCode === 1 ? 'jbb.png': 'jblk.png')"
                    mode="widthFix"
                    style="width: 600rpx;"
                />
                <view v-if="rewardIntegral" class="color-warning p5 flex-all-center">
                    <view>{{ unit }} +</view>
                    <view class="font24">{{ rewardIntegral }}</view>
                </view>
                <view class="color-white p5">{{ resultInfo }}</view>

                <navigator class="back-button" open-type="navigateBack">返回</navigator>
            </view>


            <view v-if="showAd" class="flex-all-center">
                <xwy-ad :ad_type="66"></xwy-ad>
                <xwy-ad v-if="isOpen" :ad_type="3"></xwy-ad>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "game-result-popup",
    props: {
        showAd: {
            type: Boolean,
            default: true
        },
        unit: {
            type: String,
            default: '积分'
        }
    },
    data() {
        return {
            isOpen: false,
            resultCode: null,
            rewardIntegral: null,
            resultInfo: null
        }
    },

    methods: {
        open(res) {
            this.open = true
            this.resultCode = res.code
            this.rewardIntegral = res.integral
            this.resultInfo = res.info
            this.$refs.resultPopup.open()
        }
    }
}
</script>

<style lang="scss" scoped>
.back-button {
    width: 253px;
    height: 53px;
    line-height: 48px;
    font-size: 18px;
    color: #fff;
    text-align: center;
    background-image: url(https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/guessing_songs/anc.png);
    background-size: 100%;
    background-repeat: no-repeat;
    margin: 10px auto 0;
}
</style>