<template>
    <view>
        <template v-if="!maxStep || maxStep === 0">
            <view class="step-bar-chart flex-kai" :style="{height: `calc(${MaxHeight} + 40px)`}">
                <view class="step-item font12" v-for="item in step_list" :key="item.timestamp"
                      :style="{width: `calc(100% / ${step_list.length})`}">
                    <view class="color-content">{{ item.step }}</view>
                    <view class="bar-item" style="max-height: 10px;" 
                          :style="{backgroundColor: color}"></view>
                    <view class="line"></view>
                    <view class="day color-sub">{{ getDay(item.timestamp) }}</view>
                </view>
            </view>
        </template>
        <template v-else>
            <view class="step-bar-chart flex-kai" :style="{height: `calc(${MaxHeight} + 40px)`}">
                <view class="step-item font12" v-for="item in step_list" :key="item.timestamp"
                      :style="{width: `calc(100% / ${step_list.length})`}">
                    <view class="color-content">{{ item.step }}</view>
                    <view class="bar-item bar-rise-up-" :style="item.barStyle"></view>
                    <view class="line"></view>
                    <view class="day color-sub">{{ getDay(item.timestamp) }}</view>
                </view>
            </view>
        </template>
    </view>
</template>

<script>
const defaultDayStep = (day) => {
    const today = new Date().setHours(0, 0, 0, 0) / 1000
    return [...Array(day)].map((_, i) => {
        const timestamp = today - i * 24 * 60 * 60;
        return {timestamp, step: 0};
    }).reverse()
}

export default {
    name: "step-bar-chart",
    props: {
        stepList: {
            type: Array,
            default: () => []
        },
        color: {
            type: String,
            default: '#19be6b'
        },
        targetStep: {
            type: Number,
            default: 10000
        },
        targetColor: {
            type: String,
            default: '#ff9900'
        },
        maxHeight: {
            type: Number,
            default: 150
        }
    },
    data() {
        return {}
    },
    computed: {
        step_list() {
            const stepList = this.stepList
            if (!this.stepList?.length) return defaultDayStep(7)
            if (this.maxStep === 0) return defaultDayStep(this.stepList?.length || 7)
            stepList.forEach(v => {
                const color = this.targetStep && v.step >= this.targetStep ? this.targetColor : this.color
                const height = `calc(${v.step / this.maxStep} * ${this.maxHeight}px)`
                v.barStyle = `
                    background: linear-gradient(180deg, ${color}80, ${color}ff 100%);
                    height: ${height};
                    max-height: ${height};
                `
            })
            return stepList
        },
        maxStep() {
            if (!this.stepList.length) return 0
            const max = Math.max(...this.stepList.map(item => item.step))
            if (isNaN(max)) return 0
            return max
        },
        MaxHeight() {
            return this.maxHeight + 'px'
        }
    },

    mounted() {

    },

    methods: {
        getDay(timestamp) {
            const date = new Date(timestamp * 1000)
            return `${date.getMonth() + 1}/${date.getDate()}`
        }
    }
}
</script>

<style lang="scss">
.step-bar-chart {
    .step-item {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: center;
        height: 100%;
        .bar-item {
            width: 20px;
            margin-top: 5px;
            min-height: 10px;
            border-radius: 10px 10px 0 0;
            height: 10px;
        }
        .bar-rise-up- {
            animation: rise 1s ease forwards;
        }
        @keyframes rise {
            0% {
                height: 10px;
            }
            100% {
                height: 100%;
            }
        }
        
        .line {
            width: 100%;
            height: 1px;
            background-color: #eee;
        }
    }
}
</style>