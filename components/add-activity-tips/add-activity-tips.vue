<template>
    <view>
        <view v-if="tips_description" class="tips-description">{{ tips_description }}</view>
    </view>
</template>

<script>

export default {
    name: "add-activity-tips",
    data() {
        return {
            tips_description: ''
        }
    },
    methods: {
        open(types) {
            this.types = Number(types)
            this.getConfig()
        },
        async getConfig() {
            const conf_set = await this.xwy_api.getActivityTypeConfSet()
            const conf_list = conf_set.active_goods_list
            const this_type = conf_list.find(item => Number(item.types) === this.types)
            if (this_type['free_tips']) {
                this.xwy_api.alert(this_type['free_tips'], { confirmText: '知道了' })
            }
            if (this_type.tips_description) {
                this.tips_description = this_type.tips_description
            }
        }
    }
}
</script>

<style lang="scss">
.tips-description {
    color: #e19898;
    font-size: 12px;
    padding: 10px;
}
</style>
