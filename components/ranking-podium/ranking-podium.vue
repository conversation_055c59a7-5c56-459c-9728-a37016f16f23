<template>
    <view class="podium">
        <view v-for="(item, index) in list" :class="'podium-item bg-white podium-' + (index + 1)" :key="item.id">
            <view class="headimg-view">
                <view v-if="item.headimg">
                    <image class="headimg" mode="aspectFill" :src="item.headimg"/>

                    <view class="top-icon">
                        <text :class="'iconfont icon-crown-' + (index + 1)"></text>
                    </view>
                </view>

                <view v-else class="headimg bg-background flex-all-center">
                    <text :class="'iconfont font34 icon-crown-' + (index + 1)"></text>
                </view>
            </view>
            <view class="color-title font14"
                  :class="activeId === '98a78b836d8c42bba8cba6d1335ce62b' ? 'ellipsis--l2' : 'ellipsis'">
                {{ item.nickname }}
            </view>
            <view v-if="item.value" class="font12 color-red">{{ item.value }}</view>
        </view>
    </view>
</template>

<script>
export default {
    name: "ranking-podium",
    props: {
        list: {
            type: Array,
            default: () => []
        },
        activeId: {
            type: String,
            default: ""
        }
    }
}
</script>

<style lang="scss" scoped>
.podium {
    position: relative;
    height: 160px;
    margin: 10px;

    $item-width: calc((100vw - 20px) / 3);

    .podium-item {
        position: absolute;
        bottom: 0;
        width: $item-width;
        text-align: center;
        padding: 10px;
        box-sizing: border-box;

        .headimg-view {
            position: relative;
            padding-top: 10px;
            margin: 0 auto;
        }

        .top-icon {
            position: absolute;
        }
    }

    .podium-1 {
        left: $item-width;
        height: 170px;
        border-radius: 10px 10px 0 0;
        border-left: 1px solid #f8f8f8;
        border-right: 1px solid #f8f8f8;

        .headimg-view {
            width: 66px;
            height: 66px;

            .headimg {
                width: 62px;
                height: 62px;
                border: 2px solid #fbd561;
            }

            .iconfont {
                color: #fbd561;
                font-size: 28px;
            }
        }

        .top-icon {
            right: -12px;
            top: -8px;
            transform: rotate(37deg);
        }
    }

    .podium-2 {
        left: 0;
        height: 145px;
        border-radius: 10px 0 0 0;
        border-right: 1px solid #f8f8f8;

        .headimg-view {
            width: 64px;
            height: 64px;

            .headimg {
                width: 60px;
                height: 60px;
                border: 2px solid #b3c8d9;
            }

            .iconfont {
                color: #b3c8d9;
                font-size: 26px;
            }
        }

        .top-icon {
            right: -12px;
            top: -6px;
            transform: rotate(40deg);
        }
    }

    .podium-3 {
        right: 0;
        height: 140px;
        border-left: 1px solid #f8f8f8;
        border-radius: 0 10px 0 0;

        .headimg-view {
            width: 62px;
            height: 62px;

            .headimg {
                width: 58px;
                height: 58px;
                min-width: 58px;
                border: 2px solid #eec68d;
            }

            .iconfont {
                color: #eec68d;
                font-size: 24px;
            }
        }

        .top-icon {
            right: -10px;
            top: -3px;
            transform: rotate(45deg);
        }
    }
}

.headimg {
    border-radius: 50%;
}
</style>