<template>
    <view>
        <uni-popup ref="nfcWrite" type="bottom" :is-mask-click="false" :safe-area="false"
                   @touchmove.prevent.stop="">
            <view class="nfc-write-popup bg-white text-center">
                <view class="color-title font24">NFC写入</view>
                <view class="color-content pt10">
                    <template v-if="writeStatus === 'writing'">请将NFC芯片靠近手机NFC识别区域</template>
                    <template v-else-if="writeStatus === 'success'">写入成功</template>
                </view>
                <view class="icon-container flex-all-center">
                    <view v-if="writeStatus === 'writing'" class="nfc-read-icon-box">
                        <text class="iconfont icon-nfc-read-write color-primary"></text>
                    </view>
                    <view v-else-if="writeStatus === 'success'" class="nfc-read-success-box">
                        <uni-icons type="checkbox" color="#2d8cf0" size="150px"/>
                    </view>
                </view>
                <view class="cancel-button" @click="cancelNFCOperation">取消</view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "nfc-write-popup",
    props: ['activeId'],
    data() {
        return {
            writeStatus: null
        }
    },

    methods: {
        cancelNFCOperation() {
            this.stopNFCDiscovery()
            this.$refs.nfcWrite.close()
        },

        nfcErrorTips(code) {
            return {
                '13000': '设备不支持NFC',
                '13010': '未知错误',
                '13019': '用户未授权',
                '13011': '参数无效',
                '13012': '将参数解析为NdefMessage失败',
                '13021': '已经开始NFC扫描',
                '13018': '尝试在未开始NFC扫描时停止NFC扫描',
                '13022': '标签已经连接',
                '13023': '尝试在未连接标签时断开连接',
                '13013': '未扫描到NFC标签',
                '13014': '无效的标签技术',
                '13015': '从标签上获取对应技术失败',
                '13024': '当前标签技术不支持该功能',
                '13017': '相关读写操作失败',
                '13016': '连接失败'
            }[code] || null
        },

        async write(nfcAdapter, writeData) {
            this.nfcAdapter = nfcAdapter
            this.writeData = writeData

            // 重置状态，避免再次写入的时候状态是"writing"或者"success"
            this.writeStatus = null

            if (!await this.checkAvailability()) return

            this.nfcAdapter.offDiscovered() // 先移除之前的监听
            this.nfcAdapter.onDiscovered(this.handleNFCDiscovered)

            // 开始扫描NFC
            this.nfcAdapter.startDiscovery({
                success: () => {
                    console.log('开始扫描NFC')
                    this.writeStatus = 'writing'
                    this.$refs.nfcWrite.open()
                },
                fail: err => {
                    console.error('扫描NFC失败', err)
                    let info = this.nfcErrorTips(err.errCode) || 'NFC启用失败'
                    if (err.errMsg === 'startDiscovery:fail current platform is not supported') {
                        info = '该设备无法使用小程序NFC功能，请使用安卓手机或其他支持小程序NFC的设备。'
                    }
                    this.$uni.showModal(info)
                    this.stopNFCDiscovery()
                }
            })
        },

        handleNFCDiscovered(res) {
            console.log('发现NFC设备', res)

            if (this.writeStatus === 'success') return

            try {
                // 获取NDEF类型设备
                const ndef = this.nfcAdapter.getNdef()

                // 尝试先关闭可能存在的连接
                this.ndefClose(ndef)

                if (this.writeStatus === 'success') return

                // 连接NFC设备
                ndef.connect({
                    success: () => {
                        console.log('NFC连接成功')

                        if (this.writeStatus === 'success') return

                        // 使用NDEF专用的写入方法，写入URL类型记录
                        ndef.writeNdefMessage({
                            // records: [this.createUrlRecord(this.writeData)],
                            records: this.createNfcDataRecord(this.writeData),
                            success: res => {
                                console.log('写入成功', res)
                                this.writeStatus = 'success'
                                this.ndefCloseAndStopDiscovery(ndef)
                                setTimeout(() => this.$refs.nfcWrite.close(), 1000)
                            },
                            fail: err => {
                                console.error('NFC写入失败', err)
                                setTimeout(() => {
                                    if (this.writeStatus === 'success') return
                                    this.ndefCloseAndStopDiscovery(ndef)
                                    this.$refs.nfcWrite.close()
                                    const info = this.nfcErrorTips(err.errCode) || JSON.stringify(err)
                                    this.$uni.showModal(info, {title: '写入失败'})
                                }, 1000)
                            }
                        })
                    },
                    fail: err => {
                        console.error('NFC连接失败', err)
                        this.$refs.nfcWrite.close()
                        const info = this.nfcErrorTips(err.errCode) || JSON.stringify(err)
                        this.$uni.showModal(info, {title: 'NFC连接失败'})
                        this.stopNFCDiscovery()
                    }
                })
            } catch (error) {
                console.error('处理NFC设备时出错', error)
                this.stopNFCDiscovery()
                this.$refs.nfcWrite.close()
                this.$uni.showModal('处理NFC设备时出错: ' + error.message)
            }
        },

        ndefCloseAndStopDiscovery(ndef) {
            if (this.nfcAdapter) {
                this.ndefClose(ndef)
                this.stopNFCDiscovery()
            }
        },

        ndefClose(ndef) {
            if (this.nfcAdapter) {
                try {
                    ndef.close()
                } catch (e) {
                    console.log('关闭连接出错', e)
                }
            }
        },

        stopNFCDiscovery() {
            if (this.nfcAdapter) this.nfcAdapter.stopDiscovery()
        },

        async checkAvailability() {
            let isWechat = false
            // #ifdef MP-WEIXIN
            isWechat = true
            // #endif
            if (!isWechat) {
                await this.$uni.showModal('请在安卓微信小程序使用')
                return false
            }

            /*const osName = (uni.getSystemInfoSync().osName || '').toLowerCase()
            if (osName !== 'android') {
                await this.$uni.showModal('请在安卓微信小程序使用')
                return false
            }*/

            if (!this.nfcAdapter) {
                await this.$uni.showModal('NFC不可用')
                return false
            }

            return true
        },

        str2ab(text, extraBytes) {
            const uriStr = encodeURIComponent(text)
            const bytes = []
            for (let i = 0; i < uriStr.length; i++) {
                const code = uriStr.charAt(i)
                if (code === '%') {
                    const hex = uriStr.slice(i + 1, i + 3)
                    const hexVal = parseInt(hex, 16)
                    bytes.push(hexVal)
                    i += 2
                } else {
                    bytes.push(code.charCodeAt(0))
                }
            }
            if (extraBytes) {
                bytes.unshift(...extraBytes)
            }
            return new Uint8Array(bytes).buffer
        },

        createNfcDataRecord(URLScheme) {
            return [
                {
                    id: this.str2ab('mini-ios'),
                    tnf: 1,
                    type: this.str2ab('U'),
                    payload: this.str2ab(URLScheme, [0])
                },
                {
                    id: this.str2ab('mini-android'),
                    tnf: 4,
                    type: this.str2ab('android.com:pkg'),
                    payload: this.str2ab('com.tencent.mm')
                }
            ]
        },


        /**
         * 创建URL类型的NDEF记录
         * @param {String} url URL地址
         * @returns {Object} NDEF记录对象
         */
        createUrlRecord(url) {
            // URL记录的类型标识为'U'
            const typeBuffer = new Uint8Array([0x55]); // 'U'的ASCII码

            // URL记录的有效载荷格式: [URL前缀代码, URL内容(不含前缀)]
            // URL前缀代码: 0x00 = "http://www.", 0x01 = "https://www.", 0x02 = "http://", 0x03 = "https://" 等

            let prefixCode = 0x03; // 默认使用"https://"
            let urlWithoutPrefix = url;

            if (url.startsWith('http://www.')) {
                prefixCode = 0x00;
                urlWithoutPrefix = url.substring(11);
            } else if (url.startsWith('https://www.')) {
                prefixCode = 0x01;
                urlWithoutPrefix = url.substring(12);
            } else if (url.startsWith('http://')) {
                prefixCode = 0x02;
                urlWithoutPrefix = url.substring(7);
            } else if (url.startsWith('https://')) {
                prefixCode = 0x03;
                urlWithoutPrefix = url.substring(8);
            }

            // 创建URL记录的有效载荷
            // 手动将URL转换为UTF-8编码的字节数组（替代TextEncoder）
            const urlBytes = this.stringToUTF8Array(urlWithoutPrefix);
            const payload = new Uint8Array(1 + urlBytes.length);
            payload[0] = prefixCode;

            // 复制URL内容到payload
            for (let i = 0; i < urlBytes.length; i++) {
                payload[i + 1] = urlBytes[i];
            }

            return {
                id: new ArrayBuffer(0),
                type: typeBuffer.buffer,
                payload: payload.buffer,
                tnf: 1 // NFC Forum Well Known Type
            };
        },

        /**
         * 将字符串转换为UTF-8编码的字节数组
         * @param {String} str 要转换的字符串
         * @returns {Uint8Array} UTF-8编码的字节数组
         */
        stringToUTF8Array(str) {
            // 计算UTF-8编码后的长度
            let encodedLength = 0;
            for (let i = 0; i < str.length; i++) {
                const charCode = str.charCodeAt(i);
                if (charCode < 0x80) {
                    encodedLength += 1;
                } else if (charCode < 0x800) {
                    encodedLength += 2;
                } else if (charCode < 0x10000) {
                    encodedLength += 3;
                } else {
                    encodedLength += 4;
                }
            }

            // 创建UTF-8编码的字节数组
            const bytes = new Uint8Array(encodedLength);
            let offset = 0;

            for (let i = 0; i < str.length; i++) {
                const charCode = str.charCodeAt(i);
                if (charCode < 0x80) {
                    // 1字节 (0xxxxxxx)
                    bytes[offset++] = charCode;
                } else if (charCode < 0x800) {
                    // 2字节 (110xxxxx 10xxxxxx)
                    bytes[offset++] = 0xc0 | (charCode >> 6);
                    bytes[offset++] = 0x80 | (charCode & 0x3f);
                } else if (charCode < 0x10000) {
                    // 3字节 (1110xxxx 10xxxxxx 10xxxxxx)
                    bytes[offset++] = 0xe0 | (charCode >> 12);
                    bytes[offset++] = 0x80 | ((charCode >> 6) & 0x3f);
                    bytes[offset++] = 0x80 | (charCode & 0x3f);
                } else {
                    // 4字节 (11110xxx 10xxxxxx 10xxxxxx 10xxxxxx)
                    bytes[offset++] = 0xf0 | (charCode >> 18);
                    bytes[offset++] = 0x80 | ((charCode >> 12) & 0x3f);
                    bytes[offset++] = 0x80 | ((charCode >> 6) & 0x3f);
                    bytes[offset++] = 0x80 | (charCode & 0x3f);
                }
            }

            return bytes;
        },
    }
}
</script>

<style lang="scss">
.nfc-write-popup {
    border-radius: 10px 10px 0 0;
    padding: 20px 10px;
    box-sizing: border-box;
    width: 100vw;

    .icon-container {
        height: 200px;

        .nfc-read-icon-box {
            animation: nfc-box-pulse 2s infinite ease-in-out;
            display: inline-block; // 确保动画效果可见

            @keyframes nfc-box-pulse {
                0% {
                    transform: scale(0.9);
                    opacity: .8;
                }
                50% {
                    transform: scale(1.1);
                    opacity: 1;
                }
                100% {
                    transform: scale(0.9);
                    opacity: .8;
                }
            }

            .iconfont {
                font-size: 150px;
            }
        }

        .nfc-read-success-box {
            animation: success-zoom 0.5s ease-out forwards;
            display: inline-block;

            @keyframes success-zoom {
                0% {
                    transform: scale(0.2);
                    opacity: 0;
                }
                70% {
                    transform: scale(1.1);
                    opacity: 1;
                }
                100% {
                    transform: scale(1);
                    opacity: 1;
                }
            }
        }
    }

    .cancel-button {
        margin: 0 20px;
        background-color: #F3F2F8;
        color: #161618;
        line-height: 40px;
        border-radius: 5px;
    }
}
</style>