<template>
    <view>
        <view class="search">
            <view class="search-input">
                <view class="search-input-icon">
                    <uni-icons type="search" color="#999999"/>
                </view>
                <view class="search-input-text">
                    <input
                        type="text"
                        confirm-type="search"
                        :placeholder="placeholder"
                        @input="$emit('update:search_keyword', $event.target.value)"
                        v-model="key_word"
                        @confirm="$emit('search')"
                    />
                </view>
                <view class="search-input-icon" @click="clearKeyword">
                    <uni-icons v-if="key_word" type="clear" color="#999999"/>
                </view>
            </view>
            <view class="search-btn" hover-class="navigator-hover" @click="$emit('search')">
                <text>搜索</text>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "ranking-search",
    props: {
        search_keyword: {
            type: String,
            default: ''
        },
        placeholder: {
            type: String,
            default: '搜索'
        }
    },
    watch: {
        search_keyword(val) {
            this.key_word = val
        }
    },

    data() {
        return {
            key_word: ''
        }
    },



    methods: {
        clearKeyword() {
            this.key_word = ''
            this.$emit('update:search_keyword', '')
            this.$emit('search')
        }
    }
}
</script>

<style lang="scss">
.search {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    height: 40px;
    background-color: #fff;
    .search-input {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        width: calc(100% - 90px);
        margin-right: 10px;
        height: 40px;
        background-color: #f5f5f5;
        border-radius: 20px;
        .search-input-icon {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 40px;
        }
        .search-input-text {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: flex-start;
            width: 100%;
            height: 40px;
            input {
                width: 100%;
                height: 100%;
                font-size: 14px;
                color: #333;
                background-color: #f5f5f5;
                border: none;
                outline: none;
            }
        }
    }
    .search-btn {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        width: 80px;
        height: 40px;
        text-align: center;
        border-radius: 20px;
        background-color: #2d8cf0;
        text {
            font-size: 14px;
            color: #fff;
        }
    }
}
</style>
