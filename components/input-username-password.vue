<template>
    <view>
        <uni-popup ref="input_username_password" @touchmove.stop.prevent="" :is-mask-click="false">
            <view class="bg-white" style="width: 280px; border-radius: 10px;">
                <view class="text-center color-title p10" style="border-bottom: 1px solid #eee;">
                    进入活动
                </view>
                <view class="p10">
                    <uni-easyinput v-model="username" :placeholder="Labels.username"/>
                    <view class="pt5"></view>
                    <uni-easyinput v-model="password" :placeholder="Labels.password"/>
                </view>

                <view class="flex-row text-center" style="border-top: 1px solid #eee;">
                    <!-- <view class="color-sub pt10 pb10" style="width: 50%;" hover-class="navigator-hover" @click="uniPopupClose('input_username_password')">取消</view> -->
                    <view class="color-primary pt10 pb10 w-100" hover-class="navigator-hover"
                          @click="checkUsernamePassword">确定
                    </view>
                </view>

                <view v-if="showDirectEntryButton" class="p10 text-center"
                      @click="$refs.input_username_password.close()">进入活动
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()

export default {
    name: "input-username-password",
    props: {
        labels: {
            type: Object,
            default: () => ({
                username: '请输入账号',
                password: '请输入密码'
            })
        }
    },
    data() {
        return {
            id: '',
            isAdmin: false,
            username: '',
            password: ''
        };
    },

    computed: {
        Labels() {
            if (this.id === '5ff3fe62e0712a058d92be687db350db') return {username: '姓名', password: '工号'}

            return this.labels || {username: '请输入账号', password: '请输入密码'}
        },

        showDirectEntryButton() {
            // 管理员可以不报名直接进入活动
            if (this.isAdmin) return true

            // 288体验版可以不报名直接进入活动
            return app.globalData.who === 288 && app.globalData.evn_version !== 'release'
        }
    },

    methods: {
        open(data) {
            this.detail = data.detail
            this.id = data.detail.active_id
            this.isAdmin = app.globalData.userid === data.detail.userid
            this.$refs.input_username_password.open()
            if (data.success) this.callback = data.success
        },

        async checkUsernamePassword() {
            const username = this.username
            const password = this.password
            if (!username) {
                if (this.detail.rank_set?.shield_other) {
                    return this.$uni.showModal(this.Labels.username, {
                        showCancel: true,
                        confirmText: '重新输入',
                        cancelText: '个人中心',
                        success: res => {
                            if (!res.confirm) this.$uni.navigateTo('/pages/user/user')
                        }
                    })
                }

                return this.$uni.showToast(this.Labels.username)
            }

            if (!password) return this.$uni.showToast(this.Labels.password)

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.adminUser/check_acitve_username',
                data: {
                    active_id: this.id,
                    username,
                    password
                }
            })
            uni.hideLoading()

            if (!res?.data?.user_details) return this.$uni.showModal(res?.info || '验证失败')


            await this.usernamePasswordJoin(res.data.user_details)
        },

        async usernamePasswordJoin(user_details) {
            let must_submit = JSON.stringify(user_details.conf_json)
            must_submit = must_submit.replace(/·/g, '-')
            const data = {
                active_id: this.id,
                must_submit: this._utils.base64['encode'](must_submit)
            }
            if (user_details.logs_types) data.team_id = user_details.logs_types

            this.$uni.showLoading('报名中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.user/submit_attend_active',
                data
            })
            uni.hideLoading()

            this.$refs.input_username_password.close()
            if (!res?.status) return this.$uni.showToast(res?.info || '报名失败')

            this.$uni.showToast('报名成功', 'success')

            this.callback && this.callback()
        },
    }
}
</script>

<style>

</style>
