<template>
    <view>
        <uni-popup ref="popup" type="center" @touchmove.stop.prevent="" :is-mask-click="false">
            <view v-if="show" class="popup-container bg-white">
                <view class="news-title text-center color-title p10">{{ title }}</view>
                <scroll-view scroll-y="true" class="content-container color-content">
                    <u-parse :content="content"/>
                </scroll-view>

                <view class="flex-row pt10 pl5" style="border-top: 1px solid #eee;">
                    <view @click="agreeChange">

                        <radio class="agree-radio" :checked="agree"/>
                    </view>
                    <view class="font14 color-sub">我已阅读并同意《{{ title }}》。</view>
                </view>
                <view class="flex-all-center pb10">
                    <view :class="['confirm-button', agree ? 'bg-primary' : 'bg-disabled']" @click="confirm">
                        {{ set.confirm_text }}
                    </view>
                </view>
                <view v-if="showUserCenter" class="flex-all-center pb10">
                    <navigator class="color-sub font14 pl10 pr10" url="/pages/user/user">个人中心</navigator>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "activity-notice-popup",
    props: ['set', 'showUserCenter'],
    data() {
        return {
            show: false,
            content: '',
            agree: false,
            title: ''
        }
    },

    methods: {
        async open() {
            const {open, news_id} = this.set || {}
            if (!open || !news_id) return

            await this.getNews()
            this.show = true
            this.$nextTick(() => this.$refs.popup.open())
        },

        async getNews() {
            this.$uni.showLoading('加载中...')
            const news = await this.xwy_api.getNewsDetail(this.set.news_id)
            uni.hideLoading()
            this.content = news.content || ''
            this.title = news.title || '活动须知'
        },

        agreeChange() {
            this.agree = !this.agree
        },

        confirm() {
            if (!this.agree) return this.$uni.showModal(`请勾选 我已阅读并同意《${this.title}》`)
            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.popup-container {
    width: 90vw;
    border-radius: 10px;
}
.news-title {
    border-bottom: 1px solid #eee;
}
.content-container {
    width: calc(100% - 20px);
    height: calc(85vh - 170px);
    margin: 10px;
}
.agree-radio {
    transform: scale(.7);
    position: relative;
    top: -3px;
    right: -3px;
}
.confirm-button {
    line-height: 40px;
    border-radius: 20px;
    width: 200px;
    text-align: center;
    background-color: #5cadff;
    color: #fff;
}
</style>