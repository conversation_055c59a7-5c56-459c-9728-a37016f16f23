<template>
    <view>
        <image v-if="!hideLogo && details['logo']" class="logo" mode="widthFix" :src="details['logo']"/>
        <view class="p10 bg-white">
            <view class="flex-kai">
                <view>
                    <view class="color-title font18" @click="copyId">{{ details['name'] }}</view>
                    <view v-if="details['organizer']" class="color-content font16">
                        主办方：{{ details['organizer'] }}
                    </view>
                </view>
                
                <view v-if="showShare" class="flex-all-center">

                    <button class="share-btn" type="default" :plain="true" size="mini" open-type="share">
                        <text class="iconfont icon-share-1 color-disabled font24"></text>
                    </button>
                </view>
                
            </view>
            <view v-if="!details['rank_set'] || !details['rank_set']['not_show_create_time']" class="font12 color-disabled">
                <text class="pr5">活动创建时间:</text>
                <uni-dateformat :date="details['create_time']" format="yyyy-MM-dd hh:mm:ss" 
                                :threshold="[0, 0]"/>
            </view>
            <view class="font14 color-sub ptm5">
                <text class="pr5">活动开始时间:</text>
                <uni-dateformat :date="details['begin_time'] * 1000" format="yyyy-MM-dd hh:mm:ss" 
                                :threshold="[0, 0]"/>
            </view>
            <view class="font14 color-sub">
                <text class="pr5">活动结束时间:</text>
                <uni-dateformat :date="details['end_time'] * 1000" format="yyyy-MM-dd hh:mm:ss" 
                                :threshold="[0, 0]"/>
            </view>
            <view v-if="activeDays" class="font14 color-sub pt5">
                <text class="pr5">活动时间总共:</text>
                <text>{{ activeDays }}</text>
            </view>
        </view>

    </view>
</template>

<script>
export default {
    name: "activity-logo-title-time",
    props: {
        details: {
            type: Object,
            default: () => {}
        },
        hideLogo: {
            type: Boolean,
            default: false
        },
        hideShare: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        activeDays() {
            const { begin_time, end_time } = this.details
            if (!begin_time || !end_time) return ''
            return this._utils.timeDifference(begin_time * 1000, end_time * 1000)
        },
        showShare() {
            let isWechat = true
            // #ifndef MP-WEIXIN
            isWechat = false
            // #endif
            return !this.hideShare && isWechat
        }
    },
    methods: {
        copyId() {
            this.$uni.setClipboardData(this.details.active_id, '活动ID已复制')
        }
    }
}
</script>

<style lang="scss">
.logo {
    display: block;
    width: 100vw;
    height: auto;
}

.share-btn {
    padding: 0;
    margin: 0;
    border: none !important;
    width: 40px;
    min-width: 40px;
    height: 40px;
    line-height: 40px;
}

.share-btn::after {
    border: none;
}
</style>