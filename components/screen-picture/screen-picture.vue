<template>
    <view>
        <view class="bg-white screen-pic-show" :class="{'screen-pic-hide': hide}" @click="skipScreen">
            <view v-if="time" class="count-down font12 flex-row">
                <view class="text-center" style="width: 12px;">{{ time }}</view>
                <view>秒后进入活动</view>
            </view>
            <view v-if="showButton" class="skip-btn color-white text-center">进入活动</view>

            <image class="screen-pic-image" :src="imageSrc" mode="aspectFill"/>
        </view>
    </view>
</template>

<script>
export default {
    name: "screen-picture",
    props: {
        imageSrc: {
            type: String,
            default: ''
        },
        time: {
            type: Number,
            default: 0
        },
        showButton: {
            type: Boolean,
            default: true
        },
        hide: {
            type: Boolean,
            default: false
        }
    },
    
    methods: {
        skipScreen() {
            this.$emit('skipScreen')
        }
    }
}
</script>

<style lang="scss">
.screen-pic-show {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999999;
    overflow: hidden;
    width: 100vw;
    height: 100vh;
}

.screen-pic-hide {
    animation: hide .5s;
}

@keyframes hide {
    from {
        top: 0;
        left: 0;
        border-radius: 0;
    }
    to {
        top: -100vh;
        left: 100vw;
        border-radius: 20vw;
    }
}
.count-down {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, .5);
    height: 24px;
    line-height: 24px;
    padding: 0 10px;
    border-radius: 12px;
    color: #fff;
    z-index: 999;
}

.screen-pic-image {
    display: block;
    width: 100vw;
    /* #ifdef H5 */
    max-width: 500px;
    margin: 0 auto;
    /* #endif */
    height: 100vh;
}

.skip-btn {
    position: absolute;
    bottom: 100px;
    left: 50%;
    margin-left: -100px;
    width: 200px;
    line-height: 40px;
    border-radius: 20px;
    background-color: rgba(45, 140, 241, .8);
    z-index: 999;
}

</style>