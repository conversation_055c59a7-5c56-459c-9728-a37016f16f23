<template>
    <view>
        <uni-popup ref="popup" @touchmove.stop.prevent="" :is-mask-click="false">
            <view class="popup bg-white">
                <view class="tips-text color-content text-center" @click="copyId">
                    活动已到期，{{ is_admin ? '如需继续进行活动，请联系客服解锁活动' : '请联系活动管理员' }}。
                </view>

                <view v-if="is_admin" class="flex-all-center p10">
                    <navigator class="contact-btn bg-success color-white text-center"
                               url="/pages/other/contact">联系客服
                    </navigator>
                </view>

                <view class="flex-all-center color-sub font14">
                    <navigator v-if="canBack" open-type="navigateBack" class="pl10 pr10">返回</navigator>
                    <navigator v-else class="pl10 pr10" url="/pages/user/user">个人中心</navigator>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
export default {
    name: "expiration-reminder",
    data() {
        return {
            is_admin: false,
            canBack: getCurrentPages().length > 1
        }
    },

    methods: {
        open(activity) {
            // 只有健步走活动才弹活动到期的弹窗
            if (activity.types !== 2) return

            this.is_admin = app.globalData['userid'] === activity.userid
            this.active_id = activity.active_id

            if (this.isExpired(activity)) this.$refs.popup.open()
        },

        isExpired(activity) {
            const expired_time = this.getExpiredTime(activity)
            const now = Math.floor(new Date().getTime() / 1000)
            return expired_time <= now
        },

        /**
         * @description 获取活动到期时间时间
         * @param activity  // 活动详情
         * @returns {Number}  // 活动到期时间 时间戳(秒)
         */
        getExpiredTime(activity) {
            let {expired_time = 0, create_time} = activity

            // 没有设置到期时间的活动，默认到期时间是从活动创建时间开始算起，有效期为365天
            if (!expired_time) {
                const createTime = this.time2timestamp(create_time)
                const validDays = this.day2timestamp(365)
                expired_time = Math.floor((createTime + validDays) / 1000)
            }

            return expired_time
        },

        // 年月日时分秒转时间戳
        time2timestamp: time => new Date(time.replace(/-/g, '/')).getTime(),

        // 计算day天的时间戳
        day2timestamp: day => day * 24 * 60 * 60 * 1000,

        copyId() {
            uni.setClipboardData({
                data: this.active_id,
                success: () => uni.hideToast()
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.popup {
    width: 80vw;
    padding: 10px;
    border-radius: 10px;

    .tips-text {
        padding: 10px 20px 30px;
    }

    .contact-btn {
        width: 250px;
        line-height: 44px;
        border-radius: 22px;
    }
}
</style>
