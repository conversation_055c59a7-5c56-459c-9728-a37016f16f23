<template>
    <view>
        <view class="form-item">
            <view class="top color-content">参与活动是否必须选择性别</view>
            <view class="bottom font16">
                <picker :range="range" range-key="title" :value="pickerData.value"
                        @change="required = range[$event.detail.value].value">
                    <view class="view flex-kai">
                        <view>{{ pickerData.title }}</view>
                        <view class="flex-all-center">
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </picker>
            </view>
        </view>

        <template v-if="required !== -1">
            <view class="form-item">
                <view class="top color-content">
                    <text>男性称呼</text>
                    <text class="color-sub font14 pl5">(不填默认“男”)</text>
                </view>
                <view class="bottom font16">
                    <input class="input" type="text" v-model="label.man"
                           placeholder="请输入男性称呼"/>
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">
                    <text>女性称呼</text>
                    <text class="color-sub font14 pl5">(不填默认“女”)</text>
                </view>
                <view class="bottom font16">
                    <input class="input" type="text" v-model="label.woman"
                           placeholder="请输入男性称呼"/>
                </view>
            </view>
        </template>
    </view>
</template>

<script>
export default {
    name: "active-sex-set",
    props: {
        sexRequired: {
            type: Number,
            default: 0
        },
        sexLabel: {
            type: Object,
            default: () => ({
                man: '男',
                woman: '女'
            })
        }
    },

    data() {
        return {
            required: this.sexRequired,
            label: this.sexLabel,

            // 由于一开始只有0非必选 和 1必选 所以0不能做不显示的值了
            range: [
                {value: 0, title: '非必选'},
                {value: 1, title: '必选'},
                {value: -1, title: '不显示性别'}
            ]
        }
    },

    computed: {
        pickerData() {
            let index = this.range.findIndex(item => item.value === this.required)
            if (index === -1) index = 0
            return this.range[index]
        }
    },

    watch: {
        sexRequired: {
            handler(val) {
                this.required = val
            },
            immediate: true
        },

        sexLabel: {
            handler(val) {
                this.label = val
            },
            immediate: true,
            deep: true
        },

        required(val) {
            this.$emit('update:sexRequired', val)
        },

        label: {
            handler(val) {
                this.$emit('update:sexLabel', val)
            },
            deep: true
        }
    }
}
</script>