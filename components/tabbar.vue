<template>
    <view class="tab-bar flex-row" :style="tabBarStyle">
        <view
            class="item text-center"
            v-for="(item, index) in list"
            :key="index"
            :style="'width: calc(100% / ' + list.length + ');'"
            @click="clickTabItem(index, item)"
        >
            <image class="icon" :src="index === active ? item.selpic : item.pic"/>
            <view class="title font14" 
                  :style="'color: ' + (index === active ? select_color : '#333333') + ';'">
                {{ item.title }}
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()

export default {
    name: "tab-bar",
    data() {
        return {
            active: -1,
            background_color: "#ffffff",
            color: "#80848f",
            select_color: "#0caef4",
            top_border_color: "#eeeeee",
            list: []
        }
    },
    
    computed: {
        tabBarStyle() {
            const {windowWidth} = uni.getSystemInfoSync()
            let left = 0, width = windowWidth
            // #ifdef H5
            if (windowWidth > 500) {
                left = (windowWidth - 500) / 2
                width = 500
            }
            // #endif
            const {background_color, top_border_color} = this
            return `
                background-color: ${background_color};
                border-top: 1px solid ${top_border_color};
                left: ${left}px;
                width: ${width}px;
            `
        }  
    },

    mounted() {
        this.initTabBarData()
    },

    methods: {
        initTabBarData() {
            const set = app.globalData['shop_info']?.['shop_set']?.['page_diy_set']?.['tabbar_set']
            if (!set || !set.list || !set.list.length) return false


            if (set.background_color) this.background_color = set.background_color
            if (set.color) this.color = set.color
            if (set.select_color) this.select_color = set.select_color
            if (set.top_border_color) this.top_border_color = set.top_border_color

            const list = set.list

            this.setTabBar(list)
        },


        setTabBar(list) {
            const pages = getCurrentPages()
            const current_page = pages[pages.length - 1]
            let current_route = current_page.route

            const shield_other_active = app.globalData['shop_info']?.['extend_set']?.['shield_other_active']
            // 独立的不显示客服
            if (shield_other_active?.active_id || current_route === 'pages/activity/user/detail') {
                const contact_index = list.findIndex(v => v.type === 25)
                if (contact_index >= 0) list.splice(contact_index, 1)
            }


            list.forEach((v, i) => {
                v.type = Number(v.type)

                if (v.type === 1) {
                    v.jump_type = 'new'
                    v.path = 'pages/news/preview'
                    v.route = `${v.path}?id=${v.news_id}`
                }

                if (v.type === 8) {
                    v.jump_type = 'closeAll'
                    v.route = 'pages/index/index'
                    // v.path = 'pages/activity/user/index'
                    v.path = 'pages/diy-page/diy-index'

                    if (shield_other_active?.active_id || current_route === 'pages/activity/user/detail') {
                        v.path = 'pages/activity/user/detail'
                    }
                }

                if (v.type === 9) {
                    v.jump_type = 'closeAll'
                    v.path = 'pages/user/user'
                    v.route = v.path
                    const opt = v.route.includes('?') ? '&' : '?'
                    v.route += `${opt}from=tab`
                }

                if (v.type === 12) {
                    v.jump_type = 'new'
                    v.path = 'pages/comment/list'
                    v.route = v.path
                }

                if (v.type === 25) {
                    v.jump_type = 'new'
                    v.path = 'pages/other/contact'
                    v.route = v.path
                }

                if (v.type === 41) {
                    v.jump_type = 'new'
                    v.path = this.xwy_config.getActivityPath(v.active_types, 'details').replace(/^\//, '')
                    v.route = `${v.path}?id=${v.active_id}`
                }

                if (current_route === v.path) this.active = i
            })


            this.list = list

        },

        clickTabItem(index, item) {

            if (index === this.active) return false

            if (!item.type) {
                uni.showModal({
                    title: '跳转失败',
                    content: '无此跳转类型【' + item.type + '】',
                    showCancel: false
                })
                return false
            }

            const jumpFailTips = content => {
                content = content || '跳转失败'
                uni.showModal({
                    title: '跳转失败',
                    content,
                    showCancel: false
                })
            }

            const url = '/' + item.route

            switch (item.jump_type) {
                case 'closeAll':
                    uni.reLaunch({
                        url,
                        fail: err => {
                            jumpFailTips(err.errMsg)
                        }
                    })
                    break

                default:
                    uni.navigateTo({
                        url,
                        fail: err => {
                            jumpFailTips(err.errMsg)
                        }
                    })
            }


        }
    }
}
</script>

<style scoped>
.tab-bar {
    position: fixed;
    bottom: 0;
    z-index: 100;
}

.item {
    padding-bottom: 15px;
}

.icon {
    width: 30px;
    height: 30px;
    position: relative;
    top: 3px;
}
</style>
