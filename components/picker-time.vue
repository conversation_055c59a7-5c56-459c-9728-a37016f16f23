<template>
	<view>

		<picker mode="multiSelector" :range="time_list" :value="time_index" @change="timeChange">
            {{text}}
		</picker>
	</view>
</template>

<script>
	export default {
		name:"picker-time",
		props: {
			text: {
				default: '00:00:00'
			},
			time: {
				default: '00:00:00'
			}
		},
		data() {
			return {
				time_list: (function() {
					const time_list = [[], [], []]
					for (let i = 0; i < 60; i++) {
						let time = i
						if (time < 10) time = '0' + time
						time = time.toString()
						if (i < 24) time_list[0].push(time)
						time_list[1].push(time)
						time_list[2].push(time)
					}
					return time_list
				})(),
				time_index: [0, 0, 0],
			}
		},
		watch: {
			time() {
				this.setTimeIndex()
			}
		},

		mounted() {
			this.setTimeIndex()
		},

		methods: {
			timeChange(e) {
                this.time_index = e.detail.value
				const time = this.setTime()
				this.$emit('update:time', time)
				this.$emit('timeChange', time)
			},

			setTime() {
				const time_list = this.time_list,
					time_index = this.time_index
				const h = time_list[0][time_index[0]],
					m = time_list[1][time_index[1]],
					s = time_list[2][time_index[2]]
				return h + ':' + m + ':' + s
			},

			setTimeIndex() {
          if (!this.time) return
				const time_arr = this.time.split(':')
				this.time_index = [
					Number(time_arr[0]),
					Number(time_arr[1]),
					Number(time_arr[2])
				]
			}
		}
	}
</script>

<style>

</style>
