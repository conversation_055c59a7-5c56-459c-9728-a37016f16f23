<template>
	<view>
		<swiper
			class="banner"
            :style="bannerStyle"
			v-if="list.length"
			:indicator-dots="list.length > 1"
			autoplay
			circular
			indicator-color="rgba(0, 0, 0, .3)"
			indicator-active-color="rgba(255, 255, 255, .7)"
		>
			<swiper-item class="banner-item" v-for="(item, index) in list" :key="index">
				<image class="banner-image-item" :src="item" mode="aspectFill" @click="previewImage(item)"/>
			</swiper-item>
		</swiper>
	</view>
</template>

<script>
	export default {
		name:"top-banner",
		props: {
			list: {
				default: null
			}
		},
        data() {
            return {
                bannerStyle: {
                    width: '100vw',
                    height: 'calc(50vw - 10px)'
                }
            }
        },
		mounted() {
            this.setBannerStyle()
		},
		methods: {
            setBannerStyle() {
                // #ifdef H5
                const windowWidth = uni.getSystemInfoSync().windowWidth
                if (windowWidth < 500) return
                this.bannerStyle.width = '500px'
                this.bannerStyle.height = '240px'
                // #endif
            },
            
			previewImage(current) {
			    uni.previewImage({
			        urls: this.list,
					current
			    })
			},
		}
	}
</script>

<style>
.banner {
  padding: 10px;
  box-sizing: border-box;
}

.banner-item {
	border-radius: 10px;
	overflow: hidden;
}

.banner-item, .banner-image-item {
	width: 100%;
	height: 100%;
}
</style>
