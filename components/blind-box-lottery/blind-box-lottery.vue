<template>
    <view>
        <uni-popup ref="popup" :is-mask-click="false" mask-background-color="rgba(0, 0, 0, .7)">
            <view class="my-popup color-white">
                <view class="text-center font18">
                    <template v-if="showEnd">{{ myPrize ? '获得' : '很遗憾 没有抽中' }}</template>
                    <template v-else>恭喜你获得一个盲盒</template>
                </view>


                <view v-if="prizeShow" class="my-prize-container flex-all-center flex-column">
                    <view class="my-prize-image-container">
                        <image class="my-prize-image" :src="myPrize ? myPrize.pic : prizeMissImg"
                               mode="widthFix"/>
                    </view>
                    <view v-if="myPrize && myPrize.name" class="pt10">{{ myPrize.name }}</view>
                </view>

                <view v-if="giftBoxShow" class="gift-box-container">
                    <view class="gift-box">
                        <view class="box-head" :class="{'box-head-animation': giftBoxOpen}">
                            <image
                                src="https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/blind-box-lottery/box-head.png"/>
                        </view>
                        <view class="box-body" :class="{'box-body-animation': giftBoxOpen}">
                            <image
                                src="https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/blind-box-lottery/box-body.png"/>
                        </view>
                    </view>
                </view>


                <view class="button-container flex-all-center">
                    <view class="open-button" v-if="!prizeOpened" @click="clickOpen">拆开盲盒</view>
                    <view class="close-button" v-if="showEnd" @click="close">关闭</view>
                </view>


                <view class="prize-container">
                    <view class="font12 pb5">有几率开出以下卡片</view>
                    <scroll-view class="prize-scroll-view" scroll-x="true">
                        <view class="prize-list flex-row">
                            <view class="prize-item" v-for="item in prizeList" :key="item.id">
                                <image class="prize-image" mode="aspectFill" :src="item.pic"
                                       @click="previewPrizeImage(item.pic)"/>
                            </view>
                        </view>
                    </scroll-view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "blind-box-lottery",
    emits: ['complete'],
    props: ['lotteryId', 'activeId'],
    data() {
        return {
            prizeList: [],
            prizeListScrollLeft: 0,
            giftBoxShow: true,
            giftBoxOpen: false,
            myPrize: null,
            prizeShow: false,
            prizeOpened: false,
            showEnd: false,
            prizeMissImg: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/blind-box-lottery/miss.png',
        }
    },

    mounted() {
        this.getLotterySet()
    },

    methods: {
        async getLotterySet() {
            const res = await this.xwy_api.request({
                url: 'front.flat.active.lottery_active.admin.manage_lottery/lottery_active_details',
                data: {
                    active_id: this.lotteryId
                }
            })

            if (!res?.data) return

            const {active_details: details, prize_list = []} = res.data
            if (!details) return

            const {conf = {}, end_time} = details
            this.end_time = end_time

            const {lottery_type, limit = {}} = conf
            if (lottery_type !== 'blind_box') return  // 抽奖活动不是盲盒抽奖类型

            const {active_list = [], condition} = limit
            if (condition !== 222) return  // 抽奖活动不是盲盒抽奖类型

            this.prizeList = prize_list.map(item => ({
                id: item.id,
                name: item.name,
                pic: item.pic
            }))

            this.pointIdList = active_list.filter(item => item.active_types === 3).map(item => item.id)
        },


        show(point_id) {
            if (!point_id) return
            if (!this.pointIdList.includes(point_id)) return  // 点位没有配置在抽奖里面
            const now = Date.now() / 1000
            if (now > this.end_time) return  // 抽奖活动已结束

            this.myPrize = null

            this.$refs.popup.open()
            this.startDraw(point_id)
        },

        initData() {
            this.giftBoxShow = true
            this.giftBoxOpen = false
            this.myPrize = null
            this.prizeShow = false
            this.prizeOpened = false
            this.showEnd = false
        },

        async startDraw(point_id) {
            this.initData()

            this.inDraw = true
            const res = await this.xwy_api.request({
                url: 'front.flat.active.lottery_active.user.lottery/lottery_act',
                data: {
                    lottery_active_id: this.lotteryId,
                    sport_step_active_id: this.activeId,
                    logs_types: point_id
                }
            })
            this.inDraw = false

            this.$emit('complete')

            const is_lottery = res?.data?.['lottery_result']?.is_lottery
            const prize = res?.data?.['lottery_result']?.data?.['lottery_details']
            if (is_lottery && prize) this.myPrize = {
                id: prize.id,
                name: prize.name,
                pic: prize.pic
            }
        },

        clickOpen() {
            if (this.inDraw) return this.$uni.showToast('盲盒还在准备中，请稍等')

            this.giftBoxOpen = true
            this.prizeOpened = true
            setTimeout(() => {
                this.prizeShow = true
            }, 300)
            setTimeout(() => {
                this.giftBoxShow = false
            }, 500)
            setTimeout(() => {
                this.showEnd = true
            }, 1000)
        },

        close() {
            this.$refs.popup.close()
        },

        previewPrizeImage(current) {
            this.$uni.previewImage({
                current,
                urls: this.prizeList.map(item => item.pic)
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.my-popup {
    width: 100vw;
    height: 100vh;
    padding-top: calc(100vh - 50vh - 280px);
    box-sizing: border-box;
    background-image: url(https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/blind-box-lottery/bg.png);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.my-prize-container, .gift-box-container {
    position: fixed;
    left: 0;
    bottom: 250px;
    width: 100vw;
    height: 50vh;
}

.my-prize-container {
    position: fixed;
    left: 0;
    bottom: 250px;

    .my-prize-image-container {
        background-color: #6A7094;
        border-radius: 10px;
        animation: prize-show .5s linear;
        position: relative;
        z-index: 1;

        .my-prize-image {
            width: 200px;
            display: block;
            border-radius: 10px;
        }
    }

    @keyframes prize-show {
        0% {
            transform: scale(0);
            bottom: -100px;
        }
        20% {
            transform: scale(.2);
            bottom: -80px;
        }
        40% {
            transform: scale(.4);
            bottom: -60px;
        }
        60% {
            transform: scale(.6);
            bottom: -40px;
        }
        80% {
            transform: scale(.8);
            bottom: -20px;
        }
        100% {
            transform: scale(1);
            bottom: 0;
        }
    }
}

.gift-box-container {
    display: flex;
    flex-direction: column-reverse;

    .gift-box {

        .box-head, .box-body {
            position: relative;

            image {
                width: 178px;
                margin: 0 auto;
                display: block;
            }
        }

        .box-head {
            z-index: 3;

            image {
                height: 55px;
            }
        }

        .box-body {
            z-index: 2;

            image {
                height: 87px;
            }
        }

        .box-head-animation {
            animation: box_head .5s cubic-bezier(0, 0.2, 0, 1) forwards;
            //animation-delay: .5s;
        }

        .box-body-animation {
            animation: box_body .5s forwards;
            //animation-delay: .5s;
        }

        @keyframes box_head {
            0% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(20rpx) rotate(2deg);
            }
            33% {
                transform: translateY(20rpx) rotate(0deg);
            }
            36% {
                transform: translateY(20rpx) rotate(-2deg);
            }
            39% {
                transform: translateY(20rpx) rotate(0deg);
            }
            42% {
                transform: translateY(20rpx) rotate(2deg);
            }
            45% {
                transform: translateY(20rpx) rotate(0deg);
            }
            100% {
                transform: translateY(-230rpx) rotate(0deg);
            }
        }

        @keyframes box_body {
            0% {
                transform: rotate(0) scale(1);
            }
            30% {
                transform: rotate(-2deg) scale(1.04);
            }
            40% {
                transform: rotate(0) scale(1);
            }
            450% {
                transform: rotate(1deg);
            }
            50% {
                transform: rotate(0);
            }
            55% {
                transform: rotate(-1deg);
            }
            60% {
                transform: rotate(0);
            }
        }
    }
}


.button-container {
    position: fixed;
    left: 0;
    bottom: 180px;
    width: 100vw;

    .open-button, .close-button {
        width: 150px;
        height: 44px;
        line-height: 40px;
        text-align: center;
        background: url(https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/guessing_songs/anc.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}

.prize-container {
    position: fixed;
    bottom: 10px;
    left: 10px;
    padding: 10px;
    box-sizing: border-box;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 10px;

    .prize-scroll-view {
        width: calc(100vw - 40px);

        .prize-list {
            .prize-item {
                padding: 0 5px;

                .prize-image {
                    width: 80px;
                    height: 80px;
                    display: block;
                    border-radius: 5px;
                }
            }
        }
    }
}
</style>