<template>
    <view>
        <view class="show" @click="$refs.setPopup.open()">
            <template>
                <view v-if="set.types === 0">每日签到奖励已关闭</view>
                <view v-if="set.types === 1">每日签到奖励{{ set.integral }}{{ unit }}</view>
                <view v-else>
                    <view v-for="(item, index) in set.circle_set" :key="index" class="ptm5">
                        第{{ index + 1 }}天: 奖励{{ item.integral }}{{ unit }}
                    </view>
                </view>
            </template>
            <view class="set-enter">
                <text class="iconfont icon-edit color-light-primary font14"></text>
                <text class="color-light-primary font14">修改设置</text>
            </view>
        </view>

        <uni-popup ref="setPopup" :is-mask-click="false" @touchmove.stop.prevent="">
            <view class="set-popup bg-white">
                <view class="text-center pb10">每日签到奖励设置</view>

                <uni-forms label-position="top" label-width="200">
                    <uni-forms-item label="奖励类型:">

                        <picker :range="typesOptions" range-key="title" :value="typesValue"
                                @change="typesChange">
                            <view class="form-picker flex-kai">
                                <view>{{ typesTitle }}</view>
                                <uni-icons type="forward" size="16" color="#c0c4cc"/>
                            </view>
                        </picker>
                    </uni-forms-item>

                    <template v-if="setData.types !== 0">
                        <uni-forms-item v-if="setData.types === 1" :label="`每日签到奖励${unit}数:`">
                            <uni-easyinput type="number" v-model="setData.integral" maxlength="5"/>
                        </uni-forms-item>

                        <uni-forms-item v-else label="周期签到奖励设置:">
                            <view>
                                <view class="every-day-item-set flex-kai"
                                      v-for="(item, index) in setData.circle_set" :key="index">
                                    <view class="flex-row">
                                        <view class="word">第{{ index + 1 }}天: 奖励</view>
                                        <view class="input">
                                            <uni-easyinput type="number" v-model="item.integral"
                                                           maxlength="5"/>
                                        </view>
                                        <view class="word">{{ unit }}</view>
                                    </view>
                                    <view v-show="setData.circle_set.length > 2" class="word pl10 pr10"
                                          @click="setData.circle_set.splice(index, 1)">
                                        <text class="iconfont icon-delete color-error"></text>
                                    </view>
                                </view>
                            </view>

                            <view v-show="setData.circle_set.length < 7" class="flex-all-center">
                                <view class="p10 color-light-primary"
                                      @click="setData.circle_set.push({integral: ''})">添加设置
                                </view>
                            </view>
                        </uni-forms-item>
                    </template>
                </uni-forms>

                <view class="confirm-button color-white bg-light-primary text-center"
                      hover-class="navigator-hover" @click="confirm">确定
                </view>

                <view class="flex-all-center">
                    <view class="p5 color-sub font12" @click="cancelSet">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
// 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
/** @namespace set.types */
/** @namespace set.integral */
/** @namespace set.circle_set */

export default {
    name: "daily-sign-set",
    props: {
        set: {
            type: Object,
            default: () => ({types: 0, integral: '', circle_set: []})
        },
        unit: {
            type: String,
            default: ''
        }
    },

    data() {
        return {
            setData: {types: 0, integral: '', circle_set: []}
        }
    },

    watch: {
        set: {
            handler(newVal) {
                this.setData = JSON.parse(JSON.stringify(newVal))
            },
            immediate: true,
            deep: true
        },

        'setData.types': function(newVal) {
            if (newVal === 2 && !this.setData.circle_set.length) this.setData.circle_set.push({integral: ''})
        }
    },

    computed: {
        typesOptions() {
            const unit = this.unit
            return [
                {value: 0, title: '关闭每日签到奖励'},
                {value: 1, title: `每日签到奖励固定${unit}`},
                {value: 2, title: `周期签到,每日签到奖励不同${unit}`}
            ]
        },

        typesValue() {
            const index = this.typesOptions.findIndex(item => item.value === this.setData.types)
            return index === -1 ? 0 : index
        },

        typesTitle() {
            return this.typesOptions.find(item => item.value === this.setData.types)?.title || ''
        }
    },

    methods: {
        typesChange(e) {
            this.setData.types = this.typesOptions[e.detail.value].value
        },

        cancelSet() {
            this.setData = this.set
            this.$refs.setPopup.close()
        },

        confirm() {
            const update = data => {
                this.$emit('update:set', data)
                this.$refs.setPopup.close()
            }

            const data = JSON.parse(JSON.stringify(this.setData))

            if (data.types === 0) return update(data)

            if (data.types === 1) {
                const integral = Math.floor(data.integral)
                if (isNaN(integral) || integral <= 0)
                    return this.$uni.showToast(`每日签到奖励${this.unit}设置有误，请设置大于0的整数`)
                data.integral = integral
                update(data)
            }

            for (let i = 0, len = data.circle_set.length; i < len; i++) {
                const item = data.circle_set[i]
                const integral = Math.floor(item.integral)
                if (isNaN(integral) || integral <= 0)
                    return this.$uni.showToast(`第${i + 1}天奖励设置有误，请设置大于0的整数`)
                item.integral = integral
            }

            update(data)
        }
    }
}
</script>

<style lang="scss" scoped>
.show {
    position: relative;
}

.set-enter {
    position: absolute;
    top: 0;
    right: 0;
    padding: 5px;

    .iconfont {
        padding-right: 2px;
    }
}


.set-popup {
    padding: 10px;
    border-radius: 10px;
    width: 80vw;
    max-width: 400px;

    .form-picker {
        border: 1px solid #e5e5e5;
        border-radius: 4px;
        height: 34px;
        line-height: 34px;
        padding: 0 10px;
    }

    .form-tips {
        position: relative;
        top: -5px;
    }

    .confirm-button {
        width: 150px;
        line-height: 40px;
        border-radius: 20px;
        margin: 20px auto 0;
    }

    .every-day-item-set {
        padding: 5px;

        .word {
            line-height: 36px;
        }

        .input {
            padding: 0 2px;
            width: 100px;
        }
    }
}
</style>