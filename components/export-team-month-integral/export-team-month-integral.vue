<template>
    <view>
        <uni-popup ref="popup" @touchmove.stop.prevent="">
            <view class="popup">
                <view class="text-center pb10">请选择月份</view>

                <picker mode="date" :value="date" start="2020-01-01" :end="pickerEndDate" fields="month"
                        @change="dateChange">
                    <view class="date-picker flex-row">
                        <view class="pr5">
                            <text :class="['iconfont color-sub', 'icon-calendar-' + dateYearMonth.month]"></text>
                        </view>
                        <view class="date-picker-year-month color-content">
                            {{ dateYearMonth.year }}年{{ dateYearMonth.month }}月
                        </view>
                        <view>
                            <text class="iconfont icon-more color-disabled"></text>
                        </view>
                    </view>
                </picker>

                <view class="flex-all-center pt15">
                    <view class="confirm-button color-white bg-light-primary text-center"
                          hover-class="navigator-hover" @click="confirmMonth">
                        确定
                    </view>
                </view>
                <view class="flex-all-center">
                    <view class="color-sub font14 pt10 pl10 pr10" @click="$refs.popup.close()">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
import openExcelFile from '@/utils/open-excel-file'

export default {
    name: "export-team-month-integral",
    props: ['activeId', 'unit'],

    data() {
        return {
            show: false,
            date: this._utils.getDay(0, true),
            pickerEndDate: this._utils.getDay(0, true),
        }
    },

    computed: {
        dateYearMonth() {
            const [year = '', month = ''] = this.date.split('-')
            return {year: Number(year), month: Number(month)}
        }
    },

    methods: {
        open() {
            this.$refs.popup.open()
        },

        dateChange(e) {
            this.date = e.detail.value
        },

        async confirmMonth() {
            this.$uni.showLoading('正在导出...')

            const teamList = await this.getTeamList()
            if (!teamList.length) {
                uni.hideLoading()
                this.$uni.showToast('活动未添加队伍')
                return
            }

            const {year, month} = this.dateYearMonth

            const integralList = await this.getIntegralRecord()
            if (!integralList?.length) {
                uni.hideLoading()
                this.$uni.showToast(`${year}年${month}月 没有${this.unit}数据`)
                return
            }


            integralList.forEach(({integral, team_id, time}) => {
                if (integral && team_id) {
                    const team = teamList.find(({id}) => id === team_id)
                    if (team) {
                        team.integral += (integral * 100)
                        if (time > team.last_time) team.last_time = time
                    }
                }
            })

            // teamList先按integral从大到小排序再按last_time从小到大排序
            teamList.sort((a, b) => {
                if (a.integral !== b.integral) return b.integral - a.integral
                return a.last_time - b.last_time
            })

            teamList.forEach(item => {
                item.last_time = this._utils.unitTimeToDate(item.last_time, true)
                item.integral = Number((item.integral / 100).toFixed(2))
            })

            const tableData = this.excelDataProcessing(teamList)
            openExcelFile.openDocument(tableData, `${year}年${month}月 队伍${this.unit}汇总`)

            uni.hideLoading()
            this.$refs.popup.close()
        },

        excelDataProcessing(list) {
            const tHead = ['序号', '系统标识', '队伍名称', this.unit, '最后更新时间']

            const tBody = list.map((item, index) => [
                index + 1,
                `w.${app.globalData['who']}u.${item.id || 0}`,
                item.name || '',
                item.integral || 0,
                item.last_time || ''
            ])

            return [tHead, ...tBody]
        },

        async getTeamList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/team_group_list',
                data: {
                    active_id: this.activeId,
                    page: 1,
                    perpage: 1000
                }
            })

            const list = res?.data?.['team_group_list']?.data || []
            return list.map(({id, name}) => ({
                id,
                name,
                integral: 0,
                last_time: 0
            }))
        },

        async getIntegralRecord(list = [], page = 1) {
            const res = await this.getIntegralList(page)
            list = list.concat(res.list)
            if (res.last_page) return list
            return await this.getIntegralRecord(list, page + 1)
        },

        async getIntegralList(page) {
            const {year, month} = this.dateYearMonth
            const yearMonth = `${year}-${month.toString().padStart(2, '0')}`
            const lastDay = new Date(year, month, 0).getDate().toString().padStart(2, '0')

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.userActiveIntegral/active_integral_list',
                data: {
                    active_id: this.activeId,
                    all_active_user: 1,
                    get_user_details: 1,
                    page,
                    perpage: 1000,
                    begin_time: `${yearMonth}-01 00:00:00`,
                    end_time: `${yearMonth}-${lastDay} 23:59:59`
                }
            })

            const data = res?.data?.integral_list
            if (!data) return {list: [], last_page: true}

            const {data: list, is_lastpage: last_page} = data
            return {
                list: list.map(item => ({
                    integral: item.integral_num,
                    team_id: item.user_details?.team_id || 0,
                    time: new Date(item.create_time.replace(/-/g, '/')).getTime()
                })),
                last_page
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.popup {
    width: 80vw;
    padding: 20px;
    max-width: 400px;
    background-color: #fff;
    border-radius: 10px;
}

.date-picker {
    padding: 10px;
    border-radius: 10px;
    border: 1px solid #eee;

    .date-picker-year-month {
        width: 100%;
    }
}

.confirm-button {
    width: 200px;
    line-height: 44px;
    border-radius: 22px;
}
</style>