<template>
    <view>
        <view class="form-item">
            <view class="top color-content">活动转发标题</view>
            <view class="bottom font16">
                <input class="input" type="text" v-model="share_title" placeholder="请输入活动转发标题"/>
            </view>
        </view>

        <view class="form-item">
            <view class="top color-title">
                <view>活动转发封面</view>
                <view class="color-sub font14">图片尺寸建议: 500*400</view>
            </view>
            <view style="padding-top: 5px;">
                <view class="image-view" v-if="share_image">

                    <image class="image-item" :src="share_image" mode="aspectFill"
                           @click="previewImage(share_image)"/>
                    <view class="del-image-item" @click.stop="share_image = ''">
                        <uni-icons type="closeempty" color="#e20f04"/>
                    </view>
                </view>
                <view v-else class="add-image text-center" @click="changeImage('share_image')">
                    <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                </view>

            </view>
        </view>

        <view v-if="rankSet && rankSet.closed_AD" class="form-item">
            <view class="top color-title">
                <view>活动二维码LOGO</view>
                <view class="color-sub font14">图片尺寸建议: 200*200</view>
            </view>
            <view style="padding-top: 5px;">
                <view class="image-view" v-if="qrcode_logo">

                    <image class="image-item" :src="qrcode_logo" mode="aspectFill"
                           @click="previewImage(qrcode_logo)"/>
                    <view class="del-image-item" @click.stop="qrcode_logo = ''">
                        <uni-icons type="closeempty" color="#e20f04"/>
                    </view>
                </view>
                <view v-else class="add-image text-center" @click="changeImage('qrcode_logo')">
                    <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                </view>

            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "active-share-set",
    props: ['rankSet', 'qrcodeLogo', 'shareImage', 'shareTitle', 'activeId'],
    data() {
        return {
            share_title: '',
            share_image: '',
            qrcode_logo: ''
        }
    },

    watch: {
        qrcodeLogo: {
            handler(val) {
                this.qrcode_logo = val
            },
            immediate: true
        },
        shareImage: {
            handler(val) {
                this.share_image = val
            },
            immediate: true
        },
        shareTitle: {
            handler(val) {
                this.share_title = val
            },
            immediate: true
        },

        share_title(val) {
            this.$emit('update:shareTitle', val)
        },
        share_image(val) {
            this.$emit('update:shareImage', val)
        },
        qrcode_logo(val) {
            this.$emit('update:qrcodeLogo', val)
        }
    },

    methods: {
        previewImage(src) {
            this.$uni.previewImage(src)
        },

        changeImage(key) {
            let url = '/pages/other/image_upload_or_select'
            if (this.activeId) url += `?active_id=${this.activeId}`

            this.$uni.navigateTo(url, {
                events: {
                    newImg: src => {
                        this[key] = src
                    }
                }
            })
        }
    }
}
</script>

<style lang="scss">
.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
    width: calc((100vw - 50px) / 3);
    aspect-ratio: 1;
    border-radius: 5px;
    margin: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-item {
    width: calc(100vw - 20px);
    height: 50vw;
    border-radius: 5px;
}

.image-view {
    position: relative;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .add-image {
        width: 150px;
    }

    .image-item {
        width: 500px;
        height: 200px;
    }
}
/* #endif */
</style>