<template>
    <view>
        <canvas v-if="have_qr_code_logo" class="canvas" id="qrcode" canvas-id="qrcode" 
                :style="canvasStyle"></canvas>

        <uni-popup ref="popup" type="bottom" :safe-area="false">
            <view class="share-popup-main bg-white">
                <view class="flex-row">
                    <!-- #ifndef H5 -->
                    <button class="share-item" open-type="share" @click="popupClose">
                        <text class="iconfont icon-share color-primary font28"/>
                        <view class="color-sub font14">转发给微信好友</view>
                    </button>
                    <!-- #endif -->
                    <button class="share-item" @click="getQrcode">
                        <text class="iconfont icon-qr-code font28 color-warning"/>
                        <view class="color-sub font14">{{ qrCodeTitle }}</view>
                    </button>
                </view>
                <view class="share-cancel font16 color-sub text-center" @click="popupClose">取消</view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()

export default {
    name: "active-share",
    props: {
        wechatShareTitle: {
            type: String,
            default: '转发给微信好友'  
        },
        qrCodeTitle: {
            type: String,
            default: '生成二维码'
        }
    },
    data() {
        return {
            have_qr_code_logo: false,
            qr_code_size: 500
        }
    },
    computed: {
        canvasStyle() {
            return `width: ${this.qr_code_size}px; height: ${this.qr_code_size}px;`
        }
    },

    methods: {
        popupClose() {
            this.$refs.popup.close()
        },

        open(data) {
            this.pagePath = data.page
            if (data.scene) this.scene = data.scene
            if (data.qrcode_logo) {
                this.qrcode_logo = data.qrcode_logo
                this.have_qr_code_logo = true
            }
            if (data.qr_code_size) this.qr_code_size = data.qr_code_size

            this.$refs.popup.open()
        },


        async getQrcode() {
            this.$uni.showLoading('二维码生成中...')

            if (this.share_image) return this.previewShareImage(this.share_image)

            const qr_data = {page: this.pagePath}
            if (this.scene) qr_data.scene = this.scene
            
            const qr_src = await this.xwy_api.getWxAppCode(qr_data)
            if (!qr_src) {
                uni.hideLoading()
                return this.$uni.showModal('活动海报生成失败')
            }
            this.qr_src = qr_src

            let is_h5 = false
            // #ifdef H5
            is_h5 = true
            // #endif
            if (is_h5 || !this.qrcode_logo) return this.previewShareImage(qr_src)

            await this.getImageInfo(qr_src)
        },

        async getImageInfo(qr_src) {
            const qr_info = await this.$uni.getImageInfo(qr_src)
            const qr_path = qr_info?.path
            if (!qr_info || qr_info === 'error' || !qr_path) return this.previewShareImage(qr_src)

            const logo_info = await this.$uni.getImageInfo(this.qrcode_logo)
            const logo_path = logo_info?.path
            if (!logo_info || logo_info === 'error' || !logo_path) return this.previewShareImage(qr_src)

            this.drawCanvas(qr_path, logo_path)
        },

        drawCanvas(qr_path, logo_path) {
            const ctx = uni.createCanvasContext('qrcode', this)
            const qr_size = this.qr_code_size
            const center_point = qr_size / 2
            const logo_size = qr_size * 0.4
            const logo_point = (qr_size - logo_size) / 2
            ctx.drawImage(qr_path, 0, 0, qr_size, qr_size)
            ctx.beginPath()
            ctx.arc(center_point, center_point, logo_size / 2, 0, Math.PI * 2, false)
            ctx.clip()
            ctx.drawImage(logo_path, logo_point, logo_point, logo_size, logo_size)
            ctx.restore()

            this.$nextTick(() => {
                ctx.draw(true, () => {
                    this.$nextTick(() => this.canvasToTempFilePath(0))
                })
            })
        },

        canvasToTempFilePath(count = 0) {
            uni.canvasToTempFilePath({
                canvasId: 'qrcode',
                success: res => this.previewShareImage(res.tempFilePath),
                fail: err => {
                    console.log('生成失败', err)
                    if (count >= 30) return this.previewShareImage(this.qr_src)
                    count++
                    setTimeout(() => this.canvasToTempFilePath(count), 300)
                }
            }, this)
        },

        previewShareImage(share_image) {
            uni.hideLoading()
            this.popupClose()
            this.share_image = share_image
            this.$uni.previewImage(share_image)
        },
    }
}
</script>

<style lang="scss" scoped>
.canvas {
    position: fixed;
    top: -1000vh;
    left: -1000vw;
}

.share-popup-main {
    width: 100vw;
    border-radius: 20px 20px 0 0;
    overflow: hidden;
}

.share-item {
    padding: 0;
    margin: 10px 0;
    background-color: #fff;
    border: none;
    font-weight: normal;
    width: 50% !important;
    line-height: 1.5;
}

.share-item::after, .share-btn::after {
    border: none;
}

.share-cancel {
    line-height: 44px;
    padding-bottom: 10px;
    border-top: 1px solid #eee;
}
</style>