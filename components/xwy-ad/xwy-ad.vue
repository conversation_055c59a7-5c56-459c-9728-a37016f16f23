<template>
    <view>
        <view v-for="(item, index) in ad_list" :key="index">
            <!--
            1: banner广告
            2: 激励式视频广告"
            3: 插屏广告（展示）
            4: 视频广告
            5: 前贴视频
            6: 插屏广告（点击）
            7: 格子广告
            66: 原生模板广告
            -->
            <ad-custom
                v-if="item.ad_types === 66 && ad_type === 66"
                ad-intervals="31"
                :unit-id="item.ad_id"
            ></ad-custom>
            <ad
                v-if="item.ad_types === 1 && ad_type === 1"
                ad-type="banner"
                ad-intervals="31"
                :unit-id="item.ad_id"
                @error="adError"
            ></ad>
            <ad
                v-if="item.ad_types === 4 && ad_type === 4"
                ad-type="video"
                ad-intervals="31"
                :unit-id="item.ad_id"
                @error="adError"
            ></ad>
            <ad
                v-if="item.ad_types === 7 && ad_type === 7"
                ad-type="grid"
                ad-intervals="31"
                :unit-id="item.ad_id"
                @error="adError"
            ></ad>
        </view>
    </view>
</template>

<script>
const app = getApp()
import my_storage from '@/utils/storage.js'

export default {
    name: "xwy-ad",
    props: {
        activity_id: {
            default: null
        },
        ad_type: {
            default: null
        }
    },
    data() {
        return {
            ad_list: []
        }
    },
    mounted() {
        let isH5 = false
        // #ifdef H5
        // h5不显示广告
        isH5 = true
        console.log('h5不显示广告')
        // #endif
        if (isH5) return

        // 查看活动是否去广告
        if (this.activity_id && my_storage.activityIsCloseAd(this.activity_id)) {
            console.log('本活动已去广告，不显示广告:', this.activity_id)
            return false
        }

        if (app.globalData.shop_info?.extend_set?.shield_other_active?.active_id) {
            console.log('纯净版，不显示广告：', app.globalData.shop_info.extend_set.shield_other_active.active_id)
            return false
        }


        this.getAdList()
    },
    methods: {
        getAdList() {
            if (app.globalData.shop_info?.shop_set?.ad_list?.length) {
                this.ad_list = app.globalData.shop_info.shop_set.ad_list
                this.initAd()
            }
        },

        initAd() {
            const ad_list = this.ad_list
            if (this.ad_type === 3) {
                const interstitial_ad = ad_list.find(v => v.ad_types === 3);
                if (interstitial_ad) this.createInterstitialAd(interstitial_ad.ad_id);
            }
        },

        createInterstitialAd(adid) {
            const interstitialAd = uni.createInterstitialAd({
                adUnitId: adid
            })

            interstitialAd.onLoad(() => {
                console.log("插屏 广告加载成功")

                setTimeout(() => {
                    interstitialAd.show().then(res => console.log("插屏 广告显示成功", res))
                        .catch(err => console.log("插屏 广告显示失败", err))
                }, 1000)
            })

            interstitialAd.onClose(() => console.log("插屏 广告关闭"))
            interstitialAd.onError(err => console.log("插屏 广告加载失败", err))
        },

        adError(e) {
            console.log(e)
        }
    }
}
</script>

<style>

</style>
