<template>
    <view>
        <uni-popup ref="popup" type="top" :is-mask-click="false" @touchmove.stop.prevent="">
            <view class="popup-container flex-all-center">
                <view class="popup bg-white text-center p10">
                    <view class="color-title">验证码</view>
                    <view class="code p10 flex-all-center">
                        <view v-if="in_get_code_image">
                            <load-ani/>
                        </view>
                        <view v-if="!in_get_code_image && code_image">

                            <image class="code-image" :src="code_image" mode="heightFix" />
                        </view>
                        <view v-if="!in_get_code_image && !code_image && get_code_error"
                              class="color-sub font14 text-center">{{ get_code_error }}
                        </view>
                    </view>

                    <view class="replacement-code-image flex-all-center">
                        <view v-if="!in_get_code_image && code_image" class="color-light-primary font14"
                              @click="getCodeImage">
                            <text class="iconfont icon-sync color-light-primary font14"></text>
                            <text style="padding-left: 2px;">换一张</text>
                        </view>
                    </view>

                    <view v-if="input_show && code_image" class="p10">
                        <uni-easyinput v-model="code" focus :maxlength="10" placeholder="请输入图中验证码" 
                                       @confirm="confirm"/>
                    </view>
                    <view v-if="code_image" class="confirm-button bg-primary color-white"
                          hover-class="navigator-hover" @click="confirm">确定
                    </view>
                    <view class="flex-all-center p10 color-sub font14" @click="close">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "verify-code-input-popup",
    props: {},
    data() {
        return {
            input_show: false,
            in_get_code_image: false,
            code_image: '',
            code: '',
            get_code_error: ''
        }
    },

    methods: {
        show(callback = null) {
            this.code = ''
            this.code_image = ''
            this.$refs.popup.open()
            this.input_show = true
            this.getCodeImage()
            this.callback = callback
        },
        
        setGetCodeError(info = '') {
            this.get_code_error = info
            if (info) this.$uni.showToast(info)
        },
        
        async getCodeImage() {
            this.setGetCodeError()
            this.code_image = ''
            this.in_get_code_image = true
            const res = await this.xwy_api.request({
                url: 'front.user.account.vipBuy/get_code_image'
            })
            this.in_get_code_image = false
            
            if (res?.status !== 1) return this.setGetCodeError(res?.info || '验证码获取失败')
            
            const data = res?.data
            if (!data) return this.setGetCodeError('验证码获取失败')
            
            const code_image = data.image
            if (!code_image) return this.setGetCodeError(res?.info || '验证码获取失败')
            
            const uuid = data.uuid
            if (!uuid) return this.setGetCodeError(res?.info || '验证码获取失败-uuid')
            
            this.code_image = code_image
            this.uuid = uuid
        },
        
        close() {
            this.$refs.popup.close()
            this.input_show = false
        },

        confirm() {
            if (!this.code) return this.$uni.showToast('请输入验证码')
            this.callback && this.callback({codeNum: this.code, uuid: this.uuid})
            this.close()
        }
    }
}
</script>

<style lang="scss">
.popup-container {
    width: 100%;
    height: 60vh;
    min-height: 500px;
}

.popup {
    width: 320px;
    border-radius: 10px;
}

.code, .code-image {
    height: 80px;
}

.code-image {
    display: block;
}

.replacement-code-image {
    height: 20px;
}

.confirm-button {
    margin: 0 auto;
    line-height: 44px;
    border-radius: 22px;
    width: 200px;
}
</style>