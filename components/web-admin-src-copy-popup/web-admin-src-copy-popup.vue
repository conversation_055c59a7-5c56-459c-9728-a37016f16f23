<template>
    <view>
        <uni-popup ref="copy_web_src" type="center">
            <view class="popup text-center bg-white">
                <view class="popup-close" @click="close">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top bg-primary color-white" style="padding: 50px 20px 20px;">
                    <text class="iconfont icon-screen color-white" style="font-size: 80px;"></text>
                    <view class="font18">{{ copy_web_src.title }}</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ copy_web_src.content }}
                    </view>
                    <view
                        class="button bg-info color-white"
                        hover-class="navigator-hover"
                        @click="copy"
                    >复制地址</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()

export default {
    name: "web-admin-src-copy-popup",
    props: {},
    data() {
        return {
            copy_web_src: {
                src: '',
                title: '',
                content: ''
            }
        }
    },

    methods: {
        open(options) {
            const web_base_url = app.globalData['shop_info']?.['shop_set']?.static_url
            if (!web_base_url) return this.$uni.showModal('没有配置【static_url】，无法使用web端管理')
            if (options?.successCallback) this.successCallback = options.successCallback
            if (options?.closeCallback) this.closeCallback = options.closeCallback

            const src = `${web_base_url}/web/wx-cloud-api/pages/sport_step#/pages/H5/login?who=${app.globalData['who']}`

            this.copy_web_src = {
                title: '已生成web端管理地址',
                content: '请复制后使用浏览器打开。如未设置web端登录账号密码，请到"我的"-"我的资料"里面设置',
                src
            }
            this.$refs.copy_web_src.open()
            this.successCallback?.()
            this.$uni.setClipboardData(this.copy_web_src.src)
        },

        copy() {
            this.$uni.setClipboardData(this.copy_web_src.src, '已复制')
            this.close()
        },

        close() {
            this.$refs.copy_web_src.close()
            this.closeCallback?.()
        }
    }
}
</script>

<style lang="scss">
.popup {
    width: 280px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;

    .popup-close {
        position: absolute;
        padding: 5px;
        right: 0;
        top: 0;
    }
    
    .button {
        line-height: 40px;
        border-radius: 20px;
    }
    
    .button::after {
        border: none;
    }
}
</style>