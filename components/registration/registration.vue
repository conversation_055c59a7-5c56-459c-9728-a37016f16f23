<template>
    <view>
        <uni-popup ref="choosePopup" :is-mask-click="false">
            <view class="choose-popup">
                <view class="color-title text-center p10">选择报名项目</view>
                <view class="select-box flex-kai" hover-class="navigator-hover" @click="chooseProject">
                    <view class="pr10">
                        <view v-if="project.name" class="color-content">{{ project.name }}</view>
                        <view v-else class="color-sub">请选择报名项目</view>
                    </view>
                    <!--<view class="choose-text">
                        <text v-if="!project.name" class="color-light-primary font14 color-sub">选择</text>
                        <uni-icons type="forward" :color="project.name ? '#bbbec4' : '#5cadff'" size="14"/>
                    </view>-->
                    <uni-icons type="forward" color="#bbbec4" size="14"/>
                </view>
                <view v-if="project.amount" class="color-sub font12 pl10 text-center">
                    <text class="pr5">需支付: </text>
                    <text class="color-error font14">{{ project.amount.toFixed(2) }}</text>
                    <text>元</text>
                </view>
                <view class="button-box flex-row">
                    <view
                        class="button-cancel color-sub"
                        hover-class="navigator-hover"
                        @click="cancelChoose"
                    >取消</view>
                    <view
                        class="button-confirm color-primary"
                        hover-class="navigator-hover"
                        @click="chooseConfirm"
                    >确定</view>
                </view>
            </view>
        </uni-popup>

        <uni-popup ref="paySuccessPopup" :is-mask-click="false">
            <view class="pay-success-popup text-center pt15 pb10">
                <view class="pt15">
                    <uni-icons type="checkbox" color="#19be6b" size="100"/>
                    <!--<text style="font-size: 100px;">🎉</text>-->
                </view>
                <view class="font18 color-success pb10">支付成功</view>
                <view class="color-sub font14 p10">支付成功！{{ countdown }}秒后进入报名</view>
                <view class="flex-all-center pb10">
                    <view
                        class="color-primary p10"
                        hover-class="navigator-hover"
                        @click="paySuccessPopupClose"
                    >现在报名</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
export default {
    name: "registration",
    data() {
        return {
            project: {
                name: '',
                id: null,
                amount: 0
            },
            countdown: 5
        }
    },
    mounted() {

    },
    methods: {
        async check(id, callback) {
            uni.showLoading({ mask: true })
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_pay.userBuy/check_user_have_pay_active',
                data: { active_id: id }
            })
            uni.hideLoading()

            if (res?.['status'] === 1) {
                callback && callback(true)
                return
            }
            this.callback = callback
            this.id = id

            this.$refs.choosePopup.open()
        },

        cancelChoose() {
            this.$refs.choosePopup.close()
            this.callback && this.callback(false)
        },

        chooseProject() {
            uni.navigateTo({
                url: `/pages/registration/admin/project/list?choose=1&id=${this.id}`,
                events: {
                    chooseProject: data => {
                        this.project = data
                    }
                }
            })
        },

        chooseConfirm() {
            if (!this.project.id) return uni.showToast({ title: '请选择报名项目', icon: 'none' })
            this.submit()
        },

        async submit() {
            uni.showLoading({ mask: true })
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_pay.userBuy/submit_for_buy_active',
                data: {
                    id: this.project.id,
                    active_id: this.id,
                    num: 1
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.xwy_api.alert(res?.info || '提交失败，请重试！')
            if (!res?.data?.details) return this.xwy_api.alert('提交失败，请重试！')
            if (!this.project.amount) {
                this.$refs.choosePopup.close()
                return this.callback && this.callback(true)
            }
            await this.toPay(res.data.details)
        },

        async toPay(data) {
            uni.showLoading({ mask: true })
            const res = await this.xwy_api.request({
                url: 'front.user.wechat.pay.small_app/small_app_create_pay',
                data: {
                    shopid: app.globalData['who'],
                    openid: app.globalData['openid'],
                    amount: data.amount,
                    foreign_orderid: this.id,
                    profit_sharing: 1, // 【1】代表需要进行分账
                    types: 12,
                    name: '付费报名'
                }
            })
            uni.hideLoading()
            if (res?.status !== 1) return this.xwy_api.alert(res?.info || '下单失败！请重试！')
            if (!res?.data?.['create_res']) return this.xwy_api.alert('下单失败！请重试！')

            this.requestPayment(res.data['create_res'])
        },

        requestPayment(data) {
            wx.requestPayment({
                timeStamp: data.timeStamp,
                nonceStr: data.nonceStr,
                package: data.package,
                signType: data.signType || 'MD5',
                paySign: data.paySign,
                success: res => {
                    console.log(res)
                    this.$refs.choosePopup.close()
                    this.paySuccessPopupOpen()
                },
                fail: res => {
                    console.log('支付失败', res)
                }
            })
        },

        paySuccessPopupOpen() {
            this.$refs.paySuccessPopup.open()
            this.countdown = 5
            this.timer = setInterval(() => {
                this.countdown--
                if (this.countdown <= 0) {
                    this.paySuccessPopupClose()
                }
            }, 1000)
        },

        paySuccessPopupClose() {
            clearInterval(this.timer)
            this.$refs.paySuccessPopup.close()
            this.callback && this.callback(true)
        }
    }
}
</script>

<style lang="scss">
.choose-popup, .pay-success-popup {
    background-color: #fff;
    width: 85vw;
    border-radius: 10px;
    overflow: hidden;
}
.choose-popup {
    .select-box {
        margin: 10px;
        padding: 10px;
        border-radius: 10px;
        border: 1px solid #eee;
        background-color: #fdfdfd;
        .choose-text {
            min-width: 45px;
            text-align: right;
        }
    }
    .button-box {
        margin-top: 10px;
        border-top: 1px solid #eee;
        .button-cancel, .button-confirm {
            width: 50%;
            text-align: center;
            line-height: 44px;
        }
        .button-cancel {
            box-sizing: border-box;
            border-right: 1px solid #eee;
        }
    }
}
</style>
