# Gemini 项目上下文：健步走 (step-uniapp-wxcloud)

## 1. 项目概述

这是一个名为“健步走”的综合性运动健康管理微信小程序。它使用 **uni-app 框架 (基于 Vue 2)** 构建，支持跨平台部署，但主要为微信小程序和 H5 进行了配置。

该应用功能丰富，以健步走活动为核心，包含了广泛的模块，例如：
-   **核心活动**: 步数挑战、AI 运动识别、跑步追踪、身高体重管理。
-   **游戏化**: 超过20种小游戏（如拼图、问答、街机风格游戏）、抽奖、勋章和证书体系，以增强用户粘性。
-   **社交与社群**: 用于分享动态、排行榜和团队功能的“运动圈”。
-   **内容与互动**: 朗诵挑战、投票系统、文章阅读，甚至还有一个类似 ChatGPT 的 AI 助手。
-   **后台与企业功能**: 应用包含了用于管理活动、用户、团队和导出数据的大量后台页面，表明其具有 B2B 或企业定制化的特点。

后端 API 地址为 `https://sport.xwykj.com`，该项目由“xinweiyun”(新微云) 维护。

## 2. 技术栈与全局注入

-   **框架**: uni-app (Vue 2.6.x)
-   **目标平台**: 微信小程序 (mp-weixin), H5
-   **核心依赖**: `vue@2.6.14`, `pinyin-pro`, `fetch-wechat`
-   **样式**: SCSS (全局变量/混入在 `uni.scss`)

### 全局注入 (main.js)

为了方便调用和统一管理，以下模块被注入到 Vue 全局原型中，可在所有页面和组件中通过 `this` 访问：

-   `this.xwy_api`: **核心API模块**，封装了所有后端接口请求。
-   `this._utils`: 通用工具函数集合（时间、日期、格式化 等）。
-   `this.$uni`: 对 uni-app 官方API的二次封装（如路由、showToast、showModal）。
-   `this.$wx`: 微信小程序专属API的封装（如订阅消息、支付、NFC等）。
-   `this.xwy_config`: **核心业务配置模块**，包含大量业务开关和规则函数。
-   `this.$login`: 封装了统一的登录逻辑。
-   `this.xwyLib`, `this.base64`, `this.imgImport`: “利口答题”功能库。

## 3. 构建与运行

项目推荐使用 `npm scripts` 进行构建和运行。

```json
"scripts": {
    "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch --minimize",
    "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build --minimize",
    "dev:h5": "cross-env UNI_PLATFORM=h5 vue-cli-service uni-serve",
    "build:h5": "cross-env UNI_PLATFORM=h5 vue-cli-service uni-build"
}
```

-   **微信小程序调试**: 运行 `npm run dev:mp-weixin`，然后使用微信开发者工具导入项目根目录下的 `/dist/dev/mp-weixin` 目录。
-   **H5 调试**: 运行 `npm run dev:h5`，然后在浏览器中访问对应的本地地址。

## 4. 目录结构与职责

-   `pages/`: 业务页面，按功能模块划分**分包**。
-   `components/`: 可复用的全局业务组件。
-   `utils/`: 全局工具和API封装。
    -   `api/`: 存放所有与后端交互的接口封装。
-   `config/`: **核心业务配置文件**。
    -   `config.js`: 包含大量业务开关和白名单等函数，是控制业务逻辑的重要文件。
-   `static/`: 静态资源（图片、字体等）。
-   `uni_modules/`: 第三方 uni-app 组件库。
-   `uni.scss`: 全局 SCSS 变量和混入。
-   `main.js`: 应用入口，负责全局注入和初始化。
-   `pages.json`: 页面路由、分包和导航栏配置。
-   `manifest.json`: 应用清单，配置小程序AppID、图标、权限等。

## 5. 核心配置 `config/config.js`

此文件是项目的业务逻辑大脑，而非简单的静态配置。它通过导出大量函数来集中管理不同活动、不同页面的行为差异和功能开关。**进行业务逻辑修改时，应优先考虑在此文件中添加或修改配置函数，而不是在页面中硬编码逻辑判断。**

**示例功能：**
-   根据活动ID判断是否开启某个功能（如 `openBusinessKilometers(id)`）。
-   根据用户ID或页面路径控制UI元素的显示（如 `userCenterActivityModuleShow()`）。
-   为特定活动提供定制化的文案或样式（如 `examResultScoreText(id, ...)`）。

## 6. 代码风格与规范

-   **缩进**: 4个空格。
-   **命名**: 
    -   文件和目录使用 `kebab-case` (短横线连接)。
    -   组件的 `name` 选项使用 `PascalCase` (大驼峰)。
-   **注释**: 必须使用中文，清晰地解释代码意图、函数参数和返回值。
-   **Vue**: 
    -   遵循Vue2语法，`props` 需标注类型和默认值。
    -   异步操作优先使用 `async/await` 并配合 `try/catch` 进行错误处理。
-   **API调用**: 必须通过 `this.xwy_api` 调用，禁止直接使用 `uni.request`。

## 7. Git 工作流

-   **分支模型**:
    -   `main`: 主分支。
    -   `dev`: 开发分支。
    -   `dev-*`: 日常开发主分支。
    -   `dev-*/*`: 新功能开发分支。
    -   `release`: 发布分支。
