# CLAUDE.md

这个文件为 Claude Code (claude.ai/code) 在该代码库中工作时提供指导。

## 项目概述

这是一个基于 uni-app 的微信小程序项目，名为"健步走"，主要用于步数打卡、运动管理等功能。项目使用 Vue 2 + uni-app 框架，支持多个小程序平台（主要是微信小程序）。

## 项目架构

### 技术栈
- **框架**: uni-app (Vue 2)
- **平台**: 微信小程序为主，支持多平台
- **云服务**: PHP后台(主要) + 微信云开发 (wx.cloud)
- **UI组件**: 自定义组件 + uni-app 官方组件
- **开发工具**: WebStorm、VSCode、Cursor、Trae、HBuilderX等

### 项目结构
```
├── pages/           # 页面文件（使用分包架构）
│   ├── index/       # 首页
│   ├── activity/    # 活动相关页面
│   ├── user/        # 用户相关页面
│   ├── games/       # 游戏中心
│   ├── voice/       # 朗诵活动
│   ├── clock_in/    # 打卡功能
│   └── ...          # 其他功能模块
├── components/      # 公共组件
├── utils/          # 工具函数
│   ├── api/        # API 接口
│   ├── utils.js    # 通用工具
│   └── uni-api.js  # uni-app API 封装
├── config/         # 配置文件
├── static/         # 静态资源
└── uni_modules/    # uni-app 模块
```

### 分包架构
项目采用分包架构，主要分包：
- `pages/activity` - 活动相关功能
- `pages/user` - 用户中心
- `pages/games` - 游戏中心
- `pages/voice` - 朗诵活动
- `pages/clock_in` - 打卡功能
- 其他功能模块各自独立分包

## 核心配置

### 全局配置
- **App.vue**: 应用全局配置，包含环境配置、API 域名设置
- **main.js**: 项目入口文件，全局 API 和组件注册
- **manifest.json**: uni-app 应用配置
- **pages.json**: 页面路由和分包配置

### 重要配置说明
- `config/config.js`: 项目核心配置文件，包含活动相关配置
- `utils/api/xwy_api.js`: 主要的 API 接口封装
- API 域名：https://sport.xwykj.com

## 常用命令

### 依赖管理
```bash
# 安装依赖
npm install

# 添加依赖
npm install <package-name>
```

## 开发规范

### 页面开发
1. 页面文件放在对应的分包目录下
2. 使用 `.vue` 单文件组件格式
3. 遵循 uni-app、vue2 开发规范

### 组件开发
1. 公共组件放在 `components/` 目录
2. 功能特定组件放在对应页面目录下
3. 组件命名使用 kebab-case

### API 调用
1. 使用 `this.xwy_api.request()` 方法调用 API
2. API 接口统一在 `utils/api/` 目录管理
3. 错误处理统一使用 `this.$uni.showModal()` 方法

### 样式规范
1. 使用 SCSS 预处理器
2. 公共样式在 `static/style/public.css`
3. 组件样式使用 scoped

## 核心功能模块

### 活动管理 (pages/activity)
- 健步走活动创建和管理
- 用户参与活动
- 排行榜系统
- 兑换系统

### 用户系统 (pages/user)
- 用户信息管理
- 积分系统
- 个人中心

### 游戏中心 (pages/games)
- 多种小游戏集合
- 游戏记录管理
- 排行榜系统

### 其他功能
- 朗诵活动 (pages/voice)
- 打卡功能 (pages/clock_in)
- 投票系统 (pages/vote)
- 商城系统 (pages/shop)

## 注意事项

1. **微信小程序特性**: 项目主要针对微信小程序，使用了微信云开发功能
2. **分包加载**: 项目采用分包架构，注意分包大小限制
3. **API 域名**: 生产环境 API 域名为 https://sport.xwykj.com
4. **权限管理**: 不同用户角色有不同的功能权限
5. **图片上传**: 使用腾讯云对象存储服务

## 调试和测试

### 开发环境
- 使用 Webstorm 进行开发
- 微信开发者工具预览和调试
- 真机调试测试

### 环境配置
- 开发环境会自动设置测试 token
- 生产环境使用正式 API 域名
- 根据 `evn_version` 判断环境

## 常见问题

1. **分包路径**: 注意分包路径配置，确保页面能正确加载
2. **API 调用**: 确保 API 调用时传递正确的参数
3. **权限检查**: 某些功能需要特定用户权限
4. **图片处理**: 注意图片大小限制和格式要求