# 健步走 - uni-app微信小程序

<div align="center">

![健步走LOGO](https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/public_image/main_figure/jbz.jpg)

**基于uni-app + Vue 2的综合性运动健康管理微信小程序**

[![Vue.js](https://img.shields.io/badge/Vue.js-2.6.14-green.svg)](https://vuejs.org/)
[![uni-app](https://img.shields.io/badge/uni-app-3.0.0-blue.svg)](https://uniapp.dcloud.io/)
[![微信小程序](https://img.shields.io/badge/微信小程序-1.0.0-brightgreen.svg)](https://developers.weixin.qq.com/miniprogram/dev/)

[功能特色](#-功能特色) • [快速开始](#-快速开始) • [技术架构](#-技术架构) • [项目结构](#-项目结构) • [开发指南](#-开发指南) • [部署说明](#-部署说明)

</div>

---

## 📋 项目概述

"健步走"是一个功能丰富的综合性运动健康管理微信小程序，基于uni-app框架开发，支持多平台发布。小程序以健步走活动为核心，集成了游戏中心、用户系统、朗诵活动、打卡功能等多种模块，为用户提供全方位的运动健康管理体验。

### 🎯 核心特色

- **多活动类型支持**：健步走、AI运动、朗诵活动、打卡功能、投票系统等
- **游戏化体验**：内置20+种趣味小游戏，增加用户粘性
- **社交互动**：运动圈、排行榜、分享功能，构建运动社区
- **数据统计**：详细的运动数据分析和可视化展示
- **企业定制**：支持企业定制化活动和功能

---

## ✨ 功能特色

### 🏃‍♂️ 运动健康
- **健步走活动**：创建参与健步走活动，实时步数统计
- **AI运动识别**：智能识别运动动作，科学运动指导
- **运动打卡**：多种打卡模式，培养运动习惯
- **体重管理**：体重记录、BMI分析、健康建议

### 🎮 游戏中心
- **益智游戏**：2048、五子棋、贪吃蛇、俄罗斯方块
- **运动游戏**：飞机大战、打地鼠、小马快跑
- **知识游戏**：成语接龙、垃圾分类、绕口令
- **抽奖系统**：步数抽奖、积分抽奖、红包活动

### 📱 社交互动
- **运动圈**：分享运动动态，互动评论
- **排行榜**：多种排行榜类型，激发竞争意识
- **分享功能**：一键分享活动成果
- **邀请好友**：好友邀请奖励机制

### 🏢 企业功能
- **活动管理**：完整的后台管理系统
- **数据导出**：Excel格式数据导出
- **团队管理**：部门团队分级管理
- **定制化服务**：企业定制活动页面和功能

---

## 🛠️ 技术架构

### 前端技术栈
- **框架**：uni-app (Vue 2.6.14)
- **UI组件**：uni-app官方组件 + 自定义组件
- **样式预处理**：SCSS
- **状态管理**：Vue.js响应式数据
- **图表组件**：qiun-data-charts

### 后端服务
- **主后台**：PHP API (https://sport.xwykj.com)
- **云服务**：微信云开发 (wx.cloud)

### 第三方服务
- **AI服务**：AI运动识别插件

### 架构特点
- **分包加载**：采用分包架构，优化加载性能
- **模块化设计**：功能模块独立，便于维护
- **响应式设计**：适配多种设备屏幕
- **性能优化**：图片懒加载、数据缓存、分包预加载

---

## 📁 项目结构

```
step-uniapp-wxcloud/
├── 📁 pages/                  # 页面文件（分包架构）
│   ├── 📁 index/              # 首页
│   ├── 📁 activity/           # 健步走活动模块
│   │   ├── 📁 user/           # 用户端页面
│   │   └── 📁 admin/          # 管理端页面
│   ├── 📁 games/              # 游戏中心
│   │   ├── 📁 2048/           # 2048游戏
│   │   ├── 📁 aircraft-battle/# 飞机大战
│   │   └── 📁 ...             # 其他游戏
│   ├── 📁 user/               # 用户中心
│   ├── 📁 voice/              # 朗诵活动
│   ├── 📁 clock_in/           # 打卡功能
│   ├── 📁 vote/               # 投票系统
│   ├── 📁 shop/               # 商城系统
│   ├── 📁 ai_sport/           # AI运动
│   └── 📁 ...                 # 其他功能模块
├── 📁 components/             # 公共组件
│   ├── 📁 active-share/       # 活动分享组件
│   ├── 📁 ranking-podium/     # 排行榜组件
│   ├── 📁 step-bar-chart/     # 步数图表组件
│   └── 📁 ...                 # 其他组件
├── 📁 utils/                  # 工具函数
│   ├── 📁 api/                # API接口封装
│   │   ├── xwy_api.js        # 主要API接口
│   │   └── uniapp_api.js     # uni-app API封装
│   ├── 📁 utils.js           # 通用工具函数
│   └── 📁 uni-api.js         # uni-app工具函数
├── 📁 config/                 # 配置文件
│   ├── config.js             # 主配置文件
│   └── activity-page-path.js # 活动页面路径配置
├── 📁 static/                 # 静态资源
│   └── 📁 style/             # 样式文件
├── 📁 uni_modules/            # uni-app插件模块
└── 📄 核心配置文件
    ├── App.vue               # 应用入口文件
    ├── main.js               # 项目入口文件
    ├── manifest.json         # 应用配置
    └── pages.json            # 页面路由配置
```

### 主要模块说明

#### 📱 核心模块
- **pages/activity**：健步走活动核心功能
- **pages/games**：游戏中心，包含20+种小游戏
- **pages/user**：用户中心和个人信息管理
- **pages/ai_sport**：AI运动识别和指导

#### 🎮 功能模块
- **pages/voice**：朗诵活动和录音功能
- **pages/clock_in**：多种打卡活动
- **pages/vote**：投票评选系统
- **pages/shop**：积分商城和订单管理

---

## 🚀 快速开始

### 环境要求

- **Node.js**: >= 14.0.0
- **npm**: >= 6.0.0
- **HBuilderX**: >= 3.0.0
- **微信开发者工具**: >= 1.05.2107300

### 安装依赖

```bash
# 克隆项目
git clone <项目地址>
cd step-uniapp-wxcloud

# 安装依赖
npm install
```

### 开发环境配置

1. **HBuilderX配置**
   - 打开HBuilderX，导入项目
   - 配置运行小程序到微信开发者工具
   - 在manifest.json中配置微信小程序AppID

2. **微信开发者工具配置**
   - 打开微信开发者工具
   - 导入项目目录

3. **环境变量配置**
   ```javascript
   // App.vue 中配置环境
   ext_conf = {
       who: 288, // 项目标识
       apihost: "https://sport.xwykj.com", // API域名
   }
   ```

### 运行项目
- **HBuilderX中运行**: 选择"运行" → "运行到小程序模拟器" → "微信开发者工具"

### 构建发布
- **HBuilderX中发布**: 选择"发行" → "小程序-微信"

---

## 📖 开发指南

### 代码规范

#### Vue组件规范
```vue
<template>
  <view class="container">
    <!-- 模板内容 -->
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 数据属性
    }
  },
  onLoad() {
    // 页面加载
  },
  methods: {
    // 方法定义
  }
}
</script>

<style lang="scss" scoped>
.container {
  /* 样式定义 */
}
</style>
```

#### API调用规范
```javascript
// 使用封装的API方法
this.xwy_api.request({
  url: 'api/endpoint',
  data: {
    // 请求参数
  },
  success: (res) => {
    // 成功回调
  },
  fail: (err) => {
    // 失败回调
  }
})

// async/await 使用
const res = await this.xwy_api.request({
  url: 'api/endpoint',
  data: {
    // 请求参数
  }
})
```

### 页面开发

#### 创建新页面
1. 在对应分包目录下创建.vue文件
2. 在pages.json中配置页面路由
3. 遵循分包大小限制（单个分包不超过2M）

#### 组件开发
1. 公共组件放在components目录
2. 页面特定组件放在对应页面目录
3. 组件命名使用kebab-case格式

### 样式开发

#### 样式规范
- 使用SCSS预处理器
- 公共样式在static/style/public.css
- 组件样式使用scoped属性
- 遵循BEM命名规范

#### 主题配置
```scss
// 主题色彩变量
$primary-color: #72c3fb;
$success-color: #4cd964;
$warning-color: #f0ad4e;
$error-color: #dd524d;
```

---

## 🔧 配置说明

### 核心配置文件

#### manifest.json
```json
{
  "name": "健步走",
  "appid": "__UNI__27550CD",
  "versionName": "1.0.0",
  "mp-weixin": {
    "appid": "wx0921894817922c7d",
    "setting": {
      "urlCheck": true,
      "es6": true
    }
  }
}
```

#### pages.json
- 配置页面路由和分包
- 设置页面样式和导航栏
- 配置分包预加载规则

#### config/config.js
- 活动相关配置
- API域名配置
- 功能开关配置
---

## 🚀 部署说明

### 小程序发布流程

1. **代码审查**
   - 检查代码规范
   - 测试功能完整性
   - 性能优化检查

2. **构建打包**
   ```bash
   # HBuilderX中操作
   1. 点击"发行" → "小程序-微信"
   2. 选择构建版本
   3. 等待构建完成
   ```

3. **上传代码**
   - 在微信开发者工具中上传代码
   - 填写版本号和项目备注
   - 提交代码审核

4. **提交审核**
   - 在微信小程序后台提交审核
   - 填写审核信息
   - 等待审核结果

5. **发布上线**
   - 审核通过后发布上线
   - 通知用户更新版本

---

## ⚠️ 注意事项

### 开发注意事项

#### 微信小程序限制
- **分包大小**：单个分包不超过2M，整个小程序不超过20M
- **请求数量**：并发请求数不超过10个
- **缓存限制**：本地缓存不超过10M
- **页面栈**：页面栈深度不超过10层

#### 性能优化
- **图片优化**：压缩图片大小，使用webp格式
- **分包加载**：合理配置分包，预加载重要分包
- **数据缓存**：合理使用缓存，减少网络请求
- **代码分割**：按需加载，减少主包体积
---

## 🤝 贡献指南


### 代码规范

- 遵循项目现有代码风格
- 添加必要的注释
- 确保代码通过测试
- 更新相关文档

---