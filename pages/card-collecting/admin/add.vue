<template>
    <view class="page">
        <view v-if="success" class="save-success bg-white">
            <view class="icon text-center">
                <uni-icons type="checkbox-filled" size="100" color="#19be6b"/>
                <view class="color-content font16">活动{{ form_data.active_id ? '修改' : '创建' }}成功！</view>
            </view>
            <view class="flex-all-center">
                <button class="bg-success color-white" style="width: 200px;" @click="lookMyActivityList">查看我的活动</button>
            </view>
        </view>

        <view class="type-list flex-row bg-white">
            <view
                class="type-item color-content"
                v-for="(item, index) in type_list"
                :key="index"
                :class="{'active-type': item.id === type_id}"
                :style="{width: 'calc(100% / ' + type_list.length + ')'}"
                @click="type_id = item.id"
            >
                {{ item.title }}
            </view>
        </view>

        <add-activity-tips ref="add_activity_tips"/>

        <view class="form">
            <template v-if="type_id === 1">
                <view class="form-item">
                    <view class="top color-content">
                        <text>活动名称</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="text" v-model="form_data.name" placeholder="请输入活动名称"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">主办方</view>
                    <view class="bottom font16">
                        <input
                            class="input"
                            type="text"
                            v-model="form_data.organizer"
                            placeholder="请输入主办方单位名称"
                        />
                    </view>
                </view>

                <template v-if="!form_data.active_id">
                    <view class="form-item">
                        <view class="top color-content">
                            <text>手机号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input
                                class="input"
                                type="number"
                                v-model="form_data.mobile"
                                placeholder="请输入真实手机号"
                            />
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <text>微信号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input
                                class="input"
                                type="text"
                                v-model="form_data.wechat_num"
                                placeholder="请输入真实微信号"
                            />
                        </view>
                    </view>
                </template>

                <view class="form-item">
                    <view class="top color-content">活动开始时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.begin_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">活动结束时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.end_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view v-if="!conf.active.news.news_id" class="form-item">
                    <view class="top color-content">活动说明</view>
                    <view class="bottom font16">
                        <textarea
                            class="textarea"
                            maxlength="-1"
                            auto-height
                            v-model="form_data.content"
                            placeholder="请输入活动说明"
                        />
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>活动说明文章</view>
                        <view class="font12 color-sub">绑定文章后，活动说明将会显示文章内容，不会显示上面填写的活动说明
                        </view>
                    </view>
                    <view class="bottom font16">
                        <view class="flex-kai" @click="toSelNews('content')">
                            <view class="view">
                                <view v-if="conf.active.news.news_id">
                                    {{ conf.active.news.news_title || conf.active.news.news_id }}
                                </view>
                                <view v-else class="color-sub">选择文章</view>
                            </view>
                            <view class="flex-all-center">
                                <view
                                    v-if="conf.active.news.news_id"
                                    class="color-sub font12"
                                    style="width: 30px;"
                                    @click.stop="deleteNews('content')"
                                >解绑</view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">活动说明是否显示活动规则</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker class="view" :range="['显示', '不显示']"
                                    :value="conf.active.activity_rules_hide"
                                    @change="conf.active.activity_rules_hide = Number($event.detail.value)">
                                {{ conf.active.activity_rules_hide ? '不显示' : '显示' }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>
            </template>


            <template v-if="type_id === 3">
                <view class="form-item">
                    <view class="top color-content">活动参与方式</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.enter_types_list"
                                :value="form_options.enter_types_list.findIndex(v => v.value === conf.active.enter_types)"
                                range-key="title"
                                @change="conf.active.enter_types = form_options.enter_types_list[$event.detail.value].value"
                            >
                                {{ form_options.enter_types_list.find(v => v.value === conf.active.enter_types).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="conf.active.enter_types === 2" class="form-item">
                    <view class="top color-content">
                        <text>活动密码</text>
                        <text v-if="!have_password" class="color-error font16"> *</text>
                        <text class="color-sub font12 pl5">(3-20位)</text>
                    </view>
                    <view class="bottom font16">
                        <input
                            class="input"
                            type="text"
                            v-model="conf.active.password"
                            :maxlength="20"
                            :placeholder="have_password ? '已设置密码，无需修改密码不用填写' : '请输入活动密码'"
                        />
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>参与活动需要填写的信息</view>
                        <view class="font12 color-error">注意: 第一项为排行榜显示的姓名, 无法删除</view>
                    </view>
                    <view class="bottom font16">
                        <view class="ptm5">
                            <view
                                class="ptm5 must-submit-list"
                                v-for="(item, index) in conf.must_submit"
                                :key="index"
                            >
                                <view class="flex-row">
                                    <view class="flex-row">
                                        <view style="width: 120px;">
                                            <uni-easyinput v-model="item.title" placeholder="请输入内容"/>
                                        </view>
                                        <template>
                                            <template v-if="index === 0">
                                                <picker
                                                    :range="['选填', '必填']"
                                                    :value="item.rules"
                                                    @change="mustItemRulesChange($event, index)"
                                                >
                                                    <view class="must-rules-picker">
                                                        <text class="color-content font14">
                                                            {{ item.rules === 0 ? '选填' : '必填' }}
                                                        </text>
                                                        <text class="iconfont icon-more color-sub font14"></text>
                                                    </view>
                                                </picker>
                                                <view class="must-rules-picker">
                                                    <text class="color-content font14">
                                                        {{ item.types === 1 ? '文本' : '单选' }}
                                                    </text>
                                                    <text class="iconfont icon-more color-white font14"></text>
                                                </view>
                                            </template>
                                            <template v-else>
                                                <picker
                                                    :range="['选填', '必填']"
                                                    :value="item.rules"
                                                    @change="mustItemRulesChange($event, index)"
                                                >
                                                    <view class="must-rules-picker">
                                                        <text class="color-content font14">
                                                            {{ item.rules === 0 ? '选填' : '必填' }}
                                                        </text>
                                                        <text class="iconfont icon-more color-sub font14"></text>
                                                    </view>
                                                </picker>
                                                <picker
                                                    :range="['文本', '单选']"
                                                    :value="item.types - 1"
                                                    @change="mustItemTypesChange($event, index)"
                                                >
                                                    <view class="must-rules-picker">
                                                        <text class="color-content font14">
                                                            {{ item.types === 1 ? '文本' : '单选' }}
                                                        </text>
                                                        <text class="iconfont icon-more color-sub font14"></text>
                                                    </view>
                                                </picker>
                                            </template>
                                        </template>
                                    </view>
                                    <view
                                        v-if="index !== 0"
                                        class="delete-rules font14 color-error"
                                        @click="conf.must_submit.splice(index, 1)"
                                    >删除</view>
                                </view>

                                <view v-if="item.types === 2" class="pl10">
                                    <view
                                        class="must-options-item flex-row"
                                        v-for="(item_, index_) in item.options"
                                        :key="index_"
                                    >
                                        <view
                                            class="color-sub delete-rules text-right"
                                            style="width: 20px; padding: 0 5px 0 0;"
                                        >{{ index_ + 1 }}:</view>
                                        <view style="width: 200px;">
                                            <uni-easyinput v-model="item_.text" placeholder="请输入内容"/>
                                        </view>
                                        <view class="delete-rules">
                                            <text
                                                class="color-error font14"
                                                @click="deleteOptionsItem(index, index_)"
                                            >删除</text>
                                        </view>
                                    </view>
                                    <view class="flex-row">
                                        <view class="color-sub font14 ptm5" @click="addOption(index)">
                                            + 添加新选项
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <view class="flex-row">
                                <view class="color-primary font14 ptm5" @click="addMust">+ 添加新项</view>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="rank_set.team_group_open" class="form-item">
                    <view class="top color-content">报名是否必须选择队伍</view>
                    <view class="bottom font16">

                        <picker :range="['非必选', '必选']" :value="conf.active.team_required"
                                @change="conf.active.team_required = Number($event.detail.value)">
                            <view class="flex-kai">
                                <view class="view">{{ conf.active.team_required ? '' : '非' }}必选</view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </picker>
                    </view>
                </view>

                <template v-if="rank_set.active_details_notice">
                    <view class="form-item">
                        <view class="top color-content">
                            <view>是否开启活动阅读须知</view>
                            <view class="color-sub font14">开启后，用户需要阅读并同意才能进入活动</view>
                        </view>
                        <view class="bottom font16">
                            <view class="flex-kai">
                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="form_options.active_details_notice_open_opt"
                                    :value="form_options.active_details_notice_open_opt.findIndex(v => v.value === conf.active.active_details_notice.open)"
                                    range-key="title"
                                    @change="conf.active.active_details_notice.open = form_options.active_details_notice_open_opt[$event.detail.value].value"
                                >
                                    {{ form_options.active_details_notice_open_opt.find(v => v.value === conf.active.active_details_notice.open).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-if="conf.active.active_details_notice.open" class="form-item">
                        <view class="top color-content">阅读须知文章</view>
                        <view class="bottom font16">
                            <view class="flex-kai" @click="toSelNews('notice')">
                                <view class="view">
                                    <view v-if="conf.active.active_details_notice.news_id">
                                        {{ conf.active.active_details_notice.news_title || conf.active.active_details_notice.news_id }}
                                    </view>
                                    <view v-else class="color-sub">选择文章</view>
                                </view>
                                <view class="flex-all-center">
                                    <view
                                        v-if="conf.active.active_details_notice.news_id"
                                        class="color-sub font12"
                                        style="width: 30px;"
                                        @click.stop="deleteNews('notice')"
                                    >解绑</view>
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-if="conf.active.active_details_notice.open" class="form-item">
                        <view class="top color-content">阅读须知确定按钮文字</view>
                        <view class="bottom font16">
                            <input
                                class="input"
                                type="text"
                                maxlength="10"
                                v-model="conf.active.active_details_notice.confirm_text"
                                placeholder="不填默认为“确定”"
                            />
                        </view>
                    </view>
                </template>

            </template>

            <template v-if="type_id === 2">
                <view class="form-item">
                    <view class="top color-title">
                        <text>活动主图</text>
                        <text class="font12 color-sub pl5">(设置活动缩略图及活动详情顶部图片)</text>
                        <view class="font12 color-sub">图片尺寸建议: 750*430</view>
                    </view>
                    <view style="padding-top: 5px;">
                        <view class="image-view" v-if="form_data.logo">
                            <image
                                class="image-item"
                                :src="form_data.logo"
                                mode="aspectFill"
                                @click="previewImage([form_data.logo])"
                            />
                            <view class="del-image-item" @click.stop="form_data.logo = ''">
                                <uni-icons type="closeempty" color="#e20f04"/>
                            </view>
                        </view>

                        <view v-else class="add-image text-center" @click="changeImage('logo')">
                            <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-title">
                        <text>活动页面开屏大图</text>
                        <view class="font12 color-sub">图片尺寸建议: 780*1387</view>
                    </view>
                    <view style="padding-top: 5px;">
                        <view class="image-view image-view-big" v-if="conf.active.screen_pic">
                            <image
                                class="image-item"
                                :src="conf.active.screen_pic"
                                mode="widthFix"
                                @click="previewImage([conf.active.screen_pic])"
                            />
                            <view class="del-image-item" @click.stop="conf.active.screen_pic = ''">
                                <uni-icons type="closeempty" color="#e20f04"/>
                            </view>
                        </view>
                        <view v-else class="add-image text-center" @click="changeImage('screen_pic')">
                            <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                        </view>
                    </view>
                </view>

                <template v-if="conf.active.screen_pic">
                    <view class="form-item">
                        <view class="top color-content">开屏大图进入活动倒计时</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    :range="form_options.screen_pic_count_down_options"
                                    :value="form_options.screen_pic_count_down_options.findIndex(v => v.value === conf.active.screen_pic_count_down)"
                                    range-key="title"
                                    @change="conf.active.screen_pic_count_down = form_options.screen_pic_count_down_options[$event.detail.value].value"
                                >
                                    {{ form_options.screen_pic_count_down_options.find(v => v.value === conf.active.screen_pic_count_down).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <view>开屏大图进入活动按钮</view>
                            <view class="color-sub font12">
                                按钮显示文字及颜色不可修改。如需自定义按钮文字及颜色，请自行在图片上增加，并选择不显示按钮。
                            </view>
                        </view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    :range="['不显示', '显示']"
                                    :value="conf.active.screen_pic_buttom_show"
                                    @change="conf.active.screen_pic_buttom_show = Number($event.detail.value)"
                                >
                                    {{ conf.active.screen_pic_buttom_show === 1 ? '显示' : '不显示' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>
                </template>


                <view class="form-item">
                    <view class="top">
                        <text class="color-title">排行榜顶部轮播图</text>
                        <text class="color-sub font12 pl5">
                            {{ conf.active.top_rank_banner.length }}/{{ top_rank_banner_max_count }}
                        </text>
                        <view class="font12 color-sub">图片尺寸建议: 640*272</view>
                    </view>
                    <view style="padding-top: 5px;">
                        <view class="flex-row flex-wrap">
                            <view
                                class="top-rank-banner-item"
                                v-for="(item, index) in conf.active.top_rank_banner"
                                :key="index"
                            >
                                <image
                                    :src="item"
                                    mode="aspectFill"
                                    @click="previewImage(conf.active.top_rank_banner, item)"
                                />
                                <view
                                    class="del-image-item"
                                    @click.stop="conf.active.top_rank_banner.splice(index, 1)"
                                >
                                    <uni-icons type="closeempty" color="#e20f04"/>
                                </view>
                            </view>
                            <view
                                v-if="conf.active.top_rank_banner.length < top_rank_banner_max_count"
                                class="add-image text-center"
                                @click="changeImage('top_rank_banner')"
                            >
                                <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-title">
                        <view>
                            <text>集卡活动背景图</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="font12 color-sub">图片尺寸建议: 780*1387</view>
                    </view>
                    <view style="padding-top: 5px;">
                        <view class="image-view image-view-big" v-if="conf.active.background_image">
                            <image
                                class="image-item"
                                :src="conf.active.background_image"
                                mode="widthFix"
                                @click="previewImage([conf.active.background_image])"
                            />
                            <view class="del-image-item" @click.stop="conf.active.background_image = ''">
                                <uni-icons type="closeempty" color="#e20f04"/>
                            </view>
                        </view>
                        <view v-else class="add-image text-center" @click="changeImage('background_image')">
                            <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                        </view>
                    </view>
                </view>
            </template>

            <template v-if="type_id === 6">
                <view v-if="rank_set['openNFC']" class="form-item">
                    <view class="top color-content">NFC标签自动盖章集卡</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker class="view" :range="['否', '是']"
                                    :value="conf.active.auto_stamp"
                                    @change="conf.active.auto_stamp = Number($event.detail.value)">
                                {{ conf.active.auto_stamp ? '是' : '否' }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">是否只能通过扫集卡点二维码进入才能集卡</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                :range="['否', '是']"
                                :value="conf.active.just_qrcode_sign"
                                @change="conf.active.just_qrcode_sign = Number($event.detail.value)"
                            >
                                {{ conf.active.just_qrcode_sign ? '是' : '否' }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">是否在卡片所在位置附近才能集卡</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.clock_in_types_list"
                                :value="form_options.clock_in_types_list.findIndex(v => v.value === conf.active.clock_in_on_site.open)"
                                range-key="title"
                                @change="conf.active.clock_in_on_site.open = form_options.clock_in_types_list[$event.detail.value].value"
                            >
                                {{form_options.clock_in_types_list.find(v => v.value === conf.active.clock_in_on_site.open).title}}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="conf.active.clock_in_on_site.open" class="form-item">
                    <view class="top color-content">
                        <text>集卡范围</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view class="bottom font16 view flex-row" style="line-height: 26px;">
                        <view class="font14 pr5">需在卡片附近</view>
                        <uni-number-box
                            :min="0.01"
                            :max="9999"
                            :step="0.01"
                            v-model="conf.active.clock_in_on_site.distance"
                        ></uni-number-box>
                        <view class="font14 pl5">公里内才能集卡</view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">按卡片顺序集卡</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.sequential_clock_in"
                                :value="form_options.sequential_clock_in.findIndex(v => v.value === conf.active.sequential_clock_in)"
                                range-key="title"
                                @change="conf.active.sequential_clock_in = form_options.sequential_clock_in[$event.detail.value].value"
                            >
                                {{form_options.sequential_clock_in.find(v => v.value === conf.active.sequential_clock_in).title}}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">集卡记录是否需要审核后才能显示在广场</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.not_check_list"
                                :value="form_options.not_check_list.findIndex(v => v.value === conf.active.not_check)"
                                range-key="title"
                                @change="conf.active.not_check = form_options.not_check_list[$event.detail.value].value"
                            >
                                {{form_options.not_check_list.find(v => v.value === conf.active.not_check).title}}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">集卡次数</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.sign_times_list"
                                :value="form_options.sign_times_list.findIndex(v => v.value === conf.active.sign_times_type)"
                                range-key="title"
                                @change="conf.active.sign_times_type = form_options.sign_times_list[$event.detail.value].value"
                            >
                                {{form_options.sign_times_list.find(v => v.value === conf.active.sign_times_type).title}}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="conf.active.sign_times_type === 1" class="form-item">
                    <view class="top color-content">
                        <view>统计用户集卡用时</view>
                        <view class="color-sub font12">
                            开启后, 会统计用户从第一个点到最后一个点集卡完成累计耗费的集卡时间。
                        </view>
                    </view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker class="view" :range="['关闭', '开启']"
                                    :value="conf.active.record_used_time"
                                    @change="conf.active.record_used_time = Number($event.detail.value)">
                                {{ conf.active.record_used_time ? '开启' : '关闭' }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">集卡备注填写设置</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.memo_required_list"
                                :value="form_options.memo_required_list.findIndex(v => v.value === conf.active.memo_required)"
                                range-key="title"
                                @change="conf.active.memo_required = form_options.memo_required_list[$event.detail.value].value"
                            >
                                {{form_options.memo_required_list.find(v => v.value === conf.active.memo_required).title}}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="conf.active.memo_required" class="form-item">
                    <view class="top color-content">集卡备注文本框提示语</view>
                    <view class="bottom font16">
                        <input class="input" maxlength="100" v-model="conf.active.memo_placeholder"
                               placeholder="不填默认为系统提示语"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">集卡获取微信运动步数设置</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.step_required_list"
                                :value="form_options.step_required_list.findIndex(v => v.value === conf.active.step_required)"
                                range-key="title"
                                @change="conf.active.step_required = form_options.step_required_list[$event.detail.value].value"
                            >
                                {{form_options.step_required_list.find(v => v.value === conf.active.step_required).title}}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="conf.active.step_required" class="form-item">
                    <view class="top color-content">
                        <view>最低运动步数限制</view>
                        <view class="color-sub font12">(需要达到指定步数才能集卡，0或不填不限制步数)</view>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="number" maxlength="6" v-model="conf.active.min_step" placeholder="0或不填不限制步数"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">集卡上传位置设置</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.address_required_list"
                                :value="form_options.address_required_list.findIndex(v => v.value === conf.active.address_required)"
                                range-key="title"
                                @change="conf.active.address_required = form_options.address_required_list[$event.detail.value].value"
                            >
                                {{form_options.address_required_list.find(v => v.value === conf.active.address_required).title}}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">集卡图片上传设置</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.pic_list_required_list"
                                :value="form_options.pic_list_required_list.findIndex(v => v.value === conf.active.pic_list_required)"
                                range-key="title"
                                @change="conf.active.pic_list_required = form_options.pic_list_required_list[$event.detail.value].value"
                            >
                                {{form_options.pic_list_required_list.find(v => v.value === conf.active.pic_list_required).title}}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>


                <view class="form-item">
                    <view class="top color-content">是否允许修改集卡记录</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.records_not_edit_list"
                                :value="form_options.records_not_edit_list.findIndex(v => v.value === conf.active.records_not_edit)"
                                range-key="title"
                                @change="conf.active.records_not_edit = form_options.records_not_edit_list[$event.detail.value].value"
                            >
                                {{form_options.records_not_edit_list.find(v => v.value === conf.active.records_not_edit).title}}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>


            </template>
            
            <template v-if="type_id === 5">
                <view class="form-item">
                    <view class="top color-content">意见反馈入口</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker class="view" :range="['开启', '关闭']" :value="conf.active.close_feedback"
                                    @change="conf.active.close_feedback = Number($event.detail.value)">
                                {{ conf.active.close_feedback ? '关闭' : '开启' }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <template v-if="rank_set['gift_goods']">
                    <view class="form-item">
                        <view class="top color-content">是否显示积分商城入口</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    :range="['显示', '隐藏']"
                                    :value="conf.active.gift_goods_hide"
                                    @change="conf.active.gift_goods_hide = Number($event.detail.value)"
                                >
                                    {{ conf.active.gift_goods_hide ? '隐藏' : '显示' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <text>积分单位名称</text>
                            <text class="font12 color-sub pl5">(默认: {{ default_integral_unit }})</text>
                        </view>
                        <view class="bottom font16">

                            <input class="input" type="text" v-model="conf.active.integral.unit"
                                   :placeholder="'不填默认为 ' + default_integral_unit"/>
                        </view>
                    </view>

                    <template v-if="rank_set['other_add_sport_step']">
                        <view class="form-item">
                            <view class="top color-content">步数奖励{{ integralUnit }}设置</view>
                            <view class="bottom font16 flex-row pt10 pb10">
                                <view style="width: 100px; padding-right: 2px;">
                                    <uni-easyinput type="number" maxlength="8" trim
                                                   v-model="conf.active.integral.exchange_step"/>
                                </view>
                                <view style="line-height: 36px;">步奖励1{{ integralUnit }}</view>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">最低奖励步数</view>
                            <view class="bottom font16">
                                <input class="input" type="number" maxlength="8"
                                       v-model="conf.active.min_num"
                                       placeholder="低于设置的步数不奖励,0或不填代表不限制"/>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">每天步数兑换上限</view>
                            <view class="bottom font16">
                                <input class="input" type="number" maxlength="8"
                                       v-model="conf.active.max_num"
                                       placeholder="单个用户每天奖励的步数上限,0或不填代表不设上限"/>
                            </view>
                        </view>
                    </template>
                </template>

                <view class="form-item">
                    <view class="top color-content">活动详情页面图标扩展</view>
                    <view class="bottom font16 pt5 pb5">
                        <view
                            class="flex-kai pl10 pr10"
                            style="border: 1px solid #eee; border-radius: 5px; margin-bottom: 5px;"
                            v-for="(item, index) in conf.active.detail_icon_list"
                            :key="index"
                        >
                            <view>
                                <view style="padding: 5px 0;" @click="selIcon(index)">
                                    <text class="color-content pr5">显示图标:</text>
                                    <text :class="'iconfont font28 color-primary ' + item.icon"></text>
                                    <text class="pl5 color-sub font12">(点击更换)</text>
                                </view>

                                <picker :range="detail_icon_conf" range-key="title"
                                        @change="detailIconItemChange($event, index)">
                                    <view style="padding: 5px 0;">
                                        <text class="pr5 color-content">跳转类型:</text>
                                        <text>{{ detail_icon_conf[item.type].title }}</text>
                                    </view>
                                </picker>
                                <view class="flex-row" style="padding: 5px 0;">
                                    <text class="pr5 color-content">显示文字:</text>
                                    <input v-model="item.title" placeholder="6个字以内"
                                           style="border-bottom: 1px solid #eee;"/>
                                </view>
                                <view style="padding: 5px 0;" @click="bindNewsOrCategory(index)">
                                    <text class="pr5 color-content">
                                        绑定{{ item.type === 0 ? '文章' : '分类' }}:
                                    </text>
                                    <template>
                                        <text v-if="item.id">{{ item.name }}</text>
                                        <text class="color-sub" v-else>
                                            请选择{{ item.type === 0 ? '文章' : '分类' }}
                                        </text>
                                    </template>
                                </view>
                            </view>
                            <view class="pt5" @click="delDetailIcon(index)">
                                <text class="iconfont icon-delete color-error font20"></text>
                            </view>
                        </view>

                        <view class="color-primary pt5 pb5" @click="addDetailIcon">添加扩展</view>
                    </view>
                </view>

                <active-share-set :active-id="form_data.active_id" :rank-set="rank_set"
                                  :qrcode-logo.sync="conf.active.qrcode_logo"
                                  :share-image.sync="conf.active.share_image"
                                  :share-title.sync="conf.active.share_title"/>
            </template>

        </view>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view
                class="login-btn color-white text-center font18 bg-primary"
                :disabled="loading"
                @click="save"
            >{{ form_data.active_id ? '保存' : '创建活动' }}</view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import base64 from '@/utils/base64.js'
import { pinyin  } from 'pinyin-pro'


export default {
    data() {
        return {
            loading: true,
            success: false,
            type_list: [
                {title: '基本信息', id: 1},
                {title: '活动设置', id: 3},
                {title: '集卡设置', id: 6},
                {title: '扩展设置', id: 5},
                {title: '活动图片', id: 2}
            ],
            type_id: 1,
            form_data: {
                name: '',
                wechat_num: '',
                mobile: '',
                organizer: '',
                content: '',
                begin_time: new Date(utils.getDay(0, true, '/') + ' 00:00:00').getTime(),
                end_time: new Date(utils.getDay(30, true, '/') + ' 23:59:59').getTime(),
                logo: ''
            },
            conf: {
                active: {
                    news: {
                        news_id: '',
                        news_title: ''
                    },
                    enter_types: 1,
                    password: '',
                    screen_pic: '',
                    top_rank_banner: [],
                    active_details_notice: {
                        open: 0,
                        news_id: '',
                        news_title: '',
                        confirm_text: '确定'
                    },
                    clock_in_on_site: {
                        open: 1,
                        distance: 3
                    },
                    sequential_clock_in: 0,
                    background_image: '',  // 集卡的背景图
                    not_check: 1,
                    step_required: 0,
                    min_step: '',
                    address_required: 0,
                    memo_required: 0,
                    memo_placeholder: '',
                    pic_list_required: 0,
                    records_not_edit: 1,
                    sign_times_type: 0,
                    video_list_required: 0,  // 很少活动需要上传视频的，所以默认都不能选视频上传了

                    share_title: '',
                    share_image: '',
                    qrcode_logo: '',
                    auto_stamp: 0,
                    detail_icon_list: [],

                    screen_pic_count_down: 5,
                    screen_pic_buttom_show: 1,

                    integral: {
                        unit: '积分',
                        exchange_step: ''
                    },
                    min_num: '',
                    max_num: '',
                    gift_goods_hide: 0,
                    activity_rules_hide: 0,
                    team_required: 0,
                    close_feedback: 0,
                    record_used_time: 0,

                    just_qrcode_sign: 0
                },
                must_submit: JSON.parse(JSON.stringify(this.xwy_config.defaultMustSubmit))
            },
            top_rank_banner_max_count: 6,
            rank_set: {},
            have_password: false,
            form_options: {
                enter_types_list: [
                    {title: '自由报名参与活动', value: 1},
                    {title: '需要输入密码才能进入活动', value: 2},
                    {title: '报名需要审核通过才能参与活动', value: 3}
                ],
                active_details_notice_open_opt: [
                    {value: 1, title: '开启'},
                    {value: 0, title: '关闭'}
                ],
                clock_in_types_list: [
                    {value: 1, title: '是'},
                    {value: 0, title: '否'}
                ],
                sequential_clock_in: [
                    {value: 0, title: '关闭'},
                    {value: 1, title: '开启'}
                ],
                memo_required_list: [
                    {value: 0, title: '关闭填写备注'},
                    {value: 2, title: '开启填写备注'},
                    {value: 1, title: '必须填写备注'}
                ],
                pic_list_required_list: [
                    {value: 0, title: '关闭上传图片'},
                    {value: 2, title: '开启上传图片'},
                    {value: 1, title: '必须上传图片'}
                ],
                step_required_list: [
                    {value: 0, title: '关闭上传微信运动步数'},
                    {value: 1, title: '开启上传微信运动步数'}
                ],
                address_required_list: [
                    {value: 0, title: '关闭上传当前地址'},
                    {value: 2, title: '开启上传当前地址'},
                    {value: 1, title: '必须上传当前地址'}
                ],
                sign_times_list: [
                    {value: 0, title: '不限制集卡次数'},
                    {value: 1, title: '每个点只能集卡一次'},
                    {value: 2, title: '每个点每天只能集卡一次'}
                ],
                records_not_edit_list: [
                    {value: 0, title: '是'},
                    {value: 1, title: '否'}
                ],
                not_check_list: [
                    {value: 0, title: '需要审核'},
                    {value: 1, title: '无需审核'}
                ],
                screen_pic_count_down_options: (function () {
                    const list = [{value: 0, title: '关闭倒计时'}]
                    for (let i = 1; i <= 30; i++) {
                        list.push({value: i, title: i + '秒'})
                    }
                    return list
                })(),
            },
            detail_icon_conf: [
                {
                    type: 0,
                    title: '文章详情'
                }
            ],
            default_integral_unit: '积分',
        }
    },

    computed: {
        integralUnit() {
            return this.conf?.active?.integral.unit || this.default_integral_unit
        }
    },

    onLoad(e) {
        if (e.id) {
            this.form_data.active_id = e.id
        } else {
            this.types = Number(e.types)
            this.$nextTick(() => this.$refs.add_activity_tips.open(this.types))
            this.form_data.logo = this.xwy_config.create_active_default_logo
        }
        uni.setNavigationBarTitle({
            title: e.id ? '修改活动' : `创建 ${e.name || '活动'}`
        })
        uni.showLoading({
            mask: true
        })

        login.uniLogin(err => {

            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }


            if (e.id) {
                this.getDetail()
                return false
            }

            const evn_version = app.globalData['evn_version']

            if (evn_version === 'develop') {
                this.form_data.mobile = 12345678910
                this.form_data.wechat_num = 12345678910
                this.form_data.organizer = '伟杰测试'
            }

            // 开发版和体验版不检查重复创建活动
            if (evn_version === 'develop' || evn_version === 'trial') {
                uni.hideLoading()
                return false
            }

            this.checkUserCanCreateActive()
        })
    },
    methods: {
        checkUserCanCreateActive() {

            xwy_api.ajax({
                url: 'front.flat.sport_step.admin/check_user_can_create_active',
                data: {
                    access_token: app.globalData['access_token']
                },
                success: res => {
                    console.log('检查是否能创建活动', res)
                    uni.hideLoading()
                    this.loading = false
                    if (!res.status) {
                        uni.showModal({
                            title: '提示',
                            content: res.info || '暂时不能创建活动',
                            showCancel: false,
                            success: () => {
                                uni.navigateBack()
                            }
                        })
                    }
                }
            })

        },

        getDetail() {
            const errModal = content => {
                uni.hideLoading()
                uni.showModal({
                    title: '提示',
                    content: content || '活动获取失败',
                    showCancel: false,
                    success: () => {
                        uni.navigateBack()
                    }
                })
            }

            xwy_api.getActivityDetail(this.form_data.active_id, res => {
                if (res.data?.['active_details']) {
                    const detail = res.data['active_details']

                    this.detailInit(detail)
                } else {
                    errModal(res.info)
                }
            })
        },


        detailInit(data) {
            this.types = data.types
            this.form_data.name = data.name
            this.form_data.begin_time = data.begin_time * 1000
            this.form_data.end_time = data.end_time * 1000
            if (data.organizer) this.form_data.organizer = data.organizer
            if (data.logo) this.form_data.logo = data.logo
            if (data.content) this.form_data.content = data.content

            const conf = data.conf
            if (conf.must_submit?.length) this.conf.must_submit = conf.must_submit

            const active = conf.active

            this.conf.active.enter_types = active.enter_types || 1
            if (active.news) this.conf.active.news = active.news
            if (active.screen_pic) this.conf.active.screen_pic = active.screen_pic
            if (active.top_rank_banner) this.conf.active.top_rank_banner = active.top_rank_banner
            if (active.active_details_notice) this.conf.active.active_details_notice = active.active_details_notice
            if (active.password) {
                this.old_password = active.password
                this.have_password = true
            }


            this.conf.active.memo_required = active.memo_required
            if (active.memo_placeholder) this.conf.active.memo_placeholder = active.memo_placeholder
            this.conf.active.pic_list_required = active.pic_list_required
            if (active.sign_times_type) this.conf.active.sign_times_type = active.sign_times_type
            if (active.records_not_edit) this.conf.active.records_not_edit = active.records_not_edit
            this.conf.active.not_check = active.not_check
            if (active.address_required) this.conf.active.address_required = active.address_required
            if (active.step_required) this.conf.active.step_required = active.step_required
            if (active.min_step) this.conf.active.min_step = active.min_step
            if (active.sequential_clock_in) this.conf.active.sequential_clock_in = active.sequential_clock_in
            if (active.clock_in_on_site) this.conf.active.clock_in_on_site = active.clock_in_on_site
            if (active.background_image) this.conf.active.background_image = active.background_image

            if (active.share_title) this.conf.active.share_title = active.share_title
            if (active.share_image) this.conf.active.share_image = active.share_image
            if (active.qrcode_logo) this.conf.active.qrcode_logo = active.qrcode_logo
            if (active.auto_stamp) this.conf.active.auto_stamp = active.auto_stamp
            if (active.detail_icon_list) this.conf.active.detail_icon_list = active.detail_icon_list
            if (active.screen_pic_count_down) this.conf.active.screen_pic_count_down = active.screen_pic_count_down
            if (active.hasOwnProperty('screen_pic_buttom_show')) {
                this.conf.active.screen_pic_buttom_show = active.screen_pic_buttom_show || 0
            }

            if (active.integral) this.conf.active.integral = active.integral
            if (active.min_num) this.conf.active.min_num = active.min_num
            if (active.max_num) this.conf.active.max_num = active.max_num
            if (active.gift_goods_hide) this.conf.active.gift_goods_hide = active.gift_goods_hide
            if (active.activity_rules_hide) this.conf.active.activity_rules_hide = active.activity_rules_hide
            if (active.team_required) this.conf.active.team_required = active.team_required
            if (active.close_feedback) this.conf.active.close_feedback = active.close_feedback
            if (active.record_used_time) this.conf.active.record_used_time = active.record_used_time
            if (active.just_qrcode_sign) this.conf.active.just_qrcode_sign = active.just_qrcode_sign

            if (data.rank_set) {
                const rank_set = data.rank_set

                this.rank_set = rank_set


                if (rank_set.closed_AD && rank_set.closed_AD === 1) {
                    this.detail_icon_conf.push({
                        type: 1,
                        title: '文章列表'
                    })
                }
            }

            uni.hideLoading()
        },


        addDetailIcon() {
            this.conf.active.detail_icon_list.push({
                icon: 'icon-dating',
                title: '',
                type: 0,
                id: '',
                name: ''
            })
        },
        delDetailIcon(index) {
            this.conf.active.detail_icon_list.splice(index, 1)
        },
        selIcon(index) {
            uni.navigateTo({
                url: '/pages/other/icon-list/icon-list',
                events: {
                    selIcon: class_name => {
                        this.conf.active.detail_icon_list[index].icon = class_name
                    }
                }
            })
        },
        detailIconItemChange(e, index) {
            this.conf.active.detail_icon_list[index].id = ''
            this.conf.active.detail_icon_list[index].name = ''
            this.conf.active.detail_icon_list[index].type = this.detail_icon_conf[e.detail.value].type
        },

        bindNewsOrCategory(index) {
            const item = this.conf.active.detail_icon_list[index]
            if (item.type === 0) this.bindNews(index)
            if (item.type === 1) this.bindCategory(index)
        },
        bindNews(index) {
            uni.navigateTo({
                url: '/pages/news/list?type=user&is_sel=true',
                events: {
                    selNews: data => {
                        this.conf.active.detail_icon_list[index].id = data.id
                        this.conf.active.detail_icon_list[index].name = data.title
                    }
                }
            })
        },
        bindCategory(index) {
            uni.navigateTo({
                url: '/pages/category/list?types=8&is_sel=1',
                events: {
                    selCategory: data => {
                        this.conf.active.detail_icon_list[index].id = data.id
                        this.conf.active.detail_icon_list[index].name = data.name
                    }
                }
            })
        },


        toSelNews(type) {
            uni.navigateTo({
                url: '/pages/news/list?type=user&is_sel=true',
                events: {
                    selNews: data => {
                        switch (type) {
                            case 'content':
                                this.conf.active.news.news_id = data.id
                                this.conf.active.news.news_title = data.title
                                break
                            case 'notice':
                                this.conf.active.active_details_notice.news_id = data.id
                                this.conf.active.active_details_notice.news_title = data.title
                                break
                        }

                    }
                }
            })
        },

        deleteNews(type) {
            switch (type) {
                case 'content':
                    this.conf.active.news.news_id = ''
                    this.conf.active.news.news_title = ''
                    break
                case 'notice':
                    this.conf.active.active_details_notice.news_id = ''
                    this.conf.active.active_details_notice.news_title = ''
                    break
            }
        },




        mustItemRulesChange(e, index) {
            this.conf.must_submit[index].rules = Number(e.detail.value)
        },
        mustItemTypesChange(e, index) {
            const value = Number(e.detail.value) + 1
            this.conf.must_submit[index].types = value
            if (value === 2) {
                this.conf.must_submit[index].options = this.conf.must_submit[index].options || [{text: ''}]
            }
        },
        addMust() {
            this.conf.must_submit.push({
                name: '',
                rules: 0,
                title: '',
                types: 1
            })
        },

        addOption(index) {
            this.conf.must_submit[index].options.push({text: ''})
            this.$forceUpdate()
        },
        deleteOptionsItem(index, index_) {
            if (this.conf.must_submit[index].options.length === 1) {
                uni.showToast({
                    title: '请至少保留一个选项',
                    icon: 'none',
                    mask: true
                })
                return false
            }
            this.conf.must_submit[index].options.splice(index_, 1)
            this.$forceUpdate()
        },

        changeImage(key) {
            if (!this.rank_set?.closed_AD) {
                const options = {
                    screen_pic: '无法设置开屏大图，请联系客服设置',
                    top_rank_banner: '无法设置排行榜轮播图，请联系客服设置'
                }
                if (options[key]) return this.$uni.showModal(options[key])
            }

            let url = '/pages/other/image_upload_or_select'
            if (this.form_data.active_id) url += `?active_id=${this.form_data.active_id}`
            uni.navigateTo({
                url,
                events: {
                    newImg: src => {
                        switch (key) {
                            case 'logo':
                                this.form_data.logo = src
                                break
                            case 'top_rank_banner':
                                this.conf.active.top_rank_banner.push(src)
                                break
                            default:
                                this.conf.active[key] = src
                        }
                    }
                }
            })
        },

        previewImage(urls, current = urls[0]) {
            uni.previewImage({
                urls,
                current
            })
        },


        dataCheck(data) {
            const showToast = title => {
                this.type_id = 1
                this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none')
            }

            if (!data.name) {
                showToast('请输入活动名称')
                return false
            }

            if (!data.active_id) {
                if (!data.mobile) {
                    showToast('请输入手机号')
                    return false
                }
                if (data.mobile.toString().length !== 11) {
                    showToast('手机号长度有误')
                    return false
                }
                if (!data.wechat_num) {
                    showToast('请输入微信号')
                    return false
                }
                const wechat_num = data.wechat_num.toString()
                if (wechat_num.length < 4 || wechat_num.length > 20) {
                    showToast('微信号长度有误')
                    return false
                }
                if (this._utils.isChineseChar(wechat_num)) {
                    showToast('微信号不能输入中文')
                    return false
                }
            }

            return true
        },

        setMustSubmitData() {
            const errModal = content => {
                this.type_id = 3
                uni.showModal({
                    title: '提示',
                    content,
                    showCancel: false
                })
            }


            for (let i = 0; i < this.conf.must_submit.length; i++) {
                const v = this.conf.must_submit[i]
                console.log(v)
                if (!v.title) {
                    errModal('参与活动需要填写的信息选项填写不完整，请检查。')
                    return false
                }
                v.name = pinyin(v.title, {toneType: 'none', type: 'array'}).join('_')
                if (v.types === 2) {
                    if (!v.options || !v.options.length) {
                        errModal(`${v.title} 至少需要添加一个选项。`)
                        return false
                    }
                    console.log(v.options)
                    for (let j = 0; j < v.options.length; j++) {
                        const v1 = v.options[j]
                        if (!v1 || !v1.text) {
                            errModal(`${v.title} 有未填写的选项，请检查。`)
                            return false
                        }
                    }
                }
            }

            return true
        },

        async confCheck() {
            const showToast = title => {
                this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none')
            }
            const conf = JSON.parse(JSON.stringify(this.conf))
            if (conf.active.enter_types === 2) {
                if (!this.have_password && !conf.active.password) {
                    showToast('请输入活动密码')
                    this.type_id = 3
                    return false
                }
                if (conf.active.password && conf.active.password.length < 3) {
                    showToast('活动密码不得少于3位')
                    this.type_id = 3
                    return false
                }
                if (conf.active.password && conf.active.password.length > 20) {
                    showToast('活动密码不得大于20位')
                    this.type_id = 3
                    return false
                }
                conf.active.password = conf.active.password || this.old_password
            } else {
                conf.active.password = 1
            }

            if (!this.conf.active.background_image) {
                this.type_id = 2
                return showToast('请上传集卡活动背景图')
            }

            if (!this.rank_set.openNFC && conf.active.auto_stamp) delete conf.active.auto_stamp

            const none_delete_key = ['share_title', 'share_image', 'qrcode_logo', 'auto_stamp']
            none_delete_key.forEach(key => {
                if (!conf.active[key]) delete conf.active[key]
            })

            if (!conf.active.detail_icon_list.length) delete conf.active.detail_icon_list

            if (!this.rank_set?.active_details_notice || !conf.active.active_details_notice.open || !conf.active.active_details_notice.news_id) {
                delete conf.active.active_details_notice
            }

            if (!conf.active.screen_pic) {
                delete conf.active.screen_pic
                delete conf.active.screen_pic_count_down
                delete conf.active.screen_pic_buttom_show
            }

            if (!this.rank_set.gift_goods) {
                delete conf.active.integral
                delete conf.active.gift_goods_hide
                delete conf.active.min_num
                delete conf.active.max_num
                delete conf.active.max_num
            }

            if (this.rank_set['other_add_sport_step']) {
                conf.active.exchange_rate = 1
                conf.active.kilo_unit = '步'
            } else {
                delete conf.active.integral
                delete conf.active.min_num
                delete conf.active.max_num
            }

            if (!conf.active.activity_rules_hide) delete conf.active.activity_rules_hide
            if (!this.rank_set.team_group_open || !conf.active.team_required) {
                delete conf.active.team_required
            }
            if (!conf.active.memo_placeholder) delete conf.active.memo_placeholder
            if (!conf.active.close_feedback) delete conf.active.close_feedback

            if (conf.active.sign_times_type !== 1 || !conf.active.record_used_time) {
                delete conf.active.record_used_time
            }
            if (!conf.active.just_qrcode_sign) delete conf.active.just_qrcode_sign


            let conf_str = JSON.stringify(conf)
            conf_str = conf_str.replace(/·/g, '-')
            return base64['encode'](conf_str)
        },

        async save() {
            if (this.success) return false

            const data = JSON.parse(JSON.stringify(this.form_data))

            if (!this.dataCheck(data)) return false
            if (!this.setMustSubmitData()) return false

            const conf = await this.confCheck()
            if (!conf) return false
            data.conf = conf


            data.begin_time /= 1000
            data.end_time /= 1000

            await this.saveAjax(data)
        },

        async saveAjax(data) {
            data.access_token = app.globalData['access_token']
            data.types = this.types

            this.loading = true
            uni.showLoading({
                title: '保存中...',
                mask: app.globalData['evn_version'] !== 'trial'
            })

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.admin/create_active',
                data
            })

            uni.hideLoading()
            this.loading = false

            if (!res || !res['status']) {
                xwy_api.alert(res && res['info'] || '保存失败')
                return false
            }

            this.updatePageData()

            if (data.active_id) {
                if (this.form_data.active_id) {
                    uni.showToast({
                        title: '保存成功',
                        icon: 'success',
                        mask: true
                    })
                    uni.navigateBack()
                    return false
                }
            }

            this.success = true
        },

        // 活动修改/创建成功，如果页面栈有详情或列表页面，更新页面数据
        updatePageData() {
            const pages = getCurrentPages()

            const update_page_list = [
                {
                    route: 'pages/clock_in/user/detail',
                    _function: page => {
                        page.$vm.getDetail(true)
                    }
                },
                {
                    route: 'pages/clock_in/admin/activity/manage',
                    _function: page => {
                        page.$vm.getDetail()
                    }
                },
                {
                    route: 'pages/activity/user/index',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getActivityList()
                    }
                },
                {
                    route: 'pages/activity/user/activity_list',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getList()
                    }
                }
            ]

            update_page_list.forEach(v => {
                const page = pages.find(n => n.route === v.route)
                if (page) v._function(page)
            })
        },

        lookMyActivityList() {
            const list_page_route = 'pages/activity/user/activity_list'
            const pages = getCurrentPages()
            const list_page_index = pages.findIndex(v => {
                return v.route === list_page_route && v['options'].type && v['options'].type === 'create'
            })
            if (list_page_index === -1) return this.$uni.redirectTo(`/${list_page_route}?type=create`)

            this.$uni.navigateBack(pages.length - list_page_index - 1)
        }
    }
}
</script>

<style>
.page {
    padding-top: 40px;
    padding-bottom: 75px;
}

.type-list {
    position: fixed;
    z-index: 9;
    width: 100vw;
    top: 0;
    left: 0;
}

.type-item {
    height: 40px;
    line-height: 40px;
    text-align: center;
    box-sizing: border-box;
}

.active-type {
    color: #007AFF;
    border-bottom: 2px solid #007AFF;
}

.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}


.form-item .textarea {
    width: 100%;
    line-height: 16px;
    padding: 10px 0;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}

.must-submit-list {
    border-bottom: 1px dashed #eee;
}


.must-rules-picker {
    line-height: 34px;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    margin-left: 5px;
    padding: 0 15px;
}

.delete-rules {
    line-height: 36px;
    padding: 0 10px;
}

.must-options-item {
    padding: 3px 0;
}

.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
}

.add-image {
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
    line-height: calc((100vw - 50px) / 3);
    margin: 5px;
}



.image-view {
    position: relative;
}

.image-view .image-item {
    width: calc(100vw - 20px);
    height: 50vw;
    border-radius: 5px;
    display: block;
}
.image-view-big {
    width: calc((100vw - 20px) / 2);
}
.image-view-big .image-item {
    width: calc((100vw - 20px) / 2);
    height: auto;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}


.top-rank-banner-item {
    padding: 5px;
    position: relative;
}

.top-rank-banner-item image {
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
}

.top-rank-banner-item .del-image-item {
    right: 8px;
}

.bottom-btn-view {
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.login-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}

.save-success {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 999999;
    box-sizing: border-box;
    padding-top: 10vh;
}

.save-success .icon {
    padding-bottom: 50px;
}

.uni-date-x {
    padding: 0 !important;
}

.uni-date__x-input {
    font-size: 16px !important;
    color: #000;
}

/* #ifdef H5 */

@media screen and (min-width: 500px) {
    .type-list {
        width: 500px !important;
        left: calc((100vw - 500px) / 2) !important;
    }


    .image-item {
        width: 480px !important;
        height: 200px !important;
    }

    .top-rank-banner-item image, .add-image {
        width: 150px !important;
        height: 150px !important;
        line-height: 150px !important;
    }
}

/* #endif */
</style>
