<template>
    <view>
        <view class="page bg-disabled">
            <view class="flex-kai bg-white">
                <view class="p10">
                    <button type="primary" plain style="width: 110px;" @click="pageBack">返 回</button>
                </view>
                <view class="flex-all-center color-title font16">
                    {{ point_name || ''}} 卡片位置设置
                </view>
                <view class="p10">
                    <button type="primary" @click="saveCheck" style="width: 110px;">保 存</button>
                </view>
            </view>
            <view class="flex-row">
                <view class="controls bg-white flex-all-center">
                    <canvas
                        type="2d"
                        canvas-id="canvas"
                        id="canvas"
                        :style="{width: canvas_width + 'px', height: canvas_height + 'px'}"
                        @mousemove="mousemove"
                        @mouseup="mouseup"
                        @mousedown="mousedown"
                        @error="error($event)">
                    </canvas>
                </view>
                <view class="set bg-white">
                    <view class="set-title text-center p10 color-title font16">设置</view>
                    <view class="set-container p10">
                        <uni-forms label-position="top" :label-width="120">
                            <uni-forms-item label="解锁的卡片图:">
                                <image
                                    class="card_image"
                                    v-if="card_info.unlock_src"
                                    :src="card_info.unlock_src"
                                    mode="aspectFit"
                                />
                                <view>
                                    <button
                                        type="primary"
                                        plain
                                        size="mini"
                                        @click="changeCardImage('unlock_src')"
                                    >{{ card_info.unlock_src ? '更改' : '设置' }}图片
                                    </button>
                                </view>
                            </uni-forms-item>
                            <uni-forms-item label="未解锁的卡片图:">
                                <image
                                    class="card_image"
                                    v-if="card_info.lock_src"
                                    :src="card_info.lock_src"
                                    mode="aspectFit"
                                />
                                <view>
                                    <button
                                        type="primary"
                                        plain
                                        size="mini"
                                        @click="changeCardImage('lock_src')"
                                    >{{ card_info.lock_src ? '更改' : '设置' }}图片
                                    </button>
                                </view>
                            </uni-forms-item>
                        </uni-forms>

                        <uni-forms
                            v-show="card_info.lock_src || card_info.unlock_src"
                            :label-width="100"
                            label-align="right"
                        >
                            <uni-forms-item label="预览图片显示:">
                               <view class="pt5 pb5" @click="cardShowTypeChange('unlock')">
                                   <radio :checked="card_show_type === 'unlock'"/>
                                   <text>解锁的卡片图</text>
                               </view>
                                <view class="pt5 pb5" @click="cardShowTypeChange('lock')">
                                    <radio :checked="card_show_type === 'lock'"/>
                                    <text>未解锁的卡片图</text>
                                </view>
                            </uni-forms-item>
                            <uni-forms-item label="卡片图横坐标:">
                                <uni-number-box
                                    :value="Math.floor(card_info.x)"
                                    :min="0"
                                    :max="Math.ceil(canvas_width - card_info.width)"
                                    @change="pointInfoChange($event, 'x')"
                                />
                            </uni-forms-item>
                            <uni-forms-item label="卡片图纵坐标:">
                                <uni-number-box
                                    :value="Math.floor(card_info.y)"
                                    :min="0"
                                    :max="Math.ceil(canvas_height - card_info.height)"
                                    @change="pointInfoChange($event, 'y')"
                                />
                            </uni-forms-item>
                            <uni-forms-item label="卡片图宽度:">
                                <uni-number-box
                                    :value="Math.floor(card_info.width)"
                                    :min="card_min_size"
                                    :max="canvas_width - card_info.x"
                                    @change="pointInfoChange($event, 'width')"
                                />
                            </uni-forms-item>
                            <uni-forms-item label="卡片图高度:">
                                <uni-number-box
                                    :value="Math.floor(card_info.height)"
                                    :min="card_min_size"
                                    :max="canvas_height - card_info.y"
                                    @change="pointInfoChange($event, 'height')"
                                />
                            </uni-forms-item>
                        </uni-forms>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import uniapp_api from "@/utils/api/uniapp_api"
import base64 from "@/utils/base64"

export default {
    data() {
        return {
            canvas_width: 0,
            canvas_height: 0,
            card_show_type: 'unlock',
            card_info: {
                lock_src: '',
                unlock_src: '',
                x: 0,
                y: 0,
                width: 0,
                height: 0
            },
            card_min_size: 5,
            point_name: ''
        }
    },

    onLoad(params) {
        uni.showLoading({ mask: true })
        this.point_id = params.point_id
        this.active_id = params.active_id

        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.xwy_api.alert(err.errMsg, { title: err['errTitle'] || '提示' })
            }

            this.init()
        })
    },

    methods: {
        async init() {
            uni.showLoading({ mask: true })
            await this.getActivityDetails()
            await this.getPointDetail()
            await this.getBackgroundImage()
            await this.getCardInfo()
            this.ctx = this.ctx || uni.createCanvasContext('canvas')
            await this.draw(true)
        },

        async getActivityDetails() {
            let activity_details = app.globalData['activity_detail']
            if (!activity_details) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: { active_id: this.active_id }
                })

                activity_details = res?.data?.['active_details']
            }
            this.activity_details = activity_details
        },

        async getPointDetail() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/map_point_details',
                data: { id: this.point_id }
            })

            uni.hideLoading()

            if (!res?.['status'] || !res['data']?.['map_point_details']) {
                return this.xwy_api.alert(res?.['info'] || '点位获取失败', {
                    success: () => uni.navigateBack()
                })
            }

            this.point_details = res['data']['map_point_details']
            if (this.point_details?.name) this.point_name = this.point_details.name
        },

        async getBackgroundImage() {
            const background_image = this.activity_details?.conf?.active?.background_image
            if (!background_image) return
            const image_info = await uniapp_api.getImageInfo(background_image)
            if (image_info?.errMsg !== 'getImageInfo:ok') {
                return this.xwy_api.alert('背景图片获取失败，请检查图片地址')
            }
            const max_width = document.querySelector('.controls').offsetWidth
            const max_height = document.querySelector('.controls').offsetHeight
            const {width, height} = image_info
            let scale = max_width / width
            let canvas_width = max_width
            let canvas_height = height * scale
            if (canvas_height > max_height) {
                scale = max_height / height
                canvas_width = width * scale
                canvas_height = height * scale
            }
            this.scale = scale
            this.canvas_width = canvas_width
            this.canvas_height = canvas_height
        },

        async getCardInfo() {
            const card_collecting = this.point_details?.conf?.image_set || {}
            this.card_info.lock_src = card_collecting.lock_src || ''
            this.card_info.unlock_src = card_collecting.unlock_src || ''
            this.card_info.x = (card_collecting.x || 0) * this.scale
            this.card_info.y = (card_collecting.y || 0) * this.scale
            this.card_info.width = (card_collecting.width || 0) * this.scale
            this.card_info.height = (card_collecting.height || 0) * this.scale
        },


        async draw(init = false) {
            const bg_path = this.activity_details?.conf?.active?.background_image
            if (!bg_path) return
            if (init) {
                uni.showLoading({title: '加载中...'})
                this.ctx.clearRect(0, 0, this.canvas_width, this.canvas_height)
            }
            this.ctx.drawImage(bg_path, 0, 0, this.canvas_width, this.canvas_height)
            const { x, y, width, height } = this.card_info
            const card_path = this.card_info[this.card_show_type + '_src']
            if (card_path) {
                this.ctx.drawImage(card_path, x, y, width, height)
                // this.ctx.setStrokeStyle('red')
                // const lineWidth = 2
                // this.ctx.lineWidth = lineWidth
                // this.ctx.strokeRect(x - lineWidth, y - lineWidth, width + lineWidth * 2, height + lineWidth * 2)
            }
            setTimeout(() => {
                this.ctx.draw(!init, () => {
                    if (init) uni.hideLoading()
                })
            }, init ? 300 : 0)
        },

        findTarget(touch_x, touch_y) {
            const { x, y, width, height } = this.card_info
            return touch_x >= x && touch_x <= x + width && touch_y >= y && touch_y < y + height
        },

        mousedown(e) {
            const { clientX, clientY } = this.getClientPosition(e)
            if (!this.findTarget(clientX, clientY)) return
            this.mouse_type = 'down'
            this.target_mouse_x = clientX - this.card_info.x
            this.target_mouse_y = clientY - this.card_info.y
            // 点击目标后，鼠标变成移动
            const canvas_dom = document.getElementById('canvas')
            canvas_dom.style.cursor = 'move'
        },

        mousemove(e) {
            const { clientX, clientY } = this.getClientPosition(e)
            if (!this.mouse_type || this.mouse_type !== 'down') {
                const canvas_dom = document.getElementById('canvas')
                const in_card = this.findTarget(clientX, clientY)
                canvas_dom.style.cursor = in_card ? 'pointer' : 'default'
                return
            }

            // 移动目标
            let mouseX = clientX - this.target_mouse_x
            let mouseY = clientY - this.target_mouse_y
            if (mouseY < 0) mouseY = 0
            if (mouseY + this.card_info.height >= this.canvas_height) {
                mouseY = this.canvas_height - this.card_info.height
            }
            if (mouseX < 0) mouseX = 0
            if (mouseX + this.card_info.width >= this.canvas_width) {
                mouseX = this.canvas_width - this.card_info.width
            }

            this.card_info.x = mouseX
            this.card_info.y = mouseY

            this.draw()

        },

        mouseup(e) {
            this.mouse_type = 'up'
            const { clientX, clientY } = this.getClientPosition(e)
            const in_card = this.findTarget(clientX, clientY)
            const canvas_dom = document.getElementById('canvas')
            canvas_dom.style.cursor = in_card ? 'pointer' : 'default'
        },

        getClientPosition(e) {
            return {
                clientX: e.clientX - e.currentTarget.offsetLeft,
                clientY: e.clientY - e.currentTarget.offsetTop
            }
        },

        pointInfoChange(e, type) {
            let value = isNaN(Number(e)) ? 0 : Number(e)
            const min_size = this.card_min_size
            const { x, y, width, height } = this.card_info
            const options = {
                'x': () => {
                    if (value < 0) value = 0
                    if (x + width >= this.canvas_width) value = this.canvas_width - width
                    this.card_info.x = value
                },
                'y': () => {
                    if (value < 0) value = 0
                    if (y + height >= this.canvas_height) value = this.canvas_height - height
                    this.card_info.y = value
                },
                'width': () => {
                    if (value < min_size) value = min_size
                    if (value + x >= this.canvas_width) value = this.canvas_width - x
                    this.card_info.width = value
                },
                'height': () => {
                    if (value < min_size) value = min_size
                    if (value + y >= this.canvas_height) value = this.canvas_height - y
                    this.card_info.height = value < min_size ? min_size : value
                }
            }
            options[type]?.()
            this.draw()
        },

        cardShowTypeChange(type) {
            if (type === this.card_show_type) return
            this.card_show_type = type
            this.draw()
        },

        error(e) {
            console.log('canvasError: ', e)
        },

        changeCardImage(type) {
            uni.navigateTo({
                url: '/pages/other/image_upload_or_select?active_id=' + this.active_id,
                events: {
                    newImg: src => this.getNewCardImageInfo(type, src)
                }
            })
        },

        async getNewCardImageInfo(type, src) {
            const image_info = await uniapp_api.getImageInfo(src)
            if (image_info?.errMsg !== 'getImageInfo:ok') {
                return this.xwy_api.alert('图片获取失败，请检查图片地址')
            }
            const max_width = this.canvas_width - this.card_info.x
            const max_height = this.canvas_height - this.card_info.y
            const { width, height } = image_info
            let card_width = width, card_height = height
            if (width > max_width) {
                const scale = max_width / width
                card_width = max_width
                card_height = height * scale
            }
            if (card_height > max_height) {
                const scale = max_height / height
                card_height = max_height
                card_width = width * scale
            }

            this.card_info[type] = src
            this.card_info.width = card_width
            this.card_info.height = card_height
            await this.draw(true)
        },

        saveCheck() {
            const { unlock_src, lock_src, x, y, width, height } = this.card_info
            if (!unlock_src)  return uni.showToast({ title: '请设置解锁的卡片图', icon: 'none' })
            if (!lock_src) return uni.showToast({ title: '请设置未解锁的卡片图', icon: 'none' })
            const image_set = {
                unlock_src,
                lock_src,
                x: x / this.scale,
                y: y / this.scale,
                width: width / this.scale,
                height: height / this.scale
            }
            this.save(image_set)
        },

        async save(image_set) {
            const data = {
                name: this.point_details.name,
                active_id: this.active_id,
                id: this.point_id,
                sort_num: this.point_details.sort_num || 0,
                news_id: this.point_details.news_id || ''
            }
            if (this.point_details.lat && this.point_details.lng) {
                data.lat = this.point_details.lat
                data.lng = this.point_details.lng
            }
            const conf = {
                logo: this.point_details.logo || '',
                image_set
            }
            if (this.point_details.address) conf.address = this.point_details.address

            let conf_str = JSON.stringify(conf)
            conf_str = conf_str.replace(/·/g, '-')
            data.conf = base64['encode'](conf_str)

            uni.showLoading({
                title: '保存中...',
                mask: true
            })

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/add_update_map_point',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.xwy_api.alert(res?.info || '保存失败')
            const info = res?.info || '保存成功'
            uni.showToast({
                title: info,
                icon: info.length <= 7 ? 'success' : 'none'
            })
        },

        pageBack() {
            window.history.back()
        }
    }
}
</script>

<style lang="scss">
.page {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
}
.controls, .set {
    margin: 10px;
    height: calc(100vh - 80px);
}
.controls {
    width: 100%;
    margin-right: 0;
    height: calc(100vh - 80px);
}
.set {
    width: 300px;
    min-width: 300px;
    .set-title {
        border-bottom: 1px solid #dddee1;
    }
    .set-container {
        height: calc(100vh - 180px);
        overflow-y: auto;
        .card_image {
            width: 200px;
            height: 120px;
        }
    }
}
</style>
