<template>
    <view class="page bg-background">

        <template v-if="list.length">
            <view class="flex-kai bg-white">
                <view class="color-sub p10">共{{ list.length }}张卡片</view>
                <view @click="toAddOrEdit(false)" class="color-primary p10">
                    <text>添加卡片</text>
                    <uni-icons type="forward" color="#2d8cf0"/>
                </view>
            </view>


            <view class="list">
                <view class="item bg-white" v-for="(item, index) in list" :key="index">
                    <view class="flex-row" style="padding-bottom: 10px;">
                        <view v-if="item.logo" class="pr10">
                            <image class="logo" :src="item.logo" mode="aspectFill"/>
                        </view>
                        <view>
                            <view class="color-title pb5 ellipsis--l2">{{ item.name }}</view>
                            <view v-if="item.address" class="ellipsis--l2 pb5">
                                <uni-icons type="location" size="14" color="#495060"/>
                                <text class="color-content font14">{{ item.address }}</text>
                            </view>
                            <view class="color-sub font14">排序：{{ item.sort_num }}</view>
                        </view>
                    </view>
                    <view class="tools-bar clear clearfix">
                        <view class="flex-row fr">
                            <view class="tool-item" @click="createPointQrcode(item)">
                                <text class="iconfont icon-qr-code"></text>
                            </view>
                            <view v-if="nfcOpen" class="tool-item" @click="createPointNFC(item.id)">
                                <text class="iconfont icon-nfc-tag"></text>
                            </view>
                            <view class="tool-item" @click="locationSet(item.id)">
                                <text class="iconfont icon-ZT-MAP"></text>
                            </view>
                            <view class="tool-item" @click="toAddOrEdit(item.id)">
                                <text class="iconfont icon-edit"></text>
                            </view>
                            <view class="tool-item" @click="deleteConfirm([item.id])">
                                <text class="iconfont icon-delete"></text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </template>

        <view v-if="!list.length && !loading" class="text-center" style="padding-top: 15vh;">
            <uni-icons type="images" size="100" color="#dddee1"/>
            <view class="color-sub">该活动下暂无卡片</view>
            <view class="flex-all-center pt15">
                <view class="add-team-btn color-white bg-primary text-center" @click="toAddOrEdit(false)">
                    添加卡片
                </view>
            </view>
        </view>

        <nfc-write-popup ref="nfcWritePopup" :active-id="id"/>
    </view>
</template>

<script>
const app = getApp()

export default {
    data() {
        return {
            id: '',
            loading: true,
            list: [],
            nfcOpen: false
        }
    },
    onLoad(e) {
        uni.showLoading({mask: true})
        this.id = e.id
        this.active_small_id = e.active_small_id
        if (e.category_open) this.category_open = true
        this.$login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getActiveDetails()
            this.getList()
        })
    },
    onPullDownRefresh() {
        if (!this.loading) this.getListInit()

        uni.stopPullDownRefresh()
    },
    methods: {
        async getActiveDetails() {
            let details = app.globalData['activity_detail']

            if (!details || details.active_id !== this.id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {active_id: this.id}
                })
                if (res?.data?.active_details) details = res.data.active_details
            }
            if (!details) return
            this.activity_details = details

            if (details.rank_set?.['openNFC']) this.nfcOpen = true
        },

        getListInit() {
            this.loading = true
            this.$uni.showLoading()
            this.list = []
            this.getList()
        },

        async getList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_map_point_list',
                data: {
                    active_id: this.id
                }
            })
            uni.hideLoading()
            this.loading = false

            if (res?.status !== 1) return

            const list = res.data?.map_point_list
            this.list = list.map(item => ({
                id: item.id,
                name: item.name,
                logo: item.conf?.logo || '',
                address: item.conf?.address || '',
                sort_num: item.sort_num,
                share_image: ''
            }))
        },

        async createPointQrcode(item) {
            if (item.share_image) return this.$uni.previewImage(item.share_image)

            this.$uni.showLoading('二维码生成中...')
            const qr_code_pic = await this.xwy_api.getWxAppCode({
                page: 'pages/clock_in/user/point_detail',
                scene: 'id=' + this.active_small_id + '&p_id=' + item.id
            })
            uni.hideLoading()

            if (!qr_code_pic) return this.$uni.showModal('二维码生成失败')

            item.share_image = qr_code_pic
            this.$uni.previewImage(item.share_image)
        },

        async getUrlScheme(point_id) {
            this.$uni.showLoading()
            const link = await this.xwy_api.generateNFCScheme({
                path: 'pages/card-collecting/user/map',
                query: `id=${this.id}&point_id=${point_id}&nfc=1`,
                model_id: 'CzYgyM5Z9JsvYKq6ILd0nA',
                sn: `${this.id}-${point_id}`
            })
            uni.hideLoading()
            return link

            /*const query = decodeURIComponent(`id=${this.id}&p_id=${point_id}&nfc=1`)

            return `weixin://dl/business/?appid=wx0921894817922c7d&path=pages/card-collecting/user/map&query=${query}`*/
        },

        async createPointNFC(point_id) {
            const scheme = await this.getUrlScheme(point_id)
            if (!scheme) return this.$uni.showModal('点位链接生成失败,请重试。')

            this.nfcAdapter ||= wx.getNFCAdapter()
            await this.$refs.nfcWritePopup.write(this.nfcAdapter, scheme)
        },

        locationSet(id) {
            let isH5 = true
            // #ifndef H5
            isH5 = false
            // #endif
            if (!isH5) return uni.showToast({title: '设置卡片位置需要在H5端操作', icon: 'none'})
            this.$uni.navigateTo(`./image-position-set?point_id=${id}&active_id=${this.id}`)
        },

        toAddOrEdit(id) {
            let url = `./set?active_id=${this.id}`
            if (id) url += `&point_id=${id}`
            if (!id && this.list.length) url += `&sort_num=${this.list.length + 1}`
            if (this.category_open) url += '&category_open=1'
            this.$uni.navigateTo(url, {
                events: {
                    refresh: () => {
                        this.getListInit()
                    }
                }
            })
        },

        async deleteConfirm(ids) {
            const res = await this.$uni.showModal(`确定删除${ids.length > 1 ? '选中的' : '该'}卡片?`, {
                showCancel: true,
                confirmText: '删除',
                cancelColor: '#80848f',
                confirmColor: '#ed3f14'
            })

            if (res.confirm) await this.deleteAjax(ids)
        },

        async deleteAjax(ids) {
            this.$uni.showLoading('删除中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/del_map_point',
                data: {
                    active_id: this.id,
                    ids: ids.join(',')
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res.info || '删除失败')

            this.list = this.list.filter(item => !ids.includes(item.id))
            this.$uni.showToast('已删除')
        }
    }
}
</script>

<style scoped lang="scss">
.page {
    min-height: 100vh;
    padding-bottom: 10px;
    box-sizing: border-box;
}

.item {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;

    .logo {
        width: 130px;
        min-width: 130px;
        height: 80px;
        display: block;
        border-radius: 5px;
    }

    .tools-bar {
        border-top: 1px solid #eee;
        padding-top: 10px;

        .tool-item {
            color: #80848f;
            font-size: 20px;
            width: 32px;
            height: 32px;
            line-height: 32px;
            text-align: center;
            border-radius: 50%;
            border: 1px solid #eee;

            &:not(:last-child) {
                margin-right: 10px;
            }
        }
    }
}

.add-team-btn {
    width: 200px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
}
</style>
