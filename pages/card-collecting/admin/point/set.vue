<template>
    <view class="page">
        <view class="form">
            <view class="form-item">
                <view class="top color-content">
                    <text>卡片名称</text>
                    <text class="color-error font16"> *</text>
                </view>
                <view class="bottom font16">
                    <input class="input" type="text" v-model="name" placeholder="请输入卡片名称"/>
                </view>
            </view>


            <view class="form-item">
                <view class="top color-content">
                    <text>卡片坐标</text>
                    <text v-if="clock_in_on_site_open" class="color-error font16"> *</text>
                </view>
                <view class="bottom font16">
                    <view class="flex-kai" @click="chooseLocation">
                        <view class="view">
                            <view v-if="lat && lng">{{ lat }},{{ lng }}</view>
                            <view v-else class="color-sub">请选择卡片坐标</view>
                        </view>
                        <view class="flex-all-center">
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </view>
            </view>

            <template v-if="active_id === '5a56536807e0970200f7954a6a0e11cd'">
                <view class="form-item">
                    <view class="top color-content">
                        <text>纬度坐标</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="digit" v-model="lat" placeholder="请输入纬度坐标"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <text>经度坐标</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="digit" v-model="lng" placeholder="请输入经度坐标"/>
                    </view>
                </view>
            </template>

            <view class="form-item">
                <view class="top color-content">
                    <text>卡片地址</text>
                    <!--<text class="color-error font16"> *</text>-->
                </view>
                <view class="bottom font16">
                    <textarea
                        class="textarea"
                        maxlength="-1"
                        auto-height
                        v-model="address"
                        placeholder="请输入点位地址"
                    />
                </view>
            </view>

            <view v-if="category_open" class="form-item">
                <view class="top color-content">卡片分类</view>
                <view class="bottom font16">
                    <view class="flex-kai" @click="toSelCategory">
                        <view class="view">
                            <view v-if="category_id">{{ category_name }}</view>
                            <view v-else class="color-sub">关联分类</view>
                        </view>
                        <view class="flex-all-center" @click.stop="deBindOrSelectCategory">
                            <view v-if="category_id" class="color-sub font12" style="width: 30px;">
                                解绑
                            </view>
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">卡片排序</view>
                <view class="bottom font16">
                    <input
                        class="input"
                        type="number"
                        v-model="sort_num"
                        placeholder="地图点位排序,数字越小排在越前(不填默认0)"
                    />
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">文章介绍</view>
                <view class="bottom font16">
                    <view class="flex-kai" @click="toSelNews">
                        <view class="view">
                            <view v-if="news_id">{{ news_title || news_id }}</view>
                            <view v-else class="color-sub">选择文章</view>
                        </view>
                        <view class="flex-all-center">
                            <view
                                v-if="news_id"
                                class="color-sub font12"
                                style="width: 30px;"
                                @click.stop="deleteNews"
                            >解绑</view>
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </view>
            </view>


            <view class="form-item">
                <view class="top color-title">
                    <text>卡片图片</text>
                    <text class="font12 color-sub pl5">(卡片缩略图、卡片详情顶部图片)</text>
                </view>
                <view style="padding-top: 5px;">
                    <view class="image-view" v-if="logo">
                        <image class="image-item" :src="logo" mode="aspectFill" @click="previewImage([logo])"/>
                        <view class="del-image-item" @click.stop="logo = ''">
                            <uni-icons type="closeempty" color="#e20f04"/>
                        </view>
                    </view>

                    <view v-else class="add-image text-center" @click="changeImage('logo')">
                        <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                    </view>
                </view>
            </view>

        </view>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view class="save-btn color-white text-center font18 bg-primary" @click="save">保存</view>
        </view>

        
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import base64 from '@/utils/base64.js'

export default {
    data() {
        return {
            active_id: '',
            id: '',
            name: '',
            logo: '',
            lat: '',
            lng: '',
            sort_num: '',
            news_id: '',
            news_title: '',
            address: '',
            clock_in_on_site_open: false,
            category_open: false,
            category_id: '',
            category_name: ''
        }
    },
    onLoad(e) {
        uni.showLoading({
            mask: true
        })
        this.active_id = e.active_id
        if (e.point_id) this.point_id = e.point_id
        if (e.sort_num) this.sort_num = Number(e.sort_num)
        if (e.category_open) this.category_open = true

        login.uniLogin(err => {
            if (err && err.errMsg) return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})

            this.init()
        })
    },
    methods: {
        async init() {
            await this.getCategoryList()
            await this.getActivityDetails()
        },

        async getCategoryList() {
            if (!this.category_open) return

            const res = await this.xwy_api.getCategoryList({
                types: 38,
                page: 1,
                perpage: 1000,
                active_id: this.active_id
            })

            const list = res?.data?.category_list?.data || []
            this.categoryList = list.map(item => ({category_id: item.category_id, name: item.name}))
        },

        async getActivityDetails() {
            let activity_details = app.globalData['activity_detail']

            if (!activity_details) {
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: { active_id: this.active_id }
                })

                activity_details = res?.data?.['active_details']
            }

            this.clock_in_on_site_open = activity_details?.conf?.active?.clock_in_on_site?.open === 1

            if (this.point_id) return await this.getPointDetail()
            uni.hideLoading()
        },

        async getPointDetail() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/map_point_details',
                data: { id: this.point_id }
            })

            uni.hideLoading()

            if (!res?.['status'] || !res['data']?.['map_point_details']) {
                return xwy_api.alert(res?.['info'] || '点位获取失败', { success: () => uni.navigateBack() })
            }

            const details = res['data']['map_point_details']

            this.name = details.name
            if (details.news_id) this.news_id = details.news_id
            if (details.news_title) this.news_title = details.news_title
            if (details.lat) this.lat = details.lat
            if (details.lng) this.lng = details.lng
            if (details.sort_num) this.sort_num = details.sort_num
            if (details.conf) {
                const conf = details.conf
                if (conf.logo) this.logo = conf.logo
                if (conf.address) this.address = conf.address
                if (conf.image_set) this.image_set = conf.image_set
            }
            if (details.category_id) {
                this.category_id = details.category_id
                this.category_name = this.categoryList.find(item => item.category_id === details.category_id)?.name || details.category_id
            }
        },


        chooseLocation() {
            const obj = {
                success: res => {
                    this.lat = res.latitude
                    this.lng = res.longitude

                    if (res.address || res.name) {
                        const address = `${res.address || ''}${res.name || ''}`
                        if (this.address && this.address !== address) {
                            uni.showModal({
                                title: '提示',
                                content: `卡片经纬度已改变，是否将卡片地址改成${address}`,
                                success: res => {
                                    if (res.confirm) this.address = address
                                }
                            })
                        }
                        if (!this.address) this.address = address
                    }
                },
                fail: err => {
                    console.log(err)
                    if (err.errMsg === 'chooseLocation:fail:not supported') {
                        uni.showModal({
                            title: '提示',
                            content: '不支持电脑端使用，请在手机微信打开',
                            showCancel: false
                        })
                        return false
                    }
                    if (err.errMsg !== 'chooseLocation:fail cancel') {
                        uni.showModal({
                            title: '地图拉起失败',
                            content: JSON.stringify(err),
                            showCancel: false
                        })
                        return false
                    }
                }
            }
            if (this.lat && this.lng) {
                obj.latitude = this.lat * 1
                obj.longitude = this.lng * 1
            }
            uni.chooseLocation(obj)
        },


        toSelNews() {
            uni.navigateTo({
                url: '/pages/news/list?type=user&is_sel=true',
                events: {
                    selNews: data => {
                        this.news_id = data.id
                        this.news_title = data.title
                    }
                }
            })
        },

        deleteNews() {
            this.news_id = ''
            this.news_title = ''
        },

        deBindOrSelectCategory() {
            if (!this.category_id) return this.toSelCategory()

            this.category_id = ''
            this.category_name = ''
        },

        toSelCategory() {
            this.$uni.navigateTo(`/pages/category/list?is_sel=1&types=38&active_id=${this.active_id}&just_look_active=1`, {
                events: {
                    selCategory: data => {
                        this.category_id = data.id
                        this.category_name = data.name
                    }
                }
            })
        },

        changeImage(key) {
            uni.navigateTo({
                url: '/pages/other/image_upload_or_select?active_id=' + this.active_id,
                events: {
                    newImg: src => {
                        switch (key) {
                            case 'logo':
                                this.logo = src
                                break
                        }
                    }
                }
            })
        },

        previewImage(urls, current = urls[0]) {
            uni.previewImage({
                urls,
                current
            })
        },

        save() {
            const errorToast = (title, duration = 1500) => {
                uni.showToast({
                    title,
                    icon: title.length > 7 ? 'none' : 'error',
                    duration
                })
            }

            if (!this.name) return errorToast('请输入卡片名称')
            if (this.clock_in_on_site_open && (!this.lat || !this.lng)) {
                return errorToast('活动开启了需要在卡片附近才能集卡请，选择卡片坐标', 3000)
            }

            const value = {
                name: this.name,
                active_id: this.active_id,
                sort_num: this.sort_num ? Math.floor(this.sort_num) : 0,
                news_id: this.news_id || '',
                category_id: this.category_id || ''
            }
            if (this.lat && this.lng) {
                value.lat = Number(this.lat)
                value.lng = Number(this.lng)
            }
            const conf = { logo: this.logo || '' }
            if (this.image_set) conf.image_set = this.image_set
            if (this.address) conf.address = this.address

            let conf_str = JSON.stringify(conf)
            conf_str = conf_str.replace(/·/g, '-')
            value.conf = base64['encode'](conf_str)


            if (this.point_id) value.id = this.point_id

            this.saveAjax(value)
        },

        saveAjax(data) {
            uni.showLoading({
                mask: true
            })

            data.access_token = app.globalData['access_token']

            xwy_api.ajax({
                url: 'front.flat.sport_step.admin/add_update_map_point',
                data,
                success: res => {
                    console.log('添加修改点位', res);
                    uni.hideLoading()
                    const type_text = this.point_id ? '修改' : '添加'

                    if (!res.status) {
                        uni.showModal({
                            title: '提示',
                            content: res.info || `${type_text}失败`,
                            showCancel: false
                        })
                        return false
                    }

                    uni.showToast({
                        icon: 'none',
                        title: `${type_text}成功`
                    })

                    const OEC = this.getOpenerEventChannel()
                    OEC?.emit?.('refresh')

                    this.toImagePositionSet(this.point_id || res?.data?.['res'])
                }
            })
        },

        async toImagePositionSet(id) {
            let toSet = false

            // #ifdef H5
            const res = await this.$uni.showModal(`卡片${this.point_id ? '修改' : '添加'}成功，是否前往${this.point_id ? '修改' : '设置'}卡片位置？`, {
                showCancel: true,
                confirmText: '前往'
            })
            if (res.confirm) toSet = true
            // #endif

            if (toSet) {
                this.$uni.redirectTo(`./image-position-set?point_id=${id}&active_id=${this.active_id}`)
            } else {
                uni.navigateBack()
            }
        }

    }
}
</script>

<style scoped>
.page {
    padding-bottom: 100px;
    background-color: #fff;
}

.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}


.form-item .textarea {
    width: 100%;
    line-height: 16px;
    padding: 10px 0;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}

.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
    width: 100px;
    height: 100px;
    border-radius: 5px;
    line-height: 95px;
    margin: 5px calc((100vw - 20px - (100px * 3)) / 6);
}


.image-item {
    width: calc(100vw - 20px);
    max-height: 200px;
    border-radius: 5px;
}

.image-view {
    position: relative;
    display: inline-block;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}


.bottom-btn-view {
    position: fixed;
    z-index: 99999;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.save-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}
</style>
