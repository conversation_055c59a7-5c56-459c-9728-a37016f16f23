<template>
    <view>
        <view class="background-image-view">

            <image class="background-image" :src="background_image" mode="widthFix"
                   @load="backgroundImageLoad($event)"/>
            <view class="card-item" v-for="item in point_list" :key="item.id" :style="item.style"
                  hover-class="navigator-hover" @click="toPointDetail(item.id)">
                <image :src="item.have_sign_light ? item.unlock_src : item.lock_src"/>
                <image v-if="seal_stamp_id === item.id" class="in-light-image" :src="item.unlock_src_path"/>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
export default {
    data() {
        return {
            background_image: '',
            point_list: [],
            sequential_clock_in: false,
            seal_stamp_id: null
        }
    },

    onLoad(params) {
        console.log('🚀params: ', params)
        this.id = params.id
        if (params.point_id) this.point_id = Number(params.point_id)
        if (params['nfc']) this.nfcEnter = true
        this.$uni.showLoading()
        this.$login.uniLogin(() => {
            this.getActivityDetails()
        })
    },

    methods: {
        async getActivityDetails() {
            let activity_details = app.globalData['activity_detail']

            if (!activity_details || activity_details.active_id !== this.id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {active_id: this.id}
                })
                activity_details = res?.data?.['active_details']
            }

            this.active_details = activity_details

            if (activity_details?.name) this.$uni.setNavigationBarTitle(activity_details.name)
            if (activity_details?.conf?.active?.sequential_clock_in === 1) {
                this.sequential_clock_in = true
            }

            if (!activity_details?.conf?.active?.background_image) {
                uni.hideLoading()
                await this.$uni.showModal('活动背景图未设置，请联系活动管理员。')
                return this.$uni.navigateBack()
            }

            this.background_image = activity_details.conf.active.background_image
        },

        async backgroundImageLoad(e) {
            const {width} = e.detail
            const {windowWidth} = uni.getSystemInfoSync()
            this.ratio = windowWidth / width
            await this.getPointList()

            await this.sealStamp()
        },

        async getPointList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/user_sign_light_map_point',
                data: {active_id: this.id}
            })
            uni.hideLoading()
            const list = res.data?.['sign_light_res'] || []
            if (!list.length) return

            this.point_list = list.map(v => {
                const image_set = v.conf?.image_set || {}
                let {lock_src = '', unlock_src = '', x = 0, y = 0, width = 0, height = 0} = image_set
                x *= this.ratio
                y *= this.ratio
                width *= this.ratio
                height *= this.ratio
                return {
                    id: v.id,
                    name: v.name,
                    have_sign_light: v.have_sign_light || 0,
                    lock_src,
                    unlock_src,
                    // 用来做盖章的图片，需要下载后拿到本地地址才能显示盖章动画，不然图片没加载动画不显示
                    unlock_src_path: '',
                    style: `left: ${x}px; top: ${y}px; width: ${width}px; height: ${height}px`
                }
            })
        },

        toPointDetail(id) {
            this.$uni.navigateTo(`/pages/clock_in/user/point_detail?id=${this.id}&point_id=${id}`,{
                events: {
                    reloadList: () => {
                        this.getPointList()
                        this.updatePreviousPageData()
                    }
                }
            })
        },

        async getUnlockImagePath(src) {
            const res = await this.$uni.getImageInfo(src)
            return res.path || ''
        },

        async sealStamp() {
            if (!this.nfcEnter || !this.point_id || !this.active_details?.conf?.active?.auto_stamp) return
            const point_id = this.point_id

            const index = this.point_list.findIndex(item => item.id === point_id)
            if (index === -1) return this.$uni.showModal('点位ID不正确')
            this.point_list[index].unlock_src_path = await this.getUnlockImagePath(this.point_list[index].unlock_src)

            /*const location = await this.getLocation()
            if (!location) await this.$uni.showModal('定位失败')*/

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/submit_user_point_sign',
                data: {
                    active_id: this.id,
                    point_id,
                    /*lat: location.latitude,
                    lng: location.longitude*/
                }
            })

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '盖章失败')

            this.updatePreviousPageData()

            this.seal_stamp_id = point_id

            setTimeout(() => {
                const index = this.point_list.findIndex(item => item.id === point_id)
                if (index !== -1) this.$set(this.point_list[index], 'have_sign_light', 1)
                this.seal_stamp_id = null
            }, 1000)
        },

        /*getLocation() {
            return new Promise(resolve => {
                uni.getLocation({
                    type: 'gcj02',
                    altitude: true,
                    success: res => resolve(res),
                    fail: err => {
                        this.$uni.showModal('定位失败' + JSON.stringify(err))
                        resolve(false)
                    }
                })
            })
        },*/

        updatePreviousPageData() {
            this.getOpenerEventChannel?.()?.emit?.('updateUserDetails')
            this.getOpenerEventChannel?.()?.emit?.('reloadList')
        }
    }
}
</script>

<style lang="scss">
.background-image-view {
    position: relative;

    .background-image {
        width: 100vw;
        height: auto;
        display: block;
    }
}

.card-item {
    position: absolute;

    image {
        width: 100%;
        height: 100%;
        display: block;
    }

    .in-light-image {
        position: absolute;
        top: 0;
        left: 0;

        animation: stamp 1s linear;
    }

    @keyframes stamp {
        0% {
            transform: scale(1.5);
        }
        100% {
            transform: scale(1);
        }
    }
}
</style>
