<template>
    <view class="page">
        <view class="words-list">
            <view class="words-item flex-row" v-for="(item, index) in wordsList" :key="index">
                <view v-if="item.logo" class="pr10">
                    <image class="words-logo" :src="item.logo" mode="aspectFill"/>
                </view>
                <view class="w-100">
                    <view class="color-title">{{ item.word }}</view>
                    <view class="flex-kai pt10 color-content">
                        <view>
                            <text class="stock-label">总库存:</text>
                            <text>{{ item.stock }}</text>
                        </view>
                        <view>
                            <text class="stock-label">已领:</text>
                            <text>{{ item.used_count }}</text>
                        </view>
                        <view>
                            <text class="stock-label">剩余库存:</text>
                            <text>{{ item.left_count }}</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            wordsList: []
        }
    },

    onLoad(params) {
        this.active_id = params.id
        this.getWords()
    },

    onPullDownRefresh() {
        this.getWords().finally(() => uni.stopPullDownRefresh())
    },

    methods: {
        async getWords() {
            this.wordsList = []

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.active.collect_word.user.collectWord/words_stock_list',
                data: {
                    active_id: this.active_id
                }
            })
            uni.hideLoading()

            const words_list = res?.data?.active_details?.conf?.active?.word_list || []
            const stock_list = res?.data?.['words_list'] || []

            this.wordsList = words_list.map((item, index) => ({
                word: item.word,
                logo: item.logo || '',
                stock: stock_list[index]?.stock || 0,
                used_count: stock_list[index]?.used_count || 0,
                left_count: stock_list[index]?.left_count || 0
            }))
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    background-color: #f8f8f8;
    box-sizing: border-box;
    padding-top: 1px;
}

.words-item {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
    background-color: #fff;

    .words-logo {
        width: 60px;
        height: 60px;
        border-radius: 5px;
        display: block;
    }

    .stock-label {
        padding-right: 2px;
        color: #80848f;
        font-size: 14px;
    }
}
</style>