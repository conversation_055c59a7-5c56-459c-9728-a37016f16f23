<template>
	<view>
		<view class="form">
			<view class="form-item">
				<view class="top color-content">
					<text>关卡名称</text>
					<text class="color-error font16"> *</text>
				</view>
				<view class="bottom font16">
					<input class="input" type="text" v-model="FormData.name" :maxlength="30"/>
				</view>
			</view>



			<view class="form-item">
				<view class="top color-content">
					<text>绑定答题</text>
					<text class="color-error font16"> *</text>
				</view>
				<view class="bottom font16">
					<view class="flex-kai" @click="toSelExam">
						<view class="view">
							<view v-if="FormData.exam_id">{{exam_title || FormData.exam_id}}</view>
							<view v-else class="color-sub">选择考卷</view>
						</view>
						<view class="flex-all-center">
							<view v-if="FormData.exam_id" class="color-sub font12" style="width: 30px;" @click.stop="deleteExam">解绑</view>
							<uni-icons type="forward" color="#80848f"/>
						</view>
					</view>
				</view>
			</view>

			<view class="form-item">
				<view class="top">
					<view class="color-content">过关分数</view>
					<view class="color-sub font12">答题成绩大于等于此分数即可获得一个字</view>
				</view>
				<view class="bottom font16">
					<input
						class="input"
						type="digit"
						v-model="conf.min_score"
						:maxlength="5"
						placeholder="不填默认0"
					/>
				</view>
			</view>

			<view class="form-item">
			    <view class="top color-content">过关后获得的文字</view>
			    <view class="bottom font16">
			        <view class="flex-kai">

			            <picker
			                class="view"
			                mode="selector"
			                :range="word_list"
                      :value="word_list.findIndex(v => v.word === conf.word)"
                      range-key="word"
                      @change="conf.word = word_list[$event.detail.value].word"
			            >
			                {{conf.word}}
			            </picker>
			            <view class="flex-all-center">
			                <uni-icons type="forward" color="#80848f"/>
			            </view>
			        </view>
			    </view>
			</view>

			<view class="form-item">
				<view class="top color-content">关卡排序</view>
				<view class="bottom font16">
					<input
						class="input"
						type="number"
						v-model="FormData.sort_num"
						:maxlength="5"
						placeholder="数字越小排在越前(不填默认0)"
					/>
				</view>
			</view>
		</view>

		<view class="bottom-btn-view bg-white flex-all-center">
			<view class="save-btn color-white text-center font18 bg-primary" @click="save">保存</view>
		</view>
	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'
	import base64 from '@/utils/base64.js'


	export default {
		data() {
			return {
				activity_detail: null,
				word_list: [],
				FormData: {
					name: '',
					active_id: '',
					exam_id: '',
					sort_num: ''
				},
				conf: {
					min_score: '',
					word: '随机'
				},
				exam_title: ''
			}
		},

		onLoad(e) {
		    uni.showLoading()
		    this.FormData.active_id = e.active_id
			if (e.id) this.FormData.id = e.id
		    login.uniLogin(err => {
		        if (err && err.errMsg) {
		            uni.hideLoading()
		            uni.showModal({
		                title: err.errTitle || '提示',
		                content: err.errMsg,
		                showCancel: false
		            })
		            return false
		        }

		        this.getActivityData()
		    })
		},

		methods: {
			async getActivityData() {
				let activity_detail = app.globalData.activity_detail

				if (!activity_detail) {
					const res = await xwy_api.request({
						url: 'front.flat.sport_step.active_list/active_details',
						data: {
							access_token: app.globalData.access_token,
							active_id: this.FormData.active_id
						}
					})

					activity_detail = res.data.active_details

				}

				this.activity_detail = activity_detail

				this.getWordList()

				if (!this.FormData.id) {
					uni.hideLoading()
					return false
				}

				this.getDetail()
			},

			async getDetail() {
				uni.setNavigationBarTitle({
					title: '修改关卡'
				})


				const data = {
					access_token: app.globalData.access_token,
					id: this.FormData.id
				}

				const res = await xwy_api.request({url: 'front.flat.sport_step.active_list/map_point_details', data});

				uni.hideLoading()

				if (!res || !res.status || !res.data || !res.data.map_point_details) {
					uni.showModal({
						title: '提示',
						content: res && res.info || '点位获取失败',
						showCancel: false,
						success: () => uni.navigateBack()
					})

					return false
				}

				const detail = res.data.map_point_details

				this.FormData.name = detail.name
				this.FormData.exam_id = detail.exam_id
				this.FormData.sort_num = detail.sort_num

				if (detail.conf?.min_score) this.conf.min_score = detail.conf.min_score
				if (detail.conf?.word) this.conf.word = detail.conf.word

				if (detail.exam_details?.exam_name) this.exam_title = detail.exam_details.exam_name
			},

			getWordList() {
				const word_list = this.activity_detail.conf.active.word_list || []
				this.word_list = [{word: '随机'}, ...word_list]
			},

			toSelExam() {
				if (!this.activity_detail.rank_set?.exam_open) {
					uni.showModal({
						title: '提示',
						content: '未开通答题功能，请联系客服开通。',
						showCancel: false
					})
					return false
				}

				uni.navigateTo({
					url: '/pages/likou_dati/pages/exam/exam_list/exam_list?in_select=true',
					events: {
						updateExam: data => {
							this.FormData.exam_id = data.id
							this.exam_title = data.title
						}
					}
				})
			},

			deleteExam() {
				this.FormData.exam_id = ''
				this.exam_title = ''
			},

			formDataCheck() {
				const FormData = JSON.parse(JSON.stringify(this.FormData))
				if (!FormData.name) {
					uni.showToast({
						title: '请输入关卡名称',
						icon: 'error'
					})
					return false
				}

				if (!FormData.exam_id) {
					uni.showToast({
						title: '请绑定答题',
						icon: 'error'
					})
					return false
				}

				if (!FormData.sort_num) FormData.sort_num = 0
				FormData.sort_num = Number(FormData.sort_num)

				return FormData
			},

			confCheck() {
				const conf = JSON.parse(JSON.stringify(this.conf))
				if (!conf.min_score) conf.min_score = 0
				conf.min_score = Number(conf.min_score)
				if (conf.word === '随机') conf.word = ''
				return conf
			},

			async save() {
				const data = this.formDataCheck()
				if (!data) return false
				data.access_token = app.globalData.access_token

				const conf = this.confCheck()
				let conf_str = JSON.stringify(conf)
				conf_str = conf_str.replace(/·/g, '-')
				data.conf = base64.encode(conf_str)

				const res = await xwy_api.request({
					url: 'front.flat.sport_step.admin/add_update_map_point',
					data
				})

				if (!res || !res.status) {
					xwy_api.alert(res && res.info || '保存失败')
				}

				uni.showToast({
					title: '保存成功',
					icon: 'success'
				})

				const OEC = this.getOpenerEventChannel()
				OEC && OEC.emit('reloadList')

				setTimeout(() => {
					uni.navigateBack()
				})
			}
		}
	}
</script>

<style>
	.page {
		padding-bottom: 100px;
		background-color: #fff;
	}

	.form {
		padding: 10px 0;
	}

	.form-item {
		padding: 10px;
	}


	.form-item .bottom {
		border-bottom: 1px solid #eee;
	}


	.form-item .textarea {
		width: 100%;
		line-height: 16px;
		padding: 10px 0;
	}

	.form-item .input {
		width: 100%;
		line-height: 40px;
		height: 40px;
	}

	.form-item .view {
		padding: 8px 0;
		width: 100%;
	}
	.add-image {
		border: 1px solid #eee;
		box-sizing: border-box;
		width: 100px;
		height: 100px;
		border-radius: 5px;
		line-height: 95px;
		margin: 5px calc((100vw - 20px - (100px * 3)) / 6);
	}



	.image-item {
		width: calc(100vw - 20px);
		max-height: 200px;
		border-radius: 5px;
	}

	.image-view {
		position: relative;
		display: inline-block;
	}

	.del-image-item {
		position: absolute;
		right: 10px;
		top: 8px;
		width: 30px;
		height: 30px;
		line-height: 30px;
		text-align: center;
		background-color: rgba(0, 0, 0, .5);
		border-radius: 50%;
	}


	.bottom-btn-view {
		position: fixed;
		z-index: 99999;
		bottom: 0;
		left: 0;
		width: 100vw;
		padding: 15px 0;
	}

	.save-btn {
		width: 250px;
		height: 44px;
		line-height: 44px;
		border-radius: 22px;
	}
</style>
