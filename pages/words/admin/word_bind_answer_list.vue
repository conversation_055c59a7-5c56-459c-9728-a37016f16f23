<template>
	<view class="page bg-background">
		<view v-if="list.length" class="font14 flex-kai">
			<view class="color-sub p10">共{{list.length}}个关卡</view>
			<view class="color-primary p10" hover-class="navigator-hover" @click="toAddOrEdit(false)">添加关卡</view>
		</view>


		<view class="list">
			<view class="item bg-white" v-for="(item, index) in list" :key="index">
				<view style="padding-bottom: 10px;">
					<view class="color-title pb5">{{item.name}}</view>

					<view class="color-content font14">排序: {{item.sort_num}}</view>
				</view>
				<view class="tools-bar flex-kai">
					<view class="flex-row">

					</view>
					<view class="flex-row">
						<view @click="toAddOrEdit(item.id)" class="edit">
							<text class="iconfont icon-edit color-sub font20"></text>
						</view>
						<view class="delete" @click="deleteItem(item.id)">
							<text class="iconfont icon-delete color-sub font20"></text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view v-if="!in_load && !list.length" class="text-center" style="padding-top: 15vh;">
			<text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
			<view class="color-sub">暂未创建关卡</view>
			<view class="flex-all-center pt15">
				<view class="add-team-btn color-white bg-primary text-center" @click="toAddOrEdit(false)">创建关卡</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'

	export default {
		data() {
			return {
				in_load: true,
				list: []
			}
		},

		onLoad(e) {
		    uni.showLoading()
		    this.id = e.id
		    login.uniLogin(err => {
		        if (err && err.errMsg) {
		            uni.hideLoading()
		            uni.showModal({
		                title: err.errTitle || '提示',
		                content: err.errMsg,
		                showCancel: false
		            })
		            return false
		        }

		        this.getList()
		    })
		},

		methods: {
			async getList() {
				this.in_load = true
				this.list = []
				const res = await xwy_api.request({
					url: 'front.flat.sport_step.active_list/active_map_point_list',
					data: {
						access_token: app.globalData.access_token,
						active_id: this.id
					},
				})

				this.in_load = false
				uni.hideLoading()

				if (res.data?.map_point_list?.length) {
					this.list = res.data.map_point_list
				}
			},

			toAddOrEdit(id) {
				let url = './word_bind_answer?active_id=' + this.id
				if (id) url += `&id=${id}`
				uni.navigateTo({
					url,
					events: {
						reloadList: () => {
							this.getList()
						}
					}
				})
			},

			deleteItem(id) {
				this.deleteConfirm([id])
			},

			deleteConfirm(ids) {
				uni.showModal({
					title: '提示',
					content: `确定删除${ids.length > 1 ? '选中的' : '该'}关卡?`,
					confirmText: '删除',
					cancelColor: '#80848f',
					confirmColor: '#ed3f14',
					success: res => {
						if (res.confirm) this.deleteAjax(ids)
					}
				})
			},

			deleteAjax(ids) {
				uni.showLoading({
					title: '删除中...'
				})

				xwy_api.ajax({
					url: 'front.flat.sport_step.admin/del_map_point',
					data: {
						access_token: app.globalData.access_token,
						active_id: this.id,
						ids: ids.join(',')
					},
					success: res => {
						uni.hideLoading()
						if (!res.status) {
							uni.showModal({
								title: '提示',
								content: res.info || '删除失败',
								showCancel: false
							})
							return false;
						}

						const new_list = this.list.filter(item => {
							return !ids.find(v => item.id === v)
						})
						this.list = new_list
						uni.showToast({
							title: '删除成功'
						})
					}
				})

			},

		}
	}
</script>

<style>
.page {
	min-height: 100vh;
}
.item {
	margin: 10px;
	padding: 10px;
	border-radius: 10px;
}
.tools-bar {
	border-top: 1px solid #eee;
	padding-top: 10px;
}

.edit, .delete {
	width: 32px;
	height: 32px;
	line-height: 32px;
	text-align: center;
	border-radius: 50%;
}
.edit {
	border: 1px solid #eee;
	margin-right: 10px;
}
.delete {
	border: 1px solid #eee;
}
.add-team-btn {
	width: 200px;
	height: 40px;
	line-height: 40px;
	border-radius: 20px;
}
</style>
