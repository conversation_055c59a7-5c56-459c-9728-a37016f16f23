<template>
    <view class="page">
		<view v-if="list.length" class="flex-kai">
			<view class="p10 color-sub font14">共{{list.length}}场答题</view>
		</view>

        <view class="list flex-row flex-wrap">
            <template v-for="(item, index) in list">
                <view class="item" :key="index" @click="toExamDetail(item)">
                    <view class="logo">
                        <image v-if="item.exam_details.logo" class="logo-img" mode="aspectFill"
                               :src="item.exam_details.logo"/>
                        <view v-else class="no-logo-img flex-all-center bg-background">
                            <uni-icons type="image" size="100" color="#bbbec4"/>
                        </view>
                    </view>
                    <view class="pt5">
						<view class="color-title">{{item.name || item.exam_details.exam_name}}</view>
						<view class="color-content font14">
							<text>答题</text>
							<text v-if="item.conf.min_score > 0">并成绩大于等于{{item.conf.min_score}}分可</text>
							<text v-if="!item.conf.word">随机</text>
							<text>获得一个</text>
							<text v-if="item.conf.word">“{{item.conf.word}}”</text>
							<text>字</text>
						</view>
					</view>
                </view>
            </template>
        </view>

        <view v-if="!loading && !list.length" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无答题</view>
        </view>

    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'


export default {
    data() {
        return {
			id: '',
            list: [],
            loading: true
        }
    },
    onLoad(e) {
        this.id = e.id
        uni.showLoading({
            mask: true
        })
        login.uniLogin(err => {
        	if (err && err.errMsg) {
        		uni.showModal({
        			title: err.errTitle || '提示',
        			content: err.errMsg,
        			showCancel: false
        		})
        		return false
        	}

        	this.getList()
        })
    },
    methods: {
        async getList() {
			const data = {
				access_token: app.globalData.access_token,
				active_id: this.id
			}

			const res = await xwy_api.request({url: 'front.flat.sport_step.active_list/point_exam_list', data});
			uni.hideLoading();
			this.loading = false;
			if (res?.data?.point_exam_list?.length) {
				res.data.point_exam_list.forEach(v => {
					if (v.exam_details.logo === '/pages/likou_dati/static/default.jpg') {
						v.exam_details.logo = ''
					}
				})
			    this.list = res.data.point_exam_list
				if (this.list.length === 1) {
					const item = this.list[0]
					uni.redirectTo({
					    url: '/pages/likou_dati/pages/exam/exam_details/exam_details?exam_id=' + item.exam_id + '&active_id=' + this.id + '&point_id=' + item.id + '&send_word=1'
					})
				}
			}

        },


        toExamDetail(item) {
            uni.navigateTo({
                url: '/pages/likou_dati/pages/exam/exam_details/exam_details?exam_id=' + item.exam_id + '&active_id=' + this.id + '&point_id=' + item.id + '&send_word=1'
            })
        }

    }
}
</script>

<style scoped>
    .list {
        padding: 5px;
    }
    .item {
        width: 50%;
        padding: 10px 5px;
        box-sizing: border-box;
    }
    .logo-img {
        display: block;
    }
    .logo-img, .no-logo-img {
        width: 100%;
        height: 100px;
        border-radius: 5px;
    }
</style>
