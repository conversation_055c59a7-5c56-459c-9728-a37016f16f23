<template>
	<view>
		<view class="bg-img">

			<image :src="bg_image" mode="aspectFill"/>
		</view>
		<view class="main">
			<template v-if="userid">
				<view class="text-center pt10">

					<image v-if="headimg" class="headimg" :src="headimg"/>
					<view v-if="nickname" class="pt10 color-content">{{nickname}}邀你给TA助力</view>
				</view>
				<view style="padding-top: 30px;" class="flex-all-center">
					<button class="big-btn color-white" @click="help">给TA助力</button>
				</view>
				<view class="flex-all-center">

					<navigator class="color-sub font14 p10" :url="'./detail?id=' + activity_detail.active_id">参与活动</navigator>
				</view>
			</template>
			<template v-else>
				<view v-if="activity_detail && activity_detail.conf && activity_detail.conf.active" class="p10 color-content font14">
					<view>·好友每助力一次增加一次抽字机会。</view>
					<view>·每位用户每天可接受{{activity_detail.conf.active.everyday_help_times}}次助力。</view>
					<view>·每天只能给同一好友助力一次。</view>
				</view>
				<view style="padding-top: 50px;" class="flex-all-center">
					<button class="big-btn color-white" open-type="share">邀请好友助力</button>
				</view>
			</template>
		</view>

		<template v-if="activity_detail">
			<uni-popup ref="popup" type="center">
				<view class="popup bg-white text-center">
					<view class="color-white pb10" style="background-color: #fd923b;">
						<uni-icons :type="help_status === 1 ? 'checkbox' : 'close'" size="100" color="#ffffff"/>
						<view>助力{{help_status === 1 ? '成功' : '失败'}}</view>
					</view>
					<view class="color-content" style="padding: 30px 10px;">{{help_info}}</view>
				</view>
				<view class="pt5"></view>
				<xwy-ad v-if="!activity_detail.rank_set || !activity_detail.rank_set.closed_AD" :ad_type="66"></xwy-ad>
				<view class="flex-all-center">
					<uni-icons type="close" color="#ffffff" size="28" @click="uniPopupClose('popup')"/>
				</view>
			</uni-popup>
		</template>
	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'

	export default {
		data() {
			return {
				userid: null,
				activity_detail: null,
				bg_image: '',
				headimg: '',
				nickname: '',
				help_status: '',
				help_info: ''
			}
		},
		onLoad(e) {
			this.id = e.id
            let bg_image = 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/window/help.jpg'
			if (e.userid) {
                bg_image = 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/window/help1.jpg'
                uni.hideShareMenu(undefined)
                this.userid = e.userid
            }
            this.bg_image = bg_image
			if (e.headimg) this.headimg = e.headimg
			if (e.nickname) this.nickname = e.nickname
			uni.showLoading({
				mask: true
			})
		    login.uniLogin(err => {
		        if (err && err.errMsg) {
					uni.hideLoading()
		            uni.showModal({
		                title: err.errTitle || '提示',
		                content: err.errMsg,
		                showCancel: false
		            })
		        }

				this.getDetail()
		    }, e.userid, e.id)
		},

		onShareAppMessage() {
			const userid = this.userid || app.globalData.userid
			let url = '/pages/words/user/help?id=' + this.id + '&userid=' + userid
			let headimg = ''
			let nickname = ''
			if (app.globalData.userinfo) {
				if (app.globalData.userinfo.headimg) headimg = app.globalData.userinfo.headimg
				if (app.globalData.userinfo.nickname) nickname = app.globalData.userinfo.nickname
			}
			if (this.headimg) headimg = this.headimg
			if (this.nickname) nickname = this.nickname

			if (headimg) url += `&headimg=${headimg}`
			if (nickname) url += `&nickname=${nickname}`

		    return {
		        title: '来给我助力吧！',
		        path: url
		    }
		},

		methods: {
			async getDetail() {
				let activity_detail = app.globalData.activity_detail

				if (!activity_detail) {
					const res = await xwy_api.request({
						url: 'front.flat.sport_step.active_list/active_details',
						data: {
							access_token: app.globalData.access_token,
							active_id: this.id
						}
					})

					activity_detail = res.data.active_details

				}

				this.activity_detail = activity_detail

				uni.hideLoading()
			},

			async help() {
				uni.showLoading({
					title: '助力中...',
					mask: true
				})

				const res = await xwy_api.request({
					url: 'front.flat.active.collect_word.user.collectWord/friend_help_add_times',
					data: {
						access_token: app.globalData.access_token,
						active_id: this.id,
						userid: this.userid
					}
				})
				uni.hideLoading()

				this.help_status = res.status || 0
				this.help_info = res.info || '给好友助力失败'

				this.uniPopupOpen('popup')
			},

			uniPopupClose(ref) {
			    this.$refs[ref].close()
			},

			uniPopupOpen(ref) {
			    this.$refs[ref].open()
			}
		}
	}
</script>

<style>
.bg-img image {
	width: 100vw;
	height: 100vh;
	display: block;
}
.main {
	position: fixed;
	width: 100vw;
	top: 50vh;
	left: 0;
	padding: 0 30px;
	box-sizing: border-box;
}
.headimg {
	width: 80px;
	height: 80px;
	border-radius: 50%;
	display: inline-block;
}
.big-btn {
	width: 250px;
	line-height: 40px;
	border-radius: 20px;
	background: linear-gradient(to right, #f65c42, #fd923b, #fd772a);
}
.big-btn::after {
	border: none;
}
.popup {
	width: 300px;
	border-radius: 10px;
	overflow: hidden;
}
</style>
