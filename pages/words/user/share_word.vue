<template>
	<view>
		<view class="bg-img">

			<image :src="bg_image" mode="aspectFill"/>
		</view>

		<view class="main-view flex-all-center">
			<view class="main">
				<view class="text-center pt10">

					<image v-if="headimg" class="headimg" :src="headimg"/>
					<view v-if="nickname" class="pt10 color-content">{{nickname}}送你一个</view>
				</view>
				<template>
					<view v-if="logo" class="flex-all-center" style="padding-top: 20px;">

						<image class="logo" :src="logo" mode="aspectFit" @click="previewLogo(logo)"/>
					</view>
					<view v-else class="flex-all-center" style="padding-top: 50px; padding-bottom: 20px;">
						<view class="word">

							<view class="word-text flex-all-center">{{word}}</view>
							<view class="word-bg">
								<view class="word-bg2">
									<view class="word-bg3"></view>
								</view>
							</view>

						</view>
					</view>
				</template>

				<view style="padding-top: 30px;" class="flex-all-center">
					<button class="big-btn color-white" @click="iGotThe">领取</button>
				</view>
				<view class="flex-all-center">

					<navigator class="color-sub font14 p10" :url="'./detail?id=' + active_id">参与活动</navigator>
				</view>
			</view>
		</view>


		<uni-popup ref="popup" type="center">
			<view class="popup bg-white text-center">
				<view class="color-white pb10" style="background-color: #fd923b;">
					<uni-icons :type="get_status === 1 ? 'checkbox' : 'close'" size="100" color="#ffffff"/>
					<view>领取{{get_status === 1 ? '成功' : '失败'}}</view>
				</view>
				<view class="color-content" style="padding: 30px 10px;">{{get_info}}</view>
			</view>
			<view class="pt5"></view>
			<xwy-ad v-if="!close_ad" :ad_type="66"></xwy-ad>
			<view class="flex-all-center">
				<uni-icons type="close" color="#ffffff" size="28" @click="uniPopupClose('popup')"/>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'

	export default {
		data() {
			return {
				bg_image: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/window/giving.jpg',
				headimg: '',
				nickname: '',
				word: '',
				active_id: '',
				get_status: '',
				get_info: '',
				close_ad: true,
				logo: ''
			}
		},
		onLoad(e) {
			if (e.headimg) this.headimg = e.headimg
			if (e.nickname) this.nickname = e.nickname
			if (e.word) this.word = e.word
			if (e.uuid) this.uuid = e.uuid
			if (e.active_id) this.active_id = e.active_id
			if (e.logo) this.logo = e.logo
			uni.setNavigationBarTitle({
				title: `${e.nickname || ''}送你一个 ${e.word || ''} 字`
			})
			uni.showLoading({
				mask: true
			})
		    login.uniLogin(err => {
				uni.hideLoading()
		        if (err && err.errMsg) {
		            uni.showModal({
		                title: err.errTitle || '提示',
		                content: err.errMsg,
		                showCancel: false
		            })
		        }

				if (!e.close_ad) this.close_ad = false
		    }, e.userid, e.active_id)
		},



		methods: {
			async iGotThe() {
				uni.showLoading({
					title: '领取中...',
				})

				const res = await xwy_api.request({
					url: 'front.flat.active.collect_word.user.shareWords/received_share_words',
					data: {
						access_token: app.globalData.access_token,
						active_id: this.active_id,
						uuid: this.uuid
					}
				})

				uni.hideLoading()

				this.get_status = res.status || 0
				this.get_info = res.info || '领取失败'

				this.uniPopupOpen('popup')
			},

			uniPopupClose(ref) {
			    this.$refs[ref].close()
			},

			uniPopupOpen(ref) {
			    this.$refs[ref].open()
			},

			previewLogo(logo) {
				uni.previewImage({
					urls: [logo],
					current: logo
				})
			}
		}
	}
</script>

<style>
.bg-img image {
	width: 100vw;
	height: 100vh;
	display: block;
}
.main-view {
	position: fixed;
	width: 100vw;
	height: 100vh;
	top: 0;
	left: 0;
}
.main {
	width: 90vw;
	box-sizing: border-box;
	background-color: #fffbed;
	border: 5px solid #fbf0c8;
	border-radius: 10px;
	padding: 10px 0;
}
.headimg {
	width: 80px;
	height: 80px;
	border-radius: 50%;
	display: inline-block;
}
.word {
	position: relative;
	width: 100px;
	height: 100px;
}
.word-text, .word-bg {
	width: 100px;
	height: 100px;
	position: absolute;
	top: 0;
	left: 0;
}
.word-text {
	color: #f6e0b3;
	font-size: 70px;
	font-weight: 600;
	z-index: 2;
}
.logo {
	width: 200px;
	height: 200px;
	border-radius: 10px;
	display: inline-block;
}

.word-bg {
	width: 100px;
	height: 100px;
	border-radius: 10px;
	padding: 2px;
	box-sizing: border-box;
	background-color: #d94948;
	top: 0;
	left: 0;
	z-index: 1;
	transform: rotate(45deg)
}
.word-bg2 {
	background-color: #f6e0b3;
	width: 96px;
	height: 96px;
	padding: 2px;
	box-sizing: border-box;
	border-radius: 10px;
}
.word-bg3 {
	background-color: #d94948;
	width: 92px;
	height: 92px;
	border-radius: 10px;
}
.big-btn {
	width: 250px;
	line-height: 40px;
	border-radius: 20px;
	background: linear-gradient(to right, #f65c42, #fd923b, #fd772a);
}
.big-btn::after {
	border: none;
}

.popup {
	width: 300px;
	border-radius: 10px;
	overflow: hidden;
}
</style>
