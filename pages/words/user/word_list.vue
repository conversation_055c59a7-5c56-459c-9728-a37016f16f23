<template>
	<view v-if="!init_load">
		<view class="text-center p10 font14 color-sub">
			<template v-if="no_word_list.length">加油！还差{{no_word_list.length}}个字即可合成</template>
			<template v-else>恭喜！你已集齐所有字，可以合成</template>
		</view>
		<view class="word-list flex-row flex-wrap">
			<view
				class="word-item"
				:class="{'have-word': item.holp >= 1, 'no-word': item.holp === 0}"
				v-for="(item,index) in word_list"
				:key="index"
				@click="clickWord(item)"
			>
				<view>{{item.word}}</view>
				<view class="holp" :class="{'plr3': item.holp >= 10}" v-if="item.holp">{{item.child.length}}</view>
			</view>
		</view>


		<view class="flex-all-center" style="padding-top: 50px;">
			<view class="get-word text-center" @click="getWord">获取</view>
		</view>

		<view class="flex-all-center pt10">
			<view class="get-word text-center" @click="synthetise">合成</view>
		</view>


		<uni-popup ref="word_popup" type="center">
			<view class="bg-white">

			</view>
		</uni-popup>
	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'

	export default {
		data() {
			return {
				word_list: [],
				no_word_list: []
			}
		},
		onLoad(e) {
			this.id = e.id
			uni.showLoading({
				mask: true
			})
			login.uniLogin(err => {
			    if (err && err.errMsg) {
			        uni.hideLoading()
			        uni.showModal({
			            title: err.errTitle || '提示',
			            content: err.errMsg,
			            showCancel: false
			        })
			        return false
			    }


				this.getActivityData()
			})
		},
		methods: {
			async getActivityData() {
				let activity_detail = app.globalData.activity_detail

				if (!activity_detail) {
					const res = await xwy_api.request({
						url: 'front.flat.sport_step.active_list/active_details',
						data: {
							access_token: app.globalData.access_token,
							active_id: this.id
						}
					})

					activity_detail = res.data.active_details

				}

				this.activity_detail = activity_detail


				const user_activity_detail = await xwy_api.request({
					url: 'front.flat.sport_step.user/user_attend_details',
					data: {
						access_token: app.globalData.access_token,
						active_id: this.id
					}
				})

				if (user_activity_detail?.data?.user_details) this.user_activity_detail = user_activity_detail.data.user_details

				this.getUserWordList()
			},

			async getUserWordList() {
				const res = await xwy_api.request({
					url: 'front.flat.active.collect_word.user.collectWord/collect_words_list',
					data: {
						access_token: app.globalData.access_token,
						active_id: this.id
					}
				})

				uni.hideLoading()

				const all_word_list = this.activity_detail?.conf?.active?.word_list || []
				const user_word_list = res?.data?.words_list || []
				const no_word_list = []

				all_word_list.forEach(all => {
					all.child = []
					all.holp = 0
					user_word_list.forEach(user => {
						if (user.word === all.word) {
							all.child.push(user)
							all.holp++
						}
					})
					if (all.child.length === 0) no_word_list.push(all.word)
				})
				this.word_list = all_word_list
				this.no_word_list = no_word_list
				this.init_load = false
			},

			async getWord() {
				if (!this.user_activity_detail?.checked) {
					xwy_api.alert('请先参加活动或等待管理员审核后再来获取')
					return false
				}

				uni.showLoading({
					title: '获取中...',
					mask: true
				})

				const res = await xwy_api.request({
					url: 'front.flat.active.collect_word.user.collectWord/user_collect_words',
					data: {
						access_token: app.globalData.access_token,
						active_id: this.id
					}
				})

				uni.hideLoading()

				xwy_api.alert(res && res.info || '获取失败')

				this.getUserWordList()
			},


			async synthetise() {
				const word_list = this.word_list
				const no_word_list = []
				word_list.forEach(v => {
					if (v.child.length === 0) {
						no_word_list.push(v.word)
					}
				})

				if (no_word_list.length) {
					xwy_api.alert('还未集齐哦，无法合成。缺少 [' + no_word_list.join('、') + ']')
					return false
				}

				uni.showLoading({
					title: '合成中...',
					mask: true
				})

				const res = await xwy_api.request({
					url: 'front.flat.active.collect_word.user.collectWord/together_all_words',
					data: {
						access_token: app.globalData.access_token,
						active_id: this.id
					}
				})

				uni.hideLoading()

				xwy_api.alert(res && res.info || '合成失败')

				this.getUserWordList()
			},

			clickWord(item) {
				console.log(item)
			}
		}
	}
</script>

<style>
.word-list {
	padding: 5px;
}
.word-item {
	width: 60px;
	height: 60px;
	text-align: center;
	margin: 5px;
	border-radius: 5px;
	font-size: 30px;
	line-height: 60px;
	position: relative;
}
.have-word {
	background-color: #d94948;
	color: #f6e0b3;
}
.no-word {
	background-color: #bbbec4;
	color: #80848f;
}
.holp {
	position: absolute;
	right: 2px;
	top: 2px;
	min-width: 14px;
	height: 14px;
	line-height: 14px;
	text-align: center;
	border-radius: 7px;
	background-color: #f6e0b3;
	color: #d94948;
	font-size: 12px;
}
.plr3 {
	padding-left: 3px;
	padding-right: 3px;
}
.get-word {
	background-color: #d94948;
	color: #f6e0b3;
	width: 200px;
	line-height: 40px;
	border-radius: 20px;
}
</style>
