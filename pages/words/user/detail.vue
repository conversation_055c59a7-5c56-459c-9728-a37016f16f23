<template>
    <view class="page bg-background">

        <screen-picture
            v-if="screen_pic_show"
            :hide="screen_pic_hide"
            :image-src="screen_pic"
            :time="screen_pic_count_down"
            :show-button="true"
            @skipScreen="skipScreen"
        />


        <activity-delete-tips v-if="error" :activeId="id"/>

        <view v-if="detail && detail.active_id">
            <template v-if="detail.rank_set && detail.rank_set['barrage']">
                <lff-barrage ref="lffBarrage" :activeid="id"></lff-barrage>
            </template>


            <activity-logo-title-time :details="detail"
                                      :hide-share="detail.rank_set && detail.rank_set['share_closed']"/>


            <view class="icon-list flex-row flex-wrap text-center bdb-10 bg-white">

                <navigator v-if="is_my_activity" class="icon-item" :url="'../admin/manage?id=' + id">
                    <text class="iconfont font24 color-primary icon-setting"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">活动管理</view>
                </navigator>
                <view v-if="!is_joining" class="icon-item" @click="joinActivity"
                      hover-class="navigator-hover">
                    <text class="iconfont font24 color-primary icon-users"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">参与活动</view>
                </view>
                <view v-if="is_joining" class="icon-item" @click="uniPopupOpen('my_info')"
                      hover-class="navigator-hover">
                    <text class="iconfont font24 color-primary icon-personal-data"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">报名信息</view>
                </view>

                <view v-if="!detail.rank_set || !detail.rank_set['closed_top_rank']" class="icon-item"
                      hover-class="navigator-hover" @click="toTopList()">
                    <text class="iconfont font24 color-primary icon-trophy"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">排行榜</view>
                </view>


                <navigator
                    v-if="detail.rank_set && detail.rank_set['lottery_open']"
                    class="icon-item"
                    :url="'/pages/lottery/user/lottery_record_list?active_id=' + id"
                >
                    <text class="iconfont font24 color-primary icon-gift"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">抽奖记录</view>
                </navigator>

                <view v-if="helpTimes > 0 && checked" class="icon-item" @click="toHelp">
                    <uni-icons type="hand-up" size="24" color="#2d8cf0"/>
                    <view class="color-sub font14" style="padding-top: 3px;">邀请助力</view>
                </view>

                <view class="icon-item" @click="uniPopupOpen('activity_detail')"
                      hover-class="navigator-hover">
                    <text class="iconfont font24 color-primary icon-feedback"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">活动说明</view>
                </view>


                <navigator v-if="detail.rank_set && detail.rank_set['share_friends']" class="icon-item"
                           :url="`/pages/other/friend_list?active_id=${id}`">
                    <text class="iconfont font24 color-primary icon-team"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">我推荐的好友</view>
                </navigator>


                <template v-if="headimg_plugin && headimg_plugin.length">
                    <view class="icon-item" v-for="(item, index) in headimg_plugin" :key="index"
                          @click="toIdeaAvatar(item)">

                        <image
                            v-if="item.key === 'chajian'"
                            :src="item.icon || 'https://apissl.xinweiyun.com/weixin/public/img/longmarch/icon_guajian.png'"
                            style="width: 24px; height: 24px;"
                        />

                        <image
                            v-if="item.key === 'beijing'"
                            :src="item.icon || 'https://apissl.xinweiyun.com/weixin/public/img/longmarch/icon_beijing.png'"
                            style="width: 24px; height: 24px;"
                        />
                        <view class="color-sub font14" style="padding-top: 1px;">{{ item.name }}</view>
                    </view>
                </template>


                <view
                    v-if="detail.rank_set && detail.rank_set['barrage']"
                    hover-class="navigator-hover"
                    class="icon-item"
                    @click="toBulletScreen()"
                >
                    <uni-icons type="chatboxes" size="24" color="#2d8cf0"/>
                    <!-- <text class="iconfont font24 color-primary icon-empty-state"></text> -->
                    <view class="color-sub font14" style="padding-top: 3px;">活动弹幕</view>
                </view>


                <template v-if="detail.conf.active.detail_icon_list">

                    <navigator
                        v-for="(item, index) in detail.conf.active.detail_icon_list"
                        :key="index"
                        class="icon-item"
                        :url="item.type === 0 ? ('/pages/news/preview?id=' + item.id) : ('/pages/news/list?category_id=' + item.id)"
                    >
                        <text :class="'iconfont font24 color-primary ' + item.icon"></text>
                        <view class="color-sub font14" style="padding-top: 3px;">{{ item.title }}</view>
                    </navigator>
                </template>


                <view v-if="!detail.rank_set || !detail.rank_set['share_closed']" class="icon-item"
                      hover-class="navigator-hover" @click="showActiveSharePopup">
                    <text class="iconfont font24 color-primary icon-share"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">分享</view>
                </view>
            </view>

            <view v-if="!is_joining" class="p10 bdb-10">
                <view class="join-container">
                    <view class="flex-all-center">
                        <view class="join-btn bg-primary color-white" @click="joinActivity">参与活动</view>
                    </view>
                </view>
            </view>

            <view v-if="word_list.length" class="pb10">
                <view v-if="user_details.id" class="text-center p10 font14 color-sub">
                    <template v-if="user_details.finished_time">
                        恭喜！你已合成成功!
                    </template>
                    <template v-else-if="no_word_list.length">
                        加油！还差{{ no_word_list.length }}个字即可合成
                    </template>
                    <template v-else>
                        恭喜！你已集齐所有字，可以合成
                    </template>
                </view>
                <view class="word-list flex-row flex-wrap">
                    <view
                        class="flex-all-center"
                        v-for="(item,index) in word_list"
                        :key="index"
                        :style="{width: 'calc((100vw - 10px) / ' + (detail.conf.active.one_line_word_count || 5) + ')'}"
                    >
                        <view class="word-item" @click="clickWord(item)">
                            <view>
                                <template v-if="item.logo">
                                    <image :src="item.logo" mode="aspectFill"/>
                                </template>
                                <template v-else>
                                    <view v-if="id === 'a067d9f84b5c0020f5fe84cb7d068acd'"
                                          :class="{'have-word': item.holp >= 1, 'no-word': item.holp === 0}">
                                        {{ item.word }}
                                    </view>
                                    <view v-else class="have-word">{{ item.word }}</view>
                                </template>
                            </view>
                            <view class="holp" :class="{'plr3': item.holp >= 10}" v-if="item.holp">
                                {{ item.child.length }}
                            </view>
                        </view>
                    </view>
                </view>


                <template v-if="user_details.id && !user_details.finished_time">
                    <view
                        v-if="!detail.conf.active.get_word_type || detail.conf.active.get_word_type !== 1"
                        class="flex-all-center"
                        style="padding-top: 20px;"
                    >
                        <view class="get-word text-center" @click="getWord">
                            <text>抽字</text>
                            <text class="font12 pl5">(可抽{{ can_used_times }}次)</text>
                        </view>
                    </view>

                    <view
                        v-if="detail.conf.active.get_word_type && (detail.conf.active.get_word_type === 1 || detail.conf.active.get_word_type === 2)"
                        class="flex-all-center pt10"
                    >
                        <view class="get-word text-center" @click="toAnswerList">答题集字</view>
                    </view>

                    <view class="flex-all-center pt10 text-center">
                        <view v-if="user_details.finished_time" class="get-word" @click="synthetise"
                              style="background-color: #bbbec4; color: #80848f;">已合成
                        </view>
                        <view v-else class="get-word" @click="synthetise(false)">
                            <text>合成</text>
                            <text v-if="no_word_list.length" class="font12 pl5">
                                (还差{{ no_word_list.length }}字)
                            </text>
                        </view>
                    </view>
                </template>

                <view v-if="user_details.id && user_details.finished_time && detail.conf.active.lottery_id && detail.rank_set && detail.rank_set['lottery_open']"
                    class="flex-all-center pt10 text-center">

                    <navigator class="get-word text-center"
                               :url="'/pages/lottery/user/lottery?type=word&id=' + detail.conf.active.lottery_id + '&active_id=' + id">
                        去抽奖
                    </navigator>
                </view>

                <uni-popup ref="get_word_success" type="center">
                    <view class="get-word-success text-center">
                        <view class="flex-all-center" style="padding: 40px 0;">
                            <view class="get-word-success-word">
                                <view class="get-word-success-word-text flex-all-center color-title">
                                    <template v-if="get_word_success_data.logo">
                                        <image :src="get_word_success_data.logo" mode="aspectFill"/>
                                    </template>
                                    <template v-else>{{ get_word_success_data.word }}</template>
                                </view>
                                <view v-if="!get_word_success_data.logo" class="get-word-success-word-bg">
                                    <view class="get-word-success-word-bg2">
                                        <view class="get-word-success-word-bg3"></view>
                                    </view>
                                </view>
                            </view>
                        </view>
                        <view class="color-white" style="padding-bottom: 30px;">
                            {{ get_word_success_data.info }}
                        </view>
                        <view v-if="no_word_list.length === 0" class="bg-white" style="padding: 30px 0;">
                            <view class="font14 color-content">恭喜已集齐所有字，可以合成啦!</view>
                            <view class="flex-all-center pt10">
                                <view class="get-word-success-synthetise" @click="synthetise(true)">合成
                                </view>
                            </view>
                        </view>
                    </view>
                    <view class="pt5"></view>
                    <xwy-ad v-if="popup_open && (!detail.rank_set || !detail.rank_set.closed_AD)"
                            :ad_type="66"></xwy-ad>
                    <view class="flex-all-center">
                        <uni-icons type="close" color="#ffffff" size="28"
                                   @click="uniPopupClose('get_word_success')"/>
                    </view>
                </uni-popup>
            </view>

            <uni-popup ref="word_item" type="center" :mask-click="false" @touchmove.stop.prevent="">
                <view style="background-color: #d94948; width: 300px; border-radius: 10px; padding: 50px 0;">
                    <view class="flex-all-center" style="padding-bottom: 40px;">
                        <view class="get-word-success-word">
                            <view class="get-word-success-word-text flex-all-center color-title">
                                <template v-if="popup_word_data.logo">
                                    <image :src="popup_word_data.logo" mode="aspectFill"/>
                                </template>
                                <template v-else>{{ popup_word_data.word }}</template>
                            </view>
                            <view v-if="!popup_word_data.logo" class="get-word-success-word-bg">
                                <view class="get-word-success-word-bg2">
                                    <view class="get-word-success-word-bg3"></view>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="text-center font14 color-white p10">
                        <template v-if="popup_word_data.holp">
                            共有{{ popup_word_data.holp }}个
                            <text v-if="popup_word_data.describe">{{ popup_word_data.describe }}</text>
                            <text v-else>“{{ popup_word_data.word }}”</text>
                        </template>
                        <template v-else>
                            未获得 {{ popup_word_data.describe || `“${popup_word_data.word}”` }}
                        </template>
                    </view>

                    <view
                        v-if="!detail.conf.active.give_word_close && (popup_word_data.holp > 1 || (popup_word_data.holp === 1 && !user_details.finished_time))"
                        class="flex-all-center pt10">
                        <button
                            class="text-center font14"
                            open-type="share"
                            style="width: 120px; line-height: 38px; border-radius: 19px; background-color: #f6e0b3; color: #ba823b"
                        >送给好友
                        </button>
                    </view>
                </view>
                <view class="pt5"></view>
                <xwy-ad v-if="popup_open && (!detail.rank_set || !detail.rank_set.closed_AD)"
                        :ad_type="66"></xwy-ad>
                <view class="flex-all-center">
                    <uni-icons type="close" color="#ffffff" size="28" @click="closeWordItemPopup"/>
                </view>
            </uni-popup>

            <uni-popup ref="synthetise" type="center">
                <view class="bg-white text-center"
                      style="width: 300px;border-radius: 10px; overflow: hidden;">
                    <view class="color-white pb10" style="background-color: #d94948;">
                        <uni-icons :type="synthetise_res.status === 1 ? 'checkbox' : 'close'" size="100"
                                   color="#ffffff"/>
                        <view>合成{{ synthetise_res.status === 1 ? '成功' : '失败' }}</view>
                    </view>
                    <view class="color-content" style="padding: 30px 10px;">{{ synthetise_res.info }}</view>
                    <view
                        v-if="synthetise_res.status === 1 && detail.conf.active.lottery_id && detail.rank_set && detail.rank_set['lottery_open']"
                        class="flex-all-center p10">


                        <navigator
                            :url="'/pages/lottery/user/lottery?type=word&id=' + detail.conf.active.lottery_id + '&active_id=' + id"
                            class="text-center font14"
                            style="width: 120px; line-height: 38px; border-radius: 19px; background-color: #d94948; color: #f6e0b3"
                            @click="uniPopupClose('synthetise')"
                        >去抽奖
                        </navigator>
                    </view>
                </view>
                <view class="pt5"></view>
                <xwy-ad v-if="popup_open && (!detail.rank_set || !detail.rank_set.closed_AD)"
                        :ad_type="66"></xwy-ad>
                <view class="flex-all-center">
                    <uni-icons type="close" color="#ffffff" size="28" @click="uniPopupClose('synthetise')"/>
                </view>
            </uni-popup>

            <view v-if="technology_support" class="flex-all-center" style="padding-top: 30px;">

                <navigator
                    v-if="technology_support.news_id"
                    :url="'/pages/news/preview?id=' + technology_support.news_id + '&type=support'"
                    class="text-center font14 color-sub p10"
                >{{ technology_support.button_text }}
                </navigator>
                <view v-else class="text-center font14 color-sub p10">
                    {{ technology_support.button_text }}
                </view>
            </view>

            <view v-if="!detail.rank_set || !detail.rank_set['closed_user_center']" class="flex-all-center">
                <navigator url="/pages/user/user" class="p10 color-sub font14">个人中心</navigator>
            </view>

            <xwy-ad v-if="(!detail.rank_set || !detail.rank_set.closed_AD) && !screen_pic_show"
                    :ad_type="3"></xwy-ad>


            <xwy-ad v-if="(!detail.rank_set || !detail.rank_set.closed_AD) && !screen_pic_show && !popup_open"
                    :activity_id="id" :ad_type="4"></xwy-ad>

        </view>

        <view v-if="join_popup_show" class="join-popup flex-all-center bg-white" @touchmove.stop.prevent="">
            <view>
                <view class="join-popup-c bg-white">
                    <view class="text-center font18 color-content p10">
                        <template v-if="update_attend_details">修改报名信息</template>
                        <template v-else>参加活动</template>
                    </view>


                    <template v-if="must_submit.length">
                        <template v-for="(item, index) in must_submit">

                            <input
                                v-if="item.types === 1"
                                :key="index"
                                class="join-input"
                                v-model="item.value"
                                :placeholder="'请输入' + item.title + (item.rules === 1 ? ' (必填)' : '')"
                            />
                            <picker
                                v-if="item.types === 2"
                                :range="item.options"
                                range-key="text"
                                @change="mustValueChange($event, index)"
                            >
                                <view class="join-input flex-kai">
                                    <view v-if="!item.value" class="color-sub">
                                        请选择{{ item.title }}{{ item.rules === 1 ? ' (必选)' : '' }}
                                    </view>
                                    <view v-if="item.value">{{ item.value }}</view>
                                    <text class="iconfont icon-more color-disabled font18"/>
                                </view>
                            </picker>
                        </template>

                    </template>


                    <view class="join-popup-btns flex-row text-center font18">
                        <view class="join-popup-btn-cancel color-sub" @click="cancelJoin">取消</view>
                        <view class="join-popup-btn-confirm color-success" @click="joinAjax">确定</view>
                    </view>
                </view>

                <template v-if="!detail.rank_set || !detail.rank_set.closed_AD">
                    <view class="pt5">
                        <xwy-ad :ad_type="66"></xwy-ad>
                    </view>
                    <xwy-ad :ad_type="3"></xwy-ad>
                </template>
            </view>


        </view>

        <active-share ref="activeShare"/>

        <uni-popup ref="activity_detail" type="center" @touchmove.stop.prevent="">
            <view v-if="detail && detail.conf && detail.conf.active"
                  class="uni-popup-info detail-popup bg-white">
                <view class="popup-close" @click="uniPopupClose('activity_detail')">
                    <uni-icons type="close" size="28" color="#b2b3b7"/>
                </view>
                <scroll-view
                    scroll-y="true"
                    class="detail-popup-detail"
                    style="max-height: calc(100vh - 200px); padding: 10px 0;"
                >
                    <view class="p10 bdb-10 bg-white">
                        <view class="text-center color-sub pb5">- 活动规则 -</view>

                        <view class="color-content font16">
                            参与方式：
                            <template v-if="detail.conf.active.enter_types === 1">
                                自由报名参与活动
                            </template>
                            <template v-else-if="detail.conf.active.enter_types === 2">
                                需要输入密码才能报名
                            </template>
                            <template v-else-if="detail.conf.active.enter_types === 3">
                                报名需要审核通过才能参与活动
                            </template>
                            <template v-else-if="detail.conf.active.enter_types === 4">
                                只能使用管理员提前导入的名单报名
                            </template>
                        </view>
                        <view
                            v-if="detail.conf.active.ip_set && detail.conf.active.ip_set.open && detail.conf.active.ip_set.ip_city && detail.conf.active.ip_set.ip_city.length"
                            class="color-content font16">
                            <text>城市限制：{{ detail.conf.active.ip_set.ip_city.join('、') }} 用户可参与活动
                            </text>
                        </view>
                        <view class="color-content font16" v-if="initFreeTimes > 0">
                            初始赠送：{{ initFreeTimes }}次抽字机会
                        </view>
                        <view class="color-content font16" v-if="everydayFreeTimes > 0">
                            每日赠送：{{ everydayFreeTimes }}次抽字机会
                        </view>
                        <view class="color-content font16" v-if="helpTimes > 0">
                            好友助力：每日通过好友助力最高可获得{{ helpTimes }}次抽字机会
                        </view>
                    </view>

                    <view v-if="detail.content || news_detail" class="p10 bdb-10 bg-white">
                        <view class="text-center color-sub pb5">- 活动说明 -</view>
                        <view class="color-content font16">
                            <template v-if="detail.content && !news_detail">
                                <rich-text :nodes="detail.content" space="nbsp"></rich-text>
                            </template>
                            <template v-if="news_detail">
                                <template v-if="news_detail.content">
                                    <u-parse :content="news_detail.content"/>
                                </template>
                            </template>
                        </view>
                    </view>
                    <xwy-ad v-if="!loading && popup_open && (!detail.rank_set || !detail.rank_set.closed_AD)"
                            :ad_type="66"></xwy-ad>
                </scroll-view>
            </view>
        </uni-popup>

        <uni-popup ref="my_info" type="center" @touchmove.stop.prevent="">
            <view v-if="detail && detail.conf && detail.conf.active" class="uni-popup-info bg-white p20">
                <view class="popup-close" @click="uniPopupClose('my_info')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="text-center color-sub pb5">- 活动报名信息 -</view>
                <view class="text-center p10">

                    <image
                        class="headimg"
                        :src="headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"
                        mode="aspectFill"
                    />
                    <view>
                        <text class="color-primary" @click="updateHeadimg">更改头像</text>
                    </view>
                </view>


                <view
                    class="color-content font16 ptm5"
                    v-for="(item, index) in must_submit"
                    :key="index"
                    @click="updateAttendDetailShow"
                >
                    <text>
                        {{ item.title }}：
                        <template>
                            <template v-if="item.value">{{ item.value }}</template>
                            <template v-else>
                                <template v-if="item.types === 1">未填写</template>
                                <template v-if="item.types === 2">未选择</template>
                            </template>
                        </template>
                    </text>
                    <text v-if="is_joining" class="iconfont icon-edit color-sub pl5"></text>
                </view>

                <template v-if="popup_open && (!detail.rank_set || !detail.rank_set.closed_AD)">
                    <view style="position: relative; left: -10px;">
                        <xwy-ad :ad_type="66"></xwy-ad>
                    </view>
                    <xwy-ad :ad_type="3"></xwy-ad>
                </template>
            </view>
        </uni-popup>


        <xwy-ad v-if="popup_open && (!detail.rank_set || !detail.rank_set.closed_AD)" :ad_type="3"></xwy-ad>

        <template v-if="password_dialog_show">
            <uni-popup
                ref="input_password"
                type="dialog"
                mode="input"
                :is-mask-click="false"
                @maskClick="copy(id)"
            >
                <uni-popup-dialog
                    mode="input"
                    title="活动密码"
                    :value="password"
                    placeholder="请输入活动密码"
                    @confirm="passwordInputConfirm"
                    @close="passwordInputClose"
                ></uni-popup-dialog>
            </uni-popup>
        </template>

        <expiration-reminder ref="expirationReminder"/>

        <iup v-if="detail && detail.rank_set && detail.rank_set.batch_import"
             ref="input_username_password" :labels="detail.conf.active.batch_import_label"/>

    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import my_storage from '@/utils/storage.js'
import base64 from '@/utils/base64.js'
import bs_socket from '@/utils/bullet_screen_socket.js'
import activity_tool from '@/utils/acyivity_tool.js'
import iup from "@/components/input-username-password.vue"


import lffBarrage from '@/components/lff-barrage.vue'

let interval

export default {
    components: {
        iup,
        lffBarrage
    },
    data() {
        return {
            evn_version: app.globalData['evn_version'],
            loading: true,
            screen_pic: '',
            screen_pic_show: false,
            screen_pic_hide: false,
            screen_pic_count_down: null,
            id: '',
            userid: '',
            is_my_activity: false,
            detail: {},
            user_details: {},
            error: '',
            join_popup_show: false,
            update_attend_details: false,
            headimg: '',
            username: '',
            checked: 0,
            is_joining: false,
            password: '',
            technology_support: false,
            must_submit: [],
            headimg_plugin: [],
            password_dialog_show: false,
            popup_open: false,
            news_detail: null,
            platform: uni.getSystemInfoSync().platform,
            word_list: [],
            no_word_list: [],
            can_used_times: 0,
            popup_word_data: {},
            get_word_success_data: {},
            synthetise_res: {
                status: 1,
                info: '恭喜合成成功'
            }
        }
    },

    computed: {
        initFreeTimes() {
            const times = Number(this.detail?.conf?.active?.init_times)
            return isNaN(times) ? 0 : times
        },
        helpTimes() {
            const help_times = Number(this.detail?.conf?.active?.everyday_help_times)
            return isNaN(help_times) ? 0 : help_times
        },
        everydayFreeTimes() {
            const times = Number(this.detail?.conf?.active?.everyday_times)
            return isNaN(times) ? 0 : times
        }
    },

    onLoad(e) {
        console.log('活动详情页面路径参数', e)

        let isH5 = false
        // #ifdef H5
        isH5 = true
        // #endif
        if (isH5) return this.$uni.showModal('请在小程序内打开', {success: () => uni.navigateBack()})

        if (uni.getLaunchOptionsSync().scene === 1154) return this.getSimpleDetail(e.id)

        e.screen_pic ? this.screenPicShow(e.screen_pic) : this.$uni.showLoading('数据加载中...')

        if (e.scene) {
            const sceneStr = decodeURIComponent(e.scene)
            console.log(sceneStr)
            const id = utils.getUrlParams('id', sceneStr) || ''
            const userid = utils.getUrlParams('uid', sceneStr) || ''
            e.id ||= id
            e.userid ||= userid
        }

        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            if (e.scene) return this.analysisScene(e.scene)

            if (!e.id) {
                this.loading = false
                this.error = '请指定活动id'
                return uni.hideLoading()
            }


            this.id = e.id

            this.userid = app.globalData['userid']

            this.getDetail()

        }, e.userid, e.id)
    },

    onUnload() {
        if (bs_socket.socketTask) {
            this.unload = true
            bs_socket.socketTask.close()
        }
    },

    onShow() {
        if (app.globalData['answer_get_word']) {
            const word = app.globalData['answer_get_word']
            app.globalData.answer_get_word = null
            const word_item = this.word_list.find(v => v.word === word)
            if (!word_item) return false

            const info_tips = word_item.describe || `${word_item.word}”字`
            this.get_word_success_data = {
                word: word_item.word,
                logo: word_item.logo || '',
                info: `恭喜你通过答题获得一个 ${info_tips}！`
            }

            this.uniPopupOpen('get_word_success')

            this.getUserWordList()
        }
    },

    onShareAppMessage() {
        this._utils.shareCopyActivityId(this.detail)

        const {title, path, imageUrl} = this.share_data
        return {
            title,
            path,
            imageUrl
        }
    },

    onShareTimeline() {
        return {
            title: this.detail.name,
            imageUrl: this.detail.logo || ''
        }
    },

    methods: {
        setActivityShareData() {
            let url = `/pages/words/user/detail?id=${this.id}&userid=${this.userid}`
            if (this.detail?.conf?.active?.screen_pic) url += `&screen_pic=${this.detail.conf.active.screen_pic}`

            this.share_data = {
                path: url,
                title: this.detail.name,
                imageUrl: this.detail.logo || ''
            }
        },

        async getUserWordList() {
            const res = await xwy_api.request({
                url: 'front.flat.active.collect_word.user.collectWord/collect_words_list',
                data: {
                    active_id: this.id
                }
            })


            const all_word_list = this.detail?.conf?.active?.word_list || []
            const user_word_list = res?.data?.['words_list'] || []
            const no_word_list = []

            all_word_list.forEach(all => {
                all.child = []
                all.holp = 0
                user_word_list.forEach(user => {
                    if (user.word === all.word) {
                        all.child.push(user)
                        all.holp++
                    }
                })
                if (all.child.length === 0) no_word_list.push(all.word)
            })


            this.word_list = all_word_list
            this.no_word_list = no_word_list


        },


        async getWordTimes() {
            const res = await xwy_api.request({
                url: 'front.flat.active.collect_word.user.collectWord/user_active_can_used_times',
                data: {
                    active_id: this.id
                }
            })

            let can_used_times = 0
            if (res?.data?.can_used_times) can_used_times = res.data.can_used_times
            this.can_used_times = can_used_times
        },

        async getWord() {
            if (!this.user_details?.checked) {
                xwy_api.alert('请先参加活动或等待管理员审核后再来抽字')
                return false
            }

            if (this.can_used_times === 0) {
                let tips = '没有抽字机会了'
                if (this.helpTimes > 0) tips += ',快去邀请好友助力增加抽字机会吧!'
                return this.$uni.showModal(tips)
            }

            if (!activity_tool.actionCheck({
                is_joining: this.is_joining,
                checked: this.checked,
                begin_time: this.detail.begin_time,
                end_time: this.detail.end_time
            })) return false


            this.$uni.showLoading('正在抽字中...')
            const res = await xwy_api.request({
                url: 'front.flat.active.collect_word.user.collectWord/user_collect_words',
                data: {
                    active_id: this.id
                }
            })

            await this.getWordTimes()
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '抽字失败')

            const word = res.data['collect_word']
            const word_item = this.word_list.find(v => v.word === word)
            let info = res.info
            if (word_item.describe) info = `恭喜你获得一个 ${word_item.describe}！`
            this.get_word_success_data = {
                word,
                logo: word_item && word_item.logo || '',
                info
            }

            this.uniPopupOpen('get_word_success')

            await this.getUserWordList()
        },


        mustSubmitCheck() {
            const blank_entry = this.must_submit.filter(v => v.rules && !v.value).map(v => v.title)
            if (blank_entry.length) {
                const blank_entry_str = blank_entry.join('、')
                this.$uni.showModal(`需要完善 ${blank_entry_str} 才能合成。`, {
                    showCancel: true,
                    confirmText: '去完善',
                    success: res => res.confirm && this.updateAttendDetailShow()
                })
                return false
            }
            return true
        },

        async synthetise(close_popup = false) {
            if (this.user_details.finished_time) return this.$uni.showToast('你已合成过啦，无需重复合成')

            const word_list = this.word_list

            const no_word_list = word_list.filter(item => item.child.length === 0).map(item => item.describe || item.word)

            if (no_word_list.length) {
                xwy_api.alert('还未集齐哦，无法合成。缺少 [' + no_word_list.join('、') + ']')
                return false
            }

            if (!this.mustSubmitCheck()) return

            const confirm = await this.$uni.showModal('合成成功后无法继续抽字，是否合成?', {showCancel: true})
            if (!confirm.confirm) return

            this.$uni.showLoading('合成中...')
            const res = await xwy_api.request({
                url: 'front.flat.active.collect_word.user.collectWord/together_all_words',
                data: {
                    active_id: this.id
                }
            })
            uni.hideLoading()

            close_popup && this.uniPopupClose('get_word_success')

            this.synthetise_res.status = res.status || 0
            this.synthetise_res.info = res.info || '合成失败，请重试'
            this.uniPopupOpen('synthetise')

            await this.getUserStatus()
        },

        clickWord(item) {
            // if (!item.holp) return false
            this.popup_word_data = item

            // 只有一个字不能并且已合成，不能送给别人   没有集到的字，不能送给别人
            if ((item.holp === 1 && this.user_details.finished_time) || !item.holp) {
                this.uniPopupOpen('word_item')
                return false
            }
            this.setWordShareData(item)
        },

        closeWordItemPopup() {
            this.uniPopupClose('word_item')
            this.setActivityShareData()
        },

        async setWordShareData(item) {
            this.$uni.showLoading()
            const res = await xwy_api.request({
                url: 'front.flat.active.collect_word.user.shareWords/share_words_to_friend',
                data: {
                    active_id: this.id,
                    id: item.child[0].id
                }
            })
            uni.hideLoading()

            const {nickname = '', headimg = ''} = app.globalData['userinfo'] || {}
            let path = `/pages/words/user/share_word?active_id=${this.id}&word=${item.word}&uuid=${res?.data?.uuid || ''}&nickname=${nickname}&headimg=${headimg}&userid=${this.userid}`
            if (item.logo) path += `&logo=${item.logo}`
            if (this.detail.rank_set?.closed_AD) path += `&close_ad=1`

            const title_tips = item.describe || `“${item.word}”字`

            this.share_data = {
                path,
                title: `${nickname}送你一个 ${title_tips}`,
                imageUrl: item.logo || ''
            }
            this.uniPopupOpen('word_item')
        },


        toAnswerList() {
            if (!this.user_details?.checked) {
                xwy_api.alert('请先参加活动或等待管理员审核后再来抽字')
                return false
            }


            if (!activity_tool.actionCheck({
                is_joining: this.is_joining,
                checked: this.checked,
                begin_time: this.detail.begin_time,
                end_time: this.detail.end_time
            })) return false

            uni.navigateTo({
                url: `./answer/list?id=${this.id}`
            })
        },

        toTopList() {
            // uni.removeStorageSync('top_rank_banner')
            // if (this.detail.conf?.active?.top_rank_banner?.length) {
            // 	uni.setStorageSync('top_rank_banner', this.detail.conf.active.top_rank_banner)
            // }
            //
            // let closed_AD = 0
            //
            // if (this.detail.rank_set) {
            // 	const rank_set = this.detail.rank_set
            // 	if (rank_set.closed_AD) closed_AD = rank_set.closed_AD
            // }
            //
            // uni.navigateTo({
            // 	url: `./ranking_list?id=${this.id}&closed_AD=${closed_AD}`
            // })

            uni.navigateTo({
                url: '/pages/ranking-list/ranking-list?id=' + this.id
            })
        },


        toBulletScreen() {
            const {userinfo} = app.globalData
            const nickname = this.must_submit?.[0]?.value || userinfo.nickname || '匿名用户'
            const headimg = this.headimg || userinfo.headimg || ''

            let url = `/pages/activity/other/bullet_screen?active_id=${this.id}&nickname=${nickname}&headimg=${headimg}`

            const barrage = this.active_more_data?.['active_conf_set']?.['barrage']
            if (barrage) {
                const {background, submit_button_text, navigation_bar} = barrage
                if (background) url += `&bgi=${background}`
                if (submit_button_text) url += `&send_text=${submit_button_text}`
                if (navigation_bar) url += `&navigation_bar=${JSON.stringify(navigation_bar)}`
            }
            this.$uni.navigateTo(url)
        },

        webSocketInit(reconnect = false) {
            bs_socket.webSocketInit(() => {
                bs_socket.socketTask.onMessage(res => {
                    console.log('【WEBSOCKET】收到消息', res.data)
                    this.receiveMessages(res.data)
                })
                bs_socket.socketTask.onOpen(res => {
                    console.log('【WEBSOCKET】', '链接成功！', res)
                    uni.hideLoading()
                    if (!reconnect) this.joinSocket()
                })
                bs_socket.socketTask.onClose(res => {
                    console.log('【WEBSOCKET】链接关闭！', res)
                    uni.hideLoading()
                    !this.unload && this.webSocketInit(true)
                })
            })
        },

        joinSocket() {
            const {userinfo} = app.globalData
            const nickname = this.must_submit?.[0]?.value || userinfo.nickname || '匿名用户'
            const headimg = this.headimg || userinfo.headimg || ''

            const data = {
                active_id: this.id,
                userid: this.userid,
                nickname,
                headimg,
                message: '进入活动'
            }
            bs_socket.socketTask.send({
                data: JSON.stringify(data)
            })
        },

        receiveMessages(message) {
            message = JSON.parse(message)
            if (message.active_id !== this.id) return false
            this.$refs.lffBarrage.add({item: message})
        },


        async getSimpleDetail(id) {
            uni.showLoading({
                mask: true,
                title: '数据加载中...'
            })
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details_simple',
                data: {
                    active_id: id
                }
            })
            this.loading = false
            uni.hideLoading()

            if (!res || !res.data || !res.data.active_details) {
                this.loading = false
                uni.hideLoading()
                this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                return false
            }


            if (res.data.active_more_data) {
                const active_more_data = res.data.active_more_data
                this.active_more_data = active_more_data
                if (active_more_data.technology_support) {
                    this.technology_support = active_more_data.technology_support
                }
                const headimg_plugin = active_more_data['active_conf_set']?.headimg_plugin
                if (headimg_plugin?.length) {
                    this.headimg_plugin = headimg_plugin
                }
            }


            const detail = res.data.active_details

            this.detail = detail

            my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)


            if (detail.conf) {
                const conf = detail.conf

                if (!this.screen_pic && conf.active?.screen_pic) {
                    this.screenPicShow(conf.active.screen_pic)
                }


                if (conf.must_submit) {
                    const must_submit = conf.must_submit
                    delete conf.must_submit
                    if (must_submit.length) {
                        must_submit.forEach(v => v.value = v.value || '')
                        this.must_submit = must_submit
                    }
                }
            }


            this.addLookRecords()

            if (detail.name) {
                uni.setNavigationBarTitle({
                    title: detail.name
                })
            }
        },

        toIdeaAvatar(item) {
            if (!item.img_list || !item.img_list.length) {
                uni.showModal({
                    title: '提示',
                    content: '活动未配置头像图片列表，无法使用此功能',
                    showCancel: false
                })

                return false
            }

            let path = '/pages/idea_avatar/pendant/pendant'
            if (item.key === 'beijing') path = '/pages/idea_avatar/background/background'
            path += `?id=${this.id}`
            if (this.detail.rank_set?.closed_AD) path += '&closed_AD=1'

            this.$uni.navigateTo(path, {
                success: res => res.eventChannel.emit('img_list', item.img_list)
            })
        },


        screenPicShow(src) {
            let isH5 = false
            // #ifdef H5
            // h5不显示开屏图
            isH5 = true
            // #endif
            if (isH5) return

            uni.hideLoading()

            this.screen_pic = src
            this.screen_pic_count_down = 5
            this.screen_pic_show = true
            interval = setInterval(() => {
                this.screen_pic_count_down--
                if (this.screen_pic_count_down <= 0) {
                    this.skipScreen()
                }
            }, 1000)
        },
        skipScreen() {
            clearInterval(interval)
            this.screen_pic_count_down = 0
            this.screen_pic_hide = true

            const timeout = setTimeout(() => {
                this.screen_pic_show = false
                this.screen_pic_hide = false
                if (this.loading) {
                    uni.showLoading({
                        mask: true,
                        title: '数据加载中...'
                    })
                }
                this.passwordDialogShow()

                if (this.detail.rank_set?.['batch_import'] && this.detail.conf.active.enter_types === 4 && !this.is_joining) {
                    this.$refs.input_username_password.open({
                        detail: this.detail,
                        success: () => {
                            this.$uni.showLoading()
                            this.getDetail()
                        }
                    })
                }

                clearTimeout(timeout)
            }, 500)
        },
        analysisScene(scene) {
            const sceneStr = decodeURIComponent(scene)
            console.log(sceneStr)
            const id = utils.getUrlParams('id', sceneStr)
            console.log('id===', id)
            if (!id) {
                uni.hideLoading()
                return this.$uni.showModal('从二维码获取id失败')
            }

            this.getActiveId(id)
        },


        async getActiveId(id) {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/get_long_active_id_by_short_id',
                data: {id}
            })

            const long_id = res?.data?.['long_active_id']

            if (!long_id) {
                uni.hideLoading()
                return this.$uni.showModal(res?.info || '长id获取失败')
            }

            this.id = long_id
            this.getDetail()
        },

        getDetail(just_update = false) {
            xwy_api.getActivityDetail(this.id, res => {
                if (!res || !res.data || !res.data.active_details) {
                    this.loading = false
                    uni.hideLoading()
                    this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                    return false
                }


                // #ifndef H5
                this.$refs.expirationReminder.open(res.data.active_details)
                // #endif

                const active_more_data = res.data.active_more_data
                if (active_more_data) {
                    this.active_more_data = active_more_data
                    if (active_more_data.technology_support) {
                        this.technology_support = res.data.active_more_data.technology_support
                    }
                    const headimg_plugin = active_more_data['active_conf_set']?.headimg_plugin
                    if (headimg_plugin?.length) {
                        this.headimg_plugin = headimg_plugin
                    }
                }


                const detail = res.data.active_details

                app.globalData.activity_detail = detail

                this.detail = detail


                this.getUserWordList()

                my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)

                this.setActivityShareData()

                if (detail.conf) {
                    const conf = detail.conf

                    if (conf.active) {
                        const active = conf.active
                        if (!this.screen_pic && active.screen_pic) {
                            this.screenPicShow(active.screen_pic)
                        }
                        if (active.news?.news_id) this.changeDetailContent(active.news.news_id)
                    }

                    if (conf.must_submit) {
                        const must_submit = conf.must_submit
                        delete conf.must_submit
                        if (must_submit.length) {
                            must_submit.forEach(v => v.value = v.value || '')
                            this.must_submit = must_submit
                        }
                    }
                }

                if (detail.rank_set) {
                    const rank_set = detail.rank_set
                    // 是否纯净版，纯净版显示tab栏，隐藏返回首页按钮
                    if (rank_set['shield_other']) {
                        this.$uni.hideHomeButton()

                        // 更新纯净版缓存信息
                        utils.updateShieldOtherInfo(this.detail)
                    }

                    // #ifdef MP-WEIXIN
                    if (rank_set?.['share_closed']) uni.hideShareMenu(undefined)
                    // #endif
                }


                this.addLookRecords()

                if (detail.name) {
                    uni.setNavigationBarTitle({
                        title: detail.name
                    })
                }

                if (!just_update && this.userid === detail.userid) {
                    this.is_my_activity = true
                }


                this.getUserStatus()

            })
        },


        addLookRecords() {
            const detail = this.detail
            const value = {
                active_id: detail.active_id,
                name: detail.name,
                types: detail.types,
                logo: detail.logo || this.xwy_config.active_default_logo,
                look_time: new Date().getTime()
            }

            if (detail.organizer) value.organizer = detail.organizer
            my_storage.addActivityLookRecords(value)
        },


        async getUserStatus() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: {
                    active_id: this.id
                }
            })

            if (res?.data?.user_details) {
                const attend_details = res.data.user_details
                this.user_details = attend_details
                this.is_joining = true
                this.checked = attend_details.checked || 0

                await this.getWordTimes()

                this.loading = false
                uni.hideLoading()

                if (attend_details.nickname) this.username = attend_details.nickname
                if (attend_details.headimg) this.headimg = attend_details.headimg

                if (attend_details.must_submit) {
                    this.must_submit.forEach((v, index) => {
                        attend_details.must_submit.forEach(vv => {
                            if (vv.title === v.title) this.$set(this.must_submit[index], 'value', vv.value)
                        })
                    })
                }


            } else {
                this.no_attend = true
                this.loading = false
                uni.hideLoading()

                this.detail?.rank_set?.['barrage'] && this.webSocketInit()

                if (this.screen_pic_show) return false

                this.passwordDialogShow()

                if (this.detail.rank_set?.['batch_import'] && this.detail.conf.active.enter_types === 4) {
                    this.$refs.input_username_password.open({
                        detail: this.detail,
                        success: () => {
                            this.$uni.showLoading()
                            this.getDetail()
                        }
                    })
                    return false
                }
            }

        },


        async changeDetailContent(news_id) {
            const res = await this.xwy_api.request({
                url: 'front.news/news_details',
                data: {news_id}
            })
            uni.hideLoading()

            const details = res?.data?.['news_details']

            if (!details) {
                return this.$uni.showModal(res?.info || '文章内容获取失败', {success: () => uni.navigateBack()})
            }


            if (details.video_url) {
                if (details.video_url.startsWith('http://') || details.video_url.startsWith('https://')) {
                    details.video_type = 'http'
                } else {
                    details.video_type = 'txv_id'
                }
            }


            if (details.content) {
                details.content = utils.newsContentInit(details.content)
            }

            this.news_detail = details
        },


        passwordDialogShow() {
            if (this.detail.conf.active.password && this.no_attend) {
                const passwordDialogShow = () => {
                    this.password_dialog_show = true
                    const password = my_storage.getActivityPassword(this.id)
                    if (password) {
                        this.password = password
                        this.checkActivityPassword(password)
                    }
                    const timeout = setTimeout(() => {
                        this.$refs.input_password.open()
                        clearTimeout(timeout)
                    }, 30)
                }

                // 体验版不需要输入密码
                if (app.globalData['evn_version'] === 'trial') {
                    uni.showModal({
                        title: '提示',
                        content: '此活动设置了活动密码，请勿报名参与活动！！！',
                        cancelText: '进入活动',
                        confirmText: '输入密码',
                        success: res => {
                            res.confirm && passwordDialogShow()
                        }
                    })
                    return false
                }


                passwordDialogShow()
            }
        },

        passwordInputConfirm(val) {
            if (!val) {
                // 纯净版取消输入密码出现去个人中心选项
                if (this.detail?.rank_set?.shield_other) {
                    uni.showModal({
                        title: '提示',
                        content: '请输入密码',
                        cancelText: '个人中心',
                        confirmText: '重新输入',
                        success: res => {
                            if (res?.confirm) {
                                this.$refs.input_password.open()
                            }
                            if (res?.cancel) {
                                uni.navigateTo({
                                    url: '/pages/user/user'
                                })
                            }
                        }
                    })
                    return
                }
                xwy_api.alert('请输入密码', {
                    success: () => this.$refs.input_password.open()
                })
                return false
            }
            this.checkActivityPassword(val)
        },

        async checkActivityPassword(password) {
            uni.showLoading({
                title: '密码验证中...',
                mask: true
            })

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/check_active_password',
                data: {
                    active_id: this.id,
                    password
                }
            })
            uni.hideLoading()

            if (res?.status) {
                my_storage.rememberActivityPassword(this.id, password)
                this.$refs.input_password.close()
                uni.showToast({
                    title: '密码正确',
                    icon: 'success'
                })

                return false
            }


            xwy_api.alert(res && res.info || '密码错误', {
                success: () => this.$refs.input_password.open()
            })
        },

        passwordInputClose() {
            this.copy(this.id, true)

            if (getCurrentPages().length > 1) {
                uni.navigateBack()
                return false
            }

            // 纯净版并且没有上一页面，重新弹出输入密码窗口
            if (app.globalData['shop_info']?.extend_set?.shield_other_active?.active_id) {
                // xwy_api.alert('请输入活动密码', {
                // 	success: () => this.$refs.input_password.open()
                // })
                // 不延迟的话，弹不出来
                setTimeout(() => {
                    this.$refs.input_password.open()
                }, 500)
                uni.navigateTo({
                    url: '/pages/user/user'
                })
                return false
            }

            uni.reLaunch({
                url: '/pages/index/index',
                fail: err => {
                    console.log(err)
                }
            })
        },


        async updateHeadimg() {
            uni.navigateTo({
                url: `/pages/other/headimg-list/headimg-list?active_id=${this.id}`,
                events: {
                    newImg: async obj => {
                        if (!obj?.src) return
                        uni.showLoading({
                            title: '修改中...',
                            mask: true
                        })
                        if (!obj.temp) {
                            this.updateAttendDetail(obj.src)
                            return false
                        }
                        const data = {
                            temp_data: {
                                path: obj.src
                            },
                            is_temp: 5
                        }
                        if (obj.size) data.temp_data.size = obj.size
                        const headimg = await xwy_api.uploadOneImage(data)
                        this.updateAttendDetail(headimg)
                    }
                }
            })
        },

        async joinActivity() {
            if (this.loading) return

            if (this.detail?.conf?.active?.ip_set?.open) {
                uni.showLoading()
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.user/check_user_city',
                    data: {
                        active_id: this.id
                    }
                })
                uni.hideLoading()
                if (!res?.status) {
                    xwy_api.alert(res?.info || '你当前不在所限定的城市范围内。无法参与本活动。')
                    return
                }
            }

            this.join_popup_show = true
        },

        updateAttendDetailShow() {
            this.update_attend_details = true
            this.join_popup_show = true
        },

        mustValueChange(e, index) {
            this.must_submit[index].value = this.must_submit[index].options[e.detail.value].text
            this.$forceUpdate()
        },


        cancelJoin() {
            this.join_popup_show = false
            this.update_attend_details = false
        },

        joinAjax() {
            if (this.update_attend_details) {
                this.updateAttendDetail()
                return false
            }


            this.joining()
        },


        getMustSubmitData() {
            const must_submit = JSON.parse(JSON.stringify(this.must_submit))
            for (let i = 0; i < must_submit.length; i++) {
                const v = must_submit[i]
                v.options && delete v.options
                if (v.rules === 1 && v.value === '') {
                    let tips = '输入'
                    if (v.types === 2) tips = '选择'
                    uni.showToast({
                        title: `请${tips}${v.title}`,
                        mask: true,
                        icon: v.title.length <= 4 ? 'error' : 'none'
                    })
                    return false
                }
            }
            console.log(must_submit)
            let must_submit_str = JSON.stringify(must_submit)
            must_submit_str = must_submit_str.replace(/·/g, '-')
            return base64['encode'](must_submit_str)
        },

        updateAttendDetail(headimg) {
            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id
            }

            if (headimg) data.headimg = headimg

            const must_submit = this.getMustSubmitData()
            if (must_submit === false) return false
            data.must_submit = must_submit

            uni.showLoading({
                title: '修改中...',
                mask: true
            })

            xwy_api.ajax({
                url: 'front.flat.sport_step.user/update_attend_details',
                data,
                success: res => {
                    if (!res.status) {
                        uni.showModal({
                            title: '提示',
                            content: res.info || '修改失败',
                            showCancel: false
                        })
                    }

                    uni.showToast({
                        title: res.info || '修改成功'
                    })

                    this.cancelJoin()
                    this.getDetail()
                }
            })
        },

        joining() {

            const data = {
                active_id: this.id,
                access_token: app.globalData['access_token']
            }


            if (this.must_submit && this.must_submit.length) {
                const must_submit = this.getMustSubmitData()
                if (must_submit === false) return false
                data.must_submit = must_submit
            }

            data.nickname = this.username

            this.loading = true
            this.$uni.showLoading('报名中...')


            xwy_api.ajax({
                url: 'front.flat.sport_step.user/submit_attend_active',
                data,
                success: res => {
                    this.loading = false
                    uni.hideLoading()
                    console.log('报名活动', res)
                    if (!res.status) {
                        uni.showModal({
                            title: '报名失败',
                            content: res.info || '报名失败',
                            showCancel: false
                        })
                        return false
                    }

                    this.join_popup_show = false
                    uni.showToast({
                        title: res.info || '报名成功',
                        icon: 'success'
                    })

                    const timeout = setTimeout(() => {
                        uni.showLoading({
                            mask: true
                        })
                        this.getDetail()
                        clearTimeout(timeout)
                    }, 1000)
                }
            })
        },


        showActiveSharePopup() {
            this.$refs.activeShare.open({
                page: 'pages/words/user/detail',
                scene: 'id=' + this.detail.id + '&uid=' + app.globalData['userid'],
                qrcode_logo: this.detail.rank_set?.closed_AD && this.detail.conf?.active?.qrcode_logo
            })
        },

        toHelp() {
            let url = `./help?id=${this.id}`
            if (this.user_details?.headimg) url += `&headimg=${this.user_details.headimg}`
            if (this.user_details?.must_submit?.length && this.user_details.must_submit[0].value) {
                url += `&nickname=${this.user_details.must_submit[0].value}`
            }
            uni.navigateTo({url})
        },

        copy(data, hide_toast = false) {
            uni.setClipboardData({
                data,
                success() {
                    if (hide_toast) {
                        uni.hideToast();
                        return false;
                    }
                    uni.showToast({
                        title: '复制成功',
                        icon: 'none',
                        duration: 500
                    })
                }
            })
        },

        uniPopupClose(ref) {
            this.popup_open = false
            this.$refs[ref].close()
        },

        uniPopupOpen(ref) {
            this.popup_open = true
            this.$refs[ref].open()
        }

    }
}
</script>

<style scoped>
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 80px;
}

.bdb-10 {
    border-bottom: 10px solid #f8f8f8;
}

.icon-list {
    position: sticky;
    top: 0;
    z-index: 9;
    border-top: 1px solid #eee;
}

.icon-item {
    padding: 10px 0;
    width: calc(100% / 4);
    /* border-top: 1px solid #eee; */
    box-sizing: border-box;
}


.headimg {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}


.join-btn {
    width: 250px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 20px;
}

.join-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, .7);
    z-index: 9999;
}

.join-popup-c {
    border-radius: 10px;
    overflow: hidden;
}


.join-input {
    margin: 10px;
    padding: 10px;
    border: 1px solid #bbb;
    border-radius: 5px;
    width: 70vw;
    max-width: 400px;
}

.join-popup-btns {
    border-top: 1px solid #eee;
}

.join-popup-btn-cancel, .join-popup-btn-confirm {
    width: 50%;
    box-sizing: border-box;
    line-height: 44px;
}

.join-popup-btn-cancel {
    border-right: 1px solid #eee;
}


.uni-popup-info {
    position: relative;
    width: 280px;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    z-index: 999;
    left: 50%;
    bottom: -5px;
    margin-left: -12px;
}

.detail-popup {
    width: 95vw;
    padding-bottom: 15px;
}

.word-item {
    width: 60px;
    height: 60px;
    text-align: center;
    margin: 5px 0;
    border-radius: 5px;
    font-size: 30px;
    line-height: 60px;
    position: relative;
    overflow: hidden;
}

.word-item image {
    width: 60px;
    height: 60px;
    border-radius: 5px;
    display: inline-block;
}

.have-word {
    background-color: #d94948;
    color: #f6e0b3;
}

.no-word {
    background-color: #bbbec4;
    color: #80848f;
}

.holp {
    position: absolute;
    right: 2px;
    top: 2px;
    min-width: 14px;
    height: 14px;
    line-height: 14px;
    text-align: center;
    border-radius: 7px;
    background-color: #f6e0b3;
    color: #d94948;
    font-size: 12px;
}

.plr3 {
    padding-left: 3px;
    padding-right: 3px;
}

.get-word {
    background-color: #d94948;
    color: #f6e0b3;
    width: 200px;
    line-height: 40px;
    border-radius: 20px;
    overflow: hidden;
}

.get-word-success {
    width: 300px;
    border-radius: 10px;
    background-color: #d94948;
    overflow: hidden;
}

.get-word-success-word {
    position: relative;
    width: 100px;
    height: 100px;
}

.get-word-success-word-text, .get-word-success-word-bg {
    width: 100px;
    height: 100px;
    position: absolute;
    top: 0;
    left: 0;
}

.get-word-success-word-text image {
    width: 100px;
    height: 100px;
    border-radius: 10px;
    display: inline-block;
}

.get-word-success-word-text {
    font-size: 70px;
    font-weight: 600;
    z-index: 2;
}

.get-word-success-word-bg {
    width: 100px;
    height: 100px;
    border-radius: 10px;
    padding: 2px;
    box-sizing: border-box;
    background-color: #f6e0b3;
    top: 0;
    left: 0;
    z-index: 1;
    transform: rotate(45deg)
}

.get-word-success-word-bg2 {
    background-color: #d94948;
    width: 96px;
    height: 96px;
    padding: 2px;
    box-sizing: border-box;
    border-radius: 10px;
}

.get-word-success-word-bg3 {
    background-color: #f6e0b3;
    width: 92px;
    height: 92px;
    border-radius: 10px;
}

.get-word-success-synthetise {
    width: 150px;
    background-color: #d94948;
    color: #f6e0b3;
    line-height: 36px;
    border-radius: 18px;
}
</style>
