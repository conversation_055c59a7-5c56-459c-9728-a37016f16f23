<template>
    <view>
        <view class="show">
            <view class="add-words color-light-primary flex-row" @click="addWords">
                <view>添加文字</view>
                <uni-icons type="forward" size="16" color="#5cadff"/>
            </view>

            <view class="words-item flex-row" v-for="(item, index) in wordsList" :key="item.word">
                <view class="logo">
                    <image v-if="item.logo" class="logo-image" :src="item.logo" mode="aspectFill"/>
                    <view v-else class="no-logo flex-all-center">
                        <uni-icons type="image" color="#bbbec4" size="60"/>
                    </view>
                </view>
                <view class="pl10 pt5 color-content">
                    <view>文字: {{ item.word }}</view>
                    <view v-if="item.describe">文字描述: {{ item.describe }}</view>
                    <view>抽中概率: {{ item.percent }}%</view>
                    <template v-if="stockOpen">
                        <view v-if="item.stock">库存: {{ item.stock }}</view>
                        <view v-if="item.per_person_num">获取限制: 每人最多获得{{ item.per_person_num }}个</view>
                    </template>
                </view>
                <view class="controls flex-kai">
                    <view class="control flex-all-center" @click="sortChange(index, 'up')">
                        <text class="iconfont icon-to-top color-sub font18"></text>
                    </view>
                    <view class="control flex-all-center" @click="sortChange(index, 'down')">
                        <text class="iconfont icon-to-down color-sub font18"></text>
                    </view>
                    <view class="control flex-all-center" @click="editWords(index)">
                        <text class="iconfont icon-edit color-sub font18"></text>
                    </view>
                    <view class="control flex-all-center" @click="this.wordsList.splice(index, 1)">
                        <text class="iconfont icon-delete color-sub font18"></text>
                    </view>
                </view>
            </view>
        </view>


        <uni-popup ref="popup" @touchmove.stop.prevent="">
            <view class="popup--- bg-white">
                <view class="text-center pb10">文字配置</view>

                <scroll-view class="popup---scroll" scroll-y="true">
                    <view class="flex-all-center">
                        <view class="logo" @click="changeLogo">

                            <image v-if="editWordsData.logo" class="logo-image"
                                   :src="editWordsData.logo" mode="aspectFill"/>
                            <view v-else class="logo-image flex-all-center">
                                <uni-icons type="image" color="#bbbec4" size="60"/>
                            </view>
                        </view>
                    </view>
                    <view class="flex-all-center pt5">
                        <view class="color-light-primary font14" @click="changeLogo">
                            {{ editWordsData.logo ? '修改' : '设置' }}文字图片
                        </view>
                    </view>

                    <uni-forms label-position="top" label-width="200" border>
                        <uni-forms-item label="文字" required>
                            <view class="label-tips">
                                没有设置文字图片，文字请输入一个字。设置了文字图片，文字可输入多个字。
                            </view>
                            <uni-easyinput v-model="editWordsData.word" maxlength="10"/>
                        </uni-forms-item>
                        <uni-forms-item label="文字描述">
                            <uni-easyinput v-model="editWordsData.describe" maxlength="50"/>
                        </uni-forms-item>
                        <uni-forms-item label="抽中概率">
                            <view class="label-tips">输入0-100，数越大抽中的概率越大。</view>
                            <uni-easyinput type="digit" v-model="editWordsData.percent" maxlength="5"/>
                        </uni-forms-item>
                        <template v-if="stockOpen">
                            <uni-forms-item label="库存">
                                <view class="label-tips">不填库存为0。</view>
                                <uni-easyinput type="number" v-model="editWordsData.stock" maxlength="8"/>
                            </uni-forms-item>
                            <uni-forms-item label="获取限制">
                                <view class="label-tips">设置每人最多能获取多少个这个字，不限制不填。</view>
                                <uni-easyinput type="number" v-model="editWordsData.per_person_num"
                                               maxlength="5"/>
                            </uni-forms-item>
                        </template>
                    </uni-forms>
                </scroll-view>

                <view class="edit-buttons flex-all-center font14">
                    <view class="bg-background color-sub" hover-class="navigator-hover"
                          @click="$refs.popup.close()">取消
                    </view>
                    <view class="bg-light-primary color-white" hover-class="navigator-hover"
                          @click="confirm">确定
                    </view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "words-list-set",
    emits: ['update:list'],
    props: ['list', 'activeId', 'stockOpen'],
    data() {
        return {
            wordsList: [],
            editWordsData: {
                logo: '',
                word: '',
                describe: '',
                percent: '',
                stock: '',
                per_person_num: ''
            },
            editIndex: null
        }
    },

    watch: {
        list(val) {
            this.wordsList = val
        },

        wordsList(val) {
            this.$emit('update:list', val)
        }
    },

    mounted() {
        this.wordsList = this.list
    },

    methods: {
        addWords() {
            this.editIndex = null
            this.editWordsData = {
                logo: '',
                word: '',
                describe: '',
                percent: '',
                stock: '',
                per_person_num: ''
            }
            this.$nextTick(() => this.$refs.popup.open())
        },

        editWords(index) {
            this.editIndex = index

            const editWordsData = this.wordsList[index]

            // 以前是没有描述、库存、每个人只能获得多少个的字段的
            editWordsData.describe ||= ''
            editWordsData.stock ||= ''
            editWordsData.per_person_num ||= ''

            this.editWordsData = editWordsData
            this.$nextTick(() => this.$refs.popup.open())
        },

        sortChange(index, type) {
            const list = JSON.parse(JSON.stringify(this.wordsList))
            const item = list[index]
            if (type === 'up') {
                if (index === 0) return
                list[index] = list[index - 1]
                list[index - 1] = item
            } else {
                if (index === list.length - 1) return
                list[index] = list[index + 1]
                list[index + 1] = item
            }
            this.wordsList = []
            this.$nextTick(() => {
                this.wordsList = list
            })
        },

        confirm() {
            const data = JSON.parse(JSON.stringify(this.editWordsData))
            if (!data.word) return this.$uni.showToast('请输入文字')
            const percent = Number(data.percent)
            if (isNaN(percent) || percent < 0 || percent > 100) return this.$uni.showToast('请输入正确的抽中概率')
            data.percent = percent

            if (this.stockOpen) {
                if (data.stock) {
                    const stock = Math.floor(data.stock)
                    if (isNaN(stock)) return this.$uni.showToast('请输入正确的库存')
                    data.stock = stock
                }

                if (data.per_person_num) {
                    const per_person_num = Math.floor(data.per_person_num)
                    if (isNaN(per_person_num)) return this.$uni.showToast('请输入获取限制')
                    data.per_person_num = per_person_num
                }
            } else {
                delete data.stock
                delete data.per_person_num
            }

            if (this.editIndex === null) {
                this.wordsList.push(data)
            } else {
                this.wordsList.splice(this.editIndex, 1, data)
            }
            this.$refs.popup.close()
        },

        changeLogo() {
            let url = '/pages/other/image_upload_or_select'
            if (this.activeId) url += `?active_id=${this.activeId}`
            this.$uni.navigateTo(url, {
                events: {
                    newImg: src => {
                        this.editWordsData.logo = src
                    }
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.show {
    position: relative;

    .add-words {
        position: absolute;
        right: 0;
        top: -35px;
    }
}

.words-item {
    padding: 10px;
    margin-top: 10px;
    border-radius: 10px;
    background-color: #f8f8f8;
    position: relative;
}

.logo {
    .logo-image, .no-logo {
        width: 60px;
        min-width: 60px;
        height: 60px;
        display: block;
        border-radius: 5px;
    }
}

.controls {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 135px;
    min-width: 135px;

    .control {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #fff;
    }
}

.popup--- {
    width: 300px;
    padding: 10px;
    border-radius: 10px;

    .popup---scroll {
        max-height: calc(90vh - 150px);
    }

    .edit-buttons {
        padding: 10px;
        border-top: 1px solid #eee;

        view {
            margin: 0 10px;
            width: 100px;
            line-height: 40px;
            border-radius: 20px;
            text-align: center;
        }
    }

    .label-tips {
        position: relative;
        top: -8px;
        color: #80848f;
        font-size: 12px;
    }
}

</style>