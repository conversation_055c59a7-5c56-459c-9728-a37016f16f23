<template>
    <view>
        <uni-popup ref="popup" type="bottom" :safe-area="false">
            <view class="popup">
                <uni-calendar ref="calendar" :date="date" clear-date lunar :endDate="endDate" 
                              @change="change"/>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "choose-date-popup",
    props: ['date', 'endDate'],
    methods: {
        open() {
            this.$refs.popup.open()
        },

        change(e) {
            this.$emit('change', e)
            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss">
.popup {
    background-color: #fff;
    padding-bottom: 15px;
    border-radius: 10px 10px 0 0;
    overflow: hidden;
}
</style>