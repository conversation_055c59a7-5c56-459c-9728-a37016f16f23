<template>
    <view>
        <uni-popup ref="popup" type="bottom" :safe-area="false" @touchmove.stop.prevent="">
            <view class="popup bg-white">
                <view class="flex-all-center">
                    <view class="year-switch" @click="yearChange('-')">
                        <uni-icons type="left" :color="year_index === 0 ? '#bbbec4' : '#495060'" size="22"/>
                    </view>

                    <picker :range="year_list" :value="year_index" @change="yearChange">
                        <view class="year-picker text-center font24">{{ year_ }}</view>
                    </picker>
                    <view class="year-switch" @click="yearChange('+')">
                        <uni-icons type="right" size="22"
                                   :color="year_index === year_list.length - 1 ? '#bbbec4' : '#495060'"/>
                    </view>
                </view>
                
                <view class="month-list flex-row flex-wrap">
                    <view class="month-item text-center" v-for="(item, index) in 12" :key="item" 
                          @click="yearMonthChange(index + 1)">
                        <text class="font28 color-content">{{ index + 1 }}</text>
                        <text class="color-sub">月</text>
                    </view>
                </view>

                <view class="whole-year bg-light-primary color-white text-center" @click="confirm(null)">
                    查看{{ year_ }}全年
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const THIS_YEAR = new Date().getFullYear()

export default {
    name: "year-month-choose-popup",
    emits: ['yearMonthChange'],
    props: ['year'],
    
    data() {
        return {
            year_: new Date().getFullYear(),
            month: new Date().getMonth() + 1,
            year_list: Array.from({ length: THIS_YEAR - 2000 + 1 }, (_, i) => 2000 + i)
        }
    },
    
    watch: {
        year(val) {
            this.year_ = val
        }
    },
    
    computed: {
        year_index() {
            const index = this.year_list.findIndex(year => year === this.year_)
            return index === -1 ? 0 : index
        }  
    },

    methods: {
        open() {
            this.$refs.popup.open()
        },

        yearChange(e) {
            if (e === '-') {
                if (this.year_index === 0) return
                this.year_--
            } else if (e === '+') {
                if (this.year_index === this.year_list.length - 1) return
                this.year_++
            } else {
                this.year_ = this.year_list[e.detail.value]
            }
        },

        yearMonthChange(month) {
            this.month = month
            this.confirm(month)
        },
        
        confirm(month) {
            this.$refs.popup.close()
            this.$emit('yearMonthChange', {year: this.year_, month})
        }
    }
}
</script>

<style lang="scss">
.popup {
    padding: 20px;
    border-radius: 10px 10px 0 0;
}

.year-picker {
    width: 80px;
}

.year-switch {
    width: 40px;
    text-align: center;
}

.month-list {
    padding: 20px 0;
    
    .month-item {
        width: 25%;
        height: 80px;
        line-height: 80px;
    }
}

.whole-year {
    margin: 0 auto;
    width: 200px;
    line-height: 40px;
    border-radius: 20px;
}
</style>