<template>
    <view>
        <uni-popup ref="sportTypeListPopup" type="bottom" :safe-area="false" @touchmove.stop.prevent="">
            <view class="sport-list-type-popup bg-white">
                <view class="flex-kai">
                    <view class="top-bar flex-row">
                        <view class="top-bar-item" :class="{'active-top-bar': item.type === top_bar_type}"
                              v-for="item in top_bar_list" :key="item.type" @click="top_bar_type = item.type">
                            {{ item.title }}
                        </view>
                    </view>
                    
                    <view v-show="top_bar_type === 'diy'" class="diy-manage-enter" @click="toManageUserType">
                        <uni-icons type="gear" size="14" color="#5cadff"/>
                        <text>管理</text>
                    </view>
                </view>
                

                <scroll-view :scroll-y="true" class="sport-type-list">
                    <view class="text-center flex-row flex-wrap">
                        <view v-for="item in typeList" :key="item.types" class="sport-type-item"
                              @click="$emit('clickItem', item)">
                            <template>
                                <image v-if="item.logo" class="sport-type-logo" :src="item.logo" 
                                       mode="aspectFill"/>
                                <view v-else class="sport-type-logo-word ellipsis">{{ item.name }}</view>
                            </template>
                            <view class="font14 color-content">{{ item.name }}</view>
                        </view>
                        
                        <view v-if="top_bar_type === 'diy' && typeList.length" class="sport-type-item"
                              @click="toAddUserType">
                            <view class="sport-type-add-icon flex-all-center color-disabled">
                                <uni-icons type="plusempty" size="24" color="#bbbec4"/>
                            </view>
                            <view class="font14 color-content">添加</view>
                        </view>
                    </view>
                    
                    <view v-if="top_bar_type === 'diy' && !typeList.length" class="empty-list">
                        <text class="iconfont icon-empty-state color-border"></text>
                        <view class="color-sub font12">无自定义运动类型</view>
                        <view class="add-user-type-button" @click="toAddUserType">去添加</view>
                    </view>
                </scroll-view>
                
                <view class="flex-all-center">
                    <view class="color-sub font14 p10" @click="$emit('cancel')">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import sportsCenterApi from '../api'

export default {
    name: "sport-type-list-popup",
    props: {
        sportTypeList: {
            type: Array,
            default() {
                return []
            }
        },
        diy: {
            type: Array,
            default() {
                return []
            }
        }
    },
    emits: ['clickItem', 'cancel', 'update:diy'],
    
    data() {
        return {
            top_bar_type: 'system',
            top_bar_list: [
                {type: 'system', title: '系统'},
                {type: 'diy', title: '自定义'}
            ]
        }  
    },
    
    computed: {
        typeList() {
            return this.top_bar_type === 'diy' ? this.diy : this.sportTypeList
        }
    },

    methods: {
        open() {
            this.$refs.sportTypeListPopup.open()
        },

        close() {
            this.$refs.sportTypeListPopup.close()
        },

        toManageUserType() {
            this.$uni.navigateTo('/pages/category/list?types=30', {
                events: {
                    uploadList: async () => {
                        const list = await sportsCenterApi.getUserSportTypesList(true)
                        this.$emit('update:diy', list)
                    }
                }
            })
        },

        toAddUserType() {
            this.$uni.navigateTo('/pages/category/edit?types=30', {
                events: {
                    upload: async () => {
                        const list = await sportsCenterApi.getUserSportTypesList(true)
                        this.$emit('update:diy', list)
                    }
                }
            })
        }
    }
}
</script>

<style lang="scss">
.sport-list-type-popup {
    padding: 15px 0;
    border-radius: 10px 10px 0 0;
    
    .top-bar .top-bar-item, .diy-manage-enter {
        font-size: 14px;
        margin: 10px;
        line-height: 32px;
    }
    
    .top-bar {
        .top-bar-item {
            margin: 10px;
            padding: 0 10px;
            line-height: 32px;
            border-radius: 16px;
            color: #666;
            font-size: 14px;
        }
        
        .active-top-bar {
            background-color: #5cadff;
            color: #fff;
        }
    }

    .diy-manage-enter {
        color: #5cadff;
    }

    .sport-type-list {
        padding: 10px 0;
        height: 270px;

        .sport-type-item {
            width: 150rpx;
            height: 70px;
            overflow: hidden;
            padding: 10px 0;

            .sport-type-logo, .sport-type-logo-word, .sport-type-add-icon {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                margin: 0 auto;
            }

            .sport-type-logo {
                display: block;
            }

            .sport-type-logo-word, .sport-type-add-icon {
                background-color: #f8f8f8;
            }

            .sport-type-logo-word {
                color: #80848f;
                font-size: 12px;
                line-height: 40px;
                padding: 5px;
                box-sizing: border-box;
            }
        }
        
        .empty-list {
            text-align: center;
            padding-top: 30px;
            
            .iconfont {
                font-size: 80px;
            }

            .add-user-type-button {
                width: 120px;
                line-height: 36px;
                border-radius: 18px;
                font-size: 14px;
                margin: 20px auto 0;
                background-color: #5cadff;
                color: #fff;
            }
        }
    }
}
</style>