<template>
    <view>
        <uni-popup type="bottom" ref="hoursChoosePopup" :safe-area="false">
            <view class="hours-choose-popup bg-white text-center">
                <view class="close" @click="$refs.hoursChoosePopup.close()">
                    <uni-icons type="closeempty" size="24" color="#bbbec4"/>
                </view>
                <view class="color-sub pb10">请选择运动时长</view>
                <view class="hours-list flex-row flex-wrap">
                    <view class="hours-item bg-background" v-for="item in hours_list" :key="item"
                          @click="updateValue(item)">
                        <text>{{ item }}</text>
                        <text class="color-sub font12" style="padding-left: 2px;">小时</text>
                    </view>

                    <view class="hours-item bg-background font14" @click="customValue('')">自定义</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "hours-choose-popup",
    props: {
        value: Number,
    },
    data() {
        return {
            hours_list: Array.from({ length: 16 }, (_, i) => i / 2 + 0.5)
        }
    },


    methods: {
        open() {
            this.$refs.hoursChoosePopup.open()
        },

        async customValue(value = '') {
            const res = await this.$uni.showModal(value, {
                title: '运动时长',
                showCancel: true,
                editable: true,
                placeholderText: '请输入运动时长(小时)'
            })
            if (!res.confirm) return
            
            const content = res.content.replace(/\s+/g, '')
            
            if (content === '') {
                this.$uni.showToast('请输入运动时长')
                setTimeout(() => this.customValue(''), 1000)
                return
            }
            let val = Number(content)
            if (isNaN(val) || val < 0) {
                this.$uni.showToast('运动时长输入不正确')
                setTimeout(() => this.customValue(''), 1000)
                return
            }
            
            // 最多只保留两位小数
            val = Number(val.toFixed(2))
            
            this.updateValue(val)
        },

        updateValue(value) {
            this.$emit('update:value', value)
            this.$refs.hoursChoosePopup.close()
        }
    }
}
</script>

<style lang="scss">
.hours-choose-popup {
    padding: 15px 0;
    border-radius: 10px 10px 0 0;
    position: relative;
    
    .close {
        position: absolute;
        top: 10px;
        right: 10px;
    }
    
    .hours-list {
        padding: 15rpx;
        
        .hours-item {
            width: 160rpx;
            line-height: 40px;
            margin: 10rpx;
            border-radius: 4px;
            color: #333;
        }
    }
}
</style>