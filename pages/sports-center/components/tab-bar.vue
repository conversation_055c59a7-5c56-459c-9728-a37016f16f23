<template>
    <view class="teb-bar-container bg-background">
        <view class="tab-bar flex-row bg-white">
            <view class="tab-item text-center" :style="{width: 100 / tab_list.length + '%'}"
                  v-for="item in tab_list" :key="item.title" @click="tabItemClick(item)">
                <uni-icons :type="item.uni_icon" :color="item.active ? '#5cadff' : '#bbbec4'" size="34"/>
                <view :class="'tab-title font12 ' + (item.active ? 'color-light-primary' : 'color-sub')">
                    {{ item.title }}
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "tab-bar",
    data() {
        return {
            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace tab_list */
            tab_list: [
                {title: '记录', uni_icon: 'list', active: true, eventFun: ''},
                {title: '添加', uni_icon: 'plus-filled', active: false, eventFun: 'toAdd'},
                {title: '统计', uni_icon: 'map-filled', active: false, eventFun: 'lookData'}
            ]
        }
    },

    methods: {
        tabItemClick(item) {
            if (item.active || !item.eventFun) return
            
            item.active = true
            setTimeout(() => {
                item.active = false
            }, 500)

            this[item.eventFun]()
        },


        toAdd() {
            this.$emit('toAdd')
        },

        lookData() {
            this.$uni.navigateTo('./sports-data')
        }
    }
}
</script>

<style lang="scss">
.teb-bar-container {
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 10px 10px 20px;
    width: 100%;
    box-sizing: border-box;

    .tab-bar {
        height: 52px;
        border-radius: 26px;

        .tab-title {
            margin-top: -5px;
        }
    }
}
</style>