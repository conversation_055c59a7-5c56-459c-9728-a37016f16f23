<template>
    <view class="record-day-list bg-white">
        <view class="record-item flex-row" v-for="(item, index) in record_list" :key="item.id" 
              @longpress="longPress(item)">
                <view v-if="item.sport_types.logo" class="pr10">
                    <image class="sport-type-logo" :src="item.sport_types.logo" mode="aspectFill"/>
                </view>
                <view style="width: 100%;">
                    <view class="color-sub flex-kai">
                        <view>
                            <view class="sport-name color-title">{{ item.sport_types.name || '' }}</view>
                            <view v-if="item.seconds" class="font14">{{ second2time(item.seconds) }}</view>
                            <view v-if="item.content_text" class="font14 clearfix clear">
                                <view>{{ getContentText(item.content_text).text }}</view>
                                <view class="fr color-light-primary" @click="clickItem(item)"
                                      v-if="getContentText(item.content_text).is_ellipsis">查看全文
                                </view>
                            </view>
                        </view>
                        <view class="pl10" v-if="item.num">x{{ item.num }}</view>
                    </view>
                    
                    <view v-if="item.pic_list.length" class="pic-list flex-row flex-wrap">
                        <template v-if="item.pic_list.length <= 3">
                            <image class="pic-item" mode="aspectFill"
                                   v-for="(image, img_index) in item.pic_list" :key="img_index" :src="image"
                                   @click="previewImage({current: image, urls: item.pic_list})"/>
                        </template>
                        <template v-else>
                            <image class="pic-item" mode="aspectFill"
                                   v-for="(image, img_index) in 2" :key="image" 
                                   :src="item.pic_list[img_index]"
                                   @click="previewImage({current: item.pic_list[img_index], urls: item.pic_list})"/>
                            <view class="pic-item bg-background text-center" @click="clickItem(item)">
                                <view class="font14 color-sub">x{{ item.pic_list.length - 2 }}</view>
                            </view>
                        </template>
                    </view>
                    <view class="flex-row">
                    
                    <view v-if="index !== record_list.length - 1" class="dashed-line"></view>
                </view>
            </view>
        </view>

        <uni-popup ref="previewItemPopup" @touchmove.stop.prevent="">
            <view v-if="previewItem" class="preview-item-popup bg-white">

                <scroll-view :scroll-y="true" class="preview-container">
                    <view v-if="previewItem.pic_list.length" class="pic-list flex-row flex-wrap">
                        <template v-if="previewItem.pic_list.length === 1">
                            <image class="pic-item-1" mode="widthFix" :src="previewItem.pic_list[0]"
                                   @click="previewImage(previewItem.pic_list[0])"/>
                        </template>

                        <template v-else>
                            <image :class="'pic-item pic-item-' + previewItem.pic_list.length" mode="aspectFill"
                                   v-for="(image, index) in previewItem.pic_list" :key="index" :src="image"
                                   @click="previewImage({current: image, urls: previewItem.pic_list})"/>

                        </template>
                    </view>

                    <view v-if="previewItem.pic_list.length && previewItem.content_text"
                          style="width: 100%; height: 15px;"></view>

                    <view v-if="previewItem.content_text">
                        <text class="font14 color-content">{{ previewItem.content_text }}</text>
                    </view>
                </scroll-view>
            </view>
            <view class="flex-all-center" @click="$refs.previewItemPopup.close()">
                <uni-icons type="close" size="28" color="#ffffff"/>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import sportsCenterApi from '../api'

export default {
    name: "record-day-list",
    props: ['list'],
    data() {
        return {
            record_list: [],
            previewItem: null
        }
    },
    
    watch: {
        list: {
            immediate: true,
            handler(newVal) {
                this.record_list = newVal
            }
        },

        record_list: {
            immediate: true,
            handler(newVal) {
                this.$emit('update:list', newVal)
            }
        }
    },

    methods: {
        second2time(seconds) {
            return sportsCenterApi.second2time(seconds)
        },

        getContentText(text) {
            text = text.replace(/\n/g, ' ')
            let is_ellipsis = false
            const text_all = 16 * 3
            if (text.length > text_all) {
                text = text.substring(0, text_all) + '...'
                is_ellipsis = true
            }
            return {
                text,
                is_ellipsis
            }
        },

        previewImage(params) {
            this.$uni.previewImage(params)
        },

        clickItem(item) {
            if (!item.pic_list.length && !item.content_text) return
            this.previewItem = item
            this.$refs.previewItemPopup.open()
        },

        async longPress(item) {
            const res = await this.$uni.showActionSheet(['复制', '修改', '删除'])
            
            if (res.tapIndex === 0) return this.copyRecord(item)
            if (res.tapIndex === 1) return this.toEditRecord(item)
            if (res.tapIndex === 2) return this.deleteRecord(item)
        },

        async copyRecord(item) {
            this.$uni.showLoading('复制中...')

            const data = {
                sport_types: item.sport_types.types,
                sport_date: this._utils.getDay(0, true)
            }

            if (item.num) data.num = item.num
            if (item.seconds) data.seconds = item.seconds
            if (item.content_text) data.content_text = item.content_text
            if (item.pic_list.length) data.pic_list = this._utils.base64['encode'](JSON.stringify(item.pic_list))

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exercise_record.userRecord/submit_sport_record',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '保存失败')

            this.$emit('reloadRecordList')

            this.$uni.showToast('复制成功', 'success')
        },

        toEditRecord(item) {
            const details = {
                id: item.id,
                sport_types: item.sport_types.types,
                date: item.sport_date,
                num: item.num || 0,
                seconds: item.seconds || 0
            }
            if (item.pic_list?.length) details.pic_list = item.pic_list
            if (item.content_text) details.content_text = item.content_text
            
            this.$uni.navigateTo('./add-record?edit=1', {
                success: res => {
                    res.eventChannel.emit('details', details)
                },
                events: {
                    updateRecord: data => {
                        const keys = ['date', 'num', 'seconds', 'pic_list', 'content_text']
                        keys.forEach(key => {
                            item[key] = data[key]
                        })
                        item.sport_types = data.sport
                    }
                }
            })
        },
        
        async deleteRecord(item) {
            const confirm = await this.$uni.showModal('确定删除运动记录?', {showCancel: true})
            if (!confirm.confirm) return
            
            this.$uni.showLoading('删除中...')
            const res = await sportsCenterApi.deleteSportRecord(item.id)
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '删除失败')
            
            this.$uni.showToast('已删除')
            this.$emit('reloadRecordList')
        }
    }
}
</script>

<style lang="scss">
.record-day-list {
    padding: 0 15px 20px;
    border-radius: 10px;

    .record-item {
        padding-top: 20px;

        .pic-list {
            $image-size: 70px;
            .pic-item {
                width: $image-size;
                min-width: $image-size;
                height: $image-size;
                line-height: $image-size;
                border-radius: 5px;
                display: block;
                margin-right: 5px;
                margin-top: 5px;
            }
        }

        $logo-size: 30px;

        .sport-type-logo {
            width: $logo-size;
            min-width: $logo-size;
            height: $logo-size;
            border-radius: 50%;
            display: block;
        }

        .sport-name {
            line-height: $logo-size;
        }
        
        .dashed-line {
            width: 100%;
            height: 20px;
            border-bottom: 1px dashed #eee;
        }
    }
}

.preview-item-popup {
    padding: 30rpx;
    width: 620rpx;
    border-radius: 10px;

    .preview-container {
        max-height: 70vh;

        .pic-list {
            margin-bottom: 10rpx;

            .pic-item, .pic-item-1 {
                width: 200rpx;
                border-radius: 5px;
                display: block;
                margin-right: 10rpx;
                margin-bottom: 10rpx;
            }

            .pic-item {
                height: 200rpx;
            }

            .pic-item:nth-child(3n), .pic-item-1, .pic-item-2 {
                margin-right: 0;
            }

            .pic-item-1 {
                width: 620rpx;
                height: 620rpx;
            }

            .pic-item-2 {
                width: 300rpx;
                height: 300rpx;
            }

            .pic-item-2:last-child {
                margin-left: 20rpx;
            }
        }
    }
}
</style>