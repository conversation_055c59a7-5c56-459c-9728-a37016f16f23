import xwyApi from '@/utils/api/xwy_api'

module.exports = {
    second2time(seconds) {
        seconds = Number(seconds)
        if (seconds < 60) return `${seconds}秒`
        
        if (seconds >= 3600) {
            const hour = Math.floor(seconds / 3600)
            const minute = Math.floor(seconds % 3600 / 60)
            const second = Math.floor(seconds % 3600 % 60)
            if (minute === 0 && second === 0) return `${hour}小时`
            if (second === 0) return `${hour}小时${minute}分钟`
            return `${hour}小时${minute}分${second}秒`
        }
        
        const minute = Math.floor(seconds / 60)
        const second = Math.floor(seconds % 60)
        if (second === 0) return `${minute}分钟`
        return `${minute}分${second}秒`
    },
    
    
    sport_record_system_set: null,
    
    /**
     * @description 获取运动记录系统设置
     * @param { boolean } reload 是否重新获取
     * @returns { Promise <object | null> } {sport_types_list: []}
     * */
    async getSportRecordSystemSet(reload = false) {
        if (this.sport_record_system_set && !reload) return this.sport_record_system_set
        
        const res = await xwyApi.request({
            url: 'front.flat.sport_step.exercise_record.userRecord/sport_record_system_set'
        })
        
        const data = res?.data || null
        if (data) this.sport_record_system_set = data
        return data
    },
    
    /**
     * @description 获取运动类型
     * @param { boolean } reload 是否重新获取
     * @return { Promise <array> }
     * */
    async getSportTypesList(reload = false) {
        // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
        /** @namespace res.sport_types_list */
        
        const res = await this.getSportRecordSystemSet(reload)
        return res?.sport_types_list || []
    },
    
    
    userSportTypesList: null,
    
    /**
     * @description 获取用户自定义的运动类型列表
     * @param { boolean } reload 是否重新获取，默认不重新获取，从缓存数据获取
     * @return { Promise <array> }
     * */
    async getUserSportTypesList(reload = false) {
        if (this.userSportTypesList && !reload) return this.userSportTypesList
        
        const res = await xwyApi.getCategoryList({
            types: 30,
            my_self: 1,
            page: 1,
            perpage: 100
        })
        
        const list = res?.data?.category_list?.data || []
        const user_sport_type_list = list.map(v => {
            return {
                logo: v.logo,
                name: v.name,
                types: v.category_id
            }
        })
        this.userSportTypesList = user_sport_type_list
        return user_sport_type_list
    },
    
    
    async getSportRecordsTotal(time = []) {
        if (!Array.isArray(time)) time = []
        
        const res = await xwyApi.request({
            url: 'front.flat.sport_step.exercise_record.userRecord/total_all_sport_num_hours',
            data: {
                begin_time: time[0] || '',
                end_time: time[1] || ''
            }
        })
        
        let list = res?.data?.total || []
        if (!Array.isArray(list)) list = []
        return list
    },
    
    
    /**
     * @description 获取运动记录
     * @param { object } params
     * @param { number | undefined } params.sport_types  运动类型
     * @param { undefined | 1 | 2 } params.order_types 指定排序的顺序 不传:按照运动日期排序(降序) 1:按照发布时间顺序(降序) 2:按照运动日期排序(升序)
     * @param { number } params.page
     * @param { number | undefined } params.perpage
     * @param { string | undefined } params.begin_time
     * @param { string | undefined } params.end_time
     * @return { Promise <object> }
     * */
    async getSportRecordList(params) {
        params.page ||= 1
        params.perpage ||= 20
        
        const res = await xwyApi.request({
            url: 'front.flat.sport_step.exercise_record.userRecord/sport_record_list',
            data: params
        })
        
        return res?.data?.list || null
    },
    
    /**
     * @description 删除添加的运动记录
     * @param { string | number } ids 需要删除的记录id，多个用逗号隔开
     * @returns { Promise <object> }
     * */
    async deleteSportRecord(ids) {
        return xwyApi.request({
            url: 'front.flat.sport_step.exercise_record.userRecord/del_submit_sport_records',
            data: {ids}
        })
    }
}