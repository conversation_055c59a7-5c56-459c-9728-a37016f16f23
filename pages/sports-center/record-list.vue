<!--这个页面的列表UI一开始是参考 胃之书APP 的，后续基本上是按照大家的意思改的，我的建议大多被否决了。后面如果看到有其他人的UI，然后说我们这个UI怎么这么丑，不好看。 所以要保留证据，撇清关系 2024-08-09 14:39:20-->


<template>
    <view class="page bg-background">
        <view :class="'clearfix clear p10 ' + (calendar_show ? 'bg-white' : 'bg-background')">
            <view :class="'to-set fl ' + (calendar_show ? 'bg-background' : 'bg-white')" 
                  @click="toUserSportTypeList">
                <text class="iconfont icon-setting color-sub font18"></text>
            </view>
            
            
            <view :class="'calender-icon fr ' + (calendar_show ? 'bg-background' : 'bg-white')"
                  @click="calendarShowSwitch">
                <text class="iconfont icon-calendar-1 font18"
                      :class="calendar_show ? 'color-light-primary' : 'color-sub'"></text>
            </view>
            
            <view :class="'sport-type fr flex-row ' + (calendar_show ? 'bg-background' : 'bg-white')">
                <view class="flex-row" @click="$refs.sportTypeListPopup.open()">
                    <view v-if="activeSport.logo" class="pr10">
                        <image class="sport-type-logo" :src="activeSport.logo" mode="aspectFill"/>
                    </view>
                    <view class="font14">
                        <text v-if="activeSport" class="color-light-primary">
                            {{ activeSport.name || '未知运动' }}
                        </text>
                        <text v-else class="iconfont icon-dating color-sub pr10"></text>
                    </view>
                    <uni-icons v-if="!activeSport" type="forward" size="14" color="#80848f"/>
                </view>
                <view v-if="activeSport" class="pl5" @click="clearSportType">
                    <uni-icons type="close" size="18" color="#80848f"/>
                </view>
            </view>
        </view>

        <uni-transition :show="calendar_show" :mode-class="['fade', 'slide-right']">
            <uni-calendar :date="calendar_value" :selected="calendar_selected"
                          @monthSwitch="monthSwitch" @change="calendarDateChange"/>

            <view class="month-statistics-date" v-if="year && month">{{ year }}年{{ month }}月</view>
            <view class="month-statistics flex-kai">
                <view class="month-statistics-item">
                    <view class="month-statistics-title">
                        <text class="iconfont icon-data-summary font18 color-light-primary"></text>
                        <text class="color-content font16">总共运动</text>
                    </view>
                    <view class="pt5">
                        <text class="font20 color-title font-bold">{{ month_sport_days }}</text>
                        <text class="month-statistics-unit font14 color-sub">天</text>
                    </view>
                </view>
                <view class="month-statistics-item">
                    <view class="month-statistics-title">
                        <text class="iconfont icon-line-chart font18 color-warning"></text>
                        <text class="color-content font16">连续运动</text>
                    </view>
                    <view class="pt5">
                        <text class="font20 color-title font-bold">{{ month_sport_continuous_days }}</text>
                        <text class="month-statistics-unit font14 color-sub">天</text>
                    </view>
                </view>
            </view>
        </uni-transition>

        <view class="record-list">
            <template v-for="(item, index) in record_list">
                <view v-if="dayItemShow(item)" class="record-date-item" :key="item.date">
                    <view v-if="showYear(item.year, index)" class="date-year font24">{{ item.year }}年</view>
                    <view class="flex-row">
                        <view v-if="!calendar_show" class="record-date">
                            <template>
                                <view v-if="item.date_text" class="font18">{{ item.date_text }}</view>
                                <view v-else>
                                    <view>
                                        <text class="font20">{{ item.day }}</text>
                                        <text class="font12 color-sub">{{ Number(item.month) }}月</text>
                                    </view>
                                </view>
                            </template>
                            <view class="color-sub font12">{{ item.week }}</view>
                        </view>


                        <record-day-list 
                            :list="item.record_list"
                            style="width: 100%; box-sizing: border-box;"  
                            @reloadRecordList="reloadRecordList"
                        />
                    </view>
                </view>
            </template>
        </view>

        <view v-show="list_load" class="text-center">
            <view v-show="current_page === 1" style="width: 100%; padding-top: 20vh;"></view>
            <load-ani/>
        </view>
        
        <view v-if="!list_load && !record_list.length && !calendar_show" 
              class="text-center" style="padding-top: 15vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub font12">暂无{{ activeSport && activeSport.name || '' }}记录</view>
            <view class="add-record-button bg-light-primary color-white font14"
                  hover-class="navigator-hover" @click="toAdd">添加记录
            </view>
        </view>

        <sport-type-list-popup
            ref="sportTypeListPopup"
            :sport-type-list="sport_type_list"
            :diy.sync="user_sport_type_list"
            @clickItem="sportTypeChange"
            @cancel="$refs.sportTypeListPopup.close()"
        />

        <tab-bar v-if="tab_show" @toAdd="toAdd"/>
        
        <xwy-ad v-if="!list_load && current_page === 1" :ad_type="3"></xwy-ad>
    </view>
</template>

<script>
const app = getApp()
import sportsCenterApi from './api'
import recordDayList from './components/record-day-list.vue'
import sportTypeListPopup from './components/sport-type-list-popup.vue'
import tabBar from './components/tab-bar.vue'

export default {
    components: {recordDayList, sportTypeListPopup, tabBar},
    data() {
        return {
            calendar_show: false,
            sport_type_list: [],
            user_sport_type_list: [],
            sport_types: 0,
            list_load: true,
            record_list: [],
            current_page: 1,
            is_last_page: false,
            calendar_value: this._utils.getDay(0, true),
            calendar_selected: [],
            month_sport_days: 0,
            month_sport_continuous_days: 0,
            year: 0,
            month: 0,
            tab_show: false
        }
    },

    computed: {
        activeSport() {
            const {sport_types: types, sport_type_list, user_sport_type_list} = this
            const list = [...sport_type_list, ...user_sport_type_list]
            const sport = list.find(v => v.types === types)
            if (!sport) return null
            if (sport.name) this.$uni.setNavigationBarTitle(`${sport.name}记录`)
            return sport
        }
    },

    onLoad(params) {
        this.paramsHandle(params)

        this.$login.uniLogin(() => {
            this.init()
        })
    },

    onPullDownRefresh() {
        this.reloadRecordList().finally(() => uni.stopPullDownRefresh())
    },

    onReachBottom() {
        if (this.is_last_page || this.list_load) return
        this.current_page++
        this.getRecordList()
    },

    methods: {
        paramsHandle(params) {
            // 查看某种类型的运动记录，是从其他页面跳转过来的，不用显示tab栏
            if (params.sport_types) {
                this.sport_types = Number(params.sport_types)
            } else {
                this.tab_show = true
            }
        },

        async init() {
            await this.getSportTypesList()
            await this.getUserSportTypesList()
            await this.reloadRecordList()
        },

        async getSportTypesList() {
            const list = await sportsCenterApi.getSportTypesList()
            if (!list.length) return

            this.sport_type_list = list.map(v => {
                return {
                    logo: v.logo,
                    name: v.name,
                    types: v.types
                }
            })
        },
        
        async getUserSportTypesList(reload = false) {
            this.user_sport_type_list = await sportsCenterApi.getUserSportTypesList(reload)
        },


        sportTypeChange(item) {
            this.sport_types = item.types
            this.$refs.sportTypeListPopup.close()
            this.$nextTick(() => this.reloadRecordList())
        },

        clearSportType() {
            this.sport_types = 0
            this.$nextTick(() => this.reloadRecordList())
        },


        async reloadRecordList() {
            this.current_page = 1
            this.is_last_page = false
            this.record_list = []

            await this.getRecordList()
        },

        async getRecordList() {
            const data = {
                page: this.current_page,
                perpage: 20
            }
            if (this.sport_types) data.sport_types = this.sport_types

            if (this.calendar_show) {
                this.calendar_selected = []
                this.month_sport_days = 0
                this.month_sport_continuous_days = 0

                const [year, month] = this.calendar_value.split('-')
                data.begin_time = `${year}-${month}-01`
                data.end_time = `${year}-${month}-${new Date(Number(year), Number(month), 0).getDate()}`
                data.perpage = 1000
            }

            this.list_load = true
            const res = await sportsCenterApi.getSportRecordList(data)
            this.list_load = false

            if (!res) {
                this.is_last_page = true
                return
            }

            this.is_last_page = res.is_lastpage
            this.recordListProcessing(res.data)
        },

        recordListProcessing(list = []) {
           const record_list = []
            
            list.forEach(v => {
                const date = v.sport_date || ''
                
                const item = {
                    id: v.id,
                    logs_id: v.logs_id,
                    num: v.num,
                    seconds: v.seconds,
                    sport_date: date,
                    create_time: v.create_time,
                    sport_types: {
                        types: v.sport_types
                    },
                    content_text: '',
                    pic_list: []
                }

                const {sport_types_details, extend_details} = v

                if (sport_types_details) {
                    const {name = '', logo = ''} = sport_types_details
                    item.sport_types.name = name
                    item.sport_types.logo = logo
                }

                if (extend_details) {
                    const {content_text = '', conf_json} = extend_details
                    item.content_text = content_text
                    if (conf_json?.pic_list?.length) item.pic_list = conf_json.pic_list
                }

                const record_list_item = record_list.find(list => list.date === date)
                if (record_list_item) {
                    record_list_item.record_list.push(item)
                } else {
                    const [year, month, day] = date.split('-')

                    record_list.push({
                        date,
                        year,
                        month,
                        day,
                        date_text: this.date2text(date),
                        week: this._utils.getWeek(date.replace(/-/g, '/')),
                        record_list: [item]
                    })
                }
            })

            this.record_list = record_list
            
            if (this.calendar_show) {
                this.calendar_selected = record_list.map(v => ({date: v.date}))
                const [year, month] = this.calendar_value.split('-')
                this.year = year
                this.month = Number(month)
                this.month_sport_days = record_list.length
                this.month_sport_continuous_days = this.getContinuousSportDays(this.calendar_selected)
            }
        },
        
        getContinuousSportDays(date_list) {
            if (!date_list.length) return 0
            
            // 将日期转为 Date 对象并排序
            const dates = date_list.map(item => new Date(item.date)).sort((a, b) => a - b)
            let maxCount = 1
            let currentCount = 1

            for (let i = 1; i < dates.length; i++) {
                // 如果当前日期和前一个日期连续
                if ((dates[i] - dates[i - 1]) === 86400000) { // 86400000 毫秒 = 1 天
                    currentCount++
                } else {
                    // 不是连续的，更新最大连续天数
                    maxCount = Math.max(maxCount, currentCount)
                    currentCount = 1 // 重置计数
                }
            }

            // 结束后再检查一次最大连续天数
            return Math.max(maxCount, currentCount)
        },

        date2text(date) {
            const nowDate = new Date(this._utils.getDay(0, true, '/')).getTime()
            const dataTime = new Date(date.replace(/-/g, '/')).getTime()

            if (dataTime === nowDate) return '今天'
            if (nowDate - dataTime === 86400000) return '昨天'
            return ''
        },

        dayItemShow(item) {
            if (!this.calendar_show) return true
            const [year, month, day] = this.calendar_value.split('-')
            return item.year === year && item.month === month && item.day === day
        },

        showYear(year, index) {
            const this_year = new Date().getFullYear()
            if (this.calendar_show) return false
            if (Number(year) === this_year) return false
            const one_index = this.record_list.findIndex(v => year === v.year)
            return index === one_index
        },

        calendarShowSwitch() {
            const show = !this.calendar_show
            if (show) this.calendar_value = this._utils.getDay(0, true, '-')
            this.calendar_show = show
            this.$nextTick(() => this.reloadRecordList())
        },

        monthSwitch(e) {
            this.calendar_value = `${e.year}-${e.month.toString().padStart(2, '0')}-01`
            this.$nextTick(() => this.reloadRecordList())
        },

        calendarDateChange(e) {
            const [old_year, old_month] = this.calendar_value.split('-')
            const [now_year, now_month] = e.fulldate.split('-')
            this.calendar_value = e.fulldate

            // 回到今日也是这个回调方法，所以不是本月回到今日要重新请求记录列表
            if (old_year !== now_year || old_month !== now_month) this.reloadRecordList()
        },


        toAdd() {
            this.$uni.navigateTo('./add-record', {
                events: {
                    reloadRecordList: () => this.reloadRecordList()
                }
            })
        },

        toUserSportTypeList() {
            this.$uni.navigateTo('/pages/category/list?types=30', {
                events: {
                    uploadList: () => this.getUserSportTypesList(true)
                }
            })
        }
    },
    
    onShareAppMessage() {
        return {
            title: '运动中心',
            path: `/${this.$uni.route()}`
        }
    },
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    padding-bottom: 80px;
    box-sizing: border-box;
}

.sport-type {
    line-height: 40px;
    padding: 0 10px;
    border-radius: 5px;

    .sport-type-logo {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: block;
        margin-top: 8px;
    }
}

.calender-icon, .to-set {
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 5px;
}

.calender-icon {
    margin-left: 10px;
}

.month-statistics-date {
    padding: 10px 20rpx 5px;
}

.month-statistics {
    padding: 0 20rpx;
    
    .month-statistics-item {
        width: 345rpx;
        padding: 10px;
        box-sizing: border-box;
        background-color: #fff;
        border-radius: 5px;
        
        .month-statistics-title {
            line-height: 24px;
            
            .iconfont {
                padding-right: 2px;
                position: relative;
                top: 1px;
            }
        }
        
        
        .month-statistics-unit {
            padding-left: 2px;
        }
    }
}

.record-list {
    .record-date-item {
        padding: 20px 10px;

        .date-year {
            padding-bottom: 20px;
        }

        .record-date {
            width: 60px;
            padding-right: 10px;
            text-align: right;
        }
    }
}

.add-record-button {
    width: 150px;
    line-height: 40px;
    border-radius: 20px;
    margin: 30px auto;
}
</style>