<template>
    <view class="page bg-background">
        <view v-if="!is_edit" class="clear clearfix p10">
            <view class="enter-type fr flex-row font14 color-content">
                <view class="enter-item flex-all-center" :class="{'enter-item-active': enter_type === 'form'}"
                      @click="enterTypeChange('form')">
                    <uni-icons type="list" size="24"/>
                </view>
                <view class="enter-item flex-all-center" :class="{'enter-item-active': enter_type === 'chat'}"
                      @click="enterTypeChange('chat')">
                    <uni-icons type="chat-filled" size="24"/>
                </view>
            </view>
        </view>

        <view v-show="is_edit || enter_type === 'form'">
            <view class="forms">
                <view class="form-item flex-kai" @click="sportTypeListPopupOpen">
                    <view class="form-label">
                        <text class="iconfont icon-wechat-movement"></text>
                        <text>运动类型</text>
                    </view>
                    <view>
                        <text :class="sport_types ? 'form-value' : 'form-placeholder'">
                            {{ sport_types_name || '请选择运动类型' }}
                        </text>
                        <uni-icons type="forward" size="16" color="#80848f"/>
                    </view>
                </view>

                <view class="form-item flex-kai" @click="$refs.chooseSportDatePopup.open()">
                    <view class="form-label">
                        <text :class="'iconfont icon-calendar-' + today_date"></text>
                        <text>日期</text>
                    </view>
                    <view>
                        <text :class="sport_date ? 'form-value' : 'form-placeholder'">
                            {{ sport_date || '请选择日期' }}
                        </text>
                        <uni-icons type="forward" size="16" color="#80848f"/>
                    </view>
                </view>

                <choose-date-popup ref="chooseSportDatePopup" :date="sport_date" :end-date="today" 
                                   @change="sportDateConfirm"/>

                <view class="form-item flex-kai">
                    <view class="form-label">
                        <text class="iconfont icon-data-summary"></text>
                        <text>运动数量</text>
                    </view>
                    <view>
                        <uni-easyinput type="digit" v-model="num" :maxlength="5" :input-border="false"
                                       placeholder="请输入运动数量"/>
                    </view>
                </view>

                <view class="form-item flex-kai" @click="$refs.hoursChoosePopup.open()">
                    <view class="form-label">
                        <text class="iconfont icon-time"></text>
                        <text>运动时长</text>
                    </view>
                    <view>
                        <text class="form-value">{{ hours }}</text>
                        <text class="color-sub font12 pl5 pr5">小时</text>
                        <text class="iconfont icon-edit color-sub"></text>
                    </view>
                </view>

            </view>

            <view v-if="!more_show" class="more-form flex-kai" @click="more_show = !more_show">
                <view class="form-label">
                    <text class="iconfont icon-create-ticket"></text>
                    <text>记录更多</text>
                </view>
                <uni-icons type="forward" size="14" color="#80848f"/>
            </view>

            <view v-show="more_show" class="form-text-area">
                <view class="form-label">
                    <text class="iconfont icon-writing"></text>
                    <text>运动心得</text>
                </view>

                <uni-easyinput type="textarea" v-model="content_text" :maxlength="1000"
                               placeholder="请输入运动心得"/>
            </view>

            <view v-show="more_show" class="form-images">
                <view class="form-label">
                    <text class="iconfont icon-image"></text>
                    <text>运动相册</text>
                    <text class="pl10 color-sub font14">
                        ({{ pic_list.length }}/{{ pic_list_max_length }})
                    </text>
                </view>


                <view class="image-list flex-row flex-wrap">
                    <view v-for="(item, index) in pic_list" :key="index" class="image-item">
                        <view class="delete-image flex-all-center" @click="pic_list.splice(index, 1)">
                            <uni-icons type="closeempty" size="16" color="#e20f04"/>
                        </view>
                        <image class="image-item-image" :src="item" mode="aspectFill"
                               @click="previewImage(item)"/>
                    </view>

                    <view v-show="pic_list.length < pic_list_max_length"
                          class="image-item-add flex-all-center"
                          @click="addImage">
                        <uni-icons type="plusempty" size="80" color="#e8e8e8"/>
                    </view>
                </view>
            </view>

            <view class="save flex-all-center">
                <view class="save-button bg-light-primary color-white text-center" @click="save">保存</view>
            </view>
        </view>


        <view v-show="!is_edit && enter_type === 'chat'" class="chat-container">

            <scroll-view class="chat-contents" :scroll-y="true"
                         :scroll-into-view="scrollIntoViewId" :scroll-with-animation="true">
                <view class="chat-content-item chat-content-ai">
                    <view class="color-content">
                        请输入需要记录的时间、运动名称、时长、数量(时长、数量可不输入)。如：
                    </view>
                    
                    <!--
                    2024-08-16 09:55:04
                    原来是点击提示词直接提交的，我的想法也是这样的，如果要修改可以在提交的记录里面去修改。
                    刘总说可能人家打篮球是3小时，不是2小时，需要再生成记录前修改。所以改成点击提示词替换输入框内容。
                    我说，如果替换前，输入框有内容，要不要弹窗询问用户是否要替换。刘总说没必要，又不是写文章，写了一大堆被替换，就几个字，替换就替换了
                    -->
                    <view class="enter-tips flex-row flex-wrap">
                        <view class="enter-tips-item" @click="chat_user_content = '昨天打篮球2小时'">
                            昨天打篮球2小时
                        </view>
                        <view class="enter-tips-item" @click="chat_user_content = '8月12号踢足球'">
                            8月12号踢足球
                        </view>
                    </view>
                </view>

                <template v-for="(item, index) in chat_record_list">
                    <view class="clear clearfix">
                        <view v-if="item.role === 'user'" class="chat-content-item chat-content-user fr"
                              :key="index" :id="`chat-content-item-${index}`">
                            {{ item.content }}
                        </view>
                    </view>

                    <view v-if="item.role === 'ai'" class="chat-content-item chat-content-ai"
                          :key="index" :id="`chat-content-item-${index}`">{{ item.content }}
                    </view>

                    <template v-if="item.role === 'sport-confirm'">
                        <view v-for="(sport, sport_index) in item.list" :key="sport_index">
                            <view :id="`chat-content-item-${index}`"
                                  class="chat-content-item chat-content-ai chat-record">
                                <view class="flex-kai">
                                    <view class="color-content font14">已记录:</view>
                                    <view class="color-sub">
                                        <uni-dateformat :date="sport.date" format="yyyy年M月d日"/>
                                    </view>
                                </view>

                                <view class="flex-kai pt10">
                                    <view class="fl flex-row">
                                        <view v-if="sport.sport.logo" class="pr10">
                                            <image class="chat-sport-logo" :src="sport.sport.logo"/>
                                        </view>
                                        <view>
                                            <view class="color-title">{{ sport.sport.name }}</view>
                                            <view v-if="sport.seconds" class="font14 color-sub">
                                                {{ second2time(sport.seconds) }}
                                            </view>
                                        </view>
                                    </view>

                                    <view v-if="sport.num" class="chat-sport-num color-sub">
                                        x{{ sport.num }}
                                    </view>
                                </view>
                            </view>

                            <view class="chat-record-action-bar clearfix clear">
                                <view class="chat-record-action-button fr font12"
                                      @click="deleteSportRecord(index, sport_index)">
                                    <text class="iconfont icon-delete color-sub font12"></text>
                                    <text style="padding-left: 2px;">删除</text>
                                </view>
                                <view class="chat-record-action-button fr font12"
                                      @click="editSportRecord(index, sport_index)">
                                    <text class="iconfont icon-edit color-sub font12"></text>
                                    <text style="padding-left: 2px;">修改</text>
                                </view>
                            </view>
                        </view>
                    </template>
                </template>

                <view v-if="ai_loading" id="ai-loading" class="chat-content-item chat-content-ai">
                    <load-ani/>
                </view>
            </scroll-view>


            <view class="chat-input-container bg-white">
                <view class="flex-row">
                    <view style="width: 100%;">
                        <uni-easyinput
                            type="textarea"
                            v-model="chat_user_content"
                            autoHeight
                            :maxlength="50"
                            placeholder="昨天打篮球2小时"
                        />
                    </view>
                    <view style="display: flex; flex-direction: column-reverse">
                        <view></view>

                        <view
                            :hover-class="chat_user_content.length ? 'navigator-hover' : 'none'"
                            class="chat-send flex-all-center"
                            :class="{
                                'bg-primary': chat_user_content.length && !ai_loading,
                                'bg-disabled': !chat_user_content.length || ai_loading
                            }"
                            @click="chatSend"
                        >
                            <uni-icons type="paperplane-filled" size="24" color="#ffffff"/>
                        </view>
                    </view>
                </view>
            </view>


        </view>

        <sport-type-list-popup
            ref="sportTypeListPopup"
            :sport-type-list="sport_type_list"
            :diy.sync="user_sport_type_list"
            @clickItem="changeSportType"
            @cancel="sportTypeListPopupClose()"
        />

        <hours-choose-popup ref="hoursChoosePopup" :value.sync="hours"/>
    </view>
</template>

<script>
const app = getApp()
import sportsCenterApi from "./api"
import sportTypeListPopup from './components/sport-type-list-popup.vue'
import hoursChoosePopup from './components/hours-choose-popup.vue'
import chooseDatePopup from './components/choose-date-popup.vue'

export default {
    components: {sportTypeListPopup, hoursChoosePopup, chooseDatePopup},
    data() {
        return {
            is_edit: false,
            enter_type: '',  // 录入方式   form: 表单录入  chat: 对话录入
            sport_type_list: [],
            user_sport_type_list: [],
            sport_types: 0,
            sport_date: this._utils.getDay(0, true),
            today: this._utils.getDay(0, true),
            hours: 0,
            num: '',
            content_text: '',
            pic_list: [],
            pic_list_max_length: 6,
            more_show: false,
            chat_user_content: '',
            ai_loading: false,
            chat_record_list: []
        }
    },

    computed: {
        all_sport_type_list() {
            return [...this.sport_type_list, ...this.user_sport_type_list]
        },
        
        sport_types_name() {
            if (!this.sport_types) return ''
            return this.all_sport_type_list.find(v => v.types === this.sport_types)?.name || ''
        },

        today_date() {
            return Number(this.sport_date.split('-')[2])
        },

        scrollIntoViewId() {
            if (this.ai_loading) return 'ai-loading'
            if (!this.chat_record_list) return ''
            return `chat-content-item-${this.chat_record_list.length - 1}`
        }
    },

    onLoad(params) {
        this.$uni.showLoading('加载中...')
        this.processingParams(params)
        this.$login.uniLogin(() => {
            this.init()
        })
    },

    methods: {
        processingParams(params) {
            if (params.edit) {
                this.$uni.setNavigationBarTitle('修改')
                this.is_edit = true
                this.getRecordDetails()
            }
        },

        getRecordDetails() {
            this.getOpenerEventChannel()?.on?.('details', details => {
                const keys = ['sport_types', 'id', 'num', 'content_text', 'pic_list']
                keys.forEach(key => {
                    if (details[key]) this[key] = details[key]
                })
                if (details.date) this.sport_date = details.date
                if (details.seconds) this.hours = Number((details.seconds / 3600).toFixed(2))
            })
        },


        async init() {
            this.enter_type_storage_key = `sport-enter-type-${app.globalData['who']}`
            this.enter_type = uni.getStorageSync(this.enter_type_storage_key) || 'form'
            await this.getSportTypesList()
            await this.getUserSportTypesList()
            uni.hideLoading()
        },

        second2time(seconds) {
            return sportsCenterApi.second2time(seconds)
        },

        enterTypeChange(type) {
            if (type === this.enter_type) return
            this.enter_type = type
            uni.setStorageSync(this.enter_type_storage_key, type)
        },

        async getSportTypesList() {
            const list = await sportsCenterApi.getSportTypesList()
            if (!list.length) return


            this.sport_type_list = list.map(v => {
                return {
                    logo: v.logo,
                    name: v.name,
                    types: v.types
                }
            })

            // this.$nextTick(() => this.sportTypeListPopupOpen())
        },

        async getUserSportTypesList(reload = false) {
            this.user_sport_type_list = await sportsCenterApi.getUserSportTypesList(reload)
        },

        sportDateConfirm(e) {
            if (e.fulldate) this.sport_date = e.fulldate
        },

        sportTypeListPopupOpen() {
            this.$refs.sportTypeListPopup.open()
        },
        sportTypeListPopupClose() {
            this.$refs.sportTypeListPopup.close()
        },

        changeSportType(item) {
            this.sport_types = item.types
            this.sportTypeListPopupClose()
        },

        addImage() {
            this.$uni.navigateTo('/pages/other/image_upload_or_select', {
                events: {
                    newImg: src => this.pic_list.push(src)
                }
            })
        },


        previewImage(item) {
            this.$uni.previewImage({
                current: item,
                urls: this.pic_list
            })
        },


        chatSend() {
            if (!this.chat_user_content) return this.$uni.showToast('请输入要记录的内容')

            this.askAI()
        },

        async askAI(content = this.chat_user_content) {
            if (this.ai_loading) return

            this.chat_record_list.push({
                role: 'user',
                content
            })

            this.ai_loading = true
            const res = await this.xwy_api.request({
                url: 'front.flat.active.chat.user.chat/system_product_result',
                data: {
                    system_product: 'ai_get_json_data',
                    used_company: 'doubao',
                    subject: content,
                    description: this.getChatPrompt(content)
                }
            })
            this.ai_loading = false

            const json = res?.data?.res?.content?.[0]
            if (!json) return this.chatError()

            try {
                const sports = JSON.parse(json)
                this.getSportContent(sports)
            } catch (e) {
                this.chatError()
            }
        },

        getSportContent(sports) {
            if (!sports?.length) return this.chatError()

            if (sports.length === 1) {
                const v = sports[0]
                if (!v.name) return this.chatError()
                const sport = this.all_sport_type_list.find(item => item.name === v.name)
                if (!sport) return this.chatError(`暂不支持记录${v.name}`)
            }

            const record = sports.filter(v => v.name).map(v => {
                const sport = this.all_sport_type_list.find(item => item.name === v.name)
                return sport ? {
                    date: v.date || this._utils.getDay(0, true),
                    name: v.name,
                    seconds: v.seconds || 0,
                    num: v.num || 0,
                    sport
                } : null
            }).filter(Boolean)

            if (!record.length) return this.chatError()

            this.chat_user_content = ''

            this.chatSaveRecord(record)
        },

        async chatSaveRecord(record_list) {
            const list = []

            this.ai_loading = true
            for (let i in record_list) {
                const item = record_list[i]
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.exercise_record.userRecord/submit_sport_record',
                    data: {
                        sport_types: item.sport.types,
                        sport_date: item.date,
                        num: item.num,
                        seconds: item.seconds
                    }
                })

                if (res?.status === 1 && res?.data?.res) {
                    list.push({
                        ...item,
                        id: res.data.res
                    })
                }

            }
            this.ai_loading = false

            if (!list.length) return this.chatError('记录失败')

            this.reloadRecordList()

            this.chat_record_list.push({
                role: 'sport-confirm',
                list
            })
        },

        chatError(content = '输入有误') {
            this.chat_record_list.push({
                role: 'ai',
                content
            })
        },

        getChatPrompt(content) {
            // AI有点傻，如果没有提取到日期，会使用示例里面的日期
            // 就算示例里面是 "date": "yyyy-MM-dd"，AI会给你返回"yyyy-MM-dd"。。。气死人
            const today = this._utils.getDay(0, true)
            const week = this._utils.getWeek(today)

            return `
            请根据以下文字内容提取用户运动的名称、日期、时间、次数，使用json格式返回，多个运动需要返回多条。格式如下：
            [
                {"date": "", "name": "篮球", "seconds": 1800, "num": 0}
            ]
            
            注意！！！必须按照下面要求提取：
            1、date为会话内容提取出来的日期，日期可能是周?、上周?、昨天、前天、上个月?号等，需要提取为yyyy-MM-dd 的格式。如果没有提取到日期，返回今天日期。
            2、name为提取用户需要记录的运动类型名称，需要省略动词，只保留名词，比如"打篮球"只保留"篮球"，"踢足球"只保留"足球"。
            3、seconds为提取用户做这项运动的时间，分析的文字内容里面有小时、分钟、秒等明确说明时间的才提取时间，如 打篮球2小时、游泳30分钟、跑步50秒等。没有提取到时间请返回0，如果是分钟或者小时的需要转为秒数，请返回数字，不要返回如300 * 60 等其他类型。
            4、num为提取用户做这项运动的次数,比如周一跳绳300次，那么num提取300。如果用户输入的文字里没有提取到运动的次数，那么次数就存0即可。
            5、请不要在JSON代码里面写注释。
            6、今天的日期是${today},提取日期的时候，要以今天的日期为准。比如昨天，日期date就是在今天的日期减去一天。
            7、今天是${week}，如果文字内容里面有周?、本周?、上周? 请换算成对应的日期date。
            8、同一天、同一种运动，请不要返回两条JSON数据。比如“周一跳绳300次”，你经常会返回两条JSON数据，请不要重复。
            
            下面是需要你提取分析的文字内容：
            ${content}
            `
        },

        async deleteSportRecord(index, record_index) {
            const record = this.chat_record_list[index].list[record_index]

            this.$uni.showLoading('删除中...')
            const res = await sportsCenterApi.deleteSportRecord(record.id)
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '删除失败')

            this.reloadRecordList()

            this.$uni.showToast('已删除')
            this.chat_record_list[index].list.splice(record_index, 1)
            if (!this.chat_record_list[index].list.length) {
                this.chat_record_list.splice(index, 1)

                const previous = this.chat_record_list[index - 1]
                if (previous?.role === 'user') {
                    this.chat_record_list.splice(index - 1, 1)
                }
            }
        },


        editSportRecord(index, record_index) {
            const record = this.chat_record_list[index].list[record_index]

            const details = JSON.parse(JSON.stringify(record))
            details.sport_types = details.sport.types
            delete details.sport
            delete details.name

            this.$uni.navigateTo('./add-record?edit=1', {
                success: res => {
                    res.eventChannel.emit('details', details)
                },
                events: {
                    updateRecord: details => {
                        record.sport = details.sport
                        record.name = details.sport.name
                        record.date = details.date
                        record.num = details.num
                        record.seconds = details.seconds

                        this.reloadRecordList()
                    }
                }
            })
        },


        async save() {
            if (!this.sport_types) return this.$uni.showToast('请选择运动类型')

            const data = {
                sport_types: this.sport_types,
                sport_date: this.sport_date
            }
            if (this.id) data.id = this.id

            const num = Number(this.num)
            if (isNaN(num) || num < 0) return this.$uni.showToast('请输入正确的运动次数')
            data.num = num

            const hours = Number(this.hours)
            if (isNaN(hours) || hours < 0) return this.$uni.showToast('请输入正确的运动时长')
            data.seconds = Math.floor(hours * 3600)

            if (this.content_text) {
                if (this.content_text.length > 1000) return this.$uni.showToast('运动心得不能超过1000个字')
                data.content_text = this.content_text
            }

            if (this.pic_list.length) data.pic_list = this._utils.base64['encode'](JSON.stringify(this.pic_list))

            this.$uni.showLoading('保存中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exercise_record.userRecord/submit_sport_record',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) {
                if (this.enter_type === 'chat' && !this.is_edit) return this.chatError('保存失败')
                return this.$uni.showModal(res?.info || '保存失败')
            }

            this.reloadRecordList()
            this.updateRecord()

            if (this.enter_type === 'form' || this.is_edit) {
                this.$uni.showToast('保存成功', 'success')
                this.$uni.navigateBack(1, {delay: 1000})
            }
        },

        reloadRecordList() {
            if (this.is_edit) return
            this.getOpenerEventChannel()?.emit?.('reloadRecordList')
        },

        updateRecord() {
            if (!this.is_edit) return
            this.getOpenerEventChannel()?.emit?.('updateRecord', {
                sport: this.all_sport_type_list.find(v => v.types === this.sport_types),
                num: this.num || 0,
                date: this.sport_date,
                seconds: this.hours ? (this.hours * 3600) : 0,
                content_text: this.content_text || '',
                pic_list: this.pic_list || []
            })
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    padding-top: 1px;
    box-sizing: border-box;
}

.enter-type {
    border-radius: 20px;
    background-color: #fff;
    overflow: hidden;

    .enter-item {
        width: 80px;
        height: 40px;
        
        .uni-icons {
            color: #999 !important;
        }
    }

    .enter-item-active {
        background-color: #5cadff;

        .uni-icons {
            color: #fff !important;
        }
    }
}

.form-label {
    color: #495060;

    .iconfont {
        padding-right: 3px;
        color: #5cadff;
    }
}

.forms {
    margin: 10px 20rpx;
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;

    .form-item {
        margin: 0 10px;
        padding: 10px 0;
        line-height: 36px;
        border-bottom: 1px solid #eee;

        &:last-child {
            border-bottom: none;
        }

        .form-value {
            color: #333;
        }

        .uni-easyinput {
            width: 150px;

            .uni-easyinput__content-input {
                text-align: right;
                font-size: 16px;
            }
        }

        .form-placeholder {
            color: #828282;
        }
    }
}

.more-form, .form-text-area, .form-images {
    margin: 10px 20rpx;
    background-color: #fff;
    padding: 10px 20rpx;
    border-radius: 10px;
}

.more-form {
    margin-top: 20px;
}

.form-text-area, .form-images {
    .form-label {
        padding-bottom: 10px;
    }
}

.image-list {
    .image-item, .image-item-add {
        margin: 10rpx;
    }

    .image-item, .image-item-image, .image-item-add {
        width: 203rpx;
        height: 203rpx;
    }

    .image-item {
        position: relative;

        .delete-image {
            position: absolute;
            right: 5px;
            top: 5px;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, .7);
        }
    }

    .image-item-image {
        display: block;
    }

    .image-item-add {
        border: 1px solid #eee;
        box-sizing: border-box;
        border-radius: 5px;
    }
}

.save {
    padding-top: 30px;

    .save-button {
        width: 200px;
        line-height: 44px;
        border-radius: 22px;
    }
}


.chat-container {
    .chat-contents {
        height: calc(100vh - 56px - 128px - 10px);

        .chat-content-item {
            background-color: #fff;
            margin: 10px;
            padding: 10px;
            display: inline-block;
            max-width: 80vw;
        }

        .chat-content-ai {
            border-radius: 0 10px 10px 10px;

            .enter-tips {
                padding-top: 5px;

                .enter-tips-item {
                    color: #5cadff;
                    padding: 5px 10px;
                    font-size: 14px;
                }
            }
        }

        .chat-record {
            width: 250px;

            $logo-size: 40px;

            .chat-sport-logo {
                width: $logo-size;
                min-width: $logo-size;
                height: $logo-size;
                display: block;
                border-radius: 50%;
            }

            .chat-sport-num {
                line-height: $logo-size;
            }
        }

        .chat-content-user {
            border-radius: 10px 0 10px 10px;
            background-color: #5cadff;
            color: #fff;
        }

        .chat-record-action-bar {
            width: 280px;
            margin-top: -5px;
            padding-bottom: 10px;

            .chat-record-action-button {
                color: #7E7E7E;
                line-height: 30px;
                border: 1px solid #ECEBEB;
                border-radius: 16px;
                margin-left: 10px;
                width: 70px;
                text-align: center;
                background-color: #fff;
            }
        }
    }

    .chat-input-container {
        position: fixed;
        z-index: 99;
        left: 0;
        bottom: 0;
        width: 100vw;
        box-sizing: border-box;
        padding: 10px 10px 20px 10px;

        .chat-send {
            width: 40px;
            min-width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-left: 10px;
        }
    }
}
</style>