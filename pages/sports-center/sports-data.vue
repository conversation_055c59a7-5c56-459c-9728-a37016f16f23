<template>
    <view class="page bg-background">

        <view class="sport-total bg-white">
            <view class="font18" @click="$refs.yearMonthChoosePopup.open()">
                <text class="iconfont icon-calendar-1 color-sub font18 pr5"></text>
                <text>{{ year }}年</text>
                <text v-show="month">{{ month }}月</text>
                <text class="iconfont icon-more color-sub font18"></text>
            </view>
            <view class="color-content pt10 font14">运动{{ all_count }}次 累计{{ all_time }}</view>
        </view>
        
        <view class="sport-type-list flex-row flex-wrap">
            <view class="sport-type-item flex-kai flex-column bg-white" 
                  v-for="item in sport_type_list" :key="item.types" @click="clickItem(item.types)">
                <view class="flex-row">
                    <view class="logo" v-if="item.logo">
                        <image class="logo-image" :src="item.logo" mode="aspectFill"/>
                    </view>
                    <view>
                        <view class="sport-name">{{ item.name }}</view>
                        <view v-if="item.total_data" class="color-content pt10 font14">
                            <view v-if="item.total_data.allCount">次数: {{ item.total_data.allCount }}</view>
                            <view v-if="item.total_data.num">数量: {{ item.total_data.num }}</view>
                        </view>
                    </view>
                </view>
                

                <view v-if="item.total_data && item.total_data.seconds" class="color-content">
                    <text class="iconfont icon-time color-sub" style="padding-right: 2px;"></text>
                    <text>{{ second2time(item.total_data.seconds) }}</text>
                </view>
            </view>
        </view>
        
        <view v-show="loading" class="text-center" style="padding-top: 15vh;">
            <load-ani/>
        </view>
        
        <view v-show="!loading && !sport_type_list.length" class="text-center" style="padding-top: 15vh;">
            <text class="iconfont icon-empty-state color-disabled" style="font-size: 100px;"></text>
            <view class="font14 color-sub">
                {{ year }}年<text v-show="month">{{ month }}月</text>无运动数据
            </view>
        </view>

        <year-month-choose-popup ref="yearMonthChoosePopup" :year="year" @yearMonthChange="yearMonthChange"/>

        <template v-if="!loading">
            <xwy-ad :ad_type="3"/>
            <xwy-ad v-if="sport_type_list.length" :ad_type="66"/>
        </template>
    </view>
</template>

<script>
const app = getApp()
import sportsCenterApi from './api'
import yearMonthChoosePopup from './components/year-month-choose-popup.vue'

export default {
    components: {yearMonthChoosePopup},
    data() {
        return {
            loading: true,
            sport_type_list: [],
            year: new Date().getFullYear(),
            month: new Date().getMonth() + 1
        }
    },
    
    computed: {
        all_time() {
            return sportsCenterApi.second2time(this.sport_type_list.reduce((total, item) => total + (item.total_data?.seconds || 0), 0))
        },
        
        all_count() {
            return this.sport_type_list.reduce((total, item) => total + (item.total_data?.allCount || 0), 0)
        }
    },

    onLoad() {
        this.$login.uniLogin(() => {
            this.init()
        })
    },

    methods: {
        async init() {
            await this.getSportRecordsTotal()
            this.loading = false
        },

        async yearMonthChange(date) {
            this.year = date.year
            this.month = date.month
            
            this.loading = true
            await this.getSportRecordsTotal()
            this.loading = false
        },

        getQueryTime() {
            const {year, month} = this
            
            let time = []
            
            if (month) {
                time[0] = `${year}-${month.toString().padStart(2, '0')}-01`
                time[1] = `${year}-${month.toString().padStart(2, '0')}-${new Date(year, month, 0).getDate()}`
            } else {
                time[0] = `${year}-01-01`
                time[1] = `${year}-12-31`
            }
            
            return time
        },

        async getSportRecordsTotal() {
            this.sport_type_list = []
            
            const total_list = await sportsCenterApi.getSportRecordsTotal(this.getQueryTime())
            if (!total_list.length) return
            
            this.sport_type_list = total_list.map(v => {
                const {sport_types_details = {}, total_data} = v
                const {logo = '', name = '', types = 0} = sport_types_details
                const item = {logo, name, types}
                
                if (total_data) {
                    item.total_data = {
                        allCount: total_data.allCount || 0,
                        num: Number(total_data.num) || 0,
                        seconds: Number(total_data.seconds) || 0
                    }
                }
                
                return item
            }).sort((a, b) => {
                if (a.total_data.allCount !== b.total_data.allCount) return b.total_data.allCount - a.total_data.allCount
                if (a.total_data.seconds !== b.total_data.seconds) return b.total_data.seconds - a.total_data.seconds
                if (a.total_data.num !== b.total_data.num) return b.total_data.num - a.total_data.num
                return 0
            })
        },

        second2time(seconds) {
            return sportsCenterApi.second2time(seconds)
        },

        clickItem(types) {
            this.$uni.navigateTo(`./record-list?sport_types=${types}`)
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    padding-top: 1px;
    padding-bottom: 15px;
    box-sizing: border-box;
}

.sport-total {
    margin: 32rpx;
    padding: 10px;
    border-radius: 5px;
}

.sport-type-list {
    padding: 0 16rpx;
    
    .sport-type-item {
        margin: 16rpx;
        padding: 10px;
        border-radius: 10px;
        width: 327rpx;
        height: 140px;
        box-sizing: border-box;
        position: relative;

        $logo-size: 30px;

        .logo {
            padding-right: 5px;

            .logo-image {
                width: $logo-size;
                min-width: $logo-size;
                height: $logo-size;
                display: block;
                border-radius: calc(#{$logo-size} / 2);
            }
        }
        
        .sport-name {
            line-height: $logo-size;
        }
    }
}

</style>