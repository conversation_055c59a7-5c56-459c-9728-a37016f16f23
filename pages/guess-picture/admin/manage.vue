<template>
    <view class="page bg-background">
        <activity-delete-tips v-if="error" :activeId="id"/>

        <view v-if="detail && detail.active_id">
            <!-- #ifndef H5 -->
            <image v-if="detail.logo" class="logo" mode="widthFix" :src="detail.logo"/>
            <!-- #endif -->
            <view class="bg-white p10">
                <view class="color-title font18" @click="copy(id)">{{ detail.name }}</view>
                <view v-if="detail.organizer" class="color-content font16">主办方：{{ detail.organizer }}</view>
            </view>

            <template v-if="!detail.rank_set || !detail.rank_set.closed_AD">
                <xwy-ad v-if="!popup_show" :ad_type="4"></xwy-ad>
                <xwy-ad :ad_type="3"></xwy-ad>
            </template>


            <view class="tools bg-white">
                <view class="title color-title">活动管理</view>
                <view class="list flex-row flex-wrap text-center">
                    <navigator :url="'./add?id=' + id" class="item">
                        <text class="iconfont icon-edit color-primary font34"></text>
                        <view class="font14 color-content">活动修改</view>
                    </navigator>


                    <navigator :url="'/pages/activity/admin/activity_user_list?id=' + id" class="item">
                        <text class="iconfont icon-users color-primary font34"></text>
                        <view class="font14 color-content">活动用户</view>
                    </navigator>

                    <navigator :url="'/pages/activity/admin/blacklist?id=' + id" class="item">
                        <text class="iconfont icon-blacklist color-primary font34"></text>
                        <view class="font14 color-content">黑名单用户</view>
                    </navigator>

                    <navigator
                        class="item"
                        :url="'/pages/news/list?type=user&vip=' + (detail.rank_set && detail.rank_set.closed_AD || 0)"
                    >
                        <i class="iconfont icon-writing font34 color-primary"/>
                        <view class="font14 color-content">文章管理</view>
                    </navigator>
                    <navigator
                        v-if="detail.rank_set && detail.rank_set.closed_AD"
                        class="item"
                        url="/pages/category/list?types=8"
                    >
                        <i class="iconfont icon-dating font34 color-primary"/>
                        <view class="font14 color-content">文章分类</view>
                    </navigator>
                    <view class="item" @click="copyActivityPages">
                        <text class="iconfont icon-copy color-primary font34"></text>
                        <view class="font14 color-content">复制路径</view>
                    </view>

                    <!-- #ifndef H5 -->
                    <view class="item" @click="showWebUrl">
                        <text class="iconfont icon-screen color-primary font34"></text>
                        <view class="font14 color-content">web端管理</view>
                    </view>
                    <!-- #endif -->

                    <navigator class="item" url="/pages/other/contact">
                        <uni-icons type="chatboxes" size="34" color="#2d8cf0"/>
                        <view class="font14 color-content">联系客服</view>
                    </navigator>

                    

                    <view class="item" @click="showActiveSharePopup">
                        <text class="iconfont icon-share color-primary font34"></text>
                        <view class="font14 color-content">转发分享</view>
                    </view>
                </view>
            </view>

            <view v-if="detail.conf.active.exam_id" class="tools bg-white">
                <view class="title color-title">猜图管理</view>
                <view class="list flex-row flex-wrap text-center">
                    <!--<navigator class="item" url="/pages/likou_dati/pages/exam/exam_list/exam_list">
                        <text class="iconfont icon-examination-paper color-primary font34"></text>
                        <view class="font14 color-content">我的考卷</view>
                    </navigator>-->
                    <navigator
                        class="item"
                        :url="'/pages/likou_dati/pages/exam/exam_add/exam_add?is_activity=1&exam_id=' + detail.conf.active.exam_id"
                    >
                        <text class="iconfont icon-setting color-primary font34"></text>
                        <view class="font14 color-content">猜图设置</view>
                    </navigator>
                    <navigator
                        class="item"
                        :url="'/pages/likou_dati/pages/exam/exam_question_add/exam_question_add?exam_id=' + detail.conf.active.exam_id"
                    >
                        <text class="iconfont icon-examination-paper color-primary font34"></text>
                        <view class="font14 color-content">题目管理</view>
                    </navigator>
                    <view
                        class="item"
                        hover-class="navigator-hover"
                        @click="$refs.question_method.open()"
                    >
                        <text class="iconfont icon-sort color-primary font34"></text>
                        <view class="font14 color-content">出题方式</view>
                    </view>
                    <navigator
                        class="item"
                        url="/pages/likou_dati/pages/question/question_bank_list/question_bank_list"
                    >
                        <text class="iconfont icon-dictionary color-primary font34"></text>
                        <view class="font14 color-content">我的题库</view>
                    </navigator>
                    <navigator
                        class="item"
                        url="/pages/likou_dati/pages/question/category_list/category_list"
                    >
                        <text class="iconfont icon-dating color-primary font34"></text>
                        <view class="font14 color-content">题库分类</view>
                    </navigator>
                    <navigator
                        class="item"
                        url="/pages/likou_dati/pages/question/batch_import_question/explain/explain"
                    >
                        <text class="iconfont icon-import color-primary font34"></text>
                        <view class="font14 color-content">批量导题</view>
                    </navigator>
                </view>

            </view>


            <view class="tools bg-white">
                <view class="title color-title">数据导出</view>
                <view class="list flex-row flex-wrap text-center">
                    <view class="item" @click="exportRankingData(7)">
                        <text class="iconfont icon-trophy color-primary font34"></text>
                        <view class="font14 color-content">排行榜</view>
                    </view>


                    <view class="item" hover-class="navigator-hover" @click="exportRankingData(21)">
                        <text class="iconfont icon-users color-primary font34"></text>
                        <view class="font14 color-content">活动用户</view>
                    </view>

                    <navigator class="item" url="/pages/activity/admin/export_record">
                        <text class="iconfont icon-export-excel color-primary font34"></text>
                        <view class="font14 color-content">导出记录</view>
                    </navigator>
                </view>
            </view>

        </view>


        <active-share ref="activeShare"/>

        <uni-popup ref="not_export_ranking_tips" type="center">
            <view class="export_ranking_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('not_export_ranking_tips')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top" style="padding: 50px 20px 20px;">
                    <text class="iconfont icon-export-excel color-green" style="font-size: 80px;"></text>
                    <view class="font14 color-sub">未开通导出数据功能</view>
                    <view class="font14 color-sub">请联系客服开通</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <button
                        open-type="contact"
                        class="export_ranking-btn bg-green color-white font16"
                        @click="uniPopupClose('not_export_ranking_tips')"
                    >联系客服开通</button>
                </view>
            </view>
        </uni-popup>

        <uni-popup ref="export_ranking_success" type="center">
            <view class="export_ranking_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('export_ranking_success')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top bg-primary color-white" style="padding: 50px 20px 20px;">
                    <icon type="success" size="80" color="#ffffff"></icon>
                    <view class="font18">导出成功</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ export_ranking_success_tips }}
                    </view>
                    <view
                        class="export_ranking-btn bg-info color-white"
                        hover-class="navigator-hover"
                        @click="copyDownloadSrc(false)"
                    >复制下载地址</view>
                </view>
            </view>
        </uni-popup>

        <uni-popup ref="question_method">
            <view class="question-method bg-white">
                <view class="pb10">当前选择: {{ is_rand_question === 1 ? '随机出题' : '固定出题' }}</view>
                <view class="flex-kai pt10">
                    <view
                        class="question-method-item"
                        :class="{ 'question-method-item-active': is_rand_question === 0 }"
                        hover-class="navigator-hover"
                        @click="questionMethodChange(0)"
                    >固定出题</view>
                    <view
                        class="question-method-item"
                        :class="{ 'question-method-item-active': is_rand_question === 1 }"
                        hover-class="navigator-hover"
                        @click="questionMethodChange(1)"
                    >随机出题</view>
                </view>
            </view>
            <view class="flex-all-center">
                <view @click="$refs.question_method.close()">
                    <uni-icons type="close" size="34" color="#ffffff"/>
                </view>
            </view>
        </uni-popup>

        <web-admin-src-copy-popup ref="web-admin-src-copy-popup"/>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import my_storage from '@/utils/storage.js'
import base64 from '@/utils/base64.js'

export default {
    data() {
        return {
            loading: true,
            id: '',
            detail: {},
            error: '',
            export_ranking_success_tips: '',
            web_base_url: '',
            popup_show: false,
            is_rand_question: 0
        }
    },
    onShareAppMessage() {
        let path = '/pages/guess-picture/user/detail?id=' + this.id
        if (this.detail.conf.active.screen_pic) path += `&screen_pic=` + (this.detail.conf.active.screen_pic || '')
        return {
            title: this.detail.name,
            path,
            imageUrl: this.detail.logo || ''
        }
    },
    onLoad(e) {
        console.log('活动详情页面路径参数', e)
        uni.showLoading({ mask: true })
        if (!e.id) {
            this.loading = false
            uni.hideLoading()
            this.error = '请指定活动id'
            return false
        }

        this.id = e.id
        login.uniLogin(err => {
            this.loading = false
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            if (app.globalData['shop_info']?.['shop_set']?.static_url) this.web_base_url = app.globalData['shop_info']['shop_set'].static_url


            this.getDetail()
        })
    },

    onShow() {
        if (app.globalData['update_exam'] === 1) {
            app.globalData['update_exam'] = null
            this.getExamDetails()
        }
    },

    methods: {
        getDetail() {
            xwy_api.getActivityDetail(this.id, res => {
                if (res.data?.['active_details']) {
                    const detail = res.data['active_details']
                    this.detail = detail
                    detail.conf.active.exam_id ? this.getExamDetails() : this.createExam()
                    my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)
                } else {
                    this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                }
            })
        },

        async getExamDetails() {
            const res = await xwy_api.request({
                url: 'front.flat.exam.examPapers/get_exam_papers_details',
                data: { exam_id: this.detail.conf.active.exam_id }
            })
            this.is_rand_question = res?.data?.is_rand_question || 0
            uni.hideLoading()
        },

        questionMethodChange(value) {
            if (value === 0) {
                // 固定出题重复点击不操作
                if (value !== this.is_rand_question) this.setFixedProblem()
                return this.$refs.question_method.close()
            }
            uni.navigateTo({
                url: `/pages/likou_dati/pages/question/random_question_types/random_question_types?exam_id=${this.detail.conf.active.exam_id}`
            })
            this.$refs.question_method.close()
        },

        async setFixedProblem() {
            uni.showLoading({ title: '设置中...', mask: true})
            const res = await xwy_api.request({
                url: 'front.flat.exam.examPapers/set_exam_answer_conf',
                data: {
                    exam_id: this.detail.conf.active.exam_id,
                    is_rand_question: 0
                }
            })
            uni.hideLoading()
            if (res?.status !== 1) return xwy_api.alert(res?.info || '设置失败')
            uni.showToast({ title: '固定出题设置成功', icon: 'none' })
            await this.getExamDetails()
        },

        async createExam() {
            const conf = {
                answer_question_types: 2
            }
            const res = await xwy_api.request({
                url: 'front.flat.exam.examPapers/create_exam_papers',
                data: {
                    exam_name: this.detail.name,
                    set_limit_times: 999999,
                    begin_time: this._utils.timestampToTime(this.detail.begin_time),
                    end_time: this._utils.timestampToTime(this.detail.end_time),
                    answer_limit_types: 1,
                    conf: base64['encode'](JSON.stringify(conf))
                }
            })
            const errorAlert = content => {
                uni.hideLoading()
                xwy_api.alert(content, { success: () => uni.navigateBack() })
            }
            if (res?.status !== 1) return errorAlert(res?.info || '考卷创建失败，请重新进入')
            const exam_id = res.data?.exam_id
            if (!exam_id) return errorAlert('考卷创建成功，但是接口没有返回考卷id')

            await this.setExamQuestionBank(exam_id)
        },

        async setExamQuestionBank(exam_id) {
            const question_rules_conf = {
                self_or_public: 2,       // 1我的题库，2公共题库，3从已添加到考卷的题目里出题
                rand_question_types: 2,  // 1设置不同题目类型不同数量， 2不限制题目类型，直接按照题目总数随机出题
                rand_question_num: 10,   // 不指定题型 从所有题型中拉取的题目数量
                rand_category_id: 6192,  // 党建猜图的分类id

                // rand_question_types = 1 需要传入以下参数
                // single_answer: 10,       // 单选题数量
                // multi_answer: 10,        // 多选题数量
                // true_false: 10,          // 判断题数量
                // fill_blank: 10           // 填空题数量
            }
            const res = await xwy_api.request({
                url: 'front.flat.exam.examPapers/set_exam_answer_conf',
                data: {
                    exam_id,
                    is_rand_question: 1,
                    question_rules_conf: JSON.stringify(question_rules_conf)
                }
            })

            if (res?.status !== 1) return xwy_api.alert(res?.info || '题库设置失败', {
                success: () => uni.navigateBack()
            })

            await this.saveExamId(exam_id)
        },

        async saveExamId(exam_id) {
            const data = {
                name: this.detail.name,
                organizer: this.detail.organizer || '',
                content: this.detail.content || '',
                begin_time: this.detail.begin_time,
                end_time: this.detail.end_time,
                logo: this.detail.logo || '',
                active_id: this.detail.active_id,
                types: this.detail.types
            }
            const conf = this.detail.conf
            conf.active.exam_id = exam_id
            data.conf = base64['encode'](JSON.stringify(conf))
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.admin/create_active',
                data
            })
            if (res?.status !== 1) return xwy_api.alert(res?.info || '考卷绑定失败，请重新进入', {
                success: () => uni.navigateBack()
            })

            this.getDetail()

            this.updateDetailPageData()
        },

        updateDetailPageData() {
            const pages = getCurrentPages()
            const page = pages.find(v => v.route === 'pages/guess-picture/user/detail')
            page.$vm.getDetail(true)
        },


        uniPopupOpen(ref) {
            this.popup_show = true
            this.$refs[ref].open()
        },

        uniPopupClose(ref) {
            this.popup_show = false
            this.$refs[ref].close()
        },


        exportExamDataAjax() {
            this.$uni.showLoading('导出中...')

            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id
            }

            xwy_api.ajax({
                url: 'front.flat.sport_step.export.admin_active/export_user_total_score',
                data,
                success: res => {
                    uni.hideLoading()
                    if (res?.status !== 1) return this.$uni.showModal(res.info || '导出失败')

                    this.export_ranking_success_tips = res.info || '导出成功'
                    this.export_ranking_src = res.data.url
                    this.copyDownloadSrc(true)
                    this.uniPopupOpen('export_ranking_success')


                    const title = `导出活动【${this.detail.name}】答题排行榜`;
                    my_storage.setExportExcelRecord({url: res.data.url, title})
                }
            })
        },


        exportRankingData(types) {
            const export_top_rank_excel_open = this.detail.rank_set?.['export_top_rank_excel']
            if (!export_top_rank_excel_open) return this.uniPopupOpen('not_export_ranking_tips')

            this.exportRankingDataAjax(types)
        },


        exportRankingDataAjax(types) {
            this.$uni.showLoading('导出中...')

            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id,
                types,
                page: 1,
                perpage: 3000
            }


            xwy_api.ajax({
                url: 'front.flat.sport_step.export.admin_active/export_user_total_rank',
                data,
                success: res => {
                    uni.hideLoading()
                    if (!res.status) {
                        uni.showModal({
                            title: '导出失败',
                            content: res.info || '导出失败',
                            showCancel: false
                        })
                        return false
                    }


                    this.export_ranking_success_tips = res.info || '导出成功'
                    this.export_ranking_src = res.data.url
                    this.copyDownloadSrc(true)
                    this.uniPopupOpen('export_ranking_success')


                    const title_options = {
                        7: `排行榜`,
                        21: '活动用户名单'
                    }

                    const title = `导出活动【${this.detail.name}】${title_options[types]}`
                    my_storage.setExportExcelRecord({url: res.data.url, title})
                }
            })
        },


        copyDownloadSrc(hide = false) {
            uni.setClipboardData({
                data: this.export_ranking_src,
                success: () => hide ? uni.hideToast() : this.$uni.showToast('复制成功', 'success', 1000)
            })
        },

        showActiveSharePopup() {
            this.$refs.activeShare.open({
                page: 'pages/guess-picture/user/detail',
                scene: 'id=' + this.detail.id,
                qrcode_logo: this.detail.rank_set?.closed_AD && this.detail.conf?.active?.qrcode_logo
            })
        },

        showWebUrl() {
            this.$refs['web-admin-src-copy-popup'].open({
                successCallback: () => this.popup_show = true,
                closeCallback: () => this.popup_show = false
            })
        },


        copyActivityPages() {
            let data = 'pages/guess-picture/user/detail?id=' + this.id
            if (this.detail.screen_pic) data += `&screen_pic=` + this.detail.screen_pic
            uni.setClipboardData({
                data,
                success() {
                    uni.hideToast()
                    uni.showModal({
                        title: '复制成功',
                        content: '小程序路径地址复制成功，请粘贴到公众号自定义菜单设置里',
                        showCancel: false
                    })
                }
            })
        },

        copy(data) {
            uni.setClipboardData({
                data,
                success: () => this.$uni.showToast('复制成功', 'none', 500)
            })
        }
    }
}
</script>

<style scoped>
.page {
    min-height: 100vh;
    padding-bottom: 15px;
    box-sizing: border-box;
}

.logo {
    display: block;
    width: 100vw;
    height: auto;
}


.tools {
    margin: 10px;
    border-radius: 10px;
    overflow: hidden;
}

.tools .title {
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.item {
    width: calc(100% / 4);
    padding: 10px 5px;
    box-sizing: border-box;
}



.export_ranking_popup {
    width: 280px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}

.export_ranking-btn {
    line-height: 40px;
    border-radius: 20px;
}

.export_ranking-btn::after {
    border: none;
}

.question-method {
    padding: 20px;
    border-radius: 10px;
    width: 90vw;
    max-width: 500px;
    box-sizing: border-box;
}
.question-method-item {
    width: calc(50% - 10px);
    line-height: 38px;
    border-radius: 5px;
    border: 1px solid #d3d1d1;
    text-align: center;
    box-sizing: border-box;
}
.question-method-item-active {
    color: #2d8cf0;
    border: 1px solid #2d8cf0;
}
</style>
