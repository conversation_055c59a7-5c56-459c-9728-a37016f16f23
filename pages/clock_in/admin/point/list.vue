<template>
    <view class="page bg-background">
        <template v-if="!loading">
            <view v-if="error_tips" class="text-center">
                <uni-icons type="info" size="100" color="#ff9900"/>
                <view class="color-sub font14">{{ error_tips }}</view>
            </view>

            <template v-if="list.length">
                <view class="top-add-bar flex-kai bg-white">
                    <view class="color-sub p10">共{{ list.length }}个点位</view>
                    <view @click="toAddOrEdit(false)" class="color-primary p10">
                        <text>添加点位</text>
                        <uni-icons type="forward" color="#2d8cf0"/>
                    </view>
                </view>


                <view class="list">
                    <view class="item bg-white" v-for="(item, index) in list" :key="index">
                        <view class="flex-row" style="padding-bottom: 10px;">
                            <view v-if="item.conf && item.conf.logo" class="pr10">
                                <image class="logo" :src="item.conf.logo" mode="aspectFill"/>
                            </view>
                            <view>
                                <view class="color-title pb5 ellipsis--l2">{{ item.name }}</view>
                                <view class="ellipsis--l2 pb5">
                                    <uni-icons type="location" size="14" color="#495060"/>
                                    <text class="color-content font14">
                                        {{ item.conf && item.conf.address || '' }}
                                    </text>
                                </view>
                                <view class="color-sub font14">排序：{{ item.sort_num }}</view>
                            </view>
                        </view>
                        <view class="tools-bar clearfix clear">
                            <view class="fr flex-row">
                                <view @click="createPointQrcode(item)" class="edit">
                                    <text class="iconfont icon-qr-code color-sub font20"></text>
                                </view>
                                <view v-if="nfcOpen" class="edit" @click="createPointNFC(item.id)">
                                    <text class="iconfont icon-nfc-tag color-sub font20"></text>
                                </view>
                                <view v-if="rank_set['pic_similar']" class="edit"
                                      @click="lookPointPictureList(item.id)">
                                    <uni-icons type="image" color="#80848f" size="24"/>
                                </view>
                                <view @click="toAddOrEdit(item.id)" class="edit">
                                    <text class="iconfont icon-edit color-sub font20"></text>
                                </view>
                                <view class="delete" @click="deleteItem(item.id)">
                                    <text class="iconfont icon-delete color-sub font20"></text>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </template>

            <view v-if="!list.length" class="text-center" style="padding-top: 15vh;">
                <text class="iconfont icon-map color-border" style="font-size: 100px;"></text>
                <view class="color-sub">该活动下暂无点位</view>
                <view class="flex-all-center pt15">
                    <view class="add-team-btn color-white bg-primary text-center"
                          @click="toAddOrEdit(false)">创建点位
                    </view>
                </view>
            </view>
        </template>

        <nfc-write-popup ref="nfcWritePopup" :active-id="id"/>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'

export default {
    data() {
        return {
            id: '',
            error_tips: '',
            loading: true,
            list: [],
            rank_set: {},

            // 打卡点位增加字段类型 types  判断
            // 0   是之前的关卡 id及健步走活动点位，打卡活动的打卡点
            // 1 代表是任务闯关活动里的打卡点（闯关里需要进行打卡，打卡的记录）
            types: 0
        }
    },

    computed: {
        nfcOpen() {
            return this.rank_set.openNFC
        }
    },
    
    onLoad(e) {
        this.$uni.showLoading()
        if (e.rank_set) this.rank_set = JSON.parse(e.rank_set)

        // 打卡点位增加字段类型 types  判断
        // 0   是之前的关卡 id及健步走活动点位，打卡活动的打卡点
        // 1 代表是任务闯关活动里的打卡点（闯关里需要进行打卡，打卡的记录）
        if (e.types) this.types = Number(e.types)

        this.id = e.id
        this.active_small_id = e.active_small_id
        login.uniLogin(err => {
            if (err && err.errMsg) return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})

            this.getList()
        })
    },

    onUnload() {
        // 清理NFC监听
        if (this.nfcAdapter) this.nfcAdapter.offDiscovered()
    },

    onPullDownRefresh() {
        if (!this.loading) this.getListInit()

        uni.stopPullDownRefresh()
    },
    methods: {
        getListInit() {
            this.loading = true
            this.$uni.showLoading()
            this.list = []
            this.getList()
        },

        async getList() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_map_point_list',
                data: {active_id: this.id}
            })
            
            uni.hideLoading()
            this.loading = false

            if (res?.status !== 1) return

            const list = res.data?.map_point_list || []
            this.list = list.filter(item => item.types === this.types)
        },

        async createPointQrcode(item) {
            if (item.share_image) return this.previewImage([item.share_image])

            this.$uni.showLoading('二维码生成中...')
            const share_image = await xwy_api.getWxAppCode({
                page: 'pages/clock_in/user/point_detail',
                scene: 'id=' + this.active_small_id + '&p_id=' + item.id
            })
            uni.hideLoading()

            if (!share_image) return this.$uni.showModal('二维码生成失败')

            item.share_image = share_image
            this.previewImage([item.share_image])
        },


        previewImage(urls, current = urls[0]) {
            console.log(urls)
            uni.previewImage({
                urls,
                current
            })
        },


        async getUrlScheme(point_id) {
            this.$uni.showLoading()
            const link = await this.xwy_api.generateNFCScheme({
                path: 'pages/clock_in/user/point_detail',
                query: `id=${this.id}&point_id=${point_id}&nfc=1`,
                model_id: 'CzYgyM5Z9JsvYKq6ILd0nA',
                sn: `${this.id}-${point_id}`
            })
            uni.hideLoading()
            return link
        },

        async createPointNFC(point_id) {
            const scheme = await this.getUrlScheme(point_id)
            if (!scheme) return this.$uni.showModal('点位链接生成失败,请重试。')

            this.nfcAdapter ||= wx.getNFCAdapter()
            await this.$refs.nfcWritePopup.write(this.nfcAdapter, scheme)
        },


        lookPointPictureList(id) {
            this.$uni.navigateTo(`./point-picture/list?active_id=${this.id}&point_id=${id}&is_admin=1`)
        },


        toAddOrEdit(id) {
            let url = `detail?active_id=${this.id}&rank_set=${JSON.stringify(this.rank_set)}`
            if (id) url += `&point_id=${id}`

            // 打卡点位增加字段类型 types  判断
            // 0   是之前的关卡 id及健步走活动点位，打卡活动的打卡点
            // 1 代表是任务闯关活动里的打卡点（闯关里需要进行打卡，打卡的记录）
            if (this.types) url += `&types=${this.types}`

            this.$uni.navigateTo(url, {
                events: {
                    reloadList: () => this.getListInit()
                }
            })
        },


        deleteItem(id) {
            this.deleteConfirm([id])
        },

        deleteConfirm(ids) {
            uni.showModal({
                title: '提示',
                content: `确定删除${ids.length > 1 ? '选中的' : '该'}点位?`,
                confirmText: '删除',
                cancelColor: '#80848f',
                confirmColor: '#ed3f14',
                success: res => {
                    if (res.confirm) this.deleteAjax(ids)
                }
            })
        },

        deleteAjax(ids) {
            this.$uni.showLoading('删除中...')

            xwy_api.ajax({
                url: 'front.flat.sport_step.admin/del_map_point',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id,
                    ids: ids.join(',')
                },
                success: res => {
                    uni.hideLoading()
                    if (!res.status) return this.$uni.showModal(res.info || '删除失败')

                    this.list = this.list.filter(item => {
                        return !ids.find(v => item.id === v)
                    })
                    this.$uni.showToast('已删除')
                }
            })

        },

    }
}
</script>

<style scoped lang="scss">
.page {
    min-height: 100vh;
    padding-bottom: 10px;
    padding-top: 42px;
    box-sizing: border-box;
}

.top-add-bar {
    position: fixed;
    z-index: 99;
    top: 0;
    left: 0;
    width: 100%;
    box-sizing: border-box;
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .top-add-bar {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }
}

/* #endif */

.item {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
}

.logo {
    width: 130px;
    min-width: 130px;
    height: 80px;
    display: block;
    border-radius: 5px;
}

.tools-bar {
    border-top: 1px solid #eee;
    padding-top: 10px;
}

.edit, .delete {
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 50%;
}

.edit {
    border: 1px solid #eee;
    margin-right: 10px;
}

.delete {
    border: 1px solid #eee;
}

.add-team-btn {
    width: 200px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
}
</style>
