<template>
    <view class="page bg-background">
        <view v-if="is_admin" class="flex-kai bg-white">
            <view class="p10 color-sub font14">点位已添加{{ total }}张图片</view>
            <view class="p10" @click="addPicture()">
                <text class="color-light-primary font14">添加图片</text>
                <uni-icons type="forward" size="14" color="#5cadff"/>
            </view>
        </view>
        
        <view class="image-list flex-row flex-wrap">
            <view class="image-item" v-for="item in pic_list" :key="item.id">
                <view v-if="is_admin" class="delete-icon flex-all-center" @click="deletePicture(item.id)">
                    <text class="iconfont icon-delete color-white"></text>
                </view>
                <image class="image-tag" :src="item.pic_url" mode="aspectFill" 
                       @click="previewImage(item.pic_url)"/>
            </view>
        </view>
        
        <view v-if="loading" class="text-center" :style="{paddingTop: current_page === 1 ? '30vh' : ''}">
            <load-ani/>
        </view>
        
        <view v-if="!loading && !total" class="text-center" style="padding-top: 15vh;">
            <text class="iconfont icon-empty-state color-divider" style="font-size: 120px;"></text>
            <view class="font14 color-sub">暂无图片</view>
            <view v-if="is_admin" class="big-add-button bg-light-primary color-white" 
                  hover-class="navigator-hover" @click="addPicture()">添加图片
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            loading: true,
            pic_list: [],
            current_page: 1,
            is_last_page: false,
            total: 0,
            is_admin: false
        }
    },
    
    onReachBottom() {
        if (this.is_last_page || this.loading) return
        this.current_page++
        this.loadPicList()
    },
    
    onPullDownRefresh() {
        this.reloadPicList().finally(() => uni.stopPullDownRefresh())
    },

    onLoad(params) {
        this.paramsHandle(params)
        this.$login.uniLogin(() => this.init())
    },

    methods: {
        paramsHandle(params) {
            if (params.is_admin) this.is_admin = true
            this.$uni.setNavigationBarTitle(this.is_admin ? '图片管理' : '示例图片')
            
            this.active_id = params.active_id
            this.point_id = Number(params.point_id)
        },
        
        async init() {
            await this.loadPicList()
        },
        
        async reloadPicList() {
            this.current_page = 1
            await this.loadPicList()
        },
        
        async loadPicList() {
            if (this.current_page  === 1) {
                this.pic_list = []
                this.is_last_page = false
            }
            this.loading = true
            await this.getPicList()
            this.loading = false
        },
        
        async getPicList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin.pointPic/point_pic_list',
                data: {
                    active_id: this.active_id,
                    point_id: this.point_id,
                    page: this.current_page,
                    perpage: 20
                }
            })
            
            const res_data = res?.data?.list
            if (!res_data) {
                this.is_last_page = true
                return
            }
            
            const list = res_data.data || []
            this.pic_list = list.map(({ id, pic_url }) => ({ id, pic_url }))
            this.is_last_page = res_data.is_lastpage
            this.total = res_data.total
        },

        addPicture() {
            this.$uni.navigateTo(`/pages/other/image_upload_or_select?active_id=${this.active_id}`, {
                events: {
                    newImg: src => this.$nextTick(() => this.submitAddPicture(src))
                }
            })
        },

        async submitAddPicture(url) {
            this.$uni.showLoading('正在添加...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin.pointPic/set_pic_point',
                data: {
                    url,
                    active_id: this.active_id,
                    point_id: this.point_id
                }
            })
            uni.hideLoading()
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '添加失败', {title: '添加失败'})
            this.$uni.showToast('图片已添加')
            
            await this.reloadPicList()
        },

        async deletePicture(id) {
            const modal = await this.$uni.showModal('确定删除该图片吗？', {showCancel: true})
            if (!modal.confirm) return
            
            this.$uni.showLoading('正在删除...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin.pointPic/del_pic_point',
                data: {
                    active_id: this.active_id,
                    id
                }
            })
            uni.hideLoading()
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '删除失败', {title: '删除失败'})
            this.$uni.showToast('图片已删除')
            
            await this.reloadPicList()
        },

        previewImage(url) {
            this.$uni.previewImage({
                urls: this.pic_list.map(item => item.pic_url),
                current: url
            })
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
}

.image-list {
    padding: 15rpx;
    .image-item {
        position: relative;
        padding: 15rpx;
        
        .delete-icon {
            position: absolute;
            top: 20rpx;
            right: 20rpx;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, .5);
        }
        
        .image-tag {
            width: 330rpx;
            height: 330rpx;
            border-radius: 10px;
            display: block;
        }
    }
}

.big-add-button {
    width: 250px;
    line-height: 44px;
    border-radius: 22px;
    margin: 30px auto;
}
</style>