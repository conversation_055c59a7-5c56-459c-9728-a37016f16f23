<template>
    <view class="page">
        <view class="form">
            <view class="form-item">
                <view class="top color-content">
                    <text>点位名称</text>
                    <text class="color-error font16"> *</text>
                </view>
                <view class="bottom font16">
                    <input class="input" type="text" v-model="name" placeholder="请输入点位名称"/>
                </view>
            </view>


            <view class="form-item">
                <view class="top color-content">
                    <text>点位坐标</text>
                    <text class="color-error font16"> *</text>
                </view>
                <view class="bottom font16">
                    <view class="flex-kai" @click="chooseLocation">
                        <view class="view">
                            <view v-if="lat && lng">{{ lat }},{{ lng }}</view>
                            <view v-else class="color-sub">请选择点位坐标</view>
                        </view>
                        <view class="flex-all-center">
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">
                    <text>点位地址</text>
                    <text class="color-error font16"> *</text>
                </view>
                <view class="bottom font16">
                    <textarea class="textarea" maxlength="300" auto-height="true" v-model="address"
                              placeholder="请输入点位地址"/>
                </view>
            </view>

            <view v-if="rank_set['mapPointCategorySet']" class="form-item">
                <view class="top color-content">点位分类</view>
                <view class="bottom font16">
                    <view class="flex-kai" @click="toSelCategory">
                        <view class="view">
                            <view v-if="category_id">{{ category_name }}</view>
                            <view v-else class="color-sub">关联分类</view>
                        </view>
                        <view class="flex-all-center" @click.stop="deBindOrSelectCategory">
                            <view v-if="category_id" class="color-sub font12" style="width: 30px;">
                                解绑
                            </view>
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">点位排序</view>
                <view class="bottom font16">
                    <input class="input" type="number" v-model="sort_num"
                           placeholder="地图点位排序,数字越小排在越前(不填默认0)"/>
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">文章介绍</view>
                <view class="bottom font16">
                    <view class="flex-kai" @click="toSelNews">
                        <view class="view">
                            <view v-if="news_id">{{ news_title || news_id }}</view>
                            <view v-else class="color-sub">选择文章</view>
                        </view>
                        <view class="flex-all-center">
                            <view v-if="news_id" class="color-sub font12" style="width: 30px;"
                                  @click.stop="deleteNews">解绑
                            </view>
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">答题考试</view>
                <view class="bottom font16">
                    <view class="flex-kai" @click="toSelExam">
                        <view class="view">
                            <view v-if="exam_id">{{ exam_title || exam_id }}</view>
                            <view v-else class="color-sub">选择考卷</view>
                        </view>
                        <view class="flex-all-center">
                            <view v-if="exam_id" class="color-sub font12" style="width: 30px;"
                                  @click.stop="deleteExam">解绑
                            </view>
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </view>
            </view>


            <view class="form-item">
                <view class="top color-content">抽奖设置</view>
                <view class="bottom font16">
                    <view class="flex-kai" @click="toSelLottery">
                        <view class="view">
                            <view v-if="lottery.lottery_id">{{ lottery_title || lottery.lottery_id }}</view>
                            <view v-else class="color-sub">关联抽奖活动</view>
                        </view>
                        <view class="flex-all-center">
                            <view v-if="lottery.lottery_id" class="color-sub font12" style="width: 30px;"
                                  @click.stop="deleteLottery">解绑
                            </view>
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </view>
            </view>


            <view class="form-item">
                <view class="top color-title">
                    <text>点位图片</text>
                    <text class="font12 color-sub pl5">(点位缩略图、点位详情顶部图片)</text>
                </view>
                <view style="padding-top: 5px;">
                    <view class="image-view" v-if="logo">

                        <image class="image-item" :src="logo" mode="aspectFill"
                               @click="previewImage([logo])"/>
                        <view class="del-image-item" @click.stop="logo = ''">
                            <uni-icons type="closeempty" color="#e20f04"/>
                        </view>
                    </view>

                    <view v-else class="add-image text-center" @click="changeLogo">
                        <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                    </view>
                </view>
            </view>


            <!--先不开发 等客户定制-->
            <template v-if="rank_set['redpack']">
                <view class="form-item">
                    <view class="top color-content">红包设置</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="redpack_open_opt"
                                :value="redpack_open_opt.findIndex(v => v.value === redpack_rules.open)"
                                range-key="title"
                                @change="redpack_rules.open = redpack_open_opt[$event.detail.value].value"
                            >
                                {{ redpack_open_opt.find(v => v.value === redpack_rules.open).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>


                <view class="form-item">
                    <view class="top color-content">
                        <text>单个红包金额</text>
                        <text class="color-error font16"> *</text>
                        <text class="pl5 color-sub font12">(单位: 元。)</text>
                    </view>
                    <view class="bottom font16 pt5 pb5">
                        <view class="flex-row">
                            <view style="width: 100px;">
                                <uni-easyinput type="digit" maxlength="6"
                                               v-model="redpack_rules.amount.min"/>
                            </view>
                            <view class="pl10 pr10 color-sub flex-all-center">-</view>
                            <view style="width: 100px;">
                                <uni-easyinput type="digit" maxlength="6"
                                               v-model="redpack_rules.amount.max"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view class="top color-content">
                            <text>点位红包余额</text>
                            <text class="color-error font16"> *</text>
                            <text class="pl5 color-sub font12">(单位: 元。)</text>
                        </view>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="digit" v-model="redpack_amount" maxlength="8"/>
                    </view>
                </view>
            </template>
        </view>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view class="save-btn color-white text-center font18 bg-primary" @click="save">保存</view>
        </view>


    </view>
</template>

<script>
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import base64 from '@/utils/base64.js'

export default {
    data() {
        return {
            active_id: '',
            id: '',
            name: '',
            logo: '',
            lat: '',
            lng: '',
            sort_num: '',
            news_id: '',
            news_title: '',
            address: '',
            rank_set: {},
            redpack_rules: {
                types: 1,
                open: 0,
                amount: {
                    min: 1,
                    max: 2
                }
            },
            redpack_open_opt: [
                {value: 1, title: '开启红包发放'},
                {value: 0, title: '暂停红包发放'}
            ],
            redpack_amount: 100,
            exam_id: '',
            exam_title: '',
            lottery: {
                lottery_id: '',
                must_sign: 1  // 【1】代表必需打卡签到才能参与抽奖，使用这个参数区分是打卡以后抽奖，还是健步走到达以后抽奖的
            },
            lottery_title: '',
            category_id: '',
            category_name: ''
        }
    },
    onLoad(e) {
        if (e.rank_set) this.rank_set = JSON.parse(e.rank_set)

        // 打卡点位增加字段类型 types  判断
        // 0   是之前的关卡 id及健步走活动点位，打卡活动的打卡点
        // 1 代表是任务闯关活动里的打卡点（闯关里需要进行打卡，打卡的记录）
        if (e.types) this.types = Number(e.types)

        this.$uni.showLoading()
        this.active_id = e.active_id

        login.uniLogin(err => {
            if (err && err.errMsg) return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            this.init(e)
        })
    },
    methods: {
        async init(e) {
            await this.getCategoryList()

            if (e.point_id) {
                this.point_id = e.point_id
                return await this.getPointDetail()
            }

            uni.hideLoading()
        },

        async getCategoryList() {
            if (!this.rank_set?.['mapPointCategorySet']) return

            const res = await this.xwy_api.getCategoryList({
                types: 38,
                page: 1,
                perpage: 1000,
                my_self: 1,
                active_id: this.active_id
            })

            const list = res?.data?.category_list?.data || []
            this.categoryList = list.map(item => ({category_id: item.category_id, name: item.name}))
        },


        async getPointDetail() {
            this.$uni.setNavigationBarTitle('修改点位')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/map_point_details',
                data: {id: this.point_id}
            })
            uni.hideLoading()

            const detail = res?.data?.['map_point_details']
            if (!res?.status || !detail) {
                return this.$uni.showModal(res?.info || '点位获取失败', {success: () => uni.navigateBack()})
            }

            this.name = detail.name
            if (detail.news_id) this.news_id = detail.news_id
            if (detail.news_title) this.news_title = detail.news_title
            if (detail.exam_id) this.exam_id = detail.exam_id
            if (detail.exam_details?.exam_name) this.exam_title = detail.exam_details.exam_name
            if (detail.lat) this.lat = detail.lat
            if (detail.lng) this.lng = detail.lng
            if (detail.sort_num) this.sort_num = detail.sort_num
            if (detail.category_id) {
                this.category_id = detail.category_id
                this.category_name = this.categoryList.find(item => item.category_id === detail.category_id)?.name || detail.category_id
            }
            if (detail.conf) {
                const conf = detail.conf
                if (conf.logo) this.logo = conf.logo
                if (conf.address) this.address = conf.address
                if (conf.redpack_rules) this.redpack_rules = conf.redpack_rules
                if (conf.lottery) {
                    conf.lottery.must_sign = 1  // 【1】代表必需打卡签到才能参与抽奖，使用这个参数区分是打卡以后抽奖，还是健步走到达以后抽奖的
                    this.lottery = conf.lottery
                }
            }
            if (detail.redpack_amount) this.redpack_amount = detail.redpack_amount
            if (detail['lottery_details']?.title) this.lottery_title = detail['lottery_details'].title
        },


        chooseLocation() {
            const obj = {
                success: res => {
                    this.lat = res.latitude
                    this.lng = res.longitude

                    if (res.address || res.name) {
                        const address = `${res.address || ''}${res.name || ''}`
                        if (this.address) {
                            this.$uni.showModal(`点位经纬度已改变，是否将点位地址改成${address}`, {
                                showCancel: true,
                                success: res => {
                                    if (res.confirm) this.address = address
                                }
                            })
                        } else {
                            this.address = address
                        }
                    }
                },
                fail: err => {
                    console.log(err)
                    if (err.errMsg === 'chooseLocation:fail:not supported') {
                        return this.$uni.showToast('不支持电脑端使用，请在手机微信打开')
                    }
                    if (err.errMsg !== 'chooseLocation:fail cancel') {
                        return this.$uni.showToast('地图拉起失败')
                    }
                }
            }
            if (this.lat && this.lng) {
                obj.latitude = this.lat * 1
                obj.longitude = this.lng * 1
            }
            uni.chooseLocation(obj)
        },

        deBindOrSelectCategory() {
            if (!this.category_id) return this.toSelCategory()

            this.category_id = ''
            this.category_name = ''
        },

        toSelCategory() {
            this.$uni.navigateTo(`/pages/category/list?is_sel=1&types=38&active_id=${this.active_id}&just_look_active=1`, {
                events: {
                    selCategory: data => {
                        this.category_id = data.id
                        this.category_name = data.name
                    }
                }
            })
        },


        toSelNews() {
            this.$uni.navigateTo('/pages/news/list?type=user&is_sel=true', {
                events: {
                    selNews: data => {
                        this.news_id = data.id
                        this.news_title = data.title
                    }
                }
            })
        },

        deleteNews() {
            this.news_id = ''
            this.news_title = ''
        },

        toSelExam() {
            if (!this.rank_set.exam_open) return this.$uni.showModal('未开通答题功能，请联系客服开通。')

            this.$uni.navigateTo('/pages/likou_dati/pages/exam/exam_list/exam_list?in_select=true', {
                events: {
                    updateExam: data => {
                        this.exam_id = data.id
                        this.exam_title = data.title
                    }
                }
            })
        },

        deleteExam() {
            this.exam_id = ''
            this.exam_title = ''
        },


        toSelLottery() {
            if (!this.rank_set['lottery_open']) return this.$uni.showModal('未开通抽奖功能，请联系客服开通。')

            this.$uni.navigateTo('/pages/lottery/admin/lottery/list?is_sel=1', {
                events: {
                    selLottery: data => {
                        this.lottery.lottery_id = data.id
                        this.lottery_title = data.title
                    }
                }
            })
        },

        deleteLottery() {
            this.lottery.lottery_id = ''
            this.lottery_title = ''
        },

        changeLogo() {
            this.$uni.navigateTo('/pages/other/image_upload_or_select?active_id=' + this.active_id, {
                events: {
                    newImg: src => {
                        this.logo = src
                    }
                }
            })
        },

        previewImage(urls, current = urls[0]) {
            uni.previewImage({
                urls,
                current
            })
        },

        save() {
            if (!this.name) return this.$uni.showToast('请输入点位名称')
            if (!this.lat || !this.lng) return this.$uni.showToast('请选择点位坐标')
            if (!this.address) return this.$uni.showToast('请输入点位地址')


            const value = {
                name: this.name,
                active_id: this.active_id,
                sort_num: this.sort_num ? this.sort_num * 1 : 0,
                news_id: this.news_id || '',
                exam_id: this.exam_id || '',
                lat: this.lat,
                lng: this.lng,
                category_id: this.category_id || ''
            }

            // 打卡点位增加字段类型 types  判断
            // 0   是之前的关卡 id及健步走活动点位，打卡活动的打卡点
            // 1 代表是任务闯关活动里的打卡点（闯关里需要进行打卡，打卡的记录）
            if (this.types) value.types = this.types

            const conf = {
                logo: this.logo,
                address: this.address
            }

            if (this.rank_set['redpack']) {
                const rules = this.redpack_rules
                if (rules.open) {
                    const min = Number(rules.amount.min)
                    const max = Number(rules.amount.max)
                    if (isNaN(min) || !min || isNaN(max) || !max)
                        return this.$uni.showToast('请输入正确的红包金额')
                    if (min > max) return this.$uni.showToast('红包最小金额不得大于最大金额')
                }
                conf.redpack_rules = rules
                value.redpack_amount = this.redpack_amount
            }

            if (this.lottery.lottery_id) conf.lottery = this.lottery

            let conf_str = JSON.stringify(conf)
            conf_str = conf_str.replace(/·/g, '-')
            value.conf = base64['encode'](conf_str)


            if (this.point_id) value.id = this.point_id

            this.saveAjax(value)
        },

        async saveAjax(data) {
            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/add_update_map_point',
                data
            })
            uni.hideLoading()

            const type_text = this.id ? '修改' : '添加'

            if (!res?.status) return this.$uni.showModal(res?.info || `${type_text}失败`)

            this.$uni.showToast(`${type_text}成功`)

            this.getOpenerEventChannel().emit('reloadList')
            this.$uni.navigateBack(1, {delay: 1000})
        },


    }
}
</script>

<style scoped>
.page {
    padding-bottom: 100px;
    background-color: #fff;
}

.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}


.form-item .textarea {
    width: 100%;
    line-height: 16px;
    padding: 10px 0;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}

.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
    width: 100px;
    height: 100px;
    border-radius: 5px;
    line-height: 95px;
    margin: 5px calc((100vw - 20px - (100px * 3)) / 6);
}


.image-item {
    width: calc(100vw - 20px);
    max-height: 200px;
    border-radius: 5px;
}

.image-view {
    position: relative;
    display: inline-block;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}


.bottom-btn-view {
    position: fixed;
    z-index: 99999;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.save-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}
</style>
