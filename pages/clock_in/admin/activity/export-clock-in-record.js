import openExcelFile from '@/utils/open-excel-file'
import xwy_api from '@/utils/api/xwy_api'

const records = async active_id => {
    const res = await xwy_api.request({
        url: 'front.flat.sport_step.sign_location.userSign/public_square_sign_list',
        data: {
            active_id,
            checked: 1,
            page: 1,
            perpage: 10000
        }
    })
    return res?.data?.['user_sign_list']?.data || []
}

const excelDataProcessing = (list, must_submit) => {
    const tHead = ['序号', '系统标识', '用户标识']
    must_submit.forEach(item => tHead.push(item.title))
    tHead.push('打卡备注', '打卡点', '打卡时间', '打卡图片')
    
    const who = getApp().globalData['who'] || ''
    
    const tBody = list.map((item, index) => {
        const user_must_submit = item.user_attend_details?.must_submit || []
        const must_submit_value = must_submit.map(a => {
            return user_must_submit.find(u => u.title === a.title)?.value || ''
        })
        
        return [
            index + 1,
            `w.${who}.${item.id || ''}`,
            `v.${item.userid || ''}`,
            ...must_submit_value,
            item.memo || '',
            item.point_details?.name || '',
            item.create_time,
            ...(item.conf_json?.pic_list || [])
        ]
    })
    return [tHead, ...tBody]
}

export default async function exportClockInRecord(active_id, must_submit) {
    const list = await records(active_id)
    
    const tableData = excelDataProcessing(list, must_submit)
    
    openExcelFile.openDocument(tableData, '打卡记录')
}