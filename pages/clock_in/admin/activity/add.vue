<template>
    <view class="page">
        <view v-if="success" class="save-success bg-white">
            <view class="icon text-center">
                <uni-icons type="checkbox-filled" size="100" color="#19be6b"/>
                <view class="color-content font16">活动{{ form_data.active_id ? '修改' : '创建' }}成功！</view>
            </view>
            <view class="flex-all-center">
                <button class="bg-success color-white" style="width: 200px;" @click="lookMyActivityList">
                    查看我的活动
                </button>
            </view>
        </view>

        <view class="type-list flex-row bg-white">
            <view
                class="type-item color-content font14"
                v-for="(item, index) in type_list"
                :key="index"
                :class="{'active-type': item.id === type_id}"
                :style="{width: 'calc(100% / ' + type_list.length + ')'}"
                @click="type_id = item.id"
            >{{ item.title }}
            </view>
        </view>

        <add-activity-tips ref="add_activity_tips"/>

        <view class="form">
            <template v-if="type_id === 1">
                <view class="form-item">
                    <view class="top color-content">
                        <text>活动名称</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="text" v-model="form_data.name"
                               placeholder="请输入活动名称"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">主办方</view>
                    <view class="bottom font16">
                        <input class="input" type="text" v-model="form_data.organizer"
                               placeholder="请输入主办方单位名称"/>
                    </view>
                </view>

                <template v-if="!form_data.active_id">
                    <view class="form-item">
                        <view class="top color-content">
                            <text>手机号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="number" v-model="form_data.mobile"
                                   placeholder="请输入真实手机号"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <text>微信号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="text" v-model="form_data.wechat_num"
                                   placeholder="请输入真实微信号"/>
                        </view>
                    </view>
                </template>

                <view class="form-item">
                    <view class="top color-content">活动开始时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.begin_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">活动结束时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.end_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view v-if="!conf.active.news.news_id" class="form-item">
                    <view class="top color-content">活动说明</view>
                    <view class="bottom font16">
                        <textarea
                            class="textarea"
                            maxlength="1000"
                            auto-height="true"
                            v-model="form_data.content"
                            placeholder="请输入活动说明"
                        />
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>活动说明文章</view>
                        <view class="font12 color-sub">
                            绑定文章后，活动说明将会显示文章内容，不会显示上面填写的活动说明
                        </view>
                    </view>
                    <view class="bottom font16">
                        <view class="flex-kai" @click="toSelNews">
                            <view class="view">
                                <view v-if="conf.active.news.news_id">
                                    {{ conf.active.news.news_title || conf.active.news.news_id }}
                                </view>
                                <view v-else class="color-sub">选择文章</view>
                            </view>
                            <view class="flex-all-center">
                                <view v-if="conf.active.news.news_id" class="color-sub font12"
                                      style="width: 30px;" @click.stop="deleteNews">
                                    解绑
                                </view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>
            </template>


            <template v-if="type_id === 3">
                <view class="form-item">
                    <view class="top color-content">活动参与方式</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.enter_types_list"
                                :value="form_options.enter_types_list.findIndex(v => v.value === conf.active.enter_types)"
                                range-key="title"
                                @change="enter_typesChange"
                            >
                                {{ form_options.enter_types_list.find(v => v.value === conf.active.enter_types).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="conf.active.enter_types === 2" class="form-item">
                    <view class="top color-content">
                        <text>活动密码</text>
                        <text v-if="!have_password" class="color-error font16"> *</text>
                        <text class="color-sub font12 pl5">(3-20位)</text>
                    </view>
                    <view class="bottom font16">

                        <input class="input" v-model="conf.active.password" maxlength="20"
                               :placeholder="have_password ? '已设置密码，无需修改密码不用填写' : '请输入活动密码'"/>
                    </view>
                </view>


                <view class="form-item">
                    <view class="top color-content">
                        <view>参与活动需要填写的信息</view>
                        <view class="font12 color-error">注意: 第一项为排行榜显示的姓名, 无法删除</view>
                    </view>
                    <view class="bottom font16">
                        <view class="ptm5">
                            <view class="ptm5 must-submit-list"
                                  v-for="(item, index) in conf.must_submit" :key="index">
                                <view class="flex-row">
                                    <view class="flex-row">
                                        <view style="width: 120px;">
                                            <uni-easyinput v-model="item.title" placeholder="请输入内容"/>
                                        </view>


                                        <picker :range="['选填', '必填']" :value="item.rules"
                                                @change="mustItemRulesChange($event, index)">
                                            <view class="must-rules-picker">
                                                <text class="color-content font14">
                                                    {{ item.rules === 0 ? '选填' : '必填' }}
                                                </text>
                                                <text class="iconfont icon-more color-sub font14"></text>
                                            </view>
                                        </picker>

                                        <picker :range="form_options.mustSubmitTypes" range-key="title"
                                                :value="form_options.mustSubmitTypes.findIndex(v => v.types === item.types)"
                                                :disabled="index === 0"
                                                @change="mustItemTypesChange($event, index)">
                                            <view class="must-rules-picker">
                                                <text class="color-content font14">
                                                    {{ form_options.mustSubmitTypes.find(v => v.types === item.types).title || '' }}
                                                </text>
                                                <text v-if="index !== 0"
                                                      class="iconfont icon-more color-sub font14"></text>
                                            </view>
                                        </picker>
                                    </view>
                                    <view v-if="index !== 0" class="delete-rules font14 color-error"
                                          @click="conf.must_submit.splice(index, 1)">
                                        删除
                                    </view>
                                </view>

                                <view v-if="item.types === 2" class="pl10">
                                    <view class="must-options-item flex-row"
                                          v-for="(item_, _index) in item.options" :key="_index">
                                        <view class="color-sub delete-rules text-right"
                                              style="width: 20px; padding: 0 5px 0 0;">
                                            {{ _index + 1 }}:
                                        </view>
                                        <view style="width: 200px;">
                                            <uni-easyinput v-model="item_.text" placeholder="请输入内容"
                                                           @blur="mustSubmitOptionTextChange($event, index, _index)"/>
                                        </view>
                                        <view class="delete-rules">
                                            <text class="color-error font14"
                                                  @click="deleteOptionsItem(index, _index)">
                                                删除
                                            </text>
                                        </view>
                                    </view>
                                    <view class="flex-row">
                                        <view class="color-sub font14 ptm5" @click="addOption(index)">
                                            + 添加新选项
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <view class="flex-row">
                                <view class="color-primary font14 ptm5" @click="addMust">+ 添加新项</view>
                            </view>
                        </view>
                    </view>
                </view>

                <template v-if="signatureOpen">
                    <view class="form-item">
                        <view class="top color-title">
                            <text>签名背景图</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view style="padding-top: 5px;">
                            <view class="image-view" v-if="conf.active.must_submit_signature.bg">

                                <image class="image-item" :src="conf.active.must_submit_signature.bg"
                                       mode="aspectFill"
                                       @click="previewImage([conf.active.must_submit_signature.bg])"/>
                                <view class="del-image-item"
                                      @click.stop="conf.active.must_submit_signature.bg = ''">
                                    <uni-icons type="closeempty" color="#e20f04"/>
                                </view>
                            </view>

                            <view v-else class="add-image text-center"
                                  @click="changeImage('must_submit_signature_bg')">
                                <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">签名区域宽度</view>
                        <view class="bottom font16">
                            <input class="input" type="number" maxlength="10" placeholder="不填默认200"
                                   v-model="conf.active.must_submit_signature.width"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">签名区域高度</view>
                        <view class="bottom font16">
                            <input class="input" type="number" maxlength="10" placeholder="不填默认100"
                                   v-model="conf.active.must_submit_signature.height"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">签名区域左上角离图片左边距离</view>
                        <view class="bottom font16">
                            <input class="input" type="number" maxlength="10" placeholder="不填默认0"
                                   v-model="conf.active.must_submit_signature.x"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">签名区域左上角离图片顶部距离</view>
                        <view class="bottom font16">
                            <input class="input" type="number" maxlength="10" placeholder="不填默认0"
                                   v-model="conf.active.must_submit_signature.y"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">签名颜色</view>
                        <view class="bottom font16">
                            <input class="input" maxlength="7" placeholder="不填默认黑色"
                                   v-model="conf.active.must_submit_signature.color"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">签名粗细</view>
                        <view class="bottom font16">
                            <input class="input" maxlength="7" placeholder="不填默认黑色"
                                   v-model="conf.active.must_submit_signature.size"/>
                        </view>
                    </view>
                </template>


                <view class="form-item">
                    <view class="top color-content">报名开始时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker type="datetime" v-model="conf.active.submit.begin"
                                                 end="2038-12-31" :border="false" :clear-icon="true"/>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">报名截止时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker type="datetime" v-model="conf.active.submit.end"
                                                 end="2038-12-31" :border="false" :clear-icon="true"/>
                        </view>
                    </view>
                </view>

                <view v-if="rank_set.team_group_open" class="form-item">
                    <view class="top color-content">报名时队伍是否必选</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="['非必选', '必选']"
                                :value="conf.active.team_required"
                                data-isnumber="1"
                                @change="conf.active.team_required = Number($event.detail.value)"
                            >
                                {{ conf.active.team_required === 1 ? '' : '非' }}必选
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">活动规则</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.activity_rules_hide_opt"
                                :value="form_options.activity_rules_hide_opt.findIndex(v => v.value === conf.active.activity_rules_hide)"
                                range-key="title"
                                @change="conf.active.activity_rules_hide = form_options.activity_rules_hide_opt[$event.detail.value].value"
                            >
                                {{ form_options.activity_rules_hide_opt.find(v => v.value === conf.active.activity_rules_hide).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">意见反馈</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.close_feedback_opt"
                                :value="form_options.close_feedback_opt.findIndex(v => v.value === conf.active.close_feedback)"
                                range-key="title"
                                @change="conf.active.close_feedback = form_options.close_feedback_opt[$event.detail.value].value"
                            >
                                {{ form_options.close_feedback_opt.find(v => v.value === conf.active.close_feedback).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>
            </template>


            <template v-if="type_id === 5">
                <view class="form-item">
                    <view class="top color-content">
                        <view>每天打卡开始时间</view>
                        <view class="color-sub font12">(未到设置时间不能打卡)</view>
                    </view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <view class="view">
                                <picker-time v-if="conf.active.exchange_start_time"
                                             :text="conf.active.exchange_start_time"
                                             :time.sync="conf.active.exchange_start_time"/>
                                <picker-time v-else :time.sync="conf.active.exchange_start_time"/>
                            </view>
                            <view class="flex-all-center">
                                <view v-if="conf.active.exchange_start_time"
                                      class="color-sub font12" style="width: 90px;"
                                      @click.stop="conf.active.exchange_start_time = ''">
                                    设为 00:00:00
                                </view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>每天打卡结束时间</view>
                        <view class="color-sub font12">(超过设置时间不能打卡)</view>
                    </view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <view class="view">
                                <picker-time v-if="conf.active.exchange_end_time"
                                             :time.sync="conf.active.exchange_end_time"
                                             :text="conf.active.exchange_end_time"/>
                                <picker-time v-else text="23:59:59"
                                             :time.sync="conf.active.exchange_end_time"/>
                            </view>
                            <view class="flex-all-center">
                                <view v-if="conf.active.exchange_end_time" class="color-sub font12"
                                      style="width: 90px;" @click.stop="conf.active.exchange_end_time = ''">
                                    设为 23:59:59
                                </view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>


                <view class="form-item">
                    <view class="top color-content">是否需要打卡点</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.point_hide_list"
                                :value="form_options.point_hide_list.findIndex(v => v.value === conf.active.point_hide)"
                                range-key="title"
                                @change="conf.active.point_hide = form_options.point_hide_list[$event.detail.value].value"
                            >
                                {{ form_options.point_hide_list.find(v => v.value === conf.active.point_hide).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <template v-if="!conf.active.point_hide">
                    <view class="form-item">
                        <view class="top color-content">打卡点默认展示方式</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="form_options.point_show_type_list"
                                    :value="form_options.point_show_type_list.findIndex(v => v.value === conf.active.point_show_type)"
                                    range-key="title"
                                    @change="conf.active.point_show_type = form_options.point_show_type_list[$event.detail.value].value"
                                >
                                    {{ form_options.point_show_type_list.find(v => v.value === conf.active.point_show_type).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">地图缩放等级</view>
                        <view class="bottom font16">

                            <slider
                                :min="3"
                                :max="20"
                                :value="conf.active.scale"
                                :step=".1"
                                activeColor="#2f8cec"
                                block-color="#2f8cec"
                                show-value="true"
                                @change="conf.active.scale = ~~($event.detail.value * 10) / 10"
                            ></slider>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">地图中心点坐标</view>
                        <view class="bottom font16">
                            <view class="flex-kai" @click="chooseLocation">
                                <view class="view">
                                    <view
                                        v-if="map_center_point && map_center_point.latitude && map_center_point.longitude">
                                        {{ map_center_point.latitude }},{{ map_center_point.longitude }}
                                    </view>
                                    <view v-else class="color-sub">请选择地图中心点坐标</view>
                                </view>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">是否在打卡点附近才能打卡</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="form_options.clock_in_types_list"
                                    :value="form_options.clock_in_types_list.findIndex(v => v.value === conf.active.clock_in_on_site.open)"
                                    range-key="title"
                                    @change="conf.active.clock_in_on_site.open = form_options.clock_in_types_list[$event.detail.value].value"
                                >
                                    {{ form_options.clock_in_types_list.find(v => v.value === conf.active.clock_in_on_site.open).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-if="conf.active.clock_in_on_site.open" class="form-item">
                        <view class="top color-content">
                            <text>打卡范围</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16 view flex-row" style="line-height: 26px;">
                            <view class="font14 pr5">需在打卡点附近</view>
                            <uni-number-box :min="0.01" :max="9999" :step="0.01"
                                            v-model="conf.active.clock_in_on_site.distance"/>
                            <view class="font14 pl5">公里内才能打卡</view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">按打卡点顺序打卡</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="form_options.sequential_clock_in"
                                    :value="form_options.sequential_clock_in.findIndex(v => v.value === conf.active.sequential_clock_in)"
                                    range-key="title"
                                    @change="conf.active.sequential_clock_in = form_options.sequential_clock_in[$event.detail.value].value"
                                >
                                    {{ form_options.sequential_clock_in.find(v => v.value === conf.active.sequential_clock_in).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">是否只能通过扫打卡点二维码进入才能打卡</view>
                        <view class="bottom font16">
                            <view class="flex-kai">
                                <picker
                                    class="view"
                                    :range="['否', '是']"
                                    :value="conf.active.just_qrcode_sign"
                                    @change="conf.active.just_qrcode_sign = Number($event.detail.value)"
                                >
                                    {{ conf.active.just_qrcode_sign ? '是' : '否' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-show="rank_set.team_group_open && conf.active.sequential_clock_in"
                          class="form-item">
                        <view class="top color-content">队伍打卡限制</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="['无限制', '队伍所有成员打完卡才能打卡下一打卡点']"
                                    :value="conf.active.must_team_person_finish_clock_in"
                                    @change="conf.active.must_team_person_finish_clock_in = Number($event.detail.value)"
                                >
                                    {{ conf.active.must_team_person_finish_clock_in === 1 ? '队伍所有成员打完卡才能打卡下一打卡点' : '无限制' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>
                </template>

                <template v-if="rank_set.exam_open">
                    <view class="form-item">
                        <view class="top color-content">点位答题限制</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="form_options.must_exam_open"
                                    range-key="title"
                                    :value="conf.active.must_exam.open"
                                    @change="mustExamOpenChange"
                                >
                                    {{ form_options.must_exam_open[conf.active.must_exam.open].title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-if="conf.active.must_exam.open"
                          class="form-item">
                        <view class="top color-content">需要答题达到多少分才能打卡</view>
                        <view class="bottom font16">
                            <input class="input" type="digit" maxlength="5"
                                   v-model="conf.active.must_exam.score" placeholder="不限制分数填0或不填即可"/>
                        </view>
                    </view>
                </template>


                <view class="form-item">
                    <view class="top color-content">打卡记录是否需要审核后才能显示在广场</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.not_check_list"
                                :value="form_options.not_check_list.findIndex(v => v.value === conf.active.not_check)"
                                range-key="title"
                                @change="conf.active.not_check = form_options.not_check_list[$event.detail.value].value"
                            >
                                {{ form_options.not_check_list.find(v => v.value === conf.active.not_check).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">打卡次数</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <template>

                                <picker
                                    v-if="conf.active.point_hide"
                                    class="view"
                                    mode="selector"
                                    :range="form_options.not_point_sign_times_list"
                                    :value="form_options.not_point_sign_times_list.findIndex(v => v.value === conf.active.sign_times_type)"
                                    range-key="title"
                                    @change="conf.active.sign_times_type = form_options.not_point_sign_times_list[$event.detail.value].value"
                                >
                                    {{ form_options.not_point_sign_times_list.find(v => v.value === conf.active.sign_times_type).title }}
                                </picker>

                                <picker
                                    v-else
                                    class="view"
                                    mode="selector"
                                    :range="form_options.sign_times_list"
                                    :value="form_options.sign_times_list.findIndex(v => v.value === conf.active.sign_times_type)"
                                    range-key="title"
                                    @change="conf.active.sign_times_type = form_options.sign_times_list[$event.detail.value].value"
                                >
                                    {{ form_options.sign_times_list.find(v => v.value === conf.active.sign_times_type).title }}
                                </picker>
                            </template>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-show="conf.active.sign_times_type === 2" class="form-item">
                    <view class="top color-content">
                        <view>{{ conf.active.point_hide ? '' : '每个打卡点' }}每日打卡次数上限</view>
                        <view class="color-sub font12">
                            限制{{ conf.active.point_hide ? '' : '每个打卡点' }}每日可以打卡的次数上限(不填或填0,默认为1次)。
                        </view>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="Number" v-model="conf.active.daily_submit_num"
                               placeholder="请输入次数上限"/>
                    </view>
                </view>

                <view v-if="!conf.active.point_hide && conf.active.sign_times_type === 1" class="form-item">
                    <view class="top color-content">
                        <view>统计用户打卡用时</view>
                        <view class="color-sub font12">
                            开启后, 会统计用户从第一个点到最后一个点打卡完成累计耗费的打卡时间。
                        </view>
                    </view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker class="view" :range="['关闭', '开启']"
                                    :value="conf.active.record_used_time"
                                    @change="conf.active.record_used_time = Number($event.detail.value)">
                                {{ conf.active.record_used_time ? '开启' : '关闭' }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="rank_set.submitSignBetweenThirtyMinutes" class="form-item">
                    <view class="top color-content">
                        <view>连续打卡有效时间设置 (单位: 分钟)</view>
                        <view class="color-sub font12">有效时间内连续打卡可获得奖励积分，超过有效时间则重新开始打卡。</view>
                    </view>
                    <view class="bottom font16">
                        <input class="input" maxlength="100" v-model="conf.active.submit_sign.period_minutes"
                            placeholder="请填写有效时间(单位: 分钟)"/>
                    </view>
                </view>

                <template v-if="rank_set.openNFC">
                    <view class="form-item">
                        <view class="top color-content">只能从NFC标签进入打卡</view>
                        <view class="bottom font16">
                            <view class="flex-kai">
                                <picker class="view" :range="['否', '是']"
                                    :value="conf.active.just_nfc_sign"
                                    @change="conf.active.just_nfc_sign = Number($event.detail.value)">
                                    {{ conf.active.just_nfc_sign ? '是' : '否' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">从NFC标签进入自动打卡</view>
                        <view class="bottom font16">
                            <view class="flex-kai">
                                <picker class="view" :range="['否', '是']"
                                    :value="conf.active.auto_sign_nfc"
                                    @change="conf.active.auto_sign_nfc = Number($event.detail.value)">
                                    {{ conf.active.auto_sign_nfc ? '是' : '否' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>
                </template>

                
                <template v-if="!conf.active.auto_sign_nfc">
                    <view class="form-item">
                        <view class="top color-content">打卡备注填写设置</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="form_options.memo_required_list"
                                    :value="form_options.memo_required_list.findIndex(v => v.value === conf.active.memo_required)"
                                    range-key="title"
                                    @change="conf.active.memo_required = form_options.memo_required_list[$event.detail.value].value"
                                >
                                    {{ form_options.memo_required_list.find(v => v.value === conf.active.memo_required).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-if="conf.active.memo_required" class="form-item">
                        <view class="top color-content">打卡备注文本框提示语</view>
                        <view class="bottom font16">
                            <input class="input" maxlength="100" v-model="conf.active.memo_placeholder"
                                placeholder="不填默认为系统提示语"/>
                        </view>
                    </view>

                    <view v-if="conf.active.memo_required === 1"
                        class="form-item">
                        <view class="top color-content">打卡备注最低字数</view>
                        <view class="bottom font16">
                            <input class="input" type="digit" maxlength="5"
                                v-model="conf.active.memo_min_words" placeholder="不填默认为1"/>
                        </view>
                    </view>


                    <view class="form-item">
                        <view class="top color-content">打卡获取微信运动步数设置</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="form_options.step_required_list"
                                    :value="form_options.step_required_list.findIndex(v => v.value === conf.active.step_required)"
                                    range-key="title"
                                    @change="conf.active.step_required = form_options.step_required_list[$event.detail.value].value"
                                >
                                    {{ form_options.step_required_list.find(v => v.value === conf.active.step_required).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-if="conf.active.step_required" class="form-item">
                        <view class="top color-content">
                            <view>最低运动步数限制</view>
                            <view class="color-sub font12">(需要达到指定步数才能打卡，0或不填不限制步数)</view>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="number" maxlength="6" v-model="conf.active.min_step"
                                placeholder="0或不填不限制步数"/>
                        </view>
                    </view>

                    <view v-if="conf.active.point_hide" class="form-item">
                        <view class="top color-content">打卡上传位置设置</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="form_options.address_required_list"
                                    :value="form_options.address_required_list.findIndex(v => v.value === conf.active.address_required)"
                                    range-key="title"
                                    @change="conf.active.address_required = form_options.address_required_list[$event.detail.value].value"
                                >
                                    {{ form_options.address_required_list.find(v => v.value === conf.active.address_required).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">打卡图片上传设置</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="form_options.pic_list_required_list"
                                    :value="form_options.pic_list_required_list.findIndex(v => v.value === conf.active.pic_list_required)"
                                    range-key="title"
                                    @change="conf.active.pic_list_required = form_options.pic_list_required_list[$event.detail.value].value"
                                >
                                    {{ form_options.pic_list_required_list.find(v => v.value === conf.active.pic_list_required).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-if="rank_set['pic_similar'] && conf.active.pic_list_required" class="form-item">
                        <view class="top color-content">
                            <text>打卡图片相似度通过率</text>
                            <text class="color-sub font12">(0-100)</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="number" maxlength="3" placeholder="请输入0-100的整数"
                                v-model="conf.active.similarity_percent"/>
                        </view>
                    </view>


                    <view class="form-item">
                        <view class="top color-content">打卡视频上传设置</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="form_options.video_list_required_list"
                                    :value="form_options.video_list_required_list.findIndex(v => v.value === conf.active.video_list_required)"
                                    range-key="title"
                                    @change="videoListRequiredChange"
                                >
                                    {{ form_options.video_list_required_list.find(v => v.value === conf.active.video_list_required).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">是否允许修改打卡记录</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="form_options.records_not_edit_list"
                                    :value="form_options.records_not_edit_list.findIndex(v => v.value === conf.active.records_not_edit)"
                                    range-key="title"
                                    @change="conf.active.records_not_edit = form_options.records_not_edit_list[$event.detail.value].value"
                                >
                                    {{ form_options.records_not_edit_list.find(v => v.value === conf.active.records_not_edit).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>
                </template>

                <template v-if="rank_set.gift_goods">
                    <view class="form-item">
                        <view class="top color-content">
                            <text>每次打卡奖励{{ integralUnit }}数</text>
                            <text class="font12 color-sub pl5">(不填或填0不奖励)</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" v-model="conf.active.integral.per_point_reward"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <text>每日打卡完所有点位奖励{{ integralUnit }}数</text>
                            <text class="font12 color-sub pl5">(不填或填0不奖励)</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" v-model="conf.active.integral.finished_all_point_reward"/>
                        </view>
                    </view>
                </template>


                <view v-if="showRecordListTopSet" class="form-item">
                    <view class="top color-content">打卡记录数据显示方式</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.sign_record_top_type"
                                :value="form_options.sign_record_top_type.findIndex(v => v.value === conf.active.sign_record_top_type)"
                                range-key="title"
                                @change="conf.active.sign_record_top_type = form_options.sign_record_top_type[$event.detail.value].value"
                            >
                                {{ form_options.sign_record_top_type.find(v => v.value === conf.active.sign_record_top_type).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>


                <view v-if="rank_set['sign_num_lottery']" class="form-item">
                    <view class="top color-content">打卡抽奖设置</view>
                    <view class="bottom">
                        <clock-in-lottery-set :set.sync="conf.active.sign_set.rules"/>
                    </view>
                </view>
            </template>

            <view v-show="type_id === 7">
                <template v-if="rank_set['gift_goods']">
                    <view class="form-item">
                        <view class="top color-content">是否显示积分商城入口</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    :range="['显示', '隐藏']"
                                    :value="conf.active.gift_goods_hide"
                                    @change="conf.active.gift_goods_hide = Number($event.detail.value)"
                                >
                                    {{ conf.active.gift_goods_hide ? '隐藏' : '显示' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <text>积分单位名称</text>
                            <text class="font12 color-sub pl5">(默认: {{ default_integral_unit }})</text>
                        </view>
                        <view class="bottom font16">

                            <input class="input" type="text" v-model="conf.active.integral.unit"
                                   :placeholder="'不填默认为 ' + default_integral_unit"/>
                        </view>
                    </view>

                    <view v-if="rank_set['share_friends']" class="form-item">
                        <view class="top color-content">邀请新用户参与活动奖励{{ integralUnit }}</view>
                        <view class="bottom font16">
                            <input class="input" type="number" v-model="conf.active.share_friends.integral"
                                   placeholder="不填或填0不奖励"/>
                        </view>
                    </view>

                    <template v-if="rank_set['auto_exchange_press_button'] || rank_set['one_day_auto_exchange']">
                        <view class="form-item">
                            <view class="top color-content">
                                <text>步数奖励{{ integralUnit }}设置</text>
                                <text class="font12 color-sub pl5">(不填或填0不奖励)</text>
                            </view>
                            <view class="bottom font16 flex-row pt10 pb10">
                                <view style="width: 100px; padding-right: 2px;">
                                    <uni-easyinput type="number" maxlength="8" trim
                                                   v-model="conf.active.integral.exchange_step"/>
                                </view>
                                <view style="line-height: 36px;">步奖励1{{ integralUnit }}</view>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">最低奖励步数</view>
                            <view class="bottom font16">
                                <input class="input" type="number" maxlength="8"
                                       v-model="conf.active.min_num"
                                       placeholder="低于设置的步数不奖励,0或不填代表不限制"/>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">每天步数兑换上限</view>
                            <view class="bottom font16">
                                <input class="input" type="number" maxlength="8"
                                       v-model="conf.active.max_num"
                                       placeholder="单个用户每天奖励的步数上限,0或不填代表不设上限"/>
                            </view>
                        </view>
                    </template>

                    <view v-if="rank_set.exam_open" class="form-item">
                        <view class="top color-content">
                            <view>答题奖励{{ integralUnit }}规则</view>
                            <view class="font12 color-sub pl5">
                                分数不填或填0不奖励，奖励{{ integralUnit }}不填或填0默认为1
                            </view>
                        </view>
                        <view class="bottom font16">
                            <view class="flex-row ptm5">
                                <view>每获得</view>
                                <view class="reward_step_by_exam_input" style="width: 80px;">
                                    <input class="text-center" type="digit" maxlength="5"
                                           v-model="conf.active.integral.exam_reward"/>
                                </view>
                                <view>分奖励</view>
                                <view class="reward_step_by_exam_input" style="width: 80px;">
                                    <input class="text-center" type="digit" maxlength="5"
                                           v-model="conf.active.integral.exam_reward_num"/>
                                </view>
                                <view>{{ integralUnit }}</view>
                            </view>
                        </view>
                    </view>
                </template>

                <view v-if="rank_set['morning_daily_sign']" class="form-item">
                    <view class="top color-content">每日签到奖励设置</view>
                    <view class="bottom pt10 pb10">
                        <daily-sign-set :set.sync="conf.active.daily_sign" :unit.sync="integralUnit"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">点赞功能</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="['开启点赞功能', '关闭点赞功能']"
                                :value="conf.active.closed_likes || 0"
                                @change="conf.active.closed_likes = Number($event.detail.value)"
                            >
                                {{ conf.active.closed_likes ? '关闭点赞功能' : '开启点赞功能' }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="!conf.active.closed_likes" class="form-item">
                    <view class="top color-content">点赞限制</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="['所有用户都能点赞', '只有参与了活动的用户才能点赞']"
                                :value="conf.active.like_limit || 0"
                                @change="conf.active.like_limit = Number($event.detail.value)"
                            >
                                {{ conf.active.like_limit ? '只有参与了活动的用户才能点赞' : '所有用户都能点赞' }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>


                <view class="form-item">
                    <view class="top color-content">活动详情页面图标扩展</view>
                    <view class="bottom font16 pt5 pb5">
                        <view
                            class="flex-kai pl10 pr10"
                            style="border: 1px solid #eee; border-radius: 5px; margin-bottom: 5px;"
                            v-for="(item, index) in conf.active.detail_icon_list"
                            :key="index"
                        >
                            <view>
                                <view style="padding: 5px 0;" @click="selIcon(index)">
                                    <text class="color-content pr5">显示图标:</text>
                                    <text :class="'iconfont font28 color-primary ' + item.icon"></text>
                                    <text class="pl5 color-sub font12">(点击更换)</text>
                                </view>

                                <picker :range="detail_icon_conf" range-key="title"
                                        @change="detailIconItemChange($event, index)">
                                    <view style="padding: 5px 0;">
                                        <text class="pr5 color-content">跳转类型:</text>
                                        <text>{{ detail_icon_conf[item.type].title }}</text>
                                    </view>
                                </picker>
                                <view class="flex-row" style="padding: 5px 0;">
                                    <text class="pr5 color-content">显示文字:</text>
                                    <input v-model="item.title" placeholder="6个字以内"
                                           style="border-bottom: 1px solid #eee;"/>
                                </view>
                                <view style="padding: 5px 0;" @click="bindNewsOrCategory(index)">
                                    <text class="pr5 color-content">
                                        绑定{{ item.type === 0 ? '文章' : '分类' }}:
                                    </text>
                                    <template>
                                        <text v-if="item.id">{{ item.name }}</text>
                                        <text class="color-sub" v-else>
                                            请选择{{ item.type === 0 ? '文章' : '分类' }}
                                        </text>
                                    </template>
                                </view>
                            </view>
                            <view class="pt5" @click="delDetailIcon(index)">
                                <text class="iconfont icon-delete color-error font20"></text>
                            </view>
                        </view>

                        <view class="color-primary pt5 pb5" @click="addDetailIcon">添加扩展</view>
                    </view>
                </view>


                <active-share-set :active-id="form_data.active_id" :rank-set="rank_set"
                                  :qrcode-logo.sync="conf.active.qrcode_logo"
                                  :share-image.sync="conf.active.share_image"
                                  :share-title.sync="conf.active.share_title"/>
            </view>

            <template v-if="type_id === 2">
                <active-image-set :active-id="form_data.active_id" :rankSet="rank_set"
                                  :logo.sync="form_data.logo"
                                  :screen-pic.sync="conf.active.screen_pic"
                                  :top-rank-banner.sync="conf.active.top_rank_banner"/>


                <view v-if="point_complete_badge_set_show" class="form-item">
                    <view class="top">
                        <text class="color-title">已打卡徽章图片</text>
                        <text class="font12 color-sub pl5">(打卡点列表已打卡徽章图片，不设置使用默认徽章)
                        </text>
                        <view class="font12 color-sub">图片尺寸建议: 50*50</view>
                    </view>
                    <view style="padding-top: 5px;">
                        <view v-if="conf.active.point_complete_badge" class="top-rank-banner-item">

                            <image :src="conf.active.point_complete_badge" mode="aspectFill"
                                   @click="previewImage([conf.active.point_complete_badge])"/>
                            <view class="del-image-item"
                                  @click.stop="conf.active.point_complete_badge = ''">
                                <uni-icons type="closeempty" color="#e20f04"/>
                            </view>
                        </view>
                        <view v-else class="add-image text-center"
                              @click="changeImage('point_complete_badge')">
                            <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                        </view>
                    </view>
                </view>
            </template>


            <template v-if="type_id === 6">
                <view>
                    <view class="flex-kai pb10 pt10" style="border-bottom: 1px solid #eee;"
                          v-for="(item, index) in conf.active.day_by_day" :key="index">
                        <view>
                            <view class="flex-row">
                                <view class="color-content text-right"
                                      style="width: 90px; line-height: 42px;">
                                    时间：
                                </view>
                                <uni-datetime-picker
                                    type="date"
                                    :value="item.date"
                                    :clear-icon="false"
                                    return-type="string"
                                    @change="conf.active.day_by_day[index].date = $event"
                                />
                            </view>
                            <view class="flex-row pt5">
                                <view class="color-content text-right"
                                      style="width: 90px; line-height: 36px;">
                                    人数限制：
                                </view>
                                <uni-easyinput v-model="item.max_people" type="number"
                                               placeholder="不填或填0就不能报名"/>
                            </view>
                        </view>
                        <view class="flex-all-center pr10">
                            <view class="font14 color-red" @click="conf.active.day_by_day.splice(index, 1)">
                                删除
                            </view>
                        </view>
                    </view>

                    <view class="flex-row">
                        <view class="p10 color-primary font14" @click="dayByDayPush">添加时间</view>
                    </view>
                </view>
            </template>
        </view>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view class="login-btn color-white text-center font18 bg-primary" :disabled="loading"
                  @click="save">
                {{ form_data.active_id ? '保存' : '创建活动' }}
            </view>
        </view>


    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import base64 from '@/utils/base64.js'
import {pinyin} from 'pinyin-pro'

import clockInLotterySet from '../../components/clock-in-lottery-set.vue'

export default {
    components: {clockInLotterySet},
    data() {
        return {
            loading: true,
            success: false,
            type_list: [
                {title: '基本信息', id: 1},
                {title: '活动设置', id: 3},
                {title: '打卡设置', id: 5},
                {title: '扩展设置', id: 7},
                {title: '活动图片', id: 2}
            ],
            type_id: 1,
            form_data: {
                name: '',
                wechat_num: '',
                mobile: '',
                organizer: '',
                content: '',
                begin_time: new Date(utils.getDay(0, true, '/') + ' 00:00:00').getTime(),
                end_time: new Date(utils.getDay(30, true, '/') + ' 23:59:59').getTime(),
                logo: ''
            },
            conf: {
                active: {
                    news: {
                        news_id: '',
                        news_title: ''
                    },
                    enter_types: 1,
                    // join_type: 0,
                    password: '',
                    screen_pic: '',
                    top_rank_banner: [],
                    clock_in_on_site: {
                        open: 1,
                        distance: 3
                    },
                    point_show_type: 1,
                    memo_required: 2,
                    memo_min_words: '',
                    pic_list_required: 2,
                    video_list_required: 0,
                    scale: 10,
                    sign_times_type: 0,
                    daily_submit_num: 1,
                    records_not_edit: 0,
                    point_hide: 0,
                    not_check: 0,
                    step_required: 0,
                    min_step: '',
                    address_required: 0,
                    day_by_day: [
                        {date: utils.getDay(0, true), max_people: ''}
                    ],
                    sign_record_top_type: 1,  // 1: 日历显示  2: 显示连续天数
                    close_feedback: 0,
                    sequential_clock_in: 0,
                    integral: {
                        unit: '金币',
                        finished_all_point_reward: '',
                        per_point_reward: '',
                        exchange_step: '',
                        exam_reward_num: 1,
                        exam_reward: ''
                    },
                    must_exam: {
                        open: 0,
                        score: ''
                    },
                    share_title: '',
                    share_image: '',
                    qrcode_logo: '',
                    activity_rules_hide: 0,
                    submit: {
                        begin: '',
                        end: ''
                    },
                    closed_likes: 0,
                    like_limit: 0,
                    similarity_percent: '',

                    exchange_start_time: '',
                    exchange_end_time: '',
                    point_complete_badge: '', // 点位已打卡徽章，不设置使用系统默认icon徽章
                    sign_set: {
                        rules: []
                    },
                    memo_placeholder: '',
                    team_required: 0,
                    must_team_person_finish_clock_in: 0,

                    // 邀请新用户参与活动奖励设置
                    share_friends: {
                        integral: ''
                    },

                    daily_sign: {
                        types: 0,
                        integral: '',
                        circle_set: []
                    },

                    min_num: '',
                    max_num: '',

                    // 实名信息签名背景图数据
                    must_submit_signature: {
                        bg: '',
                        x: '',
                        y: '',
                        width: '',
                        height: '',
                        color: '#000000',
                        size: 16
                    },

                    gift_goods_hide: 0,

                    detail_icon_list: [],

                    record_used_time: 0,

                    just_nfc_sign: 0,
                    auto_sign_nfc: 0,
                    submit_sign: {
                        period_minutes: ''
                    },
                    just_qrcode_sign: 0
                },
                must_submit: JSON.parse(JSON.stringify(this.xwy_config.defaultMustSubmit))
            },
            map_center_point: null,
            top_rank_banner_max_count: 6,
            form_options: {
                enter_types_list: [
                    {title: '自由报名参与活动', value: 1},
                    {title: '需要输入密码才能进入活动', value: 2},
                    {title: '报名需要审核通过才能参与活动', value: 3}
                ],
                clock_in_types_list: [
                    {value: 1, title: '是'},
                    {value: 0, title: '否'}
                ],
                point_hide_list: [
                    {value: 0, title: '需要打卡点'},
                    {value: 1, title: '不需要打卡点'}
                ],
                point_show_type_list: [
                    {value: 1, title: '地图展示'},
                    {value: 2, title: '列表展示'}
                ],
                memo_required_list: [
                    {value: 0, title: '关闭打卡填写备注'},
                    {value: 2, title: '开启打卡填写备注'},
                    {value: 1, title: '打卡必须填写备注'}
                ],
                pic_list_required_list: [
                    {value: 0, title: '关闭打卡上传图片'},
                    {value: 2, title: '开启打卡上传图片'},
                    {value: 1, title: '打卡必须上传图片'}
                ],
                video_list_required_list: [
                    {value: 0, title: '关闭打卡上传视频'},
                    {value: 2, title: '开启打卡上传视频'},
                    {value: 1, title: '打卡必须上传视频'}
                ],
                step_required_list: [
                    {value: 0, title: '关闭打卡上传微信运动步数'},
                    {value: 1, title: '开启打卡上传微信运动步数'}
                ],
                address_required_list: [
                    {value: 0, title: '关闭打卡上传当前地址'},
                    {value: 2, title: '开启打卡上传当前地址'},
                    {value: 1, title: '打卡必须上传当前地址'}
                ],
                sign_times_list: [
                    {value: 0, title: '不限制打卡次数'},
                    {value: 1, title: '每个打卡点只能打卡一次'},
                    {value: 2, title: '限制每个打卡点每天打卡次数'}
                ],
                not_point_sign_times_list: [
                    {value: 0, title: '不限制打卡次数'},
                    {value: 1, title: '只能打卡一次'},
                    {value: 2, title: '限制每天打卡次数'}
                ],
                records_not_edit_list: [
                    {value: 0, title: '是'},
                    {value: 1, title: '否'}
                ],
                not_check_list: [
                    {value: 0, title: '需要审核'},
                    {value: 1, title: '无需审核'}
                ],
                sign_record_top_type: [
                    {value: 1, title: '打卡日历显示'},
                    {value: 2, title: '打卡天数显示'}
                ],
                close_feedback_opt: [
                    {value: 0, title: '开启意见反馈'},
                    {value: 1, title: '关闭意见反馈'}
                ],
                sequential_clock_in: [
                    {value: 0, title: '关闭'},
                    {value: 1, title: '开启'}
                ],
                must_exam_open: [
                    {value: 0, title: '不限制'},
                    {value: 1, title: '需要答题才能打卡下一打卡点'},
                    {value: 2, title: '需要答题才能打卡当前打卡点'},
                ],
                activity_rules_hide_opt: [
                    {value: 0, title: '活动页面显示活动规则'},
                    {value: 1, title: '活动页面不显示活动规则'}
                ],

                mustSubmitTypes: [
                    {types: 1, title: '文本'},
                    {types: 2, title: '单选'},
                    {types: 3, title: '手机号'}
                ]
            },
            rank_set: {},
            pic_list: [],
            have_password: false,
            showRecordListTopSet: false,
            default_integral_unit: '金币',
            detail_icon_conf: [{type: 0, title: '文章详情'}]
        }
    },
    watch: {
        'conf.active.point_hide': function () {
            this.recordListTopShowHandle()
        },
        'conf.active.sign_times_type': function () {
            this.recordListTopShowHandle()
        },
        'form_data.begin_time': function () {
            this.recordListTopShowHandle()
        },
        'form_data.end_time': function () {
            this.recordListTopShowHandle()
        },
        'conf.active.submit.begin': function () {
            let {begin, end} = this.conf.active.submit
            if (begin && begin.length < 19) {
                begin += '00:00:00'
                this.conf.active.submit.begin = begin
            }
            if (end) {
                const begin_time = new Date(begin.replace(/-/g, '/')).getTime()
                const end_time = new Date(end.replace(/-/g, '/')).getTime()
                if (begin_time > end_time) {
                    this.$uni.showToast('报名开始时间不能大于报名截止时间')
                }
            }
        },
        'conf.active.submit.end': function () {
            let {begin, end} = this.conf.active.submit
            if (end && end.length < 19) {
                end += '23:59:59'
                this.conf.active.submit.end = end
            }
            if (begin) {
                const begin_time = new Date(begin.replace(/-/g, '/')).getTime()
                const end_time = new Date(end.replace(/-/g, '/')).getTime()
                if (end_time < begin_time) {
                    this.$uni.showToast('报名截止时间不能小于报名开始时间')
                }
            }
        }
    },

    computed: {
        point_complete_badge_set_show() {
            const {rank_set, conf: {active: {point_show_type, point_hide}}} = this
            return !!(rank_set['sign_logo_diy'] && point_show_type > 0 && !point_hide)
        },

        integralUnit() {
            return this.conf?.active?.integral.unit || this.default_integral_unit
        },

        signatureOpen() {
            if (!this.rank_set?.['attend_sign_pic']) return false
            return !!this.conf.must_submit.find(item => item.types === 4)
        }
    },

    onLoad(e) {
        if (e.id) {
            this.form_data.active_id = e.id
        } else {
            this.types = Number(e.types)
            this.$nextTick(() => this.$refs.add_activity_tips.open(this.types))
            this.form_data.logo = this.xwy_config.create_active_default_logo
        }
        this.$uni.setNavigationBarTitle(e.id ? '修改活动' : `创建${e.name || '活动'}`)
        this.$uni.showLoading()


        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }


            if (e.id) return this.getDetail()

            const evn_version = app.globalData['evn_version']

            if (evn_version === 'develop') {
                this.form_data.mobile = 12345678910
                this.form_data.wechat_num = 12345678910
                this.form_data.organizer = '伟杰测试'
            }

            // 开发版和体验版不检查重复创建活动
            if (evn_version === 'develop' || evn_version === 'trial') return uni.hideLoading()

            this.checkUserCanCreateActive()

        })
    },
    methods: {
        dayByDayPush() {
            this.conf.active.day_by_day.push({date: utils.getDay(), max_people: ''})
        },


        async checkUserCanCreateActive() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/check_user_can_create_active'
            })
            uni.hideLoading()
            this.loading = false

            if (res?.status !== 1) {
                await this.$uni.showModal(res.info || '暂时不能创建活动', {success: () => uni.navigateBack()})
            }
        },

        enter_typesChange(e) {
            // 把白名单导入功能设置和活动参与方式设置融合在一起，但是两个字段还是单独保存的，当用户选择使用白名单功能时enter_types=1，白名单功能开启join_type=1
            this.conf.active.enter_types = this.form_options.enter_types_list[e.detail.value].value
        },

        getDetail() {
            xwy_api.getActivityDetail(this.form_data.active_id, res => {
                if (res.data?.active_details) {
                    const detail = res.data.active_details
                    this.detailInit(detail)
                    this.recordListTopShowHandle(detail)
                } else {
                    uni.hideLoading()
                    this.$uni.showModal(res?.info || '活动获取失败', {success: () => uni.navigateBack()})
                }
            })
        },


        detailInit(data) {
            this.types = data.types
            this.form_data.name = data.name
            this.form_data.begin_time = data.begin_time * 1000
            this.form_data.end_time = data.end_time * 1000
            if (data.organizer) this.form_data.organizer = data.organizer
            if (data.logo) this.form_data.logo = data.logo
            if (data.content) this.form_data.content = data.content

            this.confDataInit(data.conf)

            if (data.rank_set) {
                const rank_set = data.rank_set

                this.rank_set = rank_set

                if (rank_set['scan_need_books']) this.type_list.splice(-1, 0, {title: '预报名设置', id: 6})

                if (rank_set['batch_import']) {
                    this.form_options.enter_types_list.push({
                        value: 4,
                        title: '只能使用管理员提前导入的名单报名'
                    })
                }

                if (rank_set['attend_sign_pic']) {
                    this.form_options.mustSubmitTypes.push({types: 4, title: '签名'})
                }

                if (rank_set.closed_AD) this.detail_icon_conf.push({type: 1, title: '文章列表'})
            }

            this.recordListTopShowHandle()

            uni.hideLoading()
        },

        confDataInit(conf) {
            this.conf.must_submit = conf.must_submit

            const active = conf.active
            this.conf.active.news = active.news
            this.conf.active.enter_types = active.enter_types
            if (active.password) {
                this.old_password = active.password
                this.have_password = true
            }
            this.conf.active.screen_pic = active.screen_pic
            this.conf.active.top_rank_banner = active.top_rank_banner
            this.conf.active.clock_in_on_site = active.clock_in_on_site
            this.conf.active.point_show_type = active.point_show_type
            this.conf.active.memo_required = active.memo_required
            this.conf.active.memo_min_words = active.memo_min_words
            this.conf.active.pic_list_required = active.pic_list_required
            this.conf.active.video_list_required = active.video_list_required
            if (active.scale) this.conf.active.scale = active.scale
            if (active.sign_times_type) this.conf.active.sign_times_type = active.sign_times_type
            if (active.daily_submit_num) this.conf.active.daily_submit_num = active.daily_submit_num
            if (active.map_center_point && active.map_center_point.coordinates && active.map_center_point.coordinates.length) {
                this.map_center_point = {
                    latitude: active.map_center_point.coordinates[1],
                    longitude: active.map_center_point.coordinates[0]
                }
            }
            if (active.records_not_edit) this.conf.active.records_not_edit = active.records_not_edit
            if (active.point_hide) this.conf.active.point_hide = active.point_hide
            if (active.not_check) this.conf.active.not_check = active.not_check
            if (active.address_required) this.conf.active.address_required = active.address_required
            if (active.sign_record_top_type) this.conf.active.sign_record_top_type = active.sign_record_top_type
            if (active.step_required) this.conf.active.step_required = active.step_required
            if (active.min_step) this.conf.active.min_step = active.min_step
            if (active.day_by_day) this.conf.active.day_by_day = active.day_by_day
            if (active.close_feedback) this.conf.active.close_feedback = active.close_feedback
            if (active.activity_rules_hide) this.conf.active.activity_rules_hide = active.activity_rules_hide
            if (active.sequential_clock_in) this.conf.active.sequential_clock_in = active.sequential_clock_in
            // 把白名单导入功能设置和活动参与方式设置融合在一起，但是两个字段还是单独保存的，当用户选择使用白名单功能时join_type=1，活动参与方式enter_types显示为4
            if (active.join_type) this.conf.active.enter_types = 4

            if (active.integral) {
                active.integralunit ||= '金币',
                active.finished_all_point_reward ||= ''
                active.per_point_reward ||= ''
                active.exchange_step ||= ''

                this.conf.active.integral = active.integral
            }

            if (active.must_exam) this.conf.active.must_exam = active.must_exam

            if (active.share_title) this.conf.active.share_title = active.share_title
            if (active.share_image) this.conf.active.share_image = active.share_image
            if (active.qrcode_logo) this.conf.active.qrcode_logo = active.qrcode_logo

            if (active.submit) {
                if (active.submit.begin) this.conf.active.submit.begin = active.submit.begin
                if (active.submit.end) this.conf.active.submit.end = active.submit.end
            }

            if (active.closed_likes) this.conf.active.closed_likes = active.closed_likes
            if (active.like_limit) this.conf.active.like_limit = active.like_limit
            if (active.similarity_percent) this.conf.active.similarity_percent = active.similarity_percent

            if (active.exchange_start_time) this.conf.active.exchange_start_time = active.exchange_start_time
            if (active.exchange_end_time) this.conf.active.exchange_end_time = active.exchange_end_time
            if (active.point_complete_badge) this.conf.active.point_complete_badge = active.point_complete_badge
            if (active.sign_set) this.conf.active.sign_set = active.sign_set
            if (active.memo_placeholder) this.conf.active.memo_placeholder = active.memo_placeholder
            if (active.team_required) this.conf.active.team_required = active.team_required
            if (active.must_team_person_finish_clock_in) this.conf.active.must_team_person_finish_clock_in = active.must_team_person_finish_clock_in
            if (active.share_friends) this.conf.active.share_friends = active.share_friends
            if (active.daily_sign) this.conf.active.daily_sign = active.daily_sign
            if (active.min_num) this.conf.active.min_num = active.min_num
            if (active.max_num) this.conf.active.max_num = active.max_num
            if (active.must_submit_signature) this.conf.active.must_submit_signature = active.must_submit_signature
            if (active.gift_goods_hide) this.conf.active.gift_goods_hide = active.gift_goods_hide
            if (active.detail_icon_list) this.conf.active.detail_icon_list = active.detail_icon_list
            if (active.record_used_time) this.conf.active.record_used_time = active.record_used_time
            if (active.auto_sign_nfc) this.conf.active.auto_sign_nfc = active.auto_sign_nfc
            if (active.just_nfc_sign) this.conf.active.just_nfc_sign = active.just_nfc_sign
            if (active.submit_sign) this.conf.active.submit_sign = active.submit_sign
            if (active.just_qrcode_sign) this.conf.active.just_qrcode_sign = active.just_qrcode_sign
        },

        // 判断是否需要显示 设置打卡日历顶部显示的方式
        // 需要点位, 每个点位只能打一次卡  不需要显示
        // 活动周期小于等于7天的不需要显示
        recordListTopShowHandle() {
            const {point_hide, sign_times_type} = this.conf.active
            // 需要点位, 每个点位只能打一次卡  不需要显示
            if (!point_hide && sign_times_type === 1) {
                this.showRecordListTopSet = false
                return
            }

            const {begin_time, end_time} = this.form_data
            if (!begin_time || !end_time) {
                this.showRecordListTopSet = false
                return
            }
            const activity_cycle = utils.dateDiff(begin_time, end_time)
            this.showRecordListTopSet = activity_cycle > 7
        },


        videoListRequiredChange(e) {
            const value = this.form_options.video_list_required_list[e.detail.value].value
            if (!this.rank_set['upload_video'] && value !== 0) {
                return this.$uni.showModal('未开通视频上传功能，请联系客服开通')
            }
            this.conf.active.video_list_required = value
        },

        mustExamOpenChange(e) {
            const value = Number(e.detail.value)
            if (value === 1 && !this.conf.active.sequential_clock_in) {
                return this.$uni.showModal('需要开启[按打卡点顺序打开]才能开启需要[答题才能打卡下一打卡点]')
            }
            this.conf.active.must_exam.open = value
        },


        addDetailIcon() {
            this.conf.active.detail_icon_list.push({
                icon: 'icon-dating',
                title: '',
                type: 0,
                id: '',
                name: ''
            })
        },
        delDetailIcon(index) {
            this.conf.active.detail_icon_list.splice(index, 1)
        },
        selIcon(index) {
            uni.navigateTo({
                url: '/pages/other/icon-list/icon-list',
                events: {
                    selIcon: class_name => {
                        this.conf.active.detail_icon_list[index].icon = class_name
                    }
                }
            })
        },
        detailIconItemChange(e, index) {
            this.conf.active.detail_icon_list[index].id = ''
            this.conf.active.detail_icon_list[index].name = ''
            this.conf.active.detail_icon_list[index].type = this.detail_icon_conf[e.detail.value].type
        },

        bindNewsOrCategory(index) {
            const item = this.conf.active.detail_icon_list[index]
            if (item.type === 0) this.bindNews(index)
            if (item.type === 1) this.bindCategory(index)
        },
        bindNews(index) {
            uni.navigateTo({
                url: '/pages/news/list?type=user&is_sel=true',
                events: {
                    selNews: data => {
                        this.conf.active.detail_icon_list[index].id = data.id
                        this.conf.active.detail_icon_list[index].name = data.title
                    }
                }
            })
        },
        bindCategory(index) {
            uni.navigateTo({
                url: '/pages/category/list?types=8&is_sel=1',
                events: {
                    selCategory: data => {
                        this.conf.active.detail_icon_list[index].id = data.id
                        this.conf.active.detail_icon_list[index].name = data.name
                    }
                }
            })
        },



        toSelNews() {
            this.$uni.navigateTo('/pages/news/list?type=user&is_sel=true', {
                events: {
                    selNews: data => {
                        this.conf.active.news.news_id = data.id
                        this.conf.active.news.news_title = data.title
                    }
                }
            })
        },

        deleteNews() {
            this.conf.active.news.news_id = ''
            this.conf.active.news.news_title = ''
        },


        mustItemRulesChange(e, index) {
            this.conf.must_submit[index].rules = Number(e.detail.value)
        },
        mustItemTypesChange(e, index) {
            const types = this.form_options.mustSubmitTypes[e.detail.value].types

            if (types === 4 && this.signatureOpen) {
                return this.$uni.showToast('已添加过签名选项')
            }

            this.conf.must_submit[index].types = types
            if (types === 2) {
                this.conf.must_submit[index].options = this.conf.must_submit[index].options || [{text: ''}]
            }
        },
        addMust() {
            this.conf.must_submit.push({
                name: '',
                rules: 0,
                title: '',
                types: 1
            })
        },

        addOption(index) {
            const item = this.conf.must_submit[index]
            item.options.push({text: ''})
            this.$set(this.conf.must_submit, index, item)
        },
        deleteOptionsItem(index, index_) {
            const item = this.conf.must_submit[index]
            if (item.options.length === 1) return this.$uni.showToast('请至少保留一个选项')

            item.options.splice(index_, 1)
            this.$set(this.conf.must_submit, index, item)
        },
        mustSubmitOptionTextChange(e, index, index_) {
            const item = this.conf.must_submit[index]
            item.options[index_].text = e.detail.value
            this.$set(this.conf.must_submit, index, item)
        },

        changeImage(key) {
            if (!this.rank_set?.closed_AD) {
                const options = {
                    screen_pic: '无法设置开屏大图，请联系客服设置',
                    top_rank_banner: '无法设置排行榜轮播图，请联系客服设置'
                }
                if (options[key]) return this.$uni.showModal(options[key])
            }

            let url = '/pages/other/image_upload_or_select'
            if (this.form_data.active_id) url += `?active_id=${this.form_data.active_id}`

            uni.navigateTo({
                url,
                events: {
                    newImg: src => {
                        const function_list = {
                            logo: () => this.form_data.logo = src,
                            top_rank_banner: () => this.conf.active.top_rank_banner.push(src),
                            must_submit_signature_bg: () => this.conf.active.must_submit_signature.bg = src
                        }
                        const default_function = () => this.conf.active[key] = src
                        function_list[key] ? function_list[key]() : default_function()
                    }
                }
            })
        },

        previewImage(urls, current = urls[0]) {
            uni.previewImage({
                urls,
                current
            })
        },

        chooseLocation() {

            const obj = {
                success: res => {
                    this.map_center_point = {
                        latitude: res.latitude,
                        longitude: res.longitude
                    }
                },
                fail: err => {
                    if (err.errMsg === 'chooseLocation:fail:not supported') {
                        return this.$uni.showModal('不支持电脑端使用，请在手机微信打开')
                    }

                    if (err.errMsg === 'chooseLocation:fail auth deny') {
                        return this.$uni.showModal('请授权小程序获取你的位置信息', {
                            confirmText: '立即授权',
                            showCancel: true,
                            success: res => {
                                res.confirm && uni.openSetting()
                            }
                        })
                    }


                    if (err.errMsg !== 'chooseLocation:fail cancel') {
                        return this.$uni.showModal(JSON.stringify(err))
                    }
                }
            }
            if (this.map_center_point && this.map_center_point.latitude && this.map_center_point.longitude) {
                obj.latitude = Number(this.map_center_point.latitude)
                obj.longitude = Number(this.map_center_point.longitude)
            }
            uni.chooseLocation(obj)
        },


        dataCheck(data) {
            const showToast = title => {
                this.type_id = 1
                this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none')
            }

            if (!data.name) {
                showToast('请输入活动名称')
                return false
            }

            if (!data.active_id) {
                if (!data.mobile) {
                    showToast('请输入手机号')
                    return false
                }
                if (data.mobile.toString().length !== 11) {
                    showToast('手机号长度有误')
                    return false
                }
                if (!data.wechat_num) {
                    showToast('请输入微信号')
                    return false
                }
                const wechat_num = data.wechat_num.toString()
                if (wechat_num.length < 4 || wechat_num.length > 20) {
                    showToast('微信号长度有误')
                    return false
                }
                if (this._utils.isChineseChar(wechat_num)) {
                    showToast('微信号不能输入中文')
                    return false
                }
            }

            return true
        },

        setMustSubmitData() {
            const errModal = content => {
                this.type_id = 3
                this.$uni.showToast(content)
            }


            for (let i = 0; i < this.conf.must_submit.length; i++) {
                const v = this.conf.must_submit[i]
                if (!v.title) {
                    errModal('参与活动需要填写的信息选项填写不完整，请检查。')
                    return false
                }
                v.name = pinyin(v.title, {toneType: 'none', type: 'array'}).join('_')
                if (v.types === 2) {
                    if (!v.options || !v.options.length) {
                        errModal(`${v.title} 至少需要添加一个选项。`)
                        return false
                    }
                    console.log(v.options)
                    for (let j = 0; j < v.options.length; j++) {
                        const v1 = v.options[j]
                        if (!v1 || !v1.text) {
                            errModal(`${v.title} 有未填写的选项，请检查。`)
                            return false
                        }
                    }
                }
            }

            return true
        },

        dayByDayCheck() {
            const day_by_day = this.conf.active.day_by_day
            const new_list = []
            day_by_day.forEach(v => {
                new_list.push(v.date)
            })
            const new_set_list = [...new Set(new_list)]
            if (new_list.length !== new_set_list.length) {
                this.type_id = 6
                this.$uni.showToast('预报名设置存在重复日期，请检查', 'none', 3000)
                return false
            }

            return true
        },


        confCheck() {
            const showToast = (title, type_id) => {
                if (type_id) this.type_id = type_id
                this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none')
                return false
            }
            const conf = JSON.parse(JSON.stringify(this.conf))
            if (conf.active.enter_types === 2) {
                if (!this.have_password && !conf.active.password) {
                    return showToast('请输入活动密码', 3)
                }
                if (conf.active.password && conf.active.password.length < 3) {
                    return showToast('活动密码不得少于3位', 3)
                }
                if (conf.active.password && conf.active.password.length > 20) {
                    return showToast('活动密码不得大于20位', 3)
                }
                conf.active.password = conf.active.password || this.old_password
            } else {
                conf.active.password = 1
            }
            if (this.map_center_point && this.map_center_point.latitude && this.map_center_point.longitude) {
                conf.active.map_center_point = {
                    type: 'Point',
                    coordinates: [this.map_center_point.longitude, this.map_center_point.latitude]
                }

            }

            // 把白名单导入功能设置和活动参与方式设置融合在一起，但是两个字段还是单独保存的，当用户选择使用白名单功能时join_type=1，活动参与方式保存为自由报名enter_types=1
            if (conf.active.enter_types === 4) {
                conf.active.enter_types = 1
                conf.active.join_type = 1
            }

            const {begin, end} = conf.active.submit
            if (begin || end) {
                if (begin && end) {
                    const begin_time = new Date(begin.replace(/-/g, '/')).getTime()
                    const end_time = new Date(end.replace(/-/g, '/')).getTime()
                    if (begin_time > end_time) {
                        return showToast('报名开始时间不能大于报名结束时间', 3)
                    }
                }
            } else {
                delete conf.active.submit
            }

            const rank_set = this.rank_set || {}

            if (rank_set['pic_similar'] && conf.active.similarity_percent) {
                const percent = Math.floor(conf.active.similarity_percent)
                if (isNaN(percent) || percent < 0 || percent > 100) {
                    return showToast('相似度百分比填写有误', 5)
                }
                conf.active.similarity_percent = percent
            }

            if (!conf.active.exchange_start_time) delete conf.active.exchange_start_time
            if (!conf.active.exchange_end_time) delete conf.active.exchange_end_time

            if (!this.point_complete_badge_set_show) {
                delete conf.active.point_complete_badge
            }

            if (!conf.active.sign_set.rules.length || !rank_set['sign_num_lottery']) {
                delete conf.active.sign_set
            }

            if (!rank_set.team_group_open || !conf.active.team_required) delete conf.active.team_required

            if (!rank_set.team_group_open || !conf.active.sequential_clock_in || conf.active.point_hide || !conf.active.must_team_person_finish_clock_in) {
                delete conf.active.must_team_person_finish_clock_in
            }

            if (rank_set.share_friends) {
                const integral = Math.floor(conf.active.share_friends.integral)
                if (isNaN(integral) || integral < 0) {
                    return showToast('邀请新用户参与活动奖励设置有误', 7)
                }
                if (integral === 0) {
                    delete conf.active.share_friends
                } else {
                    conf.active.share_friends.integral = integral
                }
            } else {
                delete conf.active.share_friends
            }

            if (rank_set['morning_daily_sign']) {
                const {types, circle_set} = conf.active.daily_sign
                if (types !== 2 && circle_set.length) conf.active.daily_sign.circle_set = []
            } else {
                delete conf.active.daily_sign
            }

            if (rank_set['auto_exchange_press_button'] || rank_set['one_day_auto_exchange']) {
                if (conf.active.min_num) {
                    const min_num = Math.floor(conf.active.min_num)
                    if (isNaN(min_num) || min_num < 0) {
                        return showToast('最低步数限制设置有误', 7)
                    }
                    conf.active.min_num = min_num
                }
                if (conf.active.max_num) {
                    const max_num = Math.floor(conf.active.max_num)
                    if (isNaN(max_num) || max_num < 0) {
                        return showToast('最高步数限制设置有误', 7)
                    }
                    conf.active.max_num = max_num
                }
            }


            if (this.signatureOpen) {
                const {bg, x, y, width = 200, height = 100, size = 16} = conf.active.must_submit_signature
                if (!bg) {
                    return showToast('请设置签名背景图', 3)
                }
                conf.active.must_submit_signature.x = isNaN(Math.floor(x)) ? 0 : Math.floor(x)
                conf.active.must_submit_signature.y = isNaN(Math.floor(y)) ? 0 : Math.floor(y)
                conf.active.must_submit_signature.width = isNaN(Math.floor(width)) ? 200 : Math.floor(width)
                conf.active.must_submit_signature.height = isNaN(Math.floor(height)) ? 100 : Math.floor(height)
                conf.active.must_submit_signature.size = isNaN(Math.floor(size) || Math.floor(size) <= 0) ? 16 : Math.floor(size)
            } else {
                delete conf.active.must_submit_signature
            }

            if (!conf.active.gift_goods_hide) delete conf.active.gift_goods_hide
            if (!conf.active.detail_icon_list.length) delete conf.active.detail_icon_list

            if (!conf.active.sequential_clock_in) delete conf.active.sequential_clock_in

            if (conf.active.point_hide || conf.active.sign_times_type !== 1) {
                delete conf.active.record_used_time
            }

            if (!rank_set.openNFC || !conf.active.just_nfc_sign) delete conf.active.just_nfc_sign

            if (rank_set.openNFC && conf.active.auto_sign_nfc) {
                // nfc自动打卡，不需要填写打卡内容、打卡图片、打卡视频、打卡步数、打卡地址，不能修改打卡记录
                conf.active.memo_required = 0
                conf.active.pic_list_required = 0
                conf.active.video_list_required = 0
                conf.active.step_required = 0
                conf.active.address_required = 0
                conf.active.records_not_edit = 1
            } else {
                delete conf.active.auto_sign_nfc
            }

            if (rank_set.submitSignBetweenThirtyMinutes && conf.active.submit_sign.period_minutes) {
                const num = Number(conf.active.submit_sign.period_minutes)
                if (isNaN(num) || num < 0) {
                    return showToast('连续打卡有效时间设置不正确', 5)
                }
                conf.active.submit_sign.period_minutes = num
            } else {
                delete conf.active.submit_sign
            }

            // 打卡活动开启了健步走功能，需要设置兑换比例，不然自动兑换30天步数没有兑换比例兑换不了
            const {auto_exchange_press_button, one_day_auto_exchange, thirty_day_auto_exchange} = rank_set
            if (auto_exchange_press_button || one_day_auto_exchange || thirty_day_auto_exchange) {
                conf.active.exchange_rate = 1
                conf.active.kilo_unit = '步'
            }

            if (conf.active.integral) {
                if (conf.active.integral.finished_all_point_reward) {
                    const num = Number(conf.active.integral.finished_all_point_reward)
                    if (isNaN(num) || num < 0) {
                        return showToast('每日打卡完所有点位奖励设置不正确', 5)
                    }
                    conf.active.integral.finished_all_point_reward = num
                } else {
                    delete conf.active.integral.finished_all_point_reward
                }

                if (conf.active.integral.per_point_reward) {
                    const num = Number(conf.active.integral.per_point_reward)
                    if (isNaN(num) || num < 0) {
                        return showToast('每次打卡奖励设置不正确', 5)
                    }
                    conf.active.integral.per_point_reward = num
                } else {
                    delete conf.active.integral.per_point_reward
                }

                if (conf.active.integral.exchange_step) {
                    const num = Number(conf.active.integral.exchange_step)
                    if (isNaN(num) || num < 0) {
                        return showToast('步数奖励设置不正确', 5)
                    }
                } else {
                    delete conf.active.integral.exchange_step
                }

                if (conf.active.integral.exam_reward_num) {
                    const exam_reward_num = Number(conf.active.integral.exam_reward_num)
                    if (isNaN(exam_reward_num) || exam_reward_num < 0) {
                        return showToast('答题奖励设置不正确', 7)
                    }
                    conf.active.integral.exam_reward_num = exam_reward_num
                } else {
                    delete conf.active.integral.exam_reward_num
                }

                if (conf.active.integral.exam_reward) {
                    const exam_reward = Number(conf.active.integral.exam_reward)
                    if (isNaN(exam_reward) || exam_reward < 0) {
                        return showToast('答题奖励设置不正确', 7)
                    }
                    conf.active.integral.exam_reward = exam_reward
                } else {
                    delete conf.active.integral.exam_reward
                }
            }

            if (!conf.active.just_qrcode_sign) delete conf.active.just_qrcode_sign


            let conf_str = JSON.stringify(conf)
            conf_str = conf_str.replace(/·/g, '-')
            return base64['encode'](conf_str)
        },

        save() {
            if (this.success) return false

            const data = JSON.parse(JSON.stringify(this.form_data))


            if (!this.dataCheck(data)) return false
            if (!this.setMustSubmitData()) return false
            if (!this.dayByDayCheck()) return false

            const conf = this.confCheck()
            if (!conf) return false
            data.conf = conf


            data.pic_list = base64['encode'](JSON.stringify(this.pic_list))

            data.begin_time /= 1000
            data.end_time /= 1000

            this.saveAjax(data)
        },

        async saveAjax(data) {
            data.types = this.types

            this.loading = true
            uni.showLoading({
                title: '保存中...',
                mask: app.globalData['evn_version'] !== 'trial'
            })

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.admin/create_active',
                data
            })

            uni.hideLoading()
            this.loading = false

            if (!res || !res.status) {
                xwy_api.alert(res && res.info || '保存失败')
                return false
            }

            this.updatePageData()

            if (data.active_id) {
                this.$uni.showToast('保存成功')
                return this.$uni.navigateBack(1, {delay: 1000})
            }

            this.success = true
        },

        // 活动修改/创建成功，如果页面栈有详情或列表页面，更新页面数据
        updatePageData() {
            const pages = getCurrentPages()

            const update_page_list = [
                {
                    route: 'pages/clock_in/user/detail',
                    _function: page => {
                        page.$vm.getDetail(true)
                    }
                },
                {
                    route: 'pages/clock_in/admin/activity/manage',
                    _function: page => {
                        page.$vm.getDetail()
                    }
                },
                {
                    route: 'pages/activity/user/index',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getActivityList()
                    }
                },
                {
                    route: 'pages/activity/user/activity_list',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getList()
                    }
                }
            ]

            update_page_list.forEach(v => {
                const page = pages.find(n => n.route === v.route)
                if (page) v._function(page)
            })
        },

        lookMyActivityList() {
            const list_page_route = 'pages/activity/user/activity_list'
            const pages = getCurrentPages()
            const list_page_index = pages.findIndex(v => {
                return v.route === list_page_route && v.options.type && v.options.type === 'create'
            })
            if (list_page_index === -1) return this.$uni.redirectTo(`/${list_page_route}?type=create`)

            this.$uni.navigateBack(pages.length - list_page_index - 1)
        }
    }
}
</script>

<style>
.page {
    padding-top: 40px;
    padding-bottom: 75px;
}

.type-list {
    position: fixed;
    z-index: 9;
    width: 100vw;
    top: 0;
    left: 0;
}

.type-item {
    height: 40px;
    line-height: 40px;
    text-align: center;
    box-sizing: border-box;
}

.active-type {
    color: #007AFF;
    border-bottom: 2px solid #007AFF;
}

.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}


.form-item .textarea {
    width: 100%;
    line-height: 16px;
    padding: 10px 0;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}

.must-submit-list {
    border-bottom: 1px dashed #eee;
}

.must-rules-picker {
    line-height: 34px;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    margin-left: 5px;
    padding: 0 15px;
}

.delete-rules {
    line-height: 36px;
    padding: 0 10px;
}

.must-options-item {
    padding: 3px 0;
}


.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
    line-height: calc((100vw - 50px) / 3);
    margin: 5px;
}

.image-item {
    width: calc(100vw - 20px);
    height: 50vw;
    border-radius: 5px;
}


.image-view {
    position: relative;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}


.top-rank-banner-item {
    padding: 5px;
    position: relative;
}

.top-rank-banner-item image {
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
}

.top-rank-banner-item .del-image-item {
    right: 8px;
}

.bottom-btn-view {
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.login-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}

.save-success {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 999999;
    box-sizing: border-box;
    padding-top: 10vh;
}

.save-success .icon {
    padding-bottom: 50px;
}

.uni-date-x {
    padding: 0 !important;
}

.uni-date__x-input {
    font-size: 16px !important;
    color: #000;
}

/* #ifdef H5 */

@media screen and (min-width: 500px) {
    .type-list {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }


    .image-item {
        width: 480px;
        height: 200px;
    }

    .top-rank-banner-item image, .add-image {
        width: 150px;
        height: 150px;
        line-height: 150px;
    }
}

/* #endif */
</style>
