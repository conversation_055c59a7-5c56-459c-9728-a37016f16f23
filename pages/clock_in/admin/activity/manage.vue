<template>
    <view class="page bg-background">
        <activity-delete-tips v-if="error" :activeId="id"/>

        <view v-if="detail && detail.active_id">
            <!-- #ifndef H5 -->
            <image v-if="detail.logo" class="logo" mode="widthFix" :src="detail.logo"/>
            <!-- #endif -->
            <view class="bg-white p10">
                <view class="color-title font18" @click="copy(id)">{{ detail.name }}</view>
                <view v-if="detail.organizer" class="color-content font16">主办方：{{ detail.organizer }}</view>
            </view>

            <template v-if="!detail.rank_set || !detail.rank_set.closed_AD">
                <xwy-ad v-if="!popup_show" :ad_type="4"></xwy-ad>
                <xwy-ad :ad_type="3"></xwy-ad>
            </template>


            <view class="tools bg-white">
                <view class="title flex-kai">
                    <view class="color-title">活动管理</view>
                    <navigator v-if="document_show" url="/pages/other/activity-document?types=5">
                        <text class="iconfont icon-word-file color-light-primary font14"></text>
                        <text class="font14 color-light-primary" style="padding-left: 2px;">活动帮助文档</text>
                    </navigator>
                </view>
                <view class="list flex-row flex-wrap text-center">
                    <view class="item" hover-class="navigator-hover" @click="toEdit">
                        <text class="iconfont icon-edit color-primary font34"></text>
                        <view class="font14 color-content">活动修改</view>
                    </view>


                    <navigator :url="'/pages/activity/admin/activity_user_list?id=' + id" class="item">
                        <text class="iconfont icon-users color-primary font34"></text>
                        <view class="font14 color-content">活动用户</view>
                    </navigator>


                    <navigator :url="'/pages/activity/admin/blacklist?id=' + id" class="item">
                        <text class="iconfont icon-blacklist color-primary font34"></text>
                        <view class="font14 color-content">黑名单用户</view>
                    </navigator>


                    <navigator v-if="detail.rank_set && detail.rank_set.team_group_open" class="item"
                               :url="'/pages/activity/admin/team_list?is_admin=1&id=' + id + '&active_types=' + detail.types">
                        <text class="iconfont icon-team color-primary font34"></text>
                        <view class="font14 color-content">队伍管理</view>
                    </navigator>


                    <navigator
                        v-if="detail.rank_set && detail.rank_set['scan_need_books']"
                        :url="'./books_date_list?id=' + id"
                        class="item"
                    >
                        <text class="iconfont color-primary font34" :class="'icon-calendar-' + this_day"></text>
                        <view class="font14 color-content">报名时间</view>
                    </navigator>


                    <navigator
                        :url="'/pages/clock_in/user/public_sign_list?is_admin=1&id=' + id + '&type=' + detail.types"
                        class="item"
                    >
                        <text class="iconfont icon-sign-in-calendar color-primary font34"></text>
                        <view class="font14 color-content">
                            {{ detail.types === 21 ? '集' : '打' }}卡审核
                        </view>
                    </navigator>


                    <navigator
                        v-if="!detail.conf.active.close_feedback"
                        :url="'/pages/activity/feedback/list?id=' + id"
                        class="item"
                    >
                        <text class="iconfont icon-chat-bubble color-primary font34"></text>
                        <view class="font14 color-content">用户反馈</view>
                    </navigator>


                    <view class="item" hover-class="navigator-hover" @click="toPointList">
                        <template>
                            <uni-icons
                                v-if="detail.types === 21"
                                type="images"
                                size="34"
                                color="#2d8cf0"
                            />
                            <text v-else class="iconfont icon-map color-primary font34"></text>
                        </template>
                        <view class="font14 color-content">
                            {{ detail.types === 21 ? '卡片' : '点位' }}管理
                        </view>
                    </view>


                    <navigator v-if="detail.rank_set && detail.rank_set['mapPointCategorySet']" class="item"
                               :url="`/pages/category/list?active_id=${id}&just_look_active=1&types=38`">
                        <text class="iconfont icon-grid color-primary font34"></text>
                        <view class="font14 color-content">
                            {{ detail.types === 21 ? '卡片' : '点位' }}类型
                        </view>
                    </navigator>

                    <navigator
                        v-if="detail.rank_set && detail.rank_set['medal_open']"
                        url="/pages/activity/admin/medal/list"
                        class="item"
                    >
                        <text class="iconfont icon-medal color-primary font34"></text>
                        <view class="font14 color-content">勋章管理</view>
                    </navigator>


                    <navigator
                        class="item"
                        :url="'/pages/news/list?type=user&vip=' + (detail.rank_set && detail.rank_set.closed_AD || 0)"
                    >
                        <text class="iconfont icon-writing font34 color-primary"/>
                        <view class="font14 color-content">文章管理</view>
                    </navigator>
                    <navigator
                        v-if="detail.rank_set && detail.rank_set.closed_AD"
                        class="item"
                        url="/pages/category/list?types=8"
                    >
                        <text class="iconfont icon-dating font34 color-primary"/>
                        <view class="font14 color-content">文章分类</view>
                    </navigator>
                    <view class="item" @click="copyActivityPages">
                        <text class="iconfont icon-copy color-primary font34"></text>
                        <view class="font14 color-content">复制路径</view>
                    </view>

                    <!-- #ifndef H5 -->
                    <view class="item" @click="showWebUrl">
                        <text class="iconfont icon-screen color-primary font34"></text>
                        <view class="font14 color-content">web端管理</view>
                    </view>
                    <!-- #endif -->

                    <navigator class="item" url="/pages/other/contact">
                        <uni-icons type="chatboxes" size="34" color="#2d8cf0"/>
                        <view class="font14 color-content">联系客服</view>
                    </navigator>

                    

                    <view class="item" @click="showActiveSharePopup">
                        <text class="iconfont icon-share color-primary font34"></text>
                        <view class="font14 color-content">转发分享</view>
                    </view>
                </view>
            </view>

            <view v-if="detail.rank_set && detail.rank_set['batch_import']" class="tools bg-white">
                <view class="title color-title">活动用户导入</view>
                <view class="list flex-row flex-wrap text-center">
                    <view hover-class="navigator-hover" class="item" @click="importUsers">
                        <text class="iconfont icon-import color-primary font34"></text>
                        <view class="font14 color-content">批量导入</view>
                    </view>


                    <navigator class="item" :url="'/pages/activity/admin/import_users/user_list?id=' + id">
                        <text class="iconfont icon-users color-primary font34"></text>
                        <view class="font14 color-content">用户名单</view>
                    </navigator>

                    <view hover-class="navigator-hover" class="item" @click="downloadImportUsersTemplate">
                        <text class="iconfont icon-export-excel color-primary font34"></text>
                        <view class="font14 color-content">模版下载</view>
                    </view>
                </view>

            </view>


            <view class="tools bg-white">
                <view class="title color-title">数据导出</view>
                <view class="list flex-row flex-wrap text-center">
                    <view class="item" @click="exportRankingData(11)">
                        <text class="iconfont icon-export-excel color-primary font34"></text>
                        <view class="font14 color-content">导出活动数据</view>
                    </view>

                    <view class="item" hover-class="navigator-hover" @click="exportRankingData(21)">
                        <text class="iconfont icon-users color-primary font34"></text>
                        <view class="font14 color-content">活动用户</view>
                    </view>

                    <view class="item" hover-class="navigator-hover" @click="exportClockInRecord">
                        <text class="iconfont icon-sign-in-calendar color-primary font34"></text>
                        <view class="font14 color-content">打卡记录</view>
                    </view>
                    
                    <view v-if="detail.rank_set && detail.rank_set['sum_exam_score']" class="item"
                          hover-class="navigator-hover" @click="exportRankingData(7)">
                        <text class="iconfont icon-examination-paper color-primary font34"></text>
                        <view class="font14 color-content">答题排行榜</view>
                    </view>

                    <view v-if="detail.rank_set && detail.rank_set['gift_goods']" class="item"
                          hover-class="navigator-hover" @click="exportRankingData(48)">
                        <text class="iconfont icon-trophy color-primary font34"></text>
                        <view class="font14 color-content">{{ integralUnit }}排行榜</view>
                    </view>

                    <view v-if="rankingJsonConfig['types_49']" class="item"
                          @click="exportMonthIntegralRanking">
                        <text class="iconfont icon-integral color-primary font34"></text>
                        <view class="font14 color-content">月{{ integralUnit }}排行榜</view>
                    </view>

                    <!--suppress JSValidateTypes-->
                    <navigator v-if="detail.rank_set && (detail.rank_set['auto_exchange_press_button'] || detail.rank_set['one_day_auto_exchange'])"
                        class="item"
                        :url="'/pages/activity/admin/export_total_step?id=' + id + '&name=' + detail.name + '&begin_time=' + detail.begin_time + '&end_time=' + detail.end_time"
                    >
                        <text class="iconfont icon-data-summary color-primary font34"></text>
                        <view class="font14 color-content">步数汇总数据</view>
                    </navigator>

                    <navigator
                        class="item"
                        url="/pages/activity/admin/export_record"
                    >
                        <text class="iconfont icon-writing color-primary font34"></text>
                        <view class="font14 color-content">导出记录</view>
                    </navigator>
                </view>

            </view>

            <view v-if="exam_open" class="tools bg-white">
                <view class="title color-title">答题管理</view>
                <view class="list flex-row flex-wrap text-center">
                    <navigator class="item" url="/pages/likou_dati/pages/exam/exam_list/exam_list">
                        <text class="iconfont icon-examination-paper color-primary font34"></text>
                        <view class="font14 color-content">我的考卷</view>
                    </navigator>
                    <navigator
                        class="item"
                        url="/pages/likou_dati/pages/question/question_bank_list/question_bank_list"
                    >
                        <text class="iconfont icon-dictionary color-primary font34"></text>
                        <view class="font14 color-content">我的题库</view>
                    </navigator>
                    <navigator
                        class="item"
                        url="/pages/likou_dati/pages/question/category_list/category_list"
                    >
                        <text class="iconfont icon-dating color-primary font34"></text>
                        <view class="font14 color-content">题库分类</view>
                    </navigator>
                    <navigator
                        class="item"
                        url="/pages/likou_dati/pages/question/batch_import_question/explain/explain"
                    >
                        <text class="iconfont icon-import color-primary font34"></text>
                        <view class="font14 color-content">批量导题</view>
                    </navigator>
                </view>

            </view>


            <view v-if="detail.rank_set && detail.rank_set['lottery_open']" class="tools bg-white">
                <view class="title color-title">抽奖活动管理</view>
                <view class="list flex-row flex-wrap text-center">
                    <navigator class="item" url="/pages/lottery/admin/lottery/list">
                        <text class="iconfont icon-dating font34 color-primary"/>
                        <view class="font14 color-content">活动列表</view>
                    </navigator>


                    <navigator
                        class="item"
                        :url="'/pages/lottery/user/lottery_record_list?active_id=' + id + '&is_admin=1'"
                    >
                        <text class="iconfont icon-gift font34 color-primary"/>
                        <view class="font14 color-content">抽奖记录</view>
                    </navigator>
                    <view class="item" hover-class="navigator-hover" @click="exportLotteryRecord">
                        <text class="iconfont icon-export-excel font34 color-primary"></text>
                        <view class="font14 color-content">导出抽奖记录</view>
                    </view>
                </view>
            </view>

            <view v-if="detail.rank_set && detail.rank_set['gift_goods']" class="tools bg-white">
                <view class="title color-title">商城管理</view>
                <view class="list flex-row flex-wrap text-center">
                    <view class="item" @click="toShopManage">
                        <uni-icons type="shop" size="34" color="#2d8cf0"/>
                        <view class="font14 color-content">商品管理</view>
                    </view>


                    <navigator
                        class="item"
                        :url="'/pages/shop/order/order_list?is_admin=1&active_id=' + id + '&unit=' + integralUnit"
                    >
                        <uni-icons type="list" size="34" color="#2d8cf0"/>
                        <view class="font14 color-content">订单记录</view>
                    </navigator>
                </view>
            </view>


        </view>
        
        <active-share ref="activeShare"/>

        <uni-popup ref="not_export_ranking_tips" type="center">
            <view class="export_ranking_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('not_export_ranking_tips')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top" style="padding: 50px 20px 20px;">
                    <text class="iconfont icon-export-excel color-green" style="font-size: 80px;"></text>
                    <view class="font14 color-sub">未开通导出数据功能</view>
                    <view class="font14 color-sub">请联系客服开通</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <button
                        open-type="contact"
                        class="export_ranking-btn bg-green color-white font16"
                        @click="uniPopupClose('not_export_ranking_tips')"
                    >联系客服开通
                    </button>
                </view>
            </view>
        </uni-popup>

        <uni-popup ref="not_export_exam_tips" type="center">
            <view class="export_ranking_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('not_export_exam_tips')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top" style="padding: 50px 20px 20px;">
                    <text class="iconfont icon-export-excel color-green" style="font-size: 80px;"></text>
                    <view class="font14 color-sub">未开通导出答题排行榜数据功能</view>
                    <view class="font14 color-sub">请联系客服开通</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <button
                        open-type="contact"
                        class="export_ranking-btn bg-green color-white font16"
                        @click="uniPopupClose('not_export_exam_tips')"
                    >联系客服开通
                    </button>
                </view>
            </view>
        </uni-popup>


        <uni-popup ref="export_ranking_success" type="center">
            <view class="export_ranking_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('export_ranking_success')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top bg-primary color-white" style="padding: 50px 20px 20px;">
                    <icon type="success" size="80" color="#ffffff"></icon>
                    <view class="font18">导出成功</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ export_ranking_success_tips }}
                    </view>
                    <view
                        class="export_ranking-btn bg-info color-white"
                        hover-class="navigator-hover"
                        @click="copyDownloadSrc(false)"
                    >复制下载地址
                    </view>
                </view>
            </view>
        </uni-popup>


        <web-admin-src-copy-popup ref="web-admin-src-copy-popup"/>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import my_storage from '@/utils/storage.js'
import exportClockInRecord from './export-clock-in-record'

export default {
    data() {
        return {
            this_day: new Date().getUTCDate(),
            loading: true,
            id: '',
            detail: {},
            error: '',
            export_ranking_success_tips: '',
            exam_open: false,
            popup_show: false,
            document_show: this.xwy_config.showActivitySpecificationDocument(5),
            rankingJsonConfig: {}
        }
    },

    computed: {
        integralUnit() {
            return this.detail?.conf?.active?.integral?.unit || '金币'
        }
    },

    onShareAppMessage() {
        let path = '/pages/clock_in/user/detail?id=' + this.id
        if (this.detail.conf.active.screen_pic) path += `&screen_pic=` + (this.detail.conf.active.screen_pic || '')
        return {
            title: this.detail.conf?.active?.share_title || this.detail.name || '',
            path,
            imageUrl: this.detail.conf?.active?.share_image || this.detail.logo || ''
        }
    },
    onLoad(e) {
        console.log('活动详情页面路径参数', e)
        uni.showLoading({ mask: true})
        if (!e.id) {
            this.loading = false
            uni.hideLoading()
            this.error = '请指定活动id'
            return false
        }

        this.id = e.id
        login.uniLogin(err => {
            this.loading = false
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            if (app.globalData['shop_info']?.['shop_set']?.static_url) {
                this.web_base_url = app.globalData['shop_info']['shop_set'].static_url
            }


            this.getDetail()
        })
    },
    methods: {
        getDetail() {
            xwy_api.getActivityDetail(this.id, res => {
                uni.hideLoading()
                if (res.data?.['active_details']) {
                    const detail = res.data['active_details']
                    my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)
                    this.detail = detail
                    if (detail.rank_set?.exam_open) this.exam_open = true

                    this.getRankCategorySet()
                } else {
                    this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                }
            })
        },

        async getRankCategorySet() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.step/top_rank_category_list',
                data: {
                    active_id: this.id
                }
            })

            const list = res?.data?.top_rank_category_list
            if (!list?.length) return

            const rankingJsonConfig = {}
            list.forEach(item => {
                rankingJsonConfig[`types_${item.id}`] = true
            })
            this.rankingJsonConfig = rankingJsonConfig
        },

        toEdit() {
            uni.navigateTo({
                url: this.xwy_config.getActivityPath(this.detail.types, 'add') + '?id=' + this.id
            })
        },

        toPointList() {
            const rank_set = this.detail.rank_set || {}
            let url = '../point/list?id=' + this.id + '&rank_set=' + JSON.stringify(rank_set) + '&active_small_id=' + this.detail.id
            if (this.detail.types === 21) {
                url = `/pages/card-collecting/admin/point/list?id=${this.id}&active_small_id=${this.detail.id}`
                if (rank_set['mapPointCategorySet']) url += '&category_open=1'
            }

            this.$uni.navigateTo(url)
        },

        async downloadImportUsersTemplate() {
            // #ifndef H5
            uni.showModal({
                title: '提示',
                content: '请在web端使用此功能',
                showCancel: false,
                success: res => {
                    res.confirm && this.web_base_url && this.showWebUrl()
                }
            })
            // #endif


            // #ifdef H5
            uni.navigateTo({
                url: '/pages/other/batch_import/download-template?id=' + this.id
            })
            // #endif

        },

        importUsers() {
            // #ifndef H5
            uni.showModal({
                title: '提示',
                content: '请在web端使用此功能',
                showCancel: false,
                success: res => {
                    res.confirm && this.web_base_url && this.showWebUrl()
                }
            })
            // #endif


            // #ifdef H5
            uni.navigateTo({
                url: '/pages/other/batch_import/import-excel?id=' + this.id
            })
            // #endif

        },

        exportMonthIntegralRanking() {
            this.$uni.navigateTo(`/pages/other/export-month-integral-ranking?active_id=${this.id}`)
        },

        uniPopupOpen(ref) {
            this.popup_show = true
            this.$refs[ref].open()
        },

        uniPopupClose(ref) {
            this.popup_show = false
            this.$refs[ref].close()
        },

        async exportClockInRecord() {
            const export_top_rank_excel_open = this.detail.rank_set?.['export_top_rank_excel']
            if (!export_top_rank_excel_open) return this.uniPopupOpen('not_export_ranking_tips')
            this.$uni.showLoading('导出中...')
            await exportClockInRecord(this.id, this.detail.conf.must_submit || [])
            uni.hideLoading()
        },

        
        exportRankingData(types) {
            /*const export_top_rank_excel_open = this.detail.rank_set?.['export_top_rank_excel']
            if (!export_top_rank_excel_open) return this.uniPopupOpen('not_export_ranking_tips')*/

            this.exportRankingDataAjax(types)
        },


        exportRankingDataAjax(types) {
            this.$uni.showLoading('导出中...')

            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id,
                types,
                page: 1,
                perpage: 3000
            }


            xwy_api.ajax({
                url: 'front.flat.sport_step.export.admin_active/export_user_total_rank',
                data,
                success: res => {
                    uni.hideLoading()
                    if (res?.status !== 1) return this.$uni.showModal(res.info || '导出失败')

                    this.export_ranking_success_tips = res.info || '导出成功'
                    this.export_ranking_src = res.data.url
                    this.copyDownloadSrc(true)
                    this.uniPopupOpen('export_ranking_success')


                    const title_options = {
                        7: '答题排行榜',
                        11: `排行榜`,
                        21: '活动用户名单'
                    }

                    const title = `导出活动【${this.detail.name}】${title_options[types]}`
                    my_storage.setExportExcelRecord({url: res.data.url, title})
                }
            })
        },


        async exportLotteryRecord() {
            uni.showLoading({
                title: '导出中...',
                mask: app.globalData['evn_version'] !== 'trial'
            })

            const res = await xwy_api.request({
                url: 'front.flat.active.lottery_active.user.lottery/lottery_records_list',
                data: {
                    is_admin: 1,
                    active_id: this.id,
                    need_excel: 1
                }
            })

            uni.hideLoading()

            this.export_ranking_success_tips = res['info'] || '导出成功'
            this.export_ranking_src = res['data'].url
            this.copyDownloadSrc(true)
            this.uniPopupOpen('export_ranking_success')


            const title = `导出活动【${this.detail.name}】抽奖记录`;
            my_storage.setExportExcelRecord({url: res['data'].url, title})
        },


        copyDownloadSrc(hide = false) {
            uni.setClipboardData({
                data: this.export_ranking_src,
                success: () => hide ? uni.hideToast() : this.$uni.showToast('复制成功', 'success', 1000)
            })
        },

        showActiveSharePopup() {
            this.$refs.activeShare.open({
                page: 'pages/clock_in/user/detail',
                scene: 'id=' + this.detail.id,
                qrcode_logo: this.detail.rank_set?.closed_AD && this.detail.conf?.active?.qrcode_logo
            })
        },
        

        showWebUrl() {
            this.$refs['web-admin-src-copy-popup'].open({
                successCallback: () => this.popup_show = true,
                closeCallback: () => this.popup_show = false
            })
        },

        toShopManage() {
            // #ifndef H5
            uni.showModal({
                title: '提示',
                content: '请在web端管理商品',
                showCancel: false,
                success: res => {
                    if (res.confirm && this.web_base_url) this.showWebUrl()
                }
            })
            const flat = true
            if (flat) return
            // #endif

            window.open(this.web_base_url + '/web/wx-cloud-api/pages/admin_manage/goods#/pages/goods/list?access_token=' + app.globalData['access_token'])
        },

        copyActivityPages() {
            let data = 'pages/clock_in/user/detail?id=' + this.id
            if (this.detail.screen_pic) data += `&screen_pic=` + this.detail.screen_pic
            uni.setClipboardData({
                data,
                success() {
                    uni.hideToast()
                    uni.showModal({
                        title: '复制成功',
                        content: '小程序路径地址复制成功，请粘贴到公众号自定义菜单设置里',
                        showCancel: false
                    })
                }
            })
        },

        copy(data) {
            if (!data) return
            uni.setClipboardData({
                data,
                success: () => this.$uni.showToast('复制成功', 'none', 500)
            })
        }
    }
}
</script>

<style scoped>
.page {
    min-height: 100vh;
    padding-bottom: 15px;
    box-sizing: border-box;
}

.logo {
    display: block;
    width: 100vw;
    height: auto;
}

.tools {
    margin: 10px;
    border-radius: 10px;
    overflow: hidden;
}

.tools .title {
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.item {
    width: calc(100% / 4);
    padding: 10px 5px;
    box-sizing: border-box;
}

.export_ranking_popup {
    width: 280px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}

.export_ranking-btn {
    line-height: 40px;
    border-radius: 20px;
}

.export_ranking-btn::after {
    border: none;
}
</style>
