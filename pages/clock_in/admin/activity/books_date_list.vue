<template>
	<view class="page bg-background">
		<view class="top-view">
			<view class="search bg-white flex-kai p10">
				<view class="input-view">
					<view class="search-icon left-icon flex-all-center">
						<uni-icons type="search" size="20" color="#bbbec4"/>
					</view>
					<input
						class="input bg-background"
						type="text"
						confirm-type="search"
						v-model="nickname"
						@confirm="reloadList"
						placeholder="输入用户名字搜索"
						placeholder-style="color:#bbbec4"
					/>
					<view class="search-icon right-icon flex-all-center" @click="clearNickname">
						<uni-icons v-if="nickname" type="close" size="20" color="#bbbec4"/>
					</view>
				</view>
				<view class="search-go color-info pl10" @click="reloadList">搜索</view>
			</view>

			<view v-if="activity_detail.conf && activity_detail.conf.active && activity_detail.conf.active.day_by_day && activity_detail.conf.active.day_by_day.length" class="flex-kai bg-white font14 p10">
				<view class="color-content flex-row" style="width: 100%;">
					<view class="pr5">按日期搜索: </view>
					<picker
						:range="activity_detail.conf.active.day_by_day"
						range-key="date"
						:value="activity_detail.conf.active.day_by_day.findIndex(v => v.date === date) === -1 ? 0 : activity_detail.conf.active.day_by_day.findIndex(v => v.date === date)"
						@change="dateChange($event)"
					>
						<view :class="{'color-sub': !date}">{{date || '未指定日期'}}</view>
					</picker>

					<uni-icons type="forward" color="#80848f" size="14"/>
				</view>
				<view
					v-if="date"
					class="color-primary text-right"
					style="min-width: 80px;"
					@click="clearDate"
				>不指定日期</view>
			</view>


		</view>


		<view v-if="list_count" class="color-sub text-center font14" style="padding-top: 10px;">
			{{search_nickname}} {{search_date}} 共{{list_count}}条报名记录
		</view>



		<view class="list">
			<view class="item bg-white" v-for="(item, index) in list" :key="index">
				<view class="color-title">{{item.nickname}}</view>
				<view class="color-content pt5">
					<text class="pr5">报名时间:</text>
					<uni-dateformat
						:date="item.books_date * 1000"
						format="yyyy-MM-dd"
						:threshold="[0, 0]"
					/>
				</view>
				<view class="color-sub pt5">
					<text class="pr5">提交时间:</text>
					<text>{{item.create_time}}</text>
				</view>
			</view>
		</view>




		<view v-if="!list.length && !loading" class="text-center" style="padding-top: 10vh;">
			<text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
			<view class="color-sub">{{search_nickname}} {{search_date}} 暂无报名记录</view>
		</view>

		<uni-load-more v-if="loading" status="loading"></uni-load-more>
		<uni-load-more v-if="is_lastpage && list.length > 5" status="noMore" :contentText="{contentnomore: '我是有底线的'}"></uni-load-more>
		<uni-load-more v-if="!is_lastpage && !loading" status="more"></uni-load-more>

	</view>
</template>

<script>
	const app = getApp()
	import xwy_api from '@/utils/api/xwy_api.js'
	import utils from '@/utils/utils.js'

	export default {
		data() {
			return {
				loading: true,
				list: [],
				load_page: 1,
				is_lastpage: false,
				date: '',
				nickname: '',
				search_date: '',
				search_nickname: '',
				list_count: 0,
				activity_detail: {}
			}
		},
		onLoad(e) {
			this.id = e.id
			this.getActivityDetail()
			this.getList()
		},
		onReachBottom() {
			!this.is_lastpage && !this.loading && this.getList()
		},
		methods: {
			async getActivityDetail() {
				let activity_detail = app.globalData.activity_detail || this.activity_detail

				if (!activity_detail) {
					const res = await xwy_api.request({
						url: 'front.flat.sport_step.active_list/active_details',
						data: {
							access_token: app.globalData.access_token,
							active_id: this.id
						}
					})

					if (!res?.data?.active_details) {
						return false
					}

					activity_detail = res.data.active_details
				}


				this.activity_detail = activity_detail
			},

			clearNickname() {
				this.nickname = ''
				this.reloadList()
			},

			reloadList() {
				this.list_count = 0
				this.load_page = 1
				this.getList()
			},

			async getList() {
				if (this.load_page === 1) {
					this.list = []
					this.is_lastpage = false
				}

				this.loading = true

				const data = {
					access_token: app.globalData.access_token,
					active_id: this.id,
					is_admin: 1,
					page: this.load_page,
					perpage: 20
				}
				if (this.date) data.date = this.date
				if (this.nickname) data.nickname = this.nickname
				this.search_date = this.date
				this.search_nickname = this.nickname
				const res = await xwy_api.request({
					url: 'front.flat.sport_step.scanPointBooks/user_submit_books_date_list',
					data
				})

				this.loading = false
				this.load_page++

				const _data = res?.data?.submit_books_date_list

				if (!_data) {
					this.is_lastpage = true
					return false
				}

				const list = _data.data || []
				this.list = [...this.list, ...list]
				this.is_lastpage = _data.is_lastpage
				this.list_count = _data.total
			},


			dateChange(e) {
				this.date = this.activity_detail.conf.active.day_by_day[e.detail.value].date
				this.reloadList()
			},

			clearDate() {
				this.date = ''
				this.reloadList()
			}
		}
	}
</script>

<style>
.page {
	min-height: 100vh;
	padding-top: 100px;
	padding-bottom: 100px;
	box-sizing: border-box;
}

.top-view {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 999;
}

.search, .search .input {
	height: 40px;
	line-height: 40px;
}
.input-view {
	position: relative;
	width: 100%;
}
.search-icon {
	position: absolute;
	width: 30px;
	height: 40px;
	top: 0;
}
.left-icon{
	left: 0;
}
.right-icon {
	right: 0;
}
.search .input {
	/* width: calc(100% - 80px); */
	padding: 0 30px;
	border-radius: 20px;
	box-sizing: border-box;
}
.search-go {
	width: 50px;
	min-width: 50px;
	text-align: right;
}


/* #ifdef H5 */
.search-go {
	padding-right: 10px;
	box-sizing: border-box;
}
@media screen and (min-width: 500px) {
	.top-view {
		width: 500px;
		left: calc(((100vw - 500px) / 2) - 10px);
	}
	.search .input {
		width: 440px;
	}
}
/* #endif */

.item {
	margin: 10px;
	padding: 10px;
	border-radius: 10px;
}
</style>
