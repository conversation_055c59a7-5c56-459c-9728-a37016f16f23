<template>
    <view>
        <view class=list>
            <view class="item flex-kai" v-for="(item, index) in list" :key="item.num">
                <view>
                    <view>打卡次数: 第{{ item.num }}次打卡</view>
                    <view>抽奖活动: {{ item.name }}</view>
                </view>
                <view class="flex-row">
                    <view class="icon-item text-center" @click="editSet(index)">
                        <text class="iconfont icon-edit color-sub"></text>
                    </view>
                    <view class="icon-item text-center" @click="list.splice(index, 1)">
                        <text class="iconfont icon-delete color-sub"></text>
                    </view>
                </view>
            </view>
        </view>
        
        <view class="p10 text-center font14 color-light-primary" @click="addSet">添加抽奖规则</view>
        
        
        <uni-popup ref="popup">
            <view class="edit-popup bg-white">
                <uni-forms label-position="top">
                    <uni-forms-item label="打卡次数:">
                        <uni-easyinput type="number" v-model="edit_data.num" maxlength="5"
                                       placeholder="第几次打卡可以抽奖，请输入整数"/>
                    </uni-forms-item>
                    <uni-forms-item label="抽奖活动:">
                        <view class="imitate-uni-easyinput" @click="toChooseLottery">
                            <view>
                                <text v-if="edit_data.name">{{ edit_data.name }}</text>
                                <text v-else class="placeholder">请选择抽奖活动</text>
                            </view>
                            <view class="icon flex-all-center">
                                <uni-icons type="forward" size="16" color="#c0c4cc"/>
                            </view>
                        </view>
                    </uni-forms-item>
                </uni-forms>
                
                <view class="confirm-button bg-light-primary color-white text-center"
                      hover-class="navigator-hover" @click="confirm">确定
                </view>
                <view class="flex-all-center">
                    <view class="p10 color-sub font12" @click="$refs.popup.close()">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>

const getDefaultItem = () => {
    return {
        num: '',
        name: '',
        lottery_active_id: ''
    }
}

export default {
    name: "clock-in-lottery-set",
    props: {
        set: {
            type: Array,
            default: () => []
        }
    },
    
    data() {
        return {
            list: [],
            edit_data: getDefaultItem(),
            edit_index: -1
        }
    },
    
    watch: {
        set: {
            handler(val) {
                this.list = val
            },
            immediate: true
        },
        list: {
            handler(val) {
                this.$emit('update:set', val)
            },
            immediate: true
        }
    },

    mounted() {

    },

    methods: {
        addSet() {
            this.edit_index = -1
            this.edit_data = getDefaultItem()
            this.$refs.popup.open()
        },

        editSet(index) {
            this.edit_index = index
            this.edit_data = JSON.parse(JSON.stringify(this.list[index]))
            this.$refs.popup.open()
        },

        toChooseLottery() {
            this.$uni.navigateTo('/pages/lottery/admin/lottery/list?is_sel=1', {
                events: {
                    selLottery: lottery => {
                        this.edit_data.lottery_active_id = lottery.id
                        this.edit_data.name = lottery.title
                    }
                }
            })  
        },

        confirm() {
            const data = JSON.parse(JSON.stringify(this.edit_data))
            if (!data.num) return this.$uni.showToast('请输入打卡次数')
            const num = Math.floor(data.num)
            if (isNaN(num)) return this.$uni.showToast('打卡次数请输入整数')
            if (num < 1) return this.$uni.showToast('打卡次数不能小于1')
            data.num = num
            if (!data.lottery_active_id) return this.$uni.showToast('请选择抽奖活动')
            
            this.edit_index === -1 ? this.list.push(data) : this.list.splice(this.edit_index, 1, data)
            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss">
.list {
    .item {
        padding: 10px;
        margin-top: 10px;
        background-color: #f8f8f8;
        border-radius: 10px;
        
        .icon-item {
            width: 30px;
            min-width: 30px;
        } 
    }
}

.edit-popup {
    width: 80vw;
    max-width: 400px;
    padding: 10px;
    border-radius: 10px;

    .imitate-uni-easyinput {
        height: 34px;
        line-height: 34px;
        padding-right: 34px;
        border: 1px solid #e5e5e5;
        font-size: 14px;
        padding-left: 10px;
        border-radius: 4px;
        position: relative;
        
        .placeholder {
            color: #808080;
        }
        
        .icon {
            position: absolute;
            top: 0;
            right: 0;
            width: 34px;
            height: 34px;
        }
    }
    
    .confirm-button {
        margin: 0 auto;
        width: 120px;
        line-height: 40px;
        border-radius: 5px;
    }
}
</style>