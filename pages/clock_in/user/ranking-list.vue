<template>
    <view class="page bg-background">

        <xwy-ad v-if="show_AD" :ad_type="3"></xwy-ad>
        
        <template v-if="top_rank_category_list.length > 1 && !team_id">
            <view class="type-bar flex-row bg-white">
                <view class="type-bar-item text-center font14" 
                      :class="{'type-bar-item-active': item.id === type_id}" 
                      v-for="item in top_rank_category_list" :key="item.id" 
                      :style="{width: 100 / top_rank_category_list.length + '%'}" 
                      @click="typeIdChange(item.id)">
                    {{ item.name }}
                </view>
            </view>
            
            <view style="height: 40px;"></view>
        </template>

        <view v-if="top_rank_text && top_rank_text.rules" class="font12 p10" 
              style="color: #e19898;" @click="copyId">{{ top_rank_text.rules }}
        </view>

        <top-banner v-if="top_rank_banner.length" :list="top_rank_banner"></top-banner>

        <template v-if="top49">
            <view v-show="type_id === 49" class="clear clearfix p10">

                <picker class="fr" mode="date" :value="date" start="2025-01-01" :end="top49DateEnd"
                        fields="month" @change="dateChange">
                    <view class="date-picker flex-row">
                        <view>
                            <text :class="['iconfont color-sub', 'icon-calendar-' + dateYearMonth.month]"></text>
                        </view>
                        <view class="text-center date-picker-year-month color-content">
                            {{ dateYearMonth.year }}年{{ dateYearMonth.month }}月
                        </view>
                        <view>
                            <text class="iconfont icon-more color-disabled"></text>
                        </view>
                    </view>
                </picker>
            </view>
        </template>

        <template v-if="podiumList.length">
            <ranking-podium :list="podiumList"/>
        </template>

        <!--打卡点打卡人次数排行榜不显示用户当前名次，因为没有名次-->
        <view v-if="list.length && total_count && type_id !== 50" class="color-sub font14 text-center pt5">
            <template v-if="!show_AD">
                我的
                <template v-if="isTeamRanking">队伍</template>
                排名: {{ my_position_num === -1 ? '未上榜' : my_position_num }}
            </template>
            <text class="pl5" v-if="total_count">
                (共{{ total_count }}{{ isTeamRanking ? '队伍' : '人' }})
            </text>
        </view>

        <view class="list">
            <view v-for="(item, index) in list" :key="index">
                <xwy-ad v-if="show_AD && index === 0" :ad_type="66"></xwy-ad>

                <view v-if="!limit_show_num || index < limit_show_num" class="item flex-row bg-white"  
                      @click="clickItem(item)">
                    <view class="index flex-all-center" @click.stop="copyUserid(item)">

                        <image class="top-3" v-if="index < 3"
                               :src="'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/no-' + (index + 1) + '.png'"/>
                        <view v-else class="color-sub font14">{{ index + 1 }}</view>
                    </view>

                    <view class="flex-all-center">
                        <view v-if="item.headimg">
                            <image mode="aspectFill" class="headimg" :src="item.headimg"/>
                        </view>
                    </view>

                    <view class="middle">
                        <view class="name color-title ellipsis">{{ item.nickname }}</view>
                        <view v-if="isTeamRanking" class="color-sub font12 pt5">查看队内排行榜</view>
                        <view v-if="item.other_info.length" class="color-sub font12 pt5">
                            <view v-for="info in item.other_info" :key="info">{{ info }}</view>
                        </view>
                    </view>

                    <view class="right flex-column flex-all-center color-red font14">
                        <view class="right-item text-right">
                            <view>
                                <text>{{ item.value }}</text>
                                <text v-if="unit" class="font12" style="padding-left: 2px;">{{ unit }}</text>
                            </view>
                        </view>

                    </view>
                </view>

                <xwy-ad v-if="show_AD && index !== 0 && ((index + 1) % 10 === 0)" :ad_type="66"></xwy-ad>

            </view>
        </view>

        <view v-if="!list.length && !loading" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无排行数据</view>
        </view>

        <uni-load-more v-if="loading" status="loading"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && list.length > 5"
            status="noMore"
            :contentText="{contentnomore: '我是有底线的'}"
        ></uni-load-more>
        <uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'

// 获取上个月的1号，格式为2021-01-01，当前是1月的话要返回去年的12月1号
const getPreviousMonthFirstDay = (date) => {
    let [year, month] = date.split('-')
    if (Number(month) === 1) {
        month = 12
        year--
    } else {
        month--
    }
    month = month.toString().padStart(2, '0')
    return `${year}-${month}-01`
}

export default {
    data() {
        return {
            id: '',
            loading: true,
            total_count: 0,
            top_rank_text: null,
            my_position_num: -1,
            list: [],
            load_page: 1,
            is_last_page: false,
            top_rank_banner: [],
            show_AD: false,
            popup_show: false,
            limit_show_num: null,
            top_rank_category_list: [],
            type_id: 11,
            team_id: null,
            integralUnit: '',
            date: getPreviousMonthFirstDay(this._utils.getDay())
        }
    },
    
    computed: {
        isTeamRanking() {
            const teamTypes = [44, 40]
            return teamTypes.includes(this.type_id)
        },
        
        isExamRanking() {
            const types = [4, 47]
            return types.includes(this.type_id)
        },

        podiumList() {
            const {list, show_AD, unit} = this
            if (!list.length && show_AD) return []
            
            return this.list.slice(0, 3).map(item => ({
                headimg: item.headimg,
                nickname: item.nickname,
                value: item.value === 'hide' ? '' : `${item.value}${unit}`
            }))
        },

        unit() {
            if (this.isExamRanking) return '分'
            if (this.type_id === 50) return '人次'
            if ([40, 48, 49].includes(this.type_id)) return this.integralUnit
            if (this.type_id === 55) return ''
            return this.isTeamRanking ? '次' : '赞'
        },

        dateYearMonth() {
            const [year = '', month = ''] = this.date.split('-')
            return {year: Number(year), month: Number(month)}
        },

        top49() {
            return this.top_rank_category_list.find(item => item.id === 49)
        },

        top49DateEnd() {
            let {year, month} = this._utils.getYearMonthDay()
            if (!this.top49?.['statistics_this_month']) {
                month--
                if (month === 12) year--
            }
            const day = new Date(year, month, 0).getDate()

            return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
        }
    },

    onLoad(e) {
        this.id = e.id
        if (e.team_id) {
            this.team_id = Number(e.team_id)
            if (e.team_name) this.$uni.setNavigationBarTitle(`${e.team_name}队内排行榜`)
        }
        this.$uni.showLoading()
        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.init(e)
        })
    },

    onPullDownRefresh() {
        this.reloadList().finally(() => uni.stopPullDownRefresh())
    },

    onReachBottom() {
        if (this.is_last_page || this.loading) return
        this.load_page++
        this.loadList()
    },
    
    methods: {
        async init(e) {
            await this.getActivityDetails()
            await this.getTypeList()
            this.type_id = e.type_id ? Number(e.type_id) : this.top_rank_category_list[0].id
            await this.getList()
            this.loading = false
            uni.hideLoading()
        },

        async fetchActivityDetails() {
            const details = app.globalData['activity_detail']
            if (details && details.active_id === this.id) return details
            
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details',
                data: {
                    active_id: this.id
                }
            })
            return res?.['data']?.['active_details']
        },

        async getActivityDetails() {
            const details = await this.fetchActivityDetails()
            if (!details) {
                await this.$uni.showModal('活动获取失败', {success: () => this.$uni.navigateBack()})
                return
            }
            
            this.setPageData(details)
        },

        setPageData(details) {
            this.active_details = details
            const rank_set = details.rank_set || {}
            if (!rank_set.closed_AD) this.show_AD = true
            if (rank_set.limit_show_num) this.limit_show_num = rank_set.limit_show_num

            const active = details.conf?.active || {}
            if (active.top_rank_banner?.length) this.top_rank_banner = active.top_rank_banner
            this.integralUnit = active.integral?.unit || '金币'
        },

        async getTypeList() {
            if (this.team_id) return []

            const jsonConfig = await this.getRankingJsonConfig()
            if (jsonConfig?.length) {
                this.top_rank_category_list = jsonConfig
                return
            }

            const {team_submit_sign_count, signOnlyExam, sum_exam_score} = this.active_details?.rank_set || {}
            
            // 只要答题，不要打卡的，只显示答题排行榜
            if (signOnlyExam) {
                this.top_rank_category_list = [{id: 47, name: '排行榜'}]
                this.type_id = 47
                return
            }
            
            const list = [{id: 11, name: '个人排行榜'}]
            
            if (sum_exam_score) list.push({id: 7, name: '答题排行榜'})
            if (team_submit_sign_count) list.push({id: 44, name: '队伍排行榜'})
            
            this.top_rank_category_list = list
        },

        async getRankingJsonConfig() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.step/top_rank_category_list',
                data: {active_id: this.id}
            })

            const list = res?.data?.top_rank_category_list || []
            // JSON没有配置排行榜类型，默认会返回健步走的默认排行榜类型，健步走默认排行榜类型包括0个人排行榜
            // 所以返回了0个人排行榜的就当成是活动没有JSON配置排行榜类型
            if (list.find(item => item.id === 0)) return []

            return list
        },

        typeIdChange(id) {
            if (id === this.type_id) return
            this.type_id = id
            this.reloadList()
        },

        dateChange(e) {
            this.date = e.detail.value
            this.reloadList()
        },


        async loadList() {
            if (!this.loading && !this.is_last_page) {
                this.loading = true
                await this.getList()
                this.loading = false
            }
        },

        async reloadList() {
            this.load_page = 1
            this.loading = true
            await this.getList()
            this.loading = false
        },

        async getList() {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
                this.my_position_num = -1
                this.total_count = 0
            }

            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id,
                top_rank_types: this.type_id,
                page: this.load_page,
                perpage: 20
            }
            if (this.team_id) data.team_id = this.team_id
            if (data.top_rank_types === 49) {
                data.year = this.dateYearMonth.year
                data.month = this.dateYearMonth.month
            }

            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace res.data.top_rank_list.position_num */
            
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.step/top_rank_list',
                data
            })


            if (!res?.data) {
                this.is_last_page = true
                return
            }

            if (res.data.rank_types_text) this.top_rank_text = res.data.rank_types_text

            if (res.data.top_rank_list) {
                const _data = res.data.top_rank_list
                
                if (_data.position_num) this.my_position_num = res.data.top_rank_list.position_num
                
                const list = _data.list.data || []
                const new_list = this.listDataInit(list)

                this.list = [...this.list, ...new_list]
                this.is_last_page = _data.list.is_lastpage
                this.total_count = _data.list.total
            } else {
                this.is_last_page = true
            }
        },

        listDataInit(list) {
            const new_list = []
            list.forEach(v => {
                const item = {
                    id: v.id || v.team_id || null,
                    userid: v.userid || null,
                    headimg: this.getHeadimg(v),
                    nickname: this.getNickname(v),
                    value: this.getValue(v),
                    other_info: this.getItemOtherInfo(v)
                }

                new_list.push(item)
            })
            return new_list
        },
        
        getHeadimg(v) {
            if (this.type_id === 50) return v.conf?.logo || ''
            if (this.type_id === 44) return v.conf?.logo || ''
            if (this.type_id === 40) return v.team_details?.conf?.logo || ''
            return v.headimg || v.user_details?.headimg || ''
        },
        
        getNickname(v) {
            if (this.type_id === 44 || this.type_id === 50) return v.name || ''
            if (this.type_id === 40) return v.team_details?.name || ''
            const must_submit = v.must_submit || v.user_details?.must_submit
            return must_submit?.[0]?.value || ' '  
        },

        getValue(v) {
            if (this.isExamRanking) return v.score || 0
            if (this.type_id === 48) return v.integral_all || 0
            if (this.type_id === 40) return v.allIntegral || 0
            if (this.type_id === 49) return Number((v['total_integral'] || 0).toFixed(2))
            if (this.type_id === 50) return v.submit_count || 0
            if (this.type_id === 55) return this.getTimeValue(v.used_time || 0)
            return v.sign_count || 0
        },

        getTimeValue(second) {
            if (!second) return ''

            const units = [
                { label: '天', value: Math.floor(second / (60 * 60 * 24)) },
                { label: '小时', value: Math.floor(second % (60 * 60 * 24) / (60 * 60)) },
                { label: '分', value: Math.floor(second % (60 * 60) / 60) },
                { label: '秒', value: second % 60 }
            ];

            // 把非0的单位拼接起来
            return units
                .filter(unit => unit.value > 0)
                .map(unit => `${unit.value}${unit.label}`)
                .join('')
        },

        
        getItemOtherInfo(v) {
            const infos = {
                11: () => {
                    return [
                        `累计打卡${v['sign_day'] || 0}天`,
                        `累计打卡${v.sign_count || 0}次`,
                        `连续打卡${v['continue_sign_day'] || 0}天`,
                    ]
                }
            }
            return infos[this.type_id]?.() || []
        },


        copyUserid(item) {
            let id = item.userid
            if (this.type_id === 44) id = item.id
            if (this.type_id === 40) id = item.team_id
            uni.setClipboardData({
                data: id.toString(),
                success: () => uni.hideToast()
            })
        },

        copyId() {
            uni.setClipboardData({
                data: this.id,
                success: () => uni.hideToast()
            })
        },

        clickItem(item) {
            if (this.isExamRanking) return

            const type = this.type_id
            const {id, userid, nickname} = item

            // 查看队内排行榜
            if (this.isTeamRanking) {
                // 默认查看打卡排行榜
                let url = `./ranking-list?id=${this.id}&team_id=${id}&team_name=${nickname}`
                // 如果是队伍积分排行榜，查看队内积分排行榜
                if (type === 40) url += '&type_id=48'
                return this.$uni.navigateTo(url)
            }

            // 查看打卡点打卡记录
            if (type === 50) {
                this.$uni.navigateTo(`./public_sign_list?id=${this.id}&point_id=${id}&point_name=${nickname}`)
                return
            }
            
            // OA开启了 zs+110 关闭排行榜点击头像查看别人记录
            if (this.active_details?.rank_set?.closed_user_exchange_list) return

            // 除了这些排行榜以外，不能查看打卡记录
            const lookRecordTypes = [11, 31, 49, 50]
            if (!lookRecordTypes.includes(type)) return

            // 查看打卡记录
            this.$uni.navigateTo(`./public_sign_list?id=${this.id}&nickname=${nickname}&userid=${userid}`)
        }
    }
}
</script>

<style scoped lang="scss">
@import "@/pages/ranking-list/ranking-list.scss";

.type-bar {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;

    .type-bar-item {
        height: 40px;
        line-height: 38px;
    }

    .type-bar-item-active {
        border-bottom: 2px solid #5cadff;
        color: #5cadff;
    }
}

.date-picker {
    background-color: #fff;
    padding: 10px;
    border-radius: 10px;

    .date-picker-year-month {
        width: 100px;
    }
}
</style>
