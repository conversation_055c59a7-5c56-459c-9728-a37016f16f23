<template>
	<view>
		<view v-if="activity_detail.active_id" class="sign-page bg-white">
			<view class="p10" v-if="activity_detail.conf.active.memo_required !== 0">
				<uni-easyinput
					type="textarea"
					v-model="memo"
					:maxlength="800"
					:placeholder="memo_placeholder"
				></uni-easyinput>
			</view>

			<view v-if="activity_detail.conf.active.point_hide && activity_detail.conf.active.address_required" class="flex-kai p10">
				<view class="flex-row">
					<view class="color-content" style="min-width: 73px;">当前位置:</view>
					<view>
						<text v-if="address" class="color-title">{{address}}</text>
						<text v-else class="color-sub">未获取</text>
					</view>
				</view>
				<view
					class="color-primary text-right"
					style="min-width: 70px;"
					hover-class="navigaotr-hover"
					@click="getAddress"
				>{{address ? '重新获取' : '获取位置'}}</view>
			</view>

			<view v-if="activity_detail.conf.active.step_required" class="flex-kai p10">
				<view class="flex-row">
					<view class="color-content" style="min-width: 73px;">运动步数:</view>
					<view>
						<text v-if="wechat_step === null" class="color-sub">未获取</text>
						<text
							v-else
							:class="{'color-warning': wechat_step >= 10000, 'color-success': wechat_step < 10000}"
						>{{wechat_step || 0}}</text>
					</view>
				</view>
				<view
					class="color-primary text-right"
					style="min-width: 70px;"
					hover-class="navigaotr-hover"
					@click="getStep"
				>{{wechat_step === null ? '获取步数' : '重新获取'}}</view>
			</view>

			<view v-if="activity_detail.conf.active.pic_list_required !== 0" class="p10">
				<view>
				    <text class="color-content">打卡相册</text>
				    <text v-if="activity_detail.conf.active.pic_list_required === 1" class="color-red">*</text>
				    <text class="color-sub font12 pl5">
						{{pic_list.length}}/{{pic_list_max_count}}
					</text>
				</view>
				<view style="padding-top: 5px;">
				    <view class="flex-row flex-wrap">
				    	<view
							class="top-rank-banner-item"
							v-for="(item, index) in pic_list"
							:key="index"
						>
							<image
								:src="item"
								mode="aspectFill"
								@click="previewImage(pic_list, item)"
							/>
							<view class="del-image-item" @click.stop="pic_list.splice(index, 1)">
							    <uni-icons type="closeempty" color="#e20f04"/>
							</view>
						</view>
						<view
							v-if="pic_list.length < pic_list_max_count"
							class="add-image text-center"
							@click="changeImage('pic_list')"
						>
						    <uni-icons type="plusempty" size="48" color="#eeeeee"/>
						</view>
				    </view>
				</view>
			</view>

			<view v-if="activity_detail.conf.active.video_list_required !== 0" class="p10">
				<view>
				    <text class="color-content">打卡视频</text>
				    <text v-if="activity_detail.conf.active.video_list_required === 1" class="color-red">*</text>
				</view>
				<view class="pt5">
					<view v-if="video_list.length" class="video-item-view">
						<video class="video-item" :src="video_list[0]"></video>
						<view class="del-image-item" @click.stop="video_list = []">
						    <uni-icons type="closeempty" color="#e20f04"/>
						</view>
					</view>
					<view v-else class="choose-video flex-all-center" @click="chooseVideo">
						<uni-icons type="plusempty" size="48" color="#eeeeee"/>
					</view>
				</view>
			</view>

			<view class="flex-all-center" style="padding-top: 10px;">
				<view
					hover-class="navigator-hover"
					class="clock-in flex-all-center bg-primary color-white"
					@click="clockIn"
				>{{_id ? '保存' : '打卡'}}</view>
			</view>
		</view>

    
	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'
	import base64 from '@/utils/base64.js'


	export default {
		data() {
			return {
				_id: '',
				activity_detail: {},
				memo: '',
				pic_list: [],
				pic_list_max_count: 6,
				video_list: [],
				// video_list_max_count: 6,
				wechat_step: null,
				address: '',
				lat: '',
				lng: ''
			}
		},

        computed: {
            memo_placeholder() {
                const {memo_required, memo_placeholder} = this.activity_detail?.conf?.active || {}
                if (!memo_required) return ''
                if (memo_placeholder) return memo_placeholder
                return `请填写打卡备注,800字内,${memo_required === 2 ? '非' : ''}必填`
            },
        },

		onLoad(e) {
			uni.showLoading({
				title: '加载中...',
			})

			if (e.active_id) this.active_id = e.active_id

			login.uniLogin(err => {
				if (err && err.errMsg) {
					uni.hideLoading()
					uni.showModal({
						title: err.errTitle || '提示',
						content: err.errMsg,
						showCancel: false
					})
					return false
				}



				this.getDetail()
			})
		},

		methods: {
			async getDetail() {
				const sign_record_edit_data = app.globalData.sign_record_edit_data

				let activity_detail = app.globalData.activity_detail

				if (!activity_detail) {
					const res = await xwy_api.request({
						url: 'front.flat.sport_step.active_list/active_details',
						data: {
							access_token: app.globalData.access_token,
							active_id: sign_record_edit_data && sign_record_edit_data.active_id || this.active_id
						}
					})

					activity_detail = res.data.active_details

				}

				this.activity_detail = activity_detail



				uni.hideLoading()

				if (!sign_record_edit_data) return false

				uni.setNavigationBarTitle({
					title: '修改打卡内容'
				})

				this.point_id = sign_record_edit_data.point_id
				this._id = sign_record_edit_data.id
				if (sign_record_edit_data.memo) this.memo = sign_record_edit_data.memo
				if (sign_record_edit_data.lat) this.lat = sign_record_edit_data.lat
				if (sign_record_edit_data.lng) this.lng = sign_record_edit_data.lng
				if (sign_record_edit_data.conf_json) {
					const conf_json = sign_record_edit_data.conf_json
					if (conf_json.pic_list?.length) this.pic_list = conf_json.pic_list
					if (conf_json.video_list?.length) this.video_list = conf_json.video_list
					if (conf_json.address) this.address = conf_json.address
					if (conf_json.wechat_step) this.wechat_step = conf_json.wechat_step
				}

				app.globalData.sign_record_edit_data = null
			},

			async getAddress() {
				uni.showLoading({
					title: '位置获取中...',
					mask: true
				})
				const {latitude, longitude} = await new Promise(resolve => {
					uni.getLocation({
						type: 'gcj02',
						success: res => {
							return resolve(res)
						},
						fail: err => {
							console.log(err)
							uni.hideLoading()
							if (err.errMsg === 'getLocation:fail auth deny') {
								uni.showModal({
									title: '位置获取失败',
									content: '请授权小程序使用位置消息',
									confirmText: '去授权',
									success: res => {
										if (res.confirm) uni.openSetting()
									}
								})
								return false
							}


							xwy_api.alert(JSON.stringify(err))
							return false
						}
					})
				})

                // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
                /** @namespace res.data.city_details */
                const res = await this.xwy_api.request({
                    url: 'front.system.city/get_city_details_lat_lng',
                    data: {
                        lat: latitude,
                        lng: longitude
                    }
                })

				uni.hideLoading()
                
                const {province = '', city = '', area = '', street = '', building = ''} = res?.data?.city_details || {}
                const address = [province, city === province ? '' : city, area, street, building].filter(Boolean).join('')
                
                if (!address) return this.$uni.showToast('位置获取失败')
				
                this.lat = latitude
                this.lng = longitude
                this.address = address
				
			},


			getStep() {
				uni.showLoading({
					title: '步数获取中...',
					mask: true
				})
				xwy_api.getWeRunData(res => {
					uni.hideLoading()
					if (res?.data?.crypt_data?.stepInfoList?.length) {
						const step_list = res.data.crypt_data.stepInfoList
						const today_step = step_list[step_list.length - 1].step

						if (today_step === this.wechat_step) {
							uni.showToast({
								title: '步数没有变化',
								icon: 'none'
							})
						}

						this.wechat_step = today_step || 0
					} else {
						xwy_api.alert('运动步数获取失败')
					}
				})
			},

			changeImage(key) {
				uni.navigateTo({
					url: '/pages/other/image_upload_or_select?active_id=' + this.activity_detail.active_id,
					events: {
						newImg: src => {
							switch(key) {
								case 'pic_list':
									this.pic_list.push(src)
									break
							}
						}
					}
				})
			},


			async chooseVideo() {
				const video_data = await new Promise(resolve => {
					uni.chooseVideo({
						success: res => {
							return resolve(res)
						}
					})
				})

				uni.showLoading({
					title: '视频上传中...',
					mask: true
				})

				const video_src = await xwy_api.uploadVideo(video_data, this.activity_detail.active_id)

				uni.hideLoading()

				if (!video_src) {
					xwy_api.alert('视频上传失败，请重试')
					return false
				}

				this.video_list.push(video_src)
			},

			previewImage(urls, current = urls[0]) {
			    console.log(urls)
			    uni.previewImage({
			        urls,
					current
			    })
			},


			dataCheck() {
				const {memo_required, memo_min_words, pic_list_required, video_list_required, address_required, point_hide, step_required} = this.activity_detail.conf.active
				if (memo_required === 1) {
            if (!this.memo) {
                uni.showToast({
                    title: '请填写打卡备注',
                    icon: 'error'
                })
                return false
            }
            const min = Number(memo_min_words)
            if (min && this.memo.length < min) {
                uni.showToast({
                    title: `备注不得少于${min}字`,
                    icon: 'none'
                })
                return false
            }
				}
				if (pic_list_required === 1 && !this.pic_list.length) {
					uni.showToast({
						title: '请上传打卡相册',
						icon: 'error'
					})
					return false
				}
				if (video_list_required === 1 && !this.video_list.length) {
					uni.showToast({
						title: '请上传打卡视频',
						icon: 'error'
					})
					return false
				}

				if (point_hide && address_required === 1 && !this.address) {
					uni.showToast({
						title: '请获取位置',
						icon: 'error'
					})
					return false
				}

				if (step_required) {
					const min_step = Number(this.activity_detail.conf.active.min_step)
					if (min_step) {
						if (this.wechat_step === null) {
							uni.showToast({
								title: '请获取运动步数',
								icon: 'error'
							})
							return false
						}
						if (this.wechat_step < min_step) {
							xwy_api.alert('运动步数不得小于' + min_step + '步', {Title: '打卡失败'})
							return false
						}
					}
				}

				const data = {
					access_token: app.globalData.access_token,
					active_id: this.activity_detail.active_id
				}
				// 没有点位id的要传运动类型，不然无法打卡
				if (this.point_id) {
					data.point_id = this.point_id
				} else {
					data.sport_types = 1
				}
				if (this._id) data.id = this._id
				if (this.lat) data.lat = this.lat
				if (this.lng) data.lng = this.lng
				if (this.memo) data.memo = this.memo

				const conf_json = {}
				if (this.pic_list.length) conf_json.pic_list = this.pic_list
				if (this.video_list.length) conf_json.video_list = this.video_list
				if (this.address) conf_json.address = this.address
				if (this.wechat_step) conf_json.wechat_step = this.wechat_step

				let conf_str = JSON.stringify(conf_json)
				conf_str = conf_str.replace(/·/g, '-')
				data.conf_json = base64.encode(conf_str)


				return data
			},

			async clockIn() {
				const data = this.dataCheck()
				if (!data) return false

				uni.showLoading({
					title: this._id ? '修改中...' : '打卡中...',
					mask: true
				})

				const res = await xwy_api.request({
					url: 'front.flat.sport_step.sign_location.userSign/submit_user_point_sign',
					data
				})

				uni.hideLoading()

				if (!res || !res.status) {
					xwy_api.alert(res && res.info || '失败')
					return false
				}

				const info = res.info || '成功'


				uni.showToast({
					title: info,
					icon: info.length <= 7 ? 'success' : 'none'
				})

				const eventChannel = this.getOpenerEventChannel()
				eventChannel.emit('reloadList')

				const timeout = setTimeout(() => {
					uni.navigateBack()
				}, 1000)
			},


		}
	}
</script>

<style>
.sign-page {
	width: 100vw;
	height: 100vh;
	box-sizing: border-box;
}


.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
    line-height: calc((100vw - 50px) / 3);
	margin: 5px;
}

.image-item {
    width: calc(100vw - 20px);
	height: 50vw;
    border-radius: 5px;
}


.image-view {
    position: relative;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}


.top-rank-banner-item {
	padding: 5px;
	position: relative;
}
.top-rank-banner-item image {
	width: calc((100vw - 50px) / 3);
	height: calc((100vw - 50px) / 3);
	border-radius: 5px;
}
.top-rank-banner-item .del-image-item {
	right: 8px;
}

.video-item-view {
	position: relative;
}
.video-item {
	width: 100%;
}
.choose-video {
	width: calc((100vw - 50px) / 3);
	height: calc((100vw - 50px) / 3);
	border-radius: 5px;
	margin: 5px;
	border: 1px solid #eee;
}

.clock-in {
	width: 200px;
	height: 40px;
	border-radius: 20px;
}

</style>
