<template>
    <view class="page bg-background">
        <view v-if="point_sign_data" class="bg-white pt10 pb10 text-center">
            <view class="flex-all-center">
                <view class="point-data-quanquan color-white flex-column flex-all-center"
                      hover-class="navigator-hover" @click="backDetailPage">
                    <view class="font34">
                        {{ point_sign_data.sign_count }} / {{ point_sign_data.all_count }}
                    </view>
                    <view>立即{{ clock_in_text }}</view>
                </view>
            </view>
            <view class="flex-row pt10">
                <view style="width: calc(100vw / 3);">
                    <view class="font18">{{ point_sign_data.sign_count }}</view>
                    <view class="font14 color-sub">已{{ clock_in_text }}</view>
                </view>
                <view style="width: calc(100vw / 3);">
                    <view class="font18">{{ point_sign_data.all_count - point_sign_data.sign_count }}</view>
                    <view class="font14 color-sub">未{{ clock_in_text }}</view>
                </view>
                <view style="width: calc(100vw / 3);">
                    <view class="font18">{{ point_sign_data.all_count }}</view>
                    <view class="font14 color-sub">目标</view>
                </view>
            </view>
        </view>

        <view v-if="sign_day_list.length && sign_day_list.length <= 7" class="bg-white p10">
            <view class="flex-kai text-center pt10 pb10">
                <view v-for="(item, index) in sign_day_list" :key="index">
                    <view v-if="item['have_sign'] === 1" class="sign-day-quanquan bg-primary">
                        <uni-icons type="checkmarkempty" color="#ffffff"/>
                        <!-- <text class="iconfont icon-calendar-1 color-white font24"></text> -->
                    </view>
                    <view v-if="item['have_sign'] === 0" class="sign-day-quanquan bg-background">
                        <!-- <uni-icons type="closeempty" color="#ffffff"/> -->
                        <text class="iconfont color-disabled font24"
                              :class="'icon-calendar-' + (index + 1)"></text>
                    </view>
                    <view class="font14 color-disabled pt5">第{{ index + 1 }}天</view>
                </view>
            </view>
            <view class="flex-all-center p10">
                <view
                    class="bg-dark-primary text-center color-white flex-all-center"
                    style="width: 250px; height: 40px; border-radius: 20px;"
                    hover-class="navigator-hover"
                    @click="backDetailPage"
                >去{{ clock_in_text }}
                </view>
            </view>
        </view>

        <view v-if="sign_day_list.length && sign_day_list.length > 7" class="sign-day-list bg-white">
            <view class="flex-row flex-wrap text-center">
                <view v-for="(item, index) in sign_day_list" :key="index" class="sign-day-item">
                    <view v-if="item['have_sign'] === 1" class="sign-day-quanquan bg-primary">
                        <uni-icons type="checkmarkempty" color="#ffffff"/>
                        <!-- <text class="iconfont icon-calendar-1 color-white font24"></text> -->
                    </view>
                    <view v-if="item['have_sign'] === 0" class="sign-day-quanquan bg-background">
                        <!-- <uni-icons type="closeempty" color="#ffffff"/> -->
                        <text
                            v-if="sign_day_list.length <= 31"
                            class="iconfont color-disabled font24"
                            :class="'icon-calendar-' + (index + 1)"
                        ></text>
                        <text v-else class="color-disabled">{{ index + 1 }}</text>
                    </view>
                    <view class="color-disabled pt5 font12">第{{ index + 1 }}天</view>
                </view>
            </view>
            <view class="flex-all-center p10">
                <view
                    class="bg-dark-primary text-center color-white flex-all-center"
                    style="width: 250px; height: 40px; border-radius: 20px;"
                    hover-class="navigator-hover"
                    @click="backDetailPage"
                >去{{ clock_in_text }}
                </view>
            </view>
        </view>

        <view v-if="!point_sign_data && !sign_day_list.length">
            <uni-calendar
                :insert="true"
                :lunar="true"
                :start-date="start_date"
                :end-date="end_date"
                :selected="calendar_selected"
                @change="calendarChange"
            />

            <view class="flex-all-center p10">
                <view
                    class="bg-dark-primary text-center color-white flex-all-center"
                    style="width: 250px; height: 40px; border-radius: 20px;"
                    hover-class="navigator-hover"
                    @click="backDetailPage"
                >去{{ clock_in_text }}
                </view>
            </view>

            <uni-popup ref="today_sign_popup" @touchmove.stop.prevent="">
                <view class="today-sign-popup bg-white">
                    <view class="close" @click="$refs.today_sign_popup.close()">
                        <uni-icons type="closeempty" size="24" color="#bbbec4"/>
                    </view>
                    <view class="title color-sub text-center pt10">
                        {{ today_sign_list_date }} 共{{ today_sign_list.length }}条{{ clock_in_text }}记录
                    </view>
                    <scroll-view class="list" scroll-y="true" style="height: 70vh;">
                        <view v-for="(item, index) in today_sign_list" :key="index" class="item">
                            <view v-if="item.memo" class="color-title">{{ item.memo }}</view>

                            <view v-if="item.img_video_count"
                                  :class="['img-list flex-row flex-wrap pt5', item.img_list_class]">

                                <image
                                    class="img-item"
                                    lazy-load="true"
                                    v-for="(img_item, img_index) in item.conf_json.pic_list"
                                    :key="img_index"
                                    :src="img_item"
                                    :mode="item.img_video_count === 1 ? 'heightFix' : 'aspectFill'"
                                    @click.stop="previewImage(img_item, item.conf_json.pic_list)"
                                />

                                <video
                                    class="img-item"
                                    :id="'video_' + item.id + '_' + video_index"
                                    v-for="(video_item, video_index) in item.conf_json.video_list"
                                    :key="video_index"
                                    :src="video_item"
                                    @play="videoPlay('video_' + item.id + '_' + video_index)"
                                ></video>
                            </view>


                            <view v-if="item.point_details && item.point_details.name" class="pt5">
                                <view style="display: inline-block;" hover-class="navigator-hover"
                                      @click="toPointDetail(item.point_details)">
                                    <uni-icons type="location" size="16" color="#80848f"/>
                                    <text class="color-content font14">{{ item.point_details.name }}</text>
                                </view>
                            </view>

                            <view v-if="item.conf_json && item.conf_json.wechat_step" class="pt5">
                                <text class="iconfont icon-walk color-sub pr5"></text>
                                <text :class="[item.conf_json.wechat_step >= 10000 ? 'color-warning' : 'color-success']">
                                    {{ item.conf_json.wechat_step }}
                                </text>
                            </view>

                            <view v-if="item.conf_json && item.conf_json.address" class="pt5">
                                <view style="display: inline-block;" hover-class="navigator-hover"
                                      @click="openLocation(item)">
                                    <uni-icons type="location" size="16" color="#80848f"/>
                                    <text class="color-content font14">{{ item.conf_json.address }}</text>
                                </view>
                            </view>

                            <view v-if="!item.checked" class="pt5">
                                <uni-tag text="待审核" type="default" size="mini" :inverted="true"/>
                            </view>
                            <view class="flex-kai pt5">
                                <view class="color-sub font14">{{ item.create_time }}</view>
                                <view v-if="!records_not_edit" hover-class="navigator-hover"
                                      @click="edit(item)">
                                    <text class="iconfont color-sub icon-edit font14"></text>
                                    <text class="color-sub font14" style="padding-left: 2px;">修改</text>
                                </view>
                            </view>
                        </view>
                    </scroll-view>
                </view>
            </uni-popup>
        </view>

        <xwy-ad :activity_id="id" :ad_type="3"></xwy-ad>
        <xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>

        <view v-if="list.length" class="text-center p10 color-sub font14">
            共{{ list.length }}条{{ clock_in_text }}记录
        </view>


        <view class="list">
            <view v-for="(item, index) in list" :key="index">
                <view class="item bg-white">
                    <view v-if="item.memo" class="color-title">{{ item.memo }}</view>

                    <view v-if="item.img_video_count"
                          :class="['img-list flex-row flex-wrap pt5', item.img_list_class]">

                        <image
                            class="img-item"
                            lazy-load="true"
                            v-for="(img_item, img_index) in item.conf_json.pic_list"
                            :key="img_index"
                            :src="img_item"
                            :mode="item.img_video_count === 1 ? 'heightFix' : 'aspectFill'"
                            @click.stop="previewImage(img_item, item.conf_json.pic_list)"
                        />

                        <video
                            class="img-item"
                            :id="'video_' + item.id + '_' + video_index"
                            v-for="(video_item, video_index) in item.conf_json.video_list"
                            :key="video_index"
                            :src="video_item"
                            @play="videoPlay('video_' + item.id + '_' + video_index)"
                        ></video>
                    </view>


                    <view v-if="item.point_details && item.point_details.name" class="pt5">
                        <view style="display: inline-block;" hover-class="navigator-hover"
                              @click="toPointDetail(item.point_details)">
                            <uni-icons type="location" size="16" color="#80848f"/>
                            <text class="color-content font14">{{ item.point_details.name }}</text>
                        </view>
                    </view>

                    <view v-if="item.conf_json && item.conf_json.wechat_step" class="pt5">
                        <text class="iconfont icon-walk color-sub pr5"></text>
                        <text :class="[item.conf_json.wechat_step >= 10000 ? 'color-warning' : 'color-success']">
                            {{ item.conf_json.wechat_step }}
                        </text>
                    </view>

                    <view v-if="item.conf_json && item.conf_json.address" class="pt5">
                        <view style="display: inline-block;" hover-class="navigator-hover"
                              @click="openLocation(item)">
                            <uni-icons type="location" size="16" color="#80848f"/>
                            <text class="color-content font14">{{ item.conf_json.address }}</text>
                        </view>
                    </view>

                    <view v-if="!item.checked" class="pt5">
                        <uni-tag text="待审核" type="default" size="mini" :inverted="true"/>
                    </view>
                    <view class="flex-kai pt5">
                        <view class="color-sub font14">{{ item.create_time }}</view>
                        <view v-if="!records_not_edit" hover-class="navigator-hover" @click="edit(item)">
                            <text class="iconfont color-sub icon-edit font14"></text>
                            <text class="color-sub font14" style="padding-left: 2px;">修改</text>
                        </view>
                    </view>
                </view>

                <view v-if="index !== 0 && ((index + 1) % 10 === 0)" style="padding-bottom: 20px;">
                    <xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>
                </view>
            </view>
        </view>


        <view v-if="!loading && !list.length" class="text-center" style="padding-top: 15vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub font14">暂无{{ clock_in_text }}记录</view>

            <view class="flex-all-center pt15">
                <navigator open-type="navigateBack" class="back-detail color-white bg-primary">
                    去{{ clock_in_text }}
                </navigator>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()

export default {
    data() {
        return {
            id: '',
            loading: true,
            list: [],
            records_not_edit: 0,
            point_sign_data: null,
            sign_day_list: [],
            calendar_selected: [],
            today_sign_list: [],
            today_sign_list_date: '',
            start_date: "2020-01-01",
            end_date: this._utils.getDay(),
            clock_in_text: '打卡'
        }
    },

    onLoad(e) {
        this.$uni.showLoading('加载中...')

        this.id = e.id
        if (e.userid) this.userid = e.userid

        this.$login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getActivityDetail()
        })
    },

    methods: {

        async getActivityDetail() {
            let activity_detail = app.globalData['activity_detail']

            if (!activity_detail) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {active_id: this.id}
                })

                activity_detail = res.data['active_details']
            }

            this.activity_detail = activity_detail
            if (activity_detail?.types === 21) {
                this.clock_in_text = '集卡'
                this.$uni.setNavigationBarTitle('集卡记录')
            }

            this.records_not_edit = activity_detail.conf.active.records_not_edit || 0

            await this.topShowHandle()
            await this.getList()
        },


        // 通过活动的设置来判断页面顶部显示什么模块
        // 设置了需要点位的，并且每个点位只能打一次卡的，圆圈显示打了几个点，还有几个点没打
        // 小于等于7天的，像签到一样一行显示，显示第几天打了，第几天没打
        // 7天以上的，日历显示哪天打了卡，点击某一天弹窗显示那一天的打卡记录
        // 7天以上的，也可以选择把所有天数显示出来(第1天、第二天...)，并显示哪一天已打卡
        async topShowHandle() {
            const active = this.activity_detail.conf.active
            if (!active.point_hide && active.sign_times_type === 1) {
                // 需要点位, 每个点位只能打一次卡
                return await this.getPointList()
            }

            const {begin_time, end_time} = this.activity_detail
            const activity_cycle = this._utils.dateDiff(begin_time * 1000, end_time * 1000)

            if (activity_cycle <= 7 || active.sign_record_top_type === 2) {
                this.sign_day_list = await this.getSignDayList()
                return
            }

            await this.setCalendarSelected()
        },

        async getPointList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/user_sign_light_map_point',
                data: {active_id: this.id}
            })
            const list = res?.data?.['sign_light_res'] || []
            const all_count = list.length
            const sign_count = list.filter(v => v.have_sign_light === 1).length
            this.point_sign_data = {all_count, sign_count}
        },

        async setCalendarSelected() {
            const list = await this.getSignDayList()
            const calendar_selected = []
            list.forEach(v => {
                if (v['have_sign'] === 1) {
                    calendar_selected.push({
                        date: v.date,
                        info: '已' + this.clock_in_text
                    })
                }
            })
            const {begin_time, end_time} = this.activity_detail
            this.start_date = this._utils.timestampToTime(begin_time)
            this.end_date = this._utils.timestampToTime(end_time)
            this.calendar_selected = calendar_selected
        },

        calendarChange(e) {
            const date = e.fulldate
            if (this.today_sign_list_date === date) {
                this.$refs.today_sign_popup.open()
                return
            }
            const list = this.list
            const today_list = list.filter(v => v.create_time.substr(0, 10) === date)
            if (today_list.length) {
                this.today_sign_list = today_list
                this.today_sign_list_date = date
                this.$refs.today_sign_popup.open()
            }
        },

        async getSignDayList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/period_sign_list',
                data: {active_id: this.id}
            })
            return res?.data?.['sgin_list'] || []
        },

        async getList() {
            const data = {active_id: this.id}
            if (this.userid) data.userid = this.userid

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/user_have_sign_point_list',
                data
            })

            this.loading = false
            uni.hideLoading()

            if (res?.data?.['point_sign_list']?.length) {
                const list = res.data['point_sign_list']

                this.list = list.map(item => {
                    const pic_length = item.conf_json?.pic_list?.length || 0
                    const video_length = item.conf_json?.video_list?.length || 0
                    const img_video_count = pic_length + video_length
                    return {
                        ...item,
                        img_video_count,
                        img_list_class: `img-list-${img_video_count > 2 ? 'more' : img_video_count}`
                    }
                })
            }
        },

        toPointDetail(point_detail) {
            this.$uni.navigateTo(`./point_detail?id=${this.id}&point_id=${point_detail.id}`, {
                events: {
                    reloadList: () => this.getPointList()
                }
            })
        },

        openLocation(item) {
            if (!item.lat || !item.lng) return false
            uni.openLocation({
                latitude: Number(item.lat),
                longitude: Number(item.lng),
                address: item.conf_json.address
            })
        },

        async edit(item) {
            app.globalData.sign_record_edit_data = item

            if (this.activity_detail.conf.active.not_check) {
                return this.$uni.navigateTo('./sign_record_edit', {
                    events: {
                        reloadList: () => {
                            this.getList()
                        }
                    }
                })
            }

            const res = await this.$uni.showModal(`记录修改后将重置为待审核状态，需要管理员重新审核才能显示在${this.clock_in_text}广场。是否继续修改?`, {
                showCancel: true,
                confirmText: '继续修改'
            })

            if (!res.confirm) return

            app.globalData.sign_record_edit_data = item

            this.uni.navigateTo('./sign_record_edit', {
                events: {
                    reloadList: () => {
                        this.getList()
                    }
                }
            })
        },

        videoPlay(id) {
            this.videoContext = this.videoContext || null
            if (this.previous_video_id) {
                if (id === this.previous_video_id) return false
                this.videoContext = uni.createVideoContext(this.previous_video_id)
                this.videoContext.pause()
            }
            this.previous_video_id = id
        },

        previewImage(src, list = [src]) {
            this.$uni.previewImage({
                urls: list,
                current: src
            })
        },

        backDetailPage() {
            this.$uni.navigateBackPage(`pages/clock_in/user/detail?id=${this.id}`)
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
}

$quanquan-color: #dc693f;
.point-data-quanquan {
    background-color: $quanquan-color;
    width: 150px;
    height: 150px;
    border-radius: 50%;
    box-shadow: 0 0 10px $quanquan-color;
}

$sign-day-size: 80rpx;
.sign-day-quanquan {
    width: $sign-day-size;
    height: $sign-day-size;
    line-height: $sign-day-size;
    border-radius: 50%;
    margin: 0 auto;

    .uni-icons {
        font-size: 48rpx !important;
    }
}

.sign-day-list {
    padding: 25rpx;

    .sign-day-item {
        width: 100rpx;
        padding-bottom: 10px;
    }
}

.today-sign-popup {
    border-radius: 10px;
    width: 90vw;
    position: relative;

    .close {
        position: absolute;
        top: 0;
        right: 0;
        padding: 10px;
    }

    .list {
        box-sizing: border-box;

        .item {
            border-bottom: 1px solid #eee;
        }
    }
}

.list {
    padding: 10px;
}

.item {
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.img-list {
    position: relative;
    left: -5px;
    padding-top: 5px;
}

.img-item {
    display: block;
    margin-left: 5px;
    margin-bottom: 5px;
    border-radius: 5px;
}

.img-list-1 .img-item {
    height: 150px;
    width: auto;
}

.img-list-2 .img-item {
    width: 120px;
    height: 150px;
}

.img-list-more .img-item {
    width: calc((100vw - 85px) / 3);
    height: calc((100vw - 85px) / 3);
}

.back-detail {
    width: 200px;
    line-height: 40px;
    border-radius: 20px;
}


/* 当日及选中的日期样式修改 */
.uni-calendar-item--isDay.data-v-6097fd5b, .uni-calendar-item--checked.data-v-6097fd5b {
    background-color: #fff !important;
    /* color: #333 !important; */
    opacity: 1 !important;
    /* border-radius: 50%; */
}

.uni-calendar-item--isDay.data-v-6097fd5b .uni-calendar-item__weeks-box-item, .uni-calendar-item--checked.data-v-6097fd5b .uni-calendar-item__weeks-box-item {
    background-color: #5cadff !important;
    /* border: 2px solid #5cadff; */
    /* box-sizing: border-box; */
    /* color: #333 !important; */
    opacity: 1 !important;
    // border-radius: 50%;
}

.uni-calendar-item--isDay.data-v-6097fd5b .uni-calendar-item--isDay, .uni-calendar-item--checked.data-v-6097fd5b .uni-calendar-item--checked {
    background-color: #5cadff !important;
}

/* 日历插入数据的文字样式修改 */
.uni-calendar-item--extra.data-v-6097fd5b {
    color: #2d8cf0 !important;
}

.uni-calendar-item--isDay.data-v-6097fd5b .uni-calendar-item--extra.data-v-6097fd5b, .uni-calendar-item--checked.data-v-6097fd5b .uni-calendar-item--extra.data-v-6097fd5b {
    color: #fff !important;
}

/* 日历插入数据右上角圆点的样式 */
.uni-calendar-item__weeks-box-circle {
    display: none;
}
</style>
