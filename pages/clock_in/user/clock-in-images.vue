<template>
    <view>
        <view class="p10">
            <custom-waterfalls-flow
                ref="waterfallsFlowRef"
                :value="image_list"
                :column="2"
                :columnSpace="1.5"
                imageKey="image"
                @imageClick="imageClick"
                @wapperClick="imageClick"
                :listStyle="{position: 'relative'}"
            >
                <view
                    class="user-info flex-row"
                    v-for="(item,index) in image_list"
                    :key="index"
                    :slot="'slot' + index"
                >
                    <image class="headimg" :src="item.headimg" mode="aspectFill"/>
                    <view class="nickname ellipsis">{{ item.nickname }}</view>
                </view>
            </custom-waterfalls-flow>
        </view>

        <view v-if="loading" class="text-center pb10">
            <view v-if="current_page === 1" style="padding-top: 20vh;"></view>
            <load-ani />
            <view class="color-sub font14 pt10">相册加载中...</view>
        </view>

        <view v-if="!loading && !image_list.length" class="text-center" style="padding-top: 15vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub font14">相册为空</view>
        </view>
    </view>
</template>

<script>

export default {
    data() {
        return {
            loading: true,
            image_list: [],
            current_page: 1,
            is_last_page: false
        }
    },

    onLoad(params) {
        this.id = params.id
        this.getImages()
    },

    onReachBottom() {
        !this.loading && !this.is_last_page && this.getImages()
    },

    onPullDownRefresh() {
        this.current_page = 1
        this.getImages().finally(() => uni.stopPullDownRefresh())
    },

    methods: {

        async getImages() {
            this.loading = true

            if (this.current_page === 1) {
                this.image_list = []
                this.is_last_page = false
            }

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/public_square_sign_list',
                data: {
                    active_id: this.id,
                    checked: 1,
                    page: this.current_page,
                    perpage: 20
                }
            })

            this.loading = false

            const data = res?.data?.['user_sign_list']
            if (!data) {
                this.is_last_page = true
                return
            }
            this.is_last_page = data.is_lastpage
            this.current_page++
            const list = data.data || []
            this.handleList(list)
        },

        handleList(list) {
            if (!list?.length) return
            const image_list = []
            list.forEach(item => {
                const pic_list = item.conf_json?.pic_list || []
                pic_list.length && pic_list.forEach(v => {
                    image_list.push({
                        id: item.id,
                        image: v,
                        nickname: item['user_attend_details']?.must_submit?.[0]?.value,
                        headimg: item['user_attend_details']?.headimg
                    })
                })
            })
            this.image_list = this.image_list.concat(image_list)
            if (this.current_page - 1 === 1) this.$refs.waterfallsFlowRef.refresh()
        },

        imageClick(e) {
            uni.previewImage({
                current: e.index,
                urls: this.image_list.map(item => item.image)
            })
        }
    }
}
</script>

<style lang="scss">
$headimg-size: 30px;
.user-info {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    box-sizing: border-box;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    padding: 5px;
    line-height: $headimg-size;
    border-radius: 5px 5px 0 0;

    .headimg {
        width: $headimg-size;
        height: $headimg-size;
        margin-right: 5px;
        border-radius: 50%;
        display: block;
    }

    .nickname {
        width: calc(100% - #{$headimg-size + 5px});
    }
}
</style>
