<template>
    <view>
        <view>

            <image class="preview-image" :src="signature_path || signature_src || bg_src" mode="widthFix"/>
        </view>

        <canvas class="canvas" id="canvas" canvas-id="canvas" :width="canvasWidth" :height="canvasHeight"
                :style="{width: canvasWidth + 'px', height: canvasHeight + 'px'}"></canvas>

        <view class="bottom-bar flex-kai bg-white">
            <navigator class="small-button bg-disabled" open-type="navigateBack">返回</navigator>
            <view class="big-button bg-light-primary" @click="toSignature">签名</view>
            <view class="small-button bg-success" @click="complete">完成</view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            // 活动设置的签名图
            bg_src: '',
            // 用户报名信息里面的合成的签名图
            signature_src: '',
            // 用户本地合成的签名图
            signature_path: '',
            canvasWidth: 0,
            canvasHeight: 0
        }
    },

    onLoad() {
        this.getData()
    },

    methods: {
        async getData() {
            this.$uni.showLoading()

            this.getOpenerEventChannel().once('data', data => {
                this.active_id = data.active_id
                if (data.title) this.$uni.setNavigationBarTitle(data.title)
                if (data.signature_src) this.signature_src = data.signature_src
                this.bg_src = data.bg
                this.signatureArea = {
                    x: data.x,
                    y: data.y,
                    width: data.width,
                    height: data.height,
                    color: data.color,
                    size: data.size
                }

                this.init()
            })
        },

        async init() {
            await this.getBgInfo()
            this.ctx = uni.createCanvasContext('canvas')
            uni.hideLoading()
        },



        async getBgInfo() {
            const info = await this.$uni.getImageInfo(this.bg_src)
            this.canvasWidth = info.width
            this.canvasHeight = info.height
            this.bg_path = info.path
        },


        toSignature() {
            const {color, size} = this.signatureArea
            this.$uni.navigateTo(`/pages/other/lime-signature/lime-signature?color=${color}&size=${size}`, {
                events: {
                    complete: path => this.getSignatureData(path)
                }
            })
        },

        async getSignatureData(path) {
            this.$uni.showLoading()
            const info = await this.$uni.getImageInfo(path)
            const {width: img_width, height: img_height} = info
            const {x, y,width:  area_width, height: area_height} = this.signatureArea

            let width = area_width
            let s = img_width / width
            let height = img_height / s
            if (height > area_height) {
                s = img_height / area_height
                width = img_width / s
                height = area_height
            }

            this.draw(path, x, y, width, height)
        },


        draw(path, x, y, width, height) {
            this.$uni.showLoading()
            this.ctx.drawImage(this.bg_path, 0, 0, this.canvasWidth, this.canvasHeight)
            this.ctx.drawImage(path, x, y, width, height)
            setTimeout(() => {
                this.$uni.showLoading()
                this.ctx.draw()
                setTimeout(() => this.canvasToTempFilePath(), 1000)
            }, 1000)
        },

        canvasToTempFilePath() {
            this.$uni.showLoading()
            uni.canvasToTempFilePath({
                canvasId: 'canvas',
                success: res => {
                    uni.hideLoading()
                    console.log('绘画的图片', res)
                    this.signature_path = res.tempFilePath
                    this.$uni.showToast('已签名', 'success')
                },
                fail: err => {
                    console.log('绘画失败', err)
                    setTimeout(() => this.canvasToTempFilePath(), 1000)
                }
            }, this)
        },

        async complete() {
            if (!this.signature_path) return this.$uni.show('还未签名')

            this.$uni.showLoading('签名保存中...')
            const res = await this.xwy_api.uploadOneImage({
                temp_data: {
                    path: this.signature_path
                },
                active_id: this.active_id
            })
            uni.hideLoading()

            this.getOpenerEventChannel().emit('handSignature', res)
            this.$uni.navigateBack()
        }
    }
}
</script>

<style lang="scss">
.preview-image {
    width: 100vw;
}

.canvas {
    position: fixed;
    left: -1000vw;
}

.bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 20px;
    box-sizing: border-box;

    .small-button, .big-button {
        text-align: center;
        color: #ffffff;
        line-height: 40px;
        border-radius: 5px;
    }

    .small-button {
        width: 80px;
    }

    .big-button {
        width: 120px;
    }
}
</style>