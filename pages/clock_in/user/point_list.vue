<template>
    <view class="page bg-background" :style="{paddingTop: topSpace + 'px'}">
        <xwy-ad :activity_id="id" :ad_type="3"></xwy-ad>

        <view class="top-container bg-white">
            <template v-if="category_list.length">
                <view v-if="categoryListShowType === 'flex'" class="flex-row">
                    <view v-for="item in category_list" :key="item.id" class="category-item text-center"
                          :class="{'category-active': item.id === category_id}"
                          :style="{width: `calc(100% / ${category_list.length})`}"
                          @click="categoryChange(item.id)">
                        {{ item.name }}
                    </view>
                </view>
                <scroll-view v-else scroll-x="true">
                    <view class="category-scroll flex-row">
                        <view v-for="item in category_list" :key="item.id" class="category-item text-center"
                              :class="{'category-active': item.id === category_id}"
                              @click="categoryChange(item.id)">
                            {{ item.name }}
                        </view>
                    </view>
                </scroll-view>
            </template>

            <view class="point-count flex-kai font14">
                <view class="p10 color-sub">共{{ point_list.length }}个打卡点</view>
                <view class="p10" @click="point_show_type = point_show_type === 1 ? 2 : 1">
                    <uni-icons v-if="point_show_type === 1" type="list" color="#2d8cf0" size="14"/>
                    <text v-if="point_show_type === 2" class="iconfont icon-map-1 color-primary"></text>
                    <text class="color-primary" style="padding-left: 2px;">
                        <template v-if="point_show_type === 1">列表模式</template>
                        <template v-if="point_show_type === 2">地图模式</template>
                    </text>
                    <uni-icons type="forward" color="#2d8cf0" size="14"/>
                </view>
            </view>
       </view>

        <uni-transition
            :mode-class="['fade', 'slide-left']"
            :show="map_show"
            :styles="{position: 'fixed', top: topSpace + 'px', left: 0, zIndex: 99}"
        >
            <view class="map-view" :style="{height: `calc(100vh - ${topSpace}px)`}">

                <map
                    id="map"
                    class="map"
                    :style="{height: `calc(100vh - ${topSpace}px)`}"
                    :longitude="map_data.longitude || 116.39752"
                    :latitude="map_data.latitude || 39.908743"
                    :markers="map_data.markers"
                    :scale="map_data.scale || 10"
                    @callouttap="mapTap($event)"
                    @markertap="mapTap($event)"
                ></map>

                <view class="map-clock-in-button bg-light-primary color-white"
                      hover-class="navigator-hover" @click="toClockIn">
                    去打卡
                </view>
            </view>
        </uni-transition>

        <uni-transition :mode-class="['fade', 'slide-right']" :show="list_show">
            <view class="point-list">
                <view v-for="(item, index) in point_list" :key="index">
                    
                    <view v-if="index === 0" style="padding-bottom: 20px;">
                        <xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>
                    </view>
                    
                    <view class="point-item flex-row bg-white" hover-class="navigator-hover"
                          @click="toPointDetail(item)">
                        <view v-if="item.conf && item.conf.logo" class="pr10">
                            <image class="logo" :src="item.conf.logo" mode="aspectFill"/>
                        </view>
                        <view style="width: 100%;">
                            <view class="color-title">{{ item.name }}</view>
                            <view v-if="item.conf && item.conf.address" class="ellipsis--l2 pt5">
                                <uni-icons type="location" size="14" color="#495060"/>
                                <text class="color-content font14">{{ item.conf.address }}</text>
                            </view>
                            <view class="clear clearfix">
                                <view v-if="team_open && team_person" class="fl font12 pt5">
                                    队伍已打卡人数: {{ item['this_point_sign_person_num'] }}/{{ team_person }}
                                </view>
                                <view class="fr">
                                    <view class="sign-tag" v-if="item.show_sign_tag">

                                        <image v-if="point_complete_badge" :src="point_complete_badge"/>
                                        <text v-else style="font-size: 50px;"
                                              class="iconfont icon-sign-tag color-success"></text>
                                    </view>
                                    <uni-tag v-if="item.show_enter" text="进入打卡" size="mini" type="success" 
                                             :circle="true"></uni-tag>
                                </view>
                            </view>
                        </view>
                    </view>
                    
                    <view v-if="index !== 0 && ((index + 1) % 10 === 0)" style="padding-bottom: 20px;">
                        <xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>
                    </view>
                </view>
            </view>
        </uni-transition>
    </view>
</template>

<script>
const app = getApp()

export default {
    data() {
        return {
            id: '',
            point_show_type: 0,
            map_show: false,
            list_show: false,
            point_list: [],
            map_data: {},
            have_sign_count: 0,
            point_complete_badge: '',
            team_person: 0,
            team_open: false,
            category_list: [],
            category_id: 0,

            // 打卡点位增加字段类型 types  判断
            // 0   是之前的关卡 id及健步走活动点位，打卡活动的打卡点
            // 1 代表是任务闯关活动里的打卡点（闯关里需要进行打卡，打卡的记录）
            point_types: 0
        }
    },
    watch: {
        point_show_type(val) {
            this.map_show = val === 1
            this.list_show = val === 2
        }
    },

    computed: {
        topSpace() {
            return this.category_list.length ? 80 : 40
        },

        categoryListShowType() {
            const list = this.category_list
            const sum = list.reduce((acc, item) => acc + item.name.length, list.length * 2)
            return sum > 25 ? 'scroll' : 'flex'
        }
    },

    onLoad(e) {
        this.$uni.showLoading('加载中...')

        this.id = e.id
        if (e.no_checked) this.no_checked = true  // 没有报名或者没有审核通过

        // 打卡点位增加字段类型 types  判断
        // 0   是之前的关卡 id及健步走活动点位，打卡活动的打卡点
        // 1 代表是任务闯关活动里的打卡点（闯关里需要进行打卡，打卡的记录）
        if (e.point_types) this.point_types = Number(e.point_types)

        this.$login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.init()
        })
    },

    onPullDownRefresh() {
        this.getPointList().finally(() => uni.stopPullDownRefresh())
    },

    methods: {
        async init() {
            await this.getActivityDetail()
            await this.getPointList()
            await this.getPointCategoryList()
        },


        async getPointCategoryList() {
            if (!this.activity_detail?.rank_set?.['mapPointCategorySet']) return

            const res = await this.xwy_api.getCategoryList({
                types: 38,
                page: 1,
                perpage: 100,
                active_id: this.id
            })

            const list = res?.data?.category_list?.data || []
            if (!list?.length) return
            // list.unshift({category_id: 0, name: '全部'})
            this.category_list = list.map(item => ({
                id: item.category_id,
                name: item.name
            }))

            if (this.category_list.length) this.categoryChange(this.category_list[0].id)
        },

        categoryChange(id) {
            if (id === this.category_id) return
            this.point_list = []
            this.map_data = {}
            this.category_id = id

            const list = id ? this.allList.filter(item => item.category_id === id) : this.allList
            this.$nextTick(() => this.pointListDataInit(list))
        },


        async getActivityDetail() {
            let activity_detail = app.globalData['activity_detail']

            if (!activity_detail || activity_detail?.active_id !== this.id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.id
                    }
                })
                activity_detail = res.data.active_details
            }

            const active = activity_detail?.conf?.active || {}

            if (active.point_complete_badge && activity_detail?.rank_set?.['sign_logo_diy']) {
                this.point_complete_badge = active.point_complete_badge
            }

            if (activity_detail.rank_set?.team_group_open) this.team_open = true
            
            this.activity_detail = activity_detail
            this.point_show_type = active.point_show_type || 2
        },


        async getPointList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/user_sign_light_map_point',
                data: {
                    active_id: this.id
                }
            })

            uni.hideLoading()

            if (res?.status === 0) return this.$uni.showModal(res?.info || '打卡点数据获取失败')
            const list_ = res?.data?.sign_light_res || []

            // 打卡点位增加字段类型 types  判断
            // 0   是之前的关卡 id及健步走活动点位，打卡活动的打卡点
            // 1 代表是任务闯关活动里的打卡点（闯关里需要进行打卡，打卡的记录）
            const list = list_.filter(item => item.types === this.point_types)
            
            if (!list?.length) return this.$uni.showModal('该活动暂未设置打卡点')

            this.allList = list
            
            if (this.category_id) {
                const id = this.category_id
                this.category_id = 0
                this.categoryChange(id)
            } else {
                this.pointListDataInit(list)
            }
            
            if (res?.data?.team_details?.['attend_person']) this.team_person = res.data.team_details['attend_person']
        },


        pointListDataInit(list) {
            const map_data = {
                longitude: list?.[0]?.lng || 116.39752,
                latitude: list?.[0]?.lat || 39.908743,
                markers: []
            }


            let have_sign_count = 0
            list.forEach(v => {
                if (v.have_sign_light) {
                    have_sign_count++

                    map_data.longitude = v.lng
                    map_data.latitude = v.lat
                }
                const {show_enter, show_sign_tag} = this.getEnterAndSignTag(v)
                v.show_enter = show_enter
                v.show_sign_tag = show_sign_tag


                const map_item = {
                    id: v.id,
                    latitude: Number(v.lat),
                    longitude: Number(v.lng),
                    width: 20,
                    height: 40,
                    callout: {
                        content: v.name,
                        fontSize: 12,
                        borderRadius: 10,
                        padding: 5,
                        display: 'ALWAYS',
                        textAlign: 'center'
                    }
                }
                if (v.conf?.logo) {
                    map_item.iconPath = v.conf.logo
                    map_item.width = 40
                    map_item.height = 40
                }

                if (v.have_sign_light) {
                    map_item.iconPath = 'http://www.xinweiyun.com/weixin/editor/pics/long_march/red_flag.png'
                    map_item.width = 40
                    map_item.height = 40
                }

                map_data.markers.push(map_item)

            })

            const {map_center_point, scale} = this.activity_detail.conf.active
            if (map_center_point && map_center_point.coordinates && map_center_point.coordinates.length) {
                map_data.longitude = map_center_point.coordinates[0]
                map_data.latitude = map_center_point.coordinates[1]
            }
            if (scale) map_data.scale = scale

            this.have_sign_count = have_sign_count
            this.point_list = list
            this.map_data = map_data
        },

        getEnterAndSignTag(v) {
            // 看企业微信2023-11-29 上午11:00 开始的聊天记录

            let show_enter = false,
                show_sign_tag = false

            if (this.no_checked) return {show_enter, show_sign_tag}

            // {value: 0, title: '不限制打卡次数'},
            // 不管打没打卡，不显示“已打卡”的戳，都显示“进入打卡”

            // {value: 1, title: '只能打卡一次'},
            // 点位有打过卡的显示“已打卡”的戳，没有打过的显示“进入打卡”

            // {value: 2, title: '每天只能打卡一次'}
            // 点位最后打卡时间 ，是今天的显示“已打卡”的戳，不是今天或者没打过卡的显示“进入打卡”

            const sign_times_type = this.activity_detail.conf.active.sign_times_type || 0

            if (sign_times_type === 0) {
                show_enter = true
                show_sign_tag = false
            }

            if (sign_times_type === 1) {
                show_enter = !v.have_sign_light
                show_sign_tag = v.have_sign_light
            }

            if (sign_times_type === 2) {
                const last_sign_time = v['user_sign_details']?.create_time
                const last_time_is_today = last_sign_time && last_sign_time.split(' ')[0] === this._utils.getDay(0, true)
                show_enter = !last_time_is_today
                show_sign_tag = last_time_is_today
            }

            return {show_enter, show_sign_tag}
        },


        mapTap(e) {
            const id = e.detail.markerId
            const point_detail = this.point_list.find(v => v.id === id)
            this.toPointDetail(point_detail)
        },

        toClockIn() {
            const point = this.point_list.find(item => !item.have_sign_light) || this.point_list[0]
            this.toPointDetail(point)
        },

        toPointDetail(item) {
            this.$uni.navigateTo(`./point_detail?id=${this.id}&point_id=${item.id}`, {
                success: res => {
                    if (this.team_open && this.activity_detail?.conf?.active?.must_team_person_finish_clock_in) {
                        res.eventChannel.emit('teamSignData', {
                            team_person: this.team_person,
                            point_list: this.point_list.map(v => ({
                                id: v.id,
                                this_point_sign_person_num: v.this_point_sign_person_num,
                            }))
                        })
                    }
                },
                events: {
                    reloadList: () => {
                        this.getPointList()
                        this.getOpenerEventChannel?.()?.emit?.('updateData')
                    }
                }
            })
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
}

.top-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    z-index: 999;

    .point-count {
        height: 40px;
    }
}

.category-item {
    line-height: 40px;
    color: #495060;
    font-size: 14px;
    display: inline-block;
    white-space: nowrap;
}

.category-scroll .category-item {
    padding: 0 10px;
}

.category-active {
    color: #5cadff;
}

.map-view {
    width: 100vw;
    z-index: 9;

    .map {
        width: 100vw;
    }

    .map-clock-in-button {
        position: absolute;
        bottom: 40px;
        left: 50%;
        margin-left: -100px;
        width: 200px;
        line-height: 44px;
        border-radius: 22px;
        text-align: center;
    }
}

.point-list {
    padding: 20px 10px;

    .point-item {
        margin-bottom: 20px;
        border-radius: 10px;
        padding: 10px;
        position: relative;

        .logo {
            width: 130px;
            min-width: 130px;
            height: 80px;
            border-radius: 10px;
            display: block;
        }

        .sign-tag image {
            width: 50px;
            height: 50px;
            display: block;
        }
    }
}
</style>
