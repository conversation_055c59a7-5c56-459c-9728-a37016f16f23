<template>
    <view class="page bg-background">

        <screen-picture
            v-if="screen_pic_show"
            :hide="screen_pic_hide"
            :image-src="screen_pic"
            :time="screen_pic_count_down"
            :show-button="screen_pic_button_show"
            @skipScreen="skipScreen"
        />


        <activity-delete-tips v-if="error" :activeId="id"/>

        <view v-if="detail && detail.active_id">
            <template v-if="detail.rank_set && detail.rank_set['barrage']">
                <lff-barrage ref="lffBarrage" :activeid="id"></lff-barrage>
            </template>

            <view v-if="detail.conf && detail.conf.active && detail.conf.active.audio_src"
                  class="audio flex-all-center" :class="{'audio_rotate': audio_play}" @click="audioControl">
                <text class="iconfont icon-background-music-play font24 color-white"></text>
            </view>


            <activity-logo-title-time :details="detail" :hide-share="hideShare"/>


            <view class="icon-list flex-row flex-wrap text-center bdb-10 bg-white">

                <navigator v-if="is_my_activity" class="icon-item"
                           :url="'../admin/activity/manage?id=' + id">
                    <text class="iconfont font24 color-primary icon-setting"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">活动管理</view>
                </navigator>
                <view v-if="!is_joining" class="icon-item" @click="joinActivity(false)">
                    <text class="iconfont font24 color-primary icon-users"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">参与活动</view>
                </view>

                <view
                    v-if="is_joining && detail.rank_set && detail.rank_set['scan_need_books'] && !books_date"
                    class="icon-item" @click="joinActivity(true)">
                    <text class="iconfont font24 color-primary" :class="'icon-calendar-' + this_day"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">选择报名日期</view>
                </view>

                <view v-if="is_joining" class="icon-item" @click="uniPopupOpen('my_info')">
                    <text class="iconfont font24 color-primary icon-personal-data"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">报名信息</view>
                </view>


                <navigator v-if="is_joining && checked && detail.rank_set && detail.rank_set['diy_books']"
                           class="icon-item" :url="`/pages/activity/other/certificate?id=${id}&finished_time=1&active_types=${detail.types}`">
                    <text class="iconfont font24 color-primary icon-certificate"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">我的证书</view>
                </navigator>


                <view
                    v-if="!detail.conf.active.activity_rules_hide || detail.content || (detail.conf.active.news && detail.conf.active.news.news_id)"
                    class="icon-item" @click="uniPopupOpen('activity_detail')">
                    <text class="iconfont font24 color-primary icon-feedback"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">活动说明</view>
                </view>

                <template v-if="!detail.rank_set || !detail.rank_set['signOnlyExam']">

                    <navigator v-if="is_joining && (detail.conf.active.enter_types !== 3 || checked === 1)"
                               class="icon-item" :url="'./sign_record_list?id=' + id">
                        <text class="iconfont font24 color-primary icon-sign-in-calendar"></text>
                        <view class="color-sub font14" style="padding-top: 3px;">
                            {{ clock_in_text }}记录
                        </view>
                    </navigator>

                    <view v-if="!hideSquare" class="icon-item" hover-class="navigator-hover"
                          @click="lookPublicSignList">
                        <text class="iconfont font24 color-primary icon-dating"></text>
                        <view class="color-sub font14" style="padding-top: 3px;">
                            {{ clock_in_text }}广场
                        </view>
                    </view>

                    <view v-if="detail.types === 5 && detail.conf.active.pic_list_required === 1"
                          class="icon-item" hover-class="navigator-hover" @click="lookImages">
                        <uni-icons type="images" size="24" color="#2d8cf0"/>
                        <view class="color-sub font14" style="padding-top: 3px;">打卡相册</view>
                    </view>
                </template>

                <view v-if="!detail.rank_set || !detail.rank_set['closed_top_rank']" class="icon-item"
                      hover-class="navigator-hover" @click="toTopList()">
                    <text class="iconfont font24 color-primary icon-trophy"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">排行榜</view>
                </view>

                <view v-if="detail.rank_set && detail.rank_set['gift_goods'] && !detail.conf.active.gift_goods_hide"
                      class="icon-item" @click="toShop">
                    <uni-icons type="shop" size="24" color="#2d8cf0"/>
                    <view class="color-sub font14" style="padding-top: 3px;">{{ integralUnit }}商城</view>
                </view>

                <template v-if="!detail.rank_set || !detail.rank_set['signOnlyExam']">
                    <view v-if="!detail.conf.active.point_hide && detail.types !== 21" class="icon-item"
                          hover-class="navigator-hover" @click="toPointList">
                        <text class="iconfont font24 color-primary icon-map"></text>
                        <view class="color-sub font14" style="padding-top: 3px;">打卡点</view>
                    </view>
                </template>

                <view v-if="sign_num_lottery_show" class="icon-item" hover-class="navigator-hover"
                      @click="toLottery">
                    <text class="iconfont font24 color-primary icon-prize-draw"></text>
                    <view class="color-sub font14 pt3">抽奖</view>
                </view>


                <navigator v-if="detail.rank_set && detail.rank_set['lottery_open']" class="icon-item"
                           :url="'/pages/lottery/user/lottery_record_list?active_id=' + id">
                    <text class="iconfont font24 color-primary icon-gift"></text>
                    <view class="color-sub font14 pt3">抽奖记录</view>
                </navigator>


                <view v-if="dailyCheckInOpen" class="icon-item" hover-class="navigator-hover"
                      @click="toDailyCheckIn">
                    <text class="iconfont font24 color-primary icon-sign-in"></text>
                    <view class="color-sub font14 pt3">每日签到</view>
                </view>

                <template v-if="headimg_plugin && headimg_plugin.length">
                    <view v-for="(item, index) in headimg_plugin" :key="index" class="icon-item"
                          @click="toIdeaAvatar(item)">

                        <image v-if="item.key === 'chajian'" style="width: 24px; height: 24px;"
                               :src="item.icon || 'https://apissl.xinweiyun.com/weixin/public/img】longmarch/icon_guajian.png'"/>

                        <image v-if="item.key === 'beijing'" style="width: 24px; height: 24px;"
                               :src="item.icon || 'https://apissl.xinweiyun.com/weixin/public/img/longmarch/icon_beijing.png'"/>
                        <view class="color-sub font14" style="padding-top: 1px;">{{ item.name }}</view>
                    </view>
                </template>


                <view v-if="detail.rank_set && detail.rank_set['barrage']" class="icon-item"
                      @click="toBulletScreen()">
                    <uni-icons type="chatboxes" size="24" color="#2d8cf0"/>
                    <view class="color-sub font14" style="padding-top: 3px;">活动弹幕</view>
                </view>


                <template v-if="detail.conf.active.detail_icon_list">

                    <navigator v-for="(item, index) in detail.conf.active.detail_icon_list"
                               :key="index" class="icon-item"
                               :url="item.type === 0 ? ('/pages/news/preview?id=' + item.id) : ('/pages/news/list?category_id=' + item.id)">
                        <text :class="'iconfont font24 color-primary ' + item.icon"></text>
                        <view class="color-sub font14" style="padding-top: 3px;">{{ item.title }}</view>
                    </navigator>
                </template>


                <navigator v-if="is_joining && detail.rank_set && detail.rank_set['share_friends']"
                           class="icon-item" :url="`/pages/other/friend_list?active_id=${id}&show_share=1`">
                    <text class="iconfont font24 color-primary icon-team"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">邀请新用户</view>
                </navigator>


                <navigator v-if="!detail.conf.active.close_feedback"
                           class="icon-item" :url="'/pages/activity/feedback/send?activity_id=' + id">
                    <text class="iconfont font24 color-primary icon-chat-bubble"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">意见反馈</view>
                </navigator>

                <view v-if="!hideShare" class="icon-item" @click="showActiveSharePopup">
                    <text class="iconfont font24 color-primary icon-share"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">分享</view>
                </view>
            </view>


            <template>
                <template v-if="detail.rank_set && detail.rank_set['signOnlyExam']">
                    <view v-if="!is_joining" class="p10 bdb-10 bg-white">
                        <view class="join-container">
                            <view class="flex-all-center">
                                <view class="join-btn bg-primary color-white" @click="joinActivity(false)">
                                    参与活动
                                </view>
                            </view>
                        </view>
                    </view>
                </template>

                <view v-else class="p10 bdb-10 bg-white">
                    <view v-if="detail.conf.active.enter_types === 3 && is_joining && checked === 0"
                          class="join-container">
                        <view class="pb5 color-sub text-center font14">
                            需要管理员审核通过后才能{{ clock_in_text }}
                        </view>
                        <view class="flex-all-center">
                            <view class="join-btn bg-background color-sub">去{{ clock_in_text }}</view>
                        </view>
                    </view>

                    <view
                        v-else-if="is_joining && detail.rank_set && detail.rank_set['scan_need_books'] && !books_date"
                        class="join-container">
                        <view class="flex-all-center">
                            <view class="join-btn bg-primary color-white" @click="joinActivity(true)">
                                选择报名日期
                            </view>
                        </view>
                    </view>

                    <view v-else class="join-container">
                        <!--雷子客户活动5a56536807e0970200f7954a6a0e11cd这个活动不用显示今日步数-->
                        <view v-if="checked && today_step !== null && stepShowOpen && id !== '5a56536807e0970200f7954a6a0e11cd'"
                              class="flex-all-center pb10 font14">
                            <view class="color-sub pr10" @click="getWeRunData(true)">
                                <text class="iconfont icon-walk color-sub font14"></text>
                                <text class="plr5">今日行走 {{ today_step || 0 }}步</text>
                                <text class="iconfont icon-sync color-sub font14"></text>
                            </view>

                            <view class="color-success pl10">
                                总{{ integralUnit }}: {{ user_details.integral_all || 0 }}
                            </view>
                        </view>
                        <view class="flex-all-center">
                            <!--没有报名的时候显示用户报名按钮。给鲜繁开发打卡点分类、步数兑换积分的时候口头说的。上面icon图标已经有打卡点的入口了 ==== 2025-02-21 16:35:57-->

                            <button :disabled="sign_btn_disabled" class="join-btn bg-primary color-white"
                                    :class="sign_btn_text.length > 6 ? 'font16' : 'font18'"
                                    @click="toSign">{{ is_joining ? sign_btn_text : '参与活动' }}
                            </button>
                        </view>
                    </view>
                </view>
            </template>


            <xwy-ad
                v-if="!password_dialog_show && !join_popup_show && !popup_open && (!detail.rank_set || !detail.rank_set.closed_AD)"
                :ad_type="66">
            </xwy-ad>


            <view class="bg-white" style="position: fixed; left: 0; bottom: 0; width: 100%; z-index: 99;">
                <view v-if="technology_support" class="flex-all-center">

                    <navigator v-if="technology_support.news_id"
                               :url="'/pages/news/preview?id=' + technology_support.news_id + '&type=support'"
                               class="text-center font14 color-sub p10">
                        {{ technology_support.button_text }}
                    </navigator>
                    <view v-else class="text-center font14 color-sub p10">
                        {{ technology_support.button_text }}
                    </view>
                </view>

                <view v-if="!detail.rank_set || !detail.rank_set['closed_user_center']"
                      class="flex-all-center pb10">
                    <navigator url="/pages/user/user" class="p5 color-sub font14">个人中心</navigator>
                </view>
            </view>

            <xwy-ad v-if="(!detail.rank_set || !detail.rank_set.closed_AD) && !screen_pic_show"
                    :ad_type="3">
            </xwy-ad>


            <template v-if="(!detail.rank_set || !detail.rank_set['signOnlyExam']) && !hideSquare">
                <view v-if="list_total" class="text-center p10 color-sub font14">
                    共{{ list_total }}条{{ clock_in_text }}记录
                </view>

                <view id="public-sign-list" class="list">
                    <view v-for="(item, index) in list" :key="index">
                        <view class="item bg-white">
                            <view class="flex-row">
                                <view class="pr10" @click="lookUserRecord(item)">
                                    <image class="item-headimg" mode="aspectFill"
                                           :src="item['user_attend_details'] && item['user_attend_details'].headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"/>
                                </view>
                                <view style="width: 100%;">
                                    <view
                                        v-if="item['user_attend_details'] && item['user_attend_details'].must_submit && item['user_attend_details'].must_submit.length"
                                        class="color-title" @click="lookUserRecord(item)">
                                        {{ item['user_attend_details'].must_submit[0].value }}
                                    </view>
                                    <view v-if="item.memo" class="color-content pt5">{{ item.memo }}</view>

                                    <view v-if="item.img_video_count"
                                          class="img-list flex-row flex-wrap pt5"
                                          :class="item.img_video_count >= 3 ? 'img-list-more' : `img-list-${item.img_video_count}`">

                                        <image
                                            class="img-item"
                                            :lazy-load="true"
                                            v-for="(img_item, img_index) in item.conf_json.pic_list"
                                            :key="img_index"
                                            :src="img_item"
                                            :mode="item.img_video_count === 1 ? 'heightFix' : 'aspectFill'"
                                            @click.stop="previewImage(img_item, item.conf_json.pic_list)"
                                        />

                                        <video
                                            class="img-item"
                                            :id="'video_' + item.id + '_' + video_index"
                                            v-for="(video_item, video_index) in item.conf_json.video_list"
                                            :key="video_index"
                                            :src="video_item"
                                            @play="videoPlay('video_' + item.id + '_' + video_index)"
                                        ></video>
                                    </view>

                                    <view v-if="item.point_details && item.point_details.name" class="pt5">
                                        <view hover-class="navigator-hover" style="display: inline-block;"
                                              @click="toPointDetail(item.point_details)">
                                            <uni-icons type="location" size="16" color="#80848f"/>
                                            <text class="color-content font14">
                                                {{ item.point_details.name }}
                                            </text>
                                        </view>
                                    </view>

                                    <view v-if="item.conf_json && item.conf_json.wechat_step" class="pt5">
                                        <text class="iconfont icon-walk color-sub pr5"></text>
                                        <text :class="item.conf_json.wechat_step >= 10000 ? 'color-warning' : 'color-success'">
                                            {{ item.conf_json.wechat_step }}
                                        </text>
                                    </view>

                                    <view v-if="item.conf_json && item.conf_json.address" class="pt5">
                                        <view hover-class="navigator-hover" style="display: inline-block;"
                                              @click="openLocation(item)">
                                            <uni-icons type="location" size="16" color="#80848f">
                                            </uni-icons>
                                            <text class="color-content font14">
                                                {{ item.conf_json.address }}
                                            </text>
                                        </view>
                                    </view>


                                    <view class="flex-kai">
                                        <view class="color-sub font14 pt5">{{ item.create_time }}</view>
                                        <view v-if="!detail.conf.active.closed_likes" @click="like(index)">
                                            <text class="iconfont icon-love color-red"
                                                  style="position: relative; top: 1px; left: 2px;"></text>
                                            <text class="pl5 color-sub font14">{{ item.agree || 0 }}</text>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                        <view v-if="(!detail.rank_set || !detail.rank_set.closed_AD) && !screen_pic_show && (index + 1) % 10 === 0"
                              style="padding-bottom: 20px;">
                            <xwy-ad :ad_type="66"></xwy-ad>
                        </view>
                    </view>
                </view>


                <uni-load-more v-if="list_loading && load_page !== 1" status="loading"></uni-load-more>
                <uni-load-more v-if="is_last_page && list.length > 5" status="noMore"
                               :contentText="{contentnomore: '我是有底线的'}"></uni-load-more>
                <uni-load-more v-if="!is_last_page && !list_loading" status="more"></uni-load-more>

                <view v-if="!list_loading && !list.length" class="text-center" style="padding-top: 15vh;">
                    <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
                    <view class="color-sub font14">暂无{{ clock_in_text }}记录</view>
                </view>
            </template>


        </view>

        <view v-if="join_popup_show" class="join-popup flex-all-center bg-white" @touchmove.stop.prevent="">
            <view>
                <view class="join-popup-c bg-white">
                    <view class="text-center font18 color-content p10">
                        <template v-if="update_attend_details">修改报名信息</template>
                        <template v-else-if="is_set_book_date">选择报名日期</template>
                        <template v-else>参加活动</template>
                    </view>

                    <template v-if="!is_set_book_date">
                        <template v-if="must_submit.length">
                            <view v-for="(item, index) in must_submit" :key="index">

                                <input
                                    v-if="item.types === 1 || item.types === 3"
                                    class="join-input"
                                    :value="item.value"
                                    :placeholder="'请输入' + item.title + (item.rules === 1 ? ' (必填)' : '')"
                                    @blur="mustTextValueChange($event, index)"
                                />
                                <picker v-if="item.types === 2" :range="item.options" range-key="text"
                                        @change="mustValueChange($event, index)">
                                    <view class="join-input flex-kai">
                                        <view v-if="!item.value" class="color-sub">
                                            请选择{{ item.title }}{{ item.rules === 1 ? ' (必选)' : '' }}
                                        </view>
                                        <view v-if="item.value">{{ item.value }}</view>
                                        <text class="iconfont icon-more color-disabled font18"/>
                                    </view>
                                </picker>
                                <view v-if="item.types === 4 && detail.rank_set && detail.rank_set['attend_sign_pic']"
                                      class="join-input flex-kai" @click="toHandSignature(index)">
                                    <view>
                                        <text v-if="item.value">已签名</text>
                                        <text v-else class="color-sub">
                                            请签名{{ item.rules === 1 ? ' (必选)' : '' }}
                                        </text>
                                    </view>

                                    <text class="iconfont icon-more color-disabled font18"/>
                                </view>
                            </view>
                        </template>


                        <navigator
                            v-if="detail.rank_set && detail.rank_set.team_group_open && (!is_joining || (is_joining && !user_details.team_id))"
                            :url="'/pages/activity/admin/team_list?selteam=1&id=' + id"
                            class="join-input flex-kai"
                        >
                            <view v-if="!team_id" class="color-sub">
                                <text>请选择队伍</text>
                                <text v-if="detail.conf.active.team_required" class="pl5">(必选)</text>
                            </view>
                            <view v-if="team_id">{{ team_name || team_id }}</view>
                            <text class="iconfont icon-more color-disabled font18"/>
                        </navigator>
                    </template>


                    <template v-if="detail.rank_set && detail.rank_set['scan_need_books'] && (!user_details.id || is_set_book_date)">

                        <picker :range="day_by_day_data_list" range-key="date_text"
                                :value="day_by_day_data_list.findIndex(v => v.date === day_by_day_date) === -1 ? 0 : day_by_day_data_list.findIndex(v => v.date === day_by_day_date)"
                                @change="dayByDayDateChange($event)">
                            <view class="join-input flex-kai">
                                <view v-if="!day_by_day_date" class="color-sub">请选择报名日期</view>
                                <view v-if="day_by_day_date">{{ day_by_day_date }}</view>
                                <text class="iconfont icon-more color-disabled font18"/>
                            </view>
                        </picker>
                    </template>


                    <view class="join-popup-btns flex-center text-center font18">
                        <view v-if="!is_set_book_date" class="join-popup-btn-cancel color-sub"
                              @click="cancelJoin">取消
                        </view>
                        <view class="join-popup-btn-confirm color-success" @click="joinAjax">确定</view>
                    </view>
                </view>

                <template v-if="!detail.rank_set || !detail.rank_set.closed_AD">
                    <view class="pt5">
                        <xwy-ad :ad_type="66"></xwy-ad>
                    </view>
                    <xwy-ad :ad_type="3"></xwy-ad>
                </template>
            </view>


        </view>

        <active-share ref="activeShare"/>

        <uni-popup ref="activity_detail" type="center" @touchmove.stop.prevent="">
            <view v-if="detail && detail.conf && detail.conf.active"
                  class="uni-popup-info detail-popup bg-white">
                <view class="popup-close" @click="uniPopupClose('activity_detail')">
                    <uni-icons type="close" size="28" color="#b2b3b7"/>
                </view>
                <scroll-view scroll-y="true" class="detail-popup-detail"
                             style="max-height: calc(100vh - 200px); padding: 10px 0;">
                    <view v-if="!detail.conf.active.activity_rules_hide" class="p10 bdb-10 bg-white">
                        <view class="text-center color-sub pb5">- 活动规则 -</view>

                        <view class="color-content font16">
                            活动参与方式：{{ ['', '自由报名参与活动', '需要输入密码才能报名', '报名需要审核通过才能参与活动'][detail.conf.active.enter_types] }}
                        </view>

                        <view v-if="detail.conf.active.exchange_start_time || detail.conf.active.exchange_end_time" class="color-content font16">
                            {{ clock_in_text }}时间：{{ detail.conf.active.exchange_start_time || '00:00:00' }} - {{ detail.conf.active.exchange_end_time || '23:59:59' }}
                        </view>


                        <view v-if="!detail.conf.active.point_hide && detail.conf.active.clock_in_on_site.open === 1" class="color-content font16">
                            {{ clock_in_text }}范围：需在{{ clock_in_text }}点附近{{ detail.conf.active.clock_in_on_site.distance || 0 }}公里内才能{{ clock_in_text }}
                        </view>

                        <template v-if="!detail.rank_set || !detail.rank_set['signOnlyExam']">
                            <view v-if="!isNaN(detail.conf.active.sign_times_type)"
                                  class="color-content font16">
                                {{ clock_in_text }}次数：{{ clockInTimesText }}
                            </view>
                            <view class="color-content font16">
                                是否需要按顺序{{clock_in_text }}：{{ detail.conf.active.sequential_clock_in === 1 ? '是' : '否' }}
                            </view>
                            <view class="color-content font16">
                                打卡后是否需要答题：{{ detail.conf.active.must_exam && detail.conf.active.must_exam.open ? '是' : '否' }}
                            </view>
                            <view v-if="detail.conf.active.must_exam && detail.conf.active.must_exam.open"
                                  class="color-content font16">
                                最低答题分数：
                                <template v-if="detail.conf.active.must_exam.score">
                                    {{ detail.conf.active.must_exam.score }}分
                                </template>
                                <template v-else>无限制</template>
                            </view>
                            <template v-if="detail.rank_set && detail.rank_set.gift_goods">
                                <view v-if="detail.conf.active.integral && detail.conf.active.integral.per_point_reward" class="color-content font16">
                                    每次打卡奖励{{ detail.conf.active.integral.per_point_reward }}{{ integralUnit }}
                                </view>
                                <view v-if="detail.conf.active.integral && detail.conf.active.integral.finished_all_point_reward" class="color-content font16">
                                    每日打卡完所有打卡点奖励{{ detail.conf.active.integral.finished_all_point_reward }}{{ integralUnit }}
                                </view>
                            </template>
                        </template>
                    </view>

                    <view v-if="detail.content || news_detail" class="p10 bdb-10 bg-white">
                        <view class="text-center color-sub pb5">- 活动说明 -</view>
                        <view class="color-content font16">
                            <template v-if="detail.content && !news_detail">
                                <rich-text :nodes="detail.content" space="nbsp" selectable="true"></rich-text>
                            </template>
                            <template v-if="news_detail">
                                <template v-if="news_detail.content">
                                    <u-parse :content="news_detail.content"/>
                                </template>
                            </template>
                        </view>
                    </view>
                    <xwy-ad v-if="!loading && (!detail.rank_set || !detail.rank_set.closed_AD)"
                            :ad_type="66"></xwy-ad>
                </scroll-view>
            </view>
        </uni-popup>

        <uni-popup ref="my_info" type="center" @touchmove.stop.prevent="">
            <view v-if="detail && detail.conf && detail.conf.active" class="uni-popup-info bg-white p20">
                <view class="popup-close" @click="uniPopupClose('my_info')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="text-center color-sub pb5">- 活动报名信息 -</view>
                <view class="text-center p10">

                    <image class="headimg" mode="aspectFill"
                           :src="headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"/>
                    <view>
                        <text class="color-primary" @click="updateHeadimg">更改头像</text>
                    </view>
                </view>


                <view class="color-content font16 ptm5" v-for="(item, index) in must_submit" :key="index"
                      @click="updateAttendDetailShow(item)">
                    <text>
                        {{ item.title }}：
                        <template>
                            <template v-if="item.types === 4">{{ item.value ? '已' : '未' }}签名</template>
                            <template v-else>
                                <template v-if="item.value">{{ item.value }}</template>
                                <template v-else>
                                    <template v-if="item.types === 1">未填写</template>
                                    <template v-if="item.types === 2">未选择</template>
                                </template>
                            </template>
                        </template>
                    </text>
                    <text v-if="is_joining && item.types !== 4"
                          class="iconfont icon-edit color-sub pl5"></text>
                </view>

                <view v-if="detail.rank_set && detail.rank_set.team_group_open"
                      class="color-content font16 ptm5">
                    <text>
                        队伍：{{ user_details.team_details && user_details.team_details.name || '未加入队伍' }}
                    </text>
                    <template v-if="!user_details.team_id">
                        <text class="color-primary pl5" @click="updateAttendDetailShow">加入队伍</text>
                        <uni-icons type="forward" color="#2d8cf0" @click="updateAttendDetailShow"/>
                    </template>
                </view>

                <view v-if="books_date" class="color-content font16 ptm5">报名日期：{{ books_date }}</view>

                <template v-if="!detail.rank_set || !detail.rank_set['signOnlyExam']">
                    <view class="color-content font16 ptm5">
                        累计{{ clock_in_text }}：{{ user_details['sign_day'] || 0 }}天
                    </view>
                    <view class="color-content font16 ptm5">
                        累计次数：{{ user_details.sign_count || 0 }}次
                    </view>
                    <view class="color-content font16 ptm5">
                        连续{{ clock_in_text }}：{{ user_details['continue_sign_day'] || 0 }}天
                    </view>
                </template>

                <view v-if="detail.conf.active.record_used_time" class="color-content font16 ptm5">
                    {{ clock_in_text }}用时：{{ clockInUsedTime }}
                </view>


                <navigator
                    v-if="detail.rank_set && detail.rank_set['gift_goods']"
                    class="color-content font16 ptm5"
                    :url="'/pages/sign_in/integral_record_list?active_id=' + id + '&unit=' + integralUnit"
                >
                    <text>我的{{ integralUnit }}：{{ user_details.integral_left || 0 }}</text>
                    <text class="pl10 color-light-primary font14">查看明细</text>
                    <text class="iconfont color-light-primary icon-more font14"></text>
                </navigator>

                <template v-if="popup_open && (!detail.rank_set || !detail.rank_set.closed_AD)">
                    <view style="position: relative; left: -10px;">
                        <xwy-ad :ad_type="66"></xwy-ad>
                    </view>
                    <xwy-ad :ad_type="3"></xwy-ad>
                </template>
            </view>
        </uni-popup>


        <template v-if="password_dialog_show">
            <uni-popup ref="input_password" type="dialog" mode="input" :is-mask-click="false"
                       @maskClick="copy(id)">
                <uni-popup-dialog mode="input" title="活动密码" :value="password" placeholder="请输入活动密码"
                                  @confirm="passwordInputConfirm"
                                  @close="passwordInputClose">
                </uni-popup-dialog>
            </uni-popup>
        </template>

        <activity-notice-popup ref="activityNoticePopup" :set="activeNoticeSet"
                               :show-user-center="detail.rank_set && detail.rank_set['shield_other']"/>

        <iup ref="input_username_password"></iup>

        <expiration-reminder ref="expirationReminder"/>


    </view>
</template>

<script>

const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import my_storage from '@/utils/storage.js'
import base64 from '@/utils/base64.js'
import bs_socket from '@/utils/bullet_screen_socket.js'


import lffBarrage from '@/components/lff-barrage.vue'

import inputUsernamePassword from '@/components/input-username-password.vue'

let interval
let innerAudioContext

export default {
    components: {
        lffBarrage,
        iup: inputUsernamePassword
    },
    data() {
        return {
            this_day: new Date().getUTCDate(),
            evn_version: app.globalData['evn_version'],
            loading: true,
            screen_pic: '',
            screen_pic_show: false,
            screen_pic_hide: false,
            screen_pic_count_down: null,
            screen_pic_button_show: true,
            id: '',
            userid: '',
            is_my_activity: false,
            detail: {},
            user_details: {},
            audio_play: false,
            error: '',
            join_popup_show: false,
            update_attend_details: false,
            headimg: '',
            username: '',
            checked: 0,
            is_joining: false,
            password: '',
            technology_support: false,
            must_submit: [],
            headimg_plugin: [],
            password_dialog_show: false,
            popup_open: false,
            news_detail: null,
            platform: uni.getSystemInfoSync().platform,
            sign_btn_text: '去打卡',
            sign_btn_disabled: false,
            list: [],
            list_loading: true,
            list_total: 0,
            load_page: 1,
            is_last_page: false,
            day_by_day_date: '',
            books_date: '',
            is_set_book_date: false,
            day_by_day_data_list: [],
            clock_in_text: '打卡',

            team_id: '',
            team_name: '',
            hideShare: false,
            today_step: null
        }
    },

    computed: {
        sign_num_lottery_show() {
            const {checked = 0, detail = {}} = this
            const {rank_set, conf = {}} = detail
            return !!(rank_set?.['sign_num_lottery'] && conf.active?.sign_set?.rules?.length && checked)
        },

        dailyCheckInOpen() {
            const {rank_set, conf} = this.detail || {}
            return !!(rank_set?.['morning_daily_sign'] && conf?.active?.daily_sign?.types)
        },

        activeNoticeSet() {
            return this.detail?.conf?.active?.active_details_notice || {}
        },

        stepShowOpen() {
            const {auto_exchange_press_button, one_day_auto_exchange, other_add_sport_step} = this.detail?.rank_set || {}
            return auto_exchange_press_button || one_day_auto_exchange || other_add_sport_step
        },

        integralUnit() {
            return this.detail?.conf?.active?.integral?.unit || '金币'
        },

        clockInUsedTime() {
            const second = this.user_details.used_time || 0
            if (!second) return '未完成'

            const units = [
                { label: '天', value: Math.floor(second / (60 * 60 * 24)) },
                { label: '小时', value: Math.floor(second % (60 * 60 * 24) / (60 * 60)) },
                { label: '分', value: Math.floor(second % (60 * 60) / 60) },
                { label: '秒', value: second % 60 }
            ];

            // 把非0的单位拼接起来
            return units
                .filter(unit => unit.value > 0)
                .map(unit => `${unit.value}${unit.label}`)
                .join('')
        },

        clockInTimesText() {
            const type = this.detail?.conf?.active?.sign_times_type || 0
            if (!type) return '无限制'
            if (type === 1) return '每个打卡点只能打卡一次'
            if (type === 2) {
                const num = this.detail?.conf?.active?.daily_submit_num || 1
                return `每个打卡点每天只能打卡${num}次`
            }
        },

        hideSquare() {
            const hide_ids = ['98d16844d90fa706a6946099fb446857']
            return hide_ids.includes(this.id)
        }
    },

    onLoad(e) {

        console.log('活动详情页面路径参数', e)


        // #ifdef H5
        const flat = true
        if (flat) return xwy_api.alert('请在小程序内打开', {success: () => uni.navigateBack()})
        // #endif

        if (uni.getLaunchOptionsSync().scene === 1154) return this.getSimpleDetail(e.id)

        e.screen_pic ? this.screenPicShow(e.screen_pic) : this.$uni.showLoading('数据加载中...')

        // 获取上级邀请人及活动ID
        let {userid = null, id: active_id = null, scene} = e
        if (scene) {
            const sceneStr = decodeURIComponent(scene)
            userid = this._utils.getUrlParams('userid', sceneStr) || this._utils.getUrlParams('uid', sceneStr) || userid
            active_id = this._utils.getUrlParams('id', sceneStr) || active_id
        }

        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            if (e.scene) return this.analysisScene(e.scene)

            if (!e.id) {
                this.loading = false
                this.error = '请指定活动id'
                return uni.hideLoading()
            }


            this.id = e.id
            this.userid = app.globalData['userid']
            this.getDetail()
        }, userid, active_id)
    },
    onShow() {
        if (this.detail?.conf?.active?.audio_src) innerAudioContext.play()
    },

    onHide() {
        if (this.audio_play) innerAudioContext.pause()
    },

    onUnload() {
        if (this.detail?.conf?.active?.audio_src) innerAudioContext.destroy()
        if (bs_socket.socketTask) {
            this.unload = true
            bs_socket.socketTask.close()
        }
    },

    onShareAppMessage() {
        this._utils.shareCopyActivityId(this.detail)

        let url = '/pages/clock_in/user/detail?id=' + this.id;
        if (this.detail?.conf?.active?.screen_pic) url += `&screen_pic=${this.detail.conf.active.screen_pic}`

        return {
            title: this.detail.conf?.active?.share_title || this.detail.name || '',
            path: url,
            imageUrl: this.detail.conf?.active?.share_image || this.detail.logo || ''
        }
    },

    onShareTimeline() {
        return {
            title: this.detail.name,
            imageUrl: this.detail.logo || ''
        }
    },

    onReachBottom() {
        !this.list_loading && !this.is_last_page && this.getList()
    },

    methods: {
        lookPublicSignList() {
            uni.pageScrollTo({
                selector: '#public-sign-list'
            })
        },
        async getList() {
            if (this.rank_set?.['signOnlyExam']) return
            if (this.hideSquare) return

            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
            }
            const data = {
                active_id: this.id,
                checked: 1,
                page: this.load_page,
                perpage: 10
            }


            this.list_loading = true

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/public_square_sign_list',
                data
            })

            this.list_loading = false
            this.load_page++

            if (res?.data?.['user_sign_list']) {
                const data_ = res.data['user_sign_list']
                this.list_total = data_.total
                this.is_last_page = data_.is_lastpage
                const list = data_.data || []

                if (list.length) {
                    if (this.is_admin) this.check_all = false
                    list.forEach(v => {
                        v.img_video_count = 0
                        const {pic_list, video_list} = v.conf_json || {}
                        if (pic_list?.length) v.img_video_count += pic_list.length
                        if (video_list?.length) v.img_video_count += video_list.length
                    })
                }
                this.list = [...this.list, ...list]
            } else {
                this.is_last_page = true
            }

        },

        lookImages() {
            this.$uni.navigateTo(`./clock-in-images?id=${this.id}`)
        },

        async like(index) {
            if (this.detail.conf.active.closed_likes) return this.$uni.showToast('活动已关闭点赞功能')

            if (this.detail.conf.active.like_limit) {
                if (!this.user_details?.id) return this.$uni.showToast('参与活动后才能点赞')
                if (!this.checked) return this.$uni.showToast('需要管理员审核通过后才能点赞')
            }

            const item = this.list[index]

            this.$uni.showLoading('点赞中...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/friend_agree_vote_record',
                data: {
                    id: item.id,
                    active_id: this.id,
                    act_types: 1
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '点赞失败')

            const title = res.info || '点赞成功'
            this.$uni.showToast(title, title.length <= 7 ? 'success' : 'none')

            item.agree++
        },


        lookUserRecord(item) {
            let url = '/pages/clock_in/user/public_sign_list?id=' + this.id + '&userid=' + item.userid + '&type=' + this.detail.types
            if (item['user_attend_details']?.must_submit?.length && item['user_attend_details'].must_submit[0].value) url += `&nickname=${item['user_attend_details'].must_submit[0].value}`
            this.$uni.navigateTo(url)
        },

        toPointDetail(point_detail) {
            this.$uni.navigateTo('./point_detail?id=' + this.id + '&point_id=' + point_detail.id, {
                events: {
                    reloadList: () => this.getPointList()
                }
            })
        },

        openLocation(item) {
            if (!item.lat || !item.lng) return false
            uni.openLocation({
                latitude: Number(item.lat),
                longitude: Number(item.lng),
                address: item.conf_json.address
            })
        },

        videoPlay(id) {
            this.videoContext = this.videoContext || null
            if (this.previous_video_id) {
                if (id === this.previous_video_id) return false
                this.videoContext = uni.createVideoContext(this.previous_video_id)
                this.videoContext.pause()
            }
            this.previous_video_id = id
        },


        previewImage(src, list) {
            list = list || [src]
            uni.previewImage({
                urls: list,
                current: src
            })
        },

        toBulletScreen() {
            let nickname = '匿名用户',
                headimg = ''

            if (app.globalData['userinfo'].nickname) nickname = app.globalData['userinfo'].nickname
            if (app.globalData['userinfo'].headimg) headimg = app.globalData['userinfo'].headimg
            if (this.must_submit && this.must_submit.length && this.must_submit[0].value) {
                nickname = this.must_submit[0].value
            }
            if (this.headimg) headimg = this.headimg

            let url = `/pages/activity/other/bullet_screen?active_id=${this.id}&nickname=${nickname}&headimg=${headimg}`
            console.log(this.active_more_data)
            if (this.active_more_data?.['active_conf_set']?.['barrage']) {
                const barrage = this.active_more_data['active_conf_set']['barrage']
                if (barrage.background) url += `&bgi=${barrage.background}`
                if (barrage['submit_button_text']) url += `&send_text=${barrage['submit_button_text']}`
                if (barrage['navigation_bar']) {
                    url += `&navigation_bar=${JSON.stringify(barrage['navigation_bar'])}`
                }
            }
            uni.navigateTo({url})
        },

        webSocketInit(reconnect = false) {
            bs_socket.webSocketInit(() => {
                bs_socket.socketTask.onMessage(res => {
                    console.log('【WEBSOCKET】收到消息', res.data)
                    this.receiveMessages(res.data)
                })
                bs_socket.socketTask.onOpen(res => {
                    console.log('【WEBSOCKET】', '链接成功！', res)
                    uni.hideLoading()
                    if (!reconnect) this.joinSocket()
                })
                bs_socket.socketTask.onClose(res => {
                    console.log('【WEBSOCKET】链接关闭！', res)
                    uni.hideLoading()
                    !this.unload && this.webSocketInit(true)
                })
            })
        },

        joinSocket() {
            let nickname = '匿名用户',
                headimg = ''

            if (app.globalData?.['userinfo']?.nickname) nickname = app.globalData['userinfo'].nickname
            if (app.globalData?.['userinfo']?.headimg) headimg = app.globalData['userinfo'].headimg
            if (this.must_submit && this.must_submit.length && this.must_submit[0].value) {
                nickname = this.must_submit[0].value
            }
            if (this.headimg) headimg = this.headimg
            const data = {
                active_id: this.id,
                userid: this.userid,
                nickname,
                headimg,
                message: '进入活动'
            }
            bs_socket.socketTask.send({
                data: JSON.stringify(data)
            })
        },

        receiveMessages(message) {
            message = JSON.parse(message)
            if (message.active_id !== this.id) return false
            this.$refs.lffBarrage.add({item: message})
        },


        async getSimpleDetail(id) {
            this.$uni.showLoading('数据加载中...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details_simple',
                data: {
                    active_id: id
                }
            })
            this.loading = false
            uni.hideLoading()

            if (!res?.data?.['active_details']) {
                this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                return false
            }


            if (res.data.active_more_data) {
                const active_more_data = res.data.active_more_data
                this.active_more_data = active_more_data
                if (active_more_data.technology_support) {
                    this.technology_support = res.data.active_more_data.technology_support
                }
                if (active_more_data['active_conf_set']) {
                    const active_conf_set = active_more_data['active_conf_set']
                    if (active_conf_set.headimg_plugin && active_conf_set.headimg_plugin.length) {
                        this.headimg_plugin = active_conf_set.headimg_plugin
                    }
                }
            }

            const detail = res.data['active_details']
            if (detail.types === 21) {
                this.clock_in_text = '集卡'

                // 雷子这个客户的活动需要把按钮文字改为“进入活动”，企业微信2025-04-15 10:05
                this.sign_btn_text = detail.active_id === '5a56536807e0970200f7954a6a0e11cd' ? '进入活动' : '去集卡'
            }
            this.detail = detail
            my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)

            if (detail.conf) {
                const conf = detail.conf

                if (!this.screen_pic && conf.active?.screen_pic) this.screenPicShow(conf.active.screen_pic)

                if (conf.active?.audio_src) this.audioInit(conf.active.audio_src)

                if (conf.must_submit) {
                    const must_submit = conf.must_submit
                    delete conf.must_submit
                    if (must_submit.length) {
                        must_submit.forEach(v => v.value = v.value || '')
                        this.must_submit = must_submit
                    }
                }
            }

            if (detail.name) uni.setNavigationBarTitle({title: detail.name})
        },

        toDailyCheckIn() {
              this.$uni.navigateTo(`/pages/sign_in/sign_in?active_id=${this.id}`, {
                  events: {
                      success: () => this.getUserStatus()
                  }
              })
        },


        toIdeaAvatar(item) {
            if (!item.img_list || !item.img_list.length) {
                return this.$uni.showModal('活动未配置头像图片列表，无法使用此功能')
            }

            let path = '/pages/idea_avatar/pendant/pendant'
            if (item.key === 'beijing') path = '/pages/idea_avatar/background/background'
            path += `?id=${this.id}`
            if (this.detail.rank_set?.closed_AD) path += '&closed_AD=1'

            this.$uni.navigateTo(path, {
                success: res => res.eventChannel.emit('img_list', item.img_list)
            })
        },

        /**
         * 无需打卡是否允许打卡判断
         * */
        async getSignDetail() {
            const {point_hide, sign_times_type} = this.detail.conf.active
            if (!point_hide || !sign_times_type) return  // 需要打卡点这里不查  不限打卡次数也不查

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/get_user_submit_sign_details',
                data: {active_id: this.id}
            })

            if (!res || !res.data) return
            const last_time = res.data['latest_sign_details']?.create_time || ''
            const today_sign_count = res.data['today_sign_count'] || 0

            // 只能打卡一次 并且 已经打过卡
            if (sign_times_type === 1 && last_time) {
                this.sign_btn_disabled = true
                this.sign_btn_text = '已' + this.clock_in_text
                return
            }

            // 每天允许打卡的次数
            const daily_submit_num = this.detail?.conf?.active?.daily_submit_num || 1

            // 每天只能打卡一次 并且 今天已经打过卡
            if (sign_times_type === 2 && daily_submit_num === 1 && today_sign_count) {
                this.sign_btn_disabled = true
                this.sign_btn_text = '今日已' + this.clock_in_text
                return
            }

            // 每天打卡n次
            if (sign_times_type === 2 && daily_submit_num > 1) {
                // 用户今日打卡次数
                const todayPointClockInCount = await this.getTodayClockInPeopleCount()

                // 达到打卡次数上限
                if (todayPointClockInCount >= daily_submit_num) {
                    this.sign_btn_disabled = true
                    this.sign_btn_text = `今日已达到${this.clock_in_text}上限(${daily_submit_num}次)`
                    return
                }
            }
        },

        async getTodayClockInPeopleCount() {
            const today = this._utils.getDay(0, true, '-')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/public_square_sign_list',
                data: {
                    active_id: this.id,
                    is_myself: 1,
                    begin_time: `${today} 00:00:00`,
                    end_time: `${today} 23:59:59`,
                    page: 1,
                    perpage: 1
                }
            })

            return res?.data?.user_sign_list?.total || 0
        },


        toSign() {
            if (!this.is_joining) return this.joinActivity(false)

            if (this.detail.conf.active.point_hide) {
                let {begin_time, end_time} = this.detail
                begin_time *= 1000
                end_time *= 1000
                const now_time = new Date().getTime()
                let tips = ''
                if (now_time < begin_time) tips = '未开始'
                if (now_time > end_time) tips = '已结束'
                if (tips) return xwy_api.alert(`活动${tips},无法${this.clock_in_text}`)
                return uni.navigateTo({url: '/pages/clock_in/user/sign_record_edit?active_id=' + this.id})
            }

            if (this.detail.types === 21) {
                return this.$uni.navigateTo(`/pages/card-collecting/user/map?id=${this.id}`, {
                    events: {
                        updateUserDetails: () => this.getUserStatus(),
                        reloadList: () => {
                            this.load_page = 1
                            this.getList()
                        }
                    }
                })
            }

            this.toPointList()
        },

        toPointList() {
            let url = './point_list?id=' + this.id
            if (!this.user_details?.checked) url += '&no_checked=1'
            this.$uni.navigateTo(url, {
                events: {
                    updateData: () => {
                        this.getUserStatus()
                        this.load_page = 1
                        this.getList()
                    }
                }
            })
        },


        toLottery() {
            const rules = this.detail.conf.active.sign_set.rules
            const sign_count = this.user_details.sign_count || 0
            if (rules.length === 1) {
                const {num, lottery_active_id: id} = rules[0]
                if (sign_count < num) return this.$uni.showToast(`需要打卡${num}次才能抽奖`, 'none', 2000)
                return this.$uni.navigateTo(`/pages/lottery/user/lottery?type=sign&id=${id}&active_id=${this.id}`)
            }

            this.$uni.navigateTo(`./lottery-list?active_id=${this.id}&sign_count=${sign_count}`, {
                success: res => res.eventChannel.emit('lotteryRules', rules)
            })
        },


        screenPicShow(src) {
            // #ifdef H5
            // h5不显示开屏图
            const flat = true
            if (flat) return false
            // #endif

            uni.hideLoading()
            this.screen_pic = src
            this.screen_pic_count_down = this.detail?.conf?.active?.screen_pic_count_down || 5

            let screen_pic_button_show = !!this.detail?.conf?.active?.screen_pic_buttom_show
            // 因为打卡活动没有隐藏“进入活动”按钮设置，所以打卡活动需要显示“进入活动”按钮
            if (this.detail?.types === 5) screen_pic_button_show = true
            this.screen_pic_button_show = screen_pic_button_show

            this.screen_pic_show = true
            interval = setInterval(() => {
                this.screen_pic_count_down--
                if (this.screen_pic_count_down <= 0) {
                    this.skipScreen()
                }
            }, 1000)
        },
        skipScreen() {
            clearInterval(interval)
            this.screen_pic_count_down = 0
            this.screen_pic_hide = true

            setTimeout(() => {
                this.screen_pic_show = false
                this.screen_pic_hide = false
                if (this.loading) this.$uni.showLoading('数据加载中...')
                this.passwordDialogShow()

                if (this.detail.rank_set?.['batch_import'] && this.detail.conf.active.join_type && !this.is_joining) {
                    this.$refs.input_username_password.open({
                        detail: this.detail,
                        success: () => {
                            this.$uni.showLoading()
                            this.getDetail()
                        }
                    })
                }
            }, 500)
        },
        analysisScene(scene) {
            const sceneStr = decodeURIComponent(scene)
            console.log(sceneStr)
            const id = utils.getUrlParams('id', sceneStr)
            console.log('id===', id)
            if (!id) {
                uni.hideLoading()
                return this.$uni.showModal('从二维码获取id失败')
            }

            this.getActiveId(id)
        },

        audioInit(src) {
            innerAudioContext = uni.createInnerAudioContext()
            innerAudioContext.autoplay = true
            innerAudioContext.loop = true
            innerAudioContext.src = src

            innerAudioContext.onPlay(() => {
                this.audio_play = true
            })
            innerAudioContext.onPause(() => {
                this.audio_play = false
            })
            innerAudioContext.onError((res) => {
                console.log('背景音乐播放失败')
                console.log(res.errMsg)
                console.log(res.errCode)
            })
        },
        audioControl() {
            console.log(innerAudioContext.paused)
            const paused = innerAudioContext.paused
            paused ? innerAudioContext.play() : innerAudioContext.pause()
        },

        async getActiveId(id) {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/get_long_active_id_by_short_id',
                data: {id}
            })
            if (res?.data?.['long_active_id']) {
                this.id = res.data['long_active_id']
                this.getDetail()
            } else {
                uni.hideLoading()
                xwy_api.alert(res && res.info || '长id获取失败')
            }
        },

        getDetail(just_update = false) {
            xwy_api.getActivityDetail(this.id, res => {
                if (!res?.data?.['active_details']) {
                    this.loading = false
                    uni.hideLoading()
                    this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                    return false
                }

                // #ifndef H5
                this.$refs.expirationReminder.open(res.data.active_details)
                // #endif

                if (res.data.active_more_data) {
                    const active_more_data = res.data.active_more_data
                    this.active_more_data = active_more_data
                    if (active_more_data.technology_support) {
                        this.technology_support = res.data.active_more_data.technology_support
                    }
                    if (active_more_data['active_conf_set']) {
                        const active_conf_set = active_more_data['active_conf_set']
                        if (active_conf_set.headimg_plugin && active_conf_set.headimg_plugin.length) {
                            this.headimg_plugin = active_conf_set.headimg_plugin
                        }
                    }
                }


                const detail = res.data['active_details']
                if (detail.types === 21) {
                    this.clock_in_text = '集卡'
                    // 雷子这个客户的活动需要把按钮文字改为“进入活动”，企业微信2025-04-15 10:05
                    this.sign_btn_text = detail.active_id === '5a56536807e0970200f7954a6a0e11cd' ? '进入活动' : '去集卡'
                } else if (detail.rank_set?.['signOnlyExam']) {
                    this.clock_in_text = '答题'
                    this.sign_btn_text = '去答题'
                }
                this.sign_times_list = [
                    {value: 0, title: `不限制${this.clock_in_text}次数`},
                    {value: 1, title: `每个${this.clock_in_text}点只能${this.clock_in_text}一次`},
                    {value: 2, title: `每个${this.clock_in_text}点每天只能${this.clock_in_text}一次`}
                ]
                app.globalData.activity_detail = detail
                this.detail = detail
                my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)

                if (detail.conf) {
                    const conf = detail.conf
                    if (conf.active) {
                        const active = conf.active
                        if (!this.screen_pic && active.screen_pic) {
                            this.screenPicShow(active.screen_pic)
                        }
                        if (active.audio_src) this.audioInit(conf.active.audio_src)
                        if (active.news?.news_id) this.changeDetailContent(active.news.news_id)
                        if (active.day_by_day?.length) {
                            this.getDayByDayData()
                        }
                    }

                    if (conf.must_submit) {
                        const must_submit = conf.must_submit
                        delete conf.must_submit
                        if (must_submit.length) {
                            must_submit.forEach(v => v.value = v.value || '')
                            this.must_submit = must_submit
                        }
                    }
                }

                if (detail.rank_set) {
                    const {shield_other, share_closed} = detail.rank_set
                    if (shield_other) {
                        this.$uni.hideHomeButton()
                        // 更新纯净版缓存信息
                        utils.updateShieldOtherInfo(this.detail)
                    }

                    if (share_closed) {
                        uni.hideShareMenu()
                        this.hideShare = true
                    }
                }


                this.addLookRecords()

                if (detail.name) this.$uni.setNavigationBarTitle(detail.name)

                if (!just_update && app.globalData['userid'] === detail.userid) {
                    this.is_my_activity = true
                }

                this.getUserStatus()

                this.getList()
            })
        },


        async getDayByDayData() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.scanPointBooks/books_date_count_list',
                data: {active_id: this.id}
            })

            const day_by_day_data_list = res?.data?.['date_list'] || []
            day_by_day_data_list.forEach(v => {
                v.date_text = `${v.date} 余${v['left_count']} / 总${v.max_people}`
                if (v['left_count'] === 0) v.date_text = `${v.date} 已满`
            })
            this.day_by_day_data_list = day_by_day_data_list
        },

        dayByDayDateChange(e) {
            const index = e.detail.value
            const day_by_day_data_list = this.day_by_day_data_list
            const item = day_by_day_data_list[index]
            this.day_by_day_date = item.date
            if (item['left_count'] === 0) {
                this.day_by_day_date = ''
                xwy_api.alert(`${item.date} 报名人数已满，请选择其他日期`)
            }
        },

        addLookRecords() {
            const detail = this.detail
            const value = {
                active_id: detail.active_id,
                name: detail.name,
                types: detail.types,
                logo: detail.logo || this.xwy_config.active_default_logo,
                look_time: new Date().getTime()
            }

            if (detail.organizer) value.organizer = detail.organizer
            my_storage.addActivityLookRecords(value)
        },


        async getUserStatus() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: {active_id: this.id}
            })

            if (res?.data?.user_details) {
                const attend_details = res.data.user_details
                this.user_details = attend_details
                this.is_joining = true
                this.checked = attend_details.checked || 0

                this.loading = false
                uni.hideLoading()

                if (attend_details.nickname) this.username = attend_details.nickname
                if (attend_details.headimg) this.headimg = attend_details.headimg

                if (attend_details.must_submit) {
                    this.must_submit.forEach((active, index) => {
                        attend_details.must_submit.forEach(user => {
                            if (active.title === user.title) {
                                this.$set(this.must_submit[index], 'value', user.value)
                            }
                        })
                    })
                }

                await this.getSignDetail()

                this.detail.rank_set?.['scan_need_books'] && await this.getBooksDate()

                if (this.stepShowOpen) await this.getWeRunData()
                await this.step2integral()
            } else {
                this.no_attend = true
                this.loading = false
                uni.hideLoading()

                if (this.screen_pic_show) return false

                this.passwordDialogShow()

                const join_type = this.detail.conf.active.join_type || 0
                if (this.detail.rank_set?.['batch_import'] && join_type) {
                    this.$refs.input_username_password.open({
                        detail: this.detail,
                        success: () => {
                            this.$uni.showLoading()
                            this.getDetail()
                        }
                    })
                    return false
                }
            }

            this.detail?.rank_set?.['barrage'] && this.webSocketInit()
        },

        async getWeRunData(reload = false) {
            const res = await this.xwy_api.getWeRunData()
            const stepList = res?.data?.['crypt_data']?.['stepInfoList'] || []
            const today = new Date(this._utils.getDay(0, true, '/')).getTime() / 1000
            this.today_step = stepList.find(item => item.timestamp === today)?.step || 0
            if (reload) this.$uni.showToast('步数已更新', 'success')
        },

        async step2integral() {
            const {auto_exchange_press_button, one_day_auto_exchange} = this.detail?.rank_set || {}
            // OA没有开启步数功能
            if (!auto_exchange_press_button && !one_day_auto_exchange) return
            // 没有报名|通过 或者 活动没有设置兑换比例
            if (!this.checked || !this.detail?.conf?.active?.integral?.exchange_step) return

            // await this.getWeRunData()

            // 避免刚同步步数到服务器，服务器没有及时获取到用户的新步数数据，所以延迟一下再兑换
            setTimeout(async () => {
                await this.xwy_api.request({
                    url: 'front.flat.sport_step.exchange/exchange_step',
                    data: {
                        active_id: this.id,
                        exchange_date: Math.floor(new Date(utils.getDay(0, true, '/')).getTime() / 1000)
                    }
                })
            }, 3000)
        },

        async getBooksDate() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.scanPointBooks/user_submit_books_date_list',
                data: {active_id: this.id}
            })

            const list = res?.data?.['submit_books_date_list']?.data || []
            this.books_date = list.map(v => utils.timestampToTime(v.books_date).slice(0, 10)).join('、')
        },

        async changeDetailContent(news_id) {
            const res = await xwy_api.request({
                url: "front.news/news_details",
                data: {news_id}
            })

            if (res?.status !== 1) return this.$uni.showModal(res.info || '文章内容获取失败')

            const detail = res.data['news_details']

            const video_url = detail.video_url
            if (video_url) {
                detail.video_type = /^https?:\/\//.test(detail.video_url) ? 'http' : 'txv_id'
            }

            if (detail.content) detail.content = utils.newsContentInit(detail.content)

            this.news_detail = detail
        },


        passwordDialogShow() {
            if (this.detail.conf.active.password && this.no_attend) {
                const passwordDialogShow = () => {
                    this.password_dialog_show = true
                    const password = my_storage.getActivityPassword(this.id)
                    if (password) {
                        this.password = password
                        this.checkActivityPassword(password)
                    }
                    const timeout = setTimeout(() => {
                        this.$refs.input_password.open()
                        clearTimeout(timeout)
                    }, 30)
                }

                // 体验版不需要输入密码
                if (app.globalData['evn_version'] === 'trial') {
                    return uni.showModal({
                        title: '提示',
                        content: '此活动设置了活动密码，请勿报名参与活动！！！',
                        cancelText: '进入活动',
                        confirmText: '输入密码',
                        success: res => {
                            res.confirm && passwordDialogShow()
                        }
                    })
                }

                passwordDialogShow()
            }

            this.showNoticePopup()
        },

        passwordInputConfirm(val) {
            if (!val) {
                // 纯净版取消输入密码出现去个人中心选项
                if (this.detail?.rank_set?.['shield_other']) {
                    return uni.showModal({
                        title: '提示',
                        content: '请输入密码',
                        cancelText: '个人中心',
                        confirmText: '重新输入',
                        success: res => {
                            if (res?.confirm) return this.$refs.input_password.open()
                            this.$uni.navigateTo('/pages/user/user')
                        }
                    })
                }

                return xwy_api.alert('请输入密码', {success: () => this.$refs.input_password.open()})
            }
            this.checkActivityPassword(val)
        },

        async checkActivityPassword(password) {
            this.$uni.showLoading('密码验证中...')

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/check_active_password',
                data: {
                    active_id: this.id,
                    password
                }
            })
            uni.hideLoading()

            if (res?.status === 1) {
                my_storage.rememberActivityPassword(this.id, password)
                this.$refs.input_password.close()
                this.$uni.showToast('密码正确', 'success')
                return this.showNoticePopup()
            }

            xwy_api.alert(res && res.info || '密码错误', {
                success: () => this.$refs.input_password.open()
            })
        },

        passwordInputClose() {
            this.copy(this.id, true)

            if (getCurrentPages().length > 1) return uni.navigateBack()

            // 纯净版并且没有上一页面，重新弹出输入密码窗口
            if (app.globalData['shop_info']?.extend_set?.shield_other_active?.active_id)
                return xwy_api.alert('请输入活动密码', {success: () => this.$refs.input_password.open()})

            this.$uni.reLaunch('/pages/index/index')
        },

        showNoticePopup() {
            if (!this.activeNoticeSet?.open || !this.activeNoticeSet?.news_id || this.is_joining) return
            this.$refs.activityNoticePopup.open()
        },


        async updateHeadimg() {
            uni.navigateTo({
                url: `/pages/other/headimg-list/headimg-list?active_id=${this.id}`,
                events: {
                    newImg: async obj => {
                        if (!obj?.src) return
                        this.$uni.showLoading('修改中...')
                        if (!obj.temp) return this.updateAttendDetail(obj.src)
                        const data = {
                            temp_data: {
                                path: obj.src
                            },
                            is_temp: 5
                        }
                        if (obj.size) data.temp_data.size = obj.size
                        const headimg = await xwy_api.uploadOneImage(data)
                        this.updateAttendDetail(headimg)
                    }
                }
            })
        },

        joinActivity(is_set_book_date = false) {
            if (this.loading) return
            if (is_set_book_date) {
                this.is_set_book_date = true
            } else {
                if (this.detail.conf?.active?.submit) {
                    const now_time = new Date().getTime()
                    const {begin, end} = this.detail.conf.active.submit
                    if (begin) {
                        const begin_time = new Date(begin.replace(/-/g, '/')).getTime()
                        if (now_time < begin_time) {
                            return this.$uni.showToast('未开始报名，报名时间从' + begin + '开始', 'none', 3000)
                        }
                    }
                    if (end) {
                        const end_time = new Date(end.replace(/-/g, '/')).getTime()
                        if (now_time > end_time) {
                            return this.$uni.showToast('报名已结束，报名时间到' + end + '结束', 'none', 3000)
                        }
                    }
                }
            }
            this.join_popup_show = true
        },

        updateAttendDetailShow(item) {
            if (item.types === 4) {
                if (item.value) return this.$uni.previewImage(item.value)
                return
            }

            this.update_attend_details = true
            this.join_popup_show = true
        },

        mustTextValueChange(e, index) {
            const item = this.must_submit[index]
            item.value = e.detail.value
            this.$set(this.must_submit, index, item)
        },

        mustValueChange(e, index) {
            const item = this.must_submit[index]
            item.value = item.options[e.detail.value].text
            this.$set(this.must_submit, index, item)
        },

        toHandSignature(index) {
            const item = this.must_submit[index]

            this.$uni.navigateTo('./signature', {
                success: res => {
                    const {bg, x = 0, y = 0, width = 200, height = 100, color = '#000000', size = 16} = this.detail?.conf?.active?.must_submit_signature || {}
                    const data = {
                        active_id: this.id,
                        title: item.title,
                        bg,
                        x,
                        y,
                        width,
                        height,
                        color,
                        size
                    }
                    if (item.value) data.signature_src = item.value

                    res.eventChannel.emit('data', data)
                },
                events: {
                    handSignature: src => {
                        item.value = src
                        this.$set(this.must_submit, index, item)
                    }
                }
            })
        },


        cancelJoin() {
            this.join_popup_show = false
            this.update_attend_details = false
        },

        joinAjax() {
            if (this.update_attend_details) {
                this.updateAttendDetail()
                return false
            }
            this.joining()
        },


        getMustSubmitData() {
            const must_submit = JSON.parse(JSON.stringify(this.must_submit))
            for (let i = 0; i < must_submit.length; i++) {
                const v = must_submit[i]
                v.options && delete v.options
                if (v.rules === 1 && v.value === '') {
                    this.$uni.showToast(`请${v.types === 2 ? '选择' : '输入'}${v.title}`)
                    return false
                }

                if (v.types === 3 && v.value && v.value.length !== 11) {
                    this.$uni.showToast(`请输入正确的${v.title}`)
                    return false
                }
            }

            let must_submit_str = JSON.stringify(must_submit)
            must_submit_str = must_submit_str.replace(/·/g, '-')
            return base64['encode'](must_submit_str)
        },

        updateAttendDetail(headimg) {
            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id
            }

            if (headimg) data.headimg = headimg
            if (this.team_id) data.team_id = this.team_id

            const must_submit = this.getMustSubmitData()
            if (must_submit === false) return false
            data.must_submit = must_submit

            this.$uni.showLoading('修改中...')

            xwy_api.ajax({
                url: 'front.flat.sport_step.user/update_attend_details',
                data,
                success: res => {
                    if (res?.status !== 1) return this.$uni.showModal(res?.info || '修改失败')

                    this.$uni.showToast('修改成功', 'success')

                    this.cancelJoin()
                    this.getDetail()
                }
            })
        },

        joining() {
            const rank_set = this.detail.rank_set || {}
            if (rank_set.team_group_open && this.detail.conf.active.team_required && !this.team_id) {
                return this.$uni.showToast('请选择队伍')
            }

            if (rank_set?.['scan_need_books'] && !this.day_by_day_date)
                return this.$uni.showToast('请选择报名日期', 'error')

            if (this.is_set_book_date) return this.submitDayByDayDate()

            const data = {
                active_id: this.id,
                access_token: app.globalData['access_token']
            }
            if (this.team_id) data.team_id = this.team_id


            if (this.must_submit && this.must_submit.length) {
                const must_submit = this.getMustSubmitData()
                if (must_submit === false) return false
                data.must_submit = must_submit
            }

            data.nickname = this.username

            this.loading = true
            this.$uni.showLoading('报名中...')


            xwy_api.ajax({
                url: 'front.flat.sport_step.user/submit_attend_active',
                data,
                success: res => {
                    if (res?.status !== 1) {
                        this.loading = false
                        uni.hideLoading()
                        return uni.showModal({
                            title: '报名失败',
                            content: res.info || '报名失败',
                            showCancel: false
                        })
                    }

                    if (this.detail.rank_set?.['scan_need_books']) return this.submitDayByDayDate()

                    this.loading = false
                    uni.hideLoading()
                    this.join_popup_show = false
                    this.$uni.showToast('报名成功', 'success')

                    setTimeout(() => {
                        uni.showLoading({
                            mask: true
                        })
                        this.getDetail()
                    }, 1000)
                }
            })
        },


        async submitDayByDayDate() {
            if (this.is_set_book_date) this.$uni.showLoading()

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.scanPointBooks/selected_books_date',
                data: {
                    active_id: this.id,
                    date: this.day_by_day_date,
                    nickname: this.must_submit[0].value
                }
            })

            this.loading = false
            uni.hideLoading()
            this.join_popup_show = false
            this.is_set_book_date = false

            if (!res?.status) {
                xwy_api.alert(res && res.info || '报名日期选择失败，请在活动页面重新选择报名日期', {
                    success: () => {
                        this.is_set_book_date = true
                        this.join_popup_show = true
                    }
                })

            } else {
                this.$uni.showToast('报名成功', 'success')
            }

            setTimeout(() => {
                this.$uni.showLoading()
                this.getDetail()
            }, 1000)
        },


        toTopList() {
            this.$uni.navigateTo('./ranking-list?id=' + this.id)
        },

        toShop() {
            app.globalData['tempData'].shop_integral_unit = this.integralUnit
            this.$uni.navigateTo('/pages/shop/goods/list?active_id=' + this.id)
        },

        showActiveSharePopup() {
            this.$refs.activeShare.open({
                page: 'pages/clock_in/user/detail',
                scene: 'id=' + this.detail.id,
                qrcode_logo: this.detail.rank_set?.closed_AD && this.detail.conf?.active?.qrcode_logo
            })
        },


        copy(data, hide_toast = false) {
            uni.setClipboardData({
                data,
                success: () => hide_toast ? uni.hideToast() : this.$uni.showToast('复制成功', 'none', 500)
            })
        },

        uniPopupClose(ref) {
            this.popup_open = false
            this.$refs[ref].close()
        },

        uniPopupOpen(ref) {
            this.popup_open = true
            this.$refs[ref].open()
        }

    }
}
</script>

<style scoped>
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 80px;
}

.bdb-10 {
    border-bottom: 10px solid #f8f8f8;
}

.icon-list {
    /* position: sticky; */
    /* top: 0; */
    /* z-index: 9; */
    border-top: 1px solid #eee;
}

.icon-item {
    padding: 10px 0;
    width: calc(100% / 4);
    /* border-top: 1px solid #eee; */
    box-sizing: border-box;
}


.headimg {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}


.join-btn {
    width: 250px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 20px;
}

.join-btn::after {
    border: none;
}

.join-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, .7);
    z-index: 9999;
}

.join-popup-c {
    border-radius: 10px;
    overflow: hidden;
}


.join-input {
    margin: 10px;
    padding: 10px;
    border: 1px solid #bbb;
    border-radius: 5px;
    width: 70vw;
    max-width: 400px;
}

.join-popup-btns {
    border-top: 1px solid #eee;
}

.join-popup-btn-cancel, .join-popup-btn-confirm {
    width: 50%;
    box-sizing: border-box;
    line-height: 44px;
}

.join-popup-btn-cancel {
    border-right: 1px solid #eee;
}


.uni-popup-info {
    position: relative;
    width: 280px;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    z-index: 999;
    left: 50%;
    bottom: -5px;
    margin-left: -12px;
}

.detail-popup {
    width: 95vw;
    padding-bottom: 15px;
}


.audio {
    position: fixed;
    border: 1px solid #fff;
    top: 20px;
    right: 20px;
    background-color: rgba(0, 0, 0, .5);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    z-index: 9;
}

.audio_rotate {
    animation: rotate 2s linear infinite;
}

@keyframes rotate {
    100% {
        transform: rotate(360deg);
    }
}


.list {
    padding: 10px;
}

.item {
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.item-headimg {
    width: 40px;
    min-width: 40px;
    height: 40px;
    display: block;
    border-radius: 50%;
}

.img-list {
    position: relative;
    left: -5px;
    padding-top: 5px;
}

.img-item {
    display: block;
    margin-left: 5px;
    margin-bottom: 5px;
    border-radius: 5px;
}

.img-list-1 .img-item {
    height: 120px;
    width: auto;
    max-width: calc(100vw - 130px);
}

.img-list-1 video {
    width: 180px !important;
}

.img-list-2 .img-item {
    width: 100px;
    height: 120px;
}

.img-list-more .img-item {
    width: calc((100vw - 150px) / 3);
    height: calc((100vw - 150px) / 3);
}

</style>
