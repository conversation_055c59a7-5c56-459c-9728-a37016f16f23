<template>
    <view class="page bg-background">
        <template v-if="is_admin && !notCheck">
            <view class="type-list flex-row bg-white">
                <view
                    class="type-item color-content"
                    v-for="(item, index) in checked_list"
                    :key="index"
                    :class="{'active-type': item.checked === checked}"
                    :style="{width: 'calc(100% / ' + checked_list.length + ')'}"
                    @click="checkedChange(item.checked)"
                >{{ item.title }}</view>
            </view>

            <view style="height: 40px;"></view>
        </template>

        <template v-if="point_id">
            <view class="type-list flex-row bg-white">
                <view
                    class="type-item color-content font14"
                    v-for="item in order_types_list"
                    :key="item.value"
                    :class="{'active-type': item.value === order_types}"
                    :style="{width: 'calc(100% / ' + order_types_list.length + ')'}"
                    @click="orderTypesChange(item.value)"
                >{{ item.title }}</view>
            </view>

            <view style="height: 40px;"></view>
        </template>


        <view v-if="list_total" class="text-center p10 color-sub font14">
            共{{ list_total }}条{{ clock_in_text }}记录
        </view>

        <xwy-ad :activity_id="id" :ad_type="3"></xwy-ad>

        <view class="list">
            <view v-for="(item, index) in list" :key="index">
                <view v-if="index === 0" style="padding-bottom: 20px;">
                    <xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>
                </view>
                <view class="item bg-white">
                    <view class="flex-row">
                        <view v-if="is_admin" class="flex-all-center" @click="checkChange(index)">
                            <radio :checked="item.check" style="transform:scale(0.8)"/>
                        </view>
                        <view class="pr10">
                            <image
                                class="headimg"
                                :src="item['user_attend_details'] && item['user_attend_details'].headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"
                                mode="aspectFill"
                            />
                        </view>
                        <view style="width: 100%;">
                            <view
                                v-if="item['user_attend_details'] && item['user_attend_details'].must_submit && item['user_attend_details'].must_submit.length"
                                class="color-title"
                            >{{ item['user_attend_details'].must_submit[0].value }}
                            </view>
                            <view v-if="item.memo" class="color-content pt5">{{ item.memo }}</view>

                            <view
                                v-if="item.img_video_count"
                                class="img-list flex-row flex-wrap pt5"
                                :class="{
                                        'img-list-1': item.img_video_count === 1,
                                        'img-list-2': item.img_video_count === 2,
                                        'img-list-more': item.img_video_count >= 3
                                     }"
                            >
                                <image
                                    class="img-item"
                                    lazy-load
                                    v-for="(img_item, img_index) in item.conf_json.pic_list"
                                    :key="img_index"
                                    :src="img_item"
                                    :mode="item.img_video_count === 1 ? 'heightFix' : 'aspectFill'"
                                    @click.stop="previewImage(img_item, item.conf_json.pic_list)"
                                />

                                <video
                                    class="img-item"
                                    :id="'video_' + item.id + '_' + video_index"
                                    v-for="(video_item, video_index) in item.conf_json.video_list"
                                    :key="video_index"
                                    :src="video_item"
                                    @play="videoPlay('video_' + item.id + '_' + video_index)"
                                ></video>
                            </view>

                            <view v-if="item['point_details'] && item['point_details'].name" class="pt5">
                                <view
                                    hover-class="navigator-hover"
                                    style="display: inline-block;"
                                    @click="toPointDetail(item['point_details'])"
                                >
                                    <uni-icons type="location" size="16" color="#80848f"/>
                                    <text class="color-content font14">{{ item['point_details'].name }}</text>
                                </view>
                            </view>

                            <view v-if="item.conf_json && item.conf_json.wechat_step" class="pt5">
                                <text class="iconfont icon-walk color-sub pr5"></text>
                                <text
                                    :class="{
                                            'color-warning': item.conf_json.wechat_step >= 10000,
                                            'color-success': item.conf_json.wechat_step < 10000}
                                        "
                                >
                                    {{ item.conf_json.wechat_step }}
                                </text>
                            </view>

                            <view v-if="item.conf_json && item.conf_json.address" class="pt5">
                                <view
                                    hover-class="navigator-hover"
                                    style="display: inline-block;"
                                    @click="openLocation(item)"
                                >
                                    <uni-icons type="location" size="16" color="#80848f"/>
                                    <text class="color-content font14">{{ item.conf_json.address }}</text>
                                </view>
                            </view>

                            <view v-if="!item.checked" class="pt5">
                                <uni-tag text="待审核" type="default" size="mini" :inverted="true"/>
                            </view>

                            <view class="flex-kai">
                                <view class="color-sub font14 pt5">{{ item.create_time }}</view>
                                <view v-if="!closed_likes && !is_admin" @click="like(index)">
                                    <text
                                        class="iconfont icon-love color-red"
                                        style="position: relative; top: 1px; left: 2px;"
                                    ></text>
                                    <text class="pl5 color-sub font14">{{ item.agree || 0 }}</text>
                                </view>
                            </view>
                        </view>
                    </view>


                    <view v-if="is_admin" class="item-bar flex-kai">
                        <view></view>
                        <view class="flex-row">
                            <view
                                v-if="!notCheck"
                                class="font14 color-content pl10 pr10"
                                hover-class="navigator-hover"
                                @click="changeChecked(item)"
                            >
                                改为{{ item.checked ? '待' : '已' }}审核
                            </view>
                            <view
                                class="font14 color-content pl10"
                                hover-class="navigator-hover"
                                @click="delItem(item.id)"
                            >删除
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="index !== 0 && ((index + 1) % 10 === 0)" style="padding-bottom: 20px;">
                    <xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>
                </view>
            </view>
        </view>


        <uni-load-more v-if="loading && load_page !== 1" status="loading"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && list.length > 5"
            status="noMore"
            :contentText="{contentnomore: '我是有底线的'}"
        ></uni-load-more>
        <uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>

        <view v-if="!loading && !list.length" class="text-center" style="padding-top: 15vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub font14">暂无{{ clock_in_text }}记录</view>
        </view>


        <template v-if="is_admin && list.length">
            <view style="height: 70px;"></view>
            <view class="bottom-bar flex-kai bg-white">
                <view class="flex-all-center" @click="checkAllChange">
                    <radio :checked="check_all" style="transform:scale(0.8)"/>
                    <text class="font14 color-sub" style="position: relative; left: -4px; top: 1px;">全选</text>
                </view>
                <view class="flex-row">
                    <view
                        v-if="!notCheck"
                        class="all-change color-content font14"
                        hover-class="navigator-hover"
                        @click="changeCheckeds"
                    >修改状态</view>
                    <view style="width: 10px;"></view>
                    <view
                        class="all-change color-content font14"
                        hover-class="navigator-hover"
                        @click="dels"
                    >删除</view>
                </view>
            </view>
        </template>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'


export default {
    data() {
        return {
            id: '',
            is_admin: 0,
            is_myself: 0,
            loading: true,
            checked: 1,
            list: [],
            list_total: 0,
            load_page: 1,
            is_last_page: false,
            checked_list: [
                {checked: 0, title: '所有记录'},
                {checked: 2, title: '待审核'},
                {checked: 1, title: '已审核'},
            ],
            check_all: false,
            point_id: null,
            order_types: 0,
            order_types_list: [],
            clock_in_text: '打卡',
            closed_likes: false,
            notCheck: false
        }
    },

    watch: {
        closed_likes(val) {
            if (val) {
                const like_type = 1
                const index = this.order_types_list.findIndex(v => v.value === like_type)
                if (index !== -1) this.order_types_list.splice(index, 1)
            }
        }
    },

    onLoad(e) {
        uni.showLoading({
            title: '加载中...',
        })

        this.id = e.id
        if (e.type && e.type === '21') this.clock_in_text = '集卡'
        this.order_types_list = [
            { value: 0, title: '默认排序' },
            { value: 1, title: '按点赞排序' },
            { value: 2, title: '按' + this.clock_in_text + '时间排序' },
        ]

        if (e.is_admin) {
            uni.setNavigationBarTitle({
                title: this.clock_in_text + '审核'
            })
            this.is_admin = 1
            this.checked = 2
            if (e.userid) this.userid = e.userid

            if (e.team_id) {
                this.team_id = e.team_id
                this.$uni.setNavigationBarTitle(`${e.team_name || ''}打卡记录`)
            }
        } else if (e.is_myself) {
            uni.setNavigationBarTitle({
                title: this.clock_in_text + '记录'
            })
            this.is_myself = 1
            this.checked = 0
        } else if (e.userid) {
            this.userid = e.userid
            this.checked = 1
            uni.setNavigationBarTitle({
                title: (e.nickname || '') + ' ' + this.clock_in_text + '记录'
            })
        } else {
            uni.setNavigationBarTitle({
                title: this.clock_in_text + '广场'
            })
            this.checked = 1
        }

        if (e.point_id) {
            this.point_id = e.point_id
            if (e['point_name']) {
                uni.setNavigationBarTitle({
                    title: e['point_name'] + ' ' + this.clock_in_text + '记录'
                })
            }
        }

        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.init()
        })
    },

    onReachBottom() {
        !this.loading && !this.is_last_page && this.getList()
    },

    methods: {
        async init() {
            await this.getActivityDetails()
            await this.getUserDetails()
            await this.getList()
        },

        async getActivityDetails() {
            let activity_details = app.globalData['activity_detail'] || this.activity_details

            if (!activity_details) {
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        access_token: app.globalData['access_token'],
                        active_id: this.id
                    }
                })
                if (res?.data?.active_details) activity_details = res['data']['active_details']
            }
            if (!activity_details) return
            this.activity_details = activity_details
            if (activity_details?.conf?.active?.closed_likes) this.closed_likes = true
            if (activity_details?.conf?.active?.not_check) {
                this.notCheck = true
                if (this.is_admin) this.checked = 1
            }
        },

        async getUserDetails() {
            if (!this.activity_details?.conf?.active?.like_limit) return
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id
                }
            })
            this.user_details = res?.data.user_details || null
        },

        checkedChange(checked) {
            if (checked === this.checked) return false
            this.checked = checked
            this.load_page = 1
            this.getList()
        },

        orderTypesChange(order_types) {
            if (order_types === this.order_types) return false
            this.order_types = order_types
            this.load_page = 1
            this.getList()
        },

        async getList() {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
            }
            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id,
                is_admin: this.is_admin,
                checked: this.checked,
                is_myself: this.is_myself,
                page: this.load_page,
                perpage: 10
            }

            if (this.userid) data.userid = this.userid
            if (this.point_id) data.point_id = this.point_id
            if (this.order_types) data.order_types = this.order_types

            this.loading = true

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/public_square_sign_list',
                data
            })

            this.loading = false
            uni.hideLoading()

            this.load_page++

            if (res?.['data']?.['user_sign_list']) {
                const data_ = res['data']['user_sign_list']
                this.list_total = data_.total
                this.is_last_page = data_.is_lastpage
                const list = data_.data || []

                if (list.length) {
                    if (this.is_admin) this.check_all = false
                    list.forEach(v => {
                        if (this.is_admin) v.check = false
                        v.img_video_count = 0
                        if (v.conf_json) {
                            if (v.conf_json.pic_list && v.conf_json.pic_list.length) {
                                v.img_video_count += v.conf_json.pic_list.length
                            }
                            if (v.conf_json.video_list && v.conf_json.video_list.length) {
                                v.img_video_count += v.conf_json.video_list.length
                            }
                        }
                    })
                }
                this.list = [...this.list, ...list]
            } else {
                this.is_last_page = true
            }

        },

        checkChange(index) {
            const item = this.list[index]
            item.check = !item.check
        },

        checkAllChange() {
            const check_all = !this.check_all
            this.check_all = check_all
            this.list.forEach(v => {
                v.check = check_all
            })
        },

        async changeChecked(item) {
            const {confirm} = await this.$uni.showModal(`确定将该记录的状态改为${item.checked ? '待' : '已'}审核?`, {showCancel: true})

            if (!confirm) return

            if (await this.changeCheckedAjax([item.id], item.checked ? 'un_checked' : 'checked')) {
                item.checked ^= 1
                this.$uni.showToast('已修改')
            }
        },

        getCheckList() {
            const ids = this.list.filter(item => item.check).map(item => item.id)

            if (!ids.length) {
                this.$uni.showToast('未勾选记录')
                return false
            }

            return ids
        },

        async changeCheckeds() {
            const ids = this.getCheckList()
            if (!ids) return false

            const checked_list = [
                {checked: 'checked', title: '已审核'},
                {checked: 'un_checked', title: '待审核'}
            ]
            const itemList = checked_list.map(item => item.title)

            const res = await this.$uni.showActionSheet(itemList, {title: '将记录状态修改为'})
            if (res?.errMsg !== 'showActionSheet:ok') return
            const index = res.tapIndex

            const {confirm} = await this.$uni.showModal(`确定将选中的${ids.length}条记录状态修改为${checked_list[index].title}?`, {showCancel: true})

            if (!confirm) return

            if (await this.changeCheckedAjax([ids], checked_list[index].checked)) {
                this.$uni.showToast('已修改')
                this.load_page = 1
                await this.getList()
            }
        },


        async delItem(id) {
            const {confirm} = await this.$uni.showModal('确定删除该记录?', {showCancel: true})
            if (!confirm) return

            if (await this.changeCheckedAjax([id], 'del')) {
                this.$uni.showToast('已删除')
                this.load_page = 1
                await this.getList()
            }
        },

        async dels() {
            const ids = this.getCheckList()
            if (!ids) return false

            const {confirm} = await this.$uni.showModal(`确定删除选中的${ids.length}条记录?`, {showCancel: true})
            if (!confirm) return

            if (await this.changeCheckedAjax([ids], 'del')) {
                this.$uni.showToast('已删除')
                this.load_page = 1
                await this.getList()
            }
        },

        async changeCheckedAjax(id_list, act_types) {
            const act_text = act_types === 'del' ? '删除' : '修改'

            this.$uni.showLoading(act_text + '中...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.sign_location.adminManage/checked_user_sign_record',
                data: {
                    active_id: this.id,
                    act_types,
                    ids: id_list.join(',')
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) {
                this.$uni.showModal(res?.info || `${act_text}失败`)
                return false
            }

            return true
        },

        async like(index) {
            if (this.closed_likes) return this.$uni.showToast('活动已关闭点赞功能')

            if (this.activity_details.conf.active.like_limit) {
                if (!this.user_details?.id) return this.$uni.showToast('参与活动后才能点赞')
                if (!this.user_details?.checked) return this.$uni.showToast('需要管理员审核通过后才能点赞')
            }

            const item = this.list[index]

            this.$uni.showLoading('点赞中...')

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/friend_agree_vote_record',
                data: {
                    access_token: app.globalData['access_token'],
                    id: item.id,
                    active_id: this.id,
                    act_types: 1
                }
            })

            uni.hideLoading()

            if (!res || !res['status']) {
                xwy_api.alert(res && res['info'] || '点赞失败')
                return false
            }

            const title = res['info'] || '点赞成功'
            uni.showToast({
                title,
                icon: title.length <= 7 ? 'success' : 'none'
            })

            item.agree++
        },

        toPointDetail(point_detail) {
            if (this.point_id) return
            uni.navigateTo({
                url: './point_detail?id=' + this.id + '&point_id=' + point_detail.id,
                events: {
                    reloadList: () => this.getPointList()
                }
            })
        },

        openLocation(item) {
            if (!item.lat || !item.lng) return false
            uni.openLocation({
                latitude: Number(item.lat),
                longitude: Number(item.lng),
                address: item.conf_json.address
            })
        },

        videoPlay(id) {
            this.videoContext = this.videoContext || null
            if (this.previous_video_id) {
                if (id === this.previous_video_id) return false
                this.videoContext = uni.createVideoContext(this.previous_video_id)
                this.videoContext.pause()
            }
            this.previous_video_id = id
        },


        previewImage(src, list) {
            list = list || [src]
            uni.previewImage({
                urls: list,
                current: src
            })
        },
    }
}
</script>

<style>
.page {
    min-height: 100vh;
    box-sizing: border-box;
}


.type-list {
    position: fixed;
    z-index: 9;
    width: 100vw;
    top: 0;
    left: 0;
}

.type-item {
    height: 40px;
    line-height: 40px;
    text-align: center;
    box-sizing: border-box;
}

.active-type {
    color: #007AFF;
    border-bottom: 2px solid #007AFF;
}


.list {
    padding: 10px;
}

.item {
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.headimg {
    width: 40px;
    min-width: 40px;
    height: 40px;
    display: block;
    border-radius: 50%;
}

.img-list {
    position: relative;
    left: -5px;
    padding-top: 5px;
}

.img-item {
    display: block;
    margin-left: 5px;
    margin-bottom: 5px;
    border-radius: 5px;
}

.img-list-1 .img-item {
    height: 120px;
    width: auto;
    max-width: calc(100vw - 130px);
}

.img-list-1 video {
    width: 180px !important;
}

.img-list-2 .img-item {
    width: 100px;
    height: 120px;
}

.img-list-more .img-item {
    width: calc((100vw - 150px) / 3);
    height: calc((100vw - 150px) / 3);
}

.item-bar {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #eee;
}

.bottom-bar {
    position: fixed;
    left: 0;
    width: 100%;
    z-index: 9;
    border-top: 1px solid #eee;
    bottom: 0;
    padding: 5px 10px 15px;
    height: 61px;
    line-height: 40px;
    box-sizing: border-box;
}

.all-change {
    line-height: 38px;
    border-radius: 20px;
    padding: 0 15px;
    border: 1px solid #eee;
    text-align: center;
}
</style>
