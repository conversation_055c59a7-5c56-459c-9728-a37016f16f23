<template>
    <view class="page bg-background">
        <view class="list">
            <view class="item flex-row" v-for="item in lottery_list" :key="item.lottery_active_id"
                  @click="toLottery(item)">
                <view class="icon flex-all-center">
                    <text class="iconfont icon-gift font34 color-error"></text>
                </view>
                <view class="pl10 w-100">
                    <view class="color-title font18">{{ item.name }}</view>
                    
                    <view class="clear clearfix pt5">
                        <view class="font14 color-sub fl">打卡{{ item.num }}次可抽奖</view>
                        <view class="lottery-button fr" 
                              :class="item.lock ? 'bg-disabled color-sub' : 'bg-warning color-white'">
                            去抽奖
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            lottery_list: []
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.sign_count = Number(params.sign_count)
        
        this.getLotteryList()
    },

    methods: {
        getLotteryList() {
            this.getOpenerEventChannel().on('lotteryRules', rules => {
                this.lottery_list = rules.map(item => {
                    item.lock = this.sign_count < item.num
                    return item
                })
            })
        },

        toLottery(item) {
            const {lock, num, lottery_active_id: id} = item
            if (lock) return this.$uni.showToast(`需要打卡${num}次才能抽奖`, 'none', 2000)
            
            this.$uni.navigateTo(`/pages/lottery/user/lottery?type=sign&id=${id}&active_id=${this.active_id}`)
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    padding-bottom: 20px;
    box-sizing: border-box;
}

.list {
    padding-top: 1px;
    
    .item {
        margin: 10px;
        padding: 10px;
        border-radius: 10px;
        background-color: #fff;
        
        .lottery-button {
            padding: 0 20px;
            line-height: 36px;
            border-radius: 18px;
        }
    }
}
</style>