<template>
	<view>
		<view class="flex-all-center pt10">

			<image v-if="headimg" class="headimg" :src="headimg" mode="aspectFill"/>
			<view v-else class="headimg flex-all-center bg-background">
				<uni-icons type="image" size="60" color="#c6cbe0"/>
			</view>
		</view>
		<view class="flex-all-center">
			<view class="p10" @click="changeHeadimg" hover-class="navigator-hover">
				<text class="color-primary">更换头像</text>
				<uni-icons type="forward" color="#2d8cf0"/>
			</view>
		</view>

		<view class="form">

		    <view class="form-item">
		        <view class="top color-title">昵称</view>
		        <view class="bottom font16">
		            <input class="input" type="nickname" v-model="nickname" placeholder="请输入昵称,请勿使用表情符号"/>
		        </view>
		    </view>

		    <!--<view class="form-item">
		        <view class="top color-title">手机号码</view>
		        <view class="bottom font16">
		            <input class="input" type="number" v-model="mobile" placeholder="请输入手机号码"/>
		        </view>
		    </view>-->


			<view class="form-item">
				<view class="top">
					<text class="color-title">web端登录账号</text>
					<text class="color-sub font14 pl5">(6-20位)</text>
				</view>
				<view class="bottom font16">
					<input class="input" v-model="username" :maxlength="20" placeholder="请输入web端登录账号, 不能输入空格"/>
				</view>
			</view>

			<view class="form-item">
				<view class="top">
					<text class="color-title">web端登录密码</text>
					<text class="color-sub font14 pl5">(6-20位)</text>
				</view>
				<view class="bottom font16">
					<input class="input" v-model="password" :maxlength="20" placeholder="无需修改密码请勿填写, 只能输入字母和数字"/>
				</view>
			</view>



		</view>

		<view class="save flex-all-center">
			<button :disabled="loading" class="save-btn text-center bg-primary color-white" @click="save">保存</button>
		</view>

    
	</view>
</template>

<script>
	const app = getApp()
	import login from '../../utils/api/login.js'
	import xwy_api from '../../utils/api/xwy_api.js'
	export default {
		data() {
			return {
				loading: true,
				nickname: '',
				mobile: '',
				username: '',
				password: '',
				headimg: ''
			}
		},
		onLoad(e) {
			if (e.neet_back) this.neet_back = true
			uni.showLoading()
			login.uniLogin(err => {
				uni.hideLoading()
				this.loading = false
				if (err && err.errMsg) {
					uni.showModal({
						title: err.errTitle || '提示',
						content: err.errMsg,
						showCancel: false
					})
				}

				this.getUserDetail()
			})
		},
		methods: {
			changeHeadimg() {
				uni.navigateTo({
					url: '/pages/other/headimg-list/headimg-list',
					events: {
						newImg: obj => {
							if (obj?.src) this.headimg = obj.src
							this.temp_path_size = obj?.size || ''
							this.temp_file = obj?.file || ''
							this.headimg_is_temp = obj?.temp || false
						}
					}
				})
			},

			async getUserDetail() {
				const userinfo = app.globalData.userinfo

				this.nickname = userinfo.nickname || xwy_api.getRandomNickname()
				this.headimg = userinfo.headimg || await xwy_api.getRandomHeadimg()
				if (userinfo.mobile) this.mobile = userinfo.mobile
				if (userinfo.username) this.username = userinfo.username
				if (userinfo.password) this.password = userinfo.password

			},

			async getHeadimg() {
				const headimg = this.headimg
				if (!this.headimg_is_temp) return headimg

				const data = {
					temp_data: {
						path: headimg
					},
					is_temp: 5
				}
				data.temp_data.size = this.temp_path_size || null
                if (this.temp_file) data.temp_data.file = this.temp_file
				return await xwy_api.uploadOneImage(data)
			},

			async save() {
				if (this.mobile && this.mobile.length !== 11) {
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none'
					})
					return false
				}




				const data = {
					nickname: this.nickname,
					mobile: this.mobile
				}
				if (this.username) {
					const username = this.username.toString()
					if (username.length < 6) {
						xwy_api.alert('web端登录账号长度不可少于6位')
						return false
					}
					if (username.includes(' ')) {
						xwy_api.alert('web端登录账号不能输入空格')
						return false
					}

					data.username = username
				}
				if (this.password) {
					const password = this.password.toString()
					if (password.length < 6) {
						xwy_api.alert('web端登录密码长度不可少于6位')
						return false
					}
					// if (password.includes(' ')) {
					// 	xwy_api.alert('web端登录密码不能输入空格')
					// 	return false
					// }
					if (!/^(\d|[a-zA-Z])+$/.test(password)) {
						xwy_api.alert('web端登录密码只能输入字母和数字')
						return false
					}
					data.password = password
				}

				if (!this.headimg) {
					uni.showToast({
						title: '请上传用户头像',
						icon: 'none'
					})
					return false
				}

				this.loading = true
				uni.showLoading({
					title: '保存中...'
				})

				data.headimg = await this.getHeadimg()


				xwy_api.saveUserDetail(data, (success, info) => {
					if (!success) {
						uni.showModal({
							title: '提示',
							content: info || '保存失败',
							showCancel: false
						})
						return false
					}

					uni.showLoading({
						title: '保存中...'
					})
					xwy_api.getUserDetail(() => {
						uni.hideLoading()
						this.loading = false
						uni.showToast({
							title: info || '保存成功!',
							icon: 'success'
						})

						if (this.neet_back) {
							const eventChannel = this.getOpenerEventChannel()
							eventChannel && eventChannel.emit('back')
							setTimeout(() => {
								uni.navigateBack()
							}, 1000)
						}
					})
				})


			}
		}
	}
</script>

<style scoped>
	.headimg {
		width: 100px;
		height: 100px;
		border-radius: 50%;
	}
	.form {
	    padding: 10px 0;
	}

	.form-item {
	    padding: 10px;
	}


	.form-item .bottom {
	    border-bottom: 1px solid #eee;
	}


	.form-item .textarea {
	    width: 100%;
	    line-height: 16px;
	    padding: 10px 0;
	}

	.form-item .input {
	    width: 100%;
	    line-height: 40px;
	    height: 40px;
	}

	.form-item .view {
	    padding: 8px 0;
	    width: 100%;
	}

	.save {
		padding: 30px 0;
	}
	.save-btn {
		width: 200px;
		line-height: 40px;
		border-radius: 20px;
	}
	.save-btn::after {
		border: none;
	}
</style>
