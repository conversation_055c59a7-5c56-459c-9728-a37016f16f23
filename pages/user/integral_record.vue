<template>
	<view>
		<view class="p10 bg-white flex-row">
			<view>

				<image class="avatar" :src="userinfo.headimg || ''"/>
			</view>
			<view class="pl10" style="line-height: 30px;">
				<view>{{userinfo.nickname || '微信用户'}}</view>
				<view>
					<text class="color-sub">我的积分：</text>
					<text class="color-info">{{userinfo.integral || 0}}</text>
				</view>
			</view>
		</view>

		<view class="list">
			<view class="item flex-kai" v-for="(item, index) in list" :key="index">
				<view>
					<view class="color-content font14">{{item.memo || (item.num > 0 ? '增加积分' : '扣减积分')}}</view>
					<view v-if="item.create_time && item.create_time !== '0'" class="color-sub font12" style="padding-top: 5px;">
						<uni-dateformat :date="item.create_time" format="yyyy-MM-dd hh:mm:ss"></uni-dateformat>
					</view>
				</view>
				<view
					class="flex-all-center font18"
					:class="{'color-red-': item.num > 0, 'color-content': item.num <= 0}"
				>{{item.num > 0 ? '+' : ''}}{{item.num}}</view>
			</view>
		</view>

		<view v-if="!list.length && !more_loading && !init_load" class="text-center" style="padding-top: 10vh;">
			<text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
			<view class="color-sub">暂无积分记录</view>
		</view>

		<uni-load-more v-if="more_loading" status="loading"></uni-load-more>
		<uni-load-more v-if="is_last_page && list.length > 5" status="noMore" :contentText="{contentnomore: '我是有底线的'}"></uni-load-more>
		<uni-load-more v-if="!is_last_page && !more_loading && !init_load" status="more"></uni-load-more>
	</view>
</template>

<script>
	const app = getApp()
	import login from '../../utils/api/login.js'
	import xwy_api from '../../utils/api/xwy_api.js'

	export default {
		data() {
			return {
				init_load: true,
				list: [],
				load_page: 1,
				is_last_page: false,
				more_loading: false,
				total_count: 0,
				userinfo: {
					nickname: '',
					headimg: '',
					integral: 0
				}
			}
		},
		onLoad() {
			uni.showLoading({
				mask: true
			})
			login.uniLogin(err => {
				if (err && err.errMsg) {
					uni.hideLoading()
					uni.showModal({
						title: err.errTitle || '提示',
						content: err.errMsg,
						showCancel: false
					})
				}

				this.getUserInfo()
				this.getList()
			})
		},
		onPullDownRefresh() {
			if (this.init_load || this.more_loading) {
				uni.stopPullDownRefresh()
				return false;
			}
			this.getUserInfo()
			this.load_page = 1
			this.getList();
			uni.stopPullDownRefresh()
		},
		onReachBottom() {
			if (!this.init_load && !this.more_loading && !this.is_last_page) this.getList()
		},
		onShow() {
			if (!this.init_load) {
				this.load_page = 1
				this.getList()
			}
		},
		methods: {
			getUserInfo() {
				xwy_api.getUserDetail(() => {
					this.userinfo = app.globalData.userinfo
				})
			},

			getList() {
				if (this.load_page === 1) {
					uni.showLoading({
						mask: true
					})
					this.list = []
					this.total_count = 0
					this.is_last_page = false
					if (!this.init_load) this.init_load = true
				} else {
					this.more_loading = true
				}

				xwy_api.ajax({
					url: 'front.user.integral_list/integral_list',
					data: {
						access_token: app.globalData.access_token,
						page: this.load_page,
						perpage: 10
					},
					success: res => {
						console.log('积分记录：', res)
						if (this.load_page === 1) {
							this.init_load = false
							uni.hideLoading()
						} else {
							this.more_loading = false
						}

						this.load_page++

						if (res?.data?.user_details?.integral) this.userinfo.integral = res.data.user_details.integral

						if (!res || !res.data || !res.data.integral_list) {
							this.is_last_page = true
							return false
						}

						const res_data = res.data.integral_list
						const list = res_data.data || []

						this.list = [...this.list, ...list]
						this.is_last_page = res_data.is_lastpage
						this.total_count = res_data.total || 0
					}
				})
			}
		}
	}
</script>

<style>
	.avatar {
		width: 60px;
		min-width: 60px;
		height: 60px;
		border-radius: 50%;
		display: block;
	}
	.list {
		border-top: 5px solid #f8f8f8;
	}
	.item {
		padding: 20px;
		border-bottom: 1px solid #eee;
	}

	.color-red- {
		color: #ee6b67;
	}
</style>
