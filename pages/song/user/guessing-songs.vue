<template>
    <view class="page">
        <view v-if="standby" class="standby text-center">
            <image
                src="https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/guessing_songs/logo.png"
                mode="widthFix"
            />

            <view style="height: 50px; width: 100%;"></view>

            <template v-if="standby_error">
                <view class="color-title font14">{{ standby_error }}</view>
                <navigator class="next-song-btn color-white font18" open-type="navigateBack">
                    返回
                </navigator>
            </template>

            <view v-if="!standby_error" class="next-song-btn color-white font18" @click="nextSong">开始猜歌</view>

            <xwy-ad v-if="show_ad" :activity_id="id" :ad_type="66"/>
        </view>

        <template v-if="!standby">
            <view class="" style="height: 1px;"></view>
            <view class="centerbox">
                <view class="top">
                    <view class="wenzi">第<text class="wenzi-text">{{ currentsong }}</text>首</view>
                    <view class="wenzi" style="float: right;">猜对歌曲数:{{ right_song }}</view>
                </view>
                <view
                    v-if="time_limit !== null"
                    class="time-limit text-center font-bold"
                    :class="time_limit < 10 ? 'font28' : 'font20'"
                >{{ time_limit }}</view>
                <view class="videoimg">
                    <view class="player-box" @click="playChange">
                        <view class="pointer-box">
                            <image
                                class="pointer-base"
                                src="https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/guessing_songs/dizuo.png"
                                mode="aspectFill"
                            />
                            <view class="pointer-image" :class="{'active-infinite': play}">
                                <image
                                    :class="{'active-play': play}"
                                    src="https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/guessing_songs/pointer.png"
                                />
                            </view>
                        </view>

                        <view class="backgroud" :class="{active: play}"></view>
                    </view>
                </view>

                <view style="width: 100%;height: 436rpx;"></view>

                <view
                    class="top"
                    :class="{'flex-center': !today_all_song || !times_data || !times_data['have_used']}"
                >
                    <view v-if="difficult_stars" class="wenzi flex-row">
                        <view>难度: </view>
                        <view style="padding-top: 2px; padding-left: 3px;">
                            <uni-rate :value="difficult_stars" :size="18" readonly/>
                        </view>
                    </view>
                    <view
                        v-if="today_all_song && times_data && times_data['have_used']"
                        class="wenzi"
                        style="float: right;"
                    >
                        今日已猜: {{ times_data['have_used'] }}/{{ today_all_song }}
                    </view>
                </view>
                <view v-if="allsong" class="caiduige">
                    <view class="progress">
                        <view
                          class="progressbar"
                          :style="{width: (currentsong>allsong?(1/allsong*504):(currentsong/allsong*504)) + 'rpx'}">
                        </view>
                    </view>
                    <view class="jindu">
                        <text class="jindutiao">{{currentsong}}/</text>{{allsong}}
                    </view>
                </view>
            </view>

            <view class="pt10 pb10">
                <xwy-ad v-if="show_ad && !popup_open" :activity_id="id" :ad_type="66"/>
            </view>

            <view v-if="question_types === 1 && question_options && question_options.length">
                <view v-for="(item, index) in question_options" :key="index" class="cai-button-box">
                    <view class="cai-button" @click="selectOption(item.title)">
                        <view class="cai-but">{{ item.title }}</view>
                    </view>
                </view>
            </view>


            <view v-if="question_types === 2" class="cai-button-box">
                <view class="cai-button">
                    <view class="cai-but flex-all-center">
                        <input
                            class="text-center"
                            v-model="input_answer"
                            placeholder="请输入歌曲名称"
                            focus
                            confirm-type="done"
                            @confirm="inputAnswerConfirm()"
                        />
                    </view>
                </view>

                <view class="next-song-btn next-song-btn-min color-white text-center" @click="inputAnswerConfirm">
                    确定
                </view>
            </view>

            <view v-if="question_explain_text" class="p10 text-center font14 color-content">
                提示: {{question_explain_text}}
            </view>
        </template>

        <uni-popup
            ref="result"
            type="top"
            :is-mask-click="false"
            mask-background-color="rgba(0,0,0,0.7)"
        >
            <xwy-ad v-if="show_ad && popup_open" :activity_id="id" :ad_type="3"></xwy-ad>
            <view class="text-center" style="padding-top: 10vh;">

                <image
                    :src="'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/guessing_songs/' + (result.is_right ? 'jbb.png': 'jblk.png')"
                    mode="widthFix"
                    style="width: 600rpx;"
                />
                <view class="color-warning p5">{{ result.info || '' }}{{ result.tips || '' }}</view>

                <view v-if="!have_next" class="color-white p5">今日猜歌次数已用完，明天再来吧！</view>

                <view v-if="have_next" class="next-song-btn color-white" @click="nextSong">下一首
                </view>
                <navigator v-if="!have_next" class="next-song-btn color-white" open-type="navigateBack">
                    返回
                </navigator>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import base64 from '@/utils/base64.js'

export default {
    data() {
        return {
            id: '',
            standby: true,
            standby_error: '',
            play: false,
            allsong: 0,
            currentsong: 0,
            right_song: 0,
            question_types: null,
            today_all_song: 0,
            question_explain_text: '',
            difficult_stars: null,
            question_options: null,
            result: {
                is_right: false,
                tips: '',
                info: ''
            },
            times_data: null,
            have_next: true,
            time_limit: null,
            input_answer: '',
            show_ad: false,
            popup_open: false
        }
    },
    beforeDestroy() {
        this.innerAudioContext.destroy()
    },

    onUnload() {
        if (this.in_guessing_songs) {
            const answer = '- back - ' + new Date().getTime()
            this.selectOption(answer, true)
        }

        this.getOpenerEventChannel?.()?.emit?.('updateUserDetails')
        
        if (this.innerAudioContext != null) {
            this.innerAudioContext.destroy()
        }
        if (this.time_limit_interval) clearInterval(this.time_limit_interval)
    },

    onLoad(e) {
        uni.showLoading({ mask: true })
        this.id = e.id
        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.init()
        })
    },

    methods: {
        async init() {
            this.innerAudioContext = null
            this.createInnerAudioContext()
            await this.getActivityDetails()
            await this.getOneSong()
            uni.hideLoading()
        },


        createInnerAudioContext() {
            let innerAudioContext = uni.createInnerAudioContext()
            // innerAudioContext.autoplay = true
            innerAudioContext.loop = true

            innerAudioContext.onError(err => {
                console.log('播放错误', err)
                this.play = false
                xwy_api.alert('播放错误')
            })

            innerAudioContext.onPlay(() => {
                this.play = true
                console.log('onPlay')
            })
            innerAudioContext.onPause(() => {
                this.play = false
                console.log('onPlay')
            })

            innerAudioContext.onEnded(() => {
                this.play = false
                console.log('onEnded')
            })
            this.innerAudioContext = innerAudioContext
        },

        playChange() {
            if (this.play) {
                this.innerAudioContext.pause()
            } else {
                this.innerAudioContext.play()
            }
        },

        async getActivityDetails() {
            let activity_detail = app.globalData['activity_detail']

            if (!activity_detail) {
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        access_token: app.globalData['access_token'],
                        active_id: this.id
                    }
                })

                activity_detail = res['data']['active_details']

            }

            if (!activity_detail?.rank_set?.closed_AD) {
                this.show_ad = true
            }

            this.activity_detail = activity_detail

            const guess_song = activity_detail?.conf?.active?.guess_song
            if (guess_song?.time_limit !== '0') this.set_time_limit = guess_song.time_limit
            if (guess_song?.daily_times !== '0') this.today_all_song = guess_song.daily_times
        },

        async getOneSong() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.guess_song.userGuess/get_one_song',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id
                }
            })
            if (res?.['data']?.times) this.setTimes(res.data.times)
            const details = res?.['data']?.['song_details']
            if (!details) {
                this.standby_error = res?.['info'] || '歌曲获取失败'
                return
            }
            this.next_song_details = details
            this.innerAudioContext.src = details.mp3
        },

        setTimes(times) {
            this.times_data = times
            if (times.left === 0) this.have_next = false
        },

        setTimeLimit() {
            if (!this.set_time_limit) return
            this.time_limit_interval = null
            let time_limit = Math.floor(this.set_time_limit)
            this.time_limit = time_limit
            this.time_limit_interval = setInterval(() => {
                time_limit--
                this.time_limit = time_limit
                if (time_limit <= 0) {
                    this.timeOut()
                    clearInterval(this.time_limit_interval)
                }
            }, 1000)
        },

        getSongDetails(details) {
            this.question_types = null
            this.question_options = null
            this.difficult_stars = null
            this.input_answer = ''
            this.currentsong++
            // this.innerAudioContext.src = details.mp3
            this.song_id = details.song_id
            this.question_types = details.conf_json.question_set.types
            this.difficult_stars = details.difficult_stars || null
            this.question_explain_text = details.conf_json.question_set.explain_text || ''
            if (details.conf_json.question_set.option_list) {
                this.question_options = JSON.parse(base64['decode'](details.conf_json.question_set.option_list.substring(1)))
            }

            // #ifdef MP-WEIXIN
            // 猜歌中，退出提交
            this.in_guessing_songs = true
            wx.enableAlertBeforeUnload({
                message: '猜歌中，是否退出？'
            })
            // #endif


            this.setTimeLimit()

            // 记录这首歌猜歌用时， 记录开始时间
            this.start_time = new Date().getTime()
        },

        timeOut() {
            const answer = '- time - out - ' + new Date().getTime()
            this.selectOption(answer)
        },

        inputAnswerConfirm() {
            if (!this.input_answer) {
                uni.showToast({
                    title: '请输入歌曲名称',
                    icon: 'none'
                })
                return
            }
            this.selectOption(this.input_answer)
        },

        async selectOption(title, not_show_tips = false) {
            // 记录这首歌猜歌用时
            const start_time = this.start_time
            const end_time = new Date().getTime()
            let seconds = Math.floor((end_time - start_time) / 1000)
            if (seconds <= 0) seconds = 1
            
            if (this.time_limit_interval) clearInterval(this.time_limit_interval)
            this.innerAudioContext.pause()
            uni.showLoading({ mask: true })
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.guess_song.userGuess/submit_guess_answer',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id,
                    answer: title,
                    song_id: this.song_id,
                    seconds
                }
            })
            uni.hideLoading()

            // #ifdef MP-WEIXIN
            this.in_guessing_songs = false
            wx.disableAlertBeforeUnload()
            // #endif

            if (not_show_tips) return

            let info = ''
            if (!res?.['status'] || res['status'] !== 1 || !res['data']) info = res['info'] || '出错了'

            if (info) {
                xwy_api.alert(info, {
                    success: () => uni.navigateBack()
                })
                return
            }

            if (res?.['data'].times) this.setTimes(res.data.times)
            if (res['data'].is_right) this.right_song++

            this.next_song_details = res['data'].next_song_details || null
            if (this.next_song_details) this.innerAudioContext.src = this.next_song_details.mp3
            this.result = {
                is_right: res['data'].is_right || '',
                tips: res['data']['reward_tips'] || '',
                info: res['info']
            }
            this.popupOpen('result')
        },

        nextSong() {
            this.standby = false
            this.popupClose('result')
            this.getSongDetails(this.next_song_details)
            this.innerAudioContext.play()
        },

        popupOpen(ref) {
            this.$refs[ref].open()
            this.popup_open = true
        },
        popupClose(ref) {
            this.popup_open = false
            this.$refs[ref].close()
        },
    }
}
</script>

<style>
.page {
    min-height: 100vh;
    box-sizing: border-box;
    background: radial-gradient(circle at 20px 20px, #AABEFA 5px, #B7C9FB 0);
    background-size: 30px 30px;
}

.standby {
    padding-top: 10vh;
}

.standby image {
    width: 250px;
}

.centerbox {
    width: 670rpx;
    height: 534rpx;
    background-image: url(https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/guessing_songs/dk.png);
    background-size: cover;
    margin: 25rpx auto 0;
    padding: 30rpx 35rpx 0 30rpx;
    position: relative;
}

.time-limit {
    position: absolute;
    top: 10rpx;
    left: 50%;
    width: 50px;
    height: 50px;
    margin-left: -25px;
    /* border-radius: 50%; */
    box-sizing: border-box;
    /* background-color: #FC8C25; */
    color: #FC8C25;
    /* border: 5px solid #24325A; */
    background-image: url(https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/guessing_songs/count_down.png);
    background-size: cover;
    line-height: 54px;
}

.centerbox .top .wenzi {
    float: left;
    color: #24325A;
    font-size: 35rpx;
    font-weight: bold;
}

.centerbox .top .wenzi .wenzi-text {
    color: #FC8C25;
}

.centerbox .videoimg {
    width: 580rpx;
    height: 280rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 0;
    right: 0;
    top: 150rpx;
    margin: auto;
}

.player-box {
    width: 150px;
    height: 150px;
    position: relative;
    border-radius: 50%;
    box-shadow: 0 3px 5px 0 rgba(227, 172, 159, 1);
    background-color: #0F0D3C;
}

.pointer-box {
    position: absolute;
    left: -38px;
    top: -22px;
    z-index: 2;
    width: 51px;
    height: 51px;
}

.pointer-base {
    width: 100%;
    height: 100%;
}

.pointer-image {
    position: absolute;
    left: 15px;
    top: 26px;
    width: 60px;
    height: 120px;
    transform-origin: 10px 8px;
    transform: rotate(25deg);
    transition: all 0.6s;
}

.pointer-image image {
    position: absolute;
    left: 0;
    top: 0;
    width: 60px;
    height: 120px;
    transform-origin: 10px 8px;
}

.active-play {
    animation: pointer 2.5s linear infinite;
}

.active-infinite {
    transform: rotate(-2deg);
}

.backgroud {
    z-index: 0;
    width: 100%;
    height: 100%;
    background-color: #333;
    border-radius: 50%;
    background: url(https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/guessing_songs/suspend.png);
    background-size: 100%;
    background-repeat: no-repeat;
}

.active {
    background: url(https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/guessing_songs/chassis.png);
    background-size: 100%;
    background-repeat: no-repeat;
    /* transform: rotate(-90deg); */
    animation: spin 6s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(1turn);
    }
}

@keyframes pointer {
    0% {
        transform: rotate(0deg);
    }

    10% {
        transform: rotate(1deg);
    }

    30% {
        transform: rotate(-1deg);
    }

    50% {
        transform: rotate(1deg);
    }

    70% {
        transform: rotate(0deg);
    }

    90% {
        transform: rotate(-2deg);
    }

    100% {
        transform: rotate(0deg);
    }
}

.centerbox .caiduige {
    width: 570rpx;
    margin-left: 40rpx;
    margin-top: 15rpx;
    height: auto;
    overflow: hidden;
}

.centerbox .caiduige .wenzi {
    width: 504rpx;
    height: 47rpx;
    text-align: center;
    line-height: 47rpx;
    float: left;
    color: #24325A;
    font-size: 30rpx;
    font-weight: bold;
}


.centerbox .caiduige .progress {
    width: 504rpx;
    height: 12rpx;
    border-radius: 12rpx;
    background-color: #FFEBA1;
    float: left;
    margin-top: 10rpx;
}

.centerbox .caiduige .progress .progressbar {
    height: 100%;
    border-radius: 12rpx;
    background-color: #FFAA33;
}

.centerbox .caiduige .jindu {
    float: right;
    height: 30rpx;
    line-height: 30rpx;
    color: #24325A;
    font-size: 25rpx;
    font-weight: bold;
}

.centerbox .caiduige .jindu .jindutiao {
    color: #FC8C25;
}

.cai-button-box {
    width: 489rpx;
    height: 140rpx;
    margin: 0 auto;
}

.cai-button {
    width: 489rpx;
    height: 95rpx;
    border-radius: 10rpx;
    background-color: #4B55BE;
    border: #3B4363 solid 2px;
}

.cai-but {
    width: 487rpx;
    height: 80rpx;
    display: flex;
    margin-bottom: 10rpx;
    border-radius: 10rpx;
    background-color: #ffffff;
    justify-content: center;
    line-height: 85rpx;
    color: #243899;
    font-size: 34rpx;
    font-weight: bold;
}

.cai-but input {
    width: 487rpx;
    padding: 10rpx;
}

.next-song-btn {
    width: 253px;
    height: 53px;
    line-height: 48px;
    background: url(https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/guessing_songs/anc.png);
    background-size: 100%;
    background-repeat: no-repeat;
    margin: 40rpx auto;
}

.next-song-btn-min {
    width: 169px;
    height: 35px;
    line-height: 31px;
}
</style>
