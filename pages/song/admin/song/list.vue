<template>
    <view class="page bg-background">
        <view class="search-count">
            <view
                class="search bg-white flex-kai p10"
            >
                <view class="input-view">
                    <view class="search-icon left-icon flex-all-center">
                        <uni-icons type="search" size="20" color="#bbbec4"/>
                    </view>
                    <input
                        class="input bg-background"
                        type="text"
                        confirm-type="search"
                        v-model="search_keyword"
                        @confirm="search"
                        placeholder="输入你要搜索的歌曲名"
                        placeholder-style="color:#bbbec4"
                    />
                    <view class="search-icon right-icon flex-all-center" @click="clearSearchKeyword">
                        <uni-icons
                            v-if="search_keyword"
                            type="close"
                            size="20"
                            color="#bbbec4"
                        />
                    </view>
                </view>
                <view class="search-go color-info pl10" @click="search">搜索</view>
            </view>

            <view v-if="total" class="total flex-kai font14 bg-white">
                <view class="p10 color-sub">共 {{ total }} 首歌曲</view>
                <view
                    v-if="!public"
                    class="p10 color-primary"
                    hover-class="navigator-hover"
                    @click="$refs.add_popup.open()"
                >
                    <text>添加歌曲</text>
                    <uni-icons type="forward" size="14" color="#2d8cf0"/>
                </view>
            </view>
        </view>

        <view class="list">
            <view class="item bg-white" v-for="item in list" :key="item.id">
                <view class="check">
                    <radio :checked="item.check" @click="checkItem(item)"/>
                </view>

                <view class="pl10 pr10">
                    <view class="color-title">{{ item.title }}</view>
                </view>

                <view class="item-opt-bar flex-kai">
                    <view class="pl10">
                        <view
                            v-if="item.id === player_id"
                            class="word-last-loading color-sub font12"
                            style="line-height: 34px;"
                        >播放中</view>
                    </view>
                    <view class="flex-row">
                        <template>
                            <view
                                v-if="item.id === player_id"
                                class="opt-item"
                                hover-class="navigator-hover"
                                @click="audioStop"
                            >
                                <text class="iconfont icon-pause color-sub font20"></text>
                            </view>
                            <view
                                v-else
                                class="opt-item"
                                hover-class="navigator-hover"
                                @click="audioPlay(item)"
                            >
                                <text class="iconfont icon-play color-sub font20"></text>
                            </view>
                        </template>
                        <template v-if="!public">
                            <view
                                class="opt-item"
                                hover-class="navigator-hover"
                                @click="toAddSong(item.song_id)"
                            >
                                <text class="iconfont icon-edit color-sub font20"></text>
                            </view>
                            <view
                                class="opt-item"
                                hover-class="navigator-hover"
                                @click="deleteItem(item)"
                            >
                                <text class="iconfont icon-delete color-sub font20"></text>
                            </view>
                        </template>
                    </view>
                </view>
            </view>
        </view>

        <view v-if="list.length" class="page-opt-bar flex-kai">
            <view>
                <radio :checked="check_all" @click="ckeckAllChange"/>
            </view>
            <view class="flex-row">
                <view v-if="!public" class="page-opt-item color-error" @click="deletes">批量删除</view>
                <view v-if="public" class="page-opt-item color-primary" @click="importSong">导入歌曲</view>
            </view>
        </view>

        <view v-if="loading" class="flex-all-center" :style="{'paddingTop': load_page === 1 ? '30vh' : '0'}">
            <load-ani></load-ani>
        </view>

        <view v-if="!loading && !list.length" class="no-list text-center">
            <text class="iconfont icon-empty-state color-disabled"></text>
            <view class="color-sub font14">暂无歌曲</view>
            <view v-if="!public" class="flex-all-center">
                <view
                    class="add-song-btn bg-primary color-white"
                    hover-class="navigator-hover"
                    @click="$refs.add_popup.open()"
                >添加歌曲</view>
            </view>
        </view>

        <uni-popup ref="add_popup" type="bottom" :safe-area="false">
            <view class="add-popup bg-white" @click="$refs.add_popup.close()">
                <view class="flex-row" style="padding-bottom: 10px;">
                    <view class="text-center" style="width: 50%;" @click="toAddSong(false)">
                        <uni-icons type="plus" size="30" color="#2d8cf0"/>
                        <view class="color-sub font14">手动添加</view>
                    </view>
                    <view class="text-center" style="width: 50%;" @click="toImportSong">
                        <uni-icons type="cloud-upload" size="30" color="#2d8cf0"/>
                        <view class="color-sub font14">导入歌曲</view>
                    </view>
                </view>
                <view class="popup-close text-center color-sub" @click="$refs.add_popup.close()">取消</view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'

export default {
    data() {
        return {
            loading: true,
            list: [],
            load_page: 1,
            is_last_page: false,
            total: 0,
            check_all: false,
            player_id: null,
            public: false,
            search_keyword: ''
        }
    },

    onReachBottom() {
        !this.loading && !this.is_last_page && this.getSongList()
    },

    beforeDestroy() {
        this.innerAudioContext.destroy()
    },

    onUnload() {
        if (this.innerAudioContext != null) {
            this.innerAudioContext.destroy()
        }
    },

    onLoad(e) {
        if (e.public) this.public = true
        if (!e.id) {
            this.loading = false
            uni.hideLoading()
            xwy_api.alert('请指定活动id')
            return false
        }
        this.id = e.id

        login.uniLogin(err => {
            if (err && err.errMsg) {
                this.loading = false
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.init()
        })
    },
    methods: {
        async init() {
            this.innerAudioContext = null
            await this.getSongList()
            this.loading = false

            this.createInnerAudioContext()
        },

        createInnerAudioContext() {
            let innerAudioContext = uni.createInnerAudioContext()
            innerAudioContext.autoplay = true

            innerAudioContext.onError(err => {
                console.log('播放错误', err)
                this.audioStop()
                xwy_api.alert('播放错误')
            })

            innerAudioContext.onPlay(() => {
                console.log('onPlay')
            })
            innerAudioContext.onCanplay(() => {
                console.log('onCanplay')
            })
            innerAudioContext.onEnded(() => {
                console.log('onEnded')
                this.player_id = null
            })
            this.innerAudioContext = innerAudioContext
        },

        audioPlay(item) {
            if (this.player_id) this.innerAudioContext.stop()
            this.player_id = item.id
            this.innerAudioContext.src = item.mp3
            this.innerAudioContext.play()
        },

        audioStop() {
            this.innerAudioContext.pause()
            this.player_id = null
        },

        clearSearchKeyword() {
            this.search_keyword = ''
            if (!this.search_keyword_tamp) return
            this.search()
        },

        async search() {
            this.loading = true
            this.load_page = 1
            await this.getSongList()
            this.loading = false
        },

        async getSongList() {
            if (this.load_page === 1) {
                this.list = []
                this.total = 0
                this.is_last_page = false
                this.check_all = false
            }

            const _data = {
                access_token: app.globalData['access_token'],
                page: this.load_page,
                perpage: 20
            }
            if (this.public) {
                _data.public = 1
            } else {
                _data.active_id = this.id
            }
            if (this.search_keyword) _data.title = this.search_keyword

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.guess_song.adminManageSong/song_list',
                data: _data
            })
            this.search_keyword_tamp = _data.title

            const data = res?.['data']?.list
            if (!data) {
                this.is_last_page = true
                return
            }
            const list = data.data || []
            list.forEach(v => v.check = false)
            this.list = [...this.list, ...list]
            this.checkAllCheck()
            this.load_page++
            this.is_last_page = data.is_lastpage
            this.total = data.total
        },

        toAddSong(song_id) {
            let url = './add?id=' + this.id
            if (song_id) url += `&song_id=${song_id}`
            uni.navigateTo({
                url,
                events: {
                    update: () => {
                        this.search()
                    }
                }
            })
        },

        checkItem(item) {
            item.check = !item.check
            this.checkAllCheck()
        },

        checkAllCheck() {
            this.check_all = this.list.every(v => v.check)
        },

        ckeckAllChange() {
            this.check_all = !this.check_all
            this.list.forEach(v => v.check = this.check_all)
        },

        deleteItem(item) {
            uni.showModal({
                title: '提示',
                content: `确定删除《${item.title}》?`,
                success: res => {
                    res.confirm && this.deleteAjax(item.id)
                }
            })
        },


        deletes() {
            const ids = this.list.filter(v => v.check).map(v => v.id)
            if (!ids?.length) {
                uni.showToast({
                    title: '请选择歌曲',
                    icon: 'none'
                })
                return
            }

            uni.showModal({
                title: '提示',
                content: `确定删除选中的${ids.length}首歌曲?`,
                success: res => {
                    res.confirm && this.deleteAjax(ids.join(','))
                }
            })
        },

        async deleteAjax(ids) {
            uni.showLoading({
                title: '删除中...'
            })
            const res = await xwy_api.deleteRecords(50, ids)

            if (!res?.['status'] || res['status'] !== 1) {
                xwy_api.alert(res?.['info'] || '删除失败')
                return
            }

            uni.showToast({
                title: '已删除',
                icon: 'success'
            })

            await this.search()
        },


        toImportSong() {
            uni.navigateTo({
                url: './list?public=1&id=' + this.id,
                events: {
                    update: () => {
                        this.search()
                    }
                }
            })
        },

        importSong() {
            const ids = this.list.filter(v => v.check).map(v => v.song_id)
            if (!ids?.length) {
                uni.showToast({
                    title: '请选择歌曲',
                    icon: 'none'
                })
                return
            }

            uni.showModal({
                title: '提示',
                content: `确定将选中的${ids.length}首歌曲导入到活动曲库?`,
                success: res => {
                    res.confirm && this.importSongAjax(ids.join(','))
                }
            })
        },

        async importSongAjax(ids) {
            uni.showLoading({
                title: '导入中...'
            })

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.guess_song.adminManageSong/selected_music_in_active',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id,
                    ids
                }
            })
            uni.hideLoading()

            if (!res?.['status'] || res['status'] !== 1) {
                xwy_api.alert(res?.['info'] || '导入失败')
                return
            }

            const info = res['info'] || '导入成功'
            uni.showToast({
                title: info,
                icon: info.length <= 7 ? 'success' : 'none'
            })

            const eventsChannel = this.getOpenerEventChannel()
            eventsChannel?.emit && eventsChannel.emit('update')

            setTimeout(() => {
                uni.navigateBack()
            }, 1000)
        }
    }
}
</script>

<style>
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-top: 100px;
    padding-bottom: 80px;
}
.search-count {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
}

.search, .search .input {
    height: 40px;
    line-height: 40px;
}
.input-view {
    position: relative;
}
.search-icon {
    position: absolute;
    width: 30px;
    height: 40px;
    top: 0;
}
.left-icon{
    left: 0;
}
.right-icon {
    right: 0;
}
.search .input {
    width: calc(100vw - 90px);
    padding: 0 30px;
    border-radius: 20px;
    box-sizing: border-box;
}
.search-go {
    width: 60px;
    min-width: 60px;
    text-align: right;
}


/* #ifdef H5 */
.search-go {
    padding-right: 10px;
    box-sizing: border-box;
}
@media screen and (min-width: 500px) {
    .search-count {
        width: 500px;
        left: calc(((100vw - 500px) / 2) - 10px);
    }
    .search .input {
        width: 440px;
    }
}
/* #endif */


.item {
    margin: 10px;
    padding: 10px 10px 10px 40px;
    border-radius: 10px;
    position: relative;
}

.check {
    position: absolute;
    left: 10px;
    top: 50%;
    margin-top: -15px;
}

.item-opt-bar {
    border-top: 1px solid #eee;
    margin-top: 10px;
    padding-top: 10px;
}


.opt-item {
    width: 32px;
    height: 32px;
    border: 1px solid #eee;
    line-height: 32px;
    text-align: center;
    border-radius: 50%;
    margin-left: 10px;
}

.add-song-btn {
    margin-top: 50px;
    width: 200px;
    line-height: 44px;
    border-radius: 22px;
}

.add-popup {
    padding-top: 10px;
    border-radius: 10px 10px 0 0;
}
.popup-close {
    padding-bottom: 20px;
    padding-top: 10px;
    border-top: 1px solid #eee;
}

.page-opt-bar {
    position: fixed;
    width: 100%;
    left: 0;
    bottom: 0;
    background-color: #fff;
    border-top: 1px solid #eee;
    padding: 10px 10px 15px;
    box-sizing: border-box;
}
.page-opt-item {
    line-height: 32px;
    padding: 0 10px;
    border: 1px solid #eee;
    border-radius: 17px;
    margin-left: 10px;
    font-size: 14px;
}
</style>
