<template>
	<view class="page">
		<view class="form">
			<view class="form-item">
			    <view class="top color-content">
			        <text>歌曲名称</text>
			        <text class="color-error font16"> *</text>
			    </view>
			    <view class="bottom font16">
			        <input
						class="input"
						v-model="title"
						placeholder="请输入歌曲的名称，此名称用户看不到"
					/>
			    </view>
			</view>

			<view class="form-item">
				<view class="top color-content">
				    <text>歌曲播放地址</text>
				    <text class="color-error font16"> *</text>
				</view>
				<view class="bottom font16">
				    <input
						class="input"
						v-model="mp3"
						placeholder="请输入歌曲播放地址"
					/>
				</view>
			</view>

			<view class="form-item">
				<view class="top color-content">
					<view class="top color-content">
						<view>答案类型</view>
						<view
							class="color-sub font12"
							v-for="item in question_types_options"
							:key="item.types"
						>
							【{{item.title}}】{{item.tips}}
						</view>
					</view>
				</view>
				<view class="bottom ptm5 flex-row color-content">
					<view
						class="pr10 flex-row"
						v-for="item in question_types_options"
						:key="item.types"
						@click="questionTypesChange(item.types)"
					>
						<radio :checked="conf_json.question_set.types === item.types"/>
						<view style="line-height: 25px;">{{item.title}}</view>
					</view>
				</view>
			</view>

			<view class="form-item">
				<view class="top color-content">答案选项</view>
				<view class="bottom ptm5">
				    <view class="flex-kai font14 color-content text-center">
						<view class="option-title">标题</view>
						<view class="option-is-right">正确答案</view>
						<view class="option-delete"></view>
					</view>
					<view class="option-list">
						<view
							v-for="(item, index) in conf_json.question_set.option_list"
							:key="index"
							class="option-item flex-kai ptm5"
						>
							<view class="option-title">
								<uni-easyinput v-model="item.title" placeholder="请输入答案选项"/>
							</view>
							<view class="option-is-right flex-all-center">
								<view>
									<checkbox
										:disabled="conf_json.question_set.types === 2"
										:checked="item.is_right === 1"
										@click="setRightOption(item)"
									/>
								</view>
							</view>
							<view class="option-delete flex-all-center">
								<view>
									<text
										class="iconfont icon-delete color-error"
										@click="conf_json.question_set.option_list.splice(index, 1)"
									></text>
								</view>
							</view>
						</view>

						<view
							v-if="conf_json.question_set.option_list.length < 6"
							class="flex-all-center"
						>
							<view
								class="color-primary font14 p5"
								@click="addOption"
							>添加选项</view>
						</view>
					</view>
				</view>
			</view>

			<view class="form-item">
				<view class="top color-content">歌曲提示</view>
				<view class="bottom font16">
				    <input class="input" v-model="conf_json.question_set.explain_text" />
				</view>
			</view>

			<view class="form-item">
				<view class="top color-content">歌曲难度系数</view>
				<view class="bottom ptm5">
				    <uni-rate v-model="difficult_stars"/>
				</view>
			</view>
		</view>

		<view class="save bg-white">
			<view class="save-btn text-center bg-primary color-white" @click="validate">保存</view>
		</view>
	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'
	import base64 from '@/utils/base64.js'

	export default {
		data() {
			return {
				title: '',
				mp3: '',
				difficult_stars: 1,
				conf_json: {
					question_set: {
						types: 1,
						option_list: [
							{title: "", is_right: 0}
						],
						explain_text: ''
					}
				},
				question_types_options: [
					{ types: 1, title: '选择', tips: '用户需从下列答案选项中选择正确的答案。' },
					{ types: 2, title: '填空', tips: '用户输入的答案与下列任意答案选项一样即为正确。'  }
				]
			}
		},
		onLoad(e) {
			uni.showLoading()
		    if (!e.id) {
		        this.loading = false
		        uni.hideLoading()
		        this.error = '请指定活动id'
		        return false
		    }

		    this.id = e.id
			if (e.song_id) {
				this.song_id = e.song_id
				uni.setNavigationBarTitle({
					title: '修改歌曲'
				})
			}
		    login.uniLogin(err => {
		        if (err && err.errMsg) {
					this.loading = false
		            uni.showModal({
		                title: err.errTitle || '提示',
		                content: err.errMsg,
		                showCancel: false
		            })
		            return false
		        }

				this.init()
		    })
		},
		methods: {
			async init() {
				if (this.song_id) {
					await this.getSongDetails()
				}
				uni.hideLoading()
			},

			async getSongDetails() {
				const res = await xwy_api.request({
					url: 'front.flat.sport_step.guess_song.adminManageSong/song_details',
					data: {
						access_token: app.globalData.access_token,
						song_id: this.song_id
					}
				})

				if (!res?.data?.song_details?.song_id) {
					xwy_api.alert('歌曲获取失败', {
						success: () => uni.navigateBack()
					})
					return
				}

				const details = res.data.song_details
				this.title = details.title || ''
				this.mp3 = details.mp3 || ''
				this.difficult_stars = details.difficult_stars || 1
				if (details.conf_json) this.conf_json = details.conf_json
			},

			questionTypesChange(types) {
				if (types === this.conf_json.question_set.types) return
				let have_is_right = false
				this.conf_json.question_set.option_list.forEach(v => {
					if (v.is_right === 1) {
						if (have_is_right) v.is_right = 0
						have_is_right = true
					}
					if (types === 2) v.is_right = 1
				})
				this.conf_json.question_set.types = types
			},

			setRightOption(item) {
				if (this.conf_json.question_set.types === 1) {
					this.conf_json.question_set.option_list.forEach(v => v.is_right = 0)
					item.is_right = 1
				}
				// if (this.conf_json.question_set.types === 2) {
				// 	item.is_right = item.is_right === 1 ? 0 : 1
				// }
			},

			addOption() {
				this.conf_json.question_set.option_list.push({title: '', is_right: this.conf_json.question_set.types === 2 ? 1 : 0})
			},

			validate() {
				const errToast = title => {
					uni.showToast({
						title,
						icon: title.length <= 7 ? 'error' : 'none'
					})
				}

				if (!this.title) {
					errToast('请输入歌曲名称')
					return
				}
				if (!this.mp3) {
					errToast('请输入歌曲播放地址')
					return
				}
				if (!this.conf_json.question_set.option_list.length) {
					errToast('请添加答案选项')
					return
				}

				let title_none_count = 0,
					is_right_count = 0
				this.conf_json.question_set.option_list.forEach(v => {
					if (!v.title) title_none_count++
					if (v.is_right === 1) is_right_count++
				})
				if (title_none_count > 0) {
					errToast('答案选项标题填写不完整')
					return
				}
				if (is_right_count === 0) {
					errToast('请选择正确答案')
					return
				}
				if (is_right_count > 1 && this.conf_json.question_set.types === 1) {
					errToast('答案选择类型为“选择”时，只能有一个正确答案')
					return
				}

				this.save()
			},

			async save() {
				uni.showLoading({
					title: '保存中...'
				})

				const data = {
					access_token: app.globalData.access_token,
					active_id: this.id,
					title: this.title,
					mp3: this.mp3,
					difficult_stars: this.difficult_stars,
					conf_json: base64.encode(JSON.stringify(this.conf_json))
				}
				if (this.song_id) data.song_id = this.song_id
				const res = await xwy_api.request({
					url: 'front.flat.sport_step.guess_song.adminManageSong/create_song',
					data
				})

				if (!res?.status || res.status !== 1) {
					xwy_api.alert(res?.info || '保存失败')
					return
				}

				const eventChannel = this.getOpenerEventChannel()
				eventChannel?.emit && eventChannel.emit('update')

				uni.showToast({
					title: '保存成功',
					icon: 'success'
				})
				if (!this.song_id) {
					setTimeout(() => {
						uni.navigateBack()
					}, 1000)
				}
			}
		}
	}
</script>

<style>
.page {
	padding-bottom: 80px;
}

.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}


.form-item .textarea {
    width: 100%;
    line-height: 16px;
    padding: 10px 0;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}


.option-title {
	width: calc(100% - 120px);
}
.option-is-right {
	width: 60px;
}
.option-delete {
	width: 40px;
}

.save {
	position: fixed;
	bottom: 0;
	left: 0;
	padding: 10px;
	padding-bottom: 20px;
	width: 100%;
	box-sizing: border-box;
	border-top: 1px solid #eee;
}
.save-btn {
	line-height: 44px;
	border-radius: 22px;
}
</style>
