<template>
    <view class="page">
        <view v-if="success" class="save-success bg-white">
            <view class="icon text-center">
                <uni-icons type="checkbox-filled" size="100" color="#19be6b"/>
                <view class="color-content font16">活动{{ form_data.active_id ? '修改' : '创建' }}成功！</view>
            </view>
            <view class="flex-all-center">
                <button class="bg-success color-white" style="width: 200px;" @click="lookMyActivityList">
                    查看我的活动
                </button>
            </view>
        </view>

        <view class="type-list flex-row bg-white">
            <view
                class="type-item color-content"
                v-for="(item, index) in type_list"
                :key="index"
                :class="{'active-type': item.id === type_id}"
                :style="{width: 'calc(100% / ' + type_list.length + ')'}"
                @click="type_id = item.id"
            >{{ item.title }}
            </view>
        </view>

        <add-activity-tips ref="add_activity_tips"/>

        <view class="form">
            <template v-if="type_id === 1">
                <view class="form-item">
                    <view class="top color-content">
                        <text>活动名称</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="text" v-model="form_data.name"
                               placeholder="请输入活动名称"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">主办方</view>
                    <view class="bottom font16">
                        <input class="input" type="text" v-model="form_data.organizer"
                               placeholder="请输入主办方单位名称"/>
                    </view>
                </view>

                <template v-if="!form_data.active_id">
                    <view class="form-item">
                        <view class="top color-content">
                            <text>手机号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="number" v-model="form_data.mobile"
                                   placeholder="请输入真实手机号"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <text>微信号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="text" v-model="form_data.wechat_num"
                                   placeholder="请输入真实微信号"/>
                        </view>
                    </view>
                </template>

                <view class="form-item">
                    <view class="top color-content">活动开始时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.begin_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">活动结束时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.end_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view v-if="!conf.active.news.news_id" class="form-item">
                    <view class="top color-content">活动说明</view>
                    <view class="bottom font16">
				                <textarea class="textarea" maxlength="-1" auto-height v-model="form_data.content"
                                  placeholder="请输入活动说明"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>活动说明文章</view>
                        <view class="font12 color-sub">
                            绑定文章后，活动说明将会显示文章内容，不会显示上面填写的活动说明
                        </view>
                    </view>
                    <view class="bottom font16">
                        <view class="flex-kai" @click="toSelNews">
                            <view class="view">
                                <view v-if="conf.active.news.news_id">
                                    {{ conf.active.news.news_title || conf.active.news.news_id }}
                                </view>
                                <view v-else class="color-sub">选择文章</view>
                            </view>
                            <view class="flex-all-center">
                                <view v-if="conf.active.news.news_id" class="color-sub font12"
                                      style="width: 30px;" @click.stop="deleteNews">解绑
                                </view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>
            </template>


            <template v-if="type_id === 3">
                <view class="form-item">
                    <view class="top color-content">活动参与方式</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.enter_types_list"
                                :value="form_options.enter_types_list.findIndex(v => v.value === conf.active.enter_types)"
                                range-key="title"
                                @change="conf.active.enter_types = form_options.enter_types_list[$event.detail.value].value"
                            >
                                {{ form_options.enter_types_list.find(v => v.value === conf.active.enter_types).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="conf.active.enter_types === 2" class="form-item">
                    <view class="top color-content">
                        <text>活动密码</text>
                        <text v-if="!have_password" class="color-error font16"> *</text>
                        <text class="color-sub font12 pl5">(3-20位)</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" v-model="conf.active.password" :maxlength="20"
                               :placeholder="have_password ? '已设置密码，无需修改密码不用填写' : '请输入活动密码'"/>
                    </view>
                </view>


                <view class="form-item">
                    <view class="top color-content">
                        <view>参与活动需要填写的信息</view>
                        <view class="font12 color-error">注意: 第一项为排行榜显示的姓名, 无法删除</view>
                    </view>
                    <view class="bottom font16">
                        <view class="ptm5">
                            <view class="ptm5 must-submit-list" v-for="(item, index) in conf.must_submit"
                                  :key="index">
                                <view class="flex-row">
                                    <view class="flex-row">
                                        <view style="width: 120px;">
                                            <uni-easyinput v-model="item.title" placeholder="请输入内容"/>
                                        </view>
                                        <template>
                                            <template v-if="index === 0">

                                                <picker :range="['选填', '必填']" :value="item.rules"
                                                        @change="mustItemRulesChange($event, index)">
                                                    <view class="must-rules-picker">
                                                        <text class="color-content font14">
                                                            {{ item.rules === 0 ? '选填' : '必填' }}
                                                        </text>
                                                        <text class="iconfont icon-more color-sub font14"></text>
                                                    </view>
                                                </picker>
                                                <view class="must-rules-picker">
                                                    <text class="color-content font14">
                                                        {{ item.types === 1 ? '文本' : '单选' }}
                                                    </text>
                                                    <text class="iconfont icon-more color-white font14"></text>
                                                </view>
                                            </template>
                                            <template v-else>

                                                <picker :range="['选填', '必填']" :value="item.rules"
                                                        @change="mustItemRulesChange($event, index)">
                                                    <view class="must-rules-picker">
                                                        <text class="color-content font14">
                                                            {{ item.rules === 0 ? '选填' : '必填' }}
                                                        </text>
                                                        <text class="iconfont icon-more color-sub font14"></text>
                                                    </view>
                                                </picker>

                                                <picker :range="['文本', '单选']" :value="item.types - 1"
                                                        @change="mustItemTypesChange($event, index)">
                                                    <view class="must-rules-picker">
                                                        <text class="color-content font14">
                                                            {{ item.types === 1 ? '文本' : '单选' }}
                                                        </text>
                                                        <text class="iconfont icon-more color-sub font14"></text>
                                                    </view>
                                                </picker>
                                            </template>
                                        </template>
                                    </view>
                                    <view v-if="index !== 0" class="delete-rules font14 color-error"
                                          @click="conf.must_submit.splice(index, 1)">删除
                                    </view>
                                </view>

                                <view v-if="item.types === 2" class="pl10">
                                    <view class="must-options-item flex-row"
                                          v-for="(item_, index_) in item.options" :key="index_">
                                        <view class="color-sub delete-rules text-right"
                                              style="width: 20px; padding: 0 5px 0 0;">
                                            {{ index_ + 1 }}:
                                        </view>
                                        <view style="width: 200px;">
                                            <uni-easyinput v-model="item_.text" placeholder="请输入内容"/>
                                        </view>
                                        <view class="delete-rules">
                                            <text class="color-error font14"
                                                  @click="deleteOptionsItem(index, index_)">删除
                                            </text>
                                        </view>
                                    </view>
                                    <view class="flex-row">
                                        <view class="color-sub font14 ptm5" @click="addOption(index)">
                                            + 添加新选项
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <view class="flex-row">
                                <view class="color-primary font14 ptm5" @click="addMust">+ 添加新项</view>
                            </view>
                        </view>
                    </view>
                </view>

            </template>

            <template v-if="type_id === 2">
                <active-image-set :active-id="form_data.active_id" :rankSet="rank_set"
                                  :logo.sync="form_data.logo"
                                  :screen-pic.sync="conf.active.screen_pic"
                                  :top-rank-banner.sync="conf.active.top_rank_banner"/>
            </template>

            <template v-if="type_id === 6">
                <view class="form-item">
                    <view class="top color-content">奖励单位名称</view>
                    <view class="bottom font16">
                        <input class="input" v-model="integral.unit" maxlength="10"
                               placeholder="请输入奖励单位名称 如: 金币"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">猜对了奖励{{ integral.unit || '金币' }}数额</view>
                    <view class="bottom font16 flex-row ptm5">

                        <picker
                            :range="['增加', '扣减']"
                            :value="integral.guess_song_success.type === '+' ? 0 : 1"
                            @change="integral.guess_song_success.type = $event.detail.value === '0' ? '+' : '-'"
                        >
                            <view class="must-rules-picker">
                                <text class="color-content font14">
                                    {{ integral.guess_song_success.type === '+' ? '增加' : '扣减' }}
                                </text>
                                <text class="iconfont icon-more color-sub font14"></text>
                            </view>
                        </picker>
                        <view class="plr5" style="width: 100px;">
                            <uni-easyinput v-model="integral.guess_song_success.value"
                                           type="number" maxlength="8" placeholder="请输入整数"/>
                        </view>
                        <view style="line-height: 36px;">{{ integral.unit || '金币' }}</view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">猜错了奖励{{ integral.unit || '金币' }}数额</view>
                    <view class="bottom font16 flex-row ptm5">

                        <picker
                            :range="['增加', '扣减']"
                            :value="integral.guess_song_fail.type === '+' ? 0 : 1"
                            @change="integral.guess_song_fail.type = $event.detail.value === '0' ? '+' : '-'"
                        >
                            <view class="must-rules-picker">
                                <text class="color-content font14">
                                    {{ integral.guess_song_fail.type === '+' ? '增加' : '扣减' }}
                                </text>
                                <text class="iconfont icon-more color-sub font14"></text>
                            </view>
                        </picker>
                        <view class="plr5" style="width: 100px;">
                            <uni-easyinput v-model="integral.guess_song_fail.value"
                                           type="number" maxlength="8" placeholder="请输入整数"/>
                        </view>
                        <view style="line-height: 36px;">{{ integral.unit || '金币' }}</view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">每日猜歌歌曲数限制</view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="conf.active.guess_song.daily_times"
                               maxlength="10" placeholder="填0或不填则不限制歌曲数"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <text>每首歌猜歌限时</text>
                        <text class="font12 color-sub pl5">(单位: 秒)</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="conf.active.guess_song.time_limit"
                               maxlength="10" placeholder="填0或不填则不限制时间"/>
                    </view>
                </view>


                <view class="form-item">
                    <view class="top color-content">活动详情页面图标扩展</view>
                    <view class="bottom font16 pt5 pb5">
                        <view
                            class="flex-kai pl10 pr10"
                            style="border: 1px solid #eee; border-radius: 5px; margin-bottom: 5px;"
                            v-for="(item, index) in conf.active.detail_icon_list"
                            :key="index"
                        >
                            <view>
                                <view style="padding: 5px 0;" @click="selIcon(index)">
                                    <text class="color-content pr5">显示图标:</text>
                                    <text :class="'iconfont font28 color-primary ' + item.icon"></text>
                                    <text class="pl5 color-sub font12">(点击更换)</text>
                                </view>

                                <picker :range="detail_icon_conf" range-key="title"
                                        @change="detailIconItemChange($event, index)">
                                    <view style="padding: 5px 0;">
                                        <text class="pr5 color-content">跳转类型:</text>
                                        <text>{{ detail_icon_conf[item.type].title }}</text>
                                    </view>
                                </picker>
                                <view class="flex-row" style="padding: 5px 0;">
                                    <text class="pr5 color-content">显示文字:</text>
                                    <input v-model="item.title" placeholder="6个字以内"
                                           style="border-bottom: 1px solid #eee;"/>
                                </view>
                                <view style="padding: 5px 0;" @click="bindNewsOrCategory(index)">
                                    <text class="pr5 color-content">
                                        绑定{{ item.type === 0 ? '文章' : '分类' }}:
                                    </text>
                                    <template>
                                        <text v-if="item.id">{{ item.name }}</text>
                                        <text class="color-sub" v-else>
                                            请选择{{ item.type === 0 ? '文章' : '分类' }}
                                        </text>
                                    </template>
                                </view>
                            </view>
                            <view class="pt5" @click="delDetailIcon(index)">
                                <text class="iconfont icon-delete color-error font20"></text>
                            </view>
                        </view>

                        <view class="color-primary pt5 pb5" @click="addDetailIcon">添加扩展</view>
                    </view>
                </view>

                <active-share-set :active-id="form_data.active_id" :rank-set="rank_set"
                                  :qrcode-logo.sync="conf.active.qrcode_logo"
                                  :share-image.sync="conf.active.share_image"
                                  :share-title.sync="conf.active.share_title"/>
            </template>
        </view>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view class="login-btn color-white text-center font18 bg-primary" :disabled="loading"
                  @click="save">{{ form_data.active_id ? '保存' : '创建活动' }}
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import base64 from '@/utils/base64.js'
import {pinyin} from 'pinyin-pro'


export default {
    data() {
        return {
            loading: true,
            success: false,
            type_list: [
                {title: '基本信息', id: 1},
                {title: '活动设置', id: 3},
                {title: '扩展设置', id: 6},
                {title: '活动图片', id: 2}
            ],
            type_id: 1,
            form_data: {
                name: '',
                wechat_num: '',
                mobile: '',
                organizer: '',
                content: '',
                begin_time: new Date(utils.getDay(0, true, '/') + ' 00:00:00').getTime(),
                end_time: new Date(utils.getDay(30, true, '/') + ' 23:59:59').getTime(),
                logo: ''
            },
            conf: {
                active: {
                    news: {
                        news_id: '',
                        news_title: ''
                    },
                    enter_types: 1,
                    password: '',
                    screen_pic: '',
                    top_rank_banner: [],
                    guess_song: {
                        daily_times: '',
                        time_limit: ''
                    },
                    detail_icon_list: [],
                    share_title: '',
                    share_image: '',
                    qrcode_logo: ''
                },
                must_submit: JSON.parse(JSON.stringify(this.xwy_config.defaultMustSubmit))
            },
            integral: {
                guess_song_success: {
                    value: 10,
                    type: '+'
                },
                guess_song_fail: {
                    value: 10,
                    type: '-'
                },
                unit: "金币"
            },
            top_rank_banner_max_count: 6,
            rank_set: {},
            pic_list: [],
            have_password: false,
            form_options: {
                enter_types_list: [
                    {title: '自由报名参与活动', value: 1},
                    {title: '需要输入密码才能进入活动', value: 2},
                    {title: '报名需要审核通过才能参与活动', value: 3}
                ]
            },
            detail_icon_conf: [
                {
                    type: 0,
                    title: '文章详情'
                }
            ]
        }
    },
    onLoad(e) {
        if (e.id) {
            this.form_data.active_id = e.id
        } else {
            this.types = Number(e.types)
            this.$nextTick(() => this.$refs.add_activity_tips.open(this.types))
            this.form_data.logo = this.xwy_config.create_active_default_logo
        }
        uni.setNavigationBarTitle({
            title: e.id ? '修改活动' : `创建${e.name || '活动'}`
        })
        uni.showLoading({
            mask: true
        })


        login.uniLogin(err => {

            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }


            if (e.id) {
                this.getDetail()
                return false
            }

            const evn_version = app.globalData['evn_version']

            if (evn_version === 'develop') {
                this.form_data.mobile = 12345678910
                this.form_data.wechat_num = 12345678910
                this.form_data.organizer = '伟杰测试'
            }

            // 开发版和体验版不检查重复创建活动
            if (evn_version === 'develop' || evn_version === 'trial') {
                uni.hideLoading()
                return false
            }

            this.checkUserCanCreateActive()
        })
    },
    methods: {
        ipListItemChange(value, index) {
            this.conf.active.ip_set.ip_city[index] = value
            this.$forceUpdate()
        },

        checkUserCanCreateActive() {

            xwy_api.ajax({
                url: 'front.flat.sport_step.admin/check_user_can_create_active',
                data: {
                    access_token: app.globalData['access_token']
                },
                success: res => {
                    console.log('检查是否能创建活动', res)
                    uni.hideLoading()
                    this.loading = false
                    if (!res.status) {
                        uni.showModal({
                            title: '提示',
                            content: res.info || '暂时不能创建活动',
                            showCancel: false,
                            success: () => {
                                uni.navigateBack()
                            }
                        })
                    }
                }
            })

        },

        getDetail() {
            const errModal = content => {
                uni.hideLoading()
                uni.showModal({
                    title: '提示',
                    content: content || '活动获取失败',
                    showCancel: false,
                    success: res => {
                        uni.navigateBack()
                    }
                })
            }

            xwy_api.getActivityDetail(this.form_data.active_id, res => {
                if (res.data?.active_details) {
                    const detail = res.data.active_details

                    this.detailInit(detail)
                } else {
                    errModal(res.info)
                }
            })
        },


        detailInit(data) {
            this.types = data.types
            this.form_data.name = data.name
            this.form_data.begin_time = data.begin_time * 1000
            this.form_data.end_time = data.end_time * 1000
            if (data.organizer) this.form_data.organizer = data.organizer
            if (data.logo) this.form_data.logo = data.logo
            if (data.content) this.form_data.content = data.content

            const conf = data.conf

            if (conf.active.password) {
                this.old_password = conf.active.password
                delete conf.active.password
                this.have_password = true
            }
            if (conf.active.integral) {
                this.integral = {
                    unit: conf.active.integral.unit || '金币',
                    guess_song_success: {
                        type: conf.active.integral.guess_song_success < 0 ? '-' : '+',
                        value: Math.abs(conf.active.integral.guess_song_success)
                    },
                    guess_song_fail: {
                        type: conf.active.integral.guess_song_fail < 0 ? '-' : '+',
                        value: Math.abs(conf.active.integral.guess_song_fail)
                    }
                }
            }

            if (conf.active.share_title) this.conf.active.share_title = conf.active.share_title
            if (conf.active.share_image) this.conf.active.share_image = conf.active.share_image
            if (conf.active.qrcode_logo) this.conf.active.qrcode_logo = conf.active.qrcode_logo

            conf.active.detail_icon_list ||= []

            this.conf = conf

            if (data.rank_set) {
                const rank_set = data.rank_set

                this.rank_set = rank_set


                if (rank_set.closed_AD && rank_set.closed_AD === 1) {
                    this.detail_icon_conf.push({
                        type: 1,
                        title: '文章列表'
                    })
                }
            }

            uni.hideLoading()
        },

        addDetailIcon() {
            this.conf.active.detail_icon_list.push({
                icon: 'icon-dating',
                title: '',
                type: 0,
                id: '',
                name: ''
            })
        },
        delDetailIcon(index) {
            this.conf.active.detail_icon_list.splice(index, 1)
        },
        selIcon(index) {
            uni.navigateTo({
                url: '/pages/other/icon-list/icon-list',
                events: {
                    selIcon: class_name => {
                        this.conf.active.detail_icon_list[index].icon = class_name
                    }
                }
            })
        },
        detailIconItemChange(e, index) {
            this.conf.active.detail_icon_list[index].id = ''
            this.conf.active.detail_icon_list[index].name = ''
            this.conf.active.detail_icon_list[index].type = this.detail_icon_conf[e.detail.value].type
        },

        bindNewsOrCategory(index) {
            const item = this.conf.active.detail_icon_list[index]
            if (item.type === 0) this.bindNews(index)
            if (item.type === 1) this.bindCategory(index)
        },
        bindNews(index) {
            uni.navigateTo({
                url: '/pages/news/list?type=user&is_sel=true',
                events: {
                    selNews: data => {
                        this.conf.active.detail_icon_list[index].id = data.id
                        this.conf.active.detail_icon_list[index].name = data.title
                    }
                }
            })
        },
        bindCategory(index) {
            uni.navigateTo({
                url: '/pages/category/list?types=8&is_sel=1',
                events: {
                    selCategory: data => {
                        this.conf.active.detail_icon_list[index].id = data.id
                        this.conf.active.detail_icon_list[index].name = data.name
                    }
                }
            })
        },


        toSelNews() {
            uni.navigateTo({
                url: '/pages/news/list?type=user&is_sel=true',
                events: {
                    selNews: data => {
                        this.conf.active.news.news_id = data.id
                        this.conf.active.news.news_title = data.title
                    }
                }
            })
        },


        deleteNews() {
            this.conf.active.news.news_id = ''
            this.conf.active.news.news_title = ''
        },


        mustItemRulesChange(e, index) {
            this.conf.must_submit[index].rules = Number(e.detail.value)
        },
        mustItemTypesChange(e, index) {
            const value = Number(e.detail.value) + 1
            this.conf.must_submit[index].types = value
            if (value === 2) {
                this.conf.must_submit[index].options = this.conf.must_submit[index].options || [{text: ''}]
            }
        },
        addMust() {
            this.conf.must_submit.push({
                name: '',
                rules: 0,
                title: '',
                types: 1
            })
        },

        addOption(index) {
            this.conf.must_submit[index].options.push({text: ''})
            this.$forceUpdate()
        },
        deleteOptionsItem(index, index_) {
            if (this.conf.must_submit[index].options.length === 1) {
                uni.showToast({
                    title: '请至少保留一个选项',
                    icon: 'none',
                    mask: true
                })
                return false
            }
            this.conf.must_submit[index].options.splice(index_, 1)
            this.$forceUpdate()
        },


        changeImage(key, index) {
            if (!this.rank_set?.closed_AD) {
                const options = {
                    screen_pic: '无法设置开屏大图，请联系客服设置',
                    top_rank_banner: '无法设置排行榜轮播图，请联系客服设置'
                }
                if (options[key]) return this.$uni.showModal(options[key])
            }

            let url = '/pages/other/image_upload_or_select'
            if (this.form_data.active_id) url += `?active_id=${this.form_data.active_id}`
            uni.navigateTo({
                url,
                events: {
                    newImg: src => {
                        switch (key) {
                            case 'logo':
                                this.form_data.logo = src
                                break
                            case 'top_rank_banner':
                                this.conf.active.top_rank_banner.push(src)
                                break
                            case 'word_logo':
                                this.conf.active.word_list[index].logo = src
                                break
                            default:
                                this.conf.active[key] = src
                        }

                        this.$forceUpdate()
                    }
                }
            })
        },


        previewImage(urls, current = urls[0]) {
            uni.previewImage({
                urls,
                current
            })
        },


        dataCheck(data) {
            const showToast = title => {
                this.type_id = 1
                this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none')
            }

            if (!data.name) {
                showToast('请输入活动名称')
                return false
            }

            if (!data.active_id) {
                if (!data.mobile) {
                    showToast('请输入手机号')
                    return false
                }
                if (data.mobile.toString().length !== 11) {
                    showToast('手机号长度有误')
                    return false
                }
                if (!data.wechat_num) {
                    showToast('请输入微信号')
                    return false
                }
                const wechat_num = data.wechat_num.toString()
                if (wechat_num.length < 4 || wechat_num.length > 20) {
                    showToast('微信号长度有误')
                    return false
                }
                if (this._utils.isChineseChar(wechat_num)) {
                    showToast('微信号不能输入中文')
                    return false
                }
            }


            return true
        },

        setMustSubmitData() {
            const errModal = content => {
                this.type_id = 3
                uni.showModal({
                    title: '提示',
                    content,
                    showCancel: false
                })
            }


            for (let i = 0; i < this.conf.must_submit.length; i++) {
                const v = this.conf.must_submit[i]
                console.log(v)
                if (!v.title) {
                    errModal('参与活动需要填写的信息选项填写不完整，请检查。')
                    return false
                }
                v.name = pinyin(v.title, {toneType: 'none', type: 'array'}).join('_')
                if (v.types === 2) {
                    if (!v.options || !v.options.length) {
                        errModal(`${v.title} 至少需要添加一个选项。`)
                        return false
                    }
                    console.log(v.options)
                    for (let j = 0; j < v.options.length; j++) {
                        const v1 = v.options[j]
                        if (!v1 || !v1.text) {
                            errModal(`${v.title} 有未填写的选项，请检查。`)
                            return false
                        }
                    }
                }
            }

            return true
        },


        confCheck() {
            const showToast = title => {
                this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none')
            }
            const conf = JSON.parse(JSON.stringify(this.conf))
            if (conf.active.enter_types === 2) {
                if (!this.have_password && !conf.active.password) {
                    showToast('请输入活动密码')
                    this.type_id = 3
                    return false
                }
                if (conf.active.password && conf.active.password.length < 3) {
                    showToast('活动密码不得少于3位')
                    this.type_id = 3
                    return false
                }
                if (conf.active.password && conf.active.password.length > 20) {
                    showToast('活动密码不得大于20位')
                    this.type_id = 3
                    return false
                }
                conf.active.password = conf.active.password || this.old_password
            } else {
                conf.active.password = 1
            }

            let guess_song_success = Number(this.integral.guess_song_success.value)
            let guess_song_fail = Number(this.integral.guess_song_fail.value)
            if (isNaN(guess_song_success) || isNaN(guess_song_fail)) {
                showToast('奖励填写有误，请填写整数')
                return
            }
            if (this.integral.guess_song_success.type === '-') {
                guess_song_success = -guess_song_success
            }
            if (this.integral.guess_song_fail.type === '-') {
                guess_song_fail = -guess_song_fail
            }
            conf.active.integral = {
                unit: this.integral.unit || '金币',
                guess_song_success,
                guess_song_fail
            }

            let conf_str = JSON.stringify(conf)
            conf_str = conf_str.replace(/·/g, '-')
            return base64['encode'](conf_str)
        },

        save() {
            if (this.success) return false

            const data = JSON.parse(JSON.stringify(this.form_data))

            if (!this.dataCheck(data)) return false
            if (!this.setMustSubmitData()) return false

            const conf = this.confCheck()
            if (!conf) return false
            data.conf = conf

            data.pic_list = base64['encode'](JSON.stringify(this.pic_list))

            data.begin_time /= 1000
            data.end_time /= 1000

            this.saveAjax(data)
        },

        async saveAjax(data) {
            data.access_token = app.globalData['access_token']
            data.types = this.types

            this.loading = true
            uni.showLoading({
                title: '保存中...',
                mask: app.globalData['evn_version'] !== 'trial'
            })

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.admin/create_active',
                data
            })

            uni.hideLoading()
            this.loading = false

            if (!res || !res.status) {
                xwy_api.alert(res && res.info || '保存失败')
                return false
            }

            this.updatePageData()

            if (data.active_id) {
                if (this.id) {
                    uni.showToast({
                        title: '保存成功',
                        icon: 'success',
                        mask: true
                    })
                    uni.navigateBack()
                    return false
                }
            }

            this.success = true
            
            if (!data.active_id && res.data?.active_id) await this.addDefaultMusic(res.data.active_id)
        },

        // 活动修改/创建成功，如果页面栈有详情或列表页面，更新页面数据
        updatePageData() {
            const pages = getCurrentPages()

            const update_page_list = [
                {
                    route: 'pages/song/user/detail',
                    _function: page => {
                        page.$vm.getDetail(true)
                    }
                },
                {
                    route: 'pages/song/admin/activity/manage',
                    _function: page => {
                        page.$vm.getDetail()
                    }
                },
                {
                    route: 'pages/activity/user/index',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getActivityList()
                    }
                },
                {
                    route: 'pages/activity/user/activity_list',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getList()
                    }
                }
            ]

            update_page_list.forEach(v => {
                const page = pages.find(n => n.route === v.route)
                if (page) v._function(page)
            })
        },

        async addDefaultMusic(active_id) {
            const system_music_res = await this.xwy_api.request({
                url: 'front.flat.sport_step.guess_song.adminManageSong/song_list',
                data: {
                    page: 1,
                    perpage: 20,
                    public: 1,
                }
            })
            const system_music_list = system_music_res?.data?.list?.data || []
            if (!system_music_list?.length) return

            const ids = system_music_list.map(v => v.song_id)

            await this.xwy_api.request({
                url: 'front.flat.sport_step.guess_song.adminManageSong/selected_music_in_active',
                data: {
                    active_id: active_id,
                    ids
                }
            })
        },

        lookMyActivityList() {
            const list_page_route = 'pages/activity/user/activity_list'
            const pages = getCurrentPages()
            const list_page_index = pages.findIndex(v => {
                return v.route === list_page_route && v.options.type && v.options.type === 'create'
            })
            if (list_page_index === -1) return this.$uni.redirectTo(`/${list_page_route}?type=create`)

            this.$uni.navigateBack(pages.length - list_page_index - 1)
        }
    }
}
</script>

<style>
.page {
    padding-top: 40px;
    padding-bottom: 75px;
}

.type-list {
    position: fixed;
    z-index: 9;
    width: 100vw;
    top: 0;
    left: 0;
}

.type-item {
    height: 40px;
    line-height: 40px;
    text-align: center;
    box-sizing: border-box;
}

.active-type {
    color: #007AFF;
    border-bottom: 2px solid #007AFF;
}

.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}


.form-item .textarea {
    width: 100%;
    line-height: 16px;
    padding: 10px 0;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}

.must-submit-list {
    border-bottom: 1px dashed #eee;
}

.must-rules-picker {
    line-height: 34px;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    margin-left: 5px;
    padding: 0 15px;
}

.delete-rules {
    line-height: 36px;
    padding: 0 10px;
}

.must-options-item {
    padding: 3px 0;
}

.bottom-btn-view {
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.login-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}

.save-success {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 999999;
    box-sizing: border-box;
    padding-top: 10vh;
}

.save-success .icon {
    padding-bottom: 50px;
}

.uni-date-x {
    padding: 0 !important;
}

.uni-date__x-input {
    font-size: 16px !important;
    color: #000;
}

/* #ifdef H5 */

@media screen and (min-width: 500px) {
    .type-list {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }
}

/* #endif */
</style>
