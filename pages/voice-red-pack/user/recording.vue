<template>
    <view class="page">

        <view class="top">
            <view class="tips color-white font14 p10">
                <text>
                    点开始录制后朗读下面文字，正确率达到{{activity_detail.conf.active.voice_redpack.redpack_rules.minPercent}}%即可获得{{activity_detail.conf.active.voice_redpack.redpack_rules.amount.min}}
                </text>
                <text v-if="activity_detail.conf.active.voice_redpack.redpack_rules.amount.max !== activity_detail.conf.active.voice_redpack.redpack_rules.amount.min">
                    -{{activity_detail.conf.active.voice_redpack.redpack_rules.amount.max}}
                </text>
                <text>元红包</text>
            </view>

            <scroll-view v-if="voice_text" scroll-y class="text-view p10" 
                         :class="{'text-center': voice_text.length < 20}">
                <text>{{ voice_text }}</text>
            </scroll-view>
        </view>

        <view class="bottom-bar">

            <view class="flex-all-center" v-if="recorder_status === 'waiting'">
                <view @click="startRecord">
                    <view class="big-btn flex-all-center">
                        <uni-icons type="mic" size="30" color="#000000"/>
                    </view>
                    <view class="font14 color-white text-center pt10">开始录制</view>
                </view>
            </view>


            <view class="btns" v-if="recorder_status !== 'waiting'">

                <view @click="rerecording">
                    <view class="flex-all-center small-btn">
                        <uni-icons type="refreshempty" size="24" color="#1c2438"/>
                    </view>
                    <view class="font14 color-white text-center pt10">重录</view>
                </view>

                <view v-if="recorder_status === 'pause'" @click="resumeRecord">
                    <view class="big-btn flex-all-center">
                        <uni-icons type="mic" size="30" color="#1c2438"/>
                    </view>
                    <view class="font14 color-white text-center pt10">继续录制</view>
                </view>
                <view v-if="recorder_status === 'start'">
                    <view class="big-btn flex-all-center" @click="pauseRecord">
                        <text class="iconfont icon-pause-filled color-title font28"></text>
                    </view>
                    <view class="font14 color-white text-center pt10">{{ recorder_time_text }}</view>
                </view>
                <view @click="endRecord">
                    <view class="flex-all-center small-btn">
                        <uni-icons type="checkmarkempty" size="24" color="#1c2438"/>
                    </view>
                    <view class="font14 color-white text-center pt10">完成</view>
                </view>

            </view>
        </view>

        <uni-popup ref="red_pack_result" type="center" :is-mask-click="false">
            <view class="red-pack-result-popup">
                <view class="red-pack-result-popup-top">
                    <view class="font18">
                        <template v-if="red_pack_result.result === 'success'">恭喜 获得红包</template>
                        <template v-else>很遗憾 与红包擦肩而过</template>
                    </view>
                    <view v-if="red_pack_result.amount !== null" class="font34" style="padding-top: 120rpx;">
                        ￥{{red_pack_result.amount}}
                    </view>
                </view>
                <navigator class="red-pack-result-popup-back" open-type="navigateBack" :delta="2">關</navigator>
                <view v-if="red_pack_result.percent" class="red-pack-result-popup-percent">
                    正确率{{red_pack_result.percent || 0}}%
                </view>
            </view>
        </uni-popup>

        
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'


export default {
    data() {
        return {
            activity_detail: {},
            recorder_status: 'waiting',  // waiting: 等待中   start: 录音中 pause: 暂停
            recorder_time: 0,
            recorder_time_text: '00:00',
            voice_text: '',
            red_pack_result: {
                result: 'success',
                amount: null,
                percent: null
            }
        }
    },
    watch: {
        recorder_time(val) {
            let mm = 0,
                ss = val

            if (val >= 60) {
                mm = Math.ceil(val / 60)
                ss = val % 60
            }

            if (mm < 10) mm = '0' + mm
            if (ss < 10) ss = '0' + ss

            this.recorder_time_text = mm + ':' + ss
        }
    },

    onLoad(e) {
        this.text_index = e.index || 0
        this.active_id = e.id
        uni.showLoading({
            title: '加载中...',
        })


        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }


            this.getDetail()

        })

        this.recorderManagerOn()
    },

    onUnload() {
        if (this.recorder_status === 'waiting') return false
        this.not_submit = true
        this.recorderManager.stop()
    },

    onHide() {
        if (this.recorder_status === 'start') this.pauseRecord()
    },

    methods: {
        async getDetail() {

            let activity_detail = app.globalData['activity_detail']

            if (!activity_detail) {
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        access_token: app.globalData['access_token'],
                        active_id: this.active_id
                    }
                })

                activity_detail = res['data']['active_details']

            }
            uni.hideLoading()

            this.activity_detail = activity_detail

            const index = this.text_index
            const text_list = activity_detail.conf?.active?.voice_redpack?.text_list
            const voice_text = text_list?.[index]?.text
            if (!voice_text) {
                xwy_api.alert('朗读内容获取失败')
                uni.navigateBack()
                return false
            }
            this.voice_text = voice_text
        },


        rerecording() {
            this.pauseRecord()
            uni.showModal({
                title: '提示',
                content: '确定重新录制？',
                success: res => {
                    if (res.confirm) {
                        this.not_submit = true
                        this.recorderManager.stop()
                    }
                }
            })
        },

        recorderTimeIntervalStart() {
            this.recorder_time_interval = null
            this.recorder_time_interval = setInterval(() => {
                this.recorder_time++
            }, 1000)
        },

        recorderManagerOn() {
            this.recorderManager = uni.getRecorderManager()

            this.recorderManager.onStart(res => {
                uni.hideLoading()
                console.log('recorder Start', res)
                this.recorder_status = 'start'
                this.recorderTimeIntervalStart()
                // #ifdef MP-WEIXIN
                wx.enableAlertBeforeUnload({
                    message: '退出后已录制的语音不会保存哦，是否确定退出？'
                })
                // #endif
            })
            this.recorderManager.onPause(res => {
                console.log('recorder Pause', res)
                clearInterval(this.recorder_time_interval)
                this.recorder_status = 'pause'
            })
            this.recorderManager.onStop(res => {
                // #ifdef MP-WEIXIN
                wx.disableAlertBeforeUnload()
                // #endif
                console.log('recorder Stop', res)
                clearInterval(this.recorder_time_interval)
                this.recorder_time = 0
                this.recorder_status = 'waiting'

                if (this.not_submit) {
                    this.not_submit = false
                    return false
                }

                this.uploadVoice(res)
            })
            this.recorderManager.onError(err => {
                uni.hideLoading()
                console.log('recorder Error', err)
                if (err && err.errMsg && err.errMsg === "operateRecorder:fail auth deny") {
                    this.getRecorderAuth()
                }
            })
        },

        getRecorderAuth() {
            uni.authorize({
                scope: 'scope.record',
                success: () => {
                    this.startRecord()
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '需要授权录音功能才能录制哦',
                        confirmText: '去授权',
                        success: res => {
                            res.confirm && uni.openSetting()
                        }
                    })
                }
            })
        },

        startRecord() {
            console.log('开始录音')
            uni.showLoading({
                title: '准备录制...',
                mask: true
            })
            this.recorderManager.start({
                duration: 600000,
                format: 'mp3' // 音频格式，有效值 aac/mp3/wav/PCM。App默认值为mp3，小程序默认值aac
            })
        },
        resumeRecord() {
            console.log('继续录音')
            this.recorderManager.resume()
            this.recorder_status = 'start'
            this.recorderTimeIntervalStart()
        },
        pauseRecord() {
            console.log('暂停录音')
            this.recorderManager.pause()
        },
        endRecord() {
            if (this.recorder_status === 'start') this.pauseRecord()
            console.log('录音结束')
            this.recorderManager.stop()
        },

        async uploadVoice(voice) {
            if (this.in_submit) return false
            this.in_submit = true
            uni.showLoading({
                title: '识别中...',
                mask: true
            })

            const audio_src = await xwy_api.uploadAudio(voice, this.activity_detail.active_id)
            this.voice_src = audio_src


            const res = await xwy_api.request({
                url: 'front.flat.active.red_pack.voice.userSubmit/submit_voice_url',
                data: {
                    access_token: app.globalData['access_token'],
                    url: audio_src
                }
            })

            if (!res?.['data']?.['task_id']) {
                uni.hideLoading()
                this.in_submit = false
                xwy_api.alert(res?.['info'] || '提交失败')
                return false
            }

            this.task_id = res['data'].task_id

            await this.getVoiceResult()
        },

        async getVoiceResult() {
            this.get_voice_result_interval = null
            this.get_voice_result_interval = setInterval(async () => {
                const res = await xwy_api.request({
                    url: 'front.flat.active.red_pack.voice.userSubmit/voice_task_result',
                    data: {
                        access_token: app.globalData['access_token'],
                        task_id: this.task_id
                    }
                })

                if (res?.['data']?.result?.text) {
                    await this.submit()
                    clearInterval(this.get_voice_result_interval)
                }
            }, 5000)
        },

        async submit() {
            const res = await xwy_api.request({
                url: 'front.flat.active.red_pack.voice.userSubmit/submit_user_voce_answer',
                data: {
                    access_token: app.globalData['access_token'],
                    url: this.voice_src,
                    key: this.text_index,
                    task_id: this.task_id,
                    active_id: this.activity_detail.active_id
                }
            })

            uni.hideLoading()
            this.in_submit = false

            this.red_pack_result = {
                result: res?.['status'] && res['status'] === 1 && res['data'] && res['data'].amount ? 'success' : 'fail',
                amount: res['data'].amount || null,
                percent: res['data'].percent || null
            }

            this.$refs.red_pack_result.open()
        }
    }
}
</script>

<style>
.page {
    width: 100vw;
    height: 100vh;
    background-color: #E1614D;
    box-sizing: border-box;
}


.top {
    background-color: #E56755;
    height: calc(100vh - 150px);
    color: #FCF7B1;
    border-radius: 0 0 750rpx 750rpx/0 0 200rpx 200rpx;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.2);
}
.tips {
    height: 80px;
    box-sizing: border-box;
}
.text-view {
    height: calc(100vh - 150px - 100rpx - 80px);
    box-sizing: border-box;
}

.bottom-bar {
    width: 100vw;
    padding: 0 20px 20px;
    /* box-shadow: 0 0 10px 3px #eee; */
    box-sizing: border-box;
    margin-top: -30px;
}

.btns {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.big-btn, .small-btn {
    border-radius: 50%;
    background-color: #E6CE9F;
}

.big-btn {
    width: 60px;
    height: 60px;
}

.small-btn {
    margin-top: 5px;
    width: 50px;
    height: 50px;
}


.red-pack-result-popup {
    background-color: #E56755;
    width: 650rpx;
    height: 1000rpx;
    box-sizing: border-box;
    border-radius: 10px;
    overflow: hidden;
    text-align: center;
}
.red-pack-result-popup-top {
    box-sizing: border-box;
    padding: 200rpx 0;
    background-color: #E56755;
    height: 800rpx;
    color: #FCF7B1;
    border-radius: 0 0 650rpx 650rpx/0 0 150rpx 150rpx;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.2);
}
.red-pack-result-popup-back {
    width: 150rpx;
    height: 150rpx;
    line-height: 150rpx;
    font-size: 34px;
    border-radius: 50%;
    background-color: #E6CE9F;
    margin: -75rpx auto 0;
}
.red-pack-result-popup-percent {
    color: #E6CE9F;
    font-size: 14px;
    padding-top: 30rpx;
}
</style>
