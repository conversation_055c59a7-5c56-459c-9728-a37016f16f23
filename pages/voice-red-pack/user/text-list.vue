<template>
    <view class="page">
        <view class="text-list">
            <view
                class="text-item bg-white"
                v-for="(item, index) in text_list"
                :key="index"
            >
                <view class="ellipsis--l3 color-content">
                    <text space="nbsp">{{ item.text }}</text>
                </view>
                <view class="opt-bar flex-kai">
                    <view></view>
                    <view class="flex-row color-sub">
                        <view
                            class="opt-item"
                            hover-class="navigator-hover"
                            @click="previewText(item.text)"
                        >预览</view>
                        <view
                            class="opt-item"
                            hover-class="navigator-hover"
                            @click="receiveRedPacket(index)"
                        >领红包</view>
                    </view>
                </view>
            </view>
        </view>

        <uni-popup ref="preview_text" type="center">
            <view class="preview-text-popup bg-white">
                <view class="p10">
                    <text class="color-content">{{ preview_text }}</text>
                </view>
                <view class="flex-all-center">
                    <view class="p10 font14 color-sub" @click="$refs.preview_text.close()">关闭</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'

export default {
    data() {
        return {
            text_list: [],
            preview_text: '',
        }
    },
    onLoad(e) {
        this.id = e.id
        uni.showLoading({title: ''})
        login.uniLogin(() => {
            this.getActivityDetail()
        })
    },

    methods: {
        async getActivityDetail() {
            let activity_detail = app.globalData['activity_detail']

            if (!activity_detail) {
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        access_token: app.globalData['access_token'],
                        active_id: this.id
                    }
                })
                activity_detail = res['data']['active_details']
            }

            this.text_list = activity_detail.conf.active.voice_redpack.text_list || []
            uni.hideLoading()
        },

        previewText(text) {
            this.preview_text = text
            this.$refs.preview_text.open()
        },

        receiveRedPacket(index) {
            uni.navigateTo({
                url: '/pages/voice-red-pack/user/recording?id=' + this.id + '&index=' + index
            })
        }
    }
}
</script>

<style scoped>
.text-list {
    padding-top: 1px;
}
.text-item {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
    box-shadow: 0 0 8px #ccc;
}
.opt-bar {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #eee;
}
.opt-item {
    line-height: 34px;
    border: 1px solid #eee;
    border-radius: 18px;
    width: 80px;
    text-align: center;
    font-size: 14px;
    margin-left: 10px;
}
.preview-text-popup {
    width: 90vw;
    padding: 10px;
    box-sizing: border-box;
    border-radius: 10px;
}
</style>
