<template>
    <view class="page bg-background">
        <view v-if="total_count" class="color-sub p10 font14 text-center">共{{ total_count }}条记录</view>

        <view class="list">
            <view
                class="item bg-white flex-row"
                v-for="item in list" :key="item.id"
            >
                <view class="headimg-view">
                    <image
                        class="headimg"
                        :class="{'player-rotate': player_id === item.id}"
                        :src="item.headimg"
                        mode="aspectFill"
                    />
                    <view
                        v-if="show_voice"
                        class="player-btn flex-all-center"
                        @click="audioChange(item.mp3, item.id)"
                    >
                        <view class="player-icon">
                            <text
                                v-if="player_id === item.id"
                                class="iconfont icon-pause color-white font24"
                            ></text>
                            <text v-else class="iconfont icon-play color-white font24"></text>
                        </view>
                    </view>
                    <view
                        v-if="player_id === item.id"
                        class="text-center word-last-loading font12 color-primary pl10"
                        style="display: inline-block;"
                    >播放中</view>
                </view>
                <view class="middle pl10">
                    <view class="color-title">{{item.name}}</view>
                    <view class="color-content font14" style="padding: 3px 0;">正确率 {{item.percent}}%</view>
                    <view class="color-sub font12">{{item.create_time}}</view>
                </view>
                <view class="amount color-warning text-right">
                    <text class="font18">{{item.amount}}</text>
                    <text class="font12">元</text>
                </view>
            </view>
        </view>

        <view v-if="!list.length && !loading" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无红包记录</view>
        </view>

        <uni-load-more v-if="loading" status="loading"></uni-load-more>
        <uni-load-more v-if="is_last_page && list.length > 8" status="noMore"
                       :contentText="{contentnomore: '我是有底线的'}"></uni-load-more>
        <uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>

    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'

export default {
    data() {
        return {
            id: '',
            list: [],
            load_page: 1,
            is_last_page: false,
            loading: true,
            total_count: 0,
            is_admin: 0,
            player_id: null,
            show_voice: false
        }
    },
    onLoad(e) {
        uni.showLoading({ mask: true })
        if (e.is_admin && e.is_admin !== '0') this.is_admin = true
        if (e.id) this.id = e.id
        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getActivityDetails()
        })
    },

    beforeDestroy() {
        this.innerAudioContext && this.innerAudioContext.destroy()
    },

    onUnload() {
        this.innerAudioContext && this.innerAudioContext.destroy()
    },

    onReachBottom() {
        !this.is_last_page && !this.loading && this.getList()
    },

    methods: {
        async getActivityDetails() {
            let activity_detail = app.globalData['activity_detail']

            if (!activity_detail) {
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        access_token: app.globalData['access_token'],
                        active_id: this.id
                    }
                })

                activity_detail = res['data']['active_details']
            }
            if (activity_detail.userid && activity_detail.userid === app.globalData['userid']) {
                this.is_admin = true
            }

            if (this.is_admin || activity_detail.conf.active.record_show_voice) {
                this.createInnerAudioContext()
                this.show_voice = true
            }

            await this.getList()
        },

        async getList() {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
            }


            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id,
                page: this.load_page,
                perpage: 20
            }

            this.loading = true

            const res = await xwy_api.request({
                url: 'front.flat.active.red_pack.voice.userSubmit/submit_voice_user_records_list',
                data
            })

            this.loading = false
            uni.hideLoading()

            this.load_page++

            if (res?.['data']?.list) {
                const res_data = res.data.list
                this.total_count = res_data.total
                this.is_last_page = res_data.is_lastpage
                const list = res_data.data || []
                const new_list = []
                list.forEach(v => {
                    new_list.push({
                        id: v.id,
                        amount: v.amount,
                        mp3: v.conf_json.mp3,
                        create_time: v.create_time,
                        name: v['user_attend_details']?.must_submit?.[0]?.value || '',
                        headimg: v['user_attend_details']?.headimg || '',
                        percent: v.percent / 100
                    })
                })
                this.list = [...this.list, ...new_list]
            } else {
                this.is_last_page = true
            }
        },

        createInnerAudioContext() {
            let innerAudioContext = uni.createInnerAudioContext()
            innerAudioContext.autoplay = false

            innerAudioContext.onError(err => {
                // console.log('播放错误', err)
                this.audioStop()
                xwy_api.alert('播放错误' + JSON.stringify(err))
            })

            // innerAudioContext.onPlay(() => {
            //     console.log('onPlay')
            // })
            // innerAudioContext.onCanplay(() => {
            //     console.log('onCanplay')
            // })
            innerAudioContext.onEnded(() => {
                // console.log('onEnded')
                this.player_id = null
            })
            this.innerAudioContext = innerAudioContext
        },

        audioChange(src, id) {
            if (id === this.player_id) {
                this.audioPause()
            } else {
                this.audioPlay(src, id)
            }
        },

        audioPlay(src, id) {
            if (this.player_src) this.innerAudioContext.stop()
            this.player_id = id
            this.innerAudioContext.src = src
            this.innerAudioContext.play()
        },

        audioPause() {
            this.innerAudioContext.pause()
            this.player_id = null
            this.player_src = ''
        },
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
}

.item {
    margin: 0 10px 10px;
    padding: 10px;
    box-shadow: 0 0 10px #eee;
    border-radius: 10px;
}

.headimg-view {
    position: relative;
}
$headimg-size: 60px;
.headimg, .player-btn {
    width: $headimg-size;
    min-width: $headimg-size;
    height: $headimg-size;
    border-radius: 50%;
    display: block;
}
.player-rotate {
    animation: rotate 3s linear infinite;
}
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
.middle {
    width: calc(100% - 170px);
}
.player-btn {
    position: absolute;
    top: 0;
    left: 0;
    line-height: $headimg-size;
    text-align: center;
    background-color: rgba(0, 0, 0, .3);
}
.amount {
    width: 100px;
    min-width: 100px;
    line-height: 50px;
}
</style>
