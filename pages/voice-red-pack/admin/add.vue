<template>
    <view class="page">
        <view v-if="success" class="save-success bg-white">
            <view class="icon text-center">
                <uni-icons type="checkbox-filled" size="100" color="#19be6b"/>
                <view class="color-content font16">
                    活动{{ form_data.active_id ? '修改' : '创建' }}成功！
                </view>
            </view>
            <view class="flex-all-center">
                <button
                    class="bg-success color-white"
                    style="width: 200px;"
                    @click="lookMyActivityList"
                >查看我的活动
                </button>
            </view>
        </view>

        <view class="type-list flex-row bg-white">
            <view
                class="type-item color-content"
                v-for="(item, index) in type_list"
                :key="index"
                :class="{'active-type': item.id === type_id}"
                :style="{width: 'calc(100% / ' + type_list.length + ')'}"
                @click="type_id = item.id"
            >
                {{ item.title }}
            </view>
        </view>

        <add-activity-tips ref="add_activity_tips"/>

        <view class="form">
            <template v-if="type_id === 1">
                <view class="form-item">
                    <view class="top color-content">
                        <text>活动名称</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view class="bottom font16">
                        <input
                            class="input"
                            v-model="form_data.name"
                            placeholder="请输入活动名称"
                        />
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">主办方</view>
                    <view class="bottom font16">
                        <input
                            class="input"
                            v-model="form_data.organizer"
                            placeholder="请输入主办方单位名称"
                        />
                    </view>
                </view>

                <template v-if="!form_data.active_id">
                    <view class="form-item">
                        <view class="top color-content">
                            <text>手机号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input
                                class="input"
                                type="number"
                                v-model="form_data.mobile"
                                placeholder="请输入真实手机号"
                            />
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <text>微信号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input
                                class="input"
                                v-model="form_data.wechat_num"
                                placeholder="请输入真实微信号"
                            />
                        </view>
                    </view>
                </template>

                <view class="form-item">
                    <view class="top color-content">活动开始时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.begin_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">活动结束时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.end_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view v-if="!conf.active.news.news_id" class="form-item">
                    <view class="top color-content">活动说明</view>
                    <view class="bottom font16">
                        <textarea
                            class="textarea"
                            maxlength="-1"
                            auto-height
                            v-model="form_data.content"
                            placeholder="请输入活动说明"
                        />
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>活动说明文章</view>
                        <view class="font12 color-sub">
                            绑定文章后，活动说明将会显示文章内容，不会显示上面填写的活动说明
                        </view>
                    </view>
                    <view class="bottom font16">
                        <view class="flex-kai" @click="toSelNews">
                            <view class="view">
                                <view v-if="conf.active.news.news_id">
                                    {{ conf.active.news.news_title || conf.active.news.news_id }}
                                </view>
                                <view v-else class="color-sub">选择文章</view>
                            </view>
                            <view class="flex-all-center">
                                <view
                                    v-if="conf.active.news.news_id"
                                    class="color-sub font12"
                                    style="width: 30px;"
                                    @click.stop="deleteNews"
                                >解绑</view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>
            </template>


            <template v-if="type_id === 3">
                <view class="form-item">
                    <view class="top color-content">活动参与方式</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.enter_types_list"
                                :value="form_options.enter_types_list.findIndex(v => v.value === conf.active.enter_types)"
                                range-key="title"
                                @change="conf.active.enter_types = form_options.enter_types_list[$event.detail.value].value"
                            >
                                {{ form_options.enter_types_list.find(v => v.value === conf.active.enter_types).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="conf.active.enter_types === 2" class="form-item">
                    <view class="top color-content">
                        <text>活动密码</text>
                        <text v-if="!have_password" class="color-error font16"> *</text>
                        <text class="color-sub font12 pl5">(3-20位)</text>
                    </view>
                    <view class="bottom font16">
                        <input
                            class="input"
                            v-model="conf.active.password"
                            :maxlength="20"
                            :placeholder="have_password ? '已设置密码，无需修改密码不用填写' : '请输入活动密码'"
                        />
                    </view>
                </view>


                <view class="form-item">
                    <view class="top color-content">
                        <view>参与活动需要填写的信息</view>
                        <view class="font12 color-error">
                            注意: 第一项为排行榜显示的姓名, 无法删除
                        </view>
                    </view>
                    <view class="bottom font16">
                        <view class="ptm5">
                            <view
                                class="ptm5 must-submit-list"
                                v-for="(item, index) in conf.must_submit"
                                :key="index"
                            >
                                <view class="flex-row">
                                    <view class="flex-row">
                                        <view style="width: 120px;">
                                            <uni-easyinput
                                                v-model="item.title"
                                                placeholder="请输入内容"
                                            />
                                        </view>
                                        <template>
                                            <template v-if="index === 0">
                                                <picker
                                                    :range="['选填', '必填']"
                                                    :value="item.rules"
                                                    @change="mustItemRulesChange($event, index)"
                                                >
                                                    <view class="must-rules-picker">
                                                        <text class="color-content font14">
                                                            {{ item.rules === 0 ? '选填' : '必填' }}
                                                        </text>
                                                        <text class="iconfont icon-more color-sub font14"></text>
                                                    </view>
                                                </picker>
                                                <view class="must-rules-picker">
                                                    <text class="color-content font14">
                                                        {{ item.types === 1 ? '文本' : '单选' }}
                                                    </text>
                                                    <text class="iconfont icon-more color-white font14"></text>
                                                </view>
                                            </template>
                                            <template v-else>
                                                <picker
                                                    :range="['选填', '必填']"
                                                    :value="item.rules"
                                                    @change="mustItemRulesChange($event, index)"
                                                >
                                                    <view class="must-rules-picker">
                                                        <text class="color-content font14">
                                                            {{ item.rules === 0 ? '选填' : '必填' }}
                                                        </text>
                                                        <text class="iconfont icon-more color-sub font14"></text>
                                                    </view>
                                                </picker>
                                                <picker
                                                    :range="['文本', '单选']"
                                                    :value="item.types - 1"
                                                    @change="mustItemTypesChange($event, index)"
                                                >
                                                    <view class="must-rules-picker">
                                                        <text class="color-content font14">
                                                            {{ item.types === 1 ? '文本' : '单选' }}
                                                        </text>
                                                        <text class="iconfont icon-more color-sub font14"></text>
                                                    </view>
                                                </picker>
                                            </template>
                                        </template>
                                    </view>
                                    <view
                                        v-if="index !== 0"
                                        class="delete-rules font14 color-error"
                                        @click="conf.must_submit.splice(index, 1)"
                                    >删除</view>
                                </view>

                                <view v-if="item.types === 2" class="pl10">
                                    <view
                                        class="must-options-item flex-row"
                                        v-for="(item_, index_) in item.options"
                                        :key="index_"
                                    >
                                        <view
                                            class="color-sub delete-rules text-right"
                                            style="width: 20px; padding: 0 5px 0 0;"
                                        >{{ index_ + 1 }}:</view>
                                        <view style="width: 200px;">
                                            <uni-easyinput
                                                v-model="item_.text"
                                                placeholder="请输入内容"
                                            />
                                        </view>
                                        <view class="delete-rules">
                                            <text
                                                class="color-error font14"
                                                @click="deleteOptionsItem(index, index_)"
                                            >删除</text>
                                        </view>
                                    </view>
                                    <view class="flex-row">
                                        <view
                                            class="color-sub font14 ptm5"
                                            @click="addOption(index)"
                                        >+ 添加新选项</view>
                                    </view>
                                </view>
                            </view>
                            <view class="flex-row">
                                <view class="color-primary font14 ptm5" @click="addMust">
                                    + 添加新项
                                </view>
                            </view>
                        </view>
                    </view>
                </view>

            </template>

            <template v-if="type_id === 2">
                <view class="form-item">
                    <view class="top color-title">
                        <text>活动主图</text>
                        <text class="font12 color-sub pl5">
                            (设置活动缩略图及活动详情顶部图片)
                        </text>
                        <view class="font12 color-sub">图片尺寸建议: 750*430</view>
                    </view>
                    <view style="padding-top: 5px;">
                        <view class="image-view" v-if="form_data.logo">
                            <image
                                class="image-item"
                                :src="form_data.logo"
                                mode="aspectFill"
                                @click="previewImage([form_data.logo])"
                            />
                            <view class="del-image-item" @click.stop="form_data.logo = ''">
                                <uni-icons type="closeempty" color="#e20f04"/>
                            </view>
                        </view>

                        <view
                            v-else
                            class="add-image text-center"
                            @click="changeImage('logo')"
                        >
                            <uni-icons
                                type="plusempty"
                                size="48"
                                color="#eeeeee"
                            />
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-title">
                        <text>活动页面开屏大图</text>
                        <view class="font12 color-sub">图片尺寸建议: 780*1387</view>
                    </view>
                    <view style="padding-top: 5px;">
                        <view class="image-view" v-if="conf.active.screen_pic">
                            <image
                                class="image-item"
                                :src="conf.active.screen_pic"
                                mode="aspectFill"
                                @click="previewImage([conf.active.screen_pic])"
                            />
                            <view
                                class="del-image-item"
                                @click.stop="conf.active.screen_pic = ''"
                            >
                                <uni-icons type="closeempty" color="#e20f04"/>
                            </view>
                        </view>
                        <view
                            v-else
                            class="add-image text-center"
                            @click="changeImage('screen_pic')"
                        >
                            <uni-icons
                                type="plusempty"
                                size="48"
                                color="#eeeeee"
                            />
                        </view>
                    </view>
                </view>

            </template>

            <template v-if="type_id === 6">
                <view class="form-item">
                    <view class="top color-content">朗读内容配置</view>
                    <view class="bottom font16">
                        <view
                            class="flex-kai voice-text-item"
                            v-for="(item, index) in conf.active.voice_redpack.text_list"
                            :key="index"
                        >
                            <view
                                class="voice-text-item-content ellipsis"
                                :class="item.text ? 'color-content' : 'color-sub'"
                                @click="editVoiceTextContent(item.text, index)"
                            >
                                {{item.text || '请输入朗读内容'}}
                            </view>
                            <view
                                class="voice-text-item-delete flex-all-center"
                                @click="deleteVoiceText(index)"
                            >
                                <uni-icons type="trash" size="20" color="#ed3f14"/>
                            </view>
                        </view>

                        <view class="flex-all-center p10">
                            <view class="color-primary font14" @click="pushVoiceText">
                                添加朗读内容
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">红包发放设置</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.redpack_open"
                                :value="form_options.redpack_open.findIndex(v => v.value === conf.active.voice_redpack.redpack_rules.open)"
                                range-key="title"
                                @change="conf.active.voice_redpack.redpack_rules.open = form_options.redpack_open[$event.detail.value].value"
                            >
                                {{ form_options.redpack_open.find(v => v.value === conf.active.voice_redpack.redpack_rules.open).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>红包金额设置</view>
                        <view class="color-sub font12">如需固定红包金额，两个值一样即可</view>
                    </view>
                    <view class="bottom ptm5">
                        <view class="flex-row" style="line-height: 36px;">
                            <view style="width: 120px;">
                                <uni-easyinput
                                    v-model="conf.active.voice_redpack.redpack_rules.amount.min"
                                    type="digit"
                                    :maxlength="8"
                                    placeholder="最大小"
                                />
                            </view>
                            <view class="font14 color-sub pl5">元</view>
                            <view class="pl10 pr10 color-sub">-</view>
                            <view style="width: 120px;">
                                <uni-easyinput
                                    v-model="conf.active.voice_redpack.redpack_rules.amount.max"
                                    type="digit"
                                    :maxlength="8"
                                    placeholder="最大金额"
                                />
                            </view>
                            <view class="font14 color-sub pl5">元</view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>最低正确率 (%)</view>
                        <view class="color-sub font12">正确率必需要大于此分数才能获得红包奖励 (0% - 100%)</view>
                    </view>
                    <view class="bottom font16">
                        <input
                            class="input"
                            type="number"
                            v-model="conf.active.voice_redpack.redpack_rules.minPercent"
                            placeholder="请输入最低正确率"
                        />
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>红包领取次数限制</view>
                        <view class="color-sub font12">达到设置次数后无法继续领取红包</view>
                    </view>
                    <view class="bottom font16">
                        <input
                            class="input"
                            type="number"
                            :maxlength="8"
                            v-model="conf.active.voice_redpack.submit_count"
                            placeholder="请输入红包领取次数"
                        />
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">红包记录是否播放用户录音</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.record_show_voice"
                                :value="form_options.record_show_voice.findIndex(v => v.value === conf.active.record_show_voice)"
                                range-key="title"
                                @change="conf.active.record_show_voice = form_options.redpack_open[$event.detail.value].value"
                            >
                                {{ form_options.record_show_voice.find(v => v.value === conf.active.record_show_voice).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>
            </template>
        </view>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view
                class="login-btn color-white text-center font18 bg-primary"
                :disabled="loading"
                @click="save"
            >{{ form_data.active_id ? '保存' : '创建活动' }}
            </view>
        </view>

        <uni-popup type="center" ref="voiceTextContent" @touchmove.stop.prevent="">
            <view class="voice-text-content-popup bg-white">
                <view class="text-center p10 color-title">请输入朗读内容</view>
                <view class="voice-text-content-popup-textarea-view">
                    <textarea
                        class="voice-text-content-popup-textarea"
                        v-model="text_content"
                        :maxlength="500"
                        :fixed="true"
                        @confirm="textContentConfirm()"
                    />
                </view>
                <view class="voice-text-content-popup-btn flex-row">
                    <view
                        class="voice-text-content-popup-btn-cancel color-sub"
                        @click="$refs.voiceTextContent.close()"
                    >取消</view>
                    <view
                        class="voice-text-content-popup-btn-confirm color-success"
                        @click="textContentConfirm"
                    >确定</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import base64 from '@/utils/base64.js'
import {pinyin} from 'pinyin-pro'


export default {
    data() {
        return {
            loading: true,
            success: false,
            type_list: [
                {title: '基本信息', id: 1},
                {title: '活动设置', id: 3},
                {title: '红包设置', id: 6},
                {title: '活动图片', id: 2}
            ],
            type_id: 1,
            form_data: {
                name: '',
                wechat_num: '',
                mobile: '',
                organizer: '',
                content: '',
                begin_time: new Date(utils.getDay(0, true, '/') + ' 00:00:00').getTime(),
                end_time: new Date(utils.getDay(30, true, '/') + ' 23:59:59').getTime(),
                logo: ''
            },
            conf: {
                active: {
                    news: {
                        news_id: '',
                        news_title: ''
                    },
                    enter_types: 1,
                    password: '',
                    screen_pic: '',
                    top_rank_banner: [],
                    voice_redpack: {
                        submit_count: 1,
                        text_list: [
                            { text: '' }
                        ],
                        redpack_rules: {
                            open: 1,
                            amount: {
                                min: '',
                                max: ''
                            },
                            minPercent: ''
                        }
                    },
                    record_show_voice: 0
                },
                must_submit: JSON.parse(JSON.stringify(this.xwy_config.defaultMustSubmit))
            },
            top_rank_banner_max_count: 6,
            rank_set: {},
            have_password: false,
            form_options: {
                enter_types_list: [
                    {title: '自由报名参与活动', value: 1},
                    {title: '需要输入密码才能进入活动', value: 2},
                    {title: '报名需要审核通过才能参与活动', value: 3}
                ],
                redpack_open: [
                    { value: 1, title: '开启红包发放' },
                    { value: 0, title: '暂停发放红包' }
                ],
                record_show_voice: [
                    { value: 0, title: '不播放' },
                    { value: 1, title: '播放' }
                ]
            },
            detail_icon_conf: [
                {
                    type: 0,
                    title: '文章详情'
                }
            ],
            text_content: '',
            voice_text_item_index: null
        }
    },
    onLoad(e) {
        if (e.id) {
            this.form_data.active_id = e.id
        } else {
            this.types = Number(e.types)
            this.$nextTick(() => this.$refs.add_activity_tips.open(this.types))
            this.form_data.logo = this.xwy_config.create_active_default_logo
        }
        uni.setNavigationBarTitle({
            title: e.id ? '修改活动' : `创建${e.name || '活动'}`
        })
        uni.showLoading({
            mask: true
        })


        login.uniLogin(err => {

            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }


            if (e.id) {
                this.getDetail()
                return false
            }

            const evn_version = app.globalData['evn_version']

            if (evn_version === 'develop') {
                this.form_data.mobile = 12345678910
                this.form_data.wechat_num = 12345678910
                this.form_data.organizer = '伟杰测试'
            }

            // 开发版和体验版不检查重复创建活动
            if (evn_version === 'develop' || evn_version === 'trial') {
                uni.hideLoading()
                return false
            }

            this.checkUserCanCreateActive()
        })
    },
    methods: {
        editVoiceTextContent(text, index) {
            this.text_content = text
            this.voice_text_item_index = index
            this.$refs.voiceTextContent.open()
        },

        pushVoiceText() {
            this.conf.active.voice_redpack.text_list.push({ text: '' })
        },

        deleteVoiceText(index) {
            this.conf.active.voice_redpack.text_list.splice(index, 1)
        },

        textContentConfirm() {
            this.conf.active.voice_redpack.text_list[this.voice_text_item_index].text = this.text_content
            this.$refs.voiceTextContent.close()
        },

        checkUserCanCreateActive() {

            xwy_api.ajax({
                url: 'front.flat.sport_step.admin/check_user_can_create_active',
                data: {
                    access_token: app.globalData['access_token']
                },
                success: res => {
                    console.log('检查是否能创建活动', res)
                    uni.hideLoading()
                    this.loading = false
                    if (!res.status) {
                        uni.showModal({
                            title: '提示',
                            content: res.info || '暂时不能创建活动',
                            showCancel: false,
                            success: () => {
                                uni.navigateBack()
                            }
                        })
                    }
                }
            })

        },

        getDetail() {
            const errModal = content => {
                uni.hideLoading()
                uni.showModal({
                    title: '提示',
                    content: content || '活动获取失败',
                    showCancel: false,
                    success: () => {
                        uni.navigateBack()
                    }
                })
            }

            xwy_api.getActivityDetail(this.form_data.active_id, res => {
                if (res.data?.['active_details']) {
                    const detail = res.data['active_details']

                    this.detailInit(detail)
                } else {
                    errModal(res.info)
                }
            })
        },


        detailInit(data) {
            this.types = data.types
            this.form_data.name = data.name
            this.form_data.begin_time = data.begin_time * 1000
            this.form_data.end_time = data.end_time * 1000
            if (data.organizer) this.form_data.organizer = data.organizer
            if (data.logo) this.form_data.logo = data.logo
            if (data.content) this.form_data.content = data.content

            const conf = data.conf

            if (conf.active.password) {
                this.old_password = conf.active.password
                delete conf.active.password
                this.have_password = true
            }

            conf.active.voice_redpack.submit_count = conf.active.voice_redpack.submit_count || 1
            conf.active.record_show_voice = conf.active.record_show_voice || 0

            this.conf = conf

            if (data.rank_set) {
                const rank_set = data.rank_set

                this.rank_set = rank_set


                if (rank_set.closed_AD && rank_set.closed_AD === 1) {
                    this.detail_icon_conf.push({
                        type: 1,
                        title: '文章列表'
                    })
                }
            }

            uni.hideLoading()
        },


        toSelNews() {
            uni.navigateTo({
                url: '/pages/news/list?type=user&is_sel=true',
                events: {
                    selNews: data => {
                        this.conf.active.news.news_id = data.id
                        this.conf.active.news.news_title = data.title
                    }
                }
            })
        },


        deleteNews() {
            this.conf.active.news.news_id = ''
            this.conf.active.news.news_title = ''
        },


        mustItemRulesChange(e, index) {
            this.conf.must_submit[index].rules = Number(e.detail.value)
        },
        mustItemTypesChange(e, index) {
            const value = Number(e.detail.value) + 1
            this.conf.must_submit[index].types = value
            if (value === 2) {
                this.conf.must_submit[index].options = this.conf.must_submit[index].options || [{text: ''}]
            }
        },
        addMust() {
            this.conf.must_submit.push({
                name: '',
                rules: 0,
                title: '',
                types: 1
            })
        },

        addOption(index) {
            this.conf.must_submit[index].options.push({text: ''})
            this.$forceUpdate()
        },
        deleteOptionsItem(index, index_) {
            if (this.conf.must_submit[index].options.length === 1) {
                uni.showToast({
                    title: '请至少保留一个选项',
                    icon: 'none',
                    mask: true
                })
                return false
            }
            this.conf.must_submit[index].options.splice(index_, 1)
            this.$forceUpdate()
        },


        changeImage(key, index) {
            if (key === 'screen_pic' && !this.rank_set.closed_AD)
                return this.$uni.showModal('无法设置开屏大图，请联系客服设置')

            let url = '/pages/other/image_upload_or_select'
            if (this.form_data.active_id) url += `?active_id=${this.form_data.active_id}`
            uni.navigateTo({
                url,
                events: {
                    newImg: src => {
                        switch (key) {
                            case 'logo':
                                this.form_data.logo = src
                                break
                            case 'top_rank_banner':
                                this.conf.active.top_rank_banner.push(src)
                                break
                            case 'word_logo':
                                this.conf.active.word_list[index].logo = src
                                break
                            default:
                                this.conf.active[key] = src
                        }

                        this.$forceUpdate()
                    }
                }
            })
        },


        previewImage(urls, current = urls[0]) {
            uni.previewImage({
                urls,
                current
            })
        },


        dataCheck(data) {
            const showToast = title => {
                this.type_id = 1
                this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none')
            }

            if (!data.name) {
                showToast('请输入活动名称')
                return false
            }

            if (!data.active_id) {
                if (!data.mobile) {
                    showToast('请输入手机号')
                    return false
                }
                if (data.mobile.toString().length !== 11) {
                    showToast('手机号长度有误')
                    return false
                }
                if (!data.wechat_num) {
                    showToast('请输入微信号')
                    return false
                }
                const wechat_num = data.wechat_num.toString()
                if (wechat_num.length < 4 || wechat_num.length > 20) {
                    showToast('微信号长度有误')
                    return false
                }
                if (this._utils.isChineseChar(wechat_num)) {
                    showToast('微信号不能输入中文')
                    return false
                }
            }


            return true
        },

        setMustSubmitData() {
            const errModal = content => {
                this.type_id = 3
                uni.showModal({
                    title: '提示',
                    content,
                    showCancel: false
                })
            }


            for (let i = 0; i < this.conf.must_submit.length; i++) {
                const v = this.conf.must_submit[i]
                console.log(v)
                if (!v.title) {
                    errModal('参与活动需要填写的信息选项填写不完整，请检查。')
                    return false
                }
                v.name = pinyin(v.title, {toneType: 'none', type: 'array'}).join('_')
                if (v.types === 2) {
                    if (!v.options || !v.options.length) {
                        errModal(`${v.title} 至少需要添加一个选项。`)
                        return false
                    }
                    console.log(v.options)
                    for (let j = 0; j < v.options.length; j++) {
                        const v1 = v.options[j]
                        if (!v1 || !v1.text) {
                            errModal(`${v.title} 有未填写的选项，请检查。`)
                            return false
                        }
                    }
                }
            }

            return true
        },


        confCheck() {
            const showToast = title => {
                this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none')
            }
            const conf = JSON.parse(JSON.stringify(this.conf))
            if (conf.active.enter_types === 2) {
                if (!this.have_password && !conf.active.password) {
                    showToast('请输入活动密码')
                    this.type_id = 3
                    return false
                }
                if (conf.active.password && conf.active.password.length < 3) {
                    showToast('活动密码不得少于3位')
                    this.type_id = 3
                    return false
                }
                if (conf.active.password && conf.active.password.length > 20) {
                    showToast('活动密码不得大于20位')
                    this.type_id = 3
                    return false
                }
                conf.active.password = conf.active.password || this.old_password
            } else {
                conf.active.password = 1
            }

            const text_list = conf.active.voice_redpack.text_list
            if (!text_list.length) {
                showToast('请配置朗读内容')
                this.type_id = 6
                return false
            }
            for (let i = 0, len = text_list.length; i < len; i++) {
                if (!text_list[i].text) {
                    showToast('朗读内容配置不完整')
                    this.type_id = 6
                    return false
                }
            }
            const min = Number(conf.active.voice_redpack.redpack_rules.amount.min)
            if (!min) {
                showToast('请填写红包奖励最小金额')
                this.type_id = 6
                return false
            }
            const max = Number(conf.active.voice_redpack.redpack_rules.amount.max)
            if (!max) {
                showToast('请填写红包奖励最大金额')
                this.type_id = 6
                return false
            }
            if (min > max) {
                showToast('红包奖励最小金额不得大于红包奖励最大金额')
                this.type_id = 6
                return false
            }
            conf.active.voice_redpack.redpack_rules.amount.min = min
            conf.active.voice_redpack.redpack_rules.amount.max = max
            const minPercent = Number(conf.active.voice_redpack.redpack_rules.minPercent)
            if (isNaN(minPercent)) {
                showToast('请填写最低正确率')
                this.type_id = 6
                return false
            }
            if (minPercent > 100) {
                showToast('最低正确率不得大于100')
                this.type_id = 6
                return false
            }
            if (minPercent < 0) {
                showToast('最低正确率不得小于0')
                this.type_id = 6
                return false
            }
            conf.active.voice_redpack.redpack_rules.minPercent = minPercent
            const submit_count = Math.floor(conf.active.voice_redpack.submit_count)
            if (isNaN(submit_count)) {
                showToast('红包领取次数限制填写有误')
                this.type_id = 6
                return false
            }
            if (submit_count < 1) {
                showToast('红包领取次数限制不得小于1')
                this.type_id = 6
                return false
            }
            conf.active.voice_redpack.submit_count = submit_count

            let conf_str = JSON.stringify(conf)
            conf_str = conf_str.replace(/·/g, '-')
            return base64['encode'](conf_str)
        },

        save() {
            if (this.success) return false

            const data = JSON.parse(JSON.stringify(this.form_data))

            if (!this.dataCheck(data)) return false
            if (!this.setMustSubmitData()) return false

            const conf = this.confCheck()
            if (!conf) return false
            data.conf = conf


            data.begin_time /= 1000
            data.end_time /= 1000

            this.saveAjax(data)
        },

        async saveAjax(data) {
            data.access_token = app.globalData['access_token']
            data.types = this.types

            this.loading = true
            uni.showLoading({
                title: '保存中...',
                mask: app.globalData['evn_version'] !== 'trial'
            })

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.admin/create_active',
                data
            })

            uni.hideLoading()
            this.loading = false

            if (!res || !res['status']) {
                xwy_api.alert(res && res['info'] || '保存失败')
                return false
            }

            this.updatePageData()

            if (data.active_id) {
                if (this.id) {
                    uni.showToast({
                        title: '保存成功',
                        icon: 'success',
                        mask: true
                    })
                    uni.navigateBack()
                    return false
                }
            }

            this.success = true
        },

        // 活动修改/创建成功，如果页面栈有详情或列表页面，更新页面数据
        updatePageData() {
            const pages = getCurrentPages()

            const update_page_list = [
                {
                    route: 'pages/voice-red-pack/user/detail',
                    _function: page => {
                        page.$vm.getDetail(true)
                    }
                },
                {
                    route: 'pages/voice-red-pack/admin/manage',
                    _function: page => {
                        page.$vm.getDetail()
                    }
                },
                {
                    route: 'pages/activity/user/index',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getActivityList()
                    }
                },
                {
                    route: 'pages/activity/user/activity_list',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getList()
                    }
                }
            ]

            update_page_list.forEach(v => {
                const page = pages.find(n => n.route === v.route)
                if (page) v._function(page)
            })
        },

        lookMyActivityList() {
            const list_page_route = 'pages/activity/user/activity_list'
            const pages = getCurrentPages()
            const list_page_index = pages.findIndex(v => {
                return v.route === list_page_route && v['options'].type && v['options'].type === 'create'
            })
            if (list_page_index === -1) return this.$uni.redirectTo(`/${list_page_route}?type=create`)

            this.$uni.navigateBack(pages.length - list_page_index - 1)
        }
    }
}
</script>

<style>
.page {
    padding-top: 40px;
    padding-bottom: 75px;
}

.type-list {
    position: fixed;
    z-index: 9;
    width: 100vw;
    top: 0;
    left: 0;
}

.type-item {
    height: 40px;
    line-height: 40px;
    text-align: center;
    box-sizing: border-box;
}

.active-type {
    color: #007AFF;
    border-bottom: 2px solid #007AFF;
}

.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}


.form-item .textarea {
    width: 100%;
    line-height: 16px;
    padding: 10px 0;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}

.must-submit-list {
    border-bottom: 1px dashed #eee;
}


.must-rules-picker {
    line-height: 34px;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    margin-left: 5px;
    padding: 0 15px;
}

.delete-rules {
    line-height: 36px;
    padding: 0 10px;
}

.must-options-item {
    padding: 3px 0;
}



.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
}

.add-image {
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
    line-height: calc((100vw - 50px) / 3);
    margin: 5px;
}

.image-item {
    width: calc(100vw - 20px);
    height: 50vw;
    border-radius: 5px;
}


.image-view {
    position: relative;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}


.top-rank-banner-item {
    padding: 5px;
    position: relative;
}

.top-rank-banner-item image {
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
}

.top-rank-banner-item .del-image-item {
    right: 8px;
}

.voice-text-item {
    padding: 5px 0;
}
.voice-text-item-content {
    width: calc(100vw - 65px);
    height: 40px;
    line-height: 38px;
    padding: 0 5px;
    border-radius: 5px;
    border: 1px solid #eee;
    box-sizing: border-box;
}
.voice-text-item-delete {
    width: 40px;
    height: 40px;
}

.voice-text-content-popup {
    border-radius: 10px;
    overflow: hidden;
}
.voice-text-content-popup-textarea-view {
    margin: 0 10px 10px;
    padding: 10px;
    border-radius: 10px;
    border: 1px solid #eee;
}
.voice-text-content-popup-textarea {
    height: 30vh;
}
.voice-text-content-popup-btn {
    border-top: 1px solid #eee;
}
.voice-text-content-popup-btn-confirm, .voice-text-content-popup-btn-cancel {
    width: 50%;
    height: 44px;
    line-height: 44px;
    text-align: center;
}
.voice-text-content-popup-btn-cancel {
    box-sizing: border-box;
    border-right: 1px solid #eee;
}

.bottom-btn-view {
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.login-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}

.save-success {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 999999;
    box-sizing: border-box;
    padding-top: 10vh;
}

.save-success .icon {
    padding-bottom: 50px;
}



.uni-date-x {
    padding: 0 !important;
}

.uni-date__x-input {
    font-size: 16px !important;
    color: #000;
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .type-list {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }


    .image-item {
        width: 480px;
        height: 200px;
    }

    .top-rank-banner-item image, .add-image {
        width: 150px;
        height: 150px;
        line-height: 150px;
    }
}
/* #endif */
</style>
