<template>
    <view>
        <template v-if="totalPage > 1">
            <view class="font14 p10" style="color: #e19898;">
                <view>
                    共{{ total }}条记录,
                    需要分{{ totalPage }}次导出,
                    每次导出{{ exportedAtOneTime }}条数据。
                </view>
            </view>

            <!--<view v-if="totalPage > 1" class="p10 font14 color-content">
                <text>每次导出</text>
                <text class="plr5">{{exportedAtOneTime}}</text>
                <text>条数据</text>
                <text class="pl10 color-primary" @click="">修改</text>
            </view>-->

            <view class="pb10">
                <view class="export-item p10 flex-kai" v-for="(item, index) in totalPage" :key="item">
                    <view class="color-title">
                        <text class="color-sub">{{ index + 1 }}、</text>
                        <text>{{ index * exportedAtOneTime + 1 }}</text>
                        <text class="plr5">-</text>
                        <text>
                            <template v-if="index + 1 === totalPage">{{ total }}</template>
                            <template v-else>{{ (index + 1) * exportedAtOneTime }}</template>
                        </text>
                    </view>
                    <view class="flex-kai color-light-primary" @click="exportItem(index + 1)">导出</view>
                </view>
            </view>
        </template>
    </view>
</template>

<script>
import openExcelFile from "@/utils/open-excel-file"

const app = getApp()

export default {
    data() {
        return {
            exportedAtOneTime: 1000,
            total: 0
        }
    },
    
    computed: {
        totalPage() {
            return Math.ceil(this.total / this.exportedAtOneTime)
        }
    },

    onLoad(params) {
        this.$uni.showLoading('加载中...')
        this.id = params.id
        this.$login.uniLogin(() => this.getActivityDetails())
    },

    methods: {
        async getActivityDetails() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details',
                data: {
                    active_id: this.id
                }
            })

            const details = res['data']['active_details']
           
            if (!details) {
                uni.hideLoading()
                return this.$uni.showModal('活动获取失败', {success: () => this.$uni.navigateBack()})
            }

            this.setTableHeader(details.conf?.must_submit || [])

            await this.checkRecordCount()
        },

        setTableHeader(must_submit) {
            const mustSubmitTitles = must_submit.map(item => item.title)
            this.mustSubmitTitles = mustSubmitTitles
            this.tableHeader = ['序号', ...mustSubmitTitles, '会员标识', '记录ID',  '红包金额', '正确率', '音频地址', '提交时间']
        },
        
        async checkRecordCount() {
            const res = await this.getRecordList(1)
            this.total = res?.data?.list?.total || 0

            if (!this.total) {
                uni.hideLoading()
                return this.$uni.showModal('暂无红包记录', {success: () => this.$uni.navigateBack()})
            }
            
            if (this.totalPage <= 1) {
                const list = res?.data?.list?.data || []
                this.exportExcel(1, list)
                return this.$uni.navigateBack()
            }
            
            uni.hideLoading()
        },

        async exportItem(page) {
            this.$uni.showLoading('导出中...')
            const res = await this.getRecordList(page)
            const list = res?.data?.list?.data || []
            this.exportExcel(page, list)
        },

        exportExcel(page, list) {
            this.$uni.showLoading('导出中...')
            
            const tableName = this.setTableName(page, list)
            const tBody = list.map((item, index) => {
                const mustSubmitValues = []
                const userMustSubmit = item['user_attend_details']?.must_submit || []
                this.mustSubmitTitles.forEach(title => {
                    mustSubmitValues.push(userMustSubmit.find(v => v.title === title)?.value || '')
                })
                const userid = item.userid || '0'
                const id = item.id || 0
                const amount = item.amount || 0
                const percent = Number((item.percent || 0) / 100) + '%'
                const audioUrl = item.conf_json?.mp3 || ''
                const sort = (page - 1) * this.exportedAtOneTime + index + 1
                return [sort, ...mustSubmitValues, userid, id, amount, percent, audioUrl, item.create_time || '']
            })
            
            const tableData = [this.tableHeader, ...tBody]
            openExcelFile.openDocument(tableData, tableName)
            
            uni.hideLoading()
        },


        
        setTableName(page, list) {
            if (this.totalPage <= 1) return '红包记录'
            const start = (page - 1) * this.exportedAtOneTime + 1
            const end = start + list.length - 1
            return `红包记录 第${start}-${end}条`
        },
        
        getRecordList(page) {
            return this.xwy_api.request({
                url: 'front.flat.active.red_pack.voice.userSubmit/submit_voice_user_records_list',
                data: {
                    active_id: this.id,
                    page,
                    perpage: this.exportedAtOneTime
                }
            })
        }
    }
}
</script>

<style lang="scss">
.export-item {
    margin: 10px;
    border-radius: 5px;
    background-color: #f8f8f8;
}
</style>