<template>
    <view class="page bg-background">

        <view class="chat-container">

            <scroll-view class="chat-contents" :scroll-y="true"
                         :scroll-into-view="scrollIntoViewId" :scroll-with-animation="true">
                <view class="chat-content-item chat-content-ai">
                    <view class="color-content">请输入需要查询的物品。如：</view>

                    <view class="enter-tips flex-row flex-wrap">
                        <view v-for="(item, index) in enterTips" :key="index" class="enter-tips-item"
                              @click="chat_user_content = item">{{ item }}
                        </view>
                    </view>
                </view>
                
                <view v-for="(item, index) in chat_record_list" :key="index"
                      :id="`chat-content-item-${index}`">

                    <view v-if="item.role === 'user'" class="clear clearfix">
                        <view class="chat-content-item chat-content-user fr">
                            <text>{{ item.content }}</text>
                        </view>
                    </view>

                    <view v-if="item.role === 'ai'" class="chat-content-item chat-content-ai">
                        <text>{{ item.content }}</text>
                    </view>
                </view>

                <view v-if="ai_loading" id="ai-loading" class="chat-content-item chat-content-ai">
                    <load-ani/>
                </view>
            </scroll-view>


            <view class="chat-input-container bg-white">
                <view class="flex-row">
                    <view style="width: 100%;">
                        <uni-easyinput type="textarea" v-model="chat_user_content" :maxlength="50"
                                       placeholder="我的鞋子放在哪里"/>
                    </view>
                    <view style="display: flex; flex-direction: column-reverse">

                        <view class="chat-send flex-all-center"
                              :class="chat_user_content.length && !ai_loading ? 'bg-primary' : 'bg-disabled'"
                              :hover-class="chat_user_content.length ? 'navigator-hover' : 'none'"
                              @click="chatSend">
                            <uni-icons type="paperplane-filled" size="24" color="#ffffff"/>
                        </view>
                    </view>
                </view>
            </view>


        </view>

    </view>
</template>

<script>

export default {
    data() {
        return {
            chat_user_content: '',
            ai_loading: false,
            chat_record_list: [],
            enterTips: ['鞋子', '鞋子在哪里', '我的鞋子放在哪里']
        }
    },

    computed: {
        scrollIntoViewId() {
            if (this.ai_loading) return 'ai-loading'
            if (!this.chat_record_list) return ''
            return `chat-content-item-${this.chat_record_list.length - 1}`
        }
    },

    onLoad() {
        this.$uni.showLoading('加载中...')
        this.$login.uniLogin(() => uni.hideLoading())
    },

    methods: {
        chatSend() {
            if (!this.chat_user_content) return this.$uni.showToast('请输入要查询的物品')

            this.askAI()
        },

        async askAI(content = this.chat_user_content) {
            if (this.ai_loading) return

            this.chat_record_list.push({
                role: 'user',
                content
            })

            this.chat_user_content = ''

            this.ai_loading = true
            const res = await this.xwy_api.request({
                url: 'front.flat.active.chat.user.chat/system_product_result',
                data: {
                    system_product: 'ai_item_storage',
                    // used_company: 'doubao',  // 豆包会输出物品记录的全称，而不是输入的物品名称
                    subject: content,
                    // description: this.getChatPrompt(content)
                }
            })
            this.ai_loading = false

            const ai_reply_list = res?.data?.res?.content
            if (!ai_reply_list?.length) return this.chatError()

            this.chat_record_list.push({
                role: 'ai',
                content: ai_reply_list.join('\n')
            })
        },

        /* getChatPrompt(content) {
             return `
             请根据以下文字内容提取出物品存放的位置
             
             注意！！！必须按照下面要求提取：
             1、如果没有在数据库里找到物品或者物品的存放位置，请回复“没有找到”
             
             下面是需要你提取分析的文字内容：
             ${content}
             `
         },*/

        chatError(content = '输入有误') {
            this.chat_record_list.push({
                role: 'ai',
                content
            })
        }
    }
}
</script>

<style lang="scss">

.chat-container {
    .chat-contents {
        height: calc(100vh - 128px - 10px);

        .enter-tips {
            padding-top: 5px;

            .enter-tips-item {
                color: #5cadff;
                padding: 5px 10px;
                font-size: 14px;
            }
        }

        .chat-content-item {
            background-color: #fff;
            margin: 10px;
            padding: 10px;
            display: inline-block;
            max-width: 80vw;
        }

        .chat-content-ai {
            border-radius: 0 10px 10px 10px;
        }
        
        .chat-content-user {
            border-radius: 10px 0 10px 10px;
            background-color: #5cadff;
            color: #fff;
        }
    }

    .chat-input-container {
        position: fixed;
        z-index: 99;
        left: 0;
        bottom: 0;
        width: 100vw;
        box-sizing: border-box;
        padding: 10px 10px 20px 10px;

        .chat-send {
            width: 40px;
            min-width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-left: 10px;
        }
    }
}
</style>