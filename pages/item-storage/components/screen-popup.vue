<template>
    <view>
        <uni-popup ref="popup" type="right" @touchmove.stop.prevent="">
            <view class="screen-popup bg-white">

                <scroll-view class="scroll-view" :scroll-y="true">
                    <uni-forms-item label="时间类型:" label-position="top">
                        <view class="date-type-list flex-row flex-wrap">
                            <view class="date-type-item" v-for="item in date_type_list" :key="item.value"
                                  @click="dateTypesChange(item.value)">
                                <view class="date-type-item-name"
                                      :class="{'date-type-active': item.value === date_types}">
                                    {{ item.name }}
                                </view>
                            </view>
                        </view>
                    </uni-forms-item>

                    <uni-forms-item label="时间范围:" label-position="top">
                        <uni-datetime-picker ref="dateRange" v-model="date_range"
                                             type="daterange" end="2038-12-31"
                                             start-placeholder="开始时间" end-placeholder="结束时间"/>
                    </uni-forms-item>
                </scroll-view>

                <view class="bottom-buttons flex-all-center">
                    <view class="reset-button" hover-class="navigator-hover" @click="reset">重置</view>
                    <view class="confirm-button" hover-class="navigator-hover" @click="confirm">确定</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "screen-popup",
    props: {
        dateTypes: {
            type: Number,
            default: 1
        },
        beginTime: {
            type: String,
            default: ''
        },
        endTime: {
            type: String,
            default: ''
        }
    },

    data() {
        return {
            date_type_list: [
                {name: '购买时间', value: 1},
                {name: '到期时间', value: 2},
                {name: '生产日期', value: 3},
                {name: '开封日期', value: 4}
            ],
            date_types: 1,
            date_range: ['', '']
        }
    },

    watch: {
        dateTypes() {
            this.date_types = this.dateTypes
        },
        beginTime() {
            this.date_range[0] = this.beginTime
        },
        endTime() {
            this.date_range[1] = this.endTime
        }
    },

    methods: {
        open() {
            this.$refs.popup.open()
        },

        reset() {
            this.date_types = 1
            this.$refs.dateRange.clear()
        },

        confirm() {
            const {date_types, dateTypes, date_range, beginTime, endTime} = this
            const [begin_time = '', end_time = ''] = date_range
            if (date_types === dateTypes && begin_time === beginTime && end_time === endTime) {
                return this.$refs.popup.close()
            }

            this.$emit('update:dateTypes', date_types)
            this.$emit('update:beginTime', begin_time)
            this.$emit('update:endTime', end_time)
            this.$emit('confirm')
            this.$refs.popup.close()
        },

        dateTypesChange(value) {
            if (value === this.date_types) return
            this.date_types = value
        }
    }
}
</script>

<style lang="scss" scoped>
.screen-popup {
    width: 80vw;
    max-width: 400px;
    padding: 10px;
    height: 100vh;
    box-sizing: border-box;

    .scroll-view {
        height: calc(100vh - 90px);

        .date-type-list {
            .date-type-item {
                width: 50%;
                padding: 5px;
                box-sizing: border-box;

                .date-type-item-name {
                    line-height: 40px;
                    border-radius: 5px;
                    text-align: center;
                    background-color: #f8f8f8;
                    color: #333;
                }

                .date-type-active {
                    background-color: #5cadff;
                    color: #fff;
                }
            }
        }
    }

    .bottom-buttons {
        padding: 10px 0;
        border-top: 1px solid #F4F4F4;

        .reset-button, .confirm-button {
            width: calc(50% - 20px);
            line-height: 38px;
            border-radius: 20px;
            text-align: center;
            margin: 0 10px;
            border: 1px solid #808080;
            color: #1A1A1A;
        }

        .confirm-button {
            background-color: #5cadff;
            border-color: #5cadff;
            color: #fff;
        }
    }
}
</style>