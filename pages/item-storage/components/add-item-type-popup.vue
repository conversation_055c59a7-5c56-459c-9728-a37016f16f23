<template>
    <view>
        <uni-popup type="bottom" ref="popup" :safe-area="false" @touchmove.stop.prevent="">
            <view class="add-item-type-popup bg-white">
                <view class="color-content text-center pb10">选择物品的录入方式</view>
                <view class="close flex-all-center" @click="$refs.popup.close()">
                    <uni-icons type="close" size="24" color="#dddee1"/>
                </view>
                <view class="type-list">
                    <view class="type-item" v-for="item in type_list" :key="item.type"
                          :style="{backgroundColor: item.bg_color}" hover-class="navigator-hover"
                          @click="clickItem(item)">
                        <view class="clearfix clear">
                            <view class="type-icon fl">
                                <text v-if="item.icon" class="iconfont font34" :class="item.icon"
                                      :style="{color: item.icon_color}"></text>
                                <uni-icons v-else class="uni-icons--" :type="item.uni_icon" size="40"
                                           :color="item.icon_color"/>
                            </view>
                            <!--<view v-if="item.disabled" class="fr color-disabled font14">敬请期待</view>-->
                        </view>
                        <view class="color-title font18 pt10 pb5">{{ item.name }}</view>
                        <view class="color-content font14">{{ item.describe }}</view>
                    </view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "add-item-type-popup",
    data() {
        return {
            type_list: []
        }
    },

    mounted() {
        this.setTypeList()
    },

    methods: {
        setTypeList() {
            const list = [
                {
                    type: 'form',
                    name: '手动录入',
                    describe: '输入信息 录入物品',
                    icon: 'icon-writing',
                    bg_color: '#E0F2F1',
                    icon_color: '#49B6B6'
                },
                {
                    type: 'code',
                    name: '条形码录入',
                    describe: '扫描商品、物品条形码',
                    icon: 'icon-bar-code',
                    bg_color: '#FFF5EC',
                    icon_color: '#FAAD5F'
                },
                {
                    type: 'batch',
                    name: '批量录入',
                    describe: '输入多个物品名称',
                    icon: 'icon-batch-entry',
                    bg_color: '#FFEAED',
                    icon_color: '#FA506A'
                },
                {
                    type: 'ai_chat',
                    name: '对话录入',
                    describe: '与AI聊天录入物品',
                    icon: 'icon-ai-chat',
                    bg_color: '#dfefff',
                    icon_color: '#3f9cf5'
                },
                {
                    type: 'copy',
                    name: '复制物品',
                    describe: '复制一个已录入的物品',
                    icon: 'icon-copy',
                    bg_color: '#EFF6FE',
                    icon_color: '#AF87F0'
                },
                {
                    type: 'excel_import',
                    name: 'Excel导入',
                    describe: 'Excel表格批量导入物品',
                    icon: 'icon-export-excel',
                    bg_color: '#e2fff2',
                    icon_color: '#21A367'
                },
                {
                    type: 'take_photo',
                    name: '拍照录入',
                    describe: '拍照识别物品',
                    uni_icon: 'camera',
                    bg_color: '#FFF5EC',
                    icon_color: '#FAAD5F',
                    disabled: true
                }
            ]

            this.type_list = list.map(item => {
                if (item.disabled) {
                    item.bg_color = '#F6F6F6'
                    item.icon_color = '#B8B8B8'
                }
                return item
            })
        },

        open() {
            this.$refs.popup.open()
        },

        clickItem(item) {
            const {type, disabled} = item
            if (disabled) return this.$uni.showToast('开发中 敬请期待')

            const options = {
                form: () => this.toAdd(),
                code: () => this.scanCode(),
                batch: () => this.toBatchAdd(),
                copy: () => this.toCopyItem(),
                ai_chat: () => this.toAiChatAdd(),
                excel_import: () => this.excelImport(),
                take_photo: () => {}
            }

            options[type]?.()
        },

        scanCode() {
            uni.scanCode({
                scanType: ['barCode'],
                success: res => {
                    if (!res?.result) return this.$uni.showModal('请扫物品条形码')

                    this.code2info(res.result)
                },
                fail: err => {
                    if (err?.errMsg === 'scanCode:fail cancel') return
                    this.$uni.showModal(JSON.stringify(err))
                }
            })
        },

        async code2info(result) {
            this.$uni.showLoading('物品识别中...')
            const res = await this.xwy_api.request({
                url: 'front.user.account.code/line_code_goods',
                data: {
                    code: result
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '物品识别失败')

            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace details.goodsName */
            /** @namespace details.brandName */

            const details = res?.data?.details
            if (!details?.goodsName) {
                // 如果没有商品名称，提示识别失败。因为有可能接口返回识别成功，但是所有数据都是空值
                return this.$uni.showModal('物品识别失败')
            }

            this.$uni.navigateTo(`../item/add/add?scan_code=1`, {
                events: {
                    itemListUpload: () => this.$emit('itemListUpload')
                },
                success: res => {
                    this.$refs.popup.close()
                    res.eventChannel.emit('scanCodeDetails', {
                        code: details.code,
                        name: details.goodsName,
                        brand_name: details.brandName || '',
                        logo: details.img || '',
                        amount: details.price || ''
                    })
                }
            })
        },
        
        toAddPage(src) {
            this.$uni.navigateTo(src, {
                events: {
                    itemListUpload: () => this.$emit('itemListUpload')
                },
                success: () => this.$refs.popup.close()
            })
        },

        toAdd() {
            this.toAddPage(`../item/add/add`)
        },

        toBatchAdd() {
            this.$refs.popup.close()
            this.$uni.navigateTo('../item/add/batch', {
                events: {
                    itemListUpload: () => this.$emit('itemListUpload')
                },
                success: () => this.$refs.popup.close()
            })
        },

        toAiChatAdd() {
            this.toAddPage(`../item/add/ai-chat`)
        },

        excelImport() {
            return this.toAddPage('./add/excel-import')
        },

        toCopyItem() {
            this.$uni.navigateTo('../item/list?copy=1', {
                events: {
                    itemListUpload: () => {
                        this.$emit('itemListUpload')
                        setTimeout(() => this.$uni.showToast('物品已复制', 'success'), 1300)
                    }
                },
                success: () => this.$refs.popup.close()
            })
        },
    }
}
</script>

<style lang="scss" scoped>
.add-item-type-popup {
    border-radius: 10px 10px 0 0;
    padding: 10px 0;
    position: relative;

    .close {
        padding: 2px;
        position: absolute;
        top: 0;
        right: 0;
    }

    .type-list {
        padding: 15px;
        display: grid;
        grid-template-columns: 2fr 2fr;
        grid-row-gap: 15px;
        grid-column-gap: 15px;

        .type-item {
            padding: 10px;
            border-radius: 5px;

            .type-icon {
                width: 34px;
                height: 34px;
                overflow: hidden;

                .uni-icons-- {
                    position: relative;
                    top: -3px;
                    left: -3px;
                }
            }
        }
    }
}
</style>