<template>
    <view :class="'item item-' + showType">
        <view class="logo">
            <image v-if="item.logo" class="logo-image" :src="item.logo" mode="aspectFill"
                   :style="{width: logoSize + 'px', height: logoSize + 'px'}"/>
            <view v-else class="no-logo flex-all-center" 
                  :style="{width: logoSize + 'px', height: logoSize + 'px'}">
                <uni-icons type="image" size="34" color="#bbbec4"/>
            </view>
        </view>

        <view class="item-info">
            <view class="pb5 ellipsis">{{ item.name }}</view>
            <view v-if="showSpacePath && item.position_category_id || item.position_place"
                  class="flex-row flex-wrap color-sub font14">
                <space-path :space-id="item.position_category_id" icon-color="#bbbec4"/>
                <view v-if="showType === 'list' && item.position_place">
                    <view v-if="item.position_category_id" class="pl5">({{ item.position_place }})</view>
                    <view v-else>{{ item.position_place }}</view>
                </view>
            </view>
            <view v-if="item.category_name || expireText" class="flex-row" style="padding-top: 2px;">
                <view v-if="item.category_name" class="category-tag font12 color-sub">
                    {{ item.category_name }}
                </view>
                <view v-if="expireText" class="font14 color-sub">{{ expireText }}</view>
            </view>
        </view>
    </view>
</template>

<script>
import spacePath from "../space-path.vue"

export default {
    name: "item-item",
    components: {spacePath},
    props: {

        // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
        /** @namespace item.logo */
        /** @namespace item.name */
        /** @namespace item.category_name */
        /** @namespace item.position_place */
        /** @namespace item.position_category_id */

        item: {
            type: Object,
            default: () => ({})
        },

        /**
         * @description 展示类型
         * @value 'list' 一行一个列表展示
         * @value 'grid' 一行多个宫格展示
         * */
        showType: {
            type: String,
            default: 'list'
        },

        // 是否显示物品存放位置路径
        showSpacePath: {
            type: Boolean,
            default: false
        },
        
        // 是否显示到期时间
        showExpire: {
            type: Boolean,
            default: true
        },

        gridWidth: {
            type: Number,
            default: 0
        }
    },
    
    data() {
        return {
            spacePath: []
        }
    },
    
    computed: {
        logoSize() {
            return this.showType === 'list' ? 60 : this.gridWidth
        },
        
        expireText() {
            if (!this.showExpire) return ''
            
            const expired_date = this.item.expired_date
            if (!expired_date) return ''
            
            const expired_date_time = new Date(expired_date.replace(/-/g, '/')).getTime()
            const today_time = new Date(this._utils.getDay(0, true, '/')).getTime()
            const expired_day = Math.ceil((expired_date_time - today_time) / (1000 * 60 * 60 * 24))
            
            if (expired_day > 30) return ''
            
            if (expired_day < 0) return '已过期'
            if (expired_day === 0) return '今天到期'
            if (expired_day === 1) return '明天到期'
            if (expired_day === 30) return '1个月后到期'
            
            return `${expired_day}天后过期`
        }
    }
}
</script>

<style lang="scss">
.item {
    .logo {
        
        .logo-image, .no-logo {
            border-radius: 5px;
        }
        
        .logo-image {
            display: block;
        }
    }

    .item-info {
        .category-tag {
            padding: 0 5px;
            border-radius: 2px;
            border: 1px solid #ddd;
            margin-right: 5px;
        }
    }
}

.item-list {
    display: flex;
    flex-direction: row;
    
    .logo {
        padding-right: 10px;
    }
}

.item-grid {
    .item-info {
        padding: 10px;
    }
}
</style>