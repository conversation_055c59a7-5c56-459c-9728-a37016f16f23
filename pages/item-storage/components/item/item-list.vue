<template>
    <view :class="'list list-' + showType">
        <view v-for="item in list" :key="item.item_id" class="item bg-white"
              :style="{width: showType === 'list' ? 'calc(100% - 20px)' : `${gridWidth}px`}"
              @click="itemClick(item.item_id)">
            <item-item :item="item" :grid-width="gridWidth" :showType="showType" 
                       :show-space-path="showSpacePath"/>
            
            <view v-if="showCopy && showType === 'list'" class="action-bar clearfix clear">
                <view class="fr flex-row">
                    <view class="action-item" @click.stop="copyItem(item.item_id)">
                        <text class="iconfont icon-copy color-light-primary font14"></text>
                        <text class="color-light-primary font14">复制物品</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import itemItem from './item-item.vue'

export default {
    name: "item-list",
    components: {itemItem},
    emits: ['itemClick', 'copyItem'],
    props: {
        list: {
            type: Array,
            default: () => []
        },

        /**
         * @description 展示类型
         * @value 'list' 一行一个列表展示
         * @value 'grid' 一行多个宫格展示
         * */
        showType: {
            type: String,
            default: 'list'
        },

        // 宫格展示 一行显示多少个
        gridCount: {
            type: Number,
            default: 2
        },

        // 是否显示物品存放位置路径
        showSpacePath: {
            type: Boolean,
            default: false
        },
        
        // 是否显示复制物品，组件编写的扩展性不强，只能这样传递参数修修补补了
        showCopy: {
            type: Boolean,
            default: false
        }
    },

    data() {
        return {
            gridWidth: 0
        }
    },
    
    computed: {
        styleChange() {
            const keys = ['showType', 'gridCount']
            const options = {}
            keys.forEach(key => options[key] = this[key])
            return options
        }
    },
    
    watch: {
        styleChange() {
            this.getGridWidth()
        }
    },

    mounted() {
        this.getGridWidth()
    },

    methods: {
        getGridWidth() {
            this.$nextTick(() => {
                uni.createSelectorQuery().in(this).select(".list").boundingClientRect(({width}) => {
                    const margin = 8 * this.gridCount * 2
                    this.gridWidth = (width - margin) / this.gridCount
                }).exec()
            })
        },

        itemClick(item_id) {
            this.$emit('itemClick', item_id)
        },

        copyItem(item_id) {
            this.$emit('copyItem', item_id)
        }
    }
}
</script>

<style lang="scss">
.list {
    .item {
        border-radius: 5px;
        box-sizing: border-box;
    }
}

.list-list {
    .item {
        margin: 10px;
        padding: 10px;
    }
}

.list-grid {
    margin: 8px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    .item {
        margin: 8px;
    }
}

.action-bar {
    border-top: 1px solid #eee;
    padding-top: 10px;
    margin-top: 10px;
    
    .action-item {
        line-height: 34px;
        border: 1px solid #8bc7ff;
        border-radius: 18px;
        padding: 0 10px;
        
        .iconfont {
            padding-right: 2px;
        }
    }
}
</style>