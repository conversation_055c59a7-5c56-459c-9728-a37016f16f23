<template>
    <view class="flex-row" :class="wrap ? 'flex-wrap' : 'no-wrap'">
        <template v-if="space_path.length">
            <view v-for="item in space_path" :key="item.id">
                <uni-icons v-if="item.id !== space_path[0].id" type="forward" :size="size" :color="iconColor"
                           :style="{padding: `0 ${iconGap}`}"/>
                <text :style="textStyle">{{ item.name }}</text>
            </view>
        </template>
    </view>
</template>

<script>
import _API_ from '../api'

export default {
    name: "space-path",
    props: {
        spaceId: {
            type: Number,
            default: 0
        },
        color: {
            type: String,
            default: ''
        },
        size: {
            type: String,
            default: '14px'
        },
        iconColor: {
            type: String,
            default: '#80848f'
        },
        iconGap: {
            type: String,
            default: '0'
        },
        wrap: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            space_path: []
        }
    },
    
    watch: {
        spaceId() {
            this.getSpacePath()
        }  
    },
    
    computed: {
        textStyle() {
            let style = `font-size: ${this.size};`
            if (this.color) style += `color: ${this.color};`
            return style
        }
    },
    
    mounted() {
        this.getSpacePath()
    },

    methods: {
        async getSpacePath() {
            if (!this.spaceId) return
            this.space_path = await _API_.getSpacePath(this.spaceId)
        }
    }
}
</script>

<style lang="scss">

</style>