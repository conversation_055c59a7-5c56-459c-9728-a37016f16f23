<template>
    <view>
        <uni-popup ref="popup" type="bottom" :safe-area="false" @touchmove.stop.prevent="">
            <view class="popup bg-white">
                <view class="flex-kai">
                    <view class="top-bar flex-row">
                        <view class="top-bar-item" :class="{'active-top-bar': item.type === top_bar_type}"
                              v-for="item in top_bar_list" :key="item.type" @click="top_bar_type = item.type">
                            {{ item.title }}
                        </view>
                    </view>

                    <view v-show="top_bar_type === 'diy'" class="diy-manage-enter" @click="toManage">
                        <uni-icons type="gear" size="14" color="#5cadff"/>
                        <text>管理</text>
                    </view>
                </view>


                <scroll-view :scroll-y="true" class="category-list">
                    <view class="text-center flex-row flex-wrap">
                        <view v-for="item in category_list" :key="item.category_id" class="category-item"
                              :class="{'category-active': item.category_id === categoryId}"
                              @click="clickItem(item)">{{ item.name }}
                        </view>

                        <view v-if="top_bar_type === 'diy' && category_list.length" class="category-item"
                              @click="toAddMyCategory">
                            <uni-icons type="plusempty" size="14" color="#bbbec4"/>
                            <text class="pl5">添加</text>
                        </view>
                    </view>

                    <view v-if="top_bar_type === 'diy' && !category_list.length" class="empty-list">
                        <text class="iconfont icon-empty-state color-border"></text>
                        <view class="color-sub font12">无自定义物品分类</view>
                        <view class="add-button" @click="toAddMyCategory">去添加</view>
                    </view>
                </scroll-view>

                <view class="flex-all-center">
                    <view class="color-sub font14 p10" @click="$refs.popup.close()">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import _API_ from '../api'

export default {
    name: "category-choose-popup",
    props: {
        categoryId: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            top_bar_type: 'system',
            top_bar_list: [
                {type: 'system', title: '系统'},
                {type: 'diy', title: '自定义'}
            ],
            system_category_list: [],
            my_category_list: [],
        }
    },

    watch: {
        categoryId(val) {
            if (!val) {
                this.top_bar_type = 'system'
                return
            }

            const {system_category_list: system, my_category_list: my, top_bar_type: type} = this
            if (system.find(v => v.category_id === val) && type !== 'system') {
                this.top_bar_type = 'system'
                return
            }
            if (my.find(v => v.category_id === val) && type !== 'diy') this.top_bar_type = 'diy'
        }
    },

    computed: {
        category_list() {
            const {top_bar_type, system_category_list, my_category_list} = this
            return top_bar_type === 'system' ? system_category_list : my_category_list
        }
    },

    methods: {
        open() {
            this.getCategoryList()
            this.$refs.popup.open()
        },

        async getCategoryList() {
            await this.getSystemCategoryList()
            await this.getMyCategoryList()
        },

        async getSystemCategoryList() {
            this.system_category_list = await _API_.getSystemCategory()
        },

        async getMyCategoryList(reload = false) {
            this.my_category_list = await _API_.getMyCategoryList(reload)
        },

        toManage() {
            this.$uni.navigateTo('/pages/category/list?types=32', {
                events: {
                    uploadList: () => this.uploadMyCategoryList()
                }
            })
        },

        toAddMyCategory() {
            this.$uni.navigateTo('/pages/category/edit?types=32', {
                events: {
                    upload: () => this.uploadMyCategoryList()
                }
            })
        },

        async uploadMyCategoryList() {
            await this.getMyCategoryList(true)
            this.$emit('uploadCategoryList')
        },

        clickItem(item) {
            this.$emit('clickItem', item)
            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.popup {
    padding: 15px 0;
    border-radius: 10px 10px 0 0;

    .top-bar .top-bar-item, .diy-manage-enter {
        font-size: 14px;
        margin: 10px;
        line-height: 32px;
    }

    .top-bar {
        .top-bar-item {
            margin: 10px;
            padding: 0 10px;
            line-height: 32px;
            border-radius: 16px;
            color: #666;
            font-size: 14px;
        }
    }

    .diy-manage-enter {
        color: #5cadff;
    }

    .category-list {
        padding: 10px;
        height: 270px;

        .category-item {
            margin: 5px;
            padding: 10px;
            font-size: 14px;
            background-color: #f8f8f8;
            color: #495060;
            border-radius: 2px;
        }
    }

    .active-top-bar, .category-active {
        background-color: #5cadff !important;
        color: #fff !important;
    }

    .empty-list {
        text-align: center;
        padding-top: 30px;

        .iconfont {
            font-size: 80px;
        }

        .add-button {
            width: 120px;
            line-height: 36px;
            border-radius: 18px;
            font-size: 14px;
            margin: 20px auto 0;
            background-color: #5cadff;
            color: #fff;
        }
    }
}
</style>