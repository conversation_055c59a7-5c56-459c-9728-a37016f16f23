<template>
    <view>
        <uni-popup ref="popup" type="bottom" @touchmove.stop.prevent="" :safe-area="false">
            <view class="popup bg-white">
                <view class="close flex-all-center" @click="$refs.popup.close()">
                    <uni-icons type="closeempty" color="#bbbec4" size="24"/>
                </view>

                <view class="color-title text-center">系统位置管理</view>


                <scroll-view class="scroll-view" :scroll-y="true">
                    <view class="p10">
                        <view>
                            <text class="color-content">已使用</text>
                            <text class="color-sub font12 pl5">(点击移除)</text>
                        </view>
                        <view class="list flex-row flex-wrap">
                            <view class="item" v-for="item in usedList" :key="item.id"
                                  hover-class="navigator-hover" @click="removeSpace(item.id)">
                                {{ item.name }}
                            </view>
                        </view>
                    </view>

                    <view class="p10">
                        <view>
                            <text class="color-content">未使用</text>
                            <text class="color-sub font12 pl5">(点击使用)</text>
                        </view>
                        <view class="list flex-row flex-wrap">
                            <view class="item" v-for="item in removedList" :key="item.id"
                                  hover-class="navigator-hover" @click="addSpace(item.id)">
                                {{ item.name }}
                            </view>
                        </view>
                    </view>
                </scroll-view>

                <view class="confirm-button bg-light-primary color-white text-center"
                      hover-class="navigator-hover" @click="confirm">确定
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import _API_ from '../api'

const STORAGE_KEY = 'item-storage-system-space-remove-id-list'

export default {
    name: "system-space-set",
    emits: ['confirm'],
    data() {
        return {
            system_space: [],
            removed_id_list: []
        }
    },

    computed: {
        usedList() {
            return this.system_space.filter(item => !this.removed_id_list.includes(item.id))
        },

        removedList() {
            return this.system_space.filter(item => this.removed_id_list.includes(item.id))
        }
    },

    methods: {
        async open() {
            this.system_space = await _API_.getSystemSpace(false, true)
            this.removed_id_list = uni.getStorageSync(STORAGE_KEY) || []
            this.$refs.popup.open()
        },

        removeSpace(id) {
            this.removed_id_list.push(id)
        },

        addSpace(id) {
            this.removed_id_list.splice(this.removed_id_list.indexOf(id), 1)
        },

        async confirm() {
            this.$uni.showLoading('保存中...')
            uni.setStorageSync(STORAGE_KEY, this.removed_id_list)
            await _API_.getSystemSpace(true)
            uni.hideLoading()
            this.$emit('confirm')
            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.popup {
    position: relative;
    padding: 10px 0;
    border-radius: 10px 10px 0 0;

    .close {
        position: absolute;
        right: 0;
        top: 0;
        width: 40px;
        height: 40px;
    }

    .scroll-view {
        height: 350px;

        .item {
            padding: 10px;
            margin: 10px;
            border-radius: 2px;
            background-color: #f8f8f8;
            color: #333;
            font-size: 14px;
        }
    }

    .confirm-button {
        margin: 20px auto;
        width: 150px;
        line-height: 40px;
        border-radius: 5px;
    }
}
</style>