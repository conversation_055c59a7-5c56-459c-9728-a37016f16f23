<template>
    <view class="page">
        
    </view>
</template>

<script>
const app = getApp()

export default {
    data() {
        return {
            
        }
    },
    
    onLoad(params) {
        this.uuid = params.uuid
        this.type = Number(params.type)
        this.$login.uniLogin(() => {
            this.init()
        })
    },

    methods: {
        async init() {
            
        }
    }
}
</script>

<style lang="scss" scoped>

</style>
