<template>
    <view class="page">
        <view class="type-bar">
            <view class="type-bar-container flex-row">
                <view class="type-item" :class="{'active-type': item.type === activeType}"
                      :style="{width: `calc(100% / ${typeOptions.length})`}"
                      v-for="item in typeOptions" :key="item.type">
                    {{ item.title }}
                </view>
            </view>
        </view>

        <view v-if="!loading && !total" class="empty-list text-center">
            <text class="iconfont icon-team color-border"></text>
            <view class="color-sub font14">暂无{{ typeTitle }}亲友</view>
            
            <button class="share-button" open-type="share">邀请亲友一起管理</button>
        </view>

        <view v-if="loading" class="text-center" :style="{paddingTop: currentPage === 1 ? '30vh' : '0'}">
            <load-ani/>
            <view v-if="currentPage === 1" class="color-sub font12">加载中...</view>
        </view>

        <view v-if="activeType === 1 && !loading && total" class="bottom-share-button">
            <button class="share-button" open-type="share">邀请亲友一起管理</button>
        </view>
    </view>
</template>

<script>
const app = getApp()

export default {
    data() {
        return {
            typeOptions: [
                {type: 1, title: '我邀请的'},
                {type: 2, title: '我管理的'}
            ],
            activeType: 1,

            loading: true,
            familyList: [],
            currentPage: 1,
            pageSize: 10,
            total: 0,
            isLastPage: false,
        }
    },

    computed: {
        typeTitle() {
            return this.typeOptions.find(item => item.type === this.activeType).title
        }
    },

    onLoad() {
        this.$login.uniLogin(() => {
            this.init()
        })
    },

    onPullDownRefresh() {
        this.currentPage = 1
        this.init()
    },

    onReachBottom() {
        if (this.loading || this.isLastPage) return
        this.currentPage++
        this.getFamilyList().finally(() => uni.stopPullDownRefresh())
    },

    methods: {
        async init() {
            await this.getFamilyList()
        },

        async getFamilyList() {
            if (this.currentPage === 1) {
                this.total = 0
                this.familyList = []
            }

            const data = {page: this.currentPage, perpage: this.pageSize}
            // 被邀请
            if (this.activeType === 2) data.is_helper = 1

            this.loading = true
            const res = await this.xwy_api.request({
                url: 'front.user.account.shareManageRank/share_manage_user_list',
                data
            })
            this.loading = false

            let total = 0, list = [], isLastPage = false

            if (res?.status === 1 && res?.data?.list) {
                const result = res.data.list
                total = result.total || 0
                list = result.data || []
                isLastPage = result.is_lastpage
            } else {
                isLastPage = true
            }

            this.total = total
            this.familyList = this.currentPage === 1 ? list : [...this.familyList, ...list]
            this.isLastPage = isLastPage
        }
    },

    onShareAppMessage() {
        return {
            title: '一起来管理我的物品吧',
            path: `/pages/item-storage/family/share?type=1&uuid=${app.globalData.userinfo.userid}`,
            imageUrl: 'https://7072-prod-0g479j60184f120d-**********.tcb.qcloud.la/public/img/jbz_xcx/small-feature-collection/item-storage.png'
        }
    }
}
</script>

<style lang="scss" scoped>
$page-bg-color: #f8f8f8;

.page {
    box-sizing: border-box;
    min-height: 100vh;
    background-color: $page-bg-color;
    padding-top: 80px;
    padding-bottom: 70px;
}

.type-bar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;
    background-color: $page-bg-color;
    padding: 20px 40px;
    width: 100vw;
    box-sizing: border-box;

    .type-bar-container {
        width: 100%;
        border-radius: 20px;
        overflow: hidden;
        background-color: #fff;
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);

        .type-item {
            height: 40px;
            line-height: 40px;
            text-align: center;
            color: #333;

            &.active-type {
                color: #fff;
                background-color: #5cadff;
                border-radius: 20px;
            }
        }
    }
}

.share-button {
    line-height: 40px;
    border-radius: 20px;
    background-color: #5cadff;
    color: #fff;
    font-size: 14px;

    &::after {
        border: none;
    }
}

.empty-list {
    padding-top: 30vh;

    .iconfont {
        font-size: 100px;
    }

    .share-button {
        margin: 50px auto 0;
        width: 200px;
    }
}

.bottom-share-button {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 99;
    background-color: $page-bg-color;
    padding: 15px;
    width: 100vw;
    box-sizing: border-box;

    .share-button {
        width: 100%;
    }
}
</style>
