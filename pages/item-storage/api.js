import xwyApi from '@/utils/api/xwy_api'

export default {
    spaceListNotLimit: null,
    
    async getSpaceListNotLimit(reload = false) {
        if (this.spaceListNotLimit && !reload) return this.spaceListNotLimit
        
        const res = await xwyApi.request({
            url: 'front.user.category/category_list_not_limit',
            data: {
                types: 33
            }
        })
        const list = res?.data?.category_list || []
        this.spaceListNotLimit = list
        return list
    },
    
    
    async getSpacePath(id) {
        if (!id) return []
        const system_space = await this.getSystemSpace()
        const my_space = await this.getSpaceListNotLimit()
        const list = [...system_space, ...my_space]
        let space = list.find(v => v.id === id)
        if (!space) return []
        
        const path = []
        while (space) {
            path.unshift({id: space.id, pid: space.pid, name: space.name})
            space = list.find(v => v.id === space.pid)
        }
        return path
    },
    
    /**
     * @description 获取物品列表
     * @param {Object} data - 请求数据对象
     * @param {Number} [data.page] - 当前页码
     * @param {Number} [data.perpage] - 每页显示的物品数量
     * @param {String} [data.keyword] - 搜索关键词，可搜索物品名称，存放地址以及备注
     * @param {Number} [data.item_category_id] - 物品分类ID，筛选物品
     * @param {Number} [data.position_category_id] - 物品存放位置ID，筛选物品
     * @param {Number} [data.date_types] - 搜索的日期类型：
     *     1 - 购买时间搜索
     *     2 - 到期时间搜索
     *     3 - 生产日期
     *     4 - 开封日期
     * @param {String} [data.begin_time] - 开始时间，格式为 yyyy-mm-dd
     * @param {String} [data.end_time] - 结束时间，格式为 yyyy-mm-dd
     * @returns {Promise<Object>} - 返回物品列表数据的Promise对象
     */
    getItemList(data) {
        data.page ||= 1
        data.perpage ||= 20
        
        return xwyApi.request({
            url: 'front.flat.sport_step.item_storage.manage/user_item_list',
            data
        })
    },
    
    initItemListData(list) {
        return list.map(item => {
            if (item.position_cate_details) delete item.position_cate_details
            if (item.item_cate_details) {
                item.category_name = item.item_cate_details.name
                delete item.item_cate_details
            }
            return item
        })
    },
    
    
    async getSystemSpace(reload = false, getAll = false) {
        const systemSet = await this.getSystemSet(reload)
        let system_space = systemSet.position_category || []
        
        system_space = system_space.map(item => {
            return {
                id: item.idx,
                name: item.name,
                logo: item.logo || '',
                level: 0,
                pid: 0
            }
        })
        
        if (!getAll) {
            const removed_id_list = uni.getStorageSync('item-storage-system-space-remove-id-list')
            if (removed_id_list?.length) {
                system_space = system_space.filter(item => !removed_id_list.includes(item.id))
            }
        }
        
        return system_space
    },
    
    async getSystemCategory() {
        const systemSet = await this.getSystemSet()
        const item_category = systemSet.item_category || []
        return item_category.map(item => {
            return {
                category_id: item.idx,
                name: item.name
            }
        })
    },
    
    systemSet: null,
    async getSystemSet(reload = false) {
        if (this.systemSet && !reload) return this.systemSet
        // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
        /** @namespace res.data.system_set */
        const res = await xwyApi.request({url: 'front.flat.sport_step.item_storage.manage/item_system_set'})
        const systemSet = res?.data?.system_set || {item_category: [], position_category: []}
        this.systemSet = systemSet
        return systemSet
    },
    
    myCategoryList: null,
    async getMyCategoryList(reload = false) {
        if (!reload && this.myCategoryList) return this.myCategoryList
        
        const res = await xwyApi.getCategoryList({
            my_self: 1,
            types: 32,
            page: 1,
            perpage: 100
        })
        
        const list = res?.data?.category_list?.data || []
        this.myCategoryList = list
        return list
    },
    
    getItemDetailsKeys() {
        return ['name', 'logo', 'position_place', 'item_category_id', 'position_category_id', 'buy_date', 'product_date', 'expired_date', 'open_date', 'num', 'left_num', 'amount', 'brand_name', 'memo', 'sort_num']
    }
}