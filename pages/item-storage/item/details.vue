<template>
    <view>
        <uni-transition :show="loading" :mode-class="['fade', 'slide-top']">
            <view class="text-center">
                <view style="width: 100%; height: 30vh;"></view>
                <load-ani/>
            </view>
        </uni-transition>

        <uni-transition :show="!loading && error_tips" :mode-class="['fade', 'slide-bottom']">
            <view class="text-center" style="padding-top: 20vh;">
                <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
                <view class="color-sub font14">{{ error_tips }}</view>
                <view class="err-back-button bg-light-primary color-white" hover-class="navigator-hover"
                      @click="back">返回
                </view>
            </view>
        </uni-transition>

        <uni-transition :show="!loading && !error_tips && item_details.item_id"
                        :mode-class="['fade', 'slide-bottom']">
            <view v-if="item_details.item_id" class="item-details-container bg-background">
                <view class="logo-name bg-white flex-row p10">
                    <template>
                        <view v-if="item_details.logo">
                            <image class="item-logo" :src="item_details.logo" mode="aspectFill"
                                   @click="previewLogo"/>
                        </view>
                        <view v-else class="item-no-logo flex-all-center bg-background">
                            <uni-icons type="image" size="34" color="#bbbec4"/>
                        </view>
                    </template>
                    <view class="pl10 w-100">
                        <view class="color-title font18">{{ item_details.name }}</view>
                        <view class="clearfix clear">
                            <view class="fr flex-row">
                                <view class="pl10 pr10" @click="toEditItem">
                                    <text class="iconfont icon-edit color-sub"></text>
                                </view>
                                <view class="pl10 pr10" @click="deleteItem">
                                    <text class="iconfont icon-delete color-sub"></text>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="item_details.position_category_id || item_details.position_place"
                      class="item-info m10 p10 bg-white uni-radius">
                    <view v-if="item_details.position_category_id" class="info-item flex-kai">
                        <view>物品位置</view>
                        <view>
                            <space-path :space-id="item_details.position_category_id"/>
                        </view>
                    </view>
                    <view v-if="item_details.position_place" class="info-item flex-kai">
                        <view>位置说明</view>
                        <view>{{ item_details.position_place }}</view>
                    </view>
                </view>

                <view v-if="item_details.category_name"
                      class="item-info m10 p10 bg-white uni-radius">
                    <view class="info-item flex-kai">
                        <view>物品分类</view>
                        <view>{{ item_details.category_name }}</view>
                    </view>
                </view>


                <view v-if="dateInfoShow" class="item-info m10 p10 bg-white uni-radius">
                    <view v-if="item_details.buy_date" class="info-item flex-kai">
                        <view>购买日期</view>
                        <view>{{ item_details.buy_date }}</view>
                    </view>
                    <view v-if="item_details.product_date" class="info-item flex-kai">
                        <view>生产日期</view>
                        <view>{{ item_details.product_date }}</view>
                    </view>
                    <view v-if="item_details.expired_date" class="info-item flex-kai">
                        <view>过期日期</view>
                        <view>
                            <text v-if="expired_days === 0" class="color-warning font12 pr5">今天到期</text>
                            <text v-else-if="expired_days < 0" class="color-error font12 pr5">
                                已过期{{ Math.abs(expired_days) }}天
                            </text>
                            <text v-else class="color-sub font12 pr5">{{ expired_days }}天</text>
                            {{ item_details.expired_date }}
                        </view>
                    </view>
                    <view v-if="item_details.open_date" class="info-item flex-kai">
                        <view>开封日期</view>
                        <view>{{ item_details.open_date }}</view>
                    </view>
                </view>

                <view v-if="itemInfoShow" class="item-info m10 p10 bg-white uni-radius">
                    <view v-if="item_details.brand_name" class="info-item flex-kai">
                        <view>品牌</view>
                        <view>{{ item_details.brand_name }}</view>
                    </view>
                    <view v-if="item_details.num" class="info-item flex-kai">
                        <view>数量</view>
                        <view>{{ item_details.num }}</view>
                    </view>
                    <view v-if="item_details.left_num" class="info-item flex-kai">
                        <view>剩余数量</view>
                        <view>{{ item_details.left_num }}</view>
                    </view>
                    <view v-if="item_details.amount" class="info-item flex-kai">
                        <view>单价</view>
                        <view>
                            <text class="font12">￥</text>
                            {{ item_details.amount.toFixed(2) }}
                        </view>
                    </view>
                    <view v-if="item_details.all_amount" class="info-item flex-kai">
                        <view>总价</view>
                        <view>
                            <text class="font12">￥</text>
                            {{ item_details.all_amount.toFixed(2) }}
                        </view>
                    </view>
                </view>

                <view v-if="item_details.memo" class="item-info m10 p10 bg-white uni-radius">
                    <view class="info-item" :class="{'flex-kai': item_details.memo.length <= 10}">
                        <view>备注说明</view>
                        <view class="color-content" :class="{'pt10': item_details.memo.length > 10}">
                            <text>{{ item_details.memo }}</text>
                        </view>
                    </view>
                </view>
            </view>
        </uni-transition>
    </view>
</template>

<script>
import spacePath from '../components/space-path.vue'

export default {
    components: {spacePath},
    data() {
        return {
            loading: true,
            item_details: {},
            error_tips: ''
        }
    },

    computed: {
        expired_days() {
            const expired_date = this.item_details.expired_date
            if (!expired_date) return null
            const today = new Date(this._utils.getDay(0, true, '/')).getTime()
            const expired_date_time = new Date(expired_date.replace(/-/g, '/')).getTime()
            return (expired_date_time - today) / (1000 * 60 * 60 * 24)
        },
        
        dateInfoShow() {
            const {buy_date, product_date, expired_date, open_date} = this.item_details
            return !!(buy_date || product_date || expired_date || open_date)
        },
        
        itemInfoShow() {
            const {brand_name, num, left_num, amount, all_amount} = this.item_details
            return !!(brand_name || num || left_num || amount || all_amount)
        }
    },

    onLoad(params) {
        this.item_id = params.item_id
        this.$login.uniLogin(() => {
            this.init()
        })
    },

    methods: {
        async init() {
            await this.getItemDetails()
        },

        async getItemDetails() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.item_storage.manage/item_goods_details',
                data: {
                    item_id: this.item_id
                }
            })
            this.loading = false

            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace details.all_amount */
            /** @namespace details.position_cate_details */
            /** @namespace details.item_cate_details */
            const details = res?.data?.details

            if (!details) {
                this.error_tips = res?.status === 1 ? '物品不存在' : res?.info || '物品不存在'
                return
            }

            if (details.name) {
                // 不知道为什么，不延迟的话修改后返回来标题还是原来的
                setTimeout(() => {
                    this.$uni.setNavigationBarTitle(details.name)
                }, 1300)
            }
            
            if (details.position_cate_details) delete details.position_cate_details
            if (details.item_cate_details) {
                if (details.item_cate_details.name) details.category_name = details.item_cate_details.name
                delete details.item_cate_details
            }

            this.item_details = details
        },

        toEditItem() {
            this.$uni.navigateTo(`./add/add?item_id=${this.item_id}`, {
                events: {
                    updateItem: () => {
                        this.updateItem()
                        this.updateItemList()
                    }
                }
            })  
        },

        async deleteItem() {
            const modal = await this.$uni.showModal('确定删除该物品', {showCancel: true})
            if (!modal.confirm) return
            
            this.$uni.showLoading('删除中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.item_storage.manage/del_item_record',
                data: {
                    ids: this.item_id
                }
            })
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '删除失败')
            
            this.$uni.showToast('已删除')
            this.updateItemList()
            this.getOpenerEventChannel?.()?.emit?.('itemDelete')
            this.$uni.navigateBack(1, {delay: 1000})
        },

        updateItemList() {
            this.getOpenerEventChannel?.()?.emit?.('reloadItemList')
        },

        async updateItem() {
            await this.getItemDetails()
            this.getOpenerEventChannel?.()?.emit?.('itemUpdate', this.item_details)
        },

        back() {
            this.$uni.navigateBack()
        },

        previewLogo() {
            if (!this.item_details.logo) return
            this.$uni.previewImage(this.item_details.logo)
        }
    }
}
</script>

<style lang="scss">
.err-back-button {
    width: 140px;
    line-height: 40px;
    border-radius: 20px;
    margin: 30px auto;
}

.item-details-container {
    min-height: 100vh;
    padding-bottom: 10px;
    box-sizing: border-box;
}

.logo-name {
    $logo-size: 60px;

    .item-logo, .item-no-logo {
        width: $logo-size;
        min-width: $logo-size;
        height: $logo-size;
        border-radius: 5px;
    }

    .item-logo {
        display: block;
    }
}

.info-item {
    padding: 10px 0;
}
</style>