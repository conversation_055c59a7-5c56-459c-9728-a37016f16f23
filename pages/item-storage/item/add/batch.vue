<template>
    <view class="page bg-background">
        <view class="list">
            <view v-if="item_list.length" class="top-bar clear clearfix bg-white font14">
                <view class="color-light-primary p10 fl"
                      @click="batchPositionPlace('')">统一设置位置说明
                </view>
                <view class="color-light-primary p10 fr" @click="batchNamesPopupOpen">添加</view>
            </view>

            <view class="form" v-for="(item, index) in item_list" :key="index">
                <view class="form-item flex-kai">
                    <view class="label">物品名称</view>
                    <view class="form-value-input">
                        <input class="form-input text-right" v-model="item.name" type="text" maxlength="20"
                               placeholder="必填, 20字以内"/>
                    </view>
                </view>
                <view class="form-item flex-kai">
                    <view class="label">位置说明</view>
                    <view class="form-value-input">
                        <input class="form-input text-right" v-model="item.position_place" type="text"
                               maxlength="50" placeholder="50字以内"/>
                    </view>
                </view>
                <view class="clearfix clear">
                    <view class="fr p10 color-sub font14" @click="item_list.splice(index, 1)">删除</view>
                </view>
            </view>
        </view>


        <view v-if="item_list.length" class="confirm-bar bg-white">
            <view class="confirm-button" hover-class="navigator-hover" @click="addConfirm">录入</view>
        </view>

        <view v-if="!item_list.length" class="text-center no-list" style="padding-top: 15vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="confirm-button" hover-class="navigator-hover" @click="batchNamesPopupOpen">
                录入物品
            </view>
        </view>


        <uni-popup ref="namesInputPopup" :is-mask-click="false">
            <view class="batch-popup bg-white">
                <view class="color-content font14 pb10">
                    <view>在下面文本框输入多个物品名称，以",""、"或换行分隔。</view>
                    <view>如: 鞋子、衣服、裤子</view>
                </view>

                <view class="pt10 pb10">
                    <uni-easyinput type="textarea" v-model="batch_names"/>
                </view>

                <view class="confirm-button" @click="batchNamesConfirm">确定</view>
                <view v-if="item_list.length" class="flex-all-center">
                    <view class="color-sub font12 p5" @click="$refs.namesInputPopup.close()">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    data() {
        return {
            batch_names: '',
            batch_position_place: '',
            item_list: []
        }
    },

    onLoad() {
        this.batchNamesPopupOpen()
    },

    methods: {
        batchNamesPopupOpen() {
            this.batch_names = ''
            this.$refs.namesInputPopup.open()
        },

        async batchPositionPlace(position_place) {
            position_place ||= this.item_list.find(item => item.position_place)?.position_place || ''
            console.log(position_place);

            const res = await this.$uni.showModal(position_place, {
                showCancel: true,
                editable: true,
                placeholderText: '请输入位置说明(50字内)'
            })

            if (!res.confirm) return

            const content = res.content
            if (content.length > 50) {
                await this.batchPositionPlace(content)
                return this.$uni.showToast('位置说明不能超过50字, 请重新输入', 'none', 3000)
            }

            this.item_list.forEach(item => {
                item.position_place = res.content
            })
        },

        batchNamesConfirm() {
            if (!this.batch_names) return this.$uni.showToast('未检测到物品')
            const names = this.batch_names.replace(/[，、\n]/g, ',')
            const name_list = names.split(',').filter(name => name.trim())
            if (!name_list.length) return this.$uni.showToast('未检测到物品')

            this.$refs.namesInputPopup.close()

            name_list.forEach(name => {
                this.item_list.push({name, position_place: ''})
            })
        },

        async addConfirm() {
            this.$uni.showLoading('录入中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.item_storage.manage/batch_add_items',
                data: {
                    item_list: this._utils.base64['encode'](JSON.stringify(this.item_list))
                }
            })
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '录入失败')
            
            this.$uni.showToast('录入成功')
            this.getOpenerEventChannel().emit('itemListUpload')
            this.$uni.navigateBack(1, {delay: 1000})
        }
    }
}
</script>

<style lang="scss" scoped>
.page {
    min-height: 100vh;
    padding-top: 50px;
    padding-bottom: 100px;
    box-sizing: border-box;
}

.top-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 99;
}

.form {
    margin-bottom: 10px;
    padding: 10px;
    background-color: #fff;

    .form-item {
        $size: 50px;

        border-bottom: 1px solid #eee;
        line-height: $size;

        &:last-child {
            border-bottom: none;
        }

        .label {
            width: 100px;
            min-width: 100px;
        }

        .form-value-input {
            width: 100%;

            .form-input {
                height: $size;
                line-height: $size;
            }
        }
    }
}

.confirm-button {
    text-align: center;
    background-color: #5cadff;
    color: #fff;
}

.confirm-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 15px;
    box-sizing: border-box;
    z-index: 99;

    .confirm-button {
        line-height: 44px;
        border-radius: 22px;
    }
}

.no-list {
    .confirm-button {
        width: 150px;
        margin: 20px auto;
        line-height: 44px;
        border-radius: 22px;
    }
}

.batch-popup {
    width: 80vw;
    max-width: 400px;
    padding: 15px;
    border-radius: 10px;

    .confirm-button {
        line-height: 40px;
        margin: 10px auto;
        width: 150px;
    }
}
</style>