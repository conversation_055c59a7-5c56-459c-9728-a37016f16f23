<template>
    <view class="page bg-background">

        <view class="chat-container">

            <scroll-view class="chat-contents" :scroll-y="true"
                         :scroll-into-view="scrollIntoViewId" :scroll-with-animation="true">
                <view class="chat-content-item chat-content-ai">
                    <view class="color-content">请输入需要查询的物品。如：</view>

                    <view class="enter-tips flex-row flex-wrap">
                        <view v-for="(item, index) in enterTips" :key="index" class="enter-tips-item"
                              @click="chat_user_content = item">{{ item }}
                        </view>
                    </view>
                </view>

                <view v-for="(item, index) in chat_record_list" :key="index">

                    <view v-if="item.role === 'user'" class="clear clearfix">
                        <view class="chat-content-item chat-content-user fr">
                            <text>{{ item.content }}</text>
                        </view>
                    </view>

                    <view v-if="item.role === 'ai'" class="chat-content-item chat-content-ai">
                        <view v-if="item.item_details" class="item-details" @click="toItemDetailsPage(index)">
                            <view class="flex-kai">
                                <view>{{ item.item_details.name }}</view>
                                <view class="color-sub font14 pl10" 
                                      style="width: 50px; min-width: 50px; line-height: 22px;">
                                    已录入
                                </view>
                            </view>
                        </view>
                        <text v-else>{{ item.content }}</text>
                    </view>

                    <view v-if="item.role === 'ai' && item.item_details" 
                          class="chat-record-action-bar clearfix clear">
                        <view class="chat-record-action-button fr font12" @click="deleteItem(index)">
                            <text class="iconfont icon-delete color-sub font12"></text>
                            <text style="padding-left: 2px;">删除</text>
                        </view>
                        <view class="chat-record-action-button fr font12" @click="toEditItem(index)">
                            <text class="iconfont icon-edit color-sub font12"></text>
                            <text style="padding-left: 2px;">修改</text>
                        </view>
                    </view>
                    
                    <view :id="`chat-content-item-${index}`"></view>
                </view>

                <view v-if="ai_loading" id="ai-loading" class="chat-content-item chat-content-ai">
                    <load-ani/>
                </view>
            </scroll-view>


            <view class="chat-input-container bg-white">
                <view class="flex-row">
                    <view style="width: 100%;">
                        <uni-easyinput type="textarea" v-model="chat_user_content" :maxlength="50"
                                       placeholder="今天买了一包卫龙的辣条，一年后过期"/>
                    </view>
                    <view style="display: flex; flex-direction: column-reverse">

                        <view class="chat-send flex-all-center"
                              :class="chat_user_content.length && !ai_loading ? 'bg-primary' : 'bg-disabled'"
                              :hover-class="chat_user_content.length ? 'navigator-hover' : 'none'"
                              @click="chatSend">
                            <uni-icons type="paperplane-filled" size="24" color="#ffffff"/>
                        </view>
                    </view>
                </view>
            </view>


        </view>

    </view>
</template>

<script>
import _API_ from '../../api'

export default {
    data() {
        return {
            chat_user_content: '',
            ai_loading: false,
            chat_record_list: [],
            enterTips: ['今天买了两包卫龙的辣条，一年后过期', '昨天买了一双鞋', '衣服放在卧室的衣柜里了']
        }
    },

    computed: {
        scrollIntoViewId() {
            if (this.ai_loading) return 'ai-loading'
            if (!this.chat_record_list) return ''
            return `chat-content-item-${this.chat_record_list.length - 1}`
        }
    },

    onLoad() {
        this.$uni.showLoading('加载中...')
        this.$login.uniLogin(() => uni.hideLoading())
    },

    methods: {
        chatSend() {
            if (!this.chat_user_content) return this.$uni.showToast('请输入要查询的物品')

            this.askAI()
        },

        async askAI(content = this.chat_user_content) {
            if (this.ai_loading) return

            this.chat_record_list.push({
                role: 'user',
                content
            })

            this.chat_user_content = ''

            this.ai_loading = true
            const res = await this.xwy_api.request({
                url: 'front.flat.active.chat.user.chat/system_product_result',
                data: {
                    system_product: 'ai_get_json_data',
                    used_company: 'doubao',
                    subject: content,
                    description: this.getChatPrompt(content)
                }
            })

            const item_str = res?.data?.res?.content || ''
            if (!item_str) return this.chatError('未获取到你要录入的物品')

            let item_list = []
            try {
                item_list = JSON.parse(item_str)
                if (!Array.isArray(item_list)) return this.chatError()
                item_list = item_list.filter(item => item.name)
            } catch (e) {
                console.log(e);
                return this.chatError()
            }

            if (!item_list?.length) return this.chatError('未获取到你要录入的物品')

            await this.addItem(item_list)
        },

        getChatPrompt(content) {
            const today = this._utils.getDay(0, true)
            const week = this._utils.getWeek(today)

            return `
             请根据以下文字内容提取文字中的物品名称、存放位置、品牌名称、备注说明、数量、价格、开封日期、购买日期、生产日期、过期日期(如果文字中有提到保质期，需要通过生产日期和保质期来计算过期日期)。
             
             数据格式如下:
             [
                {"name": "", "position_place": "", "brand_name": "", "memo": "", "num": 0, "amount": 0, "open_date": "", "buy_date": "", "product_date": "", "expired_date": ""}
             ]
             
             数据格式说明:
             name 物品名称 String 没有提取到返回""
             position_place 存放位置 String 没有提取到返回""
             brand_name 品牌名称 String 没有提取到返回""
             memo 备注说明 String 没有提取到返回""
             num 数量 Number 没有提取到返回0
             amount 价格 Number 保留两位小数没有提取到返回0
             open_date 开封日期  String 格式为yyyy-mm-dd没有提取到返回""
             buy_date 购买日期  String 格式为yyyy-mm-dd没有提取到返回""
             product_date 生产日期  String 格式为yyyy-mm-dd没有提取到返回""
             expired_date 过期日期  String 格式为yyyy-mm-dd没有提取到返回""
            
             注意: 没有提取到物品请返回空数组。
             
             今天的日期是${today},提取日期的时候，要以今天的日期为准。比如昨天，日期就是在今天的日期减去一天。
             今天是${week}，如果文字内容里面有周?、本周?、上周? 请换算成对应的日期。
             
             下面是需要你提取分析的文字内容：
             ${content}
             `
        },

        chatError(content = '输入有误') {
            this.ai_loading = false
            this.chat_record_list.push({
                role: 'ai',
                content
            })
        },

        isDate(str) {
            if (!str) return ''
            const reg = /^\d{4}-\d{2}-\d{2}$/
            return reg.test(str) ? str : ''
        },


        getItemSaveData(item) {
            item.num = item.num ? Number(item.num) || '' : ''
            item.amount = item.amount ? Number(item.amount) || '' : ''

            item.open_date = this.isDate(item.open_date)
            item.buy_date = this.isDate(item.buy_date)
            item.product_date = this.isDate(item.product_date)
            item.expired_date = this.isDate(item.expired_date)

            const data = {}
            const keys = _API_.getItemDetailsKeys()
            keys.forEach(key => {
                if (item[key]) data[key] = item[key]
            })

            return data
        },

        async addItem(list) {
            const item = list[0]
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.item_storage.manage/save_item_data',
                data: this.getItemSaveData(item)
            })
            this.ai_loading = false

            if (res?.status !== 1) return this.chatError(`${item.name} 录入失败`)

            this.getOpenerEventChannel?.()?.emit?.('itemListUpload')
            
            const item_id = res.data?.item_id
            if (!item_id) return this.chat_record_list.push({
                role: 'ai',
                content: `${item.name} 已录入`
            })

            this.showItemDetails2Record(item, item_id)
        },

        showItemDetails2Record(item, item_id) {
            item.item_id = item_id
            this.chat_record_list.push({
                role: 'ai',
                item_details: item
            })
        },

        toItemDetailsPage(index) {
            const item_id = this.chat_record_list[index].item_details.item_id
            this.$uni.navigateTo(`../details?item_id=${item_id}`, {
                events: {
                    itemDelete: () => this.deleteItemRecord(index),
                    itemUpdate: data => this.updateItem(index, data)
                }
            })
        },

        toEditItem(index) {
            const item = this.chat_record_list[index].item_details
            const item_id = item.item_id
            this.$uni.navigateTo(`./add?item_id=${item_id}`, {
                events: {
                    updateItem: data => this.updateItem(index, data)
                }
            })
        },

        updateItem(index, data) {
            this.getOpenerEventChannel?.()?.emit?.('itemListUpload')
            this.chat_record_list[index].item_details.name = data.name
        },

        async deleteItem(index) {
            const {name, item_id} = this.chat_record_list[index].item_details
            const confirm = await this.$uni.showModal(`确定删除物品 ${name} ？`, {showCancel: true})
            if (!confirm.confirm) return
            
            this.$uni.showLoading('删除中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.item_storage.manage/del_item_record',
                data: {
                    ids: item_id
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '删除失败')
            
            this.$uni.showToast('已删除')
            
            this.getOpenerEventChannel?.()?.emit?.('itemListUpload')
            
            this.deleteItemRecord(index)
        },

        deleteItemRecord(index) {
            this.chat_record_list.splice(index - 1, 2)
        }
    }
}
</script>

<style lang="scss">

.chat-container {
    .chat-contents {
        height: calc(100vh - 128px - 10px);

        .enter-tips {
            padding-top: 5px;

            .enter-tips-item {
                color: #5cadff;
                padding: 5px 10px;
                font-size: 14px;
            }
        }

        .chat-content-item {
            background-color: #fff;
            margin: 10px;
            padding: 10px;
            display: inline-block;
            max-width: 80vw;
            box-sizing: border-box;
            
            .item-details {
                width: calc(80vw - 20px);
            }
        }

        .chat-content-ai {
            border-radius: 0 10px 10px 10px;
        }

        .chat-content-user {
            border-radius: 10px 0 10px 10px;
            background-color: #5cadff;
            color: #fff;
        }

        .chat-record-action-bar {
            width: 80vw;
            padding-left: 10px;
            padding-bottom: 10px;
            margin-top: -5px;

            .chat-record-action-button {
                color: #7E7E7E;
                line-height: 30px;
                border: 1px solid #ECEBEB;
                border-radius: 16px;
                margin-left: 10px;
                width: 70px;
                text-align: center;
                background-color: #fff;
            }
        }
    }

    .chat-input-container {
        position: fixed;
        z-index: 99;
        left: 0;
        bottom: 0;
        width: 100vw;
        box-sizing: border-box;
        padding: 10px 10px 20px 10px;

        .chat-send {
            width: 40px;
            min-width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-left: 10px;
        }
    }
}
</style>