<template>
    <view class="page bg-background">
        <view class="block">
            <view>使用流程:</view>
            <view class="pt5 color-content">
                <view class="flex-row pt5" v-for="(item, index) in use_instructions" :key="index">
                    <view class="text-right" style="width: 20px;">{{ index + 1 }}、</view>
                    <view>{{ item }}</view>
                </view>
            </view>
        </view>

        <view class="block">
            <view>提示:</view>
            <view class="pt5 color-content">
                <view class="flex-row pt5" v-for="(item, index) in tips" :key="index">
                    <view class="text-right" style="width: 20px;">{{ index + 1 }}、</view>
                    <view>{{ item }}</view>
                </view>
            </view>
        </view>

        <view class="block flex-row">
            <view class="pr10">
                <text class="iconfont icon-excel-file"></text>
            </view>
            <view class='w-100'>
                <view class="color-title">物品导入模版</view>
                <view class="clearfix clear">
                    <view class="fr color-light-primary" @click="openTemplate">打开模版</view>
                </view>
            </view>
        </view>

        <view class="choose-file-button bg-light-primary color-white text-center"
              hover-class="navigator-hover" @click="getExcel">选择导入表格文件
        </view>
    </view>
</template>

<script>
import xlsx from "@/utils/lib/xlsx.mini.min"
import openExcelFile from "@/utils/open-excel-file"

export default {
    data() {
        return {
            use_instructions: [
                '打开下面的【物品导入模版】。',
                '打开后编辑模版文件并保存。',
                '保存后转发给微信好友。建议转发给【文件传输助手】。',
                '在本页面点击【选择导入表格文件】,选择编辑好的模版文件导入即可(使用手机微信导入)。'
            ],
            tips: [
                '模版文件会不定期更新，请务必每次导入时按照【使用流程】逐步进行',
                '单次最多导入100个物品',
                '导入表格文件只能使用手机微信小程序导入，电脑端不支持',
                '建议打开【物品导入模版】后转发到【文件传输助手】，使用电脑编辑。或直接使用电脑微信小程序打开【物品导入模版】'
            ],
            isPc: false
        }
    },

    onLoad() {
        const osName = (uni.getSystemInfoSync().osName || '').toLowerCase()
        if (osName === 'windows' || osName === 'mac') this.isPc = true
    },

    methods: {
        getSubmitKeys() {
            return [
                {key: 'name', title: '物品名称 必填'},
                {key: 'position_place', title: '存放位置说明'},
                {key: 'brand_name', title: '品牌名称'},
                {key: 'amount', title: '单价'},
                {key: 'num', title: '数量'},
                {key: 'left_num', title: '剩余数量'},
                {key: 'buy_date', title: '购买日期'},
                {key: 'product_date', title: '生产日期'},
                {key: 'expired_date', title: '到期日期'},
                {key: 'open_date', title: '开封日期'},
                {key: 'memo', title: '备注说明'},
                {key: 'sort_num', title: '排序'}
            ]
        },


        openTemplate() {
            const tableData = this.excelDataProcessing()
            openExcelFile.openDocument(tableData, '物品导入模版')
        },

        excelDataProcessing() {
            const keys = this.getSubmitKeys()

            const tHead = keys.map(item => item.title)

            const tBody = [
                '牛奶 (示例)',
                '家里',
                '蒙牛',
                '3',
                '5',
                '4',
                '2024-09-13',
                '2024-08-13',
                '2025-08-13',
                '2024-09-13',
                '买了5盒，还剩4盒放在家里',
                '1'
            ]
            return [tHead, tBody]
        },

        async getExcel() {
            if (this.isPc) return this.pcGetExcelFile()

            const path = await this.getExcelPath()
            if (!path) return

            const fs = wx.getFileSystemManager()
            const data = fs.readFileSync(path, 'binary') // data为binary数据

            let workerBox = xlsx.read(data, {type: "binary"})
            let rawData = []
            const options = {raw: false}
            const sheetNames = workerBox.SheetNames || []
            sheetNames.forEach(sheetName => {
                {
                    let sheet = workerBox.Sheets[sheetName]
                    rawData = rawData.concat(xlsx.utils.sheet_to_json(sheet, options))
                }
            })

            this.excel2ItemList(rawData)
        },

        pcGetExcelFile() {
            this.$uni.showToast('请使用手机微信操作')
            /*this.$uni.navigateTo('/pages/other/webview?url=https://prod-0g479j60184f120d-1304148175.tcloudbaseapp.com/web/wx-cloud-api/weijie-test/choose-excel-file-web-view', {
                events: {
                    getMessage: data => {
                        this.excel2ItemList(data)
                    }
                }
            })*/
        },

        excel2ItemList(rawData) {
            const keys = this.getSubmitKeys()

            const list = rawData.map(v => {
                const item = {}
                keys.forEach(key => {
                    item[key.key] = v[key.title] || ''
                })
                return item
            })

            const item_list = list.filter(item => item.name).reverse()

            if (!item_list.length) return this.$uni.showModal('没有提取到物品，请重新选择文件')

            this.addItems(item_list)
        },

        async addItems(item_list) {
            let count = item_list.length

            if (count > 100) {
                const res = await this.$uni.showModal(`提取到${count}物品。本次只导入前100件物品，是否导入？`, {
                    showCancel: true,
                    confirmText: '导入'
                })

                if (!res?.confirm) return

                item_list = item_list.slice(0, 100)
                count = item_list.length
            }

            for (let i = 0, len = count; i < len; i++) {
                this.$uni.showLoading(`${i + 1}/${count} 导入中...`)
                await this.addItem(item_list[i])
            }
            uni.hideLoading()
            this.$uni.showToast('导入完成', 'success')

            this.itemListUpload()
        },

        async addItem(item) {
            await this.xwy_api.request({
                url: 'front.flat.sport_step.item_storage.manage/save_item_data',
                data: item
            })
        },

        itemListUpload() {
            this.getOpenerEventChannel?.()?.emit?.('itemListUpload')
        },

        /**
         * @return Promise
         * */
        getExcelPath() {
            return new Promise(resolve => {
                wx.chooseMessageFile({
                    count: 1,
                    type: 'file',
                    extension: ['xlsx'],
                    success: res => resolve(res?.tempFiles?.[0]?.path || null),
                    fail: () => resolve(null)
                })
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-top: 1px;
}

.block {
    margin: 10px;
    padding: 10px;
    border-radius: 5px;
    background-color: #fff;
}

.icon-excel-file {
    font-size: 40px;
    color: #63c187;
}

.choose-file-button {
    margin: 30px auto;
    width: 250px;
    line-height: 44px;
    border-radius: 5px;
}
</style>