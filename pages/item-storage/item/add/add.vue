<template>
    <view class="page bg-background">
        <view class="form">
            <view class="form-item flex-kai">
                <view class="label">
                    <text>物品名称</text>
                    <text class="required">*</text>
                </view>
                <view class="form-value form-value-input">
                    <input class="form-input text-right" v-model="name" type="text" maxlength="50"
                           placeholder="必填, 50字以内"/>
                </view>
            </view>
        </view>

        <view class="form">
            <view class="form-item flex-kai">
                <view class="label">位置说明</view>
                <view class="form-value-input">
                    <input class="form-input text-right" v-model="position_place" type="text" maxlength="50"
                           placeholder="50字以内"/>
                </view>
            </view>
        </view>

        <view v-if="!item_id" class="form" @click="formMoreShowChange">
            <view class="form-item flex-kai">
                <view class="label">录入更多信息</view>
                <view class="form-value">
                    <text>{{ form_more_show ? '收起' : '展开' }}</text>
                    <uni-icons type="forward"/>
                </view>
            </view>
        </view>

        <view v-show="form_more_show">
            <view class="mb10 p10 bg-white" @click="$refs.spaceChoose.open()">
                <view class="flex-kai">
                    <view>存放位置</view>
                    <view>
                        <text class="color-light-primary">修改</text>
                        <uni-icons type="forward" size="16" color="#5cadff"/>
                    </view>
                </view>
                <view class="pt10">
                    <view v-if="position_category_id" class="pt10 color-content">
                        <space-path :space-id="position_category_id" icon-gap="5px"/>
                    </view>
                    <view v-else class="color-sub">未指定</view>
                </view>
            </view>

            <space-choose ref="spaceChoose" :pid="position_category_id" @confirm="spaceChange"/>
            
            <view class="form">
                <view class="form-item flex-kai" @click="$refs.categoryChoosePopup.open()">
                    <view class="label">物品分类</view>
                    <view class="form-value flex-row">
                        <template v-if="item_category_id">
                            <text>{{ category_name || item_category_id }}</text>
                            <view class="flex-all-center" style="width: 40px;" @click.stop="clearCategory">
                                <uni-icons type="clear" color="#bbbec4" class="pl5"/>
                            </view>
                        </template>

                        <template v-else>
                            <text class="color-sub">未指定</text>
                            <uni-icons type="forward" size="16" color="#80848f"/>
                        </template>
                    </view>
                </view>
            </view>

            <view class="form">
                <view class="form-item flex-kai">
                    <view class="label">购买数量</view>
                    <view class="form-value form-value-input">
                        <input class="form-input text-right" v-model="num" type="digit" maxlength="10"
                               placeholder="请输入购买数量"/>
                    </view>
                </view>

                <view class="form-item flex-kai">
                    <view class="label">剩余数量</view>
                    <view class="form-value form-value-input">
                        <input class="form-input text-right" v-model="left_num" type="digit" maxlength="10"
                               placeholder="请输入剩余数量"/>
                    </view>
                </view>

                <view class="form-item flex-kai">
                    <view class="label">
                        <text>物品单价</text>
                        <text class="color-sub font12" style="padding-left: 2px;">(元)</text>
                    </view>
                    <view class="form-value form-value-input">
                        <input class="form-input text-right" v-model="amount" type="digit" maxlength="10"
                               placeholder="请输入物品单价"/>
                    </view>
                </view>

                <view class="form-item flex-kai">
                    <view class="label">品牌名称</view>
                    <view class="form-value form-value-input">
                        <input class="form-input text-right" v-model="brand_name" type="text" maxlength="20"
                               placeholder="请输入物品品牌名称"/>
                    </view>
                </view>
            </view>


            <view class="form">
                <view class="form-item">
                    <uni-datetime-picker type="date" v-model="buy_date">
                        <view class="flex-kai">
                            <view class="label">购买日期</view>
                            <view class="form-value">
                                <template>
                                    <text v-if="buy_date">{{ buy_date }}</text>
                                    <text v-else class="color-sub">无</text>
                                </template>
                                <uni-icons type="forward" size="16" color="#80848f"/>
                            </view>
                        </view>
                    </uni-datetime-picker>
                </view>


                <view class="form-item">
                    <uni-datetime-picker type="date" v-model="product_date" @change="productDateChange">
                        <view class="flex-kai">
                            <view class="label">生产日期</view>
                            <view class="form-value">
                                <template>
                                    <text v-if="product_date">{{ product_date }}</text>
                                    <text v-else class="color-sub">无</text>
                                </template>
                                <uni-icons type="forward" size="16" color="#80848f"/>
                            </view>
                        </view>
                    </uni-datetime-picker>
                </view>

                <view v-if="product_date" class="form-item flex-kai">
                    <view class="label">
                        <text>有效期</text>
                        <text class="color-sub font12" style="padding-left: 2px;">(天)</text>
                    </view>
                    <view class="form-value-input">
                        <input class="form-input text-right" v-model="validity" type="number" maxlength="8"
                               placeholder="请输入有效期或选择过期时间" @blur="enterValidity"/>
                    </view>
                </view>

                <view class="form-item">
                    <uni-datetime-picker type="date" v-model="expired_date" @change="expiredDateChange">
                        <view class="flex-kai">
                            <view class="label">过期时间</view>
                            <view class="form-value">
                                <template>
                                    <text v-if="expired_date">{{ expired_date }}</text>
                                    <text v-else class="color-sub">无</text>
                                </template>
                                <uni-icons type="forward" size="16" color="#80848f"/>
                            </view>
                        </view>
                    </uni-datetime-picker>
                </view>


                <view class="form-item">
                    <uni-datetime-picker type="date" v-model="open_date">
                        <view class="flex-kai">
                            <view class="label">开封日期</view>
                            <view class="form-value">
                                <template>
                                    <text v-if="open_date">{{ open_date }}</text>
                                    <text v-else class="color-sub">无</text>
                                </template>
                                <uni-icons type="forward" size="16" color="#80848f"/>
                            </view>
                        </view>
                    </uni-datetime-picker>
                </view>
            </view>

            <view class="form">
                <view class="form-item pb10">
                    <view class="label-line">备注说明</view>
                    <uni-easyinput type="textarea" v-model="memo" :maxlength="1000"
                                   placeholder="请输入备注说明 1000字内"/>
                </view>
            </view>

            <view class="form">
                <view class="form-item flex-kai">
                    <view class="label">物品排序</view>
                    <view class="form-value-input">
                        <input class="form-input text-right" v-model="sort_num" type="number" maxlength="8"
                               placeholder="数字越小排序越靠前"/>
                    </view>
                </view>
            </view>

            <view class="p10 bg-white">
                <view>物品图片</view>
                <view class="pt10">
                    <view v-if="logo" class="logo-container">

                        <image class="logo-image" :src="logo" mode="aspectFill" @click="previewImage(logo)"/>
                        <view class="logo-clear flex-all-center" @click="logo = ''">
                            <uni-icons type="closeempty" size="16" color="#e20f04"/>
                        </view>
                    </view>
                    <view v-else class="no-logo flex-all-center bg-background" @click="logoChange">
                        <uni-icons type="plusempty" size="60" color="#dddee1"/>
                    </view>
                </view>
            </view>
        </view>

        <view class="save-container bg-white">
            <view class="save-button bg-light-primary color-white text-center" hover-class="navigator-hover"
                  @click="verify">保存
            </view>
        </view>

        <category-choose-popup ref="categoryChoosePopup" :category-id="item_category_id"
                               @clickItem="categoryChooseConfirm"/>
    </view>
</template>

<script>
import spaceChoose from "../../components/space-choose.vue"
import _API_ from "../../api"
import spacePath from "../../components/space-path.vue"
import categoryChoosePopup from "../../components/category-choose-popup.vue"

export default {
    components: {spaceChoose, spacePath, categoryChoosePopup},
    data() {
        return {
            item_id: null,
            form_more_show: false,
            name: '',
            logo: '',
            position_place: '',
            item_category_id: null,
            position_category_id: null,
            buy_date: this._utils.getDay(0, true),
            product_date: '',
            expired_date: '',
            open_date: '',
            num: '',
            left_num: '',
            amount: '',
            brand_name: '',
            memo: '',
            sort_num: '',


            space_path: [],
            category_name: '',
            validity: ''
        }
    },


    // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
    /** @namespace params.scan_code */
    onLoad(params) {
        this.setNavigationBarTitle(params)
        
        if (params.copy) this.copy_item = true
        
        if (params.space_id) {
            this.position_category_id = Number(params.space_id)
            this.getSpacePath()
        }
        
        if (params.item_id) {
            this.item_id = params.item_id
            this.getDetails()
        }
        
        if (params.scan_code) this.getScanCodeDetails()
        
        this.getFormMoreShowSet(params)
    },

    methods: {
        setNavigationBarTitle(params) {
            let title = '添加物品'
            if (params.item_id) title = '修改物品'
            if (params.copy) title = '复制物品'
            this.$uni.setNavigationBarTitle(title)
        },
        
        getFormMoreShowSet(params) {
            if (params.item_id || params.scan_code) {
                this.form_more_show = true
                return
            }
            this.form_more_show = uni.getStorageSync('item-storage-form-more-show') || false
        },
        
        formMoreShowChange() {
            const show = !this.form_more_show
            this.form_more_show = show
            const storage_key = 'item-storage-form-more-show'
            show ? uni.setStorageSync(storage_key, show) : uni.removeStorageSync(storage_key)
        },
        
        
        async getSpacePath() {
            this.space_path = await _API_.getSpacePath(this.position_category_id)
        },

        getScanCodeDetails() {
            this.getOpenerEventChannel().on('scanCodeDetails', details => {
                const keys = ['name', 'logo', 'amount', 'brand_name']
                keys.forEach(key => {
                    if (details[key]) this[key] = details[key]
                })
            })
        },

        async getDetails() {
            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.item_storage.manage/item_goods_details',
                data: {
                    item_id: this.item_id
                }
            })
            uni.hideLoading()

            const details = res?.data?.details

            if (!details) {
                return this.$uni.showModal(res?.status === 1 ? '物品不存在' : res?.info || '物品不存在')
            }


            this.initItemDetailsData(details)
        },

        initItemDetailsData(details) {
            _API_.getItemDetailsKeys().forEach(key => {
                if (details[key]) this[key] = details[key]
            })

            if (details?.item_cate_details?.name) this.category_name = details.item_cate_details.name

            if (this.position_category_id) this.getSpacePath()
            if (this.product_date && this.expired_date) this.validity = this.getValidity()
        },

        logoChange() {
            this.$uni.navigateTo('/pages/other/image_upload_or_select', {
                events: {
                    newImg: src => this.logo = src
                }
            })
        },

        previewImage(options) {
            this.$uni.previewImage(options)
        },

        categoryChooseConfirm(category) {
            this.item_category_id = category.category_id
            this.category_name = category.name
        },

        clearCategory() {
            this.item_category_id = ''
            this.category_name = ''
        },

        spaceChange(id) {
            this.position_category_id = id || null
        },

        enterValidity() {
            if (!this.product_date) return

            this.$nextTick(() => {
                if (this.validity === '') return

                const validity = Math.floor(this.validity)
                if (isNaN(validity) || validity < 0) {
                    this.validity = ''
                    return this.$uni.showToast('有效期请输入正整数')
                }

                const productTime = new Date(this.product_date.replace(/-/g, '/')).getTime()
                const validityTime = validity * 24 * 60 * 60 * 1000
                const expiredTime = productTime + validityTime
                this.expired_date = this._utils.timestampToTime(expiredTime, true).split(' ')[0]
            })
        },

        getValidity() {
            if (!this.product_date || !this.expired_date) return ''
            const expiredTime = new Date(this.expired_date.replace(/-/g, '/')).getTime()
            const productTime = new Date(this.product_date.replace(/-/g, '/')).getTime()
            const validityTime = expiredTime - productTime
            return Math.floor(validityTime / (24 * 60 * 60 * 1000))
        },

        productDateChange() {
            const validity = this.getValidity()
            if (validity < 0) this.$uni.showToast('生产日期不能大于过期日期，请重新选择')
            this.validity = validity
        },

        expiredDateChange() {
            this.$nextTick(() => {
                const validity = this.getValidity()
                if (validity < 0) this.$uni.showToast('过期时间不能小于生产日期，请重新选择')
                this.validity = validity
            })
        },

        verify() {
            if (!this.name) return this.$uni.showToast('请输入物品名称')
            if (this.expired_date && this.product_date) {
                const expiredTime = new Date(this.expired_date.replace(/-/g, '/')).getTime()
                const productTime = new Date(this.product_date.replace(/-/g, '/')).getTime()
                if (expiredTime < productTime) return this.$uni.showToast('过期时间不能小于生产日期，请重新选择')
            }

            if (this.num) {
                const num = Number(this.num)
                if (isNaN(num)) return this.$uni.showToast('请输入正确的购买数量')
                this.num = num
            }

            if (this.left_num) {
                const left_num = Number(this.left_num)
                if (isNaN(left_num)) return this.$uni.showToast('请输入正确的剩余数量')
                this.left_num = left_num
            }

            if (this.amount) {
                const amount = Number(this.amount)
                if (isNaN(amount)) return this.$uni.showToast('请输入正确的物品单价')
                this.amount = amount
            }

            this.save()
        },

        getSubmitData() {
            const data = {}
            const keys = _API_.getItemDetailsKeys()
            if (this.item_id && !this.copy_item) keys.push('item_id')
            keys.forEach(key => {
                data[key] = this[key] || ''
            })
            return data
        },

        async save() {
            const data = this.getSubmitData()

            this.$uni.showLoading(this.copy_item ? '复制中...' : '保存中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.item_storage.manage/save_item_data',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '保存失败', {title: '保存失败'})

            this.$uni.showToast(this.copy_item ? '已复制' : '已保存')
            this.getOpenerEventChannel?.()?.emit?.('itemListUpload')
            this.getOpenerEventChannel?.()?.emit?.('updateItem', data)
            this.$uni.navigateBack(this.copy_item ? 2 : 1, {delay: 1000})
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    padding-bottom: 100px;
    box-sizing: border-box;
}

.form {
    margin-bottom: 10px;
    padding: 0 10px;
    background-color: #fff;

    .form-item {
        $size: 50px;

        border-bottom: 1px solid #eee;
        line-height: $size;

        &:last-child {
            border-bottom: none;
        }

        .label {
            width: 100px;
            min-width: 100px;

            .required {
                color: #e20f04;
                font-size: 14px;
                position: relative;
                top: -4px;
                left: 2px;
            }
        }

        .form-value-input {
            width: 100%;

            .form-input {
                height: $size;
                line-height: $size;
            }
        }
    }
}

.logo-container, .logo-image, .no-logo {
    width: 120px;
    height: 120px;
    border-radius: 5px;
}

.logo-container {
    position: relative;

    .logo-clear {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, .7);
        position: absolute;
        top: 2px;
        right: 2px;
    }
}

.save-container {
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    width: 100%;
    box-sizing: border-box;
    padding: 10px 10px 15px;

    .save-button {
        line-height: 44px;
        border-radius: 22px;
    }
}
</style>