<template>
    <view class="page bg-background">
        <view class="form bg-white">
            <view class="form-item flex-kai">
                <view class="label">
                    <text>位置名称</text>
                    <text class="required">*</text>
                </view>
                <view class="form-value">
                    <input class="form-input text-right" v-model="name" type="text" maxlength="10" 
                           placeholder="必填, 10字以内"/>
                </view>
            </view>

            <view class="form-item flex-kai">
                <view class="label">位置排序</view>
                <view class="form-value">
                    <input class="form-input text-right" v-model="sort_num" type="number" maxlength="8"
                           placeholder="数字越小排序越靠前"/>
                </view>
            </view>
        </view>
        
        <view class="mb10 p10 bg-white" @click="$refs.spaceChoose.open()">
            <view class="flex-kai">
                <view>存放位置</view>
                <view>
                    <text class="color-light-primary">修改</text>
                    <uni-icons type="forward" size="16" color="#5cadff"/>
                </view>
            </view>
            <view class="pt10 color-content">
                <space-path v-if="pid" :space-id="pid" icon-gap="5px"/>
                <view v-else>未指定</view>
            </view>
        </view>

        <space-choose ref="spaceChoose" :pid="pid" :edit-space="true" :my-id="id" @confirm="spaceChange"/>
        
        <view class="p10 bg-white">
            <view>位置图片</view>
            <view class="pt10">
                <view v-if="logo" class="logo-container">

                    <image class="logo-image" :src="logo" mode="aspectFill" @click="previewImage(logo)"/>
                    <view class="logo-clear flex-all-center" @click="logo = ''">
                        <uni-icons type="closeempty" size="16" color="#e20f04"/>
                    </view>
                </view>
                <view v-else class="no-logo flex-all-center bg-background" @click="logoChange">
                    <uni-icons type="plusempty" size="60" color="#dddee1"/>
                </view>
            </view>
        </view>
        
        <view class="save-container bg-white">
            <view class="save-button bg-light-primary color-white text-center" hover-class="navigator-hover"
                  @click="save">保存
            </view>
        </view>
    </view>
</template>

<script>
import _API_ from '../api'
import spaceChoose from '../components/space-choose.vue'
import spacePath from "../components/space-path.vue"

export default {
    components: {spaceChoose, spacePath},
    data() {
        return {
            id: null,
            pid: null,
            name: '',
            logo: '',
            sort_num: '',
            space_path: []
        }
    },

    onLoad(params) {
        if (params.pid) {
            this.pid = Number(params.pid)
            this.getSpacePath()
        }
        if (params.id) {
            this.$uni.setNavigationBarTitle('修改位置')
            this.id = params.id
            this.getDetails()
        }
    },

    methods: {
        async getSpacePath() {
            this.space_path = await _API_.getSpacePath(this.pid)
        },
        
        async getDetails() {
            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.user.category/category_details',
                data: {
                    category_id: this.id
                }
            })
            uni.hideLoading()

            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace res.data.category_details */
            
            const details = res?.data?.category_details
            
            if (!details) return this.$uni.showModal('位置不存在', {success: () => this.$uni.navigateBack()})
            
            this.name = details.name
            if (details.logo) this.logo = details.logo
            if (details.sort_num) this.sort_num = details.sort_num
            if (details.pid) {
                this.pid = details.pid
                await this.getSpacePath()
            }
        },


        logoChange() {
            this.$uni.navigateTo('/pages/other/image_upload_or_select', {
                events: {
                    newImg: src => this.logo = src
                }
            })
        },

        previewImage(options) {
            this.$uni.previewImage(options)
        },

        spaceChange(id) {
            this.pid = id || null
        },
        
        async save() {
            if (!this.name || this.name.replace(/\s+/g, '') === '') return this.$uni.showToast('请填写位置名称')
            if (this.sort_num && isNaN(this.sort_num)) return this.$uni.showToast('排序必须为数字')
            
            const data = {
                name: this.name,
                types: 33,
                pid: this.pid || 0,
                logo: this.logo || '',
                sort_num: this.sort_num || ''
            }
            if (this.id) data.id = this.id
            
            this.$uni.showLoading('保存中...')
            const res = await this.xwy_api.request({
                url: 'front.user.category/create_category',
                data
            })
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '保存失败', {title: '保存失败'})
            
            this.$uni.showToast('保存成功', 'success')
            this.getOpenerEventChannel?.()?.emit?.('spaceUpload')
            this.$uni.navigateBack(1, {delay: 1000})
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
}

.form {
    margin-bottom: 10px;
    padding: 0 10px;
    
    .form-item {
        $size: 50px;
        
        border-bottom: 1px solid #eee;
        line-height: $size;
        
        &:last-child {
            border-bottom: none;
        }
        
        .label {
            width: 80px;
            min-width: 80px;
            
            .required {
                color: #e20f04;
                font-size: 14px;
                position: relative;
                top: -4px;
                left: 2px;
            }
        }

        .form-value {
            width: 100%;
            
            .form-input {
                height: $size;
                line-height: $size;
            }
        }
    }
}

.logo-container, .logo-image, .no-logo {
    width: 120px;
    height: 120px;
    border-radius: 5px;
}

.logo-container {
    position: relative;
    
    .logo-clear {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, .7);
        position: absolute;
        top: 2px;
        right: 2px;
    }
}

.save-container {
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    width: 100%;
    box-sizing: border-box;
    padding: 10px 10px 15px;
    
    .save-button {
        line-height: 44px;
        border-radius: 22px;
    }
}
</style>