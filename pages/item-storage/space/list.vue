<template>
    <view class="page bg-background">

        <template v-if="!pid">
            <view class="clear clearfix p10">
                <view class="system-space-set flex-all-center fr bg-white"
                      @click="$refs.systemSpaceSet.open()">
                    <uni-icons type="gear" size="28" color="#80848f"/>
                </view>
            </view>

            <system-space-set ref="systemSpaceSet" @confirm="getSpaceList(true)"/>
        </template>
        
        <template v-if="space">
            <view class="space-details p10 flex-row bg-white">
                <template>
                    <view v-if="space.logo">
                        <image class="space-logo" :src="space.logo" mode="aspectFill"/>
                    </view>
                    <view v-else class="space-no-logo flex-all-center bg-background">
                        <uni-icons type="image" size="34" color="#bbbec4"/>
                    </view>
                </template>
                <view class="space-info pl10">
                    <view>{{ space.name }}</view>

                    <view class="flex-kai">
                        <view class="color-sub font14">
                            <template v-if="!systemSpace">{{ space_list.length }}个子位置, </template>
                            {{ item_count }}件物品
                        </view>

                        <view v-if="!systemSpace" class="flex-row">
                            <view class="space-edit-icon pl10 pr10" @click="toEditSpace">
                                <text class="iconfont icon-edit color-sub"></text>
                            </view>
                            <view class="space-edit-icon pl10 pr10" @click="deleteSpace">
                                <text class="iconfont icon-delete color-sub"></text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <view v-if="space_path.length" class="p10 flex-row font14 color-sub bg-white">
                <view class="pr5" style="white-space: nowrap;">当前位置:</view>
                <space-path :space-id="pid" seze="14px" icon-color="#bbbec4"/>
            </view>

            <view class="look-type-bar bg-white clearfix clear">
                <view v-if="!systemSpace" class="fl flex-row">
                    <view class="look-type-item" :class="{'look-type-active': look_type === item.type}"
                          v-for="item in look_type_options" :key="item.type" 
                          @click="lookTypeChange(item.type)">{{ item.title }}
                    </view>
                </view>
                
                <view class="fr" v-show="look_type === 'item' && item_list.length">
                    <view v-show="itemListShowType === 'list'" class="flex-all-center pl10 pr10"
                          @click="itemListShowType = 'grid'">
                        <text class="iconfont icon-grid font18 color-sub"></text>
                    </view>
                    <view v-show="itemListShowType === 'grid'" class="flex-all-center pl10 pr10"
                          @click="itemListShowType = 'list'">
                        <uni-icons type="list" size="18" color="#80848f"/>
                    </view>
                </view>
            </view>
        </template>


        <view v-show="look_type === 'space'">
            <view v-if="space_list.length" class="space-list flex-row flex-wrap">
                <view class="space-item bg-white flex-all-center"
                      hover-class="navigator-hover" @click="toAddSpace">
                    <view class="text-center">
                        <uni-icons type="plusempty" color="#bbbec4" size="34"/>
                        <view class="font12 color-sub">添加位置</view>
                    </view>
                </view>

                <view class="space-item bg-white" v-for="item in space_list" :key="item.id"
                      hover-class="navigator-hover" @click="lookSpace(item.id)">
                    <view class="logo">
                        <image class="logo-image" v-if="item.logo" :src="item.logo" mode="aspectFill"/>
                        <view v-else class="no-logo flex-all-center">
                            <uni-icons type="image" size="40" color="#bbbec4"/>
                        </view>
                    </view>
                    <view class="space-name color-white font14 text-center ellipsis">{{ item.name }}</view>
                </view>
            </view>

            <view v-if="!init_loading && !space_list.length" class="text-center" style="padding-top: 15vh;">
                <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
                <view class="color-sub font14">暂无储物位置</view>
                <view class="add-space-button bg-light-primary color-white" hover-class="navigator-hover"
                      @click="toAddSpace">添加储物位置
                </view>
            </view>
        </view>


        <view v-show="look_type === 'item'">
            <view class="item-list">
                <item-list :list="item_list" :show-type="itemListShowType" @itemClick="lookItem"/>
            </view>


            <view v-if="item_list_loading" class="text-center">
                <view v-if="current_page === 1" style="width: 100%; height: 20vh;"></view>
                <load-ani/>
            </view>

            <view v-if="!init_loading && !item_list_loading && !item_list.length" class="text-center"
                  style="padding-top: 15vh;">
                <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
                <view class="color-sub font14">该位置下暂无物品</view>
                <view class="add-space-button bg-light-primary color-white" hover-class="navigator-hover"
                      @click="toAddItem">添加物品
                </view>
            </view>
        </view>


        <view v-if="init_loading" class="text-center">
            <view style="width: 100%; height: 30vh;"></view>
            <load-ani/>
        </view>

        <view class="add-icon flex-all-center bg-light-primary" hover-class="navigator-hover" @click="toAdd">
            <uni-icons type="plusempty" color="#ffffff" size="24"/>
        </view>
    </view>
</template>

<script>
import _API_ from '../api'
import itemList from "../components/item/item-list.vue"
import spacePath from "../components/space-path.vue"
import systemSpaceSet from "../components/system-space-set.vue"

export default {
    components: {itemList, spacePath, systemSpaceSet},
    data() {
        return {
            init_loading: true,
            look_type_options: [
                {title: '子位置', type: 'space'},
                {title: '物品清单', type: 'item'}
            ],
            look_type: 'space',
            space_list: [],
            pid: null,
            space: null,
            space_path: [],
            item_list_loading: false,
            current_page: 1,
            item_count: 0,
            item_list: [],
            is_last_page: false,
            itemListShowType: 'list'
        }
    },
    
    computed: {
        systemSpace() {
            return this.pid < 0
        },
    },

    onLoad(params) {
        /*uni.scanCode({
            scanType: ['barCode'],
            success: (res) => {
                console.log(res);
            }
        })*/
        if (params.pid) {
            const pid = Number(params.pid)
            this.pid = pid
            if (pid < 0) this.look_type = 'item'
            
        }
        this.$login.uniLogin(() => {
            this.init()
        })
    },

    onReachBottom() {
        if (this.look_type !== 'item' || this.init_loading || this.item_list_loading || this.is_last_page) return

        this.current_page++
        this.getItemList()
    },

    methods: {
        async init() {
            await this.getSpaceList()
            this.init_loading = false
            if (this.pid) await this.getItemList()

            if (!this.space_list.length && this.item_list.length) this.lookTypeChange('item')
        },

        lookTypeChange(type) {
            if (type === this.look_type) return
            this.look_type = type
        },

        async getSpaceList(reload = false) {
            const list = await _API_.getSpaceListNotLimit(reload)
            const system_space = await _API_.getSystemSpace(reload)
            const space_list = [...system_space, ...list].map(item => {
                return {
                    id: item.id,
                    pid: item.pid,
                    name: item.name,
                    logo: item.logo
                }
            })

            if (this.pid) {
                this.space = space_list.find(item => item.id === this.pid)
                this.space_list = space_list.filter(item => item.pid === this.pid)
                this.space_path = await _API_.getSpacePath(this.pid)
            } else {
                this.space_list = space_list.filter(item => item.pid === 0)
            }
        },

        async initSpaceList(list) {
            list = list.map(item => {
                return {
                    id: item.id,
                    pid: item.pid,
                    name: item.name,
                    logo: item.logo
                }
            })

            const system_space = await _API_.getSystemSpace()

            if (this.pid) {
                this.space = list.find(item => item.id === this.pid)
                this.space ||= system_space.find(item => item.id === this.pid)
                this.space_list = list.filter(item => item.pid === this.pid)
                this.space_path = await _API_.getSpacePath(this.pid)
            } else {
                const space_list = list.filter(item => item.pid === 0)
                this.space_list = [...system_space, ...space_list]
            }
        },

        itemListReload() {
            this.current_page = 1
            this.getItemList()
        },


        async getItemList() {
            if (this.current_page === 1) {
                this.item_list = []
                this.item_count = 0
                this.is_last_page = false
            }

            this.item_list_loading = true
            const res = await _API_.getItemList({
                page: this.current_page,
                position_category_id: this.pid
            })
            this.item_list_loading = false

            const res_data = res?.data?.list
            if (!res_data) {
                this.is_last_page = true
                return
            }

            const list = this.initItemListData(res_data.data || [])
            this.item_list = [...this.item_list, ...list]
            this.is_last_page = res_data.is_lastpage
            this.item_count = res_data.total
        },
        
        initItemListData(list) {
            return _API_.initItemListData(list)
        },
        
        lookSpace(id) {
            this.$uni.navigateTo(`./list?pid=${id}`, {
                events: {
                    spaceUpload: () => this.getSpaceList()
                }
            })
        },


        toAdd() {
            // 最后需要做成怎样的还需要发到群里让大家讨论

            // 1、当前查看子位置，点击添加就是添加子位置。当前查看物品清单，点击添加就是添加物品。（推荐）
            // 但是会有一个问题，如果我当前在看子位置，我想添加物品，还要切换到物品清单才能再点添加

            // 2、点击添加按钮，不管当前是查看的是子位置还是物品清单，都弹窗让用户选择添加子位置还是物品。
            // 这里也会有问题。比如，我当前在查看物品清单，看到这个添加按钮，一般人都会认为是直接去添加物品的。为什么又让我选择添加子位置还是物品。

            // 3、使用第2个，但是只弹一次窗让用户选择，并记住用户的选择，下次不再弹窗。比如第一次选择的是子位置，那以后点添加都是添加子位置，就是当前是查看物品列表也是直接去添加子位置。（极不推荐）
            // 这种方法的话问题就比较多了（默认第一次弹窗选择了 添加子位置）。
            // 1.如果我想修改为添加物品的话，要去哪里给用户设置。
            // 2.会给用户造成影响。比如我在查看物品清单，点击添加，直接去到添加子位置的页面，但是用户是用物品清单进来的，用户会以为自己是在添加物品。


            const options = {
                'space': () => this.toAddSpace(),
                'item': () => this.toAddItem()
            }

            options[this.look_type]?.()
        },


        toAddSpace() {
            let url = './add'
            if (this.pid) url += `?pid=${this.pid}`
            this.toAddOrEdit(url)
        },

        toEditSpace() {
            this.toAddOrEdit(`./add?id=${this.pid}`)
        },

        toAddOrEdit(url) {
            this.$uni.navigateTo(url, {
                events: {
                    spaceUpload: async () => {
                        await this.getSpaceList(true)
                        this.uploadPreviousSpace()
                    }
                }
            })
        },

        async deleteSpace() {
            const confirm = await this.$uni.showModal('确定删除该位置', {showCancel: true})
            if (!confirm.confirm) return

            this.$uni.showLoading('删除中...')
            const res = await this.xwy_api.request({
                url: 'front.user.category/category_del',
                data: {
                    ids: this.pid
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '删除失败', {title: '删除失败'})

            this.$uni.showToast('删除成功', 'success')
            await this.getSpaceList(true)
            this.uploadPreviousSpace()
            this.$uni.navigateBack(1, {delay: 1000})
        },

        toAddItem() {
            this.$uni.navigateTo(`../item/add/add?space_id=${this.pid}`, {
                events: {
                    itemListUpload: () => this.itemListReload()
                }
            })
        },

        uploadPreviousSpace() {
            this.getOpenerEventChannel?.()?.emit?.('spaceUpload')
        },

        lookItem(item_id) {
            this.$uni.navigateTo(`../item/details?item_id=${item_id}`, {
                events: {
                    itemUpdate: () => this.itemListReload(),
                    reloadItemList: () => this.itemListReload()
                }
            })
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    padding-bottom: 150px;
    overflow: hidden;
}

.system-space-set {
    width: 40px;
    height: 40px;
    border-radius: 5px;
}

.add-space-button {
    margin: 20px auto;
    width: 200px;
    line-height: 40px;
    border-radius: 20px;
}

.space-details {
    $logo-size: 60px;

    .space-logo, .space-no-logo {
        width: $logo-size;
        min-width: $logo-size;
        height: $logo-size;
        border-radius: 5px;
    }

    .space-logo {
        display: block;
    }

    .space-info {
        width: 100%;
        line-height: calc(#{$logo-size} / 2);
    }
}

.look-type-bar {
    padding: 0 10px;

    .look-type-item {
        height: 40px;
        line-height: 38px;
        box-sizing: border-box;
        padding: 0 10px;
        color: #666;
    }

    .look-type-active {
        color: #5cadff;
        border-bottom: 2px solid #5cadff;
    }
}

.space-list {
    $padding: 12rpx;
    $size: 218rpx;

    padding: $padding;

    .space-item, .logo-image, .no-logo {
        width: $size;
        height: $size;
    }

    .space-item {
        margin: $padding;
        border-radius: 5px;
        overflow: hidden;
        position: relative;

        .logo {
            .logo-image {
                display: block;
            }

            .no-logo {
                padding-bottom: 30px;
                box-sizing: border-box;

                .iconfont {
                    font-size: 50px;
                }
            }
        }

        .space-name {
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            background-color: rgba(0, 0, 0, .4);
            line-height: 30px;
        }
    }
}

.add-icon {
    position: fixed;
    right: 10px;
    bottom: 100px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
}
</style>