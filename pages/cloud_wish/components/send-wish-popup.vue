<template>
	<view>
		<uni-popup
			ref="popup"
			type="center"
			mask-background-color="rgba(0,0,0,0.5)"
			:safe-area="false"
			:is-mask-click="false"
			 @touchmove.stop.prevent=""
		>
			<view class="window">
				<view class="textarea-view">
					<textarea
						v-model="content"
						placeholder="请输入祝福语(200字内)"
						maxlength="200"
					></textarea>
					<view class="text-right font12 color-sub">{{content.length}}/200</view>
				</view>
				<view class="bottom flex-all-center">
					<view
						class="send text-center font18"
						hover-class="navigator-hover"
						@click="send"
					>提交</view>
				</view>
			</view>

			<xwy-ad v-if="show_ad" :activity_id="activity_id" :ad_type="66" style="width: 90vw;"></xwy-ad>

			<view class="close text-center">
				<text class="iconfont icon-wrong color-white font28" @click="close"></text>
			</view>
		</uni-popup>
		<xwy-ad v-if="show_ad" :activity_id="activity_id" :ad_type="3"></xwy-ad>
	</view>
</template>

<script>
	const app = getApp()
	export default {
		name: "send-wish-popup",
		props: {
			activity_id: String
		},
		data() {
			return {
				content: '',
				show_ad: false
			}
		},

		methods: {
			open(opt) {
				if (opt?.callbacks) this.callbacks = opt.callbacks
				if (opt.show_ad) this.show_ad = opt.show_ad
				if (opt.point_id) this.point_id = opt.point_id
				if (opt.taskActive) this.taskActive = opt.taskActive
				this.$refs.popup.open()
			},

			async send() {
				if (!this.content) return this.$uni.showToast('请输入祝福语')

				uni.showLoading('正在提交...')
				const res = await this.xwy_api.request({
					url: 'front.flat.active.cloud_wish.user.wish/submit_wish',
					data: {
						active_id: this.activity_id,
						content: this.content
					}
				})

				if (!res?.status) {
                    uni.hideLoading()
					await this.$uni.showModal(res?.info || '提交失败，请重试')
					return this.callbacks?.fail?.()
				}

                let integral = null
                if (this.taskActive) integral = await this.rewardIntegral()

				let success_info = '提交成功'
                if (integral) success_info += `, 奖励${integral}积分。`
				this.$uni.showToast(success_info)
				this.close()
				this.callbacks?.success?.()
			},

			close() {
				this.content = ''
				this.callbacks && this.callbacks.close && this.callbacks.close()
				this.$refs.popup.close()
			},

            async rewardIntegral() {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                    data: {
                        active_id: this.activity_id,
                        sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify({
                            types: 30,
                            point_id: this.point_id,
                            result: 'success'
                        }))
                    }
                })

                return res?.data?.num || null
            }
		}
	}
</script>

<style scoped>
.window {
	width: 90vw;
	background-color: #fefdf3;
	border-radius: 10px;
	margin-bottom: 10px;
}
.textarea-view {
	padding: 20px;
}
textarea {
	width: calc(90vw - 40px);
	height: calc(90vh - 280px - 80px);
}
.bottom {
	height: 80px;
	background: linear-gradient(#f5bf87,#e76142);
	border-radius: 10px;
}
.send {
	background: linear-gradient(#fefae9, #fdebb4);
	color: #d76e37;
	line-height: 44px;
	width: 120px;
	border-radius: 10px;
}
.close {
	padding-top: 10px;
	font-weight: 600;
}
</style>
