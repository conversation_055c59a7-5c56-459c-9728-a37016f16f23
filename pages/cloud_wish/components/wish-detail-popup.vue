<template>
	<view>
		<uni-popup
			ref="popup"
			type="center"
			mask-background-color="rgba(0,0,0,0.5)"
			:safe-area="false"
			:is-mask-click="false"
			 @touchmove.stop.prevent=""
		>
			<view class="window">
				<scroll-view scroll-y class="scroll-view">
					<view v-if="!content" style="padding-top: 10vh;">
						<uni-load-more status="loading"></uni-load-more>
					</view>
					<view class="content">
						<view>
							<text class="color-content font16" space="nbsp">{{content}}</text>
						</view>
						<view class="text-right" style="padding-top: 20px;">
							<view style="color: #ee8262;">{{nickname}}</view>
							<view style="color: #a98475;" class="font12">{{time}}</view>
						</view>
					</view>
				</scroll-view>
				<view class="flex-all-center p10">
					<view class="like-button" hover-class="navigator-hover" @click="like">给ta点赞</view>
				</view>
			</view>

			<xwy-ad v-if="show_ad" :activity_id="activity_id" :ad_type="66" style="width: 90vw;"></xwy-ad>

			<view class="close text-center">
				<text class="iconfont icon-wrong color-white font28" @click="close"></text>
			</view>
		</uni-popup>
		<xwy-ad v-if="show_ad" :activity_id="activity_id" :ad_type="3"></xwy-ad>

		<uni-popup ref="like_popup" type="center">
		    <view class="like_popup text-center">
		        <view class="popup-close" @click="uniPopupClose('like_popup')">
		            <uni-icons type="close" size="24" color="#ffffff"/>
		        </view>
		        <view class="color-white" style="padding: 20px; padding-top: 30px;">
		            <icon :type="like_status ? 'success' : 'clear'" size="80" color="#ffffff"></icon>
		            <view class="font18">点赞{{like_status ? '成功' : '失败'}}</view>
		        </view>
		        <view class="color-info" style="padding: 20px;">
		            <view class="color-content text-center font14" style="padding-bottom: 20px;">
		                {{ like_info }}
		            </view>
		        </view>
				<xwy-ad v-if="popup_show && !show_ad" :ad_type="3"></xwy-ad>
		    </view>
			<view class="pt5">
				<xwy-ad v-if="popup_show && !show_ad" :ad_type="66"></xwy-ad>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	const app = getApp()
	export default {
		name: "send-wish-popup",
		props: {
			activity_id: String
		},
		data() {
			return {
				content: '',
				time: '',
				nickname: '',
				headimg: '',
				content: '',
				show_ad: false,
				like_status: '',
				like_info: '',
				popup_show: false
			}
		},

		methods: {
			open(opt) {
				if (!opt.id) {
					uni.showToast({
						title: '祝福语获取失败',
						icon: 'error'
					})
					this.close()
					return false
				}
				this.id = opt.id
				if (opt?.callbacks) this.callbacks = opt.callbacks
				if (opt.show_ad) this.show_ad = opt.show_ad
				this.getDetail(opt.id)
				this.$refs.popup.open()
			},

			async getDetail(id) {
				const res = await this.xwy_api.request({
					url: 'front.flat.active.cloud_wish.user.wish/wish_details',
					data: {
						access_token: app.globalData.access_token,
						id
					}
				})
				const detail = res?.data?.wish_details
				if (!detail) {
					uni.showToast({
						title: res?.info || '祝福语获取失败',
						icon: 'error'
					})
					this.close()
					return false
				}


				this.callbacks && this.callbacks.success && this.callbacks.success()
				if (detail.content) this.content = detail.content
				if (detail.create_time) this.time = detail.create_time
				if (detail.checked) this.checked = detail.checked
				if (detail.user_attend_details?.headimg) this.headimg = detail.user_attend_details.headimg
				if (detail.user_attend_details?.must_submit.length &&
					detail.user_attend_details.must_submit[0].value
				) this.nickname = detail.user_attend_details.must_submit[0].value
			},

			close() {
				this.content = ''
				this.callbacks && this.callbacks.close && this.callbacks.close()
				this.$refs.popup.close()
			},

			async like() {
				uni.showLoading({
					title: '点赞中...',
					mask: app.globalData.evn_version === 'trial' ? false : true
				})

				const res = await this.xwy_api.request({
					url: 'front.flat.active.cloud_wish.user.wish/like_for_wish_details',
					data: {
						access_token: app.globalData.access_token,
						id: this.id
					}
				})

				uni.hideLoading()

				this.like_status = res?.status || 0
				this.like_info = res?.info || '点赞失败'
				this.uniPopupOpen('like_popup')
			},

			uniPopupOpen(ref) {
			    this.$refs[ref].open()
				this.popup_show = true
			},

			uniPopupClose(ref) {
			    this.$refs[ref].close()
				this.popup_show = false
			},
		}
	}
</script>

<style scoped>
.window {
	background: linear-gradient(#f5eedb, #ffffff);
	border-radius: 10px;
	margin-bottom: 10px;
}
.scroll-view {
	width: 90vw;
	height: calc(100vh - 300px);
	padding: 20px;
	box-sizing: border-box;
}
.content {
	height: ;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	height: 100%;
}
.like-button {
	width: 150px;
	border-radius: 10px;
	line-height: 44px;
	text-align: center;
	background-color: #ee8262;
	color: #f8eada;
}
.close {
	padding-top: 10px;
	font-weight: 600;
}
.like_popup {
    width: 300px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
	background: linear-gradient(#ee8262, #f8eada);
	margin-top: -10vh;
}
.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}
</style>
