<template>
	<view class="page">
		<view class="user-info flex-row">
			<image class="headimg" :src="headimg"/>
			<view class="nickname ellipsis">{{nickname}}</view>
		</view>
		<view class="content-view">
			<scroll-view class="content" scroll-y>
				<text class="color-content font16" space="nbsp">{{content}}</text>
				<view v-if="!content" class="flex-all-center" style="padding-top: 10vh;">
					<view class="loading color-sub font14">祝福语加载中</view>
				</view>
			</scroll-view>
			<view class="time text-right font14">{{time}}</view>
		</view>
		<view v-if="checked" class="like-button" hover-class="navigator-hover" @click="like">给ta点赞</view>


		<template v-if="!close_ad && content">
			<xwy-ad :ad_type="3"></xwy-ad>
			<view class="bottom-ad">
				<xwy-ad :ad_type="66"></xwy-ad>
			</view>
		</template>

		<uni-popup ref="like_popup" type="center">
		    <view class="like_popup text-center">
		        <view class="popup-close" @click="uniPopupClose('like_popup')">
		            <uni-icons type="close" size="24" color="#ffffff"/>
		        </view>
		        <view class="color-white" style="padding: 20px; padding-top: 30px;">
		            <icon :type="like_status ? 'success' : 'clear'" size="80" color="#ffffff"></icon>
		            <view class="font18">点赞{{like_status ? '成功' : '失败'}}</view>
		        </view>
		        <view class="color-info" style="padding: 20px;">
		            <view class="color-content text-center font14" style="padding-bottom: 20px;">
		                {{ like_info }}
		            </view>
		        </view>
				<xwy-ad v-if="popup_show && !close_ad" :ad_type="3"></xwy-ad>
		    </view>
			<view class="pt5">
				<xwy-ad v-if="popup_show && !close_ad" :ad_type="66"></xwy-ad>
			</view>
		</uni-popup>
	</view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'

export default {
	data() {
		return {
			nickname: '',
			headimg: '',
			content: '',
			time: '',
			checked: 0,
			popup_show: false,
			like_status: '',
			like_info: '',
			close_ad: false
		}
	},

	onLoad(e) {
		this.id = e.id
		login.uniLogin(err => {
			if (err && err.errMsg) {
			    uni.hideLoading()
			    uni.showModal({
			        title: err.errTitle || '提示',
			        content: err.errMsg,
			        showCancel: false
			    })
			    return false
			}

			this.getActivityData()
		})
	},

	methods: {
		async getActivityData() {
			let activity_detail = app.globalData.activity_detail

			if (!activity_detail) {
				const res = await xwy_api.request({
					url: 'front.flat.sport_step.active_list/active_details',
					data: {
						access_token: app.globalData.access_token,
						active_id: this.id
					}
				})

				activity_detail = res.data.active_details

			}

			if (activity_detail?.rank_set?.closed_AD) this.close_ad = true

			this.getDetail()
		},

		async getDetail() {
			const res = await this.xwy_api.request({
				url: 'front.flat.active.cloud_wish.user.wish/wish_details',
				data: {
					access_token: app.globalData.access_token,
					id: this.id
				}
			})

			const detail = res?.data?.wish_details
			if (!detail) {
				uni.showModal({
					title: '提示',
					content: res?.info || '获取失败',
					showCancel: false,
					success: () => uni.navigateBack()
				})
				return false
			}

			if (detail.content) this.content = detail.content
			if (detail.create_time) this.time = detail.create_time
			if (detail.checked) this.checked = detail.checked
			if (detail.user_attend_details?.headimg) this.headimg = detail.user_attend_details.headimg
			if (
				detail.user_attend_details?.must_submit.length &&
				detail.user_attend_details.must_submit[0].value
			) {
				this.nickname = detail.user_attend_details.must_submit[0].value
				uni.setNavigationBarTitle({
					title: this.nickname + '的祝福'
				})
			}
		},

		async like() {
			uni.showLoading({
				title: '点赞中...',
				mask: app.globalData.evn_version === 'trial' ? false : true
			})

			const res = await this.xwy_api.request({
				url: 'front.flat.active.cloud_wish.user.wish/like_for_wish_details',
				data: {
					access_token: app.globalData.access_token,
					id: this.id
				}
			})

			uni.hideLoading()

			this.like_status = res?.status || 0
			this.like_info = res?.info || '点赞失败'
			this.uniPopupOpen('like_popup')
		},

		uniPopupOpen(ref) {
		    this.$refs[ref].open()
			this.popup_show = true
		},

		uniPopupClose(ref) {
		    this.$refs[ref].close()
			this.popup_show = false
		},
	},

	onShareAppMessage() {
		return {
			path: '/pages/cloud_wish/user/wish_detail?id=' + this.id,
			title: this.nickname + '的祝福'
		}
	}
}
</script>

<style scoped lang="scss">
.page {
	position: relative;
	height: 100vh;
	background: linear-gradient(#ee8262, #f8eada);
}
.user-info {
	position: absolute;
	top: 20px;
	left: 50px;
}
$headimgSize: 60px;
.headimg {
	width: $headimgSize;
	height: $headimgSize;
	border-radius: 50%;
	display: block;
	border: 2px solid #fff;
	box-shadow: 0 0 8px #ccc;
	position: relative;
	z-index: 2;
	background-color: #ee8262;
}
.nickname {
	min-width: 100px;
	max-width: calc(100vh - 150px);
	background-color: #f5eedb;
	opacity: .8;
	border-radius: 0 10px 0 0;
	color: #a98475;
	padding: 5px 10px;
	padding-left: 30px;
	height: 20px;
	line-height: 30px;
	margin-top: 10px;
	position: relative;
	left: -20px;
	z-index: 1;
}
.content-view {
	position: absolute;
	top: 60px;
	left: 10px;
	width: calc(100vw - 20px);
	background: linear-gradient(#f5eedb, #ffffff);
	// background-color: #f5eedb;
	opacity: .8;
	border-radius: 10px;
}
.content {
	height: calc(100vh - 310px);
	padding: 20px;
	padding-top: 40px;
	box-sizing: border-box;
}
.time {
	padding: 20px;
	color: #a98475;
}
.like-button {
	position: absolute;
	bottom: 130px;
	left: 50%;
	width: 150px;
	margin-left: -75px;
	line-height: 44px;
	border-radius: 10px;
	text-align: center;
	background-color: #ee8262;
	color: #f8eada;
}
.loading {
	position: relative;
}
.loading::after {
	position: absolute;
	left: 100%;
	content: "";
	width: 20px;
	animation: loading 1.5s infinite;
}
.bottom-ad {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
}
@keyframes loading {
	0%, 100% { content: "."; }
	33% { content: ".."; }
	66% { content: "..."; }
}
.like_popup {
    width: 300px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
	background: linear-gradient(#ee8262, #f8eada);
	margin-top: -10vh;
}
.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}
</style>
