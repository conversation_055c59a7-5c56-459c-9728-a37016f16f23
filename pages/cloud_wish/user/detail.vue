<template>
    <view class="page">

        <screen-picture
            v-if="screen_pic_show"
            :hide="screen_pic_hide"
            :image-src="screen_pic"
            :time="screen_pic_count_down"
            :show-button="true"
            @skipScreen="skipScreen"
        />


        <activity-delete-tips v-if="error" :activeId="id"/>

		<template v-if="detail && detail.active_id && !screen_pic_count_down">
			<!-- #ifndef H5 -->
			<image
				v-if="detail.logo"
				class="logo"
				mode="widthFix"
				:src="detail.logo"
			/>
			<!-- #endif -->

			<view class="icon-list text-center">

			    <navigator v-if="is_my_activity && !taskActive" class="icon-item"
                           :url="'../admin/manage?id=' + id">
			        <text class="iconfont font24 color-white icon-setting"></text>
			        <!-- <view class="color-white font14" style="padding-top: 3px;">活动管理</view> -->
			    </navigator>
			    <!-- <view v-if="!is_joining" class="icon-item" @click="joinActivity">
			        <text class="iconfont font24 color-white icon-users"></text>
			        <view class="color-white font14" style="padding-top: 3px;">参与活动</view>
			    </view> -->
			    <view v-if="is_joining && !taskActive" class="icon-item" @click="uniPopupOpen('my_info')">
			        <text class="iconfont font24 color-white icon-personal-data"></text>
			        <!-- <view class="color-white font14" style="padding-top: 3px;">报名信息</view> -->
			    </view>


				<view v-if="!taskActive" class="icon-item" @click="uniPopupOpen('activity_detail')">
				    <text class="iconfont font24 color-white icon-feedback"></text>
				    <!-- <view class="color-white font14" style="padding-top: 3px;">活动说明</view> -->
				</view>

				<view v-if="!detail.rank_set || !detail.rank_set['closed_top_rank']" class="icon-item"
                      hover-class="navigator-hover" @click="toTopList()">
				    <text class="iconfont font24 color-white icon-trophy"></text>
				    <!-- <view class="color-white font14" style="padding-top: 3px;">排行榜</view> -->
				</view>

			    <view v-if="(!detail.rank_set || !detail.rank_set['share_closed']) && !taskActive"
                      class="icon-item"
                      hover-class="navigator-hover" @click="showActiveSharePopup">
			        <text class="iconfont font24 color-white icon-share"></text>
			    </view>
			</view>

			<!-- <xwy-ad
				v-if="
					!exchange_success_info &&
					!password_dialog_show &&
					!join_popup_show &&
					!popup_open &&
					(!detail.rank_set || !detail.rank_set.closed_AD)
				"
				:ad_type="4"
			></xwy-ad> -->

			<view class="wish-words-list">
				<view class="flex-kai p10">
					<view style="width: 100px;"></view>
					<view class="font18" style="color: #b17243;">祝福语</view>
					<view>
						<navigator class="font14 text-right pt5" :url="'../wish_list?look_type=all&id=' + id">
							<text style="color: #a65f12;">查看更多</text>
							<uni-icons type="forward" size="14" color="#a65f12"/>
						</navigator>
					</view>
				</view>

				<template v-for="(item, index) in wish_words_list">
					<xwy-ad
						v-if="!popup_open && !detail.rank_set.closed_AD && index === 0"
						:activity_id="id"
						:ad_type="66"
					></xwy-ad>
					<view class="wish-words-item flex-row" :key="item.id">
						<view
							v-if="item.animation && item.bubble_show"
							class="wish-bubble-item flex-all-center"
							:animation="item.animation"
							@click="showWishDetailPopup(item)"
						>
							<view class="bubble"></view>
							<image
								class="wish-bubble-headimg"
								:src="item.user_attend_details.headimg"
								mode="aspectFill"
							/>
						</view>
						<image
							class="wish-words-item-headimg"
							:src="item.user_attend_details.headimg"
							mode="aspectFill"
						/>
						<navigator class="wish-words-item-msg p10" :url="'./wish_detail?id=' + item.id">
							<view class="font18" style="color: #8e4430;">
								{{item.user_attend_details.must_submit[0].value || ''}}：
							</view>
							<view class="pt10 pb10" style="color: #aa8476;">
								<view class="ellipsis--l3">
									<text space="nbsp">{{item.content}}</text>
								</view>
							</view>
							<view class="text-right font14" style="color: #a98475;">{{item.create_time}}</view>
						</navigator>
					</view>
					<xwy-ad
						v-if="!popup_open && !detail.rank_set.closed_AD && ((index + 1) % 5 === 0)"
						:activity_id="id"
						:ad_type="66"
					></xwy-ad>
				</template>

				<view v-if="!wish_words_list.length && !wish_words_list_load" class="text-center" style="padding: 30px 0;">
				    <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
				    <view class="color-sub">暂无加精祝福语</view>
				</view>
			</view>


			<view v-if="!notShowPublishButton" class="to-send text-center font18"
                  hover-class="navigator-hover" @click="toSend">
                送祝福
            </view>

			<send-wish-popup ref="send_wish_popup" :activity_id="id"></send-wish-popup>
			<wish-detail-popup ref="wish_detail_popup" :activity_id="id"></wish-detail-popup>


			<view v-if="technology_support" class="flex-all-center">
          <navigator
              v-if="technology_support.news_id"
              :url="'/pages/news/preview?id=' + technology_support.news_id + '&type=support'"
              class="text-center font14 color-sub p10"
          >{{ technology_support.button_text }}</navigator>
          <view v-else class="text-center font14 color-sub p10">
              {{ technology_support.button_text }}
          </view>
			</view>


			<xwy-ad v-if="(!detail.rank_set || !detail.rank_set.closed_AD) && !screen_pic_show" :ad_type="3"></xwy-ad>


			<view v-if="join_popup_show" class="join-popup flex-all-center bg-white" @touchmove.stop.prevent="">
			    <view>
			    	<view class="join-popup-c bg-white">
			    	    <view class="text-center font18 color-content p10">
			    			<template v-if="update_attend_details">修改报名信息</template>
			    			<template v-else>参加活动</template>
			    	    </view>


			    		<template v-if="must_submit.length">
			    			<template v-for="(item, index) in must_submit">
			    				<input
			    					v-if="item.types === 1"
			    					:key="index"
			    				    class="join-input"
			    				    v-model="item.value"
			    				    :placeholder="'请输入' + item.title + (item.rules === 1 ? ' (必填)' : '')"
			    				/>
			    				<picker
			    					v-if="item.types === 2"
			    					:range="item.options"
			    					range-key="text"
			    					@change="mustValueChange($event, index)"
			    				>
			    					<view class="join-input flex-kai">
			    					    <view v-if="!item.value" class="color-sub">
			    							请选择{{item.title}}{{item.rules === 1 ? ' (必选)' : ''}}
			    						</view>
			    					    <view v-if="item.value">{{ item.value }}</view>
			    					    <text class="iconfont icon-more color-disabled font18"/>
			    					</view>
			    				</picker>
			    			</template>

			    		</template>




			    	    <view class="join-popup-btns flex-row text-center font18">
			    	        <view class="join-popup-btn-cancel color-sub" @click="cancelJoin">取消</view>
			    	        <view class="join-popup-btn-confirm color-success" @click="joinAjax">确定</view>
			    	    </view>
			    	</view>

					<template v-if="!detail.rank_set || !detail.rank_set.closed_AD">
						<view class="pt5">
							<xwy-ad :ad_type="66"></xwy-ad>
						</view>
						<xwy-ad :ad_type="3"></xwy-ad>
					</template>
			    </view>


			</view>

            <active-share ref="activeShare"/>

			<uni-popup ref="activity_detail" type="center" @touchmove.stop.prevent="">
			    <view v-if="detail && detail.conf && detail.conf.active" class="uni-popup-info detail-popup bg-white">
			        <view class="popup-close" @click="uniPopupClose('activity_detail')">
			            <uni-icons type="close" size="28" color="#b2b3b7"/>
			        </view>
			        <scroll-view scroll-y class="detail-popup-detail" style="max-height: calc(100vh - 200px); padding: 10px 0;">
			            <view class="p10 bdb-10 bg-white">
			                <view class="text-center color-sub pb5">- 活动规则 -</view>

							<view class="color-content font16">
								活动参与方式：
								<template v-if="detail.conf.active.enter_types === 1">
                                    自由报名参与活动
								</template>
								<template v-if="detail.conf.active.enter_types === 2">
									需要输入密码才能报名
								</template>
								<template v-if="detail.conf.active.enter_types === 3">
									报名需要审核通过才能参与活动
								</template>
							</view>


							<view v-if="!detail.rank_set || !detail.rank_set['closed_user_center']" class="flex-all-center">
								<navigator url="/pages/user/user" class="p10 color-sub font14">个人中心</navigator>
							</view>

			            </view>

			            <view v-if="detail.content || news_detail" class="p10 bdb-10 bg-white">
			                <view class="text-center color-sub pb5">- 活动说明 -</view>
			                <view class="color-content font16">
			                    <template v-if="detail.content && !news_detail">
			                    	<rich-text :nodes="detail.content" space selectable></rich-text>
			                    </template>
								<template v-if="news_detail">
									<template v-if="news_detail.content">
										<u-parse :content="news_detail.content"/>
									</template>
								</template>
			                </view>
			            </view>
			            <xwy-ad v-if="!loading && (!detail.rank_set || !detail.rank_set.closed_AD)" :ad_type="66"></xwy-ad>
			        </scroll-view>
			    </view>
			</uni-popup>

			<uni-popup ref="my_info" type="center" @touchmove.stop.prevent="">
			    <view v-if="detail && detail.conf && detail.conf.active" class="uni-popup-info bg-white p20">
			        <view class="popup-close" @click="uniPopupClose('my_info')">
			            <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
			        </view>
			        <view class="text-center color-sub pb5">- 活动报名信息 -</view>
			        <view class="text-center p10">
			            <image
							class="headimg"
							:src="headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"
			                mode="aspectFill"
					   />
					    <view>
						    <text class="color-primary" @click="updateHeadimg">更改头像</text>
					    </view>
			        </view>


					<view
						class="color-content font16 ptm5"
						v-for="(item, index) in must_submit"
						:key="index"
						@click="updateAttendDetailShow"
					>
						<text>
							{{item.title}}：
							<template>
								<template v-if="item.value">{{ item.value }}</template>
								<template v-else>
									<template v-if="item.types === 1">未填写</template>
									<template v-if="item.types === 2">未选择</template>
								</template>
							</template>
						</text>
						<text v-if="is_joining" class="iconfont icon-edit color-sub pl5"></text>
					</view>


					<template v-if="popup_open && (!detail.rank_set || !detail.rank_set.closed_AD)">
						<view style="position: relative; left: -10px;">
							<xwy-ad :ad_type="66"></xwy-ad>
						</view>
						<xwy-ad :ad_type="3"></xwy-ad>
					</template>
			    </view>
			</uni-popup>




			<template v-if="password_dialog_show">
				<uni-popup
					ref="input_password"
					type="dialog"
					mode="input"
					:is-mask-click="false"
					@maskClick="copy(id)"
				>
					<uni-popup-dialog
						mode="input"
						title="活动密码"
						:value="password"
						placeholder="请输入活动密码"
						@confirm="passwordInputConfirm"
						@close="passwordInputClose"
					></uni-popup-dialog>
				</uni-popup>
			</template>
		</template>






			<!-- <view class="p10 bdb-10 bg-white">
				<view
					v-if="detail.conf.active.enter_types === 3 && is_joining && checked === 0"
					class="join-container"
				>
					<view class="pb5 color-sub text-center font14">需要管理员审核通过后才能去运动</view>
					<view class="flex-all-center">
						<view class="join-btn bg-background color-sub">去运动</view>
					</view>
				</view>

				<view v-else class="join-container">
					<view class="flex-all-center">
						<view
							v-if="is_joining"
							class="join-btn bg-primary color-white"
							@click="toRun"
						>去运动</view>
						<view
							v-else
							class="join-btn bg-primary color-white"
							@click="joinActivity"
						>参与活动</view>
					</view>
				</view>
			</view> -->


        <iup ref="input_username_password"></iup>

        <expiration-reminder ref="expirationReminder"/>

        
    </view>
</template>

<script>
import iup from "@/components/input-username-password.vue";

const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import my_storage from '@/utils/storage.js'
import base64 from '@/utils/base64.js'
import activity_tool from '@/utils/acyivity_tool.js'

import sendWishPopup from '../components/send-wish-popup.vue'
import wishDetailPopup from '../components/wish-detail-popup.vue'

let interval

export default {
    components: {
        iup,
		sendWishPopup,
		wishDetailPopup
    },
    data() {
        return {
			evn_version: app.globalData.evn_version,
            show_tab: false,
            from_tab: false,
            loading: true,
			screen_pic: '',
            screen_pic_show: false,
			screen_pic_hide: false,
            screen_pic_count_down: null,
            id: '',
			userid: '',
            is_my_activity: false,
            detail: {},
			user_details: {},
            error: '',
            join_popup_show: false,
			update_attend_details: false,
            headimg: '',
            username: '',
            checked: 0,
            is_joining: false,
            password: '',
            technology_support: false,
			must_submit: [],
			password_dialog_show: false,
			popup_open: false,
			news_detail: null,
			platform: uni.getSystemInfoSync().platform,
			wish_words_list: [],
			load_page: 1,
			is_lastpage: false,
			wish_words_total: 0,
			wish_words_list_load: false,
            taskActive: false,
            notShowPublishButton: false
        }
    },

    onLoad(e) {
        console.log('活动详情页面路径参数', e)


		// #ifdef H5
		xwy_api.alert('请在小程序内打开', {
			success: () => uni.navigateBack()
		})
		const a = true
		if (a) return false
		// #endif

        // 任务闯关活动进来的
        if (e['task_active']) this.taskActive = true
        // 任务闯关活动进来只查看的，不显示发布按钮
        if (e['not_show_publish_button']) this.notShowPublishButton = true
        if (e.point_id) this.point_id = Number(e.point_id)

        if (e.from && e.from === 'tab') {
            this.from_tab = true
            this.$uni.hideHomeButton()
        }

		if (uni.getLaunchOptionsSync().scene === 1154) {
			this.getSimpleDetail(e.id)
			return false
		}

        e.screen_pic ? this.screenPicShow(e.screen_pic) : this.$uni.showLoading('数据加载中...')

        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                uni.showModal({
                    title: err.errTitle || '提示',
                    content: err.errMsg,
                    showCancel: false
                })
                return false
            }

			if (this.from_tab) this.show_tab = true

            if (e.scene) return this.analysisScene(e.scene)

            if (!e.id) {
                this.loading = false
                this.error = '请指定活动id'
                return uni.hideLoading()
            }


            this.id = e.id

			this.userid = app.globalData.userid

            this.getDetail()

			this.getWishWordsList()
        })
    },

    onShareAppMessage() {
        this._utils.shareCopyActivityId(this.detail)

		let url = '/pages/cloud_wish/user/detail?id=' + this.id
        if (this.detail?.conf?.active?.screen_pic) url += `&screen_pic=${this.detail.conf.active.screen_pic}`
		if (this.from_tab) url += '&from=tab'

        return {
            title: this.detail.name,
            path: url,
            imageUrl: this.detail.logo || ''
        }
    },

	onShareTimeline() {
		return {
			title: this.detail.name,
			imageUrl: this.detail.logo || ''
		}
	},

	onReachBottom() {
		!this.is_lastpage && !this.wish_words_list_load && this.getWishWordsList()
	},

    methods: {
		async getWishWordsList() {
			if (this.load_page === 1) {
				this.wish_words_list = []
				this.is_lastpage = false
			}

			this.wish_words_list_load = true
			const res = await this.xwy_api.request({
				url: 'front.flat.active.cloud_wish.user.wish/wish_list',
				data: {
					access_token: app.globalData.access_token,
					active_id: this.id,
					is_public: 1,
					page: this.load_page,
					perpage: 10
				}
			})
			this.wish_words_list_load = false

			const _data = res?.data?.wish_list
			if (!_data?.data?.length) {
				this.is_lastpage = true
				return false
			}
			this.load_page++
			this.wishListDataInit(_data.data)
			this.is_lastpage = _data.is_lastpage
			this.wish_words_total = _data.total
		},


		wishListDataInit(list) {
            // 只有第一页的才显示泡泡
            if (this.load_page === 2) {
                list.forEach(v => {
                    v.bubble_show = true
                    v.animation = {}
                })
            }

			this.wish_words_list = [...this.wish_words_list, ...list]

            // 只有第一页的才显示泡泡
			if (this.load_page === 2) this.setWishBubble()
		},

		setWishBubble() {
			this.wish_words_list.forEach(v => {
				v.bubble_show && this.setWishItemBubble(v, true)
			})
		},

		setWishItemBubble(item, first_time) {
			// 因为第一次的话是直接就瞬移到指定位置，然后停住duration毫秒，所以第一次300毫秒，这样就不会停留那么久了
			const duration = first_time ? 300 : utils.randomNum(8000, 15000)
			const animation = uni.createAnimation({
				duration,
				timingFunction: 'linear'
			})
			const {windowWidth, windowHeight} = uni.getWindowInfo()
			const min_width = -20, max_width = windowWidth - 75
			const min_height = 0, max_height = windowHeight - 50
			const move_width = utils.randomNum(min_width, max_width)
			const move_height = utils.randomNum(min_height, max_height)
			animation.translate(move_width, move_height).step()
			item.animation = animation.export()
			setTimeout(() => {
				this.setWishItemBubble(item, false)
			}, duration)
		},


		showWishDetailPopup(item) {
			this.popup_open = true
			item.bubble_show = false
			this.$refs.wish_detail_popup.open({
				id: item.id,
				callbacks: {
					close: () => this.popup_open = false
				},
				show_ad: !this.detail.rank_set?.closed_AD
			})
		},

		async getSimpleDetail(id) {
			uni.showLoading({
			    mask: true,
			    title: '数据加载中...'
			})
			const res = await xwy_api.request({
				url: 'front.flat.sport_step.active_list/active_details_simple',
				data: {
					active_id: id
				}
			})
			this.loading = false
			uni.hideLoading()

			if (!res || !res.data || !res.data.active_details) {
				this.loading = false
				uni.hideLoading()
				this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
				return false
			}


			if (res.data.active_more_data) {
				const active_more_data = res.data.active_more_data
				this.active_more_data = active_more_data
				if (active_more_data.technology_support) {
					this.technology_support = res.data.active_more_data.technology_support
				}
			}


			const detail = res.data.active_details


			this.detail = detail

			my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)



			if (detail.conf) {
				const conf = detail.conf

				if (!this.screen_pic && conf.active?.screen_pic) {
					this.screenPicShow(conf.active.screen_pic)
				}


				if (conf.must_submit) {
					const must_submit = conf.must_submit
					delete conf.must_submit
					if (must_submit.length) {
						must_submit.forEach(v => v.value = v.value || '')
						this.must_submit = must_submit
					}
				}
			}

			if (detail.rank_set) {
				const rank_set = detail.rank_set
				// 是否纯净版，纯净版显示tab栏，隐藏返回首页按钮
				if (rank_set.shield_other && !this.show_tab) {
					this.show_tab = true
				}
			}


			this.addLookRecords()

			if (detail.name) {
				uni.setNavigationBarTitle({
					title: detail.name
				})
			}

			if (!just_update && app.globalData.userid === detail.userid) {
				this.is_my_activity = true
			}

		},


		daysDiff(date1, date2) {
			return Math.ceil(Math.abs(date1 - date2) / 86400000)
		},



        screenPicShow(src) {
            // #ifdef H5
            // h5不显示开屏图
            return false
            // #endif
			uni.hideLoading()
            this.screen_pic = src
            this.screen_pic_count_down = 5
            this.screen_pic_show = true
            interval = setInterval(() => {
                this.screen_pic_count_down--
                if (this.screen_pic_count_down <= 0) {
                    this.skipScreen()
                }
            }, 1000)
        },
        skipScreen() {
            clearInterval(interval)
            this.screen_pic_count_down = 0
			this.screen_pic_hide = true

			const timeout = setTimeout(() => {
				this.screen_pic_show = false
				this.screen_pic_hide = false
				if (this.loading) {
				    uni.showLoading({
				        mask: true,
				        title: '数据加载中...'
				    })
				}
				this.passwordDialogShow()
				if (this.from_tab) this.show_tab = true

                if (this.detail.rank_set?.['batch_import'] && this.detail.conf.active.enter_types === 4 && !this.is_joining) {
                    this.$refs.input_username_password.open({
                        detail: this.detail,
                        success: () => {
                            this.$uni.showLoading()
                            this.getDetail()
                        }
                    })
                }
                
				clearTimeout(timeout)
			}, 500)
        },
        analysisScene(scene) {
            const sceneStr = decodeURIComponent(scene)
            console.log(sceneStr)
            const id = utils.getUrlParams('id', sceneStr)
            console.log('id===', id)
            if (!id) {
                uni.hideLoading()
                return this.$uni.showModal('从二维码获取id失败')
            }

			this.getActiveId(id)
        },

		async getActiveId(id) {
			const data = {
				access_token: app.globalData.access_token,
				id
			}

			const res = await xwy_api.request({url: 'front.flat.sport_step.active_list/get_long_active_id_by_short_id', data})
			if (res?.data?.long_active_id) {
				this.id = res.data.long_active_id
				this.getDetail()
				this.getWishWordsList()
			} else {
				uni.hideLoading()
				xwy_api.alert(res && res.info || '长id获取失败')
			}
		},

        getDetail(just_update = false) {
			xwy_api.getActivityDetail(this.id, res => {
				if (!res || !res.data || !res.data.active_details) {
					this.loading = false
					uni.hideLoading()
					this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
					return false
				}

          // #ifndef H5
          this.$refs.expirationReminder.open(res.data.active_details)
          // #endif

				if (res.data.active_more_data) {
					const active_more_data = res.data.active_more_data
					this.active_more_data = active_more_data
					if (active_more_data.technology_support) {
						this.technology_support = res.data.active_more_data.technology_support
					}
				}


				const detail = res.data.active_details

				app.globalData.activity_detail = detail

				this.detail = detail

				my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)



				if (detail.conf) {
					const conf = detail.conf

					if (conf.active) {
						const active = conf.active
						if (!this.screen_pic && active.screen_pic) {
							this.screenPicShow(active.screen_pic)
						}
						if (active.news?.news_id) this.changeDetailContent(active.news.news_id)
					}

					if (conf.must_submit) {
						const must_submit = conf.must_submit
						delete conf.must_submit
						if (must_submit.length) {
							must_submit.forEach(v => v.value = v.value || '')
							this.must_submit = must_submit
						}
					}
				}

				if (detail.rank_set) {
					const rank_set = detail.rank_set
					// 是否纯净版，纯净版显示tab栏，隐藏返回首页按钮
					if (rank_set?.shield_other && !this.show_tab) {
						this.show_tab = true
                        this.$uni.hideHomeButton()

            // 更新纯净版缓存信息
            utils.updateShieldOtherInfo(this.detail)
					}

                    // #ifdef MP-WEIXIN
                    if (rank_set?.['share_closed']) uni.hideShareMenu(undefined)
                    // #endif
				}


				this.addLookRecords()

				if (detail.name) {
					uni.setNavigationBarTitle({
						title: detail.name
					})
				}

				if (!just_update && app.globalData.userid === detail.userid) {
					this.is_my_activity = true
				}

				this.getUserStatus()

			})
        },


		addLookRecords() {
			const detail = this.detail
			const value = {
				active_id: detail.active_id,
				name: detail.name,
				types: detail.types,
				logo: detail.logo || this.xwy_config.active_default_logo,
				look_time: new Date().getTime()
			}

			if (detail.organizer) value.organizer = detail.organizer
			my_storage.addActivityLookRecords(value)
		},


        async getUserStatus() {
			const res = await xwy_api.request({
				url: 'front.flat.sport_step.user/user_attend_details',
				data: {
					access_token: app.globalData.access_token,
					active_id: this.id
				}
			})

			if (res?.data?.user_details) {
				const attend_details = res.data.user_details
				this.user_details = attend_details
				this.is_joining = true
				this.checked = attend_details.checked || 0

				this.loading = false
				uni.hideLoading()

				if (attend_details.nickname) this.username = attend_details.nickname
				if (attend_details.headimg) this.headimg = attend_details.headimg

				if (attend_details.must_submit) {
					this.must_submit.forEach(v => {
						attend_details.must_submit.forEach(vv => {
							if (vv.title === v.title) v.value = vv.value
						})
					})
					this.$forceUpdate()
				}


				// 报名成功后重新弹出送祝福弹窗
				this.show_send_popup && this.toSend()
			} else {
				this.no_attend = true
				this.loading = false
				uni.hideLoading()

				if (this.screen_pic_show) return false

				this.passwordDialogShow()

                if (this.detail.rank_set?.['batch_import'] && this.detail.conf.active.enter_types === 4) {
                    this.$refs.input_username_password.open({
                        detail: this.detail,
                        success: () => {
                            this.$uni.showLoading()
                            this.getDetail()
                        }
                    })
                    return false
                }
			}

        },


		changeDetailContent(news_id) {
			xwy_api.ajax({
				url: "front.news/news_details",
				data: {
					access_token: app.globalData.access_token,
					news_id
				},
				success: res => {
					console.log(res);
					uni.hideLoading();

					uni.hideLoading()
					if (!res.data || !res.data.news_details) {
						uni.showModal({
							title: '提示',
							content: res.info || '文章内容获取失败',
							showCancel: false,
							success: () => uni.navigateBack()
						})
						return false
					}

					const detail = res.data.news_details


					if (detail.video_url) {
						let video_type = 'txv_id'
						if (detail.video_url.startsWith('http://') || detail.video_url.startsWith('https://')) {
							video_type = 'http'
						}
						detail.video_type = video_type
					}


					if (detail.content) {
						detail.content = utils.newsContentInit(detail.content)
					}

					this.news_detail = detail
				}
			})
		},


		passwordDialogShow() {
			if (this.detail.conf.active.password && this.no_attend) {
				const passwordDialogShow = () => {
					this.password_dialog_show = true
					const password = my_storage.getActivityPassword(this.id)
					if (password) {
						this.password = password
						this.checkActivityPassword(password)
					}
					const timeout = setTimeout(() => {
						this.$refs.input_password.open()
						clearTimeout(timeout)
					}, 30)
				}

				// 体验版不需要输入密码
				if (app.globalData.evn_version === 'trial') {
					uni.showModal({
						title: '提示',
						content: '此活动设置了活动密码，请勿报名参与活动！！！',
						cancelText: '进入活动',
						confirmText: '输入密码',
						success: res => {
							res.confirm && passwordDialogShow()
						}
					})
					return false
				}


				passwordDialogShow()
			}
		},

		passwordInputConfirm(val) {
			if (!val) {
				// 纯净版取消输入密码出现去个人中心选项
				if (this.detail?.rank_set?.shield_other) {
					uni.showModal({
						title: '提示',
						content: '请输入密码',
						cancelText: '个人中心',
						confirmText: '重新输入',
						success: res => {
							if (res?.confirm) {
								this.$refs.input_password.open()
							}
							if (res?.cancel) {
								uni.navigateTo({
									url: '/pages/user/user'
								})
							}
						}
					})
					return
				}

				xwy_api.alert('请输入密码', {
					success: () => this.$refs.input_password.open()
				})
				return false
			}
			this.checkActivityPassword(val)
		},

		async checkActivityPassword(password) {
			uni.showLoading({
				title: '密码验证中...',
				mask: true
			})

			const res = await xwy_api.request({
				url: 'front.flat.sport_step.user/check_active_password',
				data: {
					access_token: app.globalData.access_token,
					active_id: this.id,
					password
				}
			})
			uni.hideLoading()

			if (res?.status) {
				my_storage.rememberActivityPassword(this.id, password)
				this.$refs.input_password.close()
				uni.showToast({
					title: '密码正确',
					icon: 'success'
				})

				return false
			}


			xwy_api.alert(res && res.info || '密码错误', {
				success: () => this.$refs.input_password.open()
			})
		},

		passwordInputClose() {
			this.copy(this.id, true)

			if (getCurrentPages().length > 1) {
				uni.navigateBack()
				return false
			}

			// 纯净版并且没有上一页面，重新弹出输入密码窗口
			if (app.globalData.shop_info?.extend_set?.shield_other_active?.active_id) {
				xwy_api.alert('请输入活动密码', {
					success: () => this.$refs.input_password.open()
				})
				return false
			}

			uni.reLaunch({
				url: '/pages/index/index',
				fail: err => {
					console.log(err)
				}
			})
		},


		async updateHeadimg() {
			uni.navigateTo({
				url: `/pages/other/headimg-list/headimg-list?active_id=${this.id}`,
				events: {
					newImg: async obj => {
						if (!obj?.src) return
						uni.showLoading({
							title: '修改中...',
							mask: true
						})
						if (!obj.temp) {
							this.updateAttendDetail(obj.src)
							return false
						}
						const data = {
							temp_data: {
								path: obj.src
							},
							is_temp: 5
						}
						if (obj.size) data.temp_data.size = obj.size
						const headimg = await xwy_api.uploadOneImage(data)
						this.updateAttendDetail(headimg)
					}
				}
			})
		},

		toSend() {
			if (!this.is_joining) {
				this.joinActivity()
				return false
			}
			if (!this.checked) {
				this.xwy_api.alert('报名未审核，需管理员审核后才能送祝福')
				return false
			}
			if (!activity_tool.actionCheck({
				is_joining: this.is_joining,
				checked: this.checked,
				begin_time: this.detail.begin_time,
				end_time: this.detail.end_time
			})) return false

			this.popup_open = true
			this.$refs.send_wish_popup.open({
				callbacks: {
					success: () => {
                        this.popup_open = false
                        if (this.taskActive) {
                            // 从任务闯关活动的任务跳转进来，每次只能发一条祝福语
                            this.notShowPublishButton = true
                            this.getOpenerEventChannel().emit('success')
                        }

                        // OA开启自动加精
                        if (this.detail.rank_set?.['cloud_wish_public']) {
                            this.load_page = 1
                            this.getWishWordsList()
                        }
                    },
					close: () => this.popup_open = false
				},
				show_ad: !this.detail.rank_set?.closed_AD,
                point_id: this.point_id || null,
                taskActive: this.taskActive
			})
		},

        joinActivity() {
            if (this.loading) return

            this.join_popup_show = true
        },

		updateAttendDetailShow() {
			this.update_attend_details = true
			this.join_popup_show = true
		},

		mustValueChange(e, index) {
			this.must_submit[index].value = this.must_submit[index].options[e.detail.value].text
			this.$forceUpdate()
		},


        cancelJoin() {
			this.join_popup_show = false
			this.update_attend_details = false
        },

        joinAjax() {
			if (this.update_attend_details) {
				this.updateAttendDetail()
				return false
			}



            this.joining()
        },



		getMustSubmitData() {
			const must_submit = JSON.parse(JSON.stringify(this.must_submit))
			for (let i = 0; i < must_submit.length; i++) {
				const v = must_submit[i]
				v.options && delete v.options
				if (v.rules === 1 && v.value === '') {
					let tips = '输入'
					if (v.types === 2) tips = '选择'
					uni.showToast({
						title: `请${tips}${v.title}`,
						mask: true,
						icon: v.title.length <= 4 ? 'error' : 'none'
					})
					return false
				}
			}
			console.log(must_submit)
			let must_submit_str = JSON.stringify(must_submit)
			must_submit_str = must_submit_str.replace(/·/g, '-')
			return base64.encode(must_submit_str)
		},

		updateAttendDetail(headimg) {
			const data = {
				access_token: app.globalData.access_token,
				active_id: this.id
			}

			if (headimg) data.headimg = headimg

			const must_submit = this.getMustSubmitData()
			if (must_submit === false) return false
			data.must_submit = must_submit

			uni.showLoading({
				title: '修改中...',
				mask: true
			})

			xwy_api.ajax({
				url: 'front.flat.sport_step.user/update_attend_details',
				data,
				success: res => {
					if (!res.status) {
						uni.showModal({
							title: '提示',
							content: res.info || '修改失败',
							showCancel: false
						})
					}

					uni.showToast({
						title: res.info || '修改成功'
					})

					this.cancelJoin()
					this.getDetail()
				}
			})
		},

        joining() {

            const data = {
                active_id: this.id,
				access_token: app.globalData.access_token
            }


			if (this.must_submit && this.must_submit.length) {
				const must_submit = this.getMustSubmitData()
				if (must_submit === false) return false
				data.must_submit = must_submit
			}

			data.nickname = this.username

            this.loading = true
            this.$uni.showLoading('报名中...')


			xwy_api.ajax({
				url: 'front.flat.sport_step.user/submit_attend_active',
				data,
				success: res => {
					this.loading = false
					uni.hideLoading()
					console.log('报名活动', res)
					if (!res.status) {
					    uni.showModal({
					        title: '报名失败',
					        content: res.info || '报名失败',
					        showCancel: false
					    })
					    return false
					}

					this.join_popup_show = false
					uni.showToast({
					    title: res.info || '报名成功',
					    icon: 'success'
					})

					setTimeout(() => {
						uni.showLoading({
						    mask: true
						})
						this.show_send_popup = true
						this.getDetail()
					}, 1000)
				}
			})
        },


		toRun() {
			if (!this.is_joining) {
				uni.showToast({
					title: '还未参与活动',
					icon: 'error'
				})
				return false
			}

			if (this.detail.conf.active.enter_types === 3 && !this.checked) {
				uni.showToast({
					title: '报名未审核',
					icon: 'error'
				})
				return false
			}

			uni.navigateTo({
				url: './run?id=' + this.id
			})
		},


		toTopList() {
			let url = `/pages/ranking-list/ranking-list?id=${this.id}`
            if (this.taskActive) url += '&task_active=1'

            this.$uni.navigateTo(url)
		},

        showActiveSharePopup() {
            this.$refs.activeShare.open({
                page: 'pages/cloud_wish/user/detail',
                scene: 'id=' + this.detail.id,
                qrcode_logo: this.detail.rank_set?.closed_AD && this.detail.conf?.active?.qrcode_logo
            })
        },
       

        copy(data, hide_toast = false) {
            uni.setClipboardData({
                data,
                success() {
					if (hide_toast) {
						uni.hideToast();
						return false;
					}
                    uni.showToast({
                        title: '复制成功',
                        icon: 'none',
                        duration: 500
                    })
                }
            })
        },

        uniPopupClose(ref) {
			this.popup_open = false
            this.$refs[ref].close()
        },

        uniPopupOpen(ref) {
			this.popup_open = true
            this.$refs[ref].open()
        }

    }
}
</script>

<style scoped lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 80px;
	background-color: #fef7eb;
}



.logo {
    display: block;
    width: 100vw;
    height: auto;
}

.share-btn {
    padding: 0;
    margin: 0;
    border: none !important;
    width: 40px;
    min-width: 40px;
    height: 40px;
    line-height: 40px;
}

.bdb-10 {
    border-bottom: 10px solid #f8f8f8;
}

.icon-list {
	position: fixed;
	top: 10px;
	right: 10px;
	z-index: 99;
}

.icon-item {
    width: 40px;
	height: 40px;
	line-height: 40px;
	border-radius: 50%;
	background-color: rgba(0, 0, 0, .3);
	margin-bottom: 5px;
}


.headimg {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}


.join-btn {
    width: 250px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 20px;
}

.join-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, .7);
    z-index: 9999;
}

.join-popup-c {
    border-radius: 10px;
    overflow: hidden;
}


.join-input {
    margin: 10px;
    padding: 10px;
    border: 1px solid #bbb;
    border-radius: 5px;
    width: 70vw;
    max-width: 400px;
}

.join-popup-btns {
    border-top: 1px solid #eee;
}

.join-popup-btn-cancel, .join-popup-btn-confirm {
    width: 50%;
    box-sizing: border-box;
    line-height: 44px;
}

.join-popup-btn-cancel {
    border-right: 1px solid #eee;
}


.uni-popup-info {
    position: relative;
    width: 280px;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    z-index: 999;
    left: 50%;
    bottom: -5px;
	margin-left: -12px;
}

.detail-popup {
    width: 95vw;
	padding-bottom: 15px;
}

.success {
	position: fixed;
	z-index: 99;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background-color: rgba(0, 0, 0, .7);
}
.success .main {
	width: 300px;
	border-radius: 10px;
	overflow: hidden;
}
.success .close {
	margin: 20px 50px;
	margin-bottom: 0;
	line-height: 34px;
	border-radius: 17px;
}

.wish-words-list {
	background-color: #fef7eb;
}

.wish-words-item {
	margin: 10px;
	padding: 10px;
}
$headimgSize: 50px;
.wish-words-item-headimg {
	width: $headimgSize;
	min-width: $headimgSize;
	height: $headimgSize;
	border-radius: 50%;
	display: block;
}
.wish-words-item-msg {
	background-color: #fdeecf;
	border-radius: 10px;
	width: 100%;
	margin-left: 10px;
}
.wish-bubble-item {
	position: fixed;
	top: 0;
	bottom: 0;
	border-radius: 50%;
	width: 51px;
	height: 51px;
	z-index: 99;
}
.bubble {
	position: absolute;
	top: 0;
	left: 0;
	width: 50px;
	height: 50px;
	background: transparent;
	border: 1px solid #fff;
	box-shadow: inset 0 0 10px #fff;
	border-radius: 50%;
	z-index: 9;
}

.bubble::before{
	content: '';
	position: absolute;
	width: 100%;
	height: 100%;
	transform: scale(0.25) translate(-70%, -70%);
	background: radial-gradient(#fff, transparent);
	border-radius: 50%;
}
.wish-bubble-headimg {
	width: 40px;
	height: 40px;
	display: block;
	border-radius: 50%;
	opacity: .8;
}
.to-send {
	position: fixed;
	left: 50%;
	bottom: 10vh;
	margin-left: -60px;
	background-color: #ee8262;
	opacity: .7;
	color: #fff;
	line-height: 44px;
	width: 120px;
	border-radius: 10px;
	z-index: 99;
}
</style>
