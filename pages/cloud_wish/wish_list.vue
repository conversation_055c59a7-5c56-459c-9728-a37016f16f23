<template>
	<view class="page bg-background" :style="{'paddingTop': topToolShow ? '50px' : '0'}">
		<view v-if="topToolShow" class="top-bar flex-row bg-white">
			<view
				class="top-tool-item color-title"
				:class="{'top-tool-active': look_is_checked_type === 2}"
				@click="look_is_checked_type = 2"
			>未审核</view>
			<view
				class="top-tool-item color-title"
				:class="{'top-tool-active': look_is_checked_type === 0}"
				@click="look_is_checked_type = 0"
			>已审核</view>
		</view>

		<view class="flex-kai" style="line-height: 40px;">
			<view style="width: 85px;"></view>
			<view v-if="list_count" class="color-sub font14">共{{list_count}}条祝福语</view>
			<view style="width: 85px;">
				<navigator v-if="look_type === 'all'" :url="'./wish_list?look_type=myself&id=' + activity_id">
					<text class="color-primary font14">我的祝福语</text>
					<uni-icons type="forward" size="14" color="#2d8cf0"/>
				</navigator>
			</view>
		</view>
		<view class="list">
			<template v-for="(item, index) in list">
				<xwy-ad v-if="!close_ad && index === 0" :ad_type="66"></xwy-ad>
				<view class="wish-words-item bg-white flex-row" :key="item.id">
					<view v-if="look_type === 'admin'" class="flex-all-center pr10">
						<radio :checked="item.choose" @click="itemChooseChange(index)"></radio>
					</view>
					<view style="width: 100%;">
						<navigator :url="'./user/wish_detail?id=' + item.id">
							<view class="flex-kai">
								<view>
									<view class="user-info flex-row">
										<image
											class="wish-words-item-headimg"
											:src="item.headimg"
											mode="aspectFill"
										/>
										<view class="nickname font16 color-title">
											<text>{{item.nickname}}</text>
										</view>
									</view>
								</view>
								<view class="font14">
									<template v-if="look_type !== 'all' && !notCheck">
										<text v-if="item.checked" class="color-success">已审核</text>
										<text v-else class="color-sub">未审核</text>
									</template>
									<template v-if="item.is_public">
										<text class="pl10"></text>
										<uni-tag size="small" :inverted="true" text="精" type="error" />
									</template>
								</view>
							</view>
							<view class="wish-words-item-msg">
								<view class="color-content ellipsis--l3">
									<text space="nbsp">{{item.content}}</text>
								</view>
								<view class="text-right font14 color-sub pt10">{{item.create_time}}</view>
							</view>
						</navigator>

						<view v-if="look_type !== 'myself'" class="list-item-bar flex-kai">
							<view></view>
							<view class="flex-row font14 color-sub">
								<template v-if="look_type === 'admin'">
									<view v-if="!notCheck" class="pr10" hover-class="navigator-hover"
                                          @click="itemChange(index, 'checked')">
                                        改为{{item.checked ? '未' : '已'}}审核</view>
									<view
										v-if="look_is_checked_type === 0 || notCheck"
										class="pl10 pr10"
										hover-class="navigator-hover"
										@click="itemChange(index, 'nb')"
									>{{item.is_public ? '取消' : ''}}加精</view>
                                    <view class="pl10" hover-class="navigator-hover"
                                          @click="itemChange(index, 'del')">删除
                                    </view>
								</template>

								<view v-if="look_type === 'all'" hover-class="navigator-hover"
                                      @click="like(item.id)">给ta点赞
                                </view>
							</view>
						</view>
					</view>
				</view>
				<xwy-ad v-if="!close_ad && (index + 1) % 5 === 0" :ad_type="66"></xwy-ad>
			</template>
		</view>


		<view v-if="look_type === 'admin' && list.length" class="bottom-bar flex-kai bg-white">
			<view class="p10">
				<radio :checked="choose_all" @click="setChooseAll"></radio>
			</view>
			<view class="bottom-tools flex-row font14 color-sub">
				<view class="bottom-tool-item" hover-class="navigator-hover" @click="batchOperation">批量操作</view>
			</view>
		</view>

		<view v-if="!list.length && !in_load" class="text-center" style="padding-top: 10vh;">
		    <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
		    <view class="color-sub">暂无祝福语</view>
		</view>

		<uni-load-more v-if="in_load" status="loading"></uni-load-more>
		<uni-load-more
			v-if="is_lastpage && list.length > 5"
			status="noMore"
			:contentText="{contentnomore: '我是有底线的'}"
		></uni-load-more>
		<uni-load-more v-if="!is_lastpage && !in_load" status="more"></uni-load-more>

		<uni-popup ref="like_popup" type="center">
		    <view class="like_popup bg-white text-center">
		        <view class="popup-close" @click="uniPopupClose('like_popup')">
		            <uni-icons type="close" size="24" color="#ffffff"/>
		        </view>
		        <view class="bg-primary color-white" style="padding: 20px; padding-top: 30px;">
		            <icon :type="like_status ? 'success' : 'clear'" size="80" color="#ffffff"></icon>
		            <view class="font18">点赞{{like_status ? '成功' : '失败'}}</view>
		        </view>
		        <view class="color-info bg-white" style="padding: 20px;">
		            <view class="color-content text-center font14" style="padding-bottom: 20px;">
		                {{ like_info }}
		            </view>
		        </view>
				<xwy-ad v-if="popup_show && !close_ad" :ad_type="3"></xwy-ad>
		    </view>
			<view class="pt5">
				<xwy-ad v-if="popup_show && !close_ad" :ad_type="66"></xwy-ad>
			</view>
		</uni-popup>
	</view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'

export default {
	data() {
		return {
			activity_id: '',
			in_load: true,
			look_type: 'all',
			list: [],
			load_page: 1,
			is_lastpage: false,
			list_count: 0,
			choose_all: false,
			look_is_checked_type: 2,  // 默认不传是必需审核通过才显示 【2】只查看未审核通过的
			like_status: '',
			like_info: '',
			popup_show: false,
			close_ad: false,
            notCheck: false
		}
	},

	watch: {
		load_page: function(val) {
			if (val === 1) {
				this.list = []
				this.is_lastpage = false
			}
		},
		look_is_checked_type: function(val) {
			this.load_page = 1
			this.getList()
		}
	},

    computed: {
        topToolShow() {
            return this.look_type === 'admin' && !this.notCheck
        }
    },

	onReachBottom() {
		!this.is_lastpage && !this.in_load && this.getList()
	},

	onLoad(e) {
		if (e.look_type) this.look_type = e.look_type
		this.setNavigationBarTitle()
		this.activity_id = e.id
		login.uniLogin(err => {
			if (err && err.errMsg) {
			    uni.hideLoading()
			    uni.showModal({
			        title: err.errTitle || '提示',
			        content: err.errMsg,
			        showCancel: false
			    })
			    return false
			}

			this.getActivityData()
		})
	},

	methods: {
		setNavigationBarTitle() {
			const type = this.look_type
			let title = '祝福语'
			if (type === 'myself') title = '我的祝福语'
			if (type === 'admin') title = '祝福语审核'
			uni.setNavigationBarTitle({
				title
			})
		},

		async getActivityData() {
			let activity_detail = app.globalData['activity_detail']

			if (!activity_detail || activity_detail.active_id !== this.activity_id) {
				const res = await this.xwy_api.request({
					url: 'front.flat.sport_step.active_list/active_details',
					data: {
						active_id: this.activity_id
					}
				})

				activity_detail = res.data.active_details

			}

            console.log(activity_detail);

            if (activity_detail?.rank_set?.closed_AD) this.close_ad = true
            if (activity_detail?.conf?.active?.not_check) this.notCheck = true

			await this.getList()
		},

		async getList() {
			this.in_load = true

			if (this.load_page === 1) {
				this.is_lastpage = false
				this.list = []
			}

			const data = {
				active_id: this.activity_id,
				page: this.load_page,
				perpage: 10
			}
			// if (this.look_type === 'all') data.is_public = 1
			if (this.look_type === 'myself') data.only_myself = 1
			if (this.look_type === 'admin' && this.look_is_checked_type && !this.notCheck) {
                data.is_checked = this.look_is_checked_type
            }

			const res = await this.xwy_api.request({
				url: 'front.flat.active.cloud_wish.user.wish/wish_list',
				data
			})

			this.in_load = false
			this.load_page++

			const res_data = res?.data?.wish_list
			if (!res_data) {
				this.is_lastpage = true
				return false
			}
			const list = res_data.data || []
			list.forEach(v => {
				v.choose = false
				v.nickname = ''
				v.headimg = ''
				if (v.user_attend_details) {
					const u_d = v.user_attend_details
					if (u_d.headimg) v.headimg = u_d.headimg
					if (u_d.must_submit?.length && u_d.must_submit[0].value) v.nickname = u_d.must_submit[0].value
					delete v.user_attend_details
				}
			})
			this.list = [...this.list, ...list]
			this.look_type === 'admin' && this.checkChooseAll()
			this.is_lastpage = res_data.is_lastpage
			this.list_count = res_data.total
		},

		itemChooseChange(index) {
			this.list[index].choose = !this.list[index].choose
			this.checkChooseAll()
		},

		checkChooseAll() {
			const list = this.list
			for (let i = 0; i < list.length; i++) {
				if (!list[i].choose) {
					this.choose_all = false
					return false
				}
			}
			this.choose_all = true
		},

		setChooseAll() {
			let choose_all = !this.choose_all
			this.choose_all = choose_all
			this.list.forEach(v => v.choose = choose_all)
		},

		itemChange(index, type) {
			const item = this.list[index]
			let act_types = ''
			if (type === 'checked') act_types = item.checked === 1 ? 2 : 1
			if (type === 'nb') act_types = item.is_public === 1 ? 5 : 4
			if (type === 'del') act_types = 3
			const nickname_tips = item.nickname ? (item.nickname + '的') : '该'
			const act_type_list = [
				{types: 1, title: `确定将${nickname_tips}祝福语改为已审核?`},
				{types: 2, title: `确定将${nickname_tips}祝福语改为未审核?`},
				{types: 3, title: `确定删除${nickname_tips}祝福语?`},
				{types: 4, title: `确定将${nickname_tips}祝福语加精?`},
				{types: 5, title: `确定将${nickname_tips}祝福语取消加精?`}
			]
			uni.showModal({
				title: '提示',
				content: act_type_list.find(v => v.types === act_types).title,
				success: res => {
					res.confirm && this.wishChange(item.id, act_types)
				}
			})
		},

		getChooseId() {
			const list = []
			this.list.forEach(v => v.choose && list.push(v.id))
			return list.join(',')
		},

		batchOperation() {
			const ids = this.getChooseId()
			if (!ids) {
				uni.showToast({
					title: '请选择需要操作的祝福语',
					icon: 'none'
				})
				return false
			}
			const opt_list = [
				{types: 3, title: `删除`}
			]
            if (!this.notCheck) opt_list.unshift({
                types: this.look_is_checked_type === 2 ? 1 : 2,
                title: `改为${this.look_is_checked_type === 2 ? '已' : '未'}审核`
            })
			if (this.look_is_checked_type === 0 || this.notCheck) {
				opt_list.push({types: 4, title: `加精`})
				opt_list.push({types: 5, title: `取消加精`})
			}
			const itemList = []
			opt_list.forEach(v => itemList.push(v.title))
			uni.showActionSheet({
				title: '批量操作',
				itemList,
				success: res => {
					const tap_item = opt_list[res.tapIndex]
					uni.showModal({
						title: '提示',
						content: `确定将选中的祝福语${tap_item.title}`,
						success: res => {
							res.confirm && this.wishChange(ids, tap_item.types)
						}
					})
				}
			})
		},

		async wishChange(ids, act_types) {
			const loding_type_list = [
				{types: 1, title: '修改中...'},
				{types: 2, title: '修改中...'},
				{types: 3, title: '删除中...'},
				{types: 4, title: '加精...'},
				{types: 5, title: '取消加精...'}
			]
			uni.showLoading({
				title: loding_type_list.find(v => v.types === act_types).title,
				mask: true
			})
			const res = await this.xwy_api.request({
				url: 'front.flat.active.cloud_wish.admin.wishSet/batch_change_user_submit_wish',
				data: {
					access_token: app.globalData.access_token,
					active_id: this.activity_id,
					ids,
					act_types
				}
			})

			if (!res?.status) {
				this.xwy_api.alert(res?.info || '操作失败')
				return false
			}

			const tips = res.info || '操作成功'
			uni.showToast({
				title: tips,
				icon: tips.length <= 7 ? 'success' : 'none'
			})

			this.load_page = 1
			this.getList()
		},

		async like(id) {
			uni.showLoading({
				title: '点赞中...',
				mask: app.globalData.evn_version === 'trial' ? false : true
			})

			const res = await this.xwy_api.request({
				url: 'front.flat.active.cloud_wish.user.wish/like_for_wish_details',
				data: {
					access_token: app.globalData.access_token,
					id: id
				}
			})

			uni.hideLoading()

			this.like_status = res?.status || 0
			this.like_info = res?.info || '点赞失败'
			this.uniPopupOpen('like_popup')
		},

		uniPopupOpen(ref) {
		    this.$refs[ref].open()
			this.popup_show = true
		},

		uniPopupClose(ref) {
		    this.$refs[ref].close()
			this.popup_show = false
		},
	}
}
</script>

<style scoped lang="scss">
.page {
	min-height: 100vh;
	box-sizing: border-box;
	padding-bottom: 70px;
}
.top-bar {
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	z-index: 999;
}
.top-tool-item {
	width: 50%;
	text-align: center;
	height: 40px;
	line-height: 40px;
}
.top-tool-active {
	border-bottom: 2px solid #2d8cf0;
	color: #2d8cf0;
}
.wish-words-item {
	margin: 10px;
	padding: 10px;
	border-radius: 10px;
}

$headimgSize: 30px;
.wish-words-item-headimg {
	width: $headimgSize;
	min-width: $headimgSize;
	height: $headimgSize;
	border-radius: 50%;
	display: block;
}
.nickname {
	line-height: $headimgSize;
	padding-left: 5px;
}
.wish-words-item-msg {
	padding-top: 10px;
}
.list-item-bar {
	border-top: 1px solid #eee;
	margin-top: 10px;
	padding: 10px 0;
}
.bottom-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	padding-bottom: 15px;
}

.bottom-tools {
	padding: 10px;
}
.bottom-tool-item {
	height: 30px;
	margin: 0 5px;
	border: 1px solid #eee;
	line-height: 28px;
	border-radius: 15px;
	padding: 0 5px;
	min-width: 48px;
	text-align: center;
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
	.top-bar, .bottom-bar {
		width: 500px !important;
		left: calc(((100vw - 500px) / 2) - 10px) !important;
	}
}
/* #endif */
.like_popup {
    width: 300px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}
.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}
</style>
