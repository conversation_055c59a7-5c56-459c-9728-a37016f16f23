<template>
	<view>
        <view v-if="show_one_day" class="flex-kai" style="border-bottom: 1px solid #eee;">
            <view class="p10 color-sub">
                <uni-dateformat :date="look_date" format="yyyy年M月d日"/>
                <text class="color-light-primary pl10 pr10">当日积分: {{ integral_day }}</text>
            </view>
            <view class="p10">
                <uni-datetime-picker type="date" end="2038-12-31" :value="look_date" @change="lookDateChange">
                    <text class="iconfont icon-calendar-1 color-disabled"></text>
                    <text class="color-sub" style="padding: 0 2px;">筛选</text>
                    <text class="iconfont icon-more color-disabled"></text>
                </uni-datetime-picker>
            </view>
        </view>

		<view v-if="!in_load && list_count" class="text-center font14 color-sub p10" @click="changeGodModel">
            共{{ list_count }}条{{ unit }}记录
        </view>

		<view class="record-list">
			<view class="record-item flex-kai" v-for="(item, index) in list" :key="index">
				<view>
					<view class="color-title font18">{{item.memo}}</view>
					<view class="color-sub font14">
                        <text>{{item.create_time}}</text>
                        <text v-if="god_model" class="pl10">{{ item.id }}</text>
                    </view>

                    <view v-if="god_model" class="pt5 color-sub font14" @click="copyBindId(item)">
                        {{ item.logs_types || 0 }}.{{ item.bind_id || 0 }}.{{ item.bind_active_id_str || 0 }}
                    </view>
				</view>
				<view class="flex-all-center">
					<view class="color-warning font24">{{item.integral_num}}</view>
				</view>
			</view>
		</view>

		<view v-if="!list.length && !in_load" class="text-center" style="padding-top: 10vh;">
		    <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
		    <view class="color-sub">
                <uni-dateformat class="pr5" v-if="show_one_day" :date="look_date" format="yyyy年M月d日"/>
                无{{ unit }}记录
            </view>
		</view>

		<view v-if="in_load" :style="{paddingTop: load_page === 1 ? '30vh' : '0'}">
            <uni-load-more status="loading"></uni-load-more>
        </view>
		<uni-load-more v-if="is_lastpage && list.length > 8" status="noMore"></uni-load-more>
		<uni-load-more v-if="!is_lastpage && !in_load" status="more"></uni-load-more>

	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'

	export default {
		data() {
			return {
				in_load: true,
				list: [],
				load_page: 1,
				is_lastpage: false,
				list_count: 0,
				unit: '',
                show_one_day: false,
                integral_day: 0,
                look_date: null,
                god_model: false
			}
		},

		onLoad(e) {
			this.active_id = e.active_id
			this.unit = e.unit || '金币'
            if (e.userid) this.userid = e.userid

            if (e.show_one_day) {
                this.show_one_day = true
                this.look_date = this._utils.getDay(0, true)
            }

			uni.setNavigationBarTitle({
				title: this.unit + '记录'
			})

			login.uniLogin(err => {
				if (err?.errMsg) {
					uni.hideLoading()
					xwy_api.alert(err.errMsg, {title: err.errTitle, success() {uni.navigateBack()}})
					return false
				}

				this.getList()
			})
		},


		onReachBottom() {
			!this.in_load && !this.is_lastpage && this.getList()
		},

		methods: {
            changeGodModel() {
                this.god_model_timeout && clearTimeout(this.god_model_timeout)
                this.god_model_click_count = this.god_model_click_count || 0
                this.god_model_click_count++
                this.god_model_timeout = setTimeout(() => {
                    this.god_model_click_count = 0
                    clearTimeout(this.god_model_timeout)
                }, 2000)
                if (this.god_model_click_count >= 3) {
                    this.god_model = !this.god_model
                    this.god_model_click_count = 0
                    clearTimeout(this.god_model_timeout)
                }
            },

            lookDateChange(date) {
                if (date === this.look_date) return
                this.look_date = date
                this.load_page = 1
                this.$nextTick(() => this.getList())
            },

			async getList() {
				this.in_load = true
				if (this.load_page === 1) {
					this.list = []
                    this.list_count = 0
                    this.integral_day = 0
					this.is_lastpage = false
				}

                const data = {
                    active_id: this.active_id,
                    page: this.load_page,
                    perpage: 20
                }
                if (this.userid) {
                    data.userid = this.userid
                    // data.get_user_details = 1
                }
                if (this.show_one_day) {
                    data.date = this.look_date
                    data.total_integral = 1
                }

				const res = await xwy_api.request({
					url: 'front.flat.sport_step.userActiveIntegral/active_integral_list',
					data
				})

				this.in_load = false

				this.load_page++

				if (res?.data?.integral_list?.data?.length) {
					const list = res.data.integral_list.data
					list.forEach(v => {
						v.memo = v.memo.replace(/积分/g, this.unit)
					})
					this.is_lastpage = res.data.integral_list.is_lastpage
					this.list_count = res.data.integral_list.total
					this.list = [...this.list, ...list]
				} else {
					this.is_lastpage = true
				}

                if (data.page === 1 && this.show_one_day) this.integral_day = res?.data?.total_integral || 0
			},

            copyBindId(item) {
                this.$uni.setClipboardData(`${ item.logs_types || 0 }.${ item.bind_id || 0 }.${ item['bind_active_id_str'] || 0 }`, '已复制')
            }
		}
	}
</script>

<style>
.record-item {
	padding: 20px;
}

</style>
