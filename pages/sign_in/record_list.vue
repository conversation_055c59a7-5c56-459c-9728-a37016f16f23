<template>
	<view>
		<view v-if="!in_load && list_count" class="text-center font14 color-sub">共{{list_count}}条签到记录</view>
		<view class="record-list">
			<view class="record-item p10 color-content" v-for="(item, index) in list" :key="index">
				{{item.create_time}}
			</view>
		</view>

		<view v-if="!list.length && !in_load" class="text-center" style="padding-top: 10vh;">
		    <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
		    <view class="color-sub">暂无签到记录</view>
		</view>

		<uni-load-more v-if="in_load" status="loading"></uni-load-more>
		<uni-load-more v-if="is_lastpage && list.length > 8" status="noMore" :contentText="{contentnomore: '我是有底线的'}"></uni-load-more>
		<uni-load-more v-if="!is_lastpage && !in_load" status="more"></uni-load-more>

	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'

	export default {
		data() {
			return {
				in_load: true,
				list: [],
				load_page: 1,
				is_lastpage: false,
				list_count: 0
			}
		},

		onLoad(e) {
			this.active_id = e.active_id


			login.uniLogin(err => {
				if (err?.errMsg) {
					uni.hideLoading()
					xwy_api.alert(err.errMsg, {title: err.errTitle, success() {uni.navigateBack()}})
					return false
				}

				this.getList()
			})
		},


		onReachBottom() {
			!this.in_load && !this.is_lastpage && this.getList()
		},

		methods: {
			async getList() {
				this.in_load = true
				if (this.load_page === 1) {
					this.list = []
					this.is_lastpage = false
				}
				const res = await xwy_api.request({
					url: 'front.flat.sport_step.dailySign/user_sign_list',
					data: {
						access_token: app.globalData.access_token,
						active_id: this.active_id,
						page: this.load_page,
						perpage: 20
					}
				})

				this.in_load = false
				this.load_page++

				if (res?.data?.sign_list?.data?.length) {
					const list = res.data.sign_list.data
					this.is_lastpage = res.data.sign_list.is_lastpage
					this.list_count = res.data.sign_list.total
					this.list = [...this.list, ...list]
				} else {
					this.is_lastpage = true
				}
			}
		}
	}
</script>

<style>


</style>
