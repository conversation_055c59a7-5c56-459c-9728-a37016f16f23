<template>
	<view>
		<view class="user-info flex-row">
		    <image class="headimg" :src="headimg"/>
		    <view class="pl10" style="line-height: 30px;">
		        <view class="color-title font16">{{user_name}}</view>
		        <navigator :url="'./integral_record_list?active_id=' + active_id + '&unit=' + unit">
		            <text class="color-warning font16">{{user_integral}}</text>
		            <text class="color-sub font14"> {{unit}}</text>
		        </navigator>
		    </view>
		</view>

		<view class="check-in bg-white">
		    <view class="continuity-check-in">
		        <view class="text-center continuity">已连续签到</view>
		        <view class="day text-center">
		            <view class="text">
		                <text class="num">{{consecutive_sign_in_days || 0}}</text>
		                <text class="tian">天</text>
		            </view>
		        </view>

				<view v-if="!today_have_sign_in" class="text-center pt10 color-content">
					<text>今日签到可获得</text>
					<text>
						<template v-if="sign_type === 2">
							{{sign_circle_set[consecutive_sign_in_days].integral}}
						</template>
						<template v-else>{{sign_integral || 0}}</template>
					</text>
					<text>{{unit}}</text>
				</view>
		    </view>

		    <view v-if="sign_circle_set && sign_circle_set.length" class="check-block flex-kai">
				<view v-for="(item, index) in sign_circle_set" :key="index" class="item text-center">
					<view class="circle" :class="{'activate': index < consecutive_sign_in_days}">+{{item.integral}}</view>
					<view class="day">第{{index + 1}}天</view>
				</view>
		    </view>

		    <view class="flex-all-center" style="padding-top: 30px;">
				<button
				    class="check-in-btn bg-primary color-white"
					:disabled="today_have_sign_in"
				    @click="signIn"
				>
					<template v-if="today_have_sign_in">今日已</template>
					<template>签到</template>
				</button>
			</view>
            
            <template v-if="showAD">
                <xwy-ad :ad_type="3"/>
                <xwy-ad :ad_type="66"/>
            </template>
		</view>

		<view v-if="sign_in_record_list.length" class="record-list font14" style="padding-top: 30px;">
			<view class="flex-kai color-sub">
				<view class="pl10 pr10">最近签到记录</view>
				<navigator
					v-if="sign_in_record_list.length === 5"
					class="pl10 pr10"
					:url="'./record_list?active_id=' + active_id"
				>
					<text>查看更多</text>
					<uni-icons type="forward" color="#495060" size="14"/>
				</navigator>
			</view>
			<view class="record-item p10 color-sub" v-for="(item, index) in sign_in_record_list" :key="index">
				{{item.create_time}}
			</view>
		</view>

		<uni-popup ref="popup" type="center">
			<view class="popup bg-white text-center">
				<view class="color-white pb10 bg-primary">
					<uni-icons :type="sign_in_result.status === 1 ? 'checkbox' : 'close'" size="100" color="#ffffff"/>
					<view>签到{{sign_in_result.status === 1 ? '成功' : '失败'}}</view>
				</view>
				<view class="color-content" style="padding: 30px 10px;">{{sign_in_result.info}}</view>
			</view>
			<view class="pt5"></view>

			<view class="flex-all-center">
				<uni-icons type="close" color="#ffffff" size="34" @click="uniPopupClose('popup')"/>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'
	import utils from '@/utils/utils.js'

	export default {
		data() {
			return {
				active_id: '',
				sign_type: 0,
				sign_integral: 0,
				sign_circle_set: [],
				unit: '',
				user_integral: 0,
				headimg: '',
				user_name: '',
				sign_in_result: {
					status: 0,
					integral: 0,
					info: ''
				},
				consecutive_sign_in_days: 0,
				sign_in_record_list: [],
				today_have_sign_in: false,
                showAD: false
			}
		},

		onLoad(e) {
			this.active_id = e.active_id

			uni.showLoading({
				title: '加载中...'
			})

			login.uniLogin(err => {
				if (err?.errMsg) {
					uni.hideLoading()
					xwy_api.alert(err.errMsg, {title: err.errTitle, success() {uni.navigateBack()}})
					return false
				}

				this.getActivityDetail()
				this.getActivityUserDetail()
				this.getIntegralList()
			})
		},

		methods: {
			async getActivityDetail() {
				let activity_detail = app.globalData['activity_detail']
				app.globalData.activity_detail = null
				if (!activity_detail || activity_detail.active_id !== this.active_id) {
					const res = await xwy_api.request({
						url: 'front.flat.sport_step.active_list/active_details',
						data: {
							active_id: this.active_id
						}
					})

					if (!res?.data?.active_details) {
						uni.hideLoading()
						xwy_api.alert(res?.info || '活动详情获取失败', {success() {uni.navigateBack()}})
						return false
					}

					activity_detail = res.data.active_details
				}

                this.activeDetails = activity_detail

                if (!activity_detail.rank_set?.closed_AD) this.showAD = true

				uni.hideLoading()
				this.getActivitySignData(activity_detail)
			},

			getActivitySignData(activity_detail) {
				const daily_sign_data = activity_detail.conf?.active?.daily_sign
				if (!daily_sign_data) {
					xwy_api.alert('签到数据获取失败', {success() {uni.navigateBack()}})
					return false
				}


				this.unit = activity_detail.conf.active.integral?.unit || '金币'
				this.sign_type = daily_sign_data.types || 0
				this.sign_integral = daily_sign_data.integral || 0
				if (daily_sign_data.types === 2) this.sign_circle_set = daily_sign_data.circle_set || []
			},

			async getActivityUserDetail(reload = false) {
				let activity_user_detail = app.globalData.activity_user_detail
				app.globalData.activity_user_detail = null


				if (reload) activity_user_detail = null
				if (!activity_user_detail) {
					const res = await xwy_api.request({
						url: 'front.flat.sport_step.user/user_attend_details',
						data: {
							active_id: this.active_id
						}
					})

					activity_user_detail = res?.data?.user_details
				}
                
                if (!activity_user_detail) {
                    const {nickname, headimg} = app.globalData['userinfo'] || {}
                    if (nickname && headimg) {
                        this.user_name = nickname
                        this.headimg = headimg
                    }
                    return
                }

				this.user_integral = activity_user_detail.integral_left || 0
				this.consecutive_sign_in_days = activity_user_detail.daily_sign || 0
				this.headimg =  activity_user_detail.headimg || ''
				this.user_name = activity_user_detail.must_submit && activity_user_detail.must_submit[0] && activity_user_detail.must_submit[0].value || ''
			},


			async getIntegralList() {
				this.sign_in_record_list = []
				const res = await xwy_api.request({
					url: 'front.flat.sport_step.dailySign/user_sign_list',
					data: {
						active_id: this.active_id,
						page: 1,
						perpage: 5
					}
				})

				if (res?.data?.sign_list?.data?.length) {
					const list = res.data.sign_list.data
					this.sign_in_record_list = list
					const lastday_sign_in_time = list[0].create_time.slice(0, 10)
					const today = utils.getDay(0, true)
					if (lastday_sign_in_time === today) this.today_have_sign_in = true
				}
			},

            timeCheck() {
                const now = new Date().getTime()

                const begin_time = this.activeDetails.begin_time * 1000
                if (now < begin_time) {
                    this.$uni.showToast('活动未开始')
                    return false
                }

                const end_time = this.activeDetails.end_time * 1000
                if (now > end_time) {
                    this.$uni.showToast('活动已结束')
                    return false
                }

                return true
            },

			async signIn() {
                if (!this.timeCheck()) return false
                
				uni.showLoading({
					title: '签到中...'
				})
				const res = await xwy_api.request({
					url: 'front.flat.sport_step.dailySign/submit_daily_sign',
					data: {
						active_id: this.active_id
					}
				})

				uni.hideLoading()

				res && res.status && this.signSuccess()

				this.sign_in_result.status = res?.status || 0
				this.sign_in_result.integral = res?.data?.integral || 0
				if (res?.info) this.sign_in_result.info = res.info.replace(/积分/g, this.unit)

				this.uniPopupOpen('popup')
			},

            signSuccess() {
                this.getActivityUserDetail(true)
                this.getIntegralList()
                this.getOpenerEventChannel().emit('success')
            },

			uniPopupClose(ref) {
				this.$refs[ref].close()
			},

			uniPopupOpen(ref) {
				this.$refs[ref].open()
			}
		}
	}
</script>

<style>

.user-info {
    padding: 10px;
    border-bottom: 10px solid #f8f8f8;
}
.headimg {
    width: 60px;
    min-width: 60px;
    height: 60px;
    border-radius: 50%;
    display: block;
}

.check-in .continuity-check-in {
    padding-bottom: 20px;
    border-bottom: 1px solid #ddd;
}
.check-in .continuity-check-in .continuity {
    font-size: 40rpx;
    padding: 20rpx 0;
}
.check-in .continuity-check-in .day {
    width: 300rpx;
    height: 300rpx;
    margin: 0 auto;
    background: url(http://www.xinweiyun.com/weixin/public/img/answer_sign_in_day.png) no-repeat center / 100%;
}
.check-in .continuity-check-in .day .text {
    font-size: 52rpx;
    padding-top: 110rpx;
    color: #6a576d;
}
.check-in .continuity-check-in .day .text .num {
    font-size: 100rpx;
    font-weight: 600;
}
.check-in .continuity-check-in .day .text .num,
.check-in .continuity-check-in .day .text .tian {
    vertical-align: baseline;
}
.check-in .check-block {
    padding: 20rpx 10rpx;
}
.check-in .check-block .item .circle {
    width: 90rpx;
    height: 90rpx;
    line-height: 90rpx;
    border: 1px solid #000;
    border-radius: 50%;
    box-sizing: border-box;
    font-size: 28rpx;
}
.check-in .check-block .item .activate {
    border: none;
    background-color: #e20f04;
    color: #fff;
}
.check-in .check-block .item .day {
    font-size: 28rpx;
    padding-top: 5px;
}

.check-in .check-in-btn {
    width: 200px;
    letter-spacing: 2px;
}
.check-in .check-in-btn::after {
	border: none;
}

.popup {
	width: 300px;
	border-radius: 10px;
	overflow: hidden;
}
</style>
