<template>
    <view>

        <picker :mode="pickerOptions.mode" :range="pickerOptions.range" range-key="name" 
                :value="pickerValue" @change="pickerChange">
            <view v-if="!team.id" class="color-sub">请选择分组({{ teamMust ? '必' : '选' }}填项)</view>
            <view v-else>
                <text>{{ team.name || team.id || '' }}</text>
                <text v-if="pickerOptions.mode === 'multiSelector'">
                    -{{ team_group.name || team_group.id || '' }}
                </text>
            </view>
        </picker>
    </view>
</template>

<script>
export default {
    name: "form-submit-team",
    props: ['activeId', 'teamInfo', 'teamGroupOpen', 'teamMust'],
    data() {
        return {
            pickerOptions: {
                mode: 'selector',
                range: []
            },
            team: {
                id: '',
                name: ''
            },
            team_group: {
                id: '',
                name: ''
            }
        }
    },
    
    computed: {
        pickerValue() {
            const {team_list = [], team_category_list = []} = this
            let team_index = team_list.findIndex(item => item.id === this.team.id)
            if (team_index === -1) team_index = 0
            if (this.pickerOptions.mode === 'selector') return team_index
            let team_group_index = team_category_list.findIndex(item => item.id === this.team_group.id)
            if (team_group_index === -1) team_group_index = 0

            return [team_index, team_group_index]
        }
    },

    mounted() {
        this.getActivityTeamSet()
    },

    methods: {
        async getActivityTeamSet() {
            if (this.teamInfo) {
                const {team = {id: '', name: ''}, team_group = {id: '', name: ''}} = this.teamInfo
                this.team = team
                this.team_group = team_group
            }
            
            await this.getTeamGroup()
            await this.getTeamList()
            this.setTeamInfoPickerOptions()
        },
        
        async getTeamGroup() {
            if (!this.teamGroupOpen) return
            const res = await this.xwy_api.getCategoryList({
                active_id: this.activeId,
                types: 19,
                page: 1,
                perpage: 100
            })
            const list = res?.data?.category_list?.data
            if (list?.length) this.team_category_list = list.map(item => {
                return {
                    id: item.id,
                    name: item.name
                }
            })
        },
        
        async getTeamList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/team_group_list',
                data: {
                    active_id: this.activeId,
                    page: 1,
                    perpage: 100
                }
            })
            const team_list = res?.data?.['team_group_list']?.data
            if (team_list?.length) this.team_list = team_list.map(item => {
                return {
                    id: item.id,
                    name: item.name
                }
            })
        },

        setTeamInfoPickerOptions() {
            const {team_category_list, team_list} = this
            if (!team_list) return []
            let range = team_list
            let mode = 'selector'
            if (team_category_list?.length) {
                mode = 'multiSelector'
                range = [team_list, team_category_list]
            }
            this.pickerOptions = {
                mode,
                range
            }
        },

        pickerChange(e) {
            const value = e.detail.value
            if (this.pickerOptions.mode === 'multiSelector') {
                this.team.id = this.team_list[value[0]].id
                this.team.name = this.team_list[value[0]].name
                this.team_group.id = this.team_category_list[value[1]].id
                this.team_group.name = this.team_category_list[value[1]].name
            } else {
                this.team.id = this.team_list[value].id
                this.team.name = this.team_list[value].name
            }
            
            this.$emit('change', {team: this.team, team_group: this.team_group})
        }
    }
}
</script>

<style lang="scss">

</style>