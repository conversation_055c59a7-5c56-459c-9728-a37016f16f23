<template>
    <view>
        <view v-if="loading" class="text-center" style="padding-top: 30vh;">
            <load-ani/>
        </view>

        <view v-if="project_list.length" class="top-count flex-kai">
            <view class="p10 color-sub font14">共{{ project_list.length }}个项目</view>
            <view v-if="!choose" class="p10" hover-class="navigator-hover" @click="goAddProject(null)">
                <text class="color-primary font14">添加项目</text>
                <uni-icons type="forward" color="#2d8cf0" size="14"/>
            </view>
        </view>

        <view>
            <view class="project-item" v-for="item in project_list" :key="item.id">
                <view class="color-title pb5">{{ item.name }}</view>
                <view class="flex-kai color-content">
                    <view>
                        <text class="pr5">金额:</text>
                        <text class="color-warning">
                            <template v-if="item.amount">{{ item.amount.toFixed(2) }}</template>
                            <template v-else>0</template>
                        </text>
                        <text class="color-sub font12">元</text>
                    </view>
                    <view>
                        <template v-if="choose">剩余: {{ item['left_num'] }}/{{ item.num }}</template>
                        <template v-else>已购: {{ item['buy_num'] }}/{{ item.num }}</template>
                    </view>
                </view>

                <!--<view class="color-content">排序: {{ item.sort_num }}</view>-->
                <view class="option-bar flex-kai">
                    <view></view>
                    <view class="flex-row">
                        <template v-if="choose">
                            <view class="option-item" hover-class="navigator-hover" @click="chooseProject(item)">
                                <text :class="{'color-primary': item['left_num'] > 0}">选择此项目</text>
                            </view>
                        </template>
                        <template v-else>
                            <view class="option-item" hover-class="navigator-hover" @click="goAddProject(item)">
                                <text class="iconfont icon-edit"></text>
                                <text>修改</text>
                            </view>
                            <view class="option-item" hover-class="navigator-hover" @click="deleteProject(item)">
                                <text class="iconfont icon-delete"></text>
                                <text>删除</text>
                            </view>
                        </template>
                    </view>
                </view>
            </view>
        </view>

        <view v-if="!loading && !project_list.length" class="text-center" style="padding-top: 15vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub font14">暂无项目</view>

            <view
                v-if="!choose"
                class="add-button color-white bg-primary"
                hover-class="navigator-hover"
                @click="goAddProject(null)"
            >添加项目</view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
export default {
    data() {
        return {
            loading: true,
            project_list: [],
            choose: false
        }
    },

    onLoad(e) {
        this.id = e.id
        if (e.choose) this.choose = true
        login.uniLogin(() => {
            this.init()
        })
    },

    methods: {
        async init() {
            await this.getProjectList()
            this.loading = false
        },

        async getProjectList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_pay.adminManage/active_price_list',
                data: {
                    active_id: this.id,
                    get_left_num: 1   // 【1】需要输出已购买数量，剩余可购买数量
                }
            })
            if (res?.data?.['price_list']?.length) this.project_list = res.data['price_list']
        },

        deleteProject(item) {
            uni.showModal({
                title: '提示',
                content: `确定删除项目【${item.name}】吗？`,
                confirmText: '删除',
                confirmColor: '#dd524d',
                success:  res => res.confirm && this.deleteProjectRequest(item.id)
            })
        },

        async deleteProjectRequest(id) {
            uni.showLoading({ title: '删除中...' })
            const res = await this.xwy_api.deleteRecords(58, id)
            uni.hideLoading()

            if (res?.status !== 1) {
                this.xwy_api.alert(res?.info || '删除失败')
                return
            }

            uni.showToast({ title: '删除成功', icon: 'success' })
            const index = this.project_list.findIndex(item => item.id === id)
            this.project_list.splice(index, 1)
        },

        chooseProject(item) {
            if (item['left_num'] <= 0) return uni.showToast({ title: '该项目已报名已满，无法选择！', icon: 'none' })
            const OEC = this.getOpenerEventChannel()
            OEC?.emit && OEC.emit('chooseProject', { id: item.id, name: item.name, amount: item.amount })
            uni.navigateBack()
        },

        goAddProject(item = null) {
            let url = './add?id=' + this.id

            if (item) {
                url += `&project_id=${item.id}`
                app.globalData.registration_project_item = item
            }

            uni.navigateTo({
                url,
                events: {
                    update: data => {
                        if (item) {
                            item = data
                        } else {
                            this.reloadProjectList()
                        }
                    }
                }
            })
        },

        reloadProjectList() {
            this.list = []
            this.loading = true
            this.getProjectList()
            this.loading = false
        }
    }
}
</script>

<style lang="scss">
.top-count {
    border-bottom: 1px solid #eee;
}

.project-item {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
    box-shadow: 0 0 5px 2px #eee;

    .option-bar {
        margin-top: 10px;
        border-top: 1px solid #eee;
        padding-top: 10px;
        .option-item {
            padding: 0 5px;
            color: #999;
            font-size: 14px;
            .iconfont {
                margin-right: 2px;
                font-size: 14px;
            }
        }
    }
}

.add-button {
    width: 250px;
    line-height: 44px;
    border-radius: 22px;
    margin: 30px auto;
}
</style>
