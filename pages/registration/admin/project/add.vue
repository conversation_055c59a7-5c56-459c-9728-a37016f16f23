<template>
    <view>
        <view class="form">
            <view class="form-item">
                <view class="top color-content">
                    <text>项目名称</text>
                    <text class="color-error font16"> *</text>
                </view>
                <view class="bottom font16">
                    <input class="input" v-model="name" :maxlength="50" placeholder="请填写项目名称"/>
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">
                    <text>金额</text>
                    <text class="color-sub pl5 font14">(单位: 元)</text>
                </view>
                <view class="bottom font16">
                    <input
                        class="input"
                        type="digit"
                        v-model="amount"
                        :maxlength="10"
                        placeholder="不填或填0则无需支付"
                    />
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">
                    <text>库存</text>
                    <text v-if="buy_num" class="font12 color-sub pl5">(当前已购 {{ buy_num }})</text>
                </view>
                <view class="bottom font16">
                    <input
                        class="input"
                        type="number"
                        v-model="num"
                        :maxlength="10"
                        placeholder="库存为0时不可购买, 不填默认库存为0"
                    />
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">排序</view>
                <view class="bottom font16">
                    <input
                        class="input"
                        type="number"
                        v-model="sort_num"
                        :maxlength="10"
                        placeholder="数字越小排在越前, 不填或填0默认为10000"
                    />
                </view>
            </view>
        </view>

        <view class="save bg-primary color-white text-center" @click="verify">保存</view>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
export default {
    data() {
        return {
            name: '',
            amount: '',
            num: '',
            sort_num: '',
            buy_num: 0
        }
    },

    onLoad(e) {
        this.id = e.id
        login.uniLogin(() => {
            this.init(e)
        })
    },

    methods: {
        init(e) {
            if (e.project_id) {
                this.project_id = e.project_id
                this.getProjectDetails()
            }
            uni.setNavigationBarTitle({ title: e.project_id ? '修改项目' : '添加项目' })
        },

        getProjectDetails() {
            const details = app.globalData['registration_project_item']
            this.details = details
            this.name = details.name
            this.amount = details.amount
            this.num = details.num
            this.sort_num = details.sort_num
            this.buy_num = details.buy_num
        },

        verify() {
            const errorToast = title => {
                uni.showToast({
                    title,
                    icon: 'none'
                })
            }
            if (!this.name) {
                errorToast('请填写项目名称')
                return
            }
            if (this.amount && !/^\d+(\.\d{1,2})?$/.test(this.amount)) {
                errorToast('请填写正确的金额')
                return
            }
            if (this.buy_num && Number(this.num) < this.buy_num) {
                errorToast(`可购买量(${this.num})不能小于已购数量(${this.buy_num})`)
                return
            }

            this.save()
        },

        async save() {
            const data = {
                active_id: this.id,
                name: this.name,
                amount: Number(this.amount),
                num: Number(this.num),
                sort_num: Number(this.sort_num) || 10000
            }
            if (this.project_id) data.id = this.project_id

            uni.showLoading({ title: '保存中...' })
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_pay.adminManage/create_price_set',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) {
                this.xwy_api.alert(res?.info || '保存失败')
                return
            }

            uni.showToast({
                title: '保存成功',
                icon: 'success'
            })

            let new_project_data = null
            if (this.project_id) {
                new_project_data = this.details
                new_project_data.name = data.name
                new_project_data.amount = data.amount
                new_project_data.num = data.num
                new_project_data.sort_num = data.sort_num
            }

            const OEC = this.getOpenerEventChannel()
            OEC?.emit && OEC.emit('update', new_project_data)

            setTimeout(() => {
                uni.navigateBack()
            }, 1000)
        }
    }
}
</script>

<style lang="scss">
.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}

.form-item .bottom {
    border-bottom: 1px solid #eee;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.save {
    width: 250px;
    line-height: 44px;
    border-radius: 22px;
    margin: 20px auto;
}
</style>
