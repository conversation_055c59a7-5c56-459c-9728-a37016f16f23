<template>
    <view class="page bg-background"
          :class="{'from-submit-bg': detail.conf.active.activity_type === 'form-submit'}">

        <screen-picture
            v-if="screen_pic_show"
            :hide="screen_pic_hide"
            :image-src="screen_pic"
            :time="screen_pic_count_down"
            :show-button="true"
            @skipScreen="skipScreen"
        />


        <activity-delete-tips v-if="error" :activeId="id"/>

        <view v-if="detail && detail.active_id">


            <template v-if="detail.conf.active.activity_type === 'form-submit'">

                <navigator v-if="is_my_activity" class="form-submit-sign-up-jump text-center bg-white"
                           :url="'../admin/manage?id=' + id">
                    <uni-icons type="gear-filled" color="#02b148" size="24"/>
                    <view class="color-green">活动管理</view>
                </navigator>

                <view v-if="detail.logo">
                    <image :src="detail.logo" mode="widthFix" style="width: 100%;"/>
                </view>

                <view v-if="detail.content || content_news_id" class="p10">
                    <view class="text-center" style="color: #EF9068;">活动介绍</view>
                    <view v-if="detail.content && !content_news_id">
                        <text space="nbsp">{{ detail.content }}</text>
                    </view>
                    <form-submit-content :news-id="content_news_id"/>
                </view>


                <view class="form-submit-view">
                    <view class="color-sub">
                        <view>
                            <text class="iconfont icon-time color-sub"></text>
                            <text>报名开始：</text>
                            <uni-dateformat :date="detail.begin_time * 1000" format="yyyy-MM-dd hh:mm:ss"
                                            :threshold="[0, 0]"/>
                        </view>
                        <view>
                            <text class="iconfont icon-time color-sub"></text>
                            <text>报名结束：</text>
                            <uni-dateformat :date="detail.end_time * 1000" format="yyyy-MM-dd hh:mm:ss"
                                            :threshold="[0, 0]"/>
                        </view>
                    </view>

                    <view
                        v-if="detail.rank_set && detail.rank_set['team_group_open'] && detail.conf.active.team_open"
                        class="form-submit-item">
                        <view class="form-submit-label">分组</view>
                        <view class="form-submit-input-container">
                            <form-submit-team
                                :active-id="id"
                                :team-group-open="!!detail.rank_set['team_category']"
                                :team-info="formSubmit.team"
                                :team-must="!!detail.conf.active.team_must"
                                @change="formSubmitTeamChange"
                            />
                        </view>
                    </view>

                    <view class="form-submit-item" v-for="(item, index) in must_submit" :key="index">
                        <view class="form-submit-label">{{ item.title }}</view>
                        <view class="form-submit-input-container">

                            <input
                                v-if="item.types === 1"
                                class="form-submit-input"
                                v-model="item.value"
                                :placeholder="`请输入${item.title}(${item.rules === 1 ? '必' : '选'}填项)`"
                            />
                            <picker v-if="item.types === 2" :range="item.options" range-key="text"
                                    @change="mustValueChange($event, index)">
                                <view v-if="item.value">{{ item.value }}</view>
                                <view v-else class="form-submit-placeholder">
                                    请输入{{ item.title }}({{ item.rules === 1 ? '必' : '选' }}填项)
                                </view>
                            </picker>
                            <view v-if="item.types === 3">
                                <view v-if="item.value" class="form-submit-image-view">
                                    <view class="form-submit-image-clear flex-all-center"
                                          @click="mustSubmitImageClear(index)">
                                        <uni-icons type="closeempty" color="#ececec"/>
                                    </view>
                                    <image class="form-submit-image" :src="item.value" mode="aspectFill"/>
                                </view>
                                <view v-else>
                                    <view class="form-submit-image-upload flex-all-center"
                                          hover-class="navigator-hover"
                                          @click="mustSubmitUploadImage(index)">
                                        <uni-icons type="plusempty" size="50" color="#eeeeee"/>
                                    </view>
                                    <view class="form-submit-placeholder pt10">
                                        请上传{{ item.title }}({{ item.rules === 1 ? '必' : '选' }}填项)
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="flex-all-center p10">
                        <view class="sign-up-now-button bg-green color-white" hover-class="navigator-hover"
                              @click="formSubmitSignUpCheck">立即报名
                        </view>
                    </view>
                </view>

            </template>


            <template v-if="detail.conf.active.activity_type === 'sign-up'">
                <activity-logo-title-time :details="detail"/>


                <view class="icon-list flex-row flex-wrap text-center bdb-10 bg-white">

                    <navigator v-if="is_my_activity" class="icon-item" :url="'../admin/manage?id=' + id">
                        <text class="iconfont font24 color-primary icon-setting"></text>
                        <view class="color-sub font14" style="padding-top: 3px;">活动管理</view>
                    </navigator>
                    <view v-if="!is_joining" class="icon-item" @click="joinActivity">
                        <text class="iconfont font24 color-primary icon-users"></text>
                        <view class="color-sub font14" style="padding-top: 3px;">报名</view>
                    </view>
                    <view v-if="is_joining" class="icon-item" @click="uniPopupOpen('my_info')">
                        <text class="iconfont font24 color-primary icon-personal-data"></text>
                        <view class="color-sub font14" style="padding-top: 3px;">报名信息</view>
                    </view>


                    <view class="icon-item" @click="uniPopupOpen('activity_detail')">
                        <text class="iconfont font24 color-primary icon-feedback"></text>
                        <view class="color-sub font14" style="padding-top: 3px;">活动说明</view>
                    </view>


                    <template v-if="detail.conf.active.detail_icon_list">

                        <navigator
                            v-for="(item, index) in detail.conf.active.detail_icon_list"
                            :key="index"
                            class="icon-item"
                            :url="item.type === 0 ? ('/pages/news/preview?id=' + item.id) : ('/pages/news/list?category_id=' + item.id)"
                        >
                            <text :class="'iconfont font24 color-primary ' + item.icon"></text>
                            <view class="color-sub font14" style="padding-top: 3px;">{{ item.title }}</view>
                        </navigator>
                    </template>


                    <view class="icon-item" @click="showActiveSharePopup">
                        <text class="iconfont font24 color-primary icon-share"></text>
                        <view class="color-sub font14" style="padding-top: 3px;">分享</view>
                    </view>
                </view>


                <view class="p10 bdb-10 bg-white flex-all-center">
                    <view v-if="!is_joining" class="join-btn bg-primary color-white" @click="joinActivity">
                        报名
                    </view>
                </view>
            </template>


            <xwy-ad v-if="!password_dialog_show && !join_popup_show && !popup_open && (!detail.rank_set || !detail.rank_set.closed_AD)" :ad_type="4"/>

            <view v-if="technology_support" class="flex-all-center" style="padding-top: 30px;">

                <navigator v-if="technology_support.news_id" class="text-center font14 color-sub p10" 
                           :url="'/pages/news/preview?id=' + technology_support.news_id + '&type=support'">
                    {{ technology_support.button_text }}
                </navigator>
                <view v-else class="text-center font14 color-sub p10">
                    {{ technology_support.button_text }}
                </view>
            </view>

            <view v-if="!detail.rank_set || !detail.rank_set['closed_user_center']" class="flex-all-center">
                <navigator url="/pages/user/user" class="p10 color-sub font14">个人中心</navigator>
            </view>

            <xwy-ad v-if="(!detail.rank_set || !detail.rank_set.closed_AD) && !screen_pic_show" :ad_type="3"/>

        </view>

        <view v-if="join_popup_show" class="join-popup flex-all-center bg-white" @touchmove.stop.prevent="">
            <view>
                <view class="join-popup-c bg-white">
                    <view class="text-center font18 color-content p10">
                        <template v-if="update_attend_details">修改报名信息</template>
                        <template v-else>参加活动</template>
                    </view>


                    <template v-if="must_submit.length">
                        <template v-for="(item, index) in must_submit">

                            <input
                                v-if="item.types === 1"
                                :key="index"
                                class="join-input"
                                v-model="item.value"
                                :placeholder="'请输入' + item.title + (item.rules === 1 ? ' (必填)' : '')"
                            />
                            <picker
                                v-if="item.types === 2"
                                :range="item.options"
                                range-key="text"
                                @change="mustValueChange($event, index)"
                            >
                                <view class="join-input flex-kai">
                                    <view v-if="!item.value" class="color-sub">
                                        请选择{{ item.title }}{{ item.rules === 1 ? ' (必选)' : '' }}
                                    </view>
                                    <view v-if="item.value">{{ item.value }}</view>
                                    <text class="iconfont icon-more color-disabled font18"/>
                                </view>
                            </picker>
                        </template>

                    </template>


                    <view class="join-popup-btns flex-row text-center font18">
                        <view class="join-popup-btn-cancel color-sub" @click="cancelJoin">取消</view>
                        <view class="join-popup-btn-confirm color-success" @click="joinAjax">确定</view>
                    </view>
                </view>

                <template v-if="!detail.rank_set || !detail.rank_set.closed_AD">
                    <view class="pt5">
                        <xwy-ad :ad_type="66"/>
                    </view>
                    <xwy-ad :ad_type="3"/>
                </template>
            </view>


        </view>

        <active-share ref="activeShare"/>

        <uni-popup ref="activity_detail" type="center" @touchmove.stop.prevent="">
            <view
                v-if="detail && detail.conf && detail.conf.active"
                class="uni-popup-info detail-popup bg-white"
            >
                <view class="popup-close" @click="uniPopupClose('activity_detail')">
                    <uni-icons type="close" size="28" color="#b2b3b7"/>
                </view>
                

                <scroll-view :scroll-y="true" class="detail-popup-detail" 
                             style="max-height: calc(100vh - 200px); padding: 10px 0;">
                    <view class="p10 bdb-10 bg-white">
                        <view class="text-center color-sub pb5">- 活动规则 -</view>

                        <view class="color-content font16">
                            活动参与方式：
                            <template>
                                <template v-if="detail.conf.active.enter_types === 1">
                                    自由报名参与活动
                                </template>
                                <template v-else-if="detail.conf.active.enter_types === 2">
                                    需要输入密码才能报名
                                </template>
                                <template v-else-if="detail.conf.active.enter_types === 3">
                                    报名需要审核
                                </template>
                            </template>
                        </view>
                    </view>

                    <view v-if="detail.content || news_detail" class="p10 bdb-10 bg-white">
                        <view class="text-center color-sub pb5">- 活动说明 -</view>
                        <view class="color-content font16">
                            <template v-if="detail.content && !news_detail">

                                <rich-text :nodes="detail.content" space="nbsp" 
                                           :selectable="true"></rich-text>
                            </template>
                            <template v-if="news_detail">
                                <template v-if="news_detail.content">
                                    <u-parse :content="news_detail.content"/>
                                </template>
                            </template>
                        </view>
                    </view>
                    <xwy-ad v-if="!loading && (!detail.rank_set || !detail.rank_set.closed_AD)" :ad_type="66"/>
                </scroll-view>
            </view>
        </uni-popup>

        <uni-popup ref="my_info" type="center" @touchmove.stop.prevent="">
            <view v-if="detail && detail.conf && detail.conf.active" class="uni-popup-info bg-white p20">
                <view class="popup-close" @click="uniPopupClose('my_info')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="text-center color-sub pb5">- 活动报名信息 -</view>
                <view class="text-center p10">

                    <image class="headimg" mode="aspectFill" 
                           :src="headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"/>
                    <view>
                        <text class="color-primary" @click="updateHeadimg">更改头像</text>
                    </view>
                </view>

                <view
                    class="color-content font16 ptm5"
                    v-for="(item, index) in must_submit"
                    :key="index"
                    @click="updateAttendDetailShow"
                >
                    <text>
                        {{ item.title }}：
                        <template>
                            <template v-if="item.value">{{ item.value }}</template>
                            <template v-else>
                                <template v-if="item.types === 1">未填写</template>
                                <template v-if="item.types === 2">未选择</template>
                            </template>
                        </template>
                    </text>
                    <text v-if="is_joining" class="iconfont icon-edit color-sub pl5"></text>
                </view>

                <view v-if="user_details.registration_name" class="color-content font16 ptm5">
                    报名项目：{{ user_details.registration_name }}
                </view>

                <template v-if="popup_open && (!detail.rank_set || !detail.rank_set.closed_AD)">
                    <view style="position: relative; left: -10px;">
                        <xwy-ad :ad_type="66"/>
                    </view>
                    <xwy-ad :ad_type="3"/>
                </template>
            </view>
        </uni-popup>


        <template v-if="password_dialog_show">
            <uni-popup ref="input_password" type="dialog" mode="input" :is-mask-click="false" 
                       @maskClick="copy(id)">
                <uni-popup-dialog
                    mode="input"
                    title="活动密码"
                    :value="password"
                    placeholder="请输入活动密码"
                    @confirm="passwordInputConfirm"
                    @close="passwordInputClose"
                />
            </uni-popup>
        </template>

        <registration ref="registration" v-if="detail && detail.rank_set && detail.rank_set['active_pay']"/>

        <expiration-reminder ref="expirationReminder"/>

    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import my_storage from '@/utils/storage.js'
import base64 from '@/utils/base64.js'

import formSubmitContent from "../components/form-submit-content.vue"
import formSubmitTeam from "../components/form-submit-team.vue"

export default {
    components: {formSubmitContent, formSubmitTeam},
    data() {
        return {
            evn_version: app.globalData['evn_version'],
            loading: true,
            screen_pic: '',
            screen_pic_show: false,
            screen_pic_hide: false,
            screen_pic_count_down: null,
            id: '',
            userid: '',
            is_my_activity: false,
            detail: {},
            user_details: {},
            error: '',
            join_popup_show: false,
            update_attend_details: false,
            headimg: '',
            username: '',
            checked: 0,
            is_joining: false,
            password: '',
            technology_support: false,
            must_submit: [],
            headimg_plugin: [],
            password_dialog_show: false,
            popup_open: false,
            news_detail: null,
            platform: uni.getSystemInfoSync().platform,

            formSubmit: {
                team: {
                    id: '',
                    name: ''
                },
                team_group: {
                    id: '',
                    name: ''
                }
            }
        }
    },

    computed: {
        content_news_id() {
            return this.detail?.conf?.active?.news?.news_id
        }
    },

    onUnload() {
        if (this.interval) clearInterval(this.interval)
    },

    onLoad(e) {
        console.log('活动详情页面路径参数', e)

        let isH5 = false
        // #ifdef H5
        isH5 = true
        // #endif

        if (isH5) return this.$uni.showModal('请在小程序内打开', {success: () => uni.navigateBack()})
        this.init(e)
    },


    onShareAppMessage() {
        this._utils.shareCopyActivityId(this.detail)

        const {active_id, conf, name, logo} = this.detail || {}
        let url = `/pages/registration/user/detail?id=${active_id}`
        const {screen_pic, share_title, share_image} = conf?.active || {}
        if (screen_pic) url += `&screen_pic=${screen_pic}`

        return {
            title: share_title || name || '',
            path: url,
            imageUrl: share_image || logo || ''
        }
    },

    onShareTimeline() {
        return {
            title: this.detail.name,
            imageUrl: this.detail.logo || ''
        }
    },

    methods: {
        init(e) {
            if (uni.getLaunchOptionsSync().scene === 1154) return this.getSimpleDetail(e.id)

            e.screen_pic ? this.screenPicShow(e.screen_pic) : this.$uni.showLoading('数据加载中...')


            login.uniLogin(err => {
                if (err && err.errMsg) {
                    uni.hideLoading()
                    return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
                }


                if (e.scene) return this.analysisScene(e.scene)

                if (!e.id) {
                    this.loading = false
                    this.error = '请指定活动id'
                    return uni.hideLoading()
                }


                this.id = e.id
                this.userid = app.globalData['userid']
                this.getDetail()
            })
        },

        async getSimpleDetail(id) {
            this.$uni.showLoading('数据加载中...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details_simple',
                data: {
                    active_id: id
                }
            })
            this.loading = false
            uni.hideLoading()

            const details = res?.data?.active_details
            if (!details) {
                this.loading = false
                this.error = (res['info'] || '活动详情获取失败。') + '活动id：' + this.id
                return uni.hideLoading()
            }


            if (res['data'].active_more_data) {
                const active_more_data = res['data']['active_more_data']
                this.active_more_data = active_more_data
                if (active_more_data['technology_support']) {
                    this.technology_support = res['data'].active_more_data.technology_support
                }
                if (active_more_data['active_conf_set']) {
                    const active_conf_set = active_more_data['active_conf_set']
                    if (active_conf_set.headimg_plugin && active_conf_set.headimg_plugin.length) {
                        this.headimg_plugin = active_conf_set.headimg_plugin
                    }
                }
            }


            my_storage.setActivityCloaeAdStorage(this.id, details.rank_set?.closed_AD)

            details.conf ||= {}

            const conf = details.conf
            conf.active ||= {}
            conf.active.activity_type ||= 'sign-up'

            if (!this.screen_pic && conf.active?.screen_pic) this.screenPicShow(conf.active.screen_pic)

            if (conf.must_submit) {
                const must_submit = conf.must_submit
                delete conf.must_submit
                if (must_submit.length) {
                    must_submit.forEach(v => v.value = v.value || '')
                    this.must_submit = must_submit
                }
            }

            this.detail = details


            this.addLookRecords()

            if (detail.name) this.$uni.setNavigationBarTitle(detail.name)

        },

        toIdeaAvatar(item) {
            if (!item.img_list || !item.img_list.length) {
                return this.$uni.showModal('活动未配置头像图片列表，无法使用此功能')
            }

            let path = '/pages/idea_avatar/pendant/pendant'
            if (item.key === 'beijing') path = '/pages/idea_avatar/background/background'
            path += `?id=${this.id}`
            if (this.detail.rank_set?.closed_AD) path += '&closed_AD=1'

            this.$uni.navigateTo(path, {
                success: res => res.eventChannel.emit('img_list', item.img_list)
            })
        },


        screenPicShow(src) {
            this.screen_pic = src
            this.screen_pic_count_down = 5
            this.screen_pic_show = true

            this.interval = null
            this.interval = setInterval(() => {
                this.screen_pic_count_down--
                if (this.screen_pic_count_down <= 0) {
                    this.skipScreen()
                }
            }, 1000)
        },
        skipScreen() {
            clearInterval(this.interval)
            this.screen_pic_count_down = 0
            this.screen_pic_hide = true

            const timeout = setTimeout(() => {
                this.screen_pic_show = false
                this.screen_pic_hide = false
                if (this.loading) this.$uni.showLoading('数据加载中...')
                this.passwordDialogShow()
                clearTimeout(timeout)
            }, 500)
        },
        analysisScene(scene) {
            const sceneStr = decodeURIComponent(scene)
            console.log(sceneStr)
            const id = utils.getUrlParams('id', sceneStr)
            console.log('id===', id)
            if (!id) {
                uni.hideLoading()
                return this.$uni.showModal('从二维码获取id失败')
            }

            this.getActiveId(id)
        },


        async getActiveId(id) {
            const data = {
                access_token: app.globalData['access_token'],
                id
            }

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/get_long_active_id_by_short_id',
                data
            })
            if (res?.['data']?.['long_active_id']) {
                this.id = res['data']['long_active_id']
                this.getDetail()
            } else {
                uni.hideLoading()
                xwy_api.alert(res && res['info'] || '长id获取失败')
            }
        },

        getDetail(just_update = false) {
            xwy_api.getActivityDetail(this.id, res => {
                const details = res?.data?.active_details
                if (!details) {
                    this.loading = false
                    this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                    return uni.hideLoading()
                }

                // #ifndef H5
                this.$refs.expirationReminder.open(res.data.active_details)
                // #endif

                if (res.data.active_more_data) {
                    const active_more_data = res.data.active_more_data
                    this.active_more_data = active_more_data
                    if (active_more_data.technology_support) {
                        this.technology_support = res.data.active_more_data.technology_support
                    }
                    if (active_more_data['active_conf_set']) {
                        const active_conf_set = active_more_data['active_conf_set']
                        if (active_conf_set.headimg_plugin && active_conf_set.headimg_plugin.length) {
                            this.headimg_plugin = active_conf_set.headimg_plugin
                        }
                    }
                }


                app.globalData.activity_detail = details

                my_storage.setActivityCloaeAdStorage(this.id, details.rank_set?.closed_AD)

                details.conf ||= {}

                const conf = details.conf
                conf.active ||= {}
                conf.active.activity_type ||= 'sign-up'

                if (conf.active) {
                    const active = conf.active
                    if (!this.screen_pic && active.screen_pic) this.screenPicShow(active.screen_pic)

                    if (active.news?.news_id) this.changeDetailContent(active.news.news_id)
                }

                if (conf.must_submit) {
                    const must_submit = conf.must_submit
                    delete conf.must_submit
                    if (must_submit.length) {
                        must_submit.forEach(v => v.value = v.value || '')
                        this.must_submit = must_submit
                    }
                }

                this.detail = details

                if (details.rank_set?.['shield_other']) this.$uni.hideHomeButton()

                this.addLookRecords()

                if (details.name) this.$uni.setNavigationBarTitle(details.name)

                if (!just_update && app.globalData['userid'] === details.userid) this.is_my_activity = true
                
                this.getUserStatus()

            })
        },


        addLookRecords() {
            const detail = this.detail
            const value = {
                active_id: detail.active_id,
                name: detail.name,
                types: detail.types,
                logo: detail.logo || this.xwy_config.active_default_logo,
                look_time: new Date().getTime()
            }

            if (detail.organizer) value.organizer = detail.organizer
            my_storage.addActivityLookRecords(value)
        },


        async getUserStatus() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id
                }
            })

            if (res?.['data']?.user_details) {
                const attend_details = res['data'].user_details
                this.user_details = attend_details
                this.is_joining = true
                this.checked = attend_details.checked || 0

                this.loading = false
                uni.hideLoading()

                if (attend_details.nickname) this.username = attend_details.nickname
                if (attend_details.headimg) this.headimg = attend_details.headimg

                if (attend_details.must_submit) {
                    this.must_submit.forEach(v => {
                        attend_details.must_submit.forEach(vv => {
                            if (vv.title === v.title) v.value = vv.value
                        })
                    })
                    this.$forceUpdate()
                }

                await this.getRegistrationInfo()

            } else {
                this.no_attend = true
                this.loading = false
                uni.hideLoading()

                if (this.screen_pic_show) return false

                this.passwordDialogShow()
            }

        },

        async getRegistrationInfo() {
            if (!this.detail.rank_set?.['active_pay']) return
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_pay.userBuy/check_user_have_pay_active',
                data: {active_id: this.id}
            })

            if (res?.['data']?.['buy_details']?.['price_details']?.name) {
                this.user_details.registration_name = res['data']['buy_details']['price_details'].name
            }
        },

        changeDetailContent(news_id) {
            xwy_api.ajax({
                url: "front.news/news_details",
                data: {
                    access_token: app.globalData['access_token'],
                    news_id
                },
                success: res => {
                    uni.hideLoading();

                    uni.hideLoading()
                    if (!res.data || !res.data['news_details']) {
                        return this.$uni.showModal(res.info || '文章内容获取失败', {
                            success: () => uni.navigateBack()
                        })
                    }

                    const detail = res.data['news_details']


                    if (detail.video_url) {
                        let video_type = 'txv_id'
                        if (detail.video_url.startsWith('http://') || detail.video_url.startsWith('https://')) {
                            video_type = 'http'
                        }
                        detail.video_type = video_type
                    }


                    if (detail.content) {
                        detail.content = utils.newsContentInit(detail.content)
                    }

                    this.news_detail = detail
                }
            })
        },


        passwordDialogShow() {
            if (this.detail.conf.active.password && this.no_attend) {
                const passwordDialogShow = () => {
                    this.password_dialog_show = true
                    const password = my_storage.getActivityPassword(this.id)
                    if (password) {
                        this.password = password
                        this.checkActivityPassword(password)
                    }
                    const timeout = setTimeout(() => {
                        this.$refs.input_password.open()
                        clearTimeout(timeout)
                    }, 30)
                }

                // 体验版不需要输入密码
                if (app.globalData['evn_version'] === 'trial') {
                    return this.$uni.showModal('此活动设置了活动密码，请勿报名参与活动！！！', {
                        cancelText: '进入活动',
                        confirmText: '输入密码',
                        success: res => res.confirm && passwordDialogShow()
                    })
                }


                passwordDialogShow()
            }
        },

        passwordInputConfirm(val) {
            if (!val) {
                this.$uni.showToast('请输入密码')
                return xwy_api.alert('请输入密码', {success: () => this.$refs.input_password.open()})
            }
            this.checkActivityPassword(val)
        },

        async checkActivityPassword(password) {
            this.$uni.showLoading('密码验证中...')

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/check_active_password',
                data: {
                    active_id: this.id,
                    password
                }
            })
            uni.hideLoading()

            if (res?.['status']) {
                my_storage.rememberActivityPassword(this.id, password)
                this.$refs.input_password.close()
                return this.$uni.showToast('密码正确', 'success')
            }


            xwy_api.alert(res && res['info'] || '密码错误', {
                success: () => this.$refs.input_password.open()
            })
        },

        passwordInputClose() {
            this.copy(this.id, true)

            if (getCurrentPages().length > 1) return uni.navigateBack()

            // 纯净版，重新弹出输入密码窗口
            if (app.globalData['shop_info']?.['extend_set']?.['shield_other_active']?.active_id) {
                return this.$uni.showModal('请输入活动密码', {success: () => this.$refs.input_password.open()})
            }

            this.$uni.reLaunch('/pages/index/index')
        },


        async updateHeadimg() {
            uni.navigateTo({
                url: `/pages/other/headimg-list/headimg-list?active_id=${this.id}`,
                events: {
                    newImg: async obj => {
                        if (!obj?.src) return
                        this.$uni.showLoading('修改中...')
                        if (!obj.temp) return this.updateAttendDetail(obj.src)
                        const data = {
                            temp_data: {
                                path: obj.src
                            },
                            is_temp: 5
                        }
                        if (obj.size) data.temp_data.size = obj.size
                        const headimg = await xwy_api.uploadOneImage(data)
                        this.updateAttendDetail(headimg)
                    }
                }
            })
        },

        joinActivity() {
            if (this.loading) return
            if (this.detail.conf?.active?.submit) {
                const now_time = new Date().getTime()
                const {begin, end} = this.detail.conf.active.submit
                if (begin) {
                    const begin_time = new Date(begin.replaceAll('-', '/')).getTime()
                    if (now_time < begin_time) return xwy_api.alert(`活动于${begin}开始报名`)
                }
                if (end) {
                    const end_time = new Date(end.replaceAll('-', '/')).getTime()
                    if (now_time > end_time) return xwy_api.alert(`活动于${end}截止报名`)
                }
            }


            // 判断是否开启付费报名
            if (!this.detail.rank_set?.['active_pay']) {
                this.join_popup_show = true
                return
            }

            // popup_open: true 会隐藏页面广告，因为广告会把弹窗遮挡住。这里避免打开付费弹窗时，弹窗被广告遮挡
            this.popup_open = true
            this.$refs.registration.check(this.id, success => {
                if (success) {
                    this.join_popup_show = true
                } else {
                    this.popup_open = false
                }
            })
        },

        updateAttendDetailShow() {
            this.update_attend_details = true
            this.join_popup_show = true
        },

        mustValueChange(e, index) {
            this.$set(this.must_submit[index], 'value', this.must_submit[index].options[e.detail.value].text)
        },

        mustSubmitUploadImage(index) {
            this.$uni.navigateTo(`/pages/other/image_upload_or_select?active_id=${this.id}`, {
                events: {
                    newImg: src => this.$set(this.must_submit[index], 'value', src)
                }
            })
        },

        mustSubmitImageClear(index) {
            this.$set(this.must_submit[index], 'value', '')
        },


        cancelJoin() {
            this.join_popup_show = false
            this.update_attend_details = false
        },

        joinAjax() {
            if (this.update_attend_details) {
                this.updateAttendDetail()
                return false
            }


            this.joining()
        },


        getMustSubmitData() {
            const must_submit = JSON.parse(JSON.stringify(this.must_submit))
            for (let i = 0; i < must_submit.length; i++) {
                const v = must_submit[i]
                v.options && delete v.options
                if (v.rules === 1 && v.value === '') {
                    let tips = '输入'
                    if (v.types === 2) tips = '选择'
                    return this.$uni.showToast(`请${tips}${v.title}`, v.title.length <= 4 ? 'error' : 'none')
                }
            }
            console.log(must_submit)
            let must_submit_str = JSON.stringify(must_submit)
            must_submit_str = must_submit_str.replaceAll('·', '-')
            return base64['encode'](must_submit_str)
        },

        updateAttendDetail(headimg) {
            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id
            }

            if (headimg) data.headimg = headimg

            const must_submit = this.getMustSubmitData()
            if (must_submit === false) return false
            data.must_submit = must_submit

            this.$uni.showLoading('修改中...')

            xwy_api.ajax({
                url: 'front.flat.sport_step.user/update_attend_details',
                data,
                success: res => {
                    if (res?.status !== 1) return this.$uni.showModal(res?.info || '修改失败')

                    this.$uni.showToast('修改成功', 'success')

                    this.cancelJoin()
                    this.getDetail()
                }
            })
        },

        joining() {

            const data = {
                active_id: this.id,
                access_token: app.globalData['access_token']
            }


            if (this.must_submit && this.must_submit.length) {
                const must_submit = this.getMustSubmitData()
                if (must_submit === false) return false
                data.must_submit = must_submit
            }

            data.nickname = this.username

            this.loading = true
            this.$uni.showLoading('报名中...')


            xwy_api.ajax({
                url: 'front.flat.sport_step.user/submit_attend_active',
                data,
                success: res => {
                    this.loading = false
                    uni.hideLoading()
                    console.log('报名活动', res)
                    if (!res.status) {
                        return this.$uni.showModal(res.info || '报名失败', {title: '报名失败'})
                    }

                    this.join_popup_show = false
                    this.$uni.showToast(res.info || '报名成功', 'success')

                    setTimeout(() => {
                        this.$uni.showLoading()
                        this.getDetail()
                    }, 1000)
                }
            })
        },

        showActiveSharePopup() {
            this.$refs.activeShare.open({
                page: 'pages/registration/user/detail',
                scene: 'id=' + this.detail.id,
                qrcode_logo: this.detail.rank_set?.closed_AD && this.detail.conf?.active?.qrcode_logo
            })
        },

        copy(data, hide_toast = false) {
            uni.setClipboardData({
                data,
                success: () => hide_toast ? uni.hideToast() : this.$uni.showToast('复制成功', 'none', 500)
            })
        },

        uniPopupClose(ref) {
            this.popup_open = false
            this.$refs[ref].close()
        },

        uniPopupOpen(ref) {
            this.popup_open = true
            this.$refs[ref].open()
        },


        formSubmitTeamChange(data) {
            this.formSubmit.team = data.team
            this.formSubmit.team_group = data.team_group
        },

        formSubmitSignUpCheck() {
            let {begin_time, end_time} = this.detail
            begin_time *= 1000
            end_time *= 1000
            const now = new Date().getTime()
            if (now < begin_time) return this.$uni.showToast('未到报名时间')
            if (now > end_time) return this.$uni.showToast('报名已结束')

            const team_must = this.detail.conf?.active?.team_must
            if (team_must && !this.formSubmit.team.id) return this.$uni.showToast('请选择分组')

            for (let i = 0, len = this.must_submit.length; i < len; i++) {
                const item = this.must_submit[i]
                if (item.rules === 1 && !item.value) {
                    if (item.types === 3) return this.$uni.showToast(`请上传${item.title}`)
                    return this.$uni.showToast(`${item.title}必须填写`)
                }
            }

            this.formSubmitSignUp()
        },

        async formSubmitSignUp() {
            const data = {
                active_id: this.id
            }

            const must_submit = JSON.parse(JSON.stringify(this.must_submit))
            must_submit.forEach(item => {
                if (item.hasOwnProperty('options')) delete item.options
            })

            if (this.formSubmit.team.id) {
                data.team_id = this.formSubmit.team.id

                // 由于这个定制开发队伍和队伍分类是分开选的，可以选择任意队伍和任意队伍分类，所以队伍不能绑定队伍分类，所以队伍分类要单独存起来，由于个人信息里面的字段太多了，所以刘总建议我自己保存在must_submit里面
                // 我后面导出用户名单，发现没有导出队伍，就把队伍和队伍分类拼在一起存了
                let team_name = this.formSubmit.team.name
                if (this.formSubmit.team_group.name) team_name += `-${this.formSubmit.team_group.name}`

                must_submit.unshift({
                    title: '分组',
                    name: 'fen_zu',
                    value: team_name,
                    rules: 0,
                    types: 1
                })
            }

            data.must_submit = base64['encode'](JSON.stringify(must_submit))

            this.$uni.showLoading('报名中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.user/submit_attend_active',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '报名失败')
            this.$uni.showToast('已报名', 'success')
        }

    }
}
</script>

<style scoped lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 80px;
}


.bdb-10 {
    border-bottom: 10px solid #f8f8f8;
}

.icon-list {
    position: sticky;
    top: 0;
    z-index: 9;
    border-top: 1px solid #eee;
}

.icon-item {
    padding: 10px 0;
    width: calc(100% / 4);
    /* border-top: 1px solid #eee; */
    box-sizing: border-box;
}


.headimg {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}


.join-btn {
    width: 250px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 20px;
}

.join-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, .7);
    z-index: 9999;
}

.join-popup-c {
    border-radius: 10px;
    overflow: hidden;
}


.join-input {
    margin: 10px;
    padding: 10px;
    border: 1px solid #bbb;
    border-radius: 5px;
    width: 70vw;
    max-width: 400px;
}

.join-popup-btns {
    border-top: 1px solid #eee;
}

.join-popup-btn-cancel, .join-popup-btn-confirm {
    width: 50%;
    box-sizing: border-box;
    line-height: 44px;
}

.join-popup-btn-cancel {
    border-right: 1px solid #eee;
}


.uni-popup-info {
    position: relative;
    width: 280px;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    z-index: 999;
    left: 50%;
    bottom: -5px;
    margin-left: -12px;
}

.detail-popup {
    width: 95vw;
    padding-bottom: 15px;
}

.from-submit-bg {
    background-color: #A3E6D0;
}

.form-submit-sign-up-jump {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 9;
    padding-top: 10px;
    padding-bottom: 15px;
}

.form-submit-view {
    padding: 10px;
    margin: 10px;
    border-radius: 10px;
    background-color: #fff;

    .form-submit-item {
        padding: 20px 10px;

        .form-submit-label {
            color: #000;
        }

        .form-submit-input-container {
            padding: 10px 0;
            border-bottom: 1px solid #bcbcbc;

            .form-submit-placeholder {
                color: #808080;
            }

            .form-submit-image-view {
                position: relative;

                .form-submit-image-clear {
                    position: absolute;
                    top: 5px;
                    right: 5px;
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    background-color: rgba(255, 255, 255, .5);
                }

                .form-submit-image {
                    display: block;
                }
            }

            .form-submit-image-view, .form-submit-image, .form-submit-image-upload {
                width: 120px;
                height: 120px;
            }

            .form-submit-image-view, .form-submit-image-upload {
                border-radius: 5px;
                overflow: hidden;
            }

            .form-submit-image-upload {
                border: 1px solid #EBF1F8;
                box-sizing: border-box;
            }
        }
    }

    .sign-up-now-button {
        width: 250px;
        line-height: 40px;
        text-align: center;
    }
}

</style>
