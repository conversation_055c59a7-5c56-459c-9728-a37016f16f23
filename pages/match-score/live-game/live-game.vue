<template>
    <view>
        <view v-if="!is_admin" class="clearfix clear">
            <button class="share-button fr" open-type="share">
                <text class="iconfont icon-share color-sub font24"></text>
            </button>
        </view>
        <view class="scoreboard flex-kai">
            <scoreboardItem v-if="home_team" type="home" :data="home_team"/>
            <view class="vs">
                <view class="vs-vs">VS</view>
                <view class="score-split">
                    <view class="score-split-item"></view>
                    <view class="score-split-item"></view>
                </view>
            </view>
            <scoreboardItem v-if="away_team" type="away" :data="away_team"/>
        </view>

        <template v-if="show_ad">
            <xwy-ad :ad_type="66"/>
            <xwy-ad :ad_type="3"/>
        </template>

        <template v-if="is_admin">
            <view v-if="active_types" class="operating-score flex-kai">
                <view :class="'flex-all-center operating-score-' + team"
                      v-for="team in ['home', 'away']" :key="team">
                    <view>
                        <view class="operating-score-item" v-for="item in operatingScore" :key="item.score"
                              hover-class="navigator-hover" @click="scoreOperating(team, item.score)">
                            {{ item.title }}
                        </view>
                    </view>
                </view>
            </view>

            <view v-if="score_logs.length" class="score-logs p10">
                <view class="color-sub font14 text-center">比赛日志不会保存, 每次退出页面将会清空。</view>

                <scroll-view :scroll-y="true">
                    <view class="font14" v-for="(item, index) in score_logs" :key="index">
                        <text class="color-content">【{{ item.team_name }}】</text>
                        <text class="color-title plr5">{{ item.score }}</text>
                        <text class="font12 color-sub pr5">{{ item.score_vs }}</text>
                        <uni-dateformat :date="item.time" format="hh:mm:ss" class="color-sub font12"/>
                    </view>
                </scroll-view>
            </view>
        </template>

        <view v-if="!is_admin" class="color-sub font12 text-center">下拉获取最新比分</view>
    </view>
</template>

<script>
import login from '@/utils/api/login.js'
import config from '../config'
import scoreboardItem from '../components/scoreboard-item.vue'

export default {
    components: {
        scoreboardItem
    },
    data() {
        return {
            active_types: null,
            home_team: {
                name: '队伍名',
                score: 0,
            },
            away_team: {
                name: '队伍名',
                score: 0,
            },
            score_logs: [],
            show_ad: false,
            is_admin: false
        }
    },

    computed: {
        operatingScore() {
            const active_types_info = config.activeTypes.find(v => v.types === this.active_types)
            return active_types_info?.operatingScore || config.operatingScoreDefault
        }
    },

    async onLoad(params) {
        this.active_id = params.active_id
        this.group_id = params.group_id
        if (params.is_admin) this.is_admin = true
        this.$uni.showLoading('加载中...')
        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }
            this.init()
        })
    },

    onPullDownRefresh() {
        this.getGameInfo().finally(() => uni.stopPullDownRefresh())
    },

    methods: {
        async init() {
            await this.getGameInfo()
            await this.getActiveDetails()
            uni.hideLoading()

        },

        async getGameInfo() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.score_mark.score/group_details_pk_team_list',
                data: {
                    group_id: this.group_id,
                }
            })

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '获取比赛信息失败', {
                success: () => uni.navigateBack()
            })

            const info = res?.data?.['group_details']
            if (!info) return this.$uni.showModal('获取比赛信息失败', {success: () => uni.navigateBack()})
            if (info.active_types) {
                this.active_types = info.active_types
            }
            const team_list = res.data.team_list
            if (!team_list?.length) return this.$uni.showModal('比赛队伍获取失败', {
                success: () => uni.navigateBack()
            })
            team_list.forEach(team => {
                delete team.active_id
                delete team.active_types
                delete team.create_time
                delete team.group_id
                if (team['team_details']) {
                    team.name = team['team_details'].name
                    team.headimg = team['team_details'].headimg
                    delete team['team_details']
                }
            })
            const [home_team, away_team] = team_list
            this.home_team = home_team
            this.away_team = away_team
            const NavigationBarTitle = info.name || `${home_team.name} VS ${away_team.name}`
            this.NavigationBarTitle = NavigationBarTitle  // 转发标题需要
            this.$uni.setNavigationBarTitle(NavigationBarTitle)
        },

        async getActiveDetails() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details',
                data: {active_id: this.active_id}
            })
            const details = res?.data?.active_details
            if (!details) return
            if (!details.rank_set?.closed_AD) this.show_ad = true
        },

        scoreOperating(team, score) {
            if (this.in_score_operating) return this.$uni.showToast('太快了, 请稍等')
            if (score === 'custom') return this.customScore(team)
            this.scoreOperatingRequest(team, score)
        },

        async customScore(team) {
            const res = await this.$uni.showModal('', {
                title: '自定义分数',
                showCancel: true,
                editable: true,
                placeholderText: '如需扣分请输负数',
            })
            if (!res.confirm || !res.content) return
            const score = Number(res.content)
            if (isNaN(score)) {
                this.$uni.showToast('请输入正确的分数')
                return this.customScore(team)
            }
            await this.scoreOperatingRequest(team, score)
        },

        async scoreOperatingRequest(team, score) {
            this.in_score_operating = true

            const team_info = this[`${team}_team`]
            team_info.score += score
            this.score_logs.unshift({
                team_name: team_info.name,
                score: score > 0 ? `+${score}` : score,
                score_vs: `(${this.home_team.score}:${this.away_team.score})`,
                time: new Date().getTime()
            })

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.score_mark.score/add_score',
                data: {
                    active_id: this.active_id,
                    id: team_info.id,
                    score,
                }
            })
            this.in_score_operating = false
            if (res?.status !== 1) {
                team_info.score -= score
                this.score_logs.splice(0, 1)
                await this.$uni.showModal(res?.info || '操作失败')
            }

            this.updateListScore()
        },

        updateListScore() {
            this.getOpenerEventChannel?.()?.emit?.('updateScore', {
                team_list: [
                    {id: this.home_team.id, score: this.home_team.score},
                    {id: this.away_team.id, score: this.away_team.score}
                ]
            })
        }
    },

    onShareAppMessage() {
        let path = `/pages/match-score/live-game/live-game?active_id=${this.active_id}&group_id=${this.group_id}`
        if (this.is_admin) path += '&is_admin=1'
        return {
            title: `${this.NavigationBarTitle} - 比赛记分`,
            path
        }
    },
}
</script>

<style lang="scss">
.share-button {
    background-color: rgba(0, 0, 0, 0);
    line-height: 1;
    padding: 10px 20rpx;
}
.share-button::after {
    border: none;
}

.scoreboard {
    margin: 20rpx;
    padding: 40rpx;
    border: 20rpx solid #ECB52B;
    background-color: #FAF4D4;
    border-radius: 10px;

    .vs {
        width: 100rpx;
        text-align: center;

        .vs-vs {
            color: #F3AF01;
            font-size: 48rpx;
            height: 76rpx;
            line-height: 76rpx;
        }

        .score-split {
            height: 240rpx;
            padding: 70rpx 0;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            .score-split-item {
                width: 36rpx;
                height: 36rpx;
                border-radius: 50%;
                background-color: #4C4C4C;
            }
        }
    }
}

.operating-score {
    padding-top: 30px;
    .operating-score-home, .operating-score-away {
        width: 50%;

        .operating-score-item {
            text-align: center;
            width: 250rpx;
            line-height: 40px;
            background-color: #F69348;
            color: #fff;
            box-shadow: 0 0 5px #f1b485;
            margin-bottom: 10px;
            border-radius: 5px;
        }
    }
}

.score-logs {
    scroll-view {
        height: 50vh;
    }
}

.picker {
    margin: 20rpx;
    padding: 20rpx;
    border: 1px solid #e5e5e5;
    border-radius: 5px;
    box-shadow: 0 0 5px #e5e5e5;
    background-color: #fff;

    .picker-item {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 40px;
        line-height: 40px;
    }
}

@media (min-width: 500px) {
    .scoreboard {
        width: 500px;
    }

    .operating-score-item {
        width: 200px !important;
    }
}
</style>
