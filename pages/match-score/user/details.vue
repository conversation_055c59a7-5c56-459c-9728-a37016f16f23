<template>
    <view class="page bg-background">

        <screen-picture
            v-if="screen_pic_show"
            :hide="screen_pic_hide"
            :image-src="screen_pic"
            :time="screen_pic_count_down"
            :show-button="true"
            @skipScreen="skipScreen"
        />


        <activity-delete-tips v-if="error" :activeId="id"/>

        <view v-if="detail && detail.active_id">

            <activity-logo-title-time :details="detail"/>

            <view class="icon-list flex-row flex-wrap text-center bdb-10 bg-white">

                <navigator v-if="is_my_activity" class="icon-item" :url="'../admin/manage?id=' + id">
                    <text class="iconfont font24 color-primary icon-setting"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">活动管理</view>
                </navigator>

                <!--<view v-if="!is_joining" class="icon-item" @click="joinActivity">
                    <text class="iconfont font24 color-primary icon-users"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">参与活动</view>
                </view>

                <view v-if="is_joining" class="icon-item" @click="uniPopupOpen('my_info')">
                    <text class="iconfont font24 color-primary icon-personal-data"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">报名信息</view>
                </view>-->

                <view v-if="detail.content || news_detail" class="icon-item" @click="uniPopupOpen('activity_detail')">
                    <text class="iconfont font24 color-primary icon-feedback"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">活动说明</view>
                </view>

                <view class="icon-item" @click="showActiveSharePopup">
                    <text class="iconfont font24 color-primary icon-share"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">分享</view>
                </view>
            </view>



            <!--<view v-if="detail.conf.active.enter_types === 3 && is_joining && checked === 0"
                  class="p10 bdb-10 bg-white color-sub text-center font14">已报名 待管理员审核通过
            </view>

            <view v-if="!is_joining" class="p10 bdb-10 bg-white flex-all-center">
                <view class="join-btn bg-primary color-white" @click="joinActivity">参与活动</view>
            </view>-->


            <xwy-ad v-if="!password_dialog_show && !join_popup_show && !popup_open && (!detail.rank_set || !detail.rank_set.closed_AD)" :ad_type="4"/>

            <view class="pt10">
                <view class="flex-kai p10">
                    <view style="width: 80px;"></view>
                    <view>
                        <text class="color-disabled">-</text>
                        <text class="color-content" style="padding: 0 3px;">近期比赛</text>
                        <text class="color-disabled">-</text>
                    </view>
                    <view style="width: 80px;" class="text-right">

                        <navigator v-if="schedule_list_more" :url="'../schedule/list?id=' + id">
                            <text class="color-light-primary font14">更多比赛</text>
                            <uni-icons type="forward" size="14" color="#5cadff"/>
                        </navigator>
                    </view>
                </view>
                <view class="pt10">
                    <view class="schedule-item" v-for="(item, index) in schedule_list" :key="index"
                          @click="liveGame(item)">
                        <schedule-list-item :item="item"/>
                    </view>

                    <xwy-ad v-if="(!detail.rank_set || !detail.rank_set.closed_AD) && !screen_pic_show && schedule_list.length === 10" :ad_type="66"/>
                </view>

                <view v-if="!schedule_list_loading && !schedule_list.length"
                      class="text-center" style="padding-top: 10px;">
                    <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
                    <view class="color-sub">暂无比赛</view>
                </view>

                <view v-if="schedule_list_loading" class="text-center">
                    <view style="padding-top: 30px;"></view>
                    <load-ani/>
                    <view class="font14 color-sub">赛程加载中</view>
                </view>
            </view>


            <view v-if="technology_support" class="flex-all-center" style="padding-top: 30px;">

                <navigator v-if="technology_support.news_id" class="text-center font14 color-sub p10"
                           :url="'/pages/news/preview?id=' + technology_support.news_id + '&type=support'">
                    {{ technology_support.button_text }}
                </navigator>
                <view v-else class="text-center font14 color-sub p10">
                    {{ technology_support.button_text }}
                </view>
            </view>

            <view v-if="!detail.rank_set || !detail.rank_set['closed_user_center']" class="flex-all-center">
                <navigator url="/pages/user/user" class="p10 color-sub font14">个人中心</navigator>
            </view>

            <xwy-ad v-if="(!detail.rank_set || !detail.rank_set.closed_AD) && !screen_pic_show" :ad_type="3"/>

        </view>

        <view v-if="join_popup_show" class="join-popup flex-all-center bg-white" @touchmove.stop.prevent="">
            <view>
                <view class="join-popup-c bg-white">
                    <view class="text-center font18 color-content p10">
                        <template v-if="update_attend_details">修改报名信息</template>
                        <template v-else>参加活动</template>
                    </view>


                    <template v-if="must_submit.length">
                        <template v-for="(item, index) in must_submit">

                            <input v-if="item.types === 1" :key="index" class="join-input"
                                   v-model="item.value"
                                   :placeholder="'请输入' + item.title + (item.rules === 1 ? ' (必填)' : '')"/>
                            <picker v-if="item.types === 2" :range="item.options" range-key="text"
                                    @change="mustValueChange($event, index)" :key="index">
                                <view class="join-input flex-kai">
                                    <view v-if="!item.value" class="color-sub">
                                        请选择{{item.title}}{{item.rules === 1 ? ' (必选)' : ''}}
                                    </view>
                                    <view v-if="item.value">{{ item.value }}</view>
                                    <text class="iconfont icon-more color-disabled font18"/>
                                </view>
                            </picker>
                        </template>

                    </template>




                    <view class="join-popup-btns flex-row text-center font18">
                        <view class="join-popup-btn-cancel color-sub" @click="cancelJoin">取消</view>
                        <view class="join-popup-btn-confirm color-success" @click="joinAjax">确定</view>
                    </view>
                </view>

                <template v-if="!detail.rank_set || !detail.rank_set.closed_AD">
                    <view class="pt5">
                        <xwy-ad :ad_type="66"></xwy-ad>
                    </view>
                    <xwy-ad :ad_type="3"></xwy-ad>
                </template>
            </view>


        </view>

        <active-share ref="activeShare"/>

        <uni-popup ref="activity_detail" type="center" @touchmove.stop.prevent="">
            <view v-if="detail && detail.conf && detail.conf.active" class="uni-popup-info detail-popup bg-white">
                <!--<view class="popup-close" @click="uniPopupClose('activity_detail')">
                    <uni-icons type="close" size="28" color="#b2b3b7"/>
                </view>
                <scroll-view scroll-y class="detail-popup-detail" style="max-height: calc(100vh - 200px); padding: 10px 0;">
                    <view class="p10 bdb-10 bg-white">
                        <view class="text-center color-sub pb5">- 活动规则 -</view>

                        <view class="color-content font16">
                            活动参与方式：
                            <template v-if="detail.conf.active.enter_types === 1">
                                自由报名参与活动
                            </template>
                            <template v-if="detail.conf.active.enter_types === 2">
                                需要输入密码才能报名
                            </template>
                            <template v-if="detail.conf.active.enter_types === 3">
                                报名需要审核通过才能参与活动
                            </template>
                        </view>
                    </view>-->

                    <view v-if="detail.content || news_detail" class="p10 bdb-10 bg-white">
                        <view class="text-center color-sub pb5">- 活动说明 -</view>
                        <view class="color-content font16">
                            <template v-if="detail.content && !news_detail">
                                <rich-text :nodes="detail.content" space="nbsp" selectable></rich-text>
                            </template>
                            <template v-if="news_detail">
                                <template v-if="news_detail.content">
                                    <u-parse :content="news_detail.content"/>
                                </template>
                            </template>
                        </view>
                    </view>
                    <xwy-ad v-if="!loading && (!detail.rank_set || !detail.rank_set.closed_AD)" :ad_type="66"></xwy-ad>
                <!--</scroll-view>-->
            </view>
        </uni-popup>

        <uni-popup ref="my_info" type="center" @touchmove.stop.prevent="">
            <view v-if="detail && detail.conf && detail.conf.active" class="uni-popup-info bg-white p20">
                <view class="popup-close" @click="uniPopupClose('my_info')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="text-center color-sub pb5">- 活动报名信息 -</view>
                <view class="text-center p10">

                    <image class="headimg" mode="aspectFill"
                           :src="headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"/>
                    <view>
                        <text class="color-primary" @click="updateHeadimg">更改头像</text>
                    </view>
                </view>

                <view class="color-content font16 ptm5" v-for="(item, index) in must_submit" :key="index"
                      @click="updateAttendDetailShow">
                    <text>
                        {{item.title}}：
                        <template>
                            <template v-if="item.value">{{ item.value }}</template>
                            <template v-else>
                                <template v-if="item.types === 1">未填写</template>
                                <template v-if="item.types === 2">未选择</template>
                            </template>
                        </template>
                    </text>
                    <text v-if="is_joining" class="iconfont icon-edit color-sub pl5"></text>
                </view>


                <template v-if="popup_open && (!detail.rank_set || !detail.rank_set.closed_AD)">
                    <view style="position: relative; left: -10px;">
                        <xwy-ad :ad_type="66"></xwy-ad>
                    </view>
                    <xwy-ad :ad_type="3"></xwy-ad>
                </template>
            </view>
        </uni-popup>




        <template v-if="password_dialog_show">
            <uni-popup ref="input_password" type="dialog" mode="input" :is-mask-click="false"
                       @maskClick="copy(id)">
                <uni-popup-dialog mode="input" title="活动密码" :value="password" placeholder="请输入活动密码"
                                  @confirm="passwordInputConfirm" @close="passwordInputClose">
                </uni-popup-dialog>
            </uni-popup>
        </template>

        <expiration-reminder ref="expirationReminder"/>

    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import my_storage from '@/utils/storage.js'
import base64 from '@/utils/base64.js'
import activity_tool from '@/utils/acyivity_tool.js'

import scheduleListItem from '../components/schedule-list-item.vue'
import config from "@/pages/match-score/config"

let interval

export default {
    components: {scheduleListItem},
    data() {
        return {
            evn_version: app.globalData['evn_version'],
            from_tab: false,
            loading: true,
            screen_pic: '',
            screen_pic_show: false,
            screen_pic_hide: false,
            screen_pic_count_down: null,
            id: '',
            userid: '',
            is_my_activity: false,
            detail: {},
            user_details: {},
            error: '',
            join_popup_show: false,
            update_attend_details: false,
            headimg: '',
            username: '',
            checked: 0,
            is_joining: false,
            password: '',
            technology_support: false,
            must_submit: [],
            headimg_plugin: [],
            password_dialog_show: false,
            popup_open: false,
            news_detail: null,
            platform: uni.getSystemInfoSync().platform,
            schedule_list: [],
            schedule_list_loading: true,
            schedule_list_more: false,
        }
    },
    onLoad(e) {

        console.log('活动详情页面路径参数', e)


        // #ifdef H5
        const flat = true
        if (flat) return xwy_api.alert('请在小程序内打开', {success: () => uni.navigateBack()})
        // #endif


        if (uni.getLaunchOptionsSync().scene === 1154) return this.getSimpleDetail(e.id)

        e.screen_pic ? this.screenPicShow(e.screen_pic) : this.$uni.showLoading('数据加载中...')

        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }


            if (e.scene) return this.analysisScene(e.scene)

            if (!e.id) {
                this.loading = false
                this.error = '请指定活动id'
                return uni.hideLoading()
            }


            this.id = e.id
            this.userid = app.globalData['userid']
            this.getDetail()
            this.getScheduleList()
        })
    },


    onShareAppMessage() {
        this._utils.shareCopyActivityId(this.detail)

        let url = '/pages/match-score/user/details?id=' + this.id
        if (this.detail?.conf?.active?.screen_pic) url += `&screen_pic=${this.detail.conf.active.screen_pic}`
        return {
            title: this.detail?.conf?.active?.share_title || this.detail?.name || '',
            path: url,
            imageUrl: this.detail?.conf?.active?.share_image || this.detail?.logo || ''
        }
    },

    onShareTimeline() {
        return {
            title: this.detail.name,
            imageUrl: this.detail.logo || ''
        }
    },

    methods: {
        async getSimpleDetail(id) {
            this.$uni.showLoading('数据加载中...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details_simple',
                data: {
                    active_id: id
                }
            })
            this.loading = false
            uni.hideLoading()

            if (!res || !res.data || !res.data['active_details']) {
                this.loading = false
                uni.hideLoading()
                this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                return false
            }


            if (res.data.active_more_data) {
                const active_more_data = res.data.active_more_data
                this.active_more_data = active_more_data
                if (active_more_data.technology_support) {
                    this.technology_support = res.data.active_more_data.technology_support
                }
            }


            const detail = res.data['active_details']


            this.detail = detail

            my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)


            if (detail.conf) {
                const conf = detail.conf

                if (!this.screen_pic && conf.active?.screen_pic) {
                    this.screenPicShow(conf.active.screen_pic)
                }


                if (conf.must_submit) {
                    const must_submit = conf.must_submit
                    delete conf.must_submit
                    if (must_submit.length) {
                        must_submit.forEach(v => v.value = v.value || '')
                        this.must_submit = must_submit
                    }
                }
            }


            if (detail.name) {
                uni.setNavigationBarTitle({
                    title: detail.name
                })
            }
        },

        screenPicShow(src) {
            // #ifndef H5
            uni.hideLoading()
            this.screen_pic = src
            this.screen_pic_count_down = 5
            this.screen_pic_show = true
            interval = setInterval(() => {
                this.screen_pic_count_down--
                if (this.screen_pic_count_down <= 0) {
                    this.skipScreen()
                }
            }, 1000)
            // #endif
        },
        skipScreen() {
            clearInterval(interval)
            this.screen_pic_count_down = 0
            this.screen_pic_hide = true

            const timeout = setTimeout(() => {
                this.screen_pic_show = false
                this.screen_pic_hide = false
                if (this.loading) this.$uni.showLoading('数据加载中...')
                this.passwordDialogShow()
                clearTimeout(timeout)
            }, 500)
        },
        analysisScene(scene) {
            const sceneStr = decodeURIComponent(scene)
            console.log(sceneStr)
            const id = utils.getUrlParams('id', sceneStr)
            console.log('id===', id)
            if (!id) {
                uni.hideLoading()
                return this.$uni.showModal('从二维码获取id失败')
            }

            this.getActiveId(id)
        },


        async getActiveId(id) {
            const data = {id}

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/get_long_active_id_by_short_id',
                data
            })
            if (res?.data?.['long_active_id']) {
                this.id = res.data['long_active_id']
                this.getDetail()
            } else {
                uni.hideLoading()
                xwy_api.alert(res && res.info || '长id获取失败')
            }
        },

        async getScheduleList() {
            this.schedule_list = []
            this.schedule_list_more = false
            this.schedule_list_loading = true
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.score_mark.score/team_group_list',
                data: {
                    active_id: this.id,
                    page: this.current_page,
                    perpage: 20,
                    get_group_team: 1
                }
            })
            this.schedule_list_loading = false

            const res_data = res?.data?.list
            if (!res_data) return

            this.schedule_list = this.scheduleListInit(res_data.data || [])
            this.schedule_list_more = !res_data.is_lastpage
        },

        scheduleListInit(list) {
            return list.map(item => {
                const active_types_info = config.activeTypes.find(v => v.types === item.active_types)
                if (active_types_info) {
                    item.active_types_info = {
                        name: active_types_info.name,
                        icon: active_types_info.icon
                    }
                }
                if (item.team_list && item.team_list.length) {
                    item.team_list = item.team_list.map(team => {
                        delete team.group_id
                        delete team.active_id
                        delete team.create_time
                        delete team.active_types
                        if (team['team_details']) {
                            team.name = team['team_details'].name
                            team.headimg = team['team_details'].headimg || ''
                        }
                        delete team['team_details']
                        return team
                    })
                }
                return item
            })
        },

        liveGame(item) {
            let url = `../live-game/live-game?group_id=${item.group_id}&active_id=${this.active_id}`
            this.$uni.navigateTo(url, {
                events: {
                    updateScore: data => {
                        if (!item.team_list?.length || !data.team_list?.length) return
                        item.team_list.forEach(team => {
                            const matchingItem = data.team_list.find(item => item.id === team.id)
                            team.score = matchingItem.score
                        })
                    }
                }
            })
        },

        getDetail(just_update = false) {
            xwy_api.getActivityDetail(this.id, res => {
                if (!res || !res.data || !res.data['active_details']) {
                    this.loading = false
                    uni.hideLoading()
                    this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                    return false
                }

                // #ifndef H5
                this.$refs.expirationReminder.open(res.data.active_details)
                // #endif

                if (res.data.active_more_data) {
                    const active_more_data = res.data.active_more_data
                    this.active_more_data = active_more_data
                    if (active_more_data.technology_support) {
                        this.technology_support = res.data.active_more_data.technology_support
                    }
                }


                const detail = res.data['active_details']
                app.globalData.activity_detail = detail
                this.detail = detail
                my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)


                if (detail.conf) {
                    const conf = detail.conf

                    if (conf.active) {
                        const active = conf.active
                        if (!this.screen_pic && active.screen_pic) {
                            this.screenPicShow(active.screen_pic)
                        }

                        if (active.news?.news_id) this.changeDetailContent(active.news.news_id)
                    }

                    if (conf.must_submit) {
                        const must_submit = conf.must_submit
                        delete conf.must_submit
                        if (must_submit.length) {
                            must_submit.forEach(v => v.value = v.value || '')
                            this.must_submit = must_submit
                        }
                    }
                }

                if (detail.rank_set) {
                    const rank_set = detail.rank_set
                    // 是否纯净版，纯净版显示tab栏，隐藏返回首页按钮
                    if (rank_set['shield_other']) {
                        this.$uni.hideHomeButton()

                        // 更新纯净版缓存信息
                        utils.updateShieldOtherInfo(this.detail)
                    }
                }


                this.addLookRecords()

                if (detail.name) {
                    uni.setNavigationBarTitle({
                        title: detail.name
                    })
                }

                if (!just_update && app.globalData['userid'] === detail.userid) {
                    this.is_my_activity = true
                }

                this.getUserStatus()

            })
        },


        addLookRecords() {
            const detail = this.detail
            const value = {
                active_id: detail.active_id,
                name: detail.name,
                types: detail.types,
                logo: detail.logo || this.xwy_config.active_default_logo,
                look_time: new Date().getTime()
            }

            if (detail.organizer) value.organizer = detail.organizer
            my_storage.addActivityLookRecords(value)
        },


        async getUserStatus() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: {active_id: this.id}
            })

            if (res?.data?.user_details) {
                const attend_details = res.data.user_details
                this.user_details = attend_details
                this.is_joining = true
                this.checked = attend_details.checked || 0

                this.loading = false
                uni.hideLoading()

                if (attend_details.nickname) this.username = attend_details.nickname
                if (attend_details.headimg) this.headimg = attend_details.headimg

                if (attend_details.must_submit) {
                    this.must_submit.forEach(v => {
                        attend_details.must_submit.forEach(vv => {
                            if (vv.title === v.title) v.value = vv.value
                        })
                    })
                    this.$forceUpdate()
                }

            } else {
                this.no_attend = true
                this.loading = false
                uni.hideLoading()

                if (this.screen_pic_show) return false

                this.passwordDialogShow()
            }

        },


        changeDetailContent(news_id) {
            xwy_api.ajax({
                url: "front.news/news_details",
                data: {
                    news_id,
                    access_token: app.globalData['access_token']
                },
                success: res => {
                    uni.hideLoading()

                    uni.hideLoading()
                    if (!res.data || !res.data['news_details']) {
                        uni.showModal({
                            title: '提示',
                            content: res.info || '文章内容获取失败',
                            showCancel: false,
                            success: () => uni.navigateBack()
                        })
                        return false
                    }

                    const detail = res.data['news_details']


                    if (detail.video_url) {
                        let video_type = 'txv_id'
                        if (detail.video_url.startsWith('http://') || detail.video_url.startsWith('https://')) {
                            video_type = 'http'
                        }
                        detail.video_type = video_type
                    }


                    if (detail.content) {
                        detail.content = utils.newsContentInit(detail.content)
                    }

                    this.news_detail = detail
                }
            })
        },


        passwordDialogShow() {
            if (this.detail.conf.active.password && this.no_attend) {
                const passwordDialogShow = () => {
                    this.password_dialog_show = true
                    const password = my_storage.getActivityPassword(this.id)
                    if (password) {
                        this.password = password
                        this.checkActivityPassword(password)
                    }
                    const timeout = setTimeout(() => {
                        this.$refs.input_password.open()
                        clearTimeout(timeout)
                    }, 30)
                }

                // 体验版不需要输入密码
                if (app.globalData['evn_version'] === 'trial') {
                    return uni.showModal({
                        title: '提示',
                        content: '此活动设置了活动密码，请勿报名参与活动！！！',
                        cancelText: '进入活动',
                        confirmText: '输入密码',
                        success: res => {
                            res.confirm && passwordDialogShow()
                        }
                    })
                }


                passwordDialogShow()
            }
        },

        passwordInputConfirm(val) {
            if (!val) {
                // 纯净版取消输入密码出现去个人中心选项
                if (this.detail?.rank_set?.['shield_other']) {
                    return uni.showModal({
                        title: '提示',
                        content: '请输入密码',
                        cancelText: '个人中心',
                        confirmText: '重新输入',
                        success: res => {
                            if (res?.confirm) return this.$refs.input_password.open()
                            this.$uni.navigateTo('/pages/user/user')
                        }
                    })
                }

                return xwy_api.alert('请输入密码', {success: () => this.$refs.input_password.open()})
            }
            this.checkActivityPassword(val)
        },

        async checkActivityPassword(password) {
            this.$uni.showLoading('密码验证中...')

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/check_active_password',
                data: {
                    active_id: this.id,
                    password
                }
            })
            uni.hideLoading()

            if (res?.status === 1) {
                my_storage.rememberActivityPassword(this.id, password)
                this.$refs.input_password.close()
                return this.$uni.showToast('密码正确', 'success')
            }


            xwy_api.alert(res && res.info || '密码错误', {
                success: () => this.$refs.input_password.open()
            })
        },

        passwordInputClose() {
            this.copy(this.id, true)

            if (getCurrentPages().length > 1) return uni.navigateBack()

            // 纯净版并且没有上一 页面，重新弹出输入密码窗口
            if (app.globalData['shop_info']?.extend_set?.shield_other_active?.active_id)
                return xwy_api.alert('请输入活动密码', {success: () => this.$refs.input_password.open()})

            this.$uni.reLaunch('/pages/index/index')
        },


        async updateHeadimg() {
            uni.navigateTo({
                url: `/pages/other/headimg-list/headimg-list?active_id=${this.id}`,
                events: {
                    newImg: async obj => {
                        if (!obj?.src) return
                        this.$uni.showLoading('修改中...')
                        if (!obj.temp) return this.updateAttendDetail(obj.src)
                        const data = {
                            temp_data: {
                                path: obj.src
                            },
                            is_temp: 5
                        }
                        if (obj.size) data.temp_data.size = obj.size
                        const headimg = await xwy_api.uploadOneImage(data)
                        this.updateAttendDetail(headimg)
                    }
                }
            })
        },

        joinActivity() {
            if (this.loading) return
            if (this.detail.conf?.active?.submit) {
                const now_time = new Date().getTime()
                const {begin, end} = this.detail.conf.active.submit
                if (begin) {
                    const begin_time = new Date(begin.replace(/-/g, '/')).getTime()
                    if (now_time < begin_time) {
                        xwy_api.alert(`活动于${begin}开始报名`)
                        return
                    }
                }
                if (end) {
                    const end_time = new Date(end.replace(/-/g, '/')).getTime()
                    if (now_time > end_time) {
                        xwy_api.alert(`活动于${end}截止报名`)
                        return
                    }
                }
            }

            this.join_popup_show = true
        },

        updateAttendDetailShow() {
            this.update_attend_details = true
            this.join_popup_show = true
        },

        mustValueChange(e, index) {
            this.must_submit[index].value = this.must_submit[index].options[e.detail.value].text
            this.$forceUpdate()
        },


        cancelJoin() {
            this.join_popup_show = false
            this.update_attend_details = false
        },

        joinAjax() {
            if (this.update_attend_details) {
                this.updateAttendDetail()
                return false
            }


            this.joining()
        },


        getMustSubmitData() {
            const must_submit = JSON.parse(JSON.stringify(this.must_submit))
            for (let i = 0; i < must_submit.length; i++) {
                const v = must_submit[i]
                v.options && delete v.options
                if (v.rules === 1 && v.value === '') {
                    let tips = '输入'
                    if (v.types === 2) tips = '选择'
                    uni.showToast({
                        title: `请${tips}${v.title}`,
                        mask: true,
                        icon: v.title.length <= 4 ? 'error' : 'none'
                    })
                    return false
                }
            }
            console.log(must_submit)
            let must_submit_str = JSON.stringify(must_submit)
            must_submit_str = must_submit_str.replace(/·/g, '-')
            return base64['encode'](must_submit_str)
        },

        updateAttendDetail(headimg) {
            const data = {
                active_id: this.id,
                access_token: app.globalData['access_token']
            }

            if (headimg) data.headimg = headimg

            const must_submit = this.getMustSubmitData()
            if (must_submit === false) return false
            data.must_submit = must_submit

            this.$uni.showLoading('修改中...')

            xwy_api.ajax({
                url: 'front.flat.sport_step.user/update_attend_details',
                data,
                success: res => {
                    if (res?.status !== 1) return this.$uni.showModal(res?.info || '修改失败')

                    this.$uni.showToast('修改成功', 'success')

                    this.cancelJoin()
                    this.getDetail()
                }
            })
        },

        joining() {

            const data = {
                active_id: this.id,
                access_token: app.globalData['access_token']
            }


            if (this.must_submit && this.must_submit.length) {
                const must_submit = this.getMustSubmitData()
                if (must_submit === false) return false
                data.must_submit = must_submit
            }

            data.nickname = this.username

            this.loading = true
            this.$uni.showLoading('报名中...')


            xwy_api.ajax({
                url: 'front.flat.sport_step.user/submit_attend_active',
                data,
                success: res => {
                    this.loading = false
                    uni.hideLoading()
                    console.log('报名活动', res)
                    if (!res.status) {
                        uni.showModal({
                            title: '报名失败',
                            content: res.info || '报名失败',
                            showCancel: false
                        })
                        return false
                    }

                    this.join_popup_show = false
                    uni.showToast({
                        title: res.info || '报名成功',
                        icon: 'success'
                    })

                    setTimeout(() => {
                        uni.showLoading({
                            mask: true
                        })
                        this.getDetail()
                    }, 1000)
                }
            })
        },


        toGuessing() {
            const errorToast = title => this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none')

            const question_type = this.detail.conf.active.question_type || 2

            // 设置了使用自己的题库，并且没有初始化题库生成题库id
            if (!this.detail.conf.active.question_bank_id && question_type === 2) {
                return errorToast('活动未设置题目')
            }

            if (!this.is_joining) return errorToast('还未参与活动')
            if (this.detail.conf.active.enter_types === 3 && !this.checked) return errorToast('报名未审核')

            if (!activity_tool.actionCheck({
                is_joining: this.is_joining,
                checked: this.checked,
                begin_time: this.detail.begin_time,
                end_time: this.detail.end_time
            })) return false

            uni.navigateTo({
                url: `../admin/topic/list?type=user&id=${this.id}`
            })
        },

        // toTopList() {
        //     uni.navigateTo({
        //         url: '/pages/ranking-list/ranking-list?id=' + this.id
        //     })
        // },

        showActiveSharePopup() {
            this.$refs.activeShare.open({
                page: 'pages/match-score/user/details',
                scene: 'id=' + this.detail.id,
                qrcode_logo: this.detail.rank_set?.closed_AD && this.detail.conf?.active?.qrcode_logo
            })
        },

        copy(data, hide_toast = false) {
            uni.setClipboardData({
                data,
                success: () => hide_toast ? uni.hideToast() : this.$uni.showToast('复制成功', 'none', 500)
            })
        },

        uniPopupClose(ref) {
            this.popup_open = false
            this.$refs[ref].close()
        },

        uniPopupOpen(ref) {
            this.popup_open = true
            this.$refs[ref].open()
        }

    }
}
</script>

<style scoped>
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 80px;
}

.bdb-10 {
    border-bottom: 10px solid #f8f8f8;
}

.icon-list {
    border-top: 1px solid #eee;
}

.icon-item {
    padding: 10px 0;
    width: calc(100% / 4);
    /* border-top: 1px solid #eee; */
    box-sizing: border-box;
}


.headimg {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}


.join-btn {
    width: 250px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 20px;
}

.join-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, .7);
    z-index: 9999;
}

.join-popup-c {
    border-radius: 10px;
    overflow: hidden;
}


.join-input {
    margin: 10px;
    padding: 10px;
    border: 1px solid #bbb;
    border-radius: 5px;
    width: 70vw;
    max-width: 400px;
}

.join-popup-btns {
    border-top: 1px solid #eee;
}

.join-popup-btn-cancel, .join-popup-btn-confirm {
    width: 50%;
    box-sizing: border-box;
    line-height: 44px;
}

.join-popup-btn-cancel {
    border-right: 1px solid #eee;
}


.uni-popup-info {
    position: relative;
    width: 280px;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    z-index: 999;
    left: 50%;
    bottom: -5px;
    margin-left: -12px;
}

.detail-popup {
    width: 95vw;
    padding-bottom: 15px;
}

@keyframes rotate {
    100% {
        transform: rotate(360deg);
    }
}

.schedule-item {
    margin: 0 10px 20px;
    padding: 10px;
    border-radius: 5px;
    box-shadow: 0 0 5px #e5e5e5;
    background-color: #fff;
}

</style>
