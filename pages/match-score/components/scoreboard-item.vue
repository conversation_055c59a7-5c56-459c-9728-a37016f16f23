<template>
    <view>
        <view :class="'scoreboard-' + type">
            <view class="team-name ellipsis">{{ data.name }}</view>
            <view class="score" :class="{ellipsis: data.score.toString().length > 6}"
                  :style="{fontSize: setScoreFontSize(data.score)}">
                {{ data.score }}
            </view>

        </view>
    </view>
</template>

<script>
export default {
    name: "scoreboard-item",
    props: ['type', 'data'],
    methods: {
        setScoreFontSize(score) {
            const sizes = {
                1: {default_: '240rpx', over500: '150px'},
                2: {default_: '160rpx', over500: '120px'},
                3: {default_: '100rpx', over500: '80px'},
                4: {default_: '80rpx', over500: '60px'},
                5: {default_: '70rpx', over500: '50px'},
                6: {default_: '60rpx', over500: '40px'}
            }
            const over6 = {default_: '40rpx', over500: '30px'}
            const length = score.toString().length
            const over500 = uni.getWindowInfo().windowWidth > 500
            const size_key = over500 ? 'over500' : 'default_'
            return sizes[length]?.[size_key] || over6[size_key]
        }
    }
}
</script>

<style lang="scss">
.scoreboard-home, .scoreboard-away {
    width: 240rpx;
    text-align: center;

    .team-name {
        height: 76rpx;
        line-height: 76rpx;
        font-size: 36rpx;
        color: #fff;
    }

    .score {
        font-weight: bold;
        height: 240rpx;
        line-height: 240rpx;
    }
}

.scoreboard-home {
    .team-name {
        background-color: #1F9EDD;
    }
    .score {
        color: #1F9EDD;
    }
}
.scoreboard-away {
    .team-name {
        background-color: #E75048;
    }
    .score {
        color: #E75048;
    }
}


@media (min-width: 500px) {
    .scoreboard-home, .scoreboard-away {
        top: 26px;

        .team-name {
            width: 160px;
            height: 50px;
            margin-bottom: 32px;
            text-align: center;
            line-height: 50px;
            font-size: 24px;
        }

        .score {
            width: 160px;
            height: 150px;
            line-height: 150px;
            text-align: center;
            font-weight: bold;
        }
    }

    .scoreboard-home {
        left: 32px;
    }
    .scoreboard-away {
        right: 40px;
    }
}
</style>
