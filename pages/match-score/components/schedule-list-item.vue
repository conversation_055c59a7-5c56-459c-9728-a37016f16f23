<template>
    <view>
        <view class="flex-kai pb10">
            <view class="color-title ellipsis">{{ item.name }}</view>
            <view v-if="item.active_types_info" class="no-wrap pl10">
                <text v-if="item.active_types_info.icon" style="padding-right: 2px;"
                      :class="'iconfont color-sub font14 ' + item.active_types_info.icon"></text>
                <text class="color-content font14">{{ item.active_types_info.name }}</text>
            </view>
        </view>

        <view v-if="item.team_list && item.team_list.length" class="flex-kai team-list pt10">
            <template v-for="(team, team_index) in item.team_list">
                <view class="team-item text-center" :key="team.id"
                      :style="{width: `calc(100% / ${item.team_list.length} - 50px)`}">
                    <view class="team-logo">
                        <image :src="team.headimg" mode="aspectFill"/>
                    </view>
                    <view class="color-title">{{ team.name }}</view>
                    <view class="font24 font-bold pt5">{{ team.score }}</view>
                </view>
                <view v-if="team_index < item.team_list.length - 1"
                      class="flex-all-center font18 color-sub">VS
                </view>
            </template>
        </view>
    </view>
</template>

<script>
export default {
    name: "schedule-list-item",
    props: ['item']
}
</script>

<style scoped lang="scss">
.team-logo {
    image {
        width: 24px;
        height: 24px;
        border-radius: 50%;
    }
}
</style>
