<template>
    <view class="page bg-background">
        <view v-if="total" class="flex-kai bg-white">
            <view class="color-sub font14 p10">共{{ total }}场比赛</view>
            <view v-if="is_admin" class="p10" @click="createSchedule">
                <text class="color-light-primary font14">创建比赛</text>
                <uni-icons type="forward" size="14" color="#5cadff"/>
            </view>
        </view>

        <template v-if="show_ad">
            <xwy-ad :ad_type="66"/>
            <xwy-ad :ad_type="3"/>
        </template>

        <view v-if="total && is_admin" class="pt10 color-sub font12 text-center">点击某场比赛可设置比赛分数</view>

        <view class="list">
            <template v-for="(item, index) in list">
                <view class="item" :key="item.group_id" @click="liveGame(item)">
                    <schedule-list-item :item="item"/>
                    <view v-if="is_admin" class="clearfix clearfix pt10">
                        <view class="fr color-sub" @click.stop="deleteSchedule(item)">删除赛程</view>
                    </view>
                </view>

                <xwy-ad v-if="show_ad && ((index + 1) % 10 === 0)" :ad_type="66"/>
            </template>
        </view>

        <view v-if="!loading && !list.length" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无比赛</view>
            <view v-if="is_admin" class="flex-all-center pt15">
                <button class="add-team-btn color-white bg-primary" @click="createSchedule">创建比赛</button>
            </view>
        </view>

        <view v-if="loading" class="text-center">
            <view v-if="current_page === 1" style="padding-top: 30vh;"></view>
            <load-ani/>
            <view v-if="current_page === 1" class="font14 color-sub">加载中</view>
        </view>

    </view>
</template>

<script>
import scheduleListItem from "../components/schedule-list-item.vue"
import config from "@/pages/match-score/config";

export default {
    components: {
        scheduleListItem
    },
    data() {
        return {
            loading: true,
            list: [],
            current_page: 1,
            is_last_page: false,
            total: 0,
            is_admin: false,
            show_ad: false
        }
    },

    onLoad(params) {
        this.active_id = params.id
        if (params.team_id) this.team_id = params.team_id
        if (params.group_id) this.group_id = params.group_id
        if (params['admin']) this.is_admin = true

        this.getActiveDetails()
        this.getList()
    },

    onPullDownRefresh() {
        this.current_page = 1
        this.getList().finally(() => uni.stopPullDownRefresh())
    },

    onReachBottom() {
        if (this.is_last_page || this.loading) return
        this.current_page++
        this.getList()
    },

    methods: {
        async getActiveDetails() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details',
                data: {active_id: this.active_id}
            })
            const details = res?.data?.active_details
            if (!details) return
            if (!details.rank_set?.closed_AD) this.show_ad = true
        },

        async getList() {
            if (this.current_page === 1) {
                this.list = []
                this.is_last_page = false
                this.total = 0
            }

            this.loading = true
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.score_mark.score/team_group_list',
                data: {
                    active_id: this.active_id,
                    page: this.current_page,
                    perpage: 20,
                    name: this.search_keyword,
                    get_group_team: 1
                }
            })
            this.loading = false

            const res_data = res?.data?.list
            if (!res_data) {
                this.is_last_page = true
                return
            }

            const list = this.listInit(res_data.data || [])
            this.list = [...this.list, ...list]
            this.is_last_page = res_data.is_lastpage
            this.total = res_data.total
        },

        listInit(list) {
            return list.map(item => {
                const active_types_info = config.activeTypes.find(v => v.types === item.active_types)
                if (active_types_info) {
                    item.active_types_info = {
                        name: active_types_info.name,
                        icon: active_types_info.icon
                    }
                }
                if (item.team_list && item.team_list.length) {
                    item.team_list = item.team_list.map(team => {
                        delete team.group_id
                        delete team.active_id
                        delete team.create_time
                        delete team.active_types
                        if (team['team_details']) {
                            team.name = team['team_details'].name
                            team.headimg = team['team_details'].headimg || ''
                        }
                        delete team['team_details']
                        return team
                    })
                }
                return item
            })
        },

        createSchedule() {
            this.$uni.navigateTo(`../admin/team/list?id=${this.active_id}&sel_team_to_game=1`, {
                events: {
                    uploadList: () => {
                        this.current_page = 1
                        this.getList()
                    }
                }
            })
        },

        liveGame(item) {
            // if (!item.active_types) return this.$uni.showToast('未知的比赛类型, 无法进入比赛', 'none', 3000)
            let url = `../live-game/live-game?group_id=${item.group_id}&active_id=${this.active_id}`
            if (this.is_admin) url += '&is_admin=1'
            this.$uni.navigateTo(url, {
                events: {
                    updateScore: data => {
                        if (!item.team_list?.length || !data.team_list?.length) return
                        item.team_list.forEach(team => {
                            const matchingItem = data.team_list.find(item => item.id === team.id)
                            team.score = matchingItem.score
                        })
                    }
                }
            })
        },

        async deleteSchedule(item) {
            const modal = await this.$uni.showModal(`确定删除${item.name}吗?`, {
                showCancel: true
            })
            if (!modal.confirm) return

            this.$uni.showLoading('删除中...')
            const res = await this.xwy_api.deleteRecords(78, item.group_id)
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '删除失败')
            this.$uni.showToast('删除成功', 'success')

            this.current_page = 1
            await this.getList()
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
}

.list {
    padding: 10px 0;
    .item {
        margin: 0 10px 20px;
        padding: 10px;
        border-radius: 5px;
        box-shadow: 0 0 5px #e5e5e5;
        background-color: #fff;
    }
}

.add-team-btn {
    width: 200px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
}

</style>
