<template>
    <view class="page bg-background">
        <view class="top-view">
            <view class="search bg-white flex-kai p10">
                <view class="input-view">
                    <view class="search-icon left-icon flex-all-center">
                        <uni-icons type="search" size="20" color="#bbbec4"/>
                    </view>
                    <input
                        class="input bg-background"
                        type="text"
                        confirm-type="search"
                        v-model="search_keyword"
                        @confirm="search()"
                        placeholder="输入你要搜索的队伍名称"
                        placeholder-style="color:#bbbec4"
                    />
                    <view class="search-icon right-icon flex-all-center" @click="search_keyword = ''">
                        <uni-icons v-if="search_keyword" type="close" size="20" color="#bbbec4"/>
                    </view>
                </view>
                <view class="color-info pl10" @click="search">搜索</view>
            </view>


            <view v-if="sel_team_to_game" class="p10 bg-white" @click="changeActiveTypes">
                <text class="color-sub pr5">比赛类型:</text>
                <text class="color-light-primary">{{ active_types_name }}</text>
                <uni-icons type="forward" size="16" color="#5cadff"></uni-icons>
            </view>
        </view>

        <view style="width: 100%; height: 60px;"></view>
        <view v-if="sel_team_to_game" style="width: 100%; height: 40px;"></view>


        <template v-if="list.length">
            <view v-if="!sel_team_to_game" class="flex-kai">
                <view class="color-sub p10 font14">共{{ count }}个队伍</view>
                <view v-if="!is_sel" class="color-primary p10" @click="addEditaTeam(null)">
                    <text>创建队伍</text>
                    <uni-icons type="forward" color="#2d8cf0"/>
                </view>
            </view>

            <view class="list ptm5">
                <view class="item bg-white flex-row" v-for="item in list" :key="item.id">
                    <view v-if="!is_sel" class="flex-all-center pr10" @click="checkItem(item)">

                        <radio :checked="item.is_check" :disabled="select_disabled && !item.is_check"/>
                    </view>
                    <view style="width: 100%;">
                        <view class="flex-row" style="width: 100%;">
                            <view v-if="item.headimg" class="pr10">
                                <image class="logo-image" :src="item.headimg" mode="aspectFill"/>
                            </view>
                            <view style="width: 100%;">
                                <view class="color-title">{{ item.name }}</view>
                                <view v-if="item.group_name" class="color-sub font14 pt5">
                                    {{ item.group_name }}
                                </view>
                            </view>
                        </view>
                        <view style="height: 10px;"></view>
                        <view class="tools-bar clear clearfix">
                            <view class="fl">
                                <text class="color-sub font14">序号: {{ item.active_sort }}</text>
                            </view>

                            <view class="fr flex-row">
                                <template v-if="show_edit">
                                    <view class="edit" @click="addEditaTeam(item)">
                                        <text class="iconfont icon-edit color-sub font20"></text>
                                    </view>
                                    <view class="delete" @click="deleteItem(item.id)">
                                        <text class="iconfont icon-delete color-sub font20"></text>
                                    </view>
                                </template>
                                <template v-if="is_sel">
                                    <button size="mini" class="bg-primary color-white"
                                            @click="selectTeam(item)">选择此队伍
                                    </button>
                                </template>
                            </view>
                        </view>
                    </view>
                </view>
            </view>


            <view v-if="!is_sel" class="bottom-bar bg-white clear clearfix">
                <view class="fl">
                    <view v-if="sel_team_to_game" class="color-sub font14">
                        已选 {{ sel_team_list.length }}/{{ max_sel_count }} 个队伍
                    </view>
                    <view v-else @click="checkAllChange">

                        <radio :checked="is_check_all"/>
                        <text class="color-sub font14">全选</text>
                    </view>
                </view>
                <view class="flex-row fr">
                    <view v-if="sel_team_to_game" class="batch-btn"
                          :class="sel_team_list.length === max_sel_count ? 'color-primary' : 'color-disabled'"
                          hover-class="navigator-hover" @click="setSchedule">安排赛程
                    </view>
                    <view v-else class="batch-btn color-error" hover-class="navigator-hover"
                          @click="batchDelete">批量删除
                    </view>
                </view>
            </view>
        </template>

        <uni-popup ref="select_active_types_popup">
            <view class="popup-- bg-white">
                <view class="popup--title text-center color-content">选择比赛类型</view>
                <view class="active-types-popup-list flex-row flex-wrap">
                    <view class="active-types-item flex-column flex-all-center"
                          :class="{'active-types-active': item.types === sel_active_types}"
                          v-for="item in activeTypes" :key="item.types"
                          @click="activeTypesChange(item.types)">
                        <view>
                            <text :class="'iconfont ' + item.icon"></text>
                        </view>
                        <view class="active-types-name font14">{{ item.name }}</view>
                    </view>
                </view>
                <view class="flex-all-center">
                    <view class="confirm-active-types-button bg-primary color-white text-center"
                          hover-class="navigator-hover" @click="activeTypesConfirm">确定
                    </view>
                </view>
            </view>
            <view class="text-center pt5" @click="$refs.select_active_types_popup.close">
                <uni-icons type="close" size="24" color="#ffffff"></uni-icons>
            </view>
        </uni-popup>

        <uni-popup ref="create_schedule_popup">
            <view class="popup-- bg-white">
                <view class="popup--title text-center color-content">安排赛程</view>
                <view class="create-schedule">
                    <uni-forms label-position="top" border>
                        <uni-forms-item label="赛程名称" required>
                            <uni-easyinput v-model="group_name" :maxlength="20" placeholder="请输入赛程名称"/>
                        </uni-forms-item>
                        <uni-forms-item label="比赛类型">
                            <view style="color: #333;">{{ active_types_name }}</view>
                        </uni-forms-item>
                        <uni-forms-item label="比赛队伍">
                            <view class="create-schedule-team-list flex-kai">
                                <template v-for="(item, index) in sel_team_list">
                                    <view class="create-schedule-team-item" :key="item.id"
                                          :style="{width: `calc(100% / ${sel_team_list.length} - 30px)`}">
                                        <view class="create-schedule-team-logo">
                                            <image :src="item.headimg"/>
                                        </view>
                                        <view>{{ item.name }}</view>
                                    </view>
                                    <view v-if="index < sel_team_list.length - 1"
                                          class="flex-all-center font18 color-sub">VS</view>
                                </template>
                            </view>
                        </uni-forms-item>
                    </uni-forms>

                </view>
                <view class="flex-all-center pt15">
                    <view class="confirm-active-types-button bg-primary color-white text-center"
                          hover-class="navigator-hover" @click="createSchedule">确定
                    </view>
                </view>
            </view>
            <view class="text-center pt5" @click="$refs.create_schedule_popup.close">
                <uni-icons type="close" size="24" color="#ffffff"></uni-icons>
            </view>
        </uni-popup>

        <view v-if="!loading && !list.length" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无队伍</view>
            <view v-if="!is_sel && !sel_team_to_game" class="flex-all-center pt15">
                <button class="add-team-btn color-white bg-primary" @click="addEditaTeam(null)">
                    创建队伍
                </button>
            </view>
        </view>

        <view v-if="loading" class="text-center">
            <view v-if="load_page === 1" style="padding-top: 30vh;"></view>
            <load-ani/>
            <view v-if="load_page === 1" class="font14 color-sub">加载中</view>
        </view>

    </view>
</template>

<script>

const app = getApp()
import login from '@/utils/api/login.js'
import config from '../../config'


export default {
    data() {
        return {
            is_sel: false,  // 是不是进来选择队伍的
            id: '',
            loading: true,
            list: [],
            load_page: 1,
            is_last_page: false,
            count: 0,
            search_keyword: '',
            is_check_all: false,
            sel_team_to_game: false,
            max_sel_count: 2,
            activeTypes: config.activeTypes,
            active_types: config.activeTypes[0].types,
            sel_active_types: config.activeTypes[0].types,
            group_name: ''
        }
    },

    computed: {
        select_disabled() {
            if (!this.sel_team_to_game) return false
            const max = this.max_sel_count
            const check_count = this.sel_team_list.length
            return check_count >= max
        },

        show_edit() {
            return !this.is_sel && !this.sel_team_to_game
        },

        sel_team_list() {
            return this.list.filter(v => v.is_check)
        },

        active_types_name() {
            return this.activeTypes.find(item => item.types === this.active_types)?.name || ''
        },
    },

    onLoad(e) {
        if (e.select || e['sel_team_to_game']) this.$uni.setNavigationBarTitle('选择队伍')
        if (e.select) this.is_sel = true
        if (e['sel_team_to_game']) {
            this.sel_team_to_game = true
            this.$nextTick(() => this.$refs.select_active_types_popup.open())
        }
        this.id = e.id
        login.uniLogin(err => {
            if (err && err.errMsg) {
                this.loading = false
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getList()
        })
    },

    onReachBottom() {
        if (!this.loading && !this.is_last_page) {
            this.load_page++
            this.getList()
        }
    },

    methods: {
        search() {
            this.getListInit()
        },


        getListInit() {
            this.load_page = 1
            this.getList()
        },

        async getList() {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
            }

            this.loading = true
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.score_mark.score/team_list',
                data: {
                    active_id: this.id,
                    page: this.load_page,
                    perpage: 20,
                    name: this.search_keyword
                }
            })
            this.loading = false

            const res_data = res?.data?.list
            const list = this.listDataInit(res_data.data || [])
            this.list = [...this.list, ...list]
            this.is_last_page = res_data.is_lastpage
            this.count = res_data.total
        },

        listDataInit(list) {
            return list.map(v => {
                v.is_check = false
                if (v['team_group_details']) {
                    v.group_name = v['team_group_details'].name || ''
                    delete v['team_group_details']
                }
                return v
            })
        },


        addEditaTeam(item) {
            let url = 'add?active_id=' + this.id
            if (!item) url += `&count=${this.count}`
            this.$uni.navigateTo(url, {
                events: {
                    uploadList: () => this.search()
                },
                success: res => {
                    item && res.eventChannel.emit('team_info', item)
                }
            })
        },

        deleteItem(id) {
            this.deleteConfirm([id])
        },

        checkItem(item) {
            item.is_check = !item.is_check
            if (item.is_check) {
                this.inspectCheckAll()
            } else {
                if (this.is_check_all) this.is_check_all = false
            }
        },

        // 循环数据列表，检查是否全选
        inspectCheckAll() {
            this.is_check_all = this.list.every(value => value.is_check)
        },

        checkAllChange() {
            this.is_check_all = !this.is_check_all
            this.list.forEach(v => v.is_check = this.is_check_all)
        },

        getCheckId() {
            return this.sel_team_list.map(v => v.id)
        },

        batchDelete() {
            const ids = this.getCheckId()
            if (!ids) {
                uni.showToast({
                    title: '请选择需要删除的队伍',
                    mask: true,
                    icon: 'none'
                })
                return false
            }

            this.deleteConfirm(ids)
        },

        deleteConfirm(ids) {
            uni.showModal({
                title: '提示',
                content: `确定删除${ids.length > 1 ? '选中的' : '该'}队伍?`,
                confirmText: '删除',
                cancelColor: '#80848f',
                confirmColor: '#ed3f14',
                success: res => {
                    if (res.confirm) this.deleteAjax(ids)
                }
            })
        },

        async deleteAjax(ids) {
            this.$uni.showLoading('删除中...')
            const res = await this.xwy_api.deleteRecords(77, ids.join(','))
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '删除失败')
            this.$uni.showToast('删除成功', 'success')
            const timeout = setTimeout(() => {
                this.search()
                clearTimeout(timeout)
            }, 1000)
        },

        selectTeam(team) {
            const eventChannel = this.getOpenerEventChannel()
            eventChannel && eventChannel.emit('setTeam', {team_id: team.id, team_name: team.name})
            uni.navigateBack()
        },

        changeActiveTypes() {
            this.sel_active_types = this.active_types
            this.$refs.select_active_types_popup.open()
        },

        async activeTypesChange(types) {
            if (this.sel_active_types === types) return
            this.sel_active_types = types
        },

        async activeTypesConfirm() {
            const types = this.sel_active_types
            if (types === this.active_types) return this.$refs.select_active_types_popup.close()
            this.active_types = types
            if (this.sel_team_list.length) {
                const res = await this.$uni.showModal('比赛类型已切换，是否清空已选队伍？', {
                    showCancel: true,
                    confirmText: '清空',
                    cancelText: '不清空'
                })
                res.confirm && this.list.forEach(v => v.is_check = false)
            }
            this.$refs.select_active_types_popup.close()
        },


        setSchedule() {
            const sel_team_list = this.sel_team_list
            const max = this.max_sel_count
            const check_count = sel_team_list.length
            if (!check_count) return this.$uni.showToast('请选择队伍', 'none')
            if (check_count > max) return this.$uni.showToast(`最多选择${max}个队伍`, 'none')
            if (check_count < 2) return this.$uni.showToast('至少选择2个队伍', 'none')


            this.group_name = `【${this.active_types_name}】${sel_team_list.map(v => v.name).join(' vs ')}`
            return this.$refs.create_schedule_popup.open()

            /*const team_names = sel_team_list.map(v => v.name).join('、')

            const tips = `确定安排 ${team_names} ${check_count}只球队进行${this.active_types_name}比赛吗？`
            const modal = await this.$uni.showModal(tips, {showCancel: true})
            if (!modal.confirm) return*/

            // 检测队伍的比赛类型是否和选择的比赛类型一致
        },

        async createSchedule() {
            if (!this.group_name) return this.$uni.showToast('请输入赛程名称')

            const sel_team_list = this.sel_team_list
            const team_list = sel_team_list.map(v => {
                return {
                    team_id: v.id
                }
            })
            this.$uni.showLoading('安排中...')

            const create_group_res = await this.xwy_api.request({
                url: 'front.flat.sport_step.score_mark.score/create_group',
                data: {
                    active_id: this.id,
                    name: this.group_name,
                    active_types: this.active_types
                }
            })
            const group_id = create_group_res?.data?.['res']
            if (!group_id) {
                uni.hideLoading()
                return this.$uni.showModal(group_id?.info || '赛程安排失败')
            }

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.score_mark.score/set_team_for_pk',
                data: {
                    active_id: this.id,
                    team_list: this._utils.base64['encode'](JSON.stringify(team_list)),
                    active_types: this.active_types,
                    group_id
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '赛程安排失败')
            this.$uni.showToast('赛程安排成功', 'success')

            this.getOpenerEventChannel().emit('uploadList')
            this.$uni.navigateBack(1, {delay: 1000})
        }
    }
}
</script>

<style scoped lang="scss">
.page {
    min-height: 100vh;
    padding-bottom: 60px;
    box-sizing: border-box;
}

.top-view {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9;
}

.search, .search .input {
    height: 40px;
    line-height: 40px;
}

.input-view {
    position: relative;
}

.search-icon {
    position: absolute;
    width: 30px;
    height: 40px;
    top: 0;
}

.left-icon {
    left: 0;
}

.right-icon {
    right: 0;
}

.search .input {
    width: calc(100vw - 65px);
    padding: 0 30px;
    border-radius: 20px;
    box-sizing: border-box;
}

.item {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
}

.logo-image {
    width: 50px;
    height: 50px;
    border-radius: 5px;
    display: block;
}

.tools-bar {
    border-top: 1px solid #eee;
    padding-top: 10px;
}

.edit, .delete {
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 50%;
}

.edit {
    border: 1px solid #eee;
    margin-right: 10px;
}

.delete {
    border: 1px solid #eee;
}

.add-team-btn {
    width: 200px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
}

.bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 10px 10px 20px;
    box-sizing: border-box;
    border-top: 1px solid #eee;
}

/* #ifdef H5 */

@media screen and (min-width: 500px) {
    .search {
        width: 500px;
        left: calc(((100vw - 500px) / 2) - 10px);
    }

    .search .input {
        width: calc(500px - 65px);
    }
}

/* #endif */

.popup-- {
    width: 700rpx;
    padding: 20rpx;
    box-sizing: border-box;
    border-radius: 10px;
    .popup--title {
        padding-bottom: 20rpx;
        border-bottom: 1px solid #eee;
    }
}

.active-types-popup-list {
    padding: 20px 0;
}

.active-types-item {
    border-radius: 5px;
    width: 200rpx;
    height: 200rpx;
    margin: 10rpx;
    border: 2rpx solid #eee;
    box-sizing: border-box;

    .active-types-name, .iconfont {
        color: #666;
    }

    .iconfont {
        font-size: 100rpx;
    }
}
.active-types-active {
    background-color: #f7faff;
    border: 2rpx solid #5cadff;
    .active-types-name, .iconfont {
        color: #5cadff
    }
}
.confirm-active-types-button {
    width: 200px;
    line-height: 40px;
    border-radius: 20px;
}

.create-schedule-team-list {
    .create-schedule-team-item {
        text-align: center;
        .create-schedule-team-logo {
            image {
                width: 30px;
                height: 30px;
                border-radius: 50%;
            }
        }
    }
}

</style>
