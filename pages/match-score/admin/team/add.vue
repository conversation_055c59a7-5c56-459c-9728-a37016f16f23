<template>
    <view class="page">
        <view class="logo pt10 text-center">
            <image v-if="headimg" :src="headimg" class="logo-image" mode="aspectFill" @click="setLogo"/>
            <view v-if="!headimg" class="no-logo flex-all-center bg-background" @click="setLogo">
                <uni-icons type="camera" size="50" color="#999999"/>
            </view>
            <view class="flex-all-center">
                <view class="p5 color-light-primary" hover-class="navigator-hover" @click="setLogo">
                    更换队伍图标
                </view>
            </view>
        </view>
        <view class="p10">
            <uni-forms label-position="top" label-width="300" border>
                <uni-forms-item label="队伍名称" required>
                    <uni-easyinput v-model="name" :maxlength="20" placeholder="请输入分组名称"/>
                </uni-forms-item>

                <!--<uni-forms-item label="队伍所属分组">
                    <view class="forms-picker flex-kai">
                        <view style="width: 100%;" @click="selectGroup">
                            <text v-if="group_id" style="color: #333;">{{ group_name || group_id }}</text>
                            <text v-else class="color-disabled">请选择队伍所属分组</text>
                        </view>
                        <view>
                            <uni-icons v-if="group_id" type="clear" size="15" color="#c0c4cc"
                                       @click="clearGroup"/>
                            <uni-icons v-else type="forward" size="16" color="#dddee1" @click="selectGroup"/>
                        </view>
                    </view>
                </uni-forms-item>-->

                <uni-forms-item label="队伍序号">
                    <uni-easyinput v-model="active_sort" type="number" :maxlength="5"
                                   placeholder="数字越小排在越前"/>
                </uni-forms-item>

            </uni-forms>
        </view>

        <view class="save bg-white">
            <button class="save-button bg-primary color-white" @click="validate">
                {{ id ? '保存' : '创建' }}
            </button>
        </view>

        <!-- #ifdef MP-WEIXIN -->
        <!--<ws-wx-privacy id="privacy-popup"/>-->
        <!-- #endif -->
    </view>
</template>

<script>
const app = getApp()

export default {
    data() {
        return {
            headimg: '',
            name: '',
            id: null,
            active_types: 1,
            active_sort: 1,
            // group_id: '',
            group_name: ''
        }
    },


    onLoad(params) {
        this.active_id = params.active_id
        if (params.count) this.active_sort = Number(params.count) + 1
        this.eventChannel()
    },

    methods: {
        eventChannel() {
            this.getOpenerEventChannel().on('team_info', res => {
                this.$uni.setNavigationBarTitle('队伍信息')
                this.id = res.id
                this.name = res.name
                if (res.headimg) this.headimg = res.headimg
                if (res.active_sort) this.active_sort = res.active_sort
                if (res.active_types) this.active_types = res.active_types
                // if (res.group_id) this.group_id = res.group_id
                if (res.group_name) this.group_name = res.group_name
            })
        },

        setLogo() {
            this.$uni.navigateTo('/pages/other/image_upload_or_select', {
                events: {
                    newImg: src => {
                        this.headimg = src
                    }
                }
            })
        },

        /*selectGroup() {
            this.$uni.navigateTo(`../team-group/list?id=${this.active_id}&select=1`, {
                events: {
                    selectGroup: res => {
                        this.group_id = res.id
                        this.group_name = res.name
                    }
                }
            })
        },

        clearGroup() {
            this.group_id = ''
            this.group_name = ''
        },*/


        validate() {
            if (!this.name) return this.$uni.showToast('请输入队伍名称', 'none')
            const sort = Math.floor(this.active_sort)
            if (isNaN(sort) || sort < 0) return this.$uni.showToast('请输入正确的队伍序号', 'none', 2000)
            this.active_sort = sort
            this.submit()
        },


        async submit() {
            const data = {
                active_id: this.active_id,
                name: this.name,
                active_sort: this.active_sort,
                headimg: this.headimg,
                active_types: this.active_types,
                // group_id: this.group_id
            }
            if (this.id) data.id = this.id

            this.$uni.showLoading('保存中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.score_mark.score/create_team',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '保存失败')
            this.$uni.showToast('保存成功', 'success')
            this.back()
        },

        back() {
            this.getOpenerEventChannel?.()?.emit('uploadList')
            this.$uni.navigateBack(1, {delay: 1000})
        },
    }
}
</script>

<style lang="scss">
.page {
    padding-bottom: 80px;
}

.logo {
    .logo-image, .no-logo {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        margin: 0 auto;
    }
}

.forms-picker {
    border-radius: 4px;
    border: 1px solid #e5e5e5;
    padding: 0 5px;
    height: 36px;
    line-height: 36px;
}

.save {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 2;
    width: 100vw;
    box-sizing: border-box;
    padding: 15px 20px;
    border-top: 1px solid #eee;
}

.save-button {
    padding: 0;
    margin: 0;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}

.save-button::after {
    content: "";
    border: none;
}

.uni-forms-item.is-direction-top .uni-forms-item__label {
    padding-bottom: 0 !important;
}

.forms-picker {
    border-radius: 4px;
    border: 1px solid #e5e5e5;
    padding: 0 5px;
    height: 36px;
    line-height: 36px;
}
</style>
