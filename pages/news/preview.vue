<template>
    <view>
        <view class="page" :class="{'page-bg': garbageActivity}">
            <template v-if="detail">
                <image v-if="detail.thumb_pic" class="logo" :src="detail.thumb_pic" mode="widthFix"/>
                <view class="p10 flex-kai" :class="{'bg-white': !garbageActivity}"
                      :style="garbageActivity ? '' : 'border-bottom: 1px solid #eee;'">
                    <view>
                        <view v-if="detail.title" class="color-title font18">{{ detail.title }}</view>
                    </view>
                    <view v-if="!article_bind_exam && !reward_step_by_reading" class="flex-all-center">
                        <button class="share-btn" type="default" plain size="mini" open-type="share">
                            <text class="iconfont icon-share color-disabled font24"></text>
                        </button>
                    </view>
                </view>

                <view v-if="detail.content" class="content p10">
                    <u-parse :content="detail.content"/>
                </view>

                <view v-if="article_bind_exam && detail.conf_set && detail.conf_set.bind_exam_id"
                      class="to-exam-button text-center bg-red color-white" hover-class="navigator-hover"
                      @click="toExam">去答题
                </view>

                <view v-if="answer_need_read" :class="['to-exam-button text-center color-white', answer_need_read_seconds ? 'to-exam-button-disabled' : 'to-exam-button-active']"
                      hover-class="navigator-hover" @click="toNeedReadAnswer">
                    <text>去答题</text>
                    <text v-if="answer_need_read_seconds" class="pl5">({{ answer_need_read_seconds }})</text>
                </view>

            </template>

            <template v-if="type === 'support' && !closed_create_active">
                <button class="contact bg-green" open-type="contact">
                    <text class="iconfont icon-chat-bubble color-white font34"></text>
                </button>
                <view class="add-activity bg-white">
                    <navigator
                        class="text-center bg-primary color-white"
                        url="/pages/create-activity/index"
                    >
                        创建活动
                    </navigator>
                </view>
            </template>

            <view v-if="garbageActivityStandby && seconds > 0" class="reading-reward-countdown">
                {{ seconds }}
            </view>

            <uni-popup v-if="garbageActivityStandby" ref="gameTips" :is-mask-click="false">
                <view class="game-tips bg-white">
                    <view class="game-tips-title">说明</view>
                    <view class="game-tips-content color-content">
                        <view>
                            阅读
                            <template v-if="seconds">{{ seconds }}秒，</template>
                            <template v-if="integralMin && integralMax">
                                <template v-if="integralMin === integralMax">奖励{{ integralMin }}积分</template>
                                <template v-else>随机奖励 {{ integralMin }}积分 - {{ integralMax }}积分</template>
                                。
                            </template>
                        </view>
                    </view>

                    <view class="p10">
                        <view class="start-game-button" @click="readingStart">开始阅读</view>
                    </view>
                </view>
            </uni-popup>
        </view>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import base64 from '@/utils/base64.js'

export default {
    data() {
        return {
            id: '',
            type: 'default',   // 'default': 默认,查看文章列表  'support': 联系客服、技术支持
            detail: null,
            closed_create_active: false,
            imageWidth: uni.getSystemInfoSync().windowWidth - 20,
            article_bind_exam: false,
            reward_step_by_reading: false,

            // 是不是垃圾活动，鲜繁客户定制开发那个垃圾分类活动，需要显示说明弹窗和倒计时
            garbageActivity: false,
            garbageActivityStandby: false,
            seconds: 0,
            integralMin: 0,
            integralMax: 0,
            answer_need_read: false,
            answer_need_read_seconds: 0
        }
    },

    onShareAppMessage() {
        this.inShare = true
        return {
            title: this.detail.title,
            path: '/pages/news/preview?id=' + this.id + '&type=' + this.type,
            imageUrl: this.detail.avatar || ''
        }
    },

    // 因为无法监听转发事件，如果一执行onShareAppMessage就奖励的话，很容易被用户发现不用真实转发也能获得奖励。
    // 而转发成功或取消转发时，会触发onShow，所以在onShow后执行奖励
    onShow() {
        if (this.inShare) {
            this.inShare = false
            this.shareReward()
        }


        if (this.needBack) this.$uni.navigateBack()
    },

    onUnload() {
        this.clearReadInterval()
        this.clearAnswerNeedReadInterval()
        if (this.readRewardTimeout) clearTimeout(this.readRewardTimeout)
    },


    onLoad(e) {
        this.id = e.id
        if (e.type) this.type = e.type
        if (e.active_id) {
            this.active_id = e.active_id
            if (e.active_id === 'e500dedf72811dfd04d474c3ceb1ee5c') this.garbageActivity = true
        }
        if (e.point_id) this.point_id = Number(e.point_id)
        if (e.seconds) this.seconds = Number(e.seconds)
        if (e.i_min) this.integralMin = Number(e.i_min)
        if (e.i_max) this.integralMax = Number(e.i_max)
        if (e.article_bind_exam) this.article_bind_exam = true
        if (e.reward_step_by_reading) this.reward_step_by_reading = true
        if (e.share_reward) this.share_reward = true

        // 健步走设置了需要阅读点位知识后才能答题
        if (e.answer_need_read) {
            this.answer_need_read = true
            this.answer_need_read_seconds = Number(e.answer_need_read_seconds)
            this.answer_need_read_data = {
                active_id: e.active_id,
                point_id: e.point_id,
                exam_id: e.exam_id
            }
        }


        // 不能转发出去的，学习的文章只能通过列表进来
        if (e.article_bind_exam || e.reward_step_by_reading) uni.hideShareMenu(undefined)

        uni.showLoading({
            title: '加载中...',
            mask: true
        })

        login.uniLogin(err => {
            this.closed_create_active = xwy_api.closed_create_active()

            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getDetail()
        })
    },

    methods: {
        async getDetail() {
            const data = {
                news_id: this.id
            }
            if (this.article_bind_exam || this.reward_step_by_reading) {
                data.reading_logs = 1
                data.active_id = this.active_id
                data.userid = app.globalData['userid']
            }
            
            const res = await this.xwy_api.request({
                url: "front.news/news_details",
                data
            })
            uni.hideLoading()

            const detail = res.data['news_details']
            
            if (!detail) {
                return this.$uni.showModal(res.info || '文章内容获取失败', {
                    success: () => uni.navigateBack()
                })
            }

            // 使用服务器返回的时间戳来做开始阅读的时间，避免用户设备的时间与服务器的时间有差异
            if (res.data['current_time']) this.time_to_start_reading = res.data['current_time']
            
            if (detail.thumb_pic) this.logo = detail.thumb_pic
            if (detail.title) this.$uni.setNavigationBarTitle(detail.title)

            if (detail.content) detail.content = utils.newsContentInit(detail.content)

            this.detail = detail

            this.studyComplete()

            // 阅读文章奖励积分
            await this.readingRewardCheck()
            // 阅读文章奖励积分

            this.answerNeedReadIntervalShow()
        },

        answerNeedReadIntervalShow() {
            if (!this.answer_need_read || !this.answer_need_read_seconds) return

            this.answer_need_read_interval = setInterval(() => {
                this.answer_need_read_seconds--
                if (this.answer_need_read_seconds <= 0) {
                    this.clearAnswerNeedReadInterval()
                }
            }, 1000)
        },

        clearAnswerNeedReadInterval() {
            if (!this.answer_need_read_interval) return
            clearInterval(this.answer_need_read_interval)
            this.answer_need_read_interval = null
        },

        toNeedReadAnswer() {
            if (this.answer_need_read_seconds) return this.$uni.showToast('请等待倒计时结束')

            const {active_id, point_id, exam_id} = this.answer_need_read_data

            // 这个活动阅读完点位知识文章，去答题，答完题后需要返回点位知识文章的上一页面
            if (active_id === '056a8df70d15bd6747d2ac5bfc1038c4') this.needBack = true

            this.$uni.navigateTo(`/pages/likou_dati/pages/exam/exam_details/exam_details?exam_id=${exam_id}&active_id=${active_id}&point_id=${point_id}`)
        },

        async shareReward() {
            if (!this.share_reward) return

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify({
                        types: 20,
                        point_id: this.point_id,
                        result: 'success'
                    }))
                }
            })

            const num = res.data.num
            if (num) this.$uni.showToast(`奖励${num}积分`)
            this.getOpenerEventChannel().emit('success')
        },

        studyComplete() {
            if (!this.article_bind_exam && !this.reward_step_by_reading) return
            this.getOpenerEventChannel?.()?.emit?.('studyComplete')  
        },
        
        // 阅读文章奖励积分
        async readingRewardCheck() {
            this.time_to_start_reading ||= Math.floor(new Date().getTime() / 1000) // 记录开始阅读的时间

            // if (this.point_id) return this.taskReadingReward()

            const active_id = this.detail['category_details']?.active_id
            if (!active_id) return
            let activity_details = app.globalData['activity_detail']
            // 如果不是从文章分类绑定的活动进来的，就重新获取分类绑定的活动的活动详情
            if (activity_details?.active_id !== active_id) activity_details = null
            if (!activity_details) {
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: { active_id }
                })
                activity_details = res.data.activity_details
            }
            if (!activity_details?.rank_set?.reading_reward) return
            if (!activity_details?.conf?.active?.reading_reward?.open) return
            const rules = activity_details.conf.active.reading_reward.rules
            if (!rules?.length) return
            const target = rules.find(item => item.category_id === this.detail.category_id)
            if (!target) return
            const seconds = Number(target.seconds)
            if (isNaN(seconds) || seconds <= 0) return this.getRewards()
            this.seconds = seconds

            this.taskReadingReward()

            /*this.readRewardTimeout = setTimeout(() => {
                this.getRewards()
            }, seconds * 1000)*/
        },

        readingStart() {
            this.$refs.gameTips.close()
            this.time_to_start_reading ||= Math.floor(new Date().getTime() / 1000) // 记录开始阅读的时间

            this.readInterval = setInterval(() => {
                this.seconds--
                if (this.seconds <= 0) {
                    this.clearReadInterval()
                    this.getRewards()
                }
            }, 1000)
        },

        clearReadInterval() {
            if (!this.readInterval) return
            clearInterval(this.readInterval)
            this.readInterval = null
        },


        taskReadingReward() {
            if (this.garbageActivity) {
                this.garbageActivityStandby = true
                this.$nextTick(() => this.$refs.gameTips.open())
                return
            }



            if (!this.seconds) return

            // 由于有的设备计时可能存在问题，导致计时不准确，有1-3秒误差，导致没有奖励到
            const seconds = this.seconds + 3

            this.readRewardTimeout = setTimeout(() => {
                this.getRewards()
            }, seconds * 1000)
        },

        async getRewards() {
            const data = {
                news_id: this.id,
                sign: utils.randomCoding() + base64['encode'](`${this.id}+${this.time_to_start_reading}`),
            }
            if (this.point_id) data.point_id = this.point_id

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.integral.reading/reading_reward_active_integral',
                data
            })
            if (res?.status === 1) {
                this.getOpenerEventChannel?.()?.emit?.('success')
                if (res.info) {
                    uni.showToast({
                        title: res.info,
                        icon: res.info.length <= 7 ? 'success' : 'none',
                        duration: 3000
                    })
                }
            }
        },

        toExam() {
            this.$uni.navigateTo(`/pages/likou_dati/pages/exam/exam_details/exam_details?exam_id=${this.detail.conf_set.bind_exam_id}&active_id=${this.active_id}`)
        }
    }
}
</script>

<style lang="scss">
.page {
    padding-bottom: 60px;
}

.page-bg {
    height: 100vh;
    overflow-y: auto;
    box-sizing: border-box;
    background-image: url("https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/red-small-games/lian-lian-kan/shenmu/bg.jpg");
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}

.logo {
    width: 100vw;
    height: auto;
    display: block;
}

.share-btn {
    padding: 0;
    margin: 0;
    border: none !important;
    width: 40px;
    min-width: 40px;
    height: 40px;
    line-height: 40px;
}

.share-btn::after {
    border: none;
}

.content img {
    max-width: calc(100vw - 20px) !important;
}

.content image {
    max-width: calc(100vw - 20px) !important;
}

.contact {
    position: fixed;
    bottom: 150px;
    right: 10px;
    width: 50px;
    height: 50px;
    line-height: 50px;
    border-radius: 50%;
    padding: 0;
    margin: 0;
    border: none;
    opacity: .7;
}

.contact::after {
    border: none;
}

.to-exam-button {
    margin: 20px auto;
    width: 200px;
    line-height: 44px;
    border-radius: 5px;
}

.to-exam-button-active {
    background-color: #5cadff;
}

.to-exam-button-disabled {
    background-color: #bbbec4;
}

.add-activity {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100vw;
    box-sizing: border-box;
    padding: 10px;
}

.add-activity navigator {
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
}

.game-tips {
    width: 90vw;
    border-radius: 10px;

    .game-tips-title {
        line-height: 44px;
        text-align: center;
    }

    .game-tips-content {
        padding: 0 20px;
        line-height: 30px;
    }

    .start-game-button {
        width: 150px;
        line-height: 40px;
        border-radius: 20px;
        text-align: center;
        font-size: 18px;
        background-color: #ff985e;
        color: #fff;
        margin: 10px auto;
    }
}

.reading-reward-countdown {
    position: fixed;
    top: 100px;
    right: 20px;
    width: 44px;
    height: 44px;
    line-height: 44px;
    border-radius: 50%;
    text-align: center;
    font-size: 18px;
    background-color: rgba(0, 0, 0, .7);
    color: #fff;
}
</style>
