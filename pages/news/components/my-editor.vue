<template>
	<view>
		<view
			class="toolbar"
			@click="format"
			:style="'bottom: ' + (isIOS ? keyboardHeight : 0) + 'px'"
		>
			<text class="iconfont icon-charutupian" @click.stop="insertImage"></text>
			<text :class="'iconfont icon-format-header-1 ' + (formats.header === 1 ? 'ql-active' : '')" data-name="header"
			 :data-value="1"></text>
			<text :class="'iconfont icon-format-header-2 ' + (formats.header === 2 ? 'ql-active' : '')" data-name="header"
			 :data-value="2"></text>
			<text :class="'iconfont icon-format-header-3 ' + (formats.header === 3 ? 'ql-active' : '')" data-name="header"
			 :data-value="3"></text>
			<text :class="'iconfont icon-format-header-4 ' + (formats.header === 4 ? 'ql-active' : '')" data-name="header"
			 :data-value="4"></text>
			<text :class="'iconfont icon-format-header-5 ' + (formats.header === 5 ? 'ql-active' : '')" data-name="header"
			 :data-value="5"></text>
			<text :class="'iconfont icon-format-header-6 ' + (formats.header === 6 ? 'ql-active' : '')" data-name="header"
			 :data-value="6"></text>
			<text :class="'iconfont icon-zitijiacu ' + (formats.bold ? 'ql-active' : '')" data-name="bold"></text>
			<text :class="'iconfont icon-zitishanchuxian ' + (formats.strike ? 'ql-active' : '')" data-name="strike"></text>
			<text :class="'iconfont icon-zitixieti ' + (formats.italic ? 'ql-active' : '')" data-name="italic"></text>
			<text :class="'iconfont icon-zuoduiqi ' + (formats.align === 'left' ? 'ql-active' : '')" data-name="align" data-value="left"></text>
			<text :class="'iconfont icon-juzhongduiqi ' + (formats.align === 'center' ? 'ql-active' : '')" data-name="align"
			 data-value="center"></text>
			<text :class="'iconfont icon-youduiqi ' + (formats.align === 'right' ? 'ql-active' : '')" data-name="align" data-value="right"></text>
			<text :class="'iconfont icon-zuoyouduiqi ' + (formats.align === 'justify' ? 'ql-active' : '')" data-name="align"
			 data-value="justify"></text>
<!--			<text :class="'iconfont icon-line-height ' + (formats.lineHeight ? 'ql-active' : '')" data-name="lineHeight" data-value="2"></text>-->
<!--			<text :class="'iconfont icon-Character-Spacing ' + (formats.letterSpacing ? 'ql-active' : '')" data-name="letterSpacing" data-value="2em"></text>-->
<!--			<text :class="'iconfont icon-722bianjiqi_duanqianju ' + (formats.marginTop ? 'ql-active' : '')" data-name="marginTop" data-value="20px"></text>-->
<!--			<text :class="'iconfont icon-723bianjiqi_duanhouju ' + (formats['micon-previewarginBottom'] ? 'ql-active' : '')" data-name="marginBottom" data-value="20px"></text>-->
			<!-- <text class="iconfont icon-clearedformat" @tap="removeFormat"></text> -->
			<!-- <text :class="'iconfont icon-font ' + (formats.fontFamily ? 'ql-active' : '')" data-name="fontFamily" data-value="Pacifico"></text> -->
			<text :class="'iconfont icon-fontsize ' + (formats.fontSize === '24px' ? 'ql-active' : '')" data-name="fontSize"
			 data-value="24px"></text>
			 <text class="iconfont icon-text_color" :style="'color: ' + formats.color + ';'" data-name="color" :data-value="fontColor" @tap="open"></text>
			<!-- <text :class="'iconfont icon-fontbgcolor ' + (formats.backgroundColor === '#00ff00' ? 'ql-active' : '')" data-name="backgroundColor" data-value="#00ff00"></text> -->
			<!-- 引用黄河浪的color取色器 -->
			<!-- <text class="iconfont icon-date" @tap="insertDate"></text> -->
			<text class="iconfont icon-undo" @tap="undo"></text>
			<text class="iconfont icon-redo" @tap="redo"></text>
			<text :class="'iconfont icon-zitixiahuaxian ' + (formats.underline ? 'ql-active' : '')" data-name="underline"></text>
			<!-- <text class="iconfont icon--checklist" data-name="list" data-value="check"></text> -->
			<!-- <text :class="'iconfont icon-youxupailie ' + (formats.list === 'ordered' ? 'ql-active' : '')" data-name="list" data-value="ordered"></text> -->
			<!-- <text :class="'iconfont icon-wuxupailie ' + (formats.list === 'bullet' ? 'ql-active' : '')" data-name="list" data-value="bullet"></text> -->
			<text class="iconfont icon-outdent" data-name="indent" data-value="-1"></text>
			<text class="iconfont icon-indent" data-name="indent" data-value="+1"></text>
			<text class="iconfont icon-fengexian" @tap="insertDivider"></text>
			<!-- <text class="iconfont icon-preview" @tap="store" id="2"></text> -->
<!--			<text :class="'iconfont icon-zitixiabiao ' + (formats.script === 'sub' ? 'ql-active' : '')" data-name="script" data-value="sub"></text>-->
<!--			<text :class="'iconfont icon-zitishangbiao ' + (formats.script === 'super' ? 'ql-active' : '')" data-name="script" data-value="super"></text>-->
			<!-- <text class="iconfont icon-quanping"></text> -->
			<text class="iconfont icon-shanchu" @tap="clear"></text>
			<!-- <text :class="'iconfont icon-direction-rtl ' + (formats.direction === 'rtl' ? 'ql-active' : '')" data-name="direction"
			 data-value="rtl"></text> -->
			<!-- <text class="iconfont icon-baocun" @tap="store" id="1"></text> -->
		</view>

		<view class="container">
			<editor
				id="editor"
				show-img-size
				:read-only="isEdit"
				show-img-resize
				show-img-toolbar
				class="ql-container"
				:placeholder="placeholder"
				:style="'height: calc(100vh - ' + page_dom_height + 'px - 190px);'"
				@statuschange="onStatusChange"
				@ready="onEditorReady"
			></editor>

		</view>

		<t-color-picker ref="colorPicker" :color="color" @confirm="confirm" @cancel="cancel"></t-color-picker>
	</view>
</template>

<script>
	import tColorPicke from './t-color-picker.vue';

	export default {
		name: 'my-editor',
		components: {
			't-color-picker': tColorPicke
		},
		props: {
			value: {
				type: String,
				default: ''
			},
			placeholder: {
				type: String,
				default: '开始输入...'
			},
			page_dom_height: {
				type: Number,
				default: 0
			},
			is_vip: {
				type: String,
				default: '0'
			}
		},
		data() {
			return {
				color: {
					r: 255,
					g: 0,
					b: 0,
					a: 0.6
				},
				isEdit: false,
				fontColor: '#000',
				formats: {},
				readOnly: false,
				editorHeight: 300,
				keyboardHeight: 0,
				isIOS: false,
				insertImageSrc: ''
			};
		},



		methods: {
			cancel() {
				this.isEdit = false;
			},
			open() {
				this.$refs.colorPicker.open();
				this.isEdit = true;
				// uni.hideKeyboard();
			},
			hideKey() {
				uni.hideKeyboard();
			},
			async confirm(e) {
				this.isEdit = false;
				this.fontColor = await e.hex;
				this.onStatusChange({
					detail: {
						color: e.hex
					}
				});
				this.$forceUpdate();
			},
			readOnlyChange() {
				this.readOnly = !this.readOnly
			},
			onEditorReady() {
				console.log('onEditorReady')
				const query = uni.createSelectorQuery().in(this);
				query.select('#editor').context(res => {
					this.editorCtx = res.context
					this.editorCtx.setContents({
						html: this.value
					})
				}).exec();
			},
			undo() {
				this.editorCtx.undo();
			},
			redo() {
				this.editorCtx.redo();
			},

			blur() {
				this.editorCtx.blur();
			},

			format(e) {
				let {
					name,
					value
				} = e.target.dataset;
				if (!name) return;
				this.editorCtx.format(name, value);
			},

			onStatusChange(e) {
				this.formats = e.detail;
			},

			insertDivider() {
				this.editorCtx.insertDivider({
					success: () => {
						console.log('insert divider success');
					}
				});
			},

			store() {
				this.editorCtx.getContents({
					success: res => {
						console.log('保存内容:', res)
						this.$emit('confirm', res.html)
					}
				})
			},
            
            getHtml() {
                return new Promise(resolve => {
                    this.editorCtx.getContents({
                        success: res => resolve(res.html)
                    })
                })
            },

			clear() {
				this.editorCtx.clear({
					success: () => {
						console.log("clear success");
						uni.showToast({
							title: '内容已清空，可点击上一步选项恢复已清除的内容',
							icon: 'none',
							duration: 2000
						})
					}
				})
			},

			removeFormat() {
				this.editorCtx.removeFormat();
			},

			insertDate() {
				const date = new Date();
				const formatDate = `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
				this.editorCtx.insertText({
					text: formatDate
				})
			},

			insertImage() {
				if (this.is_vip === '0') {
					this.xwy_api.alert('暂无插入图片权限，请联系客服开通。')
					return false
				}

				uni.navigateTo({
					url: '/pages/other/image_upload_or_select',
					events: {
						newImg: src => {
							this.editorCtx.insertImage({
								src,
								width: '100%',
								success: () => {
									console.log('insert image success');
								},
								fail: err => {
									console.log(err)
								}
							})
						}
					}
				})
			}
		}
	};
</script>
<style>
	@font-face {font-family: "iconfont";
	  src: url('//at.alicdn.com/t/font_945958_zfsfjju1dim.eot?t=1547618146468'); /* IE9 */
	  src: url('//at.alicdn.com/t/font_945958_zfsfjju1dim.eot?t=1547618146468#iefix') format('embedded-opentype'), /* IE6-IE8 */
	  url('//at.alicdn.com/t/font_945958_zfsfjju1dim.woff?t=1547618146468') format('woff'),
	  url('//at.alicdn.com/t/font_945958_zfsfjju1dim.ttf?t=1547618146468') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
	  url('//at.alicdn.com/t/font_945958_zfsfjju1dim.svg?t=1547618146468#iconfont') format('svg'); /* iOS 4.1- */
	}

	.iconfont {
	  font-family: "iconfont" !important;
	  font-size: 16px;
	  font-style: normal;
	  -webkit-font-smoothing: antialiased;
	  -moz-osx-font-smoothing: grayscale;
	}

	.icon-redo:before {
	  content: "\e627";
	}

	.icon-undo:before {
	  content: "\e633";
	}

	.icon-indent:before {
	  content: "\eb28";
	}

	.icon-outdent:before {
	  content: "\e6e8";
	}

	.icon-fontsize:before {
	  content: "\e6fd";
	}

	.icon-format-header-1:before {
	  content: "\e860";
	}

	.icon-format-header-4:before {
	  content: "\e863";
	}

	.icon-format-header-5:before {
	  content: "\e864";
	}

	.icon-format-header-6:before {
	  content: "\e865";
	}

	.icon-clearup:before {
	  content: "\e64d";
	}

	.icon-preview:before {
	  content: "\e631";
	}

	.icon-date:before {
	  content: "\e63e";
	}

	.icon-fontbgcolor:before {
	  content: "\e678";
	}

	.icon-clearedformat:before {
	  content: "\e67e";
	}

	.icon-font:before {
	  content: "\e684";
	}

	.icon-723bianjiqi_duanhouju:before {
	  content: "\e65f";
	}

	.icon-722bianjiqi_duanqianju:before {
	  content: "\e660";
	}

	.icon-text_color:before {
	  content: "\e72c";
	}

	.icon-format-header-2:before {
	  content: "\e75c";
	}

	.icon-format-header-3:before {
	  content: "\e75d";
	}

	.icon--checklist:before {
	  content: "\e664";
	}

	.icon-baocun:before {
	  content: "\ec09";
	}

	.icon-line-height:before {
	  content: "\e7f8";
	}

	.icon-quanping:before {
	  content: "\ec13";
	}

	.icon-direction-rtl:before {
	  content: "\e66e";
	}

	.icon-direction-ltr:before {
	  content: "\e66d";
	}

	.icon-selectall:before {
	  content: "\e62b";
	}

	.icon-copy:before {
	  content: "\ec7a";
	}

	.icon-shanchu:before {
	  content: "\ec7b";
	}

	.icon-editsekuai:before {
	  content: "\ec7c";
	}

	.icon-fengexian:before {
	  content: "\ec7f";
	}

	.icon-dianzan:before {
	  content: "\ec80";
	}

	.icon-charulianjie:before {
	  content: "\ec81";
	}

	.icon-charutupian:before {
	  content: "\ec82";
	}

	.icon-wuxupailie:before {
	  content: "\ec83";
	}

	.icon-juzhongduiqi:before {
	  content: "\ec84";
	}

	.icon-yinyong:before {
	  content: "\ec85";
	}

	.icon-youxupailie:before {
	  content: "\ec86";
	}

	.icon-youduiqi:before {
	  content: "\ec87";
	}

	.icon-zitidaima:before {
	  content: "\ec88";
	}

	.icon-xiaolian:before {
	  content: "\ec89";
	}

	.icon-zitijiacu:before {
	  content: "\ec8a";
	}

	.icon-zitishanchuxian:before {
	  content: "\ec8b";
	}

	.icon-zitishangbiao:before {
	  content: "\ec8c";
	}

	.icon-zitibiaoti:before {
	  content: "\ec8d";
	}

	.icon-zitixiahuaxian:before {
	  content: "\ec8e";
	}

	.icon-zitixieti:before {
	  content: "\ec8f";
	}

	.icon-zitiyanse:before {
	  content: "\ec90";
	}

	.icon-zuoduiqi:before {
	  content: "\ec91";
	}

	.icon-zitiyulan:before {
	  content: "\ec92";
	}

	.icon-zitixiabiao:before {
	  content: "\ec93";
	}

	.icon-zuoyouduiqi:before {
	  content: "\ec94";
	}

	.icon-duigoux:before {
	  content: "\ec9e";
	}

	.icon-guanbi:before {
	  content: "\eca0";
	}

	.icon-shengyin_shiti:before {
	  content: "\eca5";
	}

	.icon-Character-Spacing:before {
	  content: "\e964";
	}


	.container {
		/* position: absolute;
		top: 120px;
		left: 0; */
		width: 100%;
		border-bottom: 1px solid #eee;
	}

	.ql-container {
		box-sizing: border-box;
		width: 100%;
		font-size: 16px;
		line-height: 1.5;
		overflow: auto;
		padding: 10px;
	}

	.ql-active {
		color: #22C704;
	}

	.iconfont {
		display: inline-block;
		width: 32px;
		height: 30px;
		cursor: pointer;
		font-size: 20px;
	}

	.toolbar {
		box-sizing: border-box;
		padding: 10px;
		height: 130px;
		width: 100%;
		/* position: fixed;
		left: 0;
		top: 0; */
		background: #fff;
		right: 100%;
		display: flex;
		flex-wrap: wrap;
		text-align: center;
		border-bottom: 1rpx solid #eee;
	}
</style>
