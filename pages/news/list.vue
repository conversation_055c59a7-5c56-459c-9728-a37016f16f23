<template>
    <view class="page bg-background">
        <view class="top-main uni-app--maxwidth">
            <view class="search" :class="{'pb0': category_list.length}">
                <input
                    type="text"
                    class="search-input"
                    v-model="search_keyword"
                    confirm-type="search"
                    @confirm="search()"
                    placeholder="输入文章标题搜索"
                />
                <view class="search-button bg-primary" @click="search">搜索</view>
            </view>
            <view v-if="category_list.length && !category_list_hide" class="type-bar bg-white pl10 pr10">
                <scroll-view scroll-x style="width: calc(100vw - 20px);">
                    <view class="flex-row">
                        <view
                            class="type-item text-center font14 color-content"
                            :class="{'activate-type': item.category_id === category_id}"
                            v-for="item in category_list"
                            :key="item.category_id"
                            @click="changeCategory(item.category_id)"
                        >{{ item.name }}
                        </view>
                    </view>
                </scroll-view>
            </view>
            <view v-if="total_count" class="bg-white flex-kai">
                <view class="p10 color-sub font14">共 {{ total_count }} 篇文章</view>
                <view v-if="type === 'user'" class="p10 color-primary" @click="toEdit(null)">
                    <text>添加文章</text>
                    <uni-icons type="forward" color="#2d8cf0"/>
                </view>
            </view>
        </view>

        <view v-if="category_list.length" style="height: 40px;"></view>

        <view class="list">
            <view
                class="item bg-white"
                v-for="(item, index) in list"
                :key="index"
                @click="previewNews(item.news_id)"
            >
                <view class="top flex-row">
                    <image v-if="item.thumb_pic" class="logo" :src="item.thumb_pic" mode="aspectFill"/>
                    <view class="pl10 flex-column flex-kai w-100">
                        <view class="color-title ellipsis--l2">{{ item.title }}</view>
                        <view class="flex-kai w-100">
                            <view class="color-sub font12 pt5">{{ item.create_time }}</view>
                            <view v-if="show_is_read && item.have_read" class="font12">
                                <view class="have-read bg-red color-white">已阅读</view>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="tools-bar flex-kai">
                    <view>
                        <view v-if="is_sel" class="color-primary" @click.stop="selItem(index)">选择此文章
                        </view>
                    </view>
                    <view v-if="type === 'user'" class="flex-row">
                        <!-- <view hover-stop-propagation @click.stop="generateVoice(item)" class="edit">
                          <uni-icons type="sound" size="20" color="#80848f"/>
                        </view> -->
                        <view @click.stop="toEdit(item.news_id)" class="edit">
                            <text class="iconfont icon-edit color-sub font20"></text>
                        </view>
                        <view class="delete" @click.stop="deleteItem(item.news_id)">
                            <text class="iconfont icon-delete color-sub font20"></text>
                        </view>
                    </view>

                </view>
            </view>
        </view>


        <view v-if="!loading && !list.length" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无文章</view>
            <view class="flex-all-center p20">
                <view
                    v-if="type === 'user'"
                    class="not-btn text-center bg-primary color-white"
                    @click="toEdit(null)"
                >
                    发布文章
                </view>
            </view>
        </view>

        <uni-load-more v-if="loading && load_page > 1" status="loading"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && list.length > 5"
            status="noMore"
            :contentText="{contentnomore: '我是有底线的'}"
        ></uni-load-more>
        <uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import uniapp_api from '@/utils/api/uniapp_api.js'

export default {
    data() {
        return {
            loading: true,
            type: 'who',  //who: 商户的文章， user: 用户的文章
            is_sel: false,  //是否进入选择文章的
            list: [],
            load_page: 1,
            is_last_page: false,
            total_count: 0,
            is_vip: '0',
            category_list: [],
            category_id: 0,
            change_voice_data: {
                speed: 5,
                pitch: 5,
                volume: 5
            },
            search_keyword: '',
            category_list_hide: false,
            show_is_read: false
        }
    },
    onLoad(e) {
        if (e['vip']) this.is_vip = e['vip']
        if (e.type) this.type = e.type
        if (e.is_sel) this.is_sel = true
        if (e.active_id) this.active_id = e.active_id
        if (e.article_bind_exam) this.article_bind_exam = true
        if (e.reward_step_by_reading) this.reward_step_by_reading = true
        if (e.just_look_active) this.just_look_active = true
        if (e.category_id) this.category_id = e.category_id
        if (e.show_is_read) this.show_is_read = true
        if (e.point_id) this.point_id = Number(e.point_id)
        if (e.category_list) {
            const category_list = JSON.parse(e.category_list)
            if (category_list.length) {
                this.category_id = category_list[0].id
                this.category_list = category_list.map(item => {
                    return {
                        category_id: item.id,
                        name: item.n,
                        seconds: item.s,
                        i_min: Number(item.i_min) || 0,
                        i_max: Number(item.i_max) || 0
                    }
                })
                if (category_list.length === 1) this.category_list_hide = true
            }
        }

        uni.showLoading({
            title: '加载中...',
            mask: true
        })

        login.uniLogin(err => {

            if (err && err.errMsg) return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})

            if (this.type === 'user') this.getCategoryList()
            this.initGetList()
        })
    },

    onShow() {
        // 文章阅读奖励积分成功后，返回文章列表页面需要自动返回上一页，避免点击其他文章进去阅读奖励积分
        if (this.readingRewardSuccess) this.$uni.navigateBack()
    },

    onReachBottom() {
        if (!this.loading && !this.is_last_page) this.initGetList()
    },
    methods: {
        async getCategoryList() {
            const data = {
                types: 8,
                page: this.load_page,
                perpage: 1000,
                my_self: 1
            }
            if (this.category_id) data.category_id = this.category_id
            if (this.just_look_active && this.active_id) data.active_id = this.active_id

            const res = await xwy_api.getCategoryList(data)

            const list = res?.data?.category_list?.data || []

            if (!list.length) return

            this.category_list = [
                {category_id: 0, name: '全部'},
                ...list
            ]
        },

        changeCategory(id) {
            if (id === this.category_id) return false
            this.category_id = id
            this.search()
        },


        initGetList() {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
            }

            this.getList()
        },

        search() {
            this.load_page = 1
            this.initGetList()
        },

        getList() {
            this.loading = true
            const data = {
                access_token: app.globalData['access_token'],
                page: this.load_page,
                perpage: 10,
                category_id: this.category_id,
                title: this.search_keyword
            }
            if (this.type === 'user') data.my_self = 1
            if (this.active_id && this.just_look_active) {
                data.active_id = this.active_id

                //【1】获取健步走活动是否有阅读这篇文章，返回阅读状态以及今天阅读的文章id
                if (this.show_is_read) data.get_list_types = 1
            }

            xwy_api.ajax({
                url: 'front.news/news_article_list',
                data,
                success: res => {
                    uni.hideLoading()
                    this.loading = false
                    const data = res.data.article_list

                    if (!data) {
                        if (this.load_page === 1) this.total_count = 0
                        this.is_last_page = true
                        return
                    }

                    const list = data.data || []
                    this.load_page++
                    this.list = [...this.list, ...list]
                    this.is_last_page = data.is_lastpage
                    this.total_count = data.total || 0
                }
            })
        },

        generateVoice(item) {
            if (item.voice_src) {
                uni.showModal({
                    title: '提示',
                    content: '该文章已生成语音，是否继续生成?',
                    confirmText: '继续生成',
                    success: res => {
                        res.confirm && this.generateVoiceAjax(item)
                    }
                })
                return false
            }

            uni.showModal({
                title: '提示',
                content: '生成文章语音后，用户在阅读文章时可选择语音播放文章内容。是否生成？',
                confirmText: '生成',
                success: res => {
                    res.confirm && this.generateVoiceAjax(item)
                }
            })
        },

        async generateVoiceAjax(item) {
            uniapp_api.showLoading({
                title: '生成中...'
            })

            const {speed, pitch, volume} = this.change_voice_data
            const data = {
                access_token: app.globalData['access_token'],
                news_id: item.news_id,
                speed,
                pitch,
                volume
            }
            const mp3_job = await xwy_api.request({
                url: 'front.newsArticleToMp3/article_create_mp3_job',
                data
            })
            uni.hideLoading()
            if (!mp3_job?.status) {
                xwy_api.alert(mp3_job?.info || '生成失败，请重试！')
                return false
            }
        },

        previewNews(id) {
            let url = `./preview?id=${id}`
            if (this.active_id) url += `&active_id=${this.active_id}`
            if (this.point_id) {
                url += `&point_id=${this.point_id}`
                const category = this.category_list.find(item => item.category_id === this.category_id)
                if (category?.seconds) url += `&seconds=${category.seconds}`
                if (category?.i_min && category?.i_max) {
                    url += `&i_min=${category.i_min}&i_max=${category.i_max}`
                }
            }

            if (this.show_is_read) url += '&reward_step_by_reading=1'
            this.$uni.navigateTo(url, {
                events: {
                    success: () => {
                        this.getOpenerEventChannel?.()?.emit?.('success')
                        this.readingRewardSuccess = true
                    },
                    studyComplete: () => {
                        this.list.forEach(item => {
                            if (item.news_id === id) item.have_read = true
                        })
                    }
                }
            })
        },

        toEdit(id) {
            let url = './edit?is_vip=' + this.is_vip
            if (id !== null) url += `&id=${id}`
            if (!id && this.category_id) {
                const category_name = this.category_list.find(item => item.category_id === this.category_id)?.name || ''
                url += `&category_id=${this.category_id}&category_name=${category_name}`
            }
            if (this.active_id) url += `&active_id=${this.active_id}`
            if (this.just_look_active) url += '&just_look_active=1'
            if (this.article_bind_exam) url += '&article_bind_exam=1'
            if (this.reward_step_by_reading) url += '&reward_step_by_reading=1'
            uni.navigateTo({
                url,
                events: {
                    upload: () => {
                        this.load_page = 1;
                        this.initGetList();
                    }
                }
            })
        },

        deleteItem(id) {
            uni.showModal({
                title: '提示',
                content: '确定删除该文章',
                success: res => {
                    if (res.confirm) this.deleteAjax(id)
                }
            })
        },

        deleteAjax(ids) {
            this.loading = true
            uni.showLoading({
                title: '删除中...',
                mask: true
            })

            xwy_api.ajax({
                url: 'front.news/news_del',
                data: {
                    access_token: app.globalData['access_token'],
                    ids
                },
                success: res => {
                    uni.hideLoading()
                    this.loading = false

                    if (!res.status) {
                        uni.showModal({
                            title: '删除失败',
                            content: res.info || '删除失败',
                            showCancel: false
                        })
                        return false
                    }

                    uni.showToast({
                        title: res.info || '删除成功',
                        icon: !res.info || res.info.length < 8 ? 'success' : 'none'
                    })

                    const timeout = setTimeout(() => {
                        this.load_page = 1;
                        this.initGetList();
                        clearTimeout(timeout);
                    }, 1000)
                }
            })


        },

        selItem(index) {
            const eventChannel = this.getOpenerEventChannel()
            eventChannel.emit('selNews', {
                id: this.list[index].news_id,
                title: this.list[index].title
            })
            uni.navigateBack()
        }
    }
}
</script>

<style scoped lang="scss">
.page {
    min-height: 100vh;
    padding-top: 100px;
    box-sizing: border-box;
}

.top-main {
    width: 100%;
    position: fixed;
    z-index: 9999;
    top: 0;
}

.pb0 {
    padding-bottom: 0 !important;
}

.search {
    background-color: #fff;
    display: flex;
    flex-direction: row;
    padding: 10px;

    .search-input {
        width: calc(100% - 80px);
        color: #333;
        font-size: 14px;
        background-color: #f8f8f8;
        height: 40px;
        line-height: 40px;
        border-radius: 20px 0 0 20px;
        padding: 0 20px;
    }

    .search-button {
        width: 80px;
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        color: #fff;
        border-radius: 0 20px 20px 0;
        text-align: center;
        padding-right: 10px;
    }
}

.type-bar {
    border: 0;
    height: 40px;
    border-bottom: 5px solid #f8f8f8;
}


.type-item {
    height: 40px;
    line-height: 40px;
    padding: 0 10px;
    box-sizing: border-box;
    display: inline-block;
    white-space: nowrap;
}

.activate-type {
    color: #2d8cf0;
    border-bottom: 2px solid #2d8cf0;
}

.not-btn {
    width: 200px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
}

.item {
    padding: 10px;
    border-bottom: 1px solid #eee;
    margin: 10px;
    border-radius: 10px;
}

.logo {
    width: 120px;
    min-width: 120px;
    height: 60px;
    border-radius: 5px;
}

.have-read, .not-read {
    line-height: 16px;
    padding: 2px 5px;
    border-radius: 10px;
    font-size: 12px;
}

.tools-bar {
    border-top: 1px solid #eee;
    padding-top: 10px;
    margin-top: 10px;
}

.edit, .delete {
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 50%;
}

.edit {
    border: 1px solid #eee;
    margin-right: 10px;
}

.delete {
    border: 1px solid #eee;
}
</style>
