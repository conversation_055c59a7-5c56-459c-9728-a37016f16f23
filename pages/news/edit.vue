<template>
    <view class="page">
        <view class="form">
            <view class="form-item">
                <view class="top color-content">
                    <text>文章标题</text>
                    <text class="color-error font16"> *</text>
                </view>
                <view class="bottom font16">
                    <input class="input" type="text" v-model="title" placeholder="请输入文章标题"/>
                </view>
            </view>
            <view v-if="is_vip === '1'" class="form-item">
                <view class="top color-content">
                    <view>文章音频地址</view>
                    <view class="color-sub font12">可作为文章背景音乐或者文章内容播报</view>
                </view>
                <view class="bottom font16">
                    <input class="input" type="text" v-model="mp3" placeholder="请输入文章音频地址"/>
                </view>
            </view>
            
            <template v-if="is_vip === '1'">
                <view class="form-item">
                    <view class="top color-content">文章分类</view>
                    <view class="bottom font16">
                        <view class="flex-kai" @click="toSelCategory">
                            <view class="view">
                                <view v-if="category_id">{{ category_name || category_id }}</view>
                                <view v-else class="color-sub">选择分类</view>
                            </view>
                            <view class="flex-all-center">
                                <view v-if="category_id" class="color-sub font12" style="width: 30px;"
                                      @click.stop="deBindCategory">解绑
                                </view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <template v-if="article_bind_exam || reward_step_by_reading">
                    <view v-if="article_bind_exam" class="form-item">
                        <view class="top color-content">绑定答题</view>
                        <view class="bottom font16">
                            <view class="flex-kai" @click="bindExam">
                                <view class="view">
                                    <view v-if="bind_exam_id">{{ bind_exam_name || bind_exam_id }}</view>
                                    <view v-else class="color-sub">选择答题</view>
                                </view>
                                <view class="flex-all-center">
                                    <view v-if="bind_exam_id" class="color-sub font12" style="width: 30px;"
                                          @click.stop="deBindExam">解绑
                                    </view>
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>
                    
                    <view class="form-item">
                        <view class="top color-content">学习时间</view>
                        <view class="bottom font16">
                            <uni-datetime-picker
                                type="date"
                                v-model="study_date"
                                end="2038-12-31"
                                placeholder="请选择学习时间"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </template>
            </template>
            
            <!-- <view class="form-item">
                <view class="top color-content">视频地址</view>
                <view class="bottom font16">
                    <textarea
                  class="textarea"
                  maxlength="-1"
                  auto-height
                  v-model="video_src"
                  placeholder="请输入视频地址(可不填)"
                />
                </view>
            </view> -->


        </view>


        <!-- #ifndef H5 -->
        <my-editor
            ref="myEditor"
            v-if="editor_components_show"
            :value="editor_value"
            :is_vip="is_vip"
            @confirm="editorSave"
            :page_dom_height="240"
            placeholder="请输入文章内容"
        />
        <!-- #endif -->

        <!-- #ifdef H5 -->
        <!-- <template v-if="editor_components_show">
          <lRichTextEditor
            :value="editor_value"
            :height="800"
            @change="GetChange"
          ></lRichTextEditor>

          <view class="save flex-all-center bg-white">
            <view
              class="save-btn text-center bg-primary color-white"
              @click="editorSave"
            >保存</view>
          </view>
        </template> -->
        <view id="wangeditor"></view>
        <!-- #endif -->
        

        <view class="form-item">
            <view class="top color-content">缩略图</view>
            <view class="bottom font16 pt10 pb10">
                <view v-if="logo" class="logo-view">
                    <view class="logo-clear flex-all-center" @click="logo = ''">
                        <uni-icons type="closeempty" size="18" color="#e20f04"/>
                    </view>

                    <image :src="logo" class="logo-img" mode="aspectFill"/>
                </view>
                <view v-else class="add-image flex-all-center" @click="changeLogo">
                    <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                </view>
            </view>
        </view>

        <view class="form-item">
            <view class="top color-content">文章排序</view>
            <view class="bottom font16">
                <input class="input" type="number" v-model="sort_num" maxlength="8" 
                       placeholder="默认0，数字越小排在越前"/>
            </view>
        </view>

        
        <view class="save flex-all-center bg-white">
            <view class="save-btn text-center bg-primary color-white"
                  hover-class="navigator-hover" @click="editorSave()">保存
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import login from '../../utils/api/login.js'
import xwy_api from '../../utils/api/xwy_api.js'
import base64 from '../../utils/base64.js'
import utils from '../../utils/utils.js'

// H5和其他平台用的富文本插件不一样
// #ifndef H5
import myEditor from './components/my-editor.vue'
// #endif
// #ifdef H5
import E from './components/wangEditor/wangEditor.js'
// #endif

export default {
    components: {
        // #ifndef H5
        myEditor,
        // #endif
        // #ifdef H5
        // lRichTextEditor,
        // #endif

    },
    data() {
        return {
            editor_components_show: false,
            id: '',
            editor_value: '',
            title: '',
            logo: '',
            video_src: '',
            category_id: '',
            category_name: '',
            is_vip: '0',
            mp3: '',
            sort_num: '',
            article_bind_exam: false,
            reward_step_by_reading: false,
            bind_exam_id: '',
            bind_exam_name: '',
            study_date: ''
        }
    },

    onLoad(e) {
        if (e.is_vip) this.is_vip = e.is_vip
        if (e.id) this.id = e.id
        if (e.active_id) this.active_id = e.active_id
        if (e.just_look_active) this.just_look_active = true
        if (e.article_bind_exam) this.article_bind_exam = true
        if (e.reward_step_by_reading) this.reward_step_by_reading = true
        
        if (e.category_id) {
            this.category_id = e.category_id
            if (e.category_name) this.category_name = e.category_name
        }
        
        uni.setNavigationBarTitle({
            title: e.id ? '修改文章' : '添加文章',
            fail: err => {
                console.log(err)
            }
        })

        uni.showLoading({
            title: '加载中...',
            mask: true
        })

        login.uniLogin(err => {

            if (err && err.errMsg) return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})

            let is_h5 = false
            // #ifdef H5
            is_h5 = true
            // #endif
            if (is_h5) {
                setTimeout(() => {
                    this.wangEditorInit()
                }, 300)
                return false
            }

            if (this.id) return this.getDetail()


            uni.hideLoading()
            this.editor_components_show = true
        })
    },
    methods: {
        wangEditorInit() {
            this.editor = new E('#wangeditor')
            console.log(this.editor)
            this.editor.create()

            if (this.id) {
                this.getDetail()
            } else {
                uni.hideLoading()
            }

            // 粘贴图片到文本框内的上传图片方法
            this.editor.config.customUploadImg = (resultFiles, insertImgFn) => {
                this.customUploadImg(resultFiles, insertImgFn)
            }

            this.onInsertImg()
        },

        onInsertImg() {
            // 监听点击插入图片icon
            document.getElementsByClassName("w-e-icon-image")[0].parentElement.addEventListener("click", () => {
                // 监听点击上传图片icon
                const doms = document.getElementsByClassName("w-e-up-btn");
                doms && doms[0] && doms[0].addEventListener("click", () => {
                    uni.navigateTo({
                        url: '/pages/other/image_upload_or_select',
                        events: {
                            newImg: src => {
                                this.editor.txt.append(`<img src="${src}"/>`)
                            }
                        }
                    })
                })
            })
        },

        customUploadImg(resultFiles, insertImgFn) {
            const file = resultFiles[0];
            const reader = new FileReader()
            reader.readAsDataURL(file)
            reader.onload = () => {
                const size = file.size / 1024
                let type = file.type.split('/')[1]
                if (type === 'jpeg') type = 'jpg'
                const data = {
                    access_token: app.globalData.access_token,
                    pic: reader.result.replace(/^data:image\/\w+;base64,/, ""),
                    size,
                    ext: type
                }

                this.uploadImage(data, insertImgFn)

            }
        },

        async uploadImage(data, insertImgFn) {
            const res = await xwy_api.request({
                url: 'front.upload/upload_file',
                data
            })

            if (!res || !res.data || !res.data.pic_url) return false

            insertImgFn(res.data.pic_url)
        },


        getDetail() {
            xwy_api.ajax({
                url: 'front.news/news_details',
                data: {
                    access_token: app.globalData.access_token,
                    news_id: this.id
                },
                success: res => {
                    uni.hideLoading()
                    if (!res.data || !res.data.news_details) {
                        uni.showModal({
                            title: '提示',
                            content: res.info || '文章内容获取失败',
                            showCancel: false,
                            success: () => uni.navigateBack()
                        })
                        return false
                    }

                    const detail = res.data.news_details
                    if (detail.thumb_pic) this.logo = detail.thumb_pic
                    if (detail.title) this.title = detail.title
                    if (detail.mp3) this.mp3 = detail.mp3
                    if (detail.category_id) this.category_id = detail.category_id
                    if (detail.category_details && detail.category_details.name) {
                        this.category_name = detail.category_details.name
                    }
                    if (detail.content) this.editor_value = detail.content
                    if (detail.content) {
                        let content = detail.content
                        if (utils.isBase64(content)) content = base64.decode(detail.content)
                        this.editor_value = content

                        // #ifdef H5
                        if (detail.content) this.editor.txt.html(detail.content)
                        // #endif
                    }
                    if (detail.video_url) this.video_src = detail.video_url
                    if (detail.sort_num) this.sort_num = detail.sort_num
                    
                    if (detail.conf_set) {
                        const {bind_exam_id, bind_exam_name, study_date} = detail.conf_set
                        if (bind_exam_id && bind_exam_name) {
                            this.bind_exam_id = bind_exam_id
                            this.bind_exam_name = bind_exam_name
                        }
                        if (study_date) this.study_date = study_date
                    }

                    this.editor_components_show = true
                }
            })
        },
        changeLogo() {
            uni.navigateTo({
                url: '/pages/other/image_upload_or_select',
                events: {
                    newImg: src => {
                        this.logo = src
                    }
                }
            })
        },

        toSelCategory() {
            let url = '/pages/category/list?types=8&is_sel=1'
            if (this.active_id) url += `&active_id=${this.active_id}`
            if (this.just_look_active) url += '&just_look_active=1'
            uni.navigateTo({
                url,
                events: {
                    selCategory: data => {
                        this.category_id = data.id
                        this.category_name = data.name
                    }
                }
            })
        },

        deBindCategory() {
            this.category_id = ''
            this.category_name = ''
        },

        bindExam() {
            this.$uni.navigateTo('/pages/likou_dati/pages/exam/exam_list/exam_list?in_select=true', {
                events: {
                    updateExam: data => {
                        this.bind_exam_id = data.id
                        this.bind_exam_name = data.title
                    }
                }
            })  
        },
        
        deBindExam() {
            this.bind_exam_id = ''
            this.bind_exam_name = ''
        },

        /*GetChange(e) {
            this.editor_value = e
        },*/

        async editorSave() {
            if (!this.title) return this.$uni.showToast('请输入文章标题', 'error')
            
            const sort_num = Math.floor(this.sort_num)
            if (isNaN(sort_num) || sort_num < 0) return this.$uni.showToast('文章排序输入不正确')
            
            let html = ''
            
            // #ifdef H5
            html = this.editor.txt.html() || ''
            // #endif
            
            // #ifndef H5
            html = await this.$refs.myEditor.getHtml() || ''
            /* if (!html || html === '<p><br></p>' || html === '<p></p>') {
              uni.showToast({
                title: '请输入文章内容',
                icon: 'error'
              })
              return false
            } */
            // #endif
            
            
            this.add_or_update(html)
        },


        add_or_update(html) {
            const data = {
                access_token: app.globalData.access_token,
                title: this.title,
                content: html,
                // content: base64.encode(html),
                thumb_pic: this.logo || '',
                video_url: this.video_src || '',
                mp3: this.mp3 || '',
                sort_num: Math.floor(this.sort_num),
                from: '',
                category_id: this.category_id || 0
            }
            if (this.id) data.news_id = this.id
            // if (this.active_id && this.just_look_active) data.active_id = this.active_id
            if (this.active_id) data.active_id = this.active_id
            // #ifdef H5
            data.from = 'h5'
            // #endif
            
            const conf_set = {}
            if (this.bind_exam_id && this.bind_exam_name) {
                conf_set.bind_exam_id = this.bind_exam_id
                conf_set.bind_exam_name = this.bind_exam_name
            }
            if (this.study_date) conf_set.study_date = this.study_date
            data.conf_set = this._utils.base64['encode'](JSON.stringify(conf_set))

            uni.showLoading({
                title: '保存中...',
                mask: true
            })

            xwy_api.ajax({
                url: 'front.news/add_news',
                data,
                success: res => {
                    uni.hideLoading()
                    if (!res.status) {
                        uni.showModal({
                            title: '提示',
                            content: res.info || '保存失败',
                            showCancel: false
                        })
                        return false
                    }

                    uni.showToast({
                        title: res.info || '保存成功',
                        icon: !res.info || res.info.length < 8 ? 'success' : 'none'
                    })

                    const eventChannel = this.getOpenerEventChannel()
                    eventChannel.emit('upload')

                    const timeout = setTimeout(() => {
                        uni.navigateBack()
                        clearTimeout(timeout)
                    }, 1000)
                }
            })

        }

    }
}
</script>

<style lang="scss" scoped>
.page {
    padding-bottom: 70px;
}

.form-item {
    padding: 5px 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}


.form-item .textarea {
    width: 100%;
    height: 65px;
    line-height: 16px;
    padding: 5px 0;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}

.logo-view {
    position: relative;
}

.logo-clear {
    position: absolute;
    z-index: 999;
    right: 5px;
    top: 5px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, .7);
}

.logo-view, .logo-img, .add-image {
    height: 100px;
    border-radius: 5px;
}

.logo-view, .logo-img {
    width: 200px;
}

.add-image {
    background-color: #f8f8f8;
    width: 100px;
}

.save {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100vw;
    box-sizing: border-box;
    z-index: 99999;
    padding: 10px 10px 15px;
}

.save-btn {
    width: 250px;
    line-height: 40px;
    border-radius: 20px;
}

::v-deep .uni-calendar--fixed {
    z-index: 999999;
}

/*H5富文本编辑工具栏强制换行*/
::v-deep .w-e-toolbar {
    flex-wrap: wrap !important;
}
</style>
