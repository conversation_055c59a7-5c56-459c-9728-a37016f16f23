<template>
    <view class="page bg-background">
        <view class="list">
            <view class="item bg-white flex-row" v-for="(item, index) in list" :key="index"
                  @click="previewNews(item, index)">
                <image v-if="item.thumb_pic" class="logo" :src="item.thumb_pic" mode="aspectFill"/>
                <view class="flex-column flex-kai w-100">
                    <view class="color-title ellipsis--l2">{{ item.title }}</view>
                    <view>
                        <view class="clearfix clear pt5 font12">
                            <view v-if="item.have_read" class="have-read fr bg-red color-white">已学习</view>
                            <view v-else class="not-read fr bg-disabled color-white">未学习</view>
                        </view>
                        <view class="clearfix clear pt5">
                            <view class="fr color-sub font12">{{ item.create_time }}</view>
                        </view>
                    </view>
                </view>
            </view>
        </view>


        <view v-if="!loading && !list.length" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无文章</view>
        </view>
    </view>
</template>

<script>
import login from '@/utils/api/login.js'

export default {
    data() {
        return {
            loading: true,
            list: []
        }
    },
    onLoad(e) {
        this.active_id = e.active_id
        this.category_id = e.category_id
        this.study_type = Number(e.study_type)
        if (e.article_bind_exam) this.article_bind_exam = true
        if (e.reward_step_by_reading) this.reward_step_by_reading = true

        this.$uni.showLoading('加载中...')

        login.uniLogin(err => {
            if (err && err.errMsg) return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            this.getList()
        })
    },
    
    methods: {
        async getList() {
            this.loading = true
            const res = await this.xwy_api.request({
                url: 'front.news/news_article_list',
                data: {
                    active_id: this.active_id,
                    category_id: this.category_id,
                    get_list_types: 1, // 【1】获取健步走活动是否有阅读这篇文章，返回阅读状态以及今天阅读的文章id
                    page: 1,
                    perpage: 1000
                }
            })
            this.loading = false
            uni.hideLoading()

            const list = res?.data?.article_list?.data || res?.data?.['person_list']?.data || []
            const today_reading_details = res?.data?.article_list?.['today_reading_details'] || res?.data?.['person_list']?.['today_reading_details'] || null
            
            this.list = list.map(item => ({
                id: item.id,
                news_id: item.news_id,
                title: item.title,
                thumb_pic: item.thumb_pic,
                create_time: item.create_time,
                have_read: !!item.have_read,
                study_date: item.conf_set?.study_date || null
            }))
            if (today_reading_details) this.today_reading = true
        },

        previewNews(item, index) {
            if (!this.readCheck(item, index)) return
            
            let url = `../preview?id=${item.news_id}&active_id=${this.active_id}`
            if (this.article_bind_exam) url += `&article_bind_exam=1`
            if (this.reward_step_by_reading) url += `&reward_step_by_reading=1`
            
            this.$uni.navigateTo(url, {
                events: {
                    studyComplete: () => {
                        item.have_read = true
                        this.today_reading = true
                    }
                }
            })
        },

        readCheck(item, index) {
            if (item.have_read) return true
            
            // 按文章顺序学习
            if (this.study_type === 0) {
                if (this.today_reading) {
                    this.$uni.showToast('今日已学习，请明天再来。', 'none', 3000)
                    return false
                }

                if (index === 0) return true

                const previousNews = this.list[index - 1]
                if (!previousNews.have_read) {
                    this.$uni.showToast('请按顺序学习')
                    return false
                }

                return true
            }
            
            
            // 按文章的学习时间学习
            if (!item.study_date) {
                this.$uni.showModal('文章未设置学习时间,请联系活动管理员。')
                return false
            }
            
            const today = this._utils.getDay(0, true)
            if (today !== item.study_date) {
                this.$uni.showToast(`学习时间是${item.study_date}`, 'none', 3000)
                return false
            }
            
            return true
        }
    }
}
</script>

<style scoped lang="scss">
.page {
    min-height: 100vh;
    padding-top: 1px;
    padding-bottom: 15px;
    box-sizing: border-box;
}

.item {
    padding: 10px;
    border-bottom: 1px solid #eee;
    margin: 10px;
    border-radius: 10px;
}

.logo {
    width: 120px;
    min-width: 120px;
    height: 80px;
    border-radius: 5px;
    margin-right: 10px;
}

.have-read, .not-read {
    line-height: 16px;
    padding: 2px 5px;
    border-radius: 10px;
    font-size: 12px;
}
</style>
