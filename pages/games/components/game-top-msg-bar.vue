<template>
    <view class="top-msg-container clearfix clear">
        <view v-if="showLeft" class="top-msg top-msg-left fl">
            <view class="top-msg-icon">
                <view class="top-msg-inner-ring">
                    <text :class="['iconfont', leftIcon]"></text>
                </view>
            </view>
            <view class="top-msg-num">
                <slot name="leftMsg"></slot>
            </view>
        </view>

        <view v-if="showRight" class="top-msg top-msg-right fr">
            <view class="top-msg-icon">
                <view class="top-msg-inner-ring">
                    <text :class="['iconfont', rightIcon]"></text>
                </view>
            </view>
            <view class="top-msg-num">
                <slot name="rightMsg"></slot>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "game-top-msg-bar",
    props: {
        showLeft: {
            type: <PERSON>olean,
            default: true
        },
        leftIcon: {
            type: String,
            default: 'icon-alarm-clock'
        },
        showRight: {
            type: Boolean,
            default: true
        },
        rightIcon: {
            type: String,
            default: 'icon-grid'
        },
    },
}
</script>

<style lang="scss" scoped>
.top-msg-container {
    width: 100%;
    box-sizing: border-box;
    padding: 15px 3vw 10px calc(3vw + 15px);

    .top-msg {
        position: relative;
        display: flex;
        flex-direction: row;

        .top-msg-icon {
            position: absolute;
            left: -15px;
            top: -3px;
            border-radius: 50%;
            padding: 3px;

            .top-msg-inner-ring {
                width: 30px;
                height: 30px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 50%;

                .iconfont {
                    color: #fff;
                    font-size: 22px;
                }
            }
        }

        .top-msg-num {
            background-color: rgba(0, 0, 0, .3);
            color: #fff;
            height: 30px;
            border-radius: 0 15px 15px 0;
            padding: 0 15px 0 26px;
            min-width: 60px;
            font-size: 18px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .top-msg-left {
        .top-msg-icon {
            background-color: #5cadff;
            box-shadow: 0 0 5px #2d8cf0;

            .top-msg-inner-ring {
                background-color: #2d8cf0;
            }
        }

        .top-msg-num {
            box-shadow: 0 0 10px rgba(45, 140, 240, .2) inset;
        }
    }

    .top-msg-right {
        .top-msg-icon {
            background-color: #fce230;
            box-shadow: 0 0 5px #ffb400;

            .top-msg-inner-ring {
                background-color: #ffb400;
            }
        }

        .top-msg-num {
            box-shadow: 0 0 10px rgba(255, 180, 0, .2) inset;
        }
    }
}
</style>