<template>
    <view>
        <uni-popup ref="popup" :is-mask-click="false">
            <view class="countdown-container">
                <view v-for="(_, index) in allTime" :key="index">
                    <view v-if="index === time && time >= 0" class="time-num">
                        {{ time === 0 ? 'GO' : time }}
                    </view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const SoundEffects = {
    CountDown: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/music/sport/di.mp3',
    Go: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/music/sport/go.mp3'
}

export default {
    name: "ready-countdown",
    emits: ['countdownOver'],
    props: {},
    data() {
        return {
            allTime: 0,
            time: 0
        }
    },

    methods: {
        open(time = 3) {
            this.allTime = time + 1
            this.$nextTick(() => {
                this.$refs.popup.open()
                this.startCountdown()
            })
        },
        
        startCountdown() {
            this.time = this.allTime

            this.time--
            // 立即播放当前数字的音效和动画
            this.playSound()
            
            // 设置定时器
            this.countdownTimer = setInterval(() => {
                this.time--
                
                if (this.time >= 0) {
                    this.playSound()
                } else {
                    // 倒计时结束
                    this.finishCountdown()
                }
            }, 1000)
        },
        
        playSound() {
            const audioContext = uni.createInnerAudioContext()
            
            if (this.time === 0) {
                audioContext.src = SoundEffects.Go
            } else {
                audioContext.src = SoundEffects.CountDown
            }
            
            audioContext.play()
            
            // 播放完成后销毁音频实例
            audioContext.onEnded(() => {
                audioContext.destroy()
            })
            
            // 错误处理
            audioContext.onError((err) => {
                console.error('Audio play error:', err)
                audioContext.destroy()
            })
        },

        
        finishCountdown() {
            // 清除定时器
            if (this.countdownTimer) {
                clearInterval(this.countdownTimer)
                this.countdownTimer = null
            }
            
            // 延迟关闭弹窗，让GO动画完成
            setTimeout(() => {
                this.$refs.popup.close()
                this.$emit('countdownOver')
            }, 300)
        }
    },
    
    beforeDestroy() {
        // 组件销毁时清理定时器
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer)
        }
    }
}
</script>

<style lang="scss">
.countdown-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.8);
}

.time-num {
    color: white;
    font-weight: bold;
    text-align: center;
    animation: large-to-small .3s linear forwards;
}

@keyframes large-to-small {
    from {
        font-size: 800rpx;
    }
    to {
        font-size: 300rpx;
    }
}
</style>