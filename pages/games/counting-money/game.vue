<template>
    <view class="page" :style="{ backgroundImage: `url(${bgImg})` }">
        <view class="top-bar">
            <game-top-msg-bar right-icon="icon-renminbi">
                <template v-slot:leftMsg>{{ time }}</template>
                <template v-slot:rightMsg>{{ count }}</template>
            </game-top-msg-bar>
        </view>

        <game-tips-popup ref="gameTips" :tips-list="tipsList" :show-ad="showAd" @startGame="startGame"/>
        <game-result-popup ref="resultPopup" :show-ad="showAd" :unit="unit"/>

        <counting-money v-if="moneyImageList.length" ref="countingMoney" :money-image-list="moneyImageList"
            @countUpdate="countUpdate" />
    </view>
</template>

<script>
import config from './config'
import gameTopMsgBar from '../components/game-top-msg-bar.vue'
import gameTipsPopup from '../components/game-tips-popup.vue'
import countingMoney from './counting-money.vue'

export default {
    components: { gameTopMsgBar, gameTipsPopup, countingMoney },
    data() {
        return {
            moneyImageList: [],
            bgImg: config.bgImg,
            time: 30,
            count: 0,
            showAd: false,
            per_integral: 0,
            unit: '积分'
        }
    },

    computed: {
        tipsList() {
            return [
                `请在${this.time}秒内用手指按住钱往上滑，每滑走一张钱获得1分。`,
                `倒计时结束后，每获得1分奖励${this.per_integral}${this.unit}`
            ]
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        if (params.point_id) this.point_id = Number(params.point_id)
        if (params.second) this.time = Number(params.second)
        if (params.title) this.$uni.setNavigationBarTitle(params.title)
        if (params.per_integral) this.per_integral = Number(params.per_integral)
        if (params.unit) this.unit = params.unit
        if (params.show_ad) this.showAd = true

        this.$login.uniLogin(() => this.init())
    },

    onUnload() {
        clearInterval(this.downTimer)
    },

    methods: {
        async init() {
            await this.getGameData()

            this.$refs.gameTips.open()
        },

        async getGameData() {
            const data = await this.getOpenerEventChannelData()

            if (data.title) this.$uni.setNavigationBarTitle(data.title)

            if (data.bg_img) this.bgImg = data.bg_img
            this.moneyImageList = data.money_imgs || config.money_images

            if (data.navigation_bar) uni.setNavigationBarColor({
                ...data.navigation_bar,
                fail: err => console.log(err)
            })
        },

        getOpenerEventChannelData() {
            return new Promise(resolve => this.getOpenerEventChannel().once('data', data => resolve(data)))
        },


        startGame() {
            this.$refs.gameTips.close()
            this.$refs.countingMoney.startGame()

            this.downTime()
        },

        downTime() {
            this.downTimer = setInterval(() => {
                if (this.time <= 0) {
                    this.gameOver()
                    clearInterval(this.downTimer)
                    return
                }
                this.time--
            }, 1000)
        },


        countUpdate(count) {
            this.count = count
        },


        gameOver() {
            this.$refs.countingMoney.gameOver()
            this.submitResult()
        },

        async submitResult() {
            const count = this.count
            const sign = {
                types: 36,
                point_id: this.point_id,
                result: count ? 'success' : 'fail',
                count
            }

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify(sign))
                }
            })

            let resultCode = sign.result === 'success' ? 1 : 0
            let info = resultCode === 1 ? '任务完成' : '任务失败'
            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info = res.info
            }

            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res?.data?.num || null,
                info
            })


            this.getOpenerEventChannel().emit('success')
        },
    }
}
</script>

<style lang="scss">
.page {
    height: 100vh;
    max-height: 100vh;
    overflow: hidden;
    box-sizing: border-box;
    background-repeat: no-repeat;
    background-size: cover;
}

.top-bar {
    position: relative;
    z-index: 20;
}
</style>