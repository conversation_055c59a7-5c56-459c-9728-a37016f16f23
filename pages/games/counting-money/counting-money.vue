<template>
    <view class="counting-money" @touchstart="onTouchStart" @touchmove="onTouchMove" @touchend="onTouchEnd">
        <view v-if="inGame" class="money-drop">
            <view class="money-drop-item" :style="item.containerStyle"
                  v-for="item in moneyDropList" :key="item.key">
                <image class="money-drop-image" :src="item.url" :style="item.imageStyle"/>
            </view>
        </view>

        <view class="money-list">
            <image class="money-item" v-for="(item, index) in moneyList" :key="index" :src="item.url"
                   :style="{marginLeft: item.left + 'px', marginBottom: item.bottom + 'px', zIndex: 50 - index, animation: item.status === 'flyOut' ? `fly-out ${flyTime}ms` : ''}"/>
        </view>
    </view>
</template>

<script>
import config from './config'

export default {
    name: "counting-money",
    emits: ['countUpdate'],
    props: {
        moneyImageList: {
            type: Array,
            default: () => config.money_images
        }
    },
    data() {
        return {
            flyTime: 300,
            moneyList: [],
            moneyDropList: [],
            inGame: false,
            count: 0
        }
    },

    watch: {
        count(val) {
            this.$emit('countUpdate', val)
        }
    },

    mounted() {
        this.initMoneyDropList()
        this.initMoneyList()
    },

    methods: {
        startGame() {
            this.inGame = true
        },

        gameOver() {
            this.inGame = false
        },

        getMoneyCount(length) {
            const images = this.moneyImageList
            return Array.from({length}, (_, i) => images[i % images.length])
        },

        initMoneyDropList() {
            const length = 10
            this.moneyDropList = this.getMoneyCount(length).map(({url}, index) => ({
                url,
                containerStyle: this.setMoneyDropContainerStyle(index, length),
                imageStyle: this.setMoneyDropImageStyle()
            }))
        },

        /**
         * 设置掉落物容器的样式
         *
         * @param {number} index - 当前物品的索引
         * @param {number} length - 总共物品的数量
         * @returns {string} - 返回包含CSS样式的字符串，用于设置掉落物容器的样式
         *
         * 该方法根据物品的索引决定物品的移动方向（左或右），并随机生成位置、动画持续时间和延迟时间。
         */
        setMoneyDropContainerStyle(index, length) {
            // 根据索引判断方向，前半部分为左，后半部分为右  保证左右两边的数量是相对一致的
            const direction = index < length / 2 ? 'left' : 'right'

            // 根据索引奇偶决定位置的随机值，偶数位置在-10到30之间，奇数位置在70到80之间   保证左右不要重叠太多
            const distance = index % 2 === 0 ? this._utils.randomNum(-20, 60) : this._utils.randomNum(140, 160)

            // 随机生成动画持续时间（1000到3000毫秒之间）和延迟时间（0到1000毫秒之间）
            const dropDuration = this._utils.randomNum(1000, 3000)
            const delayDuration = this._utils.randomNum(0, 1000)

            // 返回CSS样式字符串，设置位置和动画
            return `${direction}: ${distance}rpx; animation: money-drop ${dropDuration}ms linear ${delayDuration}ms infinite;`
        },

        setMoneyDropImageStyle() {
            const name = Math.random() > 0.5 ? 'rotate' : 'rotate-reverse'
            const duration = this._utils.randomNum(300, 1000)
            return `animation: ${name} ${duration}ms linear infinite;`
        },


        initMoneyList() {
            this.moneyList = this.getMoneyCount(20).map(({url}, index) => ({
                url,
                bottom: this.randomGap(),
                left: this.randomGap(),
                status: 'standby',
                key: index   // 为每个物品生成唯一的key
            })).sort(() => Math.random() - 0.5)
        },

        onTouchStart(event) {
            this.startY = event.touches[0].clientY // 记录手指起始位置
        },

        onTouchMove(event) {
            if (this.inFlyOut) return
            if (!this.inGame) return
            if (this.time <= 0) return

            const moveY = event.touches[0].clientY - this.startY // 计算滑动距离
            if (moveY < -50) { // 如果向上滑动超过50px
                this.inFlyOut = true
                this.count += 1
                this.flyOut() // 调用飞出动画
            }
        },

        onTouchEnd() {
            this.inFlyOut = false
        },

        flyOut() {
            this.moneyList[0].status = 'flyOut'

            setTimeout(() => {
                this.moneyList.splice(0, 1)
                const money_config_list = this.moneyImageList
                const item = money_config_list[this._utils.randomNum(0, money_config_list.length - 1)]
                this.moneyList.push({
                    url: item.url,
                    bottom: this.randomGap(),
                    left: this.randomGap(),
                    status: 'standby',
                    // key: Math.max(...this.moneyList.map(item => item.key)) + 1  // 为每个物品生成唯一的key
                    key: new Date().getTime()  // 为每个物品生成唯一的key
                })
            }, this.flyTime)
        },

        randomGap() {
            return this._utils.randomNum(-10, 10)
        },
    }
}
</script>

<style lang="scss">
.counting-money {
    width: 100vw;
    height: 100vh;
    max-height: 100vh;
    box-sizing: border-box;
    overflow: hidden;
}

.money-drop {
    .money-drop-item {
        position: fixed;
        top: -80px;

        @keyframes money-drop {
            from {
                top: -160rpx;
            }
            to {
                top: 100vh;
            }
        }

        .money-drop-image {
            width: 80rpx;
            height: 160rpx;
            display: block;
        }

        @keyframes rotate {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        @keyframes rotate-reverse {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(-360deg);
            }
        }
    }
}

.money-list {
    .money-item {
        position: fixed;
        display: block;
        width: 400rpx;
        height: 700rpx;
        bottom: 200rpx;
        left: calc(50% - 200rpx);
    }

    @keyframes fly-out {
        from {
            bottom: 200rpx;
        }
        to {
            bottom: 100vh;
        }
    }
}
</style>