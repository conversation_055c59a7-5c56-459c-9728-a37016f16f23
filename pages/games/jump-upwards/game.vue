<template>
    <view class="content" :style="{ 'background-image': `url(${images.bgImg})` }">
        <!-- 游戏主区域 -->
        <view v-if="gameStatus === 1" class="game-area">
            <view class="game-tips-msg-bar">
                <game-top-msg-bar>
                    <template v-slot:leftMsg>{{ countdown }}</template>
                    <template v-slot:rightMsg>{{ score }}</template>
                </game-top-msg-bar>
            </view>

            <!-- 角色 Doodler -->
            <view class="doodler" :style="doodlerStyle"
                  :class="{ 'face-left': doodler.facingLeft, 'face-right': !doodler.facingLeft }">
                <image class="doodler-img" :src="images.doodlerImg" mode="aspectFill"></image>
            </view>

            <!-- 平台 -->
            <view class="platform-item" v-for="(platform, index) in platforms" :key="index"
                  :style="{
                      left: platform.x + 'px',
                      top: platform.y + 'px',
                      width: platform.width + 'px',
                      height: platform.height + 'px'
                  }"
            >
                <image class="platform-img" :src="images.platformImg" mode="aspectFill"></image>
            </view>
        </view>

        <game-tips-popup ref="gameTipsPopup" :tips-list="tipsList" :show-ad="showAD" @startGame="gameReady"/>

        <ready-countdown ref="readyCountdown" @countdownOver="readyCountdownOver"/>

        <game-result-popup ref="resultPopup" :show-ad="showAD" :unit="unit"/>
    </view>
</template>

<script>
import gameTopMsgBar from '../components/game-top-msg-bar.vue'
import gameTipsPopup from '../components/game-tips-popup.vue'
import readyCountdown from '../components/ready-countdown.vue'

const BASE_URL = 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/game/jump-and-jump/moon/'

export default {
    components: {readyCountdown, gameTipsPopup, gameTopMsgBar},
    data() {
        return {
            images: {
                bgImg: `${BASE_URL}bg.png`,
                platformImg: `${BASE_URL}cloud.png`,
                doodlerImg: `${BASE_URL}ce.png`,
            },

            // 游戏状态
            gameStatus: 0, // 0: 未开始, 1: 游戏中, 2: 已结束

            // 角色参数
            doodler: {
                x: 0,
                y: 0,
                width: 0, // 将在init中设置为屏幕宽度的1/4
                height: 0, // 将在init中设置为屏幕宽度的1/4
                facingLeft: false,
            },

            // 物理参数
            velocityX: 0,
            velocityY: 0,
            initialVelocityY: -10, // 起跳速度（调整以匹配平台间距）
            gravity: 0.4, // 重力（减小）

            // 重力感应
            tiltX: 0,
            moveSpeed: 3, // 移动速度（减慢）
            tiltSensitivity: 8, // 倾斜敏感度（降低）
            maxTilt: 3, // 最大倾斜角度（减小）
            moveSmooth: 0.1, // 移动平滑系数（提高响应性）

            // 平台参数
            platforms: [],

            score: 0,

            // 速度等级
            speedLevel: 1, // 当前速度等级
            currentJumpSpeed: -8, // 当前跳跃速度

            showAD: false,
            seconds: 30,
            countdown: 30,
            per_integral: 0,
            unit: '积分',
        }
    },

    onShow() {
        uni.setKeepScreenOn({
            keepScreenOn: true,
        })
    },

    onHide() {
        uni.setKeepScreenOn({
            keepScreenOn: false,
        })
    },

    computed: {
        tipsList() {
            return [
                '倾斜手机控制角色左右移动',
                '跳过每个平台获得1分',
                `游戏时长${this.seconds}秒`,
                '倒计时结束或掉落到底部游戏结束',
                `游戏结束后每得1分奖励${this.per_integral}${this.unit}`
            ]
        },

        doodlerStyle() {
            const {x, y, width, height} = this.doodler
            return `left: ${x}px; top: ${y}px; width: ${width}px; height: ${height}px;`
        }
    },

    onLoad(params) {
         this.active_id = params.active_id
        this.point_id = params.point_id
        this.unit = params.unit || '积分'
        this.per_integral = Number(params.per_integral) || 0
        this.seconds = Number(params.seconds) || 30
        this.countdown = this.seconds
        if (params.show_ad) this.showAD = true
        if (params.title) this.$uni.setNavigationBarTitle(params.title)

        this.init()
    },

    onUnload() {
        this.stopGameLoop()
        this.stopAccelerometer()
        this.clearCountdownInterval()
    },

    methods: {
        // 初始化
        async init() {
            await this.getGameData()

            // 平台参数
            this.platforms = []
            this.platformHeight = 20
            this.currentPlatformSpacing = 120, // 当前平台间距
                this.platformCount = 4 // 当前平台数量

            this.passedPlatforms = [] // 记录已经跳过的平台

            const {windowWidth = 0, windowHeight = 0} = uni.getWindowInfo()
            this.screenWidth = windowWidth
            this.screenHeight = windowHeight

            // 设置角色大小为屏幕宽度的1/4
            this.doodler.width = windowWidth / 4
            this.doodler.height = windowWidth / 4

            // 设置平台宽度为屏幕宽度的1/4
            this.platformWidth = windowWidth / 4

            this.$refs.gameTipsPopup.open()
        },

        async getGameData() {
            const data = await this.getOpenerEventChannelData() || {}

            if (data.bg_img) this.images.bgImg = data.bg_img
            if (data.doodler_img) this.images.doodlerImg = data.doodler_img
            if (data.platform_img) this.images.platformImg = data.platform_img

            if (data.navigation_bar) uni.setNavigationBarColor({
                ...data.navigation_bar,
                fail: err => console.log(err)
            })
        },

        getOpenerEventChannelData() {
            return new Promise(resolve => {
                if (!this.getOpenerEventChannel?.()?.once) resolve({})
                this.getOpenerEventChannel().once('data', data => resolve(data))
            })
        },

        gameReady() {
            this.$refs.gameTipsPopup.close()
            this.$refs.readyCountdown.open()
        },

        readyCountdownOver() {
            this.startCountdown()
            this.startGame()
        },

        // 开始倒计时
        startCountdown() {
            this.countdownInterval = setInterval(() => {
                this.countdown--
                if (this.countdown <= 0) this.endGame()
            }, 1000)
        },

        clearCountdownInterval() {
            if (this.countdownInterval) {
                clearInterval(this.countdownInterval)
                this.countdownInterval = null
            }
        },

        // 开始游戏
        startGame() {
            this.gameStatus = 1
            this.initGame()
            this.startGameLoop()
            this.startAccelerometer()
        },

        // 初始化游戏
        initGame() {
            // 设置角色初始位置（在屏幕下方50%的位置）
            this.doodler.x = this.screenWidth / 2 - this.doodler.width / 2
            this.doodler.y = this.screenHeight * 0.5

            // 初始化物理参数
            this.velocityX = 0
            this.velocityY = this.currentJumpSpeed
            this.tiltX = 0
            this.score = 0
            this.passedPlatforms = []
            this.speedLevel = 1
            // 动态计算初始跳跃速度，添加安全检查
            const calculatedSpeed = -Math.sqrt(2 * this.gravity * this.currentPlatformSpacing * 1.5)
            this.currentJumpSpeed = Math.max(calculatedSpeed, -20) // 限制最大跳跃速度

            // 生成初始平台
            this.placePlatforms()

            // 开局时角色位于屏幕中部，部分平台天生就在角色下方
            // 若不做处理，现有的计分逻辑会把这些平台当作“已跳过”从而直接加分
            // 这里将这些初始位于角色下方的平台预标记为已通过，避免开局即得分
            this.markInitialPassedPlatforms()
        },

        // 生成平台
        placePlatforms() {
            this.platforms = []

            // 动态计算平台数量和间距，充分利用屏幕空间
            const minSpacing = 120 // 最小间距，确保可跳跃
            const bottomMargin = 80 // 底部边距
            const topMargin = 100 // 顶部预留空间

            // 可用高度
            const availableHeight = this.screenHeight - bottomMargin - topMargin

            // 计算能放置的平台数量
            const platformCount = Math.floor(availableHeight / minSpacing) + 1

            // 重新计算间距，充分利用可用空间
            const actualSpacing = availableHeight / (platformCount - 1)

            // 保存当前设置，供新平台生成使用
            this.platformCount = platformCount
            this.currentPlatformSpacing = actualSpacing

            // 生成平台，充分利用屏幕高度
            for (let i = 0; i < platformCount; i++) {
                const randomX = Math.floor(Math.random() * (this.screenWidth - this.platformWidth))
                const platform = {
                    x: randomX,
                    y: this.screenHeight - bottomMargin - i * actualSpacing,
                    width: this.platformWidth,
                    height: this.platformHeight,
                }
                this.platforms.push(platform)
            }
        },

        // 将开局时位于角色下方的平台标记为已通过，避免一开始就计分
        markInitialPassedPlatforms() {
            // 角色底部坐标
            const doodlerBottom = this.doodler.y + this.doodler.height
            for (let i = 0; i < this.platforms.length; i++) {
                const p = this.platforms[i]
                // 平台在角色下方（平台顶 y 大于角色底）视为初始已通过
                if (doodlerBottom < p.y && !this.passedPlatforms.includes(p)) {
                    this.passedPlatforms.push(p)
                }
            }
        },

        // 生成新平台
        newPlatform() {
            // 使用当前计算的平台间距（已经是根据屏幕高度优化的）
            let platformSpacing = this.currentPlatformSpacing

            // 根据速度等级微调间距（保持在合理范围内）
            if (this.speedLevel === 2) {
                platformSpacing *= 1.1 // 增加10%
            } else if (this.speedLevel === 3) {
                platformSpacing *= 1.2 // 增加20%
            }

            // 新平台在顶部生成，基于当前最高平台的位置
            let newY = -this.platformHeight * 2 // 默认在屏幕上方
            if (this.platforms.length > 0) {
                // 找到最高的平台（Y值最小）
                const highestPlatform = this.platforms.reduce((highest, platform) =>
                    platform.y < highest.y ? platform : highest,
                )
                newY = highestPlatform.y - platformSpacing
            }

            const randomX = Math.floor(Math.random() * (this.screenWidth - this.platformWidth))
            const platform = {
                x: randomX,
                y: newY,
                width: this.platformWidth,
                height: this.platformHeight,
            }

            // 将新平台插入到数组开头（代表最高的平台）
            this.platforms.unshift(platform)
        },

        // 检查并在顶部生成新平台
        checkAndGenerateTopPlatforms() {
            // 确保顶部有足够的平台
            while (this.platforms.length > 0) {
                // 找到所有平台中最高(y坐标最小)的平台
                const highestPlatform = this.platforms.reduce((min, platform) =>
                    platform.y < min.y ? platform : min, this.platforms[0]
                )

                // 如果最高平台距离屏幕顶部太近，生成新平台
                if (highestPlatform.y > -this.currentPlatformSpacing * 2) {
                    this.newPlatform()
                } else {
                    break
                }
            }
        },

        // 启动重力感应
        startAccelerometer() {
            uni.onAccelerometerChange((res) => {
                if (this.gameStatus === 1) {
                    // 限制倾斜角度范围
                    let rawTilt = res.x
                    if (rawTilt > this.maxTilt) rawTilt = this.maxTilt
                    if (rawTilt < -this.maxTilt) rawTilt = -this.maxTilt

                    // 改进的平滑处理，提高响应性
                    const targetTilt = rawTilt * this.tiltSensitivity
                    // 如果倾斜方向改变，立即响应
                    if ((this.tiltX > 0 && targetTilt < 0) || (this.tiltX < 0 && targetTilt > 0)) {
                        this.tiltX = targetTilt * 0.5 // 立即响应方向改变
                    } else {
                        this.tiltX = this.tiltX * this.moveSmooth + targetTilt * (1 - this.moveSmooth)
                    }
                }
            })

            uni.startAccelerometer({
                interval: 'fast', // 改为快速更新
            })
        },

        // 停止重力感应
        stopAccelerometer() {
            uni.stopAccelerometer()
        },

        // 开始游戏循环
        startGameLoop() {
            this.gameTimer = setInterval(() => {
                this.update()
            }, 20) // 约60FPS
        },

        // 停止游戏循环
        stopGameLoop() {
            if (this.gameTimer) {
                clearInterval(this.gameTimer)
                this.gameTimer = null
            }
        },

        // 游戏更新循环
        update() {
            if (this.gameStatus !== 1) return

            // 水平移动（重力感应控制）
            const targetVelocityX = this.tiltX * this.moveSpeed

            // 根据速度等级调整最大移动速度
            let maxSpeed = 4 // 基础最大速度（降低）
            if (this.speedLevel === 2) {
                maxSpeed = 5
            } else if (this.speedLevel === 3) {
                maxSpeed = 6
            }

            if (targetVelocityX > maxSpeed) this.velocityX = maxSpeed
            else if (targetVelocityX < -maxSpeed) this.velocityX = -maxSpeed
            else this.velocityX = targetVelocityX

            this.doodler.x += this.velocityX

            // 处理屏幕边界（从一边穿越到另一边）
            if (this.doodler.x > this.screenWidth) {
                this.doodler.x = -this.doodler.width
            } else if (this.doodler.x + this.doodler.width < 0) {
                this.doodler.x = this.screenWidth
            }

            // 更新角色朝向
            if (this.velocityX > 0) {
                this.doodler.facingLeft = false
            } else if (this.velocityX < 0) {
                this.doodler.facingLeft = true
            }

            // 更新角色垂直移动
            this.velocityY += this.gravity

            // 计算角色的目标位置（屏幕中间50%的位置）
            const targetY = this.screenHeight * 0.5

            this.doodler.y += this.velocityY

            // 摄像机跟随逻辑：当角色上升超过目标位置时，调整世界位置
            if (this.doodler.y < targetY && this.velocityY < 0) {
                // 计算需要向下移动的距离
                const offsetY = targetY - this.doodler.y

                // 将角色重新定位到目标位置
                this.doodler.y = targetY

                // 将所有平台向下移动相同距离，创造角色上升的视觉效果
                for (let i = 0; i < this.platforms.length; i++) {
                    this.platforms[i].y += offsetY
                }

                // 检查是否需要在顶部生成新平台
                this.checkAndGenerateTopPlatforms()
            }

            // 检查游戏结束
            if (this.doodler.y > this.screenHeight) {
                this.endGame()
                return
            }

            // 平台碰撞检测
            this.updatePlatforms()

            // 更新分数
            this.updateScore()
        },

        // 更新平台
        updatePlatforms() {
            for (let i = 0; i < this.platforms.length; i++) {
                const platform = this.platforms[i]

                // 碰撞检测：只在角色下降时检测碰撞
                if (this.detectCollision(this.doodler, platform) && this.velocityY >= 0) {
                    this.velocityY = this.currentJumpSpeed // 使用当前跳跃速度
                }
            }

            // 移除超出屏幕底部的平台
            for (let i = this.platforms.length - 1; i >= 0; i--) {
                if (this.platforms[i].y >= this.screenHeight + 100) {
                    const removedPlatform = this.platforms[i]
                    this.platforms.splice(i, 1)

                    // 同时从已跳过平台记录中移除
                    const index = this.passedPlatforms.indexOf(removedPlatform)
                    if (index > -1) {
                        this.passedPlatforms.splice(index, 1)
                    }
                }
            }
        },

        // 碰撞检测
        detectCollision(doodler, platform) {
            // 缩小碰撞范围，增加游戏容错性
            const tolerance = 10 // 容错像素

            // 检查水平碰撞
            const horizontalCollision =
                doodler.x + tolerance < platform.x + platform.width &&
                doodler.x + doodler.width - tolerance > platform.x

            // 检查角色底部与平台顶部的碰撞
            const verticalCollision =
                doodler.y + doodler.height >= platform.y - tolerance &&
                doodler.y + doodler.height <= platform.y + platform.height + tolerance

            return horizontalCollision && verticalCollision
        },

        // 更新分数
        updateScore() {
            // 检查是否跳过了新的平台
            for (let i = 0; i < this.platforms.length; i++) {
                const platform = this.platforms[i]

                // 如果角色已经超过平台且该平台未被记录过
                if (
                    this.doodler.y + this.doodler.height < platform.y &&
                    !this.passedPlatforms.includes(platform)
                ) {
                    // 标记平台已被跳过
                    this.passedPlatforms.push(platform)

                    // 获得10积分
                    this.score += 1

                    // 更新速度等级
                    this.updateSpeedLevel()
                }
            }
        },

        // 更新速度等级
        updateSpeedLevel() {
            let newSpeedLevel = 1
            let newJumpSpeed = -8

            if (this.score >= 500) {
                // 第五等级：500分以上，最快速度
                newSpeedLevel = 5
                newJumpSpeed = -Math.sqrt(2 * this.gravity * this.currentPlatformSpacing * 6.5)
            } else if (this.score >= 200) {
                // 第四等级：200-499分
                newSpeedLevel = 4
                newJumpSpeed = -Math.sqrt(2 * this.gravity * this.currentPlatformSpacing * 5)
            } else if (this.score >= 100) {
                // 第三等级：100-199分以上
                newSpeedLevel = 3
                newJumpSpeed = -Math.sqrt(2 * this.gravity * this.currentPlatformSpacing * 4)
            } else if (this.score >= 20) {
                // 第二等级：20-99分，中等速度
                newSpeedLevel = 2
                newJumpSpeed = -Math.sqrt(2 * this.gravity * this.currentPlatformSpacing * 2.5)
            } else {
                // 第一等级：0-19分，最慢速度
                newSpeedLevel = 1
                // 动态计算跳跃能力，确保能跳过基础间距
                newJumpSpeed = -Math.sqrt(2 * this.gravity * this.currentPlatformSpacing * 1.5)
            }

            if (newSpeedLevel !== this.speedLevel) {
                this.speedLevel = newSpeedLevel
                this.currentJumpSpeed = newJumpSpeed
                this.initialVelocityY = newJumpSpeed
            }
        },

        // 结束游戏
        endGame() {
            this.gameStatus = 2
            this.clearCountdownInterval()
            this.stopGameLoop()
            this.stopAccelerometer()
            this.submitResult()
        },

        async submitResult() {
            if (this.isSubmit) return

            this.isSubmit = true

            const count = this.score
            const sign = {
                types: 42,
                point_id: this.point_id,
                result: count ? 'success' : 'fail',
                count
            }

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify(sign))
                }
            })
            uni.hideLoading()

            let resultCode = sign.result === 'success' ? 1 : 0
            let info = resultCode === 1 ? '任务完成' : '任务失败'
            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info = res.info
            }

            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })


            this.getOpenerEventChannel().emit('success')
        }
    },
}
</script>

<style scoped lang="scss">
// 混合器
@mixin center-flex {
    display: flex;
    justify-content: center;
    align-items: center;
}

@mixin absolute-full {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

// 主容器 - 全屏
.content {
    @include absolute-full;
    overflow: hidden;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.game-tips-msg-bar {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 20;
}

// 游戏区域 - 全屏
.game-area {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
}

// 角色样式
.doodler {
    position: absolute;
    @include center-flex;
    font-size: 60px; // 增大字体适应新的角色尺寸
    z-index: 10;
    transition: transform 0.1s ease;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);

    &.face-left {
        transform: scaleX(-1);
    }

    &.face-right {
        transform: scaleX(1);
    }
}

.platform-item {
    position: absolute;
    z-index: 1;
}

.doodler-img, .platform-img {
    width: 100%;
    height: 100%;
}
</style>
