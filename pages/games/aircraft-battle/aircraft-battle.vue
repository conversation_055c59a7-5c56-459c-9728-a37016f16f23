<template>
    <view class="page-body">
        <view style="position: absolute; z-index: 2; width: 100%;">
            <game-top-msg-bar right-icon="icon-rocket">
                <template v-slot:leftMsg>{{ time }}</template>
                <template v-slot:rightMsg>{{ score }}</template>
            </game-top-msg-bar>
        </view>

        <movable-area :style="{'background-image': `url(${images.background_1})`}">
            <view class="main">
                <view v-for="(item,index) in peerList" :key="index" class="peer"
                      :style="{'position':'absolute','top':item.y+'px','left':item.x+'px'}">
                    <image v-if="item.status === 1" mode="widthFix" :src="images.enemy2_fly_1"></image>
                    <image v-if="item.status === 2" mode="widthFix" :src="images.blast">
                    </image>
                </view>
                <view class="bullet" :style="{'position':'absolute','top':item.y+'px','left':item.x+'px'}"
                      v-for="(item,index) in bulletList" :key="index">
                    <image mode="widthFix" :src="images.bullet1"></image>
                </view>
            </view>

            <movable-view :disabled="status === 1" :x="old.x" :y="old.y" :animation="false" direction="all"
                          @change="onChange">
                <image v-if="status !== 3" mode="widthFix" :src="images.myplane"></image>
                <image v-if="status === 3" mode="widthFix" :src="images.meblast"></image>
            </movable-view>
        </movable-area>

        <game-tips-popup ref="gameTips" :tips-list="tipsList" :show-ad="showAd" @startGame="startGame"/>
        <game-result-popup ref="resultPopup" :show-ad="showAd" :unit="unit"/>
    </view>
</template>

<script>
import config from "./config.js"

import gameTipsPopup from '../components/game-tips-popup.vue'
import gameTopMsgBar from '../components/game-top-msg-bar.vue'

export default {
    components: {gameTipsPopup, gameTopMsgBar},
    data() {
        return {
            images: config.images,
            w: 0,
            h: 0,

            score: 0,
            bulletList: [],
            peerList: [], //对方主机
            x: 0,
            y: 0,
            scale: 2,
            old: {
                x: 0,
                y: 0,
                scale: 2
            },

            // createBulletTime: {},
            // bulletMoveTime: {},
            // peerMoveTime: {},
            // createPeerTime: {},
            // clearPeerTime: {},

            plane: {
                width: this.rpxToPx(150),
                height: this.rpxToPx(180)
            },
            status: 1, //1 游戏未开始 2.已开始 3.已撞击

            time: 0, // 游戏时间倒计时
            second: 30,  // 游戏初始时间
            per_integral: 0,
            unit: '积分',

            showAd: false
        }
    },

    computed: {
        tipsList() {
            const {second, per_integral, unit} = this
            return [
                `请在${second}秒内移动我方飞机射击敌方飞机，击中敌方飞机得分加1。`,
                `倒计时结束后，每1分奖励${per_integral}${unit}`
            ]
        }
    },

    watch: {
        peerList: {
            deep: true,
            handler() {
                this.checkCrashPeer();
            }
        },
    },

    onUnload() {
        this.stop()
    },

    async onLoad(params) {
        this.active_id = params.active_id
        if (params.point_id) this.point_id = Number(params.point_id)
        if (params.second) {
            this.time = Number(params.second)
            this.second = Number(params.second)
        }
        if (params.title) this.$uni.setNavigationBarTitle(params.title)
        if (params.per_integral) this.per_integral = Number(params.per_integral)
        if (params.unit) this.unit = params.unit
        if (params.show_ad) this.showAd = true

        await this.getWH().catch(() => {})
        this.init();

        this.$nextTick(() => this.$refs.gameTips.open())
    },
    methods: {
        async getWH() {
            const system = uni.getSystemInfoSync()
            this.w = system.windowWidth
            this.h = system.windowHeight
            return true
        },


        /**
         * 对方战机移动
         */
        peerMove() {
            this.peerList.forEach(item => {
                item.y += 1; //位移距离
                if (item.y >= this.h) item.status = 3
            })

        },
        clearPeer() {
            this.peerList.forEach((item, index) => {
                if (item.status === 3) this.peerList.splice(index, 1)
            });
        },
        /**
         * 判断是否撞机
         */
        checkCrashPeer() {
            const {x, y} = this.old
            const {width, height} = this.plane

            this.peerList.forEach(item => {
                if (item.status === 1) {
                    if (x >= item.x - width && x <= item.x + width) {
                        if (y >= item.y - height * 2 && y <= item.y + height) {
                            this.status = 3;
                            item.status = 2;
                            this.gameOver();
                            //根据gif动画时长来设置
                        }
                    }
                }
            });
        },
        /**
         * 创建战机
         */
        createPeer() {
            const {width, height} = this.plane
            const peer = {
                x: Math.floor(Math.random() * (this.w - width)),
                y: -height,
                status: 1, //1.正常 2.爆炸中 3.阵亡
            };
            this.peerList.push(peer);

        },
        /**
         * 开始游戏
         */
        startGame() {
            this.$refs.gameTips.close()

            if (this.status === 3) {
                this.init();
            }
            if (this.status === 2) {
                uni.showToast({
                    title: "游戏已开始",
                    icon: "none"
                })
                return;
            }
            this.status = 2;
            this.createBulletTime = setInterval(() => this.createBullet(), 100)
            this.bulletMoveTime = setInterval(() => this.bulletMove(), 10)
            //重新创建战机
            this.createPeerTime = setInterval(() => this.createPeer(), 1000)
            this.peerMoveTime = setInterval(() => this.peerMove(), 10)
            this.clearPeerTime = setInterval(() => this.clearPeer(), 100)
            this.countdown()
        },

        countdown() {
            this.time = this.second
            this.countdownInterval = setInterval(() => {
                this.time--
                if (this.time === 0) this.gameOver()
            }, 1000)
        },

        gameOver() {
            this.stop()
            this.submitResult()
        },

        /**
         * 暂停游戏
         */
        stop() {
            if (this.status !== 3) this.status = 1

            clearInterval(this.createBulletTime);
            clearInterval(this.bulletMoveTime);
            clearInterval(this.peerMoveTime);
            clearInterval(this.createPeerTime);
            clearInterval(this.clearPeerTime);
            clearInterval(this.countdownInterval);
        },
        /**
         * 判断子弹是否打中对方
         * @param {Object} x 子弹x坐标
         * @param {Object} y 子弹y坐标
         */
        checkBulletPeer(x, y) {
            //飞机的宽度
            const {width, height} = this.plane

            this.peerList.forEach(item => {
                //飞机的高度
                if (item.status === 1) {
                    if (x >= item.x && x <= item.x + width) {
                        if (y >= item.y && y <= item.y + height) {
                            item.status = 2;
                            this.score++;
                            setTimeout(() => item.status = 3, 500);
                            //根据gif动画时长来设置
                        }
                    }
                }


            })
        },
        /**
         * 重新初始化
         */
        init() {
            this.old.x = this.w / 2 - this.rpxToPx(150 / 2)
            this.old.y = this.h;
            this.bulletList = [];
            this.peerList = [];
            this.score = 0;
        },
        /**
         * 子弹移动
         */
        bulletMove() {
            //子弹每次移动距离
            this.bulletList.forEach(item => item.y -= 10);
        },
        rpxToPx(rpx) {
            return Math.floor(uni.upx2px(rpx));
        },
        //创建子弹
        createBullet() {
            const {width, height} = this.plane

            let removeIndex = [];
            this.bulletList.forEach((item, index) => {
                this.checkBulletPeer(item.x, item.y);
                if (item.y < 0) removeIndex.push(index)

            });
            removeIndex.forEach(item => this.bulletList.splice(item, 1))

            this.bulletList.push({
                x: this.old.x + width / 2,
                y: this.old.y - height / 2
            })
        },
        /**
         * 我方战机移动
         * @param {Object} e 战机移动坐标
         */
        onChange(e) {
            if (this.status !== 2) return
            this.old.x = e.detail.x
            this.old.y = e.detail.y
        },

        async submitResult() {
            const count = this.score
            const sign = {
                types: 24,
                point_id: this.point_id,
                result: count ? 'success' : 'fail',
                count
            }

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify(sign))
                }
            })

            let resultCode = sign.result === 'success' ? 1 : 0
            let info = resultCode === 1 ? '任务完成' : '任务失败'
            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info = res.info
            }


            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })


            this.getOpenerEventChannel().emit('success')
        },
    }
}
</script>

<style lang="scss">
.page-body {
    width: 100%;
    height: 100vh;
}

image {
    height: auto
}


movable-view {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 180rpx;
    width: 150rpx;
    color: #fff;
    position: absolute;
    bottom: 0;
}

.main {
    width: 100%;
    height: 100%;
    position: relative;
}

.bullet {
    position: absolute;
    width: 10rpx;

    image {
        width: 10rpx;
    }
}

.peer {
    height: 180rpx;
    width: 150rpx;

    image {
        height: 180rpx;
        width: 150rpx;
    }
}

movable-area {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    /* background-color: #D8D8D8; */
    overflow: hidden;
}

</style>