<template>
	<view class="page bg-background">
		<view v-if="daily_times" class="text-center p10 font14 color-sub">每人每天可玩{{daily_times}}次游戏</view>

		<view class="game-list pt5">
			<view class="game-item bg-white" v-for="item in game_list" :key="item.types">
				<view class="color-title">{{item.title}}</view>
				<view class="color-content font14 pt5">游戏获得{{item.exchange_num}}分可得到1{{energy_unit}}</view>
				<view class="flex-kai tool-bar">
					<view class="color-sub ptm5" hover-class="navigator-hover" @click="lookRecord(item)">
						<text>游戏记录</text>
						<uni-icons type="forward" color="#80848f"/>
					</view>
					<view class="color-primary ptm5" hover-class="navigator-hover" @click="checkTodaySubmitCount(item)">
						<text>开始游戏</text>
						<uni-icons type="forward" color="#2d8cf0"/>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'


	export default {
		data() {
			return {
				id: '',
				game_list: [],
				daily_times: null,
				energy_unit: '能量'
			}
		},
		onLoad(e) {
			uni.showLoading({
				title: '加载中...',
			})

			this.id = e.id
			login.uniLogin(err => {
			    if (err && err.errMsg) {
			        uni.hideLoading()
			        uni.showModal({
			            title: err.errTitle || '提示',
			            content: err.errMsg,
			            showCancel: false
			        })
			        return false
			    }

				this.getActivityDetail()
			})
		},
		methods: {
			async getActivityDetail() {
				let activity_detail = app.globalData.activity_detail

				if (!activity_detail) {
					const res = await xwy_api.request({
						url: 'front.flat.sport_step.active_list/active_details',
						data: {
							access_token: app.globalData.access_token,
							active_id: this.id
						}
					})


					activity_detail = res?.data?.active_details
				}

				uni.hideLoading()
				if (activity_detail?.conf?.active?.energy_unit) this.energy_unit = activity_detail.conf.active.energy_unit
				this.activity_detail = activity_detail

				this.getGamesList()
			},


			getGamesList() {
				if (!this.activity_detail?.conf?.active?.game_center?.game_list?.length) return false
				if (this.activity_detail?.conf?.active?.game_center?.daily_times) this.daily_times = this.activity_detail.conf.active.game_center.daily_times
				this.game_list = this.activity_detail.conf.active.game_center.game_list
			},

			lookRecord(item) {
				uni.navigateTo({
					url: `./record_list?id=${this.id}&types=${item.types}`
				})
			},

			async checkTodaySubmitCount(item) {
				if (!this.daily_times) {
					this.toGame(item)
					return false
				}

				uni.showLoading({
					title: '游戏准备中...',
					mask: true
				})
				const res = await xwy_api.request({
					url: 'front.flat.sport_step.game_center.userGameCenter/submit_game_count',
					data: {
						access_token: app.globalData.access_token,
						active_id: this.id
					}
				})
				uni.hideLoading()

				const today_submit_count = res?.data?.today_submit_count || 0


				if (today_submit_count >= this.daily_times) {
					xwy_api.alert(`每人每天可玩${this.daily_times}次游戏，你今天已经游戏${today_submit_count}次,不能继续玩游戏了。`)
					return false
				}

				this.toGame(item)
			},

			toGame(item) {
				const url = this.getGamePath(item.types)
				uni.navigateTo({
					url
				})
			},

			getGamePath(types) {
				const glbal = '/pages/games/'
				const options = `?id=${this.id}&types=${types}`
				let game_url = ''
				switch (types) {
					case 2:
						game_url = 'elsfk/elsfk'
						break
					case 3:
						game_url = '2048/2048'
						break
					case 4:
						game_url = 'dds/dds'
						break
				}
				return `${glbal}${game_url}${options}`
			}
		}
	}
</script>

<style>
.page {
	min-height: 100vh;
}
.game-item {
	margin: 10px;
	padding: 10px;
	border-radius: 10px;
}
.tool-bar {
	border-top: 1px solid #eee;
	margin-top: 10px;
	padding-top: 5px;
}
</style>
