<template>
    <view class="content" :style="contentStyle">
        <game-top-msg-bar>
            <template v-slot:leftMsg>
                <text class="font14" v-if="timeDesc">{{ timeDesc }}：</text>
                <text>{{ time }}</text>
            </template>

            <template v-slot:rightMsg>
                <text class="font14">已完成：</text>
                <text>{{ pairNum }}/{{ cardList.length }}</text>
            </template>
        </game-top-msg-bar>

        <view class="card-container">
            <view v-for="(item, index) in cardArrays" :key="index" class="card-item"
                  :style="{'width': `${cardW}px`, 'height': `${cardH}px`}"
                  :class="item.cardFlipClass" @click.stop="clickCard(index)">
                <view class="face front" :style="frontStyle"></view>
                <view class="face back" :class="item.cardFlipClass ? 'flip' : ''"
                      :style="{ backgroundImage: `url(${item.url})` }">
                </view>
            </view>
        </view>

        <game-tips-popup ref="gameTips" :tips-list="tipsList" :show-ad="showAd" @startGame="startGame"/>
        <game-result-popup ref="resultPopup" :show-ad="showAd" :unit="unit"/>
    </view>
</template>

<script>
import config from "./config.js"

import gameTipsPopup from '../components/game-tips-popup.vue'
import gameTopMsgBar from '../components/game-top-msg-bar.vue'

export default {
    components: {gameTipsPopup, gameTopMsgBar},
    data() {
        return {
            memory_time: 5,
            second: 30, // 翻牌倒计时
            time: 5, // 倒计时时间
            timeDesc: '', // 区分是记忆时间还是倒计时
            // timer: null, // 计时器
            gameStatus: 0, // 游戏状态

            cardRow: 4, // 每行卡牌数量
            cardList: JSON.parse(JSON.stringify(config.cardList)), // 源卡牌数组
            cardArrays: [], // 卡牌列表
            cardW: 0, // 卡牌宽度
            cardH: 0, // 卡牌高度
            cardSelectedArr: [], // 临时存放当前点击的卡牌信息

            bgImg: '',
            bgColor: '#fa9990',
            cardBgImg: '',
            pairNum: 0, // 配对的数量

            integral: 0,
            unit: '积分',

            showAd: false
        }
    },

    computed: {
        tipsList() {
            const {memory_time, second, integral, unit} = this
            return [
                `请在${memory_time}秒内，记忆牌面内容及位置。`,
                `游戏中，点击任意两张牌，若相同则配对成功。`,
                `${second}秒内全部配对成奖励${integral}${unit}`
            ]
        },

        contentStyle() {
            if (this.bgImg) return `background-image: url(${this.bgImg});`
            return `background-color: ${this.bgColor};`
        },

        frontStyle() {
            if (this.cardBgImg) return `background-image: url(${this.cardBgImg});`
            return `background-color: ${this.bgColor};`
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        if (params.point_id) this.point_id = Number(params.point_id)
        if (params.second) this.second = Number(params.second)
        if (params.memory_time) {
            this.memory_time = Number(params.memory_time)
            this.time = Number(params.memory_time)
        }
        if (params.title) this.$uni.setNavigationBarTitle(params.title)
        if (params.integral) this.integral = Number(params.integral)
        if (params.unit) this.unit = params.unit
        if (params.show_ad) this.showAd = true

        // 计算卡牌宽高
        this.getCardInfo()
        this.init()

        this.$nextTick(() => this.$refs.gameTips.open())
    },

    onUnload() {
        clearTimeout(this.timer)
    },

    methods: {
        async init() {
            await this.getGameData()

            // 获取卡牌列表
            await this.getCardArrays().catch(() => {})

            // 时间文案
            this.time = this.memory_time
            this.timeDesc = '记忆时间'
            // 配对成功的数量
            this.pairNum = 0

            // 初始化游戏状态
            this.initStatus(0)
        },

        async getGameData() {
            const data = await this.getOpenerEventChannelData()

            if (data.bg_color) this.bgColor = data.bg_color
            if (data.bg_img) this.bgImg = data.bg_img
            if (data.card_bg_img) this.cardBgImg = data.card_bg_img
            if (data.card_imgs?.length) this.cardList = data.card_imgs

            if (data.navigation_bar) uni.setNavigationBarColor({
                ...data.navigation_bar,
                fail: err => console.log(err)
            })
        },

        getOpenerEventChannelData() {
            return new Promise(resolve => this.getOpenerEventChannel().once('data', data => resolve(data)))
        },

        // 计算
        async getCardInfo() {
            const usableW = uni.getWindowInfo().windowWidth - 30 // 屏幕宽度 - padding = 可用宽度
            const cardW = Math.floor((usableW - this.cardRow * 10) / this.cardRow) // (可用宽度 - 间距) / 每行数量 = 每张卡牌宽度
            this.cardW = this.cardH = cardW // 每张卡牌宽高
        },

        getCardList() {
            return this.cardList.map((url, index) => ({url, value: index})) 
        },

        async getCardArrays() {
            const list = [...this.getCardList(), ...this.getCardList()]
            this.cardArrays = list.sort(() => Math.random() - 0.5)
        },


        // 设置游戏状态 0未开始，1记忆时间；2倒计时时间
        initStatus(status = 0) {
            this.gameStatus = status
        },
        async startGame() {
            this.$refs.gameTips.close()
            this.$uni.showToast(`准备开始记忆啦~`)
            // await this.init() // 初始化
            // 更改游戏状态为记忆阶段
            this.timeDesc = '记忆时间'
            this.time = this.memory_time
            this.initStatus(1)
            // 翻开所有牌子
            this.cardArrays.map(item => {
                item.cardFlipClass = 'card-flipped'
            })
            // 开始进入倒计时
            setTimeout(this.countdown, 1000)
        },
        // 开始游戏
        startPlaying() {
            this.cardArrays.map(item => {
                item.cardFlipClass = '' // 恢复翻开的牌子
            })
            this.$uni.showToast(`游戏开始，加油！`)
            setTimeout(() => {
                this.initStatus(2) // 更改状态为倒计时阶段，游戏开始
                this.countdown()
            }, 500)
        },
        clickCard(index) {
            if (this.is_repeat) return
            this.is_repeat = true
            setTimeout(() => this.is_repeat = false, 300)
            // 点击卡牌
            if (this.gameStatus !== 2) {
                this.$uni.showToast(`游戏还未开始！`)
                return
            }
            const item = this.cardArrays[index]
            // 防止重复点击
            if (item.cardFlipClass === 'card-flipped') return

            // 给卡牌添加classname,表示已翻开
            this.$set(item, 'cardFlipClass', 'card-flipped')
            this.$set(item, 'cardStatus', 'going')

            // 记录点击的卡牌信息
            this.cardSelectedArr.push(item.value)

            // 若翻动了两张牌，检测一致性
            if (this.cardSelectedArr.length === 2) {
                clearTimeout(this.checkTimer)
                this.checkTimer = setTimeout(() => {
                    const status = this.cardSelectedArr[0] === this.cardSelectedArr[1] ? 1 : 0
                    if (status) this.pairNum++
                    this.cardArrays.forEach(v => {
                        if (v.cardStatus !== 'going') return
                        if (!status) v.cardFlipClass = '' // 恢复翻开的牌子
                        v.cardStatus = status ? 'success' : '' // 置空状态
                    })
                    // 清空数组，为了保证数组里最多存在2条数据
                    this.cardSelectedArr = []
                    // 判断已点开卡牌数量和总数量是否相等，如果相等，表示游戏结束
                    const flippedNum = this.cardArrays.filter(item => item.cardStatus === 'success').length
                    if (flippedNum === this.cardArrays.length) { // 游戏结束 全部配对成功
                        this.submitFinish('success')
                    }
                }, 300)
            }
        },
        submitFinish(types) {
            clearTimeout(this.timer) // 游戏中显示弹出层 暂停倒计时
            this.initStatus(3)

            this.submitResult(types)
        },


        countdown() {
            // 倒计时
            if (this.time === 0) {
                // 计时结束
                if (this.gameStatus === 1) {
                    // 如果状态为记忆阶段，则进入倒计时阶段
                    // this.timeDesc = '倒计时'
                    this.timeDesc = ''
                    this.time = this.second
                    this.startPlaying()
                    return
                }
                this.$uni.showToast('时间到，游戏结束')
                this.submitFinish('fail')
                return
            }
            // 开启定时器
            this.time--
            this.timer = setTimeout(this.countdown, 1000)
        },

        async submitResult(result) {
            const sign = {
                types: 25,
                point_id: this.point_id,
                result,
            }

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify(sign))
                }
            })

            let resultCode = sign.result === 'success' ? 1 : 0
            let info = resultCode === 1 ? '任务完成' : '任务失败'
            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info = res.info
            }


            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })


            this.getOpenerEventChannel().emit('success')
        }
    }
}
</script>

<style lang="less" scoped>
.content {
    position: relative;
    min-height: 100vh;
    height: 100vh;
    background-size: cover;
    background-repeat: no-repeat;
}

.card-container {
    display: flex;
    flex-wrap: wrap;
    background-color: rgba(255, 255, 255, .8);
    padding: 5px;
    margin: 10px;
    border-radius: 10px;
}

.card-item {
    margin: 5px;
    animation: flips2 .3s ease both;

    .face {
        width: 100%;
        height: 100%;
        position: absolute;
        z-index: 10;
        transition: all .3s ease;
        border-radius: 10rpx;
        backface-visibility: hidden;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: contain;
    }

    .front {
        background-size: contain;
        z-index: 12;
    }

    .back {
        background-color: #fff;
        transform: rotateY(-180deg);
        z-index: 10;

        &.flip {
            z-index: 13;
            animation: flips .3s ease-in both;
        }
    }

    &.card-flipped {
        .front {
            z-index: 10;
            transform: rotateY(180deg);
        }

        .back {
            z-index: 12;
            animation: flips .3s ease-in both;
            background-size: contain;
        }
    }
}

@keyframes flips {
    100% {
        transform: rotateY(0deg);
    }
}

@keyframes flips2 {
    0% {
        transform: rotateY(0deg);
    }

    100% {
        transform: rotateY(180deg);
    }
}
</style>