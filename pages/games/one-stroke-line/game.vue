<template>
    <view class="layout" :style="{'background-image': `url(${images.bgImg})`}">
        <view class="top-msg-container clearfix clear">
            <view v-if="needTime" class="top-msg top-msg-left fl">
                <view class="top-msg-icon">
                    <view class="top-msg-inner-ring">
                        <text class="iconfont icon-alarm-clock"></text>
                    </view>
                </view>
                <view v-if="seconds !== null" class="top-msg-num">{{ timeText }}</view>
            </view>

            <view class="top-msg top-msg-right fr">
                <view class="top-msg-icon">
                    <view class="top-msg-inner-ring">
                        <text class="iconfont icon-grid"></text>
                    </view>
                </view>
                <view class="top-msg-num">{{ score }}</view>
            </view>
        </view>

        <view v-if="gameStart" class="one-stroke-line-game">
            <view class="width100 height100 relative">
                <view class="f-j-c" v-for="(item,index) in gridList" :key="index">
                    <view v-for="(itm,idx) in item" :style="{width: `${cellW}px`, height: `${cellW}px`}"
                          :key="idx"
                          @click.stop="backPoint(itm)">
                        <view v-show="itm.types !== -1" class="width100 height100 p_1">
                            <view class="radius_3 width100 height100 f-j-a-c relative grid-item"
                                  :class="{'join-white': itm.types === 1}">

                                <image v-if="!itm.isStart && !itm.isEnd"
                                       style="position: absolute; left: 0; top: 0; z-index: 1"
                                       :src="images.redbagImg">
                                </image>
                                <view v-if="itm.direction" class="width100 height100 join-white"
                                      style="position: absolute; z-index: 0" :class="{'overlay-left': itm.direction === 'left', 'overlay-right': itm.direction === 'right',
									'overlay-top': itm.direction === 'top','overlay-bottom': itm.direction === 'bottom'}">
                                </view>
                                <view v-if="itm.isStart" class="width100 height100 hidden radius_3 f"
                                      style="z-index: 1">

                                    <image :src="images.headImg" mode="aspectFill"></image>
                                </view>
                                <view v-if="itm.isEnd" class="width100 height100 hidden radius_3 f"
                                      style="z-index: 1">

                                    <image :src="images.endPointImg" mode="aspectFill"></image>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>

                <!-- 头部 -->
                <view class="head-point f-j-a-c p_1"
                      :style="{width: `${cellW}px`, height: `${cellW}px`, top: `${(headPoint.y * cellW)}px`, left: `${(headPoint.x * cellW)}px`}"
                      @touchstart.stop="touchStart" @touchmove.stop="touchMove" @touchend.top="touchEnd">
                    <view class="radius_3 width100 height100 f-j-a-c hidden">
                        <!-- <image :src="images.headImg" mode="aspectFill"></image> -->
                    </view>
                </view>
            </view>
        </view>

        <game-tips-popup ref="gameTips" :tips-list="tipsList" :show-ad="showAd" @startGame="startGame"/>
        <game-result-popup ref="resultPopup" :show-ad="showAd"/>
    </view>
</template>

<script>
import config from './config'


import gameTipsPopup from '../components/game-tips-popup.vue'

export default {
    components: {gameTipsPopup},
    data() {
        return {
            images: config.images,
            cellW: 0,
            // 格子
            gridList: [],
            // 存储触摸路径的点
            path: [{x: 0, y: 0}],
            // 0游戏未开始 1游戏中 2已结束
            gameStart: 0,
            // 是否允许连线，当前触摸在已连接的路线上的最后一个格子才允许连线
            permitMove: false,

            needTime: false,
            seconds: 0,
            per_integral: 0,
            showAd: false
        }
    },
    onLoad() {
        this.init()
    },

    onUnload() {
        this.clearTimeInterval()
    },

    computed: {
        headPoint() {
            return this.path[this.path.length - 1]
        },

        timeText() {
            if (!this.seconds) return '00:00'
            let mm = 0, ss = this.seconds
            if (ss >= 60) {
                mm = Math.floor(ss / 60)
                ss = ss % 60
            }
            return `${mm < 10 ? '0' + mm : mm}:${ss < 10 ? '0' + ss : ss}`
        },

        // 得分: 去除起点和终点后的已连接路径
        score() {
            return this.gridList.flat().filter(item => !item.isStart && !item.isEnd && item.types === 1).length
        },

        tipsList() {
            const {needTime, seconds, per_integral} = this

            const list = []

            if (needTime) {
                list.push('左上角为倒计时，右上角为已连线数')
                list.push(`游戏开始后，请在${seconds}秒内从起点连到终点。`)
            } else {
                list.push('右上角为已连线数')
                list.push(`游戏开始后，请从起点连到终点。`)
            }

            if (per_integral) {
                list.push(`连到终点后，每连一个格子获得${per_integral}积分。`)
            } else {
                list.push(`连到终点后，连接红包越多则积分越多。`)
            }

            return list
        }
    },

    methods: {
        async init() {
            await this.getGameData()
            this.$nextTick(() => this.$refs.gameTips.open())
        },

        async getGameData() {
            const data = await this.getOpenerEventChannelData()

            if (data.title) this.$uni.setNavigationBarTitle(data.title)

            this.active_id = data.active_id
            this.point_id = data.point_id
            this.panel = {x: data.col, y: data.row}
            this.obstacleCount = data.obstacle
            this.per_integral = data.per_integral
            if (data.seconds) {
                this.needTime = true
                this.seconds = data.seconds
            }
            if (data.show_ad) this.showAd = true

            if (data.bg_img) this.images.bgImg = data.bg_img
            if (data.start_img) this.images.headImg = data.start_img
            if (data.end_img) this.images.endPointImg = data.end_img
            if (data.fu_img) this.images.redbagImg = data.fu_img

            if (data.navigation_bar) uni.setNavigationBarColor({
                ...data.navigation_bar,
                fail: err => console.log(err)
            })
        },

        getOpenerEventChannelData() {
            return new Promise(resolve => this.getOpenerEventChannel().once('data', data => resolve(data)))
        },

        async startGame() {
            this.$refs.gameTips.close()
            this.getRect()
            this.initGrid()
            this.gameStart = true

            this.startCountdown()
        },

        startCountdown() {
            if (!this.needTime) return
            this.clearTimeInterval()
            this.timeInterval = setInterval(() => {
                this.seconds--
                if (this.seconds <= 0) {
                    this.seconds = 0
                    this.gameOver(true)
                    this.clearTimeInterval()
                }
            }, 1000)
        },

        clearTimeInterval() {
            if (!this.timeInterval) return
            clearInterval(this.timeInterval)
            this.timeInterval = null
        },

        getRect() {
            const {windowWidth} = uni.getWindowInfo()
            const usableW = windowWidth - 40
            this.cellW = usableW / this.panel.x
        },

        initGrid() {
            const {x: width, y: height} = this.panel
            const banSet = new Set(this.generateBanList(this.obstacleCount).map(({x, y}) => `${x},${y}`))

            // 使用二维数组初始化方法创建网格
            this.gridList = Array.from({length: height}, (_, y) =>
                Array.from({length: width}, (_, x) => {
                    const item = this.getDefaultItem(x, y)
                    const isStart = x === 0 && y === 0
                    const isEnd = x === width - 1 && y === height - 1

                    if (isStart) {
                        item.types = 1
                        item.isStart = 1
                        this.path = [{x, y}]
                    } else if (isEnd) {
                        item.isEnd = 1
                        this.endPoint = {x, y}
                    } else if (banSet.has(`${x},${y}`)) {
                        item.types = -1
                    }

                    return item
                })
            )
        },

        /*// 随机返回起点到终点的L路线或7路线坐标
        // 生成L型或7型路径坐标
        getLOr7() {
            const xEnd = this.panel.x - 1
            const yEnd = this.panel.y - 1
            
            // 使用数组方法代替循环，提高代码可读性
            const lineL = [
                // 水平部分 (底边)
                ...Array.from({length: xEnd + 1}, (_, i) => ({x: i, y: yEnd})),
                // 垂直部分 (左边)
                ...Array.from({length: yEnd}, (_, i) => ({x: 0, y: i}))
            ]
            
            const line7 = [
                // 水平部分 (顶边)
                ...Array.from({length: xEnd + 1}, (_, i) => ({x: i, y: 0})),
                // 垂直部分 (右边)
                ...Array.from({length: yEnd + 1}, (_, i) => ({x: xEnd, y: i}))
            ]
            
            // 随机返回其中一种路径
            return Math.random() < 0.5 ? lineL : line7
        },*/

        // 随机生成不可用格子
        generateBanList(num) {
            const {x: xCount, y: yCount} = this.panel

            // 获取保留的路径格子
            const reservedGrids = this.getRandomPath()
            // 添加起点和终点到保留格子
            reservedGrids.push({x: 0, y: 0}, {x: xCount - 1, y: yCount - 1})

            const blankGrids = []

            // 计算可用格子总数
            const totalAvailableGrids = xCount * yCount - reservedGrids.length
            // 确保请求的空白格子数量不超过可用格子总数
            const adjustedNum = Math.min(num, totalAvailableGrids)

            // 使用Set来提高查找效率
            const reservedSet = new Set(reservedGrids.map(grid => `${grid.x},${grid.y}`))
            const blankSet = new Set()

            // 随机生成空白格子
            while (blankGrids.length < adjustedNum) {
                const randomX = Math.floor(Math.random() * xCount)
                const randomY = Math.floor(Math.random() * yCount)
                const key = `${randomX},${randomY}`

                // 使用Set检查是否已存在，提高性能
                if (!reservedSet.has(key) && !blankSet.has(key)) {
                    blankGrids.push({x: randomX, y: randomY})
                    blankSet.add(key)
                }
            }

            return blankGrids
        },

        /*// 随机返回起点到终点的路径坐标
        getRandomPath() {
            const startX = 0;
            const startY = 0;
            const endX = this.panel.x - 1;
            const endY = this.panel.y - 1;
            
            // 创建一个包含路径的数组
            const path = [{x: startX, y: startY}];
            let currentX = startX;
            let currentY = startY;
            
            // 使用随机步进方式生成路径
            while (currentX !== endX || currentY !== endY) {
                // 确定可能的移动方向
                const possibleMoves = [];
                
                // 向右移动
                if (currentX < endX) {
                    possibleMoves.push({dx: 1, dy: 0});
                }
                
                // 向下移动
                if (currentY < endY) {
                    possibleMoves.push({dx: 0, dy: 1});
                }
                
                // 如果没有可能的移动方向，说明已经到达终点附近
                if (possibleMoves.length === 0) {
                    break;
                }
                
                // 随机选择一个移动方向
                const move = possibleMoves[Math.floor(Math.random() * possibleMoves.length)];
                
                // 更新当前位置
                currentX += move.dx;
                currentY += move.dy;
                
                // 将新位置添加到路径中
                path.push({x: currentX, y: currentY});
            }
            
            return path;
        },*/

        // 随机返回起点到终点的路径坐标 - 高级版本
        getRandomPath() {
            const startX = 0;
            const startY = 0;
            const endX = this.panel.x - 1;
            const endY = this.panel.y - 1;

            // 创建一个包含路径的数组
            const path = [{x: startX, y: startY}];
            let currentX = startX;
            let currentY = startY;

            // 创建一个Set来跟踪已访问的单元格
            const visited = new Set([`${startX},${startY}`]);

            // 使用随机步进方式生成路径
            while (currentX !== endX || currentY !== endY) {
                // 确定可能的移动方向 (上、右、下、左)
                const possibleMoves = [];

                // 检查四个方向
                const directions = [
                    {dx: 0, dy: -1}, // 上
                    {dx: 1, dy: 0},  // 右
                    {dx: 0, dy: 1},  // 下
                    {dx: -1, dy: 0}  // 左
                ];

                for (const dir of directions) {
                    const newX = currentX + dir.dx;
                    const newY = currentY + dir.dy;

                    // 检查新位置是否在网格内且未访问过
                    if (newX >= 0 && newX <= endX &&
                        newY >= 0 && newY <= endY &&
                        !visited.has(`${newX},${newY}`)) {

                        // 如果新位置离终点更近，增加其被选择的权重
                        const weight = (endX - newX) + (endY - newY) < (endX - currentX) + (endY - currentY) ? 3 : 1;

                        // 根据权重添加移动方向
                        for (let i = 0; i < weight; i++) {
                            possibleMoves.push({dx: dir.dx, dy: dir.dy});
                        }
                    }
                }

                // 如果没有可能的移动方向，但还未到达终点
                if (possibleMoves.length === 0) {
                    // 尝试直接向终点移动
                    if (currentX < endX) {
                        currentX++;
                    } else if (currentY < endY) {
                        currentY++;
                    }
                } else {
                    // 随机选择一个移动方向
                    const move = possibleMoves[Math.floor(Math.random() * possibleMoves.length)];

                    // 更新当前位置
                    currentX += move.dx;
                    currentY += move.dy;
                }

                // 将新位置添加到路径中并标记为已访问
                if (!visited.has(`${currentX},${currentY}`)) {
                    path.push({x: currentX, y: currentY});
                    visited.add(`${currentX},${currentY}`);
                }
            }

            return path;
        },

        getDefaultItem(x, y) {
            return {
                x,
                y,
                types: 0, // 格子类型 0未使用 1已使用 -1不能使用
                isStart: 0, // 起点
                isEnd: 0, // 终点
                direction: 0, // 相对于后面一个点的方向
            }
        },


        touchStart() {
            if (!this.gameStart) return
            this.permitMove = true
        },

        touchMove(e) {
            if (!this.permitMove || !this.gameStart) return
            const now = +new Date()
            this.lastMoveTime ||= 0 // 最后移动的时间
            if (now - this.lastMoveTime < config.throttleInterval) return
            this.lastMoveTime = now

            const touch = e.changedTouches[0]
            const {x, y} = this.getGridCoordinates(touch.clientX, touch.clientY)

            if (x < 0 || x > this.panel.x - 1 || y < 0 || y > this.panel.y - 1) return

            // 判断是否超过边界，或者移动到禁止移动的区域 或 是否已在路径(path)中 或 点位是否相邻
            if (!this.isValidPoint(x, y) || !this.pointAdjoin(x, y)) return

            // 判断是否在路径中
            if (this.isPointInPath(x, y)) { // 如果在路径中则判断是不是上一个点 是上一个点则回退一格
                const tpoint = this.path[this.path.length - 2]
                if (tpoint.x === x && tpoint.y === y) {
                    const lastPoint = this.path.pop()
                    this.gridList[lastPoint.y][lastPoint.x].types = 0
                    this.gridList[tpoint.y][tpoint.x].direction = 0
                    return
                }
                return
            }

            this.path.push({x, y, direction: 0,}); // 将新点添加到路径中
            this.gridList[y][x].types = 1
            // 查看上一个点位的位置相对于新点位在什么方向
            const topPoint = this.path[this.path.length - 2]
            topPoint.direction = this.getDirection(topPoint, {x, y})
            this.gridList[topPoint.y][topPoint.x].direction = topPoint.direction
            this.checkPathCompletion()
        },

        touchEnd() {
            this.permitMove = false
        },

        // 获取触摸点在网格中的位置
        getGridCoordinates(x, y) {
            const cellSize = this.cellW; // 每个格子的大小（可以根据实际调整）
            x = x - 20  // 减去左padding20
            y = y - 20 - 55  // 减去上padding20 减去顶部显示时间和数量的区域高度55
            return {
                x: Math.floor(x / cellSize),
                y: Math.floor(y / cellSize)
            }
        },

        // 判断点击的点是否有效
        isValidPoint(x, y) {
            const itm = this.gridList[y][x]
            if (itm.types === -1) return false
            return x >= 0 && x < this.panel.x && y >= 0 && y < this.panel.y;
        },

        // 判断点是否已在路径中
        isPointInPath(x, y) {
            return this.path.some(point => point.x === x && point.y === y);
        },

        // 判断点位是否相邻
        pointAdjoin(x, y) {
            const lastPoint = this.path[this.path.length - 1];
            const dx = Math.abs(x - lastPoint.x);
            const dy = Math.abs(y - lastPoint.y);
            return (
                (dx === 1 && lastPoint.y === y) || // 水平方向相邻
                (dy === 1 && lastPoint.x === x) // 垂直方向相邻
            );
        },

        // 获取前一个点位相对后一点位的方向
        getDirection(topItem, item) {
            if (topItem.x === item.x) {
                if (topItem.y > item.y) return 'top' // 上
                if (topItem.y < item.y) return 'bottom' // 下
            }
            if (topItem.y === item.y) {
                if (topItem.x > item.x) return 'left' // 左
                if (topItem.x < item.x) return 'right' // 右
            }
        },

        // 检查路径是否完成
        checkPathCompletion() {
            if (this.headPoint.x === this.endPoint.x && this.headPoint.y === this.endPoint.y) {
                this.gameOver(false)
            }
        },

        async gameOver(timeout = false) {
            this.clearTimeInterval()

            const sign = {
                types: 22,
                point_id: this.point_id,
                result: timeout ? 'fail' : 'success'
            }
            if (!timeout) sign.count = this.score

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify(sign))
                }
            })

            let resultCode = sign.result === 'success' ? 1 : 0
            let info = resultCode === 1 ? '任务完成' : '任务失败'
            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info = res.info
            }


            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })


            this.getOpenerEventChannel().emit('success')
        },

        // 回退点位
        backPoint(itm) {
            if (!this.gameStart) return
            const index = this.path.findIndex(v => v.x === itm.x && v.y === itm.y)
            if (index === -1) return
            const len = this.path.length - index - 1
            let i = 0;
            const backPointTimer = setInterval(() => {
                if (i >= len) {
                    clearInterval(backPointTimer)
                    return
                }
                const item = this.path.pop()
                this.gridList[item.y][item.x].types = 0
                // 充值上一个点位相对于下一个点位的方向
                const topPoint = this.path[this.path.length - 1]
                this.gridList[topPoint.y][topPoint.x].direction = 0

                i++
            }, 50)
        },

    },
}
</script>

<style scoped lang='scss'>
.one-stroke-line-game {
    padding: 20px;

    view {
        box-sizing: border-box;
    }
}

.layout {
    width: 100vw;
    min-height: 100vh;
    height: 100vh;
    padding-bottom: env(safe-area-inset-bottom);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    /* background-image: linear-gradient(to bottom, #002825, #35652d); */

    .grid-item {
        background-color: rgba(255, 255, 255, .3);

        image {
            width: 100%;
            height: 100%;
        }
    }

    .head-point {
        position: absolute;
        transition: all .05s;
        z-index: 2
    }
}


.overlay-left {
    top: 0;
    left: -50%
}

.overlay-right {
    top: 0;
    right: -50%
}

.overlay-top {
    top: -50%;
    left: 0
}

.overlay-bottom {
    bottom: -50%;
    left: 0
}

.join-white {
    background-color: #ffeef3 !important;
}

.width80 {
    width: 80%;
}

.width100 {
    width: 100%;
}

.height100 {
    height: 100%;
}

.relative {
    position: relative;
}

.f {
    display: flex;
}

.f-j-c {
    display: flex;
    justify-content: center;
}

.f-j-a-c {
    display: flex;
    justify-content: center;
    align-items: center;
}

.p_1 {
    padding: .1rem !important;
}

.radius_3 {
    border-radius: .3rem;
}

.hidden {
    overflow: hidden;
}

.top-msg-container {
    padding: 15px 3vw 10px calc(3vw + 15px);

    .top-msg {
        position: relative;
        display: flex;
        flex-direction: row;

        .top-msg-icon {
            position: absolute;
            left: -15px;
            top: -3px;
            border-radius: 50%;
            padding: 3px;

            .top-msg-inner-ring {
                width: 30px;
                height: 30px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 50%;

                .iconfont {
                    color: #fff;
                    font-size: 22px;
                }
            }
        }

        .top-msg-num {
            background-color: rgba(0, 0, 0, .3);
            color: #fff;
            height: 30px;
            border-radius: 0 15px 15px 0;
            padding: 0 15px 0 26px;
            min-width: 60px;
            font-size: 18px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .top-msg-left {
        .top-msg-icon {
            background-color: #5cadff;
            box-shadow: 0 0 5px #2d8cf0;

            .top-msg-inner-ring {
                background-color: #2d8cf0;
            }
        }

        .top-msg-num {
            box-shadow: 0 0 10px rgba(45, 140, 240, .2) inset;
        }
    }

    .top-msg-right {
        .top-msg-icon {
            background-color: #fce230;
            box-shadow: 0 0 5px #ffb400;

            .top-msg-inner-ring {
                background-color: #ffb400;
            }
        }

        .top-msg-num {
            box-shadow: 0 0 10px rgba(255, 180, 0, .2) inset;
        }
    }
}
</style>