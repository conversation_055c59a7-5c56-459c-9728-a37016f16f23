<template>
    <view class='content hidden' :style="{'background-image': `url(${imgObj.bgImg})`}">

        <game-top-msg-bar right-icon="icon-rocket">
            <template v-slot:leftMsg>{{ time }}</template>
            <template v-slot:rightMsg>{{ hitCount }}</template>
        </game-top-msg-bar>

        <view class="height100" :catchtouchmove="true">
            <view class="relative w-100 height100 hidden f-d-c-j-sb">
                <view class="w-100 height100 relative">
                    <view class="absolute"
                          :style="{'left': `${NIAN.x}px`, 'top': `${NIAN.y}px`, 'width': `${NIAN.w}px`}">
                        <image :src="imgObj.nianImg"
                               :style="{'width': `${NIAN.w}px`, height: `${NIAN.h}px`}"></image>
                    </view>
                </view>

                <!-- 火箭，左右摇摆，根据角度发射自己 -->
                <view v-show="showRocket" class="absolute f" :style="{width: `${rocket.w}px`, top: `${rocket.y}px`, left: `${rocket.x}px`,
						transform: `rotate(${rocket.angle}deg)`}" @click.stop="send">
                    <image :src="imgObj.rocketImg"
                           :style="{'width': `${rocket.w}px`, height: `${rocket.h}px`}"></image>
                </view>

                <!-- 击中效果 -->
                <view v-if="isHit" class="absolute"
                      :style="{'top': `${hitY}px`, 'left': `${hitX}px`, 'width': `${rocket.w}px`}">
                    <view class="relative f w-100">
                        <image :src="imgObj.deathImg"
                               :style="{'width': `${rocket.w}px`, height: `${rocket.h}px`}"></image>
                    </view>
                </view>
            </view>
        </view>

        <game-tips-popup ref="gameTips" :tips-list="tipsList" :show-ad="showAd" @startGame="startGame"/>
        <game-result-popup ref="resultPopup" :show-ad="showAd" :unit="unit"/>
    </view>
</template>

<script>
import config from "./config.js"

import gameTipsPopup from '../components/game-tips-popup.vue'
import gameTopMsgBar from '../components/game-top-msg-bar.vue'

export default {
    components: {gameTipsPopup, gameTopMsgBar},
    data() {
        return {
            imgObj: config.image,
            windowWidth: 0,
            windowHeight: 0,
            gameStatus: -1, // 0游戏未开始 1游戏进行中 2游戏已结束

            // downTimer: null, // 倒计时
            maxSpeed: 20, // 最大速度
            acceleration: 0.5, // 加速度


            // 火箭的数据
            rocket: {
                w: 0,
                h: 0,
                x: 0,
                y: 0,
                angle: 0, // 角度
                direction: 1, // 摇摆方向 1顺时针 -1逆时针
                status: 0, // 0未发射 1移动中
            },
            // rocketSwingTimer: null, // 火箭摇摆定时
            rocketSource: {}, // 火箭的初始数据
            showRocket: true, // 是否显示火箭

            // 敌人的数据
            NIAN: {
                w: 0,
                h: 0,
                x: 0,
                y: 0,
                direction: 1, // 移动方向
            },
            // nianMoveTimer: null, // 敌人移动定时器

            hitCount: 0, // 击中年兽的次数
            time: 0, // 游戏时间倒计时

            second: 30,  // 游戏初始时间

            // 击中效果
            isHit: false,
            hitY: 0,
            hitX: 0,

            velocity: 0, // 初始速度
            // movementInterval: null, // 冲天炮发射后移动


            per_integral: 0,
            unit: '积分',

            showAd: false
        }
    },

    computed: {
        tipsList() {
            const {second, per_integral, unit} = this
            return [
                `请在${second}秒内发射火箭攻击年兽，火箭击中年兽得分加1。`,
                `倒计时结束后，每1分奖励${per_integral}${unit}`
            ]
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        if (params.point_id) this.point_id = Number(params.point_id)
        if (params.second) {
            this.time = Number(params.second)
            this.second = Number(params.second)
        }
        if (params.title) this.$uni.setNavigationBarTitle(params.title)
        if (params.per_integral) this.per_integral = Number(params.per_integral)
        if (params.unit) this.unit = params.unit
        if (params.show_ad) this.showAd = true

        this.init()

        this.$nextTick(() => this.$refs.gameTips.open())
    },

    onUnload() {
        clearInterval(this.movementInterval)
        clearInterval(this.rocketSwingTimer)
        clearInterval(this.downTimer)
        clearInterval(this.nianMoveTimer)
    },

    methods: {
        async init() {
            await this.getGameData()

            // 获取火箭的信息
            const rocket = await this.getImageInfo(this.imgObj.rocketImg).catch(() => {})
            // 获取年兽的信息
            const NIAN = await this.getImageInfo(this.imgObj.nianImg).catch(() => {})
            this.get_system()
            // 获取火箭的高度
            if (rocket) {
                this.rocket.h = this.rocket.w / (rocket.width / rocket.height)
                this.rocket.x = this.windowWidth / 2 - this.rocket.w / 2
                this.rocket.y = this.windowHeight - this.rocket.h - 100
                this.rocketSource = this.xwyLib.copyObj(this.rocket)
            }
            if (NIAN) this.NIAN.h = this.NIAN.w / (NIAN.width / NIAN.height) // 获取敌人的高度
            this.gameStatus = 0
        },

        async getGameData() {
            const data = await this.getOpenerEventChannelData()

            if (data.title) this.$uni.setNavigationBarTitle(data.title)

            if (data.bg_img) this.imgObj.bgImg = data.bg_img
            if (data.rocket_img) this.imgObj.rocketImg = data.rocket_img
            if (data.monster_img) this.imgObj.nianImg = data.monster_img
            if (data.boom_img) this.imgObj.deathImg = data.boom_img

            if (data.navigation_bar) uni.setNavigationBarColor({
                ...data.navigation_bar,
                fail: err => console.log(err)
            })
        },

        getOpenerEventChannelData() {
            return new Promise(resolve => this.getOpenerEventChannel().once('data', data => resolve(data)))
        },

        getImageInfo(img) {
            return new Promise((s, f) => {
                uni.getImageInfo({
                    src: img,
                    success: res => s(res),
                    fail: () => f()
                })
            })
        },

        get_system() {
            const windowInfo = uni.getWindowInfo();
            this.windowWidth = windowInfo.windowWidth;
            this.windowHeight = windowInfo.windowHeight;
            const w = this.windowWidth / 5
            // 获取的火箭的数据
            this.rocket.w = w * 2
            // 获取敌人的宽
            this.NIAN.w = w * 2
            return true
        },

        // 定时摇摆火箭
        rocketSwing() {
            this.rocket.direction = Math.random() > 0.5 ? 1 : -1
            this.rocketSwingTimer = setInterval(() => {
                // 到达边界反向摇摆
                if (this.rocket.angle >= 45 || this.rocket.angle <= -45) this.rocket.direction = -this.rocket
                    .direction
                this.rocket.angle += 2 * this.rocket.direction; // 每次增加2度
            }, 25);
        },

        // 年兽移动
        nianMove() {
            this.nianMoveTimer = setInterval(() => {
                // 到达边界反向移动
                if (this.NIAN.x < 0 || this.NIAN.x > this.windowWidth - this.NIAN.w) this.NIAN.direction = -
                    this.NIAN.direction
                this.NIAN.x += 2 * this.NIAN.direction;
            }, 15)
        },

        // 游戏倒计时
        downTime() {
            this.downTimer = setInterval(() => {
                if (this.time <= 0) {
                    this.end()
                    clearInterval(this.downTimer)
                    return
                }
                this.time--
            }, 1000)
        },

        // 发射子弹
        send() {
            if (this.rocket.status === 1) return
            this.rocket.status = 1
            clearInterval(this.rocketSwingTimer)
            // 火箭移动
            // 获取发射方向的角度（基于炮口）
            const angleInRad = (this.rocket.angle * Math.PI) / 180
            // 发射速度
            this.velocity = 0
            this.movementInterval = setInterval(() => this.cannonMovement(angleInRad), 16) // 每16ms更新一次火箭的位置
        },

        cannonMovement(angleInRad) {
            if (this.gameStatus === 2) {
                clearInterval(this.movementInterval)
                return
            }
            // 计算火箭的方向
            const dx = Math.sin(angleInRad) * this.velocity // 水平方向的增量（修正）
            const dy = Math.cos(angleInRad) * this.velocity // 垂直方向的增量（修正）

            this.rocket.x += dx // 更新X坐标
            this.rocket.y -= dy // 更新Y坐标

            // 每次更新后增加速度（加速度）
            if (this.velocity < this.maxSpeed) this.velocity += this.acceleration

            // 如果火箭超出矩形范围，则停止发射
            if (this.rocket.x < -this.rocket.w || this.rocket.x > this.windowWidth || this.rocket.y < -this.rocket
                .h || this.rocket.y > this.windowHeight - 60) {
                this.refreshRocket()
                return
            }

            const collision = this.judgeCollision()
            // 撞击到年兽 显示击中动画
            if (collision) {
                clearInterval(this.nianMoveTimer)
                this.NIAN.x -= 15
                if (this.NIAN.x <= 0) this.NIAN.x = 0
                const refreshNianMove = setTimeout(() => {
                    if (this.gameStatus !== 1) {
                        clearTimeout(refreshNianMove)
                        return
                    }
                    this.nianMove()
                }, 300)
                this.refreshRocket(1)
            }
        },

        // 判断是否碰撞
        judgeCollision() {
            const rect1 = {
                x: this.rocket.x,
                y: this.rocket.y,
                width: this.rocket.w,
                height: this.rocket.h,
            }
            const rect2 = {
                x: this.NIAN.x,
                y: this.NIAN.y,
                width: this.NIAN.w,
                height: this.NIAN.h,
            }

            // 缩小击中范围 冲天炮
            rect1.width = rect1.width * .6
            rect1.height = rect1.height * .6
            rect1.x = rect1.x + rect1.width * .2
            rect1.y = rect1.y + rect1.height * .2
            // 缩小击中范围 年兽
            rect2.width = rect2.width * .6
            rect2.height = rect2.height * .6
            rect2.x = rect2.x + rect2.width * .2
            rect2.y = rect2.y + rect2.height * .2

            return this.isRectangleIntersect(rect1, rect2)
        },

        // 判断两个矩形是否相交
        isRectangleIntersect(rect1, rect2) {
            // 矩形1的左上角坐标
            const x1 = rect1.x;
            const y1 = rect1.y;
            // 矩形1的右下角坐标
            const x2 = x1 + rect1.width;
            const y2 = y1 + rect1.height;

            // 矩形2的左上角坐标
            const x3 = rect2.x;
            const y3 = rect2.y;
            // 矩形2的右下角坐标
            const x4 = x3 + rect2.width;
            const y4 = y3 + rect2.height;

            // 判断是否相交
            return !(x2 < x3 || x1 > x4 || y2 < y3 || y1 > y4);
        },


        // 开始游戏
        startGame() {
            this.$refs.gameTips.close()

            // 重置分数，倒计时
            this.time = this.second
            this.hitCount = 0
            // 开始游戏
            this.gameStatus = 1
            // 游戏时间倒计时
            this.downTime()
            // 初始化火箭
            this.initRocket()
            // 火箭左右摇摆动画
            this.rocketSwing()
            // 年兽左右移动
            this.nianMove()
        },

        // 结束游戏
        end() {
            this.gameStatus = 2
            clearInterval(this.rocketSwingTimer)
            clearInterval(this.nianMoveTimer)
            this.initRocket()

            this.submitResult()
        },

        show_hit() {
            this.isHit = true
            this.hitX = this.rocket.x;
            this.hitY = this.rocket.y;
            this.hitCount++
            this.showRocket = false

            setTimeout(() => {
                this.isHit = false
                this.hitX = 0
                this.hitY = 0
                this.showRocket = true
            }, 300)
        },

        refreshRocket(types) {
            clearInterval(this.movementInterval)
            if (types) this.show_hit()
            setTimeout(() => {
                if (this.gameStatus === 2) {
                    clearInterval(this.movementInterval)
                    return
                }
                this.initRocket()
                this.rocketSwing()
            }, 100)
        },

        initRocket() {
            this.rocket = this.xwyLib.copyObj(this.rocketSource)
        },


        async submitResult() {
            const count = this.hitCount
            const sign = {
                types: 23,
                point_id: this.point_id,
                result: count ? 'success' : 'fail',
                count
            }

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify(sign))
                }
            })

            let resultCode = sign.result === 'success' ? 1 : 0
            let info = resultCode === 1 ? '任务完成' : '任务失败'
            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info = res.info
            }


            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })


            this.getOpenerEventChannel().emit('success')
        },
    },
}
</script>

<style scoped lang='scss'>
.content {
    width: 100%;
    min-height: 100vh;
    height: 100vh;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-color: #f8f8f8;
}

.hidden {
    overflow: hidden;
}

.relative {
    position: relative;
}

.absolute {
    position: absolute;
}


.f-d-c-j-sb {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}


.width80 {
    width: 80%;
}

.height100 {
    height: 100%;
}

.f {
    display: flex;
}
</style>