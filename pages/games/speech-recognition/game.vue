<template>
    <view class="page" :style="{backgroundImage: `url(${bgImg})`}">
        <game-top-msg-bar :show-left="false" right-icon="icon-alarm-clock">
            <template v-slot:rightMsg>{{ countdown }}</template>
        </game-top-msg-bar>


        <!-- 主要内容区域 -->
        <view class="content">
            <!-- 提示信息 -->
            <view class="tips">
                <text>提示:</text>
                <text>用普通话快速朗读以下文字。</text>
                <text>口齿越清晰读音越标准，准确率越高！</text>
            </view>

            <!-- 朗读文本卡片 -->
            <view class="text-card" :style="{backgroundImage: `url(${blackboardImg})`}">
                <view class="text-content" :style="{color: textColor}">{{ readText }}</view>
            </view>

            <!-- 操作按钮 -->
            <view class="action-btn" :class="gameStatus" :style="actionButtonStyle" @click="handleAction">
                <view v-show="gameStatus === 'recording'" class="recording-text word-last-loading">录制中</view>
                <view class="btn-text" :class="{'word-last-loading': gameStatus === 'completed'}">
                    {{ buttonText }}
                </view>
            </view>
        </view>

        <game-tips-popup ref="gameTipsPopup" buttonText="好的" title="玩法说明" :tips-list="tipsList"
                         @startGame="startGame"/>

        <game-result-popup ref="resultPopup" :show-ad="showAD" :unit="unit"/>

        <xwy-ad v-if="showAD" :ad_type="3"/>

        <ready-countdown ref="readyCountdown" @countdownOver="countdownOver"/>
    </view>
</template>

<script>
const app = getApp()
import gameTopMsgBar from '../components/game-top-msg-bar.vue'
import gameTipsPopup from '../components/game-tips-popup.vue'
import readyCountdown from '../components/ready-countdown.vue'

export default {
    components: {gameTopMsgBar, gameTipsPopup, readyCountdown},
    data() {
        return {
            bgImg: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/game/speech-recognition/bg.png',
            blackboardImg: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/game/speech-recognition/text-container.png',
            textColor: '#ffffff',
            buttonBgColors: ['#FFD700', '#FFA500'],
            buttonTextColor: '#8B4513',
            gameStatus: 'waiting', // waiting, recording, completed
            readText: '',
            initialCountdown: 30,
            countdown: 30,
            accuracy: 0,
            integral: 0,
            unit: '',
            showAD: false
        }
    },

    computed: {
        buttonText() {
            switch (this.gameStatus) {
                case 'waiting':
                    return '开始挑战'
                case 'recording':
                    return '完成录制'
                case 'completed':
                    return '识别中'
                default:
                    return '开始挑战'
            }
        },

        tipsList() {
            return [
                `请在${this.initialCountdown}秒内用普通话快速朗读文字，口齿越清晰读音越标准，准确率越高。`,
                '请在安静的环境下朗读，避免噪音干扰，并保持朗读时的音量适中。',
                `准确率达到${this.accuracy}%及以上可获得${this.integral}${this.unit}。`
            ]
        },

        actionButtonStyle() {
            return `background: linear-gradient(45deg, ${this.buttonBgColors.join(', ')}); color: ${this.buttonTextColor};`
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.point_id = Number(params.point_id)
        this.unit = params.unit
        if (params.title) this.$uni.setNavigationBarTitle(params.title)
        if (params.show_ad) this.showAD = true

        this.initRecorderManager()
        this.getGameData()
    },

    onUnload() {
        if (this.recorderManager && this.gameStatus === 'recording') {
            // 手动退出不提交
            this.notSubmit = true
            this.recorderManager.stop()
        }
        this.clearCountdown()
        this.clearCheckResultTimeout()
    },

    methods: {
        getGameData() {
           this.getOpenerEventChannel().once('gameData', data => {
               const {integral, seconds, accuracy, text} = data
               this.readText = text
               this.initialCountdown = seconds
               this.countdown = seconds
               this.accuracy = accuracy
               this.integral = integral

               if (data.bg_img) this.bgImg = data.bg_img
               if (data.blackboard_img) this.blackboardImg = data.blackboard_img
               if (data.text_color) this.textColor = data.text_color
               if (data.button) {
                const {bg_colors, text_color} = data.button
                if (bg_colors?.length) this.buttonBgColors = bg_colors
                if (text_color) this.buttonTextColor = text_color
               }
               if (data.navigation_bar) uni.setNavigationBarColor({
                   ...data.navigation_bar,
                   fail: err => console.log(err)
               })

               this.$refs.gameTipsPopup.open()
           })
        },

        startGame() {
            this.$refs.gameTipsPopup.close()
        },

        initRecorderManager() {
            this.recorderManager = uni.getRecorderManager()

            this.recorderManager.onStart(() => {
                uni.hideLoading()
                console.log('录音开始')
                this.gameStatus = 'recording'
                this.startCountdown()
            })

            this.recorderManager.onStop((res) => {
                console.log('录音结束', res)
                this.gameStatus = 'completed'
                this.clearCountdown()
                if (!this.notSubmit) this.uploadAudio(res)
            })

            this.recorderManager.onError((err) => {
                console.error('录音错误', err)
                this.gameStatus = 'waiting'
                this.clearCountdown()
                if (err.errMsg === "operateRecorder:fail auth deny") {
                    this.requestRecorderAuth()
                }
            })
        },

        requestRecorderAuth() {
            uni.authorize({
                scope: 'scope.record',
                success: () => {
                    this.startRecording()
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '需要授权录音功能才能开始挑战',
                        confirmText: '去授权',
                        success: (res) => {
                            if (res.confirm) {
                                uni.openSetting()
                            }
                        }
                    })
                }
            })
        },

        handleAction() {
            if (this.gameStatus === 'completed') return

            if (this.gameStatus === 'waiting') {
                this.$refs.readyCountdown.open(3)
                // this.startRecording()
                return
            }

            if (this.gameStatus === 'recording') {
                // 刚开始3秒内不让结束录音，怕是用户连续点开始导致点到结束
                if (this.initialCountdown - this.countdown < 3) {
                    this.$uni.showToast('点的太快咯')
                    return
                }

                // 结束前2秒不能手动结束，避免手动结束和倒计时结束一起执行
                if (this.countdown <= 2) return
                this.stopRecording()
            }
        },

        countdownOver() {
            this.$uni.showLoading('准备录制')
            this.startRecording()
        },

        startRecording() {
            this.recorderManager.start({
                duration: this.initialCountdown * 1000,
                format: 'mp3'
            })
        },

        stopRecording() {
            this.recorderManager.stop()
        },

        startCountdown() {
            this.countdown = this.initialCountdown
            this.countdownInterval = setInterval(() => {
                this.countdown--
                if (this.countdown <= 0) {
                    this.stopRecording()
                }
            }, 1000)
        },

        clearCountdown() {
            if (this.countdownInterval) {
                clearInterval(this.countdownInterval)
                this.countdownInterval = null
            }
        },

        async uploadAudio(audioData) {
            try {
                this.$uni.showLoading('识别中...')

                // 上传音频文件
                const audioSrc = await this.xwy_api.uploadAudio(audioData, this.active_id)

                // 调用识别接口
                const recognitionResult = await this.getRecognitionResult(audioSrc)

                // 显示结果
                await this.submitResult(recognitionResult)

            } catch (error) {
                console.error('上传音频失败', error)

                // 添加超时错误处理
                if (error.message === '识别超时，请重试') {
                    this.$uni.showToast('识别超时，请重新尝试');
                } else {
                    this.$uni.showToast('识别失败，请重试');
                }

                this.gameStatus = 'waiting'
            } finally {
                uni.hideLoading()
            }
        },

        async getRecognitionResult(audioSrc) {
            // 提交音频URL进行识别
            const submitRes = await this.xwy_api.request({
                url: 'front.flat.active.red_pack.voice.userSubmit/submit_voice_url',
                data: {
                    url: audioSrc
                }
            })

            if (!submitRes?.data?.task_id) {
                throw new Error('提交识别任务失败')
            }

            // 轮询获取识别结果
            return new Promise((resolve, reject) => {
                let attempts = 0; // 添加尝试次数计数器
                const maxAttempts = 20; // 设置最大尝试次数

                const checkResult = async () => {
                    attempts++; // 每次尝试增加计数器

                    if (attempts > maxAttempts) {
                        reject(new Error('识别超时，请重试'));
                        return;
                    }

                    try {
                        const resultRes = await this.xwy_api.request({
                            url: 'front.flat.active.red_pack.voice.userSubmit/voice_task_result',
                            data: {
                                task_id: submitRes.data.task_id
                            }
                        })

                        if (resultRes?.data?.result?.text) {
                            // 获取最终分析结果
                            const analysisRes = await this.xwy_api.request({
                                url: 'front.flat.active.red_pack.voice.userSubmit/submit_user_voce_answer',
                                data: {
                                    url: audioSrc,
                                    system_set_text: this.readText,
                                    task_id: submitRes.data.task_id
                                }
                            })

                            resolve(analysisRes.data)
                        } else {
                            this.checkResultTimeout = setTimeout(checkResult, 5000); // 将间隔改为5000毫秒（5秒）
                        }
                    } catch (error) {
                        reject(error);
                    }
                }
                checkResult();
            })
        },

        clearCheckResultTimeout() {
            if (this.checkResultTimeout) clearTimeout(this.checkResultTimeout)
        },


        async submitResult(result) {
            const percent = result.percent

            const sign = {
                types: 37,
                point_id: this.point_id,
                result: percent >= this.accuracy ? 'success' : 'fail'
            }

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify(sign))
                }
            })

            const integral = res?.data?.num || null
            let resultCode = sign.result === 'success' ? 1 : 0

            let info = `准确率为${percent}%，`
            info += resultCode ? `获得${integral || 0}${this.unit}。` : `不足${this.accuracy}%，无法获得奖励。`

            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info += ` (${res.info})`
            }


            this.$refs.resultPopup.open({
                code: resultCode,
                integral: integral,
                info
            })


            this.getOpenerEventChannel().emit('success')
        }
    }
}
</script>

<style lang="scss">
.page {
    width: 100vw;
    height: 100vh;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    overflow: hidden;
}

.content {
    padding: 40rpx;
    color: #fff;

    .tips {
        margin-bottom: 40rpx;
        background-color: rgba(0, 0, 0, .3);
        border-radius: 20rpx;
        padding: 40rpx;

        text {
            display: block;
            font-size: 28rpx;
            line-height: 1.5;
        }
    }

    .text-card {
        border-radius: 20rpx;
        padding: 40rpx;
        margin-bottom: 100rpx;
        background-size: 100% 100%;
        background-repeat: no-repeat;

        .text-content {
            font-size: 32rpx;
            line-height: 1.8;
            font-weight: 500;
            min-height: 240rpx;
            max-height: 50vh;
            overflow-y: auto;
        }
    }

    .action-btn {
        width: 200px;
        height: 48px;
        border-radius: 24px;
        text-align: center;
        margin: 0 auto 40rpx;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);

        .btn-text {
            display: inline;
            font-size: 20px;
            font-weight: bold;
            line-height: 48px;
        }

        /*等待录制时的按钮*/
        &.waiting {
            animation: scale 1s linear infinite alternate;
        }

        @keyframes scale {
            from {
                transform: scale(1);
            }
            to {
                transform: scale(1.1);
            }
        }
        /*等待录制时的按钮*/

        /*录制中时的按钮*/
        &.recording {
            position: relative;

            .recording-text {
                position: absolute;
                font-size: 14px;
                top: -20px;
                left: 50%;
                margin-left: -21px;
                color: #2d8cf0;
            }
        }
    }
}

</style>