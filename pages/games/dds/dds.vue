<template>
    <view class="page" :style="pageBackgroundStyle">
        <view class="top-msg-container clearfix clear">
            <view class="top-msg top-msg-left fl">
                <view class="top-msg-icon">
                    <view class="top-msg-inner-ring">
                        <text class="iconfont icon-alarm-clock"></text>
                    </view>
                </view>
                <view class="top-msg-num">{{ time }}</view>
            </view>

            <view class="top-msg top-msg-right fr">
                <view class="top-msg-icon">
                    <view class="top-msg-inner-ring">
                        <text class="iconfont icon-grid"></text>
                    </view>
                </view>
                <view class="top-msg-num">{{ countResult }}</view>
            </view>
        </view>

        <view class="box">
            <view class="flex_col flex_col_3 flex_wrap">
                <view class="item" v-for="(item,index) in list" :key="index" @tap="whack(index)">

                    <image :src="imageSource.hole" mode="aspectFit" class="hole"/>

                    <image :src="imageSource.mouse" mode="aspectFit" class="mouse"
                           :class="{'mouse-show': item === 1}"/>

                    <image :src="imageSource.boom" mode="aspectFit" class="boom" v-show="item === 2"/>
                </view>
            </view>
            <view v-if="!flag" class="flex-all-center" style="padding-top: 20px;">
                <view class="start-btn text-center font18" :style="startButtonStyle" @click.stop="restart">
                    开始游戏
                </view>
            </view>
        </view>

        <view v-if="showAd" class="bottom-ad">
            <xwy-ad :ad_type="66"/>
        </view>

       <game-result-popup ref="resultPopup" :show-ad="showAd"/>
    </view>
</template>

<script>
import xwy_config from '@/config/config'

const image_base_url = `${xwy_config.object_storage_url}img/jbz_xcx/static/image/games-dds-`

export default {
    data() {
        return {
            list: [0, 0, 0, 0, 0, 0, 0, 0, 0], //数值说明 0:无状态,1:老鼠显示,2:砸中状态
            result: 0, // 结果分数
            time: 30, // 游戏时间
            timeNum: 30, // 游戏总时间
            flag: false, // 是否正在游戏
            imageSource: {
                hole: `${image_base_url}hole.png`,
                mouse: `${image_base_url}mouse.png`,
                boom: `${image_base_url}boom.png`,
            },
            from_task: false,
            showAd: false,
            pageBackgroundStyle: '',
            startButtonStyle: 'background-color: #2d8cf0; color: #ffffff;',
        }
    },
    onLoad(e) {
        this.id = e.id
        if (e.types) this.types = e.types
        if (e.show_ad) this.showAd = true
        if (e.from_task) {
            this.from_task = true
            this.point_id = Number(e.point_id)
            if (e.seconds) {
                this.timeNum = Number(e.seconds)
                this.time = Number(e.seconds)
            }
        }
        if (e.title) this.$uni.setNavigationBarTitle(e.title)

        this.getImageSet()
    },
    computed: {
        // 计算结果，使用计算函数，可方便管理得分基数
        countResult() {
            if (this.from_task) return this.result
            return this.result * 10;
        }
    },
    onUnload() {
        // 页面关闭移除计时器，否则app平台有BUG
        clearTimeout(this.aTime);
        clearInterval(this.bTime);
        clearInterval(this.cTime);
    },
    methods: {
        getImageSet() {
            this.getOpenerEventChannel().once('imageSet', set => {
                const {bg_img, mouse, boom, hole, navigation_bar, button} = set || {}
                if (bg_img) this.setPageBackgroundStyle(bg_img)
                if (mouse) this.imageSource.mouse = mouse
                if (boom) this.imageSource.boom = boom
                if (hole) this.imageSource.hole = hole

                if (navigation_bar) {
                    const {font_color = '#ffffff', bg_color = '#45454d'} = navigation_bar
                    uni.setNavigationBarColor({
                        frontColor: font_color,
                        backgroundColor: bg_color
                    })
                }

                if (button) {
                    const {font_color = '#2d8cf0', bg_color = '#ffffff'} = button
                    this.startButtonStyle = `background-color: ${bg_color}; color: ${font_color};`
                }
            })
        },

        setPageBackgroundStyle(bg_img) {
            this.pageBackgroundStyle = `background-image:url(${bg_img}); background-position: center; background-size: cover; background-repeat: no-repeat;`
        },



        // 锤打
        whack(i) {
            if (this[`mouseHideTimeout_${i}`]) clearTimeout(this[`mouseHideTimeout_${i}`])

            // 非老鼠显示状态则结束事件
            if (this.list[i] !== 1) {
                // 这里可以添加打到不是地鼠扣分
                // if (this.result > 0) this.result--
                return;
            }

            if (!this.flag) {
                uni.showToast({
                    title: '时间已结束',
                    icon: 'none'
                })
                return;
            }

            this.$set(this.list, i, 2);
            this.result++;

            // 创建匿名函数，使当前序号拥有独立作用域（减少BUG产生）
            ((j) => {
                this.aTime = setTimeout(() => {
                    this.$set(this.list, j, 0);
                }, 800);
            })(i)
        },

        clearGameInterval() {
            if (!this.bTime) return
            clearInterval(this.bTime)
            this.bTime = null
        },

        // 显示老鼠
        showMouse() {
            // 获取有效的洞穴,避免重复渲染
            let effective = () => this.list.filter(item => item === 0).map((item, index) => index)

            this.bTime = setInterval(() => {
                if (!this.flag) return

                const effectiveArr = effective();

                // 当可用洞穴数小于2个时不再显示更多的老鼠
                if (effectiveArr.length < 2) return;

                // 随机获得一个可用的洞穴序号
                let r = Math.floor(Math.random() * effectiveArr.length);

                // 将可用的洞穴显示老鼠
                this.$set(this.list, effectiveArr[r], 1);

                // 0.5秒是地鼠出来的动画时长
                // 地鼠出现随机1.5秒-5.5秒消失，去掉出来的0.5秒动画时间就是1-5秒
                let randomNumber = Math.ceil(Math.random() * 5) + .5
                this[`mouseHideTimeout_${effectiveArr[r]}`] = setTimeout(() => {
                    if (this.list[effectiveArr[r]] === 1) {
                        // 这里可以添加漏打地鼠扣分
                        // if (this.result > 0) this.result--
                        this.$set(this.list, effectiveArr[r], 0);
                    }
                }, 1000 * randomNumber)
            }, 500);  // 这个时间是每个地鼠出现的时间间隔，可以自定义配置
        },
        // 重新开始
        restart() {
            this.list = [0, 0, 0, 0, 0, 0, 0, 0, 0];
            this.result = 0;
            this.downTime();
            this.flag = true;

            this.showMouse()
        },
        // 倒计时
        downTime() {
            this.cTime = setInterval(() => {
                if (this.time > 0) {
                    this.time--;
                } else {
                    this.clearGameInterval()
                    if (this.flag) {
                        this.flag = false;
                        this.time = this.timeNum;
                        clearInterval(this.cTime);
                        showPop();
                    }
                }
            }, 1000);

            let showPop = () => {
                this.list = [0, 0, 0, 0, 0, 0, 0, 0, 0] //数值说明 0:无状态,1:老鼠显示,2:砸中状态

                // uni.showModal({
                // 	title: "游戏结束",
                // 	content: `得分：${this.countResult}`,
                // 	cancelText: "不玩了",
                // 	confirmText: "重新挑战",
                // 	success: (e) => {
                // 		if (!e.confirm) uni.navigateBack();
                // 	}
                // })

                this.from_task ? this.taskSubmit() : this.submit()
            }
        },

        async submit() {
            uni.showLoading({
                title: '游戏结束，提交中...',
                mask: true
            })
            const res = await this.xwy_api.gameSubmit(this.id, this.types, this.countResult)
            uni.hideLoading()

            this.$refs.resultPopup.open({
                code: res?.status || 0,
                integral: null,
                info: res?.info || ''
            })
        },


        async taskSubmit() {
            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify({
                        types: 16,
                        count: this.result,
                        point_id: this.point_id,
                        result: 'success'
                    }))
                }
            })
            uni.hideLoading()


            const resultCode = res?.status || 0
            let info = resultCode === 1 ? '任务完成' : '任务失败'
            if (res?.status !== 1) {
                if (res?.info) info = res.info
            }

            this.$refs.resultPopup.open({
                code: res?.status || 0,
                integral: res.data.num || null,
                info: info
            })


            this.getOpenerEventChannel().emit('success')
        }
    }
}
</script>

<style lang="scss">
/* 原来插件的global.scss，删除了无用样式后移到页面里来了 */
/* 列式弹性盒子 */
.flex_col {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
    align-content: center;
}

/* 弹性盒子允许换行 */
.flex_col.flex_wrap {
    flex-wrap: wrap;
}

/* 弹性盒子快速分栏 ,这里非常郁闷 uniapp 居然不支持 * 选择器 */
.flex_col.flex_col_3 > view {
    width: 33.33333%;
}

/* 原来插件的global.scss，删除了无用样式后移到页面里来了 */


.page {
    min-height: 100vh;
    padding-bottom: 300rpx; /* 底部广告位置 */
    box-sizing: border-box;
    background-color: #45454d;
    position: relative;
}

.top-msg-container {
    padding: 15px 3vw 10px calc(3vw + 15px);

    .top-msg {
        position: relative;
        display: flex;
        flex-direction: row;

        .top-msg-icon {
            position: absolute;
            left: -15px;
            top: -3px;
            border-radius: 50%;
            padding: 3px;

            .top-msg-inner-ring {
                width: 30px;
                height: 30px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 50%;

                .iconfont {
                    color: #fff;
                    font-size: 22px;
                }
            }
        }

        .top-msg-num {
            background-color: rgba(0, 0, 0, .3);
            color: #fff;
            height: 30px;
            border-radius: 0 15px 15px 0;
            padding: 0 15px 0 26px;
            min-width: 60px;
            font-size: 18px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .top-msg-left {
        .top-msg-icon {
            background-color: #5cadff;
            box-shadow: 0 0 5px #2d8cf0;

            .top-msg-inner-ring {
                background-color: #2d8cf0;
            }
        }

        .top-msg-num {
            box-shadow: 0 0 10px rgba(45, 140, 240, .2) inset;
        }
    }

    .top-msg-right {
        .top-msg-icon {
            background-color: #fce230;
            box-shadow: 0 0 5px #ffb400;

            .top-msg-inner-ring {
                background-color: #ffb400;
            }
        }

        .top-msg-num {
            box-shadow: 0 0 10px rgba(255, 180, 0, .2) inset;
        }
    }
}


.box {
    .item {
        height: 250rpx;
        position: relative;

        > image {
            position: absolute;

            &.hole {
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
                z-index: 1;
            }

            &.mouse {
                width: 66%;
                height: 66%;
                top: 17%;
                left: 17%;
                z-index: 2;
                display: none;
            }

            &.mouse-show {
                display: block;
                animation: grow .5s linear;
            }

            @keyframes grow {
                from {
                    transform: scale(0);
                }
                to {
                    transform: scale(1);
                }
            }

            &.boom {
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
                z-index: 3;
            }
        }
    }
}

.start-btn {
    width: 250px;
    line-height: 44px;
    border-radius: 22px;
}

.bottom-ad {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
}
</style>
