<template>
    <view class="page bg-background">
        <view v-if="!list_load && list.length" class="bg-white clearfix clear">
            <view class="p10 color-primary fr" @click="addGarbage(null)">
                <text>添加垃圾</text>
                <uni-icons type="forward" color="#2d8cf0"/>
            </view>
        </view>

        <view class="list">
            <view class="item bg-white" v-for="item in list" :key="item.id">
                <view class="flex-row">
                    <image class="logo" v-if="item.logo" :src="item.logo" mode="aspectFill"/>
                    <view>
                        <view class="color-title">垃圾名称: {{ item.name }}</view>
                        <view class="color-content" v-if="item.category_id">
                            垃圾分类: {{ item.category_name || item.category_id }}
                        </view>
                    </view>
                </view>
                <view class="tools-bar clear clearfix">
                    <view class="flex-row fr">
                        <view @click="addGarbage(item)" class="edit">
                            <text class="iconfont icon-edit color-sub font20"></text>
                        </view>
                        <view class="delete" @click="deleteGarbage(item)">
                            <text class="iconfont icon-delete color-sub font20"></text>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <view v-if="list_load" class="text-center">
            <view v-if="load_page === 1" style="width: 100%; height: 40vh;"></view>
            <load-ani/>
            <view v-if="load_page === 1" class="font14 color-sub">加载中</view>
        </view>

        <view v-if="!list_load && !list.length" class="text-center" style="padding-top: 20vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">未添加垃圾</view>
            <view class="flex-all-center pt15">
                <view class="add-button color-white bg-primary" @click="addGarbage(null)">添加垃圾</view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            list: [],
            load_page: 1,
            is_last_page: false,
            list_load: true
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.getDustbin()
    },

    onPullDownRefresh() {
        this.load_page = 1
        this.getList().finally(() => uni.stopPullDownRefresh())
    },

    onReachBottom() {
        if (!this.is_last_page && !this.list_load) {
            this.load_page++
            this.getList()
        }
    },

    methods: {
        async getDustbin() {
            const res = await this.xwy_api.getCategoryList({
                active_id: this.active_id,
                types: 37,
                page: this.load_page,
                perpage: 1000,
                my_self: 1
            })

            const list = res?.data?.category_list?.data || []
            this.dustbins = list.map(item => ({
                id: item.category_id,
                name: item.name
            }))

            await this.getList()
        },

        async getList() {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
            }

            this.list_load = true
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.game_center.rubbishKnowledge/knowledge_list',
                data: {
                    active_id: this.active_id,
                    page: this.load_page,
                    perpage: 20
                }
            })
            this.list_load = false

            const data = res?.data?.list
            if (!data) {
                this.is_last_page = true
                return
            }

            const list = (data.data || []).map(item => ({
                id: item.id,
                name: item.name,
                logo: item.logo,
                category_id: item.category_id,
                category_name: this.dustbins.find(dustbin => dustbin.id === item.category_id)?.name || ''
            }))
            this.list = [...this.list, ...list]
            this.is_last_page = data.is_lastpage
        },

        addGarbage(item = null) {
            let url = `./add?active_id=${this.active_id}`
            if (item) url += `&id=${item.id}`

            this.$uni.navigateTo(url, {
                success: res => {
                    res.eventChannel.emit('details', item)
                },
                events: {
                    updateList: () => {
                        this.load_page = 1
                        this.getList()
                    }
                }
            })
        },

        async deleteGarbage(item) {
            const confirm = await this.$uni.showModal(`确定删除 ${item.name}?`, {showCancel: true})
            if (!confirm.confirm) return

            this.$uni.showLoading('删除中...')
            const res = await this.xwy_api.deleteRecords(111, item.id)
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '删除失败，请重试！')

            this.$uni.showToast('已删除')
            this.load_page = 1
            await this.getList()
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
}

.list {
    padding-top: 1px;
}

.item {
    padding: 10px;
    border-bottom: 1px solid #eee;
    margin: 10px;
    border-radius: 10px;

    $logo-size: 40px;

    .logo {
        width: $logo-size;
        min-width: $logo-size;
        height: $logo-size;
        border-radius: 50%;
        display: block;
        margin-right: 10px;
    }

    .tools-bar {
        border-top: 1px solid #eee;
        padding-top: 10px;
        margin-top: 10px;

        .edit, .delete {
            width: 32px;
            height: 32px;
            line-height: 32px;
            text-align: center;
            border-radius: 50%;
        }

        .edit {
            border: 1px solid #eee;
            margin-right: 10px;
        }

        .delete {
            border: 1px solid #eee;
        }
    }
}


.add-button {
    width: 200px;
    line-height: 44px;
    border-radius: 22px;
}
</style>