<template>
    <view>
        <view class="flex-all-center pt10">

            <image v-if="logo" class="logo" :src="logo" mode="aspectFill"/>
            <view v-else class="logo flex-all-center bg-background">
                <uni-icons type="image" size="60" color="#c6cbe0"/>
            </view>
        </view>
        <view class="flex-all-center">
            <view class="p10" @click="changeLogo" hover-class="navigator-hover">
                <text class="color-primary">{{ logo ? '更换' : '上传' }}logo</text>
                <uni-icons type="forward" color="#2d8cf0"/>
            </view>
        </view>

        <view class="form">
            <view class="form-item">
                <view class="top color-content">
                    <text>垃圾名称</text>
                    <text class="color-error font16"> *</text>
                </view>
                <view class="bottom font16">
                    <input class="input" type="text" v-model="name" placeholder="请输入名称"/>
                </view>
            </view>


            <view class="form-item">
                <view class="top color-content">
                    <text>垃圾分类</text>
                    <text class="color-error font16"> *</text>
                </view>
                <view class="bottom font16 flex-kai" @click="changeCategory">
                    <view class="view">
                        <view v-if="category_id">{{ category_name || category_id }}</view>
                        <view v-else class="color-sub">选择垃圾分类</view>
                    </view>
                    <view class="flex-all-center">
                        <uni-icons type="forward" color="#80848f"/>
                    </view>
                </view>
            </view>
        </view>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view class="save-button color-white text-center font18 bg-primary" @click="save">保存</view>
        </view>

    </view>
</template>

<script>
export default {
    data() {
        return {
            name: '',
            logo: '',
            category_id: '',
            category_name: ''
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        if (params.id) {
            this.id = params.id
            this.getGarbageDetails()
        }
    },

    methods: {
        getGarbageDetails() {
            this.getOpenerEventChannel().once('details', details => {
                this.name = details.name
                this.logo = details.logo
                this.category_id = details.category_id
                if (details.category_name) this.category_name = details.category_name
            })
        },

        changeLogo() {
            this.$uni.navigateTo('/pages/other/image_upload_or_select', {
                events: {
                    newImg: src => {
                        this.logo = src
                    }
                }
            })
        },

        changeCategory() {
            this.$uni.navigateTo(`/pages/category/list?active_id=${this.active_id}&just_look_active=1&types=37&is_sel=1`, {
                events: {
                    selCategory: data => {
                        this.category_id = data.id
                        this.category_name = data.name
                    }
                }
            })
        },

        async save() {
            if (!this.name) return this.$uni.showToast('请输入垃圾名称')
            if (!this.category_id) return this.$uni.showToast('请选择垃圾分类')
            if (!this.logo) return this.$uni.showToast('请上传垃圾logo图片')

            const data = {
                active_id: this.active_id,
                name: this.name,
                category_id: this.category_id,
                logo: this.logo
            }
            if (this.id) data.id = this.id

            this.$uni.showLoading('保存中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.game_center.rubbishKnowledge/create_knowledge',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '保存失败，请重试！')

            this.$uni.showToast('已保存')
            this.getOpenerEventChannel().emit('updateList')
            this.$uni.navigateBack(1, {delay: 1000})
        }
    }
}
</script>

<style lang="scss">
.logo {
    width: 100px;
    height: 100px;
    border-radius: 50%;
}

.form {
    padding: 10px 0;

    .form-item {
        padding: 10px;

        .bottom {
            border-bottom: 1px solid #eee;

            .input {
                width: 100%;
                line-height: 40px;
                height: 40px;
            }

            .view {
                padding: 8px 0;
                width: 100%;
            }
        }

    }
}


.bottom-btn-view {
    position: fixed;
    z-index: 99999;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;

    .save-button {
        width: 250px;
        height: 44px;
        line-height: 44px;
        border-radius: 22px;
    }
}
</style>