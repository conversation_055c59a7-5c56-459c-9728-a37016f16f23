<template>
    <view class="page" :style="{backgroundImage: `url(${bg_img})`}">
        <uni-popup ref="gameTips" :is-mask-click="false">
            <view class="game-tips bg-white">
                <view class="game-tips-title">游戏说明</view>
                <view class="game-tips-content color-content">
                    <view>左上角为倒计时，右上角为分数。</view>
                    <view>请将上方的垃圾拖动到下方的垃圾桶进行分类。</view>
                    <template>
                        <view v-if="integral">
                            {{ time }}秒内将{{ garbage_count }}个垃圾全部分类正确可获得{{ integral }}积分。
                        </view>
                        <template v-else>
                            <view>每分类正确一个垃圾奖励{{ per_integral }}积分。</view>
                            <view>游戏时长{{ time }}秒。</view>
                            <view>倒计时结束或{{ garbage_count }}个垃圾分类完成计算成绩。</view>
                        </template>
                    </template>
                </view>

                <view class="p10">
                    <view class="start-game-button" @click="gameStart">开始分类</view>
                </view>
            </view>

            <view v-if="showAd" class="flex-all-center pt5">
                <xwy-ad :ad_type="3"></xwy-ad>
                <xwy-ad :ad_type="66"></xwy-ad>
            </view>
        </uni-popup>

        <view class="top-msg-container clearfix clear">
            <view class="top-msg top-msg-left fl">
                <view class="top-msg-icon">
                    <view class="top-msg-inner-ring">
                        <text class="iconfont icon-alarm-clock"></text>
                    </view>
                </view>
                <view class="top-msg-num">{{ time }}</view>
            </view>

            <view class="top-msg top-msg-right fr">
                <view class="top-msg-icon">
                    <view class="top-msg-inner-ring">
                        <text class="iconfont icon-grid"></text>
                    </view>
                </view>
                <view class="top-msg-num">{{ score }}</view>
            </view>
        </view>

        <movable-area class="movable-area" :style="{height: `calc(100vh - ${dustbinHeight / 2}px)`}">

            <movable-view v-if="showGarbageIndex >= 0 && showGarbageIndex < garbageList.length && !game_over"
                          class="movable-view" direction="all" :animation="false"
                          :x="garbageList[showGarbageIndex].x" :y="garbageList[showGarbageIndex].y"
                          :style="garbageSizeStyle"
                          @change="garbageMove" @touchstart="touchStart" @touchend="touchEnd">

                <image class="garbage-image" :src="garbageList[showGarbageIndex].logo"
                       :style="garbageSizeStyle" mode="aspectFit"/>
            </movable-view>
        </movable-area>
        <view class="dustbins flex-row">
            <view class="dustbin" v-for="item in dustbins" :key="item.id">
                <image class="dustbin-image" :src="item.logo" mode="aspectFit"
                       :style="{width: dustbinWidth + 'px', height: dustbinHeight + 'px'}"/>
            </view>
        </view>

        <game-result-popup ref="resultPopup" :show-ad="showAd"/>
    </view>
</template>

<script>
// 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
/** @namespace bg_img */

const {windowWidth, windowHeight} = uni.getSystemInfoSync()
import Voice from './voice'

export default {
    data() {
        return {
            seconds: 30,
            time: 30,
            showAd: false,
            dustbins: [],
            garbage_count: 0,
            garbageList: [],
            showGarbageIndex: -1,
            score: 0,
            integral: 0,
            per_integral: 0,
            bg_img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/red-small-games/garbage-sorting/bg.png',
            dustbinWidth: 0,
            dustbinHeight: 0,
            game_over: false
        }
    },

    computed: {
        garbageSize() {
            return Math.min(this.dustbinWidth, this.dustbinHeight) - 20
        },
        garbageSizeStyle() {
            return `width: ${this.garbageSize}px; height: ${this.garbageSize}px;`
        }
    },

    onLoad() {
        this.getGameData()
    },

    onUnload() {
        this.clearCountdownInterval()
        this.clearGarbageDownInterval()
        this.clearNextGarbageTimeout()
        this.voice && this.voice.destroy()
    },

    methods: {
        getGameData() {
            this.getOpenerEventChannel().once('data', data => {
                this.active_id = data.id
                this.point_id = Number(data.point_id)
                this.garbage_count = Number(data.garbage_count)
                if (data.integral) this.integral = Number(data.integral)
                if (data.per_integral) this.per_integral = Number(data.per_integral)
                if (data.seconds) {
                    this.seconds = Number(data.seconds)
                    this.time = Number(data.seconds)
                }
                if (data.title) this.$uni.setNavigationBarTitle(data.title)
                if (data.show_ad) this.showAd = true
                if (data.bg_img) this.bg_img = data.bg_img
                if (data.navigation_bar) uni.setNavigationBarColor({
                    ...data.navigation_bar,
                    fail: err => console.log(err)
                })

                this.init()
            })
        },

        async init() {
            await this.getDustbin()
            await this.getGarbage()
            this.getShowGarbage(true)
            this.voice = new Voice()

            this.$refs.gameTips.open()
        },


        gameStart() {
            this.$refs.gameTips.close()
            this.countdownInterval = setInterval(() => {
                this.time--
                if (this.time <= 0) this.gameOver()
            }, 1000)
            this.garbageDownIntervalRun()
        },

        clearCountdownInterval() {
            clearInterval(this.countdownInterval)
            this.countdownInterval = null
        },

        gameOver() {
            this.game_over = true
            this.clearCountdownInterval()
            if (this.inSubmit) return
            this.taskSubmit()
        },

        async getDustbin() {
            const res = await this.xwy_api.getCategoryList({
                active_id: this.active_id,
                types: 37,
                page: 1,
                perpage: 1000
            })


            const list = res?.data?.category_list?.data || []
            const width = windowWidth / list.length
            const dustbinHeight = await this.getDustbinHeight(list, width)
            this.dustbinWidth = width
            this.dustbinHeight = dustbinHeight
            this.dustbins = list.map((item, index) => ({
                id: item.category_id,
                name: item.name,
                logo: item.logo,
                y: windowHeight - dustbinHeight,
                x: index * width
            }))
        },

        async getDustbinHeight(list, dustbinWidth) {
            if (!list?.length) return 0

            const info = await this.$uni.getImageInfo(list[0].logo)
            const {width, height} = info
            const scale = dustbinWidth / width
            return height * scale
        },


        async getGarbage() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.game_center.rubbishKnowledge/knowledge_list',
                data: {
                    active_id: this.active_id,
                    changeRand: 1,  // 【1】答题的时候需要随机获取，每次随机不一样的，
                    page: 1,
                    perpage: this.garbage_count
                }
            })

            const list = res?.data?.list?.data || []

            if (!list.length) return this.$uni.showModal('游戏配置获取失败，请检查活动垃圾分类游戏设置', {
                success: () => this.$uni.navigateBack()
            })

            if (list.length < this.garbage_count) this.garbage_count = list.length

            const size = 60
            this.garbageList = list.map(item => ({
                id: item.id,
                name: item.name,
                logo: item.logo,
                category_id: item.category_id,
                x: windowWidth / 2 - size / 2,
                y: 120
            }))
        },

        getShowGarbage(init = false) {
            const list = this.garbageList
            this.showGarbageIndex++

            if (this.showGarbageIndex >= list.length) return this.gameOver()

            this.$nextTick(() => {
                if (!init && !this.game_over) this.garbageDownIntervalRun()
            })
        },

        garbageDownIntervalRun() {
            this.clearGarbageDownInterval()
            this.garbageDownInterval = setInterval(() => {
                this.garbageList[this.showGarbageIndex].y++
                if (this.toBottom(this.garbageList[this.showGarbageIndex].y)) {
                    this.clearGarbageDownInterval()
                    this.moveEnd()
                }
            }, 30)
        },

        clearGarbageDownInterval() {
            clearInterval(this.garbageDownInterval)
            this.garbageDownInterval = null
        },

        touchStart() {
            this.clearGarbageDownInterval()
        },

        touchEnd() {
            this.garbageDownIntervalRun()
        },

        garbageMove(e) {
            const {x, y} = e.detail
            this.garbageList[this.showGarbageIndex].x = x
            this.garbageList[this.showGarbageIndex].y = y
        },

        toBottom(y) {
            // 垃圾桶的高度, 增加1像素的容错空间，避免计算垃圾桶图片高度的时候有小数导致精确有误
            const dustbin2bottom = this.dustbinHeight / 2 + 1
            return y >= windowHeight - dustbin2bottom - this.garbageSize
        },

        moveEnd() {

            const item = this.garbageList[this.showGarbageIndex]
            const {x, y, category_id, name} = item

            const center = this.getMoveCenterPoint(x, y)
            const dustbin = this.findDustbin(center)

            if (category_id === dustbin.id) {
                // 拖对垃圾桶，加分
                this.score++
                this.voice.rightPlay()
            } else {
                this.voice.wrongPlay()
                const dustbinName = this.dustbins.find(item => item.id === category_id)?.name
                this.$uni.showToast(`${name} ${dustbinName ? '' : '不'}是 ${dustbinName || dustbin.name}`)
            }

            this.clearNextGarbageTimeout()
            this.nextGarbageTimeout = setTimeout(() => this.getShowGarbage(), 1000)
        },

        clearNextGarbageTimeout() {
            if (!this.nextGarbageTimeout) return
            clearTimeout(this.nextGarbageTimeout)
            this.nextGarbageTimeout = null
        },

        // 获取图片移动后的中心点
        getMoveCenterPoint(x, y) {
            const size = this.garbageSize
            return {
                x: x + size / 2,
                y: y + size / 2
            }
        },

        // 获取垃圾拖动到哪个垃圾桶
        findDustbin({x, y}) {
            // 给垃圾桶加上1像素的容错空间，避免计算图片高度的时候有小数导致精确有误
            const bottomY = y + this.garbageSize / 2 + 1
            return this.dustbins.find(item => {
                return x >= item.x && x <= item.x + this.dustbinWidth && bottomY >= item.y
            }) || null
        },

        async taskSubmit() {
            this.inSubmit = true

            const sign = {
                types: 18,
                point_id: this.point_id
            }
            if (this.integral) {
                // 全部答对才能奖励
                sign.result = this.score === this.garbage_count ? 'success' : 'fail'
            } else {
                // 每答对一题奖励，一个每答对是任务失败
                sign.count = this.score
                sign.result = this.score ? 'success' : 'fail'
            }

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify(sign))
                }
            })
            // 加定时器是因为有时候接口返回的太快了，立即hideLoading会导致无法隐藏loading
            setTimeout(() => uni.hideLoading(), 300)


            let resultCode = res?.status || 0
            if (sign.result === 'fail') resultCode = 0
            let info = resultCode === 1 ? '任务完成' : '任务失败'
            if (res?.status !== 1) {
                if (res?.info) info = res.info
            }

            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })

            this.getOpenerEventChannel().emit('success')
        }
    }
}
</script>

<style lang="scss">
.page {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}

.game-tips {
    width: 90vw;
    border-radius: 10px;

    .game-tips-title {
        line-height: 44px;
        text-align: center;
    }

    .game-tips-content {
        padding: 0 20px;
        line-height: 30px;
    }

    .start-game-button {
        width: 150px;
        line-height: 40px;
        border-radius: 20px;
        text-align: center;
        font-size: 18px;
        background-color: #ff985e;
        color: #fff;
        margin: 10px auto;
    }
}

.top-msg-container {
    position: fixed;
    z-index: 1;
    top: 0;
    left: 0;
    width: 100vw;
    padding: 15px 3vw 10px calc(3vw + 15px);
    box-sizing: border-box;

    .top-msg {
        position: relative;
        display: flex;
        flex-direction: row;

        .top-msg-icon {
            position: absolute;
            left: -15px;
            top: -3px;
            border-radius: 50%;
            padding: 3px;

            .top-msg-inner-ring {
                width: 30px;
                height: 30px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 50%;

                .iconfont {
                    color: #fff;
                    font-size: 22px;
                }
            }
        }

        .top-msg-num {
            background-color: rgba(0, 0, 0, .3);
            color: #fff;
            height: 30px;
            border-radius: 0 15px 15px 0;
            padding: 0 15px 0 26px;
            min-width: 60px;
            font-size: 18px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .top-msg-left {
        .top-msg-icon {
            background-color: #5cadff;
            box-shadow: 0 0 5px #2d8cf0;

            .top-msg-inner-ring {
                background-color: #2d8cf0;
            }
        }

        .top-msg-num {
            box-shadow: 0 0 10px rgba(45, 140, 240, .2) inset;
        }
    }

    .top-msg-right {
        .top-msg-icon {
            background-color: #fce230;
            box-shadow: 0 0 5px #ffb400;

            .top-msg-inner-ring {
                background-color: #ffb400;
            }
        }

        .top-msg-num {
            box-shadow: 0 0 10px rgba(255, 180, 0, .2) inset;
        }
    }
}

.movable-area {
    width: 100vw;

    .garbage-image {
        display: block;
    }
}

.dustbins {
    position: fixed;
    z-index: 1;
    left: 0;
    bottom: 0;

    .dustbin-image {
        display: block;
    }
}
</style>