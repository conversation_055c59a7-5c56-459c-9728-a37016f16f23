export default class Voice {
    constructor() {
        this.Audio = uni.createInnerAudioContext()
        this.Audio.autoplay = true
        this.rightVoice = 'http://www.xinweiyun.com/weixin/public/img/right.wav'
        this.wrongVoice = 'http://www.xinweiyun.com/weixin/public/img/wrong.wav'
    }

    // 公共播放方法，接收声音类型作为参数
    playSound(type) {
        const sound = type === 'right' ? this.rightVoice : this.wrongVoice
        
        // 播放前停止是为了避免上一次音频没有播放完毕，导致这次音频不播放
        this.Audio.stop()
        this.Audio.src = sound
        this.Audio.play()
        
        // 错误处理
        this.Audio.onError(err => {
            console.error('播放音频失败：', err)
        })
    }
    
    rightPlay() {
        this.playSound('right')
    }
    
    wrongPlay() {
        this.playSound('wrong')
    }
    
    destroy() {
        this.Audio.stop()
        this.Audio.destroy()
    }
}