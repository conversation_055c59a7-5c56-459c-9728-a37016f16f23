<template>
    <view class="content color333" :style="{'background-image': `url(${bgImg})`}">

        <view class="top-msg-container clearfix clear">
            <view class="top-msg top-msg-left fl">
                <view class="top-msg-icon">
                    <view class="top-msg-inner-ring">
                        <text class="iconfont icon-alarm-clock"></text>
                    </view>
                </view>
                <view class="top-msg-num">{{ countdown }}</view>
            </view>

            <view class="top-msg top-msg-right fr">
                <view class="top-msg-icon">
                    <view class="top-msg-inner-ring">
                        <text class="iconfont icon-line-chart"></text>
                    </view>
                </view>
                <view class="top-msg-num">
                    <text>{{ snakes.length }}/{{ pass_count }}</text>
                    <text class="font12">米</text>
                </view>
            </view>
        </view>


        <view class="f-w" :style="{height: `${usableH}px`, 'padding-bottom': `${blockPaddingBottom}px`}"
              @touchstart="touchStart" @touchend="touchEnd">
            <view class="block" :style="{'background-image': bg(x, i), transform: `rotate(${calcRotate(x,i)}deg)`,
				width: `${blockW}px`, height: `${blockW}px`}" v-for="(x, i) in blocks" :key="i">
            </view>
        </view>

        <view v-show="!started" class="game-board-wrap f-j-a-c">
            <view class="width80 radius_5 bgfff p1">
                <view class="font1_1 fontweight text-center pb_3">玩法说明</view>
                <view class="pt10 pb10">
                    <view>左上角为倒计时，右上角为长度。</view>
                    <view>上下左右滑动屏幕控制的方向。</view>
                    <view>倒计时结束或游戏结束后, 长度达到{{ pass_count }}米即可获得奖励。</view>
                </view>
                <view class="f-j-a-c pt1">
                    <view class="width60 text-center p5_0 blue_ramp radius10" @click.stop="start">
                        开始游戏
                    </view>
                </view>
            </view>
        </view>

        <game-result-popup ref="resultPopup" :show-ad="showAd"/>
    </view>
</template>

<script>
import config from "./config.js"

const initBoomCount = 20 // 爆炸冲倒计时时间(s)
export default {
    data() {
        return {
            images: this.xwyLib.copyObj(config.images),
            audioes: this.xwyLib.copyObj(config.audioes),

            level: 1, // 游戏难度
            // levelList: this.xwyLib.copyAry(config.levelList),

            bgImg: config.images.bgImg,
            usableH: 0, // 可用区域的高度
            blockW: 0, // 每格格子的宽和高
            blockRow: 15, // 每行的格子数量
            blockMax: 300, // 格子数量
            blockPaddingBottom: 0, // 底部间隔

            blocks: [], // 格子列表
            worms: [],
            wormsNum: 3, // 最多几个虫子 1个爆炸虫子+剩余的普通虫子
            snakes: [],
            direction: "right",
            timer: null,
            boom: false, // 是否生成会爆炸的虫子
            boomCount: initBoomCount, // 爆炸虫爆炸倒计时
            pollutes: [], // 被爆炸虫子污染的地方 碰到就游戏结束
            started: false, // 游戏开始了

            bgmInnerAudioContext: null,
            clockInnerAudioContext: null,

            startPoint: {
                x: 0,
                y: 0,
            },
            endPoint: {
                x: 0,
                y: 0,
            },
            countdown: 0,
            pass_count: 0,
            showAd: false
        }
    },
    onLoad(params) {
        this.active_id = params.active_id
        this.point_id = params.point_id
        this.pass_count = Number(params.count)
        if (params.level) this.level = Number(params.level)
        if (params.show_ad) this.showAd = true
        if (params.seconds) {
            const seconds = Number(params.seconds)
            this.all_time = seconds
            this.countdown = seconds
        }
        this.init()
    },
    onHide() {
        this.endGame()
    },
    onUnload() {
        this.endGame()
    },
    watch: {
        boomCount(val) {
            if (val === 0) {
                // 超过爆炸时间还没吃到,则将虫子格子变成被污染的土地,并且重置爆炸状态,同时生成一只新的虫子:
                this.handleExplodeVoice()
                this.clockInnerAudioContext.stop()
                const index = this.worms.findIndex(v => v.types === 2)
                if (index >= 0) {
                    const value = this.worms[index].value
                    this.worms.splice(index, 1)
                    this.pollutes.push(value)
                    this.blocks[value] = 3 // 被污染的地方我们用3表示
                    this.boom = false
                    this.worms.push(this.createWorm())
                }
            }
        }
    },
    methods: {
        async init() {
            await this.getBlockArea().catch(() => {})
            await this.initGame()
        },

        async getBlockArea() {
            const systemInfo = uni.getWindowInfo()
            const windowW = systemInfo.windowWidth,
                windowH = systemInfo.windowHeight
            this.blockW = windowW / this.blockRow
            /** 注释
             *提议人: 雷子
             *修改内容: 标题用绝对定位，虫子被遮住也不用管
             *修改时间: 2025-01-07 11:39:53
             */
            this.usableH = windowH
            // await this.getUsableH(windowH).catch(e => {})
            this.blockMax = Math.floor(this.usableH / this.blockW) * this.blockRow
            this.blockPaddingBottom = this.usableH % this.blockW
            return false
        },


        initWormsValue() {
            const list = [],
                usableList = this.blocks.map((v, i) => i).filter(v => this.snakes.indexOf(v) < 0)
            while (list.length < this.wormsNum) {
                const index = Math.floor(Math.random() * usableList.length)
                let randomNumber = usableList[index]
                if (!list.includes(randomNumber)) list.push(randomNumber)
            }
            return list
        },

        async initGame() {
            return new Promise(s => {
                this.blocks = new Array(this.blockMax).fill(0)
                const zero = this.blockRow * 3
                this.snakes = [zero, zero + 1, zero + 2, zero + 3]

                const numList = this.initWormsValue()
                this.worms = numList.map((value, index) => ({
                    value,
                    types: index === 0 ? 2 : 1, // 1普通虫子 2爆炸虫子
                }))

                this.pollutes = []
                this.direction = "right"
                this.timer = null
                this.boomTimer = null
                this.paint()
                s(1)
            })
        },


        async start() {
            this.started = true;
            await this.initGame().catch(() => {})

            if (this.worms.some(v => v.types === 2)) {
                this.boom = true
                this.boomCount = initBoomCount
                this.clockInnerAudioContext && this.clockInnerAudioContext.stop()
                this.boomTimer && clearInterval(this.boomTimer)
                this.handleClockVoice()
                this.boomTimer = setInterval(() => this.boomCount--, 1000)
            }
            this.handleClickVoice()
            this.handleBgmVoice()

            const all_time = this.all_time
            const start_time = Date.now()
            this.timer = setInterval(() => {
                if (all_time) {
                    const now_time = Date.now()
                    const time_diff = (now_time - start_time) / 1000
                    const left_time = Math.round(all_time - time_diff)
                    if (left_time !== this.countdown) this.countdown = left_time
                    if (left_time <= 0) return this.gameOver()
                }
                this.toWards(this.direction)
            }, 500 / this.level)
        },

        paint() {
            this.worms.forEach(v => this.$set(this.blocks, v.value, 1))
            this.snakes.forEach((x) => this.$set(this.blocks, x, 2))
        },
        bg(type, index) {
            let bg = ""
            switch (type) {
                case 0: // 地板
                    bg = "unset"
                    break
                case 1: // 虫子
                    const item = this.worms.find(v => v.value === index)
                    bg = item?.types === 1 ? `url(${this.images.worm})` : `url(${this.images.worm_4})`
                    break
                case 2: // 蛇
                    let head = this.snakes[this.snakes.length - 1]
                    let tail = this.snakes[0]
                    if (index === head) {
                        bg = `url(${this.images.snake_head})`
                    } else if (index === tail) {
                        bg = `url(${this.images.snake_tail})`
                    } else {
                        bg = `url(${this.images.snake_body})`
                    }
                    break
                case 3: // 污染的地块
                    bg = `url(${this.images.pollute})`
                    break
            }
            return bg
        },
        calcRotate(type, index) {
            let rotate = 0
            switch (type) {
                case 0: // 地板
                    rotate = 0
                    break
                case 1: // 虫子
                    rotate = 0
                    break
                case 2: // 蛇
                    let length = this.snakes.length
                    let head = this.snakes[length - 1]
                    let tail = this.snakes[0]
                    let tailPre = this.snakes[1]
                    let bodyPre = this.snakes[this.snakes.indexOf(index) + 1]
                    if (index === head) {
                        if (this.direction === "right") {
                            rotate = 90
                        } else if (this.direction === "down") {
                            rotate = 180
                        } else if (this.direction === "left") {
                            rotate = 270
                        } else {
                            rotate = 0
                        }
                    } else if (index === tail) {
                        if (tailPre - 1 === tail) {
                            // 向右走的
                            rotate = 90
                        } else if (tailPre - this.blockRow === tail) {
                            // 向下走的
                            rotate = 180
                        } else if (tailPre + 1 === tail) {
                            // 向左走的
                            rotate = 270
                        } else {
                            // 向上走的
                            rotate = 0
                        }
                    } else {
                        if (bodyPre - 1 === index) {
                            // 向右走的
                            rotate = 90
                        } else if (bodyPre - this.blockRow === index) {
                            // 向下走的
                            rotate = 180
                        } else if (bodyPre + 1 === index) {
                            // 向左走的
                            rotate = 270
                        } else {
                            // 向上走的
                            rotate = 0
                        }
                    }
                    break
            }
            return rotate
        },
        handleEatVoice() {
            const innerAudioContext = uni.createInnerAudioContext()
            innerAudioContext.autoplay = true
            innerAudioContext.src = this.audioes.eat
        },
        handleExplodeVoice() {
            const innerAudioContext = uni.createInnerAudioContext()
            innerAudioContext.autoplay = true
            innerAudioContext.src = this.audioes.explode
        },
        handleBgmVoice() {
            // 背景音乐
            this.bgmInnerAudioContext = uni.createInnerAudioContext()
            this.bgmInnerAudioContext.autoplay = true
            this.bgmInnerAudioContext.src = this.audioes.bgm
            this.bgmInnerAudioContext.loop = true
        },
        handleClickVoice() {
            // 按钮点击的声音
            const innerAudioContext = uni.createInnerAudioContext()
            innerAudioContext.autoplay = true
            innerAudioContext.src = this.audioes.click
        },
        // 爆炸倒计时的声音
        handleClockVoice() {
            this.clockInnerAudioContext = uni.createInnerAudioContext()
            this.clockInnerAudioContext.autoplay = true
            this.clockInnerAudioContext.loop = true
            this.clockInnerAudioContext.src = this.audioes.clock
        },
        // 蛇挂掉了
        handleDieVoice() {
            const innerAudioContext = uni.createInnerAudioContext()
            innerAudioContext.autoplay = true
            innerAudioContext.src = this.audioes.die
        },
        toWards(direction) {
            if (this.snakes.length === this.blockMax) {
                clearInterval(this.timer)
                return
            }
            let head = this.snakes[this.snakes.length - 1]
            let tail = this.snakes[0]
            let next
            switch (direction) {
                case "up":
                    next = head - this.blockRow
                    break
                case "down":
                    next = head + this.blockRow
                    break
                case "left":
                    next = head - 1
                    break
                case "right":
                    next = head + 1
                    break
            }
            let gameover = this.checkGame(direction, next)
            if (gameover) return this.gameOver()
            // 游戏没结束
            this.snakes.push(next)
            let nextType = this.blocks[next]
            this.blocks[next] = 2
            // 如果是空白格
            if (nextType === 0) {
                this.snakes.shift()
            } else {
                // 如果是虫子格
                this.handleEatVoice()
                this.worms = this.worms.filter(v => v.value !== next)
                let nextWorm = this.createWorm()
                this.worms.push(nextWorm)
            }
            this.blocks[tail] = 0
            this.paint()
        },

        gameOver() {
            this.handleDieVoice()
            this.bgmInnerAudioContext.pause()
            this.clockInnerAudioContext && this.clockInnerAudioContext.stop()
            clearInterval(this.timer)
            clearInterval(this.boomTimer)

            this.resultSubmit()
        },

        // 添加一只虫子
        createWorm() {
            let blocks = Array.from({length: this.blockMax}, (v, k) => k)
            // 在不是蛇和被污染的地方生成虫子
            let restBlocks = blocks.filter(x => this.snakes.indexOf(x) < 0 && this.pollutes.indexOf(x) < 0)
            // 不需要不在已有的虫子格子里面
            restBlocks = restBlocks.filter(v => this.worms.some(j => j.value !== v))
            let worm = {
                value: restBlocks[Math.floor(Math.random() * restBlocks.length)]
            }
            const types = this.worms.some(v => v.types === 2) ? 1 : 2 // 如果爆炸虫子没了 则添加爆炸虫子
            worm.types = types
            // 判断新生成的虫子是不是爆炸虫子
            if (types === 2) {
                this.boom = true
                this.boomCount = initBoomCount
                this.clockInnerAudioContext && this.clockInnerAudioContext.stop()
                this.boomTimer && clearInterval(this.boomTimer)
                this.handleClockVoice()
                this.boomTimer = setInterval(() => this.boomCount--, 1000)
            }
            return worm
        },

        touchStart(e) {
            this.startPoint = {
                x: e?.changedTouches[0]?.clientX || e?.touches[0]?.clientX,
                y: e?.changedTouches[0]?.clientY || e?.touches[0]?.clientY
            }
        },

        touchEnd(e) {
            this.endPoint = {
                x: e?.changedTouches[0]?.clientX || e?.touches[0]?.clientX,
                y: e?.changedTouches[0]?.clientY || e?.touches[0]?.clientY
            }

            const heng = this.endPoint.x ? this.endPoint.x - this.startPoint.x : 0
            const shu = this.endPoint.y ? this.endPoint.y - this.startPoint.y : 0

            if (Math.abs(heng) > 5 || Math.abs(shu) > 5) {
                const direction = Math.abs(heng) > Math.abs(shu) ?
                    this.computeDir(1, heng) :
                    this.computeDir(0, shu)

                switch (direction) {
                    case "left":
                        if (this.direction === "right") return
                        break
                    case "right":
                        if (this.direction === "left") return
                        break
                    case "up":
                        if (this.direction === "down") return
                        break
                    case "down":
                        if (this.direction === "up") return
                        break
                    default:
                }
                this.startPoint = {
                    x: 0,
                    y: 0
                }
                this.endPoint = {
                    x: 0,
                    y: 0
                }
                this.direction = direction
            }
        },

        computeDir(heng, num) {
            if (heng) return num > 0 ? "right" : "left"
            return num > 0 ? "down" : "up"
        },

        checkGame(direction, next) {
            let gameover = false
            let isSnake = this.snakes.indexOf(next) > -1
            let isPollute = this.pollutes.indexOf(next) > -1
            // 撞到蛇和被污染的地块游戏结束
            if (isSnake || isPollute) gameover = true
            // 撞到边界游戏结束
            switch (direction) {
                case "up":
                    if (next < 0) gameover = true
                    break
                case "down":
                    if (next >= this.blockMax) gameover = true
                    break
                case "left":
                    if (next % this.blockRow === this.blockRow - 1) gameover = true
                    break
                case "right":
                    if (next % this.blockRow === 0) gameover = true
                    break
            }
            return gameover
        },

        endGame() {
            if (this.bgmInnerAudioContext) {
                this.bgmInnerAudioContext.stop()
                this.bgmInnerAudioContext.destroy()
                this.bgmInnerAudioContext = null
            }
            this.clockInnerAudioContext && this.clockInnerAudioContext.stop()
            this.timer && clearInterval(this.timer)
            this.boomTimer && clearInterval(this.boomTimer)
        },


        async resultSubmit() {
            const code = this.snakes.length >= this.pass_count ? 1 : 0

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify({
                        types: 13,
                        point_id: this.point_id,
                        result: code === 1 ? 'success' : 'fail'
                    }))
                }
            })
            uni.hideLoading()


            let resultCode = code
            let info = resultCode === 1 ? '任务完成' : '任务失败'
            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info = res.info
            }

            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })


            this.getOpenerEventChannel().emit('success')
        }
    },
};
</script>

<style lang="scss">
.content {
    width: 100%;
    max-width: 640px;
    min-height: 100vh;
    background-size: cover;
}

.top-msg-container {
    position: absolute;
    z-index: 2;
    left: 0;
    top: 0;
    width: 100%;
    box-sizing: border-box;
    padding: 15px 3vw 10px calc(3vw + 15px);

    .top-msg {
        position: relative;
        display: flex;
        flex-direction: row;

        .top-msg-icon {
            position: absolute;
            left: -15px;
            top: -3px;
            border-radius: 50%;
            padding: 3px;

            .top-msg-inner-ring {
                width: 30px;
                height: 30px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 50%;

                .iconfont {
                    color: #fff;
                    font-size: 22px;
                }
            }
        }

        .top-msg-num {
            background-color: rgba(0, 0, 0, .3);
            color: #fff;
            height: 30px;
            border-radius: 0 15px 15px 0;
            padding: 0 15px 0 26px;
            min-width: 60px;
            font-size: 18px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .top-msg-left {
        .top-msg-icon {
            background-color: #5cadff;
            box-shadow: 0 0 5px #2d8cf0;

            .top-msg-inner-ring {
                background-color: #2d8cf0;
            }
        }

        .top-msg-num {
            box-shadow: 0 0 10px rgba(45, 140, 240, .2) inset;
        }
    }

    .top-msg-right {
        .top-msg-icon {
            background-color: #fce230;
            box-shadow: 0 0 5px #ffb400;

            .top-msg-inner-ring {
                background-color: #ffb400;
            }
        }

        .top-msg-num {
            box-shadow: 0 0 10px rgba(255, 180, 0, .2) inset;
        }
    }
}


.block {
    display: flex;
    justify-content: center;
    align-items: center;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    box-sizing: border-box;
}

.game-board-wrap {
    position: fixed;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, .5);
    top: 0;
    left: 0;
    z-index: 999;
}


.color333 {
    color: #333;
}

.f-j-sb-a-c {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.p1 {
    padding: 1rem !important;
}

.red-light {
    color: #f56c6c;
}

.f-w {
    display: flex;
    flex-wrap: wrap;
}

.f-j-a-c {
    display: flex;
    justify-content: center;
    align-items: center;
}

.width80 {
    width: 80%;
}

.radius_5 {
    border-radius: .5rem;
}

.bgfff {
    background-color: #fff !important;
}

.font1_1 {
    font-size: 1.1rem !important;
}

.fontweight {
    font-weight: 700;
}

.text-center {
    text-align: center;
}

.pb_3 {
    padding-bottom: .3rem !important;
}

.width50 {
    width: 50% !important;
}

.p7_0 {
    padding: .7rem 0 !important;
}

.pt1 {
    padding-top: 1rem !important;
}

.width60 {
    width: 60%;
}

.p5_0 {
    padding: .5rem 0 !important;
}

.blue_ramp {
    background: linear-gradient(to bottom right, #3b96fa, #0a73fb) !important;
    color: #fff !important;
}

.radius10 {
    border-radius: 10rem;
}

.width40 {
    width: 40%;
}

.info-bg-light {
    color: #909399;
    background-color: #f4f4f5;
}
</style>