<template>
	<view class="page bg-background">
		<view v-if="list_count" class="flex-kai bg-white">
			<view class="color-sub font14 p10">共{{list_count}}条游戏记录</view>
			<navigator open-type="navigateBack" class="color-primary font14 p10">
				<text>去玩游戏</text>
				<uni-icons type="forward" color="#2d8cf0"/>
			</navigator>
		</view>

		<view class="list">
			<view class="item p10 bg-white" v-for="(item, index) in list" :key="index">
				<view class="color-title pb5">{{getGameTitle(item.game_types)}}</view>
				<view class="color-content">获得分数: {{item.conf_json.game_data.score || 0}}</view>
				<view class="color-content">获得{{energy_unit}}: {{item.power_num}}</view>
				<view class="color-sub font14 pt5">{{item.create_time}}</view>
			</view>
		</view>

		<uni-load-more v-if="in_load" status="loading"></uni-load-more>
		<uni-load-more
			v-if="is_lastpage && list.length > 5"
			status="noMore"
			:contentText="{contentnomore: '我是有底线的'}"
		></uni-load-more>
		<uni-load-more v-if="!is_lastpage && !in_load" status="more"></uni-load-more>


		<view v-if="!list.length && !in_load" class="text-center" style="padding-top: 10vh;">
		    <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
		    <view class="color-sub pb10">暂无游戏记录</view>
			<view class="flex-all-center pt10">
				<navigator open-type="navigateBack" class="to-sport-bbtn text-center bg-primary color-white">去玩游戏</navigator>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'


	export default {
		data() {
			return {
				in_load: true,
				list: [],
				load_page: 1,
				is_lastpage: false,
				list_count: 0,
				energy_unit: '能量'
			}
		},
		onLoad(e) {
		    uni.showLoading()
		    this.id = e.id
			if (e.types) this.types = e.types

		    login.uniLogin(err => {
		        uni.hideLoading()
		        if (err && err.errMsg) {
		            uni.showModal({
		                title: err.errTitle || '提示',
		                content: err.errMsg,
		                showCancel: false
		            })
		            return false
		        }

				this.getGameList()


		    })
		},
		onReachBottom() {
			!this.is_lastpage && !this.in_load && this.getList()
		},
		methods: {
			async getGameList() {
				let activity_detail = app.globalData.activity_detail

				if (!activity_detail) {
					const res = await xwy_api.request({
						url: 'front.flat.sport_step.active_list/active_details',
						data: {
							access_token: app.globalData.access_token,
							active_id: this.id
						}
					})


					activity_detail = res?.data?.active_details
				}

				if (activity_detail?.conf?.active?.energy_unit) this.energy_unit = activity_detail.conf.active.energy_unit


				this.game_list = activity_detail?.conf?.active?.game_center?.game_list || []

				this.getList()
			},


			async getList() {
				if (this.load_page === 1) {
					this.list = []
					this.is_lastpage = false
				}

				this.in_load = true

				const data = {
					access_token: app.globalData.access_token,
					active_id: this.id,
					userid: app.globalData.userid,
					page: this.load_page,
					perpage: 20
				}
				if (this.types) data.types = this.types

				const res = await xwy_api.request({
					url: 'front.flat.sport_step.game_center.userGameCenter/submit_game_records',
					data
				})

				this.in_load = false

				const res_data = res?.data?.game_records
				if (!res_data) {
					this.is_lastpage = true
					return false
				}

				this.load_page++
				const list = res_data.data || []
				this.list = [...this.list, ...list]
				this.is_lastpage = res_data.is_lastpage
				this.list_count = res_data.total
			},

			getGameTitle(types) {
				const types_data = this.game_list.find(v => v.types === types)
				return types_data?.title || types
			},
		}
	}
</script>

<style>
.page {
	min-height: 100vh;
}
.list {
	padding: 10px 0;
}
.item {
	margin: 10px;
	border-radius: 10px;
}
.to-sport-bbtn {
	width: 200px;
	line-height: 40px;
	border-radius: 20px;
}
</style>
