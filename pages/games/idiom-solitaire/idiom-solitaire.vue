<template>
    <view class="page" :style="{backgroundImage: `url(${images.bg})`}">
        <uni-popup ref="gameTips" :is-mask-click="false">
            <view class="game-tips">
                <view class="game-tips-title">游戏说明</view>
                <view class="game-tips-content color-content">
                    <view>左上角为倒计时，右上角为接龙的成语数。</view>
                    <view>每接龙一个成语奖励{{ per_integral }}积分。</view>
                    <view>接龙的成语数量超过{{ maxSubmitCount }}不继续奖励。</view>
                    <view>游戏时长{{ time }}秒。</view>
                    <view>倒计时结束或接龙到龙尾，游戏自动结束并计算成绩。</view>
                </view>

                <view class="p10">
                    <view class="start-game-button" @click="gameStart">开始接龙</view>
                </view>
            </view>

            <view v-if="showAd" class="flex-all-center pt5">
                <xwy-ad :ad_type="3"></xwy-ad>
                <xwy-ad :ad_type="66"></xwy-ad>
            </view>
        </uni-popup>

        <view class="top-msg-container clearfix clear">
            <view class="top-msg top-msg-left fl">
                <view class="top-msg-icon">
                    <view class="top-msg-inner-ring">
                        <text class="iconfont icon-alarm-clock"></text>
                    </view>
                </view>
                <view class="top-msg-num">{{ timeText }}</view>
            </view>

            <view class="top-msg top-msg-right fr">
                <view class="top-msg-icon">
                    <view class="top-msg-inner-ring">
                        <text class="iconfont icon-grid"></text>
                    </view>
                </view>
                <view class="top-msg-num">{{ countResult }}</view>
            </view>
        </view>


        <uni-popup ref="userSayPopup">
            <view style="padding-bottom: 10vh;">
                <view class="game-tips">
                    <view class="game-tips-title">接龙</view>
                    <view class="game-tips-content color-content">
                        <view>
                            请输入一个以【{{aiIdiomLastWord}}】开头
                            <template v-if="unisonOpen">或与{{aiIdiomLastWord}}同音字开头</template>
                            的四字成语
                        </view>
                        <view class="pt10 flex-kai">
                            <view class="w-100">
                                <uni-easyinput v-model="user_say" trim :focus="userSayInputFocus"
                                           @confirm="userSayConfirm"/>
                            </view>
                            <view v-if="idiomTipsOpen" style="line-height: 36px;" @click="idiomTips"
                                  :class="['font14 pl10 no-wrap', idiomTipsCount > 0 ? 'color-light-primary' : 'color-sub']">
                                提示({{ idiomTipsCount }})
                            </view>
                        </view>
                    </view>

                    <view class="p10">
                        <view class="start-game-button" @click="userSayConfirm">确定</view>
                        <view class="flex-all-center p10">
                            <view class="font14 color-sub" @click="manualEnd">结束游戏</view>
                        </view>
                    </view>
                </view>

                <view v-if="showAd" class="flex-all-center pt5">
                    <xwy-ad :ad_type="3"></xwy-ad>
                    <xwy-ad :ad_type="66"></xwy-ad>
                </view>
            </view>
        </uni-popup>

        <view class="idiom-loong-container flex-all-center">
            <view class="idiom-loong">
                <view class="loong-head">

                    <image :src="images.head" mode="aspectFit"/>
                </view>

                <view v-for="(item, index) in loongLength" :key="item"
                      :class="['loong-body loong-body-' + index, columnBody.includes(index) ? 'column-body' : '', index === list.length ? 'current-reply' : '']"
                      @click="userSayPopupShow">

                    <image class="loong-body-image" :src="images.body" mode="scaleToFill"/>
                    <view v-if="list[index]" class="idiom-word">
                        <view v-for="(word, i) in list[index].idiom" :key="i">{{ word }}</view>
                    </view>
                </view>

                <view class="loong-tail">

                    <image :src="images.tail" mode="aspectFit"/>
                </view>
            </view>
        </view>

        <uni-popup ref="endGame">
            <view class="end-game-popup bg-white text-center">
                <view class="p10 font18 color-title">结束游戏</view>
                <view class="p10 color-content">确定结束游戏？</view>
                <view class="p10">
                    <view class="start-game-button" @click="gameOver">确定</view>
                    <view class="flex-all-center p10">
                        <view class="font14 color-sub" @click="continueGame">继续游戏</view>
                    </view>
                </view>
            </view>
        </uni-popup>

        <game-result-popup ref="resultPopup" :show-ad="showAd"/>
    </view>
</template>

<script>
import api from './api'
import {pinyin} from 'pinyin-pro'

import xwy_config from '@/config/config'
const image_base_url = `${xwy_config.object_storage_url}img/jbz_xcx/red-small-games/idiom-loong/`

export default {
    data() {
        return {
            list: [{role: 'ai', idiom: api.getFirstIdioms()}],
            user_say: '',
            images: {
                bg: `${image_base_url}bg.png`,
                head: `${image_base_url}head.png`,
                tail: `${image_base_url}tail.png`,
                body: `${image_base_url}body.png`
            },
            // 竖向的成语下标,龙体图片需要翻转90度
            columnBody: [2, 6, 10, 14],
            showAd: false,
            time: 30,
            maxSubmitCount: 0,
            per_integral: 0,
            game_over: false,
            loongLength: 17,
            inReply: false,
            userSayInputFocus: false,
            unisonOpen: false,
            idiomTipsOpen: false,
            idiomTipsCount: 0
        }
    },

    watch: {
        user_say() {
            this.aiTips = false
        }
    },

    computed: {
        aiIdiomLastWord() {
            const list = this.list.filter(item => item.role === 'ai')
            return list[list.length - 1]?.idiom?.[3] || ''
        },

        countResult() {
            return this.list.filter(item => item.role === 'user').length
        },

        timeText() {
            if (!this.time) return '00:00'
            let mm = 0, ss = this.time
            if (ss >= 60) {
                mm = Math.floor(ss / 60)
                ss = ss % 60
            }
            return `${mm < 10 ? '0' + mm : mm}:${ss < 10 ? '0' + ss : ss}`
        }
    },

    onLoad(params) {
        this.id = params.id
        if (params.show_ad) this.showAd = true
        if (params.unison) this.unisonOpen = true
        if (params.idiom_tips_count) {
            this.idiomTipsOpen = true
            this.idiomTipsCount = Math.floor(params.idiom_tips_count)
        }
        this.point_id = Number(params.point_id)
        this.per_integral = Number(params.per_integral)
        this.time = Number(params.seconds)
        if (params.max_count) this.maxSubmitCount = Number(params.max_count)
        if (params.title) this.$uni.setNavigationBarTitle(params.title)

        this.$refs.gameTips.open()
    },

    onUnload() {
        this.clearCountdownInterval()
    },

    methods: {
        gameStart() {
            this.$refs.gameTips.close()
            setTimeout(() => {
                this.userSayPopupShow()
                this.countdownStart()
            }, 1000)
        },

        countdownStart() {
            this.countdownInterval = setInterval(() => {
                this.time--
                if (this.time <=0) {
                    this.time = 0
                    this.timeout()
                }
            }, 1000)
        },

        clearCountdownInterval() {
            if (!this.countdownInterval) return
            clearInterval(this.countdownInterval)
            this.countdownInterval = null
        },

        userSayPopupOpen() {
            this.userSayInputFocus = true
            this.$refs.userSayPopup.open()
        },

        userSayPopupClose() {
            this.userSayInputFocus = false
            this.$refs.userSayPopup.close()
        },

        userSayPopupShow() {
            if (this.game_over) return
            if (this.inReply) return
            this.userSayPopupOpen()
        },

        async idiomTips() {
            if (this.idiomTipsCount <= 0) return this.$uni.showToast('提示次数已用完')

            const res = await this.$uni.showModal(`还剩${this.idiomTipsCount}次提示次数，是否使用?`, {
                showCancel: true,
                confirmText: '使用'
            })
            if (!res.confirm) return

            this.$uni.showLoading()
            const idiom = await this.useIdioms(this.list[this.list.length - 1].idiom)
            uni.hideLoading()
            if (!idiom) return

            this.idiomTipsCount--
            this.user_say = idiom
            this.$nextTick(() => {
                this.aiTips = true
            })
        },

        userSayConfirm() {
            const value = this.user_say

            if (this.aiTips) {
                this.$refs.userSayPopup.close()

                this.push2list({role: 'user', idiom: value})
                this.user_say = ''

                return this.nextAiIdiomShow(value)
            }

            if (value.length !== 4) return this.$uni.showToast('请输入四字成语')
            if (!this.sameWordCheck(value)) {
                let tips = `需要以${this.aiIdiomLastWord}开头`
                if (this.unisonOpen) tips += `或与${this.aiIdiomLastWord}同音开头`
                tips += '的四字成语'
                return this.$uni.showToast(tips)
            }
            if (this.repeatCheck(value)) return this.$uni.showToast(`${value} 出现过了`)

            this.userSayCheck()
        },

        sameWordCheck(value) {
            if (value[0] === this.aiIdiomLastWord) return true

            if (this.unisonOpen) {
                const list = this.list.filter(item => item.role === 'ai')
                const lastIdiom = list[list.length - 1].idiom
                const ai = pinyin(lastIdiom, { type: 'array' })
                const user = pinyin(value, { type: 'array' })
                if (ai[ai.length - 1] === user[0]) return true
            }

            return false
        },

        repeatCheck(idiom) {
            return this.list.map(item => item.idiom).includes(idiom)
        },

        push2list(data) {
            this.inReply = true
            this.list.push({role: data.role, idiom: ''})
            const last = this.list.length - 1
            let index = 0
            const interval = setInterval(() => {
                this.list[last].idiom += data.idiom[index]
                index++
                if (index >= 4) {
                    this.inReply = false
                    if (this.list.length === this.loongLength) this.gameOver()
                    clearInterval(interval)
                }
            }, 100)
        },

        async userSayCheck() {
            const userIdiom = this.user_say

            // return await this.AICheck(userIdiom)


            if (!await this.isIdiomCheck(userIdiom)) return this.$uni.showToast(`${userIdiom} 不是一个成语`)

            this.userSayPopupClose()


            this.push2list({role: 'user', idiom: userIdiom})
            // this.list.push({role: 'user', idiom: userIdiom})
            this.user_say = ''

            await this.nextAiIdiomShow(userIdiom)
            // this.list.push({role: 'ai', idiom: newIdiom})
        },

        async nextAiIdiomShow(userIdiom) {
            const newIdiom = await this.useIdioms(userIdiom)

            setTimeout(() => {
                if (this.game_over) return
                this.push2list({role: 'ai', idiom: newIdiom})
                setTimeout(() => {
                    if (this.game_over) return
                    this.userSayPopupShow()
                }, 1000)
            }, 1000)
        },

        async AICheck(idiom) {
            const allIdioms = this.list.map(item => item.idiom).join('、')
            const res = await api.aiSend(`你是一个成语大师，我们现在正在玩一个成语接龙的游戏，请你判断这个词语是否是成语，如果是请返回true，并返回这个成语最后一个字的接龙的成语，接龙的成语不能与下列成语重复：${allIdioms}。如果不是成语，就返回false。请直接返回json格式。{"is_idiom":true,"nex_word":"接龙的成语"}，要求你判断的词语是：${idiom}`)

            let result = {is_idiom: false, nex_word: ''}
            try {
                result = JSON.parse(res)
            } catch (error) {
                console.error(error)
            }

            if (!result.is_idiom) return this.$uni.showToast(`${idiom} 不是一个成语`)
            this.push2list({role: 'user', idiom})
            this.user_say = ''

            if (!result.nex_word) return this.$uni.showModal('发生错误')

            this.push2list({role: 'ai', idiom: result.nex_word})
        },

        async isIdiomCheck(text) {
            if (await api.isIdiomCheck(text)) {
                console.log('配置里查到的 ', text)
                return true
            }

            const res = await api.aiSend(`"${text}"是不是一个成语？判断条件不用那么苛刻，只要像成语也行。你只需要回答"是"或"不是"`)

            return !res.includes('不是')
        },

        async useIdioms(idiom, again_count = 0) {
            if (!again_count) {
                const randomIdioms = await api.getRandomWordIdioms(idiom, this.list.map(item => item.idiom))
                if (randomIdioms) {
                    console.log('配置里出的 ', randomIdioms)
                    return randomIdioms
                }
            }


            if (again_count >= 10) return this.$uni.showModal('出现错误了')

            const word = idiom[idiom.length - 1]
            const res = await api.aiSend(`以"${word}"开头给我出一个常用的四字成语。必须是以"${word}"开头！！！只需要一个成语，不要返回其他内容，标点符号也不要。不能和以下的成语重复：${this.list.map(item => item.idiom).join('、')}。${again_count ? Date.now() : ''}`)

            if (!res) return this.useIdioms(idiom, again_count + 1)
            return res.slice(0, 4)
        },

        manualEnd() {
            this.userSayPopupClose()
            this.$refs.endGame.open()
        },

        continueGame() {
            this.$refs.endGame.close()
            this.userSayPopupShow()
        },


        timeout() {
            this.clearCountdownInterval()
            this.gameOver()
        },

        gameOver() {
            this.game_over = true
            this.userSayPopupClose()
            this.$refs.endGame.close()
            this.submitResult()
        },

        async submitResult() {
            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify({
                        types: 15,
                        count: this.countResult,
                        point_id: this.point_id,
                        result: 'success'
                    }))
                }
            })
            uni.hideLoading()


            const resultCode = res?.status || 0
            let info = resultCode === 1 ? '任务完成' : '任务失败'
            if (res?.status !== 1) {
                if (res?.info) info = res.info
            }

            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })


            this.getOpenerEventChannel().emit('success')
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}

$popup-bgi: "https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/task_challenge/style1/sty1.png";
$popup-title-bgi: "https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/task_challenge/style1/sty2.png";

.game-tips {
    width: 90vw;
    position: relative;
    background-image: url(#{$popup-bgi});
    background-size: 100% 100%;
    background-repeat: no-repeat;

    .game-tips-title {
        position: relative;
        top: -22px;
        left: 50%;
        margin-left: -90px;
        width: 180px;
        height: 44px;
        line-height: 44px;
        text-align: center;
        color: #fff;
        background-image: url(#{$popup-title-bgi});
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }

    .game-tips-content {
        padding: 0 20px;
        line-height: 30px;

        image {
            height: 30px;
            vertical-align: text-bottom;
        }
    }

    .start-game-button {
        width: 150px;
        line-height: 40px;
        border-radius: 20px;
        text-align: center;
        font-size: 18px;
        background-color: #ff985e;
        color: #fff;
        margin: 10px auto;
    }
}

.top-msg-container {
    padding: 10px 10px 10px 25px;

    .top-msg {
        position: relative;
        display: flex;
        flex-direction: row;

        .top-msg-icon {
            position: absolute;
            left: -15px;
            top: -3px;
            border-radius: 50%;
            padding: 3px;

            .top-msg-inner-ring {
                width: 30px;
                height: 30px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 50%;

                .iconfont {
                    color: #fff;
                    font-size: 22px;
                }
            }
        }

        .top-msg-num {
            background-color: rgba(0, 0, 0, .3);
            color: #fff;
            height: 30px;
            border-radius: 0 15px 15px 0;
            padding: 0 15px 0 26px;
            min-width: 60px;
            font-size: 18px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .top-msg-left {
        .top-msg-icon {
            background-color: #5cadff;
            box-shadow: 0 0 5px #2d8cf0;

            .top-msg-inner-ring {
                background-color: #2d8cf0;
            }
        }

        .top-msg-num {
            box-shadow: 0 0 10px rgba(45, 140, 240, .2) inset;
        }
    }

    .top-msg-right {
        .top-msg-icon {
            background-color: #fce230;
            box-shadow: 0 0 5px #ffb400;

            .top-msg-inner-ring {
                background-color: #ffb400;
            }
        }

        .top-msg-num {
            box-shadow: 0 0 10px rgba(255, 180, 0, .2) inset;
        }
    }
}

.end-game-popup {
    width: 80vw;
    border-radius: 10px;
    padding: 10px;

    .start-game-button {
        width: 150px;
        line-height: 40px;
        border-radius: 20px;
        text-align: center;
        font-size: 18px;
        background-color: #5cadff;
        color: #fff;
        margin: 10px auto;
    }
}

.idiom-loong-container {
    padding-top: 1px;
    height: calc(100vh - 50px);
    box-sizing: border-box;
}

$width: 200rpx;
$height: 80rpx;

.idiom-loong {
    position: relative;
    margin: calc(#{$width} / 2 - #{$height} / 2) auto;
    width: calc(#{$width} * 3 + #{$height});
    height: calc(#{$width} * 4 + #{$height});
}

.loong-head, .loong-tail {
    position: absolute;

    image {
        width: $width;
        height: $width;
        display: block;
    }
}

.loong-head {
    top: -($width * .5 - $height * .5);
    left: 0;
}

.loong-tail {
    left: $width * 2;
    bottom: -($width * .5 - $height * .5);
}

.loong-body {
    position: absolute;

    .loong-body-image {
        position: absolute;
        z-index: 1;
        top: 0;
        left: 0;
        width: $width;
        height: $height;
        display: block;
    }

    .idiom-word {
        width: 100%;
        height: 100%;
        position: absolute;
        z-index: 2;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
        color: #a64b12;
    }
}

.column-body {
    .loong-body-image {
        transform: rotate(90deg) translateY($width * .5 - $height * .5) translateX($width * .5 - $height * .5);
    }

    .idiom-word {
        position: absolute;
        z-index: 2;
        flex-direction: column;
    }
}

.current-reply {
    animation: zoom .5s linear alternate infinite;
}
@keyframes zoom {
    from {
        transform: scale(1);
    }
    to {
        transform: scale(1.1);
    }
}

.loong-body-0 {
    width: $width;
    height: $height;
    top: 0;
    left: $width;
}
.loong-body-1 {
    width: $width;
    height: $height;
    top: 0;
    left: $width * 2;
}
.loong-body-2 {
    top: 0;
    left: $width * 3;
    width: $height;
    height: $width;
}
.loong-body-3 {
    width: $width;
    height: $height;
    top: $width;
    left: $width * 2 + $height;

    .idiom-word {
        flex-direction: row-reverse;
    }
}
.loong-body-4 {
    width: $width;
    height: $height;
    top: $width;
    left: $width + $height;

    .idiom-word {
        flex-direction: row-reverse;
    }
}
.loong-body-5 {
    width: $width;
    height: $height;
    top: $width;
    left: $height;

    .idiom-word {
        flex-direction: row-reverse;
    }
}
.loong-body-6 {
    width: $height;
    height: $width;
    top: $width;
    left: 0;
}
.loong-body-7 {
    flex-direction: row;
    width: $width;
    height: $height;
    top: $width * 2;
    left: 0;
}
.loong-body-8 {
    flex-direction: row;
    width: $width;
    height: $height;
    top: $width * 2;
    left: $width;
}
.loong-body-9 {
    flex-direction: row;
    width: $width;
    height: $height;
    top: $width * 2;
    left: $width * 2;
}
.loong-body-10 {
    flex-direction: column;
    width: $height;
    height: $width;
    top: $width * 2;
    left: $width * 3;
}
.loong-body-11 {
    width: $width;
    height: $height;
    top: $width * 3;
    left: $width * 2 + $height;

    .idiom-word {
        flex-direction: row-reverse;
    }
}
.loong-body-12 {
    width: $width;
    height: $height;
    top: $width * 3;
    left: $width + $height;

    .idiom-word {
        flex-direction: row-reverse;
    }
}
.loong-body-13 {
    width: $width;
    height: $height;
    top: $width * 3;
    left: $height;

    .idiom-word {
        flex-direction: row-reverse;
    }
}
.loong-body-14 {
    width: $height;
    height: $width;
    top: $width * 3;
    left: 0;
}
.loong-body-15 {
    width: $width;
    height: $height;
    top: $width * 4;
    left: 0;
}
.loong-body-16 {
    width: $width;
    height: $height;
    top: $width * 4;
    left: $width;
}
</style>