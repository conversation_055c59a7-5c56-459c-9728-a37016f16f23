import xwyApi from '@/utils/api/xwy_api'
import {first, idioms} from './idioms'

export default {
    async aiSend(content) {
        const res = await xwyApi.request({
            url: 'front.flat.active.chat.user.chat/system_product_result',
            data: {
                subject: content,
                system_product: 'ai_idiom_chain',
                used_company: 'doubao'
            }
        })
        
        return res?.data?.res?.content?.[0] || ''
    },
    
    getFirstIdioms: () => first[Math.floor(Math.random() * first.length)],
    
    async getIdiomsList() {
        let backgroundConfig = this.idiomsBackgroundConfig
        if (!backgroundConfig) {
            const res = await xwyApi.request({
                url: 'front.system.system_conf/json_conf_set',
                data: {
                    name: 'idiom_list_set'
                }
            })
            backgroundConfig = res?.data?.conf_set?.['words_list'] || []
            this.idiomsBackgroundConfig = backgroundConfig
        }
        
        return [...new Set([...first, ...idioms, ...backgroundConfig])]
    },
    
    
    async getRandomWordIdioms(word, repeatIdioms = []) {
        word = word[word.length - 1]
        let list = await this.getIdiomsList()
        list = list.filter(item => item.startsWith(word))
        if (repeatIdioms.length) list = list.filter(item => !repeatIdioms.includes(item))
        if (!list.length) return ''
        return list[Math.floor(Math.random() * list.length)]
    },
    
    async isIdiomCheck(idiom) {
        const list = await this.getIdiomsList()
        return list.includes(idiom)
    }
}