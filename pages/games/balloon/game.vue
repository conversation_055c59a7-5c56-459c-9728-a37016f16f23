<template>
    <view class="page" :style="{ 'background-image': `url(${images.bgImg})` }">
        <game-top-msg-bar right-icon="icon-balloon">
            <template v-slot:leftMsg>{{ countdown }}</template>
            <template v-slot:rightMsg>{{ score }}</template>
        </game-top-msg-bar>

        <view class="content relative f-j-a-c">
            <!-- 游戏区域 -->
            <view class="width100 f-d-c-j-a-c pt5" v-if="gameStatus === 1 || gameStatus === 2">
                <!-- 气球 -->
                <view class="balloon-container f-j-a-c relative">
                    <view v-if="showBalloon" class="balloon f"
                          :class="{ exploding: isExploding }"
                          :style="balloonStyle">
                        <image :src="images.qqImg" mode="widthFix"/>
                    </view>
                    <!-- 爆炸效果 -->
                    <view class="explosion f" v-if="isExploding">
                        <image :src="images.boomImg" mode="widthFix"/>
                    </view>

                    <!-- 打气筒 -->
                    <view class="inflator f" @click.stop="pumpBalloon">
                        <image :style="{ opacity: !isPump ? 1 : 0 }" :src="images.inflatorImg"
                               mode="widthFix"/>
                        <image :style="{ opacity: isPump ? 1 : 0 }" :src="images.inflator1Img"
                               mode="widthFix"/>
                    </view>
                </view>

                <!-- 充气进度 -->
                <view class="vertical-progress">
                    <view class="progress-track">
                        <view class="progress-fill"
                              :style="{ height: `${(pumpCount / maxPumpCount) * 100}%` }"></view>
                    </view>
                    <text class="progress-label">{{ Math.floor((pumpCount / maxPumpCount) * 100) }}%</text>
                </view>
            </view>

        </view>

        <game-tips-popup ref="gameTips" :tips-list="tipsList" :show-ad="showAD" @startGame="startGame"/>

        <game-result-popup ref="resultPopup" :show-ad="showAD" :unit="unit"/>
    </view>
</template>

<script>
import gameTopMsgBar from '../components/game-top-msg-bar.vue'
import gameTipsPopup from '../components/game-tips-popup.vue'

const BASE_URL = 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/game/balloon/'

export default {
    components: {gameTopMsgBar, gameTipsPopup},
    data() {
        return {
            images: {
                bgImg: `${BASE_URL}bg.jpg`,
                inflatorImg: `${BASE_URL}inflator.png`,
                inflator1Img: `${BASE_URL}inflator1.png`,
                boomImg: `${BASE_URL}boom.png`,
                qqImg: `${BASE_URL}qq.png`,
            },
            seconds: 30, // 默认时间
            countdown: 30, // 剩余时间

            gameStatus: 0, // 0: 未开始, 1: 游戏中, 2: 已结束
            score: 0, // 爆炸的气球数量
            pumpCount: 0, // 当前打气次数
            maxPumpCount: 5, // 气球爆炸需要的充气次数
            showBalloon: true,
            isExploding: false,
            isPump: false,

            per_integral: 0,
            unit: '积分',
            showAD: false,
        }
    },
    computed: {
        tipsList() {
            return [
                `每充气${this.maxPumpCount}次，气球会爆炸`,
                `在${this.seconds}秒内打爆尽可能多的气球`,
                `倒计时结束后，每打爆1个气球奖励${this.per_integral}${this.unit}`
            ]
        },

        // 动态计算气球的缩放样式
        balloonStyle() {
            if (this.pumpCount === 0) {
                // 初始状态，不缩放
                return `transform: scale(1); transformOrigin: bottom; transition: all 0.2s ease`
            }

            // 根据当前充气次数和最大充气次数动态计算缩放比例
            // 基础缩放从1.0开始，每次充气增加一定比例
            // 最终缩放到2.5倍（当达到maxPumpCount时）
            const baseScale = 1.0
            const maxScale = 2.5
            const scaleIncrement = (maxScale - baseScale) / this.maxPumpCount
            const currentScale = baseScale + (scaleIncrement * this.pumpCount)

            return `transform: scale(${currentScale.toFixed(2)}); transformOrigin: bottom; transition: all 0.2s ease`
        }
    },

    onUnload() {
        this.clearCountdownInterval()
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.point_id = params.point_id
        this.unit = params.unit || '积分'
        this.per_integral = Number(params.per_integral) || 0
        this.seconds = Number(params.seconds) || 30
        this.countdown = this.seconds
        if (params.show_ad) this.showAD = true
        if (params.title) this.$uni.setNavigationBarTitle(params.title)

        this.getGameData()
        this.$refs.gameTips.open()
    },


    methods: {
        async getGameData() {
            const data = await this.getOpenerEventChannelData()

            if (data.bg_img) this.images.bgImg = data.bg_img
            if (data.ball_img) this.images.qqImg = data.ball_img
            if (data.boom_img) this.images.boomImg = data.boom_img
            if (data.boom_count) this.maxPumpCount = data.boom_count

            if (data.navigation_bar) uni.setNavigationBarColor({
                ...data.navigation_bar,
                fail: err => console.log(err)
            })
        },

        getOpenerEventChannelData() {
            return new Promise(resolve => this.getOpenerEventChannel().once('data', data => resolve(data)))
        },

        // 开始游戏
        startGame() {
            if (this.gameStatus !== 0) return

            this.$refs.gameTips.close()
            this.gameStatus = 1
            this.countdown = this.seconds
            this.score = 0
            this.resetBalloon()
            this.startCountdown()
        },

        // 开始倒计时
        startCountdown() {
            this.countdownInterval = setInterval(() => {
                this.countdown--
                if (this.countdown <= 0) this.endGame()
            }, 1000)
        },

        clearCountdownInterval() {
            if (this.countdownInterval) {
                clearInterval(this.countdownInterval)
                this.countdownInterval = null
            }
        },

        // 结束游戏
        endGame() {
            this.gameStatus = 2
            this.clearCountdownInterval()

            this.submitResult()
        },

        // 重置气球
        resetBalloon() {
            this.pumpCount = 0
            this.showBalloon = true
            this.isExploding = false
        },

        // 打气筒点击
        pumpBalloon() {
            if (this.gameStatus !== 1 || this.isExploding || this.isPump) return
            this.isPump = true
            setTimeout(() => (this.isPump = false), 200)

            this.pumpCount++
            if (this.pumpCount >= this.maxPumpCount) this.explodeBalloon()
        },

        // 气球爆炸
        explodeBalloon() {
            this.isExploding = true
            this.score++

            // 爆炸动画后重置气球
            setTimeout(() => {
                this.showBalloon = false
                setTimeout(() => this.resetBalloon(), 200)
            }, 200)
        },

        async submitResult() {
            if (this.isSubmit) return

            this.isSubmit = true

            const count = this.score
            const sign = {
                types: 41,
                point_id: this.point_id,
                result: count ? 'success' : 'fail',
                count
            }

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify(sign))
                }
            })
            uni.hideLoading()

            let resultCode = sign.result === 'success' ? 1 : 0
            let info = resultCode === 1 ? '任务完成' : '任务失败'
            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info = res.info
            }

            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })


            this.getOpenerEventChannel().emit('success')
        }
    }
}
</script>

<style scoped lang="scss">
// 基础变量
$bg-dark: rgba(0, 0, 0, 0.6);
$white: #fff;
$border-radius: 20px;

.page {
    height: 100vh;
    background-size: cover;
    background-repeat: no-repeat;
}

.content {
    width: 100%;
    height: calc(100vh - 55px);
    overflow: hidden;
    background-size: cover;
    background-repeat: no-repeat;
}

.balloon-container {
    .balloon {
        position: absolute;
        left: 3.6%;
        bottom: 83%;
        transform-origin: bottom;
        transition: all 0.2s ease;
        z-index: 1;
        width: 90px;

        > image {
            width: 90px;
            height: 90px;
        }

        &.exploding {
            animation: explode 0.2s ease-out forwards;
        }
    }

    .explosion {
        position: absolute;
        left: -20px;
        bottom: 100%;
        animation: explosion 0.2s ease-out;
        z-index: 2;
        width: 150px;
    }

    .inflator {
        position: relative;
        width: 200px;
        height: 200px;

        > image {
            width: 200px;
            height: 200px;
            position: absolute;
            left: 0;
            top: 0;
        }
    }
}

@keyframes explode {
    0% {
        transform: scale(2.2);
        opacity: 1;
    }
    50% {
        transform: scale(2.5);
        opacity: 0.5;
    }
    100% {
        transform: scale(0);
        opacity: 0;
    }
}

@keyframes explosion {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

// 竖直进度条
.vertical-progress {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 15;

    .progress-track {
        width: 12px;
        height: 200px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 6px;
        overflow: hidden;
        position: relative;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

        .progress-fill {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            background: linear-gradient(to top, #dc143c, #ff6b6b);
            border-radius: 6px;
            transition: height 0.1s ease-out;
        }
    }

    .progress-label {
        margin-top: 10px;
        color: $white;
        font-size: 14px;
        font-weight: bold;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        text-align: center;
        background: $bg-dark;
        padding: 4px 8px;
        border-radius: 8px;
        min-width: 40px;
        width: 55px;
    }
}

.relative {
    position: relative;
}

.f-j-a-c {
    display: flex;
    justify-content: center;
    align-items: center;
}

.width100 {
    width: 100%;
}

.f-j-sb-a-c {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.p1 {
    padding: 1rem !important;
}

.pr_2 {
    padding-right: 0.2rem !important;
}

.red-light {
    color: #f56c6c;
}

.color333 {
    color: #333 !important;
}

.f-d-c-j-a-c {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.pt5 {
    padding-top: 5rem !important;
}

.f {
    display: flex;
}
</style>
