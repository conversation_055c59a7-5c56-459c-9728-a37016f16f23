<template>
    <view v-if="bgPath" class="page">
        <view class="top-tips text-center color-sub font14" style="height: 40px; line-height: 40px;">
            提示: 单指拖动图片，双指缩放图片。
        </view>

        <view class="movable-area-container flex-all-center"
              :style="{width: maxWidth + 'px', height: maxHeight + 'px'}">
            <view v-show="bgWidth && bgHeight">
                <view class="bg-view" :style="bgViewStyle"></view>
                <movable-area class="movable-area" scale-area="true" :style="movableAreaStyle">

                    <movable-view class="movable-view" v-if="userPath" direction="all" :out-of-bounds="true"
                                  :x="userPathData.x" :y="userPathData.y" scale="true"
                                  :style="userImageStyle">

                        <image class="image" :src="userPath" :style="userImageStyle"/>
                    </movable-view>
                </movable-area>
            </view>
        </view>


        <scroll-view class="image-list-scroll-view" scroll-x="true">
            <view class="image-list flex-row">
                <view class="image-item flex-all-center" :class="{'active-image': item === bgImage}"
                      v-for="(item, index) in imageList" :key="index">
                    <image class="image-item-image" :src="item" mode="aspectFill" @click="changeBg(item)"/>
                </view>
            </view>
        </scroll-view>


        <view class="flex-all-center">
			<button v-if="userPath" class="bottom-button bg-error color-white" hover-class="navigator-hover"
                    @click="done">
                完成并发布
            </button>
            <button v-else class="bottom-button bg-error color-white" hover-class="navigator-hover"
                    @click="changeUserImage">
                上传图片
            </button>
		</view>

		<view v-if="userPath" class="flex-all-center">
            <view class="font14 color-sub p10" @click="changeUserImage">更换图片</view>
        </view>

        <canvas class="canvas" id="canvas" canvas-id="canvas" :style="movableAreaStyle"/>

        <uni-popup ref="submitPopup">
            <view class="submit-popup bg-white">
                <view class="content">
                    <uni-easyinput type="textarea" v-model="content" maxlength="500"
                                   placeholder="写点什么吧... (请勿输入表情)"/>
                </view>
                <view class="image-path">

                    <image class="image-path-src" :src="completePath" mode="aspectFill" @click="lookPath"/>
                </view>
                <view class="flex-all-center">
                    <view class="submit-button bg-light-primary color-white text-center" @click="submit">
                        发布
                    </view>
                </view>
                <view class="flex-all-center">
                    <view class="color-sub font14 p10" @click="$refs.submitPopup.close()">关闭</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
const {windowWidth, windowHeight, pixelRatio} = uni.getWindowInfo()

export default {
    data() {
        return {
            maxWidth: windowWidth - 20,
            // 200: 顶部提示、图片列表、按钮    60: 安卓系统会吧系统的虚拟按键也算到屏幕高度里面，要去掉，大概选60像素吧
            maxHeight: windowHeight - 240 - 60,
            bgWidth: 0,
            bgHeight: 0,
            bgImage: '',
            bgPath: '',
            userPath: '',
            userPathData: {x: 0, y: 0, width: 0, height: 0},
            content: '',
            completePath: '',
            imageList: []
        }
    },

    computed: {
        movableAreaStyle() {
            return `width: ${this.bgWidth}px; height: ${this.bgHeight}px;`
        },
        userImageStyle() {
            return `width: ${this.userPathData.width}px; height: ${this.userPathData.height}px;`
        },
        bgViewStyle() {
            const {movableAreaStyle, bgImage, maxWidth, maxHeight, bgWidth, bgHeight} = this
            return `${movableAreaStyle} background-image: url(${bgImage}); left: ${(maxWidth - bgWidth) / 2}px; top: ${(maxHeight - bgHeight) / 2}px;`
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.point_id = Number(params.point_id)
        if (params.title) this.$uni.setNavigationBarTitle(params.title)

        this.$login.uniLogin(() => this.init())
    },

    methods: {
        async init() {
            await this.getImageList()
            await this.getBgInfo()
        },

        async getImageList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/link_game_system_set',
                data: {
                    active_id: this.active_id
                }
            })

            const list = res?.data?.system_set?.['ai_pic_together']
            if (!list?.length) return this.$uni.showModal('活动未配置图片', {success: () => uni.navigateBack()})

            this.imageList = list
            this.bgImage = list[0]
        },

        async changeBg(src) {
            if (src === this.bgImage) return
            this.bgWidth = 0
            this.bgHeight = 0
            this.bgImage = src
            await this.getBgInfo()
            if (this.userPath) await this.setImageData(this.userPath)
        },

        async getBgInfo() {
            const info = await this.$uni.getImageInfo(this.bgImage)
            console.log(info);
            if (!info || info === 'error') {
                const tips = '活动图片加载失败，请联系活动管理员检查图片'
                return this.$uni.showModal(tips, {success: () => uni.navigateBack()})
            }
            const {path, width, height} = info

            let bgWidth = this.maxWidth
            let bgHeight = height * (this.maxWidth / width)
            if (bgHeight > this.maxHeight) {
                bgHeight = this.maxHeight
                bgWidth = width * (this.maxHeight / height)
            }

            this.bgPath = path
            this.bgWidth = bgWidth
            this.bgHeight = bgHeight
        },

        async changeUserImage() {
            const path = await this.chooseImage()
            if (!path) return
            await this.setImageData(path)
        },

        chooseImage() {
            return new Promise(resolve => {
                uni.chooseImage({
                    count: 1,
                    success: res => resolve(res.tempFilePaths[0]),
                    fail: () => resolve(null)
                })
            })
        },

        async setImageData(_path) {
            const info = await this.$uni.getImageInfo(_path)
            if (!info || info === 'error') return this.$uni.showToast('图片获取失败，请重试')
            const {path, width, height} = info
            this.userPath = path

            const max_width = this.bgWidth / 2
            const max_height = this.bgHeight / 2

            let image_width = width, image_height = height
            if (image_width > max_width) {
                image_width = max_width
                image_height = height * (max_width / width)
            }
            if (image_height > max_height) {
                image_height = max_height
                image_width = width * (max_height / height)
            }

            this.userPathData = {
                x: max_width - image_width / 2,
                y: max_height - image_height / 2,
                width: image_width,
                height: image_height
            }
        },

        getBoundingClientRect(selector) {
            return new Promise(resolve => {
                uni.createSelectorQuery().in(this).select(selector).boundingClientRect(rect => {
                    resolve(rect)
                }).exec()
            })
        },

        async getUserMovableViewData() {
            const bgData = await this.getBoundingClientRect('.movable-area')
            const userData = await this.getBoundingClientRect('.movable-view')

            return {
                x: userData.left - bgData.left,
                y: userData.top - bgData.top,
                width: userData.width,
                height: userData.height
            }
        },

        async done() {
            if (!this.userPath) return this.$uni.showToast('请先上传图片')

            this.$uni.showLoading()
            const ctx = uni.createCanvasContext('canvas')

            const user = await this.getUserMovableViewData()
            const {x, y, width, height} = user
            ctx.drawImage(this.userPath, x, y, width, height)

            ctx.drawImage(this.bgPath, 0, 0, this.bgWidth, this.bgHeight)

            ctx.draw()

            this.canvasToTempFilePath()
        },

        canvasToTempFilePath(count = 1) {
            if (count > 10) {
                uni.hideLoading()
                return this.$uni.showToast('图片生成失败')
            }

            setTimeout(() => {
                uni.canvasToTempFilePath({
                    canvasId: 'canvas',
                    x: 0,
                    y: 0,
                    width: this.bgWidth,
                    height: this.bgHeight,
                    destWidth: this.bgWidth * (pixelRatio || 1),
                    destHeight: this.bgHeight * (pixelRatio || 1),
                    success: res => {
                        uni.hideLoading()
                        this.complete(res.tempFilePath)
                    },
                    fail: () => this.canvasToTempFilePath(count + 1)
                }, this)
            }, 300)
        },

        complete(path) {
            this.completePath = path
            this.$refs.submitPopup.open()
        },

        async submit() {
            if (!this.content) return this.$uni.showToast('请填写内容')

            this.$uni.showLoading('发布中...')

            const src = await this.getPathSrc()
            if (!src) {
                uni.hideLoading()
                return this.$uni.showModal('图片上传失败，请重试。')
            }

            const {nickname, headimg} = await this.getActiveUserInfo()

            const res = await this.xwy_api.request({
                url: 'front.user.reply/submit_content',
                data: {
                    types: 6,
                    content: this.content || '',
                    pic_list: JSON.stringify([src]),
                    nickname: nickname || app.globalData['userinfo'].nickname,
                    headimg: headimg || app.globalData['userinfo'].headimg,
                    is_long_id: 1,
                    pid: this.active_id,
                    bind_id: this.point_id
                }
            })

            if (res?.status !== 1) {
                uni.hideLoading()
                return this.$uni.showModal(res?.info || '发布失败')
            }

            await this.reward()
        },

        async getPathSrc() {
            return await this.xwy_api.uploadOneImage({
                temp_data: { path: this.completePath },
                active_id: this.active_id
            })
        },

        async getActiveUserInfo() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: { active_id: this.active_id }
            })

            const {nickname, headimg} = app.globalData['userinfo']

            const details = res?.data?.user_details
            if (!details) return {nickname, headimg}

            return {
                nickname: details.must_submit?.[0]?.value || nickname || '',
                headimg: details.headimg || headimg || ''
            }
        },

        async reward() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify({
                        types: 21,
                        point_id: this.point_id,
                        result: 'success'
                    }))
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '奖励失败')

            this.getOpenerEventChannel().emit('success')
            let info = '任务完成'
            if (res?.data?.num) info += ` 奖励${res.data.num}积分`
            this.$uni.showToast(info)
            this.$uni.navigateBack(1, {delay: 1000})
        },

        lookPath() {
            this.$uni.previewImage(this.completePath)
        }
    }
}
</script>

<style lang="scss">
.movable-area-container {
    margin: 0 10px;
    position: relative;

    .bg-view {
        position: absolute;
        z-index: 9;
        background-size: 100%;
        pointer-events: none;
    }

    .movable-area {
        pointer-events: auto;
        overflow: hidden;
    }
}

.image-list-scroll-view {
    width: calc(100vw - 10px);
    margin: 10px 5px;

    .image-list {
        .image-item {
            width: 100px;
            height: 100px;
            box-sizing: border-box;
            margin: 0 5px;
            border-radius: 5px;

            .image-item-image {
                width: 100px;
                height: 100px;
                display: block;
            }

            &.active-image {
                border: 2px solid #ed3f14;

                .image-item-image {
                    width: 96px;
                    height: 96px;
                }
            }
        }
    }
}

.bottom-button {
	width: 250px;
	line-height: 44px;
	font-size: 16px !important;
	border-radius: 5px;
	text-align: center;

    &::after {
        content: "";
        border: none
    }
}

.canvas {
    position: fixed;
    left: -1000vw;
}

.submit-popup {
    width: 85vw;
    padding: 10px;
    border-radius: 10px;
    background-color: #fff;

    .image-path {
        padding: 20px 0;

        .image-path-src {
            width: 120px;
            height: 120px;
            display: block;
            border-radius: 5px;
        }
    }

    .submit-button {
        line-height: 44px;
        border-radius: 22px;
        width: 250px;
    }
}
</style>