<template>
    <view class='layout f-d-c-j-sb-a-c' :style="{'background-image': `url(${bgImg})`}">
        <view style="width: 100vw;">
            <game-top-msg-bar right-icon="icon-rocket">
                <template v-slot:leftMsg>{{ time }}</template>
                <template v-slot:rightMsg>{{ score }}</template>
            </game-top-msg-bar>
        </view>

        <!-- 游戏开始 -->
        <!-- v-if="gameStatus" -->
        <template>
            <!-- 卡牌框 -->
            <view class="f-j-c"
                  :style="{'width': `calc(${cols * size + size / 2}px)`, 'height': `calc(${cellHeight}px)`}">
                <view v-if="cellHtml && cellHtml.length" class="width100 relative">
                    <view v-if="!item.isMove" v-for="(item,index) in cellHtml" :key="item.id" class="cell-item"
                          :class="[item.disabled ? 'disabled' :'']" @click="move(index)" :id="item.id"
                          :style="{width: `${size}px`, height: `${size}px`, left: `${item.left}px`, top: `${item.top}px`,}">
                        <image
                            :style="{ width: `${size - 1}px`, height: `${size - 1}px`, borderRadius: '5px', backgroundColor: '#71a419', border: '1px solid #71a419', }"
                            :src="item.src"></image>
                    </view>
                </view>
            </view>

            <!-- 消除展示框 -->
            <view class="move-list radius_3 f-a-c" :style="{
					width: `${moveW}px`,
					height: `${moveH}px`,
					'margin-bottom': `15px`,
					'background-image': `url(${moveBgImg})`,
				}">
            </view>
        </template>

        <game-tips-popup ref="gameTips" :tips-list="tipsList" :show-ad="showAd" @startGame="startGame"/>
        <game-result-popup ref="resultPopup" :show-ad="showAd" :unit="unit"/>
    </view>
</template>

<script>
import config from "./config.js"

import gameTipsPopup from '../components/game-tips-popup.vue'
import gameTopMsgBar from '../components/game-top-msg-bar.vue'

export default {
    components: {gameTipsPopup, gameTopMsgBar},
    data() {
        return {
            bgImg: config.bgImg,
            moveBgImg: config.moveBgImg,

            usableW: 0,
            cellHeight: 0, // 方块可用区域
            simpleData: config.simpleData.map((src, index) => ({src, index})),
            gameStatus: 0, // 0未开始 1游戏中 2已结束
            showLevel: true, // 是否显示难度选择

            moveRect: {}, // 存放区域的信息
            moveW: 0, // 存放区域宽度
            moveH: 0, // 存放区域高度
            //卡片大小
            size: 40,
            //行 目前通过可用高度计算行
            rows: 7,
            //列 最少7列
            cols: 7,
            //3个一消除
            oneGroupCount: 3,
            //每个消除组数 可以用来控制总共要消除的方块
            group: 1,
            //总共层
            layerCount: 30, // 越大越好，才能将所有图片绘制
            //储存数据的数组
            cellHtml: [],
            //待消除的数据
            moveData: [],
            canMove: true,

            endText: '', // 游戏结束语

            score: 0,

            per_integral: 0,
            unit: '积分',

            showAd: false,
            time: 0, // 游戏时间倒计时
            second: 30,  // 游戏初始时间
        }
    },

    computed: {
        tipsList() {
            const {second, per_integral, unit} = this
            return [
                `请在${second}秒内点击屏幕中的图标，将图片放置到下方卡槽中。`,
                '卡槽内存在3个相同的图标时，3个相同图标会消除，得分加1。',
                `倒计时结束或卡槽满后，游戏结束，每得1分奖励${per_integral}${unit}`
            ]
        }
    },

    async onLoad(params) {
        this.active_id = params.active_id
        if (params.point_id) this.point_id = Number(params.point_id)
        if (params.second) {
            this.time = Number(params.second)
            this.second = Number(params.second)
        }
        if (params.title) this.$uni.setNavigationBarTitle(params.title)
        if (params.per_integral) this.per_integral = Number(params.per_integral)
        if (params.unit) this.unit = params.unit
        if (params.show_ad) this.showAd = true

        

        this.getGameData()
    },

    onUnload() {
        this.clearCountdownInterval()
    },

    methods: {
        async getGameData() {
            const data = await this.getOpenerEventChannelData()

            if (data.bg_img) this.bgImg = data.bg_img
            if (data.card_imgs?.length) this.simpleData = data.card_imgs.map((src, index) => ({src, index}))
            if (data.stuck_slot_img) this.moveBgImg = data.stuck_slot_img

            if (data.navigation_bar) uni.setNavigationBarColor({
                ...data.navigation_bar,
                fail: err => console.log(err)
            })

            const system = uni.getSystemInfoSync()
            this.usableW = system.windowWidth - 60
            const usableH = system.windowHeight
            this.size = this.usableW / this.cols
            const moveImgInfo = await this.getMoveImgInfo()
            this.moveW = this.size * 7 / 0.94 // 7个方块的宽度（94%） + 栅栏的宽度（6%）
            this.moveH = this.moveW / moveImgInfo.ratio
            // 方块可用高度
            this.cellHeight = usableH - this.moveH - this.size - 60
            this.rows = Math.floor(this.cellHeight / this.size)

            this.initGame()

            this.$nextTick(() => this.$refs.gameTips.open())
        },

        getOpenerEventChannelData() {
            return new Promise(resolve => this.getOpenerEventChannel().once('data', data => resolve(data)))
        },

        async getMoveImgInfo() {
            return new Promise(s => {
                uni.getImageInfo({
                    src: this.moveBgImg,
                    success: (res) => {
                        res.ratio = res.width / res.height
                        s(res)
                    }
                })
            })
        },

        startGame() {
            this.$refs.gameTips.close()
            this.countdown()
        },

        initGame() {
            this.gameStatus = 1
            this.showLevel = false
            this.$nextTick(() => {
                this.getMoveRect()
                this.init()
            })
        },

        countdown() {
            this.time = this.second
            this.countdownInterval = setInterval(() => {
                this.time--
                if (this.time <= 0) {
                    if (this.time < 0) this.time = 0
                    this.clearCountdownInterval()
                    this.$uni.showToast('时间到，游戏结束')
                    setTimeout(() => this.gameOver(), 1000)
                }
            }, 1000)
        },

        clearCountdownInterval() {
            if (!this.countdownInterval) return
            clearInterval(this.countdownInterval)
            this.countdownInterval = null
        },

        getMoveRect() {
            const moveList = uni.createSelectorQuery().in(this).select(".move-list")
            moveList.boundingClientRect(data => {
                this.moveRect = data
            }).exec();
        },

        async init() {
            this.moveData = []
            const cellHtml = []
            //整理
            const count = this.oneGroupCount * this.group
            let renderData = Array.from(new Array(count)).map(() => {
                return this.simpleData.map(v => ({...v}))
            }).flat().sort(() => Math.random() - 0.5) //加上flat是为了把二维的拉成一维的   //最后用随机数打乱一下

            //第一步画表格
            //先绘制  最上面一层    然后  从顶层到底层绘制  进行行和列的 数据循环
            for (let ly = this.layerCount - 1; ly >= 0; ly--) {
                for (let i = 0; i < this.rows; i++) {
                    for (let j = 0; j < this.cols; j++) {
                        let pyStep = (ly + 1) % 2 === 0 ? this.size / 2 : 0 //给偏移量和不给偏移量  实现错开的效果
                        //进行  图层的渲染   id 是必要的   这个定义了 第几层ly 第几行 i 第几列j  可以判断这个卡片有没有被盖住
                        //最终  我们会以绝对定位的方式 进行 布局
                        //整个随机数
                        let item = (Math.random() > 0.7 && renderData.pop()) //取完随机数  然后用pop  随用 随删  直到没有为止
                        item && cellHtml.push({
                            ly: ly,
                            i: i,
                            j: j,
                            left: this.size * j + pyStep,
                            top: this.size * i + pyStep,
                            id: 'm' + ly + '-' + i + '-' + j,
                            index: item.index,
                            src: item.src,
                            isMove: false,
                        })
                        // if (cellHtml.length >= renderData.length) continue
                    }
                }
            }
            this.cellHtml = cellHtml.reverse()
            this.checkDisabled()
        },
        checkDisabled() {
            this.cellHtml.forEach(v => {
                const arr = v.id.substring(1).split('-').map(v => Number(v))
                const isPy = (arr[0] + 1) % 2 === 0
                for (let i = arr[0] + 1; i <= this.layerCount - 1; i++) {
                    const isPyB = (i + 1) % 2 === 0
                    if (isPy === isPyB) {
                        let el = this.cellHtml.find(item => {
                            return item.id === 'm' + i + '-' + arr[1] + '-' + arr[2]
                        })
                        if (el) {
                            v.disabled = true
                            break;
                        }
                    } else if (isPy && !isPyB) {
                        [
                            `${i}-${arr[1]}-${arr[2]}`,
                            `${i}-${arr[1]}-${arr[2] + 1}`,
                            `${i}-${arr[1] + 1}-${arr[2]}`,
                            `${i}-${arr[1] + 1}-${arr[2] + 1}`
                        ].every(k => {
                            let el = this.cellHtml.find(item => {
                                return item.id === 'm' + k
                            })
                            return !el
                        })
                        if (![
                            `${i}-${arr[1]}-${arr[2]}`,
                            `${i}-${arr[1]}-${arr[2] + 1}`,
                            `${i}-${arr[1] + 1}-${arr[2]}`,
                            `${i}-${arr[1] + 1}-${arr[2] + 1}`
                        ].every(k => {
                            let el = this.cellHtml.find(item => {
                                return item.id === 'm' + k
                            })
                            return !el
                        })) {
                            v.disabled = true
                            break;
                        } else {
                            v.disabled = false
                        }
                    } else if (!isPy && isPyB) {
                        if (![
                            `${i}-${arr[1]}-${arr[2]}`,
                            `${i}-${arr[1]}-${arr[2] - 1}`,
                            `${i}-${arr[1] - 1}-${arr[2]}`,
                            `${i}-${arr[1] - 1}-${arr[2] - 1}`
                        ].every(k => {
                            let el = this.cellHtml.find(item => {
                                return item.id === 'm' + k
                            })
                            return !el
                        })) {
                            v.disabled = true
                            break;
                        } else {
                            v.disabled = false
                        }
                    }
                }
            })
        },

        move(index) {
            if (this.time <= 0) return

            const item = this.cellHtml[index]
            if (!this.canMove || item.disabled || item.click || this.moveData.length >= 7) return
            this.canMove = false

            item.left = this.size * (this.moveData.length + .25)
            item.top = this.moveRect.top - 55 + this.moveH * .1
            item.click = true
            item.id = `${item.id}-move`
            this.$set(this.cellHtml, index, item)
            this.moveData.push(item)
            setTimeout(() => this.modeEnd(item), 1)
        },
        modeEnd(item) {
            const findResult = this.moveData.filter(v => item.index === v.index)
            setTimeout(() => {
                if (findResult.length === 3) {
                    this.score++
                    findResult.forEach(v => {
                        const el = this.cellHtml.find(i => v.id === i.id)
                        el.isMove = true
                        const index = this.moveData.findIndex(i => v.id === i.id)
                        this.$delete(this.moveData, index)
                    })
                    // 归位
                    this.moveData.forEach((it, index) => {
                        it.left = this.size * (index + .25)
                    })
                }
                this.$nextTick(() => {
                    this.gameStatus = 2

                    if (this.moveData.length === 7) {
                        this.clearCountdownInterval()
                        this.$uni.showToast('池子已满，游戏结束')
                        setTimeout(() => this.gameOver(), 1000)
                    } else if (this.cellHtml.every(v => v.isMove)) {
                        this.$uni.showToast('难度升级')
                        setTimeout(() => {
                            this.group++
                            this.initGame()
                        }, 300)
                    }
                })
                this.canMove = true
            }, 200)
            this.checkDisabled()
        },

        gameOver() {
            // 避免多次重复提交
            if (this.isGameOver) return
            this.isGameOver = true

            this.clearCountdownInterval()
            this.submitResult()
        },

        async submitResult() {
            const count = this.score
            const sign = {
                types: 26,
                point_id: this.point_id,
                result: count ? 'success' : 'fail',
                count
            }

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify(sign))
                }
            })

            let resultCode = sign.result === 'success' ? 1 : 0
            let info = resultCode === 1 ? '任务完成' : '任务失败'
            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info = res.info
            }


            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })


            this.getOpenerEventChannel().emit('success')
        }
    }

}
</script>

<style scoped lang='scss'>
.layout {
    width: 100%;
    height: 100vh;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    overflow: hidden;
}

.cell-item {
    position: absolute;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 5px;
    transition: left .2s, top .2s;
}

/* 如果被压在底下的颜色  应该是一种灰色 */
.cell-item:after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    transition: background-color .3s;
}

/* 这个是为了   当配够  有disabled 属性的时候 就会透明掉 */
.disabled:after {
    background-color: rgba(0, 0, 0, 0.4);
    border-radius: 5px;
}

.game-board-wrap {
    position: fixed;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, .5);
    top: 0;
    left: 0;
    z-index: 999;
}

.move-list {
    background-repeat: no-repeat;
    background-size: cover;
}

.f-d-c-j-sb-a-c {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
}

.f-a-c {
    display: flex;
    align-items: center;
}

.f-j-c {
    display: flex;
    justify-content: center;
}

.width100 {
    width: 100%;
}

.relative {
    position: relative;
}

.radius_3 {
    border-radius: .3rem;
}

</style>