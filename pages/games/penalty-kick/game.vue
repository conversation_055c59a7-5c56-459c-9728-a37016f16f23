<template>
    <view>
        <view class="game-tips-msg-bar">
            <game-top-msg-bar right-icon="icon-football">
                <template v-slot:leftMsg>{{ countdown }}</template>
                <template v-slot:rightMsg>{{ score }}</template>
            </game-top-msg-bar>
        </view>


        <view class="game-container"
              @touchstart="onTouchStart"
              @touchmove="onTouchMove"
              @touchend="onTouchEnd">

            <!-- 背景 -->
            <image class="background-image" :src="images.background" mode="aspectFill"/>

            <!-- 球门 -->
            <image class="goal-image" :src="images.goal" :style="goalImageStyle"/>

            <!-- 守门员 -->
            <image class="goalkeeper-image" :src="images.goalkeeper" :style="goalkeeperImageStyle"/>

            <!-- 足球 -->
            <image class="football-image" :class="{ 'football-spinning': isFootballSpinning }" :src="images.football" :style="footballImageStyle"/>
            
            <!-- 礼花效果 -->
            <view v-if="showConfetti" class="confetti-container">
                <view v-for="(item, index) in confettiItems" 
                      :key="index" 
                      class="confetti-item"
                      :style="{
                          left: item.x + 'px',
                          top: item.y + 'px',
                          width: item.size + 'px',
                          height: item.size + 'px',
                          backgroundColor: item.color,
                          transform: 'rotate(' + item.rotation + 'deg)',
                          opacity: item.opacity
                      }">
                </view>
            </view>
        </view>

        <game-tips-popup ref="gameTipsPopup" :tips-list="tipsList" :show-ad="showAD" @startGame="startGame"/>

        <ready-countdown ref="readyCountdown" @countdownOver="countdownOver"/>

        <game-result-popup ref="resultPopup" :show-ad="showAD" :unit="unit"/>

        <xwy-ad v-if="showAD" :ad_type="3"/>
    </view>
</template>

<script>
import gameTipsPopup from '../components/game-tips-popup.vue'
import gameTopMsgBar from '../components/game-top-msg-bar.vue'
import readyCountdown from '../components/ready-countdown.vue'

const {windowWidth, windowHeight} = uni.getWindowInfo()

const BASE_URL = 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/game/penalty-kick/'
const sound = {
    goal: `${BASE_URL}cheer.mp3`,
    miss: `${BASE_URL}boos.mp3`
}

export default {
    components: {gameTipsPopup, gameTopMsgBar, readyCountdown},
    data() {
        return {
            // 游戏图片资源
            images: {
                background: `${BASE_URL}bg.png`,
                goalkeeper: `${BASE_URL}goalkeeper.png`,
                goal: `${BASE_URL}goal.png`,
                football: `${BASE_URL}football.png`
            },
            gameState: 'ready', // 游戏状态：ready  //shooting  // gameover
            goalStyle: {
                top: 0,
                left: 0,
                width: 0,
                height: 0
            },
            goalkeeperStyle: {
                top: 0,
                left: 0,
                width: 0,
                height: 0
            },
            footballStyle: {
                top: 0,
                left: 0,
                width: 0,
                height: 0
            },
            isFootballSpinning: false, // 足球是否在旋转
            // 守门员移动相关
            goalkeeperMoveDistance: 10, // 每次移动的像素距离
            goalkeeperMoveInterval: 30, // 移动间隔时间（毫秒）
            
            // 礼花效果相关
            showConfetti: false,
            confettiItems: [],

            per_integral: 0,
            unit: '积分',
            seconds: 30,
            countdown: 30,
            score: 0,
            showAD: false
        }
    },

    computed: {
        goalImageStyle() {
            const {width, height, left, top} = this.goalStyle
            return `width: ${width}px; height: ${height}px; left: ${left}px; top: ${top}px;`
        },

        goalkeeperImageStyle() {
            const {width, height, left, top} = this.goalkeeperStyle
            return `width: ${width}px; height: ${height}px; left: ${left}px; top: ${top}px;`
        },

        footballImageStyle() {
            const {width, height, left, top} = this.footballStyle
            return `width: ${width}px; height: ${height}px; left: ${left}px; top: ${top}px;`
        },

        tipsList() {
            return [
                '使用手指将足球滑向球门，即可完成射门。',
                '足球未被守门员接住，则射门成功。',
                `游戏倒计时${this.seconds}秒结束后结算${this.unit}，每成功射门1次可获得${this.per_integral}${this.unit}。`
            ]
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.point_id = params.point_id
        this.unit = params.unit
        this.per_integral = Number(params.per_integral)
        this.seconds = Number(params.seconds)
        this.countdown = this.seconds
        if (params.show_ad) this.showAD = true
        if (params.title) this.$uni.setNavigationBarTitle(params.title)

        this.initGame()
    },

    // 页面销毁时清除定时器
    onUnload() {
        this.clearGoalkeeperMoveInterval()
        this.clearCountdown()
        // 清除射门动画定时器
        if (this.shootAnimationTimer) {
            clearInterval(this.shootAnimationTimer)
            this.shootAnimationTimer = null
        }
        // 清除礼花动画定时器
        if (this.confettiTimer) {
            clearInterval(this.confettiTimer)
            this.confettiTimer = null
        }
    },


    methods: {
        async initGame() {
            await this.getGameData()
            
            await this.getGoalStyle()
            await this.setGoalkeeperStyle()
            this.setFootballStyle()
            this.setGoalPosition()

            this.$refs.gameTipsPopup.open()
        },

        async getGameData() {
            const data = await this.getOpenerEventChannelData()

            if (data.bg_img) this.images.background = data.bg_img
            if (data.goalkeeper_img) this.images.goalkeeper = data.goalkeeper_img
            if (data.goal_img) this.images.goal = data.goal_img
            if (data.football_img) this.images.football = data.football_img

            if (data.navigation_bar) uni.setNavigationBarColor({
                ...data.navigation_bar,
                fail: err => console.log(err)
            })
        },

        getOpenerEventChannelData() {
            return new Promise(resolve => this.getOpenerEventChannel().once('data', data => resolve(data)))
        },

        startGame() {
            this.$refs.gameTipsPopup.close()
            this.$refs.readyCountdown.open()
        },

        countdownOver() {
            this.startCountdown()
            this.goalkeeperInitMove()
        },

        startCountdown() {
            this.countdown = this.seconds
            this.countdownInterval = setInterval(() => {
                this.countdown--
                if (this.countdown <= 0) {
                    this.gameOver()
                }
            }, 1000)
        },

        clearCountdown() {
            if (this.countdownInterval) {
                clearInterval(this.countdownInterval)
                this.countdownInterval = null
            }
        },

        async getGoalStyle() {
            const {width: imageWidth, height: imageHeight} = await this.$uni.getImageInfo(this.images.goal)

            const width = windowWidth - 40
            const ratio = imageWidth / width
            const height = imageHeight / ratio

            const left = 20
            const top = windowHeight * 0.1

            this.goalStyle = {width, height, left, top}
        },


        async setGoalkeeperStyle() {
            const {
                width: imageWidth,
                height: imageHeight
            } = await this.$uni.getImageInfo(this.images.goalkeeper)

            const height = this.goalStyle.height
            const ratio = imageHeight / height
            const width = imageWidth / ratio

            const top = this.goalStyle.top + 20
            const left = windowWidth / 2 - width / 2

            const goalInitStyle = {width, height, top, left}
            this.goalkeeperInitStyle = {...goalInitStyle}
            this.goalkeeperStyle = goalInitStyle
        },

        goalkeeperInit() {
            this.goalkeeperStyle = {...this.goalkeeperInitStyle}
        },

        goalkeeperInitMove() {
            // 清除之前的定时器
            this.clearGoalkeeperMoveInterval()

            if (this.gameState === 'gameover') return

            // 随机初始移动方向：1 向右，-1 向左
            this.goalkeeperMoveDirection = Math.random() > 0.5 ? 1 : -1

            // 开始移动
            this.goalkeeperMoveTimer = setInterval(() => {
                this.moveGoalkeeper()
            }, this.goalkeeperMoveInterval)
        },

        clearGoalkeeperMoveInterval() {
            if (this.goalkeeperMoveTimer) {
                clearInterval(this.goalkeeperMoveTimer)
                this.goalkeeperMoveTimer = null
            }
        },

        moveGoalkeeper() {
            // 计算当前位置
            const currentLeft = this.goalkeeperStyle.left
            const goalLeft = this.goalStyle.left
            const goalRight = this.goalStyle.left + this.goalStyle.width
            const goalkeeperWidth = this.goalkeeperStyle.width

            // 计算下一个位置
            const nextLeft = currentLeft + (this.goalkeeperMoveDirection * this.goalkeeperMoveDistance)

            // 检查边界并改变方向
            if (this.goalkeeperMoveDirection === -1 && nextLeft <= goalLeft) {
                // 向左移动到左边界，改变方向向右
                this.goalkeeperMoveDirection = 1
                this.goalkeeperStyle.left = goalLeft
            } else if (this.goalkeeperMoveDirection === 1 && nextLeft + goalkeeperWidth >= goalRight) {
                // 向右移动到右边界，改变方向向左
                this.goalkeeperMoveDirection = -1
                this.goalkeeperStyle.left = goalRight - goalkeeperWidth
            } else {
                // 正常移动
                this.goalkeeperStyle.left = nextLeft
            }
        },

        setFootballStyle() {
            const size = this.goalkeeperStyle.width / 2
            const left = windowWidth / 2 - size / 2
            const top = windowHeight - windowHeight * 0.15 - size

            const footballStyle = {
                width: size,
                height: size,
                left,
                top
            }
            this.footballStyle = footballStyle
            this.footballInitStyle = {...footballStyle}
        },

        footballInit() {
            this.footballStyle = {...this.footballInitStyle}
            this.isFootballSpinning = false // 停止足球旋转
        },

        // 计算进球的位置
        setGoalPosition() {
            const {top: goalTop, left: goalLeft, width: goalWidth, height: goalHeight} = this.goalStyle
            const ballSize = this.footballStyle.width

            const top = goalTop + goalHeight / 2 - ballSize / 2

            this.goalPosition = {
                left: {top, left: goalLeft + 40},
                center: {top, left: goalLeft + goalWidth / 2 - ballSize / 2},
                right: {top, left: goalLeft + goalWidth - ballSize - 40}
            }
        },

        // 触摸开始
        onTouchStart(e) {
            if (this.gameState !== 'ready') return

            this.touchStartPos = {
                startX: e.changedTouches[0].clientX,
                startY: e.changedTouches[0].clientY
            }
        },

        // 触摸移动
        onTouchMove(e) {
            // 阻止默认行为
            e.preventDefault()
        },

        // 触摸结束
        onTouchEnd(e) {
            if (this.gameState !== 'ready' || !this.touchStartPos) return

            const {clientX, clientY} = e.changedTouches[0]
            const {startX, startY} = this.touchStartPos

            // 手指滑动了多少距离
            const distance = Math.sqrt(
                Math.pow(clientX - startX, 2) +
                Math.pow(clientY - startY, 2)
            )
            // console.log(distance);
            // 如果手指滑动的距离小于10，不处理。因为可能存在不小心点到
            if (distance < 10) {
                this.touchStartPos = null
                return
            }

            // 手指向上滑动多少距离
            const distanceY = startY - clientY
            // 如果手指向上滑动的距离小于10，不处理。因为可能存在不小心点到或者是向后滑的情况
            if (distanceY <= 10) {
                this.touchStartPos = null
                return
            }

            // 算出 {startX, startY} 到 {clientX, clientY} 的角度
            const angle = Math.atan2(startY - clientY, startX - clientX) * 180 / Math.PI

            let shootDirection = 'center'
            if (angle < 80) {
                shootDirection = 'left'
            } else if (angle > 100) {
                shootDirection = 'right'
            }


            this.shootBall(shootDirection)
        },

        // 射门
        shootBall(shootDirection) {
            // 防止重复射门
            if (this.gameState !== 'ready') return
            
            // 设置游戏状态为射门中
            this.gameState = 'shooting'
            
            // 开始足球旋转动画
            this.isFootballSpinning = true
            
            // 获取目标位置
            const targetPosition = this.goalPosition[shootDirection]
            
            // 获取当前足球位置
            const startPosition = {
                left: this.footballStyle.left,
                top: this.footballStyle.top
            }
            
            // 动画时长（毫秒）
            const duration = 500
            const fps = 60  // 帧率
            const interval = 1000 / fps  // 每帧间隔时间
            const totalFrames = Math.ceil(duration / interval)
            let currentFrame = 0
            
            // 清除之前的动画定时器（如果存在）
            if (this.shootAnimationTimer) {
                clearInterval(this.shootAnimationTimer)
            }
            
            // 使用 setInterval 实现动画
            this.shootAnimationTimer = setInterval(() => {
                currentFrame++
                const progress = currentFrame / totalFrames
                
                // 使用缓动函数让动画更自然（先快后慢）
                const easeProgress = 1 - Math.pow(1 - progress, 3)
                
                if (shootDirection === 'center') {
                    // 中路射门，保持直线
                    this.footballStyle.left = startPosition.left + (targetPosition.left - startPosition.left) * easeProgress
                    this.footballStyle.top = startPosition.top + (targetPosition.top - startPosition.top) * easeProgress
                } else {
                    // 左右射门，使用简单的C字弧线
                    const t = easeProgress
                    
                    // 弧线偏移量（更小的值，产生小C字效果）
                    const curveOffset = 50
                    
                    // 计算直线轨迹
                    const linearX = startPosition.left + (targetPosition.left - startPosition.left) * t
                    const linearY = startPosition.top + (targetPosition.top - startPosition.top) * t
                    
                    // 添加横向偏移，形成C字弧线
                    // 使用 sin 函数让偏移在中间最大，两端为0
                    const offsetMultiplier = Math.sin(t * Math.PI)
                    
                    if (shootDirection === 'left') {
                        // 左侧射门，向左凸出的C字
                        this.footballStyle.left = linearX - curveOffset * offsetMultiplier
                    } else {
                        // 右侧射门，向右凸出的反C字
                        this.footballStyle.left = linearX + curveOffset * offsetMultiplier
                    }
                    
                    this.footballStyle.top = linearY
                }
                
                
                // 动画结束
                if (currentFrame >= totalFrames) {
                    clearInterval(this.shootAnimationTimer)
                    this.shootAnimationTimer = null
                    // 足球到达球门，停止守门员移动和足球旋转
                    this.clearGoalkeeperMoveInterval()
                    this.isFootballSpinning = false
                    
                    // 动画结束检测碰撞 判断是否进球
                    if (this.checkCollision()) {
                        this.handleShootResult(false)
                    } else {
                        this.handleShootResult(true)
                    }
                }
            }, interval)
        },
        
        // 检测足球和守门员是否碰撞
        checkCollision() {
            const ball = this.footballStyle
            const keeper = this.goalkeeperStyle
            
            // 获取足球中心点
            const ballCenterX = ball.left + ball.width / 2
            const ballCenterY = ball.top + ball.height / 2
            
            // 判断足球中心是否在守门员范围内（简化的矩形碰撞检测）
            const isColliding = ballCenterX >= keeper.left && 
                               ballCenterX <= keeper.left + keeper.width &&
                               ballCenterY >= keeper.top && 
                               ballCenterY <= keeper.top + keeper.height
            
            return isColliding
        },
        
        // 创建礼花效果
        createConfetti() {
            const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E2']
            const confettiCount = 40 // 礼花数量
            
            // 清空之前的礼花
            this.confettiItems = []
            
            const confettiItems = []
            for (let i = 0; i < confettiCount; i++) {
                // 礼花从屏幕顶部的随机位置开始
                const x = Math.random() * windowWidth
                const y = -20 // 从屏幕顶部上方开始
                
                // 随机水平速度，让礼花有飘动效果
                const vx = (Math.random() - 0.5) * 0.5 // -0.25 到 0.25 之间，减小水平速度
                const vy = 1 + Math.random() * 2 // 下落速度 1-3
                
                const size = 8 + Math.random() * 12 // 尺寸 8-20
                
                confettiItems.push({
                    x: x,
                    y: y,
                    vx: vx,
                    vy: vy,
                    size: size,
                    color: colors[Math.floor(Math.random() * colors.length)],
                    rotation: Math.random() * 360,
                    rotationSpeed: (Math.random() - 0.5) * 10, // -5 到 5
                    opacity: 1,
                    gravity: 0.05, // 较小的重力加速度
                    swingAmplitude: Math.random() * 5 + 3, // 摆动幅度 3-8，大幅减小
                    swingSpeed: Math.random() * 0.03 + 0.01, // 摆动速度 0.01-0.04，稍微减小
                    swingPhase: Math.random() * Math.PI * 2 // 随机初始相位，让礼花往不同方向飘
                })
            }

            this.confettiItems = confettiItems
            this.showConfetti = true
            
            // 延迟开始动画
            setTimeout(() => {
                this.animateConfetti()
            }, 50)
        },
        
        // 礼花动画
        animateConfetti() {
            // 清除之前的礼花动画定时器（如果存在）
            if (this.confettiTimer) {
                clearInterval(this.confettiTimer)
            }
            
            let frameCount = 0
            // 使用 setInterval 代替 requestAnimationFrame
            this.confettiTimer = setInterval(() => {
                frameCount++
                let allFinished = true
                
                this.confettiItems = this.confettiItems.map(item => {
                    // 更新垂直位置和速度
                    item.y += item.vy
                    item.vy += item.gravity // 重力效果
                    
                    // 更新水平位置 - 添加摆动效果，使用初始相位
                    item.x += item.vx + Math.sin(frameCount * item.swingSpeed + item.swingPhase) * item.swingAmplitude * 0.1
                    
                    // 更新旋转
                    item.rotation += item.rotationSpeed
                    
                    // 当礼花飘落到屏幕1/3位置后开始淡出
                    if (item.y > windowHeight / 3) {
                        item.opacity -= 0.03
                    }
                    
                    // 检查是否还需要继续动画
                    if (item.opacity > 0 && item.y < windowHeight / 3 + 100) {
                        allFinished = false
                    }
                    
                    return item
                })
                
                
                if (allFinished) {
                    // 动画结束，隐藏礼花
                    clearInterval(this.confettiTimer)
                    this.confettiTimer = null
                    this.showConfetti = false
                    this.confettiItems = []
                }
            }, 16) // 约60fps
        },
        
        // 处理射门结果
        handleShootResult(isGoal) {
            if (isGoal) {
                // 进球了
                this.score ++
                
                // 播放进球音效
                const goalAudio = uni.createInnerAudioContext()
                goalAudio.src = sound.goal
                goalAudio.play()
                
                // 创建礼花效果
                this.createConfetti()
                
                // 显示进球提示 - 随机选择一个激励的消息
                const goalMessages = [
                    '🎉 精彩进球！',
                    '⚽ 射门得分！',
                    '🔥 完美射门！',
                    '🏆 太棒了！',
                    '💪 技术精湛！',
                    '🎯 百发百中！'
                ]
                const randomGoalMessage = goalMessages[Math.floor(Math.random() * goalMessages.length)]
                
                uni.showToast({
                    title: randomGoalMessage,
                    icon: 'none',
                    duration: 1500
                })
            } else {
                // 被挡住了
                // 播放未进球音效
                const missAudio = uni.createInnerAudioContext()
                missAudio.src = sound.miss
                missAudio.play()
                
                // 显示未进球提示 - 随机选择一个鼓励的消息
                const missMessages = [
                    '😤 被守门员扑出！',
                    '🥅 守门员反应神速！',
                    '💨 差一点就进了！',
                    '🏃 再试一次！',
                    '🤏 就差那么一点！',
                    '🛡️ 守门员表现出色！'
                ]
                const randomMissMessage = missMessages[Math.floor(Math.random() * missMessages.length)]
                
                uni.showToast({
                    title: randomMissMessage,
                    icon: 'none',
                    duration: 1500
                })
            }
            
            // 1秒后重置足球位置并恢复守门员移动
            setTimeout(() => {
                if (this.gameState !== 'gameover') {
                    this.footballInit()
                    this.gameState = 'ready'
                    // 恢复守门员移动
                    this.goalkeeperInitMove()
                }
            }, 1000)
        },


        gameOver() {
            this.gameState = 'gameover'
            this.clearGoalkeeperMoveInterval()
            this.clearCountdown()

            this.submitResult()
        },

        async submitResult() {
            const count = this.score
            const sign = {
                types: 38,
                point_id: this.point_id,
                result: count ? 'success' : 'fail',
                count
            }

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify(sign))
                }
            })

            let resultCode = sign.result === 'success' ? 1 : 0
            let info = resultCode === 1 ? '任务完成' : '任务失败'
            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info = res.info
            }

            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })


            this.getOpenerEventChannel().emit('success')
        }
    }
}
</script>

<style lang="scss">
.game-tips-msg-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
}

.background-image {
    width: 100vw;
    height: 100vh;
    display: block;
}

.goal-image, .goalkeeper-image, .football-image {
    position: fixed;
}

.goal-image {
    z-index: 1;
}

.goalkeeper-image {
    z-index: 2;
}

.football-image {
    z-index: 3;
}

// 足球旋转动画
.football-spinning {
    animation: football-spin 0.5s linear infinite;
}

@keyframes football-spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

// 礼花容器
.confetti-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    pointer-events: none;
    z-index: 10;
}

// 礼花粒子
.confetti-item {
    position: absolute;
    border-radius: 2px;
    pointer-events: none;
}
</style>