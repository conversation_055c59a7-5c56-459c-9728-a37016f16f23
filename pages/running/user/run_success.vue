<template>
	<view class="page">
		<view class="text-center">
			<icon type="success" size="100" color="#19be6b"></icon>
			<view class="color-content" style="padding-top: 30px;">
				<text class="font18">{{ Math.floor(distance / 10) / 100 }}</text>
				<text class="color-sub">km</text>
			</view>
			<view class="color-sub pt15">运动数据提交成功</view>
		</view>

        <view v-if="!share_success" class="flex-all-center" style="padding-top: 10vh;">
            <view class="big-button color-white bg-success text-center" @click="shareToWeRun">
                分享到微信运动
            </view>
        </view>
        
		<!--<view class="flex-all-center" style="padding-top: 10vh;">
			<navigator class="back-btn color-white bg-success text-center" open-type="navigateBack">
                返回{{ id ? '活动' : ''}}
            </navigator>
		</view>-->
		<view class="flex-all-center">
			<!--<navigator class="min-route text-center color-sub font14 p10" open-type="navigateBack">
                返回{{ id ? '活动' : ''}}
            </navigator>-->
			<view class="min-route text-center color-sub font14 p10" hover-class="navigator-hover" 
                  @click="lookMyRunRecord">运动记录</view>
		</view>
	</view>
</template>

<script>
const app = getApp()
	export default {
		data() {
			return {
                distance: 0,
				id: '',
                share_success: false
			}
		},

		onLoad(e) {
			this.id = e.id
			this.distance = Number(e.meter)
		},
        
        
		methods: {

            lookMyRunRecord() {
                const pages = this.$uni.pages()
                const recordRoute = 'pages/running/user/run_list'
                const index = pages.findIndex(page => page.route === recordRoute && page.options.myself)
                if (index !== -1) return this.$uni.navigateBack(pages.length - index - 1)
                
                let url = `/pages/running/user/run_list?myself=1`
                if (this.id) url += `&id=${this.id}`
                this.$uni.redirectTo(url)
            },

            shareToWeRun() {
                const distance = this.distance
                // distance	运动距离，单位：米。 有效值1-100000，需为整数。 超过100000米无法分享
                if (distance > 100000) return this.$uni.showToast('数据异常，无法分享')
                
                const record = {
                    typeId: 3001,
                    distance
                }

                this.$wx.shareToWeRun({
                    recordList: [record],
                    success: () => {
                        this.share_success = true
                        this.$uni.showToast('分享成功', 'success')
                    }
                })
            },
		}
	}
</script>

<style>
.page {
	padding-top: 10vh;
}
.big-button {
	width: 200px;
	line-height: 44px;
	border-radius: 5px;
}
.min-route {
    width: 70px;
}
</style>
