<template>
    <view>
<!--        <image v-if="mapImage" :src="mapImage" style="width: 100vw; height: 100vw;"/>-->

        <map
            id="map"
            class="map"
            :longitude="map_center.longitude"
            :latitude="map_center.latitude"
            :polyline="map_data.running_map.polyline"
            :scale="map_scale"
            :enable-satellite="enable_satellite"
        ></map>
        
        <view v-if="!in_playback" class="map-type-switch flex-all-center" @click="enableSatelliteChange">
            <text :class="'iconfont color-white font28 icon-' + (enable_satellite ? 'map-1' : 'earth')"></text>
        </view>
        
        <view v-if="!in_playback" class="play-icon flex-all-center" @click="drawRunningTrack">
            <text class="iconfont color-white font28 icon-play"></text>
        </view>

        <view class="run-data">
            <view v-if="map_data.calories" class="clear clearfix pr10" 
                  style="position: relative; top: -10px;">
                <view class="color-sub fr font14" @click="showExplain('calories')">
                    <uni-icons type="fire" size="14" color="#80848f"/>
                    <text style="padding-right: 2px;">{{ map_data.calories || 0 }}</text>
                    <text class="font12">千卡</text>
                </view>
            </view>
            
            <view class="flex-row">
                <view class="run-data-item">
                    <view class="font14 color-sub">用时</view>
                    <view class="value-item font24">{{ map_data.seconds ? seconds2time(map_data.seconds) : 0 }}
                    </view>
                </view>

                <view class="run-data-item">
                    <view class="font14 color-sub">距离(公里)</view>
                    <view class="value-item font28">{{ map_data.meter ? metre2kilometre(map_data.meter) : 0 }}
                    </view>
                </view>

                <view class="run-data-item" @click="showExplain('speed')">
                    <view class="font14 color-sub">配速</view>
                    <view class="value-item font24">{{ map_data.pace ? seconds2time(map_data.pace) : 0 }}</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'

export default {
    data() {
        return {
            map_data: {},
            map_center: {
                longitude: 116.397480,
                latitude: 39.908810
            },
            map_scale: 16,
            // mapImage: '',
            enable_satellite: uni.getStorageSync('run_enable_satellite_' + app.globalData['who']) || false,
            in_playback: false
        }
    },

    onLoad(e) {
        // this.id = e.id
        // this.record_id = e.record_id

        login.uniLogin(err => {
            if (err?.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {
                    title: err['errTitle'],
                    success: () => uni.navigateBack()
                })
            }

            this.getMapData()
        })
    },


    methods: {
        showExplain(type) {
            this._utils.showRunExplain(type)
        },

        getMapData() {
            this.getOpenerEventChannel().on('runRecordData', runRecordData => {
                if (!runRecordData) return this.$uni.showModal('运动记录获取失败', {
                    success: () => uni.navigateBack()
                })
                this.map_data = runRecordData
                this.initMapData()
            })
        },

        initMapData() {
            const polyline = this.map_data.running_map.polyline
            // 循环polyline，删除points为空的项
            for (let i = 0; i < polyline.length; i++) {
                if (!polyline[i].points?.length) {
                    polyline.splice(i, 1)
                    i--
                }
            }

            if (!polyline?.length) return this.$uni.showModal('没有运动数据', {success: () => uni.navigateBack()})

            this.MapContext = wx.createMapContext('map')

            const points = []
            polyline.forEach(v => {
                if (v.points?.length) points.push(...v.points)
            })
            this.map_center = this._utils.getCenterLonLat(points)
            
            this.map_scale = this._utils.getMapScale(points)

            this.setMapMarkers(points)
        },

        setMapMarkers(points) {
            const icon_size = {
                width: 50,
                height: 50
            }

            this.MapContext.addMarkers({
                markers: [
                    {
                        id: 1,
                        latitude: points[0].latitude,
                        longitude: points[0].longitude,
                        iconPath: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/map-start.png',
                        ...icon_size
                    },
                    {
                        id: 2,
                        latitude: points[points.length - 1].latitude,
                        longitude: points[points.length - 1].longitude,
                        iconPath: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/map-end.png',
                        ...icon_size
                    }
                ]
            })
            

          /*  
            const api = 'https://apis.map.qq.com/ws/staticmap/v2/'
            const key = 'OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77'
            const center = `${this.map_center.latitude},${this.map_center.longitude}`
            const zoom = this.map_scale > 18 ? 18 : this.map_scale
            const size = '600*600'
            const maptype = 'roadmap'
            const markers = `size:small|icon:https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/map-end.png|${end_point_marker.latitude},${end_point_marker.longitude}`
            const path = 'color:0x53BA84|' + polyline.map(v => v.points.map(p => `${p.latitude},${p.longitude}`).join('|')).join('|')

            
            uni.request({
                method: 'GET',
                url: api,
                responseType: 'arraybuffer',
                data: {
                    key,
                    center,
                    zoom,
                    size,
                    maptype,
                    markers,
                    path
                },
                success: res => {
                    this.mapImage = 'data:image/png;base64,' + uni.arrayBufferToBase64(res.data)
                }
            })*/
        },

        drawRunningTrack() {
            const polyline = this.map_data.running_map?.polyline
            if (!polyline?.length) return
            
            const path = []
            polyline.forEach(v => path.push(...v.points))

            const markerId = 99999
            this.MapContext.addMarkers({
                markers: [{
                    id: markerId,
                    width: 32,
                    height: 32,
                    iconPath: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/map-run-location.png',
                    latitude: path[0].latitude,
                    longitude: path[0].longitude
                }]
            })
            
            this.$nextTick(() => {
                let play_seconds = 5
                if (this.map_data.seconds) {
                    play_seconds = this.map_data.seconds / 100
                    if (play_seconds < 3) play_seconds = 3
                    if (play_seconds > 30) play_seconds = 30
                }
                const duration = play_seconds * 1000
                
               
                // let moveFail = false

                this.runSwitchSatellite()
                
                this.MapContext.moveAlong({
                    markerId,
                    path,
                    autoRotate: false,
                    duration,
                    success: () => {
                        setTimeout(() => {
                            this.restoreSatelliteSet()
                            this.MapContext.removeMarkers({
                                markerIds: [markerId]
                            })
                        }, 1000)
                    },
                    fail: err => {
                        console.log('移动失败', err)
                        this.restoreSatelliteSet()
                        this.MapContext.removeMarkers({
                            markerIds: [markerId]
                        })
                        // moveFail = true
                    }
                })
                
                /*if (!moveFail) {
                    const oneStep = duration / path.length
                    let index = 0
                    const old_map_scale = this.map_scale
                    this.map_scale = 15
                    const old_map_center = JSON.parse(JSON.stringify(this.map_center))
                    
                    const interval = setInterval(() => {
                        this.map_center = path[index]
                        index++
                        if (index >= path.length - 1) {
                            setTimeout(() => {
                                this.map_scale = old_map_scale
                                this.map_center = old_map_center
                            }, 1000)
                            clearInterval(interval)
                        }
                    }, oneStep)
                }*/
            })
            
        },
        
        
        // 展示运动轨迹的时候切换为卫星地图
        runSwitchSatellite() {
            this.in_playback = true
            if (this.enable_satellite) return
            this.enable_satellite = true
        },
        // 展示完后切换回默认地图
        restoreSatelliteSet() {
            this.enable_satellite = uni.getStorageSync('run_enable_satellite_' + app.globalData['who']) || false
            this.in_playback = false
        },

        metre2kilometre: metre => Math.floor(metre / 10) / 100,
        
        seconds2time(seconds) {
            return this._utils.seconds2time(seconds)
        },


        enableSatelliteChange() {
            this.enable_satellite = !this.enable_satellite
            uni.setStorageSync('run_enable_satellite_' + app.globalData['who'], this.enable_satellite)
        }
    }
}
</script>

<style>
.map {
    width: 100vw;
    height: 100vh;
}

.map-type-switch, .play-icon {
    position: fixed;
    right: 10px;
    bottom: 170px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, .7);
}

.map-type-switch {
    bottom: 220px;
}

.run-data {
    position: fixed;
    bottom: 30px;
    left: 10px;
    z-index: 999;
    width: calc(100% - 20px);
    box-sizing: border-box;
    background-color: rgba(255, 255, 255, .9);
    border-radius: 5px;
    box-shadow: 0 0 10px #eee;
    padding: 20px 0;
}

.run-data-item {
    width: calc(100% / 3);
    text-align: center;
}

.value-item {
    height: 40px;
    line-height: 40px;
    font-weight: bold;
}
</style>
