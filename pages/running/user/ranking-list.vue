<template>
    <view class="page bg-background">

        <xwy-ad v-if="show_AD" :ad_type="3"></xwy-ad>

        <template v-if="top_rank_category_list.length > 1 && !team_id">
            <view class="type-bar flex-row bg-white">
                <view class="type-bar-item text-center font14"
                      :class="{'type-bar-item-active': item.id === type_id}"
                      v-for="item in top_rank_category_list" :key="item.id"
                      :style="{width: 100 / top_rank_category_list.length + '%'}"
                      @click="typeIdChange(item.id)">
                    {{ item.name }}
                </view>
            </view>

            <view style="height: 40px;"></view>
        </template>

        <view v-if="top_rank_text && top_rank_text.rules" class="font12 p10"
              style="color: #e19898;" @click="copyId">{{ top_rank_text.rules }}
        </view>

        <top-banner v-if="top_rank_banner.length" :list="top_rank_banner"></top-banner>

        <template v-if="searchOpen">
            <ranking-search :search_keyword.sync="search_keyword" @search="search"/>
        </template>

        <template v-if="podiumList.length">
            <ranking-podium :list="podiumList"/>
        </template>

        <view v-if="list.length && total_count" class="color-sub font14 text-center pt5">
            <template v-if="!show_AD">
                我的
                <template v-if="isTeamRanking">队伍</template>
                排名: {{ my_position_num === -1 ? '未上榜' : my_position_num }}
            </template>
            <text class="pl5" v-if="total_count">
                (共{{ total_count }}{{ isTeamRanking ? '队伍' : '人' }})
            </text>
        </view>

        <view class="list">
            <view v-for="(item, index) in list" :key="index">
                <xwy-ad v-if="show_AD && index === 0" :ad_type="66"></xwy-ad>

                <view v-if="!limit_show_num || index < limit_show_num" class="item flex-row bg-white"
                      @click="clickItem(item)">
                    <view class="index flex-all-center" @click.stop="copyUserid(item)">

                        <image class="top-3" v-if="index < 3"
                               :src="'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/no-' + (index + 1) + '.png'"/>
                        <view v-else class="color-sub font14">{{ index + 1 }}</view>
                    </view>

                    <view class="flex-all-center">
                        <view v-if="item.headimg">
                            <image mode="aspectFill" class="headimg" :src="item.headimg"/>
                        </view>
                    </view>

                    <view class="middle">
                        <view class="name color-title ellipsis">{{ item.nickname }}</view>
                        <view v-if="isTeamRanking" class="color-sub font12 pt5">查看队内排行榜</view>
                        <view v-if="item.other_info.length" class="color-sub font12 pt5">
                            <view v-for="info in item.other_info" :key="info">{{ info }}</view>
                        </view>
                    </view>

                    <view class="right flex-column flex-all-center color-red font14">
                        <view class="right-item text-right">
                            <view>
                                <text>{{ item.value }}</text>
                                <text class="font12" style="padding-left: 2px;">{{ unit }}</text>
                            </view>
                        </view>

                    </view>
                </view>

                <xwy-ad v-if="show_AD && index !== 0 && ((index + 1) % 10 === 0)" :ad_type="66"></xwy-ad>

            </view>
        </view>

        <view v-if="!list.length && !loading" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无排行数据</view>
        </view>

        <uni-load-more v-if="loading" status="loading"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && list.length > 5"
            status="noMore"
            :contentText="{contentnomore: '我是有底线的'}"
        ></uni-load-more>
        <uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'

export default {
    data() {
        return {
            id: '',
            loading: true,
            total_count: 0,
            top_rank_text: null,
            my_position_num: -1,
            list: [],
            load_page: 1,
            is_last_page: false,
            top_rank_banner: [],
            show_AD: false,
            popup_show: false,
            limit_show_num: null,
            top_rank_category_list: [],
            type_id: 15,
            team_id: null,
            top_rank_search: false,
            search_keyword: '',
            unit: 'km'
        }
    },

    computed: {
        isTeamRanking() {
            const teamTypes = [46]
            return teamTypes.includes(this.type_id)
        },
        
        isUserRanking() {
            const userTypes = [15, -1, -2]
            return userTypes.includes(this.type_id)
        },

        podiumList() {
            const {list, show_AD, unit} = this
            if (!list.length && show_AD) return []

            return this.list.slice(0, 3).map(item => ({
                headimg: item.headimg,
                nickname: item.nickname,
                value: item.value === 'hide' ? '' : `${item.value}${unit}`
            }))
        },

        searchOpen() {
            return this.top_rank_search && this.type_id === 15
        }
    },

    onLoad(e) {
        this.id = e.id
        if (e.team_id) {
            this.team_id = Number(e.team_id)
            if (e.team_name) this.$uni.setNavigationBarTitle(`${e.team_name}队内排行榜`)
        }
        this.$uni.showLoading()
        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.init()
        })
    },

    onPullDownRefresh() {
        this.reloadList().finally(() => uni.stopPullDownRefresh())
    },

    onReachBottom() {
        if (this.is_last_page || this.loading) return
        this.load_page++
        this.loadList()
    },

    methods: {
        async init() {
            await this.getActivityDetails()
            await this.getList()
            this.loading = false
            uni.hideLoading()
        },

        async fetchActivityDetails() {
            const details = app['globalData']['activity_detail']
            if (details && details.active_id === this.id) return details

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details',
                data: {
                    active_id: this.id
                }
            })
            return res?.['data']?.['active_details']
        },

        async getActivityDetails() {
            const details = await this.fetchActivityDetails()
            if (!details) {
                return await this.$uni.showModal('活动获取失败', {success: () => this.$uni.navigateBack()})
            }

            await this.setPageData(details)
        },

        async setPageData(details) {
            this.getTypeList(details)
            const rank_set = details.rank_set || {}
            if (!rank_set.closed_AD) this.show_AD = true
            if (rank_set.limit_show_num) this.limit_show_num = rank_set.limit_show_num
            if (rank_set.top_rank_search) this.top_rank_search = true

            const active = details.conf?.active || {}
            if (active.top_rank_banner?.length) this.top_rank_banner = active.top_rank_banner
        },

        getTypeList(details) {
            let list = [{id: 15, name: '个人排行榜'}]
            
            const {team_group_open, select_sex} = details?.rank_set || {}
            if (team_group_open) list.push({id: 46, name: '队伍排行榜'})
            if (select_sex) {
                const {man = '男', woman = '女'} = details?.conf?.active?.sex_label || {}
                list = list.concat([{id: -1, name: `${man}排行榜`}, {id: -2, name: `${woman}排行榜`}])
            }

            this.top_rank_category_list = list
        },

        search() {
            this.reloadList()
        },

        typeIdChange(id) {
            if (id === this.type_id) return
            this.search_keyword = ''
            this.type_id = id
            this.reloadList()
        },


        async loadList() {
            if (!this.loading && !this.is_last_page) {
                this.loading = true
                await this.getList()
                this.loading = false
            }
        },

        async reloadList() {
            this.load_page = 1
            this.loading = true
            await this.getList()
            this.loading = false
        },

        async getList() {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
                this.my_position_num = -1
                this.total_count = 0
            }

            const data = {
                active_id: this.id,
                top_rank_types: this.type_id,
                page: this.load_page,
                perpage: 20
            }
            if (this.team_id) data.team_id = this.team_id
            if (this.search_keyword) data.nickname = this.search_keyword
            
            // 查看性别排行榜，性别排行榜也是用个人排行榜，只是多穿个sex参数来筛选
            if (this.type_id < 0) {
                data.top_rank_types = 15
                data.sex = Math.abs(this.type_id)
            }

            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace res.data.top_rank_list.position_num */

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.step/top_rank_list',
                data
            })


            if (!res?.data) {
                this.is_last_page = true
                return
            }

            if (res.data.rank_types_text) this.top_rank_text = res.data.rank_types_text

            if (res.data.top_rank_list) {
                const _data = res.data.top_rank_list

                if (_data.position_num) this.my_position_num = res.data.top_rank_list.position_num

                const list = _data.list.data || []
                const new_list = this.listDataInit(list)

                this.list = [...this.list, ...new_list]
                this.is_last_page = _data.list.is_lastpage
                this.total_count = _data.list.total
            } else {
                this.is_last_page = true
            }
        },

        listDataInit(list) {
            return list.map(item => ({
                id: item.id,
                userid: item.userid || null,
                headimg: this.getHeadimg(item),
                nickname: this.getNickname(item),
                value: this.getValue(item),
                other_info: this.getItemOtherInfo(item)
            }))
        },

        getHeadimg(v) {
            if (this.isTeamRanking) return v.conf?.logo || ''
            return v.headimg || v.user_details?.headimg || ''
        },

        getNickname(v) {
            if (this.isTeamRanking) return v.name
            const must_submit = v.must_submit || v.user_details?.must_submit
            return must_submit?.[0]?.value || ' '
        },

        getValue(v) {
            if (this.isTeamRanking) return this.m2km(v['run_all_meter'] || v['run_avg_meter'])
            if (this.isUserRanking) return this.m2km(v['meter'])
            return 0
        },
        
        m2km: (m = 0) => Math.floor(m / 10) / 100,


        getItemOtherInfo(v) {
            if (this.isUserRanking) return [`总用时: ${this._utils.seconds2time(v.seconds)}`]
            return []
        },

        copyUserid(item) {
            const id = this.isTeamRanking ? item.id : item.userid
            this.$uni.setClipboardData(id.toString())
        },

        copyId() {
            this.$uni.setClipboardData(this.id)
        },

        clickItem(item) {
            const {id, nickname} = item

            if (this.isTeamRanking) return this.$uni.navigateTo(`./ranking-list?id=${this.id}&team_id=${id}&team_name=${nickname}`)
        }
    }
}
</script>

<style scoped lang="scss">
@import "@/pages/ranking-list/ranking-list.scss";

.type-bar {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;

    .type-bar-item {
        height: 40px;
        line-height: 38px;
    }

    .type-bar-item-active {
        border-bottom: 2px solid #5cadff;
        color: #5cadff;
    }
}
</style>
