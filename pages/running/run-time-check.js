import utils from '@/utils/utils'
import xwy_api from '@/utils/api/xwy_api'

const getServerCurrentTime = async () => {
    const res = await xwy_api.request({url: 'front.user.shop/server_time'})
    return res?.data?.['server_time'] || 0
}

export default {
    check: async (limit, list) => {
        if (!limit || !list?.length) return true
        
        const now = await getServerCurrentTime() * 1000
        
        const today = utils.unitTimeToDate(now).replace(/-/g, '/')
        
        return list.some(item => {
            const start_timeStamp = new Date(`${today} ${item.start_time}`).getTime()
            const end_timeStamp = new Date(`${today} ${item.end_time}`).getTime()
            return now >= start_timeStamp && now <= end_timeStamp
        })
    },
    
    getEndTime: async (list) => {
        const now = await getServerCurrentTime() * 1000
        const today = utils.unitTimeToDate(now).replace(/-/g, '/')
        
        return list.find(item => {
            const start_timeStamp = new Date(`${today} ${item.start_time}`).getTime()
            const end_timeStamp = new Date(`${today} ${item.end_time}`).getTime()
            item.end_timeStamp = end_timeStamp
            return now >= start_timeStamp && now <= end_timeStamp
        })?.end_timeStamp
    }
}