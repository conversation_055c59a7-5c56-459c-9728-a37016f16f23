<template>
    <view class="run-data-card">
        <view class="top clearfix clear">
            <view class="user-info fl flex-row">
                <view class="pr10">

                    <image class="heading-image" :src="headimg" mode="aspectFill"/>
                </view>
                <view>
                    <view class="color-title pt5">{{ nickname }}</view>
                    <view class="color-sub font12 pt5">{{ totalText }}</view>
                </view>
            </view>
            <view class="fr text-right">
                <view class="mileage color-success font-bold italic-text">
                    <text class="font34">{{ Math.floor(meter / 10) / 100 }}</text>
                    <text class="font18">km</text>
                </view>
                <view>
                    <text class="iconfont icon-walk color-border"></text>
                    <text class="color-sub font14 pl5">本次里程</text>
                </view>
            </view>
        </view>
        <view class="cut-off-rule"></view>
        <view class="bottom flex-kai">
            <view>
                <view class="pb5">
                    <text class="iconfont icon-time font14 color-border"></text>
                    <text class="font14 plr5 color-sub">运动时长</text>
                    <text class="font18 color-content italic-text font-bold">{{ time }}</text>
                </view>
                <view @click="showExplain('speed')">
                    <text class="iconfont icon-rocket font14 color-border"></text>
                    <text class="font14 plr5 color-sub">平均配速</text>
                    <text class="font18 color-content italic-text font-bold">{{ paceText }}</text>
                </view>
                <view v-if="calories || isMe" @click="whatAreCalories">
                    <uni-icons type="fire" size="14" color="#dddee1"/>
                    <text class="font14 plr5 color-sub">消耗热量</text>
                    <template>
                        <template v-if="calories">
                            <text class="font18 color-content italic-text font-bold">{{ calories }}</text>
                            <text class="color-sub italic-text font12" style="padding-left: 2px;">千卡</text>
                        </template>
                        <text class="color-sub font14" v-else>如何计算？</text>
                    </template>
                </view>
            </view>
            <view class="map-icon-container">
                <view v-if="showMapIcon" class="pb5" @click="$emit('clickMapIcon')">
                    <text class="iconfont icon-map-1 font24 color-sub"></text>
                </view>
            </view>
        </view>
        <view v-if="createTime || showDelete" class="clear clearfix">
            <view class="fr flex-row pt5">
                <view v-if="createTime" class="color-sub font12">{{ createTime }}</view>
                <view v-if="showDelete" class="color-sub font14 pl10" @click="$emit('deleteRecord')">删除</view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "run-data-card",
    emits: ['clickMapIcon', 'deleteRecord'],
    props: {
        isMe: {
            type: Boolean,
            default: false
        },
        headimg: {
            type: String,
            default: ''
        },
        nickname: {
            type: String,
            default: ''
        },
        totalText: {
            type: String,
            default: ''
        },
        meter: {
            type: Number,
            default: 0
        },
        seconds: {
            type: Number,
            default: 0
        },
        pace: {
            type: Number,
            default: 0
        },
        calories: {
            type: Number,
            default: 0
        },
        createTime: {
            type: String,
            default: ''
        },
        showMapIcon: {
            type: Boolean,
            default: false
        },
        showDelete: {
            type: Boolean,
            default: false
        }
    },
   
    computed: {
        time() {
            return this._utils.seconds2time(this.seconds)
        },
        paceText() {
            return this._utils.seconds2time(this.pace)
        }
    },

    methods: {
        showExplain(type) {
            this._utils.showRunExplain(type)
        },

        async whatAreCalories() {
            if (this.isMe && !this.calories) {
                const res = await this.$uni.showModal('跑步热量（大卡）= 体重（kg）× 距离（公里）× 1.036。前往 个人中心-体重管理 记录体重后，即可计算跑步消耗的热量。', {
                    title: '消耗热量',
                    confirmText: '记录体重',
                    showCancel: true
                })
                return res.confirm && this.$uni.navigateTo('/pages/weightHeight/user/details')
            }
            this.showExplain('calories')
        }
    }
}
</script>

<style lang="scss">
$headimg-size: 50px;
.heading-image {
    width: $headimg-size;
    min-width: $headimg-size;
    height: $headimg-size;
    border-radius: 50%;
    display: block;
}

.mileage {
    height: 40px;
    line-height: 40px;
}

.cut-off-rule {
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, 
        #82D086, 
        #A1D873, 
        #E6D969, 
        #E8CC61, 
        #E3AD4E, 
        #E37B30);
    margin: 10px 0;
}

.map-icon-container {
    display: flex;
    flex-direction: column-reverse;
}
</style>