<template>
    <view>
        <view class="form-item">
            <view class="top color-content">
                <view>每天运动时段</view>
                <view class="color-sub font12">(不在时段内不能开始和提交运动)</view>
            </view>
            <view class="bottom font16 pt10">
                <view v-for="(item, index) in dataList" class="time-item flex-row" :key="index">
                    <view class="time-text">{{ item.start_time }} - {{ item.end_time }}</view>
                    <view class="flex-row pl10">
                        <view class="set-icon flex-all-center" @click="editTime(item, index)">
                            <text class="iconfont icon-edit color-sub"></text>
                        </view>
                        <view class="set-icon flex-all-center" @click="dataList.splice(index, 1)">
                            <text class="iconfont icon-delete color-sub"></text>
                        </view>
                    </view>
                </view>

                <view class="flex-all-center">
                    <view class="p10 font14 color-light-primary" @click="addTime">添加运动时段</view>
                </view>
            </view>
        </view>

        <uni-popup ref="popup">
            <view class="set-popup bg-white">
                <view class="text-center pb10">运动时段设置</view>

                <uni-forms label-width="70" label-align="right">
                    <uni-forms-item label="开始时间:">
                        <view class="form-picker">
                            <picker-time v-if="popupData.start_time" :text="popupData.start_time"
                                         :time.sync="popupData.start_time"/>
                            <picker-time v-else :time.sync="popupData.start_time"/>
                        </view>
                    </uni-forms-item>
                    <uni-forms-item label="结束时间:">
                        <view class="form-picker">
                            <picker-time v-if="popupData.end_time" :text="popupData.end_time"
                                         :time.sync="popupData.end_time"/>
                            <picker-time v-else :time.sync="popupData.end_time"/>
                        </view>
                    </uni-forms-item>
                </uni-forms>

                <view class="confirm-button color-white bg-light-primary text-center"
                      hover-class="navigator-hover" @click="confirm">确定
                </view>

                <view class="flex-all-center">
                    <view class="p5 color-sub font12" @click="$refs.popup.close()">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "run-time-list-set",
    props: {
        list: {
            type: Array,
            default: () => []
        }
    },

    data() {
        return {
            dataList: [],
            popupData: {
                start_time: '00:00:00',
                end_time: '23:59:59'
            }
        }
    },

    watch: {
        list: {
            handler(val) {
                this.dataList = val
            },
            immediate: true,
            deep: true
        },

        dataList: {
            handler(val) {
                this.$emit('update:list', val)
            },
            deep: true
        }
    },

    methods: {
        addTime() {
            this.editIndex = -1

            this.popupData = {
                start_time: '00:00:00',
                end_time: '23:59:59'
            }

            this.$refs.popup.open()
        },

        editTime(item, index) {
            this.editIndex = index

            this.popupData.start_time = item.start_time
            this.popupData.end_time = item.end_time

            this.$refs.popup.open()
        },

        confirm() {
            const {start_time, end_time} = this.popupData
            if (!start_time) return this.$uni.showToast('请选择 开始时间')
            if (!end_time) return this.$uni.showToast('请选择 开始时间')

            const today = this._utils.getDay()
            const start_timeStamp = new Date(`${today} ${start_time}`).getTime()
            const end_timeStamp = new Date(`${today} ${end_time}`).getTime()

            if (start_timeStamp > end_timeStamp) return this.$uni.showToast('开始时间不能大于结束时间')

            if (this.dataList.some(item => item.start_time === start_time && item.end_time === end_time)) {
                return this.$uni.showToast('该时段已存在')
            }

            if (this.editIndex === -1) {
                this.dataList.push({start_time, end_time})
            } else {
                this.$set(this.dataList, this.editIndex, {start_time, end_time})
            }

            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.time-item {
    padding: 2px 0;
    $size: 32px;

    .time-text {
        line-height: $size;
    }

    .set-icon {
        background-color: #f8f8f8;
        width: $size;
        height: $size;
        border-radius: 50%;
        margin: 0 5px;
    }
}

.set-popup {
    padding: 10px;
    border-radius: 10px;
    width: 80vw;
    max-width: 400px;

    .form-picker {
        border: 1px solid #e5e5e5;
        border-radius: 4px;
        height: 34px;
        line-height: 34px;
        padding: 0 10px;
    }

    .form-tips {
        position: relative;
        top: -5px;
    }

    .confirm-button {
        width: 120px;
        line-height: 40px;
        border-radius: 20px;
        margin: 20px auto 0;
    }
}
</style>