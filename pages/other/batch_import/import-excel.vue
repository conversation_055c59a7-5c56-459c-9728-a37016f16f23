<template>
    <view>
        <view class="flex-all-center" style="padding-top: 30vh;">
            <view class="text-center p10" @click="changeFile">
                <text class="iconfont icon-import color-divider" style="font-size: 150px;"></text>
                <view class="color-sub pt10">选择需要导入的表格文件</view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import {pinyin} from 'pinyin-pro'

export default {
    onLoad(options) {
        this.id = options.id
        this.excel_js()
    },
    methods: {
        excel_js() {
            const {staticOld, static_url} = app.globalData?.shop_info?.shop_set || {}
            const defaultUrl = 'https://prod-0g479j60184f120d-1304148175.tcloudbaseapp.com/'
            let staticUrl = staticOld || static_url || defaultUrl

            // 不是HTTPS的域名浏览器会阻止
            if (!staticUrl.startsWith('https://')) staticUrl = defaultUrl

            const script = document.createElement('script')
            script.src = `${staticUrl}web/wx-cloud-api/lib/excel/xlsx.core.min.js`
            document.body.appendChild(script)
            this.changeFile()
        },


        changeFile() {
            uni.chooseFile({
                count: 1,
                extension: ['.xlsx', '.xls', '.xltx'],
                success: res => {
                    if (res['errMsg'] !== 'chooseFile:ok') {
                        this.$uni.showModal('获取文件失败！')
                        return
                    }
                    let file = res.tempFiles[0]
                    this.uploadFile(file)
                },
            })
        },


        uploadFile(file) {
            this.$uni.showLoading('导入中...')


            let reader = new FileReader()
            reader.readAsBinaryString(file)
            reader.onload = (e) => {
                const data = e.target.result
                const zzexcel = window['XLS'].read(data, {type: 'binary'})
                const result = []
                for (let i = 0; i < zzexcel['SheetNames'].length; i++) {
                    const newData = window['XLS']['utils']['sheet_to_json'](zzexcel['Sheets'][zzexcel['SheetNames'][i]])
                    result.push(...newData)
                }

                this.excel2data(result)
            }
        },

        excel2data(result) {
            if (!result?.length) {
                uni.hideLoading()
                this.$uni.showModal('没有获取到需要导入的名单，请重新导入')
                return
            }

            const user_list = []
            
            for (let i = 0; i < result.length; i++) {
                const item = result[i]
                const must_submit = []
                const must_submit_arr = Object.entries(item)
                
                const team_tag = '队伍名称'
                
                // 从第三个开始，应为第一和第二是用户名和密码
                for (let j = 3; j < must_submit_arr.length; j++) {
                    const key = must_submit_arr[j][0]
                    
                    // 队伍单独提交不用加到实名信息里面
                    if (key !== team_tag) {
                        const value = must_submit_arr[j][1]
                        must_submit.push({
                            name: pinyin(key, {toneType: 'none', type: 'array'}).join('_'),
                            title: key,
                            types: 1,
                            value: value || '',
                            rules: 0
                        })
                    }
                }
                const data = {
                    username: item['用户名'] || '',
                    password: item['密码'] || '',
                    must_submit
                }
                if (item[team_tag]) data.team_name = item[team_tag]
                user_list.push(data)
            }
            
            this.create_user(user_list)
        },

        async create_user(user_list) {
            const res = await this.xwy_api.request({
                url: 'front.user.shop.user_list.batch_user/batch_create_user',
                data: {
                    types: 3,
                    active_id: this.id,
                    user_list: this._utils.base64['encode'](JSON.stringify(user_list))
                }
            })

            uni.hideLoading()

            const title = res?.status ? '导入成功' : '导入失败'
            uni.showModal({
                title,
                content: res?.info || title,
                showCancel: false,
                success: () => {
                    uni.navigateBack()
                }
            })
        },
    }
}
</script>

<style>

</style>
