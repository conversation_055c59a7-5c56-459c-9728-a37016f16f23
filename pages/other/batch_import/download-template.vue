<template>
    <view>
        <view class="flex-all-center" style="padding-top: 30vh;">
            <view class="text-center p10" @click="download">
                <text class="iconfont icon-export-excel color-divider" style="font-size: 150px;"></text>
                <view class="color-sub pt10">模版下载</view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()

export default {
    onLoad(options) {
        this.id = options.id
        this.excel_js()
    },
    methods: {
        excel_js() {
            const {staticOld, static_url} = app.globalData?.shop_info?.shop_set || {}
            const defaultUrl = 'https://prod-0g479j60184f120d-1304148175.tcloudbaseapp.com/'
            let staticUrl = staticOld || static_url || defaultUrl

            // 不是HTTPS的域名浏览器会阻止
            if (!staticUrl.startsWith('https://')) staticUrl = defaultUrl

            const script = document.createElement('script')
            script.src = `${staticUrl}web/wx-cloud-api/lib/excel/xlsx.core.min.js`
            document.body.appendChild(script)

            const timeout = setTimeout(() => {
                this.download()
                clearTimeout(timeout)
            }, 1000)
        },

        async download() {
            this.$uni.showLoading('下载中...')

            let activity_detail = app.globalData?.['activity_detail']
            if (!activity_detail) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.id
                    }
                })

                activity_detail = res.data.active_details
                app.globalData.activity_detail = activity_detail
            }

            const tHeader = ['序号', '用户名', '密码']
            const {rank_set, conf} = activity_detail || {}
            const {active, must_submit} = conf || {}

            if (rank_set?.['team_group_open'] && !active?.closed_team) tHeader.push('队伍名称')

            if (must_submit?.length) must_submit.forEach(v => tHeader.push(v.title))

            // 生成需要导出的数据
            let data = [
                tHeader,
                // [ '张三', 20, '男' ],
                // [ '李四', 22, '女' ],
                // [ '王五', 25, '男' ]
            ]
            // 将数据转为xlsx的workbook
            const wb = window['XLS']['utils']['book_new']()
            const ws = window['XLS']['utils']['aoa_to_sheet'](data)
            window['XLS']['utils']['book_append_sheet'](wb, ws, 'Sheet1')

            // 将workbook转为二进制流
            let wbout = window['XLS'].write(wb, {type: 'array', bookType: 'xlsx'})

            // 构造下载链接并自动下载文件
            let blob = new Blob([wbout], {type: 'application/octet-stream'})
            let link = document.createElement('a')
            let href = window.URL.createObjectURL(blob)
            link.href = href
            link.download = '活动用户名单导入模板.xlsx'
            document.body.appendChild(link)
            link.click() //触发下载
            document.body.removeChild(link) // 下载完成后删除链接
            window.URL.revokeObjectURL(href) // 释放资源

            uni.hideLoading()
            uni.navigateBack()
        }
    }
}
</script>

<style>

</style>
