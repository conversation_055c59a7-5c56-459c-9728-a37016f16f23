<template>
	<view>
		<view v-if="error_msg" class="error text-center" style="padding-top: 15vh;">
			<text class="iconfont icon-info color-warning" style="font-size: 100px;"></text>
			<view class="color-content">{{error_msg}}</view>
		</view>

		<view v-else>
			<web-view :src="url" @message="getMessage"></web-view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				url: '',
				error_msg: ''
			}
		},
		onShareAppMessage() {
			return {}
		},
		onLoad(e) {
			if (!e.url) {
				this.error_msg = '请指定webvie地址'
				return false
			}
			this.url = e.url
		},
        
        methods: {
            getMessage(event) {
                const data = event?.detail?.data?.[0]?.data || null
                this.getOpenerEventChannel?.()?.emit?.('getMessage', data)
            }
        }
	}
</script>

<style>

</style>
