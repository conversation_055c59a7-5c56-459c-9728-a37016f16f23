<template>
	<view class="page">
		<view class="p10 color-content" v-html="text"></view>
		<view v-if="customer_service_wechat.length" class="p10">
			<view class="color-title">客服微信：</view>
			<view
				class="ptm5"
				v-for="(item, index) in customer_service_wechat"
				:key="index"
				@click="copy(item, true)"
			>
				<text class="color-content">{{item}}</text>
				<text class="iconfont icon-copy color-sub pl5"></text>
			</view>
		</view>

		<view v-if="tel" class="p10">
			<view class="color-title">客服电话：</view>
			<view class="ptm5" @click="makePhoneCall(tel)">
				<text class="color-content">{{tel}}</text>
				<text class="iconfont icon-make-call color-sub pl5"></text>
			</view>
		</view>

		<!-- #ifdef MP-WEIXIN -->
		<view v-if="!loading" class="contact-btn-view flex-all-center pt15 bg-white">
			<button
				class="contact-btn bg-green color-white"
				open-type="contact"
        @click="copy('积分', false)"
			>{{customer_button.text || '联系客服'}}</button>
		</view>
		<!-- #endif -->

	</view>
</template>

<script>
	const app = getApp();
	import login from '../../utils/api/login.js'
	import xwy_api from '../../utils/api/xwy_api.js'

	export default {
		data() {
			return {
				loading: true,
				text: '',
				customer_service_wechat: [],
				tel: '',
				customer_button: {}
			}
		},
		onShareAppMessage() {
			return {}
		},
		onLoad() {
			uni.showLoading({
				mask: true
			})
			login.uniLogin(err => {
				if (err && err.errMsg) {
					uni.hideLoading()
					uni.showModal({
						title: err.errTitle || '提示',
						content: err.errMsg,
						showCancel: false
					})
					return false
				}


				this.getContactData()
			})
		},
		methods: {
			getContactData() {
				xwy_api.ajax({
					url: 'front.flat.sport_step.system/customer_page_show',
					data: {
						access_token: app.globalData.access_token
					},
					success: res => {
						uni.hideLoading();
						this.loading = false;
						console.log('客服页面内容设置', res);

						if (!res || !res.data || !res.data.customer_set) return false
						const data = res.data.customer_set
						if (data.customer_button) this.customer_button = data.customer_button
						if (data.customer_service_wechat) this.customer_service_wechat = data.customer_service_wechat
						if (data.tel) this.tel = data.tel
						if (data.text) this.text = data.text
					}
				})

			},

			copy(data, showTips = false) {
				if (typeof data !== "string") data = data.toString()
				uni.setClipboardData({
					data,
					success() {
						if (showTips) {
              uni.showToast({
                title: '复制成功',
                icon: 'none'
              })
            } else {
                uni.hideToast()
            }
					}
				})
			},

			makePhoneCall(phoneNumber) {
				if (typeof phoneNumber !== "string") phoneNumber = phoneNumber.toString()
				uni.makePhoneCall({
				    phoneNumber
				})
			}
		}
	}
</script>

<style scoped>
	.page {
		padding-bottom: 100px;
	}
	.contact-btn-view {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100vw;
		padding: 20px;
		box-sizing: border-box;
	}
	.contact-btn {
		height: 40px;
		line-height: 40px;
		border-radius: 20px;
		width: 200px;
		font-size: 16px;
	}
	.contact-btn::after {
		border: none;
	}
</style>
