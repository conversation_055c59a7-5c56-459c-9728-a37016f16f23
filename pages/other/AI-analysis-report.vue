<template>
    <view class="page bg-background">
        <template v-if="report_content">
            <view class="report-title bg-white clear clearfix">
                <view class="pt10 pl10 pr10 color-title">{{ report_title }}</view>
                <view class="fr flex-row">
                    <view class="fr p10" @click="copyReport">
                        <text class="iconfont icon-copy color-disabled font14"></text>
                        <text class="color-sub font14" style="padding-left: 2px;">复制报告</text>
                    </view>
                    <view v-if="type === 'user_step'" class="fr p10" @click="selectUserStepMonthPopupShow">
                        <text class="color-light-primary">切换月份</text>
                        <uni-icons type="forward" size="16" color="#5cadff"/>
                    </view>
                </view>
            </view>


            <view class="report-content color-content">

                <text span="nbsp" :user-select="true">{{ report_content }}</text>
            </view>

            <template v-if="output_complete">
                <xwy-ad :activity_id="active_id" :ad_type="66"/>
                <xwy-ad :activity_id="active_id" :ad_type="3"/>
            </template>

            <view class="report-content-bottom p10"></view>
        </template>

        <view v-if="get_loading" class="text-center" style="padding-top: 30vh;">
            <load-ani width="15px" margin="5px"/>
            <view class="color-sub font12 pt5">AI努力分析中</view>
        </view>

        <uni-popup ref="user-step-select-month" :is-mask-click="false">
            <view class="user-step-select-month-popup">
                <view class="text-center pb10">请选择需要分析的月份</view>
                <view class="pt10">

                    <picker :range="user_step_month_options" :value="user_step_month_options_index"
                            range-key="label" @change="userStepMonthChange">
                        <view class="user-step-select-month-picker flex-kai">
                            <view>{{ user_step_month_options[user_step_month_options_index].label }}</view>
                            <uni-icons type="forward" size="16" color="#80848f"/>
                        </view>
                    </picker>

                    <view class="pt10" style="min-height: 80px;">
                        <view v-if="user_step_month_value === 'user-set'">
                            <uni-easyinput type="number" :maxlength="2" v-model="user_step_month"
                                           placeholder="请输入需要分析的月数"/>
                            <view class="color-sub font12 pt5">
                                请输入需要分析的月数，如2代表最近2个月，3代表最近3个月，如此类推，最大支持最近60个月。
                            </view>
                        </view>
                    </view>
                </view>


                <view class="user-step-select-month-popup-confirm-button bg-primary color-white text-center"
                      hover-class="navigator-hover" @click="userStepSelectMonthConfirm">
                    AI生成报告
                </view>

                <view class="flex-all-center">
                    <view class="p10 color-sub font14" @click="userStepSelectMonthCancel">取消</view>
                </view>
            </view>
            <view class="flex-center pt10" style="min-height: 120px;">
                <view style="width: 90vw;">
                    <xwy-ad :activity_id="active_id" :ad_type="66"/>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>

export default {
    data() {
        return {
            type: '', // active: 活动分析报告  user_step: 用户步数分析报告
            active_id: '',
            get_loading: false,
            report_content: '',
            user_step_month_options: [
                {value: '', label: `本月 (${new Date().getMonth() + 1}月)`},
                {value: 3, label: '近3个月'},
                {value: 6, label: '近半年'},
                {value: 12, label: '近1年'},
                {value: 24, label: '近2年'},
                {value: 36, label: '近3年'},
                {value: 'user-set', label: '自定义'},
            ],
            user_step_month_value: '',
            user_step_month: '',
            user_step_report_month: '',
            output_complete: false
        }
    },

    computed: {
        user_step_month_options_index() {
            const {user_step_month_value: value, user_step_month_options: options} = this
            const index = options.findIndex(item => item.value === value)
            return index === -1 ? options.length - 1 : index
        },

        report_title() {
            if (this.type === 'active') return '健步走活动AI分析报告'

            if (this.type === 'user_step') {
                const month = this.user_step_report_month

                let month_description = ''

                switch (true) {
                    case month === '':
                        month_description = '本月'
                        break
                    case month === 6:
                        month_description = '近半年'
                        break
                    case month % 12 === 0:
                        month_description = `近${month / 12}年`
                        break
                    default:
                        month_description = `近${month}个月`
                }

                return `${month_description}运动步数AI分析报告`
            }
        }
    },


    onLoad(params) {
        this.paramsHandle(params)

        this.$uni.showLoading('加载中...')

        this.$login.uniLogin(err => {
            uni.hideLoading()
            if (err && err.errMsg) return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})

            this.init()
        })
    },

    onUnload() {
        this.clearOutputReportContentTimeout()
    },


    methods: {
        paramsHandle(params) {
            this.type = params.type
            if (params.active_id) this.active_id = params.active_id
            this.setNavigationBarTitle(params.type)
        },

        setNavigationBarTitle(type) {
            const options = {
                active: '活动AI分析报告',
                user_step: '运动步数AI分析报告'
            }
            if (options[type]) this.$uni.setNavigationBarTitle(options[type])
        },

        init() {
            const options = {
                active: () => this.getReport(),
                user_step: () => this.selectUserStepMonthPopupShow()
            }

            options[this.type]?.()
        },

        selectUserStepMonthPopupShow() {
            this.$refs['user-step-select-month'].open()
        },

        userStepMonthChange(e) {
            const index = Number(e.detail.value)
            const value = this.user_step_month_options[index].value
            this.user_step_month_value = value
            this.user_step_month = value === 'user-set' ? '' : value
        },

        userStepSelectMonthConfirm() {
            const month = Math.floor(this.user_step_month)
            if (isNaN(month) || month < 0 || month > 60) return this.$uni.showToast('请输入正确的月数')

            this.$refs['user-step-select-month'].close()
            this.getReport()
        },

        userStepSelectMonthCancel() {
            if (this.report_content) return this.$refs['user-step-select-month'].close()
            this.$uni.navigateBack()
        },


        async getReport() {
            const api_config = {
                active: {
                    url: 'front.flat.sport_step.total_active/active_total_ai',
                    data: {
                        active_id: this.active_id
                    }
                },
                user_step: {
                    url: 'front.flat.sport_step.step/total_user_mobile_step_report',
                    data: {
                        month: this.user_step_month
                    }
                }
            }

            this.report_content = ''
            this.output_complete = false

            this.get_loading = true
            const res = await this.xwy_api.request(api_config[this.type])
            this.get_loading = false

            const errorModal = content => {
                this.$uni.showModal(content, {
                    showCancel: true,
                    confirmText: '重新生成',
                    cancelText: '返回',
                    success: res => res.confirm ? this.getReport() : this.$uni.navigateBack()
                })
            }

            if (res?.status !== 1) return errorModal(res?.info || '报告生成失败')
            const report_contents = res?.data?.['AI_res']?.content
            if (!report_contents?.length) return errorModal('报告生成失败')
            const report_content = report_contents.join('\n\n').replace(/#{1,6} /g, '')
            // this.report_content = report_content

            this.user_step_report_month = this.user_step_month
            this.outputReportContent(report_content)
        },

        outputReportContent(content) {
            this.report_content = ''

            const all_time = 10 * 1000
            let one_word_time = all_time / content.length
            if (one_word_time < 20) one_word_time = 20
            if (one_word_time > 50) one_word_time = 50

            const output = index => {
                index ||= 0
                this.report_content += content[index]
                this.scrollToReportContentBottom()
                this.outputReportContentTimeout = setTimeout(() => {
                    index === content.length - 1 ? this.outputComplete() : output(index + 1)
                }, one_word_time)
            }

            output(0)
        },

        outputComplete() {
            this.clearOutputReportContentTimeout()
            this.output_complete = true
            
            // 广告组件加载需要时间，等广告加载完以后(1秒后，有可能一秒后还没加载出来)滚动到最底部
            setTimeout(() => this.scrollToReportContentBottom(), 1000)
        },

        clearOutputReportContentTimeout() {
            clearTimeout(this.outputReportContentTimeout)
            this.outputReportContentTimeout = null
        },

        scrollToReportContentBottom() {
            this.$nextTick(() => {
                uni.pageScrollTo({
                    selector: '.report-content-bottom',
                    duration: 0
                })
            })
        },

        copyReport() {
            this.$uni.setClipboardData(`${this.report_title}\n\n${this.report_content}`, '已复制')
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    padding-top: 80px;
    box-sizing: border-box;
}

.report-title {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 9;
    width: 100vw;
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .report-title {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }
}

/* #endif */

.report-content {
    padding: 10px 10px 30px;
}

.user-step-select-month-popup {
    width: 90vw;
    max-width: 400px;
    padding: 20px;
    border-radius: 10px;
    background-color: #fff;
    box-sizing: border-box;

    .user-step-select-month-picker {
        border: 1px solid #DCDFE6;
        border-radius: 4px;
        line-height: 34px;
        padding: 0 10px;
        color: #333;
    }

    .user-step-select-month-popup-confirm-button {
        margin: 30px auto 0;
        width: 200px;
        line-height: 44px;
        border-radius: 22px;
    }
}
</style>