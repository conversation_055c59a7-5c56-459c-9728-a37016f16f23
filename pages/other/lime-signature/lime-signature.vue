<template>
    <view class="signature flex-kai">
        <view class="bottom-buttons">
            <view class="bottom-button clear-button color-sub" @tap="clear('clear')">
                <text>清</text>
                <text>空</text>
            </view>
            <view class="bottom-button color-light-primary" @tap="complete('save')">
                <text>完</text>
                <text>成</text>
            </view>
        </view>


        <view class="signature-area bg-background">
            <l-signature disableScroll ref="signatureRef" :penColor="penColor" :penSize="penSize"
                         :minLineWidth="2" landscape></l-signature>
        </view>
        <view class="top-title flex-all-center">
           <view class="text-center">
                <view class="word" v-for="(word, index) in '请在灰色区域中签名'" :key="index">{{ word }}</view>
           </view>
        </view>
    </view>
</template>

<script>
import lSignature from './components/l-signature/l-signature.vue'

export default {
    components: {
        lSignature
    },
    data() {
        return {
            penColor: '#000000',
            penSize: 16,
            show: false
        }
    },

    onLoad(params) {
        if (params.color) this.penColor = params.color
        if (params.size) this.penSize = Math.floor(params.size)
    },

    methods: {
        clear() {
            this.$refs.signatureRef.clear()
        },

        complete() {
            this.$refs.signatureRef.canvasToTempFilePath({
                quality: 0.8,
                success: res => {
                    this.getOpenerEventChannel().emit('complete', res.tempFilePath)
                    this.$uni.navigateBack()
                    /*uni.getImageInfo({
                        src: res.tempFilePath,
                        success(res) {
                            console.log('Temp res', res)
                        }
                    })*/
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.signature {
    .bottom-buttons {
        display: flex;
        flex-direction: column;
        width: 50px;
        height: 100vh;

        .bottom-button {
            flex-grow: 1;
            flex-shrink: 0;
            margin: 0;
            text-align: center;
            text-decoration: none;
            display: flex;
            align-content: center;
            justify-content: center;
            flex-direction: column;

            &.clear-button {
                border-bottom: 1px solid #eee;
                box-sizing: border-box;
            }

            text {
                transform: rotate(90deg);
            }
        }
    }

    .signature-area {
        width: calc(100vw - 110px);
        height: 100vh;
    }

    .top-title {
        width: 60px;
        height: 100vh;

        .word {
            transform: rotate(90deg);
        }
    }
}
</style>