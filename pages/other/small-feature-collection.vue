<template>
    <view>
        <view class="list flex-row flex-wrap">
            <navigator class="item" v-for="item in list" :key="item.name" :url="item.path">
                <image class="item-image" :src="item.image" mode="aspectFill"/>
            </navigator>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            list: []
        }
    },

    onLoad() {
        this.setList()
    },

    methods: {
        setList() {
            const list = [
                {
                    title: '运动中心',
                    name: 'sport-center',
                    path: '/pages/sports-center/record-list'
                },
                {
                    title: '跑步',
                    name: 'running',
                    path: '/pages/running/user/run_list'
                },
                {
                    title: '体重管理',
                    name: 'weight',
                    path: '/pages/weightHeight/user/details'
                },
                {
                    title: '物品管理',
                    name: 'item-storage',
                    path: '/pages/item-storage/item/list'
                },
                {
                    title: '健康管理',
                    name: 'health',
                    path: '/pages/health-management/measure-record'
                }
            ]

            list.forEach(item => {
                item.image = `https://7072-prod-0g479j60184f120d-**********.tcb.qcloud.la/public/img/jbz_xcx/small-feature-collection/${item.name}.png`
            })

            this.list = list
        }
    },
    
    onShareAppMessage() {
        return {
            title: '',
            path: '/pages/other/small-feature-collection'
        }
    },
}
</script>

<style lang="scss">
.list {
    padding: 1px 15rpx;

    .item {
        margin: 15rpx;
        overflow: hidden;

        .item-image {
            width: calc((750rpx - 15rpx * 2) / 2 - 30rpx);
            height: calc(calc((750rpx - 15rpx * 2) / 2 - 30rpx) * 204 / 430);
            display: block;
            border-radius: 10px;
        }
    }
}
</style>