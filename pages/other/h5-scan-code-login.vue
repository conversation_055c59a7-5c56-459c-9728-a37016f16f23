<template>
    <view>
        <view v-if="result.status">
            <view class="result text-center">
                <view v-if="result.status">
                    <uni-icons v-if="result.status === 'success'" type="checkbox-filled" size="150" color="#23C171"/>
                    <uni-icons v-if="result.status === 'fail'" type="clear" size="150" color="#dddee1"/>
                </view>
                <view v-if="result.info" class="color-sub">{{ result.info }}</view>
            </view>

            <view class="big-button bg-light-primary color-white text-center"
                  hover-class="navigator-hover" @click="toIndex">进入小程序
            </view>
            <view class="flex-all-center">
                <view class="color-sub font14 p10" @click="exitMiniProgram">退出</view>
            </view>
        </view>
        <view v-else>
            <view class="login-confirm text-center">
                <text class="iconfont icon-screen color-light-primary"></text>
                <view class="color-sub">web端请求登录</view>
            </view>
            
            <view class="big-button bg-light-primary color-white text-center"
                  hover-class="navigator-hover" @click="scanConfirmLogin">授权登录
            </view>
            <view class="flex-all-center">
                <view class="color-sub font14 p10" @click="exitMiniProgram">取消</view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            result: {
                status: '',
                info: ''
            }
        }
    },

    onLoad(params) {
        wx.hideHomeButton()
        this.$uni.showLoading('二维码识别中...')
        this.$login.uniLogin(() => {
            uni.hideLoading()
            this.getUuid(params.scene)
        })
    },

    methods: {
        getUuid(scene) {
            if (!scene || scene === 'undefined') return this.noUUID()
            const scene_str = decodeURIComponent(scene)
            const uuid = this._utils.getUrlParams('uuid', scene_str)
            if (!uuid) return this.noUUID()

            this.uuid = uuid

        },

        async scanConfirmLogin() {
            this.$uni.showLoading('登录中...')
            const res = await this.xwy_api.request({
                url: 'front.user.login/scan_confirm_login',
                data: {
                    uuid: this.uuid
                }
            })

            if (!res?.status) return this.setResult('fail', res?.info || '登录失败')

            this.setResult('success', '登录成功')
        },

        noUUID() {
            this.setResult('fail', '请扫登录二维码')
        },

        exitMiniProgram() {
            wx.exitMiniProgram({
                success: res => console.log('退出成功', res),
                fail: err => {
                    console.log('退出失败', err)
                    this.toIndex()
                }
            })
        },

        toIndex() {
            this.$uni.redirectTo('/pages/index/index')
        },
        
        setResult(status, info) {
            uni.hideLoading()
            this.result = {
                status,
                info
            }
        }
    }
}
</script>

<style lang="scss">
.result, .login-confirm {
    padding: 50px 20px 20vh;
}

.login-confirm {
    .iconfont {
        font-size: 150px;
    }
}

.big-button {
    margin: 0 auto;
    width: 250px;
    line-height: 44px;
    border-radius: 5px;
}
</style>