<template>
    <view class="page bg-background">
        <view v-if="count" class="bg-white flex-kai">
            <view class="p10 color-sub font14">共邀请了{{ count }}位好友</view>
            <view v-if="showShare" class="p10 color-primary" @click="showActiveSharePopup">
                <text>邀请好友</text>
                <uni-icons type="forward" color="#2d8cf0"/>
            </view>
        </view>

        <view class="list" v-if="list.length">
            <view class="item bg-white flex-row" v-for="(item, index) in list" :key="index">
                <image class="headimg" :src="item.headimg"/>
                <view class="pl10 flex-column flex-kai w-100">
                    <view class="flex-kai">
                        <view class="color-title pt5">{{ item.nickname }}</view>
                        <view v-if="item.have_join" class="color-sub font12">已加入活动</view>
                    </view>
                    <view v-if="item.activeName" class="color-content" style="padding: 5px 0;">
                        {{ item.activeName }}
                    </view>
                    <view class="font14 color-sub">
                        <uni-dateformat :date="item.create_time" format="yyyy-MM-dd hh:mm:ss"
                                        :threshold="[0, 0]"/>
                    </view>
                </view>
            </view>
        </view>

        <uni-load-more v-if="in_load && load_page > 1" status="loading"></uni-load-more>
        <uni-load-more v-if="is_last_page && list.length > 5" status="noMore"
                       :contentText="{contentnomore: '我是有底线的'}"></uni-load-more>
        <uni-load-more v-if="!is_last_page && !in_load" status="more"></uni-load-more>

        <view v-if="!in_load && !list.length" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无好友，快去邀请好友参与吧！</view>
            <view class="flex-all-center p20">
                <view class="not-btn text-center bg-success color-white" @click="showActiveSharePopup">
                    邀请好友
                </view>
            </view>
        </view>


        <active-share ref="activeShare"/>
    </view>
</template>

<script>
const app = getApp()

export default {
    data() {
        return {
            in_load: true,
            list: [],
            load_page: 1,
            is_last_page: false,
            count: 0,
            showShare: false
        }
    },

    onLoad(params) {
        let showShare = true
        if (params.active_id) {
            showShare = false
            this.active_id = params.active_id
        }
        if (params['show_share']) showShare = true
        if (!showShare) uni.hideShareMenu(undefined)

        this.showShare = showShare

        this.$uni.showLoading('加载中...')

        this.$login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err.errTitle || '提示'})
            }

            this.init()
        })
    },

    onShareAppMessage() {
        return this.shareInfo.share
    },

    onReachBottom() {
        if (!this.in_load && !this.is_last_page) {
            this.load_page++
            this.getList()
        }
    },

    methods: {
        async init() {
            await this.getActiveDetails()
            await this.getShareInfo()
            await this.getList()
            uni.hideLoading()
        },

        async getActiveDetails() {
            this.active_id ||= app.globalData['shop_info']?.extend_set?.shield_other_active?.id
            if (!this.active_id) return

            let activity_detail = app.globalData['activity_detail'] || null

            if (!activity_detail || activity_detail.active_id !== this.active_id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {active_id: this.active_id}
                })
                activity_detail = res.data.active_details
            }

            this.activeDetails = activity_detail
        },

        async getShareInfo() {
            // 获取默认分享信息
            let shareInfo = this.getDefaultShareInfo()

            // 如果有活动详情，更新分享信息
            if (this.activeDetails) {
                shareInfo = await this.updateShareInfoWithActivity(shareInfo)
            }


            this.shareInfo = shareInfo
        },


        getDefaultShareInfo() {
            const {id: userid, nickname = '' } = app.globalData['userinfo']
            return {
                share: {
                    title: `${nickname} 邀你一起健步走`,
                    path: `pages/index/index?userid=${userid}`,
                    imageUrl: 'http://www.xinweiyun.com/weixin/editor/attached/image/weixin_300/20220310/20220310165606_221.jpg'
                },
                qrCode: {
                    page: 'pages/index/index',
                    scene: `uid=${userid}`,
                    qrcode_logo: ''
                }
            }
        },

        async updateShareInfoWithActivity(shareInfo) {
            const {id: userid, nickname = '' } = app.globalData['userinfo']
            const {id, active_id, types, conf} = this.activeDetails

            const path = this.xwy_config.getActivityPath(types, 'details').replace(/^\//, '')

            // 更新分享路径
            shareInfo.share.path = `${path}?userid=${userid}&id=${active_id}`

            // 更新分享图片
            if (conf?.active?.share_image) shareInfo.share.imageUrl = conf.active.share_image

            // 更新分享标题
            if (conf?.active?.share_title) {
                shareInfo.share.title = conf.active.share_title
            } else {
                const name = await this.getNickname(nickname)
                shareInfo.share.title = `${name} 邀你参与活动`
            }

            // 更新二维码信息
            shareInfo.qrCode.page = path
            shareInfo.qrCode.scene = `uid=${userid}&id=${id}`
            if (conf?.active?.qrcode_logo) shareInfo.qrCode.qrcode_logo = conf.active.qrcode_logo

            return shareInfo
        },


        async getNickname(nickname) {
            if (!this.active_id) return nickname

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: {
                    active_id: this.active_id
                }
            })

            return res?.data?.user_details?.must_submit?.[0]?.value || nickname
        },


        async getList() {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
            }

            const data = {
                page: this.load_page,
                perpage: 10
            }
            if (this.active_id) data.active_id = this.active_id

            this.in_load = true
            const res = await this.xwy_api.request({
                url: 'front.user.share_friends.user_list/my_share_friends_list',
                data
            })
            this.in_load = false

            const res_data = res?.data?.['share_friends_list']
            if (!res_data) {
                this.is_last_page = true
                return
            }
            const list = this.listInit(res_data.data || [])
            this.list = [...this.list, ...list]
            this.is_last_page = res_data.is_lastpage
            this.count = res_data.total
        },

        listInit(list) {
            return list.map(item => {
                const data = {
                    headimg: item.user_details?.headimg || item.headimg,
                    nickname: item.user_details?.must_submit?.[0]?.value || item.truename || item.nickname,
                    create_time: item.create_time,
                    have_join: this.active_id && item.user_details?.id
                }

                if (this.active_id && item.user_details?.id) {
                    data.have_join = true
                    const nameData = item.user_details.must_submit?.[0]
                    if (nameData) data.activeName = `${nameData.title || ''}: ${nameData.value || ''}`
                }

                return data
            })
        },

        showActiveSharePopup() {
            this.$refs.activeShare.open(this.shareInfo.qrCode)
        }
    }
}
</script>

<style>
.page {
    min-height: 100vh;
}

.item {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
}

.headimg {
    width: 60px;
    min-width: 60px;
    height: 60px;
    border-radius: 50%;
    display: block;
}

.not-btn {
    width: 200px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
}
</style>
