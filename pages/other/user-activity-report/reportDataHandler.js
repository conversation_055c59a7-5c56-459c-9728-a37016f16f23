import xwyApi from '@/utils/api/xwy_api'
import utils from '@/utils/utils'

const attend_details_regex = /<\$(.*?)\$>/;
const attend_details_replace_field_list = [
    'create_time', 'score', 'score_seconds', 'exchange_kilo', 'exchange_step', 'real_step',
    'sign_day', 'sign_latest_time', 'continue_sign_day', 'sign_count', 'integral_all', 'integral_left'
];

const calculateAttendDays = create_time => {
    if (!create_time) return ''
    const days = utils.dateDiff(create_time, Date.now())
    return days.toString()
}

const replaceWordText = (word, attend_details, more_data) => {
    if (word.text === '<$nickname$>') word.text = attend_details.must_submit?.[0]?.value || '';
    
    if (word.text === '<$attend_days$>') word.text = calculateAttendDays(attend_details.create_time);
    
    const attend_details_match = word.text.match(attend_details_regex);
    const field = attend_details_match?.[1] || '';
    if (attend_details_replace_field_list.includes(field)) {
        let data = attend_details[field];
        if (data === 0) data = '0';
        word.text = data ? data.toString() : '';
    }
    
    if (word.text === '<$finished_time$>') {
        const time = attend_details.finished_time || '';
        word.text = time ? utils.unitTimeToDate(time * 1000, true) : '';
    }
    
    if (word.text === '<$BMIDetails.BMI_first$>') word.text = more_data['BMIDetails']?.['BMI_first'] || '0';
    if (word.text === '<$BMIDetails.BMI_last$>') word.text = more_data['BMIDetails']?.['BMI_last'] || '0';
    if (word.text === '<$BMIDetails.diffNum$>' || word.text === '<$BMIDetails$>') {
        word.text = more_data['BMIDetails']?.['diffNum'] || '0';
    }
    
    
    if (word.text === '<$sportStepEarlyTime.early_time.time$>' || word.text === '<$sportStepEarlyTime$>') {
        word.text = more_data['sportStepEarlyTime']?.['early_time']?.time || '';
    }
    if (word.text === '<$sportStepEarlyTime.early_time.step$>') {
        word.text = more_data['sportStepEarlyTime']?.['early_time']?.step || '0';
    }
    if (word.text === '<$sportStepEarlyTime.last_time.time$>') {
        word.text = more_data['sportStepEarlyTime']?.last_time?.time || '';
    }
    if (word.text === '<$sportStepEarlyTime.last_time.step$>') {
        word.text = more_data['sportStepEarlyTime']?.last_time?.step || '0';
    }
    
    return word;
};

async function fetchAndProcessReportData(active_id) {
    const res = await xwyApi.request({
        url: 'front.flat.sport_step.total_active/personal_active_report_page_list',
        data: {active_id}
    });
    
    const attend_details = res?.data?.attend_details || {};
    const more_data = res?.data?.['more_data'] || {};
    const pagesData = res?.data?.['set_param']?.['page_list_set']?.pages || [];
    
    
    pagesData.forEach(page => {
        page.line_list ||= [];
        page.line_list.forEach(line => {
            line.text_list ||= [];
            line.text_list.forEach((word, index) => {
                word ||= {};
                word.text ||= '';
                
                line.text_list[index] = replaceWordText(word, attend_details, more_data);
            });
        });
    });
    
    return pagesData;
}

export {fetchAndProcessReportData};