<template>
    <view class="report-container">

        <swiper class="swiper" vertical="true" :current="swiperCurrentIndex" :duration="300"
                @change="handleSwiperChange">
            <!-- 修改 v-for 迭代变量名以更清晰地表示其来源 (page_config from pages) -->
            <swiper-item v-for="(page_config, pageIndex) in pages" :key="pageIndex">
                <view class="swiper-item-content"
                      :style="{ backgroundImage: 'url(' + page_config['bg_pic'] + ')' }" @click="handleContentClick">
                    <!-- 应用页面级别的 top/left 定位到 text-overlay -->
                    <!-- 使用 page_config.top 和 page_config.left, 并计算宽度以适应左右边距 -->
                    <view class="text-overlay" :style="{ top: `${page_config.top || 0}px`, left: `${page_config.left || 0}px`, width: `calc(100vw - ${(page_config.left || 0) * 2}px)` }">
                        <!-- 遍历 page_config.line_list 以获取每行的布局配置 (align, top) -->
                        <view v-for="(line_config, lineIndex) in page_config.line_list" :key="lineIndex"
                              class="text-line"
                              :style="{ textAlign: line_config.align, marginTop: `${line_config.top || 0}px` }">
                            <!-- 从 displayedTexts 渲染逐字显示的文本 -->
                            <!-- 确保 displayedTexts[pageIndex] 和 displayedTexts[pageIndex][lineIndex] 存在，以避免渲染错误 -->
                            <text v-for="(word, wordIndex) in (displayedTexts[pageIndex] && displayedTexts[pageIndex][lineIndex] ? displayedTexts[pageIndex][lineIndex] : [])" :key="wordIndex"
                                  :style="{ color: word.color, fontSize: `${word.size || 16}px` }">
                                {{ word.text }}
                            </text>
                        </view>
                    </view>
                </view>
            </swiper-item>
        </swiper>
    </view>
</template>

<script>
import {fetchAndProcessReportData} from './reportDataHandler'

export default {
    data() {
        return {
            pages: [],
            swiperCurrentIndex: 0,
            displayedTexts: [], // 用于存储逐字显示的文本
        }
    },

    onLoad(params) {
        // 初始化 animationTimeouts，用于存储动画的timeout ID
        this.animationTimeouts = [];
        // 初始化 fullyRevealedPages，用于跟踪哪些页面的文字已完整显示，避免重复播放动画
        this.fullyRevealedPages = new Set();

        this.active_id = params.active_id
        this.$login.uniLogin(() => this.init())
    },

    methods: {
        async init() {
            await this.getReportDetails()
        },

        /**
         * @description 获取用户活动报告的详细数据
         * 包括页面配置、背景图片、文本行列表等
         */
        async getReportDetails() {
            this.$uni.showLoading('报告加载中...')

            try {
                this.pages = await fetchAndProcessReportData(this.active_id)
                // 初始化用于逐字显示的文本数据结构
                this.initializeDisplayedTexts()
                // 如果有页面数据，则开始播放第一页的文本动画
                if (this.pages.length > 0) this.animateTextForPage(0)
            } catch (error) {
                uni.hideLoading()
                console.error('Error fetching report details:', error)
                await this.$uni.showModal('报告加载失败', {success: () => uni.navigateBack()})
            } finally {
                uni.hideLoading()
            }
        },

        /**
         * @description 初始化 displayedTexts 数组
         * displayedTexts 用于存储每个页面、每行、每个词逐字显示的文本状态
         * 会根据从接口获取的 pages 数据结构，将每个词的 text 初始化为空字符串
         */
        initializeDisplayedTexts() {
            // 根据新的数据结构 (pages -> line_list -> text_list) 初始化 displayedTexts
            // 每个词的 text 初始化为空字符串，用于后续的逐字显示动画
            this.displayedTexts = this.pages.map(page =>
                page.line_list.map(line_item =>
                    line_item.text_list.map(word => ({...word, text: ''}))
                )
            );
        },

        /**
         * @description 为指定页面播放文本动画
         * @param {number} pageIndex - 需要播放动画的页面索引
         */
        animateTextForPage(pageIndex) {
            // 清除之前所有页面的动画定时器，防止动画冲突
            this.animationTimeouts.forEach(clearTimeout);
            this.animationTimeouts = []; // 重置定时器数组

            // 重置当前页面在 displayedTexts 中的文本内容，确保动画从头开始
            if (this.displayedTexts[pageIndex]) {
                this.displayedTexts[pageIndex].forEach(text_list_for_line => { // text_list_for_line 是一个包含词对象的数组
                    text_list_for_line.forEach(word => {
                        this.$set(word, 'text', ''); // 将每个词的 text 设置为空，Vue 会响应这个变化并更新视图
                    });
                });
            }

            // 获取当前页面的数据
            const pageData = this.pages[pageIndex];
            // 如果页面数据不存在，则直接返回
            if (!pageData) return;

            // 检查页面是否有文本需要播放动画
            // 调整逻辑以检查 pageData.line_list 以及每个 line_item.text_list
            // 如果页面没有文本行，或者所有文本行都没有文本，或者所有文本行的所有词都没有文本，则认为此页面没有文本需要动画
            if (!pageData.line_list || pageData.line_list.length === 0 ||
                pageData.line_list.every(line_item =>
                    !line_item.text_list || line_item.text_list.length === 0 ||
                    line_item.text_list.every(word => !word || !word.text || word.text.length === 0)
                )
            ) {
                this.fullyRevealedPages.add(pageIndex); // 如果没有文本，则标记为已完全显示
                return; // 直接返回，不执行后续动画逻辑
            }

            // 如果此页面之前已经被标记为完全显示过（例如，用户点击跳过动画），则立即显示所有文本
            if (this.fullyRevealedPages.has(pageIndex)) {
                this.showTextImmediately(pageIndex); // 调用立即显示文本的方法
                return;
            }

            // 定义动画延迟参数
            const initialPageDelay = 500; // 页面初始延迟
            const charDelay = 100; // 每个字符出现的延迟

            // 设置页面级别的初始延迟定时器
            const pageLevelTimeoutId = setTimeout(() => {
                let currentCumulativeDelay = 0; // 当前累积延迟时间，用于控制每个词动画的开始时间
                let longestCharAnimationEndTime = 0; // 记录最长字符动画结束的时间点，用于判断整个页面动画何时完成

                // 遍历页面的文本行列表 (line_list)
                pageData.line_list.forEach((line_item, lineListIndex) => {
                    // 遍历当前文本行的词列表 (text_list)
                    line_item.text_list.forEach((originalWord, wordIndex) => {
                        // 如果当前词有文本内容
                        if (originalWord.text && originalWord.text.length > 0) {
                            // 遍历词的每个字符，为每个字符设置一个定时器来逐字显示
                            for (let i = 0; i < originalWord.text.length; i++) {
                                const charTimeoutDelay = currentCumulativeDelay + i * charDelay; // 计算当前字符的显示延迟
                                const timeoutId = setTimeout(() => {
                                    // 确保 displayedTexts 中的路径有效，防止因数据结构问题导致错误
                                    if (this.displayedTexts[pageIndex] &&
                                        this.displayedTexts[pageIndex][lineListIndex] &&
                                        this.displayedTexts[pageIndex][lineListIndex][wordIndex]) {
                                        // 逐字追加到 displayedTexts 中对应词的 text 属性
                                        // 使用 $set 来确保 Vue 能够检测到对象属性的变化并更新视图
                                        this.$set(this.displayedTexts[pageIndex][lineListIndex][wordIndex], 'text',
                                            this.displayedTexts[pageIndex][lineListIndex][wordIndex].text + originalWord.text[i]);
                                    }
                                }, charTimeoutDelay);
                                this.animationTimeouts.push(timeoutId); // 将定时器ID存起来，方便后续清除
                                // 更新最长字符动画的结束时间
                                longestCharAnimationEndTime = Math.max(longestCharAnimationEndTime, charTimeoutDelay + charDelay);
                            }
                            // 当前词所有字符动画设置完毕后，增加累积延迟，为下一个词的动画做准备
                            currentCumulativeDelay += originalWord.text.length * charDelay + 50; // 词间停顿50ms
                        }
                    });
                    // 当前行所有词动画设置完毕后，增加累积延迟，为下一行的动画做准备
                    currentCumulativeDelay += 100; // 行间停顿100ms
                });

                // 检查是否有实际的文本内容播放了动画，然后设置页面完成显示的标记
                // 如果页面有文本行，并且至少有一个文本行中至少有一个词有文本内容
                if (pageData.line_list.some(line_item => line_item.text_list && line_item.text_list.some(word => word.text && word.text.length > 0))) {
                    // 在所有字符动画结束后，标记此页面为已完全显示
                    const completionTimeoutId = setTimeout(() => {
                        this.fullyRevealedPages.add(pageIndex);
                    }, longestCharAnimationEndTime);
                    this.animationTimeouts.push(completionTimeoutId);
                } else {
                    // 如果遍历后发现没有文本（例如，所有文本内容都是空字符串），也标记为已完成
                    this.fullyRevealedPages.add(pageIndex);
                }
            }, initialPageDelay);
            this.animationTimeouts.push(pageLevelTimeoutId); // 将页面级别定时器ID存起来
        },

        /**
         * @description 立即显示指定页面的所有文本，跳过动画
         * @param {number} pageIndex - 需要立即显示文本的页面索引
         */
        showTextImmediately(pageIndex) {
            // 清除所有正在进行的动画定时器
            this.animationTimeouts.forEach(clearTimeout);
            this.animationTimeouts = []; // 重置定时器数组

            const pageData = this.pages[pageIndex];
            // 确保 pageData, pageData.line_list, 和 displayedTexts[pageIndex] 都存在，防止错误
            if (!pageData || !pageData.line_list || !this.displayedTexts[pageIndex]) {
                // 如果 pageData 存在但其内部结构不完整（例如没有 line_list），也应标记为已显示，避免后续重复尝试动画
                if (pageData) this.fullyRevealedPages.add(pageIndex);
                return;
            }

            // 遍历页面的文本行列表 (line_list)
            pageData.line_list.forEach((originalLineItem, lineListIndex) => {
                // 确保 displayedTexts 中对应的行 (text_list 结构) 存在
                if (this.displayedTexts[pageIndex][lineListIndex]) {
                    // 遍历当前文本行的词列表 (text_list)
                    originalLineItem.text_list.forEach((originalWord, wordIndex) => {
                        // 确保 displayedTexts 中对应的词对象存在
                        if (this.displayedTexts[pageIndex][lineListIndex][wordIndex]) {
                            // 使用 $set 直接将原始文本赋值给 displayedTexts 中对应词的 text 属性
                            // 这样可以立即在视图中显示完整文本
                            this.$set(this.displayedTexts[pageIndex][lineListIndex][wordIndex], 'text', originalWord.text);
                        }
                    });
                }
            });
            // 标记此页面为已完全显示
            this.fullyRevealedPages.add(pageIndex);
        },

        /**
         * @description 处理 Swiper 滑动切换事件
         * @param {object} event - Swiper change 事件对象，包含 current (新页面索引) 和 source (触发来源)
         */
        handleSwiperChange(event) {
            const newIndex = event.detail.current // 获取滑动后的新页面索引
            const oldIndex = this.swiperCurrentIndex // 获取滑动前的旧页面索引

            // 如果索引没有变化，则不执行任何操作
            if (newIndex === oldIndex) return

            // 更新当前 Swiper 索引
            this.swiperCurrentIndex = newIndex

            // 判断滑动方向
            if (newIndex < oldIndex) { // 向上滑动 (切换到之前的页面)
                // 对于向上滑动，通常立即显示目标页面的文本，因为用户可能已经看过
                this.showTextImmediately(newIndex)
            } else { // 向下滑动 (切换到之后的页面)
                // 如果目标页面之前已经被标记为完全显示过 (例如，用户点击跳过动画或向上滑动回来过)
                if (this.fullyRevealedPages.has(newIndex)) {
                    // 则立即显示文本
                    this.showTextImmediately(newIndex)
                } else {
                    // 否则，播放目标页面的文本动画
                    this.animateTextForPage(newIndex)
                }
            }
        },

        /**
         * @description 处理页面内容点击事件
         * 用户点击页面内容时，会跳过当前页面的文本动画，并立即显示所有文本
         */
        handleContentClick() {
            // 1. 清除现有的逐字动画定时器，停止正在进行的动画
            this.animationTimeouts.forEach(clearTimeout);
            this.animationTimeouts = []; // 重置定时器数组

            // 2. 获取当前页面的索引和数据
            const currentPageIndex = this.swiperCurrentIndex;
            const originalPageData = this.pages[currentPageIndex];

            // 确保数据存在: originalPageData, originalPageData.line_list, 和 displayedTexts[currentPageIndex]
            // 如果数据不完整，则无法进行后续操作，同时标记页面为已显示，避免问题
            if (!originalPageData || !originalPageData.line_list || !this.displayedTexts[currentPageIndex]) {
                // console.warn('handleContentClick: Missing data for current page', currentPageIndex);
                if (originalPageData) this.fullyRevealedPages.add(currentPageIndex); // 如果页面数据存在但结构不完整，也标记为已显示
                return;
            }

            // 3. 立即显示当前页面的所有文本
            // 遍历页面的文本行列表 (line_list)
            originalPageData.line_list.forEach((line_item, lineListIndex) => {
                // 确保 displayedTexts 中对应的行 (text_list 结构) 存在
                if (this.displayedTexts[currentPageIndex][lineListIndex]) {
                    // 遍历当前文本行的词列表 (text_list)
                    line_item.text_list.forEach((originalWord, wordIndex) => {
                        // 确保 displayedTexts 中对应的词存在
                        if (this.displayedTexts[currentPageIndex][lineListIndex][wordIndex]) {
                            // 直接更新 displayedTexts 中对应词的 text 属性为原始文本
                            this.displayedTexts[currentPageIndex][lineListIndex][wordIndex].text = originalWord.text;
                        }
                    });
                }
            });
            // 标记当前页面为已完全显示
            this.fullyRevealedPages.add(currentPageIndex);
        }
    }
}
</script>

<style lang="scss" scoped>
.report-container {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}

.swiper {
    width: 100%;
    height: 100%;
}

.swiper-item-content {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    position: relative;
}

.text-overlay {
    position: absolute;
    width: 100%;
    box-sizing: border-box;
}

.text-line {
    word-wrap: break-word;
    word-break: break-all;
}
</style>