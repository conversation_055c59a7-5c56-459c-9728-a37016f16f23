<template>
	<view>
		<uni-calendar :start-date="'2022-9-8'" :end-date="end_date" :insert="true" :lunar="true" @change="change"/>

		<view class="p10"></view>

		<xwy-ad v-if="!loading && !close_ad && show_index === null" :ad_type="66"></xwy-ad>

		<view v-if="!list.length && !loading" class="text-center">
		    <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
		    <view class="color-sub">暂无历史</view>
		</view>

		<uni-load-more v-if="loading" status="loading"></uni-load-more>

		<xwy-ad v-if="!loading && !close_ad" :ad_type="3"></xwy-ad>

		<view class="flex-row" style="padding-bottom: 20px;">
			<view class="left-line bg-light-primary"></view>
			<view class="right">
				<view
					class="content-item bg-background p10"
					v-for="(item, index) in list"
					:key="index"
					:id="'content-' + index"
					@click="showORhidden(index)"
				>
					<view class="point bg-light-primary flex-all-center color-white font12">{{index + 1}}</view>
					<view class="color-warning">{{item.year}}年</view>
					<view class="color-title">{{item.title}}</view>
					<view v-if="show_index === index">
						<view v-if="!close_ad" class="pt10">
							<xwy-ad :ad_type="3"></xwy-ad>
							<xwy-ad :ad_type="66"></xwy-ad>
						</view>

						<view class="color-content font14">
							<text>{{item.conf_json.news_details[0].content}}</text>
						</view>
						<view>
							<view
								class="pt10"
								v-for="(img, img_index) in item.conf_json.news_details[0].picUrl"
								:key="img_index"
							>
								<image
									:src="img.url"
									mode="widthFix"
									style="width: 100%; display: block;"
									@click.stop="previewImage(item.conf_json.news_details[0].picUrl, img.url)"
								/>
								<view class="font12 color-sub text-center">{{img.pic_title}}</view>
							</view>
						</view>
					</view>

					<view class="pt10" v-if="!close_ad && show_index === null && (index + 1) % 10 === 0">
						<xwy-ad :ad_type="66"></xwy-ad>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'
	import utils from '@/utils/utils.js'

	export default {
		data() {
			return {
				date: (function() {
					const date = new Date()
					let month = date.getMonth() + 1
					if (month < 10) month = '0' + month
					let day = date.getDate()
					if (day < 10) day = '0' + day
					return month + '' + day
				})(),
				end_date: (function() {
					let end_date = utils.getDay()

					// 因为记录是从2022年9月8号开始记录的，所以要到2023年9月8号才能全部记录完，所以2023年9月8号之前只能查看从2022年9月8号到单天的
					const start_time = new Date('2023-9-8').getTime()
					const now_time = new Date().getTime()
					if (now_time >= start_time) end_date = '2100-12-31'
					return end_date
				})(),
				list: [],
				loading: true,
				show_index: null,
				close_ad: false
			}
		},

		onLoad(e) {
			this.active_id = e.active_id
			if (e.close_ad) this.close_ad = true

			login.uniLogin(err => {
				if (err?.errMsg) {
					uni.hideLoading()
					xwy_api.alert(err.errMsg, {title: err.errTitle, success() {uni.navigateBack()}})
					return false
				}

				this.getList()
			})
		},

		methods: {
			change(e) {
				let month = Number(e.month)
				if (month < 10) month = '0' + month
				let day = Number(e.date)
				if (day < 10) day = '0' + day
				this.date = month + '' + day

				this.getList()
			},

			async getList() {
				this.list = []
				this.show_index = null
				this.loading = true

				const res = await xwy_api.request({
					url: 'front.flat.sport_step.historyToday/history_list',
					data: {
						access_token: app.globalData.access_token,
						date: this.date,
						types: 1
					}
				})

				if (res?.data?.history_list?.length) {
					const list = res.data.history_list
					list.forEach(v => {
						v.year = v.year.replace(/-(\d+)/g, (match, chr) => `公元前${chr}`)
						v.conf_json.news_details.forEach(vv => {
							vv.content = vv.content.replace(/在\d+年前的今天，/g, '')
							vv.content = vv.content.replace(/-(\d+)年/g, (match, chr) => `公元前${chr}年`)
							vv.content = vv.content.replace(/&#13;/g, '')
						})
					})
					this.list = list
				}

				this.loading = false
			},

			showORhidden(index) {
				if (index === this.show_index) {
					this.hiddenAll(index)
				} else {
					this.showAll(index)
				}
			},

			showAll(index) {
				this.show_index = index
				this.pageScrollTo(index)
			},

			hiddenAll(index) {
				this.show_index = null
				this.pageScrollTo(index)
			},

			pageScrollTo(index) {
				this.$nextTick(() => {
					uni.pageScrollTo({
						selector: '#content-' + index,
						duration: 100
					})
				})
			},

			previewImage(data, current) {
				const urls = []
				data.forEach(v => {
					urls.push(v.url)
				})
				uni.previewImage({
					urls,
					current
				})
			}
		}
	}
</script>

<style>
.left-line {
	margin: 0 20px;
	width: 2px;
	min-width: 2px;
}
.right {
	width: calc(100% - 50px);
}
.content-item {
	margin-bottom: 30px;
	border-radius: 10px;
	position: relative;
}
.content-item .point {
	width: 24px;
	height: 24px;
	border-radius: 50%;
	position: absolute;
	left: -33px;
	top: 20px;
}


</style>
