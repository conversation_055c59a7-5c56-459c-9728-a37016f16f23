<template>
    <view class="page">
        <view v-if="!hide_input" class="user-src">
            <textarea maxlength="-1" auto-height="true" v-model="user_src"
                      placeholder="输入图片地址并点击确定按钮 (200字符内)"></textarea>
        </view>
        <view class="pl10">
            <button size="mini" type="primary" @click="selectImageSuccess(user_src)">确定</button>
        </view>


        <view class="img-list flex-row flex-wrap">
            <upload-headimg v-if="upload_first" @chooseAvatar="chooseAvatar"/>
            <view class="img-view" v-for="(item, index) in image_list" :key="index">
                <image
                    class="img-item"
                    :src="item.url"
                    mode="aspectFill"
                    @click="selectImageSuccess(item.url)"
                />
            </view>
            <upload-headimg v-if="!upload_first" @chooseAvatar="chooseAvatar"/>
        </view>

        <uni-load-more v-if="loading" status="loading"></uni-load-more>
        <uni-load-more v-if="is_last_page && image_list.length > 6" status="noMore"
                       :contentText="{contentnomore: '没有更多了'}"></uni-load-more>
        <uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>


    </view>
</template>

<script>
const app = getApp()

import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'

import uploadHeadimg from "./upload-headimg.vue"


export default {
    components: {
        uploadHeadimg
    },
    data() {
        return {
            image_list: [],
            load_page: 1,
            is_last_page: false,
            loading: true,
            user_src: '',
            upload_first: false,  // 上传头像是否在头像列表的最前面，某些活动后独立小程序需要把上传头像放到前面
            hide_input: false
        }
    },
    onLoad(params) {
        if (params.active_id) this.active_id = params.active_id
        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err.errTitle || '提示'})
            }

            this.getImageList()
            this.isUploadFirst()
        })
    },

    onReachBottom() {
        if (!this.is_last_page && !this.loading) this.getImageList()
    },

    methods: {
        async isUploadFirst() {
            const active_id = this.active_id || app.globalData['shop_info']?.extend_set?.shield_other_active?.active_id

            if (!active_id) return

            const ids = [
                'fe1b2751ab7dffa6436a77f84c38cb0a',
                'd37b5daefa79652ddfe09df66422563a',
                '681a69f3fe180478c835905a6af0253b',  // 鲜繁客户运动轨迹定制开发活动
            ]

            if (active_id === 'd37b5daefa79652ddfe09df66422563a') this.hide_input = true

            if (ids.includes(active_id)) {
                this.upload_first = true
                return
            }

            const who_list = [45979]
            const who = app.globalData['who']
            if (who_list.includes(who)) {
                this.upload_first = true
                return
            }

            if (active_id) {
                const activeDetails = await this.getActiveDetails(active_id)
                if (!activeDetails) return

                if (activeDetails.rank_set?.['uploadHeadimgFront']) this.upload_first = true
            }
        },

        async getActiveDetails(active_id) {
            let details = app.globalData['activity_detail']

            if (!details || details.active_id !== active_id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {active_id}
                })

                details = res.data['active_details']
            }

            return details
        },

        async getImageList() {
            if (this.load_page === 1) {
                this.image_list = []
                this.is_last_page = false
            }


            this.loading = true

            const res = await xwy_api.request({
                url: 'front.system.flat_pic/pic_list',
                data: {
                    category_id: 1389,
                    page: this.load_page,
                    perpage: 20,
                }
            })

            this.loading = false
            this.load_page++
            if (res?.data?.pic_list) {
                const data = res.data.pic_list
                const list = data.data || []
                this.image_list = [...this.image_list, ...list]
                this.is_last_page = data.is_lastpage
            } else {
                this.is_last_page = true
            }

        },


        chooseAvatar(data) {
            this.getOpenerEventChannel().emit('newImg', data)
            uni.navigateBack()
        },

        selectImageSuccess(src) {
            if (!src) return this.$uni.showToast('请输入图片地址')

            if (src.length > 200) return this.$uni.showToast('图片地址不得超过200字符')

            this.getOpenerEventChannel?.()?.emit?.('newImg', {src})

            this.$uni.navigateBack()
            uni.hideLoading()
        }
    }
}
</script>

<style scoped>
.page {
    padding-bottom: 10px;
}


.user-src {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
    border: 1px solid #eee;
}

.user-src textarea {
    width: 100%;
}


.img-list {
    padding: 5px;
}

.img-view {
    margin: 10px;
    width: 100px;
    height: 100px;
}


.img-item {
    width: 100px;
    height: 100px;
    display: block;
    border-radius: 50%;
}
</style>
