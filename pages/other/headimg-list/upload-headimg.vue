<template>
    <view>
        <!-- #ifdef MP-WEIXIN -->
        <button
            class="avatar-wrapper bg-background flex-all-center"
            open-type="chooseAvatar"
            @chooseavatar="onChooseAvatar"
        >
            <uni-icons type="plusempty" size="60" color="#c6cbe0"/>
        </button>
        <!-- #endif -->
        
        
        <!-- #ifdef H5 -->
        <button class="avatar-wrapper bg-background flex-all-center" @click="chooseAvatar">
            <uni-icons type="plusempty" size="60" color="#c6cbe0"/>
        </button>
        <!--#endif-->
    </view>
</template>

<script>
import xwy_api from "@/utils/api/xwy_api";

export default {
    name: "",
    props: {},
    data() {
        return {}
    },

    mounted() {

    },

    methods: {
        onChooseAvatar(e) {
            const path = e.detail.avatarUrl
            wx.getFileSystemManager().getFileInfo({
                filePath: path,
                success: res => this.$emit('chooseAvatar', {src: path, size: res.size, temp: true}),
                fail: () => xwy_api.alert('头像获取失败')
            })
        },

        chooseAvatar() {
            uni.chooseImage({
                count: 1,
                success: res => this.$emit('chooseAvatar', {src: res.tempFilePaths[0], file: res.tempFiles[0], temp: true})
            })
        }
    }
}
</script>

<style lang="scss">
.avatar-wrapper {
    margin: 10px;
    width: 100px;
    height: 100px;
    border-radius: 10px;
}
.avatar-wrapper::after {
    border: none;
}
</style>
