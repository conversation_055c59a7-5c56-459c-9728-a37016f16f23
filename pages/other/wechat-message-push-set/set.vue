<template>
    <view class="page bg-background">
        <template v-if="!loading">
            <view class="message p10">
                <view class="color-title font14 pb5">推送的消息内容</view>
                <view class="message-content" @click="editContent">
                    <view>{{ message.content || '未设置' }}</view>
                    <view class="edit">
                        <text class="iconfont icon-edit color-sub"></text>
                    </view>
                </view>
            </view>

            <view class="time-list p10">
                <view class="flex-kai">
                    <view class="color-title font14">消息推送时间设置</view>
                    <view @click="editTimeShow(null)">
                        <text class="font14 color-light-primary">添加推送时间</text>
                        <uni-icons type="forward" size="14" color="#5cadff"/>
                    </view>
                </view>
                <view>
                    <view class="time-item flex-kai" v-for="item in timeList" :key="item.id" 
                          @click="editTimeShow(item)">
                        <view>
                            <text>每月{{ item.days }}号 {{ item.hour }}:{{ item.minute }}</text>
                            <text class="pl10 font14" 
                                  :class="{'color-success': item.status === 1, 'color-sub': item.status === 0}">
                                ({{ getStatusText(item.status) }})
                            </text>
                        </view>
                        <view class="flex-row">
                            <view class="pl5 pr5" @click="editTimeShow(item)">
                                <text class="iconfont icon-edit color-sub"></text>
                            </view>
                            <!--<view class="pl5 pr5" @click="deleteTime(item)">
                                <text class="iconfont icon-delete color-sub"></text>
                            </view>-->
                        </view>
                    </view>

                    <view v-if="!loading && !timeList.length" class="p10 text-center">
                        <view class="font14 color-sub">未添加推送时间</view>
                    </view>
                </view>
            </view>
        </template>
        
        <uni-popup ref="editContentPopup">
            <view class="popup-- bg-white">
                <view class="popup-title color-title text-center">推送的消息内容</view>
                <view class="p10">
                    <uni-easyinput type="textarea" v-model="edit_content" 
                                   placeholder="请输入推送的消息内容 (200字内)" maxlength="200"/>
                </view>
                
                <view class="flex-all-center pt10">
                    <view class="popup-button bg-primary color-white text-center" 
                          hover-class="navigator-hover" @click="saveMessage">保存</view>
                </view>
                <view class="flex-all-center">
                    <view class="p10 color-sub font14 text-center" @click="$refs.editContentPopup.close()">
                        取消
                    </view>
                </view>
            </view>
        </uni-popup>
        
        <uni-popup ref="timeEditPopup">
            <view class="popup-- bg-white">
                <view class="popup-title color-title text-center">
                    {{ edit_time_data.id ? '修改' : '添加' }}推送时间
                </view>
                <view class="p10">

                    <picker :range="dayList" :value="edit_time_data.days ? edit_time_data.days - 1 : 0" 
                            @change="edit_time_data.days = Number($event.detail.value) + 1">
                        <view class="picker-view flex-kai">
                            <view>
                                <text class="pr5 color-content">推送日期:</text>
                                <template>
                                    <text v-if="edit_time_data.days">{{ edit_time_data.days }}号</text>
                                    <text v-else class="color-sub">请选择推送日期</text>
                                </template>
                            </view>
                            <view>
                                <uni-icons type="forward" color="#bbbec4"/>
                            </view>
                        </view>
                    </picker>


                    <picker mode="time" @change="editTimeChange" 
                            :value="`${edit_time_data.hour || '09'}:${edit_time_data.minute || '00'}`">
                        <view class="picker-view flex-kai">
                            <view>
                                <text class="pr5 color-content">推送时间:</text>
                                <template>
                                    <text v-if="edit_time_data.hour && edit_time_data.minute">
                                        {{ edit_time_data.hour }}:{{ edit_time_data.minute }}
                                    </text>
                                    <text v-else class="color-sub">请选择推送时间</text>
                                </template>
                            </view>
                            <view>
                                <uni-icons type="forward" color="#bbbec4"/>
                            </view>
                        </view>
                    </picker>


                    <picker :range="statusOptions" range-key="text" :value="editTimeStatusValue"
                            @change="edit_time_data.status = statusOptions[$event.detail.value].value">
                        <view class="picker-view flex-kai">
                            <view>
                                <text class="pr5 color-content">推送状态:</text>
                                <text>{{ getStatusText(edit_time_data.status) }}</text>
                            </view>
                            <view>
                                <uni-icons type="forward" color="#bbbec4"/>
                            </view>
                        </view>
                    </picker>
                </view>
                
                <view class="flex-all-center pt10">
                    <view class="popup-button bg-primary color-white text-center" 
                          hover-class="navigator-hover" @click="saveTime">保存</view>
                </view>
                <view class="flex-all-center">
                    <view class="p10 color-sub font14 text-center" @click="$refs.timeEditPopup.close()">
                        取消
                    </view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>

export default {
    data() {
        return {
            loading: true,
            message: {
                id: null,
                content: ''
            },
            timeList: [],
            edit_content: '',
            edit_time_data: {},
            dayList: Array.from({length: 31}, (v, i) => i + 1),
            statusOptions: [
                {text: '正常使用', value: 1},
                {text: '暂停使用', value: 0}
            ]
        }
    },
    
    computed: {
        editTimeStatusValue() {
            if (!this.edit_time_data.status) return 0
            const index = this.statusOptions.findIndex(item => item.value === this.edit_time_data.status)
            return index > -1 ? index : 0
        }
    },

    onLoad(params) {
        this.$uni.showLoading()
        this.active_id = params.id
        this.$login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                this.loading = false
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getSet()
        })
    },

    methods: {
        async getSet() {
            await this.getMessageList()
            await this.getTimeList()
            uni.hideLoading()
            this.loading = false
        },

        async getMessageList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin.timerSendSet/send_message_list',
                data: {
                    active_id: this.active_id,
                    page: 1,
                    perpage: 1
                }
            })
            const message = res?.data?.message_content?.data?.[0]
            if (!message) return
            this.message.id = message.id
            this.message.content = message.content
        },

        async getTimeList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin.timerSendSet/time_set_list'
            })
            const timeList = res?.data?.['time_set_list']
            if (timeList.length) {
                this.timeList = timeList.map(item => {
                    return {
                        id: item.id,
                        days: item.days,
                        hour: item.hour,
                        minute: item.minute,
                        status: item.status
                    }
                }).sort((a, b) => a.days - b.days)
            }
        },

        editContent() {
            this.edit_content = this.message.content
            this.$refs.editContentPopup.open()
        },


        editTimeChange(e) {
            console.log(e);
            const [hour, minute] = e.detail.value.split(':')
            this.edit_time_data.hour = hour
            this.edit_time_data.minute = minute
        },

        getStatusText(status) {
            return this.statusOptions.find(item => item.value === status)?.text || '未知'
        },

        async saveMessage() {
            const content = this.edit_content
            if (!content) this.$uni.showToast('请输入推送的消息内容')
            if (content === this.message.content) {
                this.$uni.showToast('保存成功', 'success')
                return this.$refs.editContentPopup.close()
            }

            this.$uni.showLoading('保存中...')
            const data = {
                active_id: this.active_id,
                content
            }
            if (this.message.id) data.id = this.message.id
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin.timerSendSet/message_content_set',
                data
            })
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '保存失败')
            this.$uni.showToast('保存成功', 'success')
            this.$refs.editContentPopup.close()
            await this.getMessageList() // 必须要重新获取，保证是最新的 （还有一种情况就是新添加的没有id，不重新获取拿不到id，没有id的话，修改的时候就变成添加一个新的了）
        },
        

        editTimeShow(data) {
            this.edit_time_data = data || {
                days: '',
                hour: '',
                minute: '',
                status: 1
            }
            
            this.$refs.timeEditPopup.open()
        },

        async saveTime() {
            const {id, days, hour, minute, status} = this.edit_time_data
            if (!days) return this.$uni.showToast('请选择推送日期')
            if (!hour || !minute) return this.$uni.showToast('请选择推送时间')
            
            const data = {days, hour, minute, status}
            data.active_id = this.active_id
            if (id) data.id = id
            
            this.$uni.showLoading('保存中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin.timerSendSet/time_set_create',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '保存失败')
            this.$uni.showToast('保存成功', 'success')
            this.$refs.timeEditPopup.close()
            await this.getTimeList() // 必须要重新获取，保证是最新的 （还有一种情况就是新添加的没有id，不重新获取拿不到id，没有id的话，修改的时候就变成添加一个新的了）
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 60px;
}

.message {
    .message-content {
        padding: 10px 40px 10px 10px;
        border-radius: 10px;
        background-color: #fff;
        position: relative;
        
        .edit {
            position: absolute;
            width: 40px;
            height: 40px;
            text-align: center;
            line-height: 40px;
            right: 0;
            top: 50%;
            margin-top: -20px;
        }
    }
}

.time-item {
    padding: 10px;
    border-radius: 10px;
    background-color: #fff;
    margin: 10px 0;
}

.popup-- {
    width: 320px;
    border-radius: 10px;
    
    .popup-title {
        padding: 10px;
        border-bottom: 1px solid #eee;
    }
    
    .popup-button {
        width: 200px;
        line-height: 44px;
        border-radius: 22px;
    }
}

.picker-view {
    margin: 10px;
    padding: 0 10px;
    border-radius: 5px;
    line-height: 40px;
    border: 1px solid #eee;
}
</style>