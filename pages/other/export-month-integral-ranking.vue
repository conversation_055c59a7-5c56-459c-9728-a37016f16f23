<template>
    <view>
        <view class="flex-row p10">
            <view class="color-content pr10" style="line-height: 36px;">日期:</view>

            <picker mode="date" fields="month" :value="date" start="2022-01-01" :end="thisMonthDate"
                    @change="monthChange">
                <view class="month-picker">{{ year }}年{{ month }}月</view>
            </picker>
        </view>

        <template v-if="total_page">
            <view class="font14 p10" style="color: #e19898;">
                共{{ count }}条记录，需要分{{ total_page }}次导出，每次导出{{ perpage }}条数据。
            </view>

            <view class="p10 font14 color-content">
                <text>每次导出</text>
                <text class="plr5">{{ perpage }}</text>
                <text>条数据</text>
                <text class="pl10 color-primary" @click="uniPopupOpen('perpage')">修改</text>
            </view>
        </template>


        <view class="pb10">
            <view class="flex-kai" v-for="(item, index) in total_page" :key="item">
                <view class="color-title p10">
                    <text class="color-sub">{{ index + 1 }}、</text>
                    <text>{{ index * perpage + 1 }}</text>
                    <text class="plr5">-</text>
                    <text>
                        <template v-if="index + 1 === total_page">{{ count }}</template>
                        <template v-else>{{ (index + 1) * perpage }}</template>
                    </text>
                </view>

                <view class="color-primary p10" @click="exportData(index + 1)">导出</view>
            </view>
        </view>


        <uni-popup ref="perpage" type="dialog" mode="input" :is-mask-click="false">
            <uni-popup-dialog
                mode="input"
                title="每次导出数据量"
                :value="perpage"
                :placeholder="'请输入 100 - ' + max_count + ' 内的数字'"
                @confirm="perpageInputConfirm"
            ></uni-popup-dialog>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
import openExcelFile from '@/utils/open-excel-file'

export default {
    data() {
        return {
            count: 0,
            total_page: 0,
            perpage: 1000,
            max_count: 1000,
            download_src: '',
            thisMonthDate: 0,
            year: 0,
            month: 0
        }
    },

    computed: {
        date() {
            return `${this.year}-${this.month.toString().padStart(2, '0')}-01`
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.getLastMonth()

        this.$login.uniLogin(() => this.init())
    },

    methods: {
        getLastMonth() {
            let {year, month} = this._utils.getYearMonthDay()

            this.thisMonthDate = `${year}-${month.toString().padStart(2, '0')}-01`

            month -= 1
            if (month === 0) {
                month = 12
                year -= 1
            }
            this.year = year
            this.month = month
        },

        async init() {
            this.$uni.showLoading()

            await this.getActiveMustSubmit()
            await this.getActiveTeamList()
            await this.getTotalPage()

            uni.hideLoading()
        },

        async getActiveMustSubmit() {
            let details = app.globalData['activity_detail']
            if (details && details.active_id === this.active_id && details.conf?.must_submit?.length) {
                this.must_submit = details.conf.must_submit
                this.team_open = details.rank_set?.team_group_open
                return
            }

            if (!details || details.active_id !== this.active_id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.active_id
                    }
                })
                details = res?.data?.active_details || {}
            }

            this.must_submit = details.conf?.must_submit || []
            this.team_open = details.rank_set?.team_group_open
        },

        async getActiveTeamList() {
            if (!this.team_open) {
                this.team_list = []
                return
            }

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/team_group_list',
                data: {
                    active_id: this.active_id,
                    page: 1,
                    perpage: 1000
                }
            })

            this.team_list = res?.data?.['team_group_list']?.data || []
        },

        monthChange(e) {
            const [year, month] = e.detail.value.split('-')
            this.year = Number(year)
            this.month = Number(month)

            this.getTotalPage()
        },

        async getTotalPage() {
            this.total_page = 0

            this.$uni.showLoading()
            const list = await this.getRecordList(1)
            uni.hideLoading()

            if (!list?.length) return this.$uni.showToast(`${this.year}年${this.month}月 暂无记录`, 'none', 3000)

            this.setTotalPage()
        },

        async exportData(page) {
            this.$uni.showLoading('导出中...')

            const list = await this.getRecordList(page)
            const tableData = this.excelDataProcessing(list, page)

            openExcelFile.openDocument(tableData, this.getTableTitle(page))

            uni.hideLoading()
        },

        getTableTitle(page) {
            if (this.total_page === 1) return `${this.year}年${this.month}月 积分排行榜`

            const first = (page - 1) * this.perpage + 1
            const last = page === this.total_page ? this.count : page * this.perpage

            return `${this.year}年${this.month}月 积分排行榜  ${first} - ${last}`
        },

        perpageInputConfirm(val) {
            const perpage = Number(val)
            if (isNaN(perpage) || perpage < 100 || perpage > this.max_count) {
                return this.$uni.showModal(`输入错误，请输入 100 - ${this.max_count} 内的数字`, {
                    success: () => this.uniPopupOpen('perpage')
                })
            }
            this.perpage = perpage
            this.setTotalPage()
        },

        setTotalPage() {
            this.total_page = Math.ceil(this.count / this.perpage)
        },

        excelDataProcessing(list, page) {
            const tHead = ['序号', '系统标识']
            this.must_submit.forEach(item => tHead.push(item.title))
            if (this.team_open) tHead.push('队伍')
            tHead.push('积分')

            const tBody = []

            list.forEach((item, index) => {
                const must_submit_value = this.must_submit.map(a => {
                    const user_must_submit = item.must_submit || []
                    return user_must_submit.find(u => u.title === a.title)?.value || ''
                })

                const data = [
                    (page - 1) * this.perpage + index + 1,               // 序号
                    `w.${app.globalData['who']}u.${item.userid || 0}`, // 商户号+会员号
                    ...must_submit_value,    // must_submit
                ]

                // 队伍
                if (this.team_open) data.push(this.team_list.find(t => t.id === item.team_id)?.name || '')

                data.push(item.total_integral || 0)   // 积分

                tBody.push(data)
            })


            return [tHead, ...tBody]
        },

        async getRecordList(page = 1, perpage = this.perpage) {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.step/top_rank_list',
                data: {
                    active_id: this.active_id,
                    top_rank_types: 49,
                    year: this.year,
                    month: this.month,
                    page,
                    perpage
                }
            })

            if (page === 1) this.count = res?.data?.top_rank_list?.list?.total || 0

            return res?.data?.top_rank_list?.list?.data || []
        },

        uniPopupOpen(ref) {
            this.$refs[ref].open()
        },

        uniPopupClose(ref) {
            this.$refs[ref].close()
        },
    }
}
</script>

<style lang="scss">
.month-picker {
    width: 150px;
    line-height: 34px;
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 0 10px;
}

.uni_popup {
    width: 280px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}

.uni_popup-btn {
    line-height: 40px;
    border-radius: 20px;
}

.uni_popup-btn::after {
    border: none;
}
</style>