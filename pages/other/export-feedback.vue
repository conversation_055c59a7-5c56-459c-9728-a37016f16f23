<template>
    <view>
        <template v-if="total_page">
            <view class="font14 p10" style="color: #e19898;">
                共{{ count }}条记录，需要分{{ total_page }}次导出，每次导出{{ perpage }}条数据。
            </view>

            <view class="p10 font14 color-content">
                <text>每次导出</text>
                <text class="plr5">{{ perpage }}</text>
                <text>条数据</text>
                <text class="pl10 color-primary" @click="uniPopupOpen('perpage')">修改</text>
            </view>
        </template>


        <view class="pb10">
            <view class="flex-kai" v-for="(item, index) in total_page" :key="item">
                <view class="color-title p10">
                    <text class="color-sub">{{ index + 1 }}、</text>
                    <text>{{ index * perpage + 1 }}</text>
                    <text class="plr5">-</text>
                    <text>
                        <template v-if="index + 1 === total_page">{{ count }}</template>
                        <template v-else>{{ (index + 1) * perpage }}</template>
                    </text>
                </view>

                <view class="color-primary p10" @click="exportData(index + 1)">导出</view>
            </view>
        </view>


        <uni-popup ref="perpage" type="dialog" mode="input" :is-mask-click="false">
            <uni-popup-dialog
                mode="input"
                title="每次导出数据量"
                :value="perpage"
                :placeholder="'请输入 100 - ' + max_count + ' 内的数字'"
                @confirm="perpageInputConfirm"
            ></uni-popup-dialog>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
import openExcelFile from '@/utils/open-excel-file'

export default {
    data() {
        return {
            count: 0,
            total_page: 0,
            perpage: 1000,
            max_count: 3000,
            src_list: [],
            download_src: ''
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.init()
    },

    methods: {
        async init() {
            this.$uni.showLoading()
            await this.getActiveDetails()
            await this.getOne()
            uni.hideLoading()
        },

        async getActiveDetails() {
            let details = app.globalData.activity_detail
            if (!details || details?.avtive_id !== this.active_id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.active_id
                    }
                })
                details = res?.data?.active_details
            }

            this.must_submit = details?.conf?.must_submit || []
        },

        async getOne() {
            const list = await this.getRecordList(1)
            uni.hideLoading()

            if (!list?.length) return this.$uni.showModal('暂无记录', {success: () => uni.navigateBack()})

            if (this.count <= this.perpage) {
                const tableData = this.excelDataProcessing(list, 1)
                openExcelFile.openDocument(tableData, '用户反馈')
            }

            this.setTotalPage()
        },

        async exportData(page) {
            this.$uni.showLoading('导出中...')
            const list = await this.getRecordList(page)
            const tableData = this.excelDataProcessing(list, page)
            openExcelFile.openDocument(tableData, `用户反馈 - 第${page}页`)
            uni.hideLoading()
        },

        perpageInputConfirm(val) {
            const perpage = Number(val)
            if (isNaN(perpage) || perpage < 100) {
                return this.$uni.showModal(`输入错误，请输入 100 - ${this.max_count} 内的数字`, {
                    success: () => this.uniPopupOpen('perpage')
                })
            }
            this.perpage = perpage
            this.setTotalPage()
        },

        setTotalPage() {
            this.total_page = Math.ceil(this.count / this.perpage)
            this.src_list = []
        },

        excelDataProcessing(list, page) {
            const tHead = ['序号', '系统标识', ...this.must_submit.map(item => item.title), '标题',  '反馈内容', '提交时间']

            const tBody = []

            list.forEach((item, index) => {
                const data = [
                    (page - 1) * this.perpage + index + 1,   // 序号
                    `w.${item.shopid}u.${item.send_userid || 0}`  // 商户号+会员号
                ]

                // 实名信息
                this.must_submit.forEach(must => {
                    const user_must_submit = item.user_details?.must_submit || []
                    const info = user_must_submit.find(user => user.title === must.title)
                    data.push(info?.value || '')
                })

                data.push(item.title || '')  // 标题
                data.push(item.content || '')  // 用户反馈内容
                data.push(item.create_time || '')  // 提交时间

                tBody.push(data)
            })


            return [tHead, ...tBody]
        },

        async getRecordList(page = 1) {
            const res = await this.xwy_api.request({
                url: 'front.user.message/message_list',
                data: {
                    active_id: this.active_id,
                    get_user_details: 1,
                    page,
                    perpage: this.perpage
                }
            })

            if (page === 1) this.count = res?.data?.message_list?.total || 0

            return res?.data?.message_list?.data || []
        },

        uniPopupOpen(ref) {
            this.$refs[ref].open()
        },

        uniPopupClose(ref) {
            this.$refs[ref].close()
        },
    }
}
</script>

<style lang="scss">
.uni_popup {
    width: 280px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}

.uni_popup-btn {
    line-height: 40px;
    border-radius: 20px;
}

.uni_popup-btn::after {
    border: none;
}
</style>