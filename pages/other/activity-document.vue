<template>
    <view>
        <view v-if="document_list && top_tips" class="p10 font14" style="color: #e19898;">
            {{ top_tips }}
        </view>

        <view class="pt5 pb10">
            <view class="document-item" v-for="(item, index) in document_list" :key="index">
                <view class="document-title color-content">{{ item['area_name'] }}</view>
                <view>
                    <view class="download-item flex-row" v-for="(item_, index_) in item.list" :key="index_">
                        <view>
                            <!-- #ifdef H5 -->
                            <!--不知道为什么在浏览器上面，父元素download-item的上padding没有作用，icon会往上跑-->
                            <view class="pt10"></view>
                            <!-- #endif -->
                            <text
                                class="iconfont color-light-primary"
                                :class="fileIcon(item_['file_type']).icon"
                                style="font-size: 48px;"
                                :style="{color: fileIcon(item_['file_type']).color}"
                            ></text>
                        </view>
                        <view class="clearfix clear" style="width: 100%;">
                            <view class="color-title pl10">
                                {{ item_.title }}
                                <template v-if="item_['file_type']">.{{ item_['file_type'] }}</template>
                            </view>
                            <view class="fr flex-row">
                                <view class="pl10 pr10 ptm5 color-primary fr" hover-class="navigator-hover"
                                      @click="previewFile(item_.title, item_['file_type'], item_.url)"
                                >
                                    <uni-icons type="eye" size="16" color="#2d8cf0"/>
                                    <text style="padding-left: 1px;">预览</text>
                                </view>
                                <view class="pl10 pr10 ptm5 color-primary fr" hover-class="navigator-hover"
                                      @click="downloadFile(item_.url, item_['file_type'])"
                                >
                                    <uni-icons type="download" size="16" color="#2d8cf0"/>
                                    <text style="padding-left: 1px;">下载</text>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <view v-if="!document_list.length && !loading" class="text-center" style="padding-top: 20vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub font14">暂无文档</view>
        </view>

        <!-- #ifdef MP-WEIXIN -->
        <uni-popup ref="copy_web_src" type="center">
            <view class="export_ranking_popup text-center bg-white">
                <view class="popup-close" @click="$refs.copy_web_src.close()">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top bg-primary color-white" style="padding: 50px 20px 20px;">
                    <text class="iconfont color-white" :class="fileIcon(copy_web_src.file_type).icon" style="font-size: 80px;"></text>
                    <view class="font18">{{ copy_web_src.title }}</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ copy_web_src.content }}
                    </view>
                    <view
                        class="export_ranking-btn bg-info color-white"
                        hover-class="navigator-hover"
                        @click="copy(copy_web_src.src)"
                    >复制地址</view>
                </view>
            </view>
        </uni-popup>

        <!-- #endif -->
    </view>
</template>

<script>
export default {
    data() {
        return {
            loading: true,
            top_tips: '',
            document_list: [],
            copy_web_src: {
                file_type: '',
                title: '',
                content: '',
                src: ''
            }
        }
    },

    onLoad(params) {
        uni.showLoading({
            title: '文档加载中...',
            mask: true
        })
        this.types = Number(params.types)
        this.getDocumentList()
    },

    methods: {
        async getDocumentList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin.activeSet/active_download_set',
                data: {types: this.types}
            })
            uni.hideLoading()
            this.loading = false
            const res_data = res?.data?.['system_set']
            if (!res_data) return
            if (res_data.top_tips) this.top_tips = res_data.top_tips
            if (res_data['page_title']) uni.setNavigationBarTitle({title: res_data['page_title']})
            if (res_data['down_load_list']?.length) this.document_list = res_data['down_load_list']
        },

        fileIcon(file_type) {
            const unknown = {icon: 'icon-unknown-file', color: '#999999'}
            if (!file_type) return unknown

            const word = {icon: 'icon-word-file', color: '#5da3e8'},
                  excel = {icon: 'icon-excel-file', color: '#63c187'},
                  ppt = {icon: 'icon-ppt-file', color: '#f09573'},
                  pdf = {icon: 'icon-pdf-file', color: '#d0454d'}

            const icons = {
                'doc': word,
                'docx': word,
                'xlsx': excel,
                'xls': excel,
                'ppt': ppt,
                'pptx': ppt,
                'pdf': pdf
            }
            return icons[file_type] || unknown
        },

        previewFile(file_name, file_type, file_url) {
            let evn = 'MP-WEIXIN'
            // #ifdef H5
            evn = 'H5'
            // #endif

            if (evn === 'H5') {
                const office_view_url = 'https://view.officeapps.live.com/op/view.aspx?src='
                return window.open(office_view_url + encodeURIComponent(file_url))
            }

            console.log(file_url)
            const filePath = `${wx.env.USER_DATA_PATH}/${file_name}-${new Date().getTime()}.${file_type}`
            console.log(filePath)

            uni.showLoading({
                title: '正在打开文档...',
                mask: true
            })

            uni.downloadFile({
                url: file_url,
                filePath,
                success: () => {
                    uni.openDocument({
                        filePath,
                        showMenu: true,
                        success: () => {
                            uni.hideLoading()
                            console.log('打开文档成功')
                        },
                        fail: err => {
                            uni.hideLoading()
                            console.log('打开文档失败', err)
                            this.xwy_api.alert(err.errMsg, {title: '预览失败'})
                        }
                    })
                },
                fail: err => {
                    uni.hideLoading()
                    console.log('下载文档失败', err)
                    this.xwy_api.alert(err.errMsg, {title: '预览失败'})
                }
            })
        },

        downloadFile(file_url, file_type) {
            let evn = 'MP-WEIXIN'
            // #ifdef H5
            evn = 'H5'
            // #endif

            if (evn === 'H5') return window.open(file_url)

            this.copy_web_src = {
                file_type,
                title: '文档下载',
                content: '请复制文档下载地址到浏览器打开下载',
                src: file_url
            }
            this.$refs.copy_web_src.open()
        },

        copy(data) {
            uni.setClipboardData({
                data,
                success: () => this.$uni.showToast('复制成功', 'none', 500)
            })
        }
    }
}
</script>

<style lang="scss">
.document-item {
    margin: 0 15px 20px;
    padding: 10px;
    box-shadow: 0 0 10px #eee;
    border-radius: 10px;
    background-color: #fff;

    .document-title {
        position: relative;
        padding-bottom: 10px;
        padding-left: 10px;
        border-bottom: 1px solid #eee;
    }
    .document-title::before {
        content: "";
        position: absolute;
        top: 4px;
        left: 0;
        width: 4px;
        height: 15px;
        background-color: #5cadff;
    }

    .download-item {
        padding-top: 15px;
        padding-bottom: 10px;
        border-bottom: 1px dashed #eee;
    }
    .download-item:last-child {
        border-bottom: none;
        padding-bottom: 0;
    }
}

.export_ranking_popup {
    width: 280px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}

.export_ranking-btn {
    line-height: 40px;
    border-radius: 20px;
}

.export_ranking-btn::after {
    border: none;
}
</style>
