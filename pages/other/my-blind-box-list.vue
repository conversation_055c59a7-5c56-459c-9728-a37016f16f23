<template>
    <view class="page">
        <view class="user-info flex-row">

            <image class="headimg" :src="headimg" mode="aspectFill"/>
            <view class="pl10 color-white">
                <view>{{ nickname }}</view>
                <view>已获得{{ myCount }}个盲盒</view>
            </view>
        </view>

        <view class="list flex-row flex-wrap">
            <view class="item" v-for="item in list" :key="item.id">
                <image class="item-image" :class="{'no-get': !item.is_get}" :src="item.pic" mode="aspectFit"
                       @click="lookBig(item)"/>
                <view class="text-center color-white">{{ item.name }}</view>
            </view>
        </view>


        <view v-if="canDrawnPrizesList.length" class="to-lottery" @click="toLottery">
            <image class="to-lottery-image" src="https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/blind-box-lottery/list-box.png"/>
            <view class="can-drawn-prizes-count font14 color-white">
                {{ canDrawnPrizesList.length }}
            </view>
        </view>

        <blind-box-lottery ref="blindBoxLottery" :lottery-id="lottery_id" :active-id="active_id"
                               @complete="uploadRecord"/>
    </view>
</template>

<script>
const app = getApp()

export default {
    data() {
        return {
            active_id: null,
            lottery_id: null,
            nickname: app.globalData['userinfo'].nickname || '',
            headimg: app.globalData['userinfo'].headimg || '',
            list: [],
            myCount: 0,
            canDrawnPrizesList: []
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.lottery_id = params.lottery_id
        this.join = params.join

        this.init()
    },

    methods: {
        async init() {
            this.getUserDetails()
            await this.getLotteryDetails()
            await this.uploadRecord()
        },

        async uploadRecord() {
            await this.getRecord()
            await this.getLosingLotteryPointIds()
            await this.getCanDrawnPrizesList()
        },

        async getLotteryDetails() {
            const res = await this.xwy_api.request({
                url: 'front.flat.active.lottery_active.admin.manage_lottery/lottery_active_details',
                data: {
                    active_id: this.lottery_id
                }
            })

            this.lotteryDetails = res?.data?.active_details || null
            this.prizeList = res?.data?.['prize_list'] || []
        },

        async getRecord() {
            const all_list = this.getAll()
            const my_list = await this.getMy()
            this.myGetList = my_list

            this.list = []

            this.list = all_list.map(item => ({
                ...item,
                is_get: my_list.some(my => my.prize_id === item.id)
            })).sort((v1, v2) => v2.is_get - v1.is_get)

            this.myCount = this.list.filter(item => item.is_get).length
        },

        getAll() {
            return this.prizeList.map(item => ({
                id: item.id,
                name: item.name,
                pic: item.pic
            }))
        },

        async getMy() {
            if (!this.join) return []

            const res = await this.xwy_api.request({
                url: 'front.flat.active.lottery_active.user.lottery/lottery_records_list',
                data: {
                    active_id: this.active_id,
                    lottery_active_id: this.lottery_id,
                    lottery_prize_types: 2,  // 【2】代表是查看盲盒抽奖的记录
                    page: 1,
                    page_size: 1000
                }
            })

            const list = res?.data?.['lottery_record_list']?.data
            if (!list?.length) return []

            return list.map(item => ({
                id: item.record_id,
                prize_id: item.conf_json?.prize_id || null,
                // 当时抽奖的点位ID，用来筛选哪些点抽奖了，哪些点没抽奖，没抽奖的可以重新抽奖
                point_id: item.logs_types || null
            }))
        },


        // 获取未中奖记录，用来筛选哪些点位抽过奖
        async getLosingLotteryPointIds() {
            const res = await this.xwy_api.request({
                url: 'front.flat.active.lottery_active.user.lottery/lottery_records_list',
                data: {
                    active_id: this.active_id,
                    lottery_active_id: this.lottery_id,
                    is_lottery: 2,  // 只查看未中奖记录
                    page: 1,
                    page_size: 1000
                }
            })

            const list = res?.data?.['lottery_record_list']?.data || []

            const ids = list.filter(item => item.logs_types).map(item => item.logs_types)
            this.losingLotteryPointIds = Array.from(new Set(ids)) // ids去重
        },

        async getCanDrawnPrizesList() {
            if (!this.join) return

            // 获取抽奖配置，找出抽奖活动中配置的抽奖点位ID
            const {condition, active_list} = this.lotteryDetails?.conf?.limit || {}
            if (condition !== 222) return
            if (!active_list?.length) return
            const haveLotteryPoints = active_list.filter(item => item.active_types === 3).map(item => item.id)

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_map_set',
                data: {
                    active_id: this.active_id
                }
            })

            const point_list = res?.data?.map_set?.markers || []
            if (!point_list.length) return

            // 用户当前到达的点位ID
            const my_position_id = res?.data?.my_position?.['current_point_details']?.id || null
            if (!my_position_id) return

            // 用户当前到达第几个点位
            const my_position_index = point_list.findIndex(item => item.id === my_position_id)
            if (my_position_index === -1) return

            // 只需要用户已经过和到达的点位
            const user_point_list = point_list.slice(0, my_position_index + 1)

            // 匹配用户已到达的点位中有配置在抽奖活动中的点位
            const haveLotteryPointList = user_point_list.filter(item => haveLotteryPoints.includes(item.id))
            if (!haveLotteryPointList.length) return

            // 和用户的抽奖记录比对，找出用户还未抽奖的点位
            const isGetPointIds = this.myGetList.map(item => item.point_id)
            const losingLotteryPointIds = this.losingLotteryPointIds
            const ids = Array.from(new Set([...isGetPointIds, ...losingLotteryPointIds]))
            this.canDrawnPrizesList = haveLotteryPointList.filter(point => !ids.includes(point.id))
                .map(item => item.id)
        },

        toLottery() {
            this.$nextTick(() => this.$refs.blindBoxLottery?.show(this.canDrawnPrizesList[0]))
        },



        getUserDetails() {
            this.getOpenerEventChannel?.()?.on('userDetails', res => {
                if (res?.must_submit?.[0]?.value) this.nickname = res.must_submit[0].value
                if (res?.headimg) this.headimg = res.headimg
            })
        },

        lookBig(item) {
            if (!item.is_get) return this.$uni.showToast(`未获得 ${item.name}`)

            this.$uni.previewImage({
                current: item.pic,
                urls: this.list.filter(item => item.is_get).map(item => item.pic)
            })
        }
    }
}
</script>

<style lang="scss">
.page {
    width: 100vw;
    min-height: 100vh;
    box-sizing: border-box;
    background: radial-gradient(circle at 20px 20px, #AABEFA 5px, #B7C9FB 0);
    background-size: 30px 30px;
}

.user-info {
    padding: 15px;
    line-height: 30px;

    .headimg {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: block;
    }
}

.list {
    padding: 15rpx;

    .item {
        padding: 20rpx 15rpx;
        width: 210rpx;

        .item-image {
            width: 210rpx;
            height: 210rpx;
        }

        .no-get {
            filter: grayscale(1);
        }
    }
}

.to-lottery {
    position: fixed;
    bottom: 50px;
    right: 40rpx;
    animation: zoom 1s infinite alternate linear;

    .to-lottery-image {
        width: 60px;
        height: 60px;
        display: block;
    }

    .can-drawn-prizes-count {
        position: absolute;
        right: -8px;
        top: -5px;
        min-width: 14px;
        padding: 4px;
        height: 14px;
        line-height: 14px;
        text-align: center;
        background-color: rgba(0, 0, 0, .5);
        border-radius: 12px;
    }

    @keyframes zoom {
        from {
            transform: scale(1);
        }
        to {
            transform: scale(1.2);
        }
    }
}
</style>