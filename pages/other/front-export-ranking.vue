<template>
    <view>
        <template v-if="total_page">
            <view class="font14 p10" style="color: #e19898;">
                共{{ count }}条记录，需要分{{ total_page }}次导出，每次导出{{ perpage }}条数据。
            </view>

            <view class="p10 font14 color-content">
                <text>每次导出</text>
                <text class="plr5">{{ perpage }}</text>
                <text>条数据</text>
                <text class="pl10 color-primary" @click="$refs.perpage.open()">修改</text>
            </view>
        </template>


        <view class="pb10">
            <view class="flex-kai" v-for="(item, index) in total_page" :key="item">
                <view class="color-title p10">
                    <text class="color-sub">{{ index + 1 }}、</text>
                    <text>{{ index * perpage + 1 }}</text>
                    <text class="plr5">-</text>
                    <text>
                        <template v-if="index + 1 === total_page">{{ count }}</template>
                        <template v-else>{{ (index + 1) * perpage }}</template>
                    </text>
                </view>

                <view class="color-primary p10" @click="exportData(index + 1)">导出</view>
            </view>
        </view>


        <uni-popup ref="perpage" type="dialog" mode="input" :is-mask-click="false">
            <uni-popup-dialog
                mode="input"
                title="每次导出数据量"
                :value="perpage"
                :placeholder="'请输入 100 - ' + max_count + ' 内的数字'"
                @confirm="perpageInputConfirm"
            ></uni-popup-dialog>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
import openExcelFile from '@/utils/open-excel-file'

export default {
    data() {
        return {
            count: 0,
            total_page: 0,
            perpage: 1000,
            max_count: 1000,
            src_list: [],
            types: 0
        }
    },

    computed: {
        rankingName() {
            const config = {
                61: '积分勋章'
            }
            return config[this.types]
        }
    },

    watch: {
        rankingName(val) {
            this.$uni.setNavigationBarTitle(`${val}排行榜导出`)
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.types = Math.floor(params.types)
        this.$uni.showLoading()

        this.init()
    },

    methods: {
        async init() {
            await this.getActiveDetails()
            if (this.teamOpen) await this.getTeamList()
            await this.getOne()
        },

        async getActiveDetails() {
            let details = app.globalData.activity_detail

            if (!details || details.active_id !== this.active_id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.active_id
                    }
                })

                details = res?.data?.active_details
            }

            if (!details) return

            this.must_submit = details.conf?.must_submit || []
            this.integralUnit = details.conf?.active?.integral?.unit || '积分'
            this.teamOpen = details.rank_set?.team_group_open
        },


        async getTeamList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/team_group_list',
                data: {
                    active_id: this.active_id,
                    page: 1,
                    perpage: 1000
                }
            })

            const list = res?.data?.team_group_list?.data || []
            this.teamList = list.map(({id, name}) => ({id, name}))
        },

        async getOne() {
            const list = await this.getRecordList(1)
            uni.hideLoading()

            if (!list?.length) return this.$uni.showModal('暂无记录', {success: () => uni.navigateBack()})

            if (this.count <= this.perpage) {
                const tableData = this.excelDataProcessing(list, 1)
                openExcelFile.openDocument(tableData, `${this.rankingName}排行榜`)
                return this.$uni.navigateBack()
            }

            this.setTotalPage()
        },

        async exportData(page) {
            this.$uni.showLoading('导出中...')
            const list = await this.getRecordList(page)
            const tableData = this.excelDataProcessing(list, page)
            openExcelFile.openDocument(tableData, `${this.rankingName}排行榜 - 第${page}页`)
            uni.hideLoading()
        },

        perpageInputConfirm(val) {
            const perpage = Number(val)
            if (isNaN(perpage) || perpage < 100) {
                return this.$uni.showModal(`输入错误，请输入 100 - ${this.max_count} 内的数字`, {
                    success: () => this.$refs.perpage.open()
                })
            }
            this.perpage = perpage
            this.setTotalPage()
        },

        setTotalPage() {
            this.total_page = Math.ceil(this.count / this.perpage)
            this.src_list = []
        },

        excelDataProcessing(list, page) {
            const tHead = ['序号', '系统标识']
            this.must_submit.forEach(item => tHead.push(item.title))
            if (this.teamOpen) tHead.push('队伍')
            if (this.types === 61) {
                tHead.push(`${this.integralUnit}`)
                tHead.push('勋章数')
            }

            const tBody = []

            list.forEach((item, index) => {
                const must_submit_value = this.must_submit.map(a => {
                    const user_must_submit = item.must_submit || []
                    return user_must_submit.find(u => u.title === a.title)?.value || ''
                })

                const data = [
                    (page - 1) * this.perpage + index + 1,               // 序号
                    `w.${app.globalData['who']}u.${item.userid || 0}`, // 商户号+会员号
                    ...must_submit_value,    // must_submit
                ]

                // 队伍
                if (this.teamOpen) data.push(this.teamList.find(t => t.id === item.team_id)?.name || '')

                if (this.types === 61) {
                    data.push(item.integral_all || 0)  // 积分
                    data.push(item.medal_num || 0)  // 勋章数
                }

                tBody.push(data)
            })


            return [tHead, ...tBody]
        },

        async getRecordList(page = 1) {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.step/top_rank_list',
                data: {
                    active_id: this.active_id,
                    top_rank_types: this.types,
                    page,
                    perpage: this.perpage
                }
            })

            if (page === 1) this.count = res?.data?.top_rank_list?.list?.total || 0

            return res?.data?.top_rank_list?.list?.data || []
        }
    }
}
</script>

<style lang="scss">
.uni_popup {
    width: 280px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}

.uni_popup-btn {
    line-height: 40px;
    border-radius: 20px;
}

.uni_popup-btn::after {
    border: none;
}
</style>