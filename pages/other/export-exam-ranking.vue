<template>
    <view>
        <view class="font14 p10" style="color: #e19898;">
            <view v-if="totalPage > 1">
                <view>
                    共{{ personCount }}条记录，需要分{{ totalPage }}次导出，每次导出{{ perpage }}条排行榜数据。
                </view>
            </view>
        </view>

        <view v-if="totalPage > 1" class="p10 font14 color-content">
            <text>每次导出</text>
            <text class="plr5">{{ perpage }}</text>
            <text>条数据</text>
            <text class="pl10 color-primary" @click="$refs.perpage.open()">修改</text>
        </view>

        <uni-popup ref="perpage" type="dialog" mode="input" :is-mask-click="false">
            <uni-popup-dialog mode="input" title="每次导出数据量" :value="perpage"
                              :placeholder="'请输入 100 - ' + maxCount + ' 内的数字'"
                              @confirm="perpageInputConfirm"/>
        </uni-popup>


        <view v-if="totalPage > 1" class="pb10">
            <view class="p10 flex-kai" v-for="(item, index) in src_list" :key="index">
                <view class="color-title">
                    <text class="color-sub">{{ index + 1 }}、</text>
                    <text>{{ index * perpage + 1 }}</text>
                    <text class="plr5">-</text>
                    <text>
                        <template v-if="index + 1 === totalPage">{{ personCount }}</template>
                        <template v-else>{{ (index + 1) * perpage }}</template>
                    </text>
                </view>
                <view class="flex-kai" style="width: 155px;">
                    <view>
                        <text v-if="item.src" class="color-disabled">已导出</text>
                        <text v-else class="color-primary" @click="exportData(index)">导出</text>
                    </view>
                    <view :class="[item.src ? 'color-primary' : 'color-disabled']"
                          @click="copyDownloadSrc(item.src)">
                        复制下载地址
                    </view>
                </view>
            </view>
        </view>


        <uni-popup ref="exportSuccess" type="center">
            <view class="uni_popup text-center bg-white">
                <view class="top bg-primary color-white" style="padding: 50px 20px 20px;">
                    <icon type="success" size="80" color="#ffffff"></icon>
                    <view class="font18">导出成功</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ export_success_tips }}
                    </view>
                    <view
                        class="uni_popup-btn bg-info color-white"
                        hover-class="navigator-hover"
                        @click="copyDownloadSrc(download_src, false)"
                    >复制下载地址
                    </view>
                </view>
            </view>

            <view class="flex-all-center">
                <uni-icons type="close" size="34" color="#ffffff" @click="popupClose"/>
            </view>
        </uni-popup>
    </view>
</template>

<script>

import my_storage from "@/utils/storage"

export default {
    data() {
        return {
            personCount: 0,
            perpage: 1000,
            totalPage: 1,
            maxCount: 3000,
            src_list: [],
            export_success_tips: '',
            download_src: ''
        }
    },


    onLoad(params) {
        this.exam_id = params.exam_id

        this.$login.uniLogin(() => this.init())
    },

    methods: {
        async init() {
            this.$uni.showLoading()
            await this.getPeopleTotal()
        },

        async getPeopleTotal() {
            const res = await this.xwy_api.request({
                url: 'front.flat.exam.user_answer/user_top_rank_list',
                data: {
                    exam_id: this.exam_id,
                    page: 1,
                    perpage: 1
                }
            })
            uni.hideLoading()

            const count = res?.data?.top_rank_list?.total || 0
            if (count <= this.maxCount) return this.exportOnePage()

            this.personCount = count
            this.setTotalPage()
        },

        setTotalPage() {
            this.totalPage = Math.ceil(this.personCount / this.perpage)

            let src_list = []
            for (let i = 0; i < this.totalPage; i++) {
                src_list.push({src: ''})
            }
            this.src_list = src_list
        },

        perpageInputConfirm(val) {
            const perpage = Number(val)
            if (isNaN(perpage) || perpage < 100 || perpage > this.maxCount) {
                this.$uni.showToast(`输入错误，请输入 100 - ${this.maxCount} 内的数字`)
                return setTimeout(() => this.$refs.perpage.open(), 500)
            }
            this.perpage = perpage
            this.setTotalPage()
        },

        async getDownloadSrc(page, perpage) {
            const res = await this.xwy_api.request({
                url: 'front.flat.exam.export/export_exam_user_toprank_data',
                data: {
                    exam_id: this.exam_id,
                    page,
                    perpage
                }
            })

            return {
                src: res?.data?.url,
                info: res?.info || (res?.status === 1 ? '导出成功' : '导出失败')
            }
        },

        async exportOnePage() {
            this.$uni.showLoading('导出中...')
            const res = await this.getDownloadSrc(1, this.maxCount)
            uni.hideLoading()

            if (!res.src) return this.$uni.showModal(res.info, {success: () => uni.navigateBack()})

            this.download_src = res.src
            this.export_success_tips = res.info

            my_storage.setExportExcelRecord({
                url: res.src,
                title: `导出考卷排行榜`
            })

            this.$nextTick(() => this.$refs.exportSuccess.open())
        },

        async exportData(index) {
            const page = index + 1

            this.$uni.showLoading()
            const res = await this.getDownloadSrc(page, this.perpage)
            uni.hideLoading()

            const src = res.src
            if (!src) return this.$uni.showModal(res.info)

            this.src_list[index].src = src
            this.copyDownloadSrc(src)
            this.$uni.showToast('已导出，请复制下载地址')

            my_storage.setExportExcelRecord({
                url: src,
                title: `导出考卷排行榜 第${index * this.perpage + 1}条至第${page * this.perpage}条数据`
            })
        },

        copyDownloadSrc(src, hide = false) {
            this.$uni.setClipboardData(src, hide ? '' : '已复制', hide ? 'none' : 'success')
        },

        popupClose() {
            this.$refs.exportSuccess.close()
            this.$uni.navigateBack()
        }
    }
}
</script>

<style lang="scss">
.uni_popup {
    width: 280px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.uni_popup-btn {
    line-height: 40px;
    border-radius: 20px;
}

.uni_popup-btn::after {
    border: none;
}
</style>