<template>
    <view class="page">
        <scroll-view class="scroll-view bg-white" scroll-x>
            <view class="flex-row">
                <view
                    v-for="(item, index) in type_list"
                    :key="index"
                    class="scroll-view-item color-content"
                    :class="{'active-type': item.id === type_id}"
                    @click="changeType(item.id)"
                >{{ item.name }}</view>
            </view>
        </scroll-view>

        <view v-if="type_id === 'my_image' && max_storage" class="pb10">
            <view class="flex-kai pl10">
                <view class="max-storage bg-divider">
                    <view
                        class="used-storage bg-primary"
                        :style="{'width': used_storage / max_storage * 100 + '%'}"
                    ></view>
                </view>
                <view class="pr10">
                    <text class="color-content font14">{{ max_storage }}M</text>
                    <text class="iconfont color-sub icon-sync pl10" @click="reloadUsedStorage(true)"></text>
                </view>
            </view>
            <view class="flex-row color-sub font14 p10 pt5">
                <view>已用空间 ({{ used_storage }}M)</view>
                <view style="padding-left: 30px;">可用空间 ({{ surplus_storage }}M)</view>
            </view>
        </view>

        <view class="user-src">
            <textarea maxlength="-1" auto-height v-model="user_src" placeholder="输入图片地址并点击确定按钮"></textarea>
        </view>
        <view class=" pl10">
            <button size="mini" type="primary" @click="selectImageSuccess(user_src)">确定</button>
        </view>

        <view class="add-image text-center" @click="chooseImage">
            <uni-icons type="plusempty" size="48" color="#eeeeee"/>
        </view>


        <view class="img-list flex-row flex-wrap">
            <view class="img-view" v-for="(item, index) in image_list" :key="index">
                <image
                    class="img-item"
                    :src="item.url"
                    mode="widthFix"
                    @click="selectImageSuccess(item.url)"
                />
                <view
                    v-if="type_id === 'my_image' && who !== 35211"
                    class="delete flex-all-center"
                    @click="deleteMyImage(item.id)"
                >
                    <uni-icons type="closeempty" color="#e20f04"/>
                </view>
            </view>
        </view>

        <uni-load-more v-if="loading" status="loading"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && image_list.length > 6"
            status="noMore"
            :contentText="{contentnomore: '没有更多图片了'}"
        ></uni-load-more>
        <uni-load-more
            v-if="is_last_page && !image_list.length"
            status="noMore"
            :contentText="{contentnomore: '暂无图片，请切换至其它分类试试吧！'}"
        ></uni-load-more>
        <uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>

        
    </view>
</template>

<script>
const app = getApp()

import login from '../../utils/api/login.js'
import xwy_api from '../../utils/api/xwy_api.js'
import utils from '../../utils/utils.js'


export default {
    data() {
        return {
            type_id: 'my_image',
            type_list: [
                {name: '我的图片', id: 'my_image'}
            ],
            image_list: [],
            load_page: 1,
            is_last_page: false,
            loading: true,
            user_src: '',
            image_list_1: [],
            image_list_2: [],
            max_storage: 0,
            used_storage: 0,
            surplus_storage: 0,
            who: app.globalData['who']
        }
    },
    onLoad(e) {
        this.image_key = e.key
        if (e.active_id) this.active_id = e.active_id
        if (e.is_temp) this.is_temp = e.is_temp
        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getImageTypeList()
            this.getImageList()
            this.getUsedStorage()
        })
    },
    onReachBottom() {
        if (!this.is_last_page && !this.loading) this.getImageList()
    },
    methods: {
        async reloadUsedStorage(show_loading = false) {
            show_loading && uni.showLoading({
                mask: true
            })

            await xwy_api.request({
                url: 'front.user.userStorage/refresh_user_storage',
                data: {
                    access_token: app.globalData['access_token']
                }
            })

            show_loading && uni.hideLoading()

            await this.getUsedStorage()
        },
        async getUsedStorage() {
            const {used_storage, max_storage} = await xwy_api.getUsedStorage()
            const _used_storage = Number((used_storage / 1024).toFixed(2))
            this.max_storage = max_storage
            this.used_storage = _used_storage
            this.surplus_storage = Number((max_storage - _used_storage).toFixed(2))
        },


        getImageTypeList() {
            // 这些活动不显示图片分类
            const noShowIds = ['5a56536807e0970200f7954a6a0e11cd', 'e152b18ac28433bfb0bb779d8dad82f5']
            if (this.active_id && noShowIds.includes(this.active_id)) return

            xwy_api.ajax({
                url: 'front.system.flat_pic/pic_category_list',
                data: {
                    access_token: app.globalData['access_token'],
                    category_id: app.globalData['shop_info']?.['extend_set']?.['upload_pic_flat_category_id'] || ''
                },
                success: res => {
                    if (res?.data?.['pic_category_list']?.length) {
                        const list = res.data['pic_category_list']
                        this.type_list = [...this.type_list, ...list]
                    }
                }
            })
        },

        changeType(id) {
            this.load_page = 1
            this.type_id = id
            this.getImageList()
        },

        getImageList() {
            if (this.load_page === 1) {
                this.image_list = []
                this.is_last_page = false
            }


            this.type_id === 'my_image' ? this.getUserImageList() : this.getShopImageList()
        },


        async getUserImageList() {
            this.loading = true

            const data = {
                page: this.load_page,
                perpage: 20,
                access_token: app.globalData['access_token']
            }
            const res = await xwy_api.request({url: 'front.upload/upload_list', data})
            this.loading = false

            this.load_page++
            if (res?.['data']?.['upload_data']) {
                const data = res['data']['upload_data']
                const list = data.data || []
                this.image_list = [...this.image_list, ...list]
                this.is_last_page = data.is_lastpage
            } else {
                this.is_last_page = true
            }
        },

        async getShopImageList() {
            this.loading = true

            const res = await xwy_api.request({
                url: 'front.system.flat_pic/pic_list',
                data: {
                    access_token: app.globalData['access_token'],
                    category_id: this.type_id,
                    page: this.load_page,
                    perpage: 20,
                }
            })

            this.loading = false
            this.load_page++
            if (res?.['data']?.pic_list) {
                const data = res.data.pic_list
                const list = data.data || []
                this.image_list = [...this.image_list, ...list]
                this.is_last_page = data.is_lastpage
            } else {
                this.is_last_page = true
            }


        },

        async chooseImage() {
            if (this.max_storage && this.surplus_storage <= 0) return xwy_api.alert('当前存储空间已用完，请联系客服扩容')
            
            uni.chooseImage({
                count: 1,
                success: async res => {
                    console.log(res);
                    // #ifndef H5
                    const img_big_size = this.xwy_config.activeUserUploadImageSizeLimit(this.active_id || '')   // 图片最大大小限制，单位M
                    const size_check = utils.checkImageSize(res.tempFiles, img_big_size * 1024 * 1024)
                    if (!size_check) return xwy_api.alert('图片大小超过' + img_big_size + 'M，无法上传')
                    // #endif

                    uni.showLoading({
                        title: '图片上传中...',
                        mask: true
                    })
                    

                    const img_data = {}
                    if (this.active_id) img_data['active_id'] = this.active_id
                    if (this.is_temp) img_data.is_temp = this.is_temp
                    
                    let isH5 = false
                    // #ifdef H5
                    isH5 = true
                    // #endif

                    if (isH5) {
                        img_data.temp_data = {file: res.tempFiles[0]}
                    } else {
                        img_data.temp_data = {
                            size: res.tempFiles[0].size, 
                            path: res.tempFilePaths[0]
                        }
                    }
                    
                    const src = await xwy_api.uploadOneImage(img_data)

                    uni.hideLoading()
                    this.selectImageSuccess(src)
                }
            })
        },


        selectImageSuccess(src) {
            if (!src) {
                uni.showToast({
                    title: '请输入图片地址',
                    icon: 'error'
                })
                return false
            }
            if (!src.startsWith('http://') && !src.startsWith('https://')) {
                uni.showToast({
                    title: '图片地址不正确',
                    icon: 'error'
                })
                return false
            }
            if (this.image_key) {
                const pages = getCurrentPages()
                const up_page = pages[pages.length - 2]
                console.log(up_page)
                up_page.$vm[this.image_key] = src
            } else {
                const eventChannel = this.getOpenerEventChannel()
                eventChannel.emit('newImg', src)
            }

            uni.navigateBack()
            uni.hideLoading()
        },

        deleteMyImage(id) {
            uni.showModal({
                title: '提示',
                content: '确定删除该图片?',
                success: res => {
                    if (res.confirm) this.deleteImage(id)
                }
            })
        },

        async deleteImage(id) {
            uni.showLoading({
                title: '删除中...',
                mask: true
            })

            const res = await xwy_api.request({
                url: 'front.upload/del_pic',
                data: {
                    access_token: app.globalData['access_token'],
                    ids: id
                }
            })

            if (!res || !res['status']) {
                xwy_api.alert(res && res['info'] || '删除失败')
                return false
            }

            let tips = '删除成功'
            if (res['info']) tips = res['info']

            uni.showToast({
                title: tips,
                icon: tips.length < 8 ? 'success' : 'none'
            })

            this.load_page = 1
            this.image_list = []
            await this.getUserImageList()
            await this.reloadUsedStorage(false)
        }
    }
}
</script>

<style scoped>
.page {
    padding-top: 50px;
    padding-bottom: 10px;
}

.scroll-view {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    padding: 0 10px;
    box-sizing: border-box;
    z-index: 99999;
}

.scroll-view-item {
    padding: 0 10px;
    line-height: 40px;
    height: 40px;
    white-space: nowrap;
    box-sizing: border-box;
}

.active-type {
    color: #007AFF;
    border-bottom: 2px solid #007AFF;
}

.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
    width: 100px;
    height: 100px;
    border-radius: 5px;
    line-height: 95px;
    margin-left: 15px;
}

.user-src {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
    border: 1px solid #eee;
}

.img-list {
    padding: 5px;
}

.img-view {
    position: relative;
    margin: 10px;
    width: calc(50% - 25px);
    height: auto;
}

.delete {
    position: absolute;
    right: 5px;
    top: 5px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, .7);
}

.img-item {
    width: 100%;
    height: auto;
    border-radius: 5px;
    display: block;
}

.max-storage, .used-storage {
    height: 15px;
    border-radius: 5px;
}

.max-storage {
    width: calc(100% - 80px);
    margin-top: 5px;
    position: relative;
}

.used-storage {
    position: absolute;
    top: 0;
    left: 0;
    max-width: 100%;
}

/* #ifdef H5 */

@media screen and (min-width: 500px) {
    .scroll-view {
        width: 500px;
        left: calc(((100vw - 500px) / 2) - 10px);
    }
}

/* #endif */
</style>
