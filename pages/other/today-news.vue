<template>
    <view class="pb10 pl10 pr10">
        <view class="text-center p10 pt15" style="height: 150px;">
            <view class="color-light-primary">{{ year }}年{{ month }}月{{ day }}日 {{ week }}</view>
            <text :class="'iconfont day-icon color-light-primary icon-calendar-' + day"></text>
        </view>

        <view v-if="loading" class="text-center" style="padding-top: 20vh;">
            <load-ani />
            <view class="pt5 font12 color-sub">今日简讯加载中</view>
        </view>

        <template v-if="news_list.length">
            <!-- #ifdef MP-WEIXIN -->
            <view class="p10 clear clearfix">
                <button class="button-none fr" open-type="share">
                    <text class="iconfont icon-share color-sub"></text>
                    <text class="color-sub pl5">分享给好友</text>
                </button>
            </view>
            <!-- #endif -->

            <view class="news-list pt15">
                <view v-if="title" class="pl10 color-title">{{ title }}</view>
                <view class="news-item color-content" v-for="(item, index) in news_list" :key="index">
                    <text>{{ index + 1}}、</text>
                    <text>{{ item }}</text>
                </view>
            </view>

            <view class="clearfix clear p10 pt0">
                <view class="fr" @click="copyNews">
                    <text class="iconfont icon-copy color-sub"></text>
                    <text class="color-sub pl5">一键复制</text>
                </view>
            </view>
        </template>



        <view v-if="!loading && !news_list.length" style="padding-top: 20vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">今日暂无简讯</view>
        </view>

        <template v-if="!loading && show_ad">
            <xwy-ad :activity_id="active_id || ''" :ad_type="3"></xwy-ad>
            <xwy-ad :activity_id="active_id || ''" :ad_type="66"></xwy-ad>
        </template>


        <view v-if="one_sentence_day" class="p10 pt15">
            <view class="color-title">今日微语</view>
            <view class="pt5 color-content">{{ one_sentence_day }}</view>
        </view>

        
    </view>
</template>

<script>
import login from '@/utils/api/login.js'

const date = new Date()
const weeks = ['天', '一', '二', '三', '四', '五', '六']

export default {
    data() {
        return {
            loading: true,
            active_id: null,
            show_ad: false,
            title: '',
            news_list: [],
            one_sentence_day: '',
            year: date.getFullYear(),
            month: date.getMonth() + 1,
            day: date.getDate(),
            week: '星期' + weeks[date.getDay()]
        }
    },

    onShareAppMessage() {
        let path = '/pages/other/today-news'
        if (this.active_id) path += `?active_id=${this.active_id}`
        if (!this.show_ad) {
            if (this.active_id) path += '&close_ad=1'
            else path += '?close_ad=1'
        }
        return {
            title: this.title || '今日简讯',
            path
        }
    },

    onLoad(params) {
        if (params.active_id) this.active_id = params.active_id
        if (!params.close_ad) this.show_ad = true
        login.uniLogin(() => {
            this.getNewsData()
        })
    },

    methods: {
        async getNewsData() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.historyToday/today_news_list'
            })
            this.loading = false
            if (res?.data?.['copy_short_link']) this.ShortLink = res.data['copy_short_link']
            const news_data = res?.data?.news_list
            if (!news_data) return
            if (news_data.title) this.title = news_data.title
            // if (news_data.date) this.date = news_data.date
            if (news_data['weiyu']) this.one_sentence_day = news_data['weiyu']
            if (news_data.news) this.news_list = news_data.news
        },

        async copyNews() {
            if (!this.news_list.length) return uni.showToast({ title: '暂无简讯内容', icon: 'none' })

            let text = `\n【今日简讯 | ${this.year}年${this.month}月${this.day}日】\n${this.title}\n`
            this.news_list.forEach((item, index) => {
                text += `\n${index + 1}、${item}\n`
            })

            if (this.ShortLink) text += `\n${this.ShortLink}\n`

            uni.setClipboardData({
                data: text,
                success: () => uni.showToast({ title: '复制成功', icon: 'none' })
            })
        }
    }
}
</script>

<style lang="scss">
.day-icon {
    font-size: 120px;
    
    //在h5上，iconfont好像感知不到前面元素的存在
    /* #ifdef H5 */
    position: relative;
    top: 45px;
    /* #endif */
}
.news-list {
    .news-item {
        padding: 10px;
    }
}
</style>
