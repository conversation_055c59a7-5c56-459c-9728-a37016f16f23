<template>
    <view>
        <view class="container">
            <textarea class="textarea color-content" v-model="text" auto-focus="true"
                      placeholder="请输入内容" maxlength="-1"/>
        </view>

        <view class="buttons flex-kai">
            <view class="button-small bg-disabled" hover-class="navigator-hover" @click="clear">清空</view>
            <view class="button-big bg-light-primary" hover-class="navigator-hover" @click="pasteAndCopy">
                粘贴并复制
            </view>
            <view class="button-small bg-light-primary" hover-class="navigator-hover" @click="copy">复制</view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            text: ""
        }
    },

    methods: {
        async clear() {
            if (this.text === '') return
            const res = await this.$uni.showModal('确定清空已输入的文本？', {showCancel: true})
            if (res.confirm) this.text = ''
        },

        getClipboardData() {
            return new Promise(resolve => {
                uni.getClipboardData({
                    success: res => resolve(res.data),
                    fail: err => {
                        this.$uni.showModal(JSON.stringify(err), {title: '粘贴失败'})
                        resolve('error')
                    }
                })
            })
        },

        wordProcessing() {
            return this.text.replace(/#{1,6} |\*|- |\+ |~~~|---/g, '')
                .replace(/`(.*)`/, '$1')
                .replace(/_(.*)_/, '$1')
                .replace(/_{2}(.*)_{2}/, '$1')
                .replace(/_{3}(.*)_{3}/, '$1')
                .replace(/_{4}(.*)_{4}/, '$1')
                .replace(/_{5}(.*)_{5}/, '$1')
                .replace(/_{6}(.*)_{6}/, '$1')
                .replace(/~~(.*)~~/, '$1')
        },

        copy() {
            if (this.text === '') return this.$uni.showToast('无可复制的内容')

            this.text = this.wordProcessing()

            this.$uni.setClipboardData(this.text, '已复制')
        },

        async pasteAndCopy() {
            const text = await this.getClipboardData()
            if (text === 'error') return
            this.text = text

            this.copy()
        },
    }
}
</script>

<style lang="scss">
.container {
    padding: 10px;

    .textarea {
        width: calc(100vw - 20px);
        height: calc(100vh - 100px);
        padding: 5px;
        border: 1px solid #eee;
        box-sizing: border-box;
        border-radius: 4px;
    }
}

.buttons {
    padding: 10px 20rpx;

    .button-small, .button-big {
        line-height: 44px;
        border-radius: 4px;
        text-align: center;
        color: #fff;
    }

    .button-big {
        width: 350rpx;
    }

    .button-small {
        width: 160rpx;
    }
}
</style>