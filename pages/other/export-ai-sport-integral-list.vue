<template>
    <view class="page">

        <picker :range="aiSportList" range-key="name" @change="typesPickerChange">
            <view class="types-picker flex-kai">
                <view>
                    <text v-if="types">{{ typesName }}</text>
                    <text v-else class="color-sub">请选择运动类型</text>
                </view>
                <view>
                    <uni-icons type="forward" size="16" color="#80848f"/>
                </view>
            </view>
        </picker>

        <view v-if="!types" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-export-excel color-disabled" style="font-size: 100px;"></text>
            <view class="color-content">请选择需要导出的运动类型</view>
        </view>

        <template v-if="total_page">
            <view class="font14 p10" style="color: #e19898;">
                共{{ count }}条记录，需要分{{ total_page }}次导出，每次导出{{ perpage }}条数据。
            </view>

            <view class="p10 font14 color-content">
                <text>每次导出</text>
                <text class="plr5">{{ perpage }}</text>
                <text>条数据</text>
                <text class="pl10 color-primary" @click="$refs.perpage.open()">修改</text>
            </view>
        </template>


        <view class="pb10">
            <view class="flex-kai" v-for="(item, index) in total_page" :key="item">
                <view class="color-title p10">
                    <text class="color-sub">{{ index + 1 }}、</text>
                    <text>{{ index * perpage + 1 }}</text>
                    <text class="plr5">-</text>
                    <text>
                        <template v-if="index + 1 === total_page">{{ count }}</template>
                        <template v-else>{{ (index + 1) * perpage }}</template>
                    </text>
                </view>

                <view class="color-primary p10" @click="exportData(index + 1)">导出</view>
            </view>
        </view>


        <uni-popup ref="perpage" type="dialog" mode="input" :is-mask-click="false">
            <uni-popup-dialog
                mode="input"
                title="每次导出数据量"
                :value="perpage"
                :placeholder="'请输入 100 - ' + max_count + ' 内的数字'"
                @confirm="perpageInputConfirm"
            ></uni-popup-dialog>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
import openExcelFile from "@/utils/open-excel-file"

export default {
    data() {
        return {
            count: 0,
            total_page: 0,
            perpage: 1000,
            max_count: 3000,
            aiSportList: [],
            types: 0
        }
    },

    computed: {
        typesName() {
            return this.aiSportList.find(item => item.types === this.types)?.name || this.types || ''
        }
    },

    onLoad(params) {
        this.active_id = params.active_id

        this.$uni.showLoading()

        this.$login.uniLogin(() => {
            this.init()
        })
    },

    methods: {
        async init() {
            await this.getActiveDetails()
            uni.hideLoading()
        },

        async getActiveDetails() {
            let details = app.globalData.activity_detail
            if (!details || details.active_id !== this.active_id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.active_id
                    }
                })
                details = res?.data?.active_details
            }

            if (!details) {
                this.$uni.showModal('活动获取失败，请重试。', {success: () => uni.navigateBack()})
                return
            }

            const ai_sport_list = details?.conf?.AI_motion?.motion_list || []
            if (!ai_sport_list.length) {
                if (!details) {
                    this.$uni.showModal('活动未配置AI运动类型', {success: () => uni.navigateBack()})
                    return
                }
            }

            this.aiSportList = ai_sport_list.map(({types, name}) => ({types, name}))

            this.must_submit = details.conf.must_submit || []
            if (details.rank_set?.team_group_open) {
                this.team_open = true
                await this.getActiveTeamList()
            }
        },


        async getActiveTeamList() {
            if (!this.team_open) {
                this.team_list = []
                return
            }

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/team_group_list',
                data: {
                    active_id: this.active_id,
                    page: 1,
                    perpage: 1000
                }
            })

            this.team_list = res?.data?.team_group_list?.data || []
        },


        typesPickerChange(e) {
            this.typesChange(this.aiSportList[e.detail.value].types)
        },

        typesChange(types) {
            this.types = types
            this.getTotal()
        },

        async getTotal() {
            this.total_page = 0
            this.count = 0

            this.$uni.showLoading()
            const res = await this.apiRequest(1, 1)
            uni.hideLoading()

            this.count = res.total

            if (!this.count) return this.$uni.showToast(`暂无记录`)

            this.setTotalPage()
        },

        setTotalPage() {
            this.total_page = Math.ceil(this.count / this.perpage)
        },

        perpageInputConfirm(val) {
            const perpage = Number(val)
            if (isNaN(perpage) || perpage < 1) {
                return this.$uni.showModal(`输入错误，请输入 100 - ${this.max_count} 内的数字`, {
                    success: () => this.$refs.perpage.open()
                })
            }

            this.perpage = perpage
            this.setTotalPage()
        },


        async getRecordList(page) {
            const res = await this.apiRequest(page, this.perpage)
            return res.data
        },


        async exportData(page) {
            this.$uni.showLoading('导出中...')

            const list = await this.getRecordList(page)
            const tableData = this.excelDataProcessing(list, page)

            openExcelFile.openDocument(tableData, this.getTableTitle(page))

            uni.hideLoading()
        },

        getTableTitle(page) {
            if (this.total_page === 1) return `${this.typesName} 积分排行榜`

            const first = (page - 1) * this.perpage + 1
            const last = page === this.total_page ? this.count : page * this.perpage

            return `${this.typesName} 积分排行榜  ${first} - ${last}`
        },



        excelDataProcessing(list, page) {
            const tHead = ['序号', '系统标识']
            this.must_submit.forEach(item => tHead.push(item.title))
            if (this.team_open) tHead.push('队伍')
            tHead.push(`积分 (${this.typesName})`)

            const tBody = []

            list.forEach((item, index) => {
                const must_submit_value = this.must_submit.map(a => {
                    const user_must_submit = item.user_details?.must_submit || []
                    return user_must_submit.find(u => u.title === a.title)?.value || ''
                })

                const data = [
                    (page - 1) * this.perpage + index + 1,               // 序号
                    `w.${app.globalData['who']}u.${item.userid || 0}`, // 商户号+会员号
                    ...must_submit_value,    // must_submit
                ]

                // 队伍
                if (this.team_open) data.push(this.team_list.find(t => t.id === item.user_details?.team_id)?.name || '')

                data.push(item.integral_num || 0)   // 积分

                tBody.push(data)
            })


            return [tHead, ...tBody]
        },


        async apiRequest(page, perpage) {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.ai_motion.user/ai_motion_sport_types_total',
                data: {
                    active_id: this.active_id,
                    types: this.types,
                    page,
                    perpage
                }
            })

            return res?.data?.list || {data: [], total: 0}
        }
    }
}
</script>

<style lang="scss" scoped>
.page {
    padding-top: 1px;
}

.types-picker {
    border: 1px solid #eee;
    border-radius: 4px;
    line-height: 34px;
    padding: 0 10px;
    margin: 10px;
}

.uni_popup {
    width: 280px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}

.uni_popup-btn {
    line-height: 40px;
    border-radius: 20px;
}

.uni_popup-btn::after {
    border: none;
}
</style>