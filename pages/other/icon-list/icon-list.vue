<template>
    <view>
        <view class="text-center color-sub font14 pt5">请选择图标</view>
        <view class="list flex-row flex-wrap">
            <view class="item flex-all-center" v-for="(item, index) in iconList" :key="index"
                  @click="selIcon(item)">
                <text :class="'iconfont font34 color-sub icon-' + item"></text>
            </view>
        </view>
    </view>
</template>

<script>
import iconList from './icon-list-config'
export default {
    data() {
        return {
            iconList
        }
    },
    methods: {
        selIcon(class_name) {
            const eventChannel = this.getOpenerEventChannel()
            eventChannel.emit('selIcon', `icon-${class_name}`)
            uni.navigateBack()
        }
    }
}
</script>

<style>
.list {
    padding-bottom: 15px;
}

.item {
    width: 20%;
    /*宽高比为1:1*/
    aspect-ratio: 1;
}
</style>
