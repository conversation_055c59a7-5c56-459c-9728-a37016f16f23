<template>
	<view class="">

		<image class="curr-image" :src="curr_pic" mode="aspectFit" @click="previewImage(curr_pic)"/>


		<scroll-view class="p5" scroll-x scroll-with-animation :scroll-left="scroll_left"
			style="width: 100vw; box-sizing: border-box;">
			<view class="flex-row flex-wrap" :style="{width: style_list_width + 'px;'}">
				<view
					class="style-setting-item"
					:class="{ 'style-active': item.url === curr_pic }"
					v-for="(item, index) in pic_list"
					:key="index"
					@click="changeImage(item.url, index)"
				>
					<image class="poster-logo" :src="item.url" mode="aspectFill" />
					<view class="style-title"> {{ item.title }} </view>
				</view>
			</view>
		</scroll-view>

		<view class="flex-all-center" style="padding-top: 15px;">
			<button
				class="draw-btn bg-error color-white"
				hover-class="navigator-hover"
				@click="chooseImage"
			>选择正面照图片并生成</button>
		</view>

		<view class="text-center font12 color-sub pt10">
			请上传正脸照片，合成效果会更好哦
		</view>

		<uni-popup ref="result_image" type="center" :mask-click="false">
			<view class="result-image-popup bg-white">
				<view class="close" @click="$refs.result_image.close()">
					<uni-icons type="closeempty" size="20" color="#bbbec4"/>
				</view>


				<image
					class="result-image"
					:src="result_image"
					mode="aspectFit"
					@click="previewImage(result_image)"
				/>

				<view class="text-center color-sub font12">
					图片生成记录不会保留，请点击"保存到相册"按钮自行保存。也可点击图片预览后长按保存或分享给好友。
				</view>

				<view class="flex-all-center pt15">
					<view
						class="save-image-btn bg-error color-white text-center"
						@click="saveImageToPhotosAlbum"
					>保存到相册</view>
				</view>
			</view>
		</uni-popup>

    
	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'

	export default {
		data() {
			return {
				pic_list: [],
				curr_pic: '',
				scroll_left: 0,
				style_list_width: 0,
				result_image: ''
			}
		},

		onLoad(params) {
			uni.showLoading()
			this.active_id = params.active_id
			login.uniLogin(err => {
				if (err && err.errMsg) {
					uni.hideLoading()
					uni.showModal({
						title: err.errTitle || '提示',
						content: err.errMsg,
						showCancel: false
					})
					return false
				}

				this.init()
			})
		},

		methods: {
			async init() {
				await this.getConfig()
				uni.hideLoading()
			},

			async getConfig() {
				const res = await xwy_api.request({
					url: 'front.user.image.face_swap.user/face_pic_tpl_list',
					data: {
						access_token: app.globalData.access_token,
						active_id: this.active_id
					}
				})

				if (!res?.data?.tpl_list?.pic_list?.length) {
					uni.hideLoading()
					xwy_api.alert('没有获取到图片配置')
					uni.navigateBack()
					return
				}

				this.pic_list = res.data.tpl_list.pic_list
				this.curr_pic = this.pic_list[0].url
				this.style_list_width = this.calculateScrollWidth(this.pic_list)
			},

			calculateScrollWidth(list) {
			    const one_width = 90
			    const view_width = uni.getWindowInfo().windowWidth - 10
			    if ((list.length * one_width) < (view_width * 2)) {
			        return list.length * one_width
			    }
			    return Math.ceil(list.length / 2) * one_width
			},

			changeImage(url, index) {
				if (url === this.curr_pic) return
				this.curr_pic = url
				const item_width = 90
				const view_width = uni.getWindowInfo().windowWidth - 10 // scroll-view 宽度
				const list = this.pic_list
				if ((list.length * item_width) >= (view_width * 2)) {
					const one_line_count = Math.ceil(list.length / 2)
					if (index + 1 > one_line_count) index -= one_line_count
				}
				let scroll_left = item_width * index - (view_width / 2 - item_width / 2)
				if (index === 0) scroll_left = 0
				this.scroll_left = scroll_left
			},


			chooseImage() {
				uni.chooseImage({
					count: 1,
					sizeType: ['original'],
					success: res => {
						this.uploadImage(res.tempFiles[0])
					}
				})
			},

			// getTempFileInfo(path) {
			// 	uni.showLoading({title: '生成中...'})
			// 	uni.getFileSystemManager().readFile({ //读取本地文件内容
			// 		filePath: path, // 文件路径
			// 		encoding: 'base64', // 返回格式
			// 		success: res => {
			// 			this.faceDraw(res.data)
			// 		}
			// 	})
			// },

			async uploadImage(temp_data) {
				uni.showLoading({title: '生成中...'})
				const url = await xwy_api.uploadOneImage({
					temp_data,
					is_temp: 11
				})
				if (!url) {
					uni.hideLoading()
					xwy_api.alert('生成失败，请重试')
					return
				}

				this.faceDraw(url)
			},

			async faceDraw(url) {
				const res = await xwy_api.request({
					url: 'front.user.image.face_swap.user/face_swap',
					data: {
						access_token: app.globalData.access_token,
						template_url: this.curr_pic,
						image_url: url
					}
				})
				if (res?.data?.image) {
					this.base64ImageToPath(res.data.image)
					return
				}
				uni.hideLoading()
				xwy_api.alert(res?.info || '生成失败')
			},

            base64ImageToPath(data) {
                const filePath = `${wx.env.USER_DATA_PATH}/ai-face-${new Date().getTime()}.png`

                const fs = wx.getFileSystemManager()
                fs.writeFile({
                    filePath,
                    data: data.slice(22),
                    encoding: 'base64',
                    success: () => {
                        uni.hideLoading()
                        this.result_image = filePath
                        this.$refs.result_image.open()
                    },
                    fail: () => {
                        uni.hideLoading()
                        this.$uni.showModal('生成失败，请重试！')
                    }
                })
            },

			previewImage(src) {
				uni.previewImage({
					urls: [src],
					fail: err => {
						console.log('fail', err)
					}
				})
			},

			saveImageToPhotosAlbum() {
				uni.saveImageToPhotosAlbum({
					filePath: this.result_image,
					success: () => {
						uni.showToast({
							title: '保存成功',
							icon: 'none'
						})
					},
					fail: err => {
						if (err?.errMsg && err.errMsg === 'saveImageToPhotosAlbum:fail cancel') return
						uni.showToast({
							title: '保存失败',
							icon: 'error'
						})
					}
				})
			}
		}
	}
</script>

<style lang="scss">
.curr-image {
	width: 100%;
	height: calc(100vh - 320px);
}
$poster-width: 80px;
.style-setting-item {
    text-align: center;
    width: $poster-width;
    min-width: $poster-width;
    height: $poster-width;
    margin: 5px;
    border-radius: 5px;
    box-sizing: border-box;
    overflow: hidden;
    position: relative;

    .poster-logo {
        width: $poster-width;
        height: $poster-width;
        display: block;
    }

    .style-title {
        position: absolute;
        bottom: 0;
        left: 0;
        width: $poster-width;
        text-align: center;
        font-size: 12px;
        background-color: rgba(#000, .5);
        color: #fff;
        padding: 3px;
        box-sizing: border-box;
    }
}

.style-active {
    border: 2px solid #ed3f14;

    .poster-logo, .style-title {
        width: calc(#{$poster-width} - 4px);
    }
}

.draw-btn {
	width: 250px;
	line-height: 44px;
	font-size: 16px !important;
	border-radius: 5px;
	text-align: center;
}
.draw-btn::after {
	content: "";
	border: none
}

.result-image-popup {
	width: 90vw;
	height: 90vh;
	padding: 20px 10px;
	box-sizing: border-box;
	border-radius: 10px;
	position: relative;

	.close {
		position: absolute;
		padding: 5px;
		top: 0;
		right: 0;
	}

	.result-image {
		width: calc(90vw - 20px);
		height: calc(90vh - 140px);
	}

	.save-image-btn {
		width: 250px;
		line-height: 44px;
		border-radius: 5px;
	}
}
</style>
