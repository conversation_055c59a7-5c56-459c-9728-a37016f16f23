<template>
    <view>

        <view class="font14 p10" style="color: #e19898;">
            <view v-if="total_page > 1">
                <view>
                    活动用户共{{ attend_person_count }}人，需要分{{ total_page }}次导出，每次导出{{ perpage }}条数据。
                </view>
            </view>
        </view>

        <view v-if="total_page > 1" class="p10 font14 color-content">
            <text>每次导出</text>
            <text class="plr5">{{ perpage }}</text>
            <text>条数据</text>
            <text class="pl10 color-primary" @click="uniPopupOpen('perpage')">修改</text>
        </view>


        <view v-if="total_page > 1" class="pb10">
            <view class="p10 flex-kai" v-for="(item, index) in total_page" :key="item">
                <view class="color-title">
                    <text class="color-sub">{{ index + 1 }}、</text>
                    <text>{{ index * perpage + 1 }}</text>
                    <text class="plr5">-</text>
                    <text>
                        <template v-if="index + 1 === total_page">{{ attend_person_count }}</template>
                        <template v-else>{{ (index + 1) * perpage }}</template>
                    </text>
                </view>
                <view class="flex-kai" style="width: 155px;">
                    <view>
                        <text v-if="src_list[index]" class="color-disabled">已导出</text>
                        <text v-else class="color-primary" @click="exportData(index)">导出</text>
                    </view>
                    <view
                        :class="{'color-primary': src_list[index], 'color-disabled': !src_list[index]}"
                        @click="copyDownloadSrc(src_list[index])"
                    >复制下载地址
                    </view>
                </view>
            </view>
        </view>

        <uni-popup ref="export_success" type="center">
            <view class="uni_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('export_success')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top bg-primary color-white" style="padding: 50px 20px 20px;">
                    <icon type="success" size="80" color="#ffffff"></icon>
                    <view class="font18">导出成功</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ export_success_tips }}
                    </view>
                    <view
                        class="uni_popup-btn bg-info color-white"
                        hover-class="navigator-hover"
                        @click="copyDownloadSrc(download_src, false)"
                    >复制下载地址
                    </view>
                </view>
            </view>
        </uni-popup>

        <uni-popup
            ref="perpage"
            type="dialog"
            mode="input"
            :is-mask-click="false"
        >
            <uni-popup-dialog
                mode="input"
                title="每次导出数据量"
                :value="perpage"
                :placeholder="'请输入 100 - ' + max_count + ' 内的数字'"
                @confirm="perpageInputConfirm"
            ></uni-popup-dialog>
        </uni-popup>


    </view>
</template>

<script>
import my_storage from '@/utils/storage.js'

export default {
    data() {
        return {
            attend_person_count: 0,
            perpage: 1000,
            total_page: 1,
            max_count: 3000,
            src_list: [],
            export_success_tips: '',
            download_src: ''
        }
    },


    onLoad(e) {
        this.active_id = e.active_id
        this.active_name = e.active_name

        this.$uni.showLoading('加载中...')

        this.$login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err.errTitle || '提示'})
            }

            this.getPeopleTotal()
        })
    },

    methods: {
        perpageInputConfirm(val) {
            const perpage = Number(val)
            if (isNaN(perpage) || perpage < 100 || perpage > this.max_count) {
                uni.showModal({
                    title: '提示',
                    content: `输入错误，请输入 100 - ${this.max_count} 内的数字`,
                    showCancel: false,
                    success: () => {
                        this.uniPopupOpen('perpage')
                    }
                })
                return
            }
            this.perpage = perpage
            this.setTotalPage()
        },

        async getPeopleTotal() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.export.admin_active/active_attend_person_count',
                data: {
                    active_id: this.active_id
                }
            })

            uni.hideLoading()

            const count = res?.data?.attend_person_count || 0
            if (count <= this.max_count) return this.exportOne()

            this.attend_person_count = count
            this.setTotalPage()
        },

        setTotalPage() {
            this.total_page = Math.ceil(this.attend_person_count / this.perpage)
            this.src_list = []
        },

        async getDownloadUrl(page, perpage) {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.export.admin_active/export_user_total_rank',
                data: {
                    active_id: this.active_id,
                    types: 21,
                    page,
                    perpage
                }
            })

            return {
                url: res?.data?.url || '',
                info: res?.info || ''
            }
        },

        async exportOne() {
            this.$uni.showLoading('导出中...')
            const res = await this.getDownloadUrl(1, this.max_count)
            uni.hideLoading()

            if (!res.url) return this.$uni.showModal(res.info || '导出失败')

            this.getOpenerEventChannel?.()?.emit?.('onePageExportSuccess', {
                info: res.info || '导出成功',
                url: res.url
            })

            this.$uni.navigateBack()
        },


        async exportData(index) {
            this.$uni.showLoading('导出中...')
            const res = await this.getDownloadUrl(index + 1, this.perpage)
            uni.hideLoading()

            if (!res.url) return this.$uni.showModal(res.info || '导出失败')

            this.exportSuccess(index, res.info || '导出成功', res.url)
        },

        exportSuccess(index, info, url) {
            this.src_list[index] = url
            this.download_src = url
            this.export_success_tips = info || '数据已导出，请复制此地址在浏览器打开下载Excel报表。'
            this.uniPopupOpen('export_success')
            this.copyDownloadSrc(url, true)


            let page_tips = `第${index * this.perpage + 1}条至第${(index + 1) * this.perpage}条`
            if (index + 1 === this.total_page) page_tips = `第${index * this.perpage + 1}条至第${this.attend_person_count}条`
            my_storage.setExportExcelRecord({
                url: url,
                title: `导出活动【${this.active_name}】用户数据 ${page_tips} 数据`
            })
        },

        uniPopupOpen(ref) {
            this.$refs[ref].open()
        },

        uniPopupClose(ref) {
            this.$refs[ref].close()
        },

        copyDownloadSrc(src, hide = false) {
            !hide && this.uniPopupClose('export_success')
            uni.setClipboardData({
                data: src,
                success: () => hide ? uni.hideToast() : this.$uni.showToast('复制成功', 'success', 1000)
            })
        },
    }
}
</script>

<style>
.uni_popup {
    width: 280px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}

.uni_popup-btn {
    line-height: 40px;
    border-radius: 20px;
}

.uni_popup-btn::after {
    border: none;
}

</style>
