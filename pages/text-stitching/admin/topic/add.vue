<template>
    <view class="page">
        <view class="block top-title">
            <view class="block-label">
                <text>碎片名称</text>
                <text class="pl5 color-sub font12">(如红色故事、党史事件等)</text>
            </view>
            <uni-easyinput v-model="title" maxlength="20" placeholder="请输入碎片名称"/>
        </view>

        <view class="block">
            <view class="block-label">
                <text>分类设置</text>
                <text class="color-sub font12 pl5">(最多5项，如：时间，地点，人物，事件)</text>
            </view>
            <view class="options">
                <view
                    class="option-item flex-row pb10"
                    :class="'option-' + index"
                    v-for="(item, index) in options"
                    :key="index"
                >
                    <view style="width: 100%;">
                        <uni-easyinput v-model="item.name" maxlength="20" placeholder="请输入分类名称"/>
                    </view>
                    <view
                        class="flex-all-center"
                        style="min-width: 30px; margin-left: 10px;"
                        hover-class="navigator-hover"
                        @click="deleteOption(index)"
                    >
                        <text class="iconfont icon-delete color-error"></text>
                    </view>
                </view>
                <view v-if="options.length < 5" class="clear clearfix">
                    <view
                        class="fr font14 color-primary p5"
                        hover-class="navigator-hover"
                        @click="addOption"
                    >添加分类</view>
                </view>
            </view>
        </view>

        <view class="block" v-if="options.length">
            <view class="block-label">选项设置</view>
            <view class="rows">
                <view
                    class="row-item"
                    :class="{ 'last-row': index === rows.length - 1 }"
                    v-for="(item, index) in rows"
                    :key="index"
                >
                    <view
                        class="row-option-item"
                        :class="'row-option-' + index + '-' + option_index"
                        v-for="(option_item, option_index) in item"
                        :key="option_index"
                    >
                        <uni-forms-item
                            :label="options[option_index].name + ':'"
                            label-position="top"
                            label-width="300px"
                        >
                            <uni-easyinput v-model="option_item.text" maxlength="20"/>
                        </uni-forms-item>
                    </view>
                    <view class="clear clearfix">
                        <view
                            class="fr font14 color-error p5"
                            hover-class="navigator-hover"
                            @click="deleteRow(index)"
                        >删除选项</view>
                    </view>
                </view>
                <view class="clear clearfix">
                    <view
                        class="fr font14 color-primary p5 pt10"
                        hover-class="navigator-hover"
                        @click="addRow"
                    >添加选项</view>
                </view>
            </view>
        </view>

        <view class="save-view bg-white">
            <view
                class="save-btn bg-primary color-white text-center"
                hover-class="navigator-hover"
                @click="saveCheck"
            >保存</view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            title: '',
            options: [{ name: '' }],
            rows: [[{ text: '' }]],
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.question_bank_id = params.question_bank_id
        let bar_title = '添加碎片'
        if (params.id) {
            this.id = params.id
            this.getQuestionDetails()
            bar_title = '修改碎片'
        }
        uni.setNavigationBarTitle({ title: bar_title })
    },

    methods: {
        async getQuestionDetails() {
            uni.showLoading({ mask: true })
            const res = await this.xwy_api.request({
                url: 'front.flat.exam.questionBank/get_question_bank_details',
                data: { id: this.id }
            })
            uni.hideLoading()
            if (res?.status !== 1) return this.xwy_api.alert(res?.info || '获取碎片详情失败', {
                success: () => uni.navigateBack()
            })
            if (res?.data?.title) this.title = res.data.title
            const answer_option = res?.data?.answer_option
            this.options = answer_option?.options || []
            this.rows = answer_option?.rows || []
        },

        addOption() {
            this.options.push({ name: '' })
            this.rows.forEach(v => {
                v.push({ text: '' })
            })
        },

        deleteOption(index) {
            this.options.splice(index, 1)
            this.rows.forEach(v => {
                v.splice(index, 1)
            })
        },

        addRow() {
            const row = []
            this.options.forEach(() => {
                row.push({ text: '' })
            })
            this.rows.push(row)

            this.$nextTick(() => {
                this.pageScrollTo('.last-row')
            })
        },

        deleteRow(index) {
            this.rows.splice(index, 1)
        },


        optionsCheck() {
            if (!this.options.length || this.options.length < 2) {
                this.pageScrollTo('.options')
                uni.showToast({ title: '至少需要设置两个分类', icon: 'none' })
                return false
            }
            for (let i = 0; i < this.options.length; i++) {
                const item = this.options[i]
                if (!item.name) {
                    this.pageScrollTo(`.option-${i}`)
                    uni.showToast({ title: `请输入分类${i + 1}名称`, icon: 'none' })
                    return false
                }
            }
            // 判断选项是否重复
            const options = this.options.map(v => v.name)
            const options_set = new Set(options)
            if (options.length !== options_set.size) {
                this.pageScrollTo('.options')
                uni.showToast({ title: '分类名称不能重复', icon: 'none' })
                return false
            }
            return true
        },
        rowsCheck() {
            if (!this.rows.length || this.rows.length < 2) {
                this.pageScrollTo('.rows')
                uni.showToast({ title: '至少需要设置两个选项', icon: 'none' })
                return false
            }
            for (let i = 0; i < this.rows.length; i++) {
                const item = this.rows[i]
                for (let j = 0; j < item.length; j++) {
                    const option_item = item[j]
                    if (!option_item.text) {
                        this.pageScrollTo(`.row-option-${i}-${j}`)
                        uni.showToast({ title: `请输入选项${i + 1}的${this.options[j].name}`, icon: 'none' })
                        return false
                    }
                }
            }
            return true
        },

        pageScrollTo(selector) {
            uni.pageScrollTo({ selector })
        },

        saveCheck() {
            if (!this.title) {
                this.pageScrollTo('.top-title')
                return uni.showToast({ title: '请输入碎片名称', icon: 'none' })
            }
            if (!this.optionsCheck()) return
            if (!this.rowsCheck()) return
            this.save()
        },

        async save() {
            const answer_option = {
                options: this.options,
                rows: this.rows
            }
            const data = {
                title: this.title,
                category_id: this.question_bank_id,
                question_types: 1,
                answer_option: this._utils.base64['encode'](JSON.stringify(answer_option))
            }
            if (this.id) data.id = this.id
            uni.showLoading({
                title: '保存中...',
                mask: true
            })
            const res = await this.xwy_api.request({
                url: 'front.flat.exam.questionBank/create_question_bank',
                data
            })
            uni.hideLoading()
            if (res?.status !== 1) return this.xwy_api.alert(res?.info || '保存失败')
            uni.showToast({ title: '保存成功', icon: 'success' })

            this.saveSuccess()
        },

        saveSuccess() {
            const OEC = this.getOpenerEventChannel()
            OEC?.emit?.('refresh')
            setTimeout(() => {
                uni.navigateBack()
            }, 1000)
        },
    }
}
</script>

<style lang="scss">
.page {
    padding-top: 1px;
    padding-bottom: 80px;
}

.block {
    margin: 20px 10px;
    padding: 10px;
    box-shadow: 0 0 10px #eee;
    border-radius: 10px;
    .block-label {
        padding-left: 10px;
        padding-bottom: 10px;
        position: relative;
        color: #666;
        font-size: 14px;
    }
    .block-label::after {
        content: "";
        position: absolute;
        left: 0;
        top: 3px;
        width: 5px;
        height: 15px;
        background-color: #2d8cf0;
    }
}

.row-item {
    padding-top: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #d2d0d0;
}
.row-item:first-child {
    padding-top: 0;
}

.save-view {
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 999;
    width: 100vw;
    padding: 15px;
    border-top: 1px solid #eee;
    box-sizing: border-box;
    .save-btn {
        line-height: 44px;
        border-radius: 22px;
    }
}

.uni-forms-item__inner {
    padding-bottom: 10px !important;
}
.uni-forms-item__label {
    height: 24px !important;
}
</style>
