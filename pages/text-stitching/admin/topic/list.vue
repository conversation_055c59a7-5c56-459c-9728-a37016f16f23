<template>
    <view>
        <view v-if="total && type === 'admin'" class="total flex-kai">
            <view class="color-sub font14 p10">共{{ total }}碎片</view>
            <view class="font14 color-primary p10" hover-class="navigator-hover" @click="addOrEdit(false)">
                <text>添加碎片</text>
                <uni-icons type="forward" size="14" color="#2d8cf0"/>
            </view>
        </view>

        <view class="list pt10">
            <view class="item bg-white" v-for="item in list" :key="item.id" @click="toDetails(item.id)">
                <view class="color-title">{{ item.title }}</view>
                <view v-if="type === 'user'" class="clearfix clear pt10">
                    <view
                        class="action-btn bg-primary color-white text-center fr"
                        hover-class="navigator-hover"
                        @click.stop="toDetails(item)"
                    >开始</view>
                </view>
                <template v-if="type === 'admin'">
                    <view class="pt5 color-content font14">选项: {{ item.options_text }}</view>
                    <view class="tools-bar clearfix clear">
                        <view class="fr flex-row">
                            <view class="edit" @click.stop="addOrEdit(item.id)">
                                <text class="iconfont icon-edit color-sub font20"></text>
                            </view>
                            <view class="edit" @click.stop="deleteItem(item.id)">
                                <text class="iconfont icon-delete color-sub font20"></text>
                            </view>
                        </view>
                    </view>
                </template>
            </view>
        </view>

        <view v-if="!list.length && !loading" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无碎片</view>
            <view v-if="type === 'admin'" class="flex-all-center pt15">
                <view class="add-btn bg-primary color-white text-center" @click="addOrEdit(false)">
                    添加碎片
                </view>
            </view>
        </view>
        <uni-load-more v-if="loading" status="loading"></uni-load-more>
        <uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>
    </view>
</template>

<script>
const app = getApp()
import base64 from "@/utils/base64"
export default {
    data() {
        return {
            loading: true,
            list: [],
            current_page: 1,
            is_last_page: false,
            total: 0,
            type: 'admin'
        }
    },

    onLoad(params) {
        this.id = params.id
        if (params.type) this.type = params.type
        if (this.type === 'admin') uni.setNavigationBarTitle({ title: '碎片管理' })
        this.init()
    },

    onReachBottom() {
        !this.is_last_page && !this.loading && this.getQuestionList()
    },

    methods: {
        async init() {
            uni.showLoading({ title: '加载中', mask: true })
            await this.getActivityDetails()
            if (!this.question_bank_id && this.type === 'admin') {
                await this.createQuestionBank()
                await this.saveQuestionBankId()
            }
            await this.getQuestionList()
            this.loading = false
            uni.hideLoading()
        },

        async getActivityDetails() {
            let active_details = app.globalData['activity_detail']
            if (!active_details) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: { active_id: this.id }
                })
                active_details = res.data['active_details']
            }
            if (!active_details) {
                return this.xwy_api.alert('活动获取失败，请重试', {
                    success: () => uni.navigateBack()
                })
            }
            if (this.type === 'user') uni.setNavigationBarTitle({ title: active_details.name })
            this.active_details = active_details

            // 默认读取活动的题库
            let question_bank_id = active_details?.conf?.active?.question_bank_id
            const question_type = active_details?.conf?.active?.question_type || 2
            if (question_type === 1) {
                // 如果是用户参与并且活动设置了使用公共题库，则读取公共题库
                if (this.type === 'user') question_bank_id = this.xwy_config.text_stitching_public_id
            }

            this.question_bank_id = question_bank_id
        },

        async createQuestionBank() {
            if (!this.active_details) return
            const res = await this.xwy_api.request({
                url: 'front.user.category/create_category',
                data: {
                    name: `拼碎片活动 - ${this.active_details.name} - 题库`,
                    types: 4,
                    active_id: this.id
                }
            })
            const id = res?.data?.['res']
            if (!id) return this.xwy_api.alert('出错了，请重试', { success: () => uni.navigateBack() })
            this.question_bank_id = id
        },

        async saveQuestionBankId() {
            if (!this.question_bank_id) return
            const data = {
                name: this.active_details.name,
                organizer: this.active_details.organizer || '',
                content: this.active_details.content || '',
                begin_time: this.active_details.begin_time,
                end_time: this.active_details.end_time,
                logo: this.active_details.logo || '',
                active_id: this.active_details.active_id,
                types: this.active_details.types
            }
            const conf = this.active_details.conf
            conf.active.question_bank_id = this.question_bank_id
            data.conf = base64['encode'](JSON.stringify(conf))
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/create_active',
                data
            })

            if (res?.status !== 1) return this.xwy_api.alert('出错了，请重试', {
                success: () => uni.navigateBack()
            })

            this.updateActivityDetails()
        },

        updateActivityDetails() {
            const pages = getCurrentPages()
            const page = pages.find(v => v.route === 'pages/text-stitching/user/detail')
            page?.$vm.getDetail(true)
        },

        async getQuestionList() {
            this.loading = true
            if (this.current_page === 1) {
                this.is_last_page = false
                this.list = []
                this.total = 0
            }
            const res = await this.xwy_api.request({
                url: 'front.flat.exam.questionBank/get_question_bank_list',
                data: {
                    category_id: this.question_bank_id,
                    sort_num: 2,
                    page: this.current_page,
                    perpage: 10
                }
            })

            this.loading = false
            this.current_page++
            const data = res?.data
            if (!data) {
                this.is_last_page = true
                return
            }
            const list = this.listInit(data.data || [])
            this.list = this.list.concat(list)
            this.is_last_page = data.is_lastpage
            this.total = data.total
        },

        listInit(list) {
            const newList = []
            list.forEach(v => {
                const item = {
                    id: v.id,
                    title: v.title,
                    options_text: v.answer_option.options.map(item => item.name).join('、')
                }
                newList.push(item)
            })
            return newList
        },

        async toDetails(item) {
            if (this.type === 'admin') return this.addOrEdit(item.id)

            const count = Math.floor(this.active_details?.conf?.active?.answer_count_limit?.count || 0)

            if (!isNaN(count) && count > 0) {
                this.$uni.showLoading()
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.game_center.historyMemory/get_user_answer_count',
                    data: {
                        category_id: this.question_bank_id,
                        active_id: this.id,
                        question_id: item.id
                    }
                })
                uni.hideLoading()

                const user_count = res?.data?.submit_count || 0

                if (user_count >= count) {
                    this.$uni.showModal(`【${item.title}】参与次数已达到活动设置上限(${count}次)，请参与其他题目吧。`)
                    return
                }
            }

            this.$uni.navigateTo(`../../user/stitching?id=${this.id}&question_id=${item.id}`)
        },

        addOrEdit(id) {
            let url = `./add?active_id=${this.id}&question_bank_id=${this.question_bank_id}`
            if (id) url += `&id=${id}`
            uni.navigateTo({
                url,
                events: {
                    refresh: () => {
                        this.current_page = 1
                        this.getQuestionList()
                    }
                }
            })
        },

        deleteItem(id) {
            uni.showModal({
                title: '提示',
                content: '确定删除该碎片？',
                success: res => res.confirm && this.deleteAjax(id)
            })
        },

        async deleteAjax(ids) {
            uni.showLoading({ title: '删除中...', mask: true })
            const res = await this.xwy_api.request({
                url: 'front.flat.exam.questionBank/remove_question_bank',
                data: { ids }
            })
            if (res?.status !== 1) return this.xwy_api.alert(res?.info || '删除失败')
            uni.showToast({ title: '删除成功', icon: 'success' })
            this.current_page = 1
            await this.getQuestionList()
        }
    }
}
</script>

<style lang="scss">
.add-btn {
    width: 180px;
    line-height: 44px;
    border-radius: 22px;
}

.item {
    margin: 0 10px 20px;
    padding: 10px;
    border-radius: 10px;
    box-shadow: 0 0 10px #eee;
}
.action-btn {
    width: 120px;
    line-height: 40px;
    border-radius: 20px;
}
.tools-bar {
    border-top: 1px solid #eee;
    padding-top: 10px;
    margin-top: 10px;
}
.edit {
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border: 1px solid #eee;
    border-radius: 50%;
    margin-left: 10px;
}
</style>
