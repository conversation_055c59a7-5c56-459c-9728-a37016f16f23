<template>
    <view class="page">
        <view class="block" v-for="(item, index) in options" :key="index">
            <view class="block-title color-title">{{ item.name }}</view>
            <view class="options color-content flex-row flex-wrap">
                <view v-for="(child, child_index) in item.child" :key="child_index">
                    <uni-transition
                        :mode-class="['fade', 'slide-left']"
                        :show="!child.complete || complete_show"
                    >
                        <view
                            class="option-item font14"
                            :class="{ 'child-active': child.active, 'child-complete': child.complete }"
                            @click="clickOption(index, child_index)"
                        >{{ child.text }}</view>
                    </uni-transition>
                </view>
            </view>
        </view>

        <view v-if="complete_list.length" class="complete-storage-list-show-icon flex-all-center"
              @click="$refs.complete_list_popup.open()">
            <text class="iconfont icon-time font34 color-white"></text>
<!--            <uni-icons type="bars" color="#ffffff" size="24"/>-->
        </view>

        <uni-popup ref="complete_list_popup" :safe-area="false" @touchmove.stop.prevent="">
            <view class="complete-storage-popup bg-white">
                <view class="complete-storage-popup-title color-title">历史记录</view>
                <scroll-view scroll-y="true" class="complete-storage-list">
                    <view class="pt5 pb5">
                        <view class="complete-storage-item flex-row flex-wrap font14"
                              v-for="(item, index) in complete_list" :key="index">
                            <view class="flex-row flex-wrap"
                                  v-for="(text, option_index) in item" :key="option_index">
                                <view v-if="option_index !== 0" class="pl5 pr5">
                                    <uni-icons type="arrow-right" color="#dddee1"/>
                                </view>
                                <view class="color-content">{{ text }}</view>
                            </view>
                        </view>
                    </view>
                </scroll-view>
            </view>
            <view class="flex-all-center">
                <uni-icons
                    type="close"
                    size="28"
                    color="#ffffff"
                    @click="$refs.complete_list_popup.close()"
                />
            </view>
        </uni-popup>

        <uni-popup ref="result_popup" type="top" :safe-area="false">
            <view class="bg-white result text-center">
                <text class="iconfont icon-completed color-light-primary"></text>
                <view class="color-light-primary pt10 font18">{{ result.title }}</view>
                <view class="color-sub p10">{{ result.tips }}</view>
                <!--<view class="flex-all-center p10">
                    <view
                        class="again-btn bg-primary color-white"
                        hover-class="navigator-hover"
                        @click="again"
                    >再来一次</view>
                </view>-->
                <view class="flex-all-center p10">
                    <navigator open-type="navigateBack"
                               class="again-btn bg-primary color-white">
                        返回
                    </navigator>
                </view>
                <view class="flex-all-center">
                    <navigator open-type="navigateBack" delta="2" class="font14 color-sub p10">
                        返回活动
                    </navigator>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
export default {
    data() {
        return {
            options: [],
            complete_show: false,
            result: {
                title: '',
                tips: ''
            },
            complete_list: []
        }
    },

    onLoad(params) {
        this.active_id = params.id
        this.question_id = params.question_id
        this.init()
    },

    methods: {
        async init() {
            uni.showLoading({ title: '加载中', mask: true })
            await this.getActivityDetails()
            await this.getQuestionDetails()
            uni.hideLoading()
        },

        
       
        setCompleteList() {
            const selectedTexts = this.options.map(v => v.child.find(v => v.active)?.text).filter(Boolean)
            this.complete_list.push(selectedTexts)
        },

        errorTipsAndBack(content) {
            this.$uni.showModal(content, {success: () => uni.navigateBack()})
        },
        

        async getActivityDetails() {
            let active_details = app.globalData['activity_detail']
            if (!active_details) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: { active_id: this.active_id }
                })
                active_details = res.data['active_details']
            }
            if (!active_details) {
                this.errorTipsAndBack('活动获取失败，请重试')
                return
            }
            if (active_details.conf?.active?.complete_show) this.complete_show = true
            this.active_details = active_details
        },

        async getQuestionDetails() {
            const res = await this.xwy_api.request({
                url: 'front.flat.exam.questionBank/get_question_bank_details',
                data: { id: this.question_id }
            })
            uni.hideLoading()
            if (res?.status !== 1) {
                this.errorTipsAndBack(res?.info || '获取题目详情失败')
                return
            }
            if (res?.data?.title) uni.setNavigationBarTitle({ title: res.data.title })
            const answer_option = res?.data?.answer_option
            this._options = answer_option?.options || []
            this._rows = answer_option?.rows || []
            this.setOptions()
        },

        setOptions() {
            const options = []
            this._options.forEach(v => {
                options.push({ name: v.name, child: [] })
            })
            this._rows.forEach((v, i) => {
                v.forEach((item, index) => {
                    options[index].child.push({
                        text: item.text,
                        active: false,  // 是否选中  选中后显示激活的样式
                        complete: false,// 是否完成  完成后显示完成的样式，需要移除选项或不能再次点击(根据活动的设置来判断)
                        id: i
                    })
                })
            })
            options.forEach(v => v.child = this._utils.shuffleArray(v.child))
            this.options = options

            this.$nextTick(() => {
                this.start_time = new Date().getTime()
            })
        },

        clickOption(index, child_index) {
            const child_list = this.options[index].child
            const child = child_list[child_index]
            if (child.complete) return
            if (child.active) {
                child.active = false
                return
            }
            child_list.forEach(v => v.active = false)
            child.active = true
            this.completeCheck()
        },

        completeCheck() {
            // 把所有选中的选项拿出来
            const active_list = []
            this.options.forEach(item => {
                const hasActive = item.child.find(v => v.active)
                if (hasActive) active_list.push(hasActive)
            })

            // 判断是否完成
            const complete = active_list.length === this._options.length
            if (!complete) return

            // 判断是否正确
            const firstId = active_list[0].id
            const allIdsSame = active_list.every(item => item.id === firstId)
            uni.showToast({
                title: allIdsSame ? '正确' : '错误',
                icon: allIdsSame ? 'success' : 'error',
                mask: true,
                duration: 1000
            })
            if (allIdsSame) {
                this.setCompleteList()
                let time = 1000
                if (!this.complete_show) time = 500 // 不显示已完成的选项，在toast弹窗前消失会好一点
                setTimeout(() => {
                    active_list.forEach(v => {
                        v.complete = true
                        v.active = false
                    })
                    this.checkCompleteAll()
                }, time)
                return
            }

            setTimeout(() => {
                this.options.forEach(item => {
                    item.child.forEach(v => v.active = false)
                })
            }, 1000)
        },

        async checkCompleteAll() {
            // 检查是否全部都完成了，只需要检查第一组选项就行了
            const all_length = this.options[0].child.length
            for (let i = 0, len = all_length; i < len; i++) {
                if (!this.options[0].child[i].complete) return
            }

            const end_time = new Date().getTime()
            const take_time = Math.ceil((end_time - this.start_time) / 1000)

            const submit_result = await this.submit(take_time)
            if (!submit_result) return


            let time_text = take_time + '秒'
            if (take_time >= 60) {
                const minute = Math.floor(take_time / 60)
                const second = take_time % 60
                time_text = `${minute}分${second}秒`
            }
            this.result = {
                title: '恭喜完成',
                tips: `共${all_length}个选项 用时${time_text}`
            }
            this.$refs.result_popup.open()
        },

        async submit(time) {
            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.game_center.historyMemory/submit_user_answer',
                data: {
                    active_id: this.active_id,
                    question_id: this.question_id,

                    // 为了防止用户前端恶意调用接口，因此约定签名内容及答题时间传值按照如下值：活动id+答题用时 答题用时的单位秒，然后进行base64编码，并在第一位加上一个干扰字符。
                    sign: this._utils.randomCoding() + this._utils.base64.encode(`${this.active_id}+${time}`),
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) {
                this.errorTipsAndBack(res?.info || '提交失败')
                return false
            }

            return true
        },

        again() {
            this.setOptions()
            this.$nextTick(() => this.$refs.result_popup.close())
        }
    }
}

</script>

<style lang="scss">
.page {
    padding-bottom: 150px;
}

.block {
    margin: 20px 10px;
    border-radius: 10px;
    box-shadow: 0 0 10px #ccc;
    overflow: hidden;
    .block-title {
        padding: 10px;
        border-bottom: 1px solid #eee;
        text-align: center;
    }
    .options {
        padding: 5px;
        .option-item {
            padding: 0 10px;
            margin: 5px;
            line-height: 36px;
            border-radius: 18px;
            background-color: #f8f8f8;
        }
        .child-active {
            background-color: #2d8cf0;
            color: #fff;
        }
        .child-complete {
            color: #bbbec4;
        }
    }
}
.result {
    width: 100vw;
    height: 100vh;
    box-sizing: border-box;
    padding-top: 10vh;
    .iconfont {
        font-size: 100px;
    }
    .again-btn {
        margin-top: 30px;
        width: 200px;
        line-height: 44px;
        border-radius: 22px;
    }
}

.complete-storage-list-show-icon {
    position: fixed;
    bottom: 100px;
    right: 10px;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, .5);
}

.complete-storage-popup {
    width: 90vw;
    border-radius: 10px;
    .complete-storage-popup-title {
        line-height: 40px;
        text-align: center;
        border-bottom: 1px solid #eee;
    }
    .complete-storage-list {
        height: calc(90vh - 80px);
        .complete-storage-item {
            padding: 10px;
            margin: 10px;
            border-radius: 10px;
            box-shadow: 0 0 10px #eee;
        }
    }
}
</style>
