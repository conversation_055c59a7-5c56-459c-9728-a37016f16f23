<template>
    <view class="page bg-background">
        <view v-if="total && is_admin" class="flex-kai bg-white">
            <view class="font14 color-sub p10">共{{ total }}个拼图任务</view>
            <view class="font14 color-light-primary p10" @click="toAddOrEdit(null)">
                <text>添加拼图任务</text>
                <uni-icons type="forward" size="14" color="#5cadff"/>
            </view>
        </view>
        
        <template v-if="showAD">
            <xwy-ad :ad_type="3"/>
            <xwy-ad :ad_type="66"/>
        </template>
        
        <view class="puzzle-list">
            <view class="puzzle-item bg-white p10" v-for="item in puzzle_list" :key="item.id">
                <view class="flex-row" @click="toPuzzle(item.id)">
                    <view class="puzzle-image">
                        <image class="image" :src="item.image_url" mode="aspectFill"/>
                    </view>
                    <view class="puzzle-info">
                        <view class="font18 color-title pb5">
                            <text>{{ item.job_name }}</text>
                            <text v-if="item.puzzle_type_name" class="type-tag">
                                {{ item.puzzle_type_name }}
                            </text>
                        </view>
                        <view>
                            <uni-rate :value="item.starts / 10" :size="20" readonly/>
                        </view>
                        <view v-if="item.memo" class="color-content ellipsis--l2 font14">
                            {{ item.memo }}
                        </view>
                        <view class="color-sub font14">
                            <text v-if="item.conf_json && item.conf_json.time_limit" class="pr10">
                                限时{{ item.conf_json.time_limit }}秒
                            </text>
                            <text>过关奖励{{ item.reward_integral_num }}积分</text>
                        </view>
                    </view>
                </view>
                
                <view class="tool-bar clear clearfix">
                    <view v-if="is_admin" class="fr flex-row">
                        <view class="pl10 pr10" @click="toAddOrEdit(item.id)">
                            <text class="iconfont icon-edit font14 color-sub"></text>
                            <text class="font14 color-sub">修改</text>
                        </view>
                        <view class="pl10 pr10" @click="deletePuzzle(item)">
                            <text class="iconfont icon-delete font14 color-sub"></text>
                            <text class="font14 color-sub">删除</text>
                        </view>
                    </view>
                    <view v-else class="to-puzzle-button fr color-white text-center font14" 
                          :class="item['have_finished'] ? 'bg-complete' : 'bg-go'"
                          hover-class="navigator-hover" @click="toPuzzle(item.id)">
                        {{ item['have_finished'] ? '已完成' : '去拼图' }}
                    </view>
                </view>
            </view>
        </view>

        <view v-if="loading" class="text-center" :style="{paddingTop: current_page === 1 ? '30vh' : '0'}">
            <load-ani/>
        </view>

        <view v-if="!loading && !puzzle_list.length" class="text-center" style="padding-top: 20vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无任务</view>
            <view v-if="is_admin" class="add-puzzle-button bg-primary color-white text-center"
                  hover-class="navigator-hover" @click="toAddOrEdit(null)">添加任务
            </view>
        </view>
    </view>
</template>

<script>
import puzzleConfig from './config/puzzle-config'
const PuzzleTypeList = puzzleConfig.getPuzzleTypeList()

export default {
    data() {
        return {
            loading: true,
            puzzle_list: [],
            current_page: 1,
            is_last_page: false,
            total: 0,
            is_admin: false,
            showAD: false
        }
    },

    onLoad(params) {
        this.paramsHandle(params)
        this.$login.uniLogin(() => {
            this.getPuzzleList()
        })
    },

    methods: {
        paramsHandle(params) {
            this.active_id = params.active_id
            if (params['admin']) this.is_admin = true
            if (params.point_id) this.point_id = Number(params.point_id)
            if (params.show_ad) this.showAD = true
            if (params.bg_img) this.bg_img = params.bg_img
            if (params.nav_bg_color) this.nav_bg_color = params.nav_bg_color
            if (params.nav_font_color) this.nav_font_color = params.nav_font_color
        },

        async refreshPuzzleList() {
            this.current_page = 1
            await this.getPuzzleList()
        },

        async getPuzzleList() {
            if (this.current_page === 1) {
                this.puzzle_list = []
                this.is_last_page = false
                this.total = 0
            }
            
            const data = {
                active_id: this.active_id,
                page: this.current_page,
                perpage: 20
            }
            if (this.point_id) data.point_id = this.point_id

            this.loading = true
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin.puzzle/puzzle_image_list',
                data
            })
            this.loading = false

            const res_data = res?.data?.list
            if (!res_data) {
                this.is_last_page = true
                return
            }

            const puzzle_list = this.puzzleListInit(res_data.data || [])
            this.puzzle_list = this.puzzle_list.concat(puzzle_list)
            this.is_last_page = res_data.is_lastpage
            this.total = res_data.total
        },

        puzzleListInit(list) {
            const delete_keys = ['active_id', 'create_time', 'point_details', 'point_id', 'sort_num', 'types', 'update_time', 'userid']
            list.forEach(v => {
                v.conf_json ||= {}
                if (v.conf_json.puzzle_type) {
                    v.puzzle_type_name = this.getPuzzleTypeName(v.conf_json.puzzle_type)
                }
                if (!v.image_url && v.conf_json.rand_img_list.length) {
                    v.image_url = v.conf_json.rand_img_list[0]
                }
                
                delete_keys.forEach(key => v.hasOwnProperty(key) && delete v[key])
            })

            return list
        },

        getPuzzleTypeName(type) {
            return PuzzleTypeList.find(item => item.type === type)?.name || ''
        },

        toAddOrEdit(id) {
            let url = `./admin/puzzle/add?active_id=${this.active_id}&list_total=${this.total}`
            if (id) url += `&id=${id}`

            this.$uni.navigateTo(url, {
                events: {
                    refreshPuzzleList: () => this.refreshPuzzleList()
                }
            })
        },
        
        async deletePuzzle(item) {
            const modal = await this.$uni.showModal(`确定删除【${item.job_name}】?`, {showCancel: true})
            if (!modal.confirm) return
            
            this.$uni.showLoading('删除中...')
            const res = await this.xwy_api.deleteRecords(94, item.id)
            uni.hideLoading()
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '删除失败')
            this.$uni.showToast('已删除')
            await this.refreshPuzzleList()
        },

        toPuzzle(id) {
            if (this.is_admin) return
            let url = `./user/in-puzzle?id=${id}&active_id=${this.active_id}`
            if (this.bg_img) url += `&bg_img=${this.bg_img}`
            if (this.nav_bg_color) url += `&nav_bg_color=${this.nav_bg_color}`
            if (this.nav_font_color) url += `&nav_font_color=${this.nav_font_color}`
            this.$uni.navigateTo(url, {
                events: {
                    puzzleSuccess: () => {
                        this.refreshPuzzleList()
                        this.getOpenerEventChannel?.()?.emit?.('success')
                    }
                }
            })
        }
    },

    onReachBottom() {
        if (this.is_last_page || this.loading) return
        this.current_page++
        this.getPuzzleList()
    },
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    padding-bottom: 10px;
    box-sizing: border-box;
}

.puzzle-list {
    padding-top: 1px;
    
    .puzzle-item {
        margin: 20px 10px;
        border-radius: 5px;
        
        .puzzle-image {
            padding-right: 10px;

            .image {
                $size: 90px;
                width: $size;
                min-width: $size;
                height: $size;
                border-radius: 5px;
            }
        }
        
        .puzzle-info {
            .type-tag {
                margin-left: 10px;
                padding: 0 5px;
                border-radius: 2px;
                border: 1px solid #5cadff;
                color: #5cadff;
                font-size: 12px;
            }
        }
        
        .tool-bar {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #eee;
            
            .bg-go {
                background-color: #f6b51b;
            }
            
            .bg-complete {
                background-color: #539d8c;
            }
            
            .to-puzzle-button {
                width: 90px;
                line-height: 36px;
                border-radius: 18px;
            }
        }
    }
}


.add-puzzle-button {
    margin: 30px auto;
    width: 250px;
    line-height: 44px;
    border-radius: 22px;
}
</style>