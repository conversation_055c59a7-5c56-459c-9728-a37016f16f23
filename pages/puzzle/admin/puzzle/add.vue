<template>
    <view class="page">
        <uni-forms label-position="top" :label-width="300">
            <uni-forms-item label="任务名称" required>
                <uni-easyinput v-model="job_name" :maxlength="50" placeholder="请输入任务名称"/>
            </uni-forms-item>

            <uni-forms-item label="绑定关卡">
                <view class="uni-forms-picker flex-row">
                    <view class="picker-body" @click="changeBindLevel">
                        <text v-if="point_id" class="picker-value">{{ point_name || point_id }}</text>
                        <text v-else class="picker-placeholder">请选择需要绑定的关卡</text>
                    </view>
                    <view class="picker-right-icon flex-all-center">
                        <uni-icons v-if="point_id" type="clear" size="16" color="#c0c4cc" 
                                   @click="clearLevel"/>
                        <uni-icons v-else type="forward" size="16" color="#dddddd" @click="changeBindLevel"/>
                    </view>
                </view>
            </uni-forms-item>

            <uni-forms-item label="奖励积分" required>
                <uni-easyinput v-model="reward_integral_num" type="Number" :maxlength="8" 
                               placeholder="请输入过关奖励的积分数"/>
            </uni-forms-item>

            <uni-forms-item label="任务描述">
                <uni-easyinput v-model="memo" :maxlength="50" placeholder="请输入任务描述"/>
            </uni-forms-item>

            <uni-forms-item label="拼图类型">

                <picker :range="puzzle_type_list" range-key="name" :value="puzzle_type_picker_value" 
                        @change="puzzleTypeChange">
                    <view class="uni-forms-picker flex-row">
                        <view class="picker-body">
                            <text class="picker-value">{{ puzzle_type_picker_name }}</text>
                        </view>
                        <view class="picker-right-icon flex-all-center">
                            <uni-icons type="forward" size="16" color="#dddddd"/>
                        </view>
                    </view>
                </picker>
            </uni-forms-item>

            <uni-forms-item label="拼图难度">
                <view class="second-label color-sub font12">
                    {{ map_size_restrict.min }}到{{ map_size_restrict.max }}，数值越大拼图难度越大。
                </view>

                <slider 
                    :min="map_size_restrict.min" 
                    :max="map_size_restrict.max" 
                    :value="conf_json.map_size" 
                    :show-value="true" 
                    @change="conf_json.map_size = $event.detail.value"
                />
            </uni-forms-item>

            <uni-forms-item label="拼图等级">
                <view class="second-label color-sub font12">提示: 拼图等级设置对游戏难度没有影响。</view>
                <uni-rate v-model="starts"/>
            </uni-forms-item>

            <uni-forms-item label="拼图时间限制 (秒)">
                <uni-easyinput v-model="conf_json.time_limit" type="Number" :maxlength="8"
                               placeholder="不填或填0不限制拼图时间"/>
            </uni-forms-item>

            <uni-forms-item label="拼图图片" required>
                <view class="second-label color-sub font12">
                    提示: 每次拼图从上传的图片中随机显示一张。为了保证图片清晰度，请使用正方形图片。
                </view>
                <view class="image-list flex-row flex-wrap">
                    <view class="image-container"
                          v-for="(item, index) in conf_json.rand_img_list" :key="index">
                        <!--suppress JSValidateTypes-->
                        <image class="image" :src="item" mode="aspectFill" @click="previewImage(item)"/>
                        <view class="clear-image flex-all-center"
                              @click="conf_json.rand_img_list.splice(index, 1)">
                            <uni-icons type="trash" size="24" color="#ff3810"/>
                        </view>
                    </view>
                    <view v-if="conf_json.rand_img_list.length < 20"
                          class="upload-image flex-all-center bg-background"
                          hover-class="navigator-hover" @click="changeImage">
                        <uni-icons type="plusempty" size="50" color="#eeeeee"/>
                    </view>
                </view>
            </uni-forms-item>

            <uni-forms-item label="任务排序">
                <uni-easyinput v-model="sort_num" type="Number" :maxlength="8"
                               placeholder="数字越小排序越靠前"/>
            </uni-forms-item>
            
        </uni-forms>
        
        <view class="save-container flex-all-center bg-white">
            <view class="save-button bg-primary color-white text-center" 
                  hover-calss="navigator-hover" @click="checkForm">保存
            </view>
        </view>
    </view>
</template>

<script>
import puzzleConfig from '../../config/puzzle-config'
const PuzzleTypeList = puzzleConfig.getPuzzleTypeList()

export default {
    data() {
        return {
            job_name: '',
            memo: '',
            reward_integral_num: '',
            starts: 5,
            conf_json: {
                puzzle_type: PuzzleTypeList[0].type,
                time_limit: '',
                map_size: 2,
                rand_img_list: []
            },
            sort_num: 1,
            puzzle_type_list: PuzzleTypeList,
            point_id: '',
            point_name: ''
        }
    },
    
    computed: {
        puzzle_type_picker_value() {
            const index = this.puzzle_type_list.findIndex(item => item.type === this.conf_json.puzzle_type)
            return index === -1 ? 0 : index
        },
        puzzle_type_picker_name() {
            const type_data = this.puzzle_type_list.find(item => item.type === this.conf_json.puzzle_type)
            return type_data?.name || ''
        },
        map_size_restrict() {
            return this.puzzle_type_list.find(item => item.type === this.conf_json.puzzle_type)?.map_size_restrict || {min: 2, max: 10}
        }
    },

    onLoad(params) {
        this.$uni.showLoading()
        this.paramsHandle(params)
        this.$login.uniLogin(() => {
            this.init()
        })
    },

    methods: {
        paramsHandle(params) {
            this.active_id = params.active_id
            if (params.id) this.id = Number(params.id)
            if (params.list_total) this.sort_num = Number(params.list_total) + 1
        },

        async init() {
            if (this.id) await this.getPuzzleDetails()
            uni.hideLoading()
        },

        async getPuzzleDetails() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin.puzzle/image_job_details',
                data: {
                    id: this.id
                }
            })
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '任务获取失败', {
                success: () => uni.navigateBack()
            })
            
            const details = res?.data?.details
            if (!details) return this.$uni.showModal('任务获取失败', {success: () => uni.navigateBack()})
            
            this.job_name = details.job_name
            this.reward_integral_num = details.reward_integral_num
            this.starts = details.starts / 10
            this.memo = details.memo || ''
            this.point_id = details.point_id || ''
            if (details.sort_num) this.sort_num = details.sort_num
            if (details.point_details?.name) this.point_name = details.point_details.name

            details.conf_json ||= {}
            if (details.conf_json.puzzle_type) this.conf_json.puzzle_type = details.conf_json.puzzle_type
            if (details.conf_json.time_limit) this.conf_json.time_limit = details.conf_json.time_limit
            if (details.conf_json.map_size) this.conf_json.map_size = details.conf_json.map_size

            const rand_img_list = details.conf_json.rand_img_list || []
            if (details.image_url) rand_img_list.unshift(details.image_url)
            this.conf_json.rand_img_list = rand_img_list
        },

        changeBindLevel() {
            this.$uni.navigateTo(`/pages/task-challenge/admin/level/list?id=${this.active_id}&to_bind=1`, {
                events: {
                    changeLevel: data => {
                        this.point_id = data.id
                        this.point_name = data.name
                    }
                }
            })
        },
        
        clearLevel() {
            this.point_id = null
            this.point_name = ''
        },
        

        puzzleTypeChange(e) {
            this.conf_json.puzzle_type = this.puzzle_type_list[e.target.value].type
            this.$nextTick(() => {
                const {conf_json: {map_size}, map_size_restrict: {min, max}} = this
                if (map_size < min) this.conf_json.map_size = min
                if (map_size > max) this.conf_json.map_size = max
            })
        },

        changeImage() {
            this.$uni.navigateTo(`/pages/other/image_upload_or_select?active_id=${this.active_id}`, {
                events: {
                    newImg: src => {
                        this.conf_json.rand_img_list.push(src)
                    }
                }
            })
        },

        previewImage(item) {
            this.$uni.previewImage({
                current: item,
                urls: this.conf_json.rand_img_list
            })
        },

        checkForm() {
            const {job_name, reward_integral_num, conf_json: {time_limit}} = this
            if (!job_name) return this.$uni.showToast('请输入任务名称')
            
            const integral = Math.floor(reward_integral_num)
            if (isNaN(integral)) return this.$uni.showToast('请输入正确的奖励积分数')
            
            const time = Math.floor(time_limit)
            if (isNaN(time) || time < 0) return this.$uni.showToast('请输入正确的拼图时间')

            if (!this.conf_json.rand_img_list.length) return this.$uni.showToast('请上传拼图图片')
            
            this.save()
        },
        
        async save() {
            const data = {
                active_id: this.active_id,
                job_name: this.job_name,
                memo: this.memo,
                reward_integral_num: this.reward_integral_num,
                starts: this.starts * 10,
                conf_json: this._utils.base64['encode'](JSON.stringify(this.conf_json)),
                sort_num: this.sort_num,
                point_id: this.point_id
            }
            if (this.id) data.id = this.id
            
            this.$uni.showLoading('保存中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin.puzzle/create_image_job',
                data
            })
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '保存失败')
            
            this.saveSuccess()
        },

        saveSuccess() {
            this.$uni.showToast('保存成功', 'success')
            this.getOpenerEventChannel()?.emit?.('refreshPuzzleList')
            this.$uni.navigateBack(1, {delay: 1000})
        }
    }
}
</script>

<style lang="scss">
.page {
    padding-bottom: 100px;
}

.uni-forms {
    padding: 10px;

    .uni-forms-picker {
        font-size: 14px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        height: 35px;
        line-height: 35px;
        padding-left: 10px;

        .picker-body {
            width: 100%;
        }

        .picker-right-icon {
            width: 30px;
        }

        .picker-value {
            color: #333;
        }

        .picker-placeholder {
            color: #bbbbbb;
            font-size: 12px;
        }
    }

    .second-label {
        position: relative;
        top: -8px;
    }
}

.image-container, .upload-image {
    width: 90px;
    height: 90px;
    margin: 5px;
    border-radius: 10px;
    overflow: hidden;
}

.image-container {
    position: relative;
    
    .image {
        width: 100%;
        height: 100%;
        display: block;
    }
    
    .clear-image {
        position: absolute;
        top: 5px;
        right: 5px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #00000093;
    }
}

.save-container {
    position: fixed;
    z-index: 999;
    left: 0;
    bottom: 0;
    padding: 15px;
    width: 100%;
    box-sizing: border-box;
    
    .save-button {
        width: 250px;
        line-height: 44px;
        border-radius: 22px;
    }
}

/* #ifdef H5 */
@media screen and (max-width: 500px) {
    .save-container {
        width: 500px;
        left: calc((100vw - 500px)/  2);
    }
}
/* #endif */

</style>