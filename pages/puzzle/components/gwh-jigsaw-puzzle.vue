<template>
    <view id="drawView" class="drawView">

        <view v-if="gameType === 'slice'" class="slice">
            <view id="mapView" class="mapView"
                  :style="{'width':mapWidth+'rpx', 'height':mapWidth*mapSize[1]/mapSize[0]+'rpx'}"
                  @touchstart="sliceMapTouchStart" @touchend="sliceMapTouchEnd">
                <view v-for="mapblock in mapList" :key="mapblock.key" class="block"
                      :style="{'width':mapWidth/mapSize[0]+'rpx', 'height':mapWidth/mapSize[0]+'rpx'}">
                    <image class="innerImage" :src="mapblock.imgSoruce" mode="aspectFill"
                           v-if="mapblock.id >= 0"
                           :style="{
						 'width':mapWidth+'rpx', 
						 'height':mapWidth*mapSize[1]/mapSize[0]+'rpx',
						 'left':-(mapWidth*(mapblock.id % mapSize[0])/mapSize[0])+'rpx',
						 'top':-(mapWidth*(Math.floor(mapblock.id / mapSize[0]))/mapSize[0])+'rpx',
					 }"></image>
                </view>
            </view>
            <!-- <view id="controlBox" class="controlBox">
                <view class="btn" @click="sliceMapMoving('l')">←</view>
                <view class="btn" @click="sliceMapMoving('u')">↑</view>
                <view class="btn" @click="sliceMapMoving('d')">↓</view>
                <view class="btn" @click="sliceMapMoving('r')">→</view>
            </view> -->
        </view>

        <view v-if="gameType === 'rotate'">
            <view id="mapView" class="mapView" :style="{'width':mapWidth+'rpx', 'height':mapWidth+'rpx'}">
                <image :src="sourseImg" style="position: absolute;" mode="scaleToFill"
                       :style="{'width':mapWidth+'rpx', 'height':mapWidth+'rpx'}"></image>

                <view v-for="(mapblock, i) in mapList" :key="mapblock.key" class="centerMapBlock"
                      :style="{
					'left': (mapblock.middlePoint[0] - mapblock.width[0]) + 'px',
					'top': (mapblock.middlePoint[1] - mapblock.width[0]) + 'px',
					'width': (2 * mapblock.width[0])+'px',
					'height': (2 * mapblock.width[0])+'px',
					'zIndex': i
				 }">
                    <image :src="sourseImg" :style="{
						'width':mapWidth+'rpx', 
						'height':mapWidth+'rpx',
						'marginLeft': (- (mapblock.background[2]/2 - mapblock.width[0])) + 'px',
						'marginTop': (- (mapblock.background[3]/2 - mapblock.width[0])) + 'px',
						'transform': 'rotate('+mapblock.rotate+'deg)',
					 }" mode="scaleToFill" @touchstart="rotateMapTouchStart($event, i)"
                           @touchmove="rotateMapTouchMove($event, i)"
                           @touchend="rotateMapTouchEnd"></image>
                </view>
            </view>
        </view>

        <view v-if="gameType === 'move'">
            <view id="mapView" class='mapView'
                  :style="{width:mapWidth+'rpx', 'height':mapWidth*mapSize[1]/mapSize[0]+'rpx'}">
                <view v-for="(mapblock,i) in mapList" :key="mapblock.key" class="block"
                      :style="{width:mapWidth/mapSize[0]+'rpx', 'height':mapWidth/mapSize[0]+'rpx'}">
                    <image class="innerImage" :src="mapblock.imgSource" mode="aspectFill"
                           v-if="mapblock.id >= 0"
                           :style="{
						 width:mapWidth+'rpx', 
						 'height':mapWidth*mapSize[1]/mapSize[0]+'rpx',
						 'left':-(mapWidth*(mapblock.id % mapSize[0])/mapSize[0])+'rpx',
						 'top':-(mapWidth*(Math.floor(mapblock.id / mapSize[0]))/mapSize[0])+'rpx',
					 }"></image>
                </view>
            </view>
            <scroll-view scroll-x id="waitMapView" class="waitMapView"
                         :style="{'width':mapWidth+'rpx', 'height':mapWidth/mapSize[0]+'rpx'}">
                <view v-for="bno in randomList" :key="bno"
                      :class="moveTouchStartIndex == bno?'movingBlock':'block'" :style="{
						'width':mapWidth/mapSize[0]+'rpx', 
						'height':mapWidth/mapSize[0]+'rpx',
						'left':touchStartPoint[0]+'px',
						'top':touchStartPoint[1]+'px',
					 }" :data-index="bno" @longpress="waitBlockMoveStart" @touchmove="waitBlockMoveMove"
                      @touchend="waitBlockMoveEnd"
                      v-show="!isInsideMap(bno)">
                    <image class="innerImage" :src="sourseImg" mode="aspectFill" :style="{
							 'width':mapWidth+'rpx', 
							 'height':mapWidth*mapSize[1]/mapSize[0]+'rpx',
							 'left':-(mapWidth*((bno) % mapSize[0])/mapSize[0])+'rpx',
							 'top':-(mapWidth*(Math.floor((bno) / mapSize[0]))/mapSize[0])+'rpx',
						 }"></image>
                </view>
            </scroll-view>
        </view>

        <view v-if="gameType=='cube'" class="cube">
            <view id="mapView" class="mapView f-w"
                  :style="{width:mapWidth+'rpx', 'height':mapWidth*(mapSize[1]+2)/(mapSize[0]+2)+'rpx'}">
                <view v-for="(tno,idx) in (mapSize[0]+2)" :key="idx" class="textBlock"
                      :style="{'width':mapWidth/(mapSize[0]+2)+'rpx', 'height':mapWidth/(mapSize[0]+2)+'rpx'}"
                      @click="cubeMapMove(idx-1, true)">
                    {{ (idx != 0 && idx != (mapSize[0] + 1)) ? '▲' : '' }}
                </view>
                <view v-for="(mapblock,i) in mapList" :key="mapblock.key">
                    <view v-if="i%mapSize[0] == 0" class="textBlock"
                          @click="cubeMapMove(mapSize[0]+Math.floor(i/mapSize[0]), true)"
                          :style="{'width':mapWidth/(mapSize[0]+2)+'rpx', 'height':mapWidth/(mapSize[0]+2)+'rpx'}">
                        ◀
                    </view>
                    <view class="block"
                          :style="{width:mapWidth/(mapSize[0]+2)+'rpx', 'height':mapWidth/(mapSize[0]+2)+'rpx'}">
                        <image class="innerImage" :src="mapblock.imgSource" mode="aspectFill"
                               v-if="mapblock.id >= 0"
                               :style="{
							 'width':mapWidth*mapSize[0]/(mapSize[0]+2)+'rpx', 
							 'height':mapWidth*mapSize[1]/(mapSize[0]+2)+'rpx',
							 'left':-(mapWidth*(mapblock.id % mapSize[0])/(mapSize[0]+2))+'rpx',
							 'top':-(mapWidth*(Math.floor(mapblock.id / mapSize[0]))/(mapSize[0]+2))+'rpx',
						 }"></image>
                    </view>
                    <view v-if="i%mapSize[0] == mapSize[0]-1" class="textBlock"
                          @click="cubeMapMove(mapSize[0]+Math.floor(i/mapSize[0]), false)"
                          :style="{'width':mapWidth/(mapSize[0]+2)+'rpx', 'height':mapWidth/(mapSize[0]+2)+'rpx'}">
                        ▶
                    </view>
                </view>
                <view v-for="(tno,idx) in (mapSize[0]+2)" :key="idx" class="textBlock"
                      :style="{'width':mapWidth/(mapSize[0]+2)+'rpx', 'height':mapWidth/(mapSize[0]+2)+'rpx'}"
                      @click="cubeMapMove(idx-1, false)">
                    {{ (idx != 0 && idx != (mapSize[0] + 1)) ? '▼' : '' }}
                </view>
            </view>
        </view>

    </view>
</template>

<script>
/**
 * 简单拼图游戏模块
 * @description 适用于简单的拼图游戏实现
 * @property {Array} mapSize 当gameType=slice|move时是地图大小：格式为[width, height]；当gameType=rotate时是地图层数：格式为[level]
 * @property {Number} mapWidth 地图边缘宽度（单位：rpx），地图边缘高度一般=宽度*mapSize[1]/mapSize[0]
 * @property {String} gameType = [slice] 游戏模式
 * @value slice 滑动模式
 * @value rotate 旋转模式
 * @value move 拖动模式
 * @value cube 魔方模式
 * @property {String} sourseImg 拼图图片地址
 * @property {Number} initChangeSteps 游戏初始化时，电脑移动步数
 * @event {Function()} init 游戏初始化成功时调用，返回游戏数据
 * @event {Function()} change 游戏进行过程中，用户触发操作时调用，返回游戏数据
 * @event {Function()} success 游戏完成时调用，返回游戏数据
 * */
export default {
    name: "puzzle",
    props: {
        mapSize: {
            type: Array,
            default: () => {
                return [5, 5]
            }
        },
        mapWidth: {
            type: Number,
            default: 750, //rpx
        },
        gameType: {
            type: String,
            default: 'slice'
        },
        sourseImg: {
            type: String,
            default: 'https://prod-0g479j60184f120d-1304148175.tcloudbaseapp.com/web/wx-cloud-api/pages/active/static/tree/detail-bg.jpg'
        },
        initChangeSteps: {
            type: Number,
            default: 20,
        },
    },
    
    data() {
        return {
            mapList: [
                // {
                // 	id:0, //有意义图块编号:0~(mapSize[0]*mapSize[1]-2), 当id=-1时为空图块
                // 	imgSoruce:'', //图片地址
                // }
            ],
            sliceNanBlockIndex: 0,
            rotateTouchStartdeg: 0,
            touchStartPoint: [0, 0],
            moveTouchStartIndex: -1,
            randomList: [],
        }
    },
    mounted() {
        this.gameinit()
    },
    watch: {
        mapSize(newVal) {
            this.gameinit()
        },
        gameType(newVal) {
            this.gameinit()
        },
        mapList: {
            handler() {
                this.mapList.forEach(v => {
                    v.key = `map_list_${v.id}`
                })
            },
            deep: true
        }
    },
    methods: {
        gameinit() {
            if (this.gameType == 'slice') {
                this.initSliceMap()
            } else if (this.gameType == 'rotate') {
                this.initRotateMap()
            } else if (this.gameType == 'move') {
                this.initMoveMap()
            } else if (this.gameType == 'cube') {
                this.initCubeMap()
            }
        },
        initSliceMap() {
            // 初始化原有地图
            this.mapList = [];
            for (var bno = 0; bno < this.mapSize[0] * this.mapSize[1]; bno++) {
                this.mapList.push({
                    id: bno,
                    imgSoruce: this.sourseImg
                })
            }
            this.mapList[this.mapList.length - 1].id = -1;
            //随机移动
            var NanBlockIndex = this.mapList.length - 1; //空方块位置
            for (var stp = 0; stp < this.initChangeSteps; stp++) {
                //获取空方块上下左右方块位置
                var aroundIndexs = [NanBlockIndex - 1, NanBlockIndex + 1,
                    NanBlockIndex - this.mapSize[0],
                    NanBlockIndex + this.mapSize[0]
                ];
                var acceptIndexs = []
                if (aroundIndexs[0] >= 0 && NanBlockIndex % this.mapSize[0] > 0)
                    acceptIndexs.push(aroundIndexs[0]);
                if (aroundIndexs[1] < this.mapSize[0] * this.mapSize[1] && NanBlockIndex % this.mapSize[0] < this
                    .mapSize[0] - 1)
                    acceptIndexs.push(aroundIndexs[1]);
                if (aroundIndexs[2] >= 0)
                    acceptIndexs.push(aroundIndexs[2]);
                if (aroundIndexs[3] < this.mapSize[0] * this.mapSize[1])
                    acceptIndexs.push(aroundIndexs[3]);
                //随机选择一个i进行交换
                var changeIndex = acceptIndexs[Math.floor(Math.random() * acceptIndexs.length)]
                this.mapList[NanBlockIndex].id = this.mapList[changeIndex].id
                this.mapList[changeIndex].id = -1
                NanBlockIndex = changeIndex;
            }
            this.sliceNanBlockIndex = NanBlockIndex;

            // 判断拼图顺序是否正确 如果正确 则需要重新随机
            const list = this.xwyLib.copyAry(this.mapList);
            const rdm = list.every((val, idx, arr) => {
                const len = arr.length
                if (idx < len - 1 && val.id == idx) return true
                if (idx == len - 1 && val.id == -1) return true
                return false
            })
            if (rdm) {
                this.initSliceMap()
                return
            }


            this.$forceUpdate()
            this.$emit('init', {
                mapblocks: this.mapList
            })

        },
        sliceMapTouchStart(e) {
            this.touchStartPoint = [e.touches[0].clientX, e.touches[0].clientY]
        },
        sliceMapTouchEnd(e) {
            var touch = [e.changedTouches[0].clientX, e.changedTouches[0].clientY]
            if (Math.abs(touch[0] - this.touchStartPoint[0]) >= Math.abs(touch[1] - this.touchStartPoint[1])) {
                if (touch[0] > this.touchStartPoint[0]) {
                    this.sliceMapMoving('right')
                } else {
                    this.sliceMapMoving('left')
                }
            } else {
                if (touch[1] > this.touchStartPoint[1]) {
                    this.sliceMapMoving('down')
                } else {
                    this.sliceMapMoving('up')
                }
            }
        },
        sliceMapMoving(direction = 'up') {
            var changeIndex = this.sliceNanBlockIndex;
            switch (direction) {
                case 'down':
                case 'd':
                    changeIndex = this.sliceNanBlockIndex - this.mapSize[0];
                    if (changeIndex < 0) return;
                    break;
                case 'up':
                case 'u':
                    changeIndex = this.sliceNanBlockIndex + this.mapSize[0];
                    if (changeIndex >= this.mapSize[0] * this.mapSize[1]) return;
                    break;
                case 'right':
                case 'r':
                    changeIndex = this.sliceNanBlockIndex - 1;
                    if (changeIndex < 0 || this.sliceNanBlockIndex % this.mapSize[0] == 0) return;
                    break;
                case 'left':
                case 'l':
                    changeIndex = this.sliceNanBlockIndex + 1;
                    if (changeIndex >= this.mapSize[0] * this.mapSize[1] || this.sliceNanBlockIndex % this.mapSize[
                        0] == this.mapSize[0] - 1) return;
                    break;
            }
            this.mapList[this.sliceNanBlockIndex].id = this.mapList[changeIndex].id
            this.mapList[changeIndex].id = -1
            this.sliceNanBlockIndex = changeIndex;
            this.$forceUpdate()
            this.$emit('change', {
                mapblocks: this.mapList
            })
            this.$nextTick(() => {
                var success = true;
                for (var bno = 0; bno < this.mapList.length; bno++) {
                    if (this.mapList[bno].id != bno && this.mapList[bno].id != -1) {
                        success = false;
                        break;
                    }
                }
                if (success) {
                    this.mapList[this.mapList.length - 1].id = this.mapList.length - 1
                    this.$forceUpdate()
                    this.$emit('success', {
                        mapblocks: this.mapList
                    })
                }
            })
        },
        initRotateMap() {
            //mapSize.length = 1, 拼图具体层数
            var levels = (this.mapSize.length > 0 && this.mapSize[0] > 1) ? this.mapSize[0] : 2;
            this.mapList = []
            var that = this;
            this.$nextTick(() => {
                const qy = uni.createSelectorQuery().in(this)
                qy.select('#mapView').boundingClientRect(res => {
                    var middlePoint = [(res.left + res.right) / 2, (res.top + res.bottom) / 2]
                    for (var lno = levels; lno > 0; lno--) {
                        const rotate = Math.random() * 360
                        const w = [lno * res.width / 2 / levels, lno * res.width / 2 / levels]
                        that.mapList.push({
                            background: [res.left, res.top, res.width, res.height],
                            middlePoint: middlePoint,
                            width: w,
                            imgSource: that.sourseImg,
                            rotate: rotate === 0 ? 30 : rotate,
                        })
                    }
                    // that.mapList[that.mapList.length - 1].rotate = 0; // 最中间的图 是否不改变角度
                    that.$forceUpdate()
                    this.$emit('init', {
                        mapblocks: this.mapList
                    })
                }).exec()
            })
        },
        rotateMapTouchStart(e, lno) {
            var touchPoint = [e.touches[0].clientX, e.touches[0].clientY]
            var midPoint = this.mapList[lno].middlePoint
            var startDeg = Math.atan2(touchPoint[1] - midPoint[1], touchPoint[0] - midPoint[0])
            startDeg = startDeg * 180 / Math.PI
            this.rotateTouchStartdeg = startDeg
        },
        rotateMapTouchMove(e, lno) {
            var touchPoint = [e.touches[0].clientX, e.touches[0].clientY]
            var midPoint = this.mapList[lno].middlePoint
            var startDeg = Math.atan2(touchPoint[1] - midPoint[1], touchPoint[0] - midPoint[0])
            startDeg = startDeg * 180 / Math.PI
            this.mapList[lno].rotate += startDeg - this.rotateTouchStartdeg
            this.rotateTouchStartdeg = startDeg
            this.$forceUpdate()
        },
        rotateMapTouchEnd() {
            var finishGame = true;
            for (var lno = 0; lno < this.mapList.length; lno++) {
                var rotate = this.mapList[lno].rotate
                // 规范角度5~10
                if (Math.min(Math.abs(360 - rotate), Math.abs(rotate)) < (10 - 5) * lno / this.mapList.length + 5) {
                    this.mapList[lno].rotate = 0
                } else {
                    finishGame = false
                    break
                }
            }
            this.$forceUpdate()
            this.$emit('change', {
                mapblocks: this.mapList
            })
            if (finishGame) {
                this.$emit('success', {
                    mapblocks: this.mapList
                })
            }
        },
        initMoveMap() {
            this.mapList = []
            this.randomList = []
            for (var i = 0; i < this.mapSize[0] * this.mapSize[1]; i++) {
                this.mapList.push({
                    id: -1,
                    imgSource: this.sourseImg
                })
                var ri = Math.floor(Math.random() * this.mapSize[0] * this.mapSize[1])
                while (this.randomList.indexOf(ri) >= 0) {
                    ri = Math.floor(Math.random() * this.mapSize[0] * this.mapSize[1])
                }
                this.randomList.push(ri)
            }
            this.$emit('init', {
                mapblocks: this.mapList
            })
        },
        waitBlockMoveStart(e) {
            const index = e.currentTarget.dataset.index
            // uni.vibrateShort();
            this.touchStartPoint = [
                e.touches[0].clientX - uni.upx2px(this.mapWidth / this.mapSize[0]) / 2,
                e.touches[0].clientY - uni.upx2px(this.mapWidth / this.mapSize[0]) / 2
            ]
            this.moveTouchStartIndex = index;
        },
        waitBlockMoveMove(e) {
            this.touchStartPoint = [
                e.touches[0].clientX - uni.upx2px(this.mapWidth / this.mapSize[0]) / 2,
                e.touches[0].clientY - uni.upx2px(this.mapWidth / this.mapSize[0]) / 2
            ]
        },
        waitBlockMoveEnd(e) {
            var that = this;
            const qy = uni.createSelectorQuery().in(this)
            qy.selectAll('#mapView .block').boundingClientRect(res => {
                for (var i = 0; i < res.length; i++) {
                    var blockArea = [res[i].left, res[i].right, res[i].top, res[i].bottom]
                    if (that.touchStartPoint[0] >= blockArea[0] && that.touchStartPoint[0] < blockArea[1] &&
                        that.touchStartPoint[1] >= blockArea[2] && that.touchStartPoint[1] < blockArea[3]) {
                        //在该方块范围
                        that.$nextTick(() => {
                            that.mapList[i].id = that.moveTouchStartIndex;
                            that.touchStartPoint = [0, 0];
                            that.moveTouchStartIndex = -1;
                            that.$forceUpdate()
                            that.$emit('change', {
                                mapblocks: that.mapList
                            })
                            that.$nextTick(() => {
                                var success = true;
                                for (var bno = 0; bno < that.mapList.length; bno++) {
                                    if (that.mapList[bno].id == -1) {
                                        success = false;
                                        break;
                                    }
                                }
                                if (success) {
                                    that.$emit('success', {
                                        mapblocks: that.mapList
                                    })
                                }
                            })
                        })
                        return;
                    }
                }
                that.touchStartPoint = [0, 0];
                that.moveTouchStartIndex = -1;
                that.$forceUpdate()
            }).exec()
        },
        isInsideMap(bno) {
            return this.mapList.some(val => bno == val.id)
        },
        initCubeMap() {
            this.mapList = [];
            for (var bno = 0; bno < this.mapSize[0] * this.mapSize[1]; bno++) {
                this.mapList.push({
                    id: bno,
                    imgSource: this.sourseImg
                })
            }
            //随机初始化
            for (var t = 0; t < this.initChangeSteps; t++) {
                this.cubeMapMove(Math.floor(Math.random() * (this.mapSize[0] + this.mapSize[1])), Math.random() > 0.5,
                    1)
            }
            this.$emit('init', {
                mapblocks: this.mapList
            })
        },
        cubeMapMove(direction = 0, forward = true, init = '') {
            //direction在[0,mapSize[0]+mapSize[1])之间，前mapSize[0]是竖直方向，后面是水平方向
            //forward是加减操作，竖直方向+为向下拨，水平方向+为向右拨
            //获取需要改变的方块列表
            var changeList = []
            if (direction < this.mapSize[0]) {
                for (var i = 0; i < this.mapList.length; i++) {
                    if (i % this.mapSize[0] == direction) {
                        changeList.push(this.mapList[i])
                    }
                }
            } else {
                for (var i = 0; i < this.mapList.length; i++) {
                    if (Math.floor(i / this.mapSize[0]) == direction - this.mapSize[0]) {
                        changeList.push(this.mapList[i])
                    }
                }
            }
            //改变
            var afterChangeList = []
            if (forward) {
                for (var j = 1; j < changeList.length; j++) {
                    afterChangeList.push(changeList[j])
                }
                afterChangeList.push(changeList[0])
            } else {
                afterChangeList.push(changeList[changeList.length - 1])
                for (var j = 0; j < changeList.length - 1; j++) {
                    afterChangeList.push(changeList[j])
                }
            }
            //返回
            var thei = 0
            if (direction < this.mapSize[0]) {
                for (var i = 0; i < this.mapList.length; i++) {
                    if (i % this.mapSize[0] == direction) {
                        this.mapList[i] = afterChangeList[thei++]
                    }
                }
            } else {
                for (var i = 0; i < this.mapList.length; i++) {
                    if (Math.floor(i / this.mapSize[0]) == direction - this.mapSize[0]) {
                        this.mapList[i] = afterChangeList[thei++]
                    }
                }
            }

            const success = this.mapList.every((val, idx) => idx == val.id)
            if (init && success) { // 如果在初始化的时候拼图顺序正确 则再拼一次
                this.cubeMapMove(direction, forward, init)
                return
            }

            this.$forceUpdate()
            this.$emit('change', {
                mapblocks: this.mapList
            })

            if (success) {
                this.$nextTick(() => {
                    this.mapList[this.mapList.length - 1].id = this.mapList.length - 1
                    this.$forceUpdate()
                    this.$emit('success', {
                        mapblocks: this.mapList
                    })
                })
            }
        }
    }
}
</script>

<style scoped lang='scss'>
.drawView {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
}

.mapView {
    display: block;
    overflow: hidden;
}

.waitMapView {
    margin-top: 20px;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap; // 滚动必须加的属性
}

.mapView .block {
    float: left;
    background: radial-gradient(#c5c5c5, #f5f5f5);
    overflow: hidden;
}

.mapView .textBlock {
    float: left;
    background: #FFFFFF;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: larger;
    color: #007AFF;
}

.moveBlockOver:hover {
    filter: brightness(50%);
}

.waitMapView .block {
    display: inline-block;
    margin-right: 10px;
    background: radial-gradient(#c5c5c5, #f5f5f5);
    overflow: hidden;
}

.waitMapView .movingBlock {
    position: fixed;
    margin: 0;
    background: radial-gradient(#c5c5c5, #f5f5f5);
    overflow: hidden;
    z-index: 100;
}

.innerImage {
    position: relative;
}

.controlBox {
    margin-top: 20px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.controlBox .btn {
    width: 22%;
    border-radius: 20%;
    border: #007AFF 2px solid;
    background-color: #FFFFFF;
    color: #007AFF;
    text-align: center;
}

.controlBox .btn:active {
    background-color: #007AFF;
    color: #FFFFFF;
}

.centerMapBlock {
    position: absolute;
    border: #007AFF 1px solid;
    border-radius: 50%;
    overflow: hidden;
}


.slice, .cube {
    .block {
        border: 2rpx solid #f8f8f8;
        box-sizing: border-box;
    }
}
</style>