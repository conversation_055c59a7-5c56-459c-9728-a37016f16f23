<template>
    <view class="page" :style="pageStyle">
        <view v-if="init_loading" class="init-loading text-center">
            <load-ani/>
        </view>

        <view v-if="game_time_countdown >= 0" class="game-time-show flex-all-center">
            <view>
                <view class="flex-all-center">
                    <view class="game-time-countdown">
                        <view class="game-time-countdown-time font-bold color-error flex-all-center"
                              :class="{'font34': game_time_countdown < 10, 'font24': game_time_countdown >= 10}">
                            {{ game_time_countdown }}
                        </view>
                    </view>
                </view>
                <view class="font14 color-white text-center">请在限定时间内完成拼图</view>
            </view>

        </view>

        <view class="game-container" @touchstart="startGame">
            <gwh-jigsaw-puzzle
                v-if="game_data_complete"
                :map-width="710"
                :sourse-img="sourse_img"
                :map-size="[map_size, map_size]"
                :game-type="game_type"
                :init-change-steps="map_size * 10"
                @success="gameSuccess"
            />
        </view>

        <view class="clear clearfix">
            <view class="fr pr10 pb5 font14 color-fff" @click="lookOriginalImage">查看原图</view>
        </view>
        
        <view v-if="gameplay_tips" class="gameplay-tips">
            <view class="color-title">玩法介绍</view>
            <view class="color-content pt5">{{ gameplay_tips }}</view>
        </view>
        
        <template v-if="showAD">
            <xwy-ad :ad_type="3"/>
            <xwy-ad :ad_type="66"/>
        </template>

        <game-result-popup ref="resultPopup" :show-ad="showAD"/>

        <uni-popup ref="gameTips" :is-mask-click="false">
            <view class="game-tips bg-white">
                <view class="game-tips-title">游戏说明</view>
                <view class="game-tips-content color-content text-center">
                    <template v-if="game_time_countdown > 0">{{ game_time_countdown }}秒内</template>
                    完成拼图奖励{{ integral_num }}积分。
                </view>

                <view class="p10">
                    <view class="start-game-button" @click="gameStart">开始拼图</view>
                </view>
            </view>

            <view v-if="showAD" class="flex-all-center pt5">
                <xwy-ad :ad_type="3"></xwy-ad>
                <xwy-ad :ad_type="66"></xwy-ad>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()

import gwhJigsawPuzzle from "../components/gwh-jigsaw-puzzle.vue"
import puzzleConfig from '../config/puzzle-config'
const PuzzleTypeList = puzzleConfig.getPuzzleTypeList()

export default {
    components: {
        gwhJigsawPuzzle
    },
    
    data() {
        return {
            init_loading: true,
            game_data_complete: false,
            sourse_img: '',
            game_level: 1,
            game_type: '',
            map_size: 2,
            game_success: false,
            game_start: false,
            game_time_countdown: -1,
            showAD: false,
            bg_img: '',
            integral_num: 0
        }
    },
    
    computed: {
        gameplay_tips() {
            return PuzzleTypeList.find(v => v.type === this.game_type)?.gameplay_tips || ''
        },

        pageStyle() {
            if (!this.bg_img) return ''
            return `background-image: url(${this.bg_img}); background-position: center; background-size: cover; background-repeat: no-repeat;`
        }
    },
    
    onUnload() {
        this.clearGameTimeCountdownInterval()
    },

    onLoad(params) {
        this.paramsHandle(params)
        this.$login.uniLogin(() => {
            this.init()
        })
    },

    methods: {
        paramsHandle(params) {
            this.id = Number(params.id)
            if (params.active_id) this.active_id = params.active_id
            if (params.bg_img) this.bg_img = params.bg_img
            if (params.nav_bg_color || params.nav_font_color) {
                uni.setNavigationBarColor({
                    backgroundColor: params.nav_bg_color || '#B7C9FB',
                    frontColor: params.nav_font_color || '#ffffff',
                    fail: err => console.log(err)
                })
            }
        },
        
        async init() {
            await this.getActiveDetails()
            await this.getPuzzleDetails()
            this.init_loading = false

            this.$refs.gameTips.open()
        },

        gameStart() {
            this.$refs.gameTips.close()
        },
        
        async getActiveDetails() {
            let activity_detail = app.globalData['activity_detail']
            
            if (!activity_detail || activity_detail.active_id !== this.active_id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.active_id
                    }
                })

                if (!res?.data?.active_details) {
                    uni.hideLoading()
                    return this.$uni.showModal(res?.info || '活动详情获取失败', {success() {uni.navigateBack()}})
                }

                if (res.data.active_more_data?.['active_conf_set']?.rush_round_set?.puzzle_image?.bg_img) {
                    this.bg_img = res.data.active_more_data['active_conf_set'].rush_round_set.puzzle_image.bg_img
                }

                activity_detail = res.data.active_details
            }

            if (!activity_detail.rank_set?.closed_AD) this.showAD = true

            uni.hideLoading()
        },
        
        async getPuzzleDetails() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin.puzzle/image_job_details',
                data: {
                    id: this.id
                }
            })

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '任务获取失败', {
                success: () => uni.navigateBack()
            })

            const details = res?.data?.details
            if (!details) return this.$uni.showModal('任务获取失败', {success: () => uni.navigateBack()})

            this.$uni.setNavigationBarTitle(details.job_name || '拼图')

            this.sourse_img = this.getImageUrl(details)

            this.game_level = Math.round(details.starts / 10)
            this.game_type = details.conf_json.puzzle_type
            if (details.conf_json.time_limit) {
                const time = Number(details.conf_json.time_limit)
                this.game_time_limit = time
                this.game_time_countdown = time
            }
            if (details.conf_json.map_size) this.map_size = details.conf_json.map_size
            this.integral_num = details.reward_integral_num || 0

            
            this.$nextTick(() => {
                this.game_data_complete = true
            })
            
            // this.reward_integral_num = details.reward_integral_num
            // this.memo = details.memo || ''
            // if (details.conf_json) this.conf_json = details.conf_json
        },

        getImageUrl(details) {
            if (details.image_url) return details.image_url
            const images = details.conf_json.rand_img_list
            return images[Math.floor(Math.random() * images.length)]
        },


        lookOriginalImage() {
            this.$uni.previewImage(this.sourse_img)
        },
        

        startGame() {
            if (this.game_start) return
            this.game_start = true
            this.game_start_time = Math.floor(Date.now() / 1000)
            if (this.game_time_limit) this.openGameTimeCountdown()
        },

        openGameTimeCountdown() {
            if (this.game_time_countdown_interval) this.clearGameTimeCountdownInterval()
            const limit_time = this.game_time_limit
            this.game_time_countdown_interval = setInterval(() => {
                const now_time = Math.floor(Date.now() / 1000)
                const take_time = now_time - this.game_start_time
                this.game_time_countdown = limit_time - take_time
                if (this.game_time_countdown <= 0) {
                    this.game_time_countdown = 0
                    this.gameOver()
                }
            }, 1000)
        },

        clearGameTimeCountdownInterval() {
            if (!this.game_time_countdown_interval) return
            clearInterval(this.game_time_countdown_interval)
            this.game_time_countdown_interval = null
        },

        gameSuccess() {
            if (this.game_success) return
            this.game_start = false
            this.game_success = true
            this.clearGameTimeCountdownInterval()
            const game_end_time = Math.floor(Date.now() / 1000)
            this.take_time = game_end_time - this.game_start_time
            this.increaseIntegral()
        },
        
        async increaseIntegral() {
            const {randomCoding, base64: {encode}} = this._utils
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/puzzle_result_integral',
                data: {
                    job_id: this.id,
                    active_id: this.active_id,
                    sign: randomCoding() + encode(`${this.active_id}+${this.take_time}`)
                }
            })
            let status = 'success', info = res?.info, integral = 0
            if (res?.status === 1) {
                info ||= '拼图成功'
                integral = res?.data?.num || 0
                this.getOpenerEventChannel?.()?.emit?.('puzzleSuccess')
            } else {
                status = 'fail'
                info ||= '拼图失败'
            }
            
            this.resultPopupShow(status, info, integral)
        },

        gameOver() {
            this.game_start = false
            this.clearGameTimeCountdownInterval()
            this.resultPopupShow('fail', '时间到，拼图失败')
        },

        resultPopupShow(type, info, integral = 0) {
            this.$refs.resultPopup.open({
                code: type === 'success' ? 1 : 0,
                integral,
                info
            })
        }
    }
}
</script>

<style lang="scss">
.page {
    height: 100vh;
    box-sizing: border-box;
    background: radial-gradient(circle at 20px 20px, #AABEFA 5px, #B7C9FB 0);
    background-size: 30px 30px;
    overflow: hidden;
}

.init-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    padding-top: 30vh;
    box-sizing: border-box;
    z-index: 999999;
    background-color: #fff;
}

.game-container {
    padding: 20rpx;
}

.game-time-show {
    height: 90px;

    .game-time-countdown {
        width: 60px;
        height: 60px;
        background-image: url(https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/guessing_songs/count_down.png);
        background-size: cover;
        background-repeat: no-repeat;

        .game-time-countdown-time {
            line-height: 60px;
            padding-top: 3px; // 因为闹钟背景图的圆圈不是在正中间
            width: 100%;
        }
    }
}

.gameplay-tips {
    margin: 10rpx 20rpx;
    padding: 20rpx;
    box-sizing: border-box;
    width: 710rpx;
    background-image: url(https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/guessing_songs/dk.png);
    background-size: 100% 100%;
    background-position: center;
}

.game-tips {
    width: 90vw;
    border-radius: 10px;

    .game-tips-title {
        line-height: 44px;
        text-align: center;
    }

    .game-tips-content {
        padding: 0 20px;
        line-height: 30px;
    }

    .start-game-button {
        width: 150px;
        line-height: 40px;
        border-radius: 20px;
        text-align: center;
        font-size: 18px;
        background-color: #ff985e;
        color: #fff;
        margin: 10px auto;
    }
}
</style>