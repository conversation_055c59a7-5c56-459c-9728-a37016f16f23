module.exports = {
    getPuzzleTypeList() {
        return [
            // {type: 'huarongdao', name: '华容道拼图'},
            {
                type: 'rotate',
                name: '旋转拼图',
                map_size_restrict: {min: 2, max: 10},
                gameplay_tips: '你需要通过旋转每一圈拼图来恢复完整的图案。仔细观察拼图的细节，通过不断旋转使所有拼图正确对齐。'
            },
            {
                type: 'slice', 
                name: '滑动拼图', 
                map_size_restrict: {min: 2, max: 10},
                gameplay_tips: '你需要通过滑动拼图块来复原完整的图案。请利用空白位置，不断移动拼图块，直至所有图块排列正确。'
            },
            {
                type: 'cube', 
                name: '魔方拼图', 
                map_size_restrict: {min: 2, max: 10},
                gameplay_tips: '你需要通过旋转魔方的各个面来恢复图片。点击小箭头可以旋转它们，通过不断的旋转使所有拼图正确对齐。'
            },
            /*{
                type: 'move', 
                name: '拖动拼图', 
                map_size_restrict: {min: 2, max: 10},
                gameplay_tips: '你需要通过旋转魔方的各个面来恢复图片。点击小箭头可以旋转它们，通过不断的旋转使所有拼图正确对齐。'
            }*/
        ]
    }
}