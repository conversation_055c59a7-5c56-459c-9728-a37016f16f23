<template>
    <view class="page bg-background">
        <activity-delete-tips v-if="error" :activeId="id"/>


        <view v-if="detail && detail.active_id">
            <!-- #ifndef H5 -->
            <image v-if="detail.logo" class="logo" mode="widthFix" :src="detail.logo"/>
            <!-- #endif -->
            <view class="bg-white p10">
                <view class="color-title font18" @click="copy(id)">{{ detail.name }}</view>
                <view v-if="detail.organizer" class="color-content font16">主办方：{{detail.organizer }}</view>
            </view>

            <template v-if="!detail.rank_set || !detail.rank_set.closed_AD">
                <xwy-ad v-if="!shareShow && !popup_show" :ad_type="4"></xwy-ad>
                <xwy-ad :ad_type="3"></xwy-ad>
            </template>


            <view class="tools bg-white">
                <view class="title flex-kai">
                    <view class="color-title">活动管理</view>
                    <navigator v-if="document_show" url="/pages/other/activity-document?types=5">
                        <text class="iconfont icon-word-file color-light-primary font14"></text>
                        <text class="font14 color-light-primary" style="padding-left: 2px;">活动帮助文档</text>
                    </navigator>
                </view>

                <view class="list flex-row flex-wrap text-center">
                    <view class="item" hover-class="navigator-hover" @click="toEdit">
                        <text class="iconfont icon-edit color-primary font34"></text>
                        <view class="font14 color-content">活动修改</view>
                    </view>

                    <navigator :url="'/pages/activity/admin/activity_user_list?id=' + id" class="item">
                        <text class="iconfont icon-users color-primary font34"></text>
                        <view class="font14 color-content">活动用户</view>
                    </navigator>

                    <navigator :url="'/pages/activity/admin/blacklist?id=' + id" class="item">
                        <text class="iconfont icon-blacklist color-primary font34"></text>
                        <view class="font14 color-content">黑名单用户</view>
                    </navigator>

                  
                    <navigator
                        class="item"
                        :url="`/pages/news/list?type=user&vip=${detail.rank_set && detail.rank_set.closed_AD || 0}&active_id=${id}&just_look_active=1`"
                    >
                        <text class="iconfont icon-writing font34 color-primary"/>
                        <view class="font14 color-content">文章管理</view>
                    </navigator>

                    <navigator
                        v-if="detail.rank_set && detail.rank_set.closed_AD"
                        class="item"
                        :url="`/pages/category/list?types=8&just_look_active=1&active_id=${id}`"
                    >
                        <text class="iconfont icon-dating font34 color-primary"/>
                        <view class="font14 color-content">文章分类</view>
                    </navigator>
                    <view class="item" @click="copyActivityPages">
                        <text class="iconfont icon-copy color-primary font34"></text>
                        <view class="font14 color-content">复制路径</view>
                    </view>

                    <!-- #ifndef H5 -->
                    <view class="item" @click="showWebUrl">
                        <text class="iconfont icon-screen color-primary font34"></text>
                        <view class="font14 color-content">web端管理</view>
                    </view>
                    <!-- #endif -->

                    <navigator class="item" url="/pages/other/contact">
                        <uni-icons type="chatboxes" size="34" color="#2d8cf0"/>
                        <view class="font14 color-content">联系客服</view>
                    </navigator>

                    <!-- #ifdef MP-WEIXIN -->
                    <view class="item" @click="openComment">
                        <text class="iconfont icon-feedback color-primary font34"></text>
                        <view class="font14 color-content">评价邀请</view>
                    </view>
                    <!-- #endif -->

                    <view class="item" @click="shareShow = true">
                        <text class="iconfont icon-share color-primary font34"></text>
                        <view class="font14 color-content">转发分享</view>
                    </view>

                </view>
            </view>
            

        </view>


        <canvas id="qrcode" canvas-id="qrcode"
                style="width: 500px; height: 500px; position: fixed; top: -999999px; left: -99999px;">
        </canvas>
        <view v-if="shareShow" class="share-popup" @click="shareShow = false">
            <view class="share-popup-main bg-white">
                <view class="flex-row">
                    <!-- #ifndef H5 -->
                    <button class="share-item" open-type="share">
                        <text class="iconfont icon-share color-primary font28"/>
                        <view class="color-sub font14">转发给微信好友</view>
                    </button>
                    <!-- #endif -->
                    <button class="share-item" @click="showShareImage">
                        <text class="iconfont icon-qr-code font28 color-warning"/>
                        <view class="color-sub font14">生成活动二维码</view>
                    </button>
                </view>
                <view class="share-cancel font16 color-sub text-center">取消</view>
            </view>
        </view>



        <uni-popup ref="export_ranking_success" type="center">
            <view class="export_ranking_popup text-center bg-white">

                <view class="popup-close" @click="uniPopupClose('export_ranking_success')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>

                <view class="top bg-primary color-white" style="padding: 50px 20px 20px;">
                    <icon type="success" size="80" color="#ffffff"></icon>
                    <view class="font18">导出成功</view>
                </view>

                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ export_ranking_success_tips }}
                    </view>
                    <view class="export_ranking-btn bg-info color-white"
                          hover-class="navigator-hover" @click="copyDownloadSrc(false)">复制下载地址
                    </view>
                </view>

            </view>
        </uni-popup>


        <web-admin-src-copy-popup ref="web-admin-src-copy-popup"/>


    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import my_storage from '@/utils/storage.js'

export default {
    data() {
        return {
            loading: true,
            id: '',
            detail: {},
            error: '',
            shareShow: false,
            export_ranking_success_tips: '',
            exam_open: false,
            popup_show: false,
            document_show: this.xwy_config.showActivitySpecificationDocument(25)
        }
    },

    onShareAppMessage() {
        let path = '/pages/light-up-map/user/details?id=' + this.id
        return {
            title: this.detail.conf?.active?.share_title || this.detail.name || '',
            path,
            imageUrl: this.detail.conf?.active?.share_image || this.detail.logo || ''
        }
    },

    onLoad(e) {
        console.log('活动详情页面路径参数', e)
        uni.showLoading({mask: true})
        if (!e.id) {
            this.loading = false
            uni.hideLoading()
            this.error = '请指定活动id'
            return false
        }

        this.id = e.id
        login.uniLogin(err => {
            this.loading = false
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            if (app.globalData['shop_info']?.['shop_set']?.static_url) {
                this.web_base_url = app.globalData['shop_info']['shop_set'].static_url
            }


            this.getDetail()
        })
    },

    methods: {
        getDetail() {
            xwy_api.getActivityDetail(this.id, res => {
                uni.hideLoading()
                if (res.data?.['active_details']) {
                    const detail = res.data['active_details']
                    my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)
                    this.detail = detail
                    if (detail.rank_set?.exam_open) this.exam_open = true
                } else {
                    this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                }
            })
        },

        toEdit() {
            uni.navigateTo({
                url: this.xwy_config.getActivityPath(this.detail.types, 'add') + '?id=' + this.id
            })
        },

        uniPopupOpen(ref) {
            this.popup_show = true
            this.$refs[ref].open()
        },

        uniPopupClose(ref) {
            this.popup_show = false
            this.$refs[ref].close()
        },
        

        copyDownloadSrc(hide = false) {
            uni.setClipboardData({
                data: this.export_ranking_src,
                success: () => hide ? uni.hideToast() : this.$uni.showToast('复制成功', 'success', 1000)
            })
        },

        showShareImage() {
            if (this.share_image) {
                this.previewShareImage()
                return false
            }

            this.getShareImage()
        },


        getShareImage() {
            this.$uni.showLoading('二维码生成中...')

            const qr_data = {
                page: 'pages/light-up-map/user/details',
                scene: 'id=' + this.detail.id,
                cb: qr_src => {
                    uni.hideLoading()
                    if (!qr_src) return uni.showModal({
                        title: '提示',
                        content: '活动海报生成失败',
                        showCancel: false
                    })

                    this.share_image = qr_src
                    this.previewShareImage()
                }
            }
            if (this.detail.rank_set?.closed_AD && this.detail.conf?.active?.qrcode_logo) {
                qr_data.qrcode_logo = this.detail.conf.active.qrcode_logo
                qr_data.canvas_id = 'qrcode'
                qr_data.canvas_this = this
            }

            xwy_api.getQrcode(qr_data)
        },

        previewShareImage() {
            uni.previewImage({
                urls: [this.share_image]
            })
        },


        openComment() {
            this.plugin ||= requirePlugin("wxacommentplugin")
            this.plugin.openComment({
                fail: err => console.log(err)
            })
        },

        showWebUrl() {
            this.$refs['web-admin-src-copy-popup'].open({
                successCallback: () => this.popup_show = true,
                closeCallback: () => this.popup_show = false
            })
        },

      
        copyActivityPages() {
            let data = 'pages/light-up-map/user/details?id=' + this.id
            if (this.detail.screen_pic) data += `&screen_pic=` + this.detail.screen_pic
            uni.setClipboardData({
                data,
                success() {
                    uni.hideToast()
                    uni.showModal({
                        title: '复制成功',
                        content: '小程序路径地址复制成功，请粘贴到公众号自定义菜单设置里',
                        showCancel: false
                    })
                }
            })
        },

        copy(data) {
            if (!data) return
            uni.setClipboardData({
                data,
                success: () => this.$uni.showToast('复制成功', 'none', 500)
            })
        }
    }
}
</script>

<style scoped>
.page {
    min-height: 100vh;
    padding-bottom: 15px;
    box-sizing: border-box;
}

.logo {
    display: block;
    width: 100vw;
    height: auto;
}

.tools {
    margin: 10px;
    border-radius: 10px;
    overflow: hidden;
}

.tools .title {
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.item {
    width: calc(100% / 4);
    padding: 10px 5px;
    box-sizing: border-box;
}

.share-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, .7);
}

.share-popup-main {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100vw;
    border-radius: 20px 20px 0 0;
    overflow: hidden;
}

.share-item {
    padding: 0;
    margin: 10px 0;
    background-color: #fff;
    border: none;
    font-weight: normal;
    width: 50% !important;
    line-height: 1.5;
}

.share-item::after {
    border: none;
}

.share-cancel {
    line-height: 44px;
    padding-bottom: 10px;
    border-top: 1px solid #eee;
}

.export_ranking_popup {
    width: 280px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}

.export_ranking-btn {
    line-height: 40px;
    border-radius: 20px;
}

.export_ranking-btn::after {
    border: none;
}
</style>
