<template>
    <view>
        <uni-popup ref="popup" type="bottom" :safe-area="false" @touchmove.stop.prevent="">
            <view class="popup bg-white">
                <view class="close-icon flex-all-center" @click="$refs.popup.close()">
                    <text class="iconfont icon-wrong color-disabled font18"></text>
                </view>
                <view class="text-center p10">请选择需要点亮的区域</view>
                <view v-if="cityType === 0" class="city-list flex-row flex-wrap">
                    <view class="city-item text-center" :class="{disabled: item.light}"
                          v-for="item in province_list" :key="item.id" @click="chooseCity(item)">
                        {{ item.name }}
                    </view>
                </view>
                <view v-if="cityType === 1" class="flex-row">

                    <scroll-view :scroll-y="true" :scroll-top="scrollTop" :scroll-with-animation="true"
                                 class="province-scroll-view">
                        <view class="province-item"
                              :class="provinceId === item.id ? 'bg-white' : 'bg-background'"
                              v-for="(item, index) in province_list" :key="item.id"
                              @click="provinceChange(item.id, index)">
                            {{ item.name }}
                        </view>
                    </scroll-view>

                    <scroll-view :scroll-y="true" class="city-scroll-view bg-white">
                        <view class="flex-row flex-wrap">
                            <view class="city-scroll-item" :class="{disabled: item.light}"
                                  v-for="item in cityList" :key="item.id" @click="chooseCity(item)">
                                {{ item.areaName }}
                            </view>
                        </view>
                    </scroll-view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "light-up-map-popup",
    props: {
        activeId:{
            type: String,
            default: ''
        },

        provinceList: {
            type: Array,
            default: []
        },
        
        cityType: {
            type: Number,
            default: 0
        }
    },
    
    data() {
        return {
            provinceId: '',
            cityList: [],
            scrollTop: 0
        }  
    },
    
    computed: {
        province_list() {
            return this.provinceList.map(city => ({
                id: city.id,
                name: city.areaName.slice(0, city.areaName.startsWith('内蒙古') || city.areaName.startsWith('黑龙江') ? 3 : 2),
                light: city.light
            }))
        }
    },

    methods: {
        open(data = {}) {
            const all_city_list = data.all_city_list || []
            if (data.light_city_list?.length) {
                all_city_list.forEach(city => {
                    city.light = !!data.light_city_list.find(v => v.city_area_id === city.id)
                })
            }
            
            this.all_city_list = all_city_list
            this.setCityList()
            
            this.$refs.popup.open()
        },

        provinceChange(id, index) {
            this.provinceId = id
            
            this.setCityList()
            
            const query = uni.createSelectorQuery().in(this)
            query.selectAll('.province-item').boundingClientRect()
            query.select('.province-scroll-view').boundingClientRect()
            query.exec(res => {
                const [items, {height: containerHeight}] = res
                const item = items[index]
                const itemOffsetTop = items.slice(0, index).reduce((total, curr) => total + curr.height, 0)
                this.scrollTop = itemOffsetTop + item.width / 2 - containerHeight / 2
            })
        },
        
        setCityList() {
            if (!this.provinceId || this.cityType === 0) return []
            this.cityList = this.all_city_list.filter(city => city.parentId === this.provinceId)  
        },

        async chooseCity(city) {
            if (city.light) return this.$uni.showToast(`${city.name || city.areaName || ''} 已点亮`)
            const res = await this.$uni.showModal(`确定点亮 ${city.name || city.areaName || ''} 吗?`, {showCancel: true})
            if (res.confirm) await this.lightCity(city)
        },
        
        async lightCity(city) {
            this.$uni.showLoading('点亮中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.light_map.user/submit_user_city_data',
                data: {
                    active_id: this.activeId,
                    city_area_id: city.id
                }
            })
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '点亮失败')
            this.$uni.showToast(`已点亮 ${city.name || city.areaName || ''}`)
            
            this.$emit('lightUpSuccess', city)
            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.popup {
    padding: 10px 0;
    border-radius: 10px 10px 0 0;
    position: relative;
    
    .close-icon {
        position: absolute;
        top: 0;
        right: 0;
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
    }
    
    .city-list {
        padding: 5px;
    }
    
    .city-item {
        width: calc(20% - 10px);
        margin: 5px;
        line-height: 40px;
        box-sizing: border-box;
        border: 1px solid #dedddd;
        border-radius: 2px;
        color: #333;
        font-size: 14px;
    }
    
    .disabled {
        color: #bbbec4;
    }
    
    .province-scroll-view {
        height: 50vh;
        width: 120px;
        
        .province-item {
            text-align: center;
            padding: 20px 0;
        }
    }
    
    .city-scroll-view {
        height: 50vh;
        padding: 10px;
        box-sizing: border-box;
        
        .city-scroll-item {
            padding: 10px;
            margin: 5px;
            border: 1px solid #dedddd;
            border-radius: 2px;
            font-size: 14px;
        }
    }
}
</style>