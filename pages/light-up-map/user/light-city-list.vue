<template>
    <view class="page bg-background">
        <view class="city-list">
            <view class="city-item bg-white" v-for="item in light_city_list" :key="item.id">
                <view class="city-info">
                    <text class="iconfont icon-map color-sub"></text>
                    <text>{{ item.city_name }}</text>
                </view>
                <view class="city-info color-sub font14">
                    <text class="iconfont icon-time color-sub"></text>
                    <text>{{ item.create_time }}</text>
                </view>
                <view v-if="item.memo" class="city-info color-sub font14">{{ item.memo }}</view>
                
                <view class="remove-icon" @click="removeCity(item)">
                    <text class="iconfont icon-delete color-sub"></text>
                </view>
            </view>
        </view>
        
        <view v-if="loading" class="text-center">
            <view v-show="current_page === 1" class="w-100" style="height: 30vh;"></view>
            <load-ani/>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            loading: true,
            light_city_list: [],
            current_page: 1,
            is_last_page: false
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.getUserLightCity()
    },
    
    onReachBottom() {
        if (this.loading || this.is_last_page) return
        this.current_page++
        this.getUserLightCity()
    },

    methods: {
        async getUserLightCity() {
            if (this.current_page === 1) {
                this.light_city_list = []
                this.is_last_page = false
            }
            
            this.loading = true
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.light_map.user/user_city_list',
                data: {
                    active_id: this.active_id,
                    page: this.current_page,
                    get_city_details: 1,
                    perpage: 20
                }
            })
            this.loading = false
            
            const data = res?.data?.list
            if (!data) {
                this.is_last_page = true
                return
            }

            const list = data.data || []
            const light_city_list = list.map(city => ({
                id: city.id,
                city_area_id: city.city_area_id,
                create_time: city.create_time,
                memo: city.memo,
                city_name: city['city_details']?.areaName || ''
            }))
            
            this.light_city_list = [...this.light_city_list, ...light_city_list]
            this.is_last_page = data.is_lastpage
        },
        
        async removeCity(city) {
            const modal = await this.$uni.showModal(`确定移除 ${city.city_name} 吗?`, {showCancel: true})
            if (!modal.confirm) return
            
            this.$uni.showLoading('移除中...')
            const res = await this.xwy_api.deleteRecords(99, city.id)
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '移除失败')
            
            this.$uni.showToast('已移除')
            
            this.current_page = 1
            await this.getUserLightCity()
            
            this.getOpenerEventChannel().emit('reloadUserData')
        }
    }
}
</script>

<style lang="scss" scoped>
.page {
    min-height: 100vh;
    box-sizing: border-box;
}

.city-list {
    padding-top: 1px;
    
    .city-item {
        margin: 10px;
        padding: 10px 60px 10px 30px;
        border-radius: 10px;
        line-height: 24px;
        position: relative;
        
        .city-info {
            position: relative;

            .iconfont {
                position: absolute;
                left: -20px;
            }
        }
        
        .remove-icon {
            position: absolute;
            top: 15px;
            right: 10px;
            width: 38px;
            height: 38px;
            line-height: 38px;
            text-align: center;
            background-color: #eee;
            border-radius: 50%;
        }
    }
}
</style>