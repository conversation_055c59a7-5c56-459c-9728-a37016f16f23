<template>
    <view class="page">
        <view class="init-loading flex-all-center" v-if="init_loading">
            <view class="text-center">
                <load-ani/>
            </view>
        </view>

        <activity-delete-tips v-if="error" :activeId="id"/>


        <template v-if="id">
            <view v-if="checked" class="top-msg top-msg-left" @click="lookLightCityList">
                <view class="top-msg-icon">
                    <view class="top-msg-inner-ring">
                        <text class="iconfont icon-map"></text>
                    </view>
                </view>
                <view class="top-msg-num">已点亮 {{ lightCityCount }}/{{ allCityCount }}</view>
            </view>

            

            <map id="map" class="map"
                 :scale="map_scale.scale" :min-scale="map_scale.min" :max-scale="map_scale.max"
                 :latitude="map_center.latitude" :longitude="map_center.longitude"
                 :polygons="polygons" @regionchange="regionChange"/>

            <view class="go-button-fixed color-white" @click="lightUpMap">
                <view>点亮</view>
                <view>地图</view>
            </view>


            <screen-picture
                v-if="screen_pic_show"
                :hide="screen_pic_hide"
                :image-src="screen_pic"
                :time="screen_pic_count_down"
                :show-button="true"
                @skipScreen="skipScreen"
            />


            <view class="icon-list">

                <navigator class="icon-item" v-if="is_my_activity" :url="'../admin/manage?id=' + id">
                    <text class="iconfont icon-setting"></text>
                </navigator>

                <view class="icon-item" v-if="is_joining" hover-class="navigator-hover"
                      @click="uniPopupOpen('my_info')">
                    <text class="iconfont icon-personal-data"></text>
                </view>

                <view class="icon-item" v-if="checked" hover-class="navigator-hover"
                      @click="lookLightCityList">
                    <text class="iconfont icon-footnotes"></text>
                </view>

                <view class="icon-item" hover-class="navigator-hover" @click="toTopList">
                    <text class="iconfont icon-trophy"></text>
                </view>

                <navigator class="icon-item" url="/pages/user/user">
                    <uni-icons type="person"/>
                </navigator>

                <view class="icon-item" hover-class="navigator-hover" @click="showActiveSharePopup">
                    <text class="iconfont icon-share"></text>
                </view>
            </view>

            <view v-if="join_popup_show" class="join-popup flex-all-center bg-white"
                  @touchmove.stop.prevent="">
                <view>
                    <view class="join-popup-c bg-white">
                        <view class="text-center font18 color-content p10">
                            <template v-if="update_attend_details">修改报名信息</template>
                            <template v-else>参加活动</template>
                        </view>


                        <template v-if="must_submit.length">
                            <template v-for="(item, index) in must_submit">

                                <input
                                    v-if="item.types === 1"
                                    :key="index"
                                    class="join-input"
                                    v-model="item.value"
                                    :placeholder="'请输入' + item.title + (item.rules === 1 ? ' (必填)' : '')"
                                />
                                <picker
                                    v-if="item.types === 2"
                                    :range="item.options"
                                    range-key="text"
                                    @change="mustValueChange($event, index)"
                                >
                                    <view class="join-input flex-kai">
                                        <view v-if="!item.value" class="color-sub">
                                            请选择{{ item.title }}{{ item.rules === 1 ? ' (必选)' : '' }}
                                        </view>
                                        <view v-if="item.value">{{ item.value }}</view>
                                        <text class="iconfont icon-more color-disabled font18"/>
                                    </view>
                                </picker>
                            </template>

                        </template>


                        <view class="join-popup-btns flex-row text-center font18">
                            <view class="join-popup-btn-cancel color-sub" @click="cancelJoin">取消</view>
                            <view class="join-popup-btn-confirm color-success" @click="joinAjax">确定</view>
                        </view>
                    </view>

                    <template v-if="!detail.rank_set || !detail.rank_set.closed_AD">
                        <view class="pt5">
                            <xwy-ad :ad_type="66"></xwy-ad>
                        </view>
                        <xwy-ad :ad_type="3"></xwy-ad>
                    </template>
                </view>


            </view>

            <active-share ref="activeShare"/>

            <uni-popup ref="activity_detail" type="center" @touchmove.stop.prevent="">
                <view v-if="detail && detail.conf && detail.conf.active"
                      class="uni-popup-info detail-popup bg-white">
                    <view class="popup-close" @click="uniPopupClose('activity_detail')">
                        <uni-icons type="close" size="28" color="#b2b3b7"/>
                    </view>

                    <scroll-view :scroll-y="true" style="max-height: calc(100vh - 200px); padding: 10px 0;">
                        <view class="p10 bdb-10 bg-white">
                            <view class="text-center color-sub pb5">- 活动规则 -</view>
                            <activity-logo-title-time :details="detail" hide-logo hide-share/>

                            <view class="color-content font16">
                                活动参与方式：
                                <template v-if="detail.conf.active.enter_types === 1">
                                    自由报名参与活动
                                </template>
                                <template v-if="detail.conf.active.enter_types === 2">
                                    需要输入密码才能报名
                                </template>
                                <template v-if="detail.conf.active.enter_types === 3">
                                    报名需要审核通过才能参与活动
                                </template>
                            </view>
                        </view>

                        <view v-if="detail.content || news_detail" class="p10 bdb-10 bg-white">
                            <view class="text-center color-sub pb5">- 活动说明 -</view>
                            <view class="color-content font16">
                                <template v-if="detail.content && !news_detail">

                                    <rich-text :nodes="detail.content" :space="true" :selectable="true"/>
                                </template>
                                <template v-if="news_detail">
                                    <template v-if="news_detail.content">
                                        <u-parse :content="news_detail.content"/>
                                    </template>
                                </template>
                            </view>
                        </view>
                        <xwy-ad v-if="!loading && (!detail.rank_set || !detail.rank_set.closed_AD)"
                                :ad_type="66"></xwy-ad>
                    </scroll-view>
                </view>
            </uni-popup>

            <uni-popup ref="my_info" type="center" @touchmove.stop.prevent="">
                <view v-if="detail && detail.conf && detail.conf.active" class="uni-popup-info bg-white p20">
                    <view class="popup-close" @click="uniPopupClose('my_info')">
                        <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                    </view>
                    <view class="text-center color-sub pb5">- 活动报名信息 -</view>
                    <view class="text-center p10">

                        <image
                            class="headimg"
                            :src="headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"
                            mode="aspectFill"
                        />
                        <view>
                            <text class="color-primary" @click="updateHeadimg">更改头像</text>
                        </view>
                    </view>

                    <view
                        class="color-content font16 ptm5"
                        v-for="(item, index) in must_submit"
                        :key="index"
                        @click="updateAttendDetailShow"
                    >
                        <text>
                            {{ item.title }}：
                            <template>
                                <template v-if="item.value">{{ item.value }}</template>
                                <template v-else>
                                    <template v-if="item.types === 1">未填写</template>
                                    <template v-if="item.types === 2">未选择</template>
                                </template>
                            </template>
                        </text>
                        <text v-if="is_joining" class="iconfont icon-edit color-sub pl5"></text>
                    </view>
                    
                    <view class="color-content font16 ptm5">
                        点亮区域：{{ lightCityCount }}个
                    </view>



                    <template v-if="popup_open && (!detail.rank_set || !detail.rank_set.closed_AD)">
                        <view style="position: relative; left: -10px;">
                            <xwy-ad :ad_type="66"></xwy-ad>
                        </view>
                        <xwy-ad :ad_type="3"></xwy-ad>
                    </template>
                </view>
            </uni-popup>


            <template v-if="password_dialog_show">
                <uni-popup
                    ref="input_password"
                    type="dialog"
                    mode="input"
                    :is-mask-click="false"
                    @maskClick="copy(id)"
                >
                    <uni-popup-dialog
                        mode="input"
                        title="活动密码"
                        :value="password"
                        placeholder="请输入活动密码"
                        @confirm="passwordInputConfirm"
                        @close="passwordInputClose"
                    ></uni-popup-dialog>
                </uni-popup>
            </template>


            <expiration-reminder ref="expirationReminder"/>

            <light-up-map-popup ref="lightUpMapPopup" :city-type="cityType" :active-id="id"
                                :province-list="province_list" @lightUpSuccess="lightUpSuccess"/>
        </template>

    </view>
</template>

<script>
const app = getApp()
import my_storage from '@/utils/storage.js'
import activity_tool from "@/utils/acyivity_tool"

import lightUpMapPopup from '../components/light-up-map-popup'

export default {
    components: {lightUpMapPopup},
    data() {
        return {
            loading: true,
            screen_pic: '',
            screen_pic_show: false,
            screen_pic_hide: false,
            screen_pic_count_down: null,
            id: '',
            userid: '',
            is_my_activity: false,
            detail: {},
            user_details: {},
            error: '',
            join_popup_show: false,
            update_attend_details: false,
            headimg: '',
            username: '',
            checked: 0,
            is_joining: false,
            password: '',
            technology_support: false,
            must_submit: [],
            headimg_plugin: [],
            password_dialog_show: false,
            popup_open: false,
            news_detail: null,
            init_loading: true,

            province_list: [],

            map_center: {
                latitude: 30.675715,
                longitude: 104.677734
            },
            polygons: [],

            map_scale: {
                scale: 3,
                min: 3,
                max: 10
            },

            allCityCount: 0,
        }
    },
    
    computed: {
        lightCityCount() {
            return this.user_details?.light_map_num || 0
        },

        // 0: 省  1: 市
        cityType() {
            return this.detail?.conf?.active?.city_type || 0
        }
    },

    onLoad(params) {
        this.paramsHandle(params)
        this.$login.uniLogin(() => this.init(params))
    },

    methods: {
        paramsHandle(params) {
            if (params.screen_pic) this.screenPicShow(params.screen_pic)
            if (params.id) this.id = params.id
        },

        async init(params) {
            if (params.scene) {
                const id = await this.getActiveId(params.scene)
                if (!id) return
                this.id = id
            }

            this.createMapContext()
            await this.getDetail()
            await this.getProvincesMapData()
            await this.getProvincesCity()
            
            await this.reloadUserData()

            this.init_loading = false
        },

        createMapContext() {
            this.map = uni.createMapContext('map', this)  
        },
        
        async reloadUserData() {
            await this.getUserStatus()
            if (this.user_details.id) await this.getUserLightCity()

            this.setMapParams()
        },

        async getProvincesMapData() {
            const res = await this.xwy_api.request({
                url: `${this.xwy_config.object_storage_url}json-files/sport-step/light-up-map/province-boundary-map-data.json`,
                method: 'GET',
                not_token: true
            })

            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace list.properties.adcode */
            const list = res || []

            this.provincesMapData = list.filter(v => v.adcode && v.coordinates?.length)
        },

        async getProvincesCity() {
            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace res.data.province_city_area_list */
            /** @namespace res.data.province_city_area_list.areaCode */
            const province_res = await this.xwy_api.request({
                url: 'front.user.account.cityArea/city_area_list'
            })
            const province_list = province_res?.data?.province_city_area_list || []
            this.province_list = province_list.map(item => ({
                areaCode: Number(item.areaCode),
                areaName: item.areaName,
                id: item.id,
                light: false
            }))
            
            if (this.cityType === 0) {
                this.allCityCount = province_list.length
                return
            }
            
            const city_res = await this.xwy_api.request({
                url: 'front.user.account.cityArea/city_area_list',
                data: {
                    level: 2
                }
            })
            const city_list = city_res?.data?.province_city_area_list || []
            
            this.setCityList(city_list)
        },
        
        setCityList(city_list) {
            city_list = this.addCity(city_list)
            
            this.city_list = city_list.map(item => {
                const data = {
                    level: 2,
                    areaCode: Number(item.areaCode),
                    areaName: item.areaName,
                    id: item.id,
                    parentId: item.parentId
                }

                if (item.center) data.center = this.locationStr2Obj(item.center)

                return data
            })

            this.allCityCount = city_list.length
        },
        
        locationStr2Obj(str) {
            const center_arr = str.split(',')
            return {
                longitude: Number(center_arr[0]),
                latitude: Number(center_arr[1])
            }
        },
        
        
        addCity(city_list) {
            // 香港、澳门、台湾下没有城市, 需要手动添加
            const notCityList = ['香港', '澳门', '台湾']
            notCityList.forEach(cityName => {
                const city = this.province_list.find(v => v.areaName.startsWith(cityName))
                if (city) {
                    city_list.push({
                        areaCode: city.areaCode,
                        areaName: city.areaName,
                        id: city.id,
                        parentId: city.id,
                        center: city.center
                    })
                }
            })
            return city_list
        },

        async getUserLightCity() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.light_map.user/user_city_list',
                data: {
                    active_id: this.id,
                    page: 1,
                    perpage: 100
                }
            })

            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace list.city_area_id */
            const list = res?.data?.list?.data || []
            this.user_light_city_list = list.map(item => ({
                city_area_id: item.city_area_id
            }))
        },


        setMapParams() {
            const polygons = [], markers = []

            const {province_list, city_list, provincesMapData: mapData, user_light_city_list: lightList = []} = this
            
            
            if (this.cityType === 0) {
                // 单独保存每个省的中心点，用于点亮后地图中心点移动到该省
                const province_center_location_options = {}
                
                province_list.forEach((item, index) => {
                    const {id, areaCode} = item

                    item.light = lightList.some(v => v.city_area_id === id)

                    const data = mapData.find(v => v.adcode === areaCode) || {}
                    const {coordinates = [], centroid: [longitude, latitude] = ['', ''], name} = data

                    if (latitude && longitude) {
                        province_center_location_options[id] = {longitude, latitude}
                        if (name) this.pushMarker(markers, areaCode, latitude, longitude, name)
                    }

                    this.pushPolygon(polygons, coordinates, item.light, index, name)
                })
                
                this.province_center_location_options = province_center_location_options
            }
            
            if (this.cityType === 1) {
                lightList.forEach(item => {
                    const city = city_list.find(v => v.id === item.city_area_id)
                    if (city && city.level && city.level === 2 && city.center) {
                        markers.push({
                            id: city.areaCode,
                            latitude: city.center.latitude,
                            longitude: city.center.longitude,
                            title: city.areaName,
                            width: 40,
                            height: 40,
                            iconPath: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/red_flag.png',
                        })
                    }
                    
                })
            }
            
            this.polygons = polygons
            
            this.markers = markers
            this.addMarkers()
        },

        addMarkers() {
            this.map.addMarkers({
                markers: this.markers,
                clear: true
            })
        },

        pushMarker(markers, areaCode, latitude, longitude, name) {
            markers.push({
                id: areaCode,
                latitude,
                longitude,
                title: name,
                width: 10,
                height: 10,
                iconPath: 'http://www.xinweiyun.com/weixin/editor/pics/long_march/dot-null.png',
                callout: {
                    content: name,
                    color: '#333',
                    fontSize: 16,
                    display: 'BYCLICK',
                    bgColor: '#ffffff00',
                    anchorY: 20
                }
            })
        },
        
        pushPolygon(polygons, coordinates, light, index, name) {
            this.colorList ||= ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"]
            coordinates.forEach(points => {
                polygons.push({
                    points: points.map(([lng, lat]) => ({latitude: lat, longitude: lng})),
                    strokeWidth: 1,
                    strokeColor: "#000000",
                    fillColor: light ? this.colorList[index % this.colorList.length] : '#ffffff',
                    name
                })
            })
        },

        regionChange(e) {
            if (this.cityType !== 0) return
            const {type, causedBy, detail} = e || {}

            // 监听地图缩放，如果缩放值小于设置值，不显示省份名称
            if (type !== 'end' || causedBy !== 'scale') return
            const scale = detail?.scale

            this.markers.forEach(item => {
                item.callout.display = scale > 4 ? 'ALWAYS' : 'BYCLICK'
            })
            
            this.addMarkers()
        },


        lightUpMap() {
            if (!this.is_joining) return this.joinActivity()

            if (!activity_tool.actionCheck({
                is_joining: this.is_joining,
                checked: this.checked,
                begin_time: this.detail.begin_time,
                end_time: this.detail.end_time
            })) return
            
            const data = {}
            if (this.cityType === 1) {
                data.all_city_list = this.city_list
                data.light_city_list = this.user_light_city_list
            }
            
            this.$refs.lightUpMapPopup.open(data)
        },
        
        // 点亮成功后地图中心点移动到点亮的城市
        async lightUpSuccess(city) {
            const {map, cityType, province_center_location_options} = this
            if (!map) return this.reloadUserData()
            
            const location = cityType === 1 ? city.center : province_center_location_options?.[city.id]
            
            if (!location || !location.longitude || !location.latitude) return this.reloadUserData()

            // 因为手动缩放地图时，页面数据map_scale.scale是不会动态变的，如果之前代码设置了map_scale.scale为8，然后手动缩放了地图，map_scale.scale还是8，但是实际上地图组件的缩放比例不是8，所以要改成一个非8的值，再改成8
            const scale = cityType === 1 ? 8 : 5
            
            // 获取当前地图缩放比例，直接将页面data的map_scale.scale设置为这个值。没有获取到地图缩放比例则使用目标缩放值相近的数值
            this.map_scale.scale = await this.getMapScale() || (scale - 0.01)

            this.$nextTick(() => {
                this.map_scale.scale = scale
                map.moveToLocation(location)
            })
            
            await this.reloadUserData()
        },
        
        getMapScale() {
            if (!this.map) return null
            return new Promise(resolve => {
                this.map.getScale({
                    complete: res => resolve(res?.scale || null)
                })
            })
        },

        lookLightCityList() {
            if (!this.user_light_city_list?.length) return this.$uni.showToast('未点亮任何区域')
            this.$uni.navigateTo(`./light-city-list?active_id=${this.id}`, {
                events: {
                    reloadUserData: () => this.reloadUserData()
                }
            })  
        },

        toTopList() {
            uni.navigateTo({
                url: '/pages/ranking-list/ranking-list?id=' + this.id
            })
        },

        async getActiveId(scene) {
            const sceneStr = decodeURIComponent(scene)
            const id = this._utils.getUrlParams('id', sceneStr)
            if (!id) {
                this.init_loading = false
                this.loading = false
                await this.$uni.showModal('从二维码获取id失败')
                return null
            }
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/get_long_active_id_by_short_id',
                data: {id}
            })
            const active_id = res?.data?.['long_active_id']
            if (!active_id) {
                this.init_loading = false
                this.loading = false
                await this.$uni.showModal(res && res.info || '长id获取失败')
                return null
            }
            return active_id
        },

        async getDetail() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details',
                data: {active_id: this.id}
            })
            const details = res?.data?.active_details || {}
            if (!details) {
                this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                return
            }

            my_storage.setActivityCloaeAdStorage(this.id, details.rank_set?.closed_AD)
            // #ifndef H5
            this.$refs.expirationReminder.open(details)
            // #endif
            app.globalData.activity_detail = details
            this.detail = details

            if (details.conf) {
                const conf = details.conf

                if (conf.active) {
                    const active = conf.active
                    if (!this.screen_pic && active.screen_pic) {
                        this.screenPicShow(active.screen_pic)
                    }

                    if (active.news?.news_id) this.changeDetailContent(active.news.news_id)
                }

                if (conf.must_submit) {
                    const must_submit = conf.must_submit
                    delete conf.must_submit
                    if (must_submit.length) {
                        must_submit.forEach(v => v.value = v.value || '')
                        this.must_submit = must_submit
                    }
                }
            }

            if (details.rank_set?.['shield_other']) {
                this.$uni.hideHomeButton()
                this._utils.updateShieldOtherInfo(details) // 更新纯净版缓存信息
            }

            this.addLookRecords()
            if (details.name) this.$uni.setNavigationBarTitle(details.name)
            if (app.globalData['userid'] === details.userid) this.is_my_activity = true
        },

        screenPicShow(src) {
            let isWechatApp = true
            // #ifndef MP-WEIXIN
            isWechatApp = false
            // #endif
            if (!isWechatApp) return

            uni.hideLoading()
            this.screen_pic = src
            this.screen_pic_count_down = 5
            this.screen_pic_show = true
            this.screenPicInterval = setInterval(() => {
                this.screen_pic_count_down--
                if (this.screen_pic_count_down <= 0) {
                    this.skipScreen()
                }
            }, 1000)
        },
        skipScreen() {
            clearInterval(this.screenPicInterval)
            this.screenPicInterval = null
            this.screen_pic_count_down = 0
            this.screen_pic_hide = true

            const timeout = setTimeout(() => {
                this.screen_pic_show = false
                this.screen_pic_hide = false
                this.passwordDialogShow()
                clearTimeout(timeout)
            }, 500)
        },

        addLookRecords() {
            const detail = this.detail
            const value = {
                active_id: detail.active_id,
                name: detail.name,
                types: detail.types,
                logo: detail.logo || this.xwy_config.active_default_logo,
                look_time: new Date().getTime()
            }

            if (detail.organizer) value.organizer = detail.organizer
            my_storage.addActivityLookRecords(value)
        },


        async getUserStatus() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: {active_id: this.id}
            })

            const attend_details = res?.data?.user_details
            if (attend_details) {
                const need_keys = ['id', 'light_map_num']
                need_keys.forEach(key =>  this.$set(this.user_details, key, attend_details[key] || ''))
                
                
                this.is_joining = true
                this.checked = attend_details.checked || 0

                this.loading = false
                uni.hideLoading()

                if (attend_details.nickname) this.username = attend_details.nickname
                if (attend_details.headimg) this.headimg = attend_details.headimg

                if (attend_details.must_submit) {
                    this.must_submit.forEach(v => {
                        attend_details.must_submit.forEach(vv => {
                            if (vv.title === v.title) v.value = vv.value
                        })
                    })
                }

            } else {
                this.no_attend = true
                this.loading = false
                uni.hideLoading()

                if (this.screen_pic_show) return false

                this.passwordDialogShow()
            }

        },


        changeDetailContent(news_id) {
            this.xwy_api.ajax({
                url: "front.news/news_details",
                data: {
                    access_token: app.globalData['access_token'],
                    news_id
                },
                success: res => {
                    console.log(res)
                    uni.hideLoading()

                    uni.hideLoading()
                    if (!res?.data?.['news_details']) {

                        return this.$uni.showModal(res.info || '文章内容获取失败', {
                            success: () => uni.navigateBack()
                        })
                    }

                    const detail = res.data['news_details']


                    if (detail.video_url) {
                        let video_type = 'txv_id'
                        if (detail.video_url.startsWith('http://') || detail.video_url.startsWith('https://')) {
                            video_type = 'http'
                        }
                        detail.video_type = video_type
                    }


                    if (detail.content) detail.content = this._utils.newsContentInit(detail.content)

                    this.news_detail = detail
                }
            })
        },


        passwordDialogShow() {
            if (this.detail.conf.active.password && this.no_attend) {
                const passwordDialogShow = () => {
                    this.password_dialog_show = true
                    const password = my_storage.getActivityPassword(this.id)
                    if (password) {
                        this.password = password
                        this.checkActivityPassword(password)
                    }
                    const timeout = setTimeout(() => {
                        this.$refs.input_password.open()
                        clearTimeout(timeout)
                    }, 30)
                }

                // 体验版不需要输入密码
                if (app.globalData['evn_version'] === 'trial') {
                    return this.$uni.showModal('此活动设置了活动密码，请勿报名参与活动！！！', {
                        showCancel: true,
                        cancelText: '进入活动',
                        confirmText: '输入密码',
                        success: res => {
                            res.confirm && passwordDialogShow()
                        }
                    })
                }

                passwordDialogShow()
            }
        },

        passwordInputConfirm(val) {
            if (!val) {
                // 纯净版取消输入密码出现去个人中心选项
                if (this.detail?.rank_set?.['shield_other']) {
                    uni.showModal({
                        title: '提示',
                        content: '请输入密码',
                        cancelText: '个人中心',
                        confirmText: '重新输入',
                        success: res => {
                            if (res?.confirm) {
                                this.$refs.input_password.open()
                            }
                            if (res?.cancel) {
                                uni.navigateTo({
                                    url: '/pages/user/user'
                                })
                            }
                        }
                    })
                    return this.$uni.showModal('请输入密码', {
                        showCancel: true,
                        cancelText: '个人中心',
                        confirmText: '重新输入',
                        success: res => {
                            if (res?.confirm) this.$refs.input_password.open()
                            if (res?.cancel) this.$uni.navigateTo('/pages/user/user')
                        }
                    })
                }

                return this.$uni.showModal('请输入密码', {success: () => this.$refs.input_password.open()})
            }
            this.checkActivityPassword(val)
        },

        async checkActivityPassword(password) {
            this.$uni.showLoading('密码验证中...')

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.user/check_active_password',
                data: {
                    active_id: this.id,
                    password
                }
            })
            uni.hideLoading()

            if (res?.status) {
                my_storage.rememberActivityPassword(this.id, password)
                this.$refs.input_password.close()
                return this.$uni.showToast('密码正确', '密码正确',)
            }


            await this.$uni.showModal(res && res.info || '密码错误', {
                success: () => this.$refs.input_password.open()
            })
        },

        passwordInputClose() {
            this.copy(this.id, true)

            if (getCurrentPages().length > 1) return uni.navigateBack()

            // 纯净版并且没有上一页面，重新弹出输入密码窗口
            if (app.globalData['shop_info']?.extend_set?.shield_other_active?.active_id) {
                return this.$uni.showModal('请输入活动密码', {
                    success: () => this.$refs.input_password.open()
                })
            }

            this.$uni.reLaunch('/pages/index/index')
        },


        async updateHeadimg() {
            uni.navigateTo({
                url: `/pages/other/headimg-list/headimg-list?active_id=${this.id}`,
                events: {
                    newImg: async obj => {
                        if (!obj?.src) return
                        this.$uni.showLoading('修改中...')
                        if (!obj.temp) return this.updateAttendDetail(obj.src)

                        const data = {
                            temp_data: {
                                path: obj.src
                            },
                            is_temp: 5
                        }
                        if (obj.size) data.temp_data.size = obj.size
                        const headimg = await this.xwy_api.uploadOneImage(data)
                        this.updateAttendDetail(headimg)
                    }
                }
            })
        },

        joinActivity() {
            if (this.loading) return
            if (this.detail.conf?.active?.submit) {
                const now_time = new Date().getTime()
                const {begin, end} = this.detail.conf.active.submit
                if (begin) {
                    const begin_time = new Date(begin.replace(/-/g, '/')).getTime()
                    if (now_time < begin_time) return this.$uni.showModal(`活动于${begin}开始报名`)
                }
                if (end) {
                    const end_time = new Date(end.replace(/-/g, '/')).getTime()
                    if (now_time > end_time) return this.$uni.showModal(`活动于${end}截止报名`)
                }
            }

            this.join_popup_show = true
        },

        updateAttendDetailShow() {
            this.update_attend_details = true
            this.join_popup_show = true
        },

        mustValueChange(e, index) {
            this.must_submit[index].value = this.must_submit[index].options[e.detail.value].text
            this.$forceUpdate()
        },


        cancelJoin() {
            this.join_popup_show = false
            this.update_attend_details = false
        },

        joinAjax() {
            if (this.update_attend_details) return this.updateAttendDetail()
            this.joining()
        },


        getMustSubmitData() {
            const must_submit = JSON.parse(JSON.stringify(this.must_submit))
            for (let i = 0; i < must_submit.length; i++) {
                const v = must_submit[i]
                v.options && delete v.options
                if (v.rules === 1 && v.value === '') {
                    const tips = v.types === 2 ? '选择' : '输入'
                    this.$uni.showToast(`请${tips}${v.title}`)
                    return false
                }
            }
            let must_submit_str = JSON.stringify(must_submit)
            must_submit_str = must_submit_str.replace(/·/g, '-')
            return this._utils.base64['encode'](must_submit_str)
        },

        updateAttendDetail(headimg) {
            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id
            }

            if (headimg) data.headimg = headimg

            const must_submit = this.getMustSubmitData()
            if (must_submit === false) return false
            data.must_submit = must_submit

            this.$uni.showLoading('修改中...')

            this.xwy_api.ajax({
                url: 'front.flat.sport_step.user/update_attend_details',
                data,
                success: res => {
                    if (!res.status) return this.$uni.showModal(res.info || '修改失败')
                    this.$uni.showToast(res.info || '修改成功', 'success')
                    this.cancelJoin()
                    this.getUserStatus()
                    this.reloadUserData()
                }
            })
        },

        joining() {

            const data = {
                active_id: this.id,
                access_token: app.globalData['access_token']
            }


            if (this.must_submit && this.must_submit.length) {
                const must_submit = this.getMustSubmitData()
                if (must_submit === false) return false
                data.must_submit = must_submit
            }

            data.nickname = this.username

            this.loading = true
            this.$uni.showLoading('报名中...')


            this.xwy_api.ajax({
                url: 'front.flat.sport_step.user/submit_attend_active',
                data,
                success: res => {
                    this.loading = false
                    uni.hideLoading()
                    console.log('报名活动', res)
                    if (!res.status) return this.$uni.showModal(res.info || '报名失败', {title: '报名失败'})

                    this.join_popup_show = false
                    this.$uni.showToast(res.info || '报名成功', 'success')

                    setTimeout(() => {
                        this.$uni.showLoading()
                        this.getUserStatus()
                        this.reloadUserData()
                    }, 1000)
                }
            })
        },


        showActiveSharePopup() {
            this.$refs.activeShare.open({
                page: 'pages/light-up-map/user/details',
                scene: 'id=' + this.detail.id,
                qrcode_logo: this.detail.rank_set?.closed_AD && this.detail.conf?.active?.qrcode_logo
            })
        },


        copy(data, hide_toast = false) {
            uni.setClipboardData({
                data,
                success: () => {
                    if (hide_toast) return uni.hideToast()
                    this.$uni.showToast('复制成功', 'none', 500)
                }
            })
        },

        uniPopupClose(ref) {
            this.popup_open = false
            this.$refs[ref].close()
        },

        uniPopupOpen(ref) {
            this.popup_open = true
            this.$refs[ref].open()
        }
    },

    onShareAppMessage() {
        this._utils.shareCopyActivityId(this.detail)

        let url = '/pages/light-up-map/user/details?id=' + this.id;
        if (this.detail?.conf?.active?.screen_pic) url += `&screen_pic=${this.detail.conf.active.screen_pic}`

        return {
            title: this.detail.name,
            path: url,
            imageUrl: this.detail.logo || ''
        }
    }
}
</script>

<style lang="scss">
.page {
    //padding-bottom: 100px;
}

.init-loading {
    width: 100vw;
    height: 100vh;
    background-color: #fff;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
}

.top-msg {
    position: fixed;
    top: 10px;
    display: flex;
    flex-direction: row;
    z-index: 99;

    .top-msg-icon {
        position: absolute;
        left: -15px;
        top: -3px;
        border-radius: 50%;
        padding: 3px;
        .top-msg-inner-ring {
            width: 24px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            border-radius: 50%;
            .iconfont {
                color: #fff;
            }
        }
    }

    .top-msg-num {
        background-color: rgba(0, 0, 0, .3);
        color: #fff;
        height: 24px;
        line-height: 24px;
        border-radius: 0 12px 12px 0;
        padding: 0 12px 0 20px;
        min-width: 60px;
        text-align: center;
        font-size: 14px;
    }
}
.top-msg-left {
    left: calc(3vw + 15px);

    .top-msg-icon {
        background-color: #5cadff;
        box-shadow: 0 0 5px #2d8cf0;
        .top-msg-inner-ring {
            background-color: #2d8cf0;
        }
    }
    .top-msg-num {
        box-shadow: 0 0 10px rgba(45, 140, 240, .2) inset;
    }
}

.go-button-fixed {
    position: fixed;
    left: 50vw;
    bottom: 80px;
    margin-left: -40px;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: #5cadff;
    box-shadow: 0 0 10px #5cadff;
    
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}


.icon-list {
    position: fixed;
    top: 10vh;
    right: 10px;
    display: flex;
    flex-direction: column;
    z-index: 99;

    .icon-item {
        width: 40px;
        height: 40px;
        line-height: 40px;
        border-radius: 50%;
        text-align: center;
        background-color: rgba(0, 0, 0, .5);
        margin-bottom: 5px;

        .iconfont, .uni-icons {
            font-size: 24px !important;
            color: #fff !important;
        }
    }
}

.headimg {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}

.join-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, .7);
    z-index: 9999;
}

.join-popup-c {
    border-radius: 10px;
    overflow: hidden;
}


.join-input {
    margin: 10px;
    padding: 10px;
    border: 1px solid #bbb;
    border-radius: 5px;
    width: 70vw;
    max-width: 400px;
}

.join-popup-btns {
    border-top: 1px solid #eee;
}

.join-popup-btn-cancel, .join-popup-btn-confirm {
    width: 50%;
    box-sizing: border-box;
    line-height: 44px;
}

.join-popup-btn-cancel {
    border-right: 1px solid #eee;
}

.uni-popup-info {
    position: relative;
    width: 280px;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    z-index: 999;
    left: 50%;
    bottom: -5px;
    margin-left: -12px;
}

.detail-popup {
    width: 95vw;
    padding-bottom: 15px;
}

.map {
    width: 100vw;
    height: 100vh;
}
</style>