.page {
    min-height: 100vh;
    padding-bottom: 10px;
    box-sizing: border-box;
}

.index {
    width: 40px;
    min-width: 40px;
}

.top-3 {
    width: 30px;
    height: 30px;
    display: block;
}

.item {
    margin: 10px;
    padding: 10px 10px 10px 0;
    border-radius: 10px;
}

.headimg {
    width: 60px;
    min-width: 60px;
    height: 60px;
    border-radius: 50%;
}

.middle {
    width: 100%;
    padding-left: 10px;
    overflow: hidden;
    box-sizing: border-box;
}

.name {
    line-height: 35px;
}

.right {
    width: 100px;
    min-width: 100px;
}

.right-item {
    width: 100px;
}

.like_popup {
    width: 300px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}
