<template>
    <view class="page bg-background">

        <xwy-ad v-if="show_AD" :ad_type="3"></xwy-ad>

        <view v-if="top_rank_text" class="font12 p10" style="color: #e19898;"
              @click="copyId">{{ top_rank_text }}
        </view>

        <top-banner v-if="top_rank_banner.length" :list="top_rank_banner"></top-banner>


        <template v-if="podiumList.length">
            <ranking-podium :list="podiumList"/>
        </template>


        <view v-if="list.length && total_count" class="color-sub font14 text-center pt5">
            <text v-if="!show_AD">我的排名: {{ my_position_num === -1 ? '未上榜' : my_position_num }}</text>
            <text class="pl5" v-if="total_count">
                <template v-if="!show_AD">(</template>
                共{{ total_count }}人
                <template v-if="!show_AD">)</template>
            </text>
        </view>

        <view class="list">
            <view v-for="(item, index) in list" :key="index">
                <xwy-ad v-if="show_AD && index === 0" :ad_type="66"></xwy-ad>

                <view v-if="!limit_show_num || index < limit_show_num" class="item flex-row bg-white">
                    <view class="index flex-all-center" @click.stop="copyUserid(item)">

                        <image class="top-3" v-if="index < 3" 
                               :src="'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/no-' + (index + 1) + '.png'"/>
                        <view v-else class="color-sub font14">{{ index + 1 }}</view>
                    </view>

                    <view class="flex-all-center">
                        <view @click="clickItem(item)">
                            <image v-if="item.headimg" mode="aspectFill" class="headimg" 
                                   :src="item.headimg"/>
                            <view v-else class="headimg bg-background color-sub font24 flex-all-center">
                                {{ item.nickname[0] }}
                            </view>
                        </view>
                    </view>

                    <view class="middle">
                        <view class="name color-title ellipsis" @click="clickItem(item)">
                            {{ item.nickname }}
                        </view>
                        <view v-if="item.other_info.length" class="color-sub font12 pt5">
                            <view v-for="info in item.other_info" :key="info">{{ info }}</view>
                        </view>
                    </view>

                    <view v-if="item.value !== 'hide' || show_like" @click="clickValue(item)"
                          class="right flex-column flex-all-center color-red font14">
                        <view class="right-item text-right">
                            <view v-if="item.value !== 'hide'">
                                <text>{{ item.value }}</text>
                                <text class="font12" style="padding-left: 2px;">{{ unit }}</text>
                            </view>
                            <view v-if="show_like" class="pt5">
                                <text class="iconfont icon-love color-red" 
                                      style="position: relative; top: 1px; left: -1px;" 
                                      @click.stop="like(index)">
                                </text>
                                <text v-if="item.likes" class="color-red" @click.stop="like(index)">
                                    {{ item.like }}
                                </text>
                            </view>
                        </view>

                    </view>
                </view>

                <xwy-ad v-if="show_AD && index !== 0 && ((index + 1) % 10 === 0)" :ad_type="66"></xwy-ad>

            </view>
        </view>

        <view v-if="!list.length && !loading" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无排行数据</view>
        </view>

        <uni-load-more v-if="loading" status="loading"></uni-load-more>
        <view v-if="is_last_page && list.length > 5" class="color-sub font12 text-center p10">
            没有更多排名了
        </view>
        <uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>

        <uni-popup ref="like_popup" type="center">
            <view class="like_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('like_popup')">
                    <uni-icons type="close" size="24" color="#b2b3b7"/>
                </view>
                <view class="bg-primary color-white" style="padding: 30px 20px 20px;">

                    <icon :type="like_status === 'success' ? 'success' : 'clear'" 
                          size="80" color="#ffffff"></icon>
                    <view class="font18">点赞{{ like_status === 'success' ? '成功' : '失败' }}</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ like_info }}
                    </view>
                </view>
                <xwy-ad v-if="popup_show && show_AD" :ad_type="3"></xwy-ad>
            </view>
            <view class="pt5">
                <xwy-ad v-if="popup_show && show_AD" :ad_type="66"></xwy-ad>
            </view>
        </uni-popup>


    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'

export default {
    data() {
        return {
            id: '',
            loading: true,
            total_count: 0,
            top_rank_text: '',
            my_position_num: -1,
            list: [],
            load_page: 1,
            is_last_page: false,
            top_rank_banner: [],
            show_AD: false,
            unit: '',
            show_like: false,
            like_status: '',
            like_info: '',
            popup_show: false,
            limit_show_num: null
        }
    },
    
    computed: {
        podiumList() {
            const {list, show_AD, unit} = this
            if (!list.length || show_AD) return []
            return this.list.slice(0, 3).map(item => ({
                headimg: item.headimg,
                nickname: item.nickname,
                value: item.value === 'hide' ? '' : `${item.value}${unit}`
            }))
        }
    },

    onLoad(e) {
        this.id = e.id

        // 云祝福活动做为任务闯关活动的一个任务，从任务闯关活动跳转到云祝福活动查看排行榜。由于这了获取到的活动是任务闯关活动，任务闯关活动默认的是积分排行榜，而云祝福活动是按点赞数排名的，所以传了task_active就要显示云祝福的点赞排行榜。
        if (e['task_active']) this.taskActive = true

        uni.showLoading({
            mask: true
        })
        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.init()
        })
    },
    onPullDownRefresh() {
        this.reloadList()
    },
    onReachBottom() {
        this.loadList()
    },
    methods: {
        async init() {
            await this.getActivityDetails()
            this.setPageData()
            await this.getRankingCategoryList()
            await this.getList()
            this.loading = false
            uni.hideLoading()
        },

        async getActivityDetails() {
            let activity_details = app.globalData['activity_detail']
            if (!activity_details) {
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        access_token: app.globalData['access_token'],
                        active_id: this.id
                    }
                })
                activity_details = res?.['data']?.['active_details']
            }
            if (!activity_details) {
                return xwy_api.alert('活动获取失败', {
                    complete: () => {
                        uni.navigateBack()
                    }
                })
            }
            this.activity_details = activity_details
        },

        setPageData() {
            const details = this.activity_details
            if (details.conf?.active?.top_rank_banner?.length) {
                this.top_rank_banner = details.conf.active.top_rank_banner
            }
            if (!details.rank_set?.closed_AD) this.show_AD = true
            if (details.rank_set?.limit_show_num) this.limit_show_num = details.rank_set.limit_show_num

            // 云祝福活动做为任务闯关活动的一个任务，从任务闯关活动跳转到云祝福活动查看排行榜。由于这了获取到的活动是任务闯关活动，任务闯关活动默认的是积分排行榜，而云祝福活动是按点赞数排名的，所以有taskActive就要显示云祝福的点赞排行榜。
            if (details.types === 25 && this.taskActive) details.types = 13

            this.types = details.types

            switch (details.types) {
                case 2:  // 健步走
                    const {map_types = 0, time_period_step = 0} = details.conf.active
                    if (map_types === 101 && time_period_step === 1) {
                        this.type_id = 28
                        this.unit = '步'
                    }
                    break
                case 4:  // 朗诵
                    this.type_id = 9
                    this.unit = '赞'
                    if (details.rank_set?.category_list) this.works_ranking = true // 朗诵按作品排名
                    break
                case 5:  // 在线打卡
                case 6:  // 运动打卡
                case 21: // 集卡
                    this.type_id = 11
                    this.unit = '赞'
                    break
                case 7:  // 投票活动
                    this.type_id = 10
                    this.unit = '票'
                    break
                case 8:  // 集字活动
                    this.type_id = 12
                    this.unit = '字'
                    break
                case 9:  // AI运动
                    this.type_id = 13
                    this.show_like = true
                    this.unit = details.conf.active.energy_unit || '能量'
                    break
                case 12:  // 跑步运动轨迹
                    this.type_id = 15
                    this.unit = 'km'
                    break
                case 13:  // 云祝福
                    this.type_id = 16
                    this.unit = '赞'
                    break
                case 15:  // 猜歌
                case 20:  // 寻宝
                case 25:  // 任务闯关
                    this.type_id = 20
                    this.unit = details.conf.active.integral?.unit || '金币'
                    break
                case 18:  // 猜图
                    this.type_id = 7  // 答题累计分数排行榜
                    this.unit = '分'
                    break
                case 22:  // 拼碎片活动  用的是累计答题成绩排行榜
                    this.type_id = 7  // 答题累计分数排行榜
                    this.unit = '次'
                    break
                case 29:  // 长征游戏
                    this.type_id = 32
                    break
                case 30:  // 减肥-体重
                    this.type_id = 36
                    this.unit = 'kg'
                    break
                case 33:  // 点亮地图
                    this.type_id = 42
                    this.unit = '个区域'
                    break
                default:
                    break
            }
        },
        
        async getRankingCategoryList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.step/top_rank_category_list',
                data: {
                    active_id: this.id
                }
            })
            const list = res?.data?.top_rank_category_list || []
            const category = list.find(item => item.id === this.type_id)
            if (category && category.hasOwnProperty('text')) {
                this.top_rank_text = category.text
                this.haveJsonConfigToprankText = true
            }
        },


        async loadList() {
            if (!this.loading && !this.is_last_page) {
                this.loading = true
                await this.getList()
                this.loading = false
            }
        },

        async reloadList() {
            if (this.loading) return uni.stopPullDownRefresh()
            this.load_page = 1
            this.loading = true
            await this.getList()
            this.loading = false
            uni.stopPullDownRefresh()
        },

        async getList() {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
                this.my_position_num = -1
                this.total_count = 0
            }

            if (this.works_ranking) return await this.getWorksList()

            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id,
                top_rank_types: this.type_id,
                page: this.load_page,
                perpage: 20
            }

            
            
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.step/top_rank_list',
                data
            })


            if (!res?.data) {
                this.is_last_page = true
                return false
            }

            // JSON配置了排行榜描述文字，不需要读取排行榜接口返回的描述文字
            if (!this.haveJsonConfigToprankText && res.data?.rank_types_text?.rules) {
                this.top_rank_text = res.data.rank_types_text.rules
            }

            if (res.data.top_rank_list) {
                if (res.data.top_rank_list.position_num) this.my_position_num = res.data.top_rank_list.position_num

                const data = res.data.top_rank_list
                const list = data.list.data || []
                const new_list = this.listDataInit(list)

                this.list = [...this.list, ...new_list]
                this.is_last_page = data.list.is_lastpage
                this.total_count = data.list.total
                this.load_page++
            } else {
                this.is_last_page = true
            }
        },

        listDataInit(list) {
            const new_list = []
            list.forEach(v => {
                const must_submit = v.must_submit || v.user_details?.must_submit
                const item = {
                    id: v.id,
                    userid: v.userid || null,
                    headimg: v.headimg || v.user_details?.headimg || '',
                    nickname: must_submit?.[0]?.value || ' ',
                    value: this.getItemValue(v),
                    other_info: this.getItemOtherInfo(v)
                }

                if (this.show_like) item.like = this.getItemLike(v)


                new_list.push(item)
            })
            return new_list
        },

        // 获取排行榜右边显示的值
        getItemValue(v) {
            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace v.light_map_num */
            
            const values = {
                2: () => v['real_step'], // ============================== 健步走
                4: () => v.water, // ===================================== 朗诵
                5: () => {        // ===================================== 在线打卡
                    let value = v.water
                    if (this.activity_details?.conf?.active?.closed_likes) value = 'hide'
                    return value
                },
                6: () => v.water, // ===================================== 运动打卡
                7: () => v.water, // ===================================== 投票活动
                8: () => v.water, // ===================================== 集字活动
                9: () => v.water, // ===================================== AI运动
                13: () => v.water, // ==================================== 云祝福
                12: () => Math.floor(v['meter'] / 10) / 100, // ========== 跑步运动轨迹
                15: () => v['integral_all'],  // ========================= 猜歌
                18: () => v.score, // ==================================== 猜图  用的是答题累计分数排行榜
                20: () => v['integral_all'], // ========================== 寻宝
                21: () => v.water, // ==================================== 集卡
                22: () => v.score, // ==================================== 拼碎片活动
                25: () => v['integral_all'], // ========================== 任务闯关
                29: () => 'hide', // ===================================== 长征游戏
                30: () => Math.floor(v['lose_weight'] / 100) / 10, // ==== 减肥-减重
                33: () => v.light_map_num, // ============================ 点亮地图
            }
            return values[this.types]?.() || 0
        },

        // 获取其他信息，如打卡活动的累计打卡天数、次数等
        getItemOtherInfo(v) {
            const infos = {
                5: () => {  // 在线打卡
                    return [
                        `累计打卡${v['sign_day'] || 0}天`,
                        `累计打卡${v.sign_count || 0}次`,
                        `连续打卡${v['continue_sign_day'] || 0}天`,
                    ]
                },
                6: () => [`累计打卡${v['sign_day'] || 0}天`],  // 运动打卡
                8: () => {  // 集字活动
                    const list = []
                    if (v.finished_time) list.push(this._utils.timestampToTime(v.finished_time))
                    return list
                },
                12: () => [`总用时: ${this.resolutionTime(v.seconds)}`],  // 跑步运动轨迹
                22: () => [`总用时: ${this.resolutionTime(v.score_seconds)}`],  // 拼碎片活动
                21: () => {  // 集卡
                    return [
                        `累计集卡${v['sign_day'] || 0}天`,
                        `累计集卡${v.sign_count || 0}次`,
                        `连续集卡${v['continue_sign_day'] || 0}天`,
                    ]
                },
                29: () => [`通关时间 ${this._utils.timestampToTime(v.finished_time)}`], //长征游戏
            }
            return infos[this.types]?.() || []
        },

        resolutionTime(seconds = 0) {
            let hh = 0, mm = 0, ss = 0
            const one_hour = 60 * 60
            if (seconds >= one_hour) {
                hh = Math.floor(seconds / one_hour)
                seconds %= one_hour
            }
            const one_minute = 60
            if (seconds >= one_minute) {
                mm = Math.floor(seconds / one_minute)
                seconds %= one_minute
            }
            ss = seconds
            const _h = hh > 0 ? hh + ':' : ''
            let _m = ''
            if (_h || mm > 0) {
                if (mm < 10) mm = '0' + mm
                _m = mm + "'"
            }
            const _s = (ss >= 10 ? ss : '0' + ss) + '"'
            return _h + _m + _s
        },

        getItemLike(v) {
            switch (this.type_id) {
                case 13:
                    return v.likes || 0
                default:
                    return 0
            }
        },

        async like(index) {
            const item = this.list[index]
            this.$uni.showLoading('点赞中...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.likeit/like_for_friend',
                data: {
                    active_id: this.id,
                    userid: item.userid
                }
            })
            uni.hideLoading()

            let like_status = 'success',
                like_info = '点赞成功！'

            if (res?.['status']) {
                item.likes = item.likes || 0
                item.likes++
            } else {
                like_status = 'error'
                like_info = '点赞失败！'
            }
            if (res?.['info']) like_info = res['info']
            this.like_status = like_status
            this.like_info = like_info

            this.uniPopupOpen('like_popup')
        },

        uniPopupOpen(ref) {
            this.$refs[ref].open()
            this.popup_show = true
        },

        uniPopupClose(ref) {
            this.$refs[ref].close()
            this.popup_show = false
        },

        copyUserid(item) {
            const id = this.type_id === 2 ? item.id : item.userid
            uni.setClipboardData({
                data: id.toString(),
                success: () => uni.hideToast()
            })
        },

        copyId() {
            uni.setClipboardData({
                data: this.id,
                success: () => uni.hideToast()
            })
        },

        clickItem(item) {
            const {nickname = '', userid} = item
            const clickItemFunction = {
                id_9: () => {  // 朗诵
                    this.$uni.navigateTo(`/pages/voice/user/voice_list?nickname=${nickname}&userid=${userid}&id=${this.id}`)
                },
                id_11: () => {  // 在线打卡
                    this.$uni.navigateTo(`/pages/clock_in/user/public_sign_list?nickname=${nickname}&userid=${userid}&id=${this.id}`)
                }
            }

            clickItemFunction['id_' + this.type_id]?.()
        },

        clickValue(item) {
            // 朗诵活动需要点击点赞数查看用户作品列表
            if (this.type_id !== 9) return
            this.clickItem(item)
        },

        async getWorksList() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.audio_online.user_submit/audio_list',
                data: {
                    get_data_types: 1,
                    active_id: this.id,
                    page: this.load_page,
                    perpage: 20
                }
            })

            if (!res?.['data']?.audio_list?.data?.length) {
                this.is_last_page = true
                return
            }

            this.load_page++
            const list = res['data'].audio_list.data
            const new_list = this.worksListDataInit(list)
            this.list = [...this.list, ...new_list]
            this.is_last_page = res['data'].audio_list.is_lastpage
        },

        worksListDataInit(list) {
            const new_list = []
            list.forEach(v => {
                const item = {
                    id: v.id,
                    userid: v.userid || null,
                    headimg: v['user_attend_details']?.headimg || ' ',
                    nickname: v['user_attend_details']?.must_submit?.[0]?.value || ' ',
                    value: v.vote_num || 0,
                    other_info: []
                }
                if (v.conf_json?.audio_list?.length) {
                    const audio_item = v.conf_json?.audio_list[0]
                    const works_name = audio_item.music?.name || audio_item.article?.title
                    if (works_name) item.other_info.push(`《${works_name}》`)
                }
                new_list.push(item)
            })
            return new_list
        }

    }
}
</script>

<style scoped lang="scss">
@import "./ranking-list.scss";
</style>
