<template>
    <view>
        <uni-popup ref="popup" @touchmove.stop.prevent="">
            <view class="popup bg-white text-center">
                <view class="color-content">恭喜你已获得</view>
                <view class="font20 color-title p10">{{ name || '勋章' }}</view>

                <image class="image" :src="image" mode="widthFix"/>

                <view class="flex-all-center p10">
                    <view class="confirm-button bg-light-primary color-white" @click="$refs.popup.close()">
                        确定
                    </view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "exam-prize-light-up-popup",
    props: ['image', 'name'],

    methods: {
        open() {
            this.$refs.popup.open()
        }
    }
}
</script>

<style lang="scss" scoped>
.popup {
    width: 90vw;
    padding: 10px;
    border-radius: 10px;

    .image {
        width: 90vw;
        display: block;
    }

    .confirm-button {
        width: 200px;
        line-height: 40px;
        border-radius: 20px;
    }
}
</style>