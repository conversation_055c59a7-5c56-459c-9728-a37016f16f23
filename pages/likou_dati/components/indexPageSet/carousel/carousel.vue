<template>
	<view class="swiper-box"
		:style="{'height':`calc(100vw * ${carousel.height})`,'max-height':`calc(640px * ${carousel.height})`}">
		<swiper :indicator-dots="carousel.indicator_dots" :circular="carousel.circular"
			:autoplay="carousel.autoplay" :interval="carousel.interval" :indicator-color="carousel.indicator_color"
			:indicator-active-color="carousel.indicator_active_color" :duration="carousel.duration">
			<swiper-item class="swiper-list" v-for="(itm,idx) in carousel.list" :key="idx">
				<image :src="itm.src" :mode="carousel.image_mode"/>
			</swiper-item>
		</swiper>
	</view>
</template>

<script>
	let app = getApp();
	export default {
		data() {
			return {
			}
		},
		props: {
			carousel: {
				type: Object,
				default: {},
			},
		},
		mounted() {
			// console.log('carousel',this.carousel);
		},
		methods: {

		},
	}
</script>
<style scoped lang='scss'>
	@import url('@/pages/likou_dati/common/css/common.scss');
	@import url('@/pages/likou_dati/common/css/iconfont.css');
	@import url('@/pages/likou_dati/common/css/public.css');
</style>
