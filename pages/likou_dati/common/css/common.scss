image {
    width: 100%;
    height: 100%;
}

.swiper-box {
    width: 100%;
}
.swiper-box swiper {
    width: 100%;
    height: 100%;
}

.tabbar-box {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 49;
    width: 100%;
    max-width: 640px;
    height: 60px;
    background-color: #fff;
    border-top: 1px solid #eee;
}
.tabbar-box .left-box {
    width: 50%;
}
.tabbar-box .left-box .item {
    width: 50%;
}
.tabbar-box .left-box .item .iconfont {
    font-size: 1.5rem;
    padding: 0.3rem;
}
.tabbar-box .left-box .item .text {
    font-size: 0.9rem;
}
.tabbar-box .right-btn {
    width: 50%;
}
.tabbar-box .right-btn .btn {
    height: 100%;
    font-size: 1.2rem;
}
.tabbar-box > .btn-box {
    width: 100%;
    height: 100%;
    padding: 0.7rem 1rem;
    font-size: 0.9rem;
}
.tabbar-box > .btn-box > .btn {
    width: 40%;
    height: 100%;
    border-radius: 0.3rem;
}

.fixed_modal {
    position: fixed;
    left: 50%;
    top: 0;
    transform: translateX(-50%);
    width: 100%;
    max-width: 640px;
    height: 100%;
    z-index: 100;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
}
.fixed_modal .fixed_modal_content {
    width: 85%;
    max-height: 85%;
    overflow: auto;
    background-color: #fff;
    border-radius: 10rpx;
    padding: 1rem;
}
.fixed_modal .fixed_modal_content .fixed-item {
    width: 100%;
}
.fixed_modal .fixed_modal_content .fixed-item .item_list {
    width: 100%;
    padding: 1rem 0;
}
.fixed_modal .fixed_modal_content .fixed-item .item_list .left {
    width: 3rem;
    padding-right: 0.5rem;
}
.fixed_modal .fixed_modal_content .fixed-item .item_list .left .iconfont {
    font-size: 2.2rem;
}
.fixed_modal .fixed_modal_content .fixed-item .item_list .right {
    width: calc(100% - 3rem);
    text-align: start;
    font-size: 1rem;
}
.fixed_modal .fixed_modal_content .fixed-item .modif-item {
    width: 100%;
    padding-bottom: 1rem;
}
.fixed_modal .fixed_modal_content .fixed-item .modif-item .left {
    width: 5rem;
}
.fixed_modal .fixed_modal_content .fixed-item .modif-item .right {
    width: calc(100% - 5rem);
}
.fixed_modal .fixed_modal_content .fixed-item .modif-item .right .iconfont {
    font-size: 3rem;
}
.fixed_modal .fixed_modal_content .fixed-item .modif-item .right image {
    width: 4rem;
    height: 4rem;
    border-radius: 0.2rem;
}
.fixed_modal .fixed_modal_content .fixed-item .btn-box {
    width: 100%;
}
.fixed_modal .fixed_modal_content .fixed-item .btn-box .btn {
    width: 40%;
    border-radius: 0.3rem;
    text-align: center;
    padding: 0.5rem 0;
}
.fixed_modal > .iconfont {
    padding-top: 0.5rem;
    font-size: 1.7rem;
    color: #fff;
}

.upload-img-box {
    position: relative;
    width: calc((100% - 15px) / 2);
    height: calc((100vw - 8rem - 15px) / 3);
    max-height: calc((640px - 6rem - 15px) / 3);
}
.upload-img-box image {
    border-radius: 3px;
}
.upload-img-box .iconfont {
    width: 1.5rem;
    height: 1.5rem;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    position: absolute;
    right: 0.3rem;
    top: 0.3rem;
    font-size: 1rem;
    color: #fff;
    font-weight: 700;
}

.modif-box {
    width: 100%;
    padding: 1rem;
}
.modif-box .modif-item {
    width: 100%;
    display: flex;
    padding-bottom: 1rem;
}
.modif-box .modif-item > .left {
    width: 6rem;
    height: 36px;
    font-size: 0.9rem;
    padding-right: 0.5rem;
}
.modif-box .modif-item > .right {
    width: calc(100% - 6rem);
}
.modif-box .modif-item > .right .switch {
    width: 100%;
    padding-bottom: 0.5rem;
}
.modif-box .modif-item > .right .updata-icon-box {
    width: calc((100% - 30px) / 3);
    max-width: calc((100% - 30px) / 3);
    height: calc((100vw - 8rem - 30px) / 3);
    max-height: calc((640px - 8rem - 30px) / 3);
    border-radius: 0.2rem;
    border: 1px solid #ddd;
}
.modif-box .modif-item > .right .updata-icon-box .iconfont {
    font-size: 50px;
    color: #ddd;
}
.modif-box .modif-item .check-box {
    padding-top: 5px;
}

.select-box {
    width: 2.5rem;
}
.select-box .select-item {
    width: 1.5rem;
    height: 1.5rem;
    border: 1px solid #ddd;
}

.float-box {
    position: fixed;
    bottom: 70px;
    right: 0.5rem;
}
.float-box .float-item {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    margin-bottom: 1rem;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    background-color: rgba(0, 0, 0, 0.6);
    color: #eee;
}
.float-box .float-item .iconfont {
    font-size: 1.5rem;
    padding-bottom: 0.1rem;
}
.float-box .float-item > text {
    font-size: 0.8rem;
}
.float-box .self-item {
    width: 2.5rem;
    height: 2.5rem;
}
.float-box .self-item .iconfont {
    font-size: 1.3rem;
    padding-bottom: 0;
}

.loading-box {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 19;
    background-color: rgba(0, 0, 0, 0.6);
    color: #f0f0f0;
    padding: 0.7rem 1rem;
    border-radius: 0.3rem;
    font-size: 0.9rem;
}

.top-select {
    position: sticky;
    left: 0;
    top: 0;
    background-color: #fff;
    width: 100%;
    z-index: 29;
    padding: 0 1rem;
    border-bottom: 1px solid #eee;
}
.top-select .top-select-item {
    width: 100%;
    padding-bottom: 1rem;
}
.top-select .top-select-item .title {
    width: 5rem;
}
.top-select .top-select-item .select {
    width: calc(100% - 5rem);
}
.top-select > .left,
.top-select .right {
    width: 50%;
}
.top-select .right .iconfont {
    padding-right: 0.3rem;
}

.exam-box {
    width: 100%;
    padding: 1rem;
}
.exam-box .exam-item {
    width: 100%;
    background-color: #fff;
    border-radius: 0.2rem;
    padding: 0 0.5rem;
    margin-bottom: 1rem;
}
.exam-box .exam-item .exam-top {
    width: 100%;
    padding: 1rem 0;
}
.exam-box .exam-item .exam-top .img {
    width: 4rem;
}
.exam-box .exam-item .exam-top .img .iconfont {
    font-size: 4rem;
}
.exam-box .exam-item .exam-top .img image {
    height: 4rem;
    border-radius: 0.2rem;
}
.exam-box .exam-item .exam-top .text-box {
    min-width: calc(100% - 4rem);
    max-width: 100%;
    padding-left: 0.5rem;
}
.exam-box .exam-item .exam-bottom {
    width: 100%;
    padding: 0.5rem 0;
}
.exam-box .exam-item .exam-bottom .btn {
    border-radius: 0.3rem;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.card-box {
    position: fixed;
    left: 50%;
    top: 0;
    transform: translateX(-50%);
    width: 100%;
    max-width: 640px;
    height: 100%;
    z-index: 100;
    background-color: rgba(0, 0, 0, 0.7);
}
.card-box .card-content {
    position: relative;
    background-color: #fff;
    width: 100%;
    height: 85vh;
    overflow-y: auto;
    border-top-left-radius: 1.5rem;
    border-top-right-radius: 1.5rem;
}
.card-box .card-content .top {
    width: 100%;
    height: calc(100% - 60px);
    overflow-y: auto;
    padding: 1rem;
}
.card-box .card-content .top .card-title {
    width: 100%;
    padding-bottom: 1rem;
}
.card-box .card-content .bottom {
    width: 100%;
    height: 60px;
    border-top: 1px solid #eee;
    padding: 0 1rem;
}
.card-box .card-content .bottom > view {
    width: 100%;
    border-radius: 10rem;
    padding: 0.5rem 0;
    text-align: center;
}

.details-box {
    position: relative;
    width: 90vw;
    max-width: 512px;
    min-height: 60vh;
    background-color: #fff;
    padding: 0.5rem 0.5rem 4rem 0.5rem;
    border-radius: 0.3rem;
    overflow-y: auto;
}
.details-box .user-details {
    padding: 1rem 0;
}
.details-box .user-details .headimg {
    width: 6rem;
    padding-bottom: 0.5rem;
}
.details-box .user-details .headimg image {
    height: 6rem;
    border-radius: 50%;
}
.details-box .item-box {
    width: 100%;
    flex-wrap: wrap;
    padding: 0.5rem 0;
}
.details-box .item-box .details-item {
    padding: 0 0.5rem;
    margin-bottom: 0.5rem;
}
.details-box .item-box .details-item .top {
    padding-bottom: 0.3rem;
}
.details-box .item-box .details-item .left {
    min-width: 5rem;
}
.details-box .item-box .details-item radio {
    transform: scale(0.8);
}
.details-box .item-box .details-item .label {
    padding-right: 0.5rem;
    padding-bottom: 0.5rem;
}
.details-box .item-box .all-width {
    width: 100%;
}
.details-box .item-box .half-width {
    width: 50%;
}
.details-box .item-box .person-select {
    border: 1px solid #ddd;
    border-radius: 0.2rem;
    padding: 0.5rem;
}
.details-box .btn-box {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    padding: 0 0.5rem;
    border-top: 1px solid #eee;
    background-color: #fff;
}
.details-box .btn-box .btn {
    width: 50%;
    padding: 1rem;
    text-align: center;
}
.details-box .btn-box .btn:first-child {
    border-right: 1px solid #eee;
}

.edit-item {
    width: 100%;
    border-bottom: 1px dashed #eee;
    padding: 0.5rem 1rem 1rem;
}
.edit-item .title {
    position: relative;
    width: 100%;
    padding-left: 1rem;
    padding: 0.5rem;
}
.edit-item .title::after {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 1rem;
    border-radius: 0.3rem;
    background-color: #007AFF;
}
.edit-item .value {
    width: 100%;
    margin-bottom: 1rem;
    /* 树和水滴 */
}
.edit-item .value .bg_box {
    position: relative;
    overflow: hidden;
}
.edit-item .value .bg_box .headimg-float {
    position: absolute;
    z-index: 1;
}
.edit-item .value .bg_box .headimg-float .progress_bg {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 17px;
    z-index: 2;
    border-radius: 10rem;
    background-image: linear-gradient(#9587ce, #7182d8);
}
.edit-item .value:last-child {
    margin-bottom: 0;
}
.edit-item .value .add-icon {
    font-size: 4rem;
    color: #ccc;
}
.edit-item .value .logo {
    width: 6rem;
    height: 6rem;
}
.edit-item .value .logo image {
    width: 100%;
    height: 100%;
}
.edit-item .value .left {
    width: 5rem;
    min-width: 4rem;
}
.edit-item .value .left .iconfont {
    font-size: 0.6rem;
    padding-left: 0.2rem;
}
.edit-item .value .right {
    width: calc(100% - 4rem);
    padding-left: 0.5rem;
}
.edit-item .value .right > .add-img {
    width: 2rem;
}
.edit-item .value .tree {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: row;
    height: 160upx;
    justify-content: center;
    z-index: 4;
}
.edit-item .value .tree .rain-box {
    position: relative;
    top: -100upx;
    height: 100upx;
}
.edit-item .value .tree .rain {
    position: relative;
    margin: 10upx;
    width: 60upx;
    height: 60upx;
    line-height: 60upx;
    text-align: center;
    font-size: 16upx;
    border-radius: 50%;
    animation: rain 4s infinite;
    z-index: 4;
}
.edit-item .value .tree .rain text {
    font-size: 12upx;
}
.edit-item .value .tree .rain:nth-child(1) {
    animation-delay: 0.8s;
    margin-top: 10px;
}
.edit-item .value .tree .rain:nth-child(2) {
    animation-delay: 0.5s;
    margin-top: -10px;
}
.edit-item .value .tree .rain:nth-child(4) {
    animation-delay: 0.1s;
    margin-top: -5px;
}
.edit-item .value .tree .rain-1 {
    top: -100upx;
}
.edit-item .value .tree .rain-2 {
    top: -200upx;
}
.edit-item .value .tree .rain-3 {
    top: -300upx;
}
.edit-item .value .tree .tree-img {
    position: absolute;
    bottom: 0;
    width: 280upx;
    /* height: 380upx; */
    z-index: 0;
}
.edit-item .value .tree .tree-1 {
    width: 124upx;
    /* height: 264upx; */
}
.edit-item .value .tree .tree-3 {
    width: 420upx;
    /* height: 460upx; */
}
.edit-item .value .must_submit_box {
    width: 100%;
}
.edit-item .value .must_submit_box .input-box {
    width: calc(100% - 10rem);
    max-width: 15rem;
    /* flex: initial; */
}
.edit-item .value .must_submit_box .input-box .input {
    width: 100%;
}
.edit-item .value .must_submit_box .selector-box {
    width: 5rem;
    padding-left: 0.5rem;
}
.edit-item .value .must_submit_box .selector-box .selector-item {
    padding: 0.45rem 0.5rem;
    border-radius: 0.2rem;
    border: 1px solid #ddd;
    font-size: 0.9rem;
}
.edit-item .value .must_submit_box .must_submit_item {
    padding: 1rem 0;
}
.edit-item .value .must_submit_box .must_submit_item .must-box {
    width: 100%;
}
.edit-item .value .must_submit_box .must_submit_item .must-box .must-left {
    width: calc(100% - 2.5rem);
}
.edit-item .value .must_submit_box .must_submit_item .must-box > text {
    font-size: 0.9rem;
}
.edit-item .value .must_submit_box .must_submit_item .must-radio-box {
    width: 100%;
}
.edit-item .value .must_submit_box .must_submit_item .must-radio-box .must-radio-item {
    width: 100%;
    padding-bottom: 0.5rem;
}
.edit-item .value .must_submit_box .must_submit_item .must-radio-box .must-radio-item .left {
    width: calc(100% - 2.5rem);
}
.edit-item .pic_list {
    width: calc((100% - 2rem) / 3);
}
.edit-item .pic_list image {
    width: 100%;
}
.edit-item .along_img {
    min-width: 7.5rem;
    width: 7.5rem;
    height: 5rem;
}
.edit-item .along_img image {
    width: 100%;
    height: 100%;
}

.modal-box {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1011;
    background-color: rgba(0, 0, 0, 0.6);
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
.modal-box .modal-container {
    position: relative;
    background-color: #fff;
    width: 90%;
    max-width: 576px;
    max-height: 90vh;
    border-radius: 1rem;
    padding-bottom: 3rem;
}
.modal-box .modal-container .item-box {
    width: 100%;
    max-height: calc(90vh - 3rem);
    overflow-y: scroll;
}
.modal-box .modal-container .bottom-box {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3rem;
}

.music-box {
    position: fixed;
    top: 3rem;
    right: 1rem;
    z-index: 99;
}
.music-box .btn-item {
    width: 3rem;
    margin-bottom: 1rem;
}
.music-box .btn-item .icon {
    width: 100%;
    height: 3rem;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.4);
}
.music-box .btn-item .icon .iconfont {
    font-size: 1.5rem;
    color: #fafafa;
}
.music-box .btn-item .text {
    padding-top: 0.2rem;
    font-size: 0.8rem;
    color: #ccc;
}
.music-box .turn_around {
    animation: around 7s linear infinite;
}
@keyframes around {
    0% {
        transform: rotateZ(0deg);
    }
    100% {
        transform: rotateZ(360deg);
    }
}

.upload-icon-box {
    width: 7rem;
    height: 7rem;
    border-radius: 0.2rem;
    border: 1px solid #ddd;
}
.upload-icon-box .iconfont {
    font-size: 50px;
    color: #ddd;
}

.upload-icon {
    width: 60px;
    height: 60px;
    border: 1px dashed #ccc;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.upload-icon .iconfont {
    font-size: 30px;
    color: #bbb;
}
