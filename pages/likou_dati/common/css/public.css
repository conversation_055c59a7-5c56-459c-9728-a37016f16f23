@import url("@/pages/likou_dati/common/css/child/flex.css");
@import url("@/pages/likou_dati/common/css/child/color.css");
@import url("@/pages/likou_dati/common/css/child/default.css");
@import url("@/pages/likou_dati/common/css/child/margin.css");
@import url("@/pages/likou_dati/common/css/child/padding.css");
@import url("@/pages/likou_dati/common/css/child/width_height.css");
@import url("@/pages/likou_dati/common/css/child/font.css");


.ribbon-box {
	position: absolute;
	width: 9rem;
	padding: .3rem 0;
	text-align: center;
	right: -3rem;
	top: 0.5rem;
	transform: rotateZ(45deg);
	font-size: .9rem;
}

.golden_red_bg {
	background-image: linear-gradient(45deg, #f6d365 0%, #fda085 100%);
}

.relative {
	position: relative;
}

.hidden {
	overflow: hidden;
}

.bottom-box-fixed {
	position: fixed;
	bottom: 15px;
	left: 50%;
	transform: translateX(-50%);
	max-width: 384px;
}
