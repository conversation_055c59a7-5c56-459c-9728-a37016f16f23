/**
 * padding
 */
.p0 {
	padding: 0 !important;
}

.p_1 {
	padding: .1rem !important;
}

.p_2 {
	padding: .2rem !important;
}

.p_3 {
	padding: .3rem !important;
}

.p_5 {
	padding: .5rem !important;
}

.p0_2 {
	padding: 0 .2rem !important;
}

.p0_3 {
	padding: 0 .3rem !important;
}

.p0_4 {
	padding: 0 .4rem !important;
}

.p0_5 {
	padding: 0 .5rem !important;
}

.p0_7 {
	padding: 0 .7rem !important;
}

.p0_9 {
	padding: 0 .9rem !important;
}

.p2_0 {
	padding: .2rem 0 !important;
}

.p3_0 {
	padding: .3rem 0 !important;
}

.p4_0 {
	padding: .4rem 0 !important;
}

.p5_0 {
	padding: .5rem 0 !important;
}

.p7_0 {
	padding: .7rem 0 !important;
}

.p9_0 {
	padding: .9rem 0 !important;
}

.p5-1 {
	padding: .5rem 1rem !important;
}

.p_7 {
	padding: .7rem !important;
}

.p0_7 {
	padding: 0 .7rem !important;
}

.p7_0 {
	padding: .7rem 0 !important;
}

.p_8 {
	padding: .8rem !important;
}

.p0_8 {
	padding: 0 .8rem !important;
}

.p8_0 {
	padding: .8rem 0 !important;
}

.p_9 {
	padding: .9rem !important;
}

.p0_9 {
	padding: 0 .9rem !important;
}

.p9_0 {
	padding: .9rem 0 !important;
}

.p1 {
	padding: 1rem !important;
}

.p2 {
	padding: 2rem !important;
}

.p0-1 {
	padding: 0 1rem !important;
}

.p1-0 {
	padding: 1rem 0 !important;
}

.p1_5 {
	padding: 1rem .5rem !important;
}

.p2-0 {
	padding: 2rem 0 !important;
}

.p0-2 {
	padding: 0 2rem !important;
}


.pb0 {
	padding-bottom: 0 !important;
}

.pb_2 {
	padding-bottom: .2rem !important;
}

.pb_3 {
	padding-bottom: .3rem !important;
}

.pb_5 {
	padding-bottom: .5rem !important;
}

.pb_7 {
	padding-bottom: .7rem !important;
}

.pb_9 {
	padding-bottom: .9rem !important;
}

.pb1 {
	padding-bottom: 1rem !important;
}

.pb1_5 {
	padding-bottom: 1.5rem !important;
}

.pb2 {
	padding-bottom: 2rem !important;
}

.pb2_5 {
	padding-bottom: 2.5rem !important;
}

.pb3 {
	padding-bottom: 3rem !important;
}

.pb4 {
	padding-bottom: 4rem !important;
}

.pb5 {
	padding-bottom: 5rem !important;
}

.pb6 {
	padding-bottom: 6rem !important;
}


.pt0 {
	padding-top: 0 !important;
}

.pt_2 {
	padding-top: .2rem !important;
}

.pt_3 {
	padding-top: .3rem !important;
}

.pt_5 {
	padding-top: .5rem !important;
}

.pt_7 {
	padding-top: .7rem !important;
}

.pt_9 {
	padding-top: .9rem !important;
}

.pt1 {
	padding-top: 1rem !important;
}

.pt2 {
	padding-top: 2rem !important;
}

.pt3 {
	padding-top: 3rem !important;
}

.pt4 {
	padding-top: 4rem !important;
}

.pt5 {
	padding-top: 5rem !important;
}

.pt8 {
	padding-top: 5rem !important;
}


.pr0 {
	padding-right: 0 !important;
}

.pr_2 {
	padding-right: .2rem !important;
}

.pr_3 {
	padding-right: .3rem !important;
}

.pr_5 {
	padding-right: .5rem !important;
}

.pr_7 {
	padding-right: .7rem !important;
}

.pr_8 {
	padding-right: .8rem !important;
}

.pr_9 {
	padding-right: .9rem !important;
}

.pr1 {
	padding-right: 1rem !important;
}

.pr2 {
	padding-right: 2rem !important;
}

.pr3 {
	padding-right: 3rem !important;
}

.pr4 {
	padding-right: 4rem !important;
}

.pr5 {
	padding-right: 5rem !important;
}


.pl0 {
	padding-left: 0 !important;
}

.pl_1 {
	padding-left: .1rem !important;
}

.pl_2 {
	padding-left: .2rem !important;
}

.pl_3 {
	padding-left: .3rem !important;
}

.pl_5 {
	padding-left: .5rem !important;
}

.pl_7 {
	padding-left: .7rem !important;
}

.pl_9 {
	padding-left: .9rem !important;
}

.pl1 {
	padding-left: 1rem !important;
}

.pl1_3 {
	padding-left: 1.3rem !important;
}

.pl1_5 {
	padding-left: 1.5rem !important;
}

.pl2 {
	padding-left: 2rem !important;
}

.pl3 {
	padding-left: 3rem !important;
}