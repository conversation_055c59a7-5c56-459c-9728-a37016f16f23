/**
 * 颜色
 */

.fontweight {
	font-weight: 700;
}

.bgfff {
	background-color: #fff !important;
}

.bgf7f7f7 {
	background-color: #F7F7F7 !important;
}

.coloreee {
	color: #eee !important;
}

.colorfff {
	color: #fff !important;
}

.color111 {
	color: #111 !important;
}

.color333 {
	color: #333 !important;
}

.color555 {
	color: #555 !important;
}

.color777 {
	color: #777 !important;
}

.color999 {
	color: #999 !important;
}

.colorf7f7f7 {
	color: #F7F7F7 !important;
}

.dark_bluecolor {
	color: #576B95 !important;
}

/*正文 Content*/
.color-content {
	color: #495060;
}

.bg-content {
	background-color: #495060;
}

.bg_wht_transparent_1 {
	background-color: rgba(255, 255, 255, .1) !important;
}

.bg_wht_transparent_3 {
	background-color: rgba(255, 255, 255, .3) !important;
}

.bg_wht_transparent_5 {
	background-color: rgba(255, 255, 255, .5) !important;
}

.bg_wht_transparent_7 {
	background-color: rgba(255, 255, 255, .7) !important;
}

.gray_bgcolor {
	background-color: #eee !important;
	color: #777 !important;
}

.org_bgcolor {
	background-color: #FAAA4F !important;
	color: #fff !important;
}

.gre_bgcolor {
	background-color: #00B26A !important;
	color: #fff !important;
}

.red_bgcolor {
	color: #fff !important;
	background-color: #FF454D !important;
}

.org_color {
	color: #FAAA4F !important;
}

.gre_color {
	color: #00B26A !important;
}

.red_color {
	color: #FF454D !important;
}

.blue_color {
	color: #007aff !important;
}

.blue_bgcolor {
	background-color: #007aff !important;
	color: #fff !important;
}

.gold_color {
	color: rgb(255, 215, 000);
}

.gold_bgcolor {
	background-color: rgb(255, 215, 000);
	color: #fff;
}

.silver_color {
	color: rgb(192, 192, 192);
}

.copper_color {
	color: rgb(184, 115, 51);
}

.gray_color {
	color: #777;
}

.org_bdrsolid {
	border: 1rpx solid #FAAA4F !important;
}

.gre_bdrsolid {
	border: 1rpx solid #00B26A !important;
}

.red_bdrsolid {
	border: 1rpx solid #FF454D !important;
}

.blue_bdrsolid {
	border: 1rpx solid #007aff !important;
}

.org_bdrdashed {
	border: 1rpx dashed #FAAA4F !important;
}

.gre_bdrdashed {
	border: 1rpx dashed #00B26A !important;
}

.red_bdrdashed {
	border: 1rpx dashed #FF454D !important;
}

.blue_bdrdashed {
	border: 1rpx dashed #007aff !important;
}

.gray_bdrsolid {
	border: 1rpx solid #ccc !important;
}

.gray_bdrdashed {
	border: 1rpx dashed #ccc !important;
}

.blue_bdrbsolid {
	border-bottom: 1rpx solid #007AFF !important;
}

.org_bdrbsolid {
	border-bottom: 1rpx solid #FAAA4F !important;
}

.gre_bdrbsolid {
	border-bottom: 1rpx solid #00B26A !important;
}

.red_bdrbsolid {
	border-bottom: 1rpx solid #FF454D !important;
}

.gray_bdrbsolid {
	border-bottom: 1rpx solid #eee !important;
}

.blue_bdrbdashed {
	border-bottom: 1rpx dashed #007AFF !important;
}

.org_bdrbdashed {
	border-bottom: 1rpx dashed #FAAA4F !important;
}

.gre_bdrbdashed {
	border-bottom: 1rpx dashed #00B26A !important;
}

.red_bdrbdashed {
	border-bottom: 1rpx dashed #FF454D !important;
}

.gray_bdrbdashed {
	border-bottom: 1rpx dashed #eee !important;
}


.blue_bdrtsolid {
	border-top: 1rpx solid #007AFF !important;
}

.org_bdrtsolid {
	border-top: 1rpx solid #FAAA4F !important;
}

.gre_bdrtsolid {
	border-top: 1rpx solid #00B26A !important;
}

.red_bdrtsolid {
	border-top: 1rpx solid #FF454D !important;
}

.gray_bdrtsolid {
	border-top: 1rpx solid #eee !important;
}

.blue_bdrtdashed {
	border-top: 1rpx dashed #007AFF !important;
}

.org_bdrtdashed {
	border-top: 1rpx dashed #FAAA4F !important;
}

.gre_bdrtdashed {
	border-top: 1rpx dashed #00B26A !important;
}

.red_bdrtdashed {
	border-top: 1rpx dashed #FF454D !important;
}

.gray_bdrtdashed {
	border-top: 1rpx dashed #eee !important;
}

.gray_bdrlsolid {
	border-left: 1rpx solid #eee !important;
}

.gray_bdrldashed {
	border-left: 1rpx dashed #eee !important;
}

.gray_bdrrsolid {
	border-right: 1rpx solid #eee !important;
}

.gray_bdrrdashed {
	border-right: 1rpx dashed #eee !important;
}


.brown_color {
	color: #713C37 !important;
}

.brown_bgcolor {
	background-color: #713C37 !important;
}

.brown_bdrsolid {
	border: 1px solid #713C37 !important;
}

.brown_bdrdashed {
	border: 1px dashed #713C37 !important;
}

.deepBlue_color {
	color: #2D3C55 !important;
}

.deepBlue_bgcolor {
	background-color: #2D3C55 !important;
	color: #fff !important;
}

.deepBlue_bdrsolid {
	border: 1px solid #2D3C55 !important;
}

.deepBlue_bdrdashed {
	border: 1px dashed #2D3C55 !important;
}

.blk_shadow_005 {
	box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.05);
}

.blk_shadow_01 {
	box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.1);
}

.blk_shadow_02 {
	box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.2);
}

.blk_shadow_03 {
	box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.3);
}

.blk_shadow_04 {
	box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.4);
}

.blk_shadow_05 {
	box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.5);
}

.blk_shadow_07 {
	box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.7);
}

.wht_shadow_01 {
	box-shadow: 0 0 3px 1px rgba(255, 255, 255, 0.1);
}

.wht_shadow_02 {
	box-shadow: 0 0 3px 1px rgba(255, 255, 255, 0.2);
}

.wht_shadow_03 {
	box-shadow: 0 0 3px 1px rgba(255, 255, 255, 0.3);
}

.wht_shadow_04 {
	box-shadow: 0 0 3px 1px rgba(255, 255, 255, 0.4);
}

.wht_shadow_05 {
	box-shadow: 0 0 3px 1px rgba(255, 255, 255, 0.5);
}

.wht_shadow_07 {
	box-shadow: 0 0 3px 1px rgba(255, 255, 255, 0.7);
}

.vip_bgcolor {
	background-color: #000;
	color: #FFD700;
}