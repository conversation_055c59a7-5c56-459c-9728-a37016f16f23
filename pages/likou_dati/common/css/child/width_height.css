.width25 {
	width: 25%;
}

.width30 {
	width: 30%;
}

.width33 {
	width: calc(100% / 3);
}

.width35 {
	width: 35%;
}

.width40 {
	width: 40%;
}

.width45 {
	width: 45%;
}

.width50 {
	width: 50% !important;
}

.width55 {
	width: 55%;
}

.width60 {
	width: 60%;
}

.width70 {
	width: 70%;
}

.width80 {
	width: 80%;
}

.width85 {
	width: 85%;
}

.width90 {
	width: 90%;
}

.width100 {
	width: 100%;
}

.width10px {
	width: 10px;
}

.width20px {
	width: 20px;
}

.width25px {
	width: 25px;
}

.width30px {
	width: 30px;
}

.width35px {
	width: 35px;
}

.width40px {
	width: 40px;
}

.width50px {
	width: 50px;
}

.width55px {
	width: 55px;
}

.width60px {
	width: 60px;
}

.width70px {
	width: 70px;
}

.width80px {
	width: 80px;
}

.width90px {
	width: 90px;
}

.width100px {
	width: 100px;
}

.width120px {
	width: 120px;
}

.width150px {
	width: 150px;
}

.width200px {
	width: 200px;
}


.min-width45 {
	min-width: 45%;
}

.min-width50 {
	min-width: 50%;
}

.min-width60 {
	min-width: 60%;
}

.min-width70 {
	min-width: 70%;
}

.min-width80 {
	min-width: 80%;
}

.min-width100 {
	min-width: 100%;
}

.min-width1 {
	min-width: 1rem;
}

.min-width1_5 {
	min-width: 1.5rem;
}

.min-width2 {
	min-width: 2rem;
}

.min-width2_5 {
	min-width: 2.5rem;
}

.min-width3 {
	min-width: 3rem;
}

.min-width3_2 {
	min-width: 3.2rem;
}

.min-width3_5 {
	min-width: 3.5rem;
}

.min-width4 {
	min-width: 4rem;
}

.min-width5 {
	min-width: 5rem;
}

.min-width6 {
	min-width: 6rem;
}

.min-width7 {
	min-width: 7rem;
}

.min-width8 {
	min-width: 8rem;
}

.min-width10 {
	min-width: 10rem;
}

.min-width12 {
	min-width: 12rem;
}

.min-width14 {
	min-width: 14rem;
}

.min-width15 {
	min-width: 15rem;
}


.max-width1 {
	max-width: 1rem;
}

.max-width2 {
	max-width: 2rem;
}

.max-width2_5 {
	max-width: 2.5rem;
}

.max-width3 {
	max-width: 3rem;
}

.max-width4 {
	max-width: 3rem;
}

.max-width4 {
	max-width: 4rem;
}

.max-width5 {
	max-width: 5rem;
}

.max-width6 {
	max-width: 6rem;
}

.max-width7 {
	max-width: 7rem;
}

.max-width8 {
	max-width: 8rem;
}

.max-width9 {
	max-width: 9rem;
}

.max-width10 {
	max-width: 10rem;
}




.height40 {
	height: 40%;
}

.height50 {
	height: 50%;
}

.height60 {
	height: 60%;
}

.height70 {
	height: 70%;
}

.height100 {
	height: 100%;
}

.height10px {
	height: 10px;
}

.height20px {
	height: 20px;
}

.height25px {
	height: 25px;
}

.height30px {
	height: 30px;
}

.height35px {
	height: 35px;
}

.height40px {
	height: 40px;
}

.height50px {
	height: 50px;
}

.height60px {
	height: 60px;
}

.height70px {
	height: 70px;
}

.height80px {
	height: 80px;
}

.height90px {
	height: 90px;
}

.height100px {
	height: 100px;
}

.height120px {
	height: 120px;
}

.height150px {
	height: 150px;
}

.height200px {
	height: 200px;
}

.min-height1 {
	min-height: 1rem;
}

.min-height1_5 {
	min-height: 1.5rem;
}

.min-height2 {
	min-height: 2rem;
}

.min-height3 {
	min-height: 3rem;
}

.min-height5 {
	min-height: 5rem;
}

.min-height7 {
	min-height: 7rem;
}

.min-height10 {
	min-height: 10rem;
}



.line-height36 {
	line-height: 36px;
}