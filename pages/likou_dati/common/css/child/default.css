/* 初始化 */
[v-cloak] {
	display: none;
}

* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
	word-break: break-all;
}

view,
image,
text,
input,
button,
textarea,
cover-view,
cover-image,
swiper,
ad,
scroll-view,
checkbox,
radio,
scroll {
	box-sizing: border-box;
	word-break: break-all;
}

a {
	text-decoration: none;
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
	width: 0 !important;
}

::-webkit-scrollbar {
	width: 0 !important;
	height: 0;
}

.normal {
	background-color: transparent;
	line-height: normal;
	margin: 0;
	padding: 0;
}

.text-left {
	text-align: left;
}

.text-center {
	text-align: center;
}

.text-right {
	text-align: right;
}


.err_text {
	padding: 1rem 0;
	text-align: center;
	font-size: .9rem;
	color: #777;
}

/* 底部tabbar居中 */
.uni-tabbar-bottom {
	max-width: 640px !important;
	left: 50% !important;
	bottom: 0 !important;
	transform: translateX(-50%) !important;
}

/* fixed定位居中默认 */
.position-fixed-center {
	position: fixed;
	left: 50%;
	transform: translateX(-50%);
	max-width: 640px !important;
	width: 100%;
}

.memo_text {
	color: #777;
	font-size: .9rem;
}


.one-hidden {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.two-hidden {
	overflow: hidden !important;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}


.inhidden {
	display: none;
}

.sticky {
	position: sticky;
	left: 0;
	top: 0;
	z-index: 49;
}

/* 居中 */
body {
	display: flex;
	flex-direction: column;
	align-items: center;
}

uni-page-body {
	display: flex;
	justify-content: center;
}

/* uni-app {
	max-width: 640px !important;
} */

.content {
	width: 100%;
	max-width: 640px;
}

.easyinput-view {
	border: 1px solid #e5e5e5;
	min-height: 36px;
	border-radius: 4px;
	display: flex;
	align-items: center;
	padding: 0 10px;
	font-size: 14px;
	color: grey;
}

.button-normal {
	border: none !important;
	background-color: transparent;
	padding: 0;
	line-height: normal;
	margin: 0;
	font-size: 16px;
}

.button-normal:after {
	content: '';
	width: 0;
	height: 0;
}

rich-text {
	white-space: pre-wrap;
}

.easy-input {
	display: flex;
	box-sizing: border-box;
	flex-direction: row;
	align-items: center;
	border: 1px solid #DCDFE6;
	border-radius: 4px;
	color: #333;
}

.easy-input input {
	width: auto;
	position: relative;
	overflow: hidden;
	flex: 1;
	line-height: 1;
	font-size: 14px;
	height: 35px;
	padding: 0 10px;
}

.line-title {
	position: relative;
	padding-left: .5rem;
}

.line-title::after {
	content: "";
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 3px;
	height: 1rem;
	border-radius: .3rem;
	background-color: #3176FE;
}

.close-icon {
	position: absolute;
	top: .7rem;
	right: .7rem;
	color: #999;
}

.carousel-list {
	position: relative;
	width: calc((100% - 1.4rem) / 3);
	max-width: calc((640px - 1.4rem) / 3);
}

.carousel-list .close-icon {
	position: absolute;
	right: .3rem;
	top: .3rem;
	width: 1.5rem;
	height: 1.5rem;
	background-color: #777;
}

.bg-img-fixed {
	position: fixed;
	left: 50%;
	transform: translateX(-50%);
	top: 0;
	z-index: -1;
	width: 100%;
	max-width: 640px;
}