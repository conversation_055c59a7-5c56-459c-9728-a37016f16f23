/**
 * flex布局
 * 
 */
.f {
    display: flex;
}

.f-j-c {
    display: flex;
    justify-content: center;
}

.f-j-sa {
    display: flex;
    justify-content: space-around
}

.f-j-sb {
    display: flex;
    justify-content: space-between;
}

.f-a-c {
    display: flex;
    align-items: center;
}

.f-a-e {
    display: flex;
    align-items: flex-end;
}

.f-j-a-c {
    display: flex;
    justify-content: center;
    align-items: center;
}

.f-j-c-a-e {
    display: flex;
    justify-content: center;
    align-items: flex-end;
}

.f-j-e {
    display: flex;
    justify-content: flex-end;
}

.f-j-e-a-c {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.f-j-sa-a-c {
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.f-j-sb-a-c {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.f-j-sb-a-e {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}

.f-d-c {
    display: flex;
    flex-direction: column;
}

.f-d-c-j-c {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.f-d-c-j-e{
	display: flex;
	flex-direction: column;
	justify-content: flex-end;
}

.f-d-c-j-e-a-c{
	display: flex;
	flex-direction: column;
	justify-content: flex-end;
	align-items: center;
}

.f-d-c-j-sa {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
}

.f-d-c-a-c {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.f-d-c-a-e {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.f-d-c-j-a-c {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.f-d-c-j-sb {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.f-d-c-j-sb-a-s {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
}

.f-d-c-j-sb-a-c {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
}

.f-w {
    display: flex;
    flex-wrap: wrap;
}

.f-w-w-a-c {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.f-w-w-j-sb-a-c {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
}



.display-ib {
    display: inline-block;
}
