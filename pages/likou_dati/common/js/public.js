import {
    pathToBase64,
    base64ToPath
} from './mmmm-image-tools/index.js'
import {
    setClipboardData,
    getClipboardData
} from '@/pages/likou_dati/uni_modules/u-clipboard/js_sdk'
import base64 from "./base64.js"
import {
    pinyin
} from "pinyin-pro"

let ext_conf = uni.getExtConfigSync ? uni.getExtConfigSync() : {};

if (!ext_conf.who) {
    ext_conf = {
        // "who": 6, // wx3d75365be7d18c23
        "who": 288, // wx0921894817922c7d
        // "who": 90, // wxc33eee1ee069dd73
        "apihost": "https://ja.xwykj.com",
        template_id: 'test',
        debug: 1,
    }
}

// ext_conf.apihost = "https://wx-cloud-api-1755522-1304148175.ap-shanghai.run.tcloudbase.com";

// if (ext_conf.debug == 1) ext_conf.apihost = "http://ceshiphp.io/public/index.php";

ext_conf.apihost = "https://sport.xwykj.com"

function get_url_params(name, Url) {
    //URL GET 获取值
    var reg = new RegExp("(^|\\?|&)" + name + "=([^&]*)(\\s|&|$)", "i"),
        url = Url || location.href;
    if (reg.test(url))
        return unescape(RegExp.$2.replace(/\+/g, " "));
    return "";
}

// #ifdef H5
ext_conf.who = get_url_params('shopid') ? get_url_params('shopid') : '';
// #endif


let cur_date = new Date(),
    cur_month = cur_date.getMonth() + 1,
    cur_day = cur_date.getDate(),
    cur_time = `${cur_month}${cur_day}`;

console.log('ext_conf', ext_conf);

let xwyLib = {
    thanUrl: 'web/wx-cloud-api/pages/active/#/',
    cross_domain: 'https://wx-cloud-api-1755522-1304148175.ap-shanghai.run.tcloudbase.com/crossdomain?url=',
    static_url: 'https://prod-0g479j60184f120d-1304148175.tcloudbaseapp.com/',
    ext_conf,
    cur_time,

    ajax(opt) {
        let opts = opt || {},
            Method = opts.method || 'POST',
            Url = opt.url,
            Data = opts.data || {},
            Header = opts.header || {
                'content-type': 'application/x-www-form-urlencoded'
            };

        if (!Url) {
            this.alert('API接口地址为空');
            return false;
        }

        if (opt.loading) this.showToast();

        let uploadImg = 1;
        // #ifndef MP-WEIXIN
        uploadImg = 1;
        // #endif
        if (opt.uploadImg) uploadImg = opt.uploadImg;

        //拼接请求域名和api路径
        if (uploadImg) {
            if (Url && Url.indexOf('http') === -1) Url = ext_conf.apihost + '/' + Url;
        }

        // #ifdef H5
        if (location.origin != this.ext_conf.apihost) Url = `${this.cross_domain}${Url}`
        // #endif

        if (!opts.hideLoad) uni.showNavigationBarLoading();

        let app = getApp();
        if (app.globalData.access_token && !Data.access_token) Data.access_token = app.globalData.access_token;

        if (uploadImg) {
            let requestData = {
                url: Url,
                method: Method,
                data: Data,
                header: Header,
                success: function(res) {
                    if (res.statusCode === 200) {
                        uni.hideNavigationBarLoading();
                        typeof opts.success === 'function' && opts.success(res.data, res);
                        return
                    }
                    requestData.fail(res);
                },
                fail: (res) => {
                    uni.hideLoading();
                    console.log('fail--------------', res);
                },
                complete: (res) => {
                    uni.hideNavigationBarLoading()
                    typeof opts.complete == 'function' && opts.complete(res);
                }
            };
            uni.request(requestData)
        } else {
            wx.cloud.callContainer({
                config: {
                    env: 'prod-0g479j60184f120d',
                },
                path: Url,
                method: Method,
                header: {
                    'X-WX-SERVICE': 'wx-cloud-api'
                },
                data: Data,
                success: res => {
                    if (res.statusCode === 200) {
                        uni.hideNavigationBarLoading();
                        typeof opts.success === 'function' && opts.success(res.data, res);
                        return
                    }
                    uni.hideLoading();
                    console.log('fail--------------', res, Url);
                },
                fail: (res) => {
                    uni.hideLoading();
                    console.log('fail--------------', res);
                },
                complete: (res) => {
                    uni.hideNavigationBarLoading()
                    typeof opts.complete == 'function' && opts.complete(res);
                }
            })
        }
    },
    
    decodeURIComponent(url) {
        if (decodeURIComponent(url) === url) {
            return url
        } else {
            return this.decodeURIComponent(decodeURIComponent(url))
        }
    },

    // 时间戳转换成日期
    timestampToTime(timestamp, dr) {
        var date = new Date(timestamp * 1000); //时间戳为10位需*1000，时间戳为13位的话不需乘1000
        var Y = date.getFullYear() + '-';
        var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
        var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
        var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
        var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
        var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();

        let str = Y + M + D + h + m + s;

        if (dr) {
            str = (Y + M + D).substr(0, 10)
        }

        return str;
    },

    showLoading(text = "正在加载中...") {
        uni.showLoading({
            title: text,
        })
    },

    hideLoading() {
        uni.hideLoading();
    },

    alert(text, confirmText) {
        uni.showModal({
            content: text || '',
            showCancel: false,
            confirmText: confirmText || '确认',
        })
    },

    showModal(text, obj = {}) {
        uni.showModal({
            title: obj.title || '',
            content: text || '',
            showCancel: obj.showCancel == false ? false : true,
            cancelText: obj.cancelText || '取消',
            confirmText: obj.confirmText || '确定',
            success: res => {
                obj.success && obj.success(res);
            }
        })
    },

    showToast(text, obj = {}) {
        uni.showToast({
            title: text,
            icon: obj.icon ? obj.icon : 'none',
            duration: obj.duration ? obj.duration : 2000,
        })
    },

    back_previous_page(page = 1) {
        uni.navigateBack({
            delta: page,
        })
    },

    async copy_text(opt = {}) {
        let res = await setClipboardData(opt.url || '').catch(err => {
            this.alert('复制失败！')
        });
        if (res) {
            uni.hideToast();
            let text = '复制成功！';
            if (opt.text) text = opt.text;

            if (opt.toast) {
                this.showToast(text)
                return
            }
            this.showModal(text, {
                confirmText: '复制',
                showCancel: false,
                success: res => {
                    opt.success && opt.success();
                }
            })
        }
    },

    // 获取时间  e为天数  e = 1 表示获取明天  e = -1 表示获取昨天
    get_current_date(e = 0, dr) {
        let cur_time = new Date().getTime(),
            reduce_time = '';
        if (e) reduce_time = e * 1000 * 60 * 60 * 24
        let time = (cur_time + reduce_time) / 1000;
        return this.timestampToTime(time, dr)
    },

    // 获取星期几
    getWeekDate(date) {
        var now = date ? new Date(date) : new Date();
        var day = now.getDay();
        var weeks = new Array("星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六");
        var week = weeks[day];
        return week;
    },

    // 数字转成字母
    numberToLetter(e) {
        let letter = '';
        switch (Number(e)) {
            case 0:
                letter = 'A'
                break;
            case 1:
                letter = 'B'
                break;
            case 2:
                letter = 'C'
                break;
            case 3:
                letter = 'D'
                break;
            case 4:
                letter = 'E'
                break;
            case 5:
                letter = 'F'
                break;
            case 6:
                letter = 'G'
                break;
            case 7:
                letter = 'H'
                break;
            case 8:
                letter = 'I'
                break;
            case 9:
                letter = 'J'
                break;
            case 10:
                letter = 'K'
                break;
            case 11:
                letter = 'L'
                break;
            case 12:
                letter = 'M'
                break;
            case 13:
                letter = 'N'
                break;
            case 14:
                letter = 'O'
                break;
            case 15:
                letter = 'P'
                break;
            case 16:
                letter = 'Q'
                break;
            case 17:
                letter = 'R'
                break;
            case 18:
                letter = 'S'
                break;
            case 19:
                letter = 'T'
                break;
            case 20:
                letter = 'U'
                break;
            case 21:
                letter = 'V'
                break;
            case 22:
                letter = 'W'
                break;
            case 23:
                letter = 'X'
                break;
            case 24:
                letter = 'Y'
                break;
            case 25:
                letter = 'Z'
                break;
        }

        return letter
    },

    // 判断类型 是否单选，多选或者判断题
    judge_types(types) {
        let exist = false;
        switch (Number(types)) {
            case 1:
                exist = true
                break;
            case 2:
                exist = true
                break;
            case 3:
                exist = true
                break;
            case 4:
                exist = false
                break;
            case 5:
                exist = false
                break;
        }

        return exist
    },

    // 判断基础库版本
    judge_version(version) {
        if (!version) {
            this.alert('请输入需要比较的版本号！');
            return
        }
        let SDKVersion = getApp().globalData?.systemInfo?.SDKVersion || '';
        if (!SDKVersion) return false
        let versions = version.split('.'),
            SDKVersions = SDKVersion.split('.');

        if (versions[0] < SDKVersions[0]) return true;
        if (versions[0] > SDKVersions[0]) return false;

        if (versions[1] < SDKVersions[1]) return true;
        if (versions[1] > SDKVersions[1]) return false;

        if (versions[2] <= SDKVersions[2]) return true;
        if (versions[2] > SDKVersions[2]) return false;
    },


    // 获取具体的题目类型
    get_question_types_text(types) {
        let text = '';
        switch (Number(types)) {
            case 1:
                text = '单选'
                break;
            case 2:
                text = '多选'
                break;
            case 3:
                text = '判断'
                break;
            case 4:
                text = '填空'
                break;
            case 5:
                text = '简答'
                break;
        };

        return text;
    },

    // 秒转换成时分秒
    secondsToHoursMinutesSeconds(mss) {
        var hours = parseInt((mss % (60 * 60 * 24)) / (60 * 60));
        var minutes = parseInt((mss % (60 * 60)) / 60);
        var seconds = mss % 60;
        if (hours) return hours + " 时 " + minutes + " 分 " + seconds + " 秒 ";
        if (!hours && minutes) return minutes + " 分 " + seconds + " 秒 ";
        if (!hours && !minutes) return seconds + " 秒 ";
    },

    setBarTitle(title = "", opts = {}) {
        if (!title) {
            return false;
        }

        if (uni.setNavigationBarTitle) {
            uni.setNavigationBarTitle({
                title: title,
                success() {
                    opts.success && opts.success();
                },
                fail() {
                    opts.fail && opts.fail();
                }
            })
        }
    },

    setNavigationBarColor(opt = {}) {
        if (uni.setNavigationBarTitle) {
            uni.setNavigationBarColor({
                frontColor: opt.fzColor || '#000000',
                backgroundColor: opt.bgColor || '#fff',
                success: res => {
                    opt.success && opt.success();
                },
                fail: res => {
                    opt.fail && opt.fail();
                }
            })
        }
    },

    // 汉字转成拼音
    topinyin(text, options) {
        let opt = options || {
            toneType: 'none',
            type: 'array'
        }
        return pinyin(text, opt)
    },

    // 获取当前帐号信息可以返回小程序的Appid。如果使用了微信小程序的云端插件，还可以反馈插件的id和版本
    getAccountInfoSync() {
        return uni.getAccountInfoSync()
    },

    // 将页面滚动到目标位置
    pageScrollTo(opt = {}) {
        let obj = {}

        if (opt.scrollTop) obj.scrollTop = opt.scrollTop;
        if (opt.selector) obj.selector = opt.selector;
        if (opt.selector) obj.selector = opt.selector;
        if (opt.duration) obj.duration = opt.duration;
        if (opt.success) obj.success = opt.success;
        if (opt.fail) obj.fail = opt.fail;
        if (opt.complete) obj.complete = opt.complete;

        uni.pageScrollTo(obj)
    },

    /**
     * 获取路径的参数
     * @param name  获取的参数
     * @param Url   自定义获取参数的链接
     */
    getUrlParams: function(name, Url) {
        //URL GET 获取值
        var reg = new RegExp("(^|\\?|&)" + name + "=([^&]*)(\\s|&|$)", "i"),
            url = Url || location.href;
        if (reg.test(url))
            return unescape(RegExp.$2.replace(/\+/g, " "));
        return "";
    },

    // 路由跳转
    routeJump(url, name = 'navigateTo') {
        url = this.h5_url_add_shopid(url);
        if (name == 'navigateTo') {
            uni.navigateTo({
                url,
				fail: err => console.log(err)
            })
        }
        if (name == 'redirectTo') {
            uni.redirectTo({
                url,
				fail: err => console.log(err)
            })
        }
        if (name == 'reLaunch') {
            uni.reLaunch({
                url,
				fail: err => console.log(err)
            })
        }
        if (name == 'switchTab') {
            uni.redirectTo({
                url,
				fail: err => console.log(err)
            })
        }
    },

    // H5路径加上商户号
    h5_url_add_shopid(url) {
        let pageUrl = url;
        // #ifdef H5
        if (url.includes('?')) {
            let params_url = url.split('?')[1];
            if (!params_url.includes('shopid')) {
                pageUrl = `${url}&shopid=${ext_conf.who}`
            }
        } else {
            pageUrl = `${url}?shopid=${ext_conf.who}`
        }
        // #endif
        return pageUrl
    },

    // 获取当前页面的路径和参数
    get_cur_page_and_params() {
        let cur_page = getCurrentPages()[getCurrentPages().length - 1];
        let pageUrl = '';
        let curRoute = cur_page.route;
        let curParam = cur_page.options,
            params = '';
        for (let key in curParam) {
            if (params) {
                params += `&${key}=${curParam[key]}`
            } else {
                params = `?${key}=${curParam[key]}`
            }
        }
        pageUrl = `/${curRoute}${params}`
        return pageUrl
    },

    query_page_exist(route) {
        let pages_list = getCurrentPages();
        if (!this.isArray(pages_list)) return false;

        let isTrue = false;
        for (let i in pages_list) {
            if (pages_list[i].route.includes(route)) {
                isTrue = true
                break;
            }
        }
        return isTrue
    },

    // 判断是否授权个人信息
    judgeAuth(opt = {}) {
        let app = getApp();
        let userInfo = app.globalData?.userInfo;
        if (!userInfo.nickname || userInfo.nickname == `微信用户` || userInfo.nickname == `匿名微信用户` || !userInfo
            .headimg || userInfo.headimg ==
            'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/shop/9/90/2022/04/14/6257e06f3581f_5652.jpg'
        ) {
            // #ifdef MP-WEIXIN
            opt.unauth && opt.unauth();
            // #endif
            // #ifdef H5
            this.showModal('请先完善个人信息再进行操作！---更改昵称，头像', {
                confirmText: '去完善',
                success: res => {
                    if (res.confirm) {
                        let pageUrl = xwyLib.get_cur_page_and_params();
                        uni.setStorageSync('pageUrl', pageUrl);
                        this.skip_page(203)
                    }
                }
            })

            // #endif
            return
        }
        opt.authed && opt.authed();
    },

    // 判断相应的积分消耗规则是否存在
    judge_integral_cost_set(opt = {}) {
        if (opt.closed_AD) {
            opt.success && opt.success();
            return
        }

        let app = getApp();
        if (!app.globalData.integral_cost) {
            opt.success && opt.success();
            return
        }
        // 获取设置的积分消耗规则
        let integral_cost = app.globalData.integral_cost;

        if (!this.isArray(integral_cost) || !opt.types) {
            opt.success && opt.success();
            return
        }

        let isTrue = false,
            obj = {};
        integral_cost.forEach(val => {
            if (val.types == opt.types) {
                isTrue = true;
                obj = val;
            }
        })

        if (!isTrue) {
            opt.success && opt.success();
            return
        }

        let content = `${obj.title}，是否确认？`;
        if (!opt.close_cost) content = `${obj.title}需要消耗${obj.cost}积分,是否确认？`
        this.showModal(content, {
            confirmText: '确认',
            success: response => {
                if (response.confirm) opt.success && opt.success();
            }
        })
    },


    //获取浏览器类型   get_browser 【1】微信  【2】支付宝 【3】其他浏览器
    get_browser() {
        var browser = navigator.userAgent.toLowerCase();
        if (browser.match(/Alipay/i) == "alipay") {
            console.log("支付宝app的浏览器");
            return 2;
        } else if (browser.match(/MicroMessenger/i) == "micromessenger") {
            console.log("微信app的浏览器");
            return 1;
        } else {
            console.log("其它浏览器");
            return 3;
        }
    },

    // 数组深拷贝
    copyAry(options) {
        return this.copyArray(options)
    },

    copyObj(obj) {
        let newObj = {};
        for (const key in obj) {
            let value = obj[key];
            if (Object.prototype.toString.call(value) === "[object Object]") value = this.copyObj(value);
            if (Object.prototype.toString.call(value) === "[object Array]") value = this.copyAry(value);
            newObj[key] = value;
        }
        return newObj;
    },

    copyArray(ary) {
        let newAry = []
        for (const item of ary) {
            let value = item;
            if (Object.prototype.toString.call(value) === "[object Object]") value = this.copyObj(value);
            if (Object.prototype.toString.call(value) === "[object Array]") value = this.copyAry(value);
            newAry.push(value);
        }
        return newAry;
    },

    // 获取现在所在地 微信端
    getLocation_wechat(cb, types = 1) {
        uni.getSetting({
            success: (res) => {
                console.log('查看是否授权了位置', res)
                if (!res.authSetting['scope.userLocation']) {
                    uni.authorize({
                        scope: 'scope.userLocation',
                        success: (e) => {
                            console.log('授权位置成功', e)
                            this.auth_after_getlocation(cb, types);
                        },
                        fail: (e) => {
                            uni.hideLoading();
                            uni.showModal({
                                title: '温馨提示',
                                content: '您已拒绝授权，是否去设置打开？',
                                confirmText: "确认",
                                cancelText: "取消",
                                success: (response) => {
                                    if (!response.confirm) {
                                        if (types == 1) {
                                            uni.showModal({
                                                content: `未获取到地址定位，请授权重新获取！`,
                                                success: re => {
                                                    this
                                                        .back_previous_page();
                                                }
                                            })
                                            return
                                        }
                                        this.alert('未获取到地址定位，请授权重新获取！');
                                        return
                                    }
                                    wx.openSetting({
                                        success: (r) => {
                                            console.log(
                                                '查看是否opensetting授权',
                                                r)
                                            r.authSetting = {
                                                "scope.userLocation": true,
                                            }
                                            this.auth_after_getlocation(
                                                cb, types);
                                        }
                                    });
                                }
                            });
                        }
                    })
                } else {
                    this.auth_after_getlocation(cb, types);
                }
            },
            fail: (res) => {
                uni.hideLoading()
                console.log('wx.getSetting 失败回调', res)
            }
        })
    },

    // 授权后获取地理位置
    auth_after_getlocation(cb, types = 1) {
        uni.getLocation({
            type: 'wgs84',
            altitude: true,
            success: e => {
                console.log('获取经纬度', e);
                if (e.errMsg != "getLocation:ok") {
                    this.alert('获取当前位置失败！')
                    return
                }
                cb && cb(e);
            },
            complete: res => {
                console.log('获取地理位置complete', res);
                if (types == 1) {
                    if (res.errMsg != "getLocation:ok") {
                        uni.showModal({
                            content: `未获取到地址定位，请授权重新获取！`,
                            success: re => {
                                this.back_previous_page();
                            }
                        })
                    }
                    return
                }
            }
        })
    },

    getTabIdx(type) {
        let app = getApp();
        let shop_set = app.globalData.shopconfset?.shop_set;
        if (shop_set?.page_diy_set?.tabbar_set?.list?.length) {
            let list = shop_set?.page_diy_set?.tabbar_set?.list;
            for (let i = 0, len = list.length; i < len; i++) {
                if (list[i].type == type) {
                    return i;
                    break;
                }
            }
            return -1;
        } else {
            return -1;
        }
    },

    // 是否字符串是不是JSON
    isJSON(str) {
        if (typeof str == 'string') {
            try {
                var obj = JSON.parse(str);
                if (typeof obj == 'object' && obj) {
                    return true;
                } else {
                    return false;
                }

            } catch (e) {
                console.log('error：' + str + '!!!' + e);
                return false;
            }
        }
        return false;
    },

    isArray(arr) {
        if (Array.isArray(arr) && arr.length) return true
        return false
    },

    isTrue(str) {
        if (str && str != null) return true
        return false
    },

    isInt(value) {
        return (typeof value === 'number' && !isNaN(value) && value % 1 === 0);
    },

    jsonEncode(str) {
        return base64.baseEncode(JSON.stringify(str));
    },

    decodeJson(str) {
        return JSON.parse(base64.baseDecode(str));
    },


    computed_px_percent(opt = {}) {
        let new_obj = this.copyObj(opt.data);
        let list = opt.array || ['after_text', 'before_text', 'headimg', 'person_count', 'sign_name',
            'active_qrcode'
        ];
        for (let key in new_obj) {
            list.forEach(val => {
                if (val == key) {
                    if (this.isArray(new_obj[key])) {
                        new_obj[key].forEach(v => {
                            let _obj = {
                                data: v,
                                types: opt.types || 1,
                            }
                            v = this.forin_opt(_obj);
                        })
                        return
                    }
                    let _obj = {
                        data: new_obj[key],
                        types: opt.types || 1,
                    }
                    new_obj[key] = this.forin_opt(_obj);
                }
            })
        }
        return new_obj
    },

    forin_opt(opt = {}) {
        let list = ['width', 'height', 'left', 'top'],
            data = opt.data;
        for (let key in data) {
            list.forEach(val => {
                if (val == key) {
                    if (opt.types == 1) data[key] = this.px_to_percent(data[key]);
                    if (opt.types == 2) data[key] = this.percent_to_px(data[key]);
                }
            })
        }
        return data
    },

    px_to_percent(px) {
        let app = getApp();
        let windowWidth = app.globalData.systemInfo.windowWidth;
        if (windowWidth > 640) windowWidth = 640;
        return parseFloat((px / windowWidth).toFixed(2))
    },

    percent_to_px(percent) {
        let app = getApp();
        let windowWidth = app.globalData.systemInfo.windowWidth;
        if (windowWidth > 640) windowWidth = 640;
        return parseFloat((percent * windowWidth).toFixed(2))
    },

    // 过滤emoji
    replaceEmoji(str) {
        if (!str) return ''
        return str.replace(/\uD83C[\uDF00-\uDFFF]|\uD83D[\uDC00-\uDE4F]|\uD83E[\uDD00-\uDD80]/g, "");
    },

}


let login = {
    init(cb) {
        let app = getApp();
        if (!uni.getStorageSync(`${app.globalData.who}access_token-${this.cur_time}`) && !app.globalData.access_token) {
            this.clear_storage(() => {}, 1);
            this.clear_storage(() => {}, 2);
            // #ifdef MP-WEIXIN
            this.wechatLogin(cb);
            return
            // #endif

            // #ifndef MP-WEIXIN
            this.h5Login();
            // #endif
            return
        } else {
            if (!app.globalData.access_token) {
				app.globalData.access_token = uni.getStorageSync(`${app.globalData.who}access_token-${this.cur_time}`);
			}
        }

        if (!app.globalData.userInfo || !app.globalData.shopconfset) {
            this.get_userInfo(cb)
            return
        }

        cb && cb();
    },

    h5Login() {
        let pageUrl = xwyLib.get_cur_page_and_params();
        uni.setStorageSync('pageUrl', pageUrl);
        let url = '/pages/login/password-login/password-login'
        xwyLib.routeJump(url, 'reLaunch');
    },

    // 登录 微信登录
    wechatLogin(cb) {
        xwyLib.showLoading();
        uni.login({
            "provider": 'weixin',
            success: e => {
                this.get_openid(e.code, cb);
            },
            fail: (err) => {
                uni.hideLoading()
                console.log('获取code失败', err);
            }
        })
    },

    // 获取openid
    async get_openid(code, cb) {
        let app = getApp();
        let data = {
            js_code: code,
            shopid: parseInt(this.ext_conf.who),
        };
        let obj = {
            data,
            url: 'front.user.wechat.login/code2session',
            success: res => {
                console.log('获取openid请求的参数' + JSON.stringify(data));
                console.log(`获取openid---${JSON.stringify(res)}`);
                uni.hideLoading();
                if (res.status != 1) {
                    this.alert(`用户登录失败！---${JSON.stringify(res)}`)
                    uni.hideLoading();
                    return
                }

                if (!res.data.access_token || !res.data.wechat_res || !res.data.wechat_res.openid) {
                    this.alert('登录失败！');
                    uni.hideLoading();
                    return
                }

                app.globalData.access_token = res.data.access_token;
                app.globalData.wechat_res = res.data.wechat_res;
                uni.setStorageSync(`${this.ext_conf.who}access_token-${this.cur_time}`, res
                    .data.access_token);
                uni.setStorageSync(`${this.ext_conf.who}openid-${this.cur_time}`, res.data
                    .wechat_res.openid);

                this.get_userInfo(cb);
            },
            fail: res => {
                this.alert(`获取openid---fail---${JSON.stringify(res)}`);
                console.log('获取openid---fail', res);
            },
            compelet: res => {
                console.log('获取openid---compelet', res);
            }
        };
        this.ajax(obj);
    },


    // 获取用户信息
    async get_userInfo(cb, newUserinfo = '') {
        this.showLoading();
        let app = getApp();
        if (uni.getStorageSync(`${this.ext_conf.who}userInfo${this.cur_time}`) && !newUserinfo) {
            let userInfo = JSON.parse(uni.getStorageSync(`${this.ext_conf.who}userInfo${this.cur_time}`))
            app.globalData.userInfo = userInfo;
            this.get_shopconfset(cb)
            return
        }

        let data = {
            access_token: app.globalData.access_token,
        };
        let obj = {
            data,
            url: `front.user.user/get_user_details`,
            success: res => {
                // console.log('获取用户信息', res);
                if (res.status != 1 || !res.data || !res.data.user_details) {
                    this.alert(res.info);
                    return
                }

                if (!newUserinfo) {
                    this.clear_storage(() => {}, 1);
                }

                // #ifdef MP-WEIXIN
                if (!res.data.user_details.nickname) {
                    let user_obj = {
                        nickname: '微信用户',
                        headimg: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/shop/9/90/2022/04/14/6257e06f3581f_5652.jpg',
                    }
                    login.upload_userInfo(user_obj)
                    res.data.user_details.nickname = user_obj.nickname;
                    res.data.user_details.headimg = user_obj.headimg;
                }
                // #endif
                app.globalData.userInfo = res.data.user_details;
                uni.setStorageSync(`${this.ext_conf.who}userInfo${this.cur_time}`, JSON.stringify(
                    res.data.user_details));
                if (!newUserinfo) {
                    this.get_shopconfset(cb)
                    return
                }
                uni.hideLoading();
                cb && cb(res)
            }
        };
        this.ajax(obj);
    },

    // 获取商户个性化设置
    async get_shopconfset(cb) {
        let app = getApp();
        if (uni.getStorageSync(`${this.ext_conf.who}shopconfset${this.cur_time}`)) {
            let shopconfset = JSON.parse(uni.getStorageSync(
                `${this.ext_conf.who}shopconfset${this.cur_time}`))
            app.globalData.shopconfset = shopconfset;
            this.get_cost_integral_set(cb);
            if (shopconfset?.shop_set?.static_url) this.static_url = app.globalData?.shopconfset?.shop_set
                ?.static_url;
            let page_diy_set = app.globalData.shopconfset?.shop_set?.page_diy_set;
            // 获取导航栏的颜色 如果存在导航栏颜色则赋值
            if (page_diy_set?.navigation_bar_color_set) {
                let fzColor = page_diy_set?.navigation_bar_color_set?.color || '#000000';
                let bgColor = page_diy_set?.navigation_bar_color_set?.background_color || '#fff';
                if (fzColor == 'black') fzColor = '#000000';
                if (fzColor == 'white') fzColor = '#ffffff';
                this.setNavigationBarColor({
                    fzColor,
                    bgColor,
                });
            }
            return
        }
        let obj = {
            data: {
                access_token: app.globalData.access_token,
                types: 1,
            },
            url: `front.user.shop/shopconf`,
            success: res => {
                // console.log('获取shopconf', res);
                if (res.status != 1 || !res.data) {
                    uni.hideLoading();
                    this.alert(res.info)
                    return
                }
                let shopconfset = res.data;
                app.globalData.shopconfset = shopconfset;
                if (shopconfset?.shop_set?.static_url) this.static_url = app.globalData?.shopconfset
                    ?.shop_set?.static_url;
                uni.setStorageSync(`${this.ext_conf.who}shopconfset${this.cur_time}`, JSON
                    .stringify(res.data))
                this.get_cost_integral_set(cb);
            }
        };
        this.ajax(obj);
    },

    // 考试系统设置的开启功能消耗积分-扣除积分
    async get_cost_integral_set(cb, types = '') {
        let app = getApp();
        if (uni.getStorageSync(`${this.ext_conf.who}integral_cost${this.cur_time}`)) {
            let integral_cost = JSON.parse(uni.getStorageSync(`${ext_conf.who}integral_cost${cur_time}`))
            app.globalData.integral_cost = integral_cost;
            uni.hideLoading();
            cb && cb();
            return
        }
        let data = {
            shopid: this.ext_conf.who,
        };

        if (types) data.types = types;

        let obj = {
            data,
            url: `front.user.integral_list/cost_integral_set`,
            success: res => {
                uni.hideLoading();
                console.log('获取增值服务---', res);
                if (res.status == 1 && res?.data?.shop_set?.integral_cost?.length) {
                    uni.setStorageSync(`${ext_conf.who}integral_cost${cur_time}`, JSON.stringify(res
                        .data.shop_set.integral_cost))
                    app.globalData.integral_cost = res.data.shop_set.integral_cost;
                    cb && cb();
                    return
                }
                app.globalData.integral_cost = {};
                cb && cb();
            }
        };
        this.ajax(obj);
    },

    // 更新用户信息
    async upload_userInfo(opt = {}) {
        if (!opt.nickname) {
            this.alert('请输入用户昵称！')
            return
        }

        if (opt.mobile && opt.mobile.length < 11) {
            this.alert('请正确输入手机号码！')
            return
        }

        let app = getApp();
        let params = {
            access_token: app.globalData.access_token,
            shopid: parseInt(this.ext_conf.who),
        };

        // 昵称
        if (opt.nickname) params.nickname = opt.nickname;

        // 头像
        if (opt.headimg) params.headimg = opt.headimg;

        // 手机号码
        if (opt.mobile) params.mobile = opt.mobile;

        // 性别
        if (opt.gender) params.gender = opt.gender;

        // 邮箱
        if (opt.email) params.email = opt.email;

        let obj = {
            data: params,
            url: `front.user.user/update_user_details`,
            success: res => {
                // console.log('更新用户信息', res);
                if (res.status != 1) {
                    uni.hideLoading()
                    this.alert(res.info);
                    return
                }

                this.get_userInfo(() => {
                    opt.success && opt.success(res);
                }, 1)
            }
        };
        this.ajax(obj);
    },

    // 获取配置信息
    get_json_conf_set(opt = {}) {
        this.showLoading();
        let data = {
            name: opt.name || '',
        };
        let obj = {
            data,
            url: `front.system.system_conf/json_conf_set`,
            success: res => {
                uni.hideLoading();
                // console.log('获取配置信息成功success', res);
                // if (res.status == 1 && res?.data?.conf_set?.background_image) {
                if (res.status == 1 && res?.data?.conf_set) {
                    opt.success && opt.success(res.data.conf_set);
                    return
                }
                opt.fail && opt.fail(res);
            },
            fail: res => {
                uni.hideLoading();
                console.log('获取配置信息失败fail', res);
                this.alert(JSON.stringify(res));
            }
        };
        this.ajax(obj);
    },

}


let pay = {
    // 支付
    async create_wechat_pay_params(params) {
        let openid = uni.getStorageSync(`${this.ext_conf.who}openid-${this.cur_time}`);
        let data = {
            openid: openid,
            shopid: this.ext_conf.who,
            types: params.types || 2,
        };

        if (params.foreign_orderid) data.foreign_orderid = params.foreign_orderid;
        if (params.amount) data.amount = params.amount;

        let obj = {
            data,
            url: `front.user.wechat.pay.small_app/small_app_create_pay`,
            success: res => {
                uni.hideLoading();
                if (!res.data || !res.data.create_res) {
                    params.fail && params.fail(res);
                    return
                }
                let pay_obj = res.data.create_res;

                let opt = {
                    appId: pay_obj.appId, //公众号名称，由商户传入
                    timeStamp: pay_obj.timeStamp, //时间戳，自1970年以来的秒数
                    nonceStr: pay_obj.nonceStr, //随机串
                    package: pay_obj.package,
                    signType: pay_obj.signType,
                    paySign: pay_obj.paySign,
                    success: response => {
                        params.success && params.success();
                    }
                };
                this.requestPayment(opt);
                // this.payment(op); H5支付需要这个
            },
            fail: res => {
                uni.hideLoading();
            }
        };
        this.ajax(obj);
    },

    // 小程序微信支付
    requestPayment(opt) {
        uni.requestPayment({
            'timeStamp': opt.timeStamp,
            'nonceStr': opt.nonceStr,
            'package': opt.package,
            'signType': opt.signType,
            'paySign': opt.paySign,
            'success': function(res) {
                console.log('支付 微信接口 res ---', res);
                opt.success && opt.success(res)
            },
            'fail': opt.fail || function(error) {
                if (error.errMsg === 'requestPayment:fail' && error.err_desc) {
                    self.alert(error.err_desc);
                    let logContent = '\n=================== 小程序微信支付异步 =====================\n';
                    logContent += '---【微信支付异常错误代码】---\n' + JSON.stringify(error) +
                        '\n====END=====\n';
                }
            }
        })
    },

    // h5微信支付
    isWechat: function() {
        var ua = window.navigator.userAgent.toLowerCase();
        if (ua.match(/micromessenger/i) == 'micromessenger') {
            return true;
        } else {
            return false;
        }
    },
    payment(data) {
        if (!this.isWechat()) {
            return;
        }
        if (typeof WeixinJSBridge == "undefined") {
            if (document.addEventListener) {
                document.addEventListener('WeixinJSBridgeReady', this.jsApiCall, false);
            } else if (document.attachEvent) {
                document.attachEvent('WeixinJSBridgeReady', this.jsApiCall);
                document.attachEvent('onWeixinJSBridgeReady', this.jsApiCall);
            }
            this.alert('无法支付，需要在微信里打开并支付。')
        } else {
            console.log('准备支付');
            this.jsApiCall(data);
        }
    },

    jsApiCall(data) {
        let that = this;
        //使用原生的，避免初始化appid问题
        WeixinJSBridge.invoke('getBrandWCPayRequest', {
                "appId": data.appId,
                "timeStamp": data.timeStamp,
                "nonceStr": data.nonceStr, // 支付签名随机串，不长于 32 位
                "package": data.package, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=\*\*\*）
                "signType": data.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
                "paySign": data.paySign, // 支付签名
            },
            function(res) {
                var msg = res.err_msg ? res.err_msg : res.errMsg;
                switch (msg) {
                    case "get_brand_wcpay_request:ok": //支付成功时
                        console.log(`支付成功-----${JSON.stringify(res)}`)
                        if (uni.getStorageSync('callbackpage')) {
                            let callback = JSON.parse(that.base64.baseDecode(uni
                                .getStorageSync('callbackpage')));

                            uni.removeStorageSync('callbackpage');
                            location.href = callback;
                            return
                        }
                        that.showToast('未获取到返回页，请手动返回！')

                        break;
                    default: //支付失败时
                        WeixinJSBridge.log('支付失败!' + msg + ',请返回重试.');
                        break;
                }
            })
    },
}


let adObj = {
    interstitialAd: '',

    // 创建插屏广告
    create_InterstitialAd(closed) {
        let app = getApp();

        if (closed) return

        // #ifdef MP-WEIXIN
        if (app.globalData.shopconfset?.shop_set?.ad_list) {
            if (!uni.createInterstitialAd) return

            let ad_list = app.globalData.shopconfset.shop_set.ad_list;
            if (!this.isArray(ad_list)) return

            let adID = '';
            for (let i = 0; i < ad_list.length; i++) {
                if (ad_list[i].ad_types == 3) {
                    adID = ad_list[i].ad_id
                    break;
                }
            }

            if (!adID) return
            // console.log('adID', adID);
            var interstitialAd = this.interstitialAd = uni.createInterstitialAd({
                adUnitId: adID
            });
            interstitialAd.onLoad(() => {
                console.log("插屏 广告加载成功");
                this.show_interstitialAd();
            });
            interstitialAd.onError((err) => {
                console.log("插屏 广告加载失败", err);
            });

        }
        // #endif
    },

    // 显示插屏广告
    show_interstitialAd() {
        if (this.interstitialAd) {
            this.interstitialAd.show().catch((err) => {
                console.error(err)
            })
        }
    },

    innerAudioContext: '',
    // 创建音频
    createInnerAudio(opt = {}) {
        let innerAudioContext = this.innerAudioContext = uni.createInnerAudioContext();
        innerAudioContext.autoplay = opt.autoplay || true;
        innerAudioContext.loop = opt.loop || false;
        innerAudioContext.sessionCategory = opt.sessionCategory || 'soloAmbient';
        innerAudioContext.src = opt.src || '';
        innerAudioContext.onCanplay(() => {
            console.log('音频进入可以播放状态，但不保证后面可以流畅播放');
            this.playInnerAudio();
            opt.success && opt.success();
        })
        innerAudioContext.onError(err => {
            console.log('音频播放错误err', err);
            // if (err.errCode) {
            //     let text = '';
            //     switch (err.errCode) {
            //         case 10001:
            //             text = '系统错误'
            //             break;
            //         case 10002:
            //             text = '网络错误'
            //             break;
            //         case 10003:
            //             text = '文件错误'
            //             break;
            //         case 10004:
            //             text = '格式错误'
            //             break;
            //         case -1:
            //             text = '未知错误'
            //             break;
            //     }

            //     this.showToast(`音频播放错误-${text}`);
            // }
        })
    },
    // 开始播放
    playInnerAudio() {
        this.innerAudioContext.play();
    },
    // 暂停播放
    pauseInnerAudio() {
        this.innerAudioContext.pause();
    },
    // 停止播放
    stopInnerAudio() {
        this.innerAudioContext.stop();
    },
    // 销毁当前实例
    destroyInnerAudio() {
        this.innerAudioContext.destroy();
    },
}


let basicConfiguration = {
    // 清除缓存
    clear_storage(cb, item) {
        uni.getStorageInfo({
            success: res => {
                if (!Array.isArray(res.keys)) {
                    xwyLib.alert('获取缓存失败!')
                    return
                }
                let keys = res.keys;
                if (keys.length) {
                    let storage_list = ['shopconfset', 'integral_cost'];

                    if (item == 1) {
                        storage_list = ['userInfo', 'shopconfset', 'integral_cost',
                            'image_category_list'
                        ];
                    }

                    if (item == 2) storage_list = ['access_token', 'openid'];

                    if (item == 'more') {
                        storage_list = ['userInfo', 'shopconfset', 'uni_id_token_expired',
                            'uni_id_token', 'integral_cost', 'image_category_list', 'access_token',
                            'openid',
                        ]
                    }
                    keys.forEach((val, idx, arr) => {
                        storage_list.forEach((v, i, a) => {
                            if (val.includes(v)) uni.removeStorageSync(val);
                        })
                    })
                    cb && cb();
                }
            }
        });
    },
    // 跳转到其他页面
    skip_page(e) {
        let app = getApp();
        let url = ``,
            closeUrl = '';

        if (typeof(e) == 'number') {
            switch (e) {
                // 首页
                case 8:
                    closeUrl = `/pages/index/index`
                    break
                    // 个人中心
                case 9:
                    closeUrl = `/pages/user/user`
                    break
					// 我的考卷
				case 97:
					url = `/pages/likou_dati/pages/exam/exam_list/exam_list`
					break
                    // 登录
                case 201:
                    url = `/pages/login/password-login/password-login`
                    break
                    // 注册
                case 202:
                    url = `/pages/login/register/register`
                    break
                    // 我的信息
                case 203:
                    url = `/pages/user/user_details/user_details`
                    break
                    // 发布新考卷
                case 204:
                    url = `/pages/likou_dati/pages/exam/exam_add/exam_add`
                    break
                    // 添加考题
                case 206:
                    url = `/pages/likou_dati/pages/question/add_question/add_question`
                    break
                    // 题库
                case 207:
                    url = `/pages/likou_dati/pages/question/question_bank_list/question_bank_list`
                    break
                    // 题库分类
                case 208:
                    url = `/pages/likou_dati/pages/question/category_list/category_list`
                    break
                    // 我的成绩单
                case 209:
                    url = `/pages/likou_dati/pages/exam/exam_transcript/exam_transcript`
                    break
                    // 积分明细
                case 211:
                    url = `/pages/user/integral_details/integral_details`
                    break;
                    // 批量导题说明
                case 212:
                    url = `/pages/likou_dati/pages/question/batch_import_question/explain/explain`
                    break
                    // 批量导题-excel导入
                case 213:
                    url = `/pages/likou_dati/pages/question/batch_import_question/excel_import/excel_import`
                    break
                    // 批量导题-文本导入
                case 214:
                    url = `/pages/likou_dati/pages/question/batch_import_question/text_import/text_import`
                    break
                    // 公共题库
                case 215:
                    url = `/pages/likou_dati/pages/question/question_bank_list/question_bank_list?public=1`
                    break
                    // 随机出题类型
                case 216:
                    url = `/pages/likou_dati/pages/question/random_question_types/random_question_types`
                    break
                    // 添加考卷
                case 217:
                    url = `/pages/likou_dati/pages/exam/exam_add/exam_add`
                    break;
                    // 浏览记录
                case 218:
                    url = `/pages/exam/exam/exam_browsing_storage/exam_browsing_storage`
                    break;
                    // 背题练习
                case 219:
                    url = `/pages/exam/answer/answer_open_question/answer_open_question`
                    break;
                    // 提现
                case 221:
                    url = `/pages/user/withdraw/withdraw_balance/withdraw_balance`
                    break;
                    // 提现记录
                case 222:
                    url = `/pages/user/withdraw/withdraw_record/withdraw_record`
                    break
                    // 活动广场
                case 223:
                    url = `/pages/likou_dati/pages/activity/activity_list/activity_list`
                    break;
                    // 答题广场
                case 224:
                    url = `/pages/likou_dati/pages/exam/exam_list/exam_list`
                    break;
                    // 增值服务
                case 226:
                    url = `/pages/user/integral_service/integral_service`
                    break;

            }
        }

        if (typeof(e) == 'object') {
            if (e.types && e.types.includes('closeUrl')) {
                closeUrl = e.url;
            } else {
                url = e.url
            }
        }

        if (closeUrl) {
            this.routeJump(closeUrl, 'reLaunch')
            return
        }

        this.routeJump(url)
    },

    skip_image_house(text) {
        let url = `/pages/user/image_house/image_house`;
        if (text) {
            url += `?picName=${text}`
        }
        this.routeJump(url)
    },

    previewImage(opt = {}) {
        let urls = [];
        if (typeof(opt.urls) == 'string') urls = [opt.urls];
        if (Array.isArray(opt.urls) && opt.urls.length) urls = opt.urls;

        uni.previewImage({
            urls,
            current: opt.current || '',
        })
    },

    canvasToTempFilePath(opt = {}) {
        setTimeout(() => {
            uni.canvasToTempFilePath({
                x: 0,
                y: 0,
                width: opt.width || 100,
                height: opt.height || 34.7,
                quality: opt.quality || 1,
                fileType: opt.fileType || 'jpg',
                canvasId: opt.canvasId,
                success: res => {
                    if (res.errMsg != "canvasToTempFilePath:ok" || !res.tempFilePath) {
                        this.alert('生成图片失败了---' + `${JSON.stringify(res)}`)
                        return
                    }
                    opt.success && opt.success(res)
                },
                fail: res => {
                    console.log(`生成图片失败！————${JSON.stringify(res)}`);
                    opt.num += 1;
                    if (opt.num == 21) {
                        uni.hideLoading();
                        this.alert(`生成图片失败！————${JSON.stringify(res)}`);
                        return
                    }
                    if (opt.quality >= 0.1) opt.quality -= 0.1;
                    this.canvasToTempFilePath(opt);
                }
            }, this)
        }, opt.time || 300)
    },

    loadFontFace(opt = {}) {
        uni.loadFontFace({
            global: opt.global || false,
            family: opt.family,
            source: opt.source,
            scopes: ["webview", "native"],
            success: res => {
                console.log('loadFontFace', res);
            },
            fail: res => {
                console.log('loadFontFace__fail', res);
            }
        })
    },

}


let curItemPost = {
    // 获取图片分类
    get_image_category_list(opt = {}) {
        if (uni.getStorageSync(`${ext_conf.who}image_category_list-${cur_time}`)) {
            let img_category_list = JSON.parse(uni.getStorageSync(
                `${ext_conf.who}image_category_list-${cur_time}`));
            opt.success && opt.success(img_category_list);
            return
        }

        if (!opt.category_id) return this.showModal('获取分类失败了！---没有获取到分类的ID');
        this.showLoading();
        let app = getApp();
        let data = {
            access_token: app.globalData.access_token,
            category_id: opt.category_id,
        };
        let obj = {
            data,
            url: `front.system.flat_pic/pic_category_list`,
            success: res => {
                uni.hideLoading();
                if (res.status != 1 || !res.data.pic_category_list || !res.data.pic_category_list.length) {
                    opt.fail && opt.fail(res);
                    return
                }
                uni.setStorageSync(`${ext_conf.who}image_category_list-${cur_time}`, JSON.stringify(res.data
                    .pic_category_list))
                opt.success && opt.success(res.data.pic_category_list);
            }
        };
        this.ajax(obj);
    },

    // 获取图片列表 传category_id则获取分类中的图片
    get_image_list_from_category(opt = {}) {
        let app = getApp();
        this.showLoading();
        let data = {
            access_token: app.globalData.access_token,
            category_id: opt.types || '',
            page: opt.page || 1,
            perpage: 20,
        };
        let obj = {
            data,
            url: `front.system.flat_pic/pic_list`,
            success: res => {
                uni.hideLoading();
                if (res.status != 1 || !res.data || !res.data.pic_list || !res.data.pic_list.data || !res
                    .data.pic_list.data.length) {
                    if (opt.fail) {
                        opt.fail && opt.fail(res.info);
                        return
                    }
                    this.alert(res.info);
                    return
                }
                opt.success && opt.success(res.data.pic_list);
            }
        };
        this.ajax(obj);
    },

    // 上传图片
    choose_img(opt) {
        uni.chooseImage({
            count: opt.count || 1,
            sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
            sourceType: ['album'], //从相册选择
            success: res => {
                console.log('临时路径', res);
                this.showLoading();
                let list = [];
                res.tempFiles.forEach((val, idx) => {
                    if (val.size && val.size > 1048576) {
                        uni.hideLoading();
                        this.alert('图片大小超出1M，请压缩后再上传！');
                        return
                    }
                    let types = '';
                    // #ifdef H5
                    types = val.type.split('image/')[1];
                    // #endif
                    opt.url = res.tempFilePaths[idx];
                    opt.types = types;
                    opt.size = Math.ceil(val.size / 1024);
                    this.pathToBase64(opt, (imgurl) => {
                        list.push({
                            url: imgurl,
                        })
                        if (idx == res.tempFiles.length - 1) {
                            uni.hideLoading();
                            opt.success && opt.success(list)
                        }
                    })
                })
            }
        });
    },

    // 小程序压缩图片
    compress_img(opt = {}) {
        uni.compressImage({
            src: opt.url,
            quality: 50,
            success: res => {
                if (res.errMsg == "compressImage:ok") {
                    opt.success && opt.success(res.tempFilePath)
                    return
                }
                this.alert('图片压缩失败了---' + `${JSON.stringify(res)}`)
            },
            fail: res => {
                this.alert('图片压缩失败了---' + `${JSON.stringify(res)}`)
            }
        })
    },

    //压缩
    compressEvent(img, callback) {
        let canvasWidth = img.width; //图片原始宽高
        let canvasHeight = img.height;
        //图片宽高比
        let base = canvasWidth / canvasHeight;
        //宽度最大设为1024
        // if (canvasWidth > 1024) {
        //     canvasWidth = 1024;
        //     canvasHeight = Math.floor(canvasWidth / base);
        // }
        //绘制图像到画布
        let canvas = document.createElement("canvas");
        let ctx = canvas.getContext("2d");
        canvas.width = canvasWidth;
        canvas.height = canvasHeight;
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        //将画布转为base64，mimeType类型为image/jpeg，图片质量为0.3
        let dataURL = canvas.toDataURL("image/jpeg", 0.5);
        callback ? callback(dataURL) : null; //调用回调函数
    },

    // 获取图片云地址
    async newuploadImg(opt = {}, cb) {
        let app = getApp();
        let data = {
            access_token: app.globalData.access_token,
            pic: opt.base64_url ? opt.base64_url : '',
        };
        if (opt.is_temp) data.is_temp = opt.is_temp;
        if (opt.ext) data.ext = opt.ext;
        if (opt.size) data.size = opt.size;
        let obj = {
            data,
            url: `front.upload/upload_file`,
            uploadImg: 1,
            success: res => {
                uni.hideLoading();
                if (res.status != 1 || !res.data.pic_url) {
                    uni.showModal({
                        content: res.info
                    })
                    return
                }
                cb && cb(res.data.pic_url);
            },
            fail: res => {
                uni.hideLoading();
                console.log(`获取图片云地址错误！---${JSON.stringify(res)}`);
            }
        };
        xwyLib.ajax(obj);
    },

    // 图片地址转成base64
    pathToBase64(opt = {}, cb) {
        pathToBase64(opt.url)
            .then(base64 => {
                let base64_url = `${base64.split('base64,')[1]}`;
                opt.base64_url = base64_url;
                this.newuploadImg(opt, cb)
            })
            .catch(error => {
                console.error(error)
                uni.hideLoading();
                uni.showModal({
                    content: error,
                })
            })
    },

    // 根据会员号获取当前用户资料以及积分
    get_user_info_by_userid(cb, userid) {
        this.showLoading();
        let app = getApp();
        let params = {
            userid: userid || app.globalData.userInfo.id,
        };
        let obj = {
            data: params,
            url: `front.user.user/user_details_simple`,
            success: res => {
                uni.hideLoading();
                console.log('根据会员号获取当前用户资料以及积分', res);
                if (res.status != 1 || !res.data || !res.data.userinfo) {
                    this.alert(res.info)
                    return
                }
                cb && cb(res.data.userinfo);
            }
        };
        this.ajax(obj);
    },

    // 通过活动短ID获取活动长ID
    async get_long_by_short_active(short_id, cb) {
        this.showLoading();
        let params = {
            id: short_id,
        };
        let obj = {
            data: params,
            url: `front.flat.sport_step.active_list/get_long_active_id_by_short_id`,
            success: res => {
                uni.hideLoading()
                // console.log('通过短ID获取长ID', res);
                if (res.status != 1 || !res.data || !res.data.long_active_id) {
                    console.log('通过短ID获取长ID----失败', res);
                    this.alert(`通过短ID获取长ID失败；短ID：${short_id}；${JSON.stringify(res)}`)
                    return
                }

                cb && cb(res.data.long_active_id);
            }
        };
        this.ajax(obj);
    },

    // 通过活动短ID获取活动长ID
    async get_long_by_short(opt = {}) {
        this.showLoading();
        let params = {
            id: opt.id,
            types: opt.types || 1,
        };
        let obj = {
            data: params,
            url: `front.user.wechat.short_long/get_short_long_id`,
            success: res => {
                uni.hideLoading()
                // console.log('通过短ID获取长ID', res);
                if (res.status == 1 && res.data?.exam_details?.exam_id) {
                    opt.success && opt.success(res.data.exam_details.exam_id);
                    return
                }
                console.log('通过短ID获取长ID----失败', res);
                this.alert(`通过短ID获取长ID失败；短ID：${opt.id}；${JSON.stringify(res)}`)
            }
        };
        this.ajax(obj);
    },

    // 获取二维码
    async get_qrcode_post(opt, cb) {
        this.showLoading();
        let params = {
            scene: opt.scene ? opt.scene : '',
            page: opt.path ? opt.path : '',
        };

        if (opt.width) params.width = opt.width;

        let obj = {
            data: params,
            url: `front.user.wechat.qrcode/create_qr_code`,
            success: res => {
                uni.hideLoading()
                if (res.status != 1 || !res.data || !res.data.qr_code_pic) {
                    this.alert(res.info);
                    return
                }

                cb && cb(res.data.qr_code_pic);
            },
            fail: res => {
                this.alert(`生成二维码失败!---${JSON.stringify(res)}`);
            }
        };
        this.ajax(obj);
    },

    // 获取题库详情
    async get_question_details(opt = {}) {
        this.showLoading();
        let params = {
            id: opt.id || '',
        };
        let obj = {
            data: params,
            url: `front.flat.exam.questionBank/get_question_bank_details`,
            success: res => {
                uni.hideLoading();
                if (res.status != 1) {
                    opt.fail && opt.fail(res);
                    return
                }
                opt.success && opt.success(res.data);
            },
            fail: res => {
                console.log('获取详情失败fail', res);
            }
        };
        this.ajax(obj);
    },

    // 获取题库列表
    async get_question_list(opt = {}) {
        this.showLoading();
        opt.before && opt.before();

        opt.data.sort_num = 2;

        let obj = {
            data: opt.data,
            url: `front.flat.exam.questionBank/get_question_bank_list`,
            success: res => {
                uni.hideLoading();
                if (res.status != 1 || !res.data || !res.data.data || !res.data.data.length) {
                    opt.fail && opt.fail(res);
                    return
                }
                res.data.data.forEach(val => {
                    val.is_select = false;
                    val.show_content = false;

                    switch (Number(val.question_types)) {
                        case 1:
                            val.question_types_title = '单选'
                            break;
                        case 2:
                            val.question_types_title = '多选'
                            break;
                        case 3:
                            val.question_types_title = '判断'
                            break;
                        case 4:
                            val.question_types_title = '填空'
                            break;
                        case 5:
                            val.question_types_title = '简答'
                            break;
                    }

                    let answer_option = [];
                    if (this.isJSON(val.answer_option)) answer_option = JSON.parse(val.answer_option);
                    if (answer_option && answer_option.length && val.question_types != 4 && val.question_types != 5) {
                        answer_option.forEach((v, i) => {
                            v.letter = this.numberToLetter(i);
                        })
                        val.answer_option = answer_option;
                    }
                    
                    // 显示为填词题目
                    if (val.answer_option[0]?.fill_words) val.question_types_title = '填词'
                })

                opt.success && opt.success(res.data);
            }
        };
        this.ajax(obj);
    },

    // 获取分类列表
    async get_category_list(opt = {}) {
        this.showLoading();
        opt.before && opt.before();
        let data = {
            types: opt.types || 4,
            page: opt.page || 1,
            perpage: opt.perpage || 10,
        };

        if (opt.my_self) data.my_self = opt.my_self;

        let obj = {
            data,
            url: `front.user.category/category_list`,
            success: res => {
                uni.hideLoading();
                if (res.status != 1 || !res.data || !res.data.category_list || !res.data.category_list
                    .data || !res.data.category_list.data.length) {
                    opt.fail && opt.fail(res);
                    return
                }
                opt.success && opt.success(res.data.category_list);
            }
        };
        this.ajax(obj);
    },

    // 获取文章列表
    async get_article_list(opt = {}) {
        this.showLoading();
        opt.before && opt.before();

        let data = {
            page: opt.page || 1,
            perpage: opt.perpage || 10,
        };

        if (opt.keywords) data.title = opt.keywords;
        if (opt.my_self) data.my_self = opt.my_self;
        if (opt.category_id) data.category_id = opt.category_id;

        let obj = {
            data,
            url: `front.news/news_article_list`,
            success: res => {
                uni.hideLoading();
                if (res.status == 1 && res?.data?.person_list?.data?.length) {
                    opt.success && opt.success(res.data);
                    return
                }
                opt.fail && opt.fail(res);
            }
        };
        this.ajax(obj);
    },

    // 获得文章详情
    async get_article_details(opt = {}) {
        this.showLoading();
        opt.before && opt.before();

        let data = {
            news_id: opt.news_id || '',
        };

        let obj = {
            data,
            url: `front.news/news_details`,
            success: res => {
                uni.hideLoading();
                if (res.status != 1 || !res.data || !res.data.news_details) {
                    opt.fail && opt.fail(res);
                    return
                }
                opt.success && opt.success(res.data.news_details);
            }
        };
        this.ajax(obj);
    },

    // 删除用户上传的图片
    cancel_user_pic(opt = {}) {
        if (!opt.ids) {
            this.alert('请指定要删除的文章！');
            return
        }
        this.showLoading('删除中...');
        let data = {
            ids: opt.ids || '',
        };
        let obj = {
            data,
            url: `front.upload/del_pic`,
            success: res => {
                uni.hideLoading();
                if (res.status != 1) {
                    this.alert(res.info);
                    return
                }
                this.showToast(res.info);
                opt.success && opt.success(res);
            }
        };
        this.ajax(obj);
    },

    // 删除文章
    cancel_article(opt = {}) {
        if (!opt.ids) {
            this.alert('请指定要删除的文章！');
            return
        }
        this.showLoading('删除中...');
        let data = {
            ids: opt.ids || '',
        };
        let obj = {
            data,
            url: `front.news/news_del`,
            success: res => {
                uni.hideLoading();
                if (res.status != 1) {
                    this.alert(res.info);
                    return
                }
                this.showToast(res.info);
                opt.success && opt.success(res);
            }
        };
        this.ajax(obj);
    },

    // 获取考卷列表
    async get_exam_papers_list(opt = {}) {
        this.showLoading();
        opt.before && opt.before();

        let data = {
            page: opt.page || 1,
            perpage: opt.perpage || 10,
        };

        if (opt.active_id) data.active_id = opt.active_id;
        if (opt.keywords) data.keywords = opt.keywords;
        if (opt.my_self) data.my_self = opt.my_self;
        if (opt.is_public) data.is_public = opt.is_public;
        if (opt.userid) data.userid = opt.userid;

        let obj = {
            data,
            url: `front.flat.exam.examPapers/get_exam_papers_list`,
            success: res => {
                uni.hideLoading();
                if (res.status != 1 || !res.data || !res.data.data || !res.data.data.length) {
                    opt.fail && opt.fail(res);
                    return
                }
                opt.success && opt.success(res.data);
            }
        };
        this.ajax(obj);
    },

    // 获得考卷详情
    async get_exam_papers_details(opt = {}) {
        this.showLoading();
        opt.before && opt.before();

        let data = {
            exam_id: opt.exam_id || '',
        };

        if (opt.question_details) data.question_details = opt.question_details;

        let obj = {
            data,
            url: `front.flat.exam.examPapers/get_exam_papers_details`,
            success: res => {
                uni.hideLoading();
                if (res.status != 1 || !res.data) {
                    opt.fail && opt.fail(res);
                    return
                }
                opt.success && opt.success(res.data);
            }
        };
        this.ajax(obj);
    },

    // 考卷出题设置
    exam_answer_conf_set(opt = {}) {
        this.showLoading();
        if (!opt.exam_id) {
            this.alert('请指定考卷id!');
            return
        }
        let data = {
            exam_id: opt.exam_id,
            is_rand_question: opt.is_rand_question || 0, // 0固定出题， 1随机出题
        };

        if (opt.question_rules_conf) data.question_rules_conf = opt.question_rules_conf;

        let obj = {
            data,
            url: `front.flat.exam.examPapers/set_exam_answer_conf`,
            success: res => {
                uni.hideLoading();
                if (res.status != 1) {
                    opt.fail && opt.fail(res);
                    return
                }
                opt.success && opt.success(res);
            }
        };
        this.ajax(obj);
    },

    // 考卷conf设置
    exam_conf_set(opt = {}) {
        this.showLoading();
        if (!opt.exam_id) {
            this.alert('请指定考卷id!');
            return
        }
        let data = {
            exam_id: opt.exam_id,
            conf: opt.conf,
        };

        let obj = {
            data,
            url: `front.flat.exam.examPapers/set_exam_conf`,
            success: res => {
                uni.hideLoading();
                if (res.status != 1) {
                    opt.fail && opt.fail(res);
                    return
                }
                opt.success && opt.success(res);
            }
        };

        if (opt.toprank_closed) data.toprank_closed = opt.toprank_closed;
        if (opt.look_back_closed) data.look_back_closed = opt.look_back_closed;
        if (opt.creator_closed) data.creator_closed = opt.creator_closed;
        if (opt.exam_password) data.exam_password = opt.exam_password;

        this.ajax(obj);
    },

    // 检查考卷密码是否正确
    check_exam_password(opt = {}) {
        this.showLoading();
        let data = {
            active_id: opt.active_id,
            password: opt.password,
        };
        let obj = {
            data,
            url: `front.user.web.login/check_user_password`,
            success: res => {
                uni.hideLoading();
                console.log('检查考试密码是否输入正确', res);
                if (res.status == 1 && res?.data?.password_encrypt) {
                    let password_encrypt = res.data.password_encrypt;
                    if (password_encrypt == opt.exam_password) {
                        opt.success && opt.success(res)
                    } else {
                        opt.fail && opt.fail();
                    }
                    return
                }
                opt.fail && opt.fail();
            }
        };
        this.ajax(obj)
    },


    // 获取考试时的题目
    get_answer_question_list(opt = {}) {
        this.showLoading();
        opt.before && opt.before();

        let data = {};

        if (opt.exam_id) data.exam_id = opt.exam_id;
        if (opt.data) {
            data = {
                ...data,
                ...opt.data
            }
        }

        let obj = {
            data,
            url: `front.flat.exam.userAnswer/exam_question_list`,
            success: res => {
                uni.hideLoading();
                if (res.status != 1 || !res.data.question_list || res.data.question_list == null) {
                    opt.fail && opt.fail(res);
                    return
                }
                res.data.question_list = this.decodeJson(res.data.question_list);
                if (!this.isArray(res.data.question_list)) {
                    opt.fail && opt.fail(res);
                    return
                }
                res.data.question_list.forEach(val => {
                    if (typeof val.answer_option === 'string') {
                        try {
                            val.answer_option = JSON.parse(val.answer_option)
                        } catch {}
                    }
                })
                opt.success && opt.success(res.data);
            }
        };
        this.ajax(obj);
    },

    // 获取考卷排行榜分类
    get_exam_rank_category(opt = {}) {
        this.showLoading();
        let obj = {
            data: {
                exam_id: opt.exam_id,
            },
            url: `front.flat.exam.exam_top_rank/exam_top_rank_category_list`,
            success: res => {
                uni.hideLoading();
                if (res.status == 1 && res?.data?.top_rank_category_list?.length) {
                    opt.success && opt.success(res.data);
                    return
                }
                opt.fail && opt.faill(res);
            },
            fail: res => {
                uni.hideLoading();
            }
        };
        this.ajax(obj);
    },

    // 获取考卷排行榜
    get_exam_ranking_list(opt = {}) {
        this.showLoading();
        opt.before && opt.before();

		if (opt.get_max) {
			const data = {
				exam_id: opt.exam_id,
				page: opt.page || 1,
				perpage: opt.perpage || 10,
				types: 2
			}
			this.ajax({
				url: 'front.flat.exam.exam_top_rank/exam_details_top_rank_data',
				data,
				success: res => {
				    uni.hideLoading();
				    if (res.status == 1 && res?.data?.top_rank_data?.length) {
						const list = res.data.top_rank_data
						list.forEach(v => {
							v.score = v.score || v.average_score || v.user_max_score || v.all_score
						})
						const _data = {
							current_page: data.page,
							data: list,
							is_lastpage: list.length < data.perpage,
							per_page: data.perpage,
							total: res.data.total || 0
						}
				        opt.success && opt.success(_data);
				        return
				    }
				    opt.fail && opt.fail(res);
				}
			})

			return false
		}

        let data = {
            exam_id: opt.exam_id,
            page: opt.page || 1,
            perpage: opt.perpage || 10,
        };
        
        if (opt.userid) data.userid = opt.userid
        
        let obj = {
            data,
            url: `front.flat.exam.user_answer/user_top_rank_list`, // 个人排行榜
            success: res => {
                uni.hideLoading();
                if (res.status == 1 && res?.data?.top_rank_list?.data?.length) {
                    opt.success && opt.success(res.data.top_rank_list);
                    return
                }
                opt.fail && opt.fail(res);
            }
        };
        this.ajax(obj);
    },


    // 获取考卷排行榜
    get_exam_other_ranking_list(opt = {}) {
        this.showLoading();
        opt.before && opt.before();
        let data = {
            exam_id: opt.exam_id,
            page: opt.page || 1,
            perpage: opt.perpage || 10,
            types: opt.types || 2,
        };
        let obj = {
            data,
            url: `front.flat.exam.exam_top_rank/exam_details_top_rank_data`, // 四种排行榜
            success: res => {
                uni.hideLoading();
                if (res.status == 1 && res?.data?.top_rank_data?.length) {
                    opt.success && opt.success(res.data);
                    return
                }
                opt.fail && opt.fail(res);
            }
        };
        this.ajax(obj);
    },


    // 获取活动列表
    get_activity_list(opt = {}) {
        this.showLoading();
        opt.before && opt.before();
        let data = {
            page: opt.page || 1,
            perpage: opt.perpage || 10,
        };
        if (opt.my_self) data.my_self = opt.my_self;
        if (opt.keyword) data.keyword = opt.keyword;
        if (opt.types) data.types = opt.types;
        if (opt.is_attend) data.is_attend = opt.is_attend;
        let obj = {
            data,
            url: `front.flat.sport_step.active_list/active_list`,
            success: res => {
                uni.hideLoading();
                if (res.status != 1 || !res.data || !res.data.active_list || !res.data.active_list
                    .data || !res.data.active_list.data.length) {
                    opt.fail && opt.fail(res);
                    return
                }

                opt.success && opt.success(res.data.active_list);
            }
        };
        this.ajax(obj);
    },

    // 获取题库详情
    async get_activity_details(opt = {}) {
        this.showLoading();
        let params = {
            active_id: opt.id || '',
        };
        let obj = {
            data: params,
            url: `front.flat.sport_step.active_list/active_details`,
            success: res => {
                uni.hideLoading();
                if (res.status != 1) {
                    opt.fail && opt.fail(res);
                    return
                }
                opt.success && opt.success(res.data);
            },
            fail: res => {
                console.log('获取详情失败fail', res);
            }
        };
        this.ajax(obj);

    },

    // 获取活动点位列表
    get_activity_point_list(opt = {}) {
        if (!opt.close_load) this.showLoading('加载活动关卡中...');
        let data = {
            active_id: opt.active_id,
        };
        let obj = {
            data,
            url: `front.flat.sport_step.active_list/active_map_point_list`,
            success: res => {
                uni.hideLoading();
                console.log('获取点位列表', res);
                if (res.status == 1 && res?.data?.map_point_list?.length) {
                    opt.success && opt.success(res.data.map_point_list)
                    return
                }
                opt.fail && opt.fail(res)
            },
            fail: res => {
                uni.hideLoading();
            }
        };
        this.ajax(obj);
    },

    // 点位绑定的考卷列表以及当前用户的解锁情况-哪些考卷已解锁-哪些待解锁
    get_activity_point_exam_list(opt = {}) {
        if (!opt.close_load) this.showLoading('加载中...');
        let data = {
            active_id: opt.active_id,
        };
        if (opt.get_types) data.get_types = opt.get_types;
        let obj = {
            data,
            url: `front.flat.sport_step.active_list/point_exam_list`,
            success: res => {
                uni.hideLoading();
                console.log('获取点位绑定的考卷列表 以及当前用户的解锁情况', res);
                if (res.status == 1 && res?.data?.point_exam_list && this.isArray(res?.data
                        ?.point_exam_list)) {
                    opt.success && opt.success(res.data)
                    return
                }
                opt.fail && opt.fail(res)
            },
            fail: res => {
                uni.hideLoading();
            }
        };
        this.ajax(obj);
    },


    // 添加/修改活动点位
    edit_activity_point(opt = {}) {
        let data = opt.data || {};
        let obj = {
            data,
            url: `front.flat.sport_step.admin/add_update_map_point`,
            success: res => {
                if (res.status != 1) {
                    this.alert(res.info);
                    return
                }
                if (opt.close_alert) {
                    opt.success && opt.success(res);
                } else {
                    this.showToast(res.info)
                    setTimeout(() => {
                        opt.success && opt.success(res);
                    }, 1000)
                }
            }
        };
        this.ajax(obj);
    },

    // 删除点位
    cancel_activity_point(opt = {}) {
        if (!opt.ids) {
            this.alert('请指定要删除的关卡！');
            return
        }
        this.showLoading('删除中...');
        let data = {
            ids: opt.ids || '',
            active_id: opt.active_id,
        };
        let obj = {
            data,
            url: `front.flat.sport_step.admin/del_map_point`,
            success: res => {
                uni.hideLoading();
                if (res.status != 1) {
                    this.alert(res.info);
                    return
                }
                this.showToast(res.info);
                opt.success && opt.success(res);
            }
        };
        this.ajax(obj);
    },

    // 获取点位详情
    get_activity_point_details(opt = {}) {
        if (!opt.id) {
            this.alert('未获取到关卡ID！');
            return
        }
        this.showLoading('加载中...')
        let data = {
            id: opt.id,
        };
        let obj = {
            data,
            url: `front.flat.sport_step.active_list/map_point_details`,
            success: res => {
                uni.hideLoading();
                if (res.status == 1 && res?.data?.map_point_details) {
                    opt.success && opt.success(res?.data?.map_point_details);
                    return
                }
                opt.fail && opt.fail(res);
            },
            fail: res => {
                this.alert(JSON.stringify(res));
            }
        };
        this.ajax(obj);
    },

    // 获取活动报名人员
    get_activity_ranking(opt = {}) {
        this.showLoading();
        if (!opt.active_id) {
            this.alert('请指定活动ID！');
            return
        }
        opt.before && opt.before();
        let data = {
            page: opt.page || 1,
            perpage: opt.perpage || 10,
            active_id: opt.active_id || '',
        };

        if (opt.order_by) data.order_by = opt.order_by;

        let obj = {
            data,
            url: `front.flat.sport_step.admin/active_attend_user_list`,
            success: res => {
                uni.hideLoading();
                if (res.status != 1 || !res.data || !res.data.user_list || !res.data.user_list
                    .data || !res.data.user_list.data.length) {
                    opt.fail && opt.fail(res);
                    return
                }

                opt.success && opt.success(res.data.user_list);
            }
        };
        this.ajax(obj);
    },

    // 设置最新的签名背景图
    set_sign_background(opt = {}) {
        let data = {
            active_id: opt.active_id || '',
            pic: opt.pic || '',
        };
        let obj = {
            data,
            url: `front.flat.active.sign_online.user.pic/set_sign_background`,
            success: res => {
                uni.hideLoading();
                opt.success && opt.success(res);
            },
            fail: res => {
                uni.hideLoading();
            }
        };
        this.ajax(obj);
    },

    // 获取最新的签名背景图
    get_sign_background(opt = {}) {
        let data = {
            active_id: opt.active_id || '',
        };
        let obj = {
            data,
            url: `front.flat.active.sign_online.user.pic/get_latest_sign_background`,
            success: res => {
                uni.hideLoading();
                if (res.status == 1 && res?.data?.latest_pic_details?.pic) {
                    opt.success && opt.success(res.data.latest_pic_details);
                    return
                }
                console.log('获取最新的签名背景图失败了----' + `${JSON.stringify(res)}`);
                opt.fail && opt.fail(res);
            }
        };
        this.ajax(obj);
    },

    // 判断用户是否已经参加过活动
    judge_user_is_join(opt = {}) {
        this.showLoading();
        let data = {
            active_id: opt.active_id || '',
        };
        let obj = {
            data,
            url: `front.flat.sport_step.user/user_attend_details`,
            success: res => {
                uni.hideLoading();
                if (res.status != 1 || !res.data || !res.data.user_details || res.data.user_details ==
                    null) {
                    console.log('用户还没有参加活动----' + `${JSON.stringify(res)}`);
                    opt.fail && opt.fail(res);
                    return
                }
                opt.success && opt.success(res.data.user_details);
            },
            fail: res => {
                console.log('判断用户是否已经参加过活动---fail', res);
                this.alert(JSON.stringify(res));
            }
        };
        this.ajax(obj);
    },

    // 用户报名参加活动
    user_attend_active(opt = {}) {
        this.showLoading();
        let userInfo = getApp()?.globalData?.userInfo || {}
        let name = userInfo.nickname;
        if (userInfo.truename) name = userInfo.truename;

        let arr = [{
            name: "xing_ming",
            title: "姓名",
            rules: 1,
            types: 1,
            option: [{
                "text": ""
            }],
            value: name,
        }];

        let must_submit = this.jsonEncode(arr);
        if (opt.must_submit) must_submit = opt.must_submit;

        let data = {
            active_id: opt.active_id,
            must_submit,
        };

        let obj = {
            data,
            url: `front.flat.sport_step.user/submit_attend_active`,
            success: res => {
                uni.hideLoading();
                if (res.status != 1) {
                    if (res.info) {
                        this.alert(res.info);
                    } else {
                        this.alert(JSON.stringify(res));
                    }

                    opt.fail && opt.fail(res);
                    return
                }
                opt.success && opt.success(res);
            },
            fail: res => {
                uni.hideLoading();
                console.log('报名失败了---', res);
            }
        };
        this.ajax(obj);
    },

    // 获取用户时当前第几名
    get_which_name(opt = {}) {
        this.showLoading('正在生成中...');
        let obj = {
            data: {
                active_id: opt.active_id,
            },
            url: `front.flat.active.sign_online.user.user/get_user_submit_attend_num`,
            success: res => {
                if (res.status != 1 || !res.data || res.data.submit_attend_num == null) {
                    opt.fail && opt.fail(res);
                    return
                }
                opt.success && opt.success(res.data.submit_attend_num);
            }
        };
        this.ajax(obj);
    },

    // 签名活动导出活动名单
    sign_export_excel(opt = {}) {
        let data = {
            active_id: opt.active_id,
        };
        let obj = {
            data,
            url: `front.flat.active.sign_online.admin.export/export_user_attend`,
            success: res => {
                if (res.status != 1 || !res.data || !res.data.url) {
                    opt.fail && opt.fail(res);
                    return
                }
                opt.success && opt.success(res);
            },
            fail: res => {
                console.log('签名活动导出活动名单失败！' + res);
                this.alert(JSON.stringify(res))
            }
        };
        this.ajax(obj);
    },

    // 考卷导出排行榜数据
    export_excel_from_exam_ranking(opt = {}) {
        this.showLoading('数据导出中...');
        let data = {
            exam_id: opt.exam_id,
        };
        let obj = {
            data,
            url: `front.flat.exam.export/export_exam_user_toprank_data`,
            success: res => {
                uni.hideLoading();
                console.log('考卷导出排行榜excel', res);
                if (res.status != 1 || !res.data || !res.data.url) {
                    opt.fail && opt.fail(res);
                    return
                }
                opt.success && opt.success(res);
            },
            fail: res => {
                uni.hideLoading();
                console.log('考卷导出排行榜数据失败了！' + res);
                this.alert(JSON.stringify(res))
            }
        };
        this.ajax(obj);
    },

    // 获取种树排行榜
    get_tree_ranking_list(opt = {}) {
        this.showLoading();
        if (!opt.active_id) {
            this.alert('请指定活动ID！');
            return
        }
        opt.before && opt.before();
        let data = {
            page: opt.page || 1,
            perpage: opt.perpage || 20,
            active_id: opt.active_id || '',
            top_rank_types: opt.top_rank_types || 1, // 1个人排行榜 2队伍排行榜
        };

        let obj = {
            data,
            url: `front.flat.active.plant_tree.user.user_top_rank_list/plant_tree_user_top_list`,
            success: res => {
                uni.hideLoading();
                if (res.status == 1 && res.data?.top_rank_data?.list?.data?.length) {
                    opt.success && opt.success(res.data);
                    return
                }
                opt.fail && opt.fail(res);
            }
        };
        this.ajax(obj);
    },

    cancel_question_from_exam(opt = {}) {
        if (!opt.ids) {
            this.alert(`未获取到题目ID！`);
            return
        }
        this.showLoading('正在删除中...');

        let obj = {
            data: {
                exam_id: opt.exam_id,
                ids: opt.ids,
            },
            url: `front.flat.exam.admin/del_questions_from_exam`,
            success: res => {
                uni.hideLoading();
                if (res.status != 1) {
                    opt.fail && opt.fail(res)
                    this.alert(res.info)
                    return
                }
                opt.success && opt.success(res);
            },
            fail: res => {
                uni.hideLoading();
                console.log('删除失败了', res);
            }
        };
        this.ajax(obj);
    },

    export_question_to_word(opt = {}) {
        this.showLoading('导出中...')
        let data = {
            page: opt.page || 1,
            perpage: opt.perpage || 1000,
        };

        if (opt.exam_id) data.exam_id = opt.exam_id;
        if (opt.category_id) data.category_id = opt.category_id;
        if (opt.is_public) data.is_public = opt.is_public;

        let obj = {
            data,
            url: `front.flat.exam.export/export_question_list_to_word`,
            success: res => {
                console.log('导出题目到word', res);
                uni.hideLoading();
                if (res.status != 1) {
                    this.alert(res.info);
                    opt.fail && opt.fail(res);
                    return
                }
                opt.success && opt.success(res);
            },
            fail: res => {
                this.alert(JSON.stringify(res));
            }
        };
        this.ajax(obj);
    },


    // 提现之前查看提现的提示语-收取的百分比以及自己的账户余额
    get_withdraw_tips(opt = {}) {
        this.showLoading();
        let obj = {
            data: {},
            url: `front.user.redpack.user/withdraw_tips`,
            success: res => {
                uni.hideLoading();
                if (res?.data) {
                    opt.success && opt.success(res.data);
                    return
                }
                opt.fail && opt.fail(res)
            },
            fail: res => {
                uni.hideLoading();
                this.alert(JSON.stringify(res));
            }
        };
        this.ajax(obj);
    },


    // 设置缓存，websocket用 答题对战
    set_storage(opt = {}) {
        if (!opt.name) {
            this.alert('未设置name！')
            return
        }
        this.showLoading();
        let data = {
            name: opt.name,
            value: opt.value ? this.jsonEncode(opt.value) : '',
            expire: opt.expire || '',
        }
        let obj = {
            data,
            url: `front.system.system_conf/front_set_value`,
            success: res => {
                uni.hideLoading();
                console.log('设置缓存', res);
                if (res.status == 1) {
                    opt.success && opt.success(res)
                    return
                }
                opt.fail && opt.fail(res)
            },
            fail: res => {
                uni.hideLoading();
            }
        }
        this.ajax(obj);
    },

    // 获取设置的缓存
    get_storage(opt = {}) {
        if (!opt.name) {
            this.alert('未传name！')
            return
        }
        this.showLoading();
        let data = {
            name: opt.name,
        }
        let obj = {
            data,
            url: `front.system.system_conf/front_get_value`,
            success: res => {
                uni.hideLoading();
                console.log('获取缓存', res);
                if (res.status == 1 && res?.data?.value) {
                    opt.success && opt.success(res.data.value)
                    return
                }
                opt.fail && opt.fail(res)
            },
            fail: res => {
                uni.hideLoading();
            }
        }
        this.ajax(obj);
    },


}



export default {
    ...xwyLib,
    ...login,
    ...pay,
    ...adObj,
    ...basicConfiguration,
    ...curItemPost,
}
