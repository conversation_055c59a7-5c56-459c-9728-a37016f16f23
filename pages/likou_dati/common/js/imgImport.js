let imgobj = {
    // 默认头像
    not_headimg: '/pages/likou_dati/static/avatar.jpeg',
    // 默认logo
    not_defaultimg: '/pages/likou_dati/static/default.jpg',
    // 无数据时显示
    not_dataimg: 'http://www.xinweiyun.com/weixin/public/img/noResult1013.jpg',
    // 底部tabbar
    tabbar_list: [{
            pic: "http://www.xinweiyun.com/weixin//public/wxappdarg/icon/longmarch/0.png",
            selpic: "http://www.xinweiyun.com/weixin/editor/attached/image/weixin_343/20220322/20220322160314_305.png",
            title: "首页",
            type: 8,
        },
        {
            pic: "http://www.xinweiyun.com/weixin//public/wxappdarg/icon/longmarch/7.png",
            selpic: "http://www.xinweiyun.com/weixin/editor/attached/image/weixin_343/20220322/20220322160625_385.png",
            title: "我的",
            type: 9,
        }
    ],

    // 轮播图
    swiper_list: [{
        src: 'https://7072-prod-9g723ognc40137fa-1253408216.tcb.qcloud.la/public/img/worship/slide/yjs.jpg',
    }],


    // 种树默认图
    tree_import:{
        logo: 'https://7072-prod-9g723ognc40137fa-1253408216.tcb.qcloud.la/public/img/answertree/thumbnail/thumbnail1.png',
    },

    // 签名默认图
    sign_import:{
        logo: 'https://7072-prod-9g723ognc40137fa-1253408216.tcb.qcloud.la/public/img/signature/signature/thumbnail.jpg',
    },


    // 考试模板 标题背景
    exam_titlebg: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/tag/mb.png',
    exam_result: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/tag/results.png',

    vip_img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/tag/vip.png',

    course_bgImg: `https://prod-0g479j60184f120d-1304148175.tcloudbaseapp.com/web/wx-cloud-api/pages/active/public/image/book.png`,

    luckyNormalImg: `https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/window/xiexiehuigu.png`,
}


export default {
    ...imgobj,
}
