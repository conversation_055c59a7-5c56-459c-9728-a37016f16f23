<template>
    <view class='content'>
        <!-- 考卷题目和总题数 -->
        <view class="score-box">
            <view class="title">
                {{exam_name}}
            </view>
            <view v-if="score_details.total" class="all_question_num gray_color">
                共
                <text>{{score_details.total.all_question}}</text>
                题
            </view>
            <view v-if="score_details.score >= 0" class="score">
                <text class="red_color">{{score_details.score ? parseFloat(score_details.score).toFixed(2):0}}</text>
                <text>分</text>
            </view>
            <view v-if="score_details.total" class="right_or_wrong f-j-sb-a-c">
                <view class="top-item">
                    <view class="num gre_color">
                        {{score_details.total.right}}
                    </view>
                    <view class="name gray_color">
                        答对
                    </view>
                </view>
                <view class="top-item">
                    <view class="num red_color">
                        {{score_details.total.wrong}}
                    </view>
                    <view class="name gray_color">
                        答错
                    </view>
                </view>
                <view class="top-item">
                    <view class="num">
                        {{score_details.total.right_percent}}%
                    </view>
                    <view class="name gray_color">
                        正确率
                    </view>
                </view>
            </view>
        </view>

        

        <!-- 分类 切换错题和全部题目 -->
        <view class="category-box">
            <liuyuno-tabs v-if="types_list && types_list.length" :tabData="types_list" :defaultIndex="0"
                @tabClick='change_types' :config="defaultConfig" />
        </view>

        <!-- 题目列表 -->
        <view class="question-box">
            <view v-if="types_idx == 0 || (types_idx == 1 && item.question_error)" class="question-list"
                :class="question_list.length - 1 == index?'':'gray_bdrbdashed'" v-for="(item,index) in question_list"
                :key="index">
                <view class="question-title f">
                    <view class="left" :style="{'min-width': show_artificial?'calc(100% - 5rem)':'100%'}">
                        <text class="gray_color">{{index+1}}、[{{item.question_details.question_types_title}}]</text>
                        <text>
                            {{item.question_details.title}}
                        </text>
                        <!-- 图片 -->
                        <text v-if="item.question_details.pic_list && item.question_details.pic_list.length"
                            class="blue_color" style="padding: 0 .5rem;"
                            @click.stop="previewImage(item.question_details)">[图片]</text>
                        <!-- 此题的分数 -->
                        <text
                            v-if="item.admin_set_score && item.admin_set_score.score">（此题：{{item.admin_set_score.score?parseFloat(item.admin_set_score.score).toFixed(2):0}}分）</text>
                        <text
                            v-if="(!item.admin_set_score || !item.admin_set_score.score) && item.score">（此题：{{item.score?parseFloat(item.score).toFixed(2):0}}分）</text>
                    </view>
                    <view v-if="show_artificial" class="change_score f">
                        <input type="digit" v-model="item.compute_score" placeholder="输入需要修改的分数"
                            @input="change_all_list_score(index)" />
                        <span class="memo_text">分</span>
                    </view>
                </view>
                <view v-if="item.question_details.answer_option && item.question_details.answer_option.length"
                    class="answer-option-box">
                    <view v-if="item.question_details.question_types < 4" class="answer-option-list f-a-c"
                        v-for="(itm,idx) in item.question_details.answer_option" :key="idx">
                        <view class="letter-box f-j-a-c" :class="itm.bg_color">
                            <text
                                v-if="(!itm.is_select && !itm.is_right) || (itm.is_right && !itm.is_select)">{{itm.letter}}</text>
                            <i v-if="itm.is_select && itm.is_right" class="iconfont f-j-a-c">&#xe6ad;</i>
                            <i v-if="itm.is_select && !itm.is_right" class="iconfont f-j-a-c"
                                style="font-size: 1rem">&#xe69a;</i>
                        </view>
                        <view class="text" :class="itm.fz_color">
                            {{itm.text}}
                        </view>
                    </view>
                </view>
                <view class="question-item">
                    <view class="title gray_color f-j-sb-a-c">
                        <span>[答案]</span>
                        <view v-if="show_artificial" class="score-btn f-a-c">
                            <span>快捷阅卷：</span>
                            <span class="btn gre_color" @click.stop="score_all_list_right(index)">
                                <i class="iconfont">&#xe691;</i>
                            </span>
                            <span class="btn red_color" @click.stop="score_all_list_err(index)">
                                <i class="iconfont">&#xe692;</i>
                            </span>
                        </view>
                    </view>
                    <view v-if="item.question_details.question_types < 4" class="box">
                        <view class="my_answer">
                            我的答案：
                            <text
                                :class="item.user_answer_str == item.right_answer_str?'gre_color':'red_color'">{{item.user_answer_str?item.user_answer_str:'未作答'}}</text>
                        </view>
                        <view class="right_answer">
                            正确答案：
                            <text class="gre_color">{{item.right_answer_str}}</text>
                        </view>
                    </view>
                    <view v-if="item.question_details.question_types == 4" class="box">
                        <view class="blank_box" v-for="(itm,idx) in item.user_answer">
                            <view class="blank_top">
                                填空{{idx+1}}：
                                <text :class="itm.fz_color">{{itm.text?itm.text:'未作答'}}</text>
                            </view>
                            <view class="blank_bottom">
                                正确答案：
                                <view class="" v-for="(it,ix) in itm.is_right">
                                    <text class="gre_color">{{it.text}}</text>
                                    <text v-if="ix < itm.is_right.length - 1" style="padding: 0 .5rem;">或</text>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <view v-if="item.question_details && item.question_details.explain_text" class="question-item">
                    <view class="title gray_color">
                        [答案解析]
                    </view>
                    <view class="box gray_color">
                        {{item.question_details.explain_text}}
                    </view>
                </view>

                <view class="memo_text f-j-e-a-c" style="padding-bottom: .5rem;">
                    <view class="f-j-e-a-c">
                        <i class="iconfont" style="display: inline-block;padding-right: .2rem;"
                            @click.stop="change_collect(item.question_details._id)">&#xe686;</i>
                        <text class="collect-text" @click.stop="change_collect(item.question_details._id)">收藏此题</text>
                    </view>
                </view>

               
            </view>
        </view>

        <!-- 底部操作栏 -->
        <view class="tabbar-box f">
            <view class="left-box f-a-c">
                <view class="item" @click.stop="skip_page(8)">
                    <i class="iconfont f-j-a-c">&#xe638;</i>
                    <view class="text f-j-a-c">
                        首页
                    </view>
                </view>
                <view v-if="is_master && exam_details.waiting_persont" class="item"
                    @click.stop="show_artificial = !show_artificial">
                    <i v-if="!show_artificial" class="iconfont f-j-a-c">&#xe95d;</i>
                    <i v-if="show_artificial" class="iconfont f-j-a-c">&#xe79f;</i>
                    <view class="text f-j-a-c">
                        {{show_artificial?'退出阅卷':'人工阅卷'}}
                    </view>
                </view>
            </view>
            <view class="right-btn">
                <view v-if="!show_artificial" class="btn blue_bgcolor f-j-a-c" @click.stop="skip_page(2)">
                    考卷首页
                </view>
                <view v-if="show_artificial" class="btn blue_bgcolor f-j-a-c" @click.stop="submit_artificial">
                    提交分数
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    let app = getApp();
    import liuyunoTabs from "@/pages/likou_dati/components/liuyuno-tabs/liuyuno-tabs.vue"
    export default {
        components: {
        	liuyunoTabs
        },
        data() {
            return {
                params: {},
                is_master: false, // 是否管理员
                userInfo: {},

                // 题目分类
                defaultConfig: {
                    // 要显示的key
                    key: 'label',
                    fontSize: '32',
                    // 字体颜色
                    color: '#313131',
                    // 激活字体颜色
                    activeColor: '#007AFF',
                    // 下划线左右边距，文字宽度加边距，如果设置了itemWidth则为itemWidth加边距 rpx
                    underLinePadding: 0,
                    // 下划线高度 rpx
                    underLineHeight: 4,
                    // 下划线颜色
                    underLineColor: '#007AFF',
                },
                types_list: [{
                        label: '全部',
                        value: 0,
                    },
                    {
                        label: '错题',
                        value: 1,
                    },
                ],
                types_idx: 0,

                question_list: [],
                err_list: [], // 错误地址

                score_details: {},
                exam_details: {},
                exam_name: '',

                page: 1,
                is_load: false,
                lastPage: false, // 是否最后一页

                show_artificial: false, // 阅卷状态 true为开启阅卷
            }
        },
        onLoad(options) {
            // console.log('获取的参数options', options);
            this.params = options;
            this.xwyLib.setBarTitle('答案与解析')
            this.xwyLib.init(() => {
                this.userInfo = app.globalData.userInfo;
                this.init();
            })
        },
        methods: {
            async init() {
                this.get_answer_result();
                // this.get_question_list();
            },

            // 获取答题结果
            async get_answer_result(page = 1) {
                if (this.is_load == true) {
                    return
                }
                this.is_load = true;
                this.xwyLib.showLoading();
                if (page == 1) {
                    this.exam_name = '';
                    this.score_details = {};
                    this.question_list = [];
                }
                let params = {
                    score_id: this.params.score_id,
                    perpage: 500,
                    page,
                };
                let obj = {
                    data: {
                        action: 'front/exam/user/user_submit_answer_question_list',
                        params,
                    },
                };
                let res = await this.xwyLib.cloudAjax(obj).catch(err => {
                    uni.hideLoading();
                    this.xwyLib.alert(JSON.stringify(err));
                });
                this.is_load = false;
                uni.hideLoading();
                // console.log('获取答题结果', res);
                if (res.status != 1 || !res.data.score_details) {
                    this.xwyLib.alert(res.info);
                    return
                }

                // 判断管理员
                let uid = this.userInfo._id;
                if (uid == res.data.exam_details.user_id) {
                    this.is_master = true
                }

                if (res.data.score_details.used_time) {
                    let seconds = Math.ceil(res.data.score_details.used_time / 1000);
                    let time = this.xwyLib.secondsToHoursMinutesSeconds(seconds);
                    res.data.score_details.used_time = time;
                }

                this.exam_name = res.data.exam_name;
                this.score_details = res.data.score_details;
                // res.data.exam_details.ad_closed = 1;
                this.exam_details = res.data.exam_details;
                this.lastPage = res.data.user_submit_answer.lastPage;
                this.get_question_list(res.data.user_submit_answer.list, page);
            },

            // 获取题目列表
            get_question_list(list, page) {
                if (Array.isArray(list)) {
                    let after_list = [];
                    list.forEach(val => {
                        val.question_details.question_types_title = this.xwyLib.get_question_types_text(val
                            .question_details.question_types);

                        // 题目是否做错了
                        val.question_error = false;

                        // 获取题目分数
                        val.compute_score = 0;
                        if (val.admin_set_score && val.admin_set_score.score) val.compute_score = val
                            .admin_set_score.score || 0;
                        if ((!val.admin_set_score || !val.admin_set_score.score) && val.score) val
                            .compute_score =
                            val.score || 0;

                        // 单选 多选 判断
                        if (val.question_details.answer_option?.length && this.xwyLib.judge_types(val
                                .question_details.question_types)) {
                            let right_answer_str = '',
                                user_answer_str = '';
                            val.question_details.answer_option.forEach((v, i) => {
                                v.letter = this.xwyLib.numberToLetter(i)
                                if (v.is_right) {
                                    right_answer_str += `${v.letter},`;
                                }
                                if (v.is_right) {
                                    v.bg_color = 'gre_bgcolor';
                                    v.fz_color = 'gre_color';
                                }
                                val.user_answer.forEach(value => {
                                    if (value == i) {
                                        v.is_select = true;
                                        user_answer_str += `${v.letter},`;

                                        if (!v.is_right) {
                                            v.bg_color = 'red_bgcolor';
                                            v.fz_color = 'red_color';
                                            val.compute_score = 0;
                                            val.question_error = true;
                                        }
                                    }
                                })
                            })

                            if (right_answer_str) {
                                val.right_answer_str = right_answer_str.substr(0, right_answer_str.length - 1);
                            }
                            if (user_answer_str) {
                                val.user_answer_str = user_answer_str.substr(0, user_answer_str.length - 1);
                            } else {
                                val.compute_score = 0;
                                val.question_error = true;
                            }
                        }

                        // 填空题
                        if (val.question_details.question_types == 4 && Array.isArray(val.user_answer)) {
                            val.user_answer.forEach((v, i) => {
                                if (Array.isArray(v.is_right)) {
                                    let isTrue = false;
                                    v.is_right.forEach((is_v, is_i) => {
                                        if (is_v.text == v.text) {
                                            isTrue = true;
                                        }
                                    })
                                    if (isTrue) {
                                        v.fz_color = 'gre_color';
                                    } else {
                                        v.fz_color = 'red_color';
                                        val.compute_score = 0;
                                        val.question_error = true;
                                    }
                                }
                            })
                        }

                    })
                    after_list = [...list];
                    if (page > 1) {
                        after_list = [...this.question_list, ...list]
                    }

                    this.question_list = [...after_list];
                    this.page = Number(page) + 1
                }
                // console.log('this.question_list', this.question_list);
            },

            // 跳转到其他页面
            skip_page(e) {
                if (e == 2) {
                    e = {
                        types: 'closeUrl',
                        url: `/pages/likou_dati/pages/exam/exam_details/exam_details?exam_id=${this.score_details.exam_id}`
                    };
                }
                this.xwyLib.skip_page(e);
            },

            // 改变收藏状态
            async change_collect(id) {
                this.add_collect(id);
            },

            // 添加收藏
            async add_collect(id) {
                this.xwyLib.showLoading('收藏中...');
                let params = {
                    types: 1,
                    pid: id,
                };
                let obj = {
                    data: {
                        action: 'front/user/favorite/add',
                        params,
                    },
                };
                let res = await this.xwyLib.cloudAjax(obj).catch(err => {
                    uni.hideLoading()
                    this.xwyLib.alert(JSON.stringify(err));
                });
                // console.log('添加收藏', res);
                uni.hideLoading()
                this.xwyLib.showToast(res.info);
                if (res.status != 1) {
                    return
                }
            },

            previewImage(item) {
                let list = [];
                if (item.pic_list && item.pic_list.length) {
                    item.pic_list.forEach(val => {
                        list.push(val.url);
                    })
                }
                uni.previewImage({
                    current: 0,
                    urls: list,
                })
            },

            change_types(e) {
                if (this.types_idx == e) return
                this.types_idx = e;
                if (e == 0) {

                }
                if (e == 1) {

                }
            },

            // 修改分数
            change_all_list_score(index) {
                let details = this.question_list[index];
                if (details.admin_set_score && details.admin_set_score.score) {
                    if (details.compute_score < 0) details.compute_score = 0;
                    if (details.compute_score > details.admin_set_score.score) details.compute_score = details
                        .admin_set_score.score
                }
                if ((!details.admin_set_score || !details.admin_set_score) && details.score) {
                    if (details.compute_score < 0) details.compute_score = 0;
                    if (details.compute_score > details.score) details.compute_score = details
                        .score
                }
                this.$set(this.question_list[index], 'compute_score', details.compute_score);
            },

            score_all_list_right(index) {
                let details = this.question_list[index];
                if (details.admin_set_score && details.admin_set_score.score) this.question_list[index].compute_score =
                    details.admin_set_score.score;
                if ((!details.admin_set_score || !details.admin_set_score) && details.score) this.question_list[index]
                    .compute_score = details.score;
            },
            score_all_list_err(index) {
                this.question_list[index].compute_score = 0;
            },

            // 提交人工阅卷的分数
            submit_artificial() {
                uni.showModal({
                    content: '请确认已经阅卷完毕再提交！',
                    confirmText: '提交',
                    success: res => {
                        if (res.confirm) {
                            this.submit_artificial_post()
                        }
                    }
                })
            },

            async submit_artificial_post() {
                this.xwyLib.showLoading();

                let answer_list = [],
                    question_list = this.question_list;

                question_list.forEach((val, idx) => {
                    let opt = {
                        question_record_id: val._id,
                        score: val.compute_score || 0,
                        is_right: val.compute_score > 0 ? 1 : 0,
                    }
                    answer_list.push(opt)
                })

                let params = {
                    score_id: this.score_details._id, // 成绩单ID
                    answer_list: this.base64.baseEncode(JSON.stringify(answer_list)),
                };
                let obj = {
                    data: {
                        action: 'front/exam/admin/person_review',
                        params,
                    },
                };

                let res = await this.xwyLib.cloudAjax(obj).catch(err => {
                    this.xwyLib.alert(JSON.stringify(err));
                    uni.hideLoading();
                });
                uni.hideLoading()
                // console.log('提交审阅分数', res);
                if (res.status != 1) {
                    this.xwyLib.alert(res.info);
                    return
                }

                uni.showModal({
                    content: res.info,
                    success: response => {
                        this.skip_page(2);
                    }
                })
            },
        },


        onReachBottom() {
            if (this.lastPage) {
                return
            }
            this.get_answer_result(this.page)
        }

    }
</script>

<style scoped lang='scss'>
	@import url('@/pages/likou_dati/common/css/common.scss');
	@import url('@/pages/likou_dati/common/css/iconfont.css');
	@import url('@/pages/likou_dati/common/css/public.css');
	
    .content {
        padding-bottom: calc(60px + 1rem);
        background-color: #f8f8f8;

        .score-box {
            padding: 0 1rem;
            text-align: center;
            background-color: #fff;
            margin-bottom: 1rem;

            .title {
                font-size: 1.5rem;
                padding: 1rem 0;
            }

            .score {
                padding: 1rem 0;

                >text:first-child {
                    font-size: 3rem;
                    padding-right: .3rem;
                }
            }


            .right_or_wrong {
                padding: 0 1rem 1rem;

                .top-item {
                    text-align: center;

                    .num {
                        font-size: 1.3rem;
                        padding-bottom: .2rem;
                    }
                }
            }
        }

        .category-box {
            position: sticky;
            left: 0;
            top: 0;
            width: 100%;
            padding-bottom: .5rem;
            background-color: #fff;
        }

        .question-box {
            width: 100%;
            background-color: #fff;
            padding: 0 1rem;

            .question-list {

                .question-title {
                    padding: 1rem 0;

                    >text:first-child {
                        padding-right: .5rem;
                    }

                    .change_score {
                        min-width: 5rem;
                        padding-left: .5rem;

                        input {
                            border-bottom: 1px solid #ddd;
                            text-align: center;
                        }
                    }

                    .left {
                        min-width: calc(100% - 5rem);
                    }
                }

                .answer-option-box {
                    padding-bottom: 1rem;

                    .answer-option-list {
                        padding: .5rem 0;
                        white-space: pre-line;

                        .letter-box {
                            width: 2rem;
                            min-width: 2rem;
                            height: 2rem;
                            border-radius: 50%;
                            background-color: #eee;
                            margin-right: .5rem;

                            .iconfont {
                                font-size: 1.5rem;
                            }
                        }
                    }
                }
            }
        }

        .question-item {
            width: 100%;
            padding-bottom: 1rem;

            &:last-child {
                padding-bottom: 0;
            }

            .title {
                padding-bottom: .5rem;
            }

            .box {
                width: 100%;

                .blank_box {
                    width: 100%;
                    padding-bottom: .5rem;

                    .blank_top {
                        padding-bottom: .2rem;
                    }

                    .blank_bottom {

                        >view {
                            display: inline-block;
                        }
                    }
                }

                .my_answer {
                    padding-bottom: .5rem;
                }
            }

            .score-btn {

                .btn {
                    padding: 0 1rem;

                    .iconfont {
                        font-size: 1.2rem;
                    }
                }
            }
        }

        .question-explain {
            padding: .5rem;
        }
    }
</style>
