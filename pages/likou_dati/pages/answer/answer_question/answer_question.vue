<template>
    <view class='content' v-cloak>
        <template v-if="data && show_module">
            <answer-template :template_id="template_id" :tpl_obj="templateObj" :options="data" :params="params" :hideNum="hideNum">
            </answer-template>
        </template>
    </view>
</template>

<script>
import answerTemplate from "@/pages/likou_dati/pages/answer/answer-template/answer-template"
const app = getApp();
export default {
    components: {
        answerTemplate
    },
    data() {
        return {
            params: {},
            data: '',
            template_id: '',

            hideNum: 0,

            templateObj: '',
            show_module: false,
        }
    },

    onShow() {
        // #ifdef MP-WEIXIN
        let systemInfo = app.globalData.systemInfo;
        if (systemInfo?.osName == 'ios') {
            uni.setKeepScreenOn({
                keepScreenOn: true
            });
        }
        // #endif
    },

    onHide() {
        this.hideNum++;
    },

    onUnload() {
        // #ifdef MP-WEIXIN
        wx.setVisualEffectOnCapture({ // 取消禁止截屏
            visualEffect: 'none',
        })
        // #endif
    },

    onLoad(options) {
        // #ifdef MP-WEIXIN
        // 保持屏幕常亮
        uni.setKeepScreenOn({
            keepScreenOn: true
        });
        // #endif
        this.params = options;
        this.xwyLib.init(() => {
            this.userInfo = app.globalData.userInfo;
            this.init();
        })
    },
    methods: {
        async init() {
            this.xwyLib.setBarTitle('答题中')
            this.get_answer_question_list();
        },

        // 获取题目列表
        get_answer_question_list() {
            let obj = {
                exam_id: this.params.exam_id,
                success: res => {
                    // #ifdef MP-WEIXIN
                    wx.enableAlertBeforeUnload?.({
                        message: "正在答题，是否提交本次答题成绩并返回？",
                    })
                    // #endif
                    if (res?.exam_details?.conf?.answer_template_id) {
                        this.template_id = res.exam_details?.conf.answer_template_id;
                        this.get_template_obj()
                    }
                    this.data = res;
                    this.show_module = true

                    // #ifdef MP-WEIXIN
                    if (res?.exam_details?.conf?.anti_cheating?.show == 1) {
                        wx.setVisualEffectOnCapture({ // 禁止截屏
                            visualEffect: 'hidden',
                        })
                    }
                    // #endif

                },
                fail: res => {
                    this.is_submit_answer = true;
                    this.xwyLib.showModal(res.info, {
                        success: response => {
                            this.xwyLib.back_previous_page();
                        }
                    })
                }
            };

            if (this.params.point_id) {
                obj.data = {
                    point_id: this.params.point_id
                }
            }

            this.xwyLib.get_answer_question_list(obj);
        },


        get_template_obj() {
            this.xwyLib.get_json_conf_set({
                name: `answer_template_list_${this.xwyLib.ext_conf.who}`,
                success: res => {
                    if (res?.answer_template_list?.length) {
                        const list = res.answer_template_list;
                        const obj = list.find(val => val.id == this.template_id)

                        // obj.main_color = '#8C090F'	// 顶部状态栏 和 计时模块颜色
                        // obj.auxi_color = '#970A10'	// 底部答题卡模块背景色
                        // obj.fz_color = '#74442A'	// 题目类型 题目数量 题目标题颜色
                        // obj.sel_color = '#da3636'	// 选择题 选中题目后的颜色
                        // obj.pid = 15;	// 在哪个模板的基础上修改 目前只支持模板15
                        // obj.id = 19
                        // obj.titleUrl = `https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/exam_template/yx.png` // 题目类型，数量，标题背景图
                        // obj.bgUrl = `https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/exam_template/yx_bg.jpg`	// 页面背景图

                        if(obj) this.templateObj = obj;
                        this.show_module = true
                        return
                    }
                    this.show_module = true
                },
                fail:res=> this.show_module = true
            })
        },


        answerSuccess(score) {
            // 任务闯关活动，答题成功后刷新活动状态(任务是否已完成，关卡是否已完成)
            this.getOpenerEventChannel?.()?.emit?.('success', score)
        },

        examPrizeLightUp() {
            // 健步走答题勋章点亮，刷新勋章页面数据
            this.getOpenerEventChannel?.()?.emit?.('lightUp')
        }
    }
}
</script>

<style scoped lang='scss'>
@import url('@/pages/likou_dati/common/css/common.scss');
@import url('@/pages/likou_dati/common/css/iconfont.css');
@import url('@/pages/likou_dati/common/css/public.css');

.content {
    height: 100vh;
    padding-bottom: 60px;
}
</style>
