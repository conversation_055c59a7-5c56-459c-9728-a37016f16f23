<template>
	<view class='content'>
		<!-- 默认模板 -->
		<defaultTemplate v-if="!template_id || template_id == 0" :data="options" :params="params" :hideNum="hideNum">
		</defaultTemplate>
		<!-- :ID="template_id" -->
		<templateX v-if="template_id && template_id > 0" :tplID="template_id" :tpl_obj="tpl_obj" :data="options" :params="params" :hideNum="hideNum">
		</templateX>
	</view>
</template>

<script>
	import defaultTemplate from "@/pages/likou_dati/pages/answer/answer-template/template/default.vue";
	import templateX from "@/pages/likou_dati/pages/answer/answer-template/template/templateX.vue";

	const app = getApp();
	export default {
		components: {
			defaultTemplate,
			templateX,
		},
		data() {
			return {}
		},
		props: {
			template_id: {
				default: -1,
			},
			options: {
				default: {},
			},
			params: {
				default: {},
			},
			hideNum: {
				default: 0,
			},
			tpl_obj: {
				default: {}
			},
		},
		watch:{
			tpl_obj: {
				handler(){},
				deep: true,
			},
		},
      methods: {
        answerSuccess(score) {
            this.$parent.answerSuccess(score)
        },

          examPrizeLightUp() {
              // 健步走答题勋章点亮，刷新勋章页面数据
              this.$parent.examPrizeLightUp()
          }
      }
	}
</script>
