
@import url('@/pages/likou_dati/common/css/common.scss');
@import url('@/pages/likou_dati/common/css/iconfont.css');
@import url('@/pages/likou_dati/common/css/public.css');

.question-box {
    width: 100%;
    min-height: 100vh;
    padding: 1rem 1rem 90px 1rem;
}

.question-box .question_item {
    overflow-y: auto;
}

.question-box .question_item .question-list {
}

.bottom .answer-option-box .blank_list .blank_left {
    /* width: 6.5rem; */
	max-width: 6.5rem
}

.bottom .answer-option-box .blank_list .blank_right {
    width: calc(100% - 6.5rem);
}

.bottom .answer-option-box .answer-option-list {
    padding: .3rem 0;
    white-space: pre-line;
}

.bottom .answer-option-box .answer-option-list .letter-box {
    width: 2rem;
    min-width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background-color: #eee;
    margin-right: .5rem;
}

.bottom .pic-box .pic-list:nth-child(4n) {
    margin-right: 0;
}

.bottom .pic-box .pic-list {
    width: calc((100% - 3rem) / 4);
    margin-right: 1rem;
    margin-bottom: 1rem;
}

.bottom .pic-box .pic-list image {
    width: 100%;
    height: auto;
    border-radius: .1rem;
}


.tabbar-box .right-btn .btn {
    width: 45%;
    border-radius: .2rem;
    font-size: 1rem;
    padding: .4rem 0;
    text-align: center;
}

.card-box {
    position: fixed;
    left: 50%;
    top: 0;
    transform: translateX(-50%);
    width: 100%;
    max-width: 640px;
    height: 100%;
    z-index: 100;
    background-color: rgba(0, 0, 0, .7);
}

.card-box .card-content {
    position: relative;
    background-color: #fff;
    width: 100%;
    height: 60vh;
    overflow-y: auto;
}

.card-box .card-content .top {
    width: 100%;
    height: calc(100% - 60px);
    overflow-y: auto;
    padding: 1rem;
}

.card-box .card-content .top .card-list {
    width: 3.5rem;
    height: 3.5rem;
    margin-right: 1rem;
    margin-bottom: 1rem;
    display: inline-block;
}

.card-box .card-content .top .card-list .name {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: #F8F8F8;
}

.card-box .card-content .bottom {
    width: 100%;
    height: 60px;
    border-top: 1px solid #eee;
    padding: 0 1rem;
}

.card-box .card-content .bottom>view {
    width: 100%;
    border-radius: 10rem;
    padding: .5rem 0;
    text-align: center;
}
