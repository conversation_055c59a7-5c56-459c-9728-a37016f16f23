const conf = {
	template_list:[
		{
			id: 0,
			main_color: '#fff', // 主色
			auxi_color: '#fff', // 辅色
			suc_color: '#00B26A', // 正确颜色
			err_color: '#FF454D', // 错误颜色
			sel_color: '#007AFF', // 选中颜色
			arprogress: false, // 是否使用了圆形倒计时插件
		},
		{
			id: 1,
			main_color: '#e6b980', // 主色
			auxi_color: '#eacda3', // 辅色
			suc_color: '#00B26A', // 正确颜色
			err_color: '#FF454D', // 错误颜色
			sel_color: '#E68A6C', // 选中颜色
			arprogress: false, // 是否使用了圆形倒计时插件
		},
		{
			id: 2,
			main_color: '#50A7C2', // 主色
			auxi_color: '#9AD7D1', // 辅色
			suc_color: '#00B26A', // 正确颜色
			err_color: '#FF454D', // 错误颜色
			sel_color: '#9AD3D0', // 选中颜色
			arprogress: false, // 是否使用了圆形倒计时插件
		},
		{
			id:3,
			main_color: '#f794a4', // 主色
			auxi_color: '#fdd6bd', // 辅色
			suc_color: '#00B26A', // 正确颜色
			err_color: '#FF454D', // 错误颜色
			sel_color: '#F88395', // 选中颜色
			arprogress: false, // 是否使用了圆形倒计时插件
		},
		{
			id: 4,
			main_color: '#0584EB', // 主色
			auxi_color: '#0584EB', // 辅色
			suc_color: '#00B26A', // 正确颜色
			err_color: '#FF454D', // 错误颜色
			sel_color: '#0584EB', // 选中颜色
			arprogress: false, // 是否使用了圆形倒计时插件
		},
		{
			id: 5,
			main_color: '#00cdac', // 主色
			auxi_color: '#8ddad5', // 辅色
			suc_color: '#00B26A', // 正确颜色
			err_color: '#FF454D', // 错误颜色
			sel_color: '#43CFB4', // 选中颜色
			arprogress: false, // 是否使用了圆形倒计时插件
		},
		{
			id:6,
			main_color: '#505285', // 主色
			auxi_color: '#505285', // 辅色
			suc_color: '#00B26A', // 正确颜色
			err_color: '#FF454D', // 错误颜色
			sel_color: '#4EC6CB', // 选中颜色
			arprogress: true, // 是否使用了圆形倒计时插件
		},
		{
			id: 7,
			main_color: '#667eea', // 主色
			auxi_color: '#764ba2', // 辅色
			suc_color: '#00B26A', // 正确颜色
			err_color: '#FF454D', // 错误颜色
			sel_color: '#6D72DC', // 选中颜色
			arprogress: false, // 是否使用了圆形倒计时插件
		},
		{
			id: 8,
			main_color: '#473B7B', // 主色
			auxi_color: '#30D2BE', // 辅色
			suc_color: '#00B26A', // 正确颜色
			err_color: '#FF454D', // 错误颜色
			sel_color: '#4281A6', // 选中颜色
			arprogress: true, // 是否使用了圆形倒计时插件
		},
		{
			id: 9,
			main_color: '#8EC5FC', // 主色
			auxi_color: '#E0C3FC', // 辅色
			suc_color: '#00B26A', // 正确颜色
			err_color: '#FF454D', // 错误颜色
			sel_color: '#B0C3FB', // 选中颜色
			arprogress: true, // 是否使用了圆形倒计时插件
		},
		{
			id: 10,
			main_color: '#0093E9', // 主色
			auxi_color: '#80D0C7', // 辅色
			suc_color: '#00B26A', // 正确颜色
			err_color: '#FF454D', // 错误颜色
			sel_color: '#67BED2', // 选中颜色
			arprogress: true, // 是否使用了圆形倒计时插件
		},
		{
			id: 11,
			main_color: '#8BC6EC', // 主色
			auxi_color: '#95BCEA', // 辅色
			suc_color: '#00B26A', // 正确颜色
			err_color: '#FF454D', // 错误颜色
			sel_color: '#ffcf8b', // 选中颜色
			arprogress: true, // 是否使用了圆形倒计时插件
		},
		{
			id: 12,
			main_color: '#FBAB7E', // 主色
			auxi_color: '#F7CE68', // 辅色
			suc_color: '#00B26A', // 正确颜色
			err_color: '#FF454D', // 错误颜色
			sel_color: '#ffcf8b', // 选中颜色
			arprogress: true, // 是否使用了圆形倒计时插件
		},
		{
			id: 13,
			main_color: '#DA0900', // 主色
			auxi_color: '#DA0900', // 辅色
			suc_color: '#00B26A', // 正确颜色
			err_color: '#FF454D', // 错误颜色
			sel_color: '#DA0900', // 选中颜色
			arprogress: true, // 是否使用了圆形倒计时插件
		},
		{
			id: 14,
			main_color: '#DA0900', // 主色
			auxi_color: '#DA0900', // 辅色
			suc_color: '#00B26A', // 正确颜色
			err_color: '#FF454D', // 错误颜色
			sel_color: '#DA0900', // 选中颜色
			arprogress: false, // 是否使用了圆形倒计时插件
		},
		{
			id: 15,
			main_color: '#DA0900', // 主色
			auxi_color: '#DA0900', // 辅色
			suc_color: '#00B26A', // 正确颜色
			err_color: '#FF454D', // 错误颜色
			sel_color: '#f8d4ab', // 选中颜色
			arprogress: true, // 是否使用了圆形倒计时插件
		},
		{
			id: 16,
			main_color: '#da3636', // 主色
			auxi_color: '#DA0900', // 辅色
			suc_color: '#00B26A', // 正确颜色
			err_color: '#FF454D', // 错误颜色
			sel_color: '#da3636', // 选中颜色
			arprogress: false, // 是否使用了圆形倒计时插件
		},
		{ // 元宵节风格
			id: 17,
			main_color: '#da3636', // 主色
			auxi_color: '#EE281B', // 辅色
			suc_color: '#00B26A', // 正确颜色
			err_color: '#FF454D', // 错误颜色
			sel_color: '#da3636', // 选中颜色
			arprogress: false, // 是否使用了圆形倒计时插件
		},
		{ // 中秋节风格
			id: 18,
			main_color: '#BC1C22', // 主色
			auxi_color: '#9F0D0D', // 辅色
			suc_color: '#00B26A', // 正确颜色
			err_color: '#FF454D', // 错误颜色
			sel_color: '#da3636', // 选中颜色
			arprogress: false, // 是否使用了圆形倒计时插件
		}
	]
};

export default {
	...conf
}