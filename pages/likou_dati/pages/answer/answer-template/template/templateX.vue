<template>
	<view :class="[ID >=  0 ? `templateBox${ID}` : '', ID == 17 || ID == 18 ? 'templateBox15' :'']">

		<view class="">

			<view v-if="ID == 3" class="width100 f-j-a-c" :style="{'background-color': main_color}">
				<view class="width100px height100px radius50 bg-white f-d-c-j-a-c blk_shadow_02"
					style="padding-top: 43px;margin-top: -50px;">
					<view v-if="conf.answer_time && conf.answer_question_types == 1" class="top f-j-e-a-c pb_3">
						{{conf.answer_time}}分钟
					</view>
					<view v-if="conf.question_seconds && conf.answer_question_types > 1" class="top f-j-e-a-c pb_3">
						{{question_seconds}} 秒
					</view>
				</view>
			</view>

			<view v-if="ID == 4" class="width100 f-j-sb-a-c p1 colorfff" :style="{'background-color': main_color}">
				<view class="">
					<view v-if="conf.answer_time && conf.answer_question_types == 1" class="top f-j-e-a-c">
						<i class="iconfont pr_3 font1_2">&#xe697;</i>
						{{conf.answer_time}}分钟
					</view>
					<view v-if="conf.question_seconds && conf.answer_question_types > 1" class="top f-j-e-a-c">
						<i class="iconfont pr_3 font1_2">&#xe697;</i>
						{{question_seconds}} 秒
					</view>
				</view>
				<view class="">
					共 {{question_num}} 题
				</view>
			</view>

			<view v-if="ID == 16" class="width100 p1" style="height: 92px" :style="{'background-color': main_color}">
				<view class="f-j-sb-a-c colorfff">
					<view class="">
						<view v-if="conf.answer_time && conf.answer_question_types == 1" class="top f-j-e-a-c">
							<text>限时：</text>
							<text>{{conf.answer_time}}分钟</text>
						</view>
						<view v-if="conf.question_seconds && conf.answer_question_types > 1" class="top f-j-e-a-c">
							<i class="iconfont pr_3 font1_2">&#xe697;</i>
							{{question_seconds}} 秒
						</view>
					</view>
					<view class="">
						共 {{question_num}} 题
					</view>
				</view>
				<view v-if="progress_width" class="pt_7">
					<view class="width100 bg-white radius10 p_3 pl0 pr0">
						<bing-progress :width="progress_width" bpname="progress1" :value="question_idx + 1" :min="1"
							:max="question_list.length" :activeColor="main_color" noActiveColor="#fff" strokeWidth="4px"
							infoAlign="handle" handleHeight="16px" handleWidth="40px" disabled :step="1"
							infoFontSize="10px" borderRadius="50px" :showInfo="true"
							:infoEndText="`/${question_list.length}`" infoColor="#fff" handleBorderRadius="50px"
							:handleColor="main_color">
						</bing-progress>
					</view>
				</view>
			</view>

			<view class="question-box"
				:style="{'background-image': ID == 4 || ID == 13 || ID == 14 || ID == 15 || ID == 16 || ID == 17 || ID == 18 ? '' : `linear-gradient(to bottom, ${main_color} 0%, ${auxi_color} 100%)`,'background-image': ID == 13 || ID == 14 || ID == 15 || ID == 16 || ID == 17 || ID == 18 ? `url(${bgUrl})` : ''}"
				@touchstart="touch_start" @touchend="touch_end">

				<view v-if="ID == 5" class="width100 f-j-e-a-c p1">
					<view class="radius_3 coloreee p5-1" style="background-color: rgba(0, 0, 0, .3);">
						共 {{question_num}} 题
					</view>
				</view>

				<view v-if="ID == 8 || ID == 10 || ID == 11 || ID == 15 || ID == 17 || ID == 18" class="f-j-a-c"
					:class="[ID == 8 || ID == 10 ? 'pb2' : 'pb1', ID == 15 ? 'pt2' : 'pt1']">
					<arprogress v-if="percent != -1 && show_progress" :percent="percent" :percent_all="percent_all"
						:activeColor="main_color" :width="ID == 8 ? 120 : ID == 15 ? 100 : 90">
						<text v-if="conf.answer_question_types == 1" class="font1_3" :style="{'color': main_color}">
							{{conf.answer_time}}分钟
						</text>
						<text v-if="conf.answer_question_types > 1" class="font1_3" :style="{'color': main_color}">
							{{question_seconds}}s
						</text>
					</arprogress>
					<view v-else :style="{'height': `${ID == 8 ? 120 : ID == 15 ? 100 : 90}px`}">
					</view>
				</view>


				<view v-if="question_idx == index" v-for="(item,index) in question_list" :key="index"
					:animation="animationData">
					<view class="question-list">

						<view v-if="ID == 4" class="mb1 p1 colorfff dotted width100"
							:style="{'background-color': main_color}">
							<text>{{question_idx+1}}、</text>
							<text class="pr_3">[{{item.question_types_title}}]</text>
							<text>{{item.title}}</text>
							<text v-if="item.score" class="memo_text">（{{item.score}}分）</text>
						</view>

						<view class="radius_5 question-item">

							<!-- 限时 -->
							<view v-if="ID == 1 || ID == 2 || ID == 5"
								class="width100 f-d-c-j-a-c p1 radius2 answerTime">
								<view v-if="conf.answer_time && conf.answer_question_types == 1" class="top f-j-e-a-c">
									<i v-if="ID != 2 && ID != 5" class="iconfont pr_3 font1_2">&#xe697;</i>
									<text v-if="ID != 5">答题限时：{{conf.answer_time}}分钟</text>
									<text v-if="ID == 5">{{conf.answer_time}}分钟</text>
								</view>
								<view v-if="conf.question_seconds && conf.answer_question_types > 1"
									class="top f-j-e-a-c">
									<i v-if="ID != 2 && ID != 5" class="iconfont pr_3 font1_2">&#xe697;</i>
									<text v-if="ID != 5">题目倒计时：{{question_seconds}} 秒</text>
									<text v-if="ID == 5">{{question_seconds}} 秒</text>
								</view>
								<view v-if="ID != 5" class="pt_3">
									共 {{question_num}} 题
								</view>
								<view v-if="ID == 5 && !conf.answer_time || !conf.question_seconds" class="pt_3">
									{{question_idx+1}} / {{question_num}}
								</view>
							</view>

							<view v-if="ID == 6 || ID == 13" class="p1 pb0" :class="ID == 6 ? 'coloreee':''">
								{{question_idx+1}} / {{question_num}}
							</view>
							<view v-if="(ID == 6 || ID == 9 || ID == 13) && percent != -1 && show_progress"
								class="progress f-j-a-c">
								<arprogress :percent="percent" :percent_all="percent_all" :activeColor="main_color"
									width="90">
									<text v-if="conf.answer_time && conf.answer_question_types == 1"
										:style="{'color': main_color}">
										{{conf.answer_time}}分钟
									</text>
									<text v-if="conf.question_seconds && conf.answer_question_types > 1"
										:style="{'color': main_color}">
										{{question_seconds}}s
									</text>
								</arprogress>
							</view>

							<view v-if="ID == 7 || ID == 14" class="title-bg width80 f-d-c-j-a-c pt1 pb2"
								:style="{'background-image': `url(${exam_titlebg})`, 'color': fz_color}">
								<view class="font1_2 pb_5 fontweight">
									共 {{question_num}} 题
								</view>
								<view v-if="conf.answer_time || conf.question_seconds" class="f-j-a-c">
									<text v-if="conf.answer_time && conf.answer_question_types == 1">
										{{conf.answer_time}}分钟
									</text>
									<text v-if="conf.question_seconds && conf.answer_question_types > 1">
										倒计时：{{question_seconds}}s
									</text>
								</view>
							</view>

							<view v-if="ID == 8 || ID == 9" class="fontweight pb1 f-j-a-c" :class="ID == 9 ?'pt1' :''">
								<text v-if="ID == 8" class="font1_2" :style="{'color': main_color}">
									{{question_idx+1}} / {{question_num}}
								</text>
								<text v-if="ID == 9">第{{question_idx+1}}题 · 共{{question_num}}题</text>
							</view>

							<view v-if="ID == 11 || ID == 12 || ID == 15 || ID == 17 || ID == 18" class="bgimg relative"
								:style="{'background-image': `url(${titleUrl})`, 'color': fz_color}">

								<view v-if="ID == 11 || ID == 15 || ID == 17 || ID == 18" class="pb_5 f-j-sb-a-c">
									<text>{{question_idx+1}} / {{question_num}}</text>
									<text class="pl_5">[{{item.question_types_title}}]</text>
								</view>

								<view v-if="ID == 12 && percent != -1 && show_progress"
									class="progress f-j-a-c pt2 pb1">
									<arprogress :percent="percent" :percent_all="percent_all" :activeColor="main_color"
										width="85">
										<text v-if="conf.answer_question_types == 1" :style="{'color': main_color}">
											{{conf.answer_time}}分钟
										</text>
										<text v-if="conf.answer_question_types > 1" :style="{'color': main_color}">
											{{question_seconds}}s
										</text>
									</arprogress>
								</view>
								<view v-if="ID == 12" class="width100 text-center fontweight font1_1 pb_5">
									<text>共 {{question_num}} 题</text>
								</view>
								<view class="title-box">
									<text v-if="ID == 12">{{question_idx+1}}、</text>
									<text v-if="ID == 12">[{{item.question_types_title}}]</text>
									<text>{{item.title}}</text>
									<text v-if="item.score" class="memo_text">（{{item.score}}分）</text>
								</view>
							</view>

							<view class="bottom">
								<view v-if="ID == 3" class="width100 f-j-e-a-c pb1">
									<view class="radius_3 p_5 pl2 pr2 colorfff wht_shadow_03"
										style="background-color: rgba(0,0,0, .3);">
										{{question_idx+1}} / {{question_num}}
									</view>
								</view>

								<!-- 标题 -->
								<view
									v-if="ID != 4 && ID != 10 && ID != 11 && ID != 12 && ID != 15 && ID != 17 && ID != 18"
									class="title">
									<text
										v-if="ID != 0 && ID != 3 && ID != 6 && ID != 8 && ID != 9 && ID != 10 && ID != 13">{{question_idx+1}}、</text>
									<text class="pr_3"
										:class="ID == 6 || ID == 13 ? '' : 'gray_color'">[{{item.question_types_title}}]</text>
									<text>{{item.title}}</text>
									<text v-if="item.score" class="memo_text">（{{item.score}}分）</text>
								</view>


								<view v-if="ID == 10" class="bg-white radius1 mb1 p1">
									<view class="pb_7">
										{{question_idx+1}} / {{question_num}}
									</view>
									<view class="">
										<text class="pr_3 gray_color">[{{item.question_types_title}}]</text>
										<text>{{item.title}}</text>
										<text v-if="item.score" class="memo_text">（{{item.score}}分）</text>
									</view>
								</view>


								<!-- 图片 -->
								<!-- <view v-if="item.pic_list && item.pic_list.length" class="pic-box f-w width100 pb_7">
									<view class="pic-list" v-for="(itm,idx) in item.pic_list" :key="idx">
										<image :src="itm.url" mode="widthFix" @click.stop="previewImage(item,idx)">
										</image>
									</view>
								</view> -->

								<!-- /** 注释
								*提议人: 鲜繁
								*修改内容: 题目的图片修改成大图模式，一张图占一行
								*修改时间: 2023-07-06 11:05:40
								*/ -->
								<view v-if="item.pic_list && item.pic_list.length" class="pic-box f-w width100 pb_7">
									<view class="f width100 mb_7" v-for="(itm,idx) in item.pic_list" :key="idx">
										<image :src="itm.url" mode="widthFix" @click.stop="previewImage(item,idx)">
										</image>
									</view>
								</view>



								<!-- 选项 -->
								<view class="answer-option-box">
									<view v-if="item.question_types < 4"
										class="answer-option-list f-a-c p_5 pl1 pr1 gray_bdrsolid radius10 mb1"
										:style="{'background-color': itm.is_select ? sel_color : '','color': itm.is_select ? '#fff': ''}"
										v-for="(itm,idx) in item.answer_option" :key="idx"
										@click.stop="change_answer_option_select(index,idx)">
										<view class="letter-box f-j-a-c" style="background-color: transparent;">
											{{itm.letter}}
										</view>
										<view class="text">
											{{itm.text}}
										</view>
									</view>

									<view v-if="item.question_types == 4"
										class="f blank_list gray_bdrsolid radius_2 mb1 p_7 width100 answer-option-list"
										v-for="(itm,idx) in item.answer_option" :key="idx">
										<view class="blank_left min-width6">
											<text v-if="item.answer_option.length > 1">空格{{idx+1}}</text>
											答案：
										</view>
										<view class="blank_right">
											<input class="width100" type="text" v-model.trim="itm.text"
												:placeholder="item.answer_option.length > 1 ? `输入空${idx+1}答案` : '输入答案'"
												@input="change_blank_answer(index)" />
										</view>
									</view>

									<!-- 简答题 -->
									<view v-if="item.question_types == 5">
										<uni-easyinput :maxlength="2000" autoHeight type="textarea"
											v-model="item.answer_option[0].text" placeholder="输入题目答案" />
									</view>

								</view>
							</view>

							<view class="explain-content">
								<!-- 答案解析 -->
								<view v-if="conf.explain_text_open == 1  || (conf.explain_text_open == 2 && item.select_tab=='err_bgcolor')" class="width100 p0-1 explain-box"
									:class="ID == 6 ? 'coloreee' : ID == 13 ? '' : 'gray_color'">
									<!-- 答题卡模式的解析 -->
									<view v-if="conf.answer_question_types == 1 && item.parsing" class="p1-0">
										<view v-if="item.question_types != 5" class="pb1">
											<view class="pb_5">
												[答案]
											</view>
											<view v-if="item.question_types < 4" class="box">
												<view class="right_answer">
													正确答案：
													<text :style="{'color': suc_color}">{{item.right_answer_str}}</text>
												</view>
											</view>
											<view v-if="item.question_types == 4" class="box">
												<view class="width100 pb_5" v-for="(itm,idx) in item.answer_option"
													:key="idx">
													<view class="blank_bottom">
														<text v-if="item.answer_option.length > 1">填空{{idx+1}}</text>
														<text>答案：</text>
														<view class="display-ib" v-for="(it,ix) in itm.is_right"
															:key="ix">
															<text :style="{'color': suc_color}">{{it.text}}</text>
															<text v-if="ix < itm.is_right.length - 1"
																class="p0_5">或</text>
														</view>
													</view>
												</view>
											</view>
										</view>
										<view v-if="item.explain_text" class="">
											<view class="pb_5">
												[答案解析]
											</view>
											<view class="box">
												{{item.explain_text?item.explain_text:'无解析'}}
											</view>
										</view>
									</view>
									<!-- 竞赛模式的解析 -->
									<view v-if="conf.answer_question_types > 1 && show_explain_modal"
										class="fixed_modal f-d-c-j-a-c">
										<view class="fixed_modal_content">
											<view v-if="item.question_types != 5" class="pb1">
												<view class="pb_5 gray_color">
													[答案]
												</view>
												<view v-if="item.question_types < 4" class="box">
													<view v-if="conf.answer_question_types == 3" class="my_answer">
														我的答案：
														<text
															:style="{'color': item.user_answer_str == item.right_answer_str?suc_color:err_color}">{{item.user_answer_str?item.user_answer_str:'未作答'}}</text>
													</view>
													<view class="right_answer">
														正确答案：
														<text class=""
															:style="{'color': suc_color}">{{item.right_answer_str}}</text>
													</view>
												</view>
												<view v-if="item.question_types == 4" class="box">
													<view class="width100 pb_5" v-for="(itm,idx) in item.answer_option"
														:key="idx">
														<view v-if="conf.answer_question_types == 3"
															class="blank_top pb_2">
															<text
																v-if="item.answer_option.length > 1">填空{{idx+1}}</text>
															<text v-if="item.answer_option.length == 1">回答</text>
															<text>：</text>
															<text
																:class="itm.fz_color">{{itm.text?itm.text:'未作答'}}</text>
														</view>
														<view class="blank_bottom">
															<view class="display-ib">
																<text
																	v-if="conf.answer_question_types == 3">正确答案：</text>
																<text
																	v-if="conf.answer_question_types != 3 && item.answer_option.length > 1">填空{{idx+1}}答案：</text>
																<text
																	v-if="conf.answer_question_types != 3 && item.answer_option.length == 1">答案：</text>
															</view>
															<view class="display-ib" v-for="(it,ix) in itm.is_right"
																:key="ix">
																<text class=""
																	:style="{'color': suc_color}">{{it.text}}</text>
																<text v-if="ix < itm.is_right.length - 1"
																	class="p0_5">或</text>
															</view>
														</view>
													</view>
												</view>
											</view>
											<view v-if="item.explain_text" class="">
												<view class="pb_5 gray_color">
													[答案解析]
												</view>
												<view class="box gray_color">
													{{item.explain_text?item.explain_text:'无解析'}}
												</view>
											</view>
										</view>
										<i class="iconfont colorfff font1_5 pt_5"
											@click.stop="close_explain_modal">&#xe60c;</i>
									</view>
								</view>

								<!-- 答案解析和收藏按钮 -->
								<view class="p0-1 width100 pb1 explain-btn"
                                      :class="[(conf.explain_text_open == 1 || (conf.explain_text_open == 2 && item.select_tab=='err_bgcolor')) && conf.answer_question_types == 1?'f-j-sb-a-c':'f-j-e-a-c', ID == 3 ? 'pt1' : '']">
									<!-- 解析展示 -->
									<view
                                        v-if="(conf.explain_text_open == 1 || (conf.explain_text_open == 2 && item.select_tab=='err_bgcolor')) && conf.answer_question_types == 1 && !item.parsing"
										class="font_9"
										:class="ID == 6 || ID == 10 || ID == 11 || ID == 12 || ID == 15 || ID == 17 || ID == 18 ? 'coloreee' : 'gray_color'">
										<i class="iconfont pr_2 display-ib"
											@click.stop="assignment_question_explain(index)">&#xe61e;</i>
										<text class="pr_5 pl_2"
											@click.stop="assignment_question_explain(index)">显示答案解析</text>
									</view>
								</view>

							</view>
						</view>

						<view class="transparent-box radius_5">
						</view>
					</view>
				</view>
			</view>

			<view class="tabbar-box f-a-c p0-1" :style="{'background-color': ID == 4 ? '' : auxi_color}">
				<view class="left-box f-a-c">
					<view v-if="conf.answer_question_types == 1" class="f-a-c bg-white radius_3 gray_color p_5">
						<i class="iconfont font1_2" @click.stop="open_answer_card">&#xe607;</i>
						<text class="pl_3" @click.stop="open_answer_card">答题卡</text>
						<text class="pl_3">{{time.hour}}:{{time.minute}}:{{time.second}}</text>
					</view>
				</view>
				<view class="right-btn f-j-sb-a-c">
					<view v-if="question_idx > 0 && conf.answer_question_types != 2 && conf.answer_question_types != 3"
						class="gray_bdrsolid gray_color btn blk_shadow_01 bg-white" @click.stop="before_question">
						上一题
					</view>
					<view v-if="question_idx == 0 || conf.answer_question_types > 1">
					</view>
					<view
						v-if="question_idx != question_list.length - 1 && (conf.answer_question_types == 1 || (conf.answer_question_types > 1 && question_list[question_idx].question_types != 1 && question_list[question_idx].question_types != 3))"
						class="gray_bdrsolid gray_color btn blk_shadow_01 bg-white" @click.stop="after_question()">
						下一题
					</view>
					<view
						v-if="question_idx == question_list.length - 1 && (conf.answer_question_types == 1 || (conf.answer_question_types > 1 && question_list[question_idx].question_types != 1 && question_list[question_idx].question_types != 3))"
						class="btn blk_shadow_01 gray_bdrsolid bg-white gray_color" @click.stop="submit_exam(1)">
						交卷
					</view>
				</view>
			</view>

			<!-- 答题卡 -->
			<view v-if="show_answer_card" class="card-box f-j-c-a-e" @click.self="close_answer_card">
				<view class="card-content">
					<view class="top">
						<view class="card-list" v-for="(item,index) in question_list" :key="index"
							@click.stop="change_question_idx(index)">
							<view class="name f-j-a-c"
								:class="item.select_tab?'org_bgcolor':question_idx == index?'blue_bgcolor':''">
								{{index+1}}
							</view>
						</view>

					</view>
					<view class="bottom f-j-a-c">
						<view class="gray_color gray_bdrsolid" style="width:80%;" @click.stop="close_answer_card">
							关闭
						</view>
					</view>
				</view>
			</view>

		</view>

	</view>
</template>

<script>
	import init_obj from "../init.js";
	import arprogress from '@/pages/likou_dati/components/ar-circle-progress/index.vue'
	import bingProgress from '@/pages/likou_dati/components/bing-progress/bing-progress.vue'
	const app = getApp();
	export default {
		components: {
			arprogress,
        bingProgress
		},
		data() {
			return {
				ad_list: [],
				question_list: [],
				question_idx: 0, // 当前题目
				question_num: 0, // 题目总数量
				err_text: '',
				exam_titlebg: this.imgImport.exam_titlebg,
				titleUrl: `http://www.xinweiyun.com/weixin/editor/attached/image/weixin_60/20200915/20200915145851_524.png`,
				bgUrl: "https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/bg/djdt.jpg",

				// 计时
				time: {
					hour: '00',
					minute: '00',
					second: '00',
					count: 0,
				},
				answer_timer: null,

				question_seconds: '', // 考题时间 竞赛模式使用
				question_timer: null,

				show_answer_card: false,

				exam_details: {}, // 考卷详情
				conf: {},
				isload: false,

				show_explain_modal: false,

				// 左右滑动
				start_pageX: '', // 触摸开始点的X方向的位置
				end_pageX: '', // 触摸结束点的X方向的位置
				slider: false,
				animationData: {},

				is_submit_answer: false,
				allow_after: true,

				main_color: '', // 主色
				auxi_color: '', // 辅色
				suc_color: '#00B26A', // 正确颜色
				err_color: '#FF454D', // 错误颜色
				sel_color: '#007AFF', // 选中颜色
				fz_color: '#eee',

				show_progress: false,
				percent: -1,
				percent_all: 100,

				progress_width: 0,

				hide_num: 0,
				cuts_screen: 1, // 可以切屏的次数

				single_select: false,

				ID: '',
			}
		},
		props: {
			data: {
				default: {},
			},
			params: {
				default: {},
			},
			tplID: {
				default: '',
			},
			hideNum: {},
			tpl_obj: {},
		},
		watch: {
			hideNum: {
				handler(newName, oldName) {
					this.hide_num = newName;
					if (!this.anti_cheating) return // 未开启防作弊则可以切屏
					if (this.hide_num == this.cuts_screen) {
						this.xwyLib.alert('切屏次数达到上限！再次切屏将强制提交答题！');
					}
					if (this.hide_num > this.cuts_screen && !this.isload) {
						this.submit_exam_post();
					}
				},
				immediate: true
			},
			tpl_obj: {
				handler(n) {},
				deep: true,
			},
		},
		async mounted() {
			await this.change_template()
			this.init();
		},
		beforeDestroy() {
			if (this.answer_timer) clearInterval(this.answer_timer);
			if (this.question_timer) clearInterval(this.question_timer);
			if (!this.is_submit_answer) this.submit_exam_post();
		},
		methods: {
			async init() {
				const res = this.data;
				let question_list = res.question_list;

				question_list.forEach((val, idx) => {
					val.question_types_title = this.xwyLib.get_question_types_text(val
						.question_types);
					if (this.xwyLib.isArray(val.answer_option)) {
						val.answer_option.forEach((v, i) => {
							v.is_select = false;
							v.letter = this.xwyLib.numberToLetter(i)
						})
					}
				})

				this.question_num = res.question_num || question_list.length;

				if (res.exam_details) {
					this.exam_details = res.exam_details;
					if (res.exam_details?.conf) this.conf = res.exam_details.conf;
					this.conf.answer_time = Number(this.conf.answer_time);
					this.conf.question_seconds = Number(this.conf.question_seconds);
					if (!this.conf.answer_question_types) this.conf.answer_question_types = 1
					this.question_seconds = this.conf.question_seconds ? this.conf.question_seconds :
						'';
					// 开始计时
					if (this.conf.answer_question_types == 1) this.start_time();
					// 考题倒计时
					if (this.conf.answer_question_types > 1 && this.conf.question_seconds) this
						.question_down_time();
				}

				this.question_list = question_list;

				if ((this.conf.answer_time && this.conf.answer_question_types == 1) || (this.conf.question_seconds &&
						this.conf.answer_question_types > 1)) {
					this.show_progress = true;
				}
			},


			change_template() {
				let id = this.tplID || '';

				const list = init_obj.template_list;
				const opt = list.find(val => val.id == id)
				if (opt) {
					this.main_color = opt.main_color;
					this.auxi_color = opt.auxi_color;
					this.suc_color = opt.suc_color;
					this.err_color = opt.err_color;
					this.sel_color = opt.sel_color;
				}


				switch (Number(id)) {
					case 16:
						this.bgUrl =
							`https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/bg/djbg.jpg`;
						let windowWidth = uni.getWindowInfo().windowWidth;
						if (windowWidth > 640) windowWidth = 640;
						this.progress_width = `${windowWidth - 32}px`;
						break;
					case 17: // 元宵节风格
						this.fz_color = '#74442A'
						this.titleUrl =
							'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/exam_template/yx.png'
						this.bgUrl =
							`https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/exam_template/yx_bg.jpg`;
						break;
					case 18: // 中秋节风格
						this.fz_color = '#74442A'
						this.titleUrl =
							'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/exam_template/zq.png'
						this.bgUrl =
							`https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/exam_template/zq_bg.jpg`;
						break;
				}

				const obj = this.tpl_obj;
				if (obj.id == id && obj.pid) {
					id = obj.pid
					if (obj.titleUrl) this.titleUrl = obj.titleUrl
					if (obj.bgUrl) this.bgUrl = obj.bgUrl
					if (obj.main_color) this.main_color = obj.main_color
					if (obj.auxi_color) this.auxi_color = obj.auxi_color
					if (obj.fz_color) this.fz_color = obj.fz_color
					if (obj.sel_color) this.sel_color = obj.sel_color
				}

				this.ID = id;
				this.xwyLib.setNavigationBarColor({
					fzColor: '#ffffff',
					bgColor: this.main_color,
				});

			},

			showNum(num) {
				if (num < 10) {
					return '0' + num
				}
				return num
			},

			// 开始答题后 计时
			start_time() {
				// 获取限时
				let limit_time = this.conf.answer_time ? Number(this.conf.answer_time) * 60 : '';
				this.percent_all = this.percent = limit_time;
				this.answer_timer = setInterval(() => {
					this.percent--;
					this.time.count++;
					// 需要改变页面上时分秒的值
					this.time.second = this.showNum(this.time.count % 60);
					this.time.minute = this.showNum(parseInt(this.time.count / 60) % 60)
					this.time.hour = this.showNum(parseInt(this.time.count / 60 / 60))

					// 如果存在限制时间 则判断大小
					if (limit_time && limit_time <= this.time.count) {
						clearInterval(this.answer_timer);
						this.xwyLib.alert('答题时间到了！');
						setTimeout(() => {
							this.xwyLib.showLoading();
							this.submit_exam_post();
						}, 1500)
					}
				}, 1000);
			},

			// 考题倒计时
			question_down_time() {
				this.percent = this.percent_all = this.question_seconds;
				// 获取题目限时
				this.question_timer = setInterval(() => {
					this.percent--;
					this.question_seconds = this.question_seconds - 1;
					// 如果存在限制时间 则判断大小
					if (this.question_seconds < 0) {
						clearInterval(this.question_timer);
						this.question_seconds = this.conf.question_seconds;
						if (this.question_idx < this.question_list.length - 1) this.after_question();
						if (this.question_idx >= this.question_list.length - 1) {
							clearInterval(this.question_timer);
							this.assignment_question_explain(this.question_idx);
							this.judge_explain_open_and_modalshow();
						}
					}
				}, 1000);
			},

			judge_explain_open_and_modalshow() {
                if (this.conf?.explain_text_open == 1 || (this.conf.explain_text_open == 2 && this.question_list[this.question_idx].select_tab == "err_bgcolor")) {
                    this.open_explain_modal()
                    return
                }
				this.$nextTick(() => this.close_explain_modal())
			},

			// 改变题目选项选中状态
			change_answer_option_select(index, idx) {
				if (this.single_select) return
				this.single_select = true;
				let question_list = this.question_list,
					types = question_list[index].question_types,
					answer_option = question_list[index].answer_option;

				// 判断题目类型 然后做出相应处理
				switch (Number(types)) {
					case 1:
						answer_option.forEach(val => {
							val.is_select = false;
						})
						this.$set(answer_option[idx], 'is_select', true)
						break;
					case 2:
						this.$set(answer_option[idx], 'is_select', !answer_option[idx].is_select)
						break;
					case 3:
						answer_option.forEach(val => {
							val.is_select = false;
						})
						this.$set(answer_option[idx], 'is_select', true)
						break;
				}

				this.$nextTick(() => {
					// let isTrue = false;
					// this.question_list[index].answer_option.forEach(val => {
					// 	if (val.is_select) isTrue = true
					// })
					// this.$set(this.question_list[index], 'select_tab', isTrue)
					// this.assignment_question_explain(index)
					const item = this.question_list[index].answer_option.find(val => val.is_select)
					this.$set(this.question_list[index], 'select_tab', item ? true : false)
					this.assignment_question_explain(index)
					let conf = this.conf;
					if (conf?.answer_question_types > 1) {
						if (types == 1 || types == 3) {
							setTimeout(() => {
								this.after_question()
								this.single_select = false;
							}, 700)
							return
						}
					}
					this.single_select = false;
				})
			},

			// 上一题
			before_question() {
				if (this.question_idx > 0) {
					this.question_idx -= 1;
					// this.previous_animation();
				}
			},
			// 下一题
			after_question() {
				if (!this.allow_after) return
				this.allow_after = false;
				setTimeout(() => this.allow_after = true, 700)

				let conf = this.conf;
				if (conf?.answer_question_types > 1) {
					clearInterval(this.question_timer);
					this.question_seconds = this.conf.question_seconds;
					this.assignment_question_explain(this.question_idx);
					this.$nextTick(() => this.judge_explain_open_and_modalshow())
					return
				}

				if (this.question_idx < this.question_list.length - 1) {
					this.question_idx += 1;
					// this.next_animation();
				}
			},

			// 第三种答题模式  答错直接提交
			judge_answer_error_post() {
				let index = this.question_idx,
					obj = this.question_list[index],
					isTrue = true;
				// 判断单选 多选 判断题
				if (obj.question_types < 4 && obj.right_answer_str != obj.user_answer_str) isTrue = false;
				if (obj.question_types == 4 && obj.select_tab != "gre_bgcolor") isTrue = false;
				if (obj.question_types == 5 && !obj.answer_option[0]?.text) isTrue = false;

				if (!isTrue) {
					this.xwyLib.showModal(`很遗憾，题目答错了！`, {
						showCancel: false,
						success: res => {
							this.xwyLib.showLoading();
							this.submit_exam_post();
						}
					})
					return
				}

				this.question_idx += 1;
				// 竞赛模式并且设置了每题限制时间才执行
				if (this.conf.answer_question_types > 1 && this.question_seconds) this.question_down_time();
			},

			submit_exam(e) {
				uni.showModal({
					content: '请确认是否交卷？',
					confirmText: '交卷',
					success: res => {
						if (res.confirm) {
							if (e == 1 && this.conf.answer_question_types > 1) {
								clearInterval(this.question_timer);
								this.assignment_question_explain(this.question_idx);
								this.judge_explain_open_and_modalshow();
								return
							}
							clearInterval(this.answer_timer);
							this.submit_exam_post();
						}
					}
				})
			},

			async submit_exam_post() {
				if (this.isload) return
				this.isload = true;
				this.xwyLib.showLoading();

				let answer_list = [],
					question_list = this.question_list;
				question_list.forEach((val, idx) => {
					let user_answer = [];
					let rand_num = val.rand_num || -1;
					// 单选 多选 判断
					if (val.question_types < 4) {
						val.answer_option.forEach((v, i) => {
							if (v.is_select) user_answer.push(i)
						})
					}
					// 填空
					if (val.question_types == 4 || val.question_types == 5) {
						val.answer_option.forEach((v, i) => {
							user_answer.push(v.text);
						})
					}
					const op = {
						question_id: val.id,
						user_answer,
					}
					if (rand_num >= 0) op.rand_num = rand_num;
					answer_list.push(op)
				})

				answer_list = this.base64.baseEncode(JSON.stringify(answer_list));
				let params = {
					exam_id: this.params.exam_id,
					answer_list,
				};


				if (this.params.activityID) {
					params.active_id = this.params.activityID;
                    const must_submit_res = await this.xwy_api.request({
                        url: 'front.flat.sport_step.user/user_attend_details',
                        data: {
                            active_id: this.params.activityID
                        }
                    })
                    const must_submit = must_submit_res?.data?.user_details?.must_submit
                    if (must_submit) params.must_submit = this.base64.baseEncode(JSON.stringify(must_submit))
				} else {
                    const must_submit_exam = uni.getStorageSync(`must_submit_exam${this.params.exam_id}`)
                    if (must_submit_exam) params.must_submit = must_submit_exam
                }

                if (this.params.point_id) params.point_id = this.params.point_id;
                if (this.params.send_word) params.send_word = 1;
                if (this.params.rush_round) params.rush_round = 1;  // 1答题闯关

				let obj = {
					data: params,
					url: `front.flat.exam.user_answer/submit_answer`,
					success: res => {
						if (this.params.point_id) {
							app.globalData.update_point = 1;
						}
						this.is_submit_answer = true;
						this.isload = false;
						uni.hideLoading();
						if (res.status != 1 || !res.data || !res.data.score_result) {
							this.xwyLib.alert(res.info);
							return
						}

                        if (this.data?.exam_details?.conf?.is_questionnaire) {
                            // #ifdef MP-WEIXIN
                            wx.disableAlertBeforeUnload?.()
                            // #endif
                            uni.showToast({title: '已提交', icon: 'success'})
                            return setTimeout(() => uni.navigateBack(), 1000)
                        }

                        // 任务闯关活动，答题成功后刷新活动状态(任务是否已完成，关卡是否已完成)
                        this.$parent.answerSuccess(res?.data?.score_result?.score || 0)

                        // 不知道利口这个是用来做什么的，我这边代码只有存缓存，没有读的地方，先注释
						// uni.setStorageSync(`answer_result-${res.data.score_result.score_id}`, 1)

						let url = `/pages/likou_dati/pages/answer/answer_result/answer_result?score_id=${res.data.score_result.score_id}`;
						if (this.params.activityID) url += `&activityID=${this.params.activityID}`;
						if (this.params.point_id) url += `&point_id=${this.params.point_id}`;
                        if (this.params.lottery_id) {
                            url += `&lottery_id=${this.params.lottery_id}&score_min=${this.params.score_min || 0}`
                        }
						if (this.params.single) url += `&single=${this.params.single}`
						if (this.params.closed_AD) url += `&closed_AD=1`

                        if (res.data?.exam_success_res) {
                            const exam_success_res = res.data?.exam_success_res
                            if (exam_success_res.lottery_details?.status && exam_success_res?.lottery_details?.point_details?.conf?.lottery?.lottery_id) {
                                url += `&lottery_id=${res.data.exam_success_res.lottery_details.point_details.conf.lottery.lottery_id}`
                            }
                            if (exam_success_res.send_word?.status && exam_success_res.send_word?.info) {
                                app.globalData.answer_get_word = exam_success_res.send_word.word
                                  url += `&send_word_info=${exam_success_res.send_word.info}`
                            }
                        }

                        if (this.params.task_activity_step_answer) url += '&task_activity_step_answer=1'

                        const needLightUpExamPrize = this.lightUpExamPrizeCheck(res?.data?.score_result?.score || 0)
                        if (needLightUpExamPrize) {
                            const {img_src, prize_name} = this.params
                            url += `&exam_prize=1&img_src=${img_src}&prize_name=${prize_name}`
                        }

						this.xwyLib.routeJump(url, 'redirectTo')
					}
				};
				this.xwyLib.ajax(obj);
			},

            // 检查是否点亮健步走答题勋章
            lightUpExamPrizeCheck(score) {
                const {exam_prize, have_light, min_score} = this.params
                if (!exam_prize || Number(have_light)) return false
                const lightUp = score >= Number(min_score)
                if (!lightUp) return false

                this.$parent.examPrizeLightUp()
                return true
            },

			open_answer_card() {
				this.show_answer_card = true;
			},
			close_answer_card() {
				this.show_answer_card = false;
			},
			change_question_idx(e) {
				this.question_idx = e;
				this.close_answer_card()
			},

			previewImage(item, idx) {
				let list = [];
				if (item.pic_list && item.pic_list.length) {
					item.pic_list.forEach(val => {
						list.push(val.url);
					})
				}
				uni.previewImage({
					current: idx,
					urls: list,
				})
			},


			change_blank_answer(e) {
				let question_list = this.question_list;
				let answer_option = question_list[e].answer_option,
					isTrue = false;
				for (let i = 0; i < answer_option.length; i++) {
					if (answer_option[i].text) {
						isTrue = true;
						break;
					}
				}
				this.$set(this.question_list[e], 'select_tab', isTrue)
			},


			// 改成解析模式
			assignment_question_explain(index) {
				this.xwyLib.showLoading();
				let val = this.question_list[index];
				val.parsing = true;
				val.question_types_title = this.xwyLib.get_question_types_text(val.question_types);
				// 判断类型
				if (parseInt(val.question_types) < 4) {
					if (Array.isArray(val.answer_option)) {
						let right_answer_str = '',
							user_answer_str = '';
						val.answer_option.forEach((v, i) => {
							if (v.is_right) {
								right_answer_str += `${v.letter},`;
								v.bg_color = 'gre_bgcolor';
								v.fz_color = 'gre_color';
							}
							if (v.is_select) {
								user_answer_str += `${v.letter},`;

								if (!v.is_right) {
									v.bg_color = 'err_bgcolor';
									v.fz_color = 'err_color';
								}
							}
						})

						if (right_answer_str) val.right_answer_str = right_answer_str.substr(0, right_answer_str
							.length - 1);
						if (user_answer_str) val.user_answer_str = user_answer_str.substr(0, user_answer_str.length -
							1);

						if (val.right_answer_str == val.user_answer_str) {
							val.select_tab = 'gre_bgcolor'
						} else {
							val.select_tab = 'err_bgcolor'
						}

					}
				}
				// 填空题
				if (parseInt(val.question_types) == 4) {
					let blankTrue = true,
						true_list = [];;
					val.answer_option.forEach((v, i) => {
						if (Array.isArray(v.is_right)) {
							let isTrue = false;
							for (let is_i = 0; is_i < v.is_right.length; is_i++) {
								if (v.is_right[is_i].text == v.text) {
									isTrue = true;
									true_list.push(is_i);
									break;
								}
							}
							if (isTrue) {
								v.fz_color = 'gre_color';
							} else {
								v.fz_color = 'err_color';
							}
						}
					})
					if (val.answer_option.length == true_list.length) {
						val.select_tab = 'gre_bgcolor'
					} else {
						val.select_tab = 'err_bgcolor'
					}
				}

				this.$set(this.question_list, index, val)
				uni.hideLoading();
			},

			open_explain_modal() {
				this.show_explain_modal = true;
			},

			close_explain_modal() {
				this.show_explain_modal = false;
				if (this.question_idx == this.question_list.length - 1) {
					this.xwyLib.showLoading();
					this.submit_exam_post();
					return
				}
				if (this.question_idx < this.question_list.length - 1) {
					if (this.conf.answer_question_types == 3) {
						this.judge_answer_error_post();
						return
					}

					this.question_idx += 1;
					// 竞赛模式并且设置了每题限制时间才执行
					if (this.conf.answer_question_types > 1 && this.question_seconds) {
						this.question_down_time();
					}
				}
			},

			// 触摸开始
			touch_start(e) {
				if (this.conf.answer_question_types > 1) return
				let pageX = e.changedTouches[0].pageX;
				this.start_pageX = pageX;
			},

			// 触摸结束
			touch_end(e) {
				if (this.conf.answer_question_types > 1) return
				let start_pageX = this.start_pageX;
				let end_pageX = e.changedTouches[0].pageX;

				if (this.slider) return

				// 向下翻 下一题  开始点位置屏幕右边
				// 如果开始点X大于结束点X 说明需要下翻
				if (start_pageX - 70 > end_pageX) this.next_animation();

				// 向上翻 上一题 开始点位置屏幕左边
				// 如果开始点X小于结束点X 说明需要上翻
				if (start_pageX + 70 < end_pageX) this.previous_animation();

			},

			// 向上翻动画
			previous_animation() {
				this.slider = true;
				let question_idx = this.question_idx;

				// 判断题数是否还有上翻的余地
				if (question_idx <= 0) {
					this.slider = false;
					uni.showToast({
						title: '已经到第一题了',
						icon: 'none'
					});
					return
				}

				//创建动画  移动到-150%,-150%
				let animation = uni.createAnimation({
					duration: 300,
				});
				animation.translateX('150%').step();
				this.animationData = animation.export();
				setTimeout(() => {
					this.question_idx = question_idx - 1;
					//创建动画   5s将位置移动到原位
					let animation = uni.createAnimation({
						duration: 0,
					});
					animation.translateX('0').step();
					this.animationData = animation.export();
					this.slider = false;
				}, 300)
			},

			// 向下翻动画
			next_animation() {
				// 如果是竞赛模式则不滑动
				if (this.conf.answer_question_types > 1) {
					clearInterval(this.question_timer);
					this.question_seconds = this.conf.question_seconds;
					this.assignment_question_explain(this.question_idx);
					this.$nextTick(() => {
						this.judge_explain_open_and_modalshow();
					})
					return
				}

				this.slider = true;

				let question_idx = this.question_idx,
					question_list = this.question_list;

				// 判断题数是否还有下翻的余地
				if (question_idx >= question_list.length - 1) {
					this.slider = false;
					uni.showToast({
						title: '已经是最后一题了',
						icon: 'none'
					});
					return
				}

				//创建动画  移动到-150%,-150%
				let animation = uni.createAnimation({
					duration: 300,
				});
				animation.translateX('-150%').step();
				this.animationData = animation.export();

				setTimeout(() => {
					this.question_idx = question_idx + 1;
					//创建动画   5s将位置移动到原位
					let animation = uni.createAnimation({
						duration: 0,
					});
					animation.translateX('0').step();
					this.animationData = animation.export();
					this.slider = false;
				}, 300)
			},

		}

	}
</script>

<style scoped lang='scss'>
	@import url("@/pages/likou_dati/pages/answer/answer-template/init.css");

	.content {
		width: 100%;
		min-height: 100vh;
		padding-bottom: 60px;
	}

	.tabbar-box {
		border-top: 0;
	}

	.dotted {
		position: relative;
	}

	.dotted::after {
		content: ' ';
		height: 0;
		width: 100%;
		position: absolute;
		border-bottom: 10px dotted white;
		bottom: -5px;
		left: 0px;
	}

	.title-bg {
		position: absolute;
		left: 50%;
		top: -2.5rem;
		transform: translateX(-50%);
		background-size: 100% 100%;
		background-repeat: no-repeat;
		line-height: 1;
	}

	.bgimg {
		background-size: 100%;
		height: calc(100vw * 0.62);
		max-height: 394px;
		padding: 50px 50px 50px 40px;
		background-repeat: no-repeat;

		.title-box {
			height: calc(100vw * 0.35);
			max-height: 224px;
			overflow-y: auto;
		}
	}




	.templateBox0 .question-item {
		box-shadow: 0 0 3px 1px rgba(255, 255, 255, 0.3);
		background-color: #fff;
		border-top-left-radius: 2rem;
		border-top-right-radius: 2rem;

		.answerTime {
			border-bottom-left-radius: 0;
			border-bottom-right-radius: 0;
			color: #fff;
			background-color: #E68A6C;
		}

		.bottom .title {
			padding-bottom: .7rem;
		}

		.explain-content {
			.explain-box {
				padding: 0 !important;
			}

			.explain-btn {
				padding: 0 !important;
				padding-top: 1rem;
			}
		}
	}




	.templateBox1 .question-item {
		box-shadow: 0 0 3px 1px rgba(255, 255, 255, 0.3);
		background-color: #fff;
		border-top-left-radius: 2rem;
		border-top-right-radius: 2rem;

		.answerTime {
			border-bottom-left-radius: 0;
			border-bottom-right-radius: 0;
			color: #fff;
			background-color: #E68A6C;
		}

		.bottom {
			padding: 1rem;

			.title {
				padding-bottom: .7rem;
			}
		}
	}



	.templateBox2 .question-item {
		box-shadow: 0 0 3px 1px rgba(255, 255, 255, 0.3);
		background-color: #fff;

		.answerTime {
			>.top {
				font-weight: 700;
			}
		}

		.bottom {
			padding: 1rem;

			.title {
				padding-bottom: .7rem;
			}
		}
	}


	.templateBox3 .question-item {
		.bottom {

			.title {
				margin-bottom: 1rem;
				background-color: #fff;
				padding: 1rem;
				border-radius: .3rem;
				box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.1);
			}

			.answer-option-list {
				background-color: #fff;
			}

			.blank_list {
				background-color: #fff;
			}

			.explain-btn {
				padding: 1rem !important;
			}
		}

		.explain-box {
			background-color: #fff;
			margin-top: 1rem;
			border-radius: .3rem;
		}
	}



	.templateBox4 .question-box {
		padding: 0;

		.question-item {
			padding: 0 1rem;

			.explain-box {
				padding: 0 !important;
			}

			.explain-btn {
				padding: 0 !important;
			}
		}
	}


	.templateBox5 .question-list {
		padding-top: 3rem;

		.question-item {
			position: relative;
			padding: 1rem;
			padding-top: 3rem;
			box-shadow: 0 0 3px 1px rgba(255, 255, 255, 0.3);
			background-color: #fff;

			.answerTime {
				position: absolute;
				left: 50%;
				top: -2.7rem;
				transform: translateX(-50%);
				width: 5rem;
				height: 5rem;
				border-radius: 50%;
				padding: 0 !important;
				background-color: #fff;
				display: flex;
				justify-content: center;
				align-items: center;
				box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.3);
			}

			.title {
				padding-bottom: 1rem;
			}

			.explain-box {
				padding: 0 !important;
			}

			.explain-btn {
				padding: 0 !important;
			}
		}
	}


	.templateBox6 .question-box .question-list {
		padding: .1rem;
		padding-top: 5rem;

		.question-item {
			position: relative;
			box-shadow: 0 0 3px 1px rgba(255, 255, 255, 0.3);

			.title {
				padding-bottom: 1rem;
			}

			.blank_list {
				background-color: #fafafa;
				color: #333;
			}

			.bottom {
				padding: 1rem;
				color: #eee !important;

				.answer-option-list {
					position: relative;
					border-radius: 0 !important;
					background-color: #fafafa;
					color: #333;

					&::after {
						content: '';
						position: absolute;
						left: -.9rem;
						top: -.9rem;
						width: 1.5rem;
						height: 1.5rem;
						transform: rotateZ(45deg);
						background-color: #505285;
					}

					&::before {
						content: '';
						position: absolute;
						right: -.9rem;
						bottom: -.9rem;
						width: 1.5rem;
						height: 1.5rem;
						transform: rotateZ(45deg);
						background-color: #505285;
					}
				}
			}

			.progress {
				position: absolute;
				right: 0;
				top: -48px;
				border-radius: 50%;
			}
		}
	}


	.templateBox7 .question-list {
		padding-top: 3rem;

		.question-item {
			position: relative;
			padding: 1rem;
			padding-top: 5rem;
			box-shadow: 0 0 3px 1px rgba(255, 255, 255, 0.3);
			background-color: #fff;

			.title {
				padding-bottom: 1rem;
			}

			.answer-option-list {
				border-radius: 0 !important;
				background-color: #fafafa;
				color: #333;
			}

			.explain-box {
				padding: 0 !important;
			}

			.explain-btn {
				padding: 0 !important;
			}
		}
	}



	.templateBox8 .question-list .question-item {
		position: relative;
		padding: 1rem;
		box-shadow: 0 0 3px 1px rgba(255, 255, 255, 0.3);
		background-color: #fff;

		.title {
			padding-bottom: 1rem;
		}

		.bottom .answer-option-list {
			border-radius: 0 !important;
			background-color: #fafafa;
			color: #333;
		}

		.explain-box {
			padding: 0 !important;
		}

		.explain-btn {
			padding: 0 !important;
		}
	}



	.templateBox9 .question-list {
		position: relative;
		padding-top: 3rem;
		z-index: 9;

		.progress {
			position: absolute;
			left: 50%;
			top: 0;
			transform: translateX(-50%) translateY(-50%);
			z-index: 9;
		}

		.question-item {
			position: relative;
			padding: 1rem;
			padding-top: 3rem;
			box-shadow: 0 0 3px 1px rgba(255, 255, 255, 0.3);
			background-color: #fff;

			.answerTime {
				position: absolute;
				left: 50%;
				top: -2.7rem;
				transform: translateX(-50%);
				width: 5rem;
				height: 5rem;
				border-radius: 50%;
				padding: 0 !important;
				background-color: #fff;
				display: flex;
				justify-content: center;
				align-items: center;
				box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.3);
			}

			.title {
				padding-bottom: 1rem;
			}

			.explain-box {
				padding: 0 !important;
			}

			.explain-btn {
				padding: 0 !important;
			}
		}

		.transparent-box {
			position: absolute;
			left: 50%;
			top: 50%;
			width: 100%;
			height: calc(100% - 3rem);
			background-color: rgba(255, 255, 255, .3);
			transform: translateX(-50%) translateY(calc(-50% + 1.5rem)) rotateZ(-3.5deg);
			z-index: -1;
		}

		.bottom .answer-option-list {
			border-radius: .3rem !important;
			background-color: #fafafa;
			color: #333;
			box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.2);
		}
	}



	.templateBox10 {
		.question-item {
			.bottom {
				.answer-option-list {
					background-color: #fff;
					border-radius: 1rem;
					border: 0 !important;
				}

				.blank_list {
					background-color: #fff;
				}
			}

			.explain-box {
				background-color: #fff;
				margin-top: 1rem;
				border-radius: 1rem;
			}

			.explain-btn {
				padding: 1rem !important;
				color: #eee !important;
			}
		}

		.tabbar-box .radius_3 {
			border-radius: 1rem;
		}
	}



	.templateBox11 {
		.question-box {
			padding: 0;
			padding-bottom: 90px;
		}

		.question-item {

			.bottom {
				padding: 1rem;

				.answer-option-list {
					background-color: #fff;
					border-radius: 1rem;
					border: 0 !important;
					box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.1);
				}

				.blank_list {
					background-color: #fff;
				}
			}

			.explain-content {
				padding: 0 1rem;

				.explain-box {
					background-color: #fff;
					border-radius: 1rem;
				}

				.explain-btn {
					padding: 1rem !important;
					color: #eee !important;
				}
			}
		}

		.tabbar-box .radius_3 {
			border-radius: 1rem;
		}
	}


	.templateBox12 {
		.question-box {
			padding: 0;
			padding-bottom: 90px;
			padding-top: 4rem;
		}

		.question-item {

			.bottom {
				padding: 1rem;

				.answer-option-list {
					background-color: #fff;
					border-radius: 1rem;
					border: 0 !important;
					box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.1);
				}

				.blank_list {
					background-color: #fff;
				}
			}

			.explain-content {
				padding: 0 1rem;

				.explain-box {
					background-color: #fff;
					border-radius: 1rem;
				}

				.explain-btn {
					padding: 1rem !important;
					color: #eee !important;
				}
			}

			.progress {
				position: absolute;
				right: 1rem;
				top: 0;
				transform: translateY(-50%);
			}
		}

		.tabbar-box .radius_3 {
			border-radius: 1rem;
		}

		.bgimg {
			padding: 35px 50px 50px 40px;

			.title-box {
				height: calc(100vw * 0.38);
				max-height: 243px;
			}
		}
	}



	.templateBox13 .question-box {
		background-size: 100% 100%;
		background-repeat: no-repeat;
		background-attachment: fixed;

		.question-list {
			padding: .1rem;
			padding-top: 5rem;

			.question-item {
				position: relative;
				box-shadow: 0 0 3px 1px rgba(255, 255, 255, 0.3);
				background-color: #FADFC2;

				.title {
					padding-bottom: 1rem;
				}

				.blank_list {
					background-color: #fafafa;
				}

				.bottom {
					padding: 1rem;

					.answer-option-list {
						position: relative;
						border-radius: 1rem !important;
						background-color: #fafafa;
					}
				}

				.explain-box {
					color: #777;
				}

				.progress {
					position: absolute;
					right: 0;
					top: -48px;
					border-radius: 50%;
				}
			}
		}
	}



	.templateBox14 {
		.question-box {
			background-size: 100% 100%;
			background-repeat: no-repeat;
			background-attachment: fixed;
		}

		.question-list {
			padding-top: 5rem;

			.question-item {
				position: relative;
				padding: 1rem;
				padding-top: 5rem;
				box-shadow: 0 0 3px 1px rgba(255, 255, 255, 0.3);
				background-color: #FADFC2;

				.title {
					padding-bottom: 1rem;
				}

				.answer-option-list {
					border-radius: 10rem !important;
					background-color: #fafafa;
					color: #333;
				}

				.explain-box {
					padding: 0 !important;
				}

				.explain-btn {
					padding: 0 !important;
				}

				.title-bg {
					top: -3.5rem;
				}
			}
		}
	}



	.templateBox15 {
		.question-box {
			padding: 0;
			padding-bottom: 90px;
			background-repeat: no-repeat;
			background-attachment: fixed;
			background-size: 100% 100%;
		}

		.question-item {

			.bottom {
				padding: 1rem;

				.answer-option-list {
					background-color: #fff;
					border-radius: 1rem;
					border: 0 !important;
					box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.1);
				}

				.blank_list {
					background-color: #fff;
				}
			}

			.explain-content {
				padding: 0 1rem;

				.explain-box {
					background-color: #fff;
					border-radius: 1rem;
				}

				.explain-btn {
					padding: 1rem !important;
					color: #eee !important;
				}
			}
		}

		.tabbar-box .radius_3 {
			border-radius: 1rem;
		}
	}


	.templateBox16 {
		.question-box {
			min-height: calc(100vh - 92px);
			background-repeat: no-repeat;
			background-attachment: fixed;
			background-size: 100% 100%;
		}

		.question-item {
			position: relative;
			box-shadow: 0 0 3px 1px rgba(255, 255, 255, 0.3);
			background-color: #fff;
			box-shadow: 0 0 10px 5px rgba(0, 0, 0, 0.15);

			.title {
				padding-bottom: 1rem;
			}

			.blank_list {
				background-color: #fafafa;
			}

			.bottom {
				padding: 1rem;

				.answer-option-list {
					position: relative;
					border-radius: 1rem !important;
					background-color: #fafafa;
				}
			}

			.explain-box {
				color: #777;
			}

			.progress {
				position: absolute;
				right: 0;
				top: -48px;
				border-radius: 50%;
			}
		}

		.tabbar-box {
			/* background-color: transparent !important; */
		}

		.tabbar-box .radius_3 {
			border-radius: 1rem;
		}
	}
</style>
