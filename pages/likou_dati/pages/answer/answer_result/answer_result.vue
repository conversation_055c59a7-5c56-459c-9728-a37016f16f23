<template>
    <view class='content'>
        <view class="p0-1 mb1 bg-white text-center blk_shadow_01">
            <view v-if="exam_details.exam_name" class="font1_5 p1-0">
                {{exam_details.exam_name}}
            </view>
            <view v-if="total_count.question_num" class="all_question gray_color">
                共
                <text>{{total_count.question_num}}</text>
                题
            </view>
            <view v-if="score_details.score >= 0" class="score p1-0">
                <template v-if="examResultScoreText">
                    <text class="font34 color-error">{{ examResultScoreText }}</text>
                </template>
                <template v-else>
                    <text class="red_color">{{score_details.score ? parseFloat(score_details.score).toFixed(2):0}}</text>
                    <text>分</text>
                </template>
            </view>
            <!-- 种树获取的水滴 -->
            <view v-if="total_count.reward_water" class="memo_text f-j-a-c pb1">
                <i class="iconfont blue_color pr_2">&#xe674;</i>
                <text>获得奖励{{total_count.reward_water || 0}}水滴</text>
            </view>

            <!--晓阳说答题集字获得字的提示字体要加大-->
            <!--2023-12-26 15:10:12 微信私聊-->
			      <view v-if="params['send_word_info']" class="font18" style="padding-bottom: 30px;">
                {{params['send_word_info']}}
            </view>

            <view class="f-j-sb-a-c pb1">
                <view class="text-center">
                    <view class="gre_color pb_2 font1_3">
                        {{total_count.right || 0}}
                    </view>
                    <view class="gray_color">
                        答对
                    </view>
                </view>
                <view class="text-center">
                    <view class="red_color pb_2 font1_3">
                        {{total_count.wrong || 0}}
                    </view>
                    <view class="gray_color">
                        答错
                    </view>
                </view>
                <view class="text-center">
                    <view class="pb_2 font1_3">
                        {{total_count.percent || 0}}%
                    </view>
                    <view class="gray_color">
                        正确率
                    </view>
                </view>
            </view>
        </view>

        <view v-if="score_details.used_time" class="bottom f-j-sb-a-c p0-1">
        	<text class="gray_color">用时</text>
        	<text>{{score_details.used_time}}</text>
        </view>

        <view v-if="show_explain">
            <!-- 分类 切换错题和全部题目 -->
            <view class="category-box pb_5 bg-white">
                <liuyuno-tabs v-if="types_list && types_list.length" :tabData="types_list" :defaultIndex="0"
                    @tabClick='change_types' />
            </view>

            <!-- 题目列表 -->
            <view class="question-box p0-1 bg-white">
                <view v-if="types_idx == 0 || (types_idx == 1 && !item.question_error) || (types_idx == 2 && item.question_error)" class="question-list"
                    :class="question_list.length - 1 == index?'':'gray_bdrbdashed'"
                    v-for="(item,index) in question_list" :key="index">
                    <view class="question-title f p1-0">
                        <view class="left" style="min-width: 100%">
                            <text class="gray_color">{{index+1}}、[{{item.question_details.question_types_title}}]</text>
                            <text>
                                {{item.question_details.title}}
                            </text>
                            <!-- 图片 -->
                            <text v-if="item.question_details.pic_list && item.question_details.pic_list.length"
                                class="blue_color" style="padding: 0 .5rem;"
                                @click.stop="previewImage(item.question_details)">[图片]</text>
                            <!-- 此题的分数 -->
                            <text
                                v-if="item.admin_set_score && item.admin_set_score.score">（此题：{{item.admin_set_score.score?parseFloat(item.admin_set_score.score).toFixed(2):0}}分）</text>
                            <text
                                v-if="(!item.admin_set_score || !item.admin_set_score.score) && item.score">（此题：{{item.score?parseFloat(item.score).toFixed(2):0}}分）</text>
                        </view>
                    </view>
                    <view v-if="item.question_details.answer_option && item.question_details.answer_option.length"
                        class="answer-option-box pb1">
                        <view v-if="item.question_details.question_types < 4" class="answer-option-list p5_0 f-a-c"
                            v-for="(itm,idx) in item.question_details.answer_option" :key="idx">
                            <view class="letter-box f-j-a-c radius50 bgeee" :class="itm.bg_color">
                                <text
                                    v-if="(!itm.is_select && !itm.is_right) || (itm.is_right && !itm.is_select)">{{itm.letter}}</text>
                                <i v-if="itm.is_select && itm.is_right" class="iconfont f-j-a-c font1_5">&#xe6ad;</i>
                                <i v-if="itm.is_select && !itm.is_right" class="iconfont f-j-a-c font1">&#xe692;</i>
                            </view>
                            <view class="text" :class="itm.fz_color">
                                {{itm.text}}
                            </view>
                        </view>
                    </view>
                    <view class="question-item pb1">
                        <view class="gray_color f-j-sb-a-c pb_5">[答案]</view>
                        <view v-if="item.question_details.question_types < 4" class="box">
                            <view class="my_answer pb_5">
                                我的答案：
                                <text
                                    :class="item.user_answer_str == item.right_answer_str?'gre_color':'red_color'">{{item.user_answer_str?item.user_answer_str:'未作答'}}</text>
                            </view>
                            <view class="right_answer">
                                正确答案：
                                <text class="gre_color">{{item.right_answer_str}}</text>
                            </view>
                        </view>
                        <view v-if="item.question_details.question_types == 4" class="box">
                            <view class="blank_box pb_5" v-for="(itm,idx) in item.question_details.answer_option"
                                :key="idx">
                                <view class="blank_top pb_2">
                                    填空{{idx+1}}：
                                    <text :class="itm.fz_color">{{itm.text?itm.text:'未作答'}}</text>
                                </view>
                                <view class="blank_bottom">
                                    正确答案：
                                    <view class="" v-for="(it,ix) in itm.is_right" :key="ix">
                                        <text class="gre_color">{{it.text}}</text>
                                        <text v-if="ix < itm.is_right.length - 1" class="p0_5">或</text>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view v-if="item.question_details && item.question_details.explain_text" class="question-item pb1">
                        <view class="gray_color pb_5">
                            [答案解析]
                        </view>
                        <view class="box gray_color">
                            {{item.question_details.explain_text}}
                        </view>
                    </view>
                </view>
            </view>

            <!-- 底部操作栏 -->
            <view class="tabbar-box f">
                <view class="left-box f-a-c">
                    <view v-if="!params.task_activity_step_answer" class="item" @click.stop="skip_page(8)">
                        <i class="iconfont f-j-a-c">&#xe638;</i>
                        <view class="text f-j-a-c">
                            首页
                        </view>
                    </view>
                </view>
                <view class="right-btn">
                    <view class="btn blue_bgcolor f-j-a-c" @click.stop="show_explain = false">
                        退出解析
                    </view>
                </view>
            </view>
        </view>

        <!-- 按钮组 -->
        <view v-if="!show_explain" class="btn-box f-d-c-j-a-c p1">
<!--            is_master ||-->
            <view v-if="!exam_details.conf || !exam_details.conf.look_back_closed" class="btn radius_3 gray_color gray_bdrsolid" @click.stop="show_explain = true">
                查看答案与解析
            </view>
          <view v-if="lottery_button_show" class="btn radius_3 bg-warning color-white" @click="toLottery">
              去抽奖
          </view>
            <view v-if="exam_details.exam_id && exam_details.set_limit_times !== 1 && !params.task_activity_step_answer && !params.exam_prize" class="btn radius_3 gray_color gray_bdrsolid" @click.stop="back_exam_details">
                再答一次
            </view>

            <!--不显示返回活动了，因为答题的入口太多了，又有各种活动的详情页面，又有健步走地图页，又有打卡点位页，又有答题列表页等等等-->
            <!--<view class="btn radius_3 gray_color gray_bdrsolid" @click.stop="backActivity">
                 返回活动
             </view>-->

            <!--直接改为返回了，回退两级页面，因为答题页面已经关闭了，所以回退两级页面会返回到考卷详情页的上一级页面-->
            <!--这样还是不行，党建猜图的没有经过考卷详情页，是直接进入到答题页面的，如果直接回退两级页面，就会回到党建猜图活动详情页面的上一个页面-->
            <!--<navigator class="btn radius_3 gray_color gray_bdrsolid" open-type="navigateBack" delta="2">
                返回活动
            </navigator>-->

            <!--算了，再判断一下吧，如果页面栈有考卷详情页就回退两级，没有就回退一级-->
            <view class="btn radius_3 gray_color gray_bdrsolid" @click="pageBack">
                {{ answerResultBackButtonText }}
            </view>


        </view>

		<uni-popup ref="medal_popup" type="center">
			<view class="medal-popup">
				<view class="flex-all-center medal-img-view">
					<uni-transition
						:show="point_detail.medal_details.logo"
						:mode-class="['fade','slide-top']"
						:duration="800"
					>
						<image
							class="medal-img"
							:src="point_detail.medal_details.logo"
							mode="heightFix"
						/>
					</uni-transition>
				</view>
				<view class="text-center color-warning">
					{{point_detail.medal_details.name || ''}}
				</view>

				<view class="flex-all-center" style="padding-top: 35px;">
					<navigator
						class="look-medal-list text-center"
						:url="'/pages/activity/other/medal_list?id=' + activity_detail.active_id + '&unit=' + (activity_detail.conf.active.kilo_unit || '里')"
					>查看勋章</navigator>
				</view>
				<view class="flex-all-center">
					<view class="color-sub font14 p10" @click="uniPopupClose('medal_popup')">关闭</view>
				</view>
			</view>
		</uni-popup>

        <exam-prize-light-up-popup v-if="params.exam_prize" ref="examPrizeLightUpPopup"
                                   :image="params.img_src" :name="params.prize_name"/>
    </view>
</template>

<script>
    let app = getApp();
    import xwy_api from '@/utils/api/xwy_api.js'

    import examPrizeLightUpPopup from '../../../components/exam-prize-light-up-popup.vue'

    export default {
        components: {examPrizeLightUpPopup},

        data() {
            return {
                params: {},
                is_master: false, // 是否管理员

                score_details: {},
                exam_details: {},
                total_count: {},
                exam_success_res: {},

                show_explain: false,

                types_list: [{
                        name: '全部',
                        value: 0,
                    },
                    {
                        name: '答对',
                        value: 2,
                    },
                    {
                        name: '答错',
                        value: 1,
                    },
                ],
                types_idx: 0,

                question_list: [],
                err_text: '', // 错误地址
                is_load: false,
                activity_detail: {},
                point_detail: {},
                lottery_button_show: false,
                examResultScoreText: '',
                answerResultBackButtonText: '返回'
            }
        },
        onLoad(options) {
            // console.log('获取的参数options', options);
            // if(options.answer_details){
            // 	options.answer_details = JSON.parse(options.answer_details);
            // }
            this.params = options;
            this.xwyLib.setBarTitle('答题结束')
            this.xwyLib.init(() => {
                this.userInfo = app.globalData.userInfo;
                this.init();
            })

            this.updateLoadMapPage()
        },
        methods: {
            // 更新路线图页面的数据，鲜繁那个手绘地图显示星星的活动
            updateLoadMapPage() {
                const pages = getCurrentPages()
                const map_page_route = 'pages/activity/user/road-map/road_map'
                const map_page = pages.find(page => page.route === map_page_route)
                if (map_page?.$vm?.roadMapShowStart) map_page.$vm.getMapSet?.(() => {})
            },


            async init() {
                this.get_answer_result();
                
                if (this.params.activityID) {
                    this.answerResultBackButtonText = this.xwy_config.answerResultBackButtonText(this.params.activityID)
                }

                this.examPrizeLightUp()
            },

            examPrizeLightUp() {
                if (this.params.exam_prize) {
                    this.$nextTick(() => this.$refs.examPrizeLightUpPopup.open())
                }
            },

            // 如果页面栈有地图页，返回地图页，因为可能还需要点抽奖或执行其他操作
            // 没有地图页就返回活动详情页面
            backActivity() {
                const pages = getCurrentPages()
                const map_page_route = 'pages/activity/user/road-map/road_map'
                if (pages.find(page => page.route === map_page_route)) {
                    return this.$uni.navigateBackPage(map_page_route)
                }
                this.$uni.navigateBackPage('pages/activity/user/detail?id=' + this.params.activityID)
            },

            pageBack() {
                const pages = getCurrentPages()
                const exam_details_route = 'pages/likou_dati/pages/exam/exam_details/exam_details'
                if (pages.find(page => page.route === exam_details_route)) {
                    return uni.navigateBack({
                        delta: 2
                    })
                }
                uni.navigateBack({
                    delta: 1
                })
            },

            // 获取答题结果
            get_answer_result() {
                if (this.is_load == true) return
                this.is_load = true;
                this.xwyLib.showLoading();
                this.score_details = {};
                this.question_list = [];
                let params = {
                    score_id: this.params.score_id,
                };
                let obj = {
                    data: params,
                    url: `front.flat.exam.user_answer/user_score_submit_answer_record`,
                    success: res => {
                        this.is_load = false;
                        uni.hideLoading();
                        console.log('获取答题结果', res);
                        if (res.status != 1 || !res.data.score_details) {
                            this.xwyLib.alert(res.info);
                            return
                        }


                        // 判断管理员
                        if (app.globalData.userInfo.id === res.data.exam_details.userid) this.is_master = true

                        if (res.data.score_details.used_time) {
                            let seconds = res.data.score_details.used_time;
                            res.data.score_details.used_time = this.xwyLib.secondsToHoursMinutesSeconds(seconds);
                        }

                        this.examResultScoreText = this.xwy_config.examResultScoreText(this.params.activityID, Number(res.data.score_details.score), this.activity_detail?.conf?.active?.must_exam?.score || 100)
                        
                        this.score_details = res.data.score_details;
                        this.total_count = res.data.score_details.total_count || {}
                        if (res.data.exam_details) this.exam_details = res.data.exam_details;

                        this.get_question_list(res.data.user_submit_answer_list);

						            this.showMedalPopup(res.data.score_details.score)
                    }
                };
                this.xwyLib.ajax(obj);
            },


			async showMedalPopup(score) {
				let activity_detail = app.globalData.activity_detail

				if (!activity_detail) {
					const res = await xwy_api.request({
						url: 'front.flat.sport_step.active_list/active_details',
						data: {
							access_token: app.globalData.access_token,
							active_id: this.params.activityID
						}
					})

					activity_detail = res?.data?.active_details || {}

				}

				this.activity_detail = activity_detail

				if (!activity_detail) return false

				const point_detail = await xwy_api.request({
					url: 'front.flat.sport_step.active_list/map_point_details',
					data: {
						access_token: app.globalData.access_token,
						id: this.params.point_id
					}
				})

				this.point_detail = point_detail?.data?.map_point_details || {}

				const answer_score = Number(score)
				const min_score = activity_detail.conf.active.must_exam?.score || 0
          if (answer_score >= min_score && activity_detail?.rank_set?.medal_open && this.point_detail?.medal_details?.logo) {
              this.$refs.medal_popup.open()
          }

          const {lottery_id, must_exam, score_min} = this.point_detail?.conf?.lottery || {}
          if (lottery_id && must_exam && answer_score >= score_min) {
              this.lottery_id = lottery_id
              this.lottery_button_show = true
          }
			},

			uniPopupClose(ref) {
				this.$refs[ref].close()
			},

			toLottery() {
				uni.navigateTo({
					url: '/pages/lottery/user/lottery?id=' + this.lottery_id + '&active_id=' + this.params.activityID + '&point_id=' + this.params.point_id
				})
			},

            // 获取题目列表
            get_question_list(list) {
                if (!this.xwyLib.isArray(list)) return
                let err_num = 0;
                list.forEach(val => {
                    val.question_details.question_types_title = this.xwyLib.get_question_types_text(val
                        .question_details.question_types);

                    // 题目是否做错了
                    val.question_error = false;

                    // 获取题目分数
                    val.compute_score = 0;
                    if (val.admin_set_score && val.admin_set_score.score) val.compute_score = val
                        .admin_set_score.score || 0;
                    if ((!val.admin_set_score || !val.admin_set_score.score) && val.score) val
                        .compute_score =
                        val.score || 0;

                    // 单选 多选 判断
                    if (val.question_details.answer_option?.length && this.xwyLib.judge_types(val
                            .question_details.question_types)) {
                        let right_answer_str = '',
                            user_answer_str = '';
                        val.question_details.answer_option.forEach((v, i) => {
                            v.letter = this.xwyLib.numberToLetter(i)
                            if (v.is_right) right_answer_str += `${v.letter},`;
                            if (v.is_right) {
                                v.bg_color = 'gre_bgcolor';
                                v.fz_color = 'gre_color';
                            }
                            val.user_answer.forEach(value => {
                                if (value == i) {
                                    v.is_select = true;
                                    user_answer_str += `${v.letter},`;

                                    if (!v.is_right) {
                                        v.bg_color = 'red_bgcolor';
                                        v.fz_color = 'red_color';
                                        val.compute_score = 0;
                                        val.question_error = true;
                                        err_num++
                                    }
                                }
                            })
                        })

                        if (right_answer_str) val.right_answer_str = right_answer_str.substr(0, right_answer_str
                            .length - 1);

                        if (user_answer_str) {
                            val.user_answer_str = user_answer_str.substr(0, user_answer_str.length - 1);
                        } else {
                            val.compute_score = 0;
                            val.question_error = true;
                            err_num++
                        }
                    }

                    // 填空题
                    if (val.question_details.question_types == 4 && this.xwyLib.isArray(val.question_details
                            .answer_option)) {
                        val.question_details.answer_option.forEach((v, i) => {
                            v.text = val.user_answer[i];
                            console.log('v', v);
                            if (this.xwyLib.isArray(v.is_right)) {
                                let isTrue = false;
                                v.is_right.forEach((is_v, is_i) => {
                                    if (is_v.text == v.text) isTrue = true;
                                })
                                if (isTrue) {
                                    v.fz_color = 'gre_color';
                                } else {
                                    v.fz_color = 'red_color';
                                    val.compute_score = 0;
                                    val.question_error = true;
                                    err_num++;
                                }
                            }
                        })
                    }
                })
                this.question_list = [...list];
                this.types_list[0].memo = `(${this.question_list.length})`;
                this.types_list[1].memo = `(${this.question_list.length - err_num})`;
                this.types_list[2].memo = `(${err_num})`;
                console.log('this.question_list', this.question_list);
            },

            // 跳转到其他页面
            skip_page(e) {
                if (e == 2) {
                    e = {
                        types: 'closeUrl',
                        url: `/pages/likou_dati/pages/exam/exam_details/exam_details?exam_id=${this.score_details.exam_id}`
                    };
                }
                this.xwyLib.skip_page(e);
            },

            back_exam_details() {
                uni.navigateBack()
            },

            previewImage(item) {
                let list = [];
                if (item.pic_list && item.pic_list.length) {
                    item.pic_list.forEach(val => {
                        list.push(val.url);
                    })
                }
                uni.previewImage({
                    current: 0,
                    urls: list,
                })
            },

            change_types(e) {
                if (this.types_idx == e) return
                this.types_idx = e;
            }
        }
    }
</script>

<style scoped lang='scss'>
	@import url('@/pages/likou_dati/common/css/common.scss');
	@import url('@/pages/likou_dati/common/css/iconfont.css');
	@import url('@/pages/likou_dati/common/css/public.css');

  .content {
      padding-bottom: 70px;
  }

    .btn-box .btn {
        width: 60%;
        padding: .5rem 0;
        text-align: center;
        margin: 1rem;
    }

    .score {
        >text:first-child {
            font-size: 3rem;
            padding-right: .3rem;
        }
    }

    .category-box {
        position: sticky;
        left: 0;
        top: 0;
        width: 100%;
    }

    .question-box {
        width: 100%;

        .question-title {

            .change_score {
                min-width: 5rem;

                input {
                    border-bottom: 1px solid #ddd;
                }
            }

            .left {
                min-width: calc(100% - 5rem);
            }
        }

        .answer-option-list {
            white-space: pre-line;

            .letter-box {
                width: 2rem;
                min-width: 2rem;
                height: 2rem;
                background-color: #eee;
                margin-right: .5rem;
            }
        }
    }

    .question-item {
        width: 100%;

        &:last-child {
            padding-bottom: 0;
        }

        .box {
            width: 100%;

            .blank_box {
                width: 100%;

                .blank_bottom {

                    >view {
                        display: inline-block;
                    }
                }
            }
        }
    }

    .question-explain {
        padding: .5rem;
    }

	.medal-popup {
		width: 300px;
		height: 375px;
		background-image: url(https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/window/medal.png);
		background-repeat: no-repeat;
		background-position: center;
	}

	.medal-popup .medal-img-view {
		padding-top: 115px;
	}
	.medal-popup .medal-img {
		height: 100px;
	}
	.medal-popup .look-medal-list {
		width: 150px;
		line-height: 36px;
		border-radius: 18px;
		/* background: linear-gradient(#eb4529, #e69045); */
		/* border: 2px solid #f7d4c4; */
		background-color: #edc595;
		color: #a14a2a;
	}

</style>
