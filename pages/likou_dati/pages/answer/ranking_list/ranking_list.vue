<template>
    <view class='content'>
          <view class="top-select">
            <liuyuno-tabs v-if="category_list && category_list.length > 1" :tabData="category_list" @tabClick='change_tabs'
                :activeIndex="category_idx" />
        </view> 

        <!-- <view v-if="total" class="f-j-a-c p_7">
            <text class="gray_color">共 {{total}} 人</text>
        </view> -->

        <view v-if="rank_types_text" class="p_7">
            <button class="button-normal text-left" open-type="contact">
                <text class="font_9 org_color">{{rank_types_text.rules}}</text>
                <!--<text v-if="!rank_set.exam_top_rank_multi"
                    class="font_9 gray_color">查看更多{{rank_types_text.top_rank_name}}数据</text>-->
            </button>
        </view>

        <!-- #ifdef H5 -->
        <view v-if="category_idx === 0" class="p10">
            <view class="flex-kai">
                <view style="width: 100%;">
                    <uni-easyinput v-model="userid" placeholder="按会员号搜索" 
                                   @confirm="get_exam_ranking_list(1)"/>
                </view>
                <view class="userid-search-button bg-light-primary color-white" 
                      hover-class="navigator-hover" @click="get_exam_ranking_list(1)">搜索
                </view>
            </view>
        </view>
        <!-- #endif -->

        <!-- 排行榜 -->
        <view class="ranking-box">
            <view v-if="category_idx == 0" class="ranking-item">
                <view v-for="(item,index) in ranking_list" :key="index">
                    <view class="ranking-list-box gray_bdrbdashed">
                        <view class="ranking-list f">
                            <view v-if="is_master" class="select-box f-a-c">
                                <view class="select-item f-j-a-c" :class="item.is_select?'blue_bdrsolid':''"
                                    @click.stop="change_ranking_select(index)">
                                    <i v-if="item.is_select" class="iconfont blue_color font1_5">&#xe6ad;</i>
                                </view>
                            </view>
                            <view class="logo f-a-c pr_5" @click.stop="back_to_see_result(item)">
                                <i v-if="index < 3" class="iconfont font1_8 pr_7"
                                    :class="index == 0?'gold_color':index == 1?'silver_color':index == 2?'copper_color':''"
                                    v-html="index == 0?'&#xe73f;':index == 1?'&#xe741;':index == 2?'&#xe740;':''"></i>
                                <view class="idx font1_2 pr_7 f-j-a-c" v-if="index >= 3">{{index+1}}</view>
                                <view class="img f-d-c-a-c">
                                    <image class="radius50" :src="item.headimg?item.headimg:avatar" mode="aspectFill">
                                    </image>
                                </view>
                            </view>
                            <view class="f-d-c-j-sb"
                                :style="{'width':is_master?'calc(100% - 14.5rem)':'calc(100% - 12rem)'}"
                                @click.stop="back_to_see_result(item)">
                                <view class="one-hidden pb_7">
                                    {{item.xingming || item.nickname || '游客'}}
                                </view>
                                <view class="time memo_text">
                                    {{item.create_time || ''}}
                                </view>
                            </view>
                            <!-- 分数 -->
                            <view class="f-d-c-j-sb text-right" style="width: 6rem;">
                                <view class="pb_5">
                                    {{item.score?parseFloat(item.score).toFixed(2):0}}
                                    <text class="memo_text pl_1">分</text>
                                </view>
                                <view v-if='item.amout' class="gray_color f-j-e-a-c font_8">
                                    <i class="iconfont red_color pr_2">&#xe6c2;</i>
                                    {{item.redpack_amount / 100}} 元
                                </view>
                                <view v-if="item.amount" class="memo_text f-j-e-a-c">
                                    <i class="iconfont red_color pr_2 font_9">&#xe6c2;</i>
                                    {{item.amount}} 元
                                </view>
                                <view v-if='item.used_time' class="time memo_text">
                                    {{item.used_time}}
                                </view>
                            </view>
                        </view>
                       <!-- <view v-if="is_master" class="text font_8 text-right" @click.stop="back_to_see_result(item)">
                            <text class="gray_color gray_bdrdashed">答题详情</text>
                        </view> -->
                    </view>
                </view>
            </view>

            <view v-if="category_idx > 0" class="ranking-item">
                <view v-for="(item,index) in ranking_list" :key="index">
                    <view class="ranking-list-box gray_bdrbdashed">
                        <view class="ranking-list f">
                            <view class="logo f-a-c pr_5">
                                <i v-if="index < 3" class="iconfont font1_8 pr_7"
                                    :class="index == 0?'gold_color':index == 1?'silver_color':index == 2?'copper_color':''"
                                    v-html="index == 0?'&#xe73f;':index == 1?'&#xe741;':index == 2?'&#xe740;':''"></i>
                                <view class="idx font1_2 pr_7 f-j-a-c" v-if="index >= 3">{{index+1}}</view>
                                <view class="img f-d-c-a-c">
                                    <image class="radius50" :src="item.headimg?item.headimg:avatar" mode="aspectFill">
                                    </image>
                                </view>
                            </view>
                            <view class="f-d-c-j-c" style="width:calc(100% - 12rem)">
                                <view class="one-hidden pb_7">
                                    {{item.xingming || item.nickname || '游客'}}
                                </view>
                            </view>
                            <!-- 分数 -->
                            <view class="f-d-c-j-c text-right" style="width: 6rem;">
                                <view class="pb_5">
                                    <text
                                        v-if="rank_types == 2">{{item.user_max_score?parseFloat(item.user_max_score).toFixed(2):0}}</text>
                                    <text
                                        v-if="rank_types == 3">{{item.all_score?parseFloat(item.all_score).toFixed(2):0}}</text>
                                    <text
                                        v-if="rank_types == 4">{{item.average_score?parseFloat(item.average_score).toFixed(2):0}}</text>
                                    <text class="memo_text pl_1">分</text>
                                </view>
                                <view v-if='(rank_types == 2 || rank_types == 4) && item.used_time' class="time memo_text">
                                    {{item.used_time}}
                                </view>
                                <view v-if='rank_types == 3 && item.all_used_time' class="time memo_text">
                                    {{item.all_used_time}}
                                </view>
                            </view>
                        </view>
                    </view>
                </view>

                <!--<view v-if="!rank_set.exam_top_rank_multi && ranking_list && ranking_list.length > 3" class="err_text">
                    <button class="button-normal f-j-a-c width100" open-type="contact">
                        <text class="blue_color">查看更多排行榜数据</text>
                    </button>
                </view>-->
            </view>

            <view v-if="err_text" class="err_text">
                {{err_text}}
            </view>
        </view>

        <!-- 底部操作栏 -->
        <view class="tabbar-box f">
            <view class="left-box f-a-c">
                <view class="item" @click.stop="skip_page(8)">
                    <i class="iconfont f-j-a-c">&#xe638;</i>
                    <view class="text f-j-a-c">
                        首页
                    </view>
                </view>
            </view>
            <view class="right-btn">
                <view class="btn blue_bgcolor f-j-a-c" @click.stop="skip_exam">
                    返回考卷
                </view>
            </view>
        </view>

        <!-- 删除排行榜 -->
        <view v-if="select_ranking_list && select_ranking_list.length" class="tabbar-box f">
            <view class="left-box f-j-sb-a-c p1">
                <view class="f-a-c">
                    <view class="select-box">
                        <view class="select-item f-j-a-c" :class="is_allselect?'blue_bdrsolid':''"
                            @click.stop="change_allselect">
                            <i v-if="is_allselect" class="iconfont blue_color font1_5">&#xe6ad;</i>
                        </view>
                    </view>
                    <view class="select-text">
                        选择
                    </view>
                </view>
            </view>
            <view class="right-btn bottom_btn f-j-e-a-c p_5">
                <view class="btn gray_bdrsolid gray_color f-j-a-c" @click.stop="cancel_all_ranking">
                    <i class="iconfont gray_color font1_3">&#xe625;</i>
                </view>
            </view>
        </view>

        <!-- 悬浮按钮 -->
        <view v-if="is_master" class="float-box">
            <view v-if="rank_types > 1 && other_list.length" class="float-item self-item" @click.stop="export_excel">
                <i class="iconfont">&#xe7af;</i>
            </view>

            <view
                v-if="rank_types === 1"
                class="float-item self-item"
                @click.stop="export_word"
            >
                <i class="iconfont">&#xe7ba;</i>
            </view>
        </view>


        <!-- 搜索条件 -->
        <uni-popup ref="screening" type="center" :mask-click="false">
            <view class="details-box memo_text">
                <view class="item-box">
                    <!-- 排行榜类型 -->
                    <view v-if="other_list && other_list.length" class="details-item">
                        <view class="top">
                            排行榜类型
                        </view>
                        <view class="bottom">
                            <picker :range="other_list" range-key="name" :value="other_idx" @change="change_other_list">
                                <view class="person-select f-j-sb-a-c">
                                    <view class="f-a-c">{{other_list[other_idx].name}}</view>
                                    <i class="iconfont">&#xe631;</i>
                                </view>
                            </picker>
                        </view>
                    </view>
                    <!-- 区间选择 -->
                    <view v-if="section_list && section_list.length" class="details-item">
                        <view class="top">
                            分批导出
                        </view>
                        <view class="bottom">
                            <picker :range="section_list" range-key="name" :value="section_idx"
                                    @change="change_section_list">
                                <view class="person-select f-j-sb-a-c">
                                    <view class="f-a-c">{{section_list[section_idx].name}}</view>
                                    <i class="iconfont">&#xe631;</i>
                                </view>
                            </picker>
                        </view>
                    </view>
                </view>

                <view class="btn-box f-j-sb-a-c p0">
                    <view class="btn" @click.stop="close_screening_popup">
                        取消
                    </view>
                    <view class="btn gre_color" @click.stop="excel_ranking_list">
                        导出excel数据
                    </view>
                </view>
            </view>
        </uni-popup>

        <emptyData v-if="show_empty" :contact="contact_text"></emptyData>

        
    </view>
</template>

<script>
    let app = getApp();
    import {
        setClipboardData,
        getClipboardData
    } from '@/pages/likou_dati/uni_modules/u-clipboard/js_sdk'
	import emptyData from "@/pages/likou_dati/components/emptyData/emptyData.vue"
	import liuyunoTabs from "@/pages/likou_dati/components/liuyuno-tabs/liuyuno-tabs.vue"
    export default {
		components: {
			emptyData,
			liuyunoTabs
		},
        data() {
            return {
                params: {},
                avatar: this.imgImport.not_headimg,
                show_empty: false,
                userInfo: {},
                ad_list: [],

                category_list: [],
                category_idx: 0,
                rank_types: 2,


                other_list: [],
                other_idx: 0,

                section_list: [], // 区间列表 1000/组
                section_idx: 0,

                ranking_list: [], // 考试排行榜
                page: 1,
                err_text: '',
                is_lastpage: false,
                total: 0, // 总共多少人
                rank_types_text: '',

                is_master: false,
                is_allselect: false,
                select_ranking_list: [],
                contact_text: '',
                
                userid: '',

                // rank_set: {},
            }
        },
        onLoad(options) {
            // console.log('获取的参数options', options);
            this.params = options;
            this.xwyLib.setBarTitle('排行榜')
            this.xwyLib.init(() => {
                this.userInfo = app.globalData.userInfo;

                if (this.params.userid && this.params.userid == this.userInfo.id) {
                    this.is_master = true;
                    this.judge_ranking_select_isAll();
                }

                this.init();
            })
        },
        methods: {
            async init() {
                this.get_exam_rank_category();
                // 个人排行榜
                this.get_exam_ranking_list();
            },

            // 获取排行榜分类
            get_exam_rank_category() {
                let obj = {
                    exam_id: this.params.exam_id,
                    success: res => {
                        let list = res.top_rank_category_list;
                        /*this.category_list.push({
                            name: '个人排行榜',
                            value: 0,
                            types: 1,
                        })*/
                        list.forEach((val, idx) => {
                            this.category_list.push({
                                name: val.name,
                                value: idx + 1,
                                types: val.types,
                            })
                        })

                        this.rank_types = list[0].types

                        this.other_list = list.filter(val => val.types != 1);
                        // this.rank_set = res?.exam_details?.rank_set || {};

                        if (res?.exam_details && !this.exam_details) this.exam_details = res.exam_details
                    }
                }
                this.xwyLib.get_exam_rank_category(obj);
            },

            // 个人排行榜
            get_exam_ranking_list(page = 1) {
                let obj = {
                    exam_id: this.params.exam_id,
					active_id: this.params.active_id,
                    page,
                    perpage: 20,
                    before: res => {
                        this.err_text = '';
                        if (page == 1) {
                            this.ranking_list = [];
                            this.total = 0;
                            this.is_lastpage = false;
                            this.show_empty = false;
                            this.rank_types_text = '';
                            this.contact_text = '';
                        }
                    },
                    success: res => {
                        console.log('获取考卷个人排行榜', res);
                        res.data.forEach(val => {
                            if (val.used_time) val.used_time = this.xwyLib
                                .secondsToHoursMinutesSeconds(val.used_time);
                            if (val.submit_time) val.submit_time = this.xwyLib.timestampToTime(val
                                .submit_time / 1000);


                            if (val.must_submit) {
                                let must_submit = ''
                                if (this.xwyLib.isJSON(val.must_submit)) {
                                    must_submit = JSON.parse(val.must_submit);
                                } else {
                                    must_submit = val.must_submit;
                                }
                                if (this.xwyLib.isArray(must_submit)) {
                                    for (let i in must_submit) {
                                        if (must_submit[i].name == 'xing_ming') {
                                            val.xingming = must_submit[i].value;
                                            break;
                                        }
                                    }
                                }
                            }

                        })
                        let list = [...res.data];
                        if (page > 1) {
                            list = [...this.ranking_list, ...res.data];
                            if (res.data.length < 20) this.err_text = '没有更多了，别再拉了！'
                        }

                        this.ranking_list = [...list];
                        this.total = res.total;
                        this.is_lastpage = res.is_lastpage;
                        this.page = page + 1;
                    },
                    fail: res => {
                        if (page == 1) {
                            this.show_empty = true;
                            return
                        }
                        this.err_text = res.info;
                    }
                };

                if (this.userid) obj.userid = this.userid

				// 鲜繁客人的活动排行榜只显示最高分数
				if (this.params.active_id === '0a83b3f416d6f773e8ae7c339e74b9f0') obj.get_max = true
                this.xwyLib.get_exam_ranking_list(obj)
            },


            // 其他排行榜
            get_exam_other_ranking_list(page = 1) {
                let types = 2;
                if (this.xwyLib.isArray(this.category_list)) {
                    types = this.category_list[this.category_idx].types;
                }

                let rank_set = this.rank_set,
                    perpage = 20;
                /*if (!rank_set.exam_top_rank_multi) {
                    perpage = 3;
                }*/

                let obj = {
                    exam_id: this.params.exam_id,
                    page,
                    perpage,
                    types,
                    before: res => {
                        this.err_text = '';
                        if (page == 1) {
                            this.ranking_list = [];
                            this.total = 0;
                            this.is_lastpage = false;
                            this.show_empty = false;
                            this.contact_text = '';
                            this.rank_types_text = '';
                        }
                    },
                    success: res => {
                        console.log('获取考卷其他排行榜', res);
                        res.top_rank_data.forEach(val => {
                            if (val.must_submit) {
                                let must_submit = ''
                                if (this.xwyLib.isJSON(val.must_submit)) {
                                    must_submit = JSON.parse(val.must_submit);
                                } else {
                                    must_submit = val.must_submit;
                                }
                                if (this.xwyLib.isArray(must_submit)) {
                                    for (let i in must_submit) {
                                        if (must_submit[i].name == 'xing_ming') {
                                            val.xingming = must_submit[i].value;
                                            break;
                                        }
                                    }
                                }
                            }

                            if (val.used_time) val.used_time = this.xwyLib.secondsToHoursMinutesSeconds(val.used_time);
                            if (val.all_used_time) val.all_used_time = this.xwyLib.secondsToHoursMinutesSeconds(val.all_used_time);

                        })
                        let list = [...res.top_rank_data];
                        if (page > 1) {
                            list = [...this.ranking_list, ...res.top_rank_data];
                            if (res.top_rank_data.length < 20) this.err_text = '没有更多了，别再拉了！'
                        }

                        this.ranking_list = [...list];
                        this.page = page + 1;
                        this.rank_types_text = res?.rank_types_text;
                    },
                    fail: res => {
                        if (page == 1) {
                            this.show_empty = true;
                            /*if (!this.rank_set.exam_top_rank_multi) {
                                this.contact_text = '暂无数据，联系客服'
                            }*/
                            return
                        }
                        this.err_text = res.info;
                    }
                };
                this.xwyLib.get_exam_other_ranking_list(obj)
            },


            // 跳转到其他页面
            skip_page(e) {
                this.xwyLib.skip_page(e);
            },

            // 返回考卷
            skip_exam() {
                uni.navigateBack({
                    delta: 1,
                })
            },

            async copy_excel_url(url) {
                let res = await setClipboardData(url).catch(err => {
                    this.xwyLib.alert('复制失败！')
                });
                if (res) this.xwyLib.showToast('复制成功！');
            },


            // 改变评论选中
            change_ranking_select(e) {
                let ranking_list = this.ranking_list;
                this.$set(this.ranking_list[e], 'is_select', !this.ranking_list[e].is_select);
                this.judge_ranking_select_isAll();
            },

            change_allselect() {
                this.is_allselect = !this.is_allselect;
                let list = [...this.ranking_list];
                if (!this.xwyLib.isArray(list)) return
                if (this.is_allselect) {
                    list.forEach(val => {
                        val.is_select = true;
                    })
                    this.select_ranking_list = this.ranking_list;
                } else {
                    list.forEach(val => {
                        val.is_select = false;
                    })
                    this.select_ranking_list = [];
                }
            },

            judge_ranking_select_isAll() {
                let list = [...this.ranking_list];
                let len_list = [];
                list.forEach(val => {
                    if (val.is_select) {
                        len_list.push(val)
                    }
                })
                this.select_ranking_list = len_list;
                if (len_list.length != list.length) {
                    this.is_allselect = false;
                } else {
                    this.is_allselect = true;
                }
            },

            cancel_all_ranking() {
                uni.showModal({
                    content: '是否删除所选用户排行榜？',
                    confirmText: '删除',
                    success: res => {
                        if (res.confirm) {
                            let list = [...this.select_ranking_list];
                            if (!this.xwyLib.isArray(list)) return
                            let ids = '';
                            list.forEach((val, idx) => {
                                if (idx == 0) {
                                    ids = val.id;
                                } else {
                                    ids += `,${val.id}`
                                }
                            });
                            this.cancel_ranking_post(ids);
                        }
                    }
                })
            },

            async cancel_ranking_post(ids) {
                if (this.is_load) return
                this.is_load = true;
                this.xwyLib.showLoading(`删除中...`);

                let params = {
                    ids,
                    exam_id: this.params.exam_id,
                };
                let obj = {
                    data: params,
                    url: `front.flat.exam.user_answer/del_user_exam_score`,
                    success: res => {
                        uni.hideLoading()
                        this.is_load = false;
                        console.log('删除排行', res);
                        if (!res || !res.status) {
                            this.xwyLib.alert(res.info || '删除失败');
                            return false
                        }

                        uni.showToast({
                            title: res.info || '删除成功',
                            icon: !res.info || res.info.length < 8 ? 'success' : 'none'
                        })

                        setTimeout(() => {
                            this.select_ranking_list = [];
                            this.get_exam_ranking_list();
                        }, 1000)

                    }
                };
                this.xwyLib.ajax(obj);
            },

            // 回看成绩
            back_to_see_result(item) {
                if (!this.is_master) return
                let url = `/pages/likou_dati/pages/answer/answer_result/answer_result?score_id=${item.id}`;
                this.xwyLib.routeJump(url);
            },

            // 更换排行榜类型
            change_tabs(e) {
                if (this.category_idx == e) return
                this.category_idx = e;
                this.rank_types = this.category_list[e].types;
                if (e == 0) this.get_exam_ranking_list();
                if (e > 0) {
                    this.get_exam_other_ranking_list();
                }
            },

            

            // excel导出数据
            export_excel() {
                this.get_exam_user_count();
            },

            export_word() {
                let url = `/pages/likou_dati/pages/answer/ranking_list/export_word`
                url += `?exam_id=${this.params.exam_id}&userid=${this.params.userid}`
                url += `&exam_name=${encodeURIComponent(this.exam_details.exam_name)}`
                this.xwyLib.routeJump(url)
            },

            // 关闭搜索条件
            close_screening_popup() {
                this.$refs.screening.close();
            },

            change_other_list(e) {
                this.other_idx = e.detail.value - 0;
            },

            change_section_list(e) {
                this.section_idx = e.detail.value - 0;
            },

            excel_ranking_list() {
                let text = `${this.other_list[this.other_idx].name}${this.section_list[this.section_idx].name}` || '';
                this.xwyLib.showModal(`是否导出${text}的数据？`, {
                    confirmText: '导出',
                    success: res => {
                        if (res.confirm) this.get_excel_ranking_list();
                    }
                })
            },

            get_excel_ranking_list() {
                this.xwyLib.showLoading('正在导出...');
                let data = {
                        exam_id: this.params.exam_id,
                        types: this.other_list[this.other_idx].types,
                        page: this.section_idx + 1,
                        perpage: 1000,
                    },
                    obj = {
                        data,
                        url: `front.flat.exam.export/export_exam_sum_average_high_top_rank`,
                        success: res => {
                            uni.hideLoading();
                            if (res.status == 1 && res?.data?.url) {
                                this.xwyLib.copy_text({
                                    url: res.data.url,
                                    text: res.info,
                                })
                                return
                            }
                            this.xwyLib.alert(res.info);
                        }
                    };
                this.xwyLib.ajax(obj);
            },


            get_exam_user_count() {
                this.xwyLib.showLoading('', true);
                const data = {
                        exam_id: this.params.exam_id,
                    },
                    obj = {
                        data,
                        url: `front.flat.exam.export/get_exam_user_count`,
                        success: res => {
                            uni.hideLoading();
                            if (res.status == 1 && res?.data?.person_count) {
                                let count = res.data.person_count,
                                    index = parseInt(count / 1000),
                                    digit = count % 1000,
                                    list = [];

                                if (index > 0) {
                                    for (let i = 0; i < index; i++) {
                                        list.push({
                                            name: `第${1000 * i + 1}名 ~ 第${1000 * (i+1)}名`
                                        })
                                        if (i == index - 1) {
                                            list.push({
                                                name: `第${1000 * (i + 1) + 1}名 ~ 第${1000 * (i + 1) + digit}名`
                                            })
                                        }
                                    }
                                } else {
                                    list = [{
                                        name: `第1名 ~ 第${digit}名`
                                    }]
                                }

                                this.section_list = list;

                                this.other_idx = this.other_list.findIndex(val => val.types == this.rank_types)

                                if (this.section_idx < 0 || this.section_idx >= this.section_list.length) this
                                    .section_idx = 0;

                                if (this.xwyLib.isArray(this.section_list)) {
                                    this.$refs.screening.open();
                                    return
                                }
                                this.xwyLib.alert('没有获取到可以导出的数据！')
                                return
                            }
                            this.xwyLib.alert('初始化excel表格失败！')
                        }
                    };
                this.xwyLib.ajax(obj);
            },

        },


        onReachBottom() {
            if (this.is_lastpage) return
            if (this.category_list[this.category_idx].types == 1) {
                this.get_exam_ranking_list(this.page)
            }
            if (this.category_list[this.category_idx].types > 1) {
                // if (!this.rank_set.exam_top_rank_multi) return
                this.get_exam_other_ranking_list(this.page)
            }
        }
    }
</script>

<style scoped lang='scss'>
	@import url('@/pages/likou_dati/common/css/common.scss');
	@import url('@/pages/likou_dati/common/css/iconfont.css');
	@import url('@/pages/likou_dati/common/css/public.css');

    .content {
        padding-bottom: calc(60px + 1rem);

        .ranking-box {
            width: 100%;
            padding: 0 1rem;

            .ranking-item {
                width: 100%;

                .ranking-list-box {
                    width: 100%;
                    padding: 1.2rem 0;

                    .ranking-list {
                        width: 100%;

                        .select-box {
                            width: 2.5rem;
                        }

                        .logo {
                            width: 7rem;

                            .idx {
                                width: 2.5rem;
                            }

                            .iconfont {
                                width: 2.5rem;
                            }

                            .img {
                                width: 3.5rem;

                                image {
                                    width: 100%;
                                    height: 3.5rem;
                                }
                            }
                        }
                    }


                    >.text {
                        padding-top: .5rem;

                        text {
                            border-radius: 10rem;
                            padding: .1rem .5rem;
                        }
                    }
                }
            }
        }
    }

    .tabbar-box .bottom_btn .btn {
        width: calc((100% - 1rem) / 2);
        height: 0;
        font-size: 1rem;
        border-radius: 10rem;
        padding: 1.1rem .5rem;
    }

    .float-item {
        background-color: rgba(0, 0, 0, 0.5);
    }
    
    
    
    .userid-search-button {
        line-height: 36px;
        border-radius: 2px;
        width: 60px;
        min-width: 60px;
        margin-left: 10px;
        text-align: center;
    }
</style>
