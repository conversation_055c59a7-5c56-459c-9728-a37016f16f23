<template>
    <view class="content">
        <view class="top-select p_7">
            <view class="f-j-sb-a-c">
                <view class="dark_bluecolor">
                    <i class="iconfont display-ib pr_2">&#xe675;</i>
                    <text>成绩单导出</text>
                </view>
                <i class="iconfont display-ib" @click.stop="open_popup">&#xe62a;</i>
            </view>
            <view v-if="total && list && list.length" class="pt_5 memo_text">
                <uni-data-select
                    :localdata="list"
                    v-model="page"
                    :clear="false"
                    @change="change_page"
                ></uni-data-select>
            </view>
        </view>

        <!-- 排行榜 -->
        <view class="ranking-box p0_7 width100">
            <view v-for="(item, index) in ranking_list" :key="index">
                <view class="ranking-list-box gray_bdrbdashed p7_0">
                    <view class="ranking-list f">
                        <view class="select-box f-a-c min-width2">
                            <view
                                class="select-item f-j-a-c"
                                :class="item.is_select ? 'blue_bdrsolid' : ''"
                                @click.stop="change_ranking_select(index)"
                            >
                                <i v-if="item.is_select" class="iconfont blue_color font1_5">&#xe6ad;</i>
                            </view>
                        </view>
                        <view class="logo f-a-c">
                            <i
                                v-if="page == 1 && index < 3"
                                class="iconfont font1_7 f-j-a-c"
                                :class="
                  index == 0
                    ? 'gold_color'
                    : index == 1
                    ? 'silver_color'
                    : index == 2
                    ? 'copper_color'
                    : ''
                "
                                v-html="
                  index == 0 ? '&#xe73f;' : index == 1 ? '&#xe741;' : index == 2 ? '&#xe740;' : ''
                "
                                @click.stop="copy_userid(item.userid)"
                            ></i>
                            <view
                                class="idx pr_5 f-j-a-c"
                                v-if="page > 1 || index >= 3"
                                @click.stop="copy_userid(item.userid)"
                            >
                                {{ (page - 1) * 500 + index + 1 }}
                            </view>
                            <view class="img f-d-c-a-c">
                                <image
                                    class="radius50"
                                    :src="item.headimg ? item.headimg : avatar"
                                    mode="aspectFill"
                                ></image>
                            </view>
                        </view>

                        <view class="pl_5" style="width: calc(100% - 8rem)">
                            <view class="f-a-c pb_2">
                                <view class="width100 one-hidden">
                                    {{ item.xingming || item.nickname || '匿名用户' }}
                                </view>
                                <view class="min-width5 f-j-e-a-c">
                                    <text class="red_color">
                                        {{ item.score ? parseFloat(item.score).toFixed(2) : 0 }}
                                    </text>
                                    <text class="memo_text pl_1">分</text>
                                </view>
                            </view>

                            <view
                                class="pb_2"
                                :class="item.redpack_amount || item.amount ? 'f-j-sb-a-c' : 'f-a-c'"
                            >
                                <view v-if="item.used_time" class="time memo_text">用时：{{
                                        item.used_time
                                    }}
                                </view>
                                <view class="f-a-c">
                                    <view v-if="item.redpack_amount" class="memo_text f-j-e-a-c">
                                        <i class="iconfont red_color pr_2 font_9">&#xe6c2;</i>
                                        {{ item.redpack_amount / 100 }} 元
                                    </view>
                                    <view v-if="item.amount" class="memo_text f-j-e-a-c">
                                        <i class="iconfont red_color pr_2 font_9">&#xe6c2;</i>
                                        {{ item.amount }} 元
                                    </view>
                                </view>
                            </view>

                            <view class="time memo_text">
                                {{ item.create_time }}
                            </view>
                            <view v-if="item.person_review_time" class="time memo_text">
                                阅卷时间：{{ item.person_review_time }}
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 底部操作栏 -->
        <view v-if="total" class="tabbar-box f">
            <view class="left-box f-j-sb-a-c p1">
                <view class="f-a-c" @click.stop="change_allselect">
                    <view class="select-box min-width2" style="width: 2rem">
                        <view class="select-item f-j-a-c" :class="is_allselect ? 'blue_bdrsolid' : ''">
                            <i v-if="is_allselect" class="iconfont blue_color font1_5">&#xe6ad;</i>
                        </view>
                    </view>
                    <view class="select-text memo_text">
                        <text>选择</text>
                        <text v-if="select_ranking_list.length" class="memo_text">
                            (已选{{ select_ranking_list.length }}条)
                        </text>
                    </view>
                </view>
            </view>
            <view class="right-btn bottom_btn f-j-sb-a-c pr_5 memo_text">
                <view class="gray_bdrsolid f-j-a-c p_5 radius10 width45" @click.stop="open_record">
                    <text>导出记录</text>
                </view>
                <view class="gray_bdrsolid f-j-a-c p_5 radius10 width45" @click.stop="export_word">
                    <i class="iconfont gray_color pr_2">&#xe7ba;</i>
                    <text>导出</text>
                </view>
            </view>
        </view>

        <!-- 悬浮按钮 -->
        <view class="float-box">
            <view class="float-item self-item" @click.stop="refresh_data">
                <i class="iconfont">&#xe653;</i>
            </view>
        </view>

        <uni-popup ref="search">
            <uni-popup-dialog
                mode="input"
                confirmText="搜索"
                placeholder="输入会员号查询"
                :duration="2000"
                @confirm="search"
            ></uni-popup-dialog>
        </uni-popup>

        <!-- 导出记录 -->
        <uni-popup ref="record" type="center" @maskClick="close_record()">
            <view class="details-box memo_text p0" style="min-height: 0; overflow-y: visible">
                <view class="p_7 pb0">
                    <view class="dark_bluecolor font1">
                        成绩单导出记录
                        <text class="memo_text">(两小时内)</text>
                    </view>
                    <scroll-view scroll-y style="max-height: 70vh">
                        <view
                            class="modif-item p1-0 f"
                            :class="index == 0 ? '' : 'gray_bdrtdashed'"
                            v-for="(item, index) in record_list"
                            :key="index"
                        >
                            <view class="width100 f">
                                <view class="memo_text">{{ index + 1 }}.</view>
                                <view class="pl_3">
                                    <view class="">
                                        {{ item.date }}
                                    </view>
                                    <!-- <view class="">
                                                          过期时间：{{item.expired}}
                                                      </view> -->
                                </view>
                            </view>
                            <view class="min-width6 f-j-e-a-c blue_color" @click.stop="copy_record(item)">
                                复制下载地址
                            </view>
                        </view>
                    </scroll-view>
                    <emptyData v-if="show_empty_record" text="还没有导出记录"></emptyData>
                </view>
                <i class="iconfont close-icon" @click.stop="close_record()">&#xe692;</i>
            </view>
        </uni-popup>

        <emptyData v-if="show_empty"></emptyData>
    </view>
</template>

<script>
let app = getApp()
export default {
    data() {
        return {
            params: {},
            avatar: this.imgImport.not_headimg,
            show_empty: false,
            userInfo: {},
            keywords: '',

            ranking_list: [], // 考试排行榜
            page: 1,
            total: 0, // 总共多少人

            is_allselect: false,
            select_ranking_list: [],

            record_list: [], // 导出word时存储的数据
            show_empty_record: false,

            single: 500,
            list: [],
        }
    },
    onLoad(options) {
        if (options.exam_name) {
            options.exam_name = decodeURIComponent(options.exam_name)
        }
        this.params = options
        this.xwyLib.setBarTitle('导出成绩单')
        this.xwyLib.init(() => {
            this.userInfo = app.globalData.userInfo
            this.judge_ranking_select_isAll()
            this.init()
        })
    },
    onUnload() {
        if (this.xwyLib.interstitialAd) this.xwyLib.interstitialAd.destroy()
    },
    methods: {
        async init() {
            this.get_exam_ranking_list()
        },

        search(e) {
            this.keywords = e
            this.$nextTick(() => {
                this.page = 1
                this.get_exam_ranking_list()
                this.close_popup()
            })
        },

        get_exam_ranking_list() {
            let obj = {
                exam_id: this.params.exam_id,
                page: this.page,
                perpage: this.single,
                before: (res) => {
                    this.ranking_list = []
                    this.total = 0
                    this.show_empty = false
                },
                success: (res, text) => {
                    res.data.forEach((val) => {
                        if (val.used_time) {
                            let used_time = val.used_time
                            val.used_time = this.xwyLib.secondsToHoursMinutesSeconds(used_time)
                        }
                        if (val.submit_time)
                            val.submit_time = this.xwyLib.timestampToTime(val.submit_time / 1000)

                        if (val.must_submit) {
                            let must_submit = ''
                            if (this.xwyLib.isJSON(val.must_submit)) {
                                must_submit = JSON.parse(val.must_submit)
                            } else {
                                must_submit = val.must_submit
                            }
                            if (this.xwyLib.isArray(must_submit)) {
                                for (let i in must_submit) {
                                    if (must_submit[i].name == 'xing_ming' && must_submit[i].value) {
                                        val.xingming = must_submit[i].value
                                        break
                                    }
                                }
                            }
                        }
                    })

                    this.ranking_list = res.data
                    this.total = res.total
                    if (this.page == 1) this.get_list()
                    this.judge_ranking_select_isAll()
                },
                fail: (res) => {
                    this.show_empty = true
                },
            }

            if (this.keywords) obj.userid = this.keywords
            this.xwyLib.get_exam_ranking_list(obj)
        },

        get_list() {
            const total = this.total,
                count = Math.ceil(total / this.single),
                list = []

            for (var i = 0; i < count; i++) {
                const obj = {
                    text: `第${this.single * i + 1}条 ~ 第${Math.min(this.single * (i + 1), total)}条记录`,
                    value: i + 1,
                }
                list.push(obj)
            }

            this.list = list
        },

        // 改变选中
        change_ranking_select(e) {
            this.$set(this.ranking_list[e], 'is_select', !this.ranking_list[e].is_select)
            this.judge_ranking_select_isAll()
        },

        change_allselect() {
            this.is_allselect = !this.is_allselect
            let list = [...this.ranking_list]
            if (!this.xwyLib.isArray(list)) return
            if (this.is_allselect) {
                list.forEach((val) => (val.is_select = true))
                this.select_ranking_list = this.ranking_list
                return
            }
            list.forEach((val) => (val.is_select = false))
            this.select_ranking_list = []
        },

        judge_ranking_select_isAll() {
            let list = this.xwyLib.copyAry(this.ranking_list)
            let len_list = []
            if (!this.xwyLib.isArray(list)) {
                this.is_allselect = false
                return
            }
            list.forEach((val) => {
                if (val.is_select) len_list.push(val)
            })
            this.select_ranking_list = len_list
            if (len_list.length != list.length) {
                this.is_allselect = false
                return
            }
            this.is_allselect = true
        },

        async cancel_ranking_post(ids, refresh = '') {
            this.xwyLib.showLoading(`删除中...`, true)

            const data = {
                ids,
                exam_id: this.params.exam_id,
            }
            const obj = {
                data,
                url: `front.flat.exam.user_answer/del_user_exam_score`,
                success: (res) => {
                    uni.hideLoading()
                    if (refresh == 1) {
                        this.select_ranking_list = []
                        this.get_exam_ranking_list()
                        return
                    }

                    if (!res || !res.status) {
                        this.xwyLib.alert(res.info || '删除失败')
                        return false
                    }

                    uni.showToast({
                        title: res.info || '删除成功',
                        icon: !res.info || res.info.length < 8 ? 'success' : 'none',
                    })

                    setTimeout(() => {
                        this.select_ranking_list = []
                        this.get_exam_ranking_list()
                    }, 1000)
                },
            }
            this.xwyLib.ajax(obj)
        },

        // 复制userid
        copy_userid(userid) {
            this.xwyLib.copy_text({
                url: `${userid}`,
                close_text: 1,
            })
        },

        // 刷新排行榜数据
        refresh_data() {
            this.cancel_ranking_post('', 1)
        },

        export_word() {
            if (!this.xwyLib.isArray(this.select_ranking_list)) {
                this.xwyLib.alert('请选择需要导出的成绩单！')
                return
            }

            this.export_word_post()

        },

        export_word_post() {
            const list = this.select_ranking_list

            const arr = list.map((val) => {
                const o = {
                    id: val.id,
                    xingming: val.xingming || val.nickname,
                    create_time: val.create_time,
                    total_count: val.total_count,
                    used_time: val.used_time,
                    must_submit: val.must_submit || '',
                }
                return o
            })

            const value = {
                exam_id: this.params.exam_id,
                exam_name: this.xwyLib.decodeURIComponent(this.params.exam_name),
                access_token: app.globalData.access_token,
                select_ranking_list: arr,
            }

            let timestamp = new Date().getTime()
            const name = `${app.globalData.access_token}_${this.params.exam_id}_${timestamp}`

            this.xwyLib.set_storage({
                name,
                value,
                expire: 8000,
                success: () => this.cost_user_integral(name, timestamp),
            })
        },

        // 扣减积分
        cost_user_integral(name, timestamp) {
            let url = `${this.xwyLib.static_url}web/wx-cloud-api/pages/report_word/#/`
            let params = `access_token=${app.globalData.access_token}&exam_id=${this.params.exam_id}&timestamp=${timestamp}`
            url += `?${params}`
            this.xwyLib.copy_text({
                url,
                text: `下载链接复制成功，有效期两小时，请及时在浏览器粘贴链接下载！`,
            })

            const NAME = `export_record-${this.params.exam_id}`
            const result = uni.getStorageSync(NAME) ? this.xwyLib.getStorage(NAME) : []
            const opt = {
                timestamp,
                url,
                date: this.xwyLib.timestampToTime(timestamp / 1000),
                expired: this.xwyLib.timestampToTime(timestamp / 1000 + 7200),
            }
            result.push(opt)
            this.xwyLib.setStorage(NAME, result)
        },

        open_popup() {
            this.$refs.search.open()
        },

        close_popup() {
            this.$refs.search.close()
        },

        open_record() {
            const name = `export_record-${this.params.exam_id}`
            const list = uni.getStorageSync(name) ? this.xwyLib.getStorage(name) : []
            const timestamp = +new Date()
            this.record_list = list.filter((val) => Number(val.timestamp + 7200000) > timestamp)
            if (this.xwyLib.isArray(this.record_list)) {
                this.xwyLib.setStorage(name, this.record_list)
            } else {
                this.show_empty_record = true
                uni.removeStorageSync(name)
            }
            this.$refs.record.open()
        },

        close_record() {
            this.$refs.record.close()
            this.show_empty_record = false
            this.record_list = []
        },

        copy_record(item) {
            this.xwyLib.copy_text({
                url: item.url,
                text: `下载链接复制成功，请及时在浏览器粘贴链接下载！`,
            })
        },

        change_page(e) {
            this.$nextTick(() => this.get_exam_ranking_list())
        },
    },
}
</script>

<style scoped lang="scss">
@import url('@/pages/likou_dati/common/css/common.scss');
@import url('@/pages/likou_dati/common/css/iconfont.css');
@import url('@/pages/likou_dati/common/css/public.css');

.content {
    padding-bottom: calc(70px + env(safe-area-inset-bottom));

    .ranking-box .ranking-list-box {
        width: 100%;

        .ranking-list {
            width: 100%;

            .select-box {
                width: 2rem;
            }

            .logo {
                width: 6rem;

                .idx {
                    width: 3rem;
                }

                .iconfont {
                    width: 3rem;
                }

                .img {
                    width: 3rem;

                    image {
                        width: 100%;
                        height: 3rem;
                    }
                }
            }
        }

        > .text {
            padding-top: 0.5rem;

            text {
                border-radius: 10rem;
                padding: 0.1rem 0.5rem;
            }
        }
    }
}

.tabbar-box .bottom_btn .btn {
    width: calc((100% - 1rem) / 2);
    height: 0;
    font-size: 1rem;
    border-radius: 10rem;
    padding: 1.1rem 0.5rem;
}

.float-item {
    background-color: rgba(0, 0, 0, 0.5);
}
</style>
