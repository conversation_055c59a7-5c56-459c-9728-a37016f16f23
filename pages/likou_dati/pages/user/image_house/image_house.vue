<template>
    <view class='content'>
        <view v-if="category_list && category_list.length && !params.types" class="top-select"
            style="padding: 0 1rem;height: 50px;">
            <liuyuno-tabs class="top-tabs" :tabData="category_list" :activeIndex="category_idx" @tabClick='change_tabs'
                :config="defaultConfig" />
        </view>

        <!-- 输入图片地址 -->
        <view class="p_7 pb0 f-a-c">
            <uni-easyinput class="width100" v-model="image_url" placeholder="输入图片地址并点击确认" />
            <view class="f-j-e-a-c min-width6 pl1">
                <view class="width100 p5_0 radius_3 blue_bgcolor text-center blk_shadow_005"
                      @click.stop="upload_img_input">
                    确认
                </view>
            </view>
        </view>

        <!-- 自己上传图片按钮 -->
        <view class="updata-icon-box">
            <view class="updata-icon-item f-j-a-c" @click.stop="upload_img">
                <i class="iconfont">&#xe602;</i>
            </view>
        </view>

        <!-- 图片列表 -->
        <view class="img-box f">
            <view class="img-item" style="padding-right: .5rem;">
                <view class="img-list f" v-if="(index+1) % 2 == 1" v-for="(item,index) in img_list" :key="index"
                    @click.stop="choose_img(index)">
                    <image :src="item.url" mode="widthFix"/>
                </view>
            </view>
            <view class="img-item" style="padding-left: .5rem;">
                <view class="img-list f" v-if="(index+1) % 2 == 0" v-for="(item,index) in img_list" :key="index"
                    @click.stop="choose_img(index)">
                    <image :src="item.url" mode="widthFix"/>
                </view>
            </view>
        </view>
        <view v-if="err_text" class="err_text">
            {{err_text}}
        </view>
    </view>
</template>

<script>
    let app = getApp();
    import defaultConfig from "@/pages/likou_dati/common/js/liuyuno-tabs-config";

	import liuyunoTabs from "@/pages/likou_dati/components/liuyuno-tabs/liuyuno-tabs.vue"

    export default {
		components: {
			liuyunoTabs
		},
        data() {
            return {
                params: {},
                image_url: '',

                // 分类
                category_list: [],
                category_idx: 0,
                defaultConfig,

                // 图片列表
                img_list: [],
                err_text: '',
                is_lastpage: false,
                page: 1,
            }
        },
        onLoad(options) {
            // console.log('获取的参数options', options);
            this.params = options;
            this.xwyLib.init(() => {
                this.init();
            })
        },
        methods: {
            async init() {
                this.xwyLib.setBarTitle('图片选择')
                let shopconfset = app.globalData.shopconfset;
                if (shopconfset?.extend_set?.upload_pic_flat_category_id) {
                    this.get_category_list(shopconfset?.extend_set?.upload_pic_flat_category_id);
                    if (this.params.types) return
                }
                this.get_user_storage_pic();
            },

            // 获取用户上传过的图片
            get_user_storage_pic(page = 1) {
                this.xwyLib.showLoading();
                this.err_text = '';
                if (page == 1) {
                    this.img_list = [];
                    this.is_lastpage = false;
                }
                let data = {
                    access_token: app.globalData.access_token,
                    page,
                    perpage: 20,
                };
                let obj = {
                    data,
                    url: `front.upload/upload_list`,
                    success: res => {
                        console.log('获取用户常用的图片', res);
                        uni.hideLoading();
                        if (res.status != 1 || !res.data.upload_data || !res.data.upload_data.data || !res
                            .data.upload_data.data.length) {
                            this.err_text = res.info;
                            return
                        }

                        let list = [...res.data.upload_data.data];

                        if (page > 1) {
                            list = [...this.img_list, ...res.data.upload_data.data];

                            if (res.data.upload_data.data.length < 20) {
                                this.err_text = '已经到底了，别再拉了！';
                            }
                        }

                        this.is_lastpage = res.data.upload_data.is_lastpage;
                        this.img_list = list;
                        this.page = page + 1;
                    }
                };
                this.xwyLib.ajax(obj);
            },

            // 获取图片分类
            get_category_list(id) {
                let opt = {
                    name: "常用图片",
                    sort_num: 0,
                    status: 1,
                    types: 5,
                    user_id: "",
                    _id: "",
                }
                this.xwyLib.get_image_category_list({
                    category_id: id,
                    success: res => {
                        if (!this.params.types) {
                            res.unshift({
                                ...opt
                            })
                        }
                        this.category_list = res;

                        if (this.params.types) {
                            for (let i in res) {
                                if (res[i].id == this.params.types) {
                                    this.category_idx = i;
                                    break;
                                }
                            }
                            this.get_img_list();
                        }
                    },
                    fail: res => {
                        if (this.params.types) return
                        this.category_list = [{
                            ...opt
                        }]
                    }
                })
            },

            // 获取图片列表
            async get_img_list(page = 1) {
                this.err_text = '';
                if (page == 1) {
                    this.img_list = [];
                    this.is_lastpage = false;
                }
                this.xwyLib.get_image_list_from_category({
                    types: this.category_list[this.category_idx].id,
                    page,
                    success: res => {
                        // console.log('获取图片列表', res);
                        let list = [...res.data];
                        if (page > 1) {
                            list = [...this.img_list, ...res.data];

                            if (res.data.length < 20) {
                                this.err_text = '已经到底了，别再拉了！';
                            }
                        }
                        this.is_lastpage = res.is_lastpage;
                        this.img_list = list;
                        this.page = page + 1;
                    },
                    fail: res => {
                        this.err_text = res;
                    }
                });
                uni.hideLoading();
            },

            // 切换分类
            change_tabs(e) {
                this.category_idx = e;
                let category_list = this.category_list;
                if (!category_list[e].id) {
                    this.get_user_storage_pic()
                    return
                }
                if (category_list[e].id) {
                    this.get_img_list();
                }
            },

            // 选择图片
            choose_img(e) {
                let img = this.img_list[e].url;
                app.globalData.upload_img = 1;
                let opt = {
                    picName: this.params.picName,
                    url: img,
                }
                uni.setStorageSync('image_house', JSON.stringify(opt));
                this.xwyLib.back_previous_page();
            },

            // 用户自己上传图片
            upload_img() {
                this.xwyLib.choose_img({
                    success: res => {
                        app.globalData.upload_img = 1;
                        let opt = {
                            picName: this.params.picName,
                            url: res[0].url,
                        }
                        uni.setStorageSync('image_house', JSON.stringify(opt));
                        this.xwyLib.back_previous_page();
                    }
                })
            },


            async is_img(opt = {}) {
                uni.getImageInfo({
                    src: opt.imgurl,
                    success: res => {
                        console.log('成功获取', res);
                        opt.success && opt.success(res);
                    },
                    fail: res => {
                        console.log('获取失败', res);
                        this.xwyLib.alert('请输入正确的图片地址！')
                    }
                })
            },

            upload_img_input() {
                const img = this.image_url;
                if (!img) {
                    this.xwyLib.alert('请输入图片地址！')
                    return
                }
                const obj = {
                    imgurl: img,
                    success: res => {
                        app.globalData.upload_img = 1;
                        let opt = {
                            picName: this.params.picName,
                            url: img,
                        }
                        uni.setStorageSync('image_house', JSON.stringify(opt));
                        this.xwyLib.back_previous_page();
                    }
                }
                this.is_img(obj)
            },

            // 上拉事件
            onReachBottom() {
                if (this.is_lastpage) {
                    this.err_text = '已经到底了，别再拉了！'
                    return
                }
                if (this.category_idx == 0) {
                    this.get_user_storage_pic(this.page)
                    return
                }
                if (this.category_idx > 0) {
                    this.get_img_list(this.page);
                }
            },

        }
    }
</script>

<style scoped lang='scss'>
	@import url('@/pages/likou_dati/common/css/common.scss');
	@import url('@/pages/likou_dati/common/css/iconfont.css');
	@import url('@/pages/likou_dati/common/css/public.css');

    .content {

        .top-tabs {
            position: sticky;
            left: 0;
            top: 0;
            background-color: #fff;
        }

        .updata-icon-box {
            width: 100%;
            padding: 1rem 1rem 0;

            .updata-icon-item {
                width: 7rem;
                height: 7rem;
                border-radius: .2rem;
                border: 1px solid #ddd;

                .iconfont {
                    font-size: 50px;
                    color: #ddd;
                }
            }
        }

        .img-box {
            width: 100%;
            padding: 1rem 1rem 0;
            flex-wrap: wrap;

            .img-item {
                width: 50%;
                /* width: calc((100% - 1rem) / 2); */
                /* height: calc((100vw - 3rem) / 4 * 1.25); */
                /* max-height: calc((640px - 3rem) / 4 * 1.25); */

                .img-list {
                    width: 100%;
                    margin-bottom: 1rem;
                    border-radius: .2rem;

                    image {
                        width: 100%;
                        height: 100%;
                        border-radius: .2rem;
                    }

                    .iconfont {
                        font-size: 2.5rem;
                        color: #ddd;
                    }
                }

            }
        }

        .upload-btn {
            width: 50%;
            padding: 1rem .5rem 0 1rem;

            .img-list {
                width: 100%;
                height: calc((50vw - 1.5rem) / 3 * 2);
                max-height: calc((320px - 1.5rem) / 3 * 2);
                background-color: rgba($color: #000000, $alpha: .6);
                border-radius: .2rem;

                .iconfont {
                    font-size: 2.5rem;
                    color: #ddd;
                }
            }
        }
    }
</style>
