<template>
    <view class='content' :class="{'inhidden': inhidden,'big-screen':big_screen_set.show == 1}">
        <!-- 非大图模式 -->
        <view v-if="!big_screen_set.show">
            <carousel v-if="exam_details.pics && exam_details.pics.length" :carousel="carouselSet"></carousel>
            <view class="details-box">
                <view class="exam-text p1">
                    <view class="exam-name f-a-c">
                        <view class="left" @click.stop="copy_exam_id">
                            {{exam_details.exam_name}}
                        </view>
                    </view>
                    <view class="exam-memo gray_color">
                        <text
                            v-if="!rand_question_str">{{exam_details.is_rand_question == 1?`随机出题：${cur_question_num}题`:`固定出题：${question_total}题`}}</text>
                        <text v-if="rand_question_str">
                            {{rand_question_str}}
                        </text>
                    </view>
                    <view v-if="exam_details.content" class="exam-memo gray_color">
                        {{exam_details.content}}
                    </view>
                    <view class="exam-time-box gray_color font_9 p5_0">
                        <view class="exam-time-item f-a-c">
                            <view class="left">
                                每人可答：
                            </view>
                            <view class="right">
                                {{exam_details.answer_limit_types === 2 ? '每天' : ''}}{{exam_details.set_limit_times == 99999?'不限次数':`${exam_details.set_limit_times}次`}}
                            </view>
                        </view>
                        <!-- <view v-if="exam_details.answer_time" class="exam-time-item f-a-c">
                            <view class="left">
                                考卷答题时间：
                            </view>
                            <view class="right">
                                {{exam_details.answer_time}} 分钟
                            </view>
                        </view> -->
                        <!-- <view class="exam-time-item f-a-c">
            					<view class="left">
            						创建时间：
            					</view>
            					<view class="right">
            						{{exam_details.create_date_time}}
            					</view>
            				</view> -->
                        <view v-if="exam_details.begin_time" class="exam-time-item f-a-c">
                            <view class="left">
                                开始答题时间：
                            </view>
                            <view class="right">
                                {{exam_details.begin_time}}
                            </view>
                        </view>
                        <view v-if="exam_details.end_time" class="exam-time-item f-a-c">
                            <view class="left">
                                结束答题时间：
                            </view>
                            <view class="right">
                                {{exam_details.end_time}}
                            </view>
                        </view>
                    </view>
                    <!-- 红包 -->
                    <view v-if="reward_set.show == 1" class="exam-memo gray_color">
                        <view v-if="reward_set.rules && reward_set.rules.length" class="exam-time-item">
                            <view class="redpack_rules" v-for="(item,index) in reward_set.rules" :key="index">
                                <text>成绩：{{item.min}} ~ {{item.max}} 分(含)，</text>
                                <text>奖励</text>
                                <text v-if="item.amount_min == item.amount_max">
                                    {{item.amount_min}} {{reward_unit}}
                                </text>
                                <text v-if="item.amount_min != item.amount_max">
                                    {{item.amount_min}} ~ {{item.amount_max}} {{reward_unit}}
                                </text>
                            </view>
                        </view>
                    </view>

                    <view class="modif_btn f-j-sb-a-c p5_0 font_9">
                        <view v-if="is_master" class="f-a-c gray_color" @click.stop="skip_modif_exam_details">
                            <i class="iconfont pr_5">&#xe633;</i>
                            <span>编辑考卷</span>
                        </view>
<!--                        <view class="f-a-c gray_color" @click.stop="get_qrcode">
                            <i class="iconfont pr_5">&#xe647;</i>
                            <span>考卷二维码</span>
                        </view>
                        <view class="f-a-c gray_color" @click.stop="copy_exam_answer_location">
                            <i class="iconfont pr_5">&#xe65e;</i>
                            <span>复制考卷</span>
                        </view>-->
                    </view>
                </view>

                <!-- 排行榜 -->
                <view v-if="is_master || !exam_details.conf || !exam_details.conf.toprank_closed" class="ranking-box p0-1">
                    <view class="ranking-title gray_bdrbdashed f-j-sb-a-c p1-0">
                        <view class="left">
                            排行榜
                            <text v-if="ranking_totalRow" class="memo_text">（{{ranking_totalRow}}人）</text>
                        </view>
                        <view class="right f-j-e-a-c gray_color" @click.stop="skip_ranking_list">
                            查看全部
                            <i class="iconfont font1_2">&#xe6a7;</i>
                        </view>
                    </view>
                    <view class="ranking-item">
                        <view class="ranking-list f p1-0" v-for="(item,index) in ranking_list" :key="index">
                            <view class="logo f-a-c pr_5">
                                <i class="iconfont font1_8 pr_2"
                                    :class="index == 0?'gold_color':index == 1?'silver_color':index == 2?'copper_color':''"
                                    v-html="index == 0?'&#xe73f;':index == 1?'&#xe741;':index == 2?'&#xe740;':''"></i>
                                <view class="img f-j-a-c">
                                    <image :src="item.headimg?item.headimg:avatar" mode="aspectFill"/>
                                </view>
                            </view>
                            <view class="text-box">
                                <view class="one-hidden pb_3">
									<template v-if="item.must_submit && item.must_submit.length && item.must_submit[0].value">
										{{item.must_submit[0].value}}
									</template>
									<template v-else>
										{{item.nickname || '游客'}}
									</template>
                                </view>
                                <view class="time memo_text">
                                    {{item.create_time || ''}}
                                </view>
                            </view>
                            <!-- 分数 -->
                            <view class="score-box text-right">
                                <view class="score">
                                    {{item.score?parseFloat(item.score).toFixed(2):0}}
                                    <text class="memo_text">分</text>
                                </view>
                                <view v-if="item.used_time" class="gray_color font_8">
                                    {{item.used_time || ''}}
                                </view>
                            </view>
                        </view>
                    </view>
                    <view v-if="ranking_err_text" class="err_text">
                        {{ranking_err_text}}
                    </view>
                </view>

                <!-- 底部操作栏 -->
                <view class="tabbar-box f">
                    <view class="left-box f-a-c p5-1">
                        <view class="item" @click.stop="skip_page(8)">
                            <i class="iconfont f-j-a-c font1_5">&#xe638;</i>
                            <view class="text f-j-a-c">
                                首页
                            </view>
                        </view>

                        <view v-if="is_master" class="item" @click.stop="open_other_operation">
                            <i class="iconfont f-j-a-c font1_5">&#xe62b;</i>
                            <view class="text f-j-a-c">
                                管理
                            </view>
                        </view>
                    </view>
                    
                    <!--独立的、单独的考卷、无需绑定点位、活动-->
                    <view v-if="params.active_id || params.independent" class="right-btn">
                        <view class="btn blue_bgcolor f-j-a-c" @click.stop="to_answer_question">
                            立即答题
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 大图模式 -->
        <view v-if="big_screen_set.show == 1 && show_big_screen > 0" class="big-screen f-d-c-j-a-c"
            @click.stop="skip_bigpic_examdetails" :class="{'inhidden':inhidden}"
            :style="{'background-image':`url(${show_big_screen == 2?big_screen_set.exam_details_pic: show_big_screen == 1?big_screen_set.screen_pic:''})`}">
            <view class="item f-j-a-c"
                v-if="!item.hidden && (!item.is_master || (item.is_master && is_master)) && show_big_screen == 2"
                v-for="(item,index) in btn_list" @click.stop="btn_post(item.idx)"
                :style="{'background-color':big_screen_set.button_background_color?big_screen_set.button_background_color:'','color':big_screen_set.button_text_color?big_screen_set.button_text_color:''}">
                {{item.name}}
            </view>

            <!-- 第一张大图倒计时 -->
            <view v-if="show_big_screen == 1" class="down-time">
                <view class="box">
                    跳过 {{down_time}}s
                </view>
            </view>
        </view>

        <view v-if="show_answer_details" class="fixed_modal f-d-c-j-a-c">
            <view class="fixed_modal_content details_modal">
                <view class="exam-text answer_details_modal">
                    <view class="exam-name f-j-sb-a-c">
                        <view class="left" @click.stop="copy_exam_id">
                            {{exam_details.exam_name}}
                        </view>
                        <!-- 分享按钮 -->
                        <!-- #ifndef APP-PLUS || H5 || MP-BAIDU -->
                        <view class="right f-j-e-a-c">
                            <button class="button-normal f-j-e-a-c" open-type="share">
                                <i class="iconfont gray_color" style="font-size: 1.7rem;">&#xe71f;</i>
                            </button>
                        </view>
                        <!-- #endif -->
                    </view>
                    <view class="exam-memo gray_color">
                        <text v-if="!rand_question_str">
                            {{exam_details.is_rand_question == 1?`随机出题：${cur_question_num}题`:`固定出题：${question_total}题`}}
                        </text>
                        <text v-if="rand_question_str">
                            {{rand_question_str}}
                        </text>
                    </view>
                    <view v-if='exam_details.content' class="exam-memo gray_color">
                        {{exam_details.content}}
                    </view>
                    <view class="exam-time-box gray_color">
                        <view class="exam-time-item f-a-c">
                            <view class="left">
                                每人可答：
                            </view>
                            <view class="right">
                                {{exam_details.answer_limit_types === 2 ? '每天' : ''}}{{exam_details.set_limit_times == 99999?'不限次数':`${exam_details.set_limit_times}次`}}
                            </view>
                        </view>
                        <view v-if="exam_details.answer_time" class="exam-time-item f-a-c">
                            <view class="left">
                                考卷答题时间：
                            </view>
                            <view class="right">
                                {{exam_details.answer_time}} 分钟
                            </view>
                        </view>
                        <view v-if="exam_details.begin_time" class="exam-time-item f-a-c">
                            <view class="left">
                                开始答题时间：
                            </view>
                            <view class="right">
                                {{exam_details.begin_time}}
                            </view>
                        </view>
                        <view v-if="exam_details.end_time" class="exam-time-item f-a-c">
                            <view class="left">
                                结束答题时间：
                            </view>
                            <view class="right">
                                {{exam_details.end_time}}
                            </view>
                        </view>
                    </view>
                    <view v-if="reward_set.show == 1" class="exam-memo gray_color">
                        <view v-if="reward_set.rules && reward_set.rules.length" class="exam-time-item">
                            <view class="redpack_rules" v-for="(item,index) in reward_set.rules" :key="index">
                                <text>成绩：{{item.min}} ~ {{item.max}} 分(含)，</text>
                                <text>奖励</text>
                                <text v-if="item.amount_min == item.amount_max">
                                    {{item.amount_min}} {{reward_unit}}
                                </text>
                                <text v-if="item.amount_min != item.amount_max">
                                    {{item.amount_min}} ~ {{item.amount_max}} {{reward_unit}}
                                </text>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="modif_btn f-j-sb-a-c p5-1 font_9 gray_bdrtdashed">
                    <view v-if="is_master" class="f-a-c gray_color" @click.stop="skip_modif_exam_details">
                        <i class="iconfont pr_5">&#xe633;</i>
                        <span>编辑考卷</span>
                    </view>
                   <!-- <view class="f-a-c gray_color" @click.stop="get_qrcode">
                        <i class="iconfont pr_5">&#xe647;</i>
                        <span>考卷二维码</span>
                    </view>
                    <view class="f-a-c gray_color" @click.stop="copy_exam_answer_location">
                        <i class="iconfont pr_5">&#xe65e;</i>
                        <span>复制考卷</span>
                    </view> -->
                </view>
            </view>
            <i class="iconfont" @click.stop="show_answer_details = false">&#xe618;</i>
        </view>

        <!-- 其他操作栏 -->
        <view v-if="show_other_operation" class="fixed_modal other_modal" @click.stop="close_other_operation">
            <view class="fixed-bottom-box">
                <view class="top">
                    <!-- 付费功能 -->
                    <view v-if="other_operation_pay_list && other_operation_pay_list.length"
                        class="operation-box gray_bdrbdashed">
                        <!-- <view class="operation-title">
							付费功能：
						</view> -->
                        <view class="operation-content f">
                            <view class="operation-list" v-for="(item,index) in other_operation_pay_list" :key="index">
                                <view v-if="!item.is_closed" class="operation-item"
                                    @click.stop="operation_click(item.types)">
                                    <i class="iconfont f-j-a-c blue_color" v-html="item.icon"></i>
                                    <view class="text">{{item.name}}</view>
                                </view>
                            </view>
                        </view>
                    </view>
                    <!-- 免费功能 -->
                    <view class="operation-box">
                        <!-- <view class="operation-title">
							免费功能：
						</view> -->
                        <view class="operation-content f">
                            <view v-if="!item.is_closed" class="operation-list"
                                v-for="(item,index) in other_operation_free_list" :key="index">
                                <view class="operation-item" @click.stop="operation_click(item.types)">
                                    <i class="iconfont f-j-a-c blue_color" v-html="item.icon"></i>
                                    <view class="text">{{item.name}}</view>
                                </view>
                            </view>
                            <!-- #ifdef MP-WEIXIN || MP-BAIDU -->
                            <view v-if="item.is_closed" class="operation-list"
                                v-for="(item,index) in other_operation_free_list" :key="index">
                                <button v-if="item.types == 25" class="operation-item" open-type="contact">
                                    <i class="iconfont f-j-a-c blue_color" v-html="item.icon"></i>
                                    <view class="text">{{item.name}}</view>
                                </button>
                                <view v-if="item.types == 161" class="operation-item"
                                    @click.stop="operation_click(item.types)">
                                    <i class="iconfont f-j-a-c blue_color" v-html="item.icon"></i>
                                    <view class="text">{{item.name}}</view>
                                </view>
                            </view>
                            <!-- #endif -->
                        </view>
                    </view>
                </view>
                <view class="bottom gray_bdrtdashed">
                    <view class="btn" @click.stop="close_other_operation">
                        取消
                    </view>
                </view>
            </view>
        </view>

        <!-- 选择出题类型 -->
        <view v-if="show_question_classify" class="fixed_modal f-d-c" style="z-index: 101;">
            <view class="fixed_modal_content">
                <view class="title">
                    当前选择：{{classify_list[classify_idx].name}}
                </view>
                <view class="classify-box">
                    <view class="classify-list"
                        :class="classify_idx == index?'blue_color blue_bdrsolid':'gray_bdrsolid'"
                        v-for="(item,index) in classify_list" :key="index" @click.stop="change_classify_list(index)">
                        {{item.name}}
                    </view>
                </view>
            </view>
            <i class="iconfont" @click.stop="close_question_classify">&#xe60c;</i>
        </view>

        <must-submit v-if="show_must_submit" :activityID="exam_details.active_id" :exam_id="params.exam_id"
            :types="activity_details.types" :all_must_submit="all_must_submit" @close_btn="close_must_submit">
        </must-submit>


        <!-- 功能设置 -->
        <view v-if="show_function_modal" class="fixed_modal f-d-c" style="z-index: 999;">
            <view class="fixed_modal_content">
                <!-- 选择题候选项打乱顺序 -->
                <view class="function-item f">
                    <view class="left f-a-c">
                        选择题候选项打乱顺序
                    </view>
                    <view class="right f-j-e-a-c">
                        <switch :checked="answer_option_rand == 1? true : false" data-title="answer_option_rand"
                                @change="change_conf_set_switch" />
                    </view>
                </view>

                <!-- 关闭排行榜 -->
                <view class="function-item f">
                    <view class="left f-a-c">
                        关闭排行榜
                    </view>
                    <view class="right f-j-e-a-c">
                        <switch :checked="toprank_closed == 1? true : false" data-title="toprank_closed"
                                @change="change_conf_set_switch" />
                    </view>
                </view>
                <!-- 关闭考试回看 -->
                <view class="function-item f">
                    <view class="left f-a-c">
                        关闭考试回看
                    </view>
                    <view class="right f-j-e-a-c">
                        <switch :checked="look_back_closed == 1? true : false"
                                data-title="look_back_closed" @change="change_conf_set_switch" />
                    </view>
                </view>

            </view>
            <i class="iconfont" @click.stop="close_function_modal">&#xe60c;</i>
        </view>

        <getUserInfo v-if="noUserInfo" :currUrl="currUrl"></getUserInfo>
    </view>
</template>

<script>
    let app = getApp();
    import configset from "./exam_details.js"

	import mustSubmit from "@/pages/likou_dati/components/must-submit/must-submit.vue"
	import carousel from "@/pages/likou_dati/components/indexPageSet/carousel/carousel.vue";
	import carouselSet from "@/pages/likou_dati/components/indexPageSet/carousel/carousel.js";

    export default {
		components: {
			mustSubmit,
			carousel
		},
        data() {
            return {
                params: {}, // 传递过来的参数
                userInfo: {}, // 用户信息
                is_master: false, // 是否考卷创建者
                noUserInfo: false,
                currUrl: '',
                carouselSet: {
                    ...carouselSet
                },

                inhidden: true,

                show_big_screen: 0, // 是否显示大图模式按钮组
                show_answer_details: false, // 是否显示考卷设置的信息
                down_time: 3, // 倒计时
                timer: null,

                exam_url: '', // 复制的路径
                exam_details: {
                    exam_name: '',
                    content: '',
                    creator: '',
                    begin_date: '',
                    end_date: '',
                    answer_time: '',
                    answer_limit_types: 1,
                    set_limit_times: 1,
                    show_public: 1,
                    thumb_pic: '',
                    pics: [],
                }, // 获取的考卷详情

                big_screen_set: {}, // 大图模式
                // 活动详情
                activity_details: {},

                show_other_operation: false, // 是否开启其他操作栏
                other_operation_pay_list: [], // 需要付费的列表
                other_operation_free_list: [],

                cur_question_num: 0,
                rand_question_str: '',

                ranking_list: [], // 考试排行榜
                ranking_totalRow: 0,
                ranking_err_text: '',
                avatar: this.imgImport.not_headimg,

                page: 1,
                show_must_submit: false, // 是否显示
                all_must_submit: '',

                integral_cost: [], // 设置的积分消耗规则

                classify_list: [{
                    name: '固定出题',
                }, {
                    name: '随机出题'
                }],
                classify_idx: 0,
                show_question_classify: false, // 是否显示出题类型弹框

                reward_set: '', // 种树设置
                reward_unit: '元',

                question_total: 0,

                show_function_modal: false,
                answer_option_rand: 0, // 选择题候选项打乱顺序
                toprank_closed: 0, // 是否关闭排行榜
                look_back_closed: 0, // 是否关闭回看
            }
        },

        computed: {
            // 大图模式
            btn_list() {
                return [
                    {
                    name: '开始答题',
                    idx: 1,
                    is_master: false,
                    hidden: false,
                    },
                    {
                        name: '查看排名',
                        idx: 2,
                        is_master: false,
                        hidden: false,
                    },
                    {
                        name: '考试说明',
                        idx: 3,
                        is_master: false,
                        hidden: this.exam_details?.conf?.active_explain_show === 0,
                    },
                    {
                        name: '考卷管理',
                        idx: 4,
                        is_master: true,
                        hidden: false,
                    }
                ]
            }
        },

        onLoad(options) {
            // console.log('获取的参数options', options);
            this.params = options;
            this.xwyLib.showLoading()
            this.xwyLib.init(() => {
				if (options.is_manage) this.open_other_operation()

                if (this.params.scene) {
                    let scene = decodeURIComponent(options.scene);
                    let ex = this.xwyLib.getUrlParams('ex', scene);
                    this.xwyLib.get_long_by_short({
                        id: ex,
                        success: res => {
                            this.params.exam_id = res;
                            this.$nextTick(function() {
                                this.init();
                            })
                        }
                    })
                    return
                }
                this.init(this.params);
            })
        },
        onShow() {
            if (app.globalData.update_exam == 1) {
                app.globalData.update_exam = null;
                if (this.show_question_classify) this.show_question_classify = false;
                this.init(this.params);
            }
            if (app.globalData.update == 1) {
                app.globalData.update = null;
                this.init(this.params);
            }
        },
        onUnload() {
            if (this.timer) clearInterval(this.timer);
        },
        methods: {
            // 初始化
            async init(options) {
                // this.xwyLib.get_cur_page_and_params();
                this.userInfo = app.globalData.userInfo;
                this.other_operation_free_list = configset.other_operation_free_list;
                this.other_operation_pay_list = configset.other_operation_pay_list;

                await this.get_exam_details();
                if (this.params.show_set) this.open_other_operation();
            },

            // 获取考卷详情
            async get_exam_details() {
                let obj = {
                    exam_id: this.params.exam_id,
                    question_details: 1,
                    success: res => {
                        this.inhidden = false;
                        this.xwyLib.setBarTitle(res.exam_name);
                        if (this.userInfo.id === res.userid) this.is_master = true;
                        if (res.begin_time) res.begin_time = this.xwyLib.timestampToTime(res.begin_time);
                        if (res.end_time) res.end_time = this.xwyLib.timestampToTime(res.end_time);

                        // 获取当前答题的题目数量
                        this.cur_question_num = 0;

                        this.classify_idx = res.is_rand_question; // 出题类型 固定还是随机
                        if (res.is_rand_question == 1) { // 随机出题
                            // 判断是题型出题还是没有指定题型
                            if (res.question_rules_conf?.rand_question_types == 1) {
                                let rand_types = res.question_rules_conf;
                                if (rand_types.single_answer || rand_types.multi_answer || rand_types
                                    .true_false || rand_types
                                    .fill_blank) {
                                    let rand_question_str = '';
                                    if (rand_types.single_answer) rand_question_str +=
                                        `单选${rand_types.single_answer}题`;
                                    if (rand_types.multi_answer) {
                                        if (rand_question_str) {
                                            rand_question_str += `，多选${rand_types.multi_answer}题`;
                                        } else {
                                            rand_question_str += `多选${rand_types.multi_answer}题`;
                                        }
                                    }
                                    if (rand_types.true_false) {
                                        if (rand_question_str) {
                                            rand_question_str += `，判断${rand_types.true_false}题`;
                                        } else {
                                            rand_question_str += `判断${rand_types.true_false}题`;
                                        }
                                    }
                                    if (rand_types.fill_blank) {
                                        if (rand_question_str) {
                                            rand_question_str += `，填空${rand_types.fill_blank}题`;
                                        } else {
                                            rand_question_str += `填空${rand_types.fill_blank}题`;
                                        }
                                    }
                                    this.rand_question_str = rand_question_str;
                                }
                            }

                            // 没有指定题型
                            if (res.question_rules_conf?.rand_question_types == 2) this.cur_question_num =
                                res.question_rules_conf.rand_question_num || 0;
                        }

                        // 排行榜
                        // 获取排行榜
                        if (!res.toprank_closed || this.is_master) {
                            this.get_exam_ranking_list();
                        }

                        this.exam_details = res;

                        if (this.xwyLib.isArray(res.pics)) {
                            let pic_list = [];
                            res.pics.forEach(val => {
                                pic_list.push({
                                    src: val
                                })
                            })
                            this.carouselSet = {
                                ...this.carouselSet,
                                list: pic_list,
                            }
                        }

                        if (!res.active_id && res?.conf?.must_submit?.show) this.all_must_submit =
                            res.conf.must_submit.list
                        if (res.active_id) {
                            this.get_activity_details();
                        } else {
                            this.reward_set = res?.conf?.reward_set || '';
                        }
                        if (res.questionlist) this.question_total = res.questionlist.total;
                        // 获取大图模式
                        this.big_screen_set = res.conf?.big_screen_set || {};
                        if (this.big_screen_set.show == 1) {
                            if (this.big_screen_set.screen_pic) {
                                this.show_big_screen = 1;
                                if (this.big_screen_set.down_seconds) this.down_time = this.big_screen_set.down_seconds
                                this.down_time_function();
                            } else {
                                this.show_big_screen = 2;
                            }
                        }

                        // 选择题候选项随机打乱顺序
                        if (res?.conf?.answer_option_rand) {
                            this.answer_option_rand = res.conf.answer_option_rand;
                        }
                        // 是否关闭排行榜
                        if (res?.conf?.toprank_closed) this.toprank_closed = res?.conf?.toprank_closed;

                        // 是否关闭考试回看功能
                        if (res?.conf?.look_back_closed) this.look_back_closed = res?.conf?.look_back_closed;
                    },
                    fail: res => {
                        this.xwyLib.setBarTitle('考卷详情');
                        this.xwyLib.showModal('未获取到考卷，即将跳转到首页！', {
                            success: response => {
                                let url = `/pages/index/index`;
                                this.xwyLib.routeJump(url);
                            }
                        })
                    }
                }
                this.xwyLib.get_exam_papers_details(obj);
            },

            // 开屏大图倒计时
            down_time_function() {
                this.timer = setInterval(() => {
                    this.down_time--;
                    if (this.down_time < 0) {
                        this.skip_bigpic_examdetails();
                        return
                    }
                }, 1000)
            },

            // 跳转到大图详情页
            skip_bigpic_examdetails() {
                if (this.big_screen_set.show != 1) return
                if (this.timer) clearInterval(this.timer)
                this.show_big_screen = 2;
            },

            btn_post(idx) {
                switch (parseInt(idx)) {
                    case 1:
                        this.to_answer_question();
                        break;
                    case 2:
                        this.skip_ranking_list();
                        break;
                    case 3:
                        this.show_answer_details = true;
                        break;
                    case 4:
                        this.open_other_operation();
                        break;
                }
            },

            // 如果存在活动ID则获取活动详情
            get_activity_details() {
                let obj = {
                    id: this.exam_details.active_id,
                    success: res => {
                        // console.log('活动详情', res.active_details);
                        let details = res.active_details;
                        this.activity_details = details;
                        this.all_must_submit = details?.conf?.must_submit?.list || '';

                        if (details.types == 1) {
                            this.reward_set = this.exam_details?.conf?.tree_set || '';
                            this.reward_unit = '滴';
                        }
                    },
                    fail: res => {
                        this.xwyLib.showModal('未获取到活动详情！', {
                            success: res => {
                                this.xwyLib.back_previous_page();
                            }
                        })
                    }
                };
                this.xwyLib.get_activity_details(obj);
            },

            // 获取考卷排行榜
            async get_exam_ranking_list() {
                this.ranking_err_text = '';
                let obj = {
                    exam_id: this.params.exam_id,
					active_id: this.params.active_id,
                    perpage: 3,
                    success: res => {
                        // console.log('获取考卷排行榜', res);
                        res.data.forEach(val => {
                            if (val.used_time) val.used_time = this.xwyLib
                                .secondsToHoursMinutesSeconds(val.used_time);
                            if (val.submit_time) val.submit_time = this.xwyLib.timestampToTime(val
                                .submit_time / 1000);
                        })
                        this.ranking_list = res.data;
                        this.ranking_totalRow = res.total || 0;
                    },
                    fail: res => {
                        this.ranking_err_text = res.info || '暂无人答题!';
                    }
                };

				// 鲜繁客人的活动排行榜只显示最高分数
				if (this.params.active_id === '0a83b3f416d6f773e8ae7c339e74b9f0') obj.get_max = true
                this.xwyLib.get_exam_ranking_list(obj)
            },

            //  跳转到修改页面
            skip_modif_exam_details() {
                let url = `/pages/likou_dati/pages/exam/exam_add/exam_add?exam_id=${this.params.exam_id}`;
                this.xwyLib.routeJump(url);
            },

            // 跳转到其他页面
            skip_page(e) {
                this.xwyLib.skip_page(e);
            },

            // 打开其他操作栏
            open_other_operation() {
                this.show_other_operation = true;
            },

            // 关闭其他操作栏
            close_other_operation() {
                this.show_other_operation = false;
            },

            // 操作栏的操作
            operation_click(e) {
                switch (Number(e)) {
                    // 修改设置
                    case 151:
                        this.skip_modif_exam_details();
                        break;
                        // 跳转到考卷添加考题页面
                    case 152:
                        let exam_id = '';
                        if (this.params.exam_id) {
                            exam_id = this.params.exam_id;
                        }
                        let url = `/pages/likou_dati/pages/exam/exam_question_add/exam_question_add?exam_id=${exam_id}`;
                        this.xwyLib.routeJump(url);
                        break;
                        // 复制考卷
                    case 153:
                        this.copy_exam_answer_location();
                        break;
                        // 导出排行榜
                    case 154:
                        this.export_ranking_list();
                        break;
                        // 出题类型
                    case 155:
                        this.open_question_classify();
                        break;
                        // 获取二维码
                    case 156:
                        this.get_qrcode();
                        break;
                        // 功能设置
                    case 157:
                        this.open_function_modal();
                        break;
                        // 增值服务
                    case 159:
                        this.xwyLib.skip_page(218);
                        break;
                        // 复制小程序路径
                    case 161:
                        let route = `pages/likou_dati/pages/exam/exam_details/exam_details?exam_id=${this.params.exam_id}`;
                        this.copy_excel_url(route);
                        break;
                }
            },

            // 判断是否授权个人信息
            judgeAuth(cb) {
                this.xwyLib.judgeAuth({
                    authed: res => {
                        this.noUserInfo = false;
                        cb && cb();
                    },
                    unauth: res => {
                        let page_url = this.xwyLib.get_cur_page_and_params();
                        this.noUserInfo = true;
                        this.currUrl = page_url;
                    }
                })
            },

            judge_time() {
                let begin_time = new Date(this.exam_details.begin_time).getTime(),
                    end_time = new Date(this.exam_details.end_time).getTime(),
                    cur_time = new Date().getTime();

                if (cur_time < begin_time) {
                    this.xwyLib.alert(`答题还没有开始！---答题开始时间：${this.exam_details.begin_time}`)
                    return false
                }
                if (cur_time > end_time) {
                    this.xwyLib.alert(`答题已经结束了！---答题结束时间：${this.exam_details.end_time}`)
                    return false
                }

                return true
            },

            async to_answer_question() {

                // 雷子客户健步走活动定制开发，每天只能答多少个考卷，用接口查，能不能继续答题
                if (this.params.active_id) {
                    const res = await this.xwy_api.request({
                        url: 'front.flat.sport_step.exam.sportExam/user_active_exam_times',
                        data: {
                            active_id: this.params.active_id
                        }
                    })

                    if (res?.status !== 1) return this.$uni.showModal(res?.info || '不能答题')
                }

                // 判断是否需要输入实名信息,如果需要则弹出实名信息弹框 输入后才跳转
                if (!this.judge_time()) return
                this.judgeAuth(() => {
                    this.to_answer_question_post();
                })
            },
            // 如果是活动中的考卷 则要判断是否参加过活动 参加过则直接跳转 没参加则判断是否需要实名
            judge_user_is_join() {
                this.xwyLib.judge_user_is_join({
                    active_id: this.params.active_id,
                    success: res => {
                        this.skip_answer_question();
                    },
                    fail: res => {
                        let must_submit = this.activity_details?.conf?.must_submit || {};
                        if (must_submit?.show) {
                            this.show_must_submit = true;
                            return
                        }
                        this.user_attend_active();
                    }
                })
            },

            // 报名
            user_attend_active() {
                let obj = {
                    active_id: this.exam_details.active_id,
                    success: res => {
                        this.skip_answer_question();
                    },
                };
                if (uni.getStorageSync(`must_submit_active${this.exam_details.active_id}`)) obj.must_submit = uni
                    .getStorageSync(
                        `must_submit_active${this.exam_details.active_id}`);
                this.xwyLib.user_attend_active(obj)
            },

            to_answer_question_post() {
                // 如果是活动中的考卷则判断活动的参数
                if (this.params.active_id) {
                    this.judge_user_is_join();
                    return
                }

                // 单独的考卷
                if (this.exam_details?.conf?.must_submit?.show && !uni.getStorageSync(`must_submit_exam${this.params.exam_id}`)) {
                    this.show_must_submit = true;
                    return
                }
                this.skip_answer_question();
            },

            // 跳转到答题
            skip_answer_question() {
                const {exam_id, active_id, point_id, send_word, rush_round, exam_prize} = this.params
                let url = `/pages/likou_dati/pages/answer/answer_question/answer_question?exam_id=${exam_id}`
                if (active_id) url += `&activityID=${active_id}`
                if (point_id) url += `&point_id=${point_id}`
				if (send_word) url += `&send_word=1`
				if (rush_round) url += `&rush_round=1`

                // 健步走答题勋章
                if (exam_prize && !this.isLightUp) {
                    const {have_light, min_score, img_src, prize_name} = this.params
                    url += `&exam_prize=1&have_light=${have_light}&min_score=${min_score}&img_src=${img_src}&prize_name=${prize_name}`
                }

                uni.navigateTo({
                    url,
                    events: {
                        success: () => this.getOpenerEventChannel?.()?.emit?.('success'),
                        lightUp: () => {
                            this.isLightUp = true
                            this.getOpenerEventChannel?.()?.emit?.('lightUp')
                        }
                    }
                })
            },

            close_must_submit() {
                this.show_must_submit = false;
            },

            // 复制考卷ID
            async copy_exam_id() {
                this.xwyLib.copy_text({
                    url: this.exam_details.exam_id,
                    text: '复制成功！',
                    toast: 1,
                })
            },

            // 复制考卷答题地址
            async copy_exam_answer_location() {
                let str = ''
                // #ifdef H5
                str = window.location.href;
                // #endif
                // #ifdef MP-WEIXIN
                str = this.exam_url ? this.exam_url : '';
                // #endif
                this.xwyLib.copy_text({
                    url: str,
                    text: '考卷地址复制成功，快去粘贴发送给其他人答题吧！',
                })
            },

            // 跳转到排行榜
            skip_ranking_list() {
                let url = `/pages/likou_dati/pages/answer/ranking_list/ranking_list?exam_id=${this.params.exam_id}&active_id=${this.params.active_id}&userid=${this.exam_details.creator_user_details.id}`;
                this.xwyLib.routeJump(url);
            },

            async copy_excel_url(url) {
                this.xwyLib.copy_text({
                    url,
                    text: '复制成功！',
                })
            },

            // 改变出题类型选中选项
            change_classify_list(e) {
                if (e == 0) {
                    this.classify_idx = e;
                    let params = {
                        is_rand_question: 0,
                        exam_id: this.params.exam_id,
                        success: res => {
                            this.xwyLib.alert(res.info)
                            this.get_exam_details();
                            this.rand_question_str = '';
                            this.show_question_classify = false;
                        }
                    };
                    this.xwyLib.exam_answer_conf_set(params)
                }

                if (e == 1) {
                    let url =
                        `/pages/likou_dati/pages/question/random_question_types/random_question_types?exam_id=${this.params.exam_id}`
                    this.skip_page({
                        url,
                    })
                }
            },

            // 导出排行榜
            export_ranking_list() {
                /*let text = `是否导出该考卷的排行榜数据？`;
                this.xwyLib.showModal(text, {
                    confirmText: '导出',
                    success: res => {
                        if (res.confirm) this.export_ranking_list_post();
                    }
                })*/

                const is_questionnaire = this.exam_details?.conf?.is_questionnaire
                const url = is_questionnaire ? '../export-questionnaire' : '/pages/other/export-exam-ranking'

                uni.navigateTo({
                    url: `${url}?exam_id=${this.params.exam_id}`
                })
            },

            export_ranking_list_post() {
                this.xwyLib.export_excel_from_exam_ranking({
                    exam_id: this.params.exam_id,
                    success: res => {
                        this.xwyLib.copy_text({
                            url: res.data.url,
                            text: res.info,
                        })
                    },
                    fail: res => {
                        this.xwyLib.alert(res.info);
                    }
                })
            },

            open_question_classify() {
                this.show_question_classify = true;
            },

            close_question_classify() {
                this.show_question_classify = false;
            },

            // 获取二维码
            async get_qrcode() {
                this.xwyLib.get_qrcode_post({
                    scene: `ex=${this.exam_details.id}`,
                    path: 'pages/likou_dati/pages/exam/exam_details/exam_details',
                }, res => {
                    this.xwyLib.previewImage({
                        urls: res,
                    })
                });
            },


            // 打开功能弹框
            open_function_modal() {
                this.show_function_modal = true;
            },

            // 关闭功能弹框
            close_function_modal() {
                this.show_function_modal = false;
            },

            // 改变排行榜是否显示
            change_conf_set_switch(e) {
                let title = e.currentTarget.dataset.title,
                    value = e.detail.value;
                let types = '';

                if (title === 'toprank_closed') types = 2;
                if (title === 'look_back_closed') types = 6;
                if (title === 'creator_closed') types = 7;
                if (title === 'answer_option_rand') types = 8;
                if (title === 'wrong_collect') types = 13;

                this.change_conf_set({
                    value,
                    title,
                    types,
                });
            },

            change_conf_set(obj = {}) {
                if (obj.value) {
                    this.$set(this, obj.title, 1)
                    this.$nextTick(function() {
                        this.$set(this, obj.title, 0)
                        this.xwyLib.judge_integral_cost_set({
                            closed_AD: this.closed_AD,
                            types: obj.types,
                            success: res => {
                                this.exam_conf_set({
                                    title: obj.title,
                                    value: 1,
                                });
                            },
                        })
                    })
                    return
                }
                this.$set(this, obj.title, 0)
                this.$nextTick(function() {
                    this.$set(this, obj.title, 1)
                    this.exam_conf_set({
                        title: obj.title,
                        value: 0,
                    });
                })
            },

            exam_conf_set(opt = {}) {
                let conf = this.exam_details.conf || {};
                conf[opt.title] = opt.value;

                let obj = {
                    exam_id: this.params.exam_id,
                    conf: this.xwyLib.jsonEncode(conf),
                    success: res => {
                        this.xwyLib.showToast(res.info);
                        this.$set(this, opt.title, opt.value);
                        setTimeout(() => {
                            this.get_exam_details();
                        }, 1000)
                    },
                    fail: res => {
                        if (res.info.includes('积分')) {
                            this.show_service = true;
                            this.service_text = res.info;
                            return
                        }
                        this.xwyLib.alert(res.info);
                    }
                }

                if (opt.title && opt.value) obj[opt.title] = 1;
                this.xwyLib.exam_conf_set(obj);
            },

        }

    }
</script>

<style scoped lang='scss'>
    @import url('@/pages/likou_dati/common/css/common.scss');
    @import url('@/pages/likou_dati/common/css/iconfont.css');
    @import url('@/pages/likou_dati/common/css/public.css');

    .big-screen {
        height: 100vh;
        overflow: auto;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        padding-bottom: 0 !important;

        .item {
            width: 70%;
            margin-bottom: 1rem;
            border-radius: 10rem;
            padding: .8rem 0;
            font-size: 1.1rem;
        }

        .down-time {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background-color: rgba($color: #ccc, $alpha: .7);
            border-radius: 10rem;
            padding: .4rem 1rem;

            .box {
                font-size: .9rem;
                color: #fff;
            }
        }
    }

    .content {
        position: relative;
        width: 100%;
        padding-bottom: 70px;

        .details-box {
            width: 100%;
            max-width: none;

            .exam-text {

                .exam-name {
                    width: 100%;

                    .left {
                        font-weight: 700;
                        width: calc(100% - 3rem);
                    }

                    .right {
                        width: 3rem;

                        .iconfont {
                            font-size: 1.4rem;
                        }
                    }
                }

                .exam-memo {
                    padding: .5rem 0;
                    font-size: .9rem;
                }

                .exam-time-box .exam-time-item {
                    padding-bottom: .2rem;
                }
            }
        }

        .other_modal {
            align-items: flex-end;
            z-index: 30;

            .fixed-bottom-box {
                width: 100%;

                .top {
                    width: 100%;
                    background-color: #fff;

                    .operation-box {
                        width: 100%;

                        .operation-title {
                            padding: .5rem 1rem;
                        }

                        .operation-content {
                            width: 100%;
                            flex-wrap: wrap;
                            padding-top: 1rem;

                            .operation-list {
                                width: 25%;
                                padding-bottom: 1rem;

                                .operation-item {
                                    width: 100%;

                                    .iconfont {
                                        font-size: 1.5rem;
                                        padding-bottom: .5rem;
                                    }

                                    .text {
                                        font-size: .9rem;
                                        text-align: center;
                                    }
                                }

                                button {
                                    border: 0;
                                    line-height: normal;
                                    background-color: transparent;
                                }

                                button:after {
                                    content: '';
                                    width: 0;
                                    height: 0;
                                }
                            }
                        }
                    }
                }

                .bottom {
                    width: 100%;
                    background-color: #fff;

                    .btn {
                        width: 100%;
                        padding: 1rem 0;
                        text-align: center;
                    }
                }
            }
        }

        .ranking-box {
            width: 100%;
            border-top: 1rem solid #f8f8f8;

            .ranking-title {

                .left {
                    width: calc(100% - 7rem);
                }

                .right {
                    width: 7rem;
                }
            }

            .ranking-item {
                width: 100%;

                .ranking-list {
                    width: 100%;

                    .logo {
                        width: 6rem;

                        .img {
                            width: 3rem;
                            height: 3rem;

                            image {
                                width: 100%;
                                height: 100%;
                                border-radius: 50%;
                            }
                        }
                    }

                    .text-box {
                        width: calc(100% - 13rem);
                    }

                    .score-box {
                        width: 7rem;

                        .score {
                            padding-bottom: .25rem;

                            >text {
                                font-weight: normal;
                                padding-left: .1rem;
                            }
                        }
                    }
                }
            }
        }

        .fixed_modal {

            >.iconfont {
                padding: .5rem;
                color: #f8f8f8;
                font-size: 1.5rem;
            }

            .fixed-item {
                width: 100%;
                margin-bottom: 1rem;

                .textarea {
                    width: 100%;
                    border-radius: .2rem;
                    border: 1px solid #e0e0e0;
                    padding: .5rem;
                    height: 100px;

                    textarea {
                        width: 100%;
                        height: 100%;
                        font-size: 1rem;
                    }
                }
            }

            .input {
                padding: .5rem 0;
                border-radius: .2rem;

                input {
                    width: 100%;
                }
            }

            .btn-box {
                width: 100%;

                .btn {
                    width: 40%;
                    border-radius: 100rpx;
                    text-align: center;
                    padding: .5rem 0;
                }
            }

            .title {
                padding-bottom: 1rem;
            }

            .classify-box {
                width: 100%;

                .classify-list {
                    display: inline-block;
                    border-radius: .2rem;
                    padding: .5rem 2rem;

                    &:first-child {
                        margin-right: 1rem;
                    }
                }
            }

            .function-item {
                width: 100%;
                padding: .5rem 0;

                .left {
                    width: calc(100% - 5rem);
                }

                .right {
                    width: 5rem;

                    switch {
                        transform: scale(.9);
                    }
                }
            }
        }
    }

    .tabbar-box .btn-box {
        width: 100%;

        .btn {
            width: 70%;
        }
    }

    .details_modal {
        height: 75%;
        padding: 1rem 1rem 0 1rem;
        position: relative;

        .answer_details_modal {
            height: calc(100% - 3rem);
            overflow-y: auto;

            >view {
                padding-bottom: 1rem;
            }
        }

        .modif_btn {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3rem;
        }
    }
</style>
