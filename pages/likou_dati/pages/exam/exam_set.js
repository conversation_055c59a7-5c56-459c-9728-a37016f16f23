let confset = {
    conf: {
        tree_set: {
            show: 0,
            types: 1,
            rules: [{
                min: 0, //大于
                max: 100, //小于等于 （含100分）
                amount_min: 1,
                amount_max: 1,
            }],
            is_questionnaire: 0
        },
        reward_set: {
            show: 0,
            types: 1, // 1不限制分数 只要答题就有奖励, 2根据分数获得奖励
            rules: [{
                min: 0, //大于
                max: 100, //小于等于 （含100分）
                amount_min: 1,
                amount_max: 1,
            }],
        },
        big_screen_set: {
            show: 0,
            screen_pic: '', // 开屏大图
            exam_details_pic: '', // 详情页大图
            button_text_color: '#fff', // 按钮文字颜色
            button_background_color: '#000', // 按钮背景颜色
            down_seconds: 3,
        },
        // 背题练习
        practice_set: {
            show: 0,
        },
        answer_question_types: 1, // 1答题卡模式 2竞赛模式，答完自动跳转下一题 3竞赛模式，答错自动终止作答
        question_seconds: 0, // 每题的答题时间，只在竞赛模式显示
        answer_time: 0, // 每场考试的答题时间
        explain_text_open: 0,
        active_explain_show: 1, // 活动说明是否显示
        look_back_closed: 0,  // 是否允许查看回顾
        // 抽奖设置
        lottery_set: {
            show: 0,
            active_id: '',
            min_score: 0,
        },

        // 证书设置
        certificate_set: {
            show: 0,
            types: 1, // 1不限制分数 只要答题就有证书, 2根据分数获得证书
            rules: [{
                min: 0, //大于
                max: 100, //小于等于 （含100分）
                text: '答的不错！',
            }],
        },

        answer_template_id: '', // 答题模板
        bg_music: '', // 背景音乐

        ip_set: {
            open: 0, //【1】开启  【0】关闭，不强制验证用户的答题来自的ip地址
            ip_city: [],
        },

        wechat_group: { // 微信群设置
            show: 0,
            list: [], // 指定的微信群ID
        },

        anti_cheating: { // 防作弊设置
            show: 0,
            cuts_screen: 0, // 切屏次数
        },

        waiting_persont: 0, // 是否开启人工阅卷

        share_set: {
            show: 0,
            title: '',
            logo: '',
        },
    },

    // 红包奖励设置
    reward_types: [{
        label: '不限分数，答题即可获得奖励',
        value: 1,
    },
        {
            label: '根据分数获得对应奖励',
            value: 2,
        }
    ],

    reward_show: [{
        label: '关闭奖励',
        value: 0,
    },
        {
            label: '开启奖励',
            value: 1,
        }
    ],

    big_screen_show: [{
        label: "开启使用大图模式",
        value: 1
    },
        {
            label: "不开启大图模式",
            value: 0
        }
    ],

    practice_set_show: [{
        label: "允许考生练习",
        value: 1
    },
        {
            label: "不允许考生练习",
            value: 0
        }
    ],

    // 答题模式
    answer_question_types: [
        // 2024-05-15 15:41:37
        // 特别特别重要
        // 特别特别重要
        // 特别特别重要
        // 由于利口那边答题的代码后续增加了很多功能，这边不好做到和利口那边一样。
        // 这里选了"答题卡模式"会出现一个特别严重的问题
        // 如果答案解析选择了答题错误才显示答案提示，选择了错误答案，页面上会提示正确答案。
        // 以上问题已在企业微信群里告知各位同事。
        // 特别特别重要
        // 特别特别重要
        // 特别特别重要
        {
            label: "答题卡模式",
            value: 1
        },
        {
            label: "竞赛模式，答完自动跳转下一题",
            value: 2
        },
        {
            label: "竞赛模式，答错自动终止作答",
            value: 3
        }
    ],
    
    explain_text_open: [{
        text: "关闭，不作答案提示",
        value: 0
    },
        {
            text: "答题就显示答案提示",
            value: 1
        },
        
        // 特别特别重要
        // 特别特别重要
        // 特别特别重要
        // 由于利口那边答题的代码后续增加了很多功能，这边不好做到和利口那边一样。
        // 这里选了"答题错误才显示答案提示"会出现一个特别严重的问题
        // 如果答题模式选择了答题卡模式模式，选择了错误答案，页面上会提示正确答案。
        // 以上问题已在企业微信群里告知各位同事。
        // 特别特别重要
        // 特别特别重要
        // 特别特别重要
        {
            text: "答题错误才显示答案提示",
            value: 2
        }
    ],

    look_back_closed:[{
        label: "允许查看",
        value: 0
    }, {
        label: "不允许查看",
        value: 1
    }],

    lottery_show: [{
        label: "关闭抽奖",
        value: 0
    },
        {
            label: "开启抽奖",
            value: 1
        }
    ],

    // 抽奖设置
    lottery_types: [{
        label: '不限分数，答题即可抽奖',
        value: 1,
    },
        {
            label: '根据分数获得对应抽奖次数',
            value: 2,
        }
    ],

    // 证书设置
    certificate_types: [{
        label: '不限分数，答题即可获得鼓励词',
        value: 1,
    },
        {
            label: '根据分数获得对应鼓励词',
            value: 2,
        }
    ],

    certificate_show: [{
        label: "关闭",
        value: 0
    },
        {
            label: "开启",
            value: 1
        }
    ],

    waiting_persont: [{
        label: "系统自动阅卷判分",
        value: 0
    },
        {
            label: "人工阅卷判分",
            value: 1
        }
    ],

}


const init = {

    exam_add: {
        tabs_list: [{
            name: '基本信息',
            idx: 0,
        },
            {
                name: '扩展设置',
                idx: 1,
            },
            {
                name: '模板设置',
                idx: 5,
            },
            {
                name: '大图模式',
                idx: 2,
            },
            // {
            //     name: '奖励设置',
            //     idx: 3,
            // },
            // {
            //     name: '抽奖设置',
            //     idx: 4,
            // },
            // {
            //     name: '鼓励词设置',
            //     idx: 6,
            // }
        ],

        answer_limit_num_list: [{
            value: 1,
            text: '1次'
        },
            {
                value: 2,
                text: '2次'
            },
            {
                value: 3,
                text: '4次'
            },
            {
                value: 5,
                text: '5次'
            },
            {
                value: 10,
                text: '10次'
            },
            {
                value: 99999,
                text: '不限次数'
            },
            {
                value: 15,
                text: '自定义'
            }
        ],

        answer_limit_types: [{
            value: 1,
            text: '固定次数'
        },
            {
                value: 2,
                text: '周期次数'
            }
        ],
    }

}

const exam_template = {
    answer_template_list: [{
        img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/000.jpg',
        name: '默认',
        id: 0,
        sort_num: 0,
    },
        {
            img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/001.jpg',
            name: '模板1',
            id: 1,
            sort_num: 4,
        },
        {
            img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/002.jpg',
            name: '模板2',
            id: 2,
            sort_num: 8,
        },
        {
            img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/003.jpg',
            name: '模板3',
            id: 3,
            sort_num: 12,
        },
        {
            img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/004.jpg',
            name: '模板4',
            id: 4,
            sort_num: 16,
        },
        {
            img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/005.jpg',
            name: '模板5',
            id: 5,
            sort_num: 20,
        },
        {
            img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/006.jpg',
            name: '模板6',
            id: 6,
            sort_num: 24,
        },
        {
            img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/007.jpg',
            name: '模板7',
            id: 7,
            sort_num: 28,
        },
        {
            img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/008.jpg',
            name: '模板8',
            id: 8,
            sort_num: 32,
        },
        {
            img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/009.jpg',
            name: '模板9',
            id: 9,
            sort_num: 36,
        },
        {
            img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/010.jpg',
            name: '模板10',
            id: 10,
            sort_num: 40,
        },
        {
            img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/011.jpg',
            name: '模板11',
            id: 11,
            sort_num: 3,
        },
        {
            img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/012.jpg',
            name: '模板12',
            id: 12,
            sort_num: 3,
        },
        {
            img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/013.jpg',
            name: '模板13',
            id: 13,
            sort_num: 2,
        },
        {
            img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/014.jpg',
            name: '模板14',
            id: 14,
            sort_num: 2,
        },
        {
            img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/015.jpg',
            name: '模板15',
            id: 15,
            sort_num: 2,
        }
    ]
}


export default {
    ...confset,
    ...init,
    ...exam_template,
}
