<template>
	<view class='content'>
		<view class="top-select">
			<liuyuno-tabs v-if="tabs_list && tabs_list.length" :tabData="tabs_list" @tabClick='change_tabs'
				:activeIndex="tabs_idx" :close_animation="1" />
		</view>

		<view v-if="tabs_list[tabs_idx].idx == 0" class="">
			<!-- 考卷名称 -->
        <!--is_activity 独立的考试活动不能修改考卷名称-->
			<view v-if="!is_activity" class="edit-item">
				<view class="title">
					考卷名称
					<span class="red_color">*</span>
				</view>
				<view class="value">
					<uni-easyinput type="textarea" maxlength="-1" v-model="formData.exam_name" placeholder="请输入考卷名称" />
				</view>
			</view>
			<!-- 出题人 -->
<!--			<view class="edit-item">
				<view class="title">
					出题人
				</view>
				<view class="value">
					<uni-easyinput maxlength="-1" v-model="conf.creator" placeholder="请输入出题人姓名" />
				</view>
			</view>-->
			<!-- 考试人数 -->
<!--			<view class="edit-item">
				<view class="title f-a-c">
					答题总次数
					<i class="iconfont memo_text font_9" @click.stop="showModal('所有人答题的总次数，0或空表示不限制')">&#xe65a;</i>
					<text class="memo_text pl_5">输入数字即可</text>
				</view>
				<view class="value">
					<uni-easyinput maxlength="9" type="number" v-model="formData.max_num"
						placeholder="考试人数,最多能有多少人可以答题" />
				</view>
			</view>-->


            <view class="edit-item">
				<view class="title f-a-c">
					考卷模式
					<i class="iconfont memo_text font_9"
						@click.stop="showModal('切换为问卷调查模式后，答题结束不会出成绩')">&#xe65a;</i>
				</view>
				<view class="value">
					<select-lay :value="conf.is_questionnaire" name="name" :options="[{value: 0, label: '答题模式'}, {value: 1, label: '问卷调查'}]"
							:zindex="13" @selectitem="isQuestionnaireChange" placeholder=""></select-lay>
				</view>
			</view>


			<!-- 答题次数 -->
			<view class="edit-item">
				<view class="title f-a-c">
					答题次数
					<i class="iconfont memo_text font_9"
						@click.stop="showModal('固定次数：每人总共可答题次数；周期次数：每人每天可答题次数')">&#xe65a;</i>
				</view>
				<view class="value">
					<view class="answer_num right f-a-c font_9 p_5 radius_2" @click.stop="show_answerNumber">
						<text class="gray_color">{{answer_limit_types_text}}</text>
						<text class="p0_5">{{formData.set_limit_times == 99999?'不限次数':formData.set_limit_times}}</text>
						<text v-if="formData.set_limit_times != 99999" class="gray_color">次</text>
					</view>
				</view>
			</view>
			<!-- 开始时间 -->
			<view class="edit-item">
				<view class="title">
					开始时间
				</view>
				<view class="value">
					<uni-datetime-picker v-model="formData.begin_time" end="2038/01/19" :clearIcon="false" />
				</view>
			</view>
			<!-- 结束时间 -->
			<view class="edit-item">
				<view class="title">
					结束时间
				</view>
				<view class="value">
					<uni-datetime-picker v-model="formData.end_time" end="2038/01/19" :clearIcon="false" />
				</view>
			</view>
			<!-- 考卷说明 -->
        <!--is_activity 独立的考试活动不显示考卷说明-->
			<view v-if="!is_activity" class="edit-item">
				<view class="title">
					考卷说明
				</view>
				<view class="value">
					<uni-easyinput type="textarea" :maxlength="2000" autoHeight v-model="formData.content"
						placeholder="详细说明" />
				</view>
			</view>
		</view>
		<!-- 答题次数抽屉 -->
		<uni-drawer ref="answerNumber" mode="right" :mask-click="true" width="70%">
			<scroll-view style="height: 100%;" scroll-y="true">
				<!-- 答题次数限制类型 -->
				<view class="drawer-item p1">
					<view class="f-a-c pb_5">
						答题次数限制类型
						<i class="iconfont memo_text font_9"
							@click.stop="showModal('固定次数：总共可答题次数；周期次数：每天可答题次数')">&#xe65a;</i>
					</view>
					<view class="box">
						<uni-data-checkbox v-model="formData.answer_limit_types" :localdata="answer_limit_types"
							@change="change_answer_limit_types" />
					</view>
				</view>
				<!-- 答题次数 -->
				<view class="drawer-item p1">
					<view class="pb_5">
						答题次数
					</view>
					<view class="box">
						<uni-data-checkbox v-model="answer_limit_num" :localdata="answer_limit_num_list"
							@change="formData.set_limit_times = answer_limit_num" />
						<uni-easyinput v-if="answer_limit_num == 15" type="number" maxlength="7"
							v-model="formData.set_limit_times" placeholder="答题次数 1代表限制1次 2代表限制2次 以此类推"
							:clearable="false" />
					</view>
				</view>
			</scroll-view>
		</uni-drawer>

		<!-- 扩展设置 -->
		<view v-if="tabs_list[tabs_idx].idx == 1">
			<!-- 考卷排序 -->
        <!--is_activity 独立的考试活动只有一个考卷，不用排序-->
			<view v-if="!is_activity" class="edit-item">
				<view class="title f-a-c">
					考卷排序
					<i class="iconfont memo_text font_9" @click.stop="showModal('考卷排序,数字越小排序越前')">&#xe65a;</i>
				</view>
				<view class="value">
					<uni-easyinput type="number" maxlength="5" v-model="formData.sort_num" placeholder="考卷排序,数字越小排序越前"
						:clearable="false" />
				</view>
			</view>

			<!-- 缩略图 -->
        <!--is_activity 独立的考试活动没有考卷详情，不显示缩略图-->
			<view v-if="!is_activity" class="edit-item">
				<view class="title">
					缩略图
				</view>
				<view class="value f">
					<view v-if="formData.logo" class="upload-img-box along_img f">
						<image :src="formData.logo" mode="aspectFill" @click.stop="skip_image_house('logo')">
						</image>
						<i class="iconfont f-j-a-c" @click.stop="formData.logo = ''">&#xe692;</i>
					</view>
					<view v-if="!formData.logo" class="upload-icon" @click.stop="skip_image_house('logo')">
						<i class="iconfont">&#xe602;</i>
					</view>
				</view>
			</view>

			<!-- 分享设置 -->
<!--			<view class="edit-item">
				<view class="title">
					分享设置
				</view>
				<view class="value f-a-c">
					<view class="left f-a-c memo_text">
						开启/关闭
					</view>
					<view class="right">
						<select-lay :value="conf.share_set.show" name="name" :options="active_explain_show" :zindex="26"
							@selectitem="select_share_set" placeholder=""></select-lay>
					</view>
				</view>
				<view v-if="conf.share_set.show" class="value f-a-c">
					<view class="left f-a-c memo_text">
						分享标题
					</view>
					<view class="right">
						<uni-easyinput :maxlength="50" v-model="conf.share_set.title" placeholder="输入分享的标题" />
					</view>
				</view>
				<view v-if="conf.share_set.show" class="value f-a-c">
					<view class="left f-a-c memo_text">
						分享图片
					</view>
					<view class="right">
						<view v-if="conf.share_set.logo" class="upload-img-box f" style="width:100px;height: auto;max-height: none">
							<image :src="conf.share_set.logo" mode="widthFix"
								@click.stop="skip_image_house('share_logo')">
							</image>
							<i class="iconfont f-j-a-c" @click.stop="conf.share_set.logo = ''">&#xe692;</i>
						</view>
						<view v-if="!conf.share_set.logo" class="upload-icon"
							@click.stop="skip_image_house('share_logo')">
							<i class="iconfont">&#xe602;</i>
						</view>
					</view>
				</view>
			</view>-->

			<!--背景音乐 -->
<!--			<view class="edit-item">
				<view class="title f-a-c">
					<text>背景音乐</text>
					<i class="iconfont memo_text" @click.stop="showModal('背景音乐，输入音频地址，并且确认音频可以播放！')">&#xe65a;</i>
					<button v-if="!formData.rank_set || !formData.rank_set.closed_AD"
						class="button-normal blue_color pl1" open-type="contact">联系客服</button>
				</view>
				<view class="value f">
					<uni-easyinput v-if="!formData.rank_set || !formData.rank_set.closed_AD" class="width100" disabled
						placeholder="未开通此功能，请联系客服开通！" />
					<uni-easyinput v-if="formData.rank_set && formData.rank_set.closed_AD" v-model="conf.bg_music"
						placeholder="背景音乐，输入音频地址">
					</uni-easyinput>
				</view>
			</view>-->

			<!-- 阅卷模式 -->
<!--			<view class="edit-item">
				<view class="title f-a-c">
					阅卷模式
					<i class="iconfont memo_text font_9" @click.stop="showModal('系统自动判分或者管理员人工判分!')">&#xe65a;</i>
				</view>
				<view class="value f-a-c">
					<select-lay class="width100" :value="conf.waiting_persont" name="name" :options="waiting_persont"
						:zindex="16" @selectitem="select_waiting_persont" placeholder=""></select-lay>
				</view>
			</view>-->

			<!-- 答题类型 -->
			<view class="edit-item">
				<view class="title f-a-c">
					答题类型
					<i class="iconfont memo_text font_9" @click.stop="showModal('答题类型，答题卡模式还是竞赛模式')">&#xe65a;</i>
				</view>
				<view class="value f-a-c">
					<view class="left f-a-c memo_text">
						选择类型
					</view>
					<view class="right">
						<select-lay :value="conf.answer_question_types" name="name" :options="answer_question_types"
							:zindex="13" @selectitem="select_answer_question_types" placeholder=""></select-lay>
					</view>
				</view>
				<!-- 答卷时间 -->
				<view v-if="conf.answer_question_types == 1" class="value f-a-c">
					<view class="left f-a-c memo_text">
						答卷时间
						<i class="iconfont memo_text font_9" @click.stop="showModal('考卷的答题总计时，单位分钟!')">&#xe65a;</i>
					</view>
					<view class="right">
						<uni-easyinput :maxlength="10" type="number" v-model="conf.answer_time"
							placeholder="输入答卷时间,整场考卷用时 单位:分钟" />
					</view>
				</view>
				<!-- 考题时间 -->
				<view v-if="conf.answer_question_types > 1" class="value f-a-c">
					<view class="left f-a-c memo_text">
						考题时间
						<i class="iconfont memo_text font_9" @click.stop="showModal('每题的答题时间，单位秒!')">&#xe65a;</i>
					</view>
					<view class="right">
						<uni-easyinput :maxlength="10" type="number" v-model="conf.question_seconds"
							placeholder="输入考题时间,每题的用时 单位:秒" />
					</view>
				</view>
			</view>
            
			<view class="edit-item">
				<view class="title f-a-c">
					答案解析
					<i class="iconfont memo_text font_9" @click.stop="showModal('每道题目做完后是否显示相应题目的答题解析!')">&#xe65a;</i>
				</view>
				<view class="value f-a-c">
                    <view class="left f-a-c memo_text">
                        显示类型
                    </view>
                    <view class="right">
                        <uni-data-select v-model="conf.explain_text_open" :localdata="explain_text_open"
                                         :clear="false"></uni-data-select>
                    </view>
				</view>
			</view>

        <!--<view class="edit-item">
            <view class="title f-a-c">
                考试回看
                <i class="iconfont memo_text font_9" @click.stop="showModal('考卷完成后是否显示所有题目的答案和解析!')">&#xe65a;</i>
            </view>
            <view class="value f-a-c">
                <view class="left f-a-c memo_text">
                    开启/关闭
                </view>
                <view class="right">
                    <select-lay :value="conf.look_back_closed" name="name" :options="look_back_closed"
                                :zindex="11" @selectitem="select_look_back_closed" placeholder=""></select-lay>
                </view>
            </view>
        </view>-->

			<!-- 练习模式 -->
<!--			<view class="edit-item">
				<view class="title f-a-c">
					练习模式
					<i class="iconfont memo_text font_9"
						@click.stop="showModal('考卷详情是否显示“我要练习”，可以让考生练习题目。')">&#xe65a;</i>
				</view>
				<view class="value f-a-c mb0">
					<view class="left f-a-c memo_text">
						开启/关闭
					</view>
					<view class="right">
						<select-lay :value="conf.practice_set.show" name="name" :options="practice_set_show" :zindex="9"
							@selectitem="select_practice_show" placeholder=""></select-lay>
					</view>
				</view>
			</view>-->

			<!-- 地区白名单 -->
<!--			<view class="edit-item">
				<view class="title f-a-c">
					地区白名单
					<i class="iconfont memo_text font_9" @click.stop="showModal('配置地址白名单，只有在白名单中的地址才能答题。')">&#xe65a;</i>
				</view>
				<view class="value f-a-c mb0">
					<view class="left f-a-c memo_text">
						开启/关闭
					</view>
					<view class="right">
						<select-lay :value="conf.ip_set.open" name="name" :options="must_submit_show" :zindex="8"
							@selectitem="select_ip_set" placeholder=""></select-lay>
					</view>
				</view>
				&lt;!&ndash; 白名单列表 &ndash;&gt;
				<view v-if="conf.ip_set.open" class="value f-a-c mb0 pt1">
					<view class="left f-a-c memo_text">
						白名单
					</view>
					<view class="right font_9">
						<view v-if="conf.ip_set.ip_city && conf.ip_set.ip_city.length" class="display-ib"
							v-for="(item,index) in conf.ip_set.ip_city" :key="index">
							<text>{{item}}</text>
							<text v-if="index < conf.ip_set.ip_city.length - 1">,</text>
						</view>
						<text v-if="!conf.ip_set.ip_city || !conf.ip_set.ip_city.length"
							class="gray_color">未设置白名单</text>
						<view class="display-ib blue_color pl1" @click.stop="open_white_modal">
							去设置
						</view>
					</view>
				</view>
			</view>-->

			<!-- 微信群限制 -->
<!--			<view v-if="conf.wechat_group" class="edit-item">
				<view class="title f-j-sb-a-c pr0">
					<view class="">
						<text>微信群限制</text>
						<i class="iconfont memo_text font_9 display-ib" @click.stop="wechat_group_tip">&#xe65a;</i>
						<text class="blue_color font_9 pl_5" @click.stop="wechat_group_tip">如何指定微信群</text>
					</view>
					<text v-if="conf.wechat_group.list && conf.wechat_group.list.length" class="font_9 blue_color pl1"
						@click.stop="skip_wechat_group">微信群管理</text>
				</view>
				<view class="value f-a-c mb0">
					<view class="left f-a-c memo_text">
						开启/关闭
					</view>
					<view class="right">
						<select-lay :value="conf.wechat_group.show" name="name" :options="must_submit_show" :zindex="6"
							@selectitem="select_wechat_group_show" placeholder=""></select-lay>
					</view>
				</view>
			</view>-->

			<!-- 防作弊设置 -->
<!--			<view v-if="conf.anti_cheating" class="edit-item">
				<view class="title f-a-c">
					防作弊设置
				</view>
				<view class="value f-a-c mb0">
					<view class="left f-a-c memo_text">
						开启/关闭
					</view>
					<view class="right">
						<select-lay :value="conf.anti_cheating.show" name="name" :options="must_submit_show" :zindex="4"
							@selectitem="select_anti_cheating_show" placeholder=""></select-lay>
					</view>
				</view>
				<view v-if="conf.anti_cheating.show" class="value f-a-c pt1">
					<view class="left f-a-c memo_text">
						切屏次数
						<i class="iconfont memo_text font_9"
							@click.stop="showModal('可以切屏的次数，达到次数后再次切屏将直接提交答案！设置次数为0，只要切屏就会直接提交答案！')">&#xe65a;</i>
					</view>
					<view class="right">
						<uni-easyinput :maxlength="10" type="number" v-model="conf.anti_cheating.cuts_screen"
							placeholder="可以切屏的次数，达到次数后再次切屏将直接提交答案!" />
					</view>
				</view>
			</view>-->

			<!-- 实名信息 H5才能设置实名信息 -->
            <!-- #ifdef H5 -->
			<view class="edit-item">
				<view class="title">
					实名信息
				</view>
				<view class="value f-a-c mb0">
					<view class="left f-a-c memo_text">
						开启/关闭
					</view>
					<view class="right">
						<select-lay :value="conf.must_submit.show" name="name" :options="must_submit_show" :zindex="3"
							@selectitem="select_must_submit_show" placeholder=""></select-lay>
					</view>
				</view>
				<view v-if="conf.must_submit.show" class="value f-a-c mb0">
					<view class="must_submit_box">
						<view class="must_submit_item item gray_bdrbdashed"
							v-for="(item,index) in conf.must_submit.list" :key="index">
							<view class="must-box f-j-sb-a-c" :style="{'padding-bottom':item.types == 2?'.5rem':''}">
								<!-- 选择格式 -->
								<view class="must-left f-a-c">
									<!-- 需要填写的实名信息 -->
									<view class="input-box">
										<uni-easyinput :maxlength="500" class="input" v-model="item.title"
											:disabled="index == 0" placeholder="需要填写的信息" />
									</view>
									<!-- 必填还是选填 -->
									<picker mode="selector" :value="item.rules" :range="must_submit_rules"
										:data-index="index" @change="change_rules" range-key="name">
										<view class="selector-box">
											<view class="selector-item f-j-a-c">
												<view>{{must_submit_rules[item.rules].name}}</view>
												<i class="iconfont">&#xe6a6;</i>
											</view>
										</view>
									</picker>
									<!-- 文本输入还是下拉选择 -->
									<picker mode="selector" :value="item.types - 1" :range="must_submit_types"
										:data-index="index" @change="change_types" range-key="name">
										<view class="selector-box">
											<view class="selector-item f-j-a-c">
												<view>{{must_submit_types[item.types - 1].name}}</view>
												<i class="iconfont">&#xe6a6;</i>
											</view>
										</view>
									</picker>
								</view>
								<text v-if="index > 0" class="red_color"
									@click.stop="cancel_must_submit_item(index)">删除</text>
							</view>
							<!-- 单选 -->
							<view v-if="item.types == 2" class="must-radio-box">
								<view class="must-radio">
									<view class="must-radio-item f-j-sb-a-c" v-for="(itm,idx) in item.option"
										:key="itm">
										<view class="left">
											<uni-easyinput :maxlength="2000" v-model="itm.text" placeholder="输入选项内容" />
										</view>
										<text v-if="idx > 0" class="memo_text"
											@click.stop="cancel_must_radio(index,idx)">删除</text>
									</view>
								</view>
								<view class="memo_text f-a-c" @click.stop="add_must_radio(index)">
									<i class="iconfont" style="padding-right: .2rem;">&#xe643;</i>
									添加单选
								</view>
							</view>
						</view>
						<view class="blue_color f-a-c" style="padding: .5rem 0;" @click.stop="add_must_submit_item">
							<i class="iconfont" style="padding-right: .2rem;">&#xe643;</i>
							添加新项
						</view>
					</view>
				</view>
			</view>
            <!-- #endif -->
		</view>

		<!-- 大图模式 -->
		<view v-if="tabs_list[tabs_idx].idx == 2">
			<!-- 大图模式 -->
			<view class="edit-item">
				<view class="title">
					大图模式
					<text v-if="system_tips_text.exam_big_pic_tips" class="pl_5 font_9 blue_color"
						@click.stop="skip_article_details(system_tips_text.exam_big_pic_tips)">查看示例</text>
				</view>
				<view class="value f-a-c mb0">
					<view class="left f-a-c memo_text">
						开启/关闭
					</view>
					<view class="right">
						<select-lay :value="conf.big_screen_set.show" name="name" :options="big_screen_show" :zindex="9"
							@selectitem="select_big_screen_show" placeholder=""></select-lay>
					</view>
				</view>
			</view>

			<!-- 轮播图 -->
			<view v-if="!conf.big_screen_set.show" class="edit-item">
				<view class="title">
					轮播图
				</view>
				<view class="value f-w">
					<view v-if="formData.pics && formData.pics.length" class="upload-img-box pic_list f"
						style="margin-bottom:15px;height:auto;max-height:none;"
						:style="{'margin-right':(index+1) % 3 == 0?'':'15px'}" v-for="(item,index) in formData.pics"
						:key="index">
						<image :src="item" mode="widthFix" @click.stop="previewImage(index)">
						</image>
						<i class="iconfont f-j-a-c" @click.stop="cancel_pic_list(index)">&#xe692;</i>
					</view>
					<view v-if="!formData.pics || formData.pics.length < 6" class="upload-icon"
						@click.stop="skip_image_house('pics')">
						<i class="iconfont">&#xe602;</i>
					</view>
				</view>
			</view>

			<view v-if="conf.big_screen_set.show" class="">
				<!-- 考卷开屏大图 -->
				<view class="edit-item">
					<view class="title">
						考卷开屏大图
					</view>
					<view class="value f-w">
						<view v-if="conf.big_screen_set.screen_pic" class="upload-img-box f"
							style="width: 200px;height:auto;max-height:none;">
							<image :src="conf.big_screen_set.screen_pic" mode="widthFix"
								@click.stop="skip_image_house('screen_pic')">
							</image>
							<i class="iconfont f-j-a-c" @click.stop="conf.big_screen_set.screen_pic = ''">&#xe692;</i>
						</view>
						<view v-if="!conf.big_screen_set.screen_pic" class="upload-icon"
							@click.stop="skip_image_house('screen_pic')">
							<i class="iconfont">&#xe602;</i>
						</view>
					</view>
				</view>
				<!-- 详情页大图 -->
				<view class="edit-item">
					<view class="title">
						详情页大图
					</view>
					<view class="value f-w">
						<view v-if="conf.big_screen_set.exam_details_pic" class="upload-img-box f"
							style="width: 200px;height:auto;max-height:none;">
							<image :src="conf.big_screen_set.exam_details_pic" mode="widthFix"
								@click.stop="skip_image_house('exam_details_pic')">
							</image>
							<i class="iconfont f-j-a-c"
								@click.stop="conf.big_screen_set.exam_details_pic = ''">&#xe692;</i>
						</view>
						<view v-if="!conf.big_screen_set.exam_details_pic" class="upload-icon"
							@click.stop="skip_image_house('exam_details_pic')">
							<i class="iconfont">&#xe602;</i>
						</view>
					</view>
				</view>

				<!-- 按钮文字颜色 -->
				<view class="edit-item f-a-c p5-1">
					<view class="title">
						按钮文字颜色
					</view>
					<view class="value f-j-e-a-c mb0" @click.stop="open_color_picker(1)">
						<text class="memo_text pr_5"
							v-if="conf.big_screen_set.button_text_color == 'transparent'">透明色</text>
						<view class="color-box" :style="{'background-color': conf.big_screen_set.button_text_color}">
						</view>
					</view>
				</view>

				<!-- 按钮背景颜色 -->
				<view class="edit-item f-a-c p5-1">
					<view class="title">
						按钮背景颜色
					</view>
					<view class="value f-j-e-a-c mb0" @click.stop="open_color_picker(2)">
						<text class="memo_text pr_5"
							v-if="conf.big_screen_set.button_background_color == 'transparent'">透明色</text>
						<view class="color-box"
							:style="{'background-color': conf.big_screen_set.button_background_color}">
						</view>
					</view>
				</view>
                
				<view class="edit-item">
					<view class="title f-a-c">
						活动说明
					</view>
					<view class="value f-a-c">
						<view class="left f-a-c memo_text">
							开启/关闭
						</view>
						<view class="right">
							<select-lay :value="conf.active_explain_show" name="name" :options="active_explain_show"
								:zindex="10" @selectitem="select_active_explain_show" placeholder=""></select-lay>
						</view>
					</view>
				</view>
                
				<view class="edit-item">
					<view class="title f-a-c">
						大图倒计时
					</view>
					<view class="value f-a-c">
						<view class="left f-a-c memo_text">
							倒计时(秒)
						</view>
						<view class="right">
							<uni-easyinput :maxlength="10" v-model="conf.big_screen_set.down_seconds"
								placeholder="倒计时,默认3秒" />
						</view>
					</view>
				</view>

			</view>
		</view>


		<!-- 种树水滴设置 只能整数 奖励设置 -->
		<view v-if="tabs_list[tabs_idx].idx == 3" class="">
			<view class="edit-item">
				<view class="title">
					开启/关闭
				</view>
				<view class="value">
					<select-lay :value="reward_set.show" name="name" :options="reward_show" :zindex="20"
						@selectitem="select_reward_show" placeholder=""></select-lay>
				</view>
			</view>

			<view v-if="reward_set.show == 1" class="">
				<view v-if="reward_unit == '元' && params.exam_id" class="edit-item">
					<view class="title">
						红包余额
						<text class="gray_color font_8 pl_7">
							￥ {{formData.amount_left ? formData.amount_left : 0}} 元
						</text>
					</view>
					<view class="value f-a-c">
						<uni-easyinput :maxlength="9" class="width100" type="digit" v-model="recharge_value"
							placeholder="输入需要充值的红包金额" />
						<view
							class="min-width6 gray_bdrsolid blue_color radius_3 p5_0 text-center ml1 font_8 blk_shadow_005"
							@click.stop="redpacket_recharge">
							立即充值
						</view>
					</view>
				</view>
				<view class="edit-item">
					<view class="title">
						奖励规则
					</view>
					<view class="value">
						<select-lay :value="reward_set.types" name="name" :options="reward_types" :zindex="19"
							@selectitem="select_reward_types" placeholder=""></select-lay>
					</view>
				</view>
				<!-- 奖励规则设置 -->
				<view class="edit-item">
					<view class="title f-a-c">
						设置奖励
						<i class="iconfont font_9 ml_3" @click.stop="showModal(reward_title)">&#xe65a;</i>
					</view>
					<view class="value">
						<view v-if="(reward_set.types == 1 && index == 0) || reward_set.types == 2"
							class="redpack-list p5_0 f-a-c gray_bdrbdashed" v-for="(item,index) in reward_set.rules"
							:key="index">
							<view class="left p_5 radius_2">
								<view v-if="reward_set.types == 2" class="score-item f-a-c mb_5">
									<text class="pr_5">成绩</text>
									<view class="inp radius_2 gray_bdrsolid">
										<input class="input" type="digit" v-model="item.min" />
									</view>
									<text class="p0_5"> ~ </text>
									<view class="inp radius_2 gray_bdrsolid">
										<input class="input" type="digit" v-model="item.max" />
									</view>
									<view class="pl_5">
										<text>分</text>
										<text class="memo_text">(含)</text>
									</view>
								</view>
								<view class="score-item f-a-c">
									<text class="pr_5">奖励</text>
									<view class="inp radius_2 gray_bdrsolid">
										<input class="input" :type="activity_details.types?'number':'digit'"
											v-model="item.amount_min" :data-index="index" @blur="change_amount_min" />
									</view>
									<text class="p0_5"> ~ </text>
									<view class="inp radius_2 gray_bdrsolid">
										<input class="input" :type="activity_details.types?'number':'digit'"
											v-model="item.amount_max" :data-index="index" @blur="change_amount_max" />
									</view>
									<view class="pl_5">
										{{reward_unit}}
									</view>
								</view>
							</view>
							<view v-if="index > 0" class="right f-j-e-a-c red_color"
								@click.stop="cancel_rules(index, 1)">
								<i class="iconfont gray_color ">&#xe625;</i>
							</view>
						</view>
						<view v-if="reward_set.types == 2" class="blue_color f-a-c pt_5" @click.stop="add_rules(1)">
							<i class="iconfont pr_2">&#xe643;</i>
							增加奖励规则
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 模板设置 -->
		<view v-if="tabs_list[tabs_idx].idx == 5">
			<view class="memo_text p1 blk_shadow_005">
				<view class="">
					模板设置，答题页面的展示风格
				</view>
				<view v-if="!params.exam_id" class="pt_5">
					请先添加考卷，然后再设置模板！
				</view>
				<view v-if="cur_template_name" class="pt_5">
					<text>当前模板：</text>
					<text class="blue_color">{{cur_template_name}}</text>
				</view>
			</view>
			<view class="p1 f">
				<radio-group class="width100 f-w" @change="change_answer_template">
					<label class="width50 f-d-c-a-c mb1" v-for="(item,index) in answer_template_list" :key="index"
						@click.stop="judge_open_exam_tpl(index)">
						<view class="width100 f-d-c-j-a-c">
							<view class="f width80 relative">
								<image :src="item.img" mode="widthFix"/>
							</view>
							<view class="color555 pt_3">
								<text>{{item.name}}</text>
								<text class="font_9 blue_color pl_5" @click.stop="preview_image(index)">预览</text>
							</view>
						</view>
						<view class="pt_5">
							<radio :value="item.id" :checked="item.id == conf.answer_template_id"/>
						</view>
					</label>
				</radio-group>
			</view>
		</view>


		<!-- 鼓励词设置 -->
		<view v-if="tabs_list[tabs_idx].idx == 6" class="">
			<view v-if="system_tips_text.exam_score_result_page_tips" class="blue_color p0-1 pt1"
				@click.stop="skip_article_details(system_tips_text.exam_score_result_page_tips)">
				查看示例
			</view>
			<view class="p1 f-a-c">
				<view class="min-width6">
					<text>开启 / 关闭</text>
				</view>
				<view class="width100">
					<select-lay :value="certificate_set.show" name="name" :options="certificate_show" :zindex="20"
						@selectitem="select_certificate_show" placeholder=""></select-lay>
				</view>
			</view>

			<view v-if="certificate_set.show == 1" class="p0-1 gray-bdrt">
				<view class="p1-0 f-a-c gray_bdrbdashed">
					<view class="min-width6">
						规则设置
					</view>
					<view class="width100">
						<select-lay :value="certificate_set.types" name="name" :options="certificate_types" :zindex="19"
							@selectitem="select_certificate_types" placeholder=""></select-lay>
					</view>
				</view>
				<!-- 奖励规则设置 -->
				<view class="p7_0">
					<view class="f-a-c">
						设置规则
						<i class="iconfont ml_3 font_9" @click.stop="showModal(certificate_title)">&#xe65a;</i>
					</view>
					<view class="value font_9">
						<view v-if="(certificate_set.types == 1 && index == 0) || certificate_set.types == 2"
							class="redpack-list f-a-c p7_0 gray_bdrbdashed"
							v-for="(item,index) in certificate_set.rules" :key="index">
							<view class="left radius_2">
								<view v-if="certificate_set.types == 2" class="score-item f-a-c mb_5">
									<text class="min-width3">成绩</text>
									<view class="inp radius_2 gray_bdrsolid">
										<input class="input" type="digit" v-model="item.min" />
									</view>
									<text class="p0_5"> ~ </text>
									<view class="inp radius_2 gray_bdrsolid">
										<input class="input" type="digit" v-model="item.max" />
									</view>
									<view class="pl_5">
										<text>分</text>
										<text class="memo_text">(含)</text>
									</view>
								</view>
								<view class="score-item f-a-c">
									<view class="min-width3">
										鼓励词
									</view>
									<view class="width100 radius_2 gray_bdrsolid p3_0 pl_5 pr_5">
										<input class="width100" v-model="item.text" placeholder="输入鼓励词" />
									</view>
								</view>
							</view>
							<view v-if="index > 0" class="right f-j-e-a-c red_color"
								@click.stop="cancel_rules(index, 2)">
								<i class="iconfont gray_color ">&#xe625;</i>
							</view>
						</view>
						<view v-if="certificate_set.types == 2" class="blue_color f-a-c pt_5"
							@click.stop="add_rules(2)">
							<i class="iconfont pr_2">&#xe643;</i>
							增加规则
						</view>
					</view>
				</view>
			</view>
		</view>


		<!-- 弹框 -->
		<view v-if="show_modal" class="modal-box f-d-c-j-a-c">
			<view class="modal-container">
				<template>
					<view class="item-box p_7 pt2 pb2 f-a-c">
						<view class="left memo_text min-width4">
							充值金额
						</view>
						<view class="right pl1 width100">
							<uni-easyinput :maxlength="16" class="width100" type="digit" v-model="recharge_value"
								placeholder="输入需要充值的红包金额" />
						</view>
					</view>
				</template>
				<view class="bottom-box gray_bdrtsolid f-j-sb-a-c">
					<view class="p_7 font_9 gray_color width50 text-center" @click.stop="close_modal">
						取消
					</view>
					<view class="p_7 font_9 blue_color width50 gray_bdrlsolid text-center"
						@click.stop="redpacket_recharge()">
						充值
					</view>
				</view>
			</view>
			<i class="iconfont color-white pt_5 font1_7" @click.stop="close_modal">&#xe618;</i>
		</view>



		<!-- 地区白名单输入框 -->
		<view v-if="show_white_modal" class="fixed_modal">
			<view class="fixed_modal_content p0 relative">
				<view class="top p1 pb2">
					<view class="input-title pb_7">
						<view class="pb_3">
							请输入市级地区，例如：深圳市
						</view>
						<view class="memo_text">
							<view class="">
								注意：
							</view>
							<view class="">
								1、暂不支持县级或区级地区填写，请只填写市级地区。
							</view>
							<view class="">
								<text>2、一行只填写一个白名单地址</text>
								<text class="pl_5 blue_color" @click.stop="assign_white_val_demo">示例</text>
							</view>
						</view>
					</view>
					<view class="textarea width100 radius_3 p_7">
						<textarea class="width100 height100" type="textarea" :maxlength="10000" v-model="white_val"
							placeholder="请输入地区白名单"></textarea>
					</view>
				</view>

				<view class="btn-box f gray_bdrtsolid bg-white width100">
					<view class="width50 gray_color p1-0 f-j-a-c" @click.stop="close_white_modal">
						取消
					</view>
					<view class="width50 gray_color p1-0 f-j-a-c gray_bdrlsolid" @click.stop="assign_ip_list">
						确定
					</view>
				</view>
			</view>
		</view>

		<helang-picker-color :isShow="showPickerColor" :bottom="0" @callback='color_confirm' />
		<bottom-btn :btnList="btn_list" @save="save_exam" @cancel="return_previous_page"></bottom-btn>
		<service-modal v-if="show_service" :text="service_text" @closeServiceModal="show_service = false">
		</service-modal>
	</view>
</template>

<script>
    import must_opt from "@/pages/likou_dati/components/must-submit/must-submit.js";
    import confset from "../exam_set.js";

	import liuyunoTabs from "@/pages/likou_dati/components/liuyuno-tabs/liuyuno-tabs.vue"
	import uniDrawer from "@/pages/likou_dati/uni_modules/uni-drawer/components/uni-drawer/uni-drawer.vue"
	import uniDataCheckbox from "@/pages/likou_dati/uni_modules/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox.vue"
	import selectLay from "@/pages/likou_dati/uni_modules/select-lay/components/select-lay/select-lay.vue"
    import helangPickerColor from "@/pages/likou_dati/components/helang-pickerColor/helang-pickerColor.vue"
	import bottomBtn from "@/pages/likou_dati/components/bottom-btn/bottom-btn.vue"
	import serviceModal from "@/pages/likou_dati/components/service-modal/service-modal.vue"

    let app = getApp();

	export default {
    components: {
      liuyunoTabs,
      uniDrawer,
      uniDataCheckbox,
      selectLay,
        helangPickerColor,
      bottomBtn,
      serviceModal
    },
		data() {
			return {
				params: {},
				userInfo: {},
				tabs_list: this.xwyLib.copyAry(confset.exam_add.tabs_list),
				tabs_idx: 0,

				btn_list: [{
						text: '取消',
						clickFun: 'cancel',
						cancel: 1,
					},
					{
						text: '保存',
						clickFun: 'save'
					}
				],

				formData: {
					exam_name: '',
					max_num: '',
					content: '',
					set_limit_times: 1,
					begin_time: this.xwyLib.get_current_date(0),
					end_time: this.xwyLib.get_current_date(365),
					logo: '',
					pics: '',
					sort_num: 10,
					answer_limit_types: 1, // 1固定次数 2周期次数
				},

				answer_limit_types_text: '固定次数',
				answer_limit_types: this.xwyLib.copyAry(confset.exam_add.answer_limit_types),
				answer_limit_num: 1,
				answer_limit_num_list: this.xwyLib.copyAry(confset.exam_add.answer_limit_num_list),

				conf: {},
				must_submit_show: must_opt.must_submit_show,
				must_submit_rules: must_opt.must_submit_rules,
				must_submit_types: must_opt.must_submit_types,

				reward_set: {
					show: 0,
					types: 1,
					rules: [{
						min: 0, //大于
						max: 100, //小于等于 （含100分）
						amount_min: 1,
						amount_max: 1,
					}],
				},
				reward_show: this.xwyLib.copyAry(confset.reward_show),
				reward_types: this.xwyLib.copyAry(confset.reward_types),
				reward_title: '不限制分数随机奖励，只要参与答题都可以随机获得相应奖励；根据答题分数获取奖励，则按照设置分数不同，获得不同奖励。',
				reward_unit: '元',

				activity_details: {},

				big_screen_set: this.xwyLib.copyObj(confset.conf.big_screen_set),
				big_screen_show: this.xwyLib.copyAry(confset.big_screen_show),
				practice_set_show: this.xwyLib.copyAry(confset.practice_set_show),
				active_explain_show: this.xwyLib.copyAry(confset.certificate_show),
				answer_question_types: this.xwyLib.copyAry(confset.answer_question_types),
				explain_text_open: this.xwyLib.copyAry(confset.explain_text_open),
          look_back_closed: this.xwyLib.copyAry(confset.look_back_closed),
				waiting_persont: this.xwyLib.copyAry(confset.waiting_persont),

				showPickerColor: false, // 颜色选择
				service_text: '未开通此功能，请联系客服开通！',
				show_service: false,

				redpack_amount: 0, // 红包余额
				recharge_value: 0, // 充值红包金额
				show_modal: false, // 充值弹框
				is_load: false,

				system_tips_text: {},

				answer_template_list: [],
				cur_template_name: '',

				certificate_set: this.xwyLib.copyObj(confset.conf.certificate_set || {}),
				certificate_types: this.xwyLib.copyAry(confset.certificate_types),
				certificate_show: this.xwyLib.copyAry(confset.certificate_show),
				certificate_title: '不限分数，只要参与答题都可以获得鼓励词；根据答题分数获取鼓励词，则按照设置分数不同，获得不同鼓励词。',

				show_white_modal: false,
				white_val: '',

          is_activity: false, // 是否是独立的活动，例如党建猜图
			}
		},
		onLoad(options) {
			this.params = options;
      if (options.is_activity) this.isActivitySet()
			this.xwyLib.init(() => {
				let title = this.params.exam_id ? '修改' : '创建';
				this.xwyLib.setBarTitle(`${title}考卷`)
				this.init();

				let shopconfset = app.globalData.shopconfset;
				if (shopconfset?.extend_set?.system_tips_text) this.system_tips_text = shopconfset?.extend_set
					?.system_tips_text
			})
		},

		onShow() {
			if (app.globalData.upload_img) {
				app.globalData.upload_img = null;
				let image_house = uni.getStorageSync('image_house') ? JSON.parse(uni.getStorageSync('image_house')) : {};
				const url = image_house.url;
				switch (image_house.picName) {
					case 'logo':
						this.$set(this.formData, 'logo', url);
						break;
					case 'screen_pic':
						this.$set(this.conf.big_screen_set, 'screen_pic', url)
						break;
					case 'exam_details_pic':
						this.$set(this.conf.big_screen_set, 'exam_details_pic', url);
						break;
					case 'pics':
						let pic_list = [];
						if (this.formData.pics) pic_list = this.formData.pics;
						pic_list.push(url)
						this.$set(this.formData, 'pics', pic_list);
						break;
					case 'share_logo':
						this.$set(this.conf.share_set, 'logo', url);
						break;
				}
			}
		},

		methods: {
			async init() {
				if (this.params.exam_id) {
					this.get_exam_papers_details();
					return
				}
				this.init_other_set();
				if (this.params.activityID) {
					this.get_activity_details(this.params.activityID)
				}
			},

			init_other_set() {
				if (!this.formData.conf) {
					must_opt.must_submit.show = 0;
					this.conf = {
						must_submit: must_opt.must_submit,
						tree_set: this.xwyLib.copyObj(confset.conf.tree_set),
						reward_set: this.xwyLib.copyObj(confset.conf.reward_set),
						big_screen_set: this.xwyLib.copyObj(confset.conf.big_screen_set),
						practice_set: this.xwyLib.copyObj(confset.conf.practice_set),
						answer_question_types: confset.conf.answer_question_types,
						question_seconds: confset.conf.question_seconds,
						answer_time: confset.conf.answer_time,
						explain_text_open: confset.conf.explain_text_open,
              look_back_closed: confset.conf.look_back_closed || 0,
						creator: '', // 出题人
						answer_template_id: 0,
						wechat_group: this.xwyLib.copyObj(confset.conf.wechat_group),
						anti_cheating: this.xwyLib.copyObj(confset.conf.anti_cheating),
						waiting_persont: confset.conf.waiting_persont,
                        is_questionnaire: 0
					};
					return
				}

				this.formData.conf = Object.assign({}, confset.conf, this.formData.conf);
				this.conf = this.formData.conf;
				this.reward_set = this?.conf?.reward_set;
				this.certificate_set = Object.assign(this.certificate_set, this?.conf?.certificate_set);
			},

			// 获取考卷详情
			get_exam_papers_details() {
				let obj = {
					exam_id: this.params.exam_id,
					success: res => {
						if (res.begin_time) res.begin_time = this.xwyLib.timestampToTime(res
							.begin_time);
						if (res.end_time) res.end_time = this.xwyLib.timestampToTime(res.end_time);
                        res.conf.is_questionnaire ||= 0
						this.formData = {
							...res
						};

						// 测试
						// res.rank_set.closed_AD = 0;

						if (this.formData.answer_limit_types == 2) this.answer_limit_types_text = '周期次数';
						if (this.formData.set_limit_times) {
							let limit_list = [1, 2, 4, 5, 10, 99999],
								is_custom = true;
							for (let i in limit_list) {
								if (limit_list[i] == this.formData.set_limit_times) {
									this.is_custom = false;
									break;
								}
							}
							if (is_custom) this.answer_limit_num = 15;
							if (!is_custom) this.answer_limit_num = this.formData.set_limit_times;
						}

						this.init_other_set();
						// || this.params.activityID
						if (this.formData.active_id) {
							this.get_activity_details(this.formData.active_id)
						}
					},
				};
				this.xwyLib.get_exam_papers_details(obj);
			},

			// 如果存在活动ID则获取活动详情
			get_activity_details(active_id) {
				let obj = {
					id: active_id,
					success: res => {
						this.activity_details = res.active_details;
						// 种树
						if (this.activity_details.types == 1) {
							this.reward_set = this.conf.tree_set;
							this.reward_unit = '水滴';
						}
					},
					fail: res => {
						this.xwyLib.showModal('未获取到活动详情！', {
							success: res => {
								this.xwyLib.back_previous_page();
							}
						})
					}
				};
				this.xwyLib.get_activity_details(obj);
			},



			skip_image_house(text) {
				let url = `/pages/likou_dati/pages/user/image_house/image_house`;
				if (text) url += `?picName=${text}`
				if (this.params.exam_id) url += `&active_id=${this.params.exam_id}`;
				this.xwyLib.routeJump(url)
			},

            isQuestionnaireChange(index) {
                this.$set(this.conf, 'is_questionnaire', index)
            },

			// 显示答题次数抽屉
			show_answerNumber() {
				this.$refs.answerNumber.open();
			},

			// 关闭答题次数抽屉
			close_answerNumber() {
				this.$refs.answerNumber.close();
			},

			change_answer_limit_types(e) {
				this.answer_limit_types_text = e.detail.data.text
			},

			// 返回上一页
			return_previous_page() {
				this.xwyLib.back_previous_page();
			},

			judge_params() {
				const data = this.formData;
				if (data.max_num && !this.xwyLib.isInt(Number(data.max_num))) {
					this.xwyLib.alert('考试人数错误！')
					return false
				}
				if (data.content && data.content > 2000) {
					this.xwyLib.alert('考卷说明文字不能多余3000字！')
					return
				}
				return true
			},

			save_exam() {
				if (!this.judge_params()) return

				this.xwyLib.showLoading();

				let data = {
					...this.formData,
				};

				if (!this.formData.sort_num) this.formData.sort_num = 10;

				if (data.pics?.length) data.pics = this.xwyLib.jsonEncode(data.pics);
				if (data.question_rules_conf) data.question_rules_conf = this.xwyLib.jsonEncode(data
					.question_rules_conf);
				if (this.params.exam_id) data.exam_id = this.params.exam_id;

				if (this?.activity_details?.types == 1) {
					this.conf.tree_set = this.reward_set;
				} else {
					this.conf.reward_set = this.reward_set;
				}

				if (this.certificate_set) this.conf.certificate_set = this.certificate_set;

				if (this.conf) {
					// 转拼音
					if (this.conf?.must_submit?.list && this.xwyLib.isArray(this.conf.must_submit.list)) {
						this.conf.must_submit.list.forEach(val => {
							val.name = this.xwyLib.topinyin(val.title).join('_');
						})
					}
					data.conf = this.xwyLib.jsonEncode(this.conf);
				}

				let obj = {
					data,
					url: `front.flat.exam.examPapers/create_exam_papers`,
					success: res => {
						uni.hideLoading();
						console.log('保存考卷', res);
						if (res.status != 1) {
							this.xwyLib.alert(res.info);
							return
						}

						if (this.params.activityID && res?.data?.exam_id) {
							this.add_exam(res.data.exam_id);
							return
						}

						if (!this.params.exam_id && this.xwyLib.query_page_exist('pages/index/index')) {
							let url =
								`/pages/likou_dati/pages/exam/exam_list/exam_list?my_self=1`;
							this.xwyLib.routeJump(url, 'redirectTo');
							return
						}

						this.xwyLib.showToast(res.info);
						setTimeout(() => {
							app.globalData.update_exam = 1;
							this.xwyLib.back_previous_page();
						}, 2000)
					}
				};
				this.xwyLib.ajax(obj);
			},

			add_exam(exam_id) {
				this.xwyLib.showLoading('添加考卷到活动中...');
				let data = {
					active_id: this.params.activityID,
					exam_id,
				};
				let obj = {
					data,
					url: `front.flat.exam.active/active_add_exam`,
					success: res => {
						uni.hideLoading();
						console.log('添加考卷到活动', res);
						if (res.status != 1) {
							this.xwyLib.alert(res.info);
							return
						}
						this.xwyLib.showToast(res.info);
						setTimeout(() => {
							app.globalData.update_exam = 1;
							this.xwyLib.back_previous_page();
						}, 2000)
					}
				};
				this.xwyLib.ajax(obj);
			},

			previewImage(e) {
				let list = [];
				if (this.xwyLib.isArray(this.formData.pics)) {
					this.formData.pics.forEach(val => {
						list.push(val.url);
					})
				}
				uni.previewImage({
					current: e,
					urls: list,
				})

			},

			showModal(str) {
				this.xwyLib.alert(str)
			},

			// 删除轮播图
			cancel_pic_list(e) {
				let pic_list = this.formData.pics;
				pic_list.splice(e, 1);
				this.$set(this.formData, 'pics', pic_list);
			},

			change_tabs(e) {
				if (this.tabs_idx == e) return
				if (this.tabs_list[e].idx == 5 && !this.xwyLib.isArray(this.answer_template_list)) {
					this.get_answer_template_list();
				}
				this.tabs_idx = e;
			},

			// 地区白名单开启/关闭
			select_ip_set(index, item) {
				let rank_set = this.formData?.rank_set || {};
				if (!rank_set.closed_AD) {
					this.open_service();
					return
				}

				if (index >= 0) {
					this.$set(this.conf.ip_set, 'open', item.value)
				} else {
					this.$set(this.conf.ip_set, 'open', 0)
				}
			},

			// 实名信息显示
			select_must_submit_show(index, item) {
				if (index >= 0) {
					this.$set(this.conf.must_submit, 'show', item.value)
				} else {
					this.$set(this.conf.must_submit, 'show', 1)
				}
			},

			// 答题类型
			select_answer_question_types(index, item) {
				if (index >= 0) {
					this.$set(this.conf, 'answer_question_types', item.value)
				} else {
					this.$set(this.conf, 'answer_question_types', 1)
				}
			},
            

        // 答题解析是否显示
        select_look_back_closed(index, item) {
            if (index >= 0) {
                this.$set(this.conf, 'look_back_closed', item.value)
            } else {
                this.$set(this.conf, 'look_back_closed', 0)
            }
        },

			// 开启/关闭练习模式
			select_practice_show(index, item) {
				if (index >= 0) {
					this.$set(this.conf.practice_set, 'show', item.value)
				} else {
					this.$set(this.conf.practice_set, 'show', 1)
				}
			},

			// 开启/关闭微信群设置
			select_wechat_group_show(index, item) {
				if (!this.params.exam_id) {
					this.xwyLib.alert('请先添加考卷，然后再设置微信群！');
					return
				}
				// 判断是否购买了此功能
				let limit_group_conf = this.conf?.limit_group_conf || '';
				if (!limit_group_conf) {
					this.xwyLib.judge_integral_cost_set({
						types: 10,
						success: res => {
							this.exam_conf_set({
								title: 'limit_group_conf',
								value: item.value,
							});
						},
					})
					return
				}

				if (index >= 0) {
					this.$set(this.conf.wechat_group, 'show', item.value)
				} else {
					this.$set(this.conf.wechat_group, 'show', 0)
				}
			},

			// 开启/关闭防作弊
			select_anti_cheating_show(index, item) {
				if (!this.params.exam_id) {
					this.xwyLib.alert('请先添加考卷，然后再设置防作弊！');
					return
				}
				// 判断是否购买了此功能
				let jump_screen = this.conf?.jump_screen || '';
				if (!jump_screen) {
					this.xwyLib.judge_integral_cost_set({
						types: 11,
						success: res => {
							this.exam_conf_set({
								title: 'jump_screen',
								value: item.value
							});
						},
					})
					return
				}

				if (index >= 0) {
					this.$set(this.conf.anti_cheating, 'show', item.value)
				} else {
					this.$set(this.conf.anti_cheating, 'show', 0)
				}
			},

			select_active_explain_show(index, item) {
				if (index >= 0) {
					this.$set(this.conf, 'active_explain_show', item.value)
				} else {
					this.$set(this.conf, 'active_explain_show', 1)
				}
			},

			select_big_screen_show(index, item) {
				// if (item.value == 1) {
				// 	let rank_set = this.formData?.rank_set || {};
				// 	if (!rank_set.closed_AD && !rank_set.big_screen) {
				// 		this.open_service();
				// 		return
				// 	}
				// }
				if (index >= 0) {
					this.$set(this.conf.big_screen_set, 'show', item.value)
				} else {
					this.$set(this.conf.big_screen_set, 'show', 1)
				}
			},

			select_reward_show(index, item) {
				if (index >= 0) {
					this.reward_set.show = item.value;
				} else {
					this.reward_set.show = 0
				}
			},

			// 通用奖励设置
			select_reward_types(index, item) {
				if (index >= 0) {
					this.reward_set.types = item.value;
				} else {
					this.reward_set.types = 1
				}
			},


			select_certificate_show(index, item) {
				if (item.value == 1) {
					let rank_set = this.formData?.rank_set || {};
					if (!rank_set.closed_AD) {
						this.open_service();
						return
					}
				}

				if (index >= 0) {
					this.certificate_set.show = item.value;
				} else {
					this.certificate_set.show = 0
				}
			},

			select_certificate_types(index, item) {
				if (index >= 0) {
					this.certificate_set.types = item.value;
				} else {
					this.certificate_set.types = 1
				}
			},


			// 人工阅卷
			select_waiting_persont(index, item) {
				if (index >= 0) {
					this.$set(this.conf, 'waiting_persont', item.value)
				} else {
					this.$set(this.conf, 'waiting_persont', 0)
				}
			},

			// 改变选填还是必填
			change_rules(e) {
				let index = e.currentTarget.dataset.index,
					value = e.detail.value;
				this.$set(this.conf.must_submit.list[index], 'rules', parseInt(value));
			},

			// 改变文本输入还是单选
			change_types(e) {
				let index = e.currentTarget.dataset.index,
					value = Number(e.detail.value) + 1;
				this.$set(this.conf.must_submit.list[index], 'types', parseInt(value));
			},

			// 添加实名信息新项
			add_must_submit_item() {
				this.conf.must_submit.list.push({
					name: '',
					title: '',
					rules: 0,
					types: 1,
					option: [{
						text: ''
					}],
				})
			},

			// 添加实名信息单选选项
			add_must_radio(index) {
				let must_submit = this.conf.must_submit.list,
					option = must_submit[index].option;
				option.push({
					text: '',
				})
				this.$set(this.conf.must_submit.list[index], 'option', option)
			},

			// 删除实名信息项
			cancel_must_submit_item(e) {
				this.conf.must_submit.list.splice(e, 1);
			},

			// 删除实名信息单选选项
			cancel_must_radio(index, idx) {
				let option = this.conf.must_submit.list[index].option;
				option.splice(idx, 1);
				this.$set(this.conf.must_submit.list[index], 'option', option);
			},

			// 添加新的红包奖励规则
			add_rules(e) {
				if (e == 1) {
					this.reward_set.rules.push({
						min: 0, //大于
						max: 100, //小于等于 （含100分）
						amount_min: 1,
						amount_max: 1,
					})
				}
				if (e == 2) {
					this.certificate_set.rules.push({
						min: 0, //大于
						max: 100, //小于等于 （含100分）
						text: '',
					})
				}
			},

			// 删除红包奖励规则
			cancel_rules(index, e) {
				if (e == 1) this.reward_set.rules.splice(index, 1);
				if (e == 2) this.certificate_set.rules.splice(index, 1);
			},

			change_amount_min(e) {
				let index = e.currentTarget.dataset.index;
				if (e.detail.value < 0.01) {
					this.xwyLib.alert('不能小于0.01！');
					this.$set(this.reward_set.rules[index], 'amount_min', 0.01)
					return
				}
			},

			change_amount_max(e) {
				let index = e.currentTarget.dataset.index;
				if (e.detail.value < 0.01) {
					this.xwyLib.alert('不能小于0.01！');
					this.$set(this.reward_set.rules[index], 'amount_max', 0.01)
					return
				}
			},

			open_color_picker(e) {
				this.color_idx = e;
				this.showPickerColor = true;
			},

			// 大图按钮背景颜色选择
			color_confirm(e) {
				if (!e || !e.includes('#') || (e.length != 7 && e.length != 4)) {
					this.showPickerColor = false;
					return
				}
				let color = e;
				if (this.color_idx == 1) this.$set(this.conf.big_screen_set, 'button_text_color', color);
				if (this.color_idx == 2) this.$set(this.conf.big_screen_set, 'button_background_color', color);
				this.showPickerColor = false
			},


			open_modal() {
				// #ifdef H5
				let get_browser = this.xwyLib.get_browser();
				if (get_browser != 1) {
					let url =
						`${this.xwyLib.static_url}web/wx-cloud-api/pages/active/index.html#/pages/exam/exam/exam_add/exam_add`
					url += `?shopid=${this.params.shopid}`
					if (this.params.exam_id) url += `&exam_id=${this.params.exam_id}`;
					console.log('url', url);
					this.xwyLib.copy_text({
						url,
						text: '请点击复制按钮，在微信中打开支付！',
					});
					return
				}
				// #endif
				this.show_modal = true;
			},

			close_modal() {
				this.show_modal = false;
			},

			// 红包充值
			redpacket_recharge() {
				// #ifdef H5
				this.xwyLib.alert('暂不支持网页充值，请前往小程序充值红包余额！')
				return
				let get_browser = this.xwyLib.get_browser();
				if (get_browser != 1) {
					let url =
						`${this.xwyLib.static_url}web/wx-cloud-api/pages/active/index.html#/pages/exam/exam/exam_add/exam_add`
					url += `?shopid=${this.params.shopid}`
					if (this.params.exam_id) url += `&exam_id=${this.params.exam_id}`;
					this.xwyLib.copy_text({
						url,
						text: '请点击复制按钮，在微信中打开支付！',
					});
					return
				}
				// #endif


				if (!this.recharge_value) {
					this.xwyLib.alert('请输入需要充值的红包金额！')
					return
				}

				if (this.is_load) return
				this.is_load = true;
				this.xwyLib.showLoading();

				// #ifdef MP-WEIXIN
				this.wechat_pay();
				// #endif

				// #ifdef H5
				// #endif
			},


			wechat_pay() {
				let obj = {
					amount: this.recharge_value,
					foreign_orderid: this.params.exam_id,
					success: res => {
						this.xwyLib.showToast('充值成功！');
						setTimeout(() => {
							this.get_exam_papers_details();
							this.recharge_value = 0;
						}, 1000)
					},
					fail: res => {
						this.xwyLib.alert(JSON.stringify(res));
					}
				};
				this.xwyLib.create_wechat_pay_params(obj);
			},


			// 跳转到文章详情
			skip_article_details(news_id) {
				if (!news_id) return
				let url = `/pages/news/preview?id=${news_id}`;
				this.xwyLib.routeJump(url);
			},

			change_answer_template(e) {
				this.conf.answer_template_id = e.detail.value;
				const list = this.answer_template_list;
				list.forEach(val => {
					if (val.id == e.detail.value) this.cur_template_name = val.name
				})
			},

			preview_image(index) {
				let list = [];
				if (this.xwyLib.isArray(this.answer_template_list)) {
					this.answer_template_list.forEach(val => {
						list.push(val.img);
					})
				}
				uni.previewImage({
					current: index,
					urls: list,
				})
			},

			// 判断是否开通了考试模板
			judge_open_exam_tpl(index) {
          const item = this.answer_template_list[index]
          const {id, name} = item
          if (id === this.conf.answer_template_id) return

				if (!this.params.exam_id) {
					this.xwyLib.alert('请先添加考卷，然后再设置模板！')
					return
				}

          this.conf.answer_template_id = id
          this.cur_template_name = name
			},


			// 获取答题模板列表
			get_answer_template_list(cb) {
          this.xwyLib.get_json_conf_set({
					name: `answer_template_list_${this.xwyLib.ext_conf.who || app.globalData.who}`,
					success: res => {
              if (res?.answer_template_list && this.xwyLib.isArray(res.answer_template_list)) {
							res.answer_template_list.sort((a, b) => {
								return a.sort_num - b.sort_num
							})
                const current_template = res.answer_template_list.find(val => val.id === this.conf?.answer_template_id)
                if (current_template?.name) this.cur_template_name = current_template.name
							this.answer_template_list = res.answer_template_list;
							cb && cb()
							return
						}
						this.answer_template_list = [{
							id: 0,
							img: "https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/000.jpg",
							integral: 0,
							name: "默认模板",
							sort_num: 0
						}]
						this.cur_template_name = this.answer_template_list[0].name;
					},
					fail: res => {
						this.answer_template_list = [{
							id: 0,
							img: "https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/template/000.jpg",
							integral: 0,
							name: "默认模板",
							sort_num: 0
						}]
						this.cur_template_name = this.answer_template_list[0].name;
					}
				})
			},



			// 跳转到地区白名单
			open_white_modal() {
				let list = this.conf?.ip_set?.ip_city || [];
				let str = '';
				if (this.xwyLib.isArray(list)) {
					list.forEach((val, idx) => {
						if (idx == 0) {
							str = `${val}`;
						} else {
							str += `\n${val}`
						}
					})
				}
				this.white_val = str;
				this.show_white_modal = true;
			},

			close_white_modal() {
				this.show_white_modal = false;
				this.white_val = "";
			},

			removeEmpty(arr) {
				for (var i = 0; i < arr.length; i++) {
					if (arr[i] === "" || typeof(arr[i]) == "undefined" || arr[i] == null) {
						arr.splice(i, 1);
						i -= 1; // i - 1 ,因为空元素在数组下标 2 位置，删除空之后，后面的元素要向前补位
					}
				}
				return arr;
			},

			assign_ip_list() {
				let str = JSON.parse(JSON.stringify(this.white_val));
				if (str) {
					let ary = str.split(/[\r\n]/);
					ary = this.removeEmpty(ary);
					if (!ary.length) {
						this.xwyLib.alert('没有解析出结果，请检查输入的内容');
						return false;
					}
					this.conf.ip_set.ip_city = ary;
				}
				this.close_white_modal();
			},

			assign_white_val_demo() {
				this.white_val = `北京市\n上海市\n广州市\n深圳市`;
			},

			wechat_group_tip() {
				let title = `1、开启功能后，未通过指定微信群进入考卷的，不能答题。
				2、如何指定微信群：管理员分享考卷到微信群，然后管理员需要先通过分享链接进入一次小程序，这样就算指定了一个微信群。
				3、微信群可以指定多个。`;
				this.xwyLib.alert(title);
			},

			skip_wechat_group() {
				let list = this.conf.wechat_group.list;
				uni.setStorageSync(`wechat_group_list-${this.params.exam_id}`, this.xwyLib.jsonEncode(list));
				let url = `/pages/exam/exam/exam_add/wechat_group?exam_id=${this.params.exam_id}`;
				this.xwyLib.routeJump(url);
			},


			exam_conf_set(opt = {}) {
				let conf = this.conf || {};

				switch (opt.title) {
					case 'limit_group_conf':
						conf.wechat_group.show = opt.value;
						break;
					case 'jump_screen':
						conf.anti_cheating.show = opt.value;
						break;
				}

				conf[opt.title] = 1;

				let obj = {
					exam_id: this.params.exam_id,
					conf: this.xwyLib.jsonEncode(conf),
					success: res => {
						this.xwyLib.showToast(`设置成功！`);
						switch (opt.title) {
							case 'limit_group_conf':
								this.$set(this.conf.wechat_group, 'show', opt.value);
								break;
							case 'jump_screen':
								this.$set(this.conf.anti_cheating, 'show', opt.value);
								break;
						}
						this.$set(this.conf, opt.title, 1);
					},
					fail: res => {
						if (res.info.includes('积分')) {
							this.open_service();
							return
						}
						this.xwyLib.alert(res.info);
					}
				}
				if (opt.title) obj[opt.title] = 1;
				this.xwyLib.exam_conf_set(obj);
			},


			select_share_set(index, item) {
				let rank_set = this.formData?.rank_set || {};
				if (!rank_set.closed_AD) {
					this.open_service()
					return
				}

				if (index >= 0) {
					this.$set(this.conf.share_set, 'show', item.value)
					return
				}
				this.$set(this.conf.share_set, 'show', 0)
			},

			open_service(){
				this.show_service = true
			},



        // 独立活动的设置, 如党建猜图，没有考卷详情，不需要设置大图模式
        isActivitySet() {
            this.is_activity = true

            // 不需要大图模式
            const big_image_set_idx = 2
            const index = this.tabs_list.findIndex(v => v.idx === big_image_set_idx)
            if (index !== -1) this.tabs_list.splice(index, 1)
        },
		},
	}
</script>

<style scoped lang='scss'>
@import url('@/pages/likou_dati/common/css/common.scss');
@import url('@/pages/likou_dati/common/css/iconfont.css');
@import url('@/pages/likou_dati/common/css/public.css');
	.content {
		width: 100%;
		min-height: 100vh;
		padding-bottom: 200px;

		.answer_num {
			width: 100%;
			border: 1px solid #e7e7e7;
		}

		.drawer-item {
			width: 100%;

			.box .uni-easyinput {
				padding: 1rem 0;
			}
		}

		.redpack-list {
			width: 100%;

			.left {
				width: calc(100% - 3rem);

				.score-item {
					width: 100%;

					.inp {
						width: calc((100% - 8rem) / 2);
						padding: .3rem;

						.input {
							text-align: center !important;
							width: 100% !important;
							border: none !important;
						}
					}

					.uni-easyinput {
						text-align: center;
					}
				}
			}

			.right {
				width: 3rem;
			}
		}
	}

	.color-box {
		width: 2rem;
		height: 2rem;
		border: .1rem solid #eee;
	}

	.gray-bdrt {
		border-top: 10px solid #f0f0f0
	}

	.shade-box {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, .5);
	}

	.textarea {
		min-height: 150px;
		height: 150px;
		background-color: #f9f9f9;
		border: 1px solid #eee;

		textarea {
			width: 100%;
			height: 100%;
		}
	}
</style>
