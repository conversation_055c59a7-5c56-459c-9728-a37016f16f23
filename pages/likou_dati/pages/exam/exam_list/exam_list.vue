<template>
    <view class="content">
        <!-- 顶部盒子 已连续签到天数和我的积分数量 -->
        <view class="top_box">
            <uni-easyinput class="search-container" v-model="search_keyword" placeholder="输入考卷名称搜索"
                           prefix-icon="search" :input-border="false"
                           confirm-type="search" @confirm="search"/>

            <view class="create-btn" @click.stop="skip_page(217)">创建考卷</view>
        </view>

        <!-- 我的考卷 -->
        <view class="exam-box">
            <view class="exam-list p_7" v-for="(item,index) in exam_list" :key="index">
                <view class="exam-top gray_bdrbdashed f" @click.stop="skip_modif_exam(item.exam_id)">
                    <view class="img f">
                        <image :src="item.logo?item.logo:default_img" mode="aspectFill"/>
                    </view>
                    <view class="text-box f-d-c-j-sb">
                        <view class="name">
                            <text>{{index+1}}、</text>
                            <text>{{item.exam_name}}</text>
                        </view>
                        <view class="memo gray_color">{{item.create_time}}</view>
                    </view>
                </view>
                <view class="exam-bottom pt_5 f-j-sb-a-c">
                    <view class="btn f memo_text" @click.stop="cancel_exam(item.exam_id)">
                        <i class="iconfont pr_3 f-j-a-c">&#xe625;</i>
                        <view class="text f-j-a-c">
                            删除
                        </view>
                    </view>

					<view class="btn f memo_text" @click.stop="toExamManage(item.exam_id)">
					    <i class="iconfont pr_3 f-j-a-c">&#xe62b;</i>
					    <view class="text f-j-a-c">
					        管理
					    </view>
					</view>

                    <view class="btn f memo_text" @click.stop="copyExam(item.exam_id)">
                        <i class="iconfont pr_3 f-j-a-c">&#xe65e;</i>
                        <view class="text f-j-a-c">
                            复制考卷
                        </view>
                    </view>


                    <view class="btn f memo_text" @click.stop="skip_modif_exam(item.exam_id,1)">
                        <i class="iconfont pr_3 f-j-a-c">&#xe633;</i>
                        <view class="text f-j-a-c">
                            编辑
                        </view>
                    </view>

					<view v-if="params.in_select" class="btn f memo_text" @click.stop="selExam(item.exam_id, item.exam_name)">
					    <view class="text f-j-a-c color-primary">
					        选择此考卷
					    </view>
					</view>
                </view>

            </view>
        </view>
        <view v-if="err_text" class="err_text">{{err_text}}</view>

        <!-- 悬浮框 -->
        <view class="float-box">
            <view class="float-item" @click.stop="skip_page(207)">
                <i class="iconfont">&#xe7a2;</i>
                <text>我的题库</text>
            </view>
        </view>

        <emptyData v-if="show_empty"></emptyData>
    </view>
</template>

<script>
	import emptyData from "@/pages/likou_dati/components/emptyData/emptyData.vue"

    let app = getApp();
    export default {
        components: {
            emptyData
        },
        data() {
            return {
                params: {},
                avator_img: this.imgImport.not_headimg,
                default_img: this.imgImport.not_defaultimg,

                exam_length: 0,

                show_question_classify: false,

                exam_list: [],
                page: 1,
                is_lastpage: false,
                show_empty: false,
                err_text: '',
                is_load: false,

                search_keyword: ''
            }
        },
        onLoad(options) {
            // console.log('获取的参数options', options);
            this.xwyLib.showLoading();
            this.xwyLib.init(() => {
                this.params = options;
                this.init();
            })
        },
        onShow() {
            if (app.globalData.update_exam == 1) {
                app.globalData.update_exam = null;
                this.get_exam_papers_list();
            }
        },
        methods: {
            async init() {
                this.get_exam_papers_list();
            },

            search() {
                this.get_exam_papers_list(1)
            },

            get_exam_papers_list(page = 1) {
                let obj = {
                    page,
                    perpage: 20,
					my_self: 1,
                    before: res => {
                        this.err_text = '';
                        if (page == 1) {
                            this.is_lastpage = false;
                            this.exam_list = [];
                            this.show_empty = false;
                        }
                    },
                    success: res => {
                        let list = res.data;
                        if (page > 1) {
                            list = [...this.exam_list, ...res.data];
                            if (res.data.length < 20) this.err_text = '没有更多了，别再拉了！';
                        }

                        this.exam_list = list;
                        this.page = page + 1;
                        this.is_lastpage = res.is_lastpage;
                    },
                    fail: res => {
                        if (page == 1) {
                            this.show_empty = true;
                            return
                        }
                        this.err_text = res.info;
                    }
                };

                if (this.search_keyword) obj.keywords = this.search_keyword

                this.xwyLib.get_exam_papers_list(obj);
            },

            // 删除考卷
            cancel_exam(id) {
                this.xwyLib.showModal('是否删除该考卷?', {
                    confirmText: '删除',
                    success: res => {
                        if (res.confirm) this.cancel_exam_post(id);
                    }
                })
            },

			toExamManage(id) {
				uni.navigateTo({
					url: '../exam_details/exam_details?is_manage=1&exam_id=' + id
				})
			},

            copyExam(exam_id) {
                this.xwyLib.showLoading();
                this.xwyLib.ajax({
                    url: 'front.flat.exam.examAdmin/copy_exam',
                    data: { exam_id },
                    success: res => {
                        uni.hideLoading()
                        if (res?.status !== 1) return this.xwyLib.alert(res?.info || '考卷复制失败')
                        uni.showToast({
                            title: res.info || '考卷复制成功',
                            icon: !res.info || res.info.length <= 7 ? 'success' : 'none'
                        })
                        setTimeout(() => {
                            this.get_exam_papers_list()
                        }, 1000)
                    },
                    fail: () => uni.hideLoading()
                })
            },

            async cancel_exam_post(id) {
                this.xwyLib.showLoading();
                let data = {
                    ids: id,
                };
                let obj = {
                    data,
                    url: `front.flat.exam.examPapers/remove_exam_papers`,
                    success: res => {
                        uni.hideLoading();
                        console.log('删除考卷', res);
                        if (res.status != 1) {
                            this.xwyLib.alert(res.info);
                            return
                        }
                        this.xwyLib.showModal(res.info, {
                            success: response => {
                                this.get_exam_papers_list();
                            }
                        })
                    }
                };
                this.xwyLib.ajax(obj);
            },


            // 跳转到修改考卷
            skip_modif_exam(id, e) {
                let url = `/pages/likou_dati/pages/exam/exam_details/exam_details?exam_id=${id}`;

                if (e == 1) {
                    url = `/pages/likou_dati/pages/exam/exam_add/exam_add?exam_id=${id}`;
                }
                this.xwyLib.routeJump(url);
            },

			selExam(id, title) {
				const eventChannel = this.getOpenerEventChannel()
				eventChannel && eventChannel.emit('updateExam', {id, title})
				uni.navigateBack()
			},

            // 跳转页面
            skip_page(e) {
                this.xwyLib.skip_page(e);
            },


            open_question_classify() {
                this.show_question_classify = true;
            },

            close_question_classify() {
                this.show_question_classify = false;
            },

        },

        onReachBottom() {
            if (this.is_lastpage) return
            this.get_exam_papers_list(this.page);
        },

    }
</script>

<style scoped lang="scss">
	@import url('@/pages/likou_dati/common/css/common.scss');
	@import url('@/pages/likou_dati/common/css/iconfont.css');
	@import url('@/pages/likou_dati/common/css/public.css');

    .content {
        position: relative;
        background-color: #f8f8f8;
        padding-bottom: 70px;
        min-height: 100vh;

        .top_box {
            background-color: #fff;
            display: flex;
            padding: 10px;

            .search-container {
                border-radius: 18px;
                background-color: #f8f8f8;
                width: calc(100% - 110px);
            }

            .create-btn {
                margin-left: 10px;
                width: 100px;
                line-height: 36px;
                border-radius: 18px;
                text-align: center;
                background-color: #5cadff;
                color: #fff;
            }
        }

        .exam-box {
            width: 100%;
            padding: 1rem;

            .exam-list {
                width: 100%;
                background-color: #fff;
                border-radius: .2rem;
                margin-bottom: 1rem;

                .exam-top {
                    width: 100%;
                    padding-bottom: .5rem;

                    .img {
                        width: 8rem;

                        .iconfont {
                            font-size: 4rem;
                        }

                        image {
                            width: 100%;
                            height: calc(8rem / 3 * 2);
                            border-radius: .2rem;
                        }
                    }

                    .text-box {
                        width: calc(100% - 8rem);
                        padding-left: 1rem;

                        .name {
                            padding-bottom: .5rem;
                        }

                        .memo {
                            font-size: .9rem;
                        }
                    }
                }

                .exam-bottom {
                    width: 100%;

                    .btn {
                        border-radius: 10rem;
                        padding: .2rem 0;
                    }
                }
            }
        }


        .fixed_modal {

            .fixed-item {
                width: 100%;
                margin-bottom: 1rem;
            }

            .input {
                padding: .5rem 0;
                border-radius: .2rem;

                input {
                    width: 100%;
                }
            }

            .btn-box {
                width: 100%;

                .btn {
                    width: 40%;
                    border-radius: .3rem;
                    text-align: center;
                    padding: .5rem 0;
                }
            }
        }
    }
</style>
