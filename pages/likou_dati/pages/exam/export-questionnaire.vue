<template>
    <view>
        <template v-if="total_page">
            <view class="font14 p10" style="color: #e19898;">
                共{{ count }}条记录，需要分{{ total_page }}次导出，每次导出{{ perpage }}条数据。
            </view>

            <view class="p10 font14 color-content">
                <text>每次导出</text>
                <text class="plr5">{{ perpage }}</text>
                <text>条数据</text>
                <text class="pl10 color-primary" @click="uniPopupOpen('perpage')">修改</text>
            </view>
        </template>


        <view class="pb10">
            <view class="flex-kai" v-for="(item, index) in total_page" :key="item">
                <view class="color-title p10">
                    <text class="color-sub">{{ index + 1 }}、</text>
                    <text>{{ index * perpage + 1 }}</text>
                    <text class="plr5">-</text>
                    <text>
                        <template v-if="index + 1 === total_page">{{ count }}</template>
                        <template v-else>{{ (index + 1) * perpage }}</template>
                    </text>
                </view>

                <view class="color-primary p10" @click="exportData(index + 1)">导出</view>
            </view>
        </view>


        <uni-popup ref="perpage" type="dialog" mode="input" :is-mask-click="false">
            <uni-popup-dialog
                mode="input"
                title="每次导出数据量"
                :value="perpage"
                :placeholder="'请输入 100 - ' + max_count + ' 内的数字'"
                @confirm="perpageInputConfirm"
            ></uni-popup-dialog>
        </uni-popup>
    </view>
</template>

<script>
import openExcelFile from '@/utils/open-excel-file'

export default {
    data() {
        return {
            count: 0,
            total_page: 0,
            perpage: 1000,
            max_count: 1000,
            src_list: [],
            download_src: ''
        }
    },

    onLoad(params) {
        this.exam_id = params.exam_id
        this.init()
    },

    methods: {
        async init() {
            this.$uni.showLoading()
            await this.getExamQuestionList()
            await this.getOne()
            uni.hideLoading()
        },

        async getExamQuestionList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.exam.admin/exam_question_list_fixed',
                data: {
                    exam_id: this.exam_id,
                    page: 1,
                    perpage: 100
                }
            })

            this.questionList = res?.data?.question_list?.data || []
        },

        async getOne() {
            const list = await this.getRecordList(1)
            uni.hideLoading()

            if (!list?.length) return this.$uni.showModal('暂无记录', {success: () => uni.navigateBack()})

            if (this.count <= this.perpage) {
                const tableData = this.excelDataProcessing(list, 1)
                console.log(tableData);
                openExcelFile.openDocument(tableData, '问卷调查记录')
                // return this.$uni.navigateBack()
            }

            this.setTotalPage()
        },

        async exportData(page) {
            this.$uni.showLoading('导出中...')
            const list = await this.getRecordList(page)
            const tableData = this.excelDataProcessing(list, page)
            openExcelFile.openDocument(tableData, `问卷调查 - 第${page}页`)
            uni.hideLoading()
        },

        perpageInputConfirm(val) {
            const perpage = Number(val)
            if (isNaN(perpage) || perpage < 100) {
                return this.$uni.showModal(`输入错误，请输入 100 - ${this.max_count} 内的数字`, {
                    success: () => this.uniPopupOpen('perpage')
                })
            }
            this.perpage = perpage
            this.setTotalPage()
        },

        setTotalPage() {
            this.total_page = Math.ceil(this.count / this.perpage)
            this.src_list = []
        },

        excelDataProcessing(list, page) {
            const tHead = ['序号', '系统标识', '姓名', '提交时间']

            this.questionList.forEach((item, index) => {
                tHead.push(`第${index + 1}题、${item.question_details?.title || ''}`)
            })

            const tBody = []

            list.forEach((item, index) => {
                const data = [
                    (page - 1) * this.perpage + index + 1,               // 序号
                    `w.${item.shopid}u.${item.userid || 0}`, // 商户号+会员号
                    item.user_details?.must_submit?.[0]?.value || '',
                    item.create_time || ''
                ]

                item.answer_list ||= []

                this.questionList.forEach(q => {
                    const answer = item.answer_list.find(a => a.question_id === q.question_id)

                    const options = q.question_details?.answer_option || []
                    const user_answer = answer?.user_answer || []

                    let answerData = ''

                    // 填空题，直接返回的是填写的答案
                    if (q.question_details?.question_types === 4) {
                        answerData = user_answer.join('、\n')
                    } else {
                        answerData = user_answer.map(u => options[u]?.text || '').join('、\n')
                    }

                    data.push(answerData || '')
                })

                tBody.push(data)
            })


            return [tHead, ...tBody]
        },

        async getRecordList(page = 1) {
            const res = await this.xwy_api.request({
                url: 'front.flat.exam.export/user_questions_details',
                data: {
                    exam_id: this.exam_id,
                    page,
                    perpage: this.perpage,

                    // 如果传了活动id，则输出当前用户在这个活动里填写的报名信息。如果传 get_exam_user_details  【1】则获取在考卷里填写的实名信息  如果只要考卷里填写的实名信息，active_id也必须要传，但不用对应哪个活动ID，随便写点都行
                    get_exam_user_details: 1,
                    active_id: '1111'
                }
            })

            if (page === 1) this.count = res?.data?.['user_answer_list']?.total || 0

            return res?.data?.['user_answer_list']?.data || []
        },

        uniPopupOpen(ref) {
            this.$refs[ref].open()
        },

        uniPopupClose(ref) {
            this.$refs[ref].close()
        },
    }
}
</script>

<style lang="scss">
.uni_popup {
    width: 280px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}

.uni_popup-btn {
    line-height: 40px;
    border-radius: 20px;
}

.uni_popup-btn::after {
    border: none;
}
</style>