<template>
    <view class='content'>
        <!-- 题目列表 -->
        <view class="question-box">
            <view class="question-title question-item gray_bdrbsolid f-j-sb-a-c">
                <view class="">
                    题目列表
                </view>
                <view class="f-j-a-c">
                    <!-- <view>
                        共 {{all_question_score}} 分
                    </view> -->
                    <view v-if="total">
                        共 {{total}} 题
                    </view>
                </view>
                <view class="blue_color f-j-e-a-c" @click.stop="open_add_modal">
                    <i class="iconfont pr_2">&#xe643;</i>
                    <text>添加</text>
                </view>
            </view>
            <!-- 已导入的题目 -->
            <view class="question-item gray_bdrbdashed" style="padding-bottom: 0" v-for="(item,index) in question_list"
                :key="index">
                <view class="question-num f-j-sb-a-c pb1" :class="item.show_list?'gray_bdrbdashed':''"
                    @click.stop="change_show_list(item,index)">
                    <view class="f-a-c" @click.stop="change_row_select(index)">
                        <view class="is_select f-j-a-c" :class="item.is_rowselect?'blue_bdrsolid':'gray_bdrsolid'"
                            style="margin-right: .5rem;">
                            <i v-if="item.is_rowselect" class="iconfont blue_color">&#xe6ad;</i>
                        </view>
                        <text>{{20 * index + 1}} ~ {{20 * index + item.all_num}} 题</text>
                    </view>
                    <view class="">
                        <i class="iconfont" v-html="item.show_list?'&#xe6a6;':'&#xe6a7;'"></i>
                    </view>
                </view>
                <view v-if="item.show_list" class="question-list-box">
                    <view class="question-list pt1" :class="index == item.list.length - 1?'':'gray_bdrbdashed'"
                        v-for="(itm,idx) in item.list">
                        <!-- 标题 -->
                        <view class="title f-j-sb mb_5">
                            <view class="left f-a-c">
                                <view class="is_select f-j-a-c" :class="itm.is_select?'blue_bdrsolid':'gray_bdrsolid'"
                                    style="margin-right: .5rem;" @click.stop="change_item_select(index,idx)">
                                    <i v-if="itm.is_select" class="iconfont blue_color">&#xe6ad;</i>
                                </view>
                                <text class="gray_color">
                                    {{(20 * index) + idx+1}}、[{{itm.question_details.question_types_title}}]
                                </text>
                                <text>
                                    {{itm.question_details.title}}
                                </text>
                                <text v-if="itm.question_details.pic_list && itm.question_details.pic_list.length"
                                    class="blue_color" style="padding: 0 .5rem;"
                                    @click.stop="previewImage(itm.question_details)">[图片]</text>
                            </view>
                            <view class="right">
                                <view class="blue_bdrdashed f-j-a-c" @click.stop="open_modal(itm)">
                                    <text
                                        class="blue_color">{{itm.score > 0 ? itm.score : itm.question_details.score}}</text>
                                    <text class="memo_text">分</text>
                                </view>
                            </view>
                        </view>
                        <!-- 答案选项 -->
                        <view class="answer-option-box">
                            <view v-if="itm.question_details.question_types < 4" class="answer-option-list f-a-c"
                                v-for="(it,ix) in itm.question_details.answer_option">
                                <view class="is_select f-j-a-c" :class="it.is_right?'gray_bdrsolid':'gray_bdrsolid'">
                                    <i v-if="it.is_right" class="iconfont gray_color">&#xe6ad;</i>
                                </view>
                                <view class="right">
                                    {{it.text}}
                                </view>
                            </view>

                            <view v-if="itm.question_details.question_types == 4" class="f blank_list"
                                v-for="(it,ix) in itm.question_details.answer_option">
                                <view class="blank_left">
                                    空格{{ix+1}}：
                                </view>
                                <view class="blank_right">
                                    <view class="" v-for="(i,x) in it.is_right">
                                        <text class="blue_color">{{i.text}}</text>
                                        <text v-if="x != it.is_right.length - 1" style="padding: 0 .5rem;">或</text>
                                    </view>
                                </view>
                            </view>
                        </view>
                        <!-- 题目操作栏 -->
                        <view class="operation-box f-j-e-a-c p5_0">
                            <view class="operation-item memo_text p5_0 f-j-e-a-c mr2"
                                @click.stop="cancel_exam_question(index,idx)">
                                <i class="iconfont pr_2">&#xe625;</i>
                                删除
                            </view>
                            <!-- 修改题目后需要 -->
                            <view class="operation-item memo_text p5_0 f-j-e-a-c" @click.stop="open_modal(itm)">
                                <i class="iconfont pr_2">&#xe633;</i>
                                编辑
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <!-- 添加题目 -->
            <view class="question-item gray_bdrbsolid f-j-sb f-w pb0">
                <view class="top blue_color gray_bdrsolid f-j-a-c" v-for="(item,index) in add_method_list" :key="index"
                    @click.stop="add_method(item)">
                    <i class="iconfont" v-html="item.icon"></i>
                    {{item.title}}
                </view>
            </view>

            <view v-if="question_list.length && !is_lastpage" class="p1 f-j-a-c">
                <span class="gray_color radius_2 p_5 font_9 blk_shadow_01"
                    @click.stop="load_next_page_question">加载下一页</span>
            </view>
        </view>

        <!-- 表单弹框 -->
        <view v-if="show_modal" class="fixed_modal f-d-c-j-a-c">
            <view class="fixed_modal_content p0-1">
                <!-- 操作弹框 -->
                <view v-if="modal_index == 0" class="fixed-item">
                    <view class="item_list f-a-c" :class="index < add_method_list.length - 1?'gray_bdrbdashed':''"
                        v-for="(item,index) in add_method_list" :key='index' @click.stop="add_method(item)">
                        <view class="left">
                            <i class="iconfont blue_color" v-html="item.icon"></i>
                        </view>
                        <view class="right">
                            <view class="name">
                                {{item.title}}
                            </view>
                            <view class="memo_text">
                                {{item.memo}}
                            </view>
                        </view>
                    </view>
                </view>
                <!-- 修改科目 -->
                <view v-if="modal_index == 1" class="fixed-item p1-0">
                    <view class="modif-item f-a-c pb1">
                        <view class="left">
                            题目分数
                        </view>
                        <view class="right">
                            <uni-easyinput type="digit" v-model="modif_obj.score" placeholder="请输入题目分数" />
                        </view>
                    </view>
                    <view class="modif-item f-a-c pb1">
                        <view class="left">
                            题目排序
                        </view>
                        <view class="right">
                            <uni-easyinput type="number" v-model="modif_obj.sort_num" placeholder="排序，数字越小排在越前" />
                        </view>
                    </view>
                    <view class="btn-box f-j-sb-a-c">
                        <view class="gray_bdrsolid font_9 p5_0 text-center gray_color radius_3" style="width: 45%;"
                            @click.stop="close_modal">
                            取消
                        </view>
                        <view class="blue_bgcolor font_9 p5_0 text-center radius_3" style="width: 45%;"
                            @click.stop="edit_question_from_exam">
                            修改
                        </view>
                    </view>
                </view>
            </view>
            <i class="iconfont" @click.stop="close_modal">&#xe618;</i>
        </view>


        <!-- 底部操作栏 -->
        <view class="tabbar-box f">
            <view class="left-box f-a-c">
                <view class="select-box">
                    <view class="select-item f-j-a-c" :class="is_allselect?'blue_bdrsolid':''"
                        @click.stop="change_allselect">
                        <i v-if="is_allselect" class="iconfont blue_color">&#xe6ad;</i>
                    </view>
                </view>
                <view class="select-text">
                    全选
                </view>
            </view>
            <view class="right-btn">
                <view class="btn-box f-j-sb-a-c p_5">
                    <view class="btn gray_color font_9 gray_bdrsolid f-j-a-c radius_3"
                        @click.stop="cancel_all_question(1)">
                        删除
                    </view>
                    <view class="btn blue_bgcolor font_9 f-j-a-c radius_3" @click.stop="cancel_all_question(2)">
                        编辑
                    </view>
                </view>
            </view>
        </view>

        <emptyData v-if="show_empty"></emptyData>

        
    </view>
</template>

<script>
	import {
		setClipboardData,
		getClipboardData
	} from '@/pages/likou_dati/uni_modules/u-clipboard/js_sdk'
	let app = getApp();

	import emptyData from "@/pages/likou_dati/components/emptyData/emptyData.vue"

	export default {
		components: {
			emptyData
		},
        data() {
            return {
                params: {},
                total: 0,
                all_question_score: 0,

                add_method_list: [{
                        title: '新建题目',
                        icon: '&#xe95d;',
                        memo: '手动添加题目 一题一题的添加',
                        clickFun: 'skip_add_question',
                    },
                    {
                        title: '我的题库',
                        icon: '&#xe607;',
                        memo: '从我的题库中选题',
                        clickFun: 'skip_question_bank_list',
                    },
                    {
                        title: '公共题库',
                        icon: '&#xe7a2;',
                        memo: '从公共题库选题',
                        clickFun: 'skip_question_bank_list(1)',
                    },
                    // {
                    //     title: '导出题目',
                    //     icon: '&#xe7ba;',
                    //     memo: 'Word导出题目',
                    //     clickFun: 'export_question_to_word',
                    // }
                ],

                question_list: [],
                is_load: false,
                is_lastpage: false,
                err_text: '',
                show_empty: false,

                show_add_modal: false, // 是否显示添加弹框
                show_modal: false, // 是否显示弹框
                modif_obj: {},
                modal_index: 0,
                is_allselect: false, // 是否全选

                edit_multi: false,  // 单个修改还是多个修改
            }
        },
        onLoad(options) {
            // console.log('获取的参数options', options);
            this.params = options;
            this.xwyLib.init(() => {
                this.xwyLib.setBarTitle('题目管理')
                this.init();
            })
        },
        onShow() {
            if (app.globalData.updateExamQuestion == 1) {
                app.globalData.updateExamQuestion = null;
                app.globalData.update = 1;
                this.get_question_in_exam()
            }
        },
        methods: {
            async init() {
                if (this.params.exam_id) this.get_question_in_exam();
            },

            // 获取考卷中的题目
            get_question_in_exam(page = 1) {
                if (this.is_load) return;
                this.is_load = true;
                this.xwyLib.showLoading();
                this.err_text = '';
                if (page == 1) {
                    this.is_lastpage = false;
                    this.question_list = [];
                    this.show_empty = false;
                    this.total = '';
                }

                let data = {
                    exam_id: this.params.exam_id,
                    page,
                    perpage: 20,
                };
                let obj = {
                    data,
                    url: `front.flat.exam.admin/exam_question_list_fixed`,
                    success: res => {
                        this.is_load = false;
                        uni.hideLoading();
                        console.log('获取考卷中的题目', res);
                        if (res.status != 1 || !res.data || !res.data.question_list || !res.data.question_list
                            .data || !res.data
                            .question_list.data.length) {
                            if (page == 1) {
                                this.show_empty = true;
                                this.question_list = [];
                                this.all_question_list = 0;
                                this.all_question_score = 0;
                                return
                            }
                            this.err_text = res.info;
                            return
                        }

                        let list = [...res.data.question_list.data];

                        let all_obj = {
                            all_num: 0,
                            list: [],
                            show_list: true,
                            all_score: 0,
                            is_rowselect: false,
                        };

                        list.forEach((val, idx) => {
                            val.question_details.question_types_title = this.xwyLib
                                .get_question_types_text(val
                                    .question_details.question_types);
                            all_obj.all_score += Number(val.score);
                            val.is_select = false;
                            all_obj.all_num++;
                        })
                        all_obj.list = list;

                        this.question_list = [...this.question_list, all_obj];
                        this.page = page + 1;
                        this.is_lastpage = res.data.question_list.is_lastpage;
                        this.total = res.data.question_list.total;
                        console.log('question_list', this.question_list);
                    }
                };
                this.xwyLib.ajax(obj);

            },

            // 跳转到题库列表
            skip_question_bank_list(e) {
                let url =
                    `/pages/likou_dati/pages/question/question_bank_list/question_bank_list?exam_id=${this.params.exam_id}&addQuestion=1`;
                if (e == 1) url += `&public=1`
                this.xwyLib.routeJump(url);
            },

            // 跳转到新建题目
            skip_add_question() {
                let url = `/pages/likou_dati/pages/question/add_question/add_question?exam_id=${this.params.exam_id}`;
                this.xwyLib.routeJump(url);
            },

            // 删除考卷中的题目
            cancel_exam_question(index, idx) {
                uni.showModal({
                    content: `是否删除该题目？`,
                    confirmText: '删除',
                    success: res => {
                        if (res.confirm) {
                            let id = this.question_list[index].list[idx].id;
                            this.cancel_exam_question_post(id);
                        }
                    }
                })
            },

            async cancel_exam_question_post(ids = '') {
                this.xwyLib.cancel_question_from_exam({
                    exam_id: this.params.exam_id,
                    ids,
                    success: res => {
                        this.xwyLib.alert(`删除成功！`);
                        this.get_question_in_exam();
                        // app.globalData.update = 1;
                    }
                })
            },


            // 删除所选题目
            cancel_all_question(postindex) {
                if (!this.xwyLib.isArray(this.question_list)) {
                    this.xwyLib.alert(`请选择题目！`);
                    return
                }

                let list = this.question_list,
                    question_list = [];

                list.forEach((val, idx) => {
                    if (this.xwyLib.isArray(val.list)) {
                        val.list.forEach(v => {
                            if (v.is_select) question_list.push(v)
                        })
                    }
                })
                if (!question_list || !question_list.length) {
                    this.xwyLib.alert(`请选择题目！`);
                    return
                }

                if (postindex == 1) {
                    uni.showModal({
                        content: '是否删除所选题目？',
                        confirmText: '删除',
                        success: res => {
                            if (res.confirm) {
                                let ids = '';
                                question_list.forEach((val, idx) => {
                                    if (idx == 0) ids = `${val.id}`
                                    if (idx > 0) ids += `,${val.id}`
                                })
                                this.cancel_exam_question_post(ids);
                            }
                        }
                    })
                }

                if (postindex == 2) {
                    this.show_modal = true;
                    this.modal_index = 1;
                    this.edit_multi = true;
                }
            },

            // 打开弹框
            open_modal(e) {
                if (e) {
                    this.modif_obj = {
                        ...e
                    }
                }
                this.show_modal = true;
                this.modal_index = 1;
                this.edit_multi = false;
            },

            // 关闭弹框
            close_modal() {
                this.show_modal = false;
                this.modif_obj = {};
            },

            edit_question_from_exam() {
                this.xwyLib.showLoading('正在修改中...');
                let list = [];
                // 单个修改
                if (!this.edit_multi) {
                    list = [{
                        id: this.modif_obj.id,
                        score: parseFloat(this.modif_obj.score),
                        sort_num: parseInt(this.modif_obj.sort_num),
                    }]
                } else {
                    let question_list = this.question_list;
                    question_list.forEach(val => {
                        if (this.xwyLib.isArray(val.list)) {
                            val.list.forEach(v => {
                                if (v.is_select) {
                                    list.push({
                                        id: v.id,
                                        score: parseFloat(this.modif_obj.score),
                                        sort_num: parseInt(this.modif_obj.sort_num),
                                    })
                                }
                            })
                        }
                    })
                }

                let obj = {
                    data: {
                        exam_id: this.params.exam_id,
                        question_list: this.xwyLib.jsonEncode(list),
                    },
                    url: `front.flat.exam.admin/batch_change_exam_question_list`,
                    success: res => {
                        uni.hideLoading();
                        if (res.status != 1) {
                            this.xwyLib.alert(res.info)
                            return
                        }
                        this.xwyLib.alert(`修改成功！`)
                        this.get_question_in_exam();
                        this.close_modal();
                    }
                };
                this.xwyLib.ajax(obj);
            },

            previewImage(item) {
                let list = [];
                if (item.pic_list && item.pic_list.length) {
                    item.pic_list.forEach(val => {
                        list.push(val.url);
                    })
                }
                uni.previewImage({
                    current: 0,
                    urls: list,
                })
            },

            export_question_to_word() {
                let close_cost = '';
                if (this.all_question_list < 100) {
                    close_cost = 1;
                }
                this.xwyLib.judge_integral_cost_set(() => {
                    this.export_question_to_word_post();
                }, 5, close_cost)
            },

            async export_question_to_word_post() {
                if (this.is_load) {
                    return
                }
                this.xwyLib.showLoading('导出中...');
                this.is_load = true
                let params = {
                    exam_id: this.params.exam_id,
                };
                let obj = {
                    data: {
                        action: 'front/exam/admin/exam_question_to_word',
                        params,
                    },
                };
                let res = await this.xwyLib.cloudAjax(obj).catch(err => {
                    this.xwyLib.alert(JSON.stringify(err));
                });
                uni.hideLoading();
                this.is_load = false;
                if (res.status != 1) {
                    this.xwyLib.alert(res.info);
                    return
                }

                uni.showModal({
                    content: res.info,
                    confirmText: '复制',
                    showCancel: false,
                    success: response => {
                        this.copy_url(res.data.result.url);
                    },
                })
            },

            // 复制考卷ID
            async copy_url(url) {
                let res = await setClipboardData(url).catch(err => {
                    this.xwyLib.alert('复制失败！')
                });
                if (res) {
                    this.xwyLib.showToast(`复制成功!`)
                }
            },


            change_show_list(item, index) {
                let question_list = this.question_list;
                this.$set(question_list[index], 'show_list', !item.show_list)
            },

            open_add_modal() {
                this.modal_index = 0;
                this.show_modal = true;
            },

            // 切换一题的选中
            change_item_select(index, idx) {
                let question_obj = this.question_list[index],
                    item = question_obj.list[idx];
                item.is_select = !item.is_select;
                this.$set(this.question_list[index].list, idx, item);

                this.$nextTick(function() {
                    this.is_allselect = this.judge_question_select();
                    this.$set(this.question_list[index], 'is_rowselect', this.judge_row_select(index))
                })
            },

            // 切换一行的选中
            change_row_select(index) {
                let question_list = this.question_list,
                    obj = question_list[index];
                obj.is_rowselect = !obj.is_rowselect;
                if (Array.isArray(obj.list)) {
                    obj.list.forEach(val => {
                        val.is_select = obj.is_rowselect;
                    })
                }
                this.$set(this.question_list, index, obj);
                this.$nextTick(function() {
                    this.is_allselect = this.judge_question_select();
                })
            },

            // 切换所有的选中
            change_allselect() {
                this.is_allselect = !this.is_allselect;
                let question_list = this.question_list;
                if (Array.isArray(question_list)) {
                    question_list.forEach(val => {
                        val.is_rowselect = this.is_allselect
                        if (Array.isArray(val.list)) {
                            val.list.forEach(v => {
                                v.is_select = this.is_allselect
                            })
                        }
                    })
                }
                this.question_list = [...question_list]
            },


            // 判断是否所有的题目都选中了
            judge_question_select() {
                let question_list = this.question_list,
                    is_allselect = true;
                if (Array.isArray(question_list)) {
                    for (let i = 0; i < question_list.length; i++) {
                        if (Array.isArray(question_list[i].list)) {
                            for (let j = 0; j < question_list[i].list.length; j++) {
                                if (question_list[i].list[j].is_select == false) {
                                    is_allselect = false;
                                    break;
                                }
                            }
                        }
                    }
                }
                return is_allselect
            },

            // 判断一行所有的题目都选中了
            judge_row_select(index) {
                let list = this.question_list[index].list,
                    is_rowselect = true;
                for (let i = 0; i < list.length; i++) {
                    if (list[i].is_select == false) {
                        is_rowselect = false;
                        break
                    }
                }
                return is_rowselect
            },


            add_method(item) {
                switch (item.clickFun) {
                    case 'skip_add_question':
                        this.skip_add_question();
                        break;
                    case 'skip_question_bank_list':
                        this.skip_question_bank_list();
                        break;
                    case 'skip_question_bank_list(1)':
                        this.skip_question_bank_list(1);
                        break;
                    case 'export_question_to_word':
                        this.export_question_to_word();
                        break;
                }
            },

            // 加载下一页题目
            load_next_page_question() {
                if (this.is_lastpage) return;
                this.get_question_in_exam(this.page);
            },

        },

        onReachBottom() {
            if (this.is_lastpage) return;
            this.get_question_in_exam(this.page);
        }

    }
</script>

<style scoped lang='scss'>
	@import url('@/pages/likou_dati/common/css/common.scss');
	@import url('@/pages/likou_dati/common/css/iconfont.css');
	@import url('@/pages/likou_dati/common/css/public.css');

    .content {
        width: 100%;
        padding-bottom: 70px;

        .question-box {
            width: 100%;

            .question-title {
                position: sticky;
                left: 0;
                top: 0;
                background-color: #fff;

                >view {

                    &:first-child,
                    &:last-child {
                        width: 4rem;
                    }

                    &:nth-child(2) {
                        width: calc(100% - 8rem);
                        text-align: center;

                        >view {
                            padding: 0 .5rem;
                        }
                    }

                    &:last-child {
                        text-align: right;
                    }
                }
            }

            .question-item {
                padding: 1rem;
                font-size: .9rem;

                >.top {
                    width: 45%;
                    padding: .6rem 0;
                    border-radius: 100rpx;
                    margin-bottom: 1rem;

                    .iconfont {
                        padding-right: .3rem;
                    }
                }

                .question-list-box {
                    .title {

                        text:nth-child(2) {
                            padding-right: .3rem;
                        }

                        .left {
                            max-width: calc(100% - 4.5rem);
                            flex-wrap: wrap;
                            word-wrap: break-word;
                        }

                        .right {
                            width: 4rem;

                            >view {
                                width: 100%;
                                border-radius: 10rem;
                                padding: .2rem 0;

                                >text:first-child {
                                    padding-right: .1rem;
                                }
                            }
                        }
                    }

                    .answer-option-box {

                        .answer-option-list {
                            flex-wrap: wrap;
                            padding: .3rem 0;

                            >.right {
                                font-size: .9rem;
                            }
                        }

                        .blank_list {
                            width: 100%;
                            padding-bottom: .3rem;

                            .blank_left {
                                width: 4rem;
                            }

                            .blank_right {
                                width: calc(100% - 4rem);

                                >view {
                                    display: inline-block;
                                }
                            }
                        }
                    }

                }
            }
        }

        .fixed_modal {

            .item_list {
                width: 100%;
                padding: 1rem 0;

                .left {
                    width: 3rem;
                    padding-right: .5rem;

                    .iconfont {
                        font-size: 2.2rem;
                    }
                }

                .right {
                    width: calc(100% - 3rem);
                    text-align: start;
                    font-size: 1rem;
                }
            }

            .modif-item {
                width: 100%;

                .left {
                    width: 5rem;
                }

                .right {
                    width: calc(100% - 5rem);

                    .iconfont {
                        font-size: 3rem;
                    }

                    image {
                        width: 4rem;
                        height: 4rem;
                        border-radius: .2rem;
                    }
                }
            }

            .btn-box {
                width: 100%;

                .btn {
                    width: 40%;
                    border-radius: 100rpx;
                    text-align: center;
                    padding: .5rem 0;
                }
            }

            >.iconfont {
                padding-top: .5rem;
                font-size: 1.5rem;
                color: #fff;
            }
        }

        .tabbar-box {

            .left-box {
                width: 50%;
                padding: 1rem;

                .iconfont {
                    font-size: 1.5rem;
                }
            }

            .right-btn {
                width: 50%;

                .btn-box {
                    width: 100%;
                    height: 100%;

                    .btn {
                        width: calc((100% - 1rem) / 2);
                        height: 0;
                        padding: 1.1rem .5rem;
                        font-size: 1rem;
                    }
                }
            }
        }


        .is_select {
            width: 1.2rem;
            height: 1.2rem;
            margin-right: .3rem;

            .iconfont {
                font-size: 1.2rem;
            }
        }

    }
</style>
