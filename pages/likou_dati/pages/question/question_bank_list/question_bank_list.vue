<template>
    <view class='content'>
        <view class="top-select p0-1">
            <liuyuno-tabs v-if="category_list && category_list.length" :tabData="category_list" @tabClick='change_tabs'
                :activeIndex="category_idx" />
            <liuyuno-tabs v-if="question_types_list && question_types_list.length" :tabData="question_types_list"
                :activeIndex="question_types_idx" @tabClick='change_question_types' />
            <view class="radius10 p_5">
                <uni-easyinput prefixIcon="search" trim v-model="keywords" placeholder="输入关键字"
                    @confirm="search_keywords" />
            </view>
        </view>

        <view class="question-box p0-1">
            <view class="question-list f gray_bdrbdashed pt1" v-for="(item,index) in question_list" :key="index">
                <!-- v-if="params.addQuestion && params.addQuestion == 1" -->
                <view v-if="!params.public || (params.public && params.addQuestion)" class="select-box">
                    <view class="select-item f-j-a-c" :class="item.is_select?'blue_bdrsolid':''"
                        @click.stop="change_question_select(index)">
                        <i v-if="item.is_select" class="iconfont blue_color font1_5">&#xe6ad;</i>
                    </view>
                </view>
                <view class="question-item">
                    <view class="item-title f-j-sb-a-c pb_5" @click.stop="change_answer_options_show(index)">
                        <view class="left">
                            <text class="gray_color">{{index+1}}.</text>
                            <text class="gray_color">[{{item.question_types_title}}]
                            </text>
                            <i class="iconfont gray_color pr_5" style="display: inline-block;"
                                @click.stop="skip_modif_question(item.id)">&#xe633;</i>
                            <text>{{item.title}}</text>
                            <text v-if="item.pic_list && item.pic_list.length" class="blue_color p0_5"
                                @click.stop="previewImage(item)">[图片]</text>
                            <text v-if="item.score" class="color-sub font14 pl10">({{ item.score }}分)</text>
                        </view>
                        <view class="right f-j-e-a-c">
                            <i class="iconfont" v-html="item.show_content?'&#xe6a6;':'&#xe6a7;'"></i>
                        </view>
                    </view>

                    <view v-if="item.show_content" class="item-content gray_color font_9">
                        <!-- 题目选项 -->
                        <view class="item-answer-option">
                            <view v-if="item.question_types < 4" class="f-a-c" v-for="(itm,idx) in item.answer_option"
                                :key="idx">
                                <!-- <text>{{itm.letter}}.</text> -->
                                <view class="left f-j-a-c" :class="itm.is_right?'blue_bdrsolid':'gray_bdrsolid'">
                                    <i v-if="itm.is_right" class="iconfont blue_color">&#xe6ad;</i>
                                </view>
                                <text>{{itm.text}}</text>
                            </view>

                            <view v-if="item.question_types == 4" class="f blank_list"
                                v-for="(itm,idx) in item.answer_option" :key="idx">
                                <view class="blank_left">
                                    空格{{idx+1}}：
                                </view>
                                <view class="blank_right">
                                    <view class="" v-for="(it,ix) in itm.is_right" :key="ix">
                                        <text class="blue_color">{{it.text}}</text>
                                        <text v-if="ix < itm.is_right.length - 1" style="padding: 0 .5rem;">或</text>
                                    </view>
                                </view>
                            </view>

                            <view v-if="item.explain_text" class="memo_text">
                                解析：{{item.explain_text}}
                            </view>
                        </view>
                    </view>
                    <view class="f-j-sb-a-c pt_5">
                        <view class="memo_text p5_0">
                            <!-- <i class="iconfont" style="padding-right: .2rem;">&#xe6bb;</i> -->
                            {{item.create_time}}
                        </view>
                        <view v-if="!params.public" class="f-j-e-a-c">
                            <view class="btn memo_text f-a-c radius10 p5_1 font_9 mr2"
                                @click.stop="cancel_question(item.id)">
                                删除
                            </view>
                            <view class="btn blue_color f-a-c radius10 p5_1 font_9"
                                @click.stop="skip_modif_question(item.id)">
                                编辑
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view v-if="err_text" class="err_text">{{err_text}}</view>
        <!-- 底部操作栏 -->
        <view v-if="params.addQuestion && params.addQuestion == 1" class="tabbar-box f">
            <view class="left-box f-j-sb-a-c p1">
                <view class="f-a-c">
                    <view class="select-box">
                        <view class="select-item f-j-a-c" :class="is_allselect?'blue_bdrsolid':''"
                            @click.stop="change_allselect">
                            <i v-if="is_allselect" class="iconfont blue_color font1_5">&#xe6ad;</i>
                        </view>
                    </view>
                    <view class="select-text">
                        全选
                    </view>
                </view>
                <view class="">
                    共
                    <text class="blue_color p0_5">{{total}}</text>
                    题
                </view>
            </view>
            <view class="right-btn">
                <view class="btn blue_bgcolor f-j-a-c" @click.stop="add_question_to_exam">
                    添加到考卷
                </view>
            </view>
        </view>
        <view v-if="!params.addQuestion && !params.public" class="tabbar-box f">
            <view class="left-box f-j-sb-a-c p1">
                <view class="f-a-c">
                    <view class="select-box">
                        <view class="select-item f-j-a-c" :class="is_allselect?'blue_bdrsolid':''"
                            @click.stop="change_allselect">
                            <i v-if="is_allselect" class="iconfont blue_color font1_5">&#xe6ad;</i>
                        </view>
                    </view>
                    <view class="select-text">
                        全选
                    </view>
                </view>
                <view class="">
                    共
                    <text class="blue_color p0_5">{{total}}</text>
                    题
                </view>
            </view>
            <view class="right-btn bottom_btn f-j-sb-a-c p_5">
                <view class="btn gray_bdrsolid gray_color f-j-a-c" @click.stop="cancel_all_question">
                    删除
                </view>
                <view class="btn f-j-a-c blue_bgcolor" @click.stop="open_all_modif_category">
                    编辑
                </view>
            </view>
        </view>
        <view v-if="!params.addQuestion && params.public" class="tabbar-box f-j-a-c">
            <view class="">
                共
                <text class="blue_color p0_5">{{total}}</text>
                题
            </view>
        </view>


        <!-- 右边悬浮按钮 -->
        <view class="float-box">
            <view v-if="!params.public" class="float-item" @click.stop="skip_add_question">
                <i class="iconfont">&#xe602;</i>
                <text>添加题目</text>
            </view>
            <view v-if="!params.public" class="float-item" @click.stop="skip_page(97)">
                <i class="iconfont">&#xe7a4;</i>
                <text>我的考卷</text>
            </view>
        </view>


        <!-- 添加题目弹框 -->
        <view v-if="show_all_modif_category" class="fixed_modal f-d-c-j-a-c">
            <view class="fixed_modal_content">
                <view class="fixed-item f-a-c mb1">
                    <view class="left">
                        当前分类
                    </view>
                    <view class="right">
                        <!-- 选择分类 -->
                        <picker mode="selector" :value="category_idx" :range="category_list" range-key="name"
                            @change="change_category_list">
                            <view class="selector-box radius_3">
                                <view class="selector-item f-j-sb-a-c p_5 font_9">
                                    <view>{{category_list[category_idx].name}}</view>
                                    <i class="iconfont">&#xe6a6;</i>
                                </view>
                            </view>
                        </picker>
                    </view>
                </view>
                <view class="fixed-item f-a-c mb1">
                    <view class="left">
                        题目分数
                    </view>
                    <view class="right">
                        <uni-easyinput v-model="batch_modification_score" maxlength="5" trim="all"/>
                    </view>
                </view>
                <view class="btn-box f-j-sb-a-c">
                    <view class="red_bdrsolid red_color btn" @click.stop="close_all_modif_category">
                        取消
                    </view>
                    <view class="blue_bdrsolid blue_color btn" @click.stop="modif_question_category">
                        修改
                    </view>
                </view>
            </view>
            <i class="iconfont" @click.stop="close_all_modif_category">&#xe6b7;</i>
        </view>

        <emptyData v-if="show_empty"></emptyData>
    </view>
</template>

<script>
    let app = getApp();

	import liuyunoTabs from "@/pages/likou_dati/components/liuyuno-tabs/liuyuno-tabs.vue"
	import emptyData from "@/pages/likou_dati/components/emptyData/emptyData.vue"

    export default {
		components: {
			liuyunoTabs,
			emptyData
		},
        data() {
            return {
                params: {},
                show_empty: false,

                // 顶部题库分类
                category_list: [],
                category_idx: 0,
                show_all_modif_category: false,
                modif_question_list: [], // 修改分类时获取得所选题目
                keywords: '', // 搜索关键字

                // 题目分类
                question_types_list: [{
                        name: '全部',
                        value: 0,
                    },
                    {
                        name: '单选题',
                        value: 1,
                    },
                    {
                        name: '多选题',
                        value: 2,
                    },
                    {
                        name: '判断题',
                        value: 3,
                    },
                    {
                        name: '填空题',
                        value: 4,
                    }
                ],
                question_types_idx: 0,

                // 题目列表
                question_list: [],
                is_lastpage: false,
                page: 1,
                total: 0, // 总题数


                is_allselect: false, // 是否全选
                err_text: '',

                count_list: [],
                count_idx: 0,
                is_load: false,

                batch_modification_score: '', // 批量修改分数
            }
        },

        onLoad(options) {
            // console.log('获取的参数options', options);
            this.params = options;
            this.xwyLib.showLoading();
            this.xwyLib.setBarTitle(options.public ? '公共题库' : '我的题库')
            this.xwyLib.init(() => {
                // this.userInfo = app.globalData.userInfo;
                this.init();
            })
        },

        onShow() {
            if (app.globalData.update == 1) {
                app.globalData.update = null;
                this.get_question_list();
            }
            if (app.globalData.questionUpdateID) {
                let id = app.globalData.questionUpdateID;
                app.globalData.questionUpdateID = null;
                uni.removeStorageSync('questionUpdateID')
                this.get_question_details(id);
            }
        },

        methods: {
            async init() {
                this.get_category_list();
            },

            get_question_list(page = 1) {
                this.xwyLib.showLoading();

                let data = {
                    access_token: app.globalData.access_token,
                    page,
                    perpage: 20,
                };

                if (this.params.public) data.is_public = 1;

                // 判断题库分类ID是否存在
                if (Array.isArray(this.category_list) && this.category_list.length) data.category_id = this
                    .category_list[this.category_idx].category_id;
                // 判断题目分类ID是否存在
                if (this.question_types_idx > 0) data.question_types = this.question_types_idx;
                // 模糊搜索
                if (this.keywords) data.keywords = this.keywords;

                let obj = {
                    data,
                    before: res => {
                        this.err_text = '';
                        if (page == 1) {
                            this.question_list = [];
                            this.is_lastpage = false;
                            this.show_empty = false;
                            this.total = 0;
                        }
                    },
                    success: res => {
                        let list = res.data;
                        if (list?.length) this.is_allselect = false;
                        if (page > 1) {
                            list = [...this.question_list, ...res.data];

                            if (res.data.length < 20) {
                                this.err_text = '没有更多了，别再拉了！';
                            }
                        }
                        this.question_list = list;
                        this.is_lastpage = res.is_lastpage;
                        this.page = page + 1;
                        this.total = res.total;
                    },
                    fail: res => {
                        if (page == 1) {
                            this.show_empty = true;
                            return
                        }
                        this.err_text = res.info;
                    }
                };
                this.xwyLib.get_question_list(obj);
            },

            get_question_details(id) {
                this.xwyLib.get_question_details({
                    id,
                    success: res => {
                        let question_types_title = '';
                        switch (Number(res.question_types)) {
                            case 1:
                                question_types_title = '单选'
                                break;
                            case 2:
                                question_types_title = '多选'
                                break;
                            case 3:
                                question_types_title = '判断'
                                break;
                            case 4:
                                question_types_title = '填空'
                                break;
                            case 5:
                                question_types_title = '简答'
                                break;
                        }
                        res.question_types_title = question_types_title;

                        let answer_option = [];
                        if (this.xwyLib.isJSON(res.answer_option)) answer_option = JSON.parse(res
                            .answer_option);
                        if (answer_option && answer_option.length && res.question_types != 4 && res
                            .question_types != 5) {
                            answer_option.forEach((v, i) => {
                                v.letter = this.xwyLib.numberToLetter(i);
                            })
                        }
                        res.answer_option = answer_option;
                        let list = this.question_list;
                        for (let i in list) {
                            if (list[i].id == id) {
                                this.$set(this.question_list, i, res)
                                break;
                            }
                        }

                        // 显示为填词题目
                        if (res.answer_option[0]?.fill_words) res.question_types_title = '填词'
                    },
                    fail: res => {
                        this.xwyLib.showModal(res.info, {
                            success: response => {
                                this.xwyLib.back_previous_page();
                            }
                        })
                    }
                })
            },

            // 获取题库分类
            async get_category_list(page = 1) {
                this.xwyLib.showLoading();
                let opt = {
                    types: this.params.public ? 6 : 4, // 6公共题库分类 4我的考题分类
                    page,
                    perpage: 100,
                    before: res => {
                        if (page = 1) this.category_list = [];
                    },
                    success: res => {
                        let list = [...res.data];

                        if (!this.params.public) {
                            let opt = {};
                            Object.assign(opt, list[0])
                            opt.name = '全部';
                            opt.label = '全部';
                            opt.value = 0;
                            opt.category_id = '';
                            opt.sort_num = 1;
                            list.unshift(opt)
                        }

                        this.category_list = list;
                        this.get_question_list();
                    },
                    fail: res => {
                        if (!this.params.public) {
                            let opt = {};
                            opt.name = '全部';
                            opt.label = '全部';
                            opt.value = 0;
                            opt.category_id = '';
                            opt.sort_num = 1;
                            this.category_list = [opt];
                            this.get_question_list();
                            return
                        }
                        this.err_text = '未获取到公共题库！'
                    }
                };
                if(!this.params.public) opt.my_self = 1;
                this.xwyLib.get_category_list(opt);
            },


            // 切换分类
            select_category_list(index, item) {
                if (index >= 0) {
                    this.category_idx = item.value;
                    this.get_question_list();
                } else {
                    this.category_idx = 0;
                }
            },
            // 切换题目分类
            select_question_types_list(index, item) {
                if (index >= 0) {
                    this.question_types_idx = item.value;
                    this.get_question_list();
                } else {
                    this.question_types_idx = 0;
                }
            },

            // 切换分类
            change_tabs(e) {
                this.category_idx = e;
                this.is_allselect = false;
                this.get_question_list();
            },

            change_question_types(e) {
                this.question_types_idx = e;
                this.is_allselect = false;
                this.get_question_list();
            },

            // 搜索关键字
            search_keywords() {
                this.get_question_list();
            },

            // 改变题目选中
            change_question_select(e) {
                this.$set(this.question_list[e], 'is_select', !this.question_list[e].is_select);
                let list = [...this.question_list];
                let len_list = [];
                list.forEach(val => {
                    if (val.is_select) {
                        len_list.push(val)
                    }
                })
                if (len_list.length != list.length) {
                    this.is_allselect = false;
                } else {
                    this.is_allselect = true;
                }
            },

            change_allselect() {
                this.is_allselect = !this.is_allselect;
                let list = [...this.question_list];
                if (Array.isArray(list)) {
                    if (this.is_allselect) {
                        list.forEach(val => {
                            val.is_select = true;
                        })
                    } else {
                        list.forEach(val => {
                            val.is_select = false;
                        })
                    }
                }
            },

            add_question_to_exam() {
                let list = this.question_list,
                    question_list = [];

                if (list.length) {
                    list.forEach((val, idx) => {
                        if (val.is_select) {
                            question_list.push(val)
                        }
                    })
                }
                if (!question_list || !question_list.length) {
                    uni.showModal({
                        content: '请选择题目！'
                    })
                }
                this.xwyLib.showLoading();
                let q_list = [];
                question_list.forEach((val, idx) => {
                    q_list.push({
                        question_id: val.id,
                        score: parseFloat(val.score),
                        sort_num: val.sort_num,
                    })
                })
                console.log('q_list', q_list);
                let params = {
                    exam_id: this.params.exam_id,
                    question_list: this.xwyLib.jsonEncode(q_list),
                };
                console.log('params', params);
                let obj = {
                    data: params,
                    url: `front.flat.exam.admin/send_questions_to_exam`,
                    success: res => {
                        console.log('添加题目到考卷', res);
                        uni.hideLoading();
                        if (res.status != 1) {
                            uni.showModal({
                                content: res.info,
                            })
                            return
                        }
                        uni.showModal({
                            content: res.info,
                            success: response => {
                                app.globalData.updateExamQuestion = 1
                                this.xwyLib.back_previous_page()
                            }
                        })
                    },
                    fail: res => {
                        console.log('添加题目到考卷失败---' + `${JSON.stringify(res)}`);
                    }
                };
                this.xwyLib.ajax(obj)
            },

            // 删除题目
            async cancel_question(id) {
                uni.showModal({
                    content: '是否删除该题？',
                    confirmText: '删除',
                    success: res => {
                        if (res.confirm) {
                            this.cancel_question_post(id);
                        }
                    }
                })
            },

            async cancel_question_post(id) {
                this.xwyLib.showLoading();
                let data = {
                    ids: id,
                };
                let obj = {
                    data,
                    url: `front.flat.exam.questionBank/remove_question_bank`,
                    success: res => {
                        uni.hideLoading();
                        if (res.status != 1) {
                            this.xwyLib.alert(res.info);
                            return
                        }
                        this.xwyLib.showModal(res.info, {
                            success: res => {
                                this.get_question_list();
                            }
                        })
                    }
                };
                this.xwyLib.ajax(obj);
            },

            // 跳转到修改题目
            skip_modif_question(id) {
                let url = `/pages/likou_dati/pages/question/add_question/add_question?question_id=${id}`;
                this.xwyLib.routeJump(url);
            },

            // 加载更多
            async loading_more_data() {
                await this.$refs.udb.loadMore();
            },

            skip_page(e) {
                this.xwyLib.skip_page(e);
            },

            skip_add_question() {
                let url = `/pages/likou_dati/pages/question/add_question/add_question`;
                if (this.category_list && this.category_list.length) {
                    let types_id = this.category_list[this.category_idx]._id;
                    url += `?types_id=${types_id}`
                }
                this.xwyLib.routeJump(url);
            },

            change_answer_options_show(e) {
                let list = [...this.question_list];
                this.$set(this.question_list[e], 'show_content', !list[e].show_content)
            },

            // 删除所选题目
            cancel_all_question() {
                let list = this.question_list,
                    question_list = list.filter(val => {
                        return val.is_select
                    })

                if (!question_list || !question_list.length) {
                    this.xwyLib.alert('请选择题目');
                    return
                }

                uni.showModal({
                    content: '是否删除所选题目？',
                    confirmText: '删除',
                    success: res => {
                        if (res.confirm) {
                            let ids = '';
                            question_list.forEach((val, idx) => {
                                if (idx == 0) {
                                    ids = val.id;
                                } else {
                                    ids += `,${val.id}`
                                }
                            })
                            this.cancel_question_post(ids);
                        }
                    }
                })
            },

            previewImage(item) {
                let list = [];

                if (item.pic_list && item.pic_list.length) {
                    item.pic_list.forEach(val => {
                        list.push(val.url);
                    })
                }

                uni.previewImage({
                    current: 0,
                    urls: list,
                })
            },

            // 改变选填还是必填
            change_category_list(e) {
                let value = e.detail.value;
                if (this.category_idx == value) return
                this.category_idx = value;
            },

            open_all_modif_category() {
                let list = this.question_list,
                    question_list = [];

                if (list.length) {
                    list.forEach((val, idx) => {
                        if (val.is_select) {
                            question_list.push(val)
                        }
                    })
                }
                if (!question_list || !question_list.length) {
                    uni.showModal({
                        content: '请选择题目！'
                    })
                    return
                }
                this.batch_modification_score = '';
                this.modif_question_list = question_list;
                this.show_all_modif_category = true;
            },

            close_all_modif_category() {
                this.show_all_modif_category = false;
            },


            async modif_question_category() {
                let ids = '';
                // 获取需要修改的题目
                this.modif_question_list.forEach((val, idx) => {
                    if (idx === 0) {
                        ids = `${val.id}`
                    } else {
                        ids += `,${val.id}`
                    }
                })

                const score = Number(this.batch_modification_score);
                if (isNaN(score) || score < 0) return this.xwyLib.alert('请输入正确的分数！')

				// 获取需要修改成的分类
				let category_id = ''
				if (this.xwyLib.isArray(this.category_list)) {
				    category_id = this.category_list[this.category_idx].category_id || '';
				}
				const data = {
					ids,
					category_id,
				}
				 if (category_id) data.category_id = category_id;
         if (score) data.score = score;
                let obj = {
                    data,
                    url: `front.flat.exam.questionBank/multi_edit_question_bank`,
                    success: res => {
                        if (res.status != 1) {
                            this.xwyLib.alert('修改失败！')
                            return
                        }
                        this.close_all_modif_category();
                        this.xwyLib.showModal('修改成功!', {
                            success: response => {
                                this.get_question_list();
                            }
                        })
                    }
                };
                this.xwyLib.ajax(obj);
            },
        },

        onReachBottom() {
            if (this.is_lastpage) return
            this.is_allselect = false;
            this.get_question_list(this.page);
        },
    }
</script>

<style scoped lang='scss'>
	@import url('@/pages/likou_dati/common/css/common.scss');
	@import url('@/pages/likou_dati/common/css/iconfont.css');
	@import url('@/pages/likou_dati/common/css/public.css');

    .content {
        position: relative;
        padding-bottom: calc(60px + 14rem);

        .top-select {
            border-bottom: 0;
        }

        .fixed_modal {

            .fixed-item {
                width: 100%;
                max-height: 15rem;
                flex-wrap: wrap;
                overflow-y: auto;

                .left {
                    width: 5rem;
                }

                .right {
                    width: calc(100% - 5rem);

                    .selector-box {
                        width: 100%;
                        border: 1px solid #ddd;

                        .selector-item {
                            width: 100%;
                        }
                    }
                }
            }

            .btn-box {
                width: 100%;

                .btn {
                    width: 40%;
                    border-radius: 100rpx;
                    text-align: center;
                    font-size: .9rem;
                    padding: .5rem 0;
                }
            }
        }
    }

    .question-box .question-list .question-item {
        width: 100%;

        .item-title {
            width: 100%;

            .left {
                width: calc(100% - 1.5rem);

                >text {
                    &:nth-child(2) {
                        padding: 0 .5rem;
                    }
                }
            }

            .right {
                width: 1.5rem;

                .iconfont {
                    font-size: 1.2rem;
                }
            }
        }

        .item-content .item-answer-option {

            >view {
                padding-bottom: .5rem;

                .left {
                    width: 1.2rem;
                    height: 1.2rem;
                    margin-right: .5rem;

                    .iconfont {
                        font-size: 1.2rem;
                    }
                }
            }

            .blank_list {
                width: 100%;

                .blank_left {
                    width: 4rem;
                }

                .blank_right {
                    width: calc(100% - 4rem);

                    >view {
                        display: inline-block;
                    }
                }
            }
        }

    }


    .tabbar-box .bottom_btn .btn {
        width: calc((100% - 1rem) / 2);
        height: 0;
        font-size: 1rem;
        border-radius: 10rem;
        padding: 1.1rem .5rem;
    }
</style>
