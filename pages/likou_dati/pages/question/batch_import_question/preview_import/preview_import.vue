<template>
    <view class='content'>
        <!-- 分类 -->
        <view v-if="category_list && category_list.length" class="category-box f-a-c gray_bdrbdashed">
            <view class="left">
                题库分类：
            </view>
            <view class="right">
                <select-lay :value="category_idx" name="name" :options="category_list" :zindex="10"
                    @selectitem="select_category_list" placeholder=""></select-lay>
            </view>
        </view>

        <!-- 题目列表 -->
        <view class="question-box">
            <view class="question-list f gray_bdrbdashed" v-for="(item,index) in question_list" :key="index">
                <view v-if="show_modif" class="question-item">
                    <view class="item-title">
                        <text class="gray_color">{{index+1}}、</text>
                        <text class="gray_color">[{{item.question_types_title}}]</text>
                        <!-- 题目内容 -->
                        <uni-easyinput type="textarea" v-model="item.title"></uni-easyinput>
                    </view>
                    <view class="item-content gray_color">
                        <!-- 题目选项 单选，多选，判断 -->
                        <view v-if="item.question_types < 4" class="item-answer-option">
                            <radio-group v-if="item.question_types == 1 || item.question_types == 3" :data-index="index"
                                @change="change_radio">
                                <label class="answer_options_list f-a-c" v-for="(itm,idx) in item.answer_option"
                                    :key="idx">
                                    <view style="min-width: 4rem;">
                                        <radio :value="`${idx}`" :checked="itm.is_right" />
                                        <text>{{itm.letter}}、</text>
                                        <text v-if="item.question_types == 3">{{itm.text}}</text>
                                    </view>
                                    <input class="input-init" @click.stop="" v-if="item.question_types == 1"
                                        v-model="itm.text" placeholder="输入选项内容" />
                                    <!-- <uni-easyinput @focus.stop="" @click.stop="" v-if="item.question_types == 1" v-model="itm.text" placeholder="输入选项内容" /> -->
                                </label>
                            </radio-group>
                            <checkbox-group v-if="item.question_types == 2" :data-index="index"
                                @change="change_checbox">
                                <label class="answer_options_list f-a-c" v-for="(itm,idx) in item.answer_option"
                                    :key="idx">
                                    <view style="min-width: 4rem;">
                                        <checkbox :value="`${idx}`" :checked="itm.is_right" />
                                        <text>{{itm.letter}}、</text>
                                    </view>
                                    <input class="input-init" @click.stop="" v-model="itm.text" placeholder="输入选项内容" />
                                    <!-- <uni-easyinput v-model="itm.text" placeholder="输入选项内容" /> -->
                                </label>
                            </checkbox-group>
                        </view>
                        <!-- 题目选项 填空 -->
                        <view v-if="item.question_types == 4" class="item-answer-option">
                            <view class="blank_list f" v-for="(itm,idx) in item.answer_option" :key="idx">
                                <view class="blank_left" style="line-height: 36px;">
                                    空格{{idx+1}}：
                                </view>
                                <view class="blank_right">
                                    <view style="width: 100%;" v-for="(it,ix) in itm.is_right" :key="ix">
                                        <uni-easyinput v-model="it.text" placeholder="输入答案" />
                                        <text v-if="ix != itm.is_right.length - 1" style="padding: .5rem 0;">或</text>
                                    </view>
                                </view>
                            </view>
                        </view>
                        <view class="f">
                            <view style="line-height: 36px;min-width: 4rem">解析：</view>
                            <uni-easyinput style="width: 100%;" type="textarea" v-model="item.explain_text" placeholder="题目解析内容,可不填" />
                        </view>
                    </view>
                </view>

                <view v-if="!show_modif" class="question-item">
                    <view class="item-title">
                        <text class="gray_color">{{index+1}}、</text>
                        <text class="gray_color">[{{item.question_types_title}}]</text>
                        <!-- 题目内容 -->
                        <text>{{item.title}}</text>
                    </view>
                    <view class="item-content gray_color">
                        <!-- 题目选项 单选，多选，判断 -->
                        <view v-if="item.question_types < 4" class="item-answer-option">
                            <view class="answer_options_list f-a-c" v-for="(itm,idx) in item.answer_option">
                                <view class="left f-j-a-c" :class="itm.is_right?'blue_bdrsolid':'gray_bdrsolid'">
                                    <i v-if="itm.is_right" class="iconfont blue_color">&#xe6ad;</i>
                                </view>
                                <text>{{itm.letter}}、</text>
                                <text>{{itm.text}}</text>
                            </view>
                        </view>
                        <!-- 题目选项 填空 -->
                        <view v-if="item.question_types == 4" class="item-answer-option">
                            <view class="blank_list f-a-c" v-for="(itm,idx) in item.answer_option">
                                <view class="blank_left">
                                    空格{{idx+1}}：
                                </view>
                                <view class="blank_right">
                                    <view class="" v-for="(it,ix) in itm.is_right">
                                        <text class="blue_color">{{it.text}}</text>
                                        <text v-if="ix != itm.is_right.length - 1" style="padding: 0 .5rem;">或</text>
                                    </view>
                                </view>
                            </view>
                        </view>
                        <!-- 解析 -->
                        <view v-if="item.explain_text">
                            解析：{{item.explain_text}}
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <view class="tabbar-box f">
            <view class="right-btn f-j-a-c">
                <view class="btn f-j-a-c" @click.stop="back_page">
                    返回
                </view>
            </view>
            <view class="right-btn f-j-a-c">
                <view class="btn f-j-a-c" @click.stop="show_modif = !show_modif">
                    {{show_modif?'退出编辑':'编辑'}}
                </view>
            </view>
            <view class="right-btn f-j-a-c">
                <view class="btn f-j-a-c gre_color" @click.stop="batch_save">
                    批量导入
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    let app = getApp();
	import selectLay from "@/pages/likou_dati/uni_modules/select-lay/components/select-lay/select-lay.vue"
    export default {
		components: {
			selectLay
		},
        data() {
            return {
                params: {},

                // 顶部分类
                category_list: [],
                category_idx: 0,

                question_list: [],
                show_modif: false,
            }
        },
        onLoad(options) {
            // console.log('获取的参数options', options);/
            this.params = options;
            this.xwyLib.init(() => {
                this.init();
            })
        },
        methods: {
            async init() {
                this.xwyLib.setBarTitle('预览')
                if (!this.params.category_id) this.get_category_list();
                this.get_question_list();
            },

            get_question_list() {
                if (uni.getStorageSync('previewImport')) {
                    let list = JSON.parse(uni.getStorageSync('previewImport'));

                    if (!this.xwyLib.isArray(list)) {
                        this.xwyLib.alert('获取题目失败！')
                        return
                    }
                    list.forEach(val => {
                        switch (Number(val.question_types)) {
                            case 1:
                                val.question_types_title = '单选'
                                break;
                            case 2:
                                val.question_types_title = '多选'
                                break;
                            case 3:
                                val.question_types_title = '判断'
                                break;
                            case 4:
                                val.question_types_title = '填空'
                                break;
                            case 5:
                                val.question_types_title = '简答'
                                break;
                        }

                        if (this.xwyLib.isArray(val.answer_option)) {
                            val.answer_option.forEach((v, i) => {
                                v.letter = this.xwyLib.numberToLetter(i);
                            })
                        }
                    })
                    this.question_list = list;
                }
            },

            // 获取题库分类
            async get_category_list(page = 1) {
                this.xwyLib.showLoading();
                let opt = {
                    types: this.params.public ? 6 : 4, // 6公共题库分类 4我的考题分类
                    page,

                    perpage: 100,
                    before: res => {
                        if (page = 1) this.category_list = [];
                    },
                    success: res => {
                        let list = [...res.data];
                        let opt = {};
                        Object.assign(opt, list[0])
                        opt.name = '不指定分类';
                        opt.category_id = '';
                        opt.sort_num = 1;
                        list.unshift(opt)

                        list.forEach((val, idx) => {
                            val.label = val.name;
                            val.value = idx;
                        })

                        this.category_list = list;
                    },
                    fail: res => {
                        if (!this.params.public) {
                            let opt = {};
                            opt.name = '不指定分类';
                            opt.category_id = '';
                            opt.sort_num = 1;
                            this.category_list = [opt];
                            return
                        }
                        this.err_text = '未获取到公共题库！'
                    }
                };
                if (!this.params.public) opt.my_self = 1
                this.xwyLib.get_category_list(opt);
            },


            back_page() {
                this.xwyLib.back_previous_page();
            },

            // 选择题库分类
            select_category_list(index, item) {
                if (index >= 0) {
                    this.category_idx = item.value;
                } else {
                    this.category_idx = 0;
                }
            },

            batch_save() {
                // this.xwyLib.judge_integral_cost_set(() => {
                this.batch_save_post();
                // }, 3)
            },

            // 文本批量导入题目
            async batch_save_post() {
                this.xwyLib.showLoading();
                let question_list = this.question_list;

                if (this.params.category_id) {
                    question_list.forEach(val => {
                        val.category_id = this.params.category_id;
                    })
                }

                if (!this.params.category_id) {
                    let category_list = this.category_list,
                        category_idx = this.category_idx;
                    if (category_idx > 0) {
                        question_list.forEach(val => {
                            val.category_id = category_list[category_idx].category_id;
                        })
                    }
                }

                let params = {
                    question_list: this.xwyLib.jsonEncode(question_list),
                };

                let obj = {
                    data: params,
                    url: `front.flat.exam.questionBank/batch_export_question_bank`,
                    success: res => {
                        uni.hideLoading()
                        if (res.status != 1) {
                            this.xwyLib.alert(res.info);
                            return
                        }

                        uni.showModal({
                            content: res.info,
                            success: response => {
                                let url =
                                    `/pages/likou_dati/pages/question/question_bank_list/question_bank_list`;
                                if (this.params.category_id) url += `?public=1`
                                uni.redirectTo({url})
                            }
                        })
                    }
                };

                this.xwyLib.ajax(obj);
            },


            change_radio(e) {
                let index = e.currentTarget.dataset.index,
                    value = e.detail.value || '',
                    answer_option = this.question_list[index].answer_option;
                for (let i in answer_option) {
                    answer_option[i].is_right = false;
                    if (value == i) answer_option[i].is_right = true;
                }
            },

            change_checbox(e) {
                let index = e.currentTarget.dataset.index,
                    val_list = e.detail.value || [];
                this.question_list[index].answer_option.forEach((val, idx) => {
                    val.is_right = false;
                    for (let i in val_list) {
                        if (val_list[i] == idx) {
                            val.is_right = true;
                            break;
                        }
                    }
                })
            },
        }
    }
</script>

<style scoped lang='scss'>
	@import url('@/pages/likou_dati/common/css/common.scss');
	@import url('@/pages/likou_dati/common/css/iconfont.css');
	@import url('@/pages/likou_dati/common/css/public.css');

    .content {
        padding-bottom: 70px;

        .category-box {
            position: sticky;
            left: 0;
            top: 0;
            width: 100%;
            padding: 1rem;
            background-color: #fff;
            z-index: 1;

            .left {
                width: 6rem;
            }

            .right {
                width: calc(100% - 6rem);
            }
        }

        .question-box {
            padding: 0 1rem;

            .question-list {
                padding-top: 1rem;
                padding-bottom: .5rem;

                .select-box {

                    .iconfont {
                        font-size: 1.5rem;
                    }
                }

                .question-item {
                    width: 100%;

                    .item-title {
                        width: 100%;
                        padding-bottom: 1rem;
                    }

                    .item-content {
                        font-size: .9rem;
                        padding-bottom: .5rem;

                        .item-answer-option {

                            .answer_options_list {
                                padding-bottom: .5rem;

                                &:last-child {
                                    padding-bottom: 1rem;
                                }

                                .left {
                                    width: 1.2rem;
                                    height: 1.2rem;
                                    margin-right: .5rem;

                                    .iconfont {
                                        font-size: 1.2rem;
                                    }
                                }
                            }

                            .blank_list {
                                width: 100%;
                                padding-bottom: .3rem;

                                &:last-child {
                                    padding-bottom: 1rem;
                                }

                                .blank_left {
                                    width: 4rem;
                                }

                                .blank_right {
                                    width: calc(100% - 4rem);

                                    >view {
                                        display: inline-block;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        .tabbar-box {

            .right-btn {

                .btn {
                    width: 100%;
                    text-align: center;
                    font-size: 1rem;
                }

                &:nth-child(2) {
                    border: 1px solid #eee;
                    border-top: 0;
                    border-bottom: 0;
                }
            }
        }

        .input-init {
            width: 100%;
            border: 1px solid #ddd;
            border-radius: .2rem;
            height: 36px;
            line-height: 36px;
            padding: 0 .5rem;
            font-size: .9rem;
        }
    }
</style>
