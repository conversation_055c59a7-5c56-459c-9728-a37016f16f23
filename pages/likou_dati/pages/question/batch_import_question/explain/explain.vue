<template>
    <view class='content'>
        <view class="tips" v-html="page_top_tips">
        </view>

        <view class="btn-box">
            <view class="btn blue_bdrsolid blue_color f-j-a-c" @click.stop="skip_page(213)">
                批量上传-excel导入
            </view>
            <view class="btn blue_bdrsolid blue_color f-j-a-c" @click.stop="skip_page(214)">
                批量上传-文本导入
            </view>
        </view>
    </view>
</template>

<script>
    let app = getApp();

    export default {
        data() {
            return {
                params: {},
                page_top_tips: '批量导入题目，可以选择1 使用Excel批量导入题目 2使用文本复制粘贴导入题目',
            }
        },
        onLoad(options) {
            console.log('获取的参数options', options);
            this.params = options;
            this.xwyLib.init(() => {
                this.init();
            })
        },
        methods: {
            async init() {
                console.log('app.globalData', app.globalData);
                this.xwyLib.setBarTitle('批量导题')
            },

            // 跳转页面
            skip_page(e) {
                // #ifndef H5
                if (e == 213) {
                    uni.showModal({
                        content: `请在电脑端上传excel文件！`,
                        confirmText: '复制',
                        showCancel: false,
                        success: res => {
                            let static_url = app.globalData?.shopconfset?.shop_set?.static_url || '',
                                url =
                                `${static_url}${this.xwyLib.thanUrl}pages/exam/question/batch_import_question/explain/explain?shopid=${this.xwyLib.ext_conf.who}`;
                            console.log('url', url);
                            this.xwyLib.copy_text({
                                url,
                                text: `复制成功，请在浏览器打开！`,
                                toast: 1,
                            })
                        }
                    })
                    return
                }
                // #endif

                // 判断url是否含有分类ID 如果含有分类ID则带过去
                if (this.params.category_id) {
                    let url =
                        `/pages/likou_dati/pages/exam/question/batch_import_question/excel_import/excel_import?category_id=${this.xwyLib.getUrlParams('category_id')}`;
                    if (e == 214) {
                        url =
                            `/pages/likou_dati/pages/exam/question/batch_import_question/text_import/text_import?category_id=${this.xwyLib.getUrlParams('category_id')}`;
                    }
                    this.xwyLib.skip_page({
                        url,
                    });
                    return
                }
                this.xwyLib.skip_page(e);
            },

        }
    }
</script>

<style scoped lang='scss'>
	@import url('@/pages/likou_dati/common/css/common.scss');
	@import url('@/pages/likou_dati/common/css/iconfont.css');
	@import url('@/pages/likou_dati/common/css/public.css');

    .content {
        padding: 0 1rem;

        .tips {
            padding: 1rem 0;
        }

        .btn-box {
            width: 100%;
            padding-top: 2rem;

            .btn {
                width: 100%;
                border-radius: .3rem;
                margin-bottom: 1rem;
                padding: .7rem 0;
                font-size: 1.1rem;
            }
        }
    }
</style>
