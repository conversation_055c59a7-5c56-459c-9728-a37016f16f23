import xwyLib from "@/pages/likou_dati/common/js/public.js"
// 共用方法
let analysisCommonFunction = {
	// 删掉数组里面的空值
	removeEmpty(arr) {
		for (var i = 0; i < arr.length; i++) {
			if (arr[i] === "" || typeof(arr[i]) == "undefined" || arr[i] == null) {
				arr.splice(i, 1);
				i -= 1; // i - 1 ,因为空元素在数组下标 2 位置，删除空之后，后面的元素要向前补位
			}
		}
		return arr;
	},
	// 字符串删除换行 空格
	trim_str(str, type) {
		type = type || 1;
		switch (type) {
			case 1:
				return str.replace(/\s+/g, "");
			case 2:
				return str.replace(/(^\s*)|(\s*$)/g, "");
			case 3:
				return str.replace(/(^\s*)/g, "");
			case 4:
				return str.replace(/(\s*$)/g, "");
			case 5:
				return str.replace(/\n|\r/g, "");
			case 5:
				return str.replace(/&nbsp;/ig, "");
			default:
				return str;
		}
	},
	// 错误提示
	err_alert(obj) {
		let text = '';
		if (obj.index == 1) {
			text = `${obj.errText} 答题选项解析出错！`
		}
		if (obj.index == 2) {
			text = obj.errText;
		}

		let err_text = `${text} __ 第${obj.idx}题 - ${obj.title}`
		xwyLib.alert(err_text)
	}

}

// 第一种解析模板
let analysisTypeList1 = {
	analytical(textareaValue) {

		if (!textareaValue) {
			xwyLib.alert(`请输入需要导入的题目内容！`)
			return
		}

		textareaValue = '\n' + textareaValue;
		var textareaValueArr = textareaValue.split(/[\r\n]\d+./);

		textareaValueArr = analysisCommonFunction.removeEmpty(textareaValueArr);
		if (!textareaValueArr.length) {
			xwyLib.alert('没有解析出结果，请检查输入的内容');
			return false;
		}

		// 把每道题里面的内容按 "回车" 切割
		for (var i = 0, len = textareaValueArr.length; i < len; i++) {
			textareaValueArr[i] = textareaValueArr[i].split(/[\r\n]/);
			textareaValueArr[i] = analysisCommonFunction.removeEmpty(textareaValueArr[i]);
		}

		var data = this.arr2data(textareaValueArr);

		return data;
	},

	// 把切割的数组拼接成ajax提交的数据
	arr2data(arr) {
		var data = [];
		for (var i = 0, len = arr.length; i < len; i++) {
			data[i] = {
				title: arr[i][0],
				question_types: 1,
				answer_option: [],
				explain_text: '',
			};

			if (!arr[i][1]) {
				xwyLib.alert(`第${i+1}题只有标题！`)
				return
			}

			for (var j = 0, len_j = arr[i].length; j < len_j; j++) {
				var opt_item = analysisCommonFunction.trim_str(arr[i][j]);
				let answerOptions = {
					text: '',
					is_right: false,
				}

				// 单选 或 多选 切割答案选项
				var reg_A = /(A|a)(\.|、|．|。|\:|：)/,
					letterA = /A|a/;
				if (opt_item.search(letterA) === 0 && opt_item.search(reg_A) === 0) {
					data[i].answer_option[0] = answerOptions;
					data[i].answer_option[0].text = opt_item.replace(reg_A, '');
				} else if (opt_item.search(letterA) === 0 && opt_item.search(reg_A) === -1) {
					let obj = {
						idx: i + 1,
						index: 2,
						title: data[i].title,
						errText: 'A选项出错',
					};
					analysisCommonFunction.err_alert(obj)
					return
				}
				var reg_B = /(B|b)(\.|、|．|。|\:|：)/,
					letterB = /B|b/;
				if (opt_item.search(letterB) === 0 && opt_item.search(reg_B) === 0) {
					data[i].answer_option[1] = answerOptions;
					data[i].answer_option[1].text = opt_item.replace(reg_B, '');
				} else if (opt_item.search(letterB) === 0 && opt_item.search(reg_B) === -1) {
					let obj = {
						idx: i + 1,
						index: 2,
						title: data[i].title,
						errText: 'B选项出错',
					};
					analysisCommonFunction.err_alert(obj)
					return;
				}
				var reg_C = /(C|c)(\.|、|．|。|\:|：)/,
					letterC = /C|c/;
				if (opt_item.search(reg_C) === 0) {
					data[i].answer_option[2] = answerOptions;
					data[i].answer_option[2].text = opt_item.replace(reg_C, '');
				} else if (opt_item.search(letterC) === 0 && opt_item.search(reg_C) === -1) {
					let obj = {
						idx: i + 1,
						index: 2,
						title: data[i].title,
						errText: 'C选项出错',
					};
					analysisCommonFunction.err_alert(obj)
					return;
				}
				var reg_D = /(D|d)(\.|、|．|。|\:|：)/,
					letterD = /D|d/;
				if (opt_item.search(reg_D) === 0) {
					data[i].answer_option[3] = answerOptions;
					data[i].answer_option[3].text = opt_item.replace(reg_D, '');
				} else if (opt_item.search(letterD) === 0 && opt_item.search(reg_D) === -1) {
					let obj = {
						idx: i + 1,
						index: 2,
						title: data[i].title,
						errText: 'D选项出错',
					};
					analysisCommonFunction.err_alert(obj)
					return;
				}
				var reg_E = /(E|e)(\.|、|．|。|\:|：)/,
					letterE = /E|e/;
				if (opt_item.search(letterE) === 0 && opt_item.search(reg_E) === 0) {
					data[i].answer_option[4] = answerOptions;
					data[i].answer_option[4].text = opt_item.replace(reg_E, '');
				} else if (opt_item.search(letterE) === 0 && opt_item.search(reg_E) === -1) {
					let obj = {
						idx: i + 1,
						index: 2,
						title: data[i].title,
						errText: 'E选项出错',
					};
					analysisCommonFunction.err_alert(obj)
					return
				}
				var reg_F = /(F|f)(\.|、|．|。|\:|：)/,
					letterF = /F|f/;
				if (opt_item.search(letterF) === 0 && opt_item.search(reg_F) === 0) {
					data[i].answer_option[5] = answerOptions;
					data[i].answer_option[5].text = opt_item.replace(reg_F, '');
				} else if (opt_item.search(letterF) === 0 && opt_item.search(reg_F) === -1) {
					let obj = {
						idx: i + 1,
						index: 2,
						title: data[i].title,
						errText: 'F选项出错',
					};
					analysisCommonFunction.err_alert(obj)
					return
				}
				var reg_G = /(G|g)(\.|、|．|。|\:|：)/,
					letterG = /G|g/;
				if (opt_item.search(letterG) === 0 && opt_item.search(reg_G) === 0) {
					data[i].answer_option[6] = answerOptions;
					data[i].answer_option[6].text = opt_item.replace(reg_G, '');
				} else if (opt_item.search(letterG) === 0 && opt_item.search(reg_G) === -1) {
					let obj = {
						idx: i + 1,
						index: 2,
						title: data[i].title,
						errText: 'G选项出错',
					};
					analysisCommonFunction.err_alert(obj)
					return
				}
				var reg_H = /(H|h)(\.|、|．|。|\:|：)/,
					letterH = /H|h/;
				if (opt_item.search(letterH) === 0 && opt_item.search(reg_H) === 0) {
					data[i].answer_option[7] = answerOptions;
					data[i].answer_option[7].text = opt_item.replace(reg_H, '');
				} else if (opt_item.search(letterH) === 0 && opt_item.search(reg_H) === -1) {
					let obj = {
						idx: i + 1,
						index: 2,
						title: data[i].title,
						errText: 'H选项出错',
					};
					analysisCommonFunction.err_alert(obj)
					return
				}
				var reg_I = /(I|i)(\.|、|．|。|\:|：)/,
					letterI = /I|i/;
				if (opt_item.search(letterI) === 0 && opt_item.search(reg_I) === 0) {
					data[i].answer_option[8] = answerOptions;
					data[i].answer_option[8].text = opt_item.replace(reg_I, '');
				} else if (opt_item.search(letterI) === 0 && opt_item.search(reg_I) === -1) {
					let obj = {
						idx: i + 1,
						index: 2,
						title: data[i].title,
						errText: 'I选项出错',
					};
					analysisCommonFunction.err_alert(obj)
					return
				}
				var reg_J = /(J|j)(\.|、|．|。|\:|：)/,
					letterJ = /J|j/;
				if (opt_item.search(letterJ) === 0 && opt_item.search(reg_J) === 0) {
					data[i].answer_option[9] = answerOptions;
					data[i].answer_option[9].text = opt_item.replace(reg_J, '');
				} else if (opt_item.search(letterJ) === 0 && opt_item.search(reg_J) === -1) {
					let obj = {
						idx: i + 1,
						index: 2,
						title: data[i].title,
						errText: 'J选项出错',
					};
					analysisCommonFunction.err_alert(obj)
					return
				}
				// 判断单选或多选的答案选项是否错误
				if (data[i].answer_option && data[i].answer_option.length) {
					for (var item = 0; item < data[i].answer_option.length; item++) {
						if (item > 0 && item <= data[i].answer_option.length - 1) {
							if (!data[i].answer_option[item - 1]) {
								var answerItem = this.getMatchingABC(item);
								// console.log(`第${i+1}题${answerItem}答案选项出错!`);
								let obj = {
									idx: i + 1,
									index: 1,
									title: data[i].title,
									errText: answerItem,
								};
								analysisCommonFunction.err_alert(obj)
								return
							}
						}
					}
				}

				// 解析正确答案
				var reg_rightanswer = /【答案】|答案：|答案:|【正确答案】|正确答案：|正确答案:/;

				if (opt_item.search(reg_rightanswer) === 0) {
					var right_answer_str = opt_item.replace(reg_rightanswer, '');
					var right_answer_arr = right_answer_str.split(/[\.|、|．|。|,|，]/);
					let answer_list = analysisCommonFunction.removeEmpty(right_answer_arr);
					if (!answer_list || !answer_list.length) {
						let obj = {
							idx: i + 1,
							index: 2,
							title: data[i].title,
							errText: '没有解析出正确答案，请检查输入的内容',
						};
						analysisCommonFunction.err_alert(obj)
						return
					}
					// 单选 或 多选
					if (data[i].answer_option && data[i].answer_option.length) {
						answer_list = this.ABC2number(answer_list);
						for (let x = 0; x < answer_list.length; x++) {
							if (answer_list[x].length > 1) {
								let obj = {
									idx: i + 1,
									index: 2,
									title: data[i].title,
									errText: `正确答案解析失败！`,
								};
								analysisCommonFunction.err_alert(obj)
								return
							}
						}
						// 给选项赋值  is_right为true表示正确答案
						let is_err_row = false;
						for(let value in answer_list){
							if(!data[i].answer_option[answer_list[value]]){
								is_err_row = true;
								break;
							}
							data[i].answer_option[answer_list[value]].is_right = true;
						}
						if(is_err_row){
							xwyLib.alert(`第${i+1}题导入格式错误！`)
							return
						}
						data[i].question_types = 1;
						if (answer_list.length > 1) {
							data[i].question_types = 2;
							if (answer_list.length > data[i].answer_option.length) {
								let obj = {
									idx: i + 1,
									index: 2,
									title: data[i].title,
									errText: `多选题答案选项数量少于正确答案数量！`,
								};
								analysisCommonFunction.err_alert(obj)
							}
						}
					}

					// 判断题 或 填空题
					if ((!data[i].answer_option || !data[i].answer_option.length) && answer_list.length) {
						let str = analysisCommonFunction.trim_str(answer_list[0]);
						let judgeQuestion = false;
						switch (str) {
							case '对':
								judgeQuestion = true
								break;
							case '错':
								judgeQuestion = true
								break;
							case '正确':
								judgeQuestion = true
								break;
							case '错误':
								judgeQuestion = true
								break;
							case '√':
								judgeQuestion = true
								break;
							case '×':
								judgeQuestion = true
								break;
						}
						// 如果存在则是判断题
						if (judgeQuestion) {
							data[i].answer_option = [{
								text: '正确',
								is_right: false,
							}, {
								text: '错误',
								is_right: false,
							}];
							data[i].question_types = 3;
							answer_list = this.judgeQuestionAnswer(answer_list);
							if (!answer_list || !answer_list.length) {
								let obj = {
									idx: i + 1,
									index: 2,
									title: data[i].title,
									errText: `判断题没有正确答案！`,
								};
								analysisCommonFunction.err_alert(obj)
								return
							}
							answer_list.forEach(value => {
								data[i].answer_option[value].is_right = true;
							})
						} else {
							answer_list = answer_list.join(',');
							let blank_list = answer_list.split(/;|；/);
							let answer_options = [];
							if (Array.isArray(blank_list)) {
								blank_list.forEach((text, text_idx) => {
									if (text) {
										let t_list = [],
											bn_list = [];;
										t_list = text.split(',');
										if (Array.isArray(t_list)) {
											t_list.forEach(t => {
												bn_list.push({
													text: t
												})
											})
										}
										answer_options.push({
											text: '',
											is_right: bn_list,
										})
									}
								})
							}

							if (!Array.isArray(answer_options) || !answer_options.length) {
								xwyLib.alert(`第${i+1}题；${data[i].title}，答案出错!`)
								return
							}

							data[i].answer_option = answer_options;
							data[i].question_types = 4;
						}
					}
				}

				// 解析答案解析
				var reg_analysis = /【解析】|解析：|解析:/;
				if (opt_item.search(reg_analysis) === 0) {
					// 把解析和解析以后的内容都归到解析里面
					var analysis = '';
					for (var k = j, len_k = arr[i].length; k < len_k; k++) {
						analysis += arr[i][k];
					}
					data[i].explain_text = analysis.replace(reg_analysis, '');
				}
			}
		}

		return data;
	},

	// 把正确答案的ADC转成123
	ABC2number(arr) {
		for (var i = 0, len = arr.length; i < len; i++) {
			arr[i] = analysisCommonFunction.trim_str(arr[i]);
			if (arr[i] === 'A') arr[i] = '0';
			if (arr[i] === 'B') arr[i] = '1';
			if (arr[i] === 'C') arr[i] = '2';
			if (arr[i] === 'D') arr[i] = '3';
			if (arr[i] === 'E') arr[i] = '4';
			if (arr[i] === 'F') arr[i] = '5';
			if (arr[i] === 'G') arr[i] = '6';
			if (arr[i] === 'H') arr[i] = '7';
			if (arr[i] === 'I') arr[i] = '8';
			if (arr[i] === 'J') arr[i] = '9';
		}
		arr.sort();

		return arr;
	},

	// 判断题答案转换
	judgeQuestionAnswer(arr) {
		var str = arr.join('');
		var res = '';
		switch (str) {
			case '对':
				res = ['0']
				break;
			case '√':
				res = ['0']
				break;
			case '错':
				res = ['1']
				break;
			case '×':
				res = ['1']
				break;
			case '正确':
				res = ['0']
				break;
			case '错误':
				res = ['1']
				break;
		}

		return res
	},

	// 判断是什么题目
	questionTitleFor(str, i) {
		var opt = {
			question_types: '',
			title: '',
		};

		var splitList = ['[单选]', '[多选]', '[判断]', '[填空]', '【单选】', '【多选】', '【判断】', '【填空】']

		splitList.forEach((val, idx) => {
			if (str.includes(val)) {
				switch (idx) {
					case 0:
						opt.question_types = 1;
						break;
					case 1:
						opt.question_types = 2;
						break;
					case 2:
						opt.question_types = 3;
						break;
					case 3:
						opt.question_types = 4;
						break;
					case 4:
						opt.question_types = 1;
						break;
					case 5:
						opt.question_types = 2;
						break;
					case 6:
						opt.question_types = 3;
						break;
					case 7:
						opt.question_types = 4;
						break;
				};
				opt.title = str.split(val)[1];
			}
		})
		if (!opt.question_types) {
			xwyLib.alert(`第${i+1}题请按照提示添加类型！`)
			return
		}
		// JSON.stringify(opt)
		return opt
	},

	// 获取匹配下标的相应字母
	getMatchingABC(item) {
		var answerItem = '';
		switch (item - 1) {
			case 0:
				answerItem = 'A'
				break;
			case 1:
				answerItem = 'B'
				break;
			case 2:
				answerItem = 'C'
				break;
			case 3:
				answerItem = 'D'
				break;
			case 4:
				answerItem = 'E'
				break;
			case 5:
				answerItem = 'F'
				break;
			case 6:
				answerItem = 'G'
				break;
			case 7:
				answerItem = 'H'
				break;
			case 8:
				answerItem = 'I'
				break;
			case 9:
				answerItem = 'J'
				break;
		}
		return answerItem
	},
}


export default {
	...analysisTypeList1
}
