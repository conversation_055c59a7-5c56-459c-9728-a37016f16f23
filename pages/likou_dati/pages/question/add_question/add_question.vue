<template>
    <view class='content'>
        <view>
            <!-- 题库分类 -->
            <view v-if="category_list && category_list.length" class="edit-item">
                <view class="title">
                    题库分类
                </view>
                <view class="value">
                    <select-lay :value="category_idx" name="name" :options="category_list" :zindex="9"
                        @selectitem="select_category_list" placeholder=""></select-lay>
                </view>
            </view>


            <!-- 题目类型 -->
            <view class="edit-item">
                <view class="title">
                    题目类型
                </view>
                <view class="value">
                    <select-lay :value="question_details.question_types" name="name" :options="question_types"
                        :zindex="8" @selectitem="change_answer_option_types" placeholder=""></select-lay>
                </view>

                <view v-show="question_details.question_types === 99" class="font14 color-sub">
                    提示: 填词类型题目的题目需要插入空格或手动输入空格"(_)"才能设置答案，题目中的空格"(_)"为需要填入的答案。
                </view>
            </view>


            <!-- 题目名称 -->
            <view v-show="question_details.question_types !== 98" class="edit-item">
                <view class="title">
                    题目名称
                </view>
                <view class="value">
                    <uni-easyinput type="textarea" maxlength="-1" v-model="question_details.title"
                        placeholder="请输入题目名称" />
                </view>

                <view v-show="question_details.question_types === 99">
                    <view class="clear clearfix" style="padding-bottom: 5px;">
                        <view class="font14 color-light-primary fl" @click="insertSpace('')">
                            插入空格 (_)
                        </view>
                        <view class="font14 color-light-primary fr" @click="removeSpace('')">
                            删除空格 (_)
                        </view>
                    </view>

                    <view class="font14 color-sub">
                        提示: 填词类型题目的题目需要插入空格或手动输入空格"(_)"才能设置答案，题目中的空格"(_)"为需要填入的答案。
                    </view>
                </view>
            </view>


            <view v-show="question_details.question_types === 98">
                <view class="edit-item">
                    <view class="title f-a-c">上联</view>
                    <view class="value">
                        <uni-easyinput trim v-model="couplet.up"/>
                    </view>
                </view>
                <view class="clear clearfix p10">
                    <view class="font14 color-light-primary fl" @click="insertSpace('couplet-up')">
                        插入空格 (_)
                    </view>
                    <view class="font14 color-light-primary fr" @click="removeSpace('couplet-up')">
                        删除空格 (_)
                    </view>
                </view>
                <view class="edit-item">
                    <view class="title f-a-c">下联</view>
                    <view class="value">
                        <uni-easyinput trim v-model="couplet.down"/>
                    </view>
                </view>
                <view class="clear clearfix p10">
                    <view class="font14 color-light-primary fl" @click="insertSpace('couplet-down')">
                        插入空格 (_)
                    </view>
                    <view class="font14 color-light-primary fr" @click="removeSpace('couplet-down')">
                        删除空格 (_)
                    </view>
                </view>

                <view class="font14 color-sub p10 pt0">
                    提示: 上下联需要插入空格或手动输入空格"(_)"才能设置答案，空格"(_)"为需要填入的答案。
                </view>
            </view>


            
            <!-- 题目排序 -->
            <view class="edit-item">
                <view class="title f-a-c">
                    题目排序
                    <i class="iconfont memo_text ml_5 font_5" @click.stop="show_modal('题目排序,数字越小排序越前')">&#xe65a;</i>
                </view>
                <view class="value">
                    <uni-easyinput type="number" v-model="question_details.sort_num" placeholder="题目排序,数字越小排序越前" />
                </view>
            </view>

            <template>
                <!--对联的答案选项-->
                <template v-if="question_details.question_types === 98">
                    <view class="edit-item">
                        <view class="title f-j-sb-a-c">上联空格选项</view>
                        <view class="value">
                            <view class="item-content font_9">
                                <view class="blank-box gray_bdrsolid p_5 mb1"
                                      v-for="(item,index) in couplet_answer_option.up" :key="index">
                                    <view class="blank-title f-j-sb-a-c">
                                        空格{{index+1}}参考答案（匹配任意一个都能得分）
                                    </view>
                                    <view class="item f-a-c" v-for="(itm,idx) in item" :key="idx">
                                        <i v-if="item.length > 1" class="iconfont f-j-a-c red_color"
                                            @click.stop="removeCoupletOption('up', index, idx)">&#xe669;</i>
                                        <uni-easyinput class="input" trim :value="itm.text" maxlength="1"
                                                       @input="coupletOptionTextChange($event, 'up', index, idx)"/>
                                    </view>
                                    <view class="item f" style="padding-bottom: 0;"
                                        @click.stop="addCoupletOption('up', index)">
                                        <i class="iconfont f-j-a-c blue_color">&#xe643;</i>
                                        <view class="add-text blue_color">
                                            添加一个参考答案
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="edit-item">
                        <view class="title f-j-sb-a-c">下联空格选项</view>
                        <view class="value">
                            <view class="item-content font_9">
                                <view class="blank-box gray_bdrsolid p_5 mb1"
                                      v-for="(item,index) in couplet_answer_option.down" :key="index">
                                    <view class="blank-title f-j-sb-a-c">
                                        空格{{index+1}}参考答案（匹配任意一个都能得分）
                                    </view>
                                    <view class="item f-a-c" v-for="(itm,idx) in item" :key="idx">
                                        <i v-if="idx === 0" class="iconfont f-j-a-c gray_color">&#xe669;</i>
                                        <i v-if="idx > 0" class="iconfont f-j-a-c red_color"
                                            @click.stop="removeCoupletOption('down', index, idx)">&#xe669;</i>
                                        <uni-easyinput class="input" trim :value="itm.text" maxlength="1"
                                                       @input="coupletOptionTextChange($event, 'down', index, idx)"/>
                                    </view>
                                    <view class="item f" style="padding-bottom: 0;"
                                        @click.stop="addCoupletOption('down', index)">
                                        <i class="iconfont f-j-a-c blue_color">&#xe643;</i>
                                        <view class="add-text blue_color">
                                            添加一个参考答案
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </template>


                <view v-else class="edit-item">
                    <view class="title f-j-sb-a-c">
                        <text v-if="question_details.question_types != 4 && question_details.question_types !== 99">候选项</text>
                        <text :class="question_details.question_types != 4 && question_details.question_types !== 99 ? 'memo_text': ''">正确答案</text>
                    </view>
                    <view class="value">
                        <view
                            v-if="question_details.question_types == 1 || question_details.question_types == 2 || question_details.question_types == 3"
                            class="item-content font_9">
                            <view class="item f" v-for="(item,index) in answer_option" :key="index">
                                <i v-if="index > 0" class="iconfont f-j-a-c red_color"
                                    @click.stop="cancel_answer_option(index)">&#xe669;</i>
                                <i v-if="index == 0" class="iconfont f-j-a-c gray_color">&#xe669;</i>
                                <uni-easyinput class="input" type="textarea" maxlength="-1" :clearable="false"
                                    v-model="item.text" :disabled="question_details.question_types == 3"
                                    placeholder="输入选项内容" />
                                <view class="select-box f-j-a-c" @click.stop="change_isRight_active(index)">
                                    <view class="select-item f-j-a-c" :class="item.is_right?'blue_bdrsolid':''">
                                        <i v-if="item.is_right" class="iconfont blue_color">&#xe6ad;</i>
                                    </view>
                                </view>
                            </view>
                            <view v-if="question_details.question_types != 3" class="item f"
                                @click.stop="add_answer_option">
                                <i class="iconfont f-j-a-c blue_color">&#xe643;</i>
                                <view class="add-text blue_color">
                                    添加选项
                                </view>
                            </view>
                        </view>

                        <!-- 填空题 -->
                        <view v-if="question_details.question_types == 4 || question_details.question_types == 99" class="item-content font_9">
                            <view class="blank-box gray_bdrsolid p_5 mb1" v-for="(item,index) in answer_option"
                                :key="index">
                                <view class="blank-title f-j-sb-a-c">
                                    空格{{index+1}}参考答案（匹配任意一个都能得分）
                                    <i v-if="index > 0 && question_details.question_types === 4"
                                       class="iconfont red_color"
                                       @click.stop="cancel_blankanswer_option(index)">&#xe625;</i>
                                </view>
                                <view class="item f-a-c" v-for="(itm,idx) in item.is_right" :key="idx">
                                    <i v-if="idx == 0" class="iconfont f-j-a-c gray_color">&#xe669;</i>
                                    <i v-if="idx > 0" class="iconfont f-j-a-c red_color"
                                        @click.stop="cancel_blank_answer_option(index,idx)">&#xe669;</i>
                                    <uni-easyinput class="input" trim v-model="itm.text" maxlength="30"
                                                   :placeholder="'参考答案' + (question_details.question_types === 99 ? '(填词类型答案只能输入1个字)' : '')" />
                                </view>
                                <view class="item f" style="padding-bottom: 0;"
                                    @click.stop="add_blank_item_answer_option(index)">
                                    <i class="iconfont f-j-a-c blue_color">&#xe643;</i>
                                    <view class="add-text blue_color">
                                        添加一个参考答案
                                    </view>
                                </view>
                            </view>
                            <view v-if="question_details.question_types === 4" class="item f"
                                  @click.stop="add_blank_answer_option">
                                <i class="iconfont f-j-a-c blue_color">&#xe643;</i>
                                <view class="add-text blue_color">
                                    添加一个填空项
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </template>
            
            <view v-show="question_details.question_types !== 98 && question_details.question_types !== 99">
                <!-- 题目解析 -->
                <view class="edit-item">
                    <view class="title">
                        题目解析
                    </view>
                    <view class="value">
                        <uni-easyinput type="textarea" maxlength="-1" v-model="question_details.explain_text"
                                       placeholder="请输入题目解析" />
                    </view>
                </view>
                <!-- 题目分数 -->
                <view class="edit-item">
                    <view class="title">
                        题目分数
                    </view>
                    <view class="value">
                        <uni-easyinput type="digit" v-model="question_details.score" placeholder="请输入题目分数" />
                    </view>
                </view>

                <!-- 考卷图片列表 -->
                <view class="edit-item">
                    <view class="title">
                        图片上传
                    </view>
                    <view class="value f-w">
                        <view v-if="question_details.pic_list && question_details.pic_list.length"
                              class="upload-img-box pic_list f" style="margin-bottom:15px;height:auto;max-height:none;"
                              :style="{'margin-right':(index+1) % 3 == 0?'':'15px'}"
                              v-for="(item,index) in question_details.pic_list" :key="index">
                            <image :src="item.url" mode="widthFix" @click.stop="previewImage(index)">
                            </image>
                            <i class="iconfont f-j-a-c" @click.stop="cancel_pic_list(index)">&#xe692;</i>
                        </view>
                        <view v-if="!question_details.pic_list || question_details.pic_list.length < 6"
                              class="updata-icon-box f-j-a-c" @click.stop="skip_default_img_depot('pic_list')">
                            <i class="add-icon iconfont">&#xe600;</i>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <bottom-btn :btnList="btn_list" @save="save_question" @cancel="return_previous_page"></bottom-btn>
    </view>
</template>

<script>
    let app = getApp();

	import bottomBtn from "@/pages/likou_dati/components/bottom-btn/bottom-btn.vue"
	import selectLay from "@/pages/likou_dati/uni_modules/select-lay/components/select-lay/select-lay.vue"

    export default {
		components: {
			bottomBtn,
			selectLay
		},
        data() {
            return {
                params: {},
                question_details: {
                    title: '',
                    question_types: 1,
                    explain_text: '',
                    score: 0,
                    sort_num: 100,
                },

                question_types: [{
                        "label": "单选",
                        "value": 1
                    },
                    {
                        "label": "多选",
                        "value": 2
                    },
                    {
                        "label": "判断",
                        "value": 3
                    },
                    {
                        "label": "填空",
                        "value": 4
                    },
                    // {
                    // 	"label": "简答",
                    // 	"value": 5
                    // }
                    {
                        "label": "填词",
                        "value": 99
                    },
                    {
                        "label": "填对联",
                        "value": 98
                    },
                ],

                answer_option_title: '单选',
                answer_option: [{
                    text: '',
                    is_right: false,
                }],

                category_list: [], // 分类列表
                category_idx: 0, // 分类下标


                btn_list: [{
                        text: '取消',
                        clickFun: 'cancel',
                        cancel: 1,
                    },
                    {
                        text: '保存',
                        clickFun: 'save'
                    }
                ],

                // 对联的上下联
                couplet: {
                    up: '',
                    down: ''
                },
                // 填词上下联答案候选
                couplet_answer_option: {
                    up: [],
                    down: []
                }
            }
        },
        
        watch: {
              'question_details.title': function() {
                  this.set99options()
              },
            couplet: {
                handler: function(val) {
                    this.set98options(val)
                },
                deep: true
            }
        },
        
        onLoad(options) {
            // console.log('获取的参数options', options);
            this.params = options
            this.xwyLib.setBarTitle(options.question_id ? '修改题目' : '添加题目')
            this.xwyLib.init(() => {
                this.userInfo = app.globalData.userInfo;
                this.init();
            })
        },

        onShow() {
            if (app.globalData.upload_img) {
                app.globalData.upload_img = null;
                let depot = uni.getStorageSync('image_house') ? JSON.parse(uni.getStorageSync('image_house')) : {};
                if (depot.picName != 'pic_list') {
                    this.$set(this.question_details, depot.picName, depot.url);
                } else {
                    let pic_list = [];
                    if (this.question_details.pic_list) {
                        pic_list = this.question_details.pic_list;
                    }
                    pic_list.push({
                        url: depot.url
                    })
                    this.$set(this.question_details, depot.picName, pic_list);
                }
            }
        },

        methods: {
            insertSpace(type) {
                if (type) {
                    if (type === 'couplet-up') {
                        this.couplet.up += '(_)'
                    } else if (type === 'couplet-down') {
                        this.couplet.down += '(_)'
                    }
                    return
                }
                this.question_details.title += '(_)'
            },
            
            removeSpace(type) {
                const checkAndRemove = arr => {
                    const len = arr.length
                    if (arr[len - 1] === ')' && arr[len - 2] === '_' && arr[len - 3] === '(') {
                        return arr.slice(0, len - 3)
                    }
                    return arr
                }

                if (type) {
                    if (type === 'couplet-up') {
                        this.couplet.up = checkAndRemove(this.couplet.up)
                    } else if (type === 'couplet-down') {
                        this.couplet.down = checkAndRemove(this.couplet.down)
                    }
                    return
                }

                this.question_details.title = checkAndRemove(this.question_details.title)
            },

            set98options(val) {
                if (this.question_details.question_types !== 98) return
                const {up, down} = val
                this.setCoupletAnswerOption('up', up)
                this.setCoupletAnswerOption('down', down)
            },
            setCoupletAnswerOption(type, value) {
                const spaceCount = (value.match(/\(_\)/g) || []).length
                const options = this.couplet_answer_option[type]
                if (options.length > spaceCount) {
                    this.couplet_answer_option[type] = options.slice(0, spaceCount)
                    return
                }
                for (let i = 0; i < spaceCount - options.length; i++) {
                    this.couplet_answer_option[type].push([{text: ''}])
                }
            },

            removeCoupletOption(type, index, idx) {
                this.couplet_answer_option[type][index].splice(idx, 1)
            },

            addCoupletOption(type, index) {
                this.couplet_answer_option[type][index].push({text: ''})
            },

            coupletOptionTextChange(value, key, index, idx) {
                this.$set(this.couplet_answer_option[key][index][idx], 'text', value)
            },


            set99options() {
                if (this.question_details.question_types !== 99) return
                
                const title = this.question_details.title
                const spaceCount = (title.match(/\(_\)/g) || []).length

                const options = this.answer_option
                if (options.length > spaceCount) {
                    this.answer_option = options.slice(0, spaceCount)
                    return
                }

                for (let i = 0; i < spaceCount - options.length; i++) {
                    this.answer_option.push({
                        text: '',
                        is_right: [{text: ''}]
                    })
                }
            },
            
            async init() {
                // this.xwyLib.create_InterstitialAd(24);
                if (this.params.question_id) {
                    this.xwyLib.showLoading();
                    await this.get_question_details();
                }
                this.get_category_list();
            },
            // 获取题目详情
            async get_question_details() {
                this.xwyLib.get_question_details({
                    id: this.params.question_id,
                    success: res => {
                        if (this.xwyLib.isJSON(res.answer_option)) {
                            this.answer_option = JSON.parse(res.answer_option);
                        } else {
                            this.answer_option = res.answer_option;
                        }
                        
                        if (this.answer_option?.[0]?.fill_words) res.question_types = 99
                        if (this.answer_option?.[0]?.fill_couplet) {
                            res.question_types = 98
                            this.getCoupletSet(res.title, this.answer_option)
                        }

                        this.question_details = {
                            title: res.title,
                            question_types: res.question_types,
                            explain_text: res.explain_text,
                            score: res.score,
                            pic_list: res.pic_list,
                            sort_num: res.sort_num,
                            category_id: res.category_id,
                        };
                    },
                    fail: res => {
                        this.xwyLib.showModal(res.info, {
                            success: response => {
                                this.xwyLib.back_previous_page();
                            }
                        })
                    }
                })
            },

            getCoupletSet(title, answer_option) {
                const [up_title, down_title] = title.split(' ')
                this.couplet.up = up_title
                this.couplet.down = down_title

                const reg = /\(_\)/g
                const up_space_count = (up_title.match(reg) || []).length
                const down_space_count = (down_title.match(reg) || []).length
                this.couplet_answer_option.up = answer_option.slice(0, up_space_count).map(item => item.is_right)
                this.couplet_answer_option.down = answer_option.slice(up_space_count, up_space_count + down_space_count).map(item => item.is_right)
            },

            // 获取题库分类
            async get_category_list() {
                this.xwyLib.showLoading();
                let opt = {
                    types: 4,
                    my_self: 1,
                    perpage: 100,
                    before: res => {
                        this.category_list = [];
                    },
                    success: res => {
                        let list = [...res.data],
                            opt = {};

                        Object.assign(opt, list[0])
                        opt.name = '全部';
                        opt.label = '全部';
                        opt.category_id = '';
                        opt.sort_num = 1;
                        list.unshift(opt)

                        list.forEach((val, idx) => {
                            val.label = val.name;
                            val.value = idx;
                            if (this.params.question_id && val.category_id == this.question_details
                                .category_id) {
                                this.category_idx = idx;
                            }
                        })

                        this.category_list = list;
                        if (this.params.types_id) {
                            list.forEach((val, idx) => {
                                if (val.category_id == this.params.types_id) this.category_idx = idx
                            })
                        }
                    },
                };
                this.xwyLib.get_category_list(opt);
            },

            select_category_list(index, item) {
                if (index >= 0) {
                    this.category_idx = item.value;
                } else {
                    this.category_idx = 0;
                }
            },


            // 改变答题选项类型
            change_answer_option_types(index, item) {
                if (index >= 0) {
                    this.question_details.question_types = item.value;
                } else {
                    this.question_details.question_types = 0;
                }

                let title = '';
                switch (Number(this.question_details.question_types)) {
                    case 1:
                        title = '单选';
                        this.answer_option.forEach(val => {
                            val.is_right = false;
                        })
                        this.answer_option = [{
                            text: '',
                            is_right: false,
                        }]
                        break;
                    case 2:
                        title = '多选';
                        this.answer_option = [{
                            text: '',
                            is_right: false,
                        }]
                        break;
                    case 3:
                        title = '判断';
                        this.answer_option = [{
                            text: '正确',
                            is_right: false,
                        }, {
                            text: '错误',
                            is_right: false,
                        }]
                        break;
                    case 4:
                        title = '填空';
                        this.answer_option = [{
                                text: '',
                                is_right: [{
                                    text: ''
                                }]
                            }

                        ]
                        break;
                    case 5:
                        title = '简答'
                        break;
                    case 98:
                        title = '填对联'
                        this.answer_option = []
                        this.question_details.title = ''
                        this.couplet = {up: '', down: ''}
                        this.couplet_answer_option = {up: [], down: []}
                        break;
                    case 99:
                        title = '填词'
                        this.answer_option = []
                        this.set99options()
                        break;
                }
                this.answer_option_title = title;
            },
            // 改变正确答案选中
            change_isRight_active(e) {
                // 判断是否选择了题目类型
                let types = this.question_details.question_types;
                if (!types) {
                    uni.showModal({
                        content: `请先选择题目类型！`,
                    })
                    return
                }

                let answer_option = this.answer_option;
                switch (Number(types)) {
                    // 单选
                    case 1:
                        answer_option.forEach((val, idx) => {
                            val.is_right = false;
                            if (idx == e) {
                                val.is_right = true;
                            }
                        })
                        break;
                        // 多选
                    case 2:
                        this.$set(this.answer_option[e], 'is_right', !answer_option[e].is_right);
                        break;
                        // 单选
                    case 3:
                        answer_option.forEach((val, idx) => {
                            val.is_right = false;
                            if (idx == e) {
                                val.is_right = true;
                            }
                        })
                        break;
                }

            },
            // 删除答题选项
            cancel_answer_option(e) {
                let answer_option = this.answer_option,
                    types = this.question_details.question_types;
                if (types == 3) {
                    uni.showModal({
                        content: `判断题不可修改选项！`,
                    })
                    return
                }
                if (answer_option.length == 1) {
                    uni.showModal({
                        content: `至少保留一个选项！`,
                    })
                    return
                }
                answer_option.splice(e, 1);
            },

            // 添加答题选项
            add_answer_option() {
                this.answer_option.push({
                    text: '',
                    is_right: false,
                })
            },

            add_blank_answer_option() {
                this.answer_option.push({
                    text: '',
                    is_right: [{
                        text: ''
                    }]
                })
            },

            add_blank_item_answer_option(index) {
                this.answer_option[index].is_right.push({
                    text: ''
                })
            },

            // 删除空格
            cancel_blankanswer_option(index) {
                this.answer_option.splice(index, 1);
                this.$set(this.answer_option, this.answer_option);
            },
            // 删除参考答案
            cancel_blank_answer_option(index, idx) {
                this.answer_option[index].is_right.splice(idx, 1);
                this.$set(this.answer_option[index], this.answer_option[index])
            },

            // 返回上一页
            return_previous_page() {
                uni.navigateBack({
                    delta: 1,
                })
            },

            // 判断题目参数
            judge_question() {
                let obj = this.question_details;
                if (!obj.title) {
                    uni.showModal({
                        content: `请输入题目标题！`
                    })
                    return false
                }
                if (!obj.question_types) {
                    uni.showModal({
                        content: `请选择题目类型！`
                    })
                    return false
                }
                // 判断选项内容是否填写了
                if (obj.question_types == 1 || obj.question_types == 2 || obj.question_types == 3) {
                    let answer_option = this.answer_option,
                        is_input = true;
                    for (let i = 0; i < answer_option.length; i++) {
                        if (!answer_option[i].text) {
                            uni.showModal({
                                content: `选项${i+1}未填写内容！`,
                            })
                            is_input = false;
                            break;
                        }
                    }
                    // 如果都填写了答案则判断是否选择了正确答案
                    if (!is_input) {
                        return
                    }
                    let is_right = false;
                    for (let i = 0; i < answer_option.length; i++) {
                        if (answer_option[i].is_right) {
                            is_right = true;
                        }
                    }
                    if (!is_right) {
                        uni.showModal({
                            content: `请选择题目的正确答案！`
                        })
                        return false
                    }
                }

                if (obj.question_types == 4) {
                    let answer_option = this.answer_option;
                    let isTure = true;
                    answer_option.forEach((val, idx) => {
                        val.is_right.forEach((v, i) => {
                            if (!v.text) {
                                isTure = false;
                            }
                        })
                    })

                    if (!isTure) {
                        uni.showModal({
                            content: `请输入参考答案！`
                        })
                        return false
                    }
                }
                
                if (obj.question_types === 99) {
                    if (!this.fillWordsAnswerOptionsCheck()) return false
                }

                return true
            },

            fillWordsAnswerOptionsCheck() {
                const options = this.answer_option
                
                if (!options.length) {
                    this.$uni.showToast('题目请插入空格')
                    return false
                }
                
                for (let i = 0, len = options.length; i < len; i++) {
                    const right = options[i].is_right
                    if (!right.length) {
                        this.$uni.showToast(`请添加空格${i + 1}的参考答案`)
                        return false
                    }
                    
                    for (let j = 0, len = right.length; j < len; j++) {
                        const text = right[j].text
                        if (!text) {
                            this.$uni.showToast(`请填写空格${i + 1}的参考答案`)
                            return false
                        }
                        if (text.length > 1) {
                            this.$uni.showModal(`填词类型题目的空格参考答案只能设置为1个字，空格${i + 1}的参考答案【${text}】字数超过1个字，请重新设置`)
                            return false
                        }
                    }
                }
                
                return true
            },

            coupletCheck() {
                if (!this.coupletTitleCheck()) return false
                if (!this.coupletOptionsCheck()) return false
                return true
            },
            coupletTitleCheck() {
                const {up, down} = this.couplet
                if (!up) {
                    this.$uni.showToast('请填写上联')
                    return false
                }
                if (!down) {
                    this.$uni.showToast('请填写下联')
                    return false
                }

                const reg = /\(_\)/g

                const up_space_count = (up.match(reg) || []).length
                if (up_space_count === 0) {
                    this.$uni.showToast('上联请插入空格')
                    return false
                }
                const down_space_count = (down.match(reg) || []).length
                if (down_space_count === 0) {
                    this.$uni.showToast('下联请插入空格')
                    return false
                }

                const up_length = up.replace(reg, '').length + up_space_count
                if (up_length > 10) {
                    this.$uni.showToast('上联最多10字')
                    return false
                }
                const down_length = down.replace(reg, '').length + down_space_count
                if (down_length > 10) {
                    this.$uni.showToast('下联最多10字')
                    return false
                }
                if (up_length !== down_length) {
                    this.$uni.showToast('上下联字数不一致')
                    return false
                }

                return true
            },
            coupletOptionsCheck() {
                const {up, down} = this.couplet_answer_option
                for (let i = 0, len = up.length; i < len; i++) {
                    if (up[i].some(item => !item.text)) {
                        this.$uni.showToast(`上联空格选项${i+1}未填写完`)
                        return false
                    }
                }
                for (let i = 0, len = down.length; i < len; i++) {
                    if (down[i].some(item => !item.text)) {
                        this.$uni.showToast(`下联空格选项${i+1}未填写完`)
                        return false
                    }
                }

                return true
            },
            

            save_question() {
                this.save_question_details();
            },

            // 保存考题
            async save_question_details() {
                if (this.question_details.question_types === 98) {
                    // 检查对联设置
                    if (!this.coupletCheck()) return
                } else {
                    // 判断题目
                    if (!this.judge_question()) return
                }

                let params = this.question_details;

                let answer_option = JSON.parse(JSON.stringify(this.answer_option))
                if (params.question_types === 98) {
                    params.title = `${this.couplet.up} ${this.couplet.down}`

                    const up = this.couplet_answer_option.up.map(item => ({
                        text: '',
                        is_right: item
                    }))
                    const down = this.couplet_answer_option.down.map(item => ({
                        text: '',
                        is_right: item
                    }))
                    answer_option = [...up, ...down]

                    params.question_types = 4
                    answer_option[0].fill_couplet = 1
                }
                answer_option.forEach(item => {
                    if (item.hasOwnProperty('fill_words')) delete item.fill_words
                })
                
                if (params.question_types === 99) {
                    params.question_types = 4
                    answer_option[0].fill_words = 1
                }
                
                params.score = parseFloat(params.score)
                params.shopid = parseInt(this.xwyLib.ext_conf.who);
                params.answer_option = this.base64.baseEncode(JSON.stringify(answer_option));
                params.sort_num = parseInt(this.question_details.sort_num) || 100;
                if (params.pic_list?.length) params.pic_list = this.xwyLib.jsonEncode(params.pic_list);

                let category_id = '';
                if (this.category_list && this.category_list.length) category_id = this.category_list[this
                    .category_idx].category_id;
                if (category_id) params.category_id = category_id;
                if (this.params.question_id) params.id = this.params.question_id;

                let obj = {
                    data: params,
                    url: `front.flat.exam.questionBank/create_question_bank`,
                    success: res => {
                        console.log('添加或者修改题目', res);
                        // exam_id存在表示需要添加到考题里面
                        if (this.params.exam_id) {
                            this.add_question_to_exam(res.data.id);
                            return
                        }

                        if (this.params.question_id) {
                            app.globalData.questionUpdateID = this.params.question_id;
                            this.xwyLib.back_previous_page();
                            return
                        }

                        this.xwyLib.showToast('添加成功！');
                        this.question_details = {
                                title: '',
                                question_types: 1,
                                explain_text: '',
                                score: 0,
                            }

                            this.answer_option_title = '单选'
                            this.answer_option = [{
                                text: '',
                                is_right: false,
                            }]

                        app.globalData.update = 1;
                    }
                };
                this.xwyLib.ajax(obj);
            },

            // 添加题目到考卷
            async add_question_to_exam(id) {
                let list = [{
                    question_id: id,
                    score: parseFloat(this.question_details.score),
                    sort_num: this.question_details.sort_num,
                }]
                let params = {
                    exam_id: this.params.exam_id,
                    question_list: this.xwyLib.jsonEncode(list),
                };
                let obj = {
                    data: params,
                    url: `front.flat.exam.admin/send_questions_to_exam`,
                    success: res => {
                        console.log('添加或者修改题目', res);
                        uni.hideLoading();
                        if (res.status != 1) {
                            uni.showModal({
                                content: res.info,
                            })
                            return
                        }
                        uni.showModal({
                            content: res.info,
                            success: response => {
                                app.globalData.updateExamQuestion = 1
                                this.xwyLib.back_previous_page()
                            }
                        })
                    }
                };
                this.xwyLib.ajax(obj);

            },
            show_modal(text) {
                uni.showModal({
                    content: text,
                    showCancel: false,
                })
            },

            // 跳转到默认图库
            skip_default_img_depot(text) {
                let url = `/pages/likou_dati/pages/user/image_house/image_house`;
                if (text) {
                    url += `?picName=${text}`
                }
                this.xwyLib.routeJump(url)
            },

            // 上传轮播图
            upload_pic_list() {
                let length = 6;
                if (this.question_details.pic_list && this.question_details.pic_list.length) {
                    length = 6 - parseInt(this.question_details.pic_list.length);
                }
                this.xwyLib.choose_img({
                    count: length,
                    success: res => {
                        // console.log('上传轮播图', res);
                        let list = [];
                        if (this.question_details.pic_list) {
                            list = [...this.question_details.pic_list];
                        }
                        this.$set(this.question_details, 'pic_list', [...list, ...res])
                    }
                })
            },

            // 删除轮播图
            cancel_pic_list(e) {
                let pic_list = this.question_details.pic_list;
                pic_list.splice(e, 1);
                this.$set(this.question_details, 'pic_list', pic_list);
            },

            previewImage(e) {
                let list = [];
                if (this.question_details.pic_list) {
                    this.question_details.pic_list.forEach(val => {
                        list.push(val.url);
                    })
                }

                uni.previewImage({
                    current: e,
                    urls: list,
                })
            },


        }
    }
</script>

<style scoped lang='scss'>
	@import url('@/pages/likou_dati/common/css/common.scss');
	@import url('@/pages/likou_dati/common/css/iconfont.css');
	@import url('@/pages/likou_dati/common/css/public.css');

    .content {
        min-height: 100vh;
        padding-bottom: 70px;

        .item {
            padding-bottom: 1rem;

            .iconfont {
                width: 2.5rem;
                font-size: 1.5rem;
            }

            .input {
                width: calc(100% - 5rem);
            }

            .add-text {
                font-size: 1rem;
            }
        }

        .blank-box {
            width: 100%;
            border-radius: .2rem;

            .blank-title {
                position: relative;
                width: 100%;
                padding-bottom: .5rem;

                .iconfont {
                    font-size: 1.1rem;
                }
            }
        }

    }
</style>
