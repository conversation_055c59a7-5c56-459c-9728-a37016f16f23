<template>
    <view class="content">
        <!-- 我的分类 -->
        <view class="item-box">
            <!-- 标题 -->
            <view class="item-title f-j-sb-a-c">
                <view class="left">
                    <text>我的题库分类</text>
                    <text class="gray_color" style="font-size: .9rem;">已创建{{total}}个分类</text>
                </view>
                <view class="blue_color" @click.stop="open_modal(false)">
                    创建分类
                </view>
            </view>
            <!-- 题库分类列表 -->
            <view class="exam-box">
                <view class="exam-item" v-for="(item,index) in category_list">
                    <view class="exam-top gray_bdrbdashed f">
                        <!-- <view class="img">
							<i v-if="!item.logo" class="iconfont gray_color">&#xe791;</i>
							<image v-if="item.logo" :src="item.logo" mode="aspectFill"/>
						</view> -->
                        <view class="text-box">
                            <view class="name">
                                <text>{{index+1}}、</text>
                                <text>{{item.name}}</text>
                            </view>
                        </view>
                    </view>
                    <view class="exam-bottom pt_5 f-j-sb-a-c">
                        <view class="btn memo_text f-a-c" @click.stop="cancel_category(item.category_id)">
                            <i class="iconfont pr_3 f-j-a-c">&#xe625;</i>
                            <view class="text f-j-a-c">
                                删除
                            </view>
                        </view>
                        <view class="btn memo_text f-a-c" @click.stop="open_modal(item)">
                            <i class="iconfont pr_3 f-j-a-c">&#xe633;</i>
                            <view class="text f-j-a-c">
                                编辑
                            </view>
                        </view>
                        <view v-if="params.select" class="btn memo_text f-a-c" @click="selectCategory(item)">
                            <view class="text f-j-a-c color-light-primary">选择</view>
                        </view>
                    </view>
                </view>
            </view>
            <view v-if="err_text" class="err_text">
                {{err_text}}
            </view>
        </view>

        <!-- 表单弹框 -->
        <view v-if="show_modal" class="fixed_modal">
            <view class="fixed_modal_content">
                <view class="fixed-item f-a-c">
                    <view class="left">
                        分类名称
                    </view>
                    <view class="right">
                        <uni-easyinput v-model="category_obj.name" placeholder="请输入分类名称" />
                    </view>
                </view>
                <view class="fixed-item f-a-c">
                    <view class="left">
                        分类排序
                    </view>
                    <view class="right">
                        <uni-easyinput type="number" v-model="category_obj.sort_num" placeholder="排序，数字越小排在越前" />
                    </view>
                </view>
                <view class="btn-box f-j-sb-a-c">
                    <view class="red_bdrsolid gray_color gray_bdrsolid btn" @click.stop="close_modal">
                        取消
                    </view>
                    <view class="blue_bdrsolid blue_bgcolor btn" @click.stop="add_or_modif_exam">
                        {{modif_id?'修改':'创建'}}
                    </view>
                </view>
            </view>
        </view>

        <emptyData v-if="show_empty"></emptyData>
    </view>
</template>

<script>
    let app = getApp();

	import emptyData from '@/pages/likou_dati/components/emptyData/emptyData.vue'

    export default {
		components: {
			emptyData
		},
        data() {
            return {
                params: {},
                userInfo: {},
                show_empty: false,

                category_list: [], // 考题分类
                total: 0, // 总共多少分类
                is_lastpage: false, // 是否最后一页
                page: 1,

                show_modal: false, // 显示添加或者修改模态框

                modif_id: '',

                category_obj: {
                    name: '',
                    logo: '',
                    sort_num: 10,
                    types: 3,
                },

                err_text: '',
            }
        },
        onLoad(options) {
            this.xwyLib.showLoading();
            this.params = options;
            this.xwyLib.setBarTitle('题库分类')
            this.xwyLib.init(() => {
                this.userInfo = app.globalData.userInfo;
                this.init();
            })
        },
        onShow() {
            if (app.globalData.update_exam == 1) {
                app.globalData.update_exam = null;
                this.get_category_list();
            }
        },
        methods: {
            async init() {
                await this.get_category_list();
            },

            // 获取题库分类
            async get_category_list(page = 1) {
                this.xwyLib.showLoading();
                let opt = {
                    page,
                    types: 4,
                    my_self: 1,
                    before: res => {
                        this.err_text = '';
                        if (page == 1) {
                            this.category_list = [];
                            this.is_lastpage = false;
                            this.show_empty = false;
                        }
                    },
                    success: res => {
                        console.log('res', res);
                        let list = [...res.data];

                        if (page > 1) {
                            list = [...this.category_list, ...res.data];

                            if (res.data.length < 10) {
                                this.err_text = '已经到底了，别再拉了！'
                            }
                        }

                        this.is_lastpage = res.is_lastpage;
                        this.total = res.total;
                        this.category_list = list;
                        this.page = page + 1;
                    },
                    fail: res => {
                        if (page == 1) {
                            this.show_empty = true;
                            return
                        }
                        this.err_text = res.info;
                    }
                };
                this.xwyLib.get_category_list(opt);
            },

            open_modal(e) {
                if (e) {
                    this.category_obj.name = e.name;
                    this.category_obj.logo = e.logo;
                    this.category_obj.sort_num = e.sort_num;
                    this.modif_id = e.category_id;
                }
                this.show_modal = true;
            },

            close_modal() {
                this.show_modal = false;
                this.modif_id = '';
                this.category_obj.name = '';
                this.category_obj.logo = '';
                this.category_obj.sort_num = 10;
            },

            // 添加或者修改分类
            add_or_modif_exam() {
                if (!this.category_obj.name) {
                    this.xwyLib.alert('请输入分类名称！')
                    return
                }
                this.add_post();
            },

            // 添加或者修改分类
            async add_post() {
                let params = {
                    name: this.category_obj.name,
                    logo: this.category_obj.logo,
                    sort_num: parseInt(this.category_obj.sort_num),
                    types: 4,
                };

                if (this.modif_id) params.id = this.modif_id;

                let obj = {
                    data: params,
                    url: `front.user.category/create_category`,
                    success: res => {
                        // console.log(`${this.modif_id?'修改':'添加'}分类`,res);
                        uni.hideLoading();
                        if (res.status != 1) {
                            this.xwyLib.showToast(res.info)
                            return
                        }
                        this.xwyLib.showModal(res.info, {
                            success: res => {
                                this.close_modal();
                                this.get_category_list();
                            }
                        })

                    }
                };
                this.xwyLib.ajax(obj);
            },

            // 删除分类
            cancel_category(id) {
                this.xwyLib.showModal('是否删除改分类？', {
                    confirmText: '删除',
                    success: res => {
                        if (res.confirm) this.cancel_category_post(id);
                    }
                })
            },

            async cancel_category_post(id) {
                this.xwyLib.showLoading();
                let params = {
                    ids: id,
                };
                let obj = {
                    data: params,
                    url: `front.user.category/category_del`,
                    success: res => {
                        uni.hideLoading();
                        if (res.status != 1) {
                            this.xwyLib.alert(res.info);
                            return
                        }
                        this.xwyLib.showModal(res.info, {
                            success: response => {
                                this.get_category_list();
                            }
                        })
                    }
                };

                this.xwyLib.ajax(obj);
            },


            selectCategory(item) {
                this.getOpenerEventChannel().emit('selectCategory', {
                    id: item.category_id,
                    name: item.name
                })
                this.$uni.navigateBack()
            }
        },

        onReachBottom() {
            if (this.is_lastpage) return
            this.get_category_list(this.page);
        },

    }
</script>

<style scoped lang="scss">
	@import url('@/pages/likou_dati/common/css/common.scss');
	@import url('@/pages/likou_dati/common/css/iconfont.css');
	@import url('@/pages/likou_dati/common/css/public.css');

    .content {
        position: relative;
        background-color: #f8f8f8;
        min-height: 100vh;
        padding-bottom: 60px;

        .item-box {
            width: 100%;

            .item-title {
                position: sticky;
                left: 0;
                top: 0;
                width: 100%;
                background-color: #fff;

                >view {
                    padding: 1rem;
                }

                .left {
                    >text:last-child {
                        padding-left: .5rem;
                    }
                }
            }

            .exam-box {
                width: 100%;
                padding: 1rem;

                .exam-item {
                    width: 100%;
                    background-color: #fff;
                    border-radius: .2rem;
                    padding: 0 .5rem;
                    margin-bottom: 1rem;

                    .exam-top {
                        width: 100%;
                        padding: 1rem 0;

                        .img {
                            width: 4rem;

                            .iconfont {
                                font-size: 4rem;
                            }

                            image {
                                width: 100%;
                                height: 4rem;
                                border-radius: .2rem;
                            }
                        }

                        .text-box {
                            width: calc(100% - 4rem);
                        }
                    }

                    .exam-bottom {
                        width: 100%;

                        .btn {
                            border-radius: 10rem;
                            padding: .2rem 0;
                        }
                    }
                }
            }
        }

        .fixed_modal {

            .fixed-item {
                width: 100%;
                margin-bottom: 1rem;

                .left {
                    width: 5rem;
                }

                .right {
                    width: calc(100% - 5rem);

                    .iconfont {
                        font-size: 3rem;
                    }

                    image {
                        width: 4rem;
                        height: 4rem;
                        border-radius: .2rem;
                    }
                }
            }

            .btn-box {
                width: 100%;

                .btn {
                    width: 40%;
                    border-radius: .3rem;
                    text-align: center;
                    padding: .5rem 0;
                }
            }
        }
    }
</style>
