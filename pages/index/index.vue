<template>
    <view></view>
</template>

<script>
const app = getApp()

export default {
    onLoad(e) {
        this.$uni.showLoading('加载中...')

        let {userid = null, id: active_id = null, scene} = e
        if (scene) {
            const sceneStr = decodeURIComponent(scene)
            userid = this._utils.getUrlParams('userid', sceneStr) || this._utils.getUrlParams('uid', sceneStr) || userid
            active_id = this._utils.getUrlParams('id', sceneStr) || active_id
        }

        this.$uni.hideHomeButton()

        this.$login.uniLogin(err => {
            if (err && err.errMsg) this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            this.pageJump()
        }, userid, active_id)
    },


    methods: {
        pageJump() {
            // #ifdef H5
            // 这个 if (document) 判断只是为了解决在某些IDE上，直接return 后面代码会警告“不可到达的代码”，没有实际意义
            if (document) return this.$uni.reLaunch('/pages/activity/user/activity_list?type=create')
            // #endif

            // let url = '/pages/activity/user/index'
            let url = '/pages/diy-page/diy-index'
            const urlOptions = ['from=tab']
            const shield = app.globalData['shop_info']?.['extend_set']?.['shield_other_active']

            if (shield?.active_id) {
                const {types, active_id, screen_pic} = shield
                if (active_id === 'personal_wechat_mobile_step') return this.jumpStepPage()
                url = this.getPath(types)
                urlOptions.push(`id=${active_id}`)
                if (screen_pic) urlOptions.push(`screen_pic=${screen_pic}`)
            }

            url += `?${urlOptions.join('&')}`

            uni.hideLoading()
            this.$uni.reLaunch(url)
        },

        getPath(types = 0) {
            return this.xwy_config.getActivityPath(types, 'details') || '/pages/activity/user/detail'
        },

        jumpStepPage() {
            uni.hideLoading()
            this.$uni.reLaunch('/pages/wechat-step/wechat-step-page')
        }
    }
}
</script>
