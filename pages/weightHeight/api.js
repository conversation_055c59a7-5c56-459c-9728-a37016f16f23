import xwy_api from '@/utils/api/xwy_api'
import utils from '@/utils/utils'

module.exports = {
    async getRecordList(params) {
        params.page ||= 1
        params.perpage ||= 10
        
        const res = await xwy_api.request({
            url: 'front.flat.sport_step.lose_weight.user/user_weight_height_list',
            data: params
        })
        const res_data = res?.data?.list
        const list = this.recordListHandle(res_data?.data || [])
        const is_last_page = res_data ? res_data.is_lastpage : true

        return {list, is_last_page}
    },

    recordListHandle(list) {
        const newList = []
        list.forEach(v => {
            const weight = Math.floor(v.weight / 100) / 10,
                height = Math.floor(v.height / 100) / 10
            
            const submit_date = utils.unitTimeToDate(v.submit_date * 1000)

            newList.push({
                id: v.id,
                active_id: v.active_id,
                weight,
                height,
                BMI: this.bmiCalculation(weight, height),
                create_time: v.create_time,
                submit_date,
                date: submit_date.substring(5, 10),
                pic_list: v.conf_json?.pic_list || [],
                nickname: v.user_details?.must_submit?.[0]?.value || '',
                headimg: v.user_details?.headimg || ''
            })
        })
        return newList
    },

    bmiCalculation(weight, height) {
        if (!weight || !height) return {value: null, category: null}
        const heightInMeters = height / 100
        const bmi = Number((weight / (heightInMeters * heightInMeters)).toFixed(1))
        return {
            value: bmi,
            category: this.bmiCategory(bmi)
        }
    },

    bmiCategory(bmi) {
        if (bmi < 18.5) return '偏瘦'
        if (bmi >= 18.5 && bmi < 24) return '正常'
        if (bmi >= 24 && bmi < 28) return '偏胖'
        if (bmi >= 28) return '肥胖'
    },
}