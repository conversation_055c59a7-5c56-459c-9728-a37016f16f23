<template>
    <view class="page bg-background">
        <template v-if="myself">
            <view :class="'clearfix clear p10 ' + (calendar_show ? 'bg-white' : 'bg-background')">
                <view :class="'calender-icon fr ' + (calendar_show ? 'bg-background' : 'bg-white')"
                      @click="calendarShowSwitch">
                    <text class="iconfont icon-calendar-1 font18"
                          :class="calendar_show ? 'color-light-primary' : 'color-sub'"></text>
                </view>

            </view>

            <uni-transition :show="calendar_show" :mode-class="['fade', 'slide-right']">
                <uni-calendar :date="calendar_value" :selected="calendar_selected"
                              :start-date="calendarStartDate" :end-date="calendarEndDate"
                              @monthSwitch="monthSwitch" @change="calendarDateChange"/>
            </uni-transition>
        </template>

        <view>
            <view class="m10" v-for="item in record_list" :key="item.id">
                <record-list-item :item="item" complete-time :show-user-info="!myself"
                                  @deleteItem="getRecordList"/>
            </view>
        </view>


        <view v-if="loading" class="text-center"
              :style="{paddingTop: current_page === 1 && !calendar_show ? '30vh' : ''}">
            <load-ani/>
        </view>

        <view v-if="!loading && !record_list.length" class="text-center"
              :style="{paddingTop: calendar_show ? '30px' : '20vh'}">
            <view v-if="calendar_show && myself && !id" class="flex-all-center">
                <view class="add-record bg-light-primary color-white" @click="recordWeight">记录体重</view>
            </view>
            <template v-else>
                <text class="iconfont icon-empty-state color-divider" style="font-size: 100px;"></text>
                <view class="color-sub font14">暂无记录</view>
            </template>
        </view>

        <record-popup ref="recordPopup" :active-id="id" :last-height="last_height"
                      :submit-weight-upload-photo="upload_photo"
                      @success="recordSuccess" @fail="recordFail"/>
    </view>
</template>

<script>
import recordListItem from '../components/record-list-item.vue'
import recordPopup from '../components/record-popup.vue'
import __api from '../api'

export default {
    components: {recordListItem, recordPopup},
    data() {
        return {
            id: '',
            loading: true,
            record_list: [],
            current_page: 1,
            is_last_page: false,
            myself: false,
            calendar_show: false,
            calendar_value: this._utils.getDay(0, true),
            calendar_selected: [],
            calendarStartDate: '1970-01-01',
            calendarEndDate: this._utils.getDay(0, true),
            upload_photo: 0,
            last_height: 0
        }
    },

    onLoad(params) {
        this.paramsHandle(params)
        this.$login.uniLogin(() => this.getRecordList())
    },

    onPullDownRefresh() {
        this.current_page = 1
        this.is_last_page = false
        this.getRecordList().finally(() => uni.stopPullDownRefresh())
    },

    onReachBottom() {
        if (this.loading || this.is_last_page) return
        this.loading = true
        this.current_page++
        this.getRecordList().finally(() => this.loading = false)
    },

    methods: {
        paramsHandle(params) {
            if (params.id) this.id = params.id
            if (params.myself) this.myself = true
            if (params.upload_photo) this.upload_photo = Number(params.upload_photo)
            if (params.last_height) this.last_height = Number(params.last_height)
            if (params.begin_time) this.calendarStartDate = this._utils.unitTimeToDate(params.begin_time * 1000)
            if (params.end_time) {
                const endTime = params.end_time * 1000
                const today = new Date().getTime()
                if (endTime < today) this.calendarEndDate = this._utils.unitTimeToDate(endTime)
            }
        },

        getRequestRecordListData() {
            const data = {
                page: this.current_page,
                perpage: 20
            }


            if (this.id) data.active_id = this.id
            if (this.myself) data.myself = 1

            if (this.calendar_show) {
                const {year, month} = this._utils.getYearMonthDay(this.calendar_value)
                const lastDay = new Date(year, month, 0).getDate()
                const _month = month.toString().padStart(2, '0')

                data.perpage = 100
                data.begin_time = `${year}-${_month}-01`
                data.end_time = `${year}-${_month}-${lastDay}`
            }

            return data
        },

        async getRecordList() {
            if (this.current_page === 1) {
                this.is_last_page = false
                this.record_list = []
            }

            this.loading = true
            const res = await __api.getRecordList(this.getRequestRecordListData())
            this.loading = false

            if (this.calendar_show) return this.setCalendarData(res.list)

            const list = this.record_list.concat(res.list)
            this.record_list = this.myself ? list.map((item, index) => ({
                ...item,
                diff: index === list.length - 1 ? 0 : this._utils.formatDecimal(item.weight - list[index + 1].weight)
            })) : list
            this.is_last_page = res.is_last_page
        },


        calendarShowSwitch() {
            const show = !this.calendar_show
            if (show) this.calendar_value = this._utils.getDay(0, true, '-')
            this.calendar_show = show

            this.current_page = 1
            this.calendar_selected = []
            this.$nextTick(() => this.getRecordList())
        },

        setCalendarData(list) {
            let calendar_value = null
            this.calendar_selected = list.map(item => {
                calendar_value ||= item.submit_date
                return {
                    date: item.submit_date,
                    info: `${item.weight}kg`,
                    data: item
                }
            })

            if (calendar_value) {
                this.calendar_value = calendar_value
            } else {
                const {year, month} = this._utils.getYearMonthDay(this.calendar_value)
                this.calendar_value = calendar_value || `${year}-${month.toString().padStart(2, '0')}-01`
            }

            this.record_list = this.calendar_selected.filter(item => item.date === this.calendar_value).map(item => item.data)
        },

        monthSwitch(e) {
            this.calendar_value = `${e.year}-${e.month.toString().padStart(2, '0')}-01`
            this.current_page = 1
            this.calendar_selected = []
            this.$nextTick(() => this.getRecordList())
        },

        calendarDateChange(e) {
            const [old_year, old_month] = this.calendar_value.split('-')
            const [now_year, now_month] = e.fulldate.split('-')
            this.calendar_value = e.fulldate

            // 回到今日也是这个回调方法，所以不是本月回到今日要重新请求记录列表
            if (old_year !== now_year || old_month !== now_month) {
                this.current_page = 1
                this.getRecordList()
            } else {
                this.record_list = this.calendar_selected.filter(item => item.date === this.calendar_value).map(item => item.data)
            }
        },

        recordWeight() {
            this.$refs.recordPopup.show(this.calendar_value)
        },

        recordSuccess() {
            this.getRecordList()
        },

        recordFail(info) {
            this.$uni.showModal(info)
        }
    }
}
</script>

<style lang="scss" scoped>
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding: 1px 0;
}

.calender-icon {
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 5px;
}

.add-record {
    width: 150px;
    line-height: 44px;
    border-radius: 22px;
}

::v-deep .uni-calendar-item__weeks-box-circle {
    display: none;
}
</style>