<template>
    <view class="page bg-background">
        <view class="init-loading flex-all-center" v-if="init_loading">
            <view class="text-center">
                <load-ani/>
            </view>
        </view>

        <activity-delete-tips v-if="error" :activeId="id"/>

        <view class="top color-white text-center">
            <view class="top-weight">
                <view>
                    <template v-if="last_data.weight">
                        <text class="current-weight font-bold italic-text">{{ last_data.weight }}</text>
                        <text class="font14 pl2 italic-text">kg</text>
                    </template>
                    <template v-else>未记录</template>
                </view>
                <view class="font14">当前体重</view>
            </view>

            <view class="top-height-bmi flex-kai">
                <view class="top-height">
                    <template v-if="last_data.height">
                        <text>身高: {{ last_data.height }}</text>
                        <text class="font14 pl2">cm</text>
                    </template>
                    <template v-else>身高: 未记录</template>
                </view>
                <view class="top-bmi" @click="whatIsBMI">
                    <view v-if="last_data.weight && last_data.height" class="flex-all-center">
                        <view class="top-bmi-value">
                            <view class="bmi-question-mark flex-all-center">?</view>
                            <text>BMI: {{ last_data.BMI.value }}</text>
                            <text class="font14 pl5">({{ last_data.BMI.category }})</text>
                        </view>
                    </view>
                    <view v-else>BMI: 无</view>
                </view>
            </view>
        </view>

        <what-is-bmi-popup ref="whatIsBmiPopup" @close="whatIsBmiPopupClose"/>

        <view v-if="last_data.BMI.value" class="bmi-bar bg-white">
            <bmi-progress-bar :bmi="last_data.BMI.value"/>
        </view>

        <view class="bg-background">
            <analysis-report :active-id="id" :data="last_data" @show="reportShow" @close="reportClose"/>
        </view>

        <view class="bg-background pt10 pb10">
            <recent-weight-line-chart ref="recentWeightLineChart" :record-list.sync="record_list"/>
        </view>

        <view v-if="!init_loading" class="weight-goal-container bg-background p10 pt0">
            <weight-goal :active-id="id || null" :weight="last_data.weight || 0"/>

            <view v-if="clockOpen" class="clock-in-enter text-center" @click="toClockIn">
                <uni-icons type="calendar" size="28px" color="#5cadff"/>
                <view class="color-light-primary font12">去打卡</view>
            </view>
        </view>

        <view v-if="record_list.length" class="record">
            <view class="record-title flex-kai">
                <view>历史记录</view>
                <view @click="lookMoreRecord">
                    <text class="color-light-primary font14">查看更多</text>
                    <uni-icons type="forward" size="14" color="#5cadff"/>
                </view>
            </view>

            <view class="record-list">
                <view class="m10" v-for="item in record_list" :key="item.id">
                    <record-list-item :item="item" @deleteItem="getRecordList"/>
                </view>
            </view>
        </view>


        <view class="bottom-bar flex-all-center bg-white">
            <view v-if="id" class="bottom-bar-icon-item" hover-class="navigator-hover"
                  @click="uniPopupOpen('activity_detail')">
                <text class="iconfont icon-feedback"></text>
            </view>
            <view class="record-button" hover-class="navigator-hover" @click="goToRecord">记录体重</view>
            <view v-if="id" class="bottom-bar-icon-item" hover-class="navigator-hover" @click="toTopList">
                <text class="iconfont icon-trophy"></text>
            </view>
        </view>

        <record-popup ref="recordPopup" :active-id="id" :last-height="last_data.height || 0"
                      :submit-weight-upload-photo="detail.conf && detail.conf.active && detail.conf.active.submit_weight_upload_photo || 0"
                      @success="recordSuccess" @fail="recordFail" @close="recordClose"/>


        <template v-if="id">
            <screen-picture
                v-if="screen_pic_show"
                :hide="screen_pic_hide"
                :image-src="screen_pic"
                :time="screen_pic_count_down"
                :show-button="true"
                @skipScreen="skipScreen"
            />


            <view class="icon-list">

                <navigator class="icon-item" v-if="is_my_activity" :url="'../admin/manage?id=' + id">
                    <text class="iconfont icon-setting"></text>
                </navigator>

                <view class="icon-item" v-if="is_joining" hover-class="navigator-hover"
                      @click="uniPopupOpen('my_info')">
                    <text class="iconfont icon-personal-data"></text>
                </view>

                <view v-if="clockOpen" class="icon-item" hover-class="navigator-hover"
                      @click="toClockInRecord">
                    <uni-icons type="calendar"/>
                </view>

                <navigator class="icon-item" url="/pages/user/user">
                    <uni-icons type="person"/>
                </navigator>

                <view class="icon-item" hover-class="navigator-hover" @click="showActiveSharePopup">
                    <text class="iconfont icon-share"></text>
                </view>
            </view>

            <view v-if="join_popup_show" class="join-popup flex-all-center bg-white"
                  @touchmove.stop.prevent="">
                <view>
                    <view class="join-popup-c bg-white">
                        <view class="text-center font18 color-content p10">
                            <template v-if="update_attend_details">修改报名信息</template>
                            <template v-else>参加活动</template>
                        </view>


                        <template v-if="must_submit.length">
                            <view v-for="(item, index) in must_submit" :key="index">

                                <input v-if="item.types === 1" class="join-input" :value="item.value"
                                       :placeholder="`请输入${item.title}${item.rules === 1 ? ' (必填)' : ''}`"
                                       @input="mustValueChange('input', $event, index)"
                                />
                                <picker v-if="item.types === 2" :range="item.options" range-key="text"
                                        @change="mustValueChange('picker', $event, index)">
                                    <view class="join-input flex-kai">
                                        <view v-if="!item.value" class="color-sub">
                                            请选择{{ item.title }}{{ item.rules === 1 ? ' (必选)' : '' }}
                                        </view>
                                        <view v-if="item.value">{{ item.value }}</view>
                                        <text class="iconfont icon-more color-disabled font18"/>
                                    </view>
                                </picker>
                            </view>

                        </template>


                        <view class="join-popup-btns flex-row text-center font18">
                            <view class="join-popup-btn-cancel color-sub" @click="cancelJoin">取消</view>
                            <view class="join-popup-btn-confirm color-success" @click="joinAjax">确定</view>
                        </view>
                    </view>

                    <template v-if="!detail.rank_set || !detail.rank_set.closed_AD">
                        <view class="pt5">
                            <xwy-ad :ad_type="66"></xwy-ad>
                        </view>
                        <xwy-ad :ad_type="3"></xwy-ad>
                    </template>
                </view>


            </view>

            <active-share ref="activeShare"/>

            <uni-popup ref="activity_detail" type="center" @touchmove.stop.prevent="">
                <view v-if="detail && detail.conf && detail.conf.active"
                      class="uni-popup-info detail-popup bg-white">
                    <view class="popup-close" @click="uniPopupClose('activity_detail')">
                        <uni-icons type="close" size="28" color="#b2b3b7"/>
                    </view>

                    <scroll-view :scroll-y="true" style="max-height: calc(100vh - 200px); padding: 10px 0;">
                        <view class="p10 bdb-10 bg-white">
                            <view class="text-center color-sub pb5">- 活动规则 -</view>
                            <activity-logo-title-time :details="detail" hide-logo hide-share/>

                            <view class="color-content font16">
                                活动参与方式：
                                <template v-if="detail.conf.active.enter_types === 1">
                                    自由报名参与活动
                                </template>
                                <template v-if="detail.conf.active.enter_types === 2">
                                    需要输入密码才能报名
                                </template>
                                <template v-if="detail.conf.active.enter_types === 3">
                                    报名需要审核通过才能参与活动
                                </template>
                            </view>
                        </view>

                        <view v-if="detail.content || news_detail" class="p10 bdb-10 bg-white">
                            <view class="text-center color-sub pb5">- 活动说明 -</view>
                            <view class="color-content font16">
                                <template v-if="detail.content && !news_detail">

                                    <rich-text :nodes="detail.content" :space="true" :selectable="true"/>
                                </template>
                                <template v-if="news_detail">
                                    <template v-if="news_detail.content">
                                        <u-parse :content="news_detail.content"/>
                                    </template>
                                </template>
                            </view>
                        </view>
                        <xwy-ad v-if="!loading && (!detail.rank_set || !detail.rank_set.closed_AD)"
                                :ad_type="66"></xwy-ad>
                    </scroll-view>
                </view>
            </uni-popup>

            <uni-popup ref="my_info" type="center" @touchmove.stop.prevent="">
                <view v-if="detail && detail.conf && detail.conf.active" class="uni-popup-info bg-white p20">
                    <view class="popup-close" @click="uniPopupClose('my_info')">
                        <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                    </view>
                    <view class="text-center color-sub pb5">- 活动报名信息 -</view>
                    <view class="text-center p10">

                        <image class="headimg" mode="aspectFill"
                               :src="headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"/>
                        <view>
                            <text class="color-primary" @click="updateHeadimg">更改头像</text>
                        </view>
                    </view>

                    <view class="color-content font16 ptm5" v-for="(item, index) in must_submit" :key="index"
                          @click="updateAttendDetailShow">
                        <text>
                            {{ item.title }}：
                            <template>
                                <template v-if="item.value">{{ item.value }}</template>
                                <template v-else>
                                    <template v-if="item.types === 1">未填写</template>
                                    <template v-else-if="item.types === 2">未选择</template>
                                </template>
                            </template>
                        </text>
                        <text v-if="is_joining" class="iconfont icon-edit color-sub pl5"></text>
                    </view>

                    <view class="color-content font16 ptm5">
                        减重：{{ Math.floor(user_details['lose_weight'] / 100) / 10 }}kg
                    </view>


                    <template v-if="popup_open && (!detail.rank_set || !detail.rank_set.closed_AD)">
                        <view style="position: relative; left: -10px;">
                            <xwy-ad :ad_type="66"></xwy-ad>
                        </view>
                        <xwy-ad :ad_type="3"></xwy-ad>
                    </template>
                </view>
            </uni-popup>


            <template v-if="password_dialog_show">
                <uni-popup ref="input_password" type="dialog" mode="input" :is-mask-click="false"
                           @maskClick="copy(id)">
                    <uni-popup-dialog
                        mode="input"
                        title="活动密码"
                        :value="password"
                        placeholder="请输入活动密码"
                        @confirm="passwordInputConfirm"
                        @close="passwordInputClose"
                    ></uni-popup-dialog>
                </uni-popup>
            </template>


            <expiration-reminder ref="expirationReminder"/>

        </template>

    </view>
</template>

<script>
const app = getApp()
import bmiProgressBar from "../components/bmi-progress-bar.vue"
import recordPopup from '../components/record-popup.vue'
import recordListItem from '../components/record-list-item.vue'
import recentWeightLineChart from '../components/recent-weight-line-chart.vue'
import whatIsBmiPopup from '../components/what-is-bmi-popup.vue'
import analysisReport from '../components/analysis-report/report-popup.vue'
import weightGoal from '../components/weight-goal.vue'
import __api from '../api'
import my_storage from '@/utils/storage.js'
import activity_tool from '@/utils/acyivity_tool.js'

export default {
    components: {
        bmiProgressBar,
        recordPopup,
        recordListItem,
        recentWeightLineChart,
        whatIsBmiPopup,
        analysisReport,
        weightGoal
    },
    data() {
        return {
            loading: true,
            screen_pic: '',
            screen_pic_show: false,
            screen_pic_hide: false,
            screen_pic_count_down: null,
            id: '',
            userid: '',
            is_my_activity: false,
            detail: {},
            user_details: {},
            error: '',
            join_popup_show: false,
            update_attend_details: false,
            headimg: '',
            username: '',
            checked: 0,
            is_joining: false,
            password: '',
            technology_support: false,
            must_submit: [],
            headimg_plugin: [],
            password_dialog_show: false,
            popup_open: false,
            news_detail: null,
            platform: uni.getSystemInfoSync().platform,
            init_loading: true,
            record_list: []
        }
    },

    computed: {
        last_data() {
            const data = {
                weight: null,
                height: null,
                BMI: {value: null, category: null},
                create_time: null
            }
            if (this.record_list.length) {
                const {weight = null, height = null, BMI, create_time} = this.record_list?.[0] || {}
                data.weight = weight
                data.height = height
                data.create_time = create_time
                if (BMI && BMI.value && BMI.category) data.BMI = BMI
            }

            return data
        },

        clockOpen() {
            return !!this.detail?.rank_set?.['weightAddSign']
        }
    },

    onLoad(params) {
        this.paramsHandle(params)
        this.$login.uniLogin(() => this.init(params))
    },

    methods: {
        paramsHandle(params) {
            if (params.screen_pic) this.screenPicShow(params.screen_pic)
            if (params.id) this.id = params.id
        },

        async init(params) {
            if (params.scene) {
                const id = await this.getActiveId(params.scene)
                if (!id) return
                this.id = id
            }
            if (this.id) {
                await this.getDetail()
                await this.getUserStatus()
            }
            await this.getRecordList()
            this.init_loading = false
        },

        async getActiveId(scene) {
            const sceneStr = decodeURIComponent(scene)
            const id = this._utils.getUrlParams('id', sceneStr)
            if (!id) {
                this.init_loading = false
                this.loading = false
                await this.$uni.showModal('从二维码获取id失败')
                return null
            }
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/get_long_active_id_by_short_id',
                data: {id}
            })
            const active_id = res?.data?.['long_active_id']
            if (!active_id) {
                this.init_loading = false
                this.loading = false
                await this.$uni.showModal(res && res.info || '长id获取失败')
                return null
            }
            return active_id
        },

        async getDetail() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details',
                data: {active_id: this.id}
            })
            const details = res?.data?.active_details || {}
            if (!details) {
                this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                return
            }

            my_storage.setActivityCloaeAdStorage(this.id, details.rank_set?.closed_AD)
            // #ifndef H5
            this.$refs.expirationReminder.open(details)
            // #endif
            app.globalData.activity_detail = details
            this.detail = details

            if (details.conf) {
                const conf = details.conf

                if (conf.active) {
                    const active = conf.active
                    if (!this.screen_pic && active.screen_pic) this.screenPicShow(active.screen_pic)

                    if (active.news?.news_id) await this.changeDetailContent(active.news.news_id)
                }

                if (conf.must_submit) {
                    const must_submit = conf.must_submit
                    delete conf.must_submit
                    if (must_submit.length) {
                        must_submit.forEach(v => v.value = v.value || '')
                        this.must_submit = must_submit
                    }
                }
            }

            if (details.rank_set?.['shield_other']) {
                this.$uni.hideHomeButton()
                this._utils.updateShieldOtherInfo(details) // 更新纯净版缓存信息
            }

            this.addLookRecords()
            if (details.name) this.$uni.setNavigationBarTitle(details.name)
            if (app.globalData['userid'] === details.userid) this.is_my_activity = true
        },

        screenPicShow(src) {
            let isWechatApp = true
            // #ifndef MP-WEIXIN
            isWechatApp = false
            // #endif
            if (!isWechatApp) return

            uni.hideLoading()
            this.screen_pic = src
            this.screen_pic_count_down = 5
            this.screen_pic_show = true
            this.screenPicInterval = setInterval(() => {
                this.screen_pic_count_down--
                if (this.screen_pic_count_down <= 0) {
                    this.skipScreen()
                }
            }, 1000)
        },
        skipScreen() {
            clearInterval(this.screenPicInterval)
            this.screenPicInterval = null
            this.screen_pic_count_down = 0
            this.screen_pic_hide = true

            const timeout = setTimeout(() => {
                this.screen_pic_show = false
                this.screen_pic_hide = false
                this.passwordDialogShow()
                clearTimeout(timeout)
            }, 500)
        },

        addLookRecords() {
            const detail = this.detail
            const value = {
                active_id: detail.active_id,
                name: detail.name,
                types: detail.types,
                logo: detail.logo || this.xwy_config.active_default_logo,
                look_time: new Date().getTime()
            }

            if (detail.organizer) value.organizer = detail.organizer
            my_storage.addActivityLookRecords(value)
        },


        async getUserStatus() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: {active_id: this.id}
            })

            const attend_details = res?.data?.user_details
            if (attend_details) {
                this.user_details = attend_details
                this.is_joining = true
                this.checked = attend_details.checked || 0

                this.loading = false
                uni.hideLoading()

                if (attend_details.nickname) this.username = attend_details.nickname
                if (attend_details.headimg) this.headimg = attend_details.headimg

                if (attend_details.must_submit) {
                    this.must_submit.forEach(v => {
                        attend_details.must_submit.forEach(vv => {
                            if (vv.title === v.title) v.value = vv.value
                        })
                    })
                }

            } else {
                this.no_attend = true
                this.loading = false
                uni.hideLoading()

                if (this.screen_pic_show) return false

                this.passwordDialogShow()
            }

        },


        async changeDetailContent(news_id) {
            const res = await this.xwy_api.request({
                url: "front.news/news_details",
                data: {news_id}
            })
            uni.hideLoading()

            const details = res?.data?.['news_details']
            if (!details) {
                await this.$uni.showModal(res.info || '文章内容获取失败')
                return this.$uni.navigateBack()
            }

            if (details.video_url) {
                const url = details.video_url
                const isHttp = url.startsWith('http://') || url.startsWith('https://')
                details.video_type = isHttp ? 'http' : 'txv_id'
            }

            if (details.content) details.content = this._utils.newsContentInit(details.content)
            this.news_detail = details
        },


        passwordDialogShow() {
            if (this.detail.conf.active.password && this.no_attend) {
                const passwordDialogShow = () => {
                    this.password_dialog_show = true
                    const password = my_storage.getActivityPassword(this.id)
                    if (password) {
                        this.password = password
                        this.checkActivityPassword(password)
                    }
                    const timeout = setTimeout(() => {
                        this.$refs.input_password.open()
                        clearTimeout(timeout)
                    }, 30)
                }

                // 体验版不需要输入密码
                if (app.globalData['evn_version'] === 'trial') {
                    return this.$uni.showModal('此活动设置了活动密码，请勿报名参与活动！！！', {
                        showCancel: true,
                        cancelText: '进入活动',
                        confirmText: '输入密码',
                        success: res => {
                            res.confirm && passwordDialogShow()
                        }
                    })
                }

                passwordDialogShow()
            }
        },

        passwordInputConfirm(val) {
            if (!val) {
                // 纯净版取消输入密码出现去个人中心选项
                if (this.detail?.rank_set?.['shield_other']) {
                    uni.showModal({
                        title: '提示',
                        content: '请输入密码',
                        cancelText: '个人中心',
                        confirmText: '重新输入',
                        success: res => {
                            if (res?.confirm) {
                                this.$refs.input_password.open()
                            }
                            if (res?.cancel) {
                                uni.navigateTo({
                                    url: '/pages/user/user'
                                })
                            }
                        }
                    })
                    return this.$uni.showModal('请输入密码', {
                        showCancel: true,
                        cancelText: '个人中心',
                        confirmText: '重新输入',
                        success: res => {
                            if (res?.confirm) this.$refs.input_password.open()
                            if (res?.cancel) this.$uni.navigateTo('/pages/user/user')
                        }
                    })
                }

                return this.$uni.showModal('请输入密码', {success: () => this.$refs.input_password.open()})
            }
            this.checkActivityPassword(val)
        },

        async checkActivityPassword(password) {
            this.$uni.showLoading('密码验证中...')

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.user/check_active_password',
                data: {
                    active_id: this.id,
                    password
                }
            })
            uni.hideLoading()

            if (res?.status) {
                my_storage.rememberActivityPassword(this.id, password)
                this.$refs.input_password.close()
                return this.$uni.showToast('密码正确', '密码正确',)
            }


            await this.$uni.showModal(res && res.info || '密码错误', {
                success: () => this.$refs.input_password.open()
            })
        },

        passwordInputClose() {
            this.copy(this.id, true)

            if (getCurrentPages().length > 1) return uni.navigateBack()

            // 纯净版并且没有上一页面，重新弹出输入密码窗口
            if (app.globalData['shop_info']?.extend_set?.shield_other_active?.active_id) {
                return this.$uni.showModal('请输入活动密码', {
                    success: () => this.$refs.input_password.open()
                })
            }

            this.$uni.reLaunch('/pages/index/index')
        },


        async updateHeadimg() {
            uni.navigateTo({
                url: `/pages/other/headimg-list/headimg-list?active_id=${this.id}`,
                events: {
                    newImg: async obj => {
                        if (!obj?.src) return
                        this.$uni.showLoading('修改中...')
                        if (!obj.temp) return this.updateAttendDetail(obj.src)

                        const data = {
                            temp_data: {
                                path: obj.src
                            },
                            is_temp: 5
                        }
                        if (obj.size) data.temp_data.size = obj.size
                        const headimg = await this.xwy_api.uploadOneImage(data)
                        this.updateAttendDetail(headimg)
                    }
                }
            })
        },

        joinActivity() {
            if (this.loading) return
            if (this.detail.conf?.active?.submit) {
                const now_time = new Date().getTime()
                const {begin, end} = this.detail.conf.active.submit
                if (begin) {
                    const begin_time = new Date(begin.replace(/-/g, '/')).getTime()
                    if (now_time < begin_time) return this.$uni.showModal(`活动于${begin}开始报名`)
                }
                if (end) {
                    const end_time = new Date(end.replace(/-/g, '/')).getTime()
                    if (now_time > end_time) return this.$uni.showModal(`活动于${end}截止报名`)
                }
            }

            this.join_popup_show = true
        },

        updateAttendDetailShow() {
            this.update_attend_details = true
            this.join_popup_show = true
        },

        mustValueChange(formType, e, index) {
            const item = this.must_submit[index]

            if (formType === 'input') {
                item.value = e.detail.value
            } else if (formType === 'picker') {
                item.value = item.options[e.detail.value].text
            }

            this.$set(this.must_submit, index, item)
        },


        cancelJoin() {
            this.join_popup_show = false
            this.update_attend_details = false
        },

        joinAjax() {
            if (this.update_attend_details) return this.updateAttendDetail()
            this.joining()
        },


        getMustSubmitData() {
            const must_submit = JSON.parse(JSON.stringify(this.must_submit))
            for (let i = 0; i < must_submit.length; i++) {
                const v = must_submit[i]
                v.options && delete v.options
                if (v.rules === 1 && v.value === '') {
                    const tips = v.types === 2 ? '选择' : '输入'
                    this.$uni.showToast(`请${tips}${v.title}`)
                    return false
                }
            }
            let must_submit_str = JSON.stringify(must_submit)
            must_submit_str = must_submit_str.replace(/·/g, '-')
            return this._utils.base64['encode'](must_submit_str)
        },

        updateAttendDetail(headimg) {
            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id
            }

            if (headimg) data.headimg = headimg

            const must_submit = this.getMustSubmitData()
            if (must_submit === false) return false
            data.must_submit = must_submit

            this.$uni.showLoading('修改中...')

            this.xwy_api.ajax({
                url: 'front.flat.sport_step.user/update_attend_details',
                data,
                success: res => {
                    if (!res.status) return this.$uni.showModal(res.info || '修改失败')
                    this.$uni.showToast(res.info || '修改成功', 'success')
                    this.cancelJoin()
                    this.getUserStatus()
                }
            })
        },

        joining() {

            const data = {
                active_id: this.id,
                access_token: app.globalData['access_token']
            }


            if (this.must_submit && this.must_submit.length) {
                const must_submit = this.getMustSubmitData()
                if (must_submit === false) return false
                data.must_submit = must_submit
            }

            data.nickname = this.username

            this.loading = true
            this.$uni.showLoading('报名中...')


            this.xwy_api.ajax({
                url: 'front.flat.sport_step.user/submit_attend_active',
                data,
                success: res => {
                    this.loading = false
                    uni.hideLoading()
                    console.log('报名活动', res)
                    if (!res.status) return this.$uni.showModal(res.info || '报名失败', {title: '报名失败'})

                    this.join_popup_show = false
                    this.$uni.showToast(res.info || '报名成功', 'success')

                    setTimeout(() => {
                        this.$uni.showLoading()
                        this.getUserStatus()
                    }, 1000)
                }
            })
        },

        toTopList() {
            this.$uni.navigateTo(`./ranking-list?id=${this.id}`)
        },


        showActiveSharePopup() {
            this.$refs.activeShare.open({
                page: 'pages/weightHeight/user/details',
                scene: 'id=' + this.detail.id,
                qrcode_logo: this.detail.rank_set?.closed_AD && this.detail.conf?.active?.qrcode_logo
            })
        },


        copy(data, hide_toast = false) {
            uni.setClipboardData({
                data,
                success: () => {
                    if (hide_toast) return uni.hideToast()
                    this.$uni.showToast('复制成功', 'none', 500)
                }
            })
        },

        async toClockIn() {
            if (!this.userCheck()) return
            if (!this.timeCheck()) return
            if (!await this.clockInCountCheck()) return
            this.$uni.navigateTo(`../clock-in/clock-in?active_id=${this.id}`)
            // this.$uni.navigateTo(`/pages/clock_in/user/sign_record_edit?active_id=${this.id}`)
        },

        userCheck() {
            if (!this.user_details?.id) {
                this.$uni.showToast('未参与活动，不能打卡')
                return false
            }
            if (!this.user_details.checked) {
                this.$uni.showToast('报名审核未通过，不能打卡')
                return false
            }
            return true
        },

        timeCheck() {
            let {begin_time, end_time} = this.detail
            begin_time *= 1000
            end_time *= 1000
            const now_time = new Date().getTime()
            if (now_time < begin_time) {
                this.$uni.showToast('活动未开始')
                return false
            }
            if (now_time > end_time) {
                this.$uni.showToast('活动已结束')
                return false
            }
            return true
        },

        async clockInCountCheck() {
            const sign_times_type = this.detail.conf.active.sign_times_type
            if (!sign_times_type) return true

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/get_user_submit_sign_details',
                data: {active_id: this.id}
            })
            uni.hideLoading()

            const data = res?.data
            if (!data) return true

            const last_time = data['latest_sign_details']?.create_time || ''
            const today_sign_count = data['today_sign_count'] || 0


            if (sign_times_type === 1 && last_time) {
                this.$uni.showToast('已打卡')
                return false
            }
            if (sign_times_type === 2 && today_sign_count) {
                this.$uni.showToast('今日已打卡')
                return false
            }

            return true
        },

        toClockInRecord() {
            this.$uni.navigateTo(`/pages/clock_in/user/public_sign_list?id=${this.id}&type=5`)
        },


        uniPopupClose(ref) {
            this.popup_open = false
            this.$refs[ref].close()
            this.$refs.recentWeightLineChart?.chartShow()
        },

        uniPopupOpen(ref) {
            this.popup_open = true
            this.$refs[ref].open()
            this.$refs.recentWeightLineChart?.chartHide()
        },


        async getRecordList() {
            this.record_list = []
            // 列表只显示10个，因为要计算体重差值所以要获取第11个，不然第10个记录的差值无法计算
            const data = {page: 1, perpage: 11, myself: 1}
            if (this.id) data.active_id = this.id
            const record_data = await __api.getRecordList(data)
            const list = record_data.list || []
            this.record_list = list.map((item, index) => ({
                ...item,
                diff: index === list.length - 1 ? 0 : this._utils.formatDecimal(item.weight - list[index + 1].weight) // 计算体重差值，最后一条记录没有参照数据无法计算，所以差值为0
            })).slice(0, 10) // 截取前 10 条记录

            // 需要先从dom中移除图表，再重新渲染，不然无法更新图表数据
            this.$refs.recentWeightLineChart?.chartHide()
            setTimeout(() => this.$refs.recentWeightLineChart?.chartShow(), 300)
        },

        lookMoreRecord() {
            let url = './record-list?myself=1'
            if (this.last_data.height) url += `&last_height=${this.last_data.height}`
            if (this.id) {
                const {begin_time, end_time, conf} = this.detail
                url += `&id=${this.id}&begin_time=${begin_time}&end_time=${end_time}`
                if (conf?.active?.submit_weight_upload_photo) {
                    url += `&upload_photo=${conf.active.submit_weight_upload_photo}`
                }
            }
            this.$uni.navigateTo(url)
        },

        async goToRecord() {
            if (this.id) {
                if (!this.is_joining) return this.joinActivity()

                if (!activity_tool.actionCheck({
                    is_joining: this.is_joining,
                    checked: this.checked,
                    begin_time: this.detail.begin_time,
                    end_time: this.detail.end_time
                })) return false
            }

            // 由于接口每天只保存一次数据，提示用户数据会覆盖
            const last_record_time = this.last_data.create_time
            if (last_record_time) {
                const date = last_record_time.split(' ')[0]
                const today = this._utils.getDay(0, true)
                if (date === today) {
                    const tips = '今天已记录过身高体重，重新记录会覆盖今天记录的身高体重。是否重新记录？'
                    const res = await this.$uni.showModal(tips, {showCancel: true})
                    if (!res.confirm) return
                }
            }
            this.$refs.recordPopup.show()
            this.$refs.recentWeightLineChart?.chartHide()
        },

        recordSuccess(info) {
            this.$refs.recentWeightLineChart?.chartShow()
            this.$uni.showToast(info || '已记录', 'success')
            this.getRecordList()
            this.id && this.getUserStatus()
        },
        recordFail(info) {
            this.$refs.recentWeightLineChart?.chartShow()
            this.$uni.showModal(info || '记录失败', {title: '记录失败'})
        },
        recordClose() {
            this.$refs.recentWeightLineChart?.chartShow()
        },

        reportShow() {
            this.$refs.recentWeightLineChart?.chartHide()
        },
        reportClose() {
            this.$refs.recentWeightLineChart?.chartShow()
        },

        whatIsBMI() {
            this.uniPopupOpen('whatIsBmiPopup')
        },
        whatIsBmiPopupClose() {
            this.$refs.recentWeightLineChart?.chartShow()
        }
    },

    onShareAppMessage() {
        const {detail = {}, id} = this
        this._utils.shareCopyActivityId(detail)

        const {screen_pic, name, logo, share_title, share_image} = detail.conf?.active || {}

        let url = '/pages/weightHeight/user/details?id=' + id
        if (screen_pic) url += `&screen_pic=${screen_pic}`

        return {
            title: share_title || name || '',
            path: url,
            imageUrl: share_image || logo || ''
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    padding-bottom: 100px;
    box-sizing: border-box;
}

.init-loading {
    width: 100vw;
    height: 100vh;
    background-color: #fff;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
}

.pl2 {
    padding-left: 2px;
}

.top {
    background: linear-gradient(#00be76, #03bb8c);

    .top-weight {
        padding: 30px 0;

        .current-weight {
            font-size: 50px;
        }
    }

    .top-height-bmi {
        .top-height, .top-bmi {
            line-height: 40px;
            width: 50%;
            background-color: rgba(0, 0, 0, .2);
            box-sizing: border-box;
        }

        .top-bmi {
            border-left: 1px solid #82c6b2;

            .top-bmi-value {
                position: relative;

                .bmi-question-mark {
                    position: absolute;
                    top: 4px;
                    left: -16px;
                    color: #fff;
                    border: 1px solid #fff;
                    font-size: 12px;
                    width: 14px;
                    height: 14px;
                    border-radius: 7px;
                    box-sizing: border-box;
                }
            }
        }
    }
}

.bmi-bar {
    padding: 20px 10px;
}

.record {
    padding: 10px 0;

    .record-title {
        padding: 10px;
    }
}

.bottom-bar {
    $size: 44px;

    position: fixed;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 10px 10px 20px;
    box-sizing: border-box;
    text-align: center;
    line-height: $size;

    .record-button {
        width: 150px;
        background-color: #00ba9a;
        color: #fff;
        height: $size;
        border-radius: calc(#{$size} / 2);
        margin: 0 5vw;
    }

    .bottom-bar-icon-item {
        width: $size;
        height: $size;
        border-radius: 50%;
        color: #fff;
        background-color: #00ba9a;

        .iconfont {
            font-size: 24px;
        }
    }
}


.icon-list {
    position: fixed;
    top: 10vh;
    right: 10px;
    display: flex;
    flex-direction: column;

    .icon-item {
        width: 40px;
        height: 40px;
        line-height: 40px;
        border-radius: 50%;
        text-align: center;
        background-color: rgba(0, 0, 0, .5);
        margin-bottom: 5px;

        .iconfont, .uni-icons {
            font-size: 24px !important;
            color: #fff !important;
        }
    }
}

.headimg {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}

.join-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, .7);
    z-index: 9999;
}

.join-popup-c {
    border-radius: 10px;
    overflow: hidden;
}


.join-input {
    margin: 10px;
    padding: 10px;
    border: 1px solid #bbb;
    border-radius: 5px;
    width: 70vw;
    max-width: 400px;
}

.join-popup-btns {
    border-top: 1px solid #eee;
}

.join-popup-btn-cancel, .join-popup-btn-confirm {
    width: 50%;
    box-sizing: border-box;
    line-height: 44px;
}

.join-popup-btn-cancel {
    border-right: 1px solid #eee;
}

.uni-popup-info {
    position: relative;
    width: 280px;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    z-index: 999;
    left: 50%;
    bottom: -5px;
    margin-left: -12px;
}

.detail-popup {
    width: 95vw;
    padding-bottom: 15px;
}

.weight-goal-container {
    position: relative;

    .clock-in-enter {
        position: absolute;
        right: 20px;
        top: 10px;
    }
}
</style>