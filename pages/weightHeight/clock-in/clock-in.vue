<template>
    <view>
        <view class="sign-page bg-white">
            <view class="p10" v-if="memo_required !== 0">
                <uni-easyinput
                    type="textarea"
                    v-model="memo"
                    :maxlength="800"
                    :placeholder="`请填写打卡备注,800字内,${memo_required === 2 ? '非' : ''}必填`"
                ></uni-easyinput>
            </view>

            <view v-if="pic_list_required !== 0" class="p10">
                <view>
                    <text class="color-content">打卡相册</text>
                    <text v-if="pic_list_required === 1" class="color-red">*
                    </text>
                    <text class="color-sub font12 pl5">
                        {{ pic_list.length }}/{{ pic_list_max_count }}
                    </text>
                </view>

                <view style="padding-top: 5px;">
                    <view class="flex-row flex-wrap">
                        <view class="image-item" v-for="(item, index) in pic_list" :key="index">
                            <image :src="item" mode="aspectFill" @click="previewImage(pic_list, item)"/>
                            <view class="del-image-item" @click.stop="pic_list.splice(index, 1)">
                                <uni-icons type="closeempty" color="#e20f04"/>
                            </view>
                        </view>
                        <view v-if="pic_list.length < pic_list_max_count" class="add-image text-center"
                              @click="addImage">
                            <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                        </view>
                    </view>
                </view>
            </view>

            <view class="flex-all-center pt10">
                <view class="clock-in flex-all-center bg-primary color-white"
                      hover-class="navigator-hover" @click="clockIn">打卡
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
export default {
    data() {
        return {
            memo_required: 0,
            memo: '',
            pic_list_required: 0,
            pic_list: [],
            pic_list_max_count: 6
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.getActiveDetails()
    },

    methods: {
        async getActiveDetails() {
            this.activeDetails = await this.fetchActivityDetails()
            if (!this.activeDetails) return this.$uni.showModal('活动获取失败', {success: () => uni.navigateBack()})

            const {memo_required = 0, pic_list_required = 0} = this.activeDetails.conf?.active || {}
            this.memo_required = memo_required
            this.pic_list_required = pic_list_required
        },

        async fetchActivityDetails() {
            const details = app.globalData['activity_detail']
            if (details && details.active_id === this.id) return details

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details',
                data: {
                    active_id: this.active_id
                }
            })
            return res?.data?.active_details
        },

        addImage() {
            this.$uni.navigateTo('/pages/other/image_upload_or_select?active_id=' + this.id, {
                events: {
                    newImg: src => this.pic_list.push(src)
                }
            })
        },

         previewImage(urls, current = urls[0]) {
            uni.previewImage({
                urls,
                current
            })
        },

        dataCheck() {
            const {memo_required, pic_list_required} = this
            if (memo_required === 1 && !this.memo) {
                this.$uni.showToast('请填写备注')
                return false
            }
            if (pic_list_required === 1 && !this.pic_list.length) {
                this.$uni.showToast('请上传相册')
                return false
            }


            const data = {
                active_id: this.active_id
            }
            if (this.memo) data.memo = this.memo

            const conf_json = {}
            if (this.pic_list.length) conf_json.pic_list = this.pic_list

            let conf_str = JSON.stringify(conf_json)
            conf_str = conf_str.replace(/·/g, '-')
            data.conf_json = this._utils.base64['encode'](conf_str)


            return data
        },

        async clockIn() {
            const data = this.dataCheck()
            if (!data) return false

            // 没有点位id的要传运动类型，不然无法打卡
            data.sport_types = 1

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/submit_user_point_sign',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) {
                await this.$uni.showModal(res?.info || `打卡失败`)
                return false
            }

            this.memo = ''
            this.pic_list = []

            this.getOpenerEventChannel().emit('clockInSuccess')

            this.$uni.showToast('打卡成功', 'success')
            this.$uni.navigateBack(1, {delay: 1000})
        },
    }
}
</script>

<style lang="scss">
.sign-page {
    width: 100vw;
    height: 100vh;
    box-sizing: border-box;
}


.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
    line-height: calc((100vw - 50px) / 3);
    margin: 5px;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}


.image-item {
    padding: 5px;
    position: relative;

    image {
        width: calc((100vw - 50px) / 3);
        height: calc((100vw - 50px) / 3);
        border-radius: 5px;
    }

    .del-image-item {
        right: 8px;
    }
}

.clock-in {
    width: 200px;
    height: 40px;
    border-radius: 20px;
}
</style>