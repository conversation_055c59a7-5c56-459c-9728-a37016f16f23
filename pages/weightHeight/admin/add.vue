<template>
    <view class="page">
        <view v-if="success" class="save-success bg-white">
            <view class="icon text-center">
                <uni-icons type="checkbox-filled" size="100" color="#19be6b"/>
                <view class="color-content font16">活动{{ form_data.active_id ? '修改' : '创建' }}成功！</view>
            </view>
            <view class="flex-all-center">
                <button class="bg-success color-white" style="width: 200px;" @click="lookMyActivityList">
                    查看我的活动
                </button>
            </view>
        </view>

        <view class="type-list flex-row bg-white">
            <view
                class="type-item color-content font14"
                v-for="(item, index) in type_list"
                :key="index"
                :class="{'active-type': item.id === type_id}"
                :style="{width: 'calc(100% / ' + type_list.length + ')'}"
                @click="type_id = item.id"
            >{{ item.title }}
            </view>
        </view>

        <add-activity-tips ref="add_activity_tips"/>

        <view v-if="page_init_success" class="form">
            <template v-if="type_id === 1">
                <view class="form-item">
                    <view class="top color-content">
                        <text>活动名称</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="text" v-model="form_data.name"
                               placeholder="请输入活动名称"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">主办方</view>
                    <view class="bottom font16">
                        <input class="input" type="text" v-model="form_data.organizer"
                               placeholder="请输入主办方单位名称"/>
                    </view>
                </view>

                <template v-if="!form_data.active_id">
                    <view class="form-item">
                        <view class="top color-content">
                            <text>手机号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="number" v-model="form_data.mobile"
                                   placeholder="请输入真实手机号"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <text>微信号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="text" v-model="form_data.wechat_num"
                                   placeholder="请输入真实微信号"/>
                        </view>
                    </view>
                </template>

                <view class="form-item">
                    <view class="top color-content">活动开始时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.begin_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">活动结束时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.end_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view v-if="!conf.active.news.news_id" class="form-item">
                    <view class="top color-content">活动说明</view>
                    <view class="bottom font16">
                        <textarea
                            class="textarea"
                            maxlength="-1"
                            auto-height="true"
                            v-model="form_data.content"
                            placeholder="请输入活动说明"
                        />
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>活动说明文章</view>
                        <view class="font12 color-sub">
                            绑定文章后，活动说明将会显示文章内容，不会显示上面填写的活动说明
                        </view>
                    </view>
                    <view class="bottom font16">
                        <view class="flex-kai" @click="toSelNews">
                            <view class="view">
                                <view v-if="conf.active.news.news_id">
                                    {{ conf.active.news.news_title || conf.active.news.news_id }}
                                </view>
                                <view v-else class="color-sub">选择文章</view>
                            </view>
                            <view class="flex-all-center">
                                <view
                                    v-if="conf.active.news.news_id"
                                    class="color-sub font12"
                                    style="width: 30px;"
                                    @click.stop="deleteNews"
                                >解绑
                                </view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>
            </template>


            <template v-if="type_id === 3">
                <view class="form-item">
                    <view class="top color-content">活动参与方式</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.enter_types_list"
                                :value="form_options.enter_types_list.findIndex(v => v.value === conf.active.enter_types)"
                                range-key="title"
                                @change="enter_typesChange"
                            >
                                {{ form_options.enter_types_list.find(v => v.value === conf.active.enter_types).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="conf.active.enter_types === 2" class="form-item">
                    <view class="top color-content">
                        <text>活动密码</text>
                        <text v-if="!have_password" class="color-error font16"> *</text>
                        <text class="color-sub font12 pl5">(3-20位)</text>
                    </view>
                    <view class="bottom font16">

                        <input class="input" v-model="conf.active.password" maxlength="20"
                               :placeholder="have_password ? '已设置密码，无需修改密码不用填写' : '请输入活动密码'"/>
                    </view>
                </view>


                <view class="form-item">
                    <view class="top color-content">
                        <view>参与活动需要填写的信息</view>
                        <view class="font12 color-error">注意: 第一项为排行榜显示的姓名, 无法删除</view>
                    </view>
                    <view class="bottom font16">
                        <view class="ptm5">
                            <view class="ptm5 must-submit-list"
                                  v-for="(item, index) in conf.must_submit" :key="index">
                                <view class="flex-row">
                                    <view class="flex-row">
                                        <view style="width: 120px;">
                                            <uni-easyinput v-model="item.title" placeholder="请输入内容"/>
                                        </view>
                                        <template>
                                            <template v-if="index === 0">

                                                <picker :range="['选填', '必填']" :value="item.rules"
                                                        @change="mustItemRulesChange($event, index)">
                                                    <view class="must-rules-picker">
                                                        <text class="color-content font14">
                                                            {{ item.rules === 0 ? '选填' : '必填' }}
                                                        </text>
                                                        <text class="iconfont icon-more color-sub font14"></text>
                                                    </view>
                                                </picker>
                                                <view class="must-rules-picker">
                                                    <text class="color-content font14">
                                                        {{ item.types === 1 ? '文本' : '单选' }}
                                                    </text>
                                                    <text class="iconfont icon-more color-white font14"></text>
                                                </view>
                                            </template>
                                            <template v-else>

                                                <picker :range="['选填', '必填']" :value="item.rules"
                                                        @change="mustItemRulesChange($event, index)">
                                                    <view class="must-rules-picker">
                                                        <text class="color-content font14">
                                                            {{ item.rules === 0 ? '选填' : '必填' }}
                                                        </text>
                                                        <text class="iconfont icon-more color-sub font14"></text>
                                                    </view>
                                                </picker>

                                                <picker :range="['文本', '单选']" :value="item.types - 1"
                                                        @change="mustItemTypesChange($event, index)">
                                                    <view class="must-rules-picker">
                                                        <text class="color-content font14">
                                                            {{ item.types === 1 ? '文本' : '单选' }}
                                                        </text>
                                                        <text class="iconfont icon-more color-sub font14"></text>
                                                    </view>
                                                </picker>
                                            </template>
                                        </template>
                                    </view>
                                    <view v-if="index !== 0" class="delete-rules font14 color-error"
                                          @click="conf.must_submit.splice(index, 1)">删除
                                    </view>
                                </view>

                                <view v-if="item.types === 2" class="pl10">
                                    <view class="must-options-item flex-row"
                                          v-for="(item_, index_) in item.options" :key="index_">
                                        <view class="color-sub delete-rules text-right"
                                              style="width: 20px; padding: 0 5px 0 0;">
                                            {{ index_ + 1 }}:
                                        </view>
                                        <view style="width: 200px;">
                                            <uni-easyinput v-model="item_.text" placeholder="请输入内容"/>
                                        </view>
                                        <view class="delete-rules">
                                            <text class="color-error font14"
                                                  @click="deleteOptionsItem(index, index_)">删除
                                            </text>
                                        </view>
                                    </view>
                                    <view class="flex-row">
                                        <view class="color-sub font14 ptm5" @click="addOption(index)">
                                            + 添加新选项
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <view class="flex-row">
                                <view class="color-primary font14 ptm5" @click="addMust">+ 添加新项</view>
                            </view>
                        </view>
                    </view>
                </view>


                <view class="form-item">
                    <view class="top color-content">报名开始时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="conf.active.submit.begin"
                                end="2038-12-31"
                                :border="false"
                                :clear-icon="true"
                                placeholder="选择报名开始时间"
                            />
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">报名截止时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="conf.active.submit.end"
                                end="2038-12-31"
                                :border="false"
                                :clear-icon="true"
                                placeholder="选择报名截止时间"
                            />
                        </view>
                    </view>
                </view>

            </template>


            <template v-if="type_id === 5">
                <view class="form-item">
                    <view class="top color-content">记录体重上传照片</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                :range="form_options.submit_weight_upload_photo"
                                :value="form_options.submit_weight_upload_photo.findIndex(v => v.value === conf.active.submit_weight_upload_photo)"
                                range-key="title"
                                @change="conf.active.submit_weight_upload_photo = form_options.submit_weight_upload_photo[$event.detail.value].value"
                            >
                                {{ form_options.submit_weight_upload_photo.find(v => v.value === conf.active.submit_weight_upload_photo).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <template v-if="rank_set.weightAddSign">
                    <view class="form-item">
                        <view class="top color-content">打卡记录是否需要审核后才能显示在广场</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker class="view" :range="['需要审核', '无需审核']"
                                        :value="conf.active.not_check"
                                        @change="conf.active.not_check = Number($event.detail.value)">
                                    {{ conf.active.not_check ? '无需审核' : '需要审核' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">打卡次数</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    :range="form_options.not_point_sign_times_list"
                                    :value="form_options.not_point_sign_times_list.findIndex(v => v.value === conf.active.sign_times_type)"
                                    range-key="title"
                                    @change="conf.active.sign_times_type = form_options.not_point_sign_times_list[$event.detail.value].value"
                                >
                                    {{
                                        form_options.not_point_sign_times_list.find(v => v.value === conf.active.sign_times_type).title
                                    }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">打卡备注填写设置</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="form_options.memo_required_list"
                                    :value="form_options.memo_required_list.findIndex(v => v.value === conf.active.memo_required)"
                                    range-key="title"
                                    @change="conf.active.memo_required = form_options.memo_required_list[$event.detail.value].value"
                                >
                                    {{
                                        form_options.memo_required_list.find(v => v.value === conf.active.memo_required).title
                                    }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">打卡图片上传设置</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="form_options.pic_list_required_list"
                                    :value="form_options.pic_list_required_list.findIndex(v => v.value === conf.active.pic_list_required)"
                                    range-key="title"
                                    @change="conf.active.pic_list_required = form_options.pic_list_required_list[$event.detail.value].value"
                                >
                                    {{
                                        form_options.pic_list_required_list.find(v => v.value === conf.active.pic_list_required).title
                                    }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>
                </template>

                <active-share-set :active-id="form_data.active_id" :rank-set="rank_set"
                                  :qrcode-logo.sync="conf.active.qrcode_logo"
                                  :share-image.sync="conf.active.share_image"
                                  :share-title.sync="conf.active.share_title"/>
            </template>

            <template v-if="type_id === 2">
                <active-image-set :active-id="form_data.active_id" :rankSet="rank_set"
                                  :logo.sync="form_data.logo"
                                  :screen-pic.sync="conf.active.screen_pic"
                                  :top-rank-banner.sync="conf.active.top_rank_banner"/>
            </template>

        </view>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view class="login-btn color-white text-center font18 bg-primary" :disabled="loading"
                  @click="save">
                {{ form_data.active_id ? '保存' : '创建活动' }}
            </view>
        </view>


    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import base64 from '@/utils/base64.js'
import {pinyin} from 'pinyin-pro'


export default {
    data() {
        return {
            loading: true,
            page_init_success: false,
            success: false,
            type_list: [
                {title: '基本信息', id: 1},
                {title: '活动设置', id: 3},
                {title: '扩展设置', id: 5},
                {title: '活动图片', id: 2}
            ],
            type_id: 1,
            form_data: {
                name: '',
                wechat_num: '',
                mobile: '',
                organizer: '',
                content: '',
                begin_time: new Date(utils.getDay(0, true, '/') + ' 00:00:00').getTime(),
                end_time: new Date(utils.getDay(30, true, '/') + ' 23:59:59').getTime(),
                logo: ''
            },
            conf: {
                active: {
                    news: {
                        news_id: '',
                        news_title: ''
                    },
                    enter_types: 1,
                    password: '',
                    screen_pic: '',
                    top_rank_banner: [],
                    share_title: '',
                    share_image: '',
                    qrcode_logo: '',
                    submit: {
                        begin: '',
                        end: ''
                    },
                    not_check: 0,
                    memo_required: 0,
                    pic_list_required: 0,
                    sign_times_type: 0,
                    submit_weight_upload_photo: 0
                },
                must_submit: JSON.parse(JSON.stringify(this.xwy_config.defaultMustSubmit))
            },
            top_rank_banner_max_count: 6,
            form_options: {
                enter_types_list: [
                    {title: '自由报名参与活动', value: 1},
                    {title: '需要输入密码才能进入活动', value: 2},
                    {title: '报名需要审核通过才能参与活动', value: 3}
                ],
                submit_weight_upload_photo: [
                    {value: 0, title: '关闭上传图片'},
                    {value: 1, title: '开启上传图片'},
                    {value: 2, title: '必须上传图片'}
                ],
                not_point_sign_times_list: [
                    {value: 0, title: '不限制打卡次数'},
                    {value: 1, title: '只能打卡一次'},
                    {value: 2, title: '每天只能打卡一次'}
                ],
                memo_required_list: [
                    {value: 0, title: '关闭打卡填写备注'},
                    {value: 2, title: '开启打卡填写备注'},
                    {value: 1, title: '打卡必须填写备注'}
                ],
                pic_list_required_list: [
                    {value: 0, title: '关闭打卡上传图片'},
                    {value: 2, title: '开启打卡上传图片'},
                    {value: 1, title: '打卡必须上传图片'}
                ]
            },
            rank_set: {},
            have_password: false
        }
    },

    watch: {
        'conf.active.submit.begin': function () {
            let {begin, end} = this.conf.active.submit
            if (begin && begin.length < 19) {
                begin += '00:00:00'
                this.conf.active.submit.begin = begin
            }
            if (end) {
                const begin_time = new Date(begin.replace(/-/g, '/')).getTime()
                const end_time = new Date(end.replace(/-/g, '/')).getTime()
                if (begin_time > end_time) {
                    this.$uni.showToast('报名开始时间不能大于报名截止时间')
                }
            }
        },
        'conf.active.submit.end': function () {
            let {begin, end} = this.conf.active.submit
            if (end && end.length < 19) {
                end += '23:59:59'
                this.conf.active.submit.end = end
            }
            if (begin) {
                const begin_time = new Date(begin.replace(/-/g, '/')).getTime()
                const end_time = new Date(end.replace(/-/g, '/')).getTime()
                if (end_time < begin_time) {
                    this.$uni.showToast('报名截止时间不能小于报名开始时间')
                }
            }
        }
    },
    onLoad(e) {
        if (e.id) {
            this.form_data.active_id = e.id
        } else {
            this.types = Number(e.types)
            this.$nextTick(() => this.$refs.add_activity_tips.open(this.types))
            this.form_data.logo = this.xwy_config.create_active_default_logo
        }
        uni.setNavigationBarTitle({
            title: e.id ? '修改活动' : `创建${e.name || '活动'}`
        })
        this.$uni.showLoading()


        login.uniLogin(err => {
            if (err?.errMsg) {
                this.loading = false
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.init()
        })
    },
    methods: {
        async init() {
            if (!this.form_data.active_id && !await this.checkUserCanCreateActive()) return

            if (this.form_data.active_id) await this.getDetail()

            this.loading = false
            this.page_init_success = true
            uni.hideLoading()
        },


        async checkUserCanCreateActive() {
            const evn_version = app.globalData['evn_version']
            if (evn_version === 'develop') {
                this.form_data.mobile = 12345678910
                this.form_data.wechat_num = 12345678910
                this.form_data.organizer = '伟杰测试'
            }

            // 开发版和体验版不检查重复创建活动
            if (evn_version === 'develop' || evn_version === 'trial') return true

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.admin/check_user_can_create_active'
            })
            if (res?.status === 1) return true

            uni.hideLoading()
            return this.$uni.showModal(res.info || '暂时不能创建活动', {success: () => uni.navigateBack()})
        },


        enter_typesChange(e) {
            this.conf.active.enter_types = this.form_options.enter_types_list[e.detail.value].value
        },

        async getDetail() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details',
                data: {
                    active_id: this.form_data.active_id
                }
            })

            const detail = res?.data?.active_details
            if (!detail) return this.$uni.showModal(res?.info || '活动获取失败', {
                success: () => uni.navigateBack()
            })
            this.detailInit(detail)
        },


        detailInit(data) {
            this.types = data.types
            this.form_data.name = data.name
            this.form_data.begin_time = data.begin_time * 1000
            this.form_data.end_time = data.end_time * 1000
            if (data.organizer) this.form_data.organizer = data.organizer
            if (data.logo) this.form_data.logo = data.logo
            if (data.content) this.form_data.content = data.content

            this.confDataInit(data.conf)
            if (data.rank_set) this.rank_set = data.rank_set

            uni.hideLoading()
        },

        confDataInit(conf) {
            if (conf.must_submit) this.conf.must_submit = conf.must_submit

            const active = conf.active
            this.conf.active.enter_types = active.enter_types
            if (active.news) this.conf.active.news = active.news
            if (active.password) {
                this.old_password = active.password
                this.have_password = true
            }
            if (active.screen_pic) this.conf.active.screen_pic = active.screen_pic
            if (active.top_rank_banner) this.conf.active.top_rank_banner = active.top_rank_banner
            if (active.share_title) this.conf.active.share_title = active.share_title
            if (active.share_image) this.conf.active.share_image = active.share_image
            if (active.qrcode_logo) this.conf.active.qrcode_logo = active.qrcode_logo
            if (active.not_check) this.conf.active.not_check = active.not_check
            if (active.memo_required) this.conf.active.memo_required = active.memo_required
            if (active.pic_list_required) this.conf.active.pic_list_required = active.pic_list_required
            if (active.sign_times_type) this.conf.active.sign_times_type = active.sign_times_type
            if (active.submit_weight_upload_photo) this.conf.active.submit_weight_upload_photo = active.submit_weight_upload_photo

            if (active.submit) {
                if (active.submit.begin) this.conf.active.submit.begin = active.submit.begin
                if (active.submit.end) this.conf.active.submit.end = active.submit.end
            }
        },


        toSelNews() {
            uni.navigateTo({
                url: '/pages/news/list?type=user&is_sel=true',
                events: {
                    selNews: data => {
                        this.conf.active.news.news_id = data.id
                        this.conf.active.news.news_title = data.title
                    }
                }
            })
        },

        deleteNews() {
            this.conf.active.news.news_id = ''
            this.conf.active.news.news_title = ''
        },


        mustItemRulesChange(e, index) {
            this.conf.must_submit[index].rules = Number(e.detail.value)
        },
        mustItemTypesChange(e, index) {
            const value = Number(e.detail.value) + 1
            this.conf.must_submit[index].types = value
            if (value === 2) {
                this.conf.must_submit[index].options = this.conf.must_submit[index].options || [{text: ''}]
            }
        },
        addMust() {
            this.conf.must_submit.push({
                name: '',
                rules: 0,
                title: '',
                types: 1
            })
        },

        addOption(index) {
            const item = this.conf.must_submit[index]
            item.options.push({text: ''})
            this.$set(this.conf.must_submit, index, item)
        },
        deleteOptionsItem(index, index_) {
            const item = this.conf.must_submit[index]
            if (item.options.length === 1) return this.$uni.showToast('请至少保留一个选项')
            item.options.splice(index_, 1)
            this.$set(this.conf.must_submit, index, item)
        },


        changeImage(key) {
            if (!this.rank_set?.closed_AD) {
                const options = {
                    screen_pic: '无法设置开屏大图，请联系客服设置',
                    top_rank_banner: '无法设置排行榜轮播图，请联系客服设置'
                }
                if (options[key]) return this.$uni.showModal(options[key])
            }

            let url = '/pages/other/image_upload_or_select'
            if (this.form_data.active_id) url += `?active_id=${this.form_data.active_id}`

            uni.navigateTo({
                url,
                events: {
                    newImg: src => {
                        const function_list = {
                            logo: () => this.form_data.logo = src,
                            top_rank_banner: () => this.conf.active.top_rank_banner.push(src)
                        }
                        const default_function = () => this.conf.active[key] = src
                        function_list[key] ? function_list[key]() : default_function()
                    }
                }
            })
        },

        previewImage(urls, current = urls[0]) {
            uni.previewImage({
                urls,
                current
            })
        },

        dataCheck(data) {
            const showToast = title => {
                this.type_id = 1
                this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none')
            }

            if (!data.name) {
                showToast('请输入活动名称')
                return false
            }

            if (!data.active_id) {
                if (!data.mobile) {
                    showToast('请输入手机号')
                    return false
                }
                if (data.mobile.toString().length !== 11) {
                    showToast('手机号长度有误')
                    return false
                }
                if (!data.wechat_num) {
                    showToast('请输入微信号')
                    return false
                }
                const wechat_num = data.wechat_num.toString()
                if (wechat_num.length < 4 || wechat_num.length > 20) {
                    showToast('微信号长度有误')
                    return false
                }
                if (this._utils.isChineseChar(wechat_num)) {
                    showToast('微信号不能输入中文')
                    return false
                }
            }

            return true
        },

        setMustSubmitData() {
            const errModal = content => {
                this.type_id = 3
                this.$uni.showModal(content)
            }


            for (let i = 0; i < this.conf.must_submit.length; i++) {
                const v = this.conf.must_submit[i]
                if (!v.title) {
                    errModal('参与活动需要填写的信息选项填写不完整，请检查。')
                    return false
                }
                v.name = pinyin(v.title, {toneType: 'none', type: 'array'}).join('_')
                if (v.types === 2) {
                    if (!v.options || !v.options.length) {
                        errModal(`${v.title} 至少需要添加一个选项。`)
                        return false
                    }

                    for (let j = 0; j < v.options.length; j++) {
                        const v1 = v.options[j]
                        if (!v1 || !v1.text) {
                            errModal(`${v.title} 有未填写的选项，请检查。`)
                            return false
                        }
                    }
                }
            }

            return true
        },


        confCheck() {
            const showToast = title => {
                this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none')
            }
            const conf = JSON.parse(JSON.stringify(this.conf))
            if (conf.active.enter_types === 2) {
                if (!this.have_password && !conf.active.password) {
                    showToast('请输入活动密码')
                    this.type_id = 3
                    return false
                }
                if (conf.active.password && conf.active.password.length < 3) {
                    showToast('活动密码不得少于3位')
                    this.type_id = 3
                    return false
                }
                if (conf.active.password && conf.active.password.length > 20) {
                    showToast('活动密码不得大于20位')
                    this.type_id = 3
                    return false
                }
                conf.active.password = conf.active.password || this.old_password
            } else {
                conf.active.password = 1
            }


            const {begin, end} = conf.active.submit
            if (begin || end) {
                if (begin && end) {
                    const begin_time = new Date(begin.replace(/-/g, '/')).getTime()
                    const end_time = new Date(end.replace(/-/g, '/')).getTime()
                    if (begin_time > end_time) {
                        this.type_id = 3
                        showToast('报名开始时间不能大于报名结束时间')
                        return false
                    }
                }
            } else {
                delete conf.active.submit
            }


            if (!this.rank_set['weightAddSign']) {
                conf.active.not_check = 0
                conf.active.memo_required = 0
                conf.active.pic_list_required = 0
                conf.active.sign_times_type = 0
            }

            if (!conf.active.not_check) delete conf.active.not_check
            if (!conf.active.memo_required) delete conf.active.memo_required
            if (!conf.active.pic_list_required) delete conf.active.pic_list_required
            if (!conf.active.sign_times_type) delete conf.active.sign_times_type

            if (!conf.active.submit_weight_upload_photo) delete conf.active.submit_weight_upload_photo

            let conf_str = JSON.stringify(conf)
            conf_str = conf_str.replace(/·/g, '-')
            return base64['encode'](conf_str)
        },

        save() {
            if (this.success) return false

            const data = JSON.parse(JSON.stringify(this.form_data))


            if (!this.dataCheck(data)) return false
            if (!this.setMustSubmitData()) return false

            const conf = this.confCheck()
            if (!conf) return false
            data.conf = conf

            data.begin_time /= 1000
            data.end_time /= 1000

            this.saveAjax(data)
        },

        async saveAjax(data) {
            data.types = this.types

            this.loading = true
            this.$uni.showLoading('保存中...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.admin/create_active',
                data
            })
            uni.hideLoading()
            this.loading = false

            if (!res || !res.status) return xwy_api.alert(res && res.info || '保存失败')

            this.updatePageData()

            if (data.active_id && this.id) {
                this.$uni.showToast('保存成功', 'success')
                return uni.navigateBack()
            }

            this.success = true
        },

        // 活动修改/创建成功，如果页面栈有详情或列表页面，更新页面数据
        updatePageData() {
            const pages = getCurrentPages()

            const update_page_list = [
                {
                    route: 'pages/weightHeight/user/details',
                    _function: page => {
                        page.$vm.getDetail(true)
                    }
                },
                {
                    route: 'pages/weightHeight/admin/manage',
                    _function: page => {
                        page.$vm.getDetail()
                    }
                },
                {
                    route: 'pages/activity/user/index',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getActivityList()
                    }
                },
                {
                    route: 'pages/activity/user/activity_list',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getList()
                    }
                }
            ]

            update_page_list.forEach(v => {
                const page = pages.find(n => n.route === v.route)
                if (page) v._function(page)
            })
        },

        lookMyActivityList() {
            const list_page_route = 'pages/activity/user/activity_list'
            const pages = getCurrentPages()
            const list_page_index = pages.findIndex(v => {
                return v.route === list_page_route && v.options.type && v.options.type === 'create'
            })
            if (list_page_index === -1) return this.$uni.redirectTo(`/${list_page_route}?type=create`)

            this.$uni.navigateBack(pages.length - list_page_index - 1)
        }
    }
}
</script>

<style lang="scss">
.page {
    padding-top: 40px;
    padding-bottom: 75px;
}

.type-list {
    position: fixed;
    z-index: 9;
    width: 100vw;
    top: 0;
    left: 0;
}

.type-item {
    height: 40px;
    line-height: 40px;
    text-align: center;
    box-sizing: border-box;
}

.active-type {
    color: #007AFF;
    border-bottom: 2px solid #007AFF;
}

.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}


.form-item .textarea {
    width: 100%;
    line-height: 16px;
    padding: 10px 0;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}

.must-submit-list {
    border-bottom: 1px dashed #eee;
}

.must-rules-picker {
    line-height: 34px;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    margin-left: 5px;
    padding: 0 15px;
}

.delete-rules {
    line-height: 36px;
    padding: 0 10px;
}

.must-options-item {
    padding: 3px 0;
}

.bottom-btn-view {
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.login-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}

.save-success {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 999999;
    box-sizing: border-box;
    padding-top: 10vh;
}

.save-success .icon {
    padding-bottom: 50px;
}

.uni-date-x {
    padding: 0 !important;
}

.uni-date__x-input {
    font-size: 16px !important;
    color: #000;
}

.forms-picker {
    border-radius: 4px;
    border: 1px solid #e5e5e5;
    padding: 0 5px;
    height: 36px;
    line-height: 36px;
}

.input-word {
    line-height: 37px;
    color: #666;
    font-size: 14px;
}

.input-container {
    width: 80px;
    margin: 0 2px;
}

/* #ifdef H5 */

@media screen and (min-width: 500px) {
    .type-list {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }
}

/* #endif */

.ai-sport-list {
    .ai-sport-item {
        position: relative;
        background-color: #f8f8f8;
        border-radius: 5px;
        margin: 5px;
        padding: 10px;

        .ai-sport-item-logo, .task-item-logo {
            margin-right: 10px;
        }
    }
}

.item-controls {
    position: absolute;
    right: 0;
    top: 0;
}

.item-info {
    font-size: 14px;
    color: #495060;
    .item-info-label {
        white-space: nowrap;
        padding-right: 3px;
    }
}

.ai-sport-item-logo, .task-item-logo {
    image {
        display: block;
    }

    .ai-sport-item-logo-image, .task-item-logo-image {
        height: 60px;
        border-radius: 5px;
    }

    .ai-sport-item-logo-image {
        width: 90px;
        min-width: 90px;
    }

    .task-item-logo-image {
        width: 60px;
        min-width: 60px;
    }
}

.ai-sport-item-edit, .ai-sport-types-change {
    width: 90vw;
    padding: 0 10px;
    border-radius: 10px;
    box-sizing: border-box;
}

.ai-sport-item-edit {
    .ai-sport-item-edit-buttons {
        padding: 10px;
        border-top: 1px solid #eee;

        view {
            margin: 0 10px;
            width: 100px;
            line-height: 40px;
            border-radius: 20px;
            text-align: center;
        }
    }
}

.ai-sport-types-change {
    padding-bottom: 10px;

    .ai-sport-type-list {
        .ai-sport-type-item {
            margin: 5px;
            width: calc((100% - 40px) / 4);
            height: 60px;
            padding: 5px;
            box-sizing: border-box;
            border: 1px solid #eee;
            border-radius: 5px;
            text-align: center;
            font-size: 14px;
        }

        .selected {
            border-color: #4cd964;
            position: relative;
        }

        .selected::before {
            content: "已选";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 16px;
            line-height: 16px;
            font-size: 12px;
            text-align: center;
            color: #fff;
            border-radius: 0 0 5px 5px;
            background-color: #4cd964;
        }
    }
}


.reading-reward-rules {
    .reading-reward-rule {
        position: relative;
        background-color: #f8f8f8;
        border-radius: 5px;
        margin: 5px;
        padding: 10px;
        font-size: 14px;
        color: #495060;
    }
}
</style>
