<template>
    <view class="page bg-background">
        <activity-delete-tips v-if="error" :activeId="id"/>


        <view v-if="detail && detail.active_id">
            <!-- #ifndef H5 -->
            <image v-if="detail.logo" class="logo" mode="widthFix" :src="detail.logo"/>
            <!-- #endif -->
            <view class="bg-white p10">
                <view class="color-title font18" @click="copy(id)">{{ detail.name }}</view>
                <view v-if="detail.organizer" class="color-content font16">主办方：{{detail.organizer }}</view>
            </view>

            <template v-if="!detail.rank_set || !detail.rank_set.closed_AD">
                <xwy-ad v-if="!popup_show" :ad_type="4"></xwy-ad>
                <xwy-ad :ad_type="3"></xwy-ad>
            </template>


            <view class="tools bg-white">
                <view class="title flex-kai">
                    <view class="color-title">活动管理</view>
                    <navigator v-if="document_show" url="/pages/other/activity-document?types=5">
                        <text class="iconfont icon-word-file color-light-primary font14"></text>
                        <text class="font14 color-light-primary" style="padding-left: 2px;">活动帮助文档</text>
                    </navigator>
                </view>

                <view class="list flex-row flex-wrap text-center">
                    <view class="item" hover-class="navigator-hover" @click="toEdit">
                        <text class="iconfont icon-edit color-primary font34"></text>
                        <view class="font14 color-content">活动修改</view>
                    </view>


                    <navigator :url="'/pages/activity/admin/activity_user_list?id=' + id" class="item">
                        <text class="iconfont icon-users color-primary font34"></text>
                        <view class="font14 color-content">活动用户</view>
                    </navigator>


                    <navigator :url="'/pages/activity/admin/blacklist?id=' + id" class="item">
                        <text class="iconfont icon-blacklist color-primary font34"></text>
                        <view class="font14 color-content">黑名单用户</view>
                    </navigator>


                    <navigator :url="'../user/record-list?id=' + id" class="item">
                        <text class="iconfont icon-electronic-scale color-primary font34"></text>
                        <view class="font14 color-content">体重记录</view>
                    </navigator>


                    <navigator v-if="clockOpen" class="item"
                               :url="'/pages/clock_in/user/public_sign_list?is_admin=1&id=' + id + '&type=5'">
                        <text class="iconfont icon-sign-in-calendar color-primary font34"></text>
                        <view class="font14 color-content">打卡审核</view>
                    </navigator>


                    <navigator class="item"
                               :url="`/pages/news/list?type=user&vip=${detail.rank_set && detail.rank_set.closed_AD || 0}&active_id=${id}&just_look_active=1`">
                        <text class="iconfont icon-writing font34 color-primary"/>
                        <view class="font14 color-content">文章管理</view>
                    </navigator>


                    <navigator v-if="detail.rank_set && detail.rank_set.closed_AD" class="item"
                               :url="`/pages/category/list?types=8&just_look_active=1&active_id=${id}`">
                        <text class="iconfont icon-dating font34 color-primary"/>
                        <view class="font14 color-content">文章分类</view>
                    </navigator>
                    <view class="item" @click="copyActivityPages">
                        <text class="iconfont icon-copy color-primary font34"></text>
                        <view class="font14 color-content">复制路径</view>
                    </view>

                    <!-- #ifndef H5 -->
                    <view class="item" @click="showWebUrl">
                        <text class="iconfont icon-screen color-primary font34"></text>
                        <view class="font14 color-content">web端管理</view>
                    </view>
                    <!-- #endif -->

                    <navigator class="item" url="/pages/other/contact">
                        <uni-icons type="chatboxes" size="34" color="#2d8cf0"/>
                        <view class="font14 color-content">联系客服</view>
                    </navigator>

                    

                    <view class="item" @click="showActiveSharePopup">
                        <text class="iconfont icon-share color-primary font34"></text>
                        <view class="font14 color-content">转发分享</view>
                    </view>
                </view>
            </view>


            <view class="tools bg-white">
                <view class="title color-title">数据导出</view>
                <view class="list flex-row flex-wrap text-center">
                    <view class="item" @click="exportBackendRanking(36)">
                        <text class="iconfont icon-electronic-scale color-primary font34"></text>
                        <view class="font14 color-content">减重排行榜</view>
                    </view>

                    <navigator class="item" url="/pages/activity/admin/export_record">
                        <text class="iconfont icon-export-excel color-primary font34"></text>
                        <view class="font14 color-content">导出记录</view>
                    </navigator>
                </view>

            </view>

        </view>



        <active-share ref="activeShare"/>


        <web-admin-src-copy-popup ref="web-admin-src-copy-popup"/>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import my_storage from '@/utils/storage.js'

export default {
    data() {
        return {
            loading: true,
            id: '',
            detail: {},
            error: '',
            popup_show: false,
            document_show: this.xwy_config.showActivitySpecificationDocument(30)
        }
    },

    onShareAppMessage() {
        let path = '/pages/weightHeight/user/details?id=' + this.id
        return {
            title: this.detail.conf?.active?.share_title || this.detail.name || '',
            path,
            imageUrl: this.detail.conf?.active?.share_image || this.detail.logo || ''
        }
    },

    computed: {
        clockOpen() {
            return !!this.detail?.rank_set?.['weightAddSign']
        }
    },

    onLoad(e) {
        uni.showLoading({mask: true})
        if (!e.id) {
            this.loading = false
            this.error = '请指定活动id'
            return uni.hideLoading()
        }

        this.id = e.id
        login.uniLogin(err => {
            this.loading = false
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            if (app.globalData['shop_info']?.['shop_set']?.static_url) {
                this.web_base_url = app.globalData['shop_info']['shop_set'].static_url
            }


            this.getDetail()
        })
    },

    methods: {
        getDetail() {
            xwy_api.getActivityDetail(this.id, res => {
                uni.hideLoading()
                if (res.data?.['active_details']) {
                    const detail = res.data['active_details']
                    my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)
                    this.detail = detail
                    if (detail.rank_set?.exam_open) this.exam_open = true
                } else {
                    this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                }
            })
        },

        toEdit() {
            uni.navigateTo({
                url: this.xwy_config.getActivityPath(this.detail.types, 'add') + '?id=' + this.id
            })
        },
        

        uniPopupOpen(ref) {
            this.popup_show = true
            this.$refs[ref].open()
        },

        uniPopupClose(ref) {
            this.popup_show = false
            this.$refs[ref].close()
        },


        showActiveSharePopup() {
            this.$refs.activeShare.open({
                age: 'pages/weightHeight/user/details',
                scene: 'id=' + this.detail.id,
                qrcode_logo: this.detail.rank_set?.closed_AD && this.detail.conf?.active?.qrcode_logo
            })
        },

        showWebUrl() {
            this.$refs['web-admin-src-copy-popup'].open({
                successCallback: () => this.popup_show = true,
                closeCallback: () => this.popup_show = false
            })
        },

        copyActivityPages() {
            let data = 'pages/weightHeight/user/details?id=' + this.id
            if (this.detail.screen_pic) data += `&screen_pic=` + this.detail.screen_pic
            uni.setClipboardData({
                data,
                success() {
                    uni.hideToast()
                    uni.showModal({
                        title: '复制成功',
                        content: '小程序路径地址复制成功，请粘贴到公众号自定义菜单设置里',
                        showCancel: false
                    })
                }
            })
        },

        copy(data) {
            if (!data) return
            uni.setClipboardData({
                data,
                success: () => this.$uni.showToast('复制成功', 'none', 500)
            })
        },

        exportBackendRanking(types) {
            this.$uni.navigateTo(`/pages/other/export-backend-ranking?active_id=${this.id}&types=${types}`)
        },
    }
}
</script>

<style scoped>
.page {
    min-height: 100vh;
    padding-bottom: 15px;
    box-sizing: border-box;
}

.logo {
    display: block;
    width: 100vw;
    height: auto;
}

.tools {
    margin: 10px;
    border-radius: 10px;
    overflow: hidden;
}

.tools .title {
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.item {
    width: calc(100% / 4);
    padding: 10px 5px;
    box-sizing: border-box;
}
</style>
