<template>
        <view v-if="show" class="line-chart">
            <view class="pt10 pl10 pr10 color-white flex-kai">
                <view>
                    <text>最近{{ lookDataType === 'weight' ? '体重' : '身高' }}变化</text>
                    <text class="font12" style="padding-left: 2px;">
                        (单位: {{ lookDataType === 'weight' ? 'kg' : 'cm' }})
                    </text>
                </view>
                <view @click="lookDataTypeSwitch">
                    <text class="font14">查看{{ lookDataType === 'weight' ? '身高' : '体重' }}变化</text>
                    <uni-icons type="forward" size="14" color="#ffffff"/>
                </view>
            </view>
            <view class="line-chart-container">
                <qiun-data-charts type="line" :opts="lineChartOpts" :chartData="lineChartData" />
            </view>
        </view>
</template>

<script>
export default {
    name: "recent-weight-line-chart",
    props: {
        recordList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            show: true,  // 用来控制是否显示，比如父组件需要显示弹窗时，可以通过这个属性控制图标隐藏，避免图表层级过高问题
            lineChartOpts: {
                color: ["#ffffff"],
                background: "",
                padding: [20, 10, 10, 10],
                pixelRatio: uni.getSystemInfoSync().pixelRatio,
                fontColor: "#ffffff",
                enableScroll: false,
                legend: {
                    show: false
                },
                xAxis: {
                    disableGrid: true,
                    fontColor: "#ffffff",
                    fontSize: 12
                },
                yAxis: {
                    gridType: "dash",
                    fontSize: 12,
                    fontColor: "#ffffff"
                },
                extra: {
                    line: {
                        type: "curve",
                        width: 2,
                        activeType: "solid"
                    }
                }
            },
            lineChartData: {},
            lookDataType: 'weight' // weight 体重 or height 身高
        }
    },

    mounted() {
        this.setLineChartData()
    },

    methods: {
        chartShow() {
            this.show = true
            this.setLineChartData()
        },
        chartHide() {
            this.show = false
        },
        
        
        lookDataTypeSwitch() {
            this.lookDataType = this.lookDataType === 'weight' ? 'height' : 'weight'
            this.setLineChartData()
        },

        defaultRecordList() {
            const date = new Date()
            const defaultRecordList = []
            for (let i = 0; i < 7; i++) {
                date.setDate(date.getDate() - 1)
                const month = date.getMonth() + 1
                const day = date.getDate()
                defaultRecordList.push({
                    date: `${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`,
                    weight: 0,
                    height: 0
                })
            }
            return defaultRecordList
        },
        
        setLineChartData() {
            let record_list = JSON.parse(JSON.stringify(this.recordList))
            if (!record_list.length) record_list = this.defaultRecordList()
            if (record_list.length > 7) record_list.length = 7
            
            const value_type = this.lookDataType
            const dates = [], values = []
            let min = 99999999, max = 0
            record_list.reverse().forEach(item => {
                dates.push(item.date)
                values.push(item[value_type])
                if (item[value_type] < min) min = item[value_type]
                if (item[value_type] > max) max = item[value_type]
            })
            
            min = Math.floor(min / 10) * 10
            max = Math.ceil(max / 10) * 10
            if (min === max) max += 10
            
            this.$set(this.lineChartOpts, 'yAxis.min', min)
            this.$set(this.lineChartOpts, 'yAxis.max', max)
            
            this.lineChartData = {
                categories: dates,
                series: [{
                    name: value_type === 'weight' ? "体重" : "身高",
                    lineType: "solid",
                    data: values
                }]
            }
        }
    }
}
</script>

<style lang="scss">
.line-chart {
    background: linear-gradient(to right, #18BB84, #159B9D);
    margin: 10px;
    border-radius: 5px;

    .line-chart-container {
        width: 100%;
        height: 180px;
    }
}
</style>