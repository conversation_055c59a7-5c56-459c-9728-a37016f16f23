<template>
    <view>
        <view class="cell-list flex-row flex-wrap">
            <view class="cell-item">
                <view class="cell-title">
                    <text class="iconfont icon-height color-light-primary"></text>
                    <text>身高</text>
                </view>
                <view>
                    <text class="value">{{ height }}</text>
                    <text class="unit">cm</text>
                </view>
            </view>

            <view class="cell-item">
                <view class="cell-title">
                    <text class="iconfont icon-electronic-scale color-success"></text>
                    <text>体重</text>
                </view>
                <view>
                    <text class="value">{{ weight }}</text>
                    <text class="unit">kg</text>
                </view>
            </view>

            <view class="cell-item">
                <view class="cell-title">
                    <text class="iconfont icon-muscle color-warning"></text>
                    <text>体脂率</text>
                    <view class="question-mark" @click="whatIsBodyFat">?</view>
                    <text class="iconfont icon-edit edit-icon" @click="editBodyFat"></text>
                </view>
                <view>
                    <text class="value">{{ bodyFat }}</text>
                    <text class="unit">%</text>
                </view>
            </view>

            <view class="cell-item">
                <view class="cell-title">
                    <text class="iconfont icon-mbi" style="color: #815BFB;"></text>
                    <text>BMI</text>
                    <view class="question-mark" @click="whatIsBMI">?</view>
                </view>
                <view>
                    <text class="value">{{ bmi }}</text>
                </view>
            </view>
        </view>

        <uni-popup ref="editPopup" type="center">
            <view class="popup-content bg-white">
                <view class="text-center color-title pb10">修改信息</view>
                <uni-forms :modelValue="formData" label-align="right" label-width="60">
                    <!--<uni-forms-item label="身高:">
                        <uni-easyinput v-model="formData.height" placeholder="请输入身高（cm）"/>
                    </uni-forms-item>
                    <uni-forms-item label="体重:">
                        <uni-easyinput v-model="formData.weight" placeholder="请输入体重（kg）"/>
                    </uni-forms-item>-->

                    <uni-forms-item label="生日:">
                        <picker mode="date" :value="formData.birthday" @change="onBirthDateChange">
                            <view class="form-picker flex-kai">
                                <view>
                                    <text v-if="formData.birthday">{{ formData.birthday }}</text>
                                    <text v-else class="color-sub font14">请选择出生年月日</text>
                                </view>
                                <uni-icons type="forward" size="16" color="#c0c4cc"/>
                            </view>
                        </picker>
                    </uni-forms-item>

                    <uni-forms-item label="性别:">
                        <picker :range="genderOptions" range-key="label" :value="formData.gender"
                                @change="onGenderChange">
                            <view class="form-picker flex-kai">
                                <view>
                                    <text v-if="formDataGenderText">{{ formDataGenderText }}</text>
                                    <text v-else class="color-sub font14">请选择性别</text>
                                </view>
                                <uni-icons type="forward" size="16" color="#c0c4cc"/>
                            </view>
                        </picker>
                    </uni-forms-item>
                </uni-forms>

                <view class="flex-all-center">
                    <view class="submit-button bg-light-primary color-white text-center"
                          @click="submitForm">确定
                    </view>
                </view>
                <view class="flex-all-center">
                    <view class="color-sub font14 p10" @click="$refs.editPopup.close()">取消</view>
                </view>
            </view>
        </uni-popup>

        <what-is-bmi-popup ref="whatIsBmiPopup"/>
    </view>
</template>

<script>
import whatIsBmiPopup from '../what-is-bmi-popup.vue'

export default {
    name: "report-data-card",
    components: {whatIsBmiPopup},
    props: ['weight', 'height', 'bmi'],
    data() {
        return {
            gender: null,
            birthday: '',
            formData: {
                height: '',
                weight: '',
                birthday: '',
                gender: null // 默认选择0，表示未选择性别
            },
            genderOptions: [
                {value: 1, label: '男'},
                {value: 0, label: '女'}
            ] // 性别选项
        }
    },

    computed: {
        formDataGenderText() {
            return this.genderOptions.find(item => item.value === this.formData.gender)?.label || ''
        },

        bodyFat() {
            if (this.gender === null || !this.birthday) return 0

            const age = Math.floor((new Date() - new Date(this.birthday)) / 1000 / 60 / 60 / 24 / 365)

            return this._utils.formatDecimal(1.2 * this.bmi + 0.23 * age - 10.8 * this.gender - 5.4)
        }
    },

    mounted() {
        this.getGenderAndBirthday()
    },

    methods: {
        whatIsBMI() {
            this.$refs.whatIsBmiPopup.open()
        },

        whatIsBodyFat() {
            this.$uni.showModal('体脂率是指人体内脂肪重量占体重的比例，又称体脂百分数，反映人体内脂肪含量的多少。健康体脂率范围成年男性在10%~20%，成年女性在20%~30%。人体体脂率应保持在正常范围，若男性体脂率超过20%，女性超过30%则视为肥胖。计算公式：体脂率（%）= 1.2 × BMI + 0.23 × 年龄 - 10.8 × 性别系数（男性1，女性0） - 5.4', {
                title: '什么是体脂率',
                confirmText: '我知道了'
            })
        },

        setGenderAndBirthday(gender, birthday) {
            uni.setStorageSync('weight-height-gender-birthday', {gender, birthday})
        },

        getGenderAndBirthday() {
            const storage = uni.getStorageSync('weight-height-gender-birthday')
            if (storage) {
                this.gender = storage.gender || null
                this.birthday = storage.birthday || ''
            }
        },
        
        editBodyFat() {
            console.log('修改体脂率')
            this.openPopup()
        },

        onBirthDateChange(e) {
            this.formData.birthday = e.detail.value
        },

        onGenderChange(e) {
            this.formData.gender = this.genderOptions[e.detail.value].value
        },

        submitForm() {
            if (!this.formData.birthday) {
                this.$uni.showToast('请选择生日')
                return
            }
            if (this.formData.gender === null) {
                this.$uni.showToast('请选择性别')
                return
            }

            this.birthday = this.formData.birthday
            this.gender = this.formData.gender

            this.setGenderAndBirthday(this.gender, this.birthday)

            this.$refs.editPopup.close() // 关闭弹窗
        },
        openPopup() {
            this.formData.height = this.height || ''
            this.formData.weight = this.weight || ''
            this.formData.birthday = this.birthday || ''
            this.formData.gender = this.gender || null

            this.$refs.editPopup.open() // 打开弹窗
        }
    }
}
</script>

<style lang="scss" scoped>
.cell-list {
    .cell-item {
        position: relative;
        width: calc(50% - 10px);
        padding: 10px;
        margin: 5px;
        box-sizing: border-box;
        border-radius: 5px;
        background-color: #fff;
        box-shadow: 0 0 10px #eee;

        .cell-title {
            font-size: 14px;
            color: #80848f;
            line-height: 24px;
            padding-bottom: 10px;

            .iconfont {
                font-size: 18px;
                padding-right: 2px;
                position: relative;
                top: 1px;
            }

            .question-mark {
                display: inline-block;
                font-size: 12px;
                color: #bbbec4;
                width: 14px;
                height: 14px;
                line-height: 14px;
                text-align: center;
                border: 1px solid #bbbec4;
                border-radius: 8px;
                position: relative;
                top: -5px;
                left: 2px;
                transform: scale(.8);
            }

            .edit-icon {
                position: absolute;
                top: 10px;
                right: 10px;
                font-size: 16px;
                color: #bbbec4;
            }
        }

        .value {
            font-size: 18px;
            font-weight: 600;
        }

        .unit {
            font-size: 14px;
            color: #80848f;
            padding-left: 2px;
        }
    }
}

.popup-content {
    width: 300px;
    padding: 10px;
    border-radius: 10px;

    .submit-button {
        width: 150px;
        line-height: 44px;
        border-radius: 22px;
    }

    .form-picker {
        border: 1px solid #e5e5e5;
        border-radius: 4px;
        height: 34px;
        line-height: 34px;
        padding: 0 10px;
    }
}
</style>