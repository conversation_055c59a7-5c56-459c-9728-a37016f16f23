<template>
    <view class="clearfix clear bg-background">
        <view class="fr color-light-primary font14 pt10 pr10" @click="analysisReport">
            <text class="iconfont icon-answer-sheet color-light-primary font14"></text>
            体重报告
            <text class="iconfont icon-more color-light-primary font14"></text>
        </view>

        <uni-popup ref="popup" @touchmove.prevent.stop="" :is-mask-click="false">
            <view class="report-popup bg-white">
                <view class="title text-center">体重报告</view>
                <scroll-view class="report-content" scroll-y="true">
                    <data-card :weight="weight" :height="height" :bmi="bmi"/>

                    <view v-if="reportLoading" class="text-center" style="padding-top: 80px;">
                        <load-ani/>
                        <view class="color-sub font14 pt5">报告加载中</view>
                    </view>

                    <view v-if="!reportLoading">
                        <template v-if="reportContent">
                            <text v-if="reportContentString" class="report-text" space="nbsp">
                                {{ reportContent }}
                            </text>
                            <template v-else>
                                <view v-for="(item, index) in reportContent" :key="index" class="pb10 pt10">
                                    <view class="color-title font18 pb5">{{ item.title }}</view>
                                    <text space="nbsp" class="color-content report-text">
                                        {{ item.content }}
                                    </text>
                                </view>
                            </template>
                        </template>
                        <view v-else class="text-center color-sub" style="padding-top: 30vh;">
                            暂无体重报告
                        </view>
                    </view>
                </scroll-view>

                <view v-if="!reportLoading" class="flex-all-center">
                    <view class="color-sub font14 p10" @click="close">关闭</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import __api from '@/pages/weightHeight/api'

import dataCard from './report-data-card.vue'

export default {
    name: "analysis-report",
    components: {dataCard},

    props: {
        activeId: {
            type: String,
            default: ''
        },
        data: {
            type: Object,
            default: () => ({})
        }
    },

    data() {
        return {
            reportLoading: false,
            reportContent: null,
            reportContentString: false
        }
    },

    computed: {
        weight() {
            return this.data?.weight || ''
        },

        height() {
            return this.data?.height || ''
        },

        bmi() {
            return this.data?.BMI?.value || ''
        }
    },

    methods: {
        show() {
            this.$emit('show')
            this.$refs.popup.open()
        },

        close() {
            this.$emit('close')
            this.$refs.popup.close()
        },

        async analysisReport() {
            this.show()

            if (this.reportContent) return

            this.reportLoading = true

            const data = {page: 1, perpage: 30, myself: 1}
            if (this.activeId) data.active_id = this.activeId
            const {list} = await __api.getRecordList(data)
            if (!list?.length) {
                this.reportLoading = false
                this.$uni.showToast('没有体重记录')
                return this.close()
            }

            const record_str = JSON.stringify(list.map(item => ({
                BMI: {
                    value: item.BMI.value,
                    title: item.BMI.category
                },
                time: item.create_time,
                height: item.height,
                weight: item.weight
            })))

            const subject = `体重数据（JSON字符串格式）：${record_str}。

请基于提供的体重数据，完成以下三个任务：

体重波动分析：分析体重的变化趋势，识别是否存在异常波动或规律性变化。
BMI指数追踪：计算BMI（体质指数），评估体重是否在健康范围内，并针对超重或偏轻的情况提供专业建议和科学的减重或增重策略。
个性化计划：根据体重变化趋势和BMI指数，制定个性化的饮食、作息和运动建议，帮助用户更科学、可持续地进行体重管理。
数据返回要求：

请使用以下JSON数据格式呈现结果：{"fluctuate": "体重波动分析", "BMI": "BMI指数追踪", "plan": "个性化计划"}。
不要使用Markdown格式(#、##、###、####、-、*等)，以纯文本形式返回。需要换行的地方请使用换行符（\\n）。
示例：
{"fluctuate": "", "BMI": "", "plan": "根据体重波动分析和 BMI 指数追踪的结果，建议制定个性化的饮食、作息和运动计划，以帮助更科学、可持续的进行体重管理。\\n\\n饮食建议:.....\\n\\n作息建议:.....\\n\\n运动建议:....."}。

请确保 体重波动分析(fluctuate)、BMI指数追踪(BMI)、个性化计划(plan) 每一项都有详细的分析或计划，并且分析计划要具体，且符合科学健康管理的原则。`

            const res = await this.xwy_api.request({
                url: 'front.flat.active.chat.user.chat/system_product_result',
                data: {
                    system_product: 'ai_get_content',
                    subject,
                    used_company: 'stepfun'
                }
            })

            console.log(res);

            const content = res?.data?.res?.content?.[0]
            if (!content) {
                this.reportLoading = false
                return
            }

            try {
                const report = JSON.parse(content.replace(/\n- /g, '\n'))
                const {fluctuate, BMI, plan} = report
                this.reportContent = [
                    {title: '体重波动分析', content: fluctuate},
                    {title: 'BMI指数追踪', content: BMI},
                    {title: '个性化计划', content: plan}
                ]
            } catch (e) {
                console.log(e);
                this.reportContent = content.replace(/"fluctuate": /g, "体重波动分析\n")
                    .replace(/"BMI": /g, "\n\nBMI指数追踪\n")
                    .replace(/"plan": /g, "\n\n个性化计划\n")
                    .replace(/^{/, '')
                    .replace(/}$/, '')
                    .replace(/\n- /g, '\n')
                    .replace(/\\n/g, '\n')
                    .replace(/",/g, '')
                    .replace(/"/g, '')
            }


            this.reportContentString = typeof this.reportContent === 'string'
            this.reportLoading = false
        },
    }
}
</script>

<style lang="scss" scoped>
.report-popup {
    width: 700rpx;
    border-radius: 10px;

    .title {
        padding: 10px;
    }

    .report-content {
        width: 100%;
        box-sizing: border-box;
        padding: 10px;
        height: 70vh;
    }
}

.report-text {
    word-break: break-all;
    white-space: normal;
}
</style>