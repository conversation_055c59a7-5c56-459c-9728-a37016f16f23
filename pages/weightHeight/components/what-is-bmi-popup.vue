<template>
    <view>
        <uni-popup ref="uniPopup" @close="popupClose">
            <view class="popup--- bg-white">
                <view class="color-title text-center p10">什么是BMI</view>
                <view class="color-content p5" v-for="item in explains" :key="item">{{ item }}</view>
                <view class="flex-all-center">
                    <view class="p10 font14 color-sub" @click="popupClose">我知道了</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "what-is-bmi-popup",
    data() {
        return {
            explains: [
                '身体质量指数(BMI)又称为体重指数、体质指数，该指标是通过体重(千克)除以身高(米)的平方计算得来，这个公式所得比值在一定程度可以反映人体密度。',
                '因计算方式简单，现在被普遍用于评价人体的营养状况、胖瘦程度或身体发育水平。',
                '一般情况下，我国成年人身体质量指数在18.5至23.9内属正常范围。低于18.5表示体重过低，高于23.9表示超重或肥胖。'
            ]
        }
    },
    methods: {
        open() {
            this.$refs.uniPopup.open()
        },
        popupClose() {
            this.$refs.uniPopup.close()
            this.$emit('close')
        }
    }
}
</script>

<style lang="scss">
.popup--- {
    width: 320px;
    padding: 10px;
    border-radius: 10px;
}
</style>