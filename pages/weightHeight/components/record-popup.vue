<template>
    <view>
        <uni-popup ref="uniPopup" @close="popupClose">
            <view class="record-popup bg-white">
                <view class="title text-center">身高体重记录</view>

                <view v-if="date" class="text-center font14 color-sub">{{ date }}</view>

                <uni-forms label-position="top">
                    <uni-forms-item label="体重(kg)" required>
                        <uni-easyinput v-model="weight" type="digit" 
                                       :placeholder="`请输入体重 (${minWeight} - ${maxWeight})`"/>
                    </uni-forms-item>
                    <uni-forms-item label="身高(cm)" required>
                        <uni-easyinput v-model="height" type="digit" 
                                       :placeholder="`请输入身高 (${minHeight} - ${maxWeight})`"/>
                    </uni-forms-item>
                    <uni-forms-item v-if="submitWeightUploadPhoto" label="图片"
                                    :required="submitWeightUploadPhoto === 2">
                        <view class="image-container flex-row flex-wrap">
                            <view class="image-item" v-for="(item, index) in pic_list" :key="item">
                                <image class="image" :src="item" mode="aspectFill" @click=""/>
                                <view class="image-delete flex-all-center" @click="pic_list.splice(index, 1)">
                                    <uni-icons type="closeempty" color="#ffffff" size="24px"/>
                                </view>
                            </view>
                            <view class="add-image flex-all-center bg-background" v-if="pic_list.length < 3"
                                  @click="addImage">
                                <uni-icons type="plusempty" color="#eeeeee" size="48px"/>
                            </view>
                        </view>
                    </uni-forms-item>
                </uni-forms>
                
                <view class="flex-all-center text-center">
                    <view class="save-button bg-success color-white" @click="verify">保存</view>
                </view>
                <view class="flex-all-center">
                    <view class="p10 font14 color-sub" @click="cancel">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "record-popup",
    props: {
        activeId: {
            type: String,
            default: ''
        },
        lastHeight: {
            type: Number,
            default: 0
        },
        submitWeightUploadPhoto: {
            type: Number,
            default: 0
        }
    },
    
    data() {
        return {
            height: '',
            weight: '',
            pic_list: [],
            date: '',
            minHeight: 30,
            maxHeight: 300,
            minWeight: 1,
            maxWeight: 1000
        }
    },

    mounted() {
        
    },

    methods: {
        show(date = '') {
            if (date) this.date = date
            this.height = this.lastHeight || ''
            this.pic_list = []
            this.$refs.uniPopup.open()
        },

        cancel() {
            this.$refs.uniPopup.close()
            this.popupClose()
        },

        popupClose() {
            this.$emit('close')
        },

        addImage() {
            this.$uni.navigateTo(`/pages/other/image_upload_or_select?active_id=${this.activeId}`, {
                events: {
                    newImg: src => {
                        if (this.pic_list.includes(src)) return this.$uni.showToast('图片已存在')
                        this.pic_list.push(src)
                    }
                }
            })
        },

        previewImage(src) {
            this.$uni.previewImage({
                urls: this.pic_list,
                current: src
            })
        },

        verify() {
            if (!this.weight) return this.$uni.showToast('请输入体重')
            if (!this.height) return this.$uni.showToast('请输入身高')
            const height = Math.floor(this.height * 10) / 10
            const weight = Math.floor(this.weight * 10) / 10
            if (isNaN(height)) return this.$uni.showToast('身高只能输入数字')
            if (isNaN(weight)) return this.$uni.showToast('体重只能输入数字')
            
            const {minHeight, maxHeight, minWeight, maxWeight} = this
            if (weight < minWeight || weight > maxWeight) {
                return this.$uni.showToast(`体重范围为 ${minWeight}kg - ${maxWeight}kg`)
            }
            if (height < minHeight || height > maxHeight) {
                return this.$uni.showToast(`身高范围为 ${minHeight}cm - ${maxHeight}cm`)
            }

            if (this.submitWeightUploadPhoto === 2 && !this.pic_list.length) {
                return this.$uni.showToast('请上传图片')
            }

            
            this.save(this._utils.formatDecimal(height), this._utils.formatDecimal(weight))
        },
        
        async save(height, weight) {
            const data = {height, weight}
            if (this.activeId) data.active_id = this.activeId
            if (this.pic_list.length) {
                data.pic_list = this._utils.base64['encode'](JSON.stringify(this.pic_list))
            }
            if (this.date) data.submit_date = this.date
            
            this.$uni.showLoading('保存中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.lose_weight.user/submit_weight_height',
                data
            })
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$emit('fail', res?.info || '记录失败')
            this.$emit('success', '已记录')
            this.$refs.uniPopup.close()
        }
    }
}
</script>

<style lang="scss">
.record-popup {
    width: 680rpx;
    padding: 20rpx 40rpx;
    box-sizing: border-box;
    border-radius: 5px;
    
    .title {
        padding-bottom: 10px;
        margin-bottom: 10px;
    }
    
    .save-button {
        width: 200px;
        line-height: 44px;
        border-radius: 22px;
    }

    .image-container {
        .image-item, .add-image {
            width: 170rpx;
            height: 170rpx;
            margin: 40rpx 40rpx 40rpx 0;

            &:nth-child(3n) {
                margin-right: 0;
            }
        }

        .image-item {
            position: relative;

            .image {
                width: 100%;
                height: 100%;
                display: block;
            }

            .image-delete {
                position: absolute;
                top: 5px;
                right: 5px;
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background-color: rgba(0, 0, 0, .5);
            }
        }

        .add-image {
            border-radius: 10px;
        }
    }
}

/* #ifdef H5 */
// 屏幕大于500px
@media screen and (min-width: 500px) {
    .record-popup {
        width: 400px;
    }
}
/* #endif */
</style>