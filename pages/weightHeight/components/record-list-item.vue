<template>
    <view class="item">
        <view v-if="showUserInfo" class="flex-kai">
            <view class="user-info flex-row">
                <image class="headimg" :src="item['headimg'] || ''" mode="aspectFill"/>
                <view class="nickname ellipsis">{{ item['nickname'] || '' }}</view>
            </view>
            <view class="font12 color-sub">{{ item['submit_date'] }}</view>
        </view>

        <view class="flex-center">
            <view class="data">
                <view>
                    <text class="font18">{{ item['height'] }}</text>
                    <text class="font14">cm</text>
                </view>
                <view class="pt5 font14 color-sub">身高</view>
            </view>

            <view class="cut-off-rule"></view>

            <view class="data">
                <view>
                    <text class="font20">{{ item['weight'] }}</text>
                    <text class="font14">kg</text>
                </view>
                <view class="pt5 font14 color-sub">体重</view>

                <view class="diff" v-if="item['diff']">
                    <template v-if="item['diff'] > 0">
                        <uni-icons type="arrow-up" size="12px" color="#ed3f14"/>
                        <text class="font12 color-error">{{ item['diff'] }}</text>
                    </template>
                    <template v-else>
                        <uni-icons type="arrow-down" size="12px" color="#19be6b"/>
                        <text class="font12 color-success">{{ Math.abs(item['diff']) }}</text>
                    </template>
                </view>

                <view v-if="item['pic_list'].length" class="image-icon flex-all-center"
                      @click="lookPic(item['pic_list'])">
                    <uni-icons type="images" size="24px" color="#80848f"/>
                </view>
            </view>

            <view class="cut-off-rule"></view>

            <view class="data">
                <view>
                    <text class="font18">{{ item['BMI'].value }}</text>
                    <text class="font14 pl2 color-sub">({{ item['BMI'].category }})</text>
                </view>
                <view class="pt5 font14 color-sub">BMI</view>
            </view>
        </view>

        <view v-if="!showUserInfo || !item['active_id']" class="clearfix clear pt10">
            <view class="fr">
                <text v-if="!showUserInfo" class="font12 color-sub">{{ item['submit_date'] }}</text>
                <text class="iconfont icon-delete color-sub font12 pl10" @click="deleteItem(item.id)"></text>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "record-list-item",
    props: {
        item: {
            type: Object,
            default: () => {
                return {
                    id: null,
                    active_id: '',
                    height: 0,
                    weight: 0,
                    BMI: {
                        value: 0,
                        category: ''
                    },
                    date: '',
                    create_time: '',
                    headimg: '',
                    nickname: ''
                }
            }
        },
        showUserInfo: {
            type: Boolean,
            default: false
        }
    },

    methods: {
        lookPic(urls) {
            this.$uni.previewImage({urls})
        },

        async deleteItem(id) {
            const {confirm} = await this.$uni.showModal('确定删除该记录', {showCancel: true})
            if (!confirm) return

            this.$uni.showLoading('删除中...')
            const res = await this.xwy_api.deleteRecords(88, id)
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '删除失败')

            this.$uni.showToast('已删除')
            this.$emit('deleteItem')
        }
    }
}
</script>

<style lang="scss" scoped>
.item {
    padding: 10px;
    background: #fff;
    border-radius: 5px;

    .user-info {
        width: calc(100% - 130px);
        padding-bottom: 10px;

        .headimg {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: block;
        }

        .nickname {
            line-height: 40px;
            padding-left: 5px;
            width: calc(100% - 40px);
        }
    }

    .data {
        width: 30%;
        text-align: center;
        position: relative;

        .diff {
            position: absolute;
            right: 0;
            top: -10px;
        }

        .image-icon {
            position: absolute;
            right: 0;
            bottom: -4px;
            width: 30px;
            height: 30px;
        }
    }

    .cut-off-rule {
        width: 1px;
        height: 30px;
        margin: auto 0;
        background-color: #f5f5f5;
    }
}
</style>