<template>
    <view class="weight-goal bg-white">
        <view class="flex-kai">
            <view class="left-right flex-all-center">
                <view class="text-center">
                    <view class="color-title">
                        <text class="font20">{{ initialWeight }}</text>
                        <text class="font14">kg</text>
                    </view>
                    <view class="color-sub font12">初始体重</view>
                </view>
            </view>
            <view class="middle flex-all-center">
                <view @click="changeWeightGoal">
                    <torus-progress-bar
                        :progress="weightLossPercentage"
                        :textList="torusProgressBarTextList"
                        width="150px"
                        ringWidth="16px"
                        ringColor="#09CCBE"
                        targetColor="#09CCBE"
                    />
                </view>
            </view>
            <view class="left-right flex-all-center">
                <view class="text-center">
                    <view class="color-title">
                        <text class="font20">{{ weight }}</text>
                        <text class="font14">kg</text>
                    </view>
                    <view class="color-sub font12">目前体重</view>
                </view>
            </view>
        </view>

        <view v-if="weightGoal" class="text-center font14 color-sub">
            <text v-if="weightDiff > 0">累计减重{{ weightDiff }}kg({{ weightLossPercentage }}%),</text>
            <text>
                <template v-if="needLoseWeight > 0">
                    还差{{ needLoseWeight }}kg({{ percentageToGoal }}%)达成目标
                </template>
                <template v-else>已达成目标</template>
            </text>
        </view>
    </view>
</template>

<script>
import torusProgressBar from './torus-progress-bar.vue'

export default {
    name: "weight-goal",
    components: {torusProgressBar},
    props: ['activeId', 'weight'],
    data() {
        return {
            // 初始体重值，第一次记录的体重值
            initialWeight: 0,
            // 目标体重，用户设置的目标体重
            weightGoal: 0
        }
    },

    computed: {
        // 已减体重
        weightDiff() {
            return this._utils.formatDecimal(this.initialWeight - this.weight)
        },

        // 目标减重, 需要减多少kg才算完成目标
        targetWeightLoss() {
            return this._utils.formatDecimal(this.initialWeight - this.weightGoal)
        },

        // 减重百分比
        weightLossPercentage() {
            const percentage = Math.round(this.weightDiff / this.targetWeightLoss * 100)
            if (percentage < 0) return 0
            if (percentage > 100) return 100
            return this._utils.formatDecimal(percentage)
        },

        // 还需要减重多少才能完成目标
        needLoseWeight() {
            return this._utils.formatDecimal(this.weight - this.weightGoal)
        },

        // 还差多少完成的 百分比
        percentageToGoal() {
            return Math.round(100 - this.weightLossPercentage)
        },

        torusProgressBarTextList() {
            return [
                {
                    text: this.weightGoal + 'kg',
                    color: '#000000',
                    fontSize: '28px'
                },
                {
                    text: '设定目标',
                    color: '#09CCBE',
                    fontSize: '14px'
                }
            ]
        }
    },

    mounted() {
        this.getWeightGoal()
        this.getInitialWeight()
    },

    methods: {
        getWeightGoal() {
            this.WeightGoalStorageKey = 'weight_goal' + (this.activeId || '')
            this.weightGoal = uni.getStorageSync(this.WeightGoalStorageKey) || 0
        },

        async getInitialWeight() {
            const data = {
                myself: 1,
                order_types: 1, // 按时间升序获取
                page: 1,
                perpage: 1
            }
            if (this.activeId) data.active_id = this.activeId

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.lose_weight.user/user_weight_height_list',
                data
            })
            const weight = res?.data?.list?.data?.[0]?.weight || 0
            this.initialWeight = this._utils.formatDecimal(weight / 1000)
        },

        async changeWeightGoal() {
            let weightGoal = this.weightGoal.toString()
            if (weightGoal === '0') weightGoal = ''
            const res = await this.$uni.showModal(weightGoal, {
                title: '请输入目标体重 (kg)',
                showCancel: true,
                editable: true,
                placeholderText: '请输入目标体重 (kg)'
            })

            if (!res.confirm) return

            const newWeightGoal = Number(res.content)
            
            if (isNaN(newWeightGoal) || newWeightGoal < 1 || newWeightGoal > 1000) {
                this.$uni.showToast('请输入正确的目标体重')
                return this.changeWeightGoal()
            }

            // 最多保留两位小数
            this.weightGoal = this._utils.formatDecimal(newWeightGoal)
            this.saveWeightGoal()
        },

        saveWeightGoal() {
            uni.setStorageSync(this.WeightGoalStorageKey, this.weightGoal)
        }
    }
}
</script>

<style lang="scss">
.weight-goal {
    padding: 10px;
    border-radius: 10px;
}

.left-right {
    width: calc((100% - 150px) / 2);
}

.middle {
    width: 150px;
}
</style>