<template>
    <view class="bmi-progress-bar flex-row">
        <view class="bmi-progress-bar-item"
              v-for="(item, index) in bmi_progress_data.list" :key="index"
              :style="{backgroundColor: item.color, width: `${(item.max - item.min) / bmi_progress_data.all * 100}%`}">
            <view class="bmi-progress-bar-item-text" :style="{color: item.color}">
                {{ item.category }}
            </view>
            <template v-if="index !== 0">
                <view class="bmi-progress-bar-top-value">{{ item.min }}</view>
                <view class="bmi-progress-bar-value-staff"></view>
            </template>
            <view v-if="user_bmi_bar_index === index" class="bmi-progress-bar-user-bmi"
                  :style="{borderColor: item.color, left: `${(bmi_value - item.min) / (item.max - item.min) * 100}%`}"></view>
        </view>
    </view>
</template>

<script>
const BMI_MIN = 15, BMI_MAX = 32
const BMI_ALL = BMI_MAX - BMI_MIN

export default {
    name: "bmi-progress-bar",
    props: {
        bmi: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            bmi_progress_data: {
                all: BMI_ALL,
                min: BMI_MIN,
                max: BMI_MAX,
                list: [
                    {
                        category: '偏瘦',
                        color: '#57AEFE',
                        min: BMI_MIN,
                        max: 18.4
                    },
                    {
                        category: '正常',
                        color: '#2CD994',
                        min: 18.5,
                        max: 23.9
                    },
                    {
                        category: '偏胖',
                        color: '#FDB64C',
                        min: 24,
                        max: 27.9
                    },
                    {
                        category: '肥胖',
                        color: '#FF5E5E',
                        min: 28,
                        max: BMI_MAX
                    }
                ]
            }
        }
    },

    computed: {
        // 因为bmi最小值和最大值是固定了，为了防止用户的bmi值超出范围，所以需要对bmi值进行处理
        bmi_value() {
            const bmi = this.bmi
            const {min, max} = this.bmi_progress_data
            if (bmi < min) return min
            if (bmi > max) return max
            return bmi
        },

        user_bmi_bar_index() {
            const list = this.bmi_progress_data.list
            const bmi_value = this.bmi_value
            return list.findIndex(item => item.min <= bmi_value && item.max >= bmi_value)
        }
    },

    mounted() {

    },

    methods: {}
}
</script>

<style lang="scss">

.bmi-progress-bar {
    width: 100%;
    padding: 30px 0;
    box-sizing: border-box;

    $bar-size: 8px;

    .bmi-progress-bar-item {
        height: $bar-size;
        position: relative;

        &:first-child {
            border-radius: calc(#{$bar-size} / 2) 0 0 calc(#{$bar-size} / 2);
        }

        &:last-child {
            border-radius: 0 calc(#{$bar-size} / 2) calc(#{$bar-size} / 2) 0;
        }


        .bmi-progress-bar-item-text {
            position: absolute;
            bottom: -30px;
            left: 0;
            width: 100%;
            text-align: center;
        }

        .bmi-progress-bar-top-value {
            position: absolute;
            top: -30px;
            left: 0;
            width: 40px;
            text-align: center;
            margin-left: -20px;
            font-size: 14px;
            color: #999;
        }

        .bmi-progress-bar-value-staff {
            position: absolute;
            z-index: 1;
            left: 0;
            top: 0;
            width: 2px;
            height: $bar-size;
            background-color: #fff;
        }

        .bmi-progress-bar-user-bmi {
            $offset: $bar-size * .5 - $bar-size;
            position: absolute;
            z-index: 2;
            top: 0;
            margin-top: $offset;
            margin-left: $offset * 2;
            width: $bar-size;
            height: $bar-size;
            border-radius: 50%;
            border-width: calc(#{$bar-size} / 2);
            border-style: solid;
            background-color: #fff;
        }
    }
}
</style>