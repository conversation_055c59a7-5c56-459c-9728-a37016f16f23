<template>
	<view class="content" :style="{ width: width, height: width }">
		<view class="ring-wrap" :style="{ width: width, height: width }">
			<view class="ring" :style="ringStyle">
			</view>
			<view 
				class="ring-mask"
				:style="{
					width: `calc(${width} - ${ringWidth} - ${ringWidth})`,
					height: `calc(${width} - ${ringWidth} - ${ringWidth})`,
					top: ringWidth,
					left: ringWidth,
					backgroundColor: backgroundColor
				}"
			></view>
			<view class="ring-text">
				<view 
					v-for="(item, index) in textList" :key="index"
					:style="{
						fontSize: item.fontSize,
						color: item.color
					}"
				>{{item.text}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:"torus-progress-bar",
		props: {
			progress: {
				default: 0
			},
			ringColor: {
				default: '#19be6b'
			},
            targetColor: {
                type: String,
                default: '#ff9900'
            },
			maskColor: {
				default: '#e5e5e5'
			},
			backgroundColor: {
				default: '#ffffff'
			},
			ringWidth: {
				default: '18px'
			},
			width: {
				default: '150px'
			},
			textList: {
				default: []
			}
		},
        
        data() {
            return {
                ringStyle: ''
            }
        },
        
        watch: {
            progress() {
                clearInterval(this.interval)
                this.drawRing(0)
                this.setProgress()
            }
        },

        mounted() {
            this.setProgress()
        },
        
        methods: {
            setProgress() {
                const progress = this.progress
                const onStep = 2

                let _progress = 0
                this.interval = setInterval(() => {
                    _progress += onStep
                    if (_progress > progress) {
                        _progress = progress
                        this.drawRing(_progress)
                        clearInterval(this.interval)
                    }
                    this.drawRing(_progress)
                }, 20)
            },

            drawRing(_progress) {
                const {ringColor, targetColor, maskColor} = this
                const isProgressFull = _progress >= 100
                const color = isProgressFull ? targetColor : ringColor
                const startColor = color
                const endColor = isProgressFull ? color : `${color}80` // 100%以上不需要透明渐变

                this.ringStyle =  `background: conic-gradient(${startColor} ${_progress / 2}%, ${endColor} ${_progress}%, ${maskColor} ${_progress}% 100%)`
            }
        }
	}
</script>

<style>
	.content {
		display: inline-block;
		padding: 10px;
	}
	.ring-wrap {
		position: relative;
	}

	.ring {
		width: 100%;
		height: 100%;
		border-radius: 50%;
	}

	.ring-mask {
		position: absolute;
		border-radius: 50%;
	}

	.ring-text {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		text-align: center;
	}
</style>