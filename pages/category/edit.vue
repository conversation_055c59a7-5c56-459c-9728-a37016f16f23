<template>
    <view class="page">

        <view class="flex-all-center pt10">

            <image v-if="logo" class="logo" :src="logo" mode="aspectFill"/>
            <view v-else class="logo flex-all-center bg-background">
                <uni-icons type="image" size="60" color="#c6cbe0"/>
            </view>
        </view>
        <view class="flex-all-center">
            <view class="p10" @click="changeLogo" hover-class="navigator-hover">
                <text class="color-primary">更换logo</text>
                <uni-icons type="forward" color="#2d8cf0"/>
            </view>
        </view>

        <view class="form">
            <view class="form-item">
                <view class="top color-content">
                    <text>名称</text>
                    <text class="color-error font16"> *</text>
                </view>
                <view class="bottom font16">
                    <input class="input" type="text" v-model="name" placeholder="请输入名称"/>
                </view>
            </view>


            <view class="form-item">
                <view class="top color-content">序号</view>
                <view class="bottom font16">
                    <input class="input" type="number" v-model="sort_num"
                           placeholder="数字越小排在越前(不填默认0)"/>
                </view>
            </view>

            <template v-if="types === 38">
                <view class="form-item">
                    <view class="top color-content">打卡奖励的积分数</view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="conf_set.submit_sign.reward_integral"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>每人每个月提交的打卡次数上限</view>
                        <view class="color-sub font14">超过这个次数无法提交打卡</view>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="conf_set.submit_sign.limit_num"/>
                    </view>
                </view>
            </template>
        </view>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view class="login-btn color-white text-center font18 bg-primary" @click="add">保存</view>
        </view>

    </view>
</template>

<script>
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'

import sportsCenterApi from '@/pages/sports-center/api'


export default {
    data() {
        return {
            id: '',
            types: 0,
            name: '',
            sort_num: '',
            logo: '',
            conf_set: {
                submit_sign: {
                    reward_integral: '',
                    limit_num: ''
                }
            }
        }
    },

    onLoad(e) {
        if (e.id) this.id = Number(e.id)
        if (e.active_id) this.active_id = e.active_id
        this.types = Number(e.types)
        this.$uni.setNavigationBarTitle(e.id ? '修改' : '添加')

        this.$uni.showLoading('加载中...')

        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            if (this.id) return this.getDetail()

            uni.hideLoading()
        })
    },
    
    methods: {
        async getDetail() {
            const res = await xwy_api.request({
                url: 'front.user.category/category_details',
                data: {
                    category_id: this.id
                }
            })
            uni.hideLoading()
            
            if (res?.data?.['category_details']) {
                const detail = res.data['category_details']
                if (detail.name) this.name = detail.name
                if (detail.sort_num) this.sort_num = detail.sort_num
                if (detail.logo) this.logo = detail.logo

                if (this.types === 38 && detail.conf_set?.submit_sign) this.conf_set = detail.conf_set
            }
        },

        changeLogo() {
            this.$uni.navigateTo('/pages/other/image_upload_or_select', {
                events: {
                    newImg: src => {
                        this.logo = src
                    }
                }
            })
        },

        async nameCheck() {
            // 30 运动中心运动类型不允许重复
            if (!this.types || this.types !== 30) return true

            const err = () => {
                this.$uni.showToast(`${this.name}已存在，无需重复添加`)
                return false
            }

            this.$uni.showLoading()
            const system_types = await sportsCenterApi.getSportTypesList()
            uni.hideLoading()
            if (system_types.some(v => v.name === this.name)) return err()

            this.$uni.showLoading()
            let user_types = await sportsCenterApi.getUserSportTypesList()
            uni.hideLoading()
            if (this.id) user_types = user_types.filter(v => v.types !== this.id)

            if (user_types.some(v => v.name === this.name)) return err()

            return true
        },

        async add() {
            if (!this.name) return this.$uni.showToast('请输入名称')
            if (!await this.nameCheck()) return

            if (this.types === 37 && !this.logo) return this.$uni.showToast('请上传垃圾桶logo图片')

            const data = {
                name: this.name,
                types: this.types
            }

            if (this.id) data.id = this.id
            if (this.active_id) data.active_id = this.active_id
            if (this.sort_num) data.sort_num = this.sort_num
            if (this.logo) data.logo = this.logo

            if (this.types === 38) {
                const conf_set = this.conf_set
                const reward_integral = Math.floor(conf_set.submit_sign.reward_integral)
                if (isNaN(reward_integral)) return this.$uni.showToast('请输入正确的打卡积分奖励数')
                const limit_num = Math.floor(conf_set.submit_sign.limit_num)
                if (isNaN(limit_num)) return this.$uni.showToast('请输入正确的打卡次数限制')
                conf_set.submit_sign.reward_integral = reward_integral
                conf_set.submit_sign.limit_num = limit_num
                data.conf_set = this._utils.base64['encode'](JSON.stringify(conf_set))
            }
            
            this.$uni.showLoading('保存中...')
            const res = await xwy_api.request({
                url: 'front.user.category/create_category',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '保存失败', {title: '保存失败'})

            this.$uni.showToast('保存成功')

            const eventChannel = this.getOpenerEventChannel()
            eventChannel.emit('upload')

            const timeout = setTimeout(() => {
                uni.navigateBack()
                clearTimeout(timeout)
            }, 1000)
        }
    }
}
</script>

<style scoped>
.logo {
    width: 100px;
    height: 100px;
    border-radius: 50%;
}

.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}


.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.bottom-btn-view {
    position: fixed;
    z-index: 99999;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.login-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}

</style>
