<template>
    <view class="page bg-background">
        <view v-if="total_count" class="bg-white flex-kai">
            <view class="p10 color-sub font14">共 {{ total_count }}个{{ categoryTitle }}</view>
            <view class="p10 color-primary" @click="toEdit(null)">
                <text>添加{{ categoryTitle }}</text>
                <uni-icons type="forward" color="#2d8cf0"/>
            </view>
        </view>

        <view class="list pt5">
            <view class="item bg-white" v-for="item in list" :key="item.category_id">
                <view class="flex-row">
                    <image class="logo" v-if="item.logo" :src="item.logo" mode="aspectFill"/>
                    <view>
                        <view class="name color-title" @click="copy(item.category_id, '')">
                            {{ item.name }}
                        </view>
                        <view v-if="types === 38 && item.conf_set && item.conf_set.submit_sign"
                              class="color-content font14">
                            <view v-if="item.conf_set.submit_sign.reward_integral">
                                打卡奖励: {{ item.conf_set.submit_sign.reward_integral }}积分
                            </view>
                            <view v-if="item.conf_set.submit_sign.limit_num">
                                月打卡限制: {{ item.conf_set.submit_sign.limit_num }}次
                            </view>
                        </view>
                    </view>
                </view>
                <view class="tools-bar flex-kai">
                    <view>
                        <view v-if="is_sel" class="color-primary" hover-stop-propagation
                              @click.stop="selItem(item)">
                            选择此{{ categoryTitle }}
                        </view>
                    </view>
                    <view class="flex-row">
                        <view hover-stop-propagation @click.stop="toEdit(item.category_id)" class="edit">
                            <text class="iconfont icon-edit color-sub font20"></text>
                        </view>
                        <view class="delete" hover-stop-propagationclass 
                              @click.stop="deleteItem(item.category_id)">
                            <text class="iconfont icon-delete color-sub font20"></text>
                        </view>
                    </view>

                </view>
            </view>
        </view>


        <view v-if="!loading && !list.length" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无{{ categoryTitle }}</view>
            <view class="flex-all-center p20">
                <view class="not-btn text-center bg-primary color-white" @click="toEdit(null)">
                    添加{{ categoryTitle }}
                </view>
            </view>
        </view>

        <uni-load-more v-if="loading && load_page > 1" status="loading"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && list.length > 5"
            status="noMore"
            :contentText="{contentnomore: '我是有底线的'}"
        ></uni-load-more>
        <uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>


    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'

export default {
    data() {
        return {
            types: null,
            loading: true,
            is_sel: false,  //是否进入选择分类的
            list: [],
            load_page: 1,
            is_last_page: false,
            total_count: 0
        }
    },
    onLoad(e) {
        if (e.is_sel) this.is_sel = true
        this.types = Number(e.types)
        if (e.active_id) this.active_id = e.active_id
        if (e.just_look_active) this.just_look_active = true
        if (e.same_activity) this.same_activity = true
        if (e.active_types) this.active_types = Number(e.active_types)

        this.setTitleText(e.types)

        this.$uni.showLoading('加载中...')

        login.uniLogin(err => {
            if (err && err.errMsg) return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            this.initGetList()
        })
    },
    
    computed: {
        categoryTitle() {
            const options = {
                30: '运动类型',
                37: '垃圾桶',
                38: '打卡点类型'
            }
            return options[this.types] || '分类'
        }
    },
    
    onReachBottom() {
        if (!this.loading && !this.is_last_page) this.initGetList()
    },
    
    methods: {
        setTitleText(types) {
            const options = {
                '8': '文章分类',
                '16': '作品分类',
                '19': '队伍分类',
                '30': '自定义运动类型',
                '37': '垃圾桶管理',
                '38': '打卡点类型',
                '43': '运动圈分类'
            }
            uni.setNavigationBarTitle({title: options[types] || '分类'})
        },

        initGetList() {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
            }

            this.getList()
        },

        async getList() {
            const data = {
                types: this.types,
                page: this.load_page,
                perpage: 10,
                my_self: 1
            }
            // 文章分类显示所有的，因为之前的文章分类都没有存活动id，如果现在按照活动id来查的话，那么以前添加的分类都显示不出来
            // 如果有just_look_active，那么就是只查看当前活动的分类
            if (this.active_id && (this.types !== 8 || this.just_look_active)) data.active_id = this.active_id

            this.loading = true
            const res = await xwy_api.getCategoryList(data)
            uni.hideLoading()
            this.loading = false

            const resData = res?.data?.category_list

            if (!resData) {
                if (this.load_page === 1) this.total_count = 0
                this.is_last_page = true
                return
            }

           const list = resData.data || []
            this.load_page++
            this.list = [...this.list, ...list]
            this.is_last_page = resData.is_lastpage
            this.total_count = resData.total || 0
        },

        toEdit(id) {
            // 因为选择作品的时候用的是showActionSheet，showActionSheet的itemList长度最大为6个，所以限制只能添加6个分类
            if (!id && this.types === 16 && this.list.length >= 6) {
                // 投票活动(7)作品分类不限制最多6个，因为投票是用picker组件来选的，不受showActionSheet的itemList长度限制
                if (this.active_types !== 7) {
                    return this.$uni.showToast(`最多添加6个${this.categoryTitle}`)
                }
            }
            
            let url = './edit?types=' + this.types
            if (id) url += `&id=${id}`
            if (this.active_id) url += `&active_id=${this.active_id}`
            uni.navigateTo({
                url,
                events: {
                    upload: () => {
                        this.load_page = 1
                        this.initGetList()

                        this.getOpenerEventChannel?.()?.emit?.('uploadList')
                    }
                }
            })
        },

        async deleteItem(id) {
            const res = await this.$uni.showModal(`确定删除该${this.categoryTitle}`, {showCancel: true})
            if (res.confirm) await this.deleteAjax(id)
        },

        async deleteAjax(ids) {
            this.loading = true
            
            this.$uni.showLoading('删除中...')
            const res = await xwy_api.request({
                url: 'front.user.category/category_del',
                data: {
                    ids
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res.info || '删除失败', {title: '删除失败'})

            this.$uni.showToast('已删除')

            this.getOpenerEventChannel?.()?.emit?.('uploadList')

            setTimeout(() => {
                this.load_page = 1
                this.initGetList()
            }, 1000)
        },

        selItem(item) {
            // 如果是阅读奖励积分选择文章分类的，如果选择的分类不是绑定的当前活动，提示不能选择
            if (this.same_activity && this.active_id && item.active_id !== this.active_id) {
                return this.$uni.showToast(`所选${this.categoryTitle}未绑定当前活动，无法选择`)
            }

            const eventChannel = this.getOpenerEventChannel()
            eventChannel.emit('selCategory', {id: item.category_id, name: item.name})
            uni.navigateBack()
        },

        copy(data, tips) {
            this.$uni.setClipboardData(data.toString(), tips)
        }
    }
}
</script>

<style scoped lang="scss">
.page {
    min-height: 100vh;
}

.not-btn {
    width: 200px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
}

.item {
    padding: 10px;
    border-bottom: 1px solid #eee;
    margin: 10px;
    border-radius: 10px;

    $logo-size: 40px;

    .logo {
        width: $logo-size;
        min-width: $logo-size;
        height: $logo-size;
        border-radius: 50%;
        display: block;
        margin-right: 10px;
    }

    .name {
        line-height: $logo-size;
    }
}


.tools-bar {
    border-top: 1px solid #eee;
    padding-top: 10px;
    margin-top: 10px;
}

.edit, .delete {
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 50%;
}

.edit {
    border: 1px solid #eee;
    margin-right: 10px;
}

.delete {
    border: 1px solid #eee;
}
</style>
