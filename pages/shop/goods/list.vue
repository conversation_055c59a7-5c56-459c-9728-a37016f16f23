<template>
    <view class="page bg-background">
        <view v-if="user_details.id" class="user-info bg-white flex-row">
            <view class="headimg pr10">
                <image :src="user_details.headimg" mode="aspectFill"/>
            </view>
            <view>
                <view class="color-content">{{ user_details.must_submit[0].value || '' }}</view>

                <navigator
                    v-if="!not_pay"
                    class="color-warning pt15"
                    :url="'/pages/sign_in/integral_record_list?active_id=' + active_id + '&unit=' + unit"
                >我的{{ unit }}: {{ user_details.integral_left || 0 }}
                </navigator>
            </view>
        </view>


        <view class="top-view">
            <view class="search bg-white flex-kai p10">
                <view class="input-view">
                    <view class="search-icon left-icon flex-all-center">
                        <uni-icons type="search" size="20" color="#bbbec4"/>
                    </view>
                    <input
                        class="input bg-background"
                        type="text"
                        confirm-type="search"
                        v-model="search_keyword"
                        @confirm="search()"
                        placeholder="输入商品名称搜索"
                        placeholder-style="color:#bbbec4"
                    />
                    <view class="search-icon right-icon flex-all-center" @click="search_keyword = ''">
                        <uni-icons v-if="search_keyword" type="close" size="20" color="#bbbec4"/>
                    </view>
                </view>
                <view class="search-go color-info pl10" @click="search">搜索</view>
            </view>

            <scroll-view class="category-scroll-view bg-white" v-if="category_list.length" scroll-x="true">
                <view class="category-list flex-row color-content">
                    <view class="category-item"
                          :class="{'color-light-primary': item.category_id === category_id}"
                          v-for="item in category_list" :key="item.category_id"
                          @click="changeCategory(item.category_id)">{{ item.name }}
                    </view>
                </view>
            </scroll-view>
        </view>
        

        <view class="list flex-row flex-wrap">
            <view class="item bg-white" v-for="(item, index) in goods_list" :key="index"
                  @click="toGoodsDetails(item.goods_id)">

                <view class="pb10">
                    <image class="thumb" :src="item.thumb" mode="aspectFill"/>
                </view>
                <view>
                    <view class="ellipsis--l2 font14">{{ item.title }}</view>
                    <view v-if="item.price" class="pt5">
                        <my-price class="color-red" :price="item.price"></my-price>
                    </view>
                    <view v-if="item.integral" class="pt5 color-warning font14">
                        {{ item.integral }}{{ unit }}
                    </view>
                </view>
            </view>
        </view>

        <view v-if="!in_loading && !goods_list.length" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无商品</view>
        </view>

        <uni-load-more v-if="in_loading && load_page > 1" status="loading"></uni-load-more>
        <uni-load-more v-if="!is_last_page && !in_loading" status="more"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && goods_list.length > 8"
            status="noMore"
            :contentText="{contentnomore: '我是有底线的'}"
        ></uni-load-more>


        <view v-if="!not_pay && !shop_exchange_limit" class="tabbar flex-row bg-white">

            <navigator class="tabbar-item text-center" :url="'./cart?active_id=' + active_id">
                <view class="cart-count" v-if="cart_count">
                    <uni-badge :text="cart_count" :custom-style="{backgroundColor: '#ff0000'}"></uni-badge>
                </view>
                <uni-icons class="tabbar-icon" type="cart" size="24" color="#666666"/>
                <view class="title font14">购物车</view>
            </navigator>

            <navigator class="tabbar-item text-center" :url="'../order/order_list?active_id=' + active_id">
                <uni-icons class="tabbar-icon" type="list" size="24" color="#666666"/>
                <view class="title font14">订单</view>
            </navigator>
        </view>


        <navigator class="order-list-icon flex-all-center" 
                   :url="'../order/order_list?active_id=' + active_id">
            <uni-icons type="list" size="30" color="#ffffff"/>
        </navigator>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import {getCartCount} from '@/utils/storage.js'

import myPrice from '@/pages/shop/components/my-price.vue'

export default {
    components: {
        myPrice
    },
    data() {
        return {
            active_id: '',
            unit: app.globalData['tempData'].shop_integral_unit || '金币',
            shop_exchange_limit: app.globalData['tempData'].shop_exchange_limit || 0,
            in_loading: true,
            goods_list: [],
            load_page: 1,
            is_last_page: false,
            total: 0,
            cart_count: 0,
            user_details: {},
            not_pay: false,
            category_list: [],
            category_id: 0,
            search_keyword: ''
        }
    },

    onLoad(e) {
        this.active_id = e.active_id
        if (e.not_pay) this.not_pay = true
        uni.showLoading({
            mask: true,
            title: '数据加载中...'
        })

        login.uniLogin(err => {

            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace err.errTitle */
            
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err.errTitle || '提示'})
            }

            this.init()
        })
    },

    onShow() {
        this.active_id && this.getCartCount()
        this.user_details && this.user_details.id && this.getUserStatus()
    },

    onReachBottom() {
        !this.in_loading && !this.is_last_page && this.getGoodsList()
    },


    methods: {
        async init() {
            await this.getUserStatus()
            await this.getCategoryList()
            await this.getGoodsList()
            this.getCartCount()
        },
        
        getCartCount() {
            this.cart_count = getCartCount(this.active_id)
        },

        async getUserStatus() {
            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace res.data.user_details.integral_left */
            
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: {
                    active_id: this.active_id
                }
            })

            if (res?.data?.user_details) {
                this.user_details = res.data.user_details
            }
        },
        
        async getCategoryList() {
            const res = await this.xwy_api.getCategoryList({
                types: 10,
                active_id: this.active_id,
                page: 1,
                perpage: 100
            })
            
            const list = res?.data?.category_list?.data
            
            if (list?.length) this.category_list = [{category_id: 0, name: '全部'}, ...list]
        },


        changeCategory(id) {
            if (id === this.category_id) return
            this.category_id = id
            this.load_page = 1
            this.getGoodsList()
        },

        search() {
            this.load_page = 1
            this.getGoodsList()
        },

        async getGoodsList() {
            if (this.load_page === 1) {
                this.goods_list = []
                this.is_last_page = false
            }

            this.in_loading = true

            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace res.data.goods_list.sku_list */
            
            const res = await xwy_api.request({
                url: 'front.user.shop.goods.goods_list/goods_list',
                data: {
                    active_id: this.active_id,
                    page: this.load_page,
                    perpage: 10,
                    category_id: this.category_id,
                    title: this.search_keyword
                }
            })

            uni.hideLoading()
            
            
            this.in_loading = false
            this.load_page++

            if (res?.data?.goods_list) {
                const goods_data = res.data.goods_list
                const goods_list = goods_data.data || []

                goods_list.forEach(v => {
                    if (v.sku_list && v.sku_list.length) {
                        v.price = v.sku_list[0].price
                        v.integral = v.sku_list[0].integral

                        v.sales = 0
                        v.stock = 0

                        v.sku_list.forEach(sku => {
                            v.sales += sku.sales
                            v.stock += sku.stock
                        })
                    }
                })


                this.goods_list = [...this.goods_list, ...goods_list]
                this.is_last_page = goods_data.is_lastpage
                this.total = goods_data.total
            } else {
                this.is_last_page = true
            }
        },

        toGoodsDetails(goods_id) {
            const {active_id, not_pay, shop_exchange_limit} = this
            let url = `./detail?goods_id=${goods_id}&active_id=${active_id}`
            if (not_pay) url += `&not_pay=${not_pay}`
            if (shop_exchange_limit) url += `&shop_exchange_limit=${shop_exchange_limit}`
            this.$uni.navigateTo(url)
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 150px;
}

.user-info {
    padding: 20px;
}

.headimg image {
    width: 60px;
    min-width: 60px;
    height: 60px;
    border-radius: 50%;
    display: block;
}

.top-view {
    position: sticky;
    top: 0;
    z-index: 10;
}

.search {
    box-sizing: border-box;
    height: 60px;
    line-height: 40px;

    .input {
        height: 40px;
        line-height: 40px;
        padding: 0 30px;
        border-radius: 20px;
        box-sizing: border-box;
    }

    .input-view {
        position: relative;
        width: 100%;
    }

    .search-icon {
        position: absolute;
        width: 30px;
        height: 40px;
        top: 0;
    }

    .left-icon {
        left: 0;
    }

    .right-icon {
        right: 0;
    }

    .search-go {
        width: 60px;
        min-width: 60px;
        text-align: center;
    }
}

/* #ifdef H5 */
.search-go {
    padding-right: 10px;
    box-sizing: border-box;
}

@media screen and (min-width: 500px) {
    .search {
        width: 500px;
    }
}
/* #endif */

.category-scroll-view {
    width: 100vw;
    padding: 0 10px;
    box-sizing: border-box;
}

.category-item {
    white-space: nowrap;
    padding: 10px;
}


.list {
    padding: 5px;
}

.item {
    margin: 5px;
    padding: 10px;
    border-radius: 10px;
    box-sizing: border-box;
    width: calc((100vw - 30px) * .5);
    overflow: hidden;
}

.thumb {
    width: calc(((100vw - 30px) * .5) - 20px);
    height: calc(((100vw - 30px) * .5) - 20px);
    display: block;
}

.tabbar {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    z-index: 100;
    border-top: 1px solid #eee;
}

.tabbar-item {
    position: relative;
    padding-bottom: 15px;
    width: 50%;
    color: #666666;
}

.tabbar-icon {
    position: relative;
    top: 4px;
}

.cart-count {
    position: absolute;
    top: -3px;
    left: 50%;
    z-index: 9;
}

.order-list-icon {
    position: fixed;
    right: 10px;
    bottom: 80px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, .7);
}
</style>
