<template>
	<view class="page">
		<view v-if="error" class="text-center">
		    <uni-icons type="info" size="100" color="#ff9900"/>
		    <view class="color-sub font14">{{ error }}</view>
		</view>

		<template v-if="goods_detail.goods_id">
			<view v-if="goods_detail.thumb">
				<image class="thumb" :src="goods_detail.thumb" mode="widthFix"/>
			</view>
			<view class="p10">
				<view class="color-title font16">{{goods_detail.title}}</view>
				<view class="flex-kai">
					<view>
						<my-price
							v-if="goods_detail.price"
							class="color-red"
							:price="goods_detail.price"
							:big_size="18"
							:small_size="14"
							:unit_size="14"
							:point_size="14"
						></my-price>
						<view v-if="goods_detail.integral" class="color-warning">
							{{goods_detail.integral}}{{unit}}
						</view>
					</view>
					<view v-if="!not_pay" class="flex-row color-sub font12">
						<view class="pr10">库存:{{goods_detail.stock || 0}}</view>
						<view>已兑换:{{goods_detail.sales || 0}}</view>
					</view>
				</view>
			</view>

			<view v-if="goods_detail.content" class="p10">
				<view class="color-sub">商品详情</view>
				<view v-show="false">{{goods_detail.content}}</view>
				<u-parse :content="goods_detail.content"/>
			</view>

			<view v-if="!not_pay" class="goods-nav">
				<uni-goods-nav
					:fill="true"
					:options="goods_nav_options"
					:button-group="buttonGroup"
					@click="clickOptionItem"
					@buttonClick="clickButtonItem"
				/>
			</view>

			<!-- #ifdef MP-WEIXIN -->
			<view v-if="not_pay" class="contact-btn-view flex-all-center pt15 bg-white">
				<button
					class="contact-btn bg-green color-white"
					open-type="contact"
				>{{customer_button.text || '联系客服'}}</button>
			</view>
			<!-- #endif -->
		</template>
	</view>
</template>
<script>
	const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'
	import {getCartCount} from '@/utils/storage.js'

	import myPrice from '@/pages/shop/components/my-price.vue'

	export default {
		components: {
			myPrice
		},
		data() {
			return {
				error: '',
				goods_detail: {},
				unit: app.globalData['tempData'].shop_integral_unit || '金币',
				goods_nav_options: [
					/* {
						icon: 'shop',
						text: '店铺',
						infoBackgroundColor: '#007aff',
						infoColor: "#f5f5f5"
					}, */
					{
						icon: 'cart',
						text: '购物车',
						info: 0
					}
				],
				buttonGroup: [
					{
						text: '加入购物车',
						backgroundColor: 'linear-gradient(90deg, #FFCD1E, #FF8A18)',
						color: '#fff'
					},
					{
						text: '立即购买',
						backgroundColor: 'linear-gradient(90deg, #FE6035, #EF1224)',
						color: '#fff'
					}
				],
				not_pay: false,
				imageWidth: uni.getSystemInfoSync().windowWidth - 20,
                shop_exchange_limit: 0
			}
		},

		onLoad(e) {
			this.goods_id = e.goods_id
			this.active_id = e.active_id
			if (e.not_pay && e.not_pay === 'true') this.not_pay = true
			if (e.shop_exchange_limit) {
                this.shop_exchange_limit = Number(e.shop_exchange_limit)
                this.shopExchangeLimitSet()
            }

			uni.showLoading({
				mask: true,
				title: '数据加载中...'
			})

		    login.uniLogin(err => {
		        if (err && err.errMsg) {
		            uni.hideLoading()
		            uni.showModal({
		                title: err.errTitle || '提示',
		                content: err.errMsg,
		                showCancel: false
		            })
		            return false
		        }

				!e.not_pay && this.getCartCount()
				this.getGoodsDetail()
		    })
		},

		methods: {
			getCartCount() {
                if (this.not_pay || this.shop_exchange_limit) return
				this.goods_nav_options[0].info = getCartCount(this.active_id)
			},

            shopExchangeLimitSet() {
                this.goods_nav_options = [] // 关闭购物车icon
                // 不能加入购物车
                this.buttonGroup = [
                    /*{
                        text: '加入购物车',
                        backgroundColor: 'linear-gradient(90deg, #FFCD1E, #FF8A18)',
                        color: '#fff'
                    },*/
                    {
                        text: '立即购买',
                        backgroundColor: 'linear-gradient(90deg, #FE6035, #EF1224)',
                        color: '#fff'
                    }
                ]
            },

			async getGoodsDetail() {

				const res = await xwy_api.request({
					url: 'front.user.shop.goods.goods_list/goods_details',
					data: {
						access_token: app.globalData.access_token,
						goods_id: this.goods_id
					}
				})

				uni.hideLoading()

				if (!res || !res.data || !res.data.goods_details) {
					this.error = res && res.info || '商品数据获取失败'
					return false
				}


				const detail = res.data.goods_details

				if (detail.title) {
					uni.setNavigationBarTitle({
						title: detail.title
					})
				}

				if (detail.status !== 1) {
					this.error = '商品status=' + detail.status
				}

				this.goods_detail = detail

				this.skuInit(detail.sku_list)
			},

			skuInit(sku_list) {
				if (!sku_list || !sku_list.length) {
					!this.not_pay && xwy_api.alert('商品暂未设置商品销售信息或商品规格属性，无法购买')
					return false
				}

				let sales = 0,
					stock = 0
				sku_list.forEach(v => {
					sales += v.sales
					stock += v.stock
				})

				this.goods_detail.sales = sales
				this.goods_detail.stock = stock

				if (this.goods_detail.is_default) {
					this.goods_detail.price = sku_list[0].price
					if (sku_list[0].integral) this.goods_detail.integral = sku_list[0].integral
				}
			},

			clickOptionItem(e) {
				switch (e.index) {
					case 0:
						uni.navigateTo({
							url: './cart?active_id=' + this.active_id
						})
						break
				}
			},



			clickButtonItem(e) {
				if (!this.goods_detail.sku_list || !this.goods_detail.sku_list.length) {
					xwy_api.alert('商品暂未设置商品销售信息或商品规格属性，无法购买')
					return false
				}
				if (!this.goods_detail.stock || this.goods_detail.stock === '0') {
					uni.showToast({
						title: '库存不足',
						icon: 'none'
					})
					return
				}
                
                // 限制兑换数量，不会显示加入购物车，所以第0个是直接购买，直接去购买
                if (this.shop_exchange_limit) {
                    return this.$uni.showModal('本活动只能兑换一次奖品，请慎重选择。', {
                        showCancel: true,
                        cancelText: '重新选择',
                        confirmText: '立即兑换',
                        success: res => res.confirm && this.buyNow()
                    })
                }

				switch (e.index) {
					case 0:
						this.toCard()
						break
					case 1:
						this.buyNow()
						break
				}
			},

			toCard() {
				if (this.goods_detail.is_default) {
					const storage_key = 'shop_cart_' + this.active_id
					const storage_cart_data = uni.getStorageSync(storage_key) || []

					const pay_goods_data = this.setDefaultGoodsPayData()

					let goods_in_cart = false
					for (let i = 0; i < storage_cart_data.length; i++) {
						const item = storage_cart_data[i]
						if (item.goods_id === pay_goods_data.goods_id && item.sku_id === pay_goods_data.sku_id) {
							item.buy_count++
							goods_in_cart = true
							break
						}
					}

					if (!goods_in_cart) storage_cart_data.push(pay_goods_data)

					uni.setStorageSync(storage_key, storage_cart_data)

					uni.showToast({
						title: '已加入购物车',
						icon: 'success'
					})

					this.getCartCount()
				}
			},

			async buyNow() {
                const {goods_detail, shop_exchange_limit, active_id} = this
                
                if (!goods_detail.is_default) return this.$uni.showToast('非标准品，不能购买')
                
                if (shop_exchange_limit) {
                    let user_order_count = this.user_order_count || 0
                    
                    if (!this.hasOwnProperty('user_order_count')) {
                        this.$uni.showLoading()
                        const res = await xwy_api.request({
                            url: 'front.user.shop.order.order_list/order_list',
                            data: {
                                foreign_orderid: active_id
                            }
                        })
                        uni.hideLoading()

                        user_order_count = res?.data?.order_list?.total || 0
                        this.user_order_count = user_order_count
                    }
                    
                    if (user_order_count) return this.$uni.showToast('购买次数已达上限')
                }
				
                const pay_goods_data = this.setDefaultGoodsPayData()
                app.globalData.pay_goods_list = [pay_goods_data]
                this.$uni.navigateTo(`../order/pay_order?active_id=${active_id}`)
			},

			setDefaultGoodsPayData() {
				const pay_goods_data = {
					goods_id: this.goods_detail.goods_id,
					title: this.goods_detail.title,
					thumb: this.goods_detail.thumb,
					is_default: this.goods_detail.is_default,
					price: this.goods_detail.sku_list[0].price,
					integral: this.goods_detail.sku_list[0].integral,
					sku_id: this.goods_detail.sku_list[0].id,
					buy_count: 1
				}

				return pay_goods_data
			}

		}
	}
</script>

<style>
	.page {
		padding-bottom: 80px;
	}
	.thumb {
		width: 100%;
		height: auto;
		display: block;
	}
	.goods-nav {
		position: fixed;
		bottom: 0;
		left: 0;
		background-color: #fff;
		width: 100%;
		padding-bottom: 15px;
	}

	.contact-btn-view {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100vw;
		padding: 20px;
		box-sizing: border-box;
	}
	.contact-btn {
		height: 40px;
		line-height: 40px;
		border-radius: 20px;
		width: 200px;
		font-size: 16px;
	}
	.contact-btn::after {
		border: none;
	}
</style>
