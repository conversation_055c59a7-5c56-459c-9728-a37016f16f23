<template>
	<view class="page bg-background">
		<template v-if="cart_list.length">
			<view class="top-bar flex-kai bg-white">
				<view class="p10 color-sub font14">共{{goods_count}}件商品</view>
				<view class="p10 color-sub font14" @click="in_edit = !in_edit">{{in_edit ? '完成' : '编辑'}}</view>
			</view>

			<view class="list">

				<view class="item flex-row bg-white" v-for="(item, index) in cart_list" :key="index">
					<view class="flex-all-center pr5" @click="checkChange(index)">
						<radio :checked="item.check" color="#ff0000" style="transform:scale(0.8)"/>
					</view>
					<view class="flex-row" style="width: 100%;">
						<view v-if="item.thumb" class="pr10">
							<image class="thumb" :src="item.thumb"/>
						</view>
						<view class="flex-column flex-kai" style="width: 100%;">
							<view>
								<view class="color-title ellipsis--l2">{{item.title}}</view>
								<view v-if="item.price || !item.integral" class="pt5">
									<my-price class="color-red" :price="item.price"></my-price>
								</view>
								<view v-if="item.integral" class="pt5 color-warning">{{item.integral}}{{unit}}</view>
							</view>
							<view class="flex-kai">
								<view></view>
								<view>
									<uni-number-box :min="1" :max="99" v-model="item.buy_count"></uni-number-box>
								</view>
							</view>
						</view>
					</view>
				</view>

			</view>


			<view class="bottom-bar flex-kai bg-white">
				<view class="flex-row">
					<view class="flex-all-center" @click="checkAllChange">
						<radio :checked="check_all" color="#ff0000" style="transform:scale(0.8)"/>
						<text class="font14 color-sub" style="position: relative; left: -4px; top: 1px;">全选</text>
					</view>

					<uni-transition :mode-class="['fade', 'slide-left']" :show="!in_edit">
						<view class="font12 pl5">
							<text class="color-content">合计:</text>
							<my-price
								v-if="price_count || !integral_count"
								class="color-red"
								:price="price_count"
								:big_size="16"
								:small_size="14"
								:unit_size="12"
								:point_size="12"
							></my-price>
							<template v-if="integral_count">
								<text class="pl10 font16 color-warning">{{integral_count}}</text>
								<text class="color-sub">{{unit}}</text>
							</template>
						</view>
					</uni-transition>

				</view>
				<view v-if="!in_edit" class="to-pay" @click="toPay">结算</view>
				<view v-if="in_edit" class="del color-sub" @click="del">删除</view>
			</view>
		</template>



		<view v-if="!in_load && !cart_list.length" class="text-center" style="padding-top: 10vh;">
			<text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
			<view class="color-sub font14">购物车是空的哟</view>
		</view>
	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'

	import myPrice from '@/pages/shop/components/my-price.vue'

	export default {
		components: {
			myPrice
		},
		data() {
			return {
				active_id: '',
				in_load: true,
				cart_list: [],
				goods_count: 0,
				price_count: 0,
				integral_count: 0,
				unit: app.globalData['tempData'].shop_integral_unit || '金币',
				in_edit: false,
				check_all: false
			}
		},
		watch: {
			cart_list: {
				handler(val) {
					let count = 0,
						price = 0,
						integral = 0,
						check_all = true

					val.forEach(v => {
						count += v.buy_count
						if (v.check) {
							if (v.integral) {
								const goods_all_integral = v.integral * v.buy_count
								integral += goods_all_integral
							}
							if (v.price) {
								const goods_all_price = v.price * v.buy_count
								price += goods_all_price
							}
						}
						if (!v.check) check_all = false
					})

					this.goods_count = count
					this.price_count = price
					this.integral_count = integral
					this.check_all = check_all

					this.saveStorage()
				},
				deep: true
			}
		},

		onLoad(e) {
			this.active_id = e.active_id
			uni.showLoading({
				mask: true,
				title: '购物车加载中...'
			})

		    login.uniLogin(err => {
		        if (err && err.errMsg) {
		            uni.hideLoading()
		            uni.showModal({
		                title: err.errTitle || '提示',
		                content: err.errMsg,
		                showCancel: false
		            })
		            return false
		        }


				this.getCartList()
		    })
		},

		onShow() {
			this.active_id && this.getCartList()
		},

		methods: {
			getCartList() {
				const storage_key = 'shop_cart_' + this.active_id
				const storage_cart_data = uni.getStorageSync(storage_key) || []
				storage_cart_data.forEach(v => {
					v.check = v.check || false
				})
				this.cart_list = storage_cart_data
				this.in_load = false
				uni.hideLoading()
			},

			saveStorage() {
				const storage_key = 'shop_cart_' + this.active_id
				uni.setStorageSync(storage_key, this.cart_list)
			},

			checkChange(index) {
				const item = this.cart_list[index]
				item.check = !item.check
			},

			checkAllChange() {
				const check_all = !this.check_all
				this.check_all = check_all
				this.cart_list.forEach(v => {
					v.check = check_all
				})
			},

			del() {
				let del_count = 0
				const new_list = []

				this.cart_list.forEach(v => {
					if (v.check) {
						del_count++
					} else {
						new_list.push(v)
					}
				})

				if (!del_count) {
					xwy_api.alert('请勾选要删除的商品')
					return false
				}

				uni.showModal({
					title: '提示',
					content: '确定删除选中的商品',
					success: res => {
						if (res.confirm) {
							this.cart_list = new_list
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							})
						}
					}
				})
			},

			toPay() {
				let pay_count = 0
				const new_list = []

				this.cart_list.forEach(v => {
					if (v.check) {
						pay_count++
						new_list.push(v)
					}
				})

				if (!pay_count) {
					xwy_api.alert('请勾选需要结算的商品')
					return false
				}

				app.globalData.pay_goods_list = new_list
				uni.navigateTo({
					url: '../order/pay_order?from_cart=1&active_id=' + this.active_id
				})
			}
		}
	}
</script>

<style>
.page {
	min-height: 100vh;
	box-sizing: border-box;
	padding-top: 40px;
	padding-bottom: 70px;
}
.top-bar, .bottom-bar {
	position: fixed;
	left: 0;
	width: 100%;
	z-index: 9;
}
.top-bar {
	top: 0;
}
.item {
	margin: 10px;
	padding: 10px;
	border-radius: 10px;
}
.thumb {
	width: 80px;
	min-width: 80px;
	height: 80px;
	border-radius: 5px;
	display: block;
}
.bottom-bar {
	border-top: 1px solid #eee;
	bottom: 0;
	padding: 5px 10px;
	padding-bottom: 15px;
	height: 61px;
	line-height: 40px;
	box-sizing: border-box;
}
.to-pay {
	width: 80px;
	height: 40px;
	font-size: 16px;
	line-height: 40px;
	border-radius: 20px;
	background-color: #f00;
	color: #fff;
	text-align: center;
}
.del {
	width: 80px;
	line-height: 38px;
	border-radius: 20px;
	border: 1px solid #f00;
	color: #f00;
	text-align: center;
}
</style>
