<template>
	<view style="display: inline-block;">
		<text v-show="show_unit" :style="'font-size: ' + unit_size + 'px;'">{{unit}}</text>
		<text :style="'font-size: ' + big_size + 'px;'">{{integer}}</text>
		<text :style="'font-size: ' + point_size + 'px;'">.</text>
		<text :style="'font-size: ' + small_size + 'px;'">{{float}}</text>
	</view>
</template>

<script>
	export default {
		name:"my-price",
		props: {
			price: {
				default: '0.00'
			},
			big_size: {
				default: 16
			},
			small_size: {
				default: 14
			},
			show_unit: {
				default: true
			},
			unit: {
				default: '￥'
			},
			unit_size: {
				default: 12
			},
			point_size: {
				default: 14
			}
		},
		data() {
			return {
				integer: '0',
				float: '00'
			}
		},
		watch: {
			price(val) {
				this.priceDiscriminate()
			}
		},
		mounted() {
			this.priceDiscriminate()
		},
		methods: {
			// 把价格分成整数部分和小数部分
			priceDiscriminate() {
				if (!this.price) {
					this.integer = '0'
					this.float = '00'
					return false
				}
				const price_str = this.price.toFixed(2)
				const price_arr = price_str.split('.')
				this.integer = price_arr[0]
				this.float = price_arr[1]
			}
		}
	}
</script>

<style>

</style>