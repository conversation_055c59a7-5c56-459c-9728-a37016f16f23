<template>
    <view>
        <view @click="show">
            <slot name="default"></slot>
        </view>

        <uni-popup ref="popup" type="bottom" :safe-area="false">
            <view class="my-popup bg-white">
                <view class="title">更改订单状态</view>

                <view class="status-list flex-row flex-wrap">
                    <view class="status-item flex-all-center" v-for="item in statusList" :key="item.status"
                          :class="{'active-status': item.status === orderStatus}"
                          hover-class="navigator-hover" @click="change(item)">
                        <view>{{ item.title }}</view>
                    </view>
                </view>

                <view class="flex-all-center">
                    <view class="p10 color-sub font14" @click="$refs.popup.close()">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import orderStatus from '@/pages/shop/order/order_status.js'

export default {
    name: "change-order-status",
    emits: ['update:orderStatus', 'change'],
    props: ['orderId', 'orderStatus'],
    data() {
        return {
            statusList: []
        }
    },

    methods: {
        async show() {
            const {order_status_list} = await orderStatus.get()
            this.statusList = order_status_list.filter(item => item.types !== 0).map(item => ({
                status: item.types,
                title: item.title
            }))

            this.$nextTick(() => this.$refs.popup.open())
        },

        async change(item) {
            if (item.status === this.orderStatus) return

            const confirm = await this.$uni.showModal(`确定将订单状态修改为 ${item.title}?`, {showCancel: true})
            if (!confirm.confirm) return

            this.$uni.showLoading('修改中...')
            const res = await this.xwy_api.request({
                url: 'front.user.shop.order.order_set/change_order_status',
                data: {
                    orderid: this.orderId,
                    order_status: item.status
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '订单状态修改失败')

            this.$uni.showToast('已修改')
            this.$emit('update:orderStatus', item.status)
            this.$emit('change', item.status)
            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss">
.my-popup {
    border-radius: 10px 10px 0 0;
    box-sizing: border-box;
    width: 100%;
    padding: 10px;

    .title {
        padding: 10px 10px 20px;
        text-align: center;
    }
}

.status-item {
    margin: 5px;
    width: calc(33.33% - 10px);
    height: 60px;
    box-sizing: border-box;
    border: 1px solid #eee;
    border-radius: 5px;
}

.active-status {
    border-color: #5cadff;
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .my-popup {
        width: 500px;
        margin-left: calc((100vw - 500px) / 2);
    }
}
/* #endif */
</style>