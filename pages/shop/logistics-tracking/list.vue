<template>
    <view class="page bg-background">
        <view class="list">
            <view class="item" v-for="item in list" :key="item.id">
                <view>订单编号: {{ item.order_num }}</view>
                <view v-for="(num, index) in item.delivery_num" :key="index">
                    物流单号{{ item.delivery_num.length === 1 ? '' : index + 1 }}: {{ num }}
                </view>
                <view>录入时间: {{ item.create_time }}</view>
                <view class="clear clearfix">
                    <view class="fr flex-row">
                        <view class="color-light-primary pl10 pr10" @click="lookOrder(item.order_num)">
                            查看订单
                        </view>
                        <view class="color-sub pl10 pr10" @click="deleteItem(item.id)">删除</view>
                    </view>
                </view>
            </view>
        </view>

        <view v-if="loading" class="text-center">
            <view v-if="load_page === 1" style="width: 100%; height: 40vh;"></view>
            <load-ani/>
            <view v-if="load_page === 1" class="font14 color-sub">加载中</view>
        </view>

        <view v-if="!loading && !list.length" class="text-center" style="padding-top: 20vh;">
            <text class="iconfont icon-logistics color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无物流单号</view>
            <view class="flex-all-center pt15">
                <view class="add-button color-white bg-primary" hover-class="navigator-hover"
                      @click="addTracking">添加物流单号
                </view>
            </view>
        </view>

        <view v-if="list.length" class="add-icon flex-all-center bg-light-primary" @click="addTracking">
            <uni-icons type="plusempty" size="36" color="#ffffff"/>
        </view>

        <uni-popup ref="addFormPopup" :is-mask-click="false">
            <view class="add-form-popup bg-white">
                <view class="color-title text-center p10 font18">手动录入物流单号</view>
                <view class="pt10 pb10">
                    <uni-forms label-position="left" label-width="90" label-align="right">
                        <uni-forms-item label="订单号:">
                            <view class="false-input flex-kai" @click="chooseOrder">
                                <template>
                                    <view v-if="order_id" class="pr10">{{ order_id }}</view>
                                    <view v-else style="color: #808080">选择订单</view>
                                </template>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" size="16" color="#bbbec4"/>
                                </view>
                            </view>
                        </uni-forms-item>
                        <view v-for="(item, index) in tracking_list" :key="index">
                            <uni-forms-item :label="`物流单号${index + 1}:`">
                                <uni-easyinput v-model="item.num" placeholder="请输入物流单号"/>
                            </uni-forms-item>
                        </view>
                        <view v-show="tracking_list.length < 5" class="flex-all-center">
                            <view class="color-light-primary p10" @click="tracking_list.push({num: ''})">
                                添加物流单号
                            </view>
                        </view>
                    </uni-forms>
                </view>

                <view class="flex-all-center text-center">
                    <view class="add-button color-white bg-primary" hover-class="navigator-hover"
                          @click="formAddTracking">录入
                    </view>
                </view>
            </view>
            <view class="flex-all-center">
                <view class="pt5" @click="$refs.addFormPopup.close()">
                    <uni-icons type="close" size="34" color="#ffffff"/>
                </view>
            </view>
        </uni-popup>

        <uni-popup ref="addType" type="bottom" :safe-area="false">
            <view class="add-type-popup bg-white text-center">
                <view class="p10 color-title font18">请选择添加方式</view>
                <view class="flex-all-center pt15 pb10">
                    <view class="add-type-item" @click="addFormPopupShow">
                        <text class="iconfont icon-create-ticket color-light-primary"></text>
                        <view class="color-content pt10">单个添加</view>
                    </view>
                    <view class="add-type-item" @click="batchImport">
                        <text class="iconfont icon-export-excel color-light-primary"></text>
                        <view class="color-content pt10">批量导入</view>
                    </view>
                </view>
                <view class="flex-all-center">
                    <view class="color-sub font14 p10" @click="$refs.addType.close()">关闭</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()

export default {
    data() {
        return {
            loading: true,
            list: [],
            load_page: 1,
            is_last_page: false,
            order_id: '',
            tracking_list: [{num: ''}]
        }
    },

    onLoad(params) {
        this.active_id = params.active_id

        this.$login.uniLogin(() => this.init())
    },

    onReachBottom() {
        if (this.is_last_page || this.loading) return
        this.load_page++
        this.getList()
    },

    methods: {
        async init() {
            await this.reloadList()
        },

        async reloadList() {
            this.load_page = 1
            await this.getList()
        },

        async getList() {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
            }

            this.loading = true
            const res = await this.xwy_api.request({
                url: 'front.user.shop.order.deliveryOrder/delivery_order_num_list',
                data: {
                    active_id: this.active_id,
                    page: this.load_page,
                    perpage: 20
                }
            })
            this.loading = false

            const resData = res?.data?.list
            if (!resData) {
                this.is_last_page = true
                return
            }

            this.is_last_page = resData.is_lastpage
            const list = resData.data || []

            this.list = this.list.concat(list.map(item => ({
                id: item.id,
                delivery_num: item.delivery_num.split(','),
                order_num: item.order_num,
                create_time: item.create_time
            })))
        },

        addTracking() {
            let isH5 = false
            // #ifdef H5
            isH5 = true
            // #endif

            if (!isH5) return this.$refs.addFormPopup.open()

            this.$refs.addType.open()
        },

        addFormPopupShow() {
            this.$refs.addType.close()
            this.order_id = ''
            this.tracking_list = [{num: ''}]
            this.$refs.addFormPopup.open()
        },

        async formAddTracking() {
            if (!this.order_id) return this.$uni.showToast('请选择订单')
            const list = this.tracking_list.filter(item => item.num).map(item => item.num).join(',')
            if (!list) return this.$uni.showToast('请录入物流单号')

            this.$uni.showLoading('录入中...')
            const res = await this.enter([{order_num: this.order_id, delivery_num: list}])
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '录入失败')
            this.$uni.showToast('已录入')
            this.$refs.addFormPopup.close()
            await this.reloadList()
        },

        chooseOrder() {
            this.$uni.navigateTo(`../order/order_list?active_id=${this.active_id}&is_admin=1&select=1`, {
                events: {
                    selectOrder: order => {
                        this.order_id = order.orderid
                    }
                }
            })
        },

        batchImport() {
            this.$refs.addType.close()

            this.excel_js()
        },

        excel_js() {
            const script = document.createElement('script')

            const static_url = app.globalData?.['shop_info']?.['shop_set']?.static_url || 'https://prod-0g479j60184f120d-1304148175.tcloudbaseapp.com/'
            script.src = `${static_url}web/wx-cloud-api/lib/excel/xlsx.core.min.js`
            document.body.appendChild(script)
            this.changeFile()
        },


        changeFile() {
            uni.chooseFile({
                count: 1,
                extension: ['.xlsx', '.xls', '.xltx'],
                success: res => {
                    if (res['errMsg'] !== 'chooseFile:ok') {
                        this.$uni.showModal('获取文件失败！')
                        return
                    }
                    let file = res.tempFiles[0]
                    this.getFileData(file)
                },
            })
        },


        getFileData(file) {
            let reader = new FileReader()
            reader.readAsBinaryString(file)
            reader.onload = (e) => {
                const data = e.target.result
                const zzexcel = window['XLS'].read(data, {type: 'binary'})
                const result = []
                for (let i = 0; i < zzexcel['SheetNames'].length; i++) {
                    const newData = window['XLS']['utils']['sheet_to_json'](zzexcel['Sheets'][zzexcel['SheetNames'][i]])
                    result.push(...newData)
                }

                this.excel2data(result)
            }
        },

        async excel2data(result) {
            const order_list = []

            result.forEach((item, index) => {
                if (index > 0) {
                    const obj2list = Object.entries(item)

                    const delivery_list = []
                    obj2list.forEach((v, i) => {
                        if (i > 1) delivery_list.push(v[1])
                    })

                    if (delivery_list.length) {
                        order_list.push({
                            order_num: obj2list[1][1],
                            delivery_num: delivery_list.join(',')
                        })
                    }
                }
            })

            this.$uni.showLoading('导入中...')
            const res = await this.enter(order_list)
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '导入失败')
            this.$uni.showToast('已导入')
            await this.reloadList()
        },


        enter(order_list) {
            return this.xwy_api.request({
                url: 'front.user.shop.order.deliveryOrder/import_delivery_order',
                data: {
                    active_id: this.active_id,
                    order_list: this._utils.base64['encode'](JSON.stringify(order_list))
                }
            })
        },

        lookOrder(order_num) {
            this.$uni.navigateTo(`../order/order_detail?orderid=${order_num}&is_admin=1`)
        },

        async deleteItem(id) {
            const {confirm} = await this.$uni.showModal('确定删除?', {showCancel: true})
            if (!confirm) return

            this.$uni.showLoading('删除中...')
            const res = await this.xwy_api.deleteRecords(115, id)
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '删除失败')
            this.$uni.showToast('已删除')

            await this.reloadList()
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    padding-bottom: 150px;
    box-sizing: border-box;
}

.list {
    padding-top: 1px;

    .item {
        margin: 10px;
        padding: 10px;
        border-radius: 10px;
        background-color: #fff;
    }
}

.add-button {
    width: 200px;
    line-height: 44px;
    border-radius: 22px;
}

.add-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    position: fixed;
    right: 10px;
    bottom: 100px;
}

.add-type-popup {
    border-radius: 10px 10px 0 0;

    .add-type-item {
        width: 50%;

        .iconfont {
            font-size: 48px;
        }
    }
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .add-type-popup {
        width: 500px;
        margin-left: calc((100vw - 500px) / 2);
    }

    .add-icon {
        right: calc((100vw - 500px) / 2 + 10px);
    }
}

/* #endif */

.add-form-popup {
    width: 95vw;
    max-width: 400px;
    padding: 10px 20px;
    border-radius: 10px;
    box-sizing: border-box;

    .false-input {
        width: 100%;
        box-sizing: border-box;
        border: 1px solid #e5e5e5;
        border-radius: 4px;
        min-height: 36px;
        line-height: 22px;
        padding: 7px 10px;
    }
}
</style>