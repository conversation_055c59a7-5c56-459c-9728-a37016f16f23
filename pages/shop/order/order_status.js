import xwy_api from '@/utils/api/xwy_api.js'
import $uni from '@/utils/uni-api'

module.exports = {
	is_get: false,  // 是否已经获取过了
	order_status_option: {},
	order_status_list: [],
	
	async get() {
		if (this.is_get) {
			return {
				order_status_option: this.order_status_option,
				order_status_list: this.order_status_list
			}
		}
		
		const res = await xwy_api.request({
			url: 'front.user.shop.order.order_set/order_status_set'
		})
		
		let order_status_list = [],
			order_status_option = {}
		
		if (res?.data?.order_status?.length) {
			order_status_list = res.data.order_status
			order_status_list.forEach(v => {
				const key = v.types + ''
				order_status_option[key] = v
			})
		}
		
		this.order_status_option = order_status_option
		this.order_status_list = order_status_list
		
		return {
			order_status_option,
			order_status_list
		}
	},
    
    async deleteOrder(active_id, orderid, is_admin) {
        const confirm = await $uni.showModal('确定删除该订单?', {showCancel: true})
        if (!confirm.confirm) return false
        
        const data = {orderid}
        if (is_admin) data.active_id = active_id
        
        $uni.showLoading('删除中...')
        const res = await xwy_api.request({
            url: 'front.user.shop.order.order_list/user_del_order',
            data
        })
        uni.hideLoading()
        
        if (res?.status !== 1) {
            await $uni.showModal(res?.info || '订单删除失败', {title: '订单删除失败'})
            return false
        }
        
        $uni.showToast('已删除')
        return true
    }
}