<template>
	<view class="page bg-background">
		<view v-if="error" class="text-center">
		    <uni-icons type="info" size="100" color="#ff9900"/>
		    <view class="color-sub font14">{{ error }}</view>
		</view>


		<view v-if="pay_order" class="text-center bg-white p10">
			<uni-icons type="checkbox-filled" size="80" color="#19be6b"/>
			<view class="color-content">订单提交成功！</view>
		</view>


		<view v-if="order_detail.orderid" class="pt10">
			<view
				v-if="order_detail.name || order_detail.tel || order_detail.address"
				class="block-item font14 p10"
			>

				<view v-if="order_detail.name" class="pt5 flex-row" @click="copy(order_detail.name)">
					<view class="color-sub" style="min-width: 40px;">姓名:</view>
					<view>
						<text class="color-content">{{order_detail.name}}</text>
						<text class="iconfont icon-copy font14 color-sub pl5"></text>
					</view>
				</view>

				<view v-if="order_detail.tel" class="pt5 flex-row" @click="copy(order_detail.tel + '')">
					<view class="color-sub" style="min-width: 40px;">电话:</view>
					<view>
						<text class="color-content">{{order_detail.tel}}</text>
						<text class="iconfont icon-copy font14 color-sub pl5"></text>
					</view>
				</view>

				<view v-if="order_detail.address" class="pt5 flex-row" @click="copy(order_detail.address)">
					<view class="color-sub" style="min-width: 40px;">地址:</view>
					<view>
						<text class="color-content">{{order_detail.address}}</text>
						<text class="iconfont icon-copy font14 color-sub pl5"></text>
					</view>
				</view>
			</view>

			<view v-if="goods_list.length" class="block-item">
				<view class="color-content p10 font14">商品列表</view>
				<view>
					<view class="flex-row p10 pt0" v-for="(item, index) in goods_list" :key="index">
						<view v-if="item.thumb" class="pr10">
							<image class="goods-logo" :src="item.thumb" mode="aspectFill"/>
						</view>
						<view>
							<view class="color-title font16 pb5 ellipsis">{{item.title}}</view>
							<view v-if="item.sku_title" class="color-content font12">{{item.sku_title}}</view>
							<view class="font12 color-content">数量: {{item['counts']}}</view>
							<view class="color-title pt10">
								<my-price v-if="item.amount" :price="item.amount"></my-price>
								<view v-if="item.integral" class="color-title font14">{{unit}}: {{item.integral}}</view>
							</view>

						</view>
					</view>
				</view>
			</view>

			<view class="block-item p10 font14 color-content">
				<view @click="copy(order_detail.orderid)">
					订单编号: {{order_detail.orderid}}
					<text class="iconfont icon-copy font14 color-sub pl5"></text>
				</view>
				<view class="pt5" v-if="order_detail.amount">
					商品总额: <my-price :price="order_detail.amount" :big_size="14"></my-price>
				</view>
				<view class="pt5" v-if="order_detail.integral">商品{{unit}}: {{order_detail.integral}}</view>
				<view class="pt5">
					<text class="pr5">订单状态:</text>
					<text :style="{color: order_status_option[order_detail.order_status].color || '#495060'}">
						{{order_status_option[order_detail.order_status].title || order_detail.order_status || ''}}
					</text>
				</view>
				<view class="pt5">下单时间: {{order_detail.create_time}}</view>
				<view class="pt5" v-if="order_detail.memo">订单备注: {{order_detail.memo}}</view>
			</view>

            <view v-if="logistics_tracking.length" class="block-item">
				<view class="color-content p10 font14">物流单号</view>
                <view class="pl10 pb5" v-for="(item, index) in logistics_tracking" :key="index"
                      @click="copy(item)">
                    <text>{{ item }}</text>
                    <text class="iconfont icon-copy font14 color-sub pl5"></text>
                </view>
			</view>
		</view>


		<view class="bottom-bar flex-kai bg-white">
			<view></view>
			<view class="flex-row">
                <change-order-status v-if="is_admin" :order-id="orderid"
                                     :order-status="order_detail.order_status" @change="orderStatusChange">
                    <view class="font14 color-sub" hover-class="navigator-hover">
                        <text class="iconfont icon-edit font14 color-sub"></text>
                        <text style="padding-left: 2px;">更改订单状态</text>
                    </view>
                </change-order-status>

                <view class="font14 color-sub pl10" hover-class="navigator-hover" @click="deleteOrder">
                    <text class="iconfont icon-delete font14 color-sub"></text>
                    <text style="padding-left: 2px;">删除订单</text>
                </view>
			</view>
		</view>

	</view>
</template>

<script>
    const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'
	import orderStatus from './order_status.js'

	import myPrice from '@/pages/shop/components/my-price.vue'
	import changeOrderStatus from "@/pages/shop/components/change-order-status.vue"

	export default {
		components: {changeOrderStatus, myPrice},
		data() {
			return {
				orderid: '',
				order_detail: {},
				goods_list: [],
				order_status_option: {},
				order_status_list: [],
				pay_order: false,
				is_admin: false,
				error: '',
				unit: app.globalData['tempData'].shop_integral_unit || '金币',
                logistics_tracking: []
			}
		},
		onLoad(e) {
			this.orderid = e.orderid
			if (e.pay_order) this.pay_order = true
			if (e.is_admin) this.is_admin = Number(e.is_admin) === 1

			uni.showLoading({
				mask: true,
				title: '数据加载中...'
			})

			login.uniLogin(err => {
			    if (err && err.errMsg) {
			        uni.hideLoading()
			        uni.showModal({
			            title: err.errTitle || '提示',
			            content: err.errMsg,
			            showCancel: false
			        })
			        return false
			    }

				this.getOrderStatus()

			})
		},

		methods: {
			async getOrderStatus() {
				const {order_status_option, order_status_list} = await orderStatus.get()
				this.order_status_option = order_status_option
				this.order_status_list = order_status_list

				await this.getOrderDetail()
			},

			async getOrderDetail() {
				const res = await xwy_api.request({
					url: 'front.user.shop.order.order_list/order_details',
					data: {
						orderid: this.orderid
					}
				})

				uni.hideLoading()

				if (res?.data?.order_details) {
					const order_detail = res.data.order_details
					if (order_detail.goods_list && order_detail.goods_list.length) {
						const goods_list = JSON.parse(JSON.stringify(order_detail.goods_list))
						this.initGoodsList(goods_list)
						// delete order_detail.goods_list
					}
					this.order_detail = order_detail

                    this.getLogisticsTracking()
				} else {
					this.error = res && res.info || '订单获取失败'
				}
			},

			initGoodsList(goods_list) {
				goods_list.forEach(v => {
					const detail = v.goods_details,
						sku = v.goods_sku_details


					v.thumb = sku.thumb_pic || detail.thumb
					v.title = detail.title
					if (!detail.is_default) v.sku_title = sku.title

					// delete v.goods_details
					// delete v.goods_sku_details
				})

				this.goods_list = goods_list
			},

            async getLogisticsTracking() {
                const res = await this.xwy_api.request({
                    url: 'front.user.shop.order.deliveryOrder/order_delivery_num_list',
                    data: {
                        orderid: this.orderid
                    }
                })

                const list = res?.data?.['delivery_list']
                if (!list?.length) return

                this.logistics_tracking = list.map(item => item.delivery_num.split(',')).flat()
            },

            orderStatusChange(status) {
                this.order_detail.order_status = status
                this.getOpenerEventChannel().emit('changeOrderStatus', status)
            },


            async deleteOrder() {
                const success = await orderStatus.deleteOrder(this.order_detail.foreign_orderid, this.orderid, this.is_admin)
                if (!success) return

                this.getOpenerEventChannel().emit('uploadList')
                this.$uni.navigateBack(1, {delay: 1000})
            },


			copy(data) {
				uni.setClipboardData({
					data,
					success: () => {
						uni.showToast({
							title: '复制成功',
							icon: 'none'
						})
					}
				})
			}
		}
	}
</script>

<style>
	.page {
		min-height: 100vh;
		box-sizing: border-box;
		padding-bottom: 60px;
	}

	.block-item {
		margin: 10px;
		margin-top: 0;
		border-radius: 5px;
		background-color: #fff;
	}

	.goods-logo {
		width: 80px;
		height: 80px;
		min-width: 60px;
		border-radius: 5px;
	}

	.bottom-bar {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		box-sizing: border-box;
		padding: 10px;
		padding-bottom: 15px;
		border-top: 1px solid #eee;
	}
</style>
