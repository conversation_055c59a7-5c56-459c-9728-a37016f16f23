<template>
	<view class="page">
		<view v-if="user_details.id" class="user-info bg-white flex-row">
			<view class="headimg pr10">
				<image :src="user_details.headimg" mode="aspectFill"/>
			</view>
			<view>
				<view class="color-content">{{user_details.must_submit[0].value || ''}}</view>
				<view class="color-warning pt15">我的{{unit}}: {{user_details['integral_left'] || 0}}</view>
			</view>
		</view>

		<view class="bg-background" style="height: 10px;"></view>

		<view class="goods-list">
			<view
				class="goods-item flex-row p10"
				v-for="(item, index) in pay_goods_list"
				:key="index"
			>
				<view v-if="item.thumb" class="pr10">
					<image class="thumb" :src="item.thumb"/>
				</view>
				<view class="flex-column flex-kai" style="width: 100%;">
					<view>
						<view class="color-title ellipsis--l2">{{item.title}}</view>
						<view v-if="item.price || !item.integral" class="pt5">
							<my-price class="color-red" :price="item.price"></my-price>
						</view>
						<view v-if="item.integral" class="pt5 color-warning">{{item.integral}}{{unit}}</view>
					</view>
					<view v-if="!shop_exchange_limit" class="flex-kai">
						<view></view>
						<view>
							<uni-number-box :min="1" :max="99" v-model="item.buy_count"></uni-number-box>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="bg-background" style="height: 10px;"></view>

		<view class="p10">
			<view class="color-sub font14 pb5">收货人姓名:</view>
			<uni-easyinput v-model="name" :maxlength="20" placeholder="必填" prefixIcon="person-filled"></uni-easyinput>
		</view>

		<view class="p10">
			<view class="color-sub font14 pb5">收货人手机号码:</view>
			<uni-easyinput v-model="tel" type="number" :maxlength="11" placeholder="必填" prefixIcon="phone-filled" trim="all"></uni-easyinput>
		</view>

		<view class="p10">
			<view class="flex-kai color-sub font14 pb5">
                <view>收货地址:</view>
                <view @click="takeTtYourselfChange">
                    <checkbox :checked="take_it_yourself"/>自提
                </view>
            </view>
			<uni-easyinput v-if="!take_it_yourself" v-model="address" type="textarea"
                           :maxlength="200"></uni-easyinput>
		</view>

		<view class="p10">
			<view class="color-sub font14 pb5">下单备注:</view>
			<uni-easyinput
				type="textarea"
				v-model="memo"
				:maxlength="200"
				placeholder="请填写下单备注,200字内,可不填"
			></uni-easyinput>
		</view>

		<view v-if="goods_count" class="pay-bar flex-kai bg-white">

			<view class="font14 color-sub" style="line-height: 40px;">
				<text class="pr5">合计:</text>
				<my-price
					 v-if="goods_all_price || !goods_all_integral"
					class="color-red"
					:price="goods_all_price"
					:big_size="18"
					:small_size="14"
					:unit_size="14"
					:point_size="14"
				></my-price>
				<template v-if="goods_all_integral">
					<text class="pl10 font18 color-warning">{{goods_all_integral}}</text>
					<text>{{unit}}</text>
				</template>
			</view>

			<view class="pay-order-now" @click="placeOrder">立即下单</view>
		</view>
	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'
	import base64 from '@/utils/base64.js'

	import myPrice from '@/pages/shop/components/my-price.vue'

	export default {
		components: {
			myPrice
		},
		data() {
			return {
				pay_goods_list: [],
				goods_count: 0,
				goods_all_price: 0,
				goods_all_integral: 0,
				name: '',
				tel: '',
				address: '',
				memo: '',
				unit: app.globalData['tempData'].shop_integral_unit || '金币',
                shop_exchange_limit: app.globalData['tempData'].shop_exchange_limit || 0,
				user_details: {},
                take_it_yourself: false
			}
		},
		watch: {
			pay_goods_list: {
				handler(val) {
					let count = 0,
						price = 0,
						integral = 0

					val.forEach(v => {
						count += v.buy_count
						if (v.integral) {
							const goods_all_integral = v.integral * v.buy_count
							integral += goods_all_integral
						}
						if (v.price) {
							const goods_all_price = v.price * v.buy_count
							price += goods_all_price
						}
					})

					this.goods_count = count
					this.goods_all_price = price
					this.goods_all_integral = integral
				},
				deep: true
			}
		},
		onLoad(e) {
			this.active_id = e.active_id
			if (e.from_cart) this.from_cart = true

			uni.showLoading({
				mask: true,
				title: '数据加载中...'
			})

		    login.uniLogin(err => {
		        if (err && err.errMsg) {
		            uni.hideLoading()
		            uni.showModal({
		                title: err.errTitle || '提示',
		                content: err.errMsg,
		                showCancel: false
		            })
		            return false
		        }

				this.getUserStatus()
				this.getUserAddress()

				this.initPayGoodsList()
		    })
		},
		methods: {
			async getUserStatus() {
				const res = await xwy_api.request({
					url: 'front.flat.sport_step.user/user_attend_details',
					data: {
						access_token: app.globalData.access_token,
						active_id: this.active_id
					}
				})

				uni.hideLoading()

				if (res?.data?.user_details) {
					const attend_details = res.data.user_details
					this.user_details = attend_details
				}
			},

			initPayGoodsList() {
				this.pay_goods_list = [...app.globalData.pay_goods_list]

			},

            takeTtYourselfChange() {
                this.take_it_yourself = !this.take_it_yourself
                this.address = this.take_it_yourself ? '自提' : ''
            },

			userCheck() {
				if (this.tel.toString().length !== 11) {
					xwy_api.alert('请输入正确的 收货人手机号码')
					return false
				}

				if (!this.name) {
					xwy_api.alert('请输入 收货人姓名')
					return false
				}

				if (this.goods_all_integral && (!this.user_details.integral_left || this.user_details.integral_left < this.goods_all_integral)) {
					xwy_api.alert(`${this.unit}不足`)
					return false
				}

				return true

			},

			getUserAddress() {
				const data = uni.getStorageSync('user_address')
				if (!data) return false
				if (data.name) this.name = data.name
				if (data.tel) this.tel = data.tel
				if (data.address) this.address = data.address
			},

			saveUserAddress() {
				const data = {
					name: this.name,
					tel: this.tel,
					address: this.address
				}

				uni.setStorageSync('user_address', data)
			},

			async placeOrder() {
				if (!this.userCheck()) return false

				this.saveUserAddress()


				uni.showLoading({
					title: '订单提交中...',
					mask: true
				})

				const res = await xwy_api.request({
					url: 'front.flat.sport_step.gift_goods/buy_active_gift_goods',
					data: {
						access_token: app.globalData.access_token,
						active_id: this.active_id,
						goods_list: this.getGoodsData(),
						name: this.name,
						tel: this.tel,
						address: this.address,
						memo: this.memo
					}
				})

				uni.hideLoading()

				if (!res || !res.status) {
					xwy_api.alert(res && res.info || '下单失败')
					return false
				}

				this.from_cart && this.resetCartData()

				uni.redirectTo({
					url: './order_detail?pay_order=1&orderid=' + res.data.create_res.orderid
				})
			},

			getGoodsData() {
				const list = []
				this.pay_goods_list.forEach(v => {
					const item = {
						goods_id: v.goods_id,
						sku_id: v.sku_id,
						buy_count: v.buy_count
					}
					list.push(item)
				})

				const list_str = JSON.stringify(list)
				const list_base64 = base64.encode(list_str)

				return list_base64
			},

			resetCartData() {
				const storage_key = 'shop_cart_' + this.active_id
				const storage_cart_data = uni.getStorageSync(storage_key) || []
				const list = app.globalData.pay_goods_list

				let i = storage_cart_data.length
				while(i--) {
					const item = storage_cart_data[i]
					const is_in_index = list.findIndex(v => v.goods_id === item.goods_id && v.sku_id === item.sku_id)
					if (is_in_index !== -1) storage_cart_data.splice(i, 1)
				}

				uni.setStorageSync(storage_key, storage_cart_data)
			}
		}
	}
</script>

<style>
	.page {
		padding-bottom: 70px;
	}

	.user-info {
		padding: 20px;
	}
	.headimg image {
		width: 60px;
		min-width: 60px;
		height: 60px;
		border-radius: 50%;
		display: block;
	}

	.thumb {
		width: 80px;
		min-width: 80px;
		height: 80px;
		border-radius: 5px;
		display: block;
	}

	.pay-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		z-index: 999;
		border-top: 1px solid #eee;
		padding: 5px 10px;
		padding-bottom: 15px;
		box-sizing: border-box;
	}
	.pay-order-now {
		width: 120px;
		height: 40px;
		font-size: 16px;
		line-height: 40px;
		border-radius: 20px;
		background: linear-gradient(90deg, #FE6035, #EF1224);
		color: #fff;
		text-align: center;
	}
</style>
