<template>
    <view class="page bg-background">
        <view v-if="!in_loading" class="top-bar bg-white">
            <view class="search flex-kai p10">
                <view class="input-view">
                    <view class="search-icon left-icon flex-all-center">
                        <uni-icons type="search" size="20" color="#bbbec4"/>
                    </view>
                    <input
                        class="input bg-background"
                        type="text"
                        confirm-type="search"
                        v-model="keyword"
                        @confirm="search"
                        placeholder="根据姓名/订单名称搜索"
                        placeholder-style="color:#bbbec4"
                    />
                    <view class="search-icon right-icon flex-all-center" @click="keyword = ''">
                        <uni-icons v-if="keyword" type="close" size="20" color="#bbbec4"/>
                    </view>
                </view>
                <view class="search-go color-info pl10" @click="search">搜索</view>
            </view>

            <view class="flex-kai font14">
                <view class="p10 color-sub">
                    <text>
                        <template v-if="!start_time && !end_time">近一个月共{{ total }}条订单记录</template>
                        <template v-else>{{ start_time }} 至 {{ end_time }}共{{ total }}条订单记录</template>
                    </text>
                    <text v-if="is_admin" class="pl10 color-primary" @click="getOrderList(true)">
                        导出订单记录
                    </text>
                </view>
                <view class="p10 color-light-primary text-right" @click="screenOpen" style="min-width: 50px;">
                    <text>筛选</text>
                    <uni-icons type="forward" size="14" color="#5cadff"/>
                </view>
            </view>
        </view>

        <view class="list">
            <view class="item bg-white" v-for="(item, index) in order_list" :key="index">
                <view hover-class="navigator-hover" @click="toOrderDetail(index)">
                    <view class="color-content">订单编号: {{ item.orderid }}</view>
                    <view v-if="item.name" class="color-content pt5">下单用户: {{ item.name }}</view>
                    <view class="color-content pt5" v-if="item.amount">
                        订单金额:
                        <my-price :price="item.amount"></my-price>
                    </view>
                    <view class="color-content pt5" v-if="item.integral">
                        订单{{ unit }}: {{ item.integral }}
                    </view>
                    <view class="color-content pt5">
                        <text class="pr5">订单状态:</text>
                        <text :style="{color: order_status_option[item.order_status].color || '#495060'}">
                            {{ order_status_option[item.order_status].title || item.order_status || '' }}
                        </text>
                    </view>
                    <view class="color-sub pt5">下单时间: {{ item.create_time }}</view>
                </view>

                <view class="item-bar flex-kai">
                    <view></view>
                    <view class="flex-row">
                        <view v-if="select" class="font14 color-light-primary pl10"
                              hover-class="navigator-hover" @click="selectOrder(item)">
                            选择订单
                        </view>
                        <template v-else>
                            <change-order-status v-if="is_admin" :order-id="item.orderid"
                                                 :order-status.sync="item.order_status">
                                <view class="font14 color-sub" hover-class="navigator-hover">
                                    <text class="iconfont icon-edit font14 color-sub"></text>
                                    <text style="padding-left: 2px;">更改订单状态</text>
                                </view>
                            </change-order-status>

                            <view class="font14 color-sub pl10" hover-class="navigator-hover"
                                  @click="deleteOrder(item.orderid)">
                                <text class="iconfont icon-delete font14 color-sub"></text>
                                <text style="padding-left: 2px;">删除订单</text>
                            </view>
                        </template>
                    </view>
                </view>
            </view>
        </view>

        <view v-if="!in_loading && !order_list.length" class="text-center" style="padding-top: 20vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无订单</view>
        </view>

        <uni-popup ref="screen" type="right" background-color="#fff">
            <view class="screen-main pt15" style="width: 280px;">
                <view class="close-screen flex-all-center" @click="screenClose">
                    <uni-icons type="closeempty" size="24" color="#e1e1e1"/>
                </view>

                <view class="p10">
                    <view class="color-sub font14 pb5">姓名/订单名称:</view>
                    <uni-easyinput v-model="keyword" :maxlength="50" trim="all"></uni-easyinput>
                </view>

                <view class="p10">
                    <view class="color-sub font14 pb5">订单号:</view>
                    <uni-easyinput v-model="orderid" :maxlength="30" trim="all"></uni-easyinput>
                </view>

                <view class="p10">
                    <view class="color-sub font14 pb5">订单状态:</view>
                    <uni-data-select
                        v-model="order_status"
                        :localdata="screen_order_status_list"
                    ></uni-data-select>
                </view>

                <view class="p10">
                    <view class="color-sub font14 pb5">下单时间段:</view>
                    <uni-datetime-picker
                        :value="start_time"
                        returnType="string"
                        placeholder="起始时间"
                        start="2022-6-1"
                        end="2038-12-31"
                        @change="screenTimeChange($event, 'start_time')"
                    />
                    <view class="pt5"></view>
                    <uni-datetime-picker
                        :value="end_time"
                        returnType="string"
                        placeholder="结束时间"
                        start="2022-6-1"
                        end="2038-12-31"
                        @change="screenTimeChange($event, 'end_time')"
                    />
                </view>

                <view class="flex-center pt15">
                    <view class="screen-btn bg-background color-sub" @click="resetScreenData">重置</view>
                    <view class="screen-btn bg-primary color-white" @click="screen">筛选</view>
                </view>
            </view>
        </uni-popup>

        <uni-load-more v-if="in_loading && load_page > 1" status="loading"></uni-load-more>
        <uni-load-more v-if="!is_last_page && !in_loading" status="more"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && order_list.length > 8"
            status="noMore"
            :contentText="{contentnomore: '我是有底线的'}"
        ></uni-load-more>


        <uni-popup ref="export_success" type="center">
            <view class="uni_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('export_success')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top bg-primary color-white" style="padding: 50px 20px 20px;">
                    <icon type="success" size="80" color="#ffffff"></icon>
                    <view class="font18">导出成功</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ export_success_tips }}
                    </view>
                    <view
                        class="uni_popup-btn bg-info color-white"
                        hover-class="navigator-hover"
                        @click="copyDownloadSrc(download_src, false)"
                    >复制下载地址
                    </view>
                </view>
            </view>
        </uni-popup>

    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import orderStatus from './order_status.js'
import my_storage from '@/utils/storage.js'

import myPrice from '@/pages/shop/components/my-price.vue'
import changeOrderStatus from "@/pages/shop/components/change-order-status.vue"

export default {
    components: {myPrice, changeOrderStatus},
    data() {
        return {
            active_id: '',
            in_loading: true,
            order_list: [],
            load_page: 1,
            is_last_page: false,
            total: 0,
            keyword: '',
            unit: app.globalData['tempData'].shop_integral_unit || '金币',
            order_status_option: {},
            order_status_list: [],
            screen_order_status_list: [],
            orderid: '',
            start_time: '',
            end_time: '',
            order_status: 0,
            is_admin: 0,
            export_success_tips: '',
            download_src: '',
            select: false
        }
    },

    onLoad(e) {
        this.active_id = e.active_id
        this.is_admin = e.is_admin ? Number(e.is_admin) : 0
        if (e.select) this.select = true
        if (e.unit) {
            this.unit = e.unit
            app.globalData['tempData'].shop_integral_unit = e.unit
        }
        uni.showLoading({
            mask: true,
            title: '数据加载中...'
        })


        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err.errTitle || '提示'})
            }

            this.getOrderStatus()

        })
    },

    onReachBottom() {
        !this.in_loading && !this.is_last_page && this.getOrderList()
    },

    methods: {
        async getOrderStatus() {
            const {order_status_option, order_status_list} = await orderStatus.get()
            this.order_status_option = order_status_option
            this.order_status_list = order_status_list

            this.screenOrderStatusListInit()
            await this.getOrderList()
        },

        screenOrderStatusListInit() {
            this.screen_order_status_list = this.order_status_list.map(v => ({
                text: v.title,
                value: v.types
            }))
        },

        toOrderDetail(index) {
            const item = this.order_list[index]
            this.$uni.navigateTo(`./order_detail?orderid=${item.orderid}&is_admin=${this.is_admin}`, {
                events: {
                    uploadList: () => {
                        this.load_page = 1
                        this.getOrderList()
                    },
                    changeOrderStatus: status => {
                        item.order_status = status
                    }
                }
            })
        },

        selectOrder(item) {
            this.getOpenerEventChannel().emit('selectOrder', item)
            this.$uni.navigateBack()
        },

        async deleteOrder(orderid) {
            const success = await orderStatus.deleteOrder(this.active_id, orderid, this.is_admin)
            if (!success) return

            this.load_page = 1
            await this.getOrderList()
        },

        screenOpen() {
            this.$refs.screen.open()
        },
        screenClose() {
            this.$refs.screen.close()
        },

        screenTimeChange(e, key) {
            if (!e) {
                this[key] = ''
                return false
            }
            let value = e
            if (value.length !== 19) value += '00:00:00'
            this[key] = value
        },

        resetScreenData() {
            this.keyword = ''
            this.orderid = ''
            this.start_time = ''
            this.end_time = ''
            this.order_status = ''

            this.load_page = 1
            this.getOrderList()
        },

        screen() {
            this.screenClose()
            this.$uni.showLoading('筛选中...')
            this.load_page = 1
            this.getOrderList()
        },

        search() {
            this.$uni.showLoading('搜索中...')
            this.load_page = 1
            this.getOrderList()
        },

        async getOrderList(need_excel = false) {
            if (need_excel) {
                this.$uni.showLoading('导出中...')
            } else {
                if (this.load_page === 1) {
                    this.order_list = []
                    this.is_last_page = false
                }
            }

            this.in_loading = true

            const data = {
                foreign_orderid: this.active_id,
                is_admin: this.is_admin,
                keyword: this.keyword,
                orderid: this.orderid,
                start_time: this.start_time,
                end_time: this.end_time,
                order_status: this.order_status
            }

            if (need_excel) {
                data.need_excel = 1
            } else {
                data.page = this.load_page
                data.perpage = 10
            }

            const res = await xwy_api.request({
                url: 'front.user.shop.order.order_list/order_list',
                data
            })

            uni.hideLoading()
            this.in_loading = false

            if (need_excel) {
                if (!res || !res.data || !res.data.url) {
                    return this.$uni.showModal(res?.info || '导出失败')
                }
                this.download_src = res.data.url
                this.export_success_tips = res.info || '数据已导出，请复制此地址在浏览器打开下载Excel报表。'
                this.uniPopupOpen('export_success')
                this.copyDownloadSrc(res.data.url, true)

                my_storage.setExportExcelRecord({
                    url: res.data.url,
                    title: `导出订单记录`
                })

                return
            }

            this.load_page++

            if (res?.data?.order_list) {
                const order_data = res.data.order_list
                const order_list = order_data.data || []
                this.order_list = [...this.order_list, ...order_list]
                this.is_last_page = order_data.is_lastpage
                this.total = order_data.total
            } else {
                this.is_last_page = true
            }
        },

        uniPopupOpen(ref) {
            this.$refs[ref].open()
        },

        uniPopupClose(ref) {
            this.$refs[ref].close()
        },

        copyDownloadSrc(src, hide = false) {
            !hide && this.uniPopupClose('export_success')
            uni.setClipboardData({
                data: src,
                success: () => hide ? uni.hideToast() : this.$uni.showToast('复制成功', 'success', 1000)
            })
        },
    }
}
</script>

<style>
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-top: 100px;
}

.top-bar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;
}

.search, .search .input {
    height: 40px;
    line-height: 40px;
}

.input-view {
    position: relative;
}

.search-icon {
    position: absolute;
    width: 30px;
    height: 40px;
    top: 0;
}

.left-icon {
    left: 0;
}

.right-icon {
    right: 0;
}

.search .input {
    width: calc(100vw - 90px);
    padding: 0 30px;
    border-radius: 20px;
    box-sizing: border-box;
}

.search-go {
    width: 60px;
    min-width: 60px;
    text-align: right;
}


/* #ifdef H5 */
.search-go {
    padding-right: 10px;
    box-sizing: border-box;
}

@media screen and (min-width: 500px) {
    .top-bar {
        width: 500px;
        left: calc(((100vw - 500px) / 2) - 10px);
    }

    .search .input {
        width: 440px;
    }
}

/* #endif */

.item {
    margin: 10px;
    padding: 10px;
    border-radius: 5px;
}

.item-bar {
    border-top: 1px solid #eee;
    padding-top: 10px;
    margin-top: 10px;
}

.uni-stat__select {
    padding: 0 !important;
}

.uni-select {
    width: 260px;
}

.uni-date-changed {
    padding: 0 30px !important;
}

.screen-main {
    position: relative;
}

.close-screen {
    position: absolute;
    right: 5px;
    top: 5px;
    width: 40px;
    height: 40px;
}

.screen-btn {
    width: 80px;
    line-height: 36px;
    border-radius: 18px;
    text-align: center;
    font-size: 14px;
}

.uni_popup {
    width: 280px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}

.uni_popup-btn {
    line-height: 40px;
    border-radius: 20px;
}

.uni_popup-btn::after {
    border: none;
}
</style>
