<template>
    <view class="geek_card" v-if="isShow" :class="{'mask--': showMask, 'fixed--': fixed}">
        <!-- 卡片容器 -->
        <view class="card_container">
            <!-- 卡片 -->
            <view class="cardImgList">
                <view class="cardImg" v-for="(item, index) in cardListInfo" :key="index"
                      :style="{
                        top: item.top, 
						left: item.left, 
						zIndex: item.zIndex, 
						transform: item.transform, 
						position: item.position,
						transition: `all ${time / 1000}s linear`
					  }"
                >
                    <view class="container" :style="{paddingBottom: item.spacing}">
                        <card-item :item="item" :width="width" :height="height" :show-time="showCardTime"
                                   :show-reverse-side="showReverseSide" @clickCard="clickCard(item)"/>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>
<script>
import cardItem from './card-item.vue'

export default {
    name: "geek-card",
    components: {cardItem},
    props: {
        // 卡片列表
        cardList: {
            default: []
        },
        // 卡牌发放动画时间
        time: {
            default: 300,
            type: Number
        },
        // 一行的数量
        lineNum: {
            default: 3,
            type: Number
        },
        // 卡牌间距
        spacing: {
            default: 30,
            type: Number
        },
        // 卡牌宽
        width: {
            default: 230,
            type: Number
        },
        // 卡牌高
        height: {
            default: 260,
            type: Number
        },
        alwaysShow: {
            default: false,
            type: Boolean
        },
        showMask: {
            default: true,
            type: Boolean
        },
        fixed: {
            default: true,
            type: Boolean
        },
        showCardTime: {
            default: false,
            type: Boolean
        },
        showReverseSide: {
            default: false,
            type: Boolean
        }
    },
    data() {
        return {
            // 是否显示
            isShow: false,
            // 整合后的卡牌信息
            cardListInfo: []
        }
    },
    methods: {
        // 显示
        show() {
            this.isShow = true;
            // 获取卡片位置
            setTimeout(() => {
                this.Position();
            }, 500)
        },
        // 隐藏
        hidden() {
            this.isShow = false;
        },
        // 卡片位置
        Position() {
            let halfHeight = (uni.getSystemInfoSync().windowHeight - 60) / 2
            if (!this.fixed) halfHeight = 0
            
            this.cardListInfo.forEach((item, index) => {

                setTimeout(() => {
                    // 下标 / 一排的数量 = 第几排 
                    // 从第 0 排开始
                    let num = Math.floor(index / this.lineNum);
                    // 下标 - 求出的整数 * 一排的数量 = 在本排的第几个
                    // 从第 1 个开始
                    let sort = index - num * this.lineNum;
                    // 最大排数
                    let maxRowNum = Math.ceil(this.cardListInfo.length / this.lineNum);
                    // 求出 y轴 的位置
                    this.cardListInfo[index].top = num * (this.height + this.spacing) + this.spacing + halfHeight + 'px';
                    // 求出 x轴 的位置
                    this.cardListInfo[index].left = sort ? sort * (this.width + this.spacing) + this.spacing + 'px' : this.spacing + 'px';
                    this.cardListInfo[index].transform = null;
                    // 如果为本排为最后一排
                    this.cardListInfo[index].spacing = (num + 1 === maxRowNum ? this.spacing : 0) + 'px';
                    setTimeout(() => {
                        this.cardListInfo[index].zIndex = 1;
                        this.cardListInfo[index].position = 'absolute';

                        if (index === this.cardListInfo.length - 1) this.end()

                    }, this.time)
                }, (index + 1) * this.time)
            })
        },

        end() {
            this.$emit('end')
            if (!this.alwaysShow) this.hidden()  
        },

        clickCard(item) {
            this.$emit('clickCard', item)  
        },
        
        // TODO nvue 取消冒泡
        clear(e) {
            // #ifndef APP-NVUE
            e.stopPropagation()
            // #endif
        },
    },
    created() {
        this.cardListInfo = Array.from(this.cardList, (item, index) => {
            return Object.assign({
                top: `50vh`,
                left: `50vw`,
                transform: `translate(-50%, -50%)`,
                zIndex: this.cardList.length - index,
                position: 'fixed',
            }, item);
        });
    },
    watch: {}
}
</script>
<style lang="less" scoped>
.mask-- {
    background-color: rgba(0, 0, 0, 0.6);
}

.fixed-- {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 999;
}

.geek_card {
    .card_container {
        .isBlindBg {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: 0;
            z-index: 99;

            > image {
                width: 750rpx;
            }
        }

        // 获取卡片弹窗
        .cardImgList {
            position: relative;
            overflow-y: scroll;
            width: 100vw;
            min-height: 100vh;

            .cardImg {
                position: absolute;
                background-position: center;
                background-size: cover;
                border-radius: 5px;
            }
        }
    }
}
</style>