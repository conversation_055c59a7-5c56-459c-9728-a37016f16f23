<template>
    <view class="card" :class="{'show-reverse-side': showReverseSide, 'card-animation': showBack}"
          :style="cardStyle" @click="clickCard">
        <view class="front">
            <view>{{ item.title }}</view>
        </view>
        <view class="back">
            <view>{{ item.title }}</view>
        </view>
    </view>
</template>

<script>
// 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
/** @namespace item.title */
export default {
    name: "card-item",
    props: {
        item: {
            type: Object,
            default: () => ({})
        },
        width: {
            type: Number,
            default: 0
        },
        height: {
            type: Number,
            default: 0
        },
        showTime: {
            type: Boolean,
            default: false
        },
        showReverseSide: {
            type: Boolean,
            default: false
        }
    },
    
    computed: {
        cardStyle() {
            const width = this.width ? `${this.width}px` : '340rpx'
            const height = this.height ? `${this.height}px` : '415rpx'
            return `width: ${width}; height: ${height}`
        }
    },
    
    data() {
        return {
            showBack: false
        }
    },

    mounted() {

    },

    methods: {
        clickCard() {
            if (!this.showReverseSide) return this.$emit('clickCard')
            
            if (this.showBack) return
            this.showBack = true
            setTimeout(() => {
                this.$emit('clickCard')
            }, 800)
            setTimeout(() => {
                this.showBack = false
            }, 1200)
        }
    }
}
</script>

<style lang="scss">
.show-reverse-side {
    position: relative;
    transform-style: preserve-3d;
    /* 给卡片添加默认动画 */
    animation: rotate-reverse 1.2s cubic-bezier(0.66, -0.47, 0.33, 1.5) forwards;
}

/* 设置鼠标移入卡片时执行动画 */
.card-animation {
    /* 动画（名称 时长 第三个属性是贝塞尔曲线，我们可以自定义动画的运动轨迹，让动画的运动轨迹有了很多种可能 第四个属性是当我们的动画完成时的状态，一般动画完成之后就回到了0%的状态，默认值是backwards，当我们给的属性值是forwards时，那么动画到100%的时候就会停下来，不会回到0%） */
    /* 哈哈，这里又啰嗦了，大家见谅哈 */
    animation: rotate 1.2s cubic-bezier(0.66, -0.47, 0.33, 1.5) forwards;
    /* 大家有没有发现咱们的动画看起来有点生硬，不是很自然 */
    /* 这里再给大家介绍一个属性，划重点了哦！！ */
}

.front,
.back {
    /* 绝对定位 子元素是绝对定位，父元素需要相对定位 */
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 10px;
    box-sizing: border-box;
    /* 弹性布局，让元素垂直陈列 */
    display: flex;
    flex-direction: column;
    /* 现在子元素垂直陈列，那么就是让子元素水平居中 */
    align-items: center;
    /* 平均分配高度给每一个子元素 */
    justify-content: space-around;
    background-color: #fff;
    /* 隐藏旋转div元素的背面 */
    backface-visibility: hidden;

    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    border-radius: 5px;
    font-size: 18px;
}

.front {
    color: #ffe0b3;
    background-image: url(https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/weijie-temp/cardbg.jpg);
}

.back {
    color: #B40E1F;
    background-image: url(https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/weijie-temp/cardfront.jpg);
    transform: rotateY(180deg);
}

/* 接下来我们定义一下旋转动画 */
@keyframes rotate {
    0% {
        transform: rotateY(0deg);
    }

    100% {
        transform: rotateY(180deg);
    }
}

@keyframes rotate-reverse {
    0% {
        transform: rotateY(180deg);
    }

    100% {
        transform: rotateY(0deg);
    }
}
</style>