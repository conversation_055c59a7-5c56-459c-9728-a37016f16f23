<template>
    <view class="page">
        <movable-area class="movable-area">
            <movable-view class="card-slot-item flex-all-center" v-for="(item, index) in card_slot"
                          :key="index" direction="none" :x="item.x" :y="item.y"
                          :style="{width: card_w + 'px', height: card_h + 'px'}">
                {{ index + 1 }}
            </movable-view>
            <movable-view v-for="(item, index) in card_list" :key="item.id" direction="all" out-of-bounds
                          :x="item.left" :y="item.top" @change="change($event, item)"
                          :style="{zIndex: item.zIndex, opacity: item.opacity, width: card_w + 'px', height: card_h + 'px'}"
                          @touchstart="touchStart(item)" @touchend="touchEnd">
                <card-item :item="item" :width="card_w" :height="card_h"/>
            </movable-view>
        </movable-area>

        <view class="complete-view bg-white">
            <view class="complete-button text-center" @click="complete">完成</view>
        </view>
    </view>
</template>

<script>
import cardItem from './card-item.vue'

export default {
    name: "draw-card-new",
    components: {cardItem},
    props: {
        list: {
            type: Array,
            default: () => []
        },
        lineNum: {
            type: Number,
            default: 3  
        },

        width: {
            type: Number,
            default: 0
        },
        height: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            card_list: [],
            card_slot: [],
            card_w: this.width,
            card_h: this.height,
            touchIndex: null
        }
    },

    mounted() {
        this.Position()
    },

    methods: {
        Position() {
            const halfHeight = (uni.getSystemInfoSync().windowHeight - 60) / 2
            const card_list = JSON.parse(JSON.stringify(this.list))
            const positionList = [], card_slot = []
            card_list.forEach((item, index) => {
                item.index = index
                item.target_index = null

                const x = 10 + index % this.lineNum * (this.card_w + 10)
                item.left = x

                const y = 10 + halfHeight + Math.floor(index / this.lineNum) * (this.card_h + 10)
                item.top = y

                item.zIndex = 1
                item.opacity = 1

                positionList.push({x, y, id: item.id})
                card_slot.push({x, y: y - halfHeight, have_card: false})
            })

            this.card_list = card_list
            this.card_slot = card_slot
            this.positionList = positionList
        },
        
        touchStart(target) {
            this.touchIndex = target.index
            this.targetIndex = null

            if (target.target_index !== null) {
                this.$set(this.card_slot[target.target_index], 'have_card', false)
            }

            clearTimeout(this.restoreStyleTimeout)

            this.card_list.forEach(item => {
                const cur = item.id === target.id
                item.zIndex = cur ? 100 : 1
                item.opacity = cur ? 1 : .5
            })
        },


        change(e, item) {
            if (e.detail.source !== 'touch') return


            const theX = e.detail.x, theY = e.detail.y
            this.targetIndex = this.findTargetPositionIndex({
                curCenterX: theX + this.card_w / 2,
                curCenterY: theY + this.card_h / 2
            })

        },

        touchEnd() {
            this.card_list.forEach((item, index) => {
                if (index === this.touchIndex) {
                    this.move2target(item)
                }

                this.restoreStyleTimeout = setTimeout(() => {
                    item.zIndex = 1
                    item.opacity = 1
                }, 500)
            })
        },

        
        /* 找出拖动到positionList队列里的哪个目标索引
			 * [curObj.curCenterX], Number 当前拖动的模块的中心点x轴坐标
			 * [curObj.curCenterY], Number 当前拖动的模块的中心点y轴坐标
			 * return 返回拖动到的目标索引
			 */
        findTargetPositionIndex(curObj) {
            let resultIndex = null;
            for (let i = 0, len = this.card_slot.length; i < len; i++) {
                const item = this.card_slot[i];
                if (curObj.curCenterX >= item.x && curObj.curCenterX <= item.x + this.card_w) {
                    if (curObj.curCenterY >= item.y && curObj.curCenterY <= item.y + this.card_h && !item.have_card) {
                        resultIndex = i;
                        break;
                    }
                }
            }
            return resultIndex;
        },

        move2target(item) {
            let x = 1, y = 1
            if (this.targetIndex !== null) {
                x = this.card_slot[this.targetIndex].x
                y = this.card_slot[this.targetIndex].y
                this.$set(this.card_list[this.touchIndex], 'target_index', this.targetIndex)
                this.$set(this.card_slot[this.targetIndex], 'have_card', true)
            } else {
                x = this.positionList[this.touchIndex].x
                y = this.positionList[this.touchIndex].y
                this.$set(this.card_list[this.touchIndex], 'target_index', null)
            }
            
            item.left = 1
            item.top = 1
            this.$nextTick(() => {
                item.left = x
                item.top = y
            })
        },

        
        async complete() {
            const list = JSON.parse(JSON.stringify(this.card_list))
            
            if (list.some(item => item.target_index === null)) {
                return this.$uni.showToast('请将所有卡片移动到卡槽内')
            }
            
            const ids = list.sort((a, b) => a.target_index - b.target_index).map(item => item.id).join(',')
            
            this.$emit('complete', ids)
        }
    }
}
</script>

<style lang="scss" scoped>
.movable-area {
    width: 100vw;
    height: calc(100vh - 80px);
}

.card-slot-item {
    border-radius: 5px;
    font-size: 38px;
    color: #eadfc9;
    background-image: url(https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/weijie-temp/cardfront.jpg);
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}

.complete-view {
    position: fixed;
    z-index: 999999;
    bottom: 0;
    left: 0;
    width: 100%;
    box-sizing: border-box;
    padding: 10px;
    background-color: #FFFCFD;

    .complete-button {
        width: 200px;
        margin: 0 auto;
        line-height: 40px;
        border-radius: 20px;
        background-color: #ffca64;
        color: #fff;
    }
}
</style>