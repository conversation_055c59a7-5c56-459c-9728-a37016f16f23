<template>
    <view class="page">
        <movable-area class="movable-area" :style="{height: movableAreaHeight}">
            <movable-view v-for="(item, index) in card_list" :key="item.id" direction="all" out-of-bounds
                          :x="item.left" :y="item.top" @change="change($event, item)"
                          :style="{zIndex: item.zIndex, opacity: item.opacity, width: card_w + 'px', height: card_h + 'px'}"
                          @touchstart="touchStart(item)" @touchend="touchEnd">
                <card-item :item="item" :width="card_w" :height="card_h" :touch-index="touchIndex"/>
            </movable-view>
        </movable-area>
        
        <view class="complete-view bg-white">
            <view class="complete-button bg-light-primary color-white text-center" @click="complete">
                完成
            </view>
        </view>
    </view>
</template>

<script>
import cardItem from './card-item.vue'

export default {
    name: "draw-card",
    components: {cardItem},
    props: {
        list: {
            type: Array,
            default: () => []
        },
        
        width: {
            type: Number,
            default: 0
        },
        height: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            card_list: [],
            card_w: this.width,
            card_h: this.height,
            touchIndex: null
        }
    },
    
    computed: {
        movableAreaHeight() {
            const line = Math.ceil(this.card_list.length / 2)
            return this.card_h * line + (line + 1) * 10 + 'px'
        }
    },

    mounted() {
        this.Position()
    },

    methods: {
        Position() {
            const card_list = JSON.parse(JSON.stringify(this.list))
            const positionList = []
            card_list.forEach((item, index) => {
                item.index = index
                
                const x = index % 2 === 0 ? 10 : this.card_w + 20
                item.left = x

                const y = Math.floor(index / 2) * (this.card_h + 10) + 10
                item.top = y
                
                item.zIndex = 1
                item.opacity = 1

                positionList.push({x, y, id: item.id})
            })
            
            this.card_list = card_list
            this.positionList = positionList
        },
        
        touchStart(target) {
            this.touchIndex = this.findCardIndex(target.id)
            // this.touchIndex = target
            this.targetIndex = null

            console.log(this.touchIndex);

            clearTimeout(this.restoreStyleTimeout)

            this.card_list.forEach(item => {
                const cur = item.id === target.id
                item.zIndex = cur ? 100 : 1
                item.opacity = cur ? 1 : .5
            })
        },


        change(e, item) {
            if (e.detail.source !== 'touch') return


            const theX = e.detail.x, theY = e.detail.y
            const targetIndex = this.findTargetPositionIndex({
                curCenterX: theX + this.card_w / 2,
                curCenterY: theY + this.card_h / 2
            })


            // 目标位置已改变(不是上次的目标位置)    目标位置不是本身位置
            if (targetIndex !== this.targetIndex && targetIndex !== this.touchIndex) {
                this.otherMove(targetIndex)
            }

            this.targetIndex = targetIndex

            console.log(this.targetIndex);
        },
        
        touchEnd() {
            this.card_list.forEach((item, index) => {
                if (index === this.touchIndex) {
                    // item.top = 1
                    // item.left = 1
                    // this.$nextTick(() => {
                    //     item.top = this.positionList[index].y
                    //     item.left = this.positionList[index].x
                    // })
                    
                    this.move2target(item)
                }

                this.restoreStyleTimeout = setTimeout(() => {
                    item.zIndex = 1
                    item.opacity = 1
                }, 500)
            })
        },

        
        findCardIndex(id) {
            return this.card_list.sort((a, b) => a.index - b.index).findIndex(item => item.id === id);
        },

        /* 找出拖动到positionList队列里的哪个目标索引
			 * [curObj.curCenterX], Number 当前拖动的模块的中心点x轴坐标
			 * [curObj.curCenterY], Number 当前拖动的模块的中心点y轴坐标
			 * return 返回拖动到的目标索引
			 */
        findTargetPositionIndex(curObj) {
            let resultIndex = this.touchIndex;
            for(let i = 0, len = this.positionList.length; i < len; i++) {
                const item = this.positionList[i];
                if(curObj.curCenterX >= item.x && curObj.curCenterX <= item.x + this.card_w) {
                    if(curObj.curCenterY >= item.y && curObj.curCenterY <= item.y + this.card_h) {
                        resultIndex = i;
                        break;
                    }
                }
            }
            return resultIndex;
        },

        move2target(item) {
            const index = this.targetIndex === null ? this.touchIndex : this.targetIndex
            const {x, y} = this.positionList[index]
            item.left = 1
            item.top = 1
            this.$nextTick(() => {
                item.left = x
                item.top = y
                item.index = this.targetIndex

                // console.log(this.card_list.map(item => item.index));
            })
        },

        otherMove(targetIndex) {
            let direction = ''
            if (targetIndex > this.touchIndex) direction = 'forward'
            if (targetIndex < this.touchIndex) direction = 'backward'
            

            this.card_list.sort((a, b) => a.index - b.index).forEach((item, index) => {
                if (direction === 'forward') {
                    if (index > this.touchIndex && index <= targetIndex) {
                        item.index = index - 1
                        if (index % 2 === 0) {
                            item.left = this.card_w + 20
                            item.top -= (this.card_h + 10)
                        } else {
                            item.left = 10
                        }
                    }
                }
                
                if (direction === 'backward') {
                    item.index = index + 1
                    if (index < this.touchIndex && index >= targetIndex) {
                        if (index % 2 === 0) {
                            item.left = this.card_w + 20
                        } else {
                            item.left = 10
                            item.top += (this.card_h + 10)
                        }
                    }
                }
            })

            this.touchIndex = this.targetIndex
        },

        async complete() {
            const confirm = await this.$uni.showModal('确定完成吗?', {showCancel: true})
            if (!confirm.confirm) return
            
            this.$emit('complete', this.card_list.sort((a, b) => a.index - b.index).map(item => item.id).join(','))
        }
    }
}
</script>

<style lang="scss" scoped>
.page {
    padding-bottom: 80px;
}

.movable-area {
    width: 100vw;
}

.complete-view {
    position: fixed;
    z-index: 999999;
    bottom: 0;
    left: 0;
    width: 100%;
    box-sizing: border-box;
    padding: 10px;
    
    .complete-button {
        width: 200px;
        margin: 0 auto;
        line-height: 40px;
        border-radius: 20px;
    }
}
</style>