<template>
    <view class="page">
        <geek-card v-if="card_list.length && !start_drag" ref="geekCard" :cardList="card_list" :time="300"
                   :lineNum="lineNum" :spacing="10" :width="card_width" :height="card_height"
                   @end="sendCardEnd"/>
        
        <view v-if="start_drag">
            <view v-if="playTipsShow" class="play-tips flex-all-center" @click="playTipsShow = false">
                <view class="text-center font24">请将下面卡片按照事件发生的时间先后顺序拖动到上面卡槽中</view>
            </view>
            <draw-card-new :list="card_list" :lineNum="lineNum" :width="card_width" :height="card_height"
                           @complete="complete"/>
        </view>
    </view>
</template>

<script>
const app = getApp()
import geekCard from '../components/geek-card.vue'
import drawCardNew from '../components/draw-card-new.vue'

export default {
    components: {geekCard, drawCardNew},
    data() {
        return {
            card_list: [],
            start_drag: false,
            card_width: 0,
            card_height: 0,
            playTipsShow: true,
            lineNum: 3
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.point_id = params.point_id
        this.$login.uniLogin(() => {
            this.init()
        })
    },

    methods: {
        async init() {
            await this.getEvents()
        },

        getCardSize(lineNum) {
            const windowWidth = uni.getSystemInfoSync().windowWidth
            const margin = 10
            const width = (windowWidth - margin * (lineNum + 1)) / lineNum
            this.card_width = width
            this.card_height = width * 1.3
        },
        
        async getEvents() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.game_center.redHistoryEvents/round_events_list',
                data: {
                    active_id: this.active_id,
                    point_id: this.point_id,
                    page: 1,
                    perpage: 1
                }
            })
            
            const event = res?.data?.list?.data?.[0]
            
            if (!event) return this.$uni.showModal('任务设置不完整', {success: () => uni.navigateBack()})
            
            if (event.job_name) this.$uni.setNavigationBarTitle(event.job_name)
            
            this.getCardList(event)
        },

        getCardList(event) {
            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace event.events_list_details */
            /** @namespace event.conf_json.history_events_set */
            
            const all_list = event.events_list_details
            const num = Number(event.conf_json.history_events_set.rand_num)

            this.lineNum = num > 6 ? 4 : num < 3 ? num : 3
            this.getCardSize(this.lineNum)
            
            const nums = new Set()
            while (nums.size < num) {
                nums.add(this._utils.randomNum(0, all_list.length - 1))
            }

            this.card_list = [...nums].map(n => {
                const item = all_list[n]
                return {
                    id: item.id,
                    date_time: item.date_time,
                    title: item.title,
                    pic: item.pic,
                    memo: item.memo
                }
            })
            
            this.$nextTick(() => {
                setTimeout(() => {
                    this.$refs.geekCard.show()
                }, 500)
            })
        },

        sendCardEnd() {
            this.start_drag = true
        },

        async complete(ids) {
            this.$uni.showLoading('提交中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.game_center.redHistoryEvents/submit_rand_option_reward',
                data: {
                    active_id: this.active_id,
                    point_id: this.point_id,
                    ids
                }
            })
            uni.hideLoading()

            this.getOpenerEventChannel().emit('success')
            
            app.globalData['tempData'].sort_card_result = res
            
            this.$uni.redirectTo('./result')
        }
    }
}
</script>

<style lang="scss" scoped>
.page {
    width: 100vw;
    height: 100vh;
    background-color: #FFFCFD;
}

.play-tips {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 9999999;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, .7);
    box-sizing: border-box;
    padding: 0 5vw 60px;
    line-height: 40px;
    color: #ffefd8;
}
</style>