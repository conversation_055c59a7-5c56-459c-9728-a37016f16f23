<template>
    <view class="page">
        <view v-if="is_is_right !== null" class="is-right-tips">
            {{ is_is_right ? '恭喜你' : '很遗憾' }}
        </view>
        
        <view v-if="info" class="pt15 text-center color-sub">{{ info }}</view>
        
        <view v-if="rightSortList.length" style="padding-top: 10vh;">
            <geek-card ref="geekCard" :cardList="rightSortList" :lineNum="lineNum" :time="200" :spacing="10"
                       always-show :show-mask="false" :fixed="false" show-card-time show-reverse-side
                       :width="card_width" :height="card_height" @clickCard="clickCard"/>
        </view>
        
        <uni-popup ref="cardInfo" @touchmove.stop.prevent="">
            <view class="card-info">
                <view class="text-center font24">{{ cardInfo.title }}</view>
                <view class="text-center font18 pt10">{{ cardInfo.date }}</view>

                <scroll-view class="memo-scroll" v-if="cardInfo.memo" :scroll-y="true">
                    <view>
                        <text>{{ cardInfo.memo }}</text>
                    </view>
                </scroll-view>
            </view>
            <view class="flex-all-center" @click="$refs.cardInfo.close()">
                <uni-icons type="close" size="28" color="#ffffff"/>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
import geekCard from '../components/geek-card.vue'

export default {
    components: {geekCard},
    data() {
        return {
            is_is_right: null,
            info: '',
            rightSortList: [],
            card_width: 0,
            card_height: 0,
            cardInfo: null,
            lineNum: 3
        }
    },

    onLoad() {
        this.getResult()
    },

    methods: {
        getResult() {
            const res = app.globalData['tempData'].sort_card_result
            this.is_is_right = this.getIsRight(res)
            this.info = res?.info || '答错啦'
            this.getRightSortList(res)
        },

        getIsRight(res) {
            if (res?.data?.res?.hasOwnProperty('is_right')) return res.data.res.is_right
            return res?.status === 1
        },

        getRightSortList(res) {
            const list = res?.data?.res?.['right_sort_list'] || []
            if (!list.length) return
            
            this.rightSortList = list.map(item => ({
                id: item.id,
                date_time: item.date_time,
                date: this.getDate(item.date_time),
                memo: item.memo,
                pic: item.pic,
                title: item.title
            }))
            
            this.$nextTick(() => {
                this.$refs.geekCard.show()
                this.getCardSize()
            })
        },

        getDate(date_time) {
            const [year, month, day] = date_time.split('-')
            let date = `${year}年${Number(month)}月`
            if (day) date += `${Number(day)}日`
            return date
        },

        getCardSize() {
            const num = this.rightSortList.length
            const lineNum = num > 6 ? 4 : num < 3 ? num : 3
            this.lineNum = lineNum
            const windowWidth = uni.getSystemInfoSync().windowWidth
            const margin = 10
            const width = (windowWidth - margin * (lineNum + 1)) / lineNum
            this.card_width = width
            this.card_height = width * 1.3
        },

        clickCard(item) {
            this.cardInfo = item
            this.$nextTick(() => {
                this.$refs.cardInfo.open()
            })
        }
    }
}
</script>

<style lang="scss">
.page {
    width: 100vw;
    height: 100vh;
    box-sizing: border-box;
    overflow: hidden;
    background-color: #FFFCFD;
}

.is-right-tips {
    padding-top: 10vh;
    padding-bottom: 10px;
    text-align: center;
    color: #ffcd80;
    font-size: 34px;
}

.card-info {
    width: 700rpx;
    height: 910rpx;
    box-sizing: border-box;
    padding: 100rpx 60rpx;
    background-image: url(https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/weijie-temp/cardfront.jpg);
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    border-radius: 20rpx;
    color: #B40E1F;
    
    .memo-scroll {
        margin-top: 40rpx;
        height: 500rpx;
    }
}
</style>