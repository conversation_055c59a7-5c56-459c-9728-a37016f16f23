<template>
    <view class="page">
        <uni-forms label-position="top" label-width="200" border>
            <uni-forms-item label="任务名称">
                <uni-easyinput v-model="job_name" placeholder="可不填"/>
            </uni-forms-item>
            
            <uni-forms-item label="任务事件数量" required>
                <uni-easyinput v-model="rand_num" type="number"
                               :placeholder="'任务随机出的事件数量 请输入2-' + maxRandNum + '的整数'"/>
            </uni-forms-item>
        </uni-forms>
        
        <view class="event p10">
            <view class="flex-kai">
                <view>事件管理</view>
                <view @click="toAddEvent">
                    <text class="color-light-primary font14">添加事件</text>
                    <text class="iconfont color-light-primary icon-more font14"></text>
                </view>
            </view>
            <view class="event-list">
                <view class="event-item flex-kai" v-for="(item, index) in event_list" :key="item.id">
                    <view class="event-info bg-background clear clearfix">
                        <view class="color-title fl">{{ item.title }}</view>
                        <view class="color-content fr">{{ item.date }}</view>
                    </view>
                    <view class="flex-all-center" @click="event_list.splice(index, 1)">
                        <text class="iconfont color-sub icon-delete"></text>
                    </view>
                </view>
            </view>

            <view v-if="!event_list.length" class="text-center" style="padding-top: 20px;">
                <text class="iconfont icon-empty-state color-border" style="font-size: 80px;"></text>
                <view class="color-sub">未添加事件</view>
            </view>
        </view>
        
        <view class="save-view bg-white">
            <view class="save-button bg-light-primary color-white text-center" @click="save">保存</view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            rand_num: '',
            event_list: [],
            job_name: ''
        }
    },
    
    computed: {
        maxRandNum() {
            let max = this.event_list.length
            if (max === 0 || max < 2 || max > 8) max = 8
            return max
        }  
    },

    onLoad(params) {
        this.$uni.showLoading('加载中...')
        
        this.active_id = params.active_id
        this.point_id = params.point_id
        this.point_name = params.point_name
        
        this.$uni.setNavigationBarTitle(`${params.point_name} 关卡事件管理`)
        
        this.getSet()
    },

    methods: {
        async getSet() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.game_center.redHistoryEvents/round_events_list',
                data: {
                    active_id: this.active_id,
                    point_id: this.point_id,
                    page: 1,
                    perpage: 1
                }
            })
            uni.hideLoading()
            
            const set = res?.data?.list?.data?.[0] || {}
            const {conf_json, events_list_details, job_name, id} = set
            
            if (id) this.id = id
            
            if (events_list_details?.length) {
                this.event_list = this.initEventList(events_list_details)
            }
            
            if (conf_json?.['history_events_set']?.rand_num) {
                this.rand_num = conf_json['history_events_set'].rand_num
            }
            this.job_name = job_name || this.point_name
        },
        
        initEventList(list) {
            return list.map(item => ({
                id: item.id,
                title: item.title,
                // date_time: item.date_time,
                date: this.getDate(item.date_time),
                // pic: item.pic,
                // memo: item.memo
            }))
        },

        getDate(date_time) {
            const [year, month, day] = date_time.split('-')
            let date = `${year}年${Number(month)}月`
            if (day) date += `${Number(day)}日`
            return date
        },

        toAddEvent() {
            this.$uni.navigateTo(`./list?active_id=${this.active_id}&select=1`, {
                success: res => {
                    res.eventChannel.emit('haveIds', this.event_list.map(item => item.id))
                },
                events: {
                    selectEvents: list => {
                        this.event_list = this.initEventList(list)
                    }
                }
            })
        },
        
        async save() {
            const rand_num = Math.floor(this.rand_num)
            const ids = this.event_list.map(item => item.id)
            if (ids.length < 2) return this.$uni.showToast('请至少添加2个事件')
            
            if (isNaN(rand_num)) return this.$uni.showToast('请输入正确的任务事件数量')
            if (rand_num < 2) return this.$uni.showToast('任务事件数量不能小于2')
            if (rand_num > ids.length) return this.$uni.showToast('任务事件数量不能大于添加的事件数量')
            if (rand_num > this.maxRandNum) return this.$uni.showToast(`任务事件数量不能大于${this.maxRandNum}`)
            
            this.$uni.showLoading('保存中...')
            
            if (this.id) {
                const res = await this.deleteSet()
                if (res?.status !== 1) {
                    uni.hideLoading()
                    return this.$uni.showModal(res?.info || '保存失败')
                }
            }
            
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.game_center.redHistoryEvents/set_events_to_rushing_round',
                data: {
                    active_id: this.active_id,
                    point_id: this.point_id,
                    rand_num,
                    ids: ids.join(','),
                    job_name: this.job_name
                }
            })
            
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '保存失败')
            this.$uni.showToast('保存成功', 'success')
            
            this.$uni.navigateBack(1, {delay: 1000})
        },
        
        async deleteSet() {
            return this.xwy_api.deleteRecords(107, this.id)
        }
    }
}
</script>

<style lang="scss">
.page {
    padding-bottom: 80px;
}

.uni-forms {
    padding: 10px;
}

.event-list {
    padding-top: 1px;

    .event-item {
        padding: 10px 5px;
        border-radius: 5px;

        .event-info {
            width: 100%;
            padding: 10px;
            margin-right: 10px;
        }
    }
}

.save-view {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    box-sizing: border-box;
    padding: 10px;
    
    .save-button {
        line-height: 44px;
        border-radius: 22px;
    }
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .save-view {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }
}
/* #endif */
</style>