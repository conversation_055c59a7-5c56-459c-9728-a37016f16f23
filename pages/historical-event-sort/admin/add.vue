<template>
    <view class="page">
        <uni-forms label-position="top" :label-width="300">
            <uni-forms-item label="事件名称" required>
                <uni-easyinput v-model="title" :maxlength="50" placeholder="请输入历史事件名称"/>
            </uni-forms-item>

            <uni-forms-item label="事件发生时间">

                <picker mode="multiSelector" :range="date_range" :value="dateRangeValue" range-key="title"
                        @columnchange="dateColumnChange" @change="dateChange">
                    <view class="uni-forms-picker flex-row">
                        <view class="picker-body">
                            <text class="picker-value">
                                {{ date[0] }} 年 {{ date[1] }} 月
                                <template v-if="date[2]">{{ date[2] }} 日</template>
                            </text>
                        </view>
                        <view class="picker-right-icon flex-all-center">
                            <uni-icons type="forward" size="16" color="#dddddd"/>
                        </view>
                    </view>
                </picker>
            </uni-forms-item>


            <uni-forms-item label="事件图片">
                <view v-if="pic" class="image-container">
                    <!--suppress JSValidateTypes-->
                    <image class="image" :src="pic" mode="aspectFill" @click="previewImage"/>
                    <view class="clear-image flex-all-center" @click="pic = ''">
                        <uni-icons type="trash" size="24" color="#ff3810"/>
                    </view>
                </view>
                <view v-else class="upload-image flex-all-center bg-background"
                      hover-class="navigator-hover" @click="changeImage">
                    <uni-icons type="plusempty" size="50" color="#eeeeee"/>
                </view>
            </uni-forms-item>

            <uni-forms-item label="事件描述">
                <uni-easyinput type="textarea" autoHeight v-model="memo" :maxlength="1000"
                               placeholder="请输入事件描述(1000字内)"/>
            </uni-forms-item>
        </uni-forms>

        <view class="save-container flex-all-center bg-white">
            <view class="save-button bg-primary color-white text-center"
                  hover-calss="navigator-hover" @click="checkForm">保存
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            title: '',
            memo: '',
            pic: '',
            date: [1937, 7, 7],
            date_range: [[], [], []]
        }
    },
    
    computed: {
        dateRangeValue() {
            const [year, month, day] = this.date
            const [year_r, month_r, day_r] = this.date_range
            
            const value = [0, 0, 0]
            value[0] = year_r.findIndex(item => item.value === year)
            value[1] = month_r.findIndex(item => item.value === month)
            value[2] = day_r.findIndex(item => item.value === day)
            
            return value
        }
    },

    onLoad(params) {
        this.$uni.showLoading()
        this.paramsHandle(params)
        this.setDateRange()
        this.$login.uniLogin(() => {
            this.init()
        })
    },

    methods: {
        paramsHandle(params) {
            this.active_id = params.active_id
            if (params.id) this.id = Number(params.id)
        },

        setDateRange() {
            const date_range = [[], [], []]
            
            const start_year = 1800
            const current_year = new Date().getFullYear()
            for (let i = start_year; i <= current_year; i++) {
                date_range[0].push({value: i, title: `${i}年`})
            }

            for (let i = 1; i <= 12; i++) {
                date_range[1].push({value: i, title: `${i}月`})
            }
            
            this.date_range = date_range
            
            this.setDateRangeDays(1937, 7)
        },

        setDateRangeDays(year, month) {
            const days = new Date(year, month, 0).getDate()
            console.log(year, month, days);
            const day_list = [{value: 0, title: '不设置'}]
            for (let i = 1; i <= days; i++) {
                day_list.push({value: i, title: `${i}日`})
            }
            this.$set(this.date_range, 2, day_list)
        },

        dateColumnChange(e) {
            const {column, value} = e.detail
            
            if (column === 2) return

            if (column === 0) this.dateColumnYear = this.date_range[0][value].value
            if (column === 1) this.dateColumnMonth = this.date_range[1][value].value

            const year = this.dateColumnYear || this.date[0],
                month = this.dateColumnMonth || this.date[1]
                
            this.setDateRangeDays(year, month)
        },
        
        dateChange(e) {
            const [year_i, month_i, day_i] = e.detail.value
            const [years, months, days] = this.date_range
            this.date = [years[year_i].value, months[month_i].value, days[day_i].value]
        },

        async init() {
            if (this.id) await this.getEventDetails()
            uni.hideLoading()
        },

        async getEventDetails() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.game_center.redHistoryEvents/event_details',
                data: {
                    id: this.id
                }
            })

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '事件获取失败', {
                success: () => uni.navigateBack()
            })

            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace res.data.event_details */
            const details = res?.data?.event_details
            if (!details) return this.$uni.showModal('事件获取失败', {success: () => uni.navigateBack()})

            this.title = details.title
            this.pic = details.pic
            this.memo = details.memo || ''

            let [year, month, day = 0] = details.date_time.split('-')
            year = Number(year)
            month = Number(month)
            if (day) day = Number(day)
            this.date = [year, month, day]
            this.setDateRangeDays(year, month, day)
        },

       
        changeImage() {
            this.$uni.navigateTo(`/pages/other/image_upload_or_select?active_id=${this.active_id}`, {
                events: {
                    newImg: src => {
                        this.pic = src
                    }
                }
            })
        },

        previewImage() {
            this.$uni.previewImage(this.pic)
        },

        checkForm() {
            const {title} = this
            if (!title) return this.$uni.showToast('请输入历史事件名称')

            this.save()
        },

        getDateData() {
            let [year, month, day] = this.date
            month = month < 10 ? `0${month}` : month
            if (day === 0) return `${year}-${month}`
            day = day < 10 ? `0${day}` : day
            return `${year}-${month}-${day}`
        },

        async save() {
            const data = {
                active_id: this.active_id,
                title: this.title,
                date_time: this.getDateData(),
                pic: this.pic,
                memo: this.memo
            }
            if (this.id) data.id = this.id

            this.$uni.showLoading('保存中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.game_center.redHistoryEvents/create_history_event',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '保存失败')

            this.saveSuccess(data)
        },

        saveSuccess(data) {
            this.$uni.showToast('保存成功', 'success')
            if (this.id) {
                this.getOpenerEventChannel()?.emit?.('updateEventItem', data)
            } else {
                this.getOpenerEventChannel()?.emit?.('refreshEventList')
            }
            this.$uni.navigateBack(1, {delay: 1000})
        }
    }
}
</script>

<style lang="scss">
.page {
    padding-bottom: 100px;
}

.uni-forms {
    padding: 10px;

    .uni-forms-picker {
        font-size: 14px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        height: 35px;
        line-height: 35px;
        padding-left: 10px;

        .picker-body {
            width: 100%;
        }

        .picker-right-icon {
            width: 30px;
        }

        .picker-value {
            color: #333;
        }

        .picker-placeholder {
            color: #bbbbbb;
            font-size: 12px;
        }
    }

    .second-label {
        position: relative;
        top: -8px;
    }
}

.image-container, .upload-image {
    width: 150px;
    height: 150px;
    border-radius: 10px;
    overflow: hidden;
}

.image-container {
    position: relative;

    .image {
        width: 100%;
        height: 100%;
    }

    .clear-image {
        position: absolute;
        top: 5px;
        right: 5px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #00000093;
    }
}

.save-container {
    position: fixed;
    z-index: 999;
    left: 0;
    bottom: 0;
    padding: 15px;
    width: 100%;
    box-sizing: border-box;

    .save-button {
        width: 250px;
        line-height: 44px;
        border-radius: 22px;
    }
}

/* #ifdef H5 */
@media screen and (max-width: 500px) {
    .save-container {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }
}

/* #endif */

</style>