<template>
    <view class="page bg-background">
        <view v-if="total" class="flex-kai bg-white">
            <view class="font14 color-sub p10">共{{ total }}个事件</view>
            <view v-if="!select" class="font14 color-light-primary p10" @click="toAddOrEdit(null)">
                <text>添加事件</text>
                <uni-icons type="forward" size="14" color="#5cadff"/>
            </view>
        </view>

        <view class="event-list">
            <view class="event-item bg-white p10" v-for="item in event_list" :key="item.id">
                <view class="flex-row">
                    <view class="flex-all-center pr10">
                        <radio :checked="item.checked" @click="itemChecked(item)"/>
                    </view>
                    <view v-if="item.pic" class="event-image pr10">
                        <image class="image" :src="item.pic" mode="aspectFill"/>
                    </view>
                    <view class="event-info">
                        <view class="font18 color-title pb5">
                            <text>{{ item.title }}</text>
                        </view>
                        
                        <view class="font16 color-title pb5">{{ item.date }}</view>
                        
                        <view v-if="item.memo" class="color-sub ellipsis--l2 font14">
                            {{ item.memo }}
                        </view>
                        
                    </view>
                </view>

                <view v-if="!select" class="tool-bar clear clearfix">
                    <view class="fr flex-row">
                        <view class="pl10 pr10" @click="toAddOrEdit(item.id)">
                            <text class="iconfont icon-edit font14 color-sub"></text>
                            <text class="font14 color-sub">修改</text>
                        </view>
                        <view class="pl10 pr10" @click="deleteEvent(item)">
                            <text class="iconfont icon-delete font14 color-sub"></text>
                            <text class="font14 color-sub">删除</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <view v-if="loading" class="text-center" :style="{paddingTop: current_page === 1 ? '30vh' : '0'}">
            <load-ani/>
        </view>

        <view v-if="!loading && !event_list.length" class="text-center" style="padding-top: 20vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无事件</view>
            <view class="add-event-button bg-primary color-white text-center"
                  hover-class="navigator-hover" @click="toAddOrEdit(null)">添加事件
            </view>
        </view>
        
        <view v-if="event_list.length" class="bottom-bar flex-kai bg-white">
            <view @click="checkAllChange">
                <radio :checked="checkAll"/>
                <text class="color-sub">全选</text>
            </view>
            <view class="flex-row">
                <view v-if="select" class="bottom-bar-button confirm-button" @click="selectConfirm">确定</view>
                <view v-else class="bottom-bar-button" @click="deleteEventsConfirm">删除</view>
            </view>
        </view>
    </view>
</template>

<script>

export default {
    data() {
        return {
            loading: true,
            event_list: [],
            current_page: 1,
            is_last_page: false,
            total: 0,
            select: false,
            checkAll: false
        }
    },

    onLoad(params) {
        this.paramsHandle(params)
        this.$login.uniLogin(() => {
            this.init()
            
        })
    },

    methods: {
        paramsHandle(params) {
            this.active_id = params.active_id
            if (params.select) this.select = true
        },
        
        async init() {
            this.chooseIds = []
            if (this.select) await this.getChooseIds()
            await this.getEventList()
        },


        async getChooseIds() {
            return new Promise(resolve => {
                this.getOpenerEventChannel().once('haveIds', ids => {
                    this.chooseIds = ids
                    resolve()
                })
            })
        },

        async refreshEventList() {
            this.current_page = 1
            await this.getEventList()
        },

        async getEventList() {
            if (this.current_page === 1) {
                this.event_list = []
                this.is_last_page = false
                this.total = 0
            }

            const data = {
                active_id: this.active_id,
                page: this.current_page,
                perpage: 100
            }

            this.loading = true
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.game_center.redHistoryEvents/events_list',
                data
            })
            this.loading = false

            const res_data = res?.data?.list
            if (!res_data) {
                this.is_last_page = true
                return
            }

            const event_list = this.eventListInit(res_data.data || [])
            this.event_list = this.event_list.concat(event_list)
            this.is_last_page = res_data.is_lastpage
            this.total = res_data.total
            
            this.isCheckAll()
        },

        eventListInit(list) {
            /*const delete_keys = ['active_id', 'conf_json', 'create_time', 'update_time']
            list.forEach(v => {
                delete_keys.forEach(key => v.hasOwnProperty(key) && delete v[key])
            })*/

            return list.map(item => ({
                id: item.id,
                title: item.title,
                date_time: item.date_time,
                date: this.getDate(item.date_time),
                pic: item.pic,
                memo: item.memo,
                checked: this.chooseIds.includes(item.id)
            }))
        },
        
        getDate(date_time) {
            const [year, month, day] = date_time.split('-')
            let date = `${year}年${Number(month)}月`
            if (day) date += `${Number(day)}日`
            return date
        },


        toAddOrEdit(id) {
            let url = `./add?active_id=${this.active_id}`
            if (id) url += `&id=${id}`

            this.$uni.navigateTo(url, {
                events: {
                    refreshEventList: () => this.refreshEventList(),
                    updateEventItem: data => this.updateEventItem(data)
                }
            })
        },

        updateEventItem(data) {
            const item = this.event_list.find(item => item.id === data.id)
            if (!item) return
            const copyKeys = ['title', 'pic', 'memo', 'date_time']
            item.date = this.getDate(data.date_time)
            copyKeys.forEach(key => item[key] = data[key])
        },

        selectConfirm() {
            this.getOpenerEventChannel().emit('selectEvents', this.event_list.filter(item => item.checked))
            this.$uni.navigateBack()
        },

        itemChecked(item) {
            item.checked = !item.checked
            this.isCheckAll()
        },

        isCheckAll() {
            this.$nextTick(() => {
                this.checkAll = this.event_list.every(item => item.checked)
            })
        },

        checkAllChange() {
            this.checkAll = !this.checkAll
            this.event_list.forEach(item => item.checked = this.checkAll)
        },

        async deleteEvent(item) {
            const modal = await this.$uni.showModal(`确定删除【${item.title}】?`, {showCancel: true})
            if (!modal.confirm) return

            await this.deleteEvents(item.id)
            await this.refreshEventList()
        },
        
        async deleteEventsConfirm() {
            const id_list = this.event_list.filter(item => item.checked).map(item => item.id)
            if (!id_list.length) return this.$uni.showToast('请选择需要删除的事件')
            
            const modal = await this.$uni.showModal(`确定删除选中的${id_list.length}个事件?`, {showCancel: true})
            if (!modal.confirm) return
            
            await this.deleteEvents(id_list.join(','))
            await this.refreshEventList()
        },
        
        async deleteEvents(ids) {
            this.$uni.showLoading('删除中...')
            const res = await this.xwy_api.deleteRecords(106, ids)
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '删除失败')
            this.$uni.showToast('已删除')
        }
    },

    onReachBottom() {
        if (this.is_last_page || this.loading) return
        this.current_page++
        this.getEventList()
    },
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    padding-bottom: 100px;
    box-sizing: border-box;
}

.event-list {
    padding-top: 1px;

    .event-item {
        margin: 20px 10px;
        border-radius: 5px;

        .event-image {
            .image {
                $size: 90px;
                width: $size;
                min-width: $size;
                height: $size;
                border-radius: 5px;
            }
        }

        .tool-bar {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #eee;

            .bg-go {
                background-color: #f6b51b;
            }

            .bg-complete {
                background-color: #539d8c;
            }

            .to-event-button {
                width: 90px;
                line-height: 36px;
                border-radius: 18px;
            }
        }
    }
}

.add-event-button {
    margin: 30px auto;
    width: 250px;
    line-height: 44px;
    border-radius: 22px;
}

.bottom-bar {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    box-sizing: border-box;
    padding: 10px;
    border-top: 1px solid #eee;
    
    .bottom-bar-button {
        color: #80848f;
        line-height: 30px;
        width: 60px;
        text-align: center;
        border-radius: 16px;
        font-size: 14px;
        margin-left: 10px;
        border: 1px solid #eee;
    }
    
    .confirm-button {
        color: #5cadff;
        border: 1px solid #5cadff;
    }
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .bottom-bar {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }
}
/* #endif */

</style>