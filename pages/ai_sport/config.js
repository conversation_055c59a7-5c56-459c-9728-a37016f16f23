export default {
    getDefaultIconsSet(rank_set = null) {
        const options = [
            {name: 'set', title: '活动设置'},
            {name: 'user_info', title: '报名信息'},
            {name: 'active_rule', title: '活动说明'},
            {name: 'ranking_list', title: '排行榜'},
            {name: 'record_list', title: '运动记录'},
            {name: 'games_center', title: '游戏中心'},
            {name: 'user_center', title: '个人中心'},
            {name: 'share', title: '转发分享'},
            {name: 'open_more', title: '展开更多'},
            {name: 'close_more', title: '关闭更多'}
        ]
        if (!rank_set?.games_center) {
            const index = options.findIndex(item => item.name === 'games_center')
            if (index !== -1) options.splice(index, 1)
        }
        
        return options.map(item => {
            return {
                ...item,
                type: 'system',
                image: '',
                hide: 0
            }
        })
    },
    
    getActiveIconsSet(icons_set, rank_set = null) {
        // 如果有新需求，要添加新icon的话，判断所有icon配置在保存的设置里面有没有，没有就添加
        this.getDefaultIconsSet(rank_set).forEach(icons => {
            if (!icons_set.some(item => item.name === icons.name)) {
                // 不能使用push，因为最后两个“展开、收起”必须是在最后，所以在头部添加
                icons_set.unshift(icons)
            }
        })
        return icons_set
    },
    
    
    getSupportedSportTypes() {
        return [
            {types: 1, sportKey: 'Rope-Skipping'}, // 跳绳
            {types: 2, sportKey: 'Squat'},         // 深蹲
            {types: 3, sportKey: 'Push-Up'},       // 俯卧撑
            // {types: 4, sportKey: ''},           // 引体向上
            {types: 5, sportKey: 'Plank'},         // 平板支撑
            {types: 6, sportKey: 'Sit-Up'},        // 仰卧起坐
            // {types: 7, sportKey: 'Hand-Lift'},  // 开合跳
            {types: 7, sportKey: 'Jumping-Jack'},  // 开合跳
            {types: 8, sportKey: 'High-Knee'},     // 高抬腿
            // {types: 9, sportKey: ''},           // 扩胸
            // {types: 10, sportKey: ''},          // 菱形伸展
        ]
    }
}