/*
 * 各种工具方法
 *       
 * @Alphaair
 * 20190519 create.
 * 20190917 修正对象拷贝时Date对象复制成Object的错误。
 * 20190917 cloneArray拷贝时Date对象复制成Object的错误。
 * 20200229 doPipeTasks更名为executePipes，新增invoke方法。
 * 20200308 时间戳换成秒。
 * 20210406 增加guid()方法。
 * 20210413 修复cloneArray()错误。
 * 20210415 增加isEmptyArray()工具方法。
 * 20210421 增加isNone工具方法。
 * 20210505 增加trimEndRegion方法。
 * 20220611 修复formatDate带非GMT8时区时错误的问题。
 **/

/**
 * 复制数组
 * 
 * @param {Array} array 要复制克隆的数组
 * @returns {Array} 复制好的数组
 */
function cloneArray(array) {
	if (array instanceof Array === false)
		return array;

	let dest = array.map(item => {
		if (item instanceof Array)
			return cloneArray(item);
		else if (item instanceof Date)
			return item;
		else if (typeof item === 'object')
			return clone(item);
		else
			return item;
	});

	return dest;
}

/**
 * 深拷贝对象
 * @param {Object,Array} 要复制的源对象 
 * @param {Object} 合并的目前对象，为空是新建对象
 * */
function clone(source, dest) {

	if (source instanceof Array)
		return cloneArray(source);

	if (!dest)
		dest = {};

	if (!source || typeof source !== 'object')
		return null;

	let keys = Object.keys(source);
	keys.forEach(key => {
		if (source[key] instanceof Array)
			dest[key] = cloneArray(source[key]);
		else if (source[key] instanceof Date)
			dest[key] = source[key];
		else if (typeof source[key] === 'object')
			dest[key] = clone(source[key]);
		else
			dest[key] = source[key];
	});

	return dest;
}

/**
 * 安全方式调用fn
 * 
 * @param {Function} fn 要调用的方法
 * @returns 如果fn存在则返回其引用，否则返回一个空函数
 */
function invoke(fn) {
	if (fn instanceof Function)
		return fn;

	return function() {};
}

/**
 * 判断obj是否为一个null、undefind、''的空对象
 * 而不是以!obj形式判断，以增加安全性。
 * 
 * @param {Object} obj 接受判断的对象
 */
function isNone(obj) {
	if (obj === null || obj === undefined || obj === '')
		return true;

	return false;
}

/**
 * 判断arr是否为null或undefind或空数组
 * 
 * @param {Object} arr 接受判断的数组
 * @return {Boolean} true为空（null、undefind)，反之为false
 */
function isEmptyArray(arr) {
	if (arr instanceof Array === false)
		return true;

	if (arr.length < 1)
		return true;

	return false;
}

module.exports = {
	/**
	 * 深拷贝对象
	 * 
	 * @param {Object} source 拷贝的源对象
	 * @param {Object} desc 目标对象,为空时完全复制一个source对象
	 * @returns {Object} 新的对象
	 */
	clone,
	/**
	 * 获取当前时间戳
	 * 
	 * @returns {Number} 当前时间戳
	 * */
	timestamp() {
		let stamp = (new Date()).getTime() / 1000;
		stamp = parseInt(stamp);

		return stamp;
	},
	invoke,
	/**
	 * 判断obj是否为一个null、undefind、''的空对象
	 * 而不是以!obj形式判断，以增加安全性。
	 * 
	 * @param {Object} obj 接受判断的对象
	 */
	isNone,
	/**
	 * 判断arr是否为null或undefind或空数组
	 * 
	 * @param {Object} arr 接受判断的数组
	 * @return {Boolean} true为空（null、undefind)，反之为false
	 */
	isEmptyArray
};
