<template>
    <view class="page">
        <view v-if="success" class="save-success bg-white">
            <view class="icon text-center">
                <uni-icons type="checkbox-filled" size="100" color="#19be6b"/>
                <view class="color-content font16">活动{{ form_data.active_id ? '修改' : '创建' }}成功！</view>
            </view>
            <view class="flex-all-center">
                <button class="bg-success color-white" style="width: 200px;" @click="lookMyActivityList">
                    查看我的活动
                </button>
            </view>
        </view>

        <view class="type-list flex-row bg-white" :style="{'fontSize': type_bar_font_size}">
            <view
                class="type-item color-content"
                :class="{'active-type': item.id === type_id}"
                v-for="item in type_list"
                :key="item.id"
                :style="{width: 'calc(100% / ' + type_list.length + ')'}"
                @click="typeIdChange(item.id)"
            >
                {{ item.title }}
            </view>
        </view>

        <add-activity-tips ref="add_activity_tips"/>

        <view class="form">
            <template v-if="type_id === 1">
                <view class="form-item">
                    <view class="top color-content">
                        <text>活动名称</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="text" v-model="form_data.name" placeholder="请输入活动名称"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">主办方</view>
                    <view class="bottom font16">
                        <input class="input" type="text" v-model="form_data.organizer"
                               placeholder="请输入主办方单位名称"/>
                    </view>
                </view>

                <template v-if="!form_data.active_id">
                    <view class="form-item">
                        <view class="top color-content">
                            <text>手机号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="number" v-model="form_data.mobile"
                                   placeholder="请输入真实手机号"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <text>微信号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="text" v-model="form_data.wechat_num"
                                   placeholder="请输入真实微信号"/>
                        </view>
                    </view>
                </template>

                <view class="form-item">
                    <view class="top color-content">活动开始时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.begin_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">活动结束时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.end_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view v-if="!conf.active.news.news_id" class="form-item">
                    <view class="top color-content">活动说明</view>
                    <view class="bottom font16">
                        <textarea class="textarea" maxlength="2000" auto-height="true"
                                  v-model="form_data.content" placeholder="请输入活动说明"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>活动说明文章</view>
                        <view class="font12 color-sub">
                            绑定文章后，活动说明将会显示文章内容，不会显示上面填写的活动说明
                        </view>
                    </view>
                    <view class="bottom font16">
                        <view class="flex-kai" @click="toSelNews">
                            <view class="view">
                                <view v-if="conf.active.news.news_id">
                                    {{ conf.active.news.news_title || conf.active.news.news_id }}
                                </view>
                                <view v-else class="color-sub">选择文章</view>
                            </view>
                            <view class="flex-all-center">
                                <view v-if="conf.active.news.news_id" class="color-sub font12"
                                      style="width: 30px;" @click.stop="deleteNews">
                                    解绑
                                </view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">活动说明是否显示活动规则</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker class="view" :range="['显示', '不显示']"
                                    :value="conf.active.activity_rules_hide"
                                    @change="conf.active.activity_rules_hide = Number($event.detail.value)">
                                {{ conf.active.activity_rules_hide ? '不显示' : '显示' }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>
            </template>

            <template v-if="type_id === 3">
                <view class="form-item">
                    <view class="top color-content">活动参与方式</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                :range="form_options.enter_types_list"
                                range-key="title"
                                :value="form_options.enter_types_list.findIndex(v => v.value === conf.active.enter_types)"
                                @change="conf.active.enter_types = form_options.enter_types_list[$event.detail.value].value"
                            >
                                {{ form_options.enter_types_list.find(v => v.value === conf.active.enter_types).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="conf.active.enter_types === 2" class="form-item">
                    <view class="top color-content">
                        <text>活动密码</text>
                        <text v-if="!have_password" class="color-error font16"> *</text>
                        <text class="color-sub font12 pl5">(3-20位)</text>
                    </view>
                    <view class="bottom font16">

                        <input class="input" v-model="conf.active.password" maxlength="20"
                               :placeholder="have_password ? '已设置密码，无需修改密码不用填写' : '请输入活动密码'"/>
                    </view>
                </view>


                <view v-if="rank_set && rank_set.team_group_open" class="form-item">
                    <view class="top color-content">报名时队伍是否必选</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker class="view" mode="selector" :range="['非必选', '必选']"
                                    :value="conf.active.team_required"
                                    @change="conf.active.team_required = Number($event.detail.value)">
                                {{ conf.active.team_required === 1 ? '' : '非' }}必选
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>


                <must-submit-set :must-submit.sync="conf.must_submit"/>

                <view class="form-item">
                    <view class="top color-content">活动城市限制功能</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                :range="ip_set_options"
                                range-key="title"
                                :value="ip_set_options.findIndex(v => v.value === conf.active.ip_set.open)"
                                @change="conf.active.ip_set.open = ip_set_options[$event.detail.value].value"
                            >
                                {{ ip_set_options.find(v => v.value === conf.active.ip_set.open).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="conf.active.ip_set.open" class="form-item">
                    <view class="top color-content">
                        <text>允许参加活动的城市</text>
                        <text class="color-sub font12 pl5">(如: 深圳市)</text>
                    </view>
                    <view class="bottom font16">
                        <view class="flex-row ptm5" v-for="(item, index) in conf.active.ip_set.ip_city"
                              :key="index">
                            <uni-easyinput :value="item" @input="ipListItemChange($event, index)"/>
                            <view class="font14 color-error pl5" style="line-height: 36px;"
                                  @click="conf.active.ip_set.ip_city.splice(index, 1)">删除
                            </view>
                        </view>

                        <view class="flex-all-center">
                            <view class="font14 color-primary ptm5"
                                  @click="conf.active.ip_set.ip_city.push('')">添加城市
                            </view>
                        </view>
                    </view>
                </view>
            </template>

            <template v-if="type_id === 8">
                <view class="p10 font14 color-sub">
                    <view>提示：</view>
                    <view v-for="(item, index) in sportSetTips" :key="index">
                        {{ index + 1 }}、{{ item }}
                    </view>
                </view>

                <view v-if="rewardModeSwitchSetOpen" class="form-item">
                    <view class="top color-content">奖励方式</view>
                    <view class="bottom font16 flex-kai">

                        <picker class="view" :range="rewardModeOptions" range-key="title"
                                :value="rewardModePickerData.value" @change="aiSportActivityRewardModeChange">
                            {{ rewardModePickerData.text }}
                        </picker>
                        <view class="flex-all-center">
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">{{ integralOrWaterUnit }}单位</view>
                    <view class="bottom font16">
                        <input v-if="conf.active.ai_sport_activity_reward_mode === 'integral'" class="input"
                               v-model="conf.active.integral.unit" placeholder="不填默认为“积分”"/>
                        <input v-else class="input" v-model="conf.active.energy_unit"
                               placeholder="不填默认为“能量”"/>
                    </view>
                </view>

                <view v-if="conf.active.ai_sport_activity_reward_mode === 'integral'" class="form-item">
                    <view class="top color-content">
                        <view>每日所有运动可获得的{{ integralUnit }}上限</view>
                        <view class="color-sub font12">
                            用户每日运动获得的{{ integralUnit }}达到上限后, 继续运动不获得积分。
                        </view>
                    </view>
                    <view class="bottom font16">

                        <input class="input" type="digit" v-model="conf.active.daily_ai_sports_max_integral"
                               maxlength="10" :placeholder="`请输入${integralUnit}上限`"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>
                            <text>每日最大运动次数</text>
                            <text class="pl5 color-sub font14">(全局)</text>
                        </view>
                        <view class="color-sub font12">
                            用户每日运动次数达到设置次数时，无法继续参与运动。不限次数不设置即可。
                        </view>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="conf.active.submit_times"
                               placeholder="请输入运动次数"/>
                    </view>
                </view>


                <view v-if="conf.active.ai_sport_activity_reward_mode === 'water'" class="form-item">
                    <view class="top color-content">
                        <view>
                            <text>每日获得{{ waterUnit }}上限</text>
                            <text class="pl5 color-sub font14">(全局)</text>
                        </view>
                        <view class="color-sub font12">
                            用户每日获得{{ waterUnit }}达到设置数后，当天将不再增加{{ waterUnit }}。不限制不设置即可。
                        </view>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="conf.active.max_daily_energy"
                               placeholder="请输入上限数"/>
                    </view>
                </view>


                <view class="form-item">
                    <view class="top color-content">
                        <view>
                            <text>单次运动时间限制</text>
                            <text class="color-sub font12 pl5">(全局，单位: 秒)</text>
                        </view>
                        <view class="color-sub font12">
                            单次运动达到设置时间时，运动暂停，无法继续进行本次运动。不限时间不设置即可。
                        </view>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="conf.active.per_seconds"
                               placeholder="请输入运动时间"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top flex-kai">
                        <view class="color-content">运动类型设置</view>
                        <view v-if="conf.AI_motion.motion_list.length" class="color-sub font14"
                              @click="clearAiMotionList">清空设置
                        </view>
                    </view>
                    <view class="bottom font16">
                        <view class="ai-sport-list">
                            <view class="ai-sport-item flex-row"
                                  v-for="(item, index) in conf.AI_motion.motion_list" :key="item.types">
                                <view class="item-controls flex-row">
                                    <view class="p5" @click="editAiSportItem(index)">
                                        <text class="iconfont icon-edit color-sub font18"></text>
                                    </view>
                                    <view class="p5" @click="deleteAiSportItem(index)">
                                        <text class="iconfont icon-delete color-sub font18"></text>
                                    </view>
                                </view>
                                <view class="ai-sport-item-logo">
                                    <image v-if="item.logo" class="ai-sport-item-logo-image"
                                           :src="item.logo" mode="aspectFill"/>
                                    <view v-else class="ai-sport-item-logo-image flex-all-center">
                                        <uni-icons type="image" color="#bbbec4" size="60"/>
                                    </view>
                                </view>
                                <view class="item-info">
                                    <view class="flex-row">
                                        <view class="item-info-label">运动类型:</view>
                                        <view>{{ getAiSportTypesName(item.types) }}</view>
                                    </view>
                                    <!--<view class="flex-row">
                                        <view class="item-info-label">运动名称:</view>
                                        <view>
                                            {{ item.name || getAiSportTypesName(item.types) }}
                                        </view>
                                    </view>-->
                                    <view class="flex-row">
                                        <template
                                            v-if="conf.active.ai_sport_activity_reward_mode === 'integral'">
                                            <view class="item-info-label">积分奖励:</view>
                                            <view>{{ item.num }}{{ getAiSportTypesNumName(item.types) }}{{ item.name }}奖励{{ item.reward_value }}{{ integralUnit }}
                                            </view>
                                        </template>
                                        <template v-else>
                                            <view class="item-info-label">能量奖励:</view>
                                            <view>1{{ getAiSportTypesNumName(item.types) }}{{ item.name }}兑换{{ item.reward_value }}{{ waterUnit }}
                                            </view>
                                        </template>
                                    </view>
                                    <view class="flex-row">
                                        <view class="item-info-label">每日最大运动次数:</view>
                                        <template>
                                            <view v-if="Number(item.submit_times)">
                                                {{ item.submit_times }}次
                                            </view>
                                            <view v-else-if="Number(conf.active.submit_times)">
                                                <text>{{ conf.active.submit_times }}次</text>
                                                <text class="color-sub font12 pl5">(全局)</text>
                                            </view>
                                            <view v-else>无限制</view>
                                        </template>
                                    </view>
                                    <view class="flex-row">
                                        <view class="item-info-label">单次运动时间限制:</view>
                                        <template>
                                            <view v-if="Number(item.per_seconds)">
                                                {{ item.per_seconds }}秒
                                            </view>
                                            <view v-else-if="Number(conf.active.per_seconds)">
                                                <text>{{ conf.active.submit_times }}秒</text>
                                                <text class="color-sub font12 pl5">(全局)</text>
                                            </view>
                                            <view v-else>无限制</view>
                                        </template>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view v-if="conf.AI_motion.motion_list.length < all_ai_sport_type_count"
                              class="flex-all-center pb5">
                            <view class="p5 color-light-primary font14" @click="editAiSportItem(null)">
                                添加运动类型
                            </view>
                        </view>
                    </view>
                </view>

                <uni-popup ref="aiSportItemEditPopup">
                    <view class="bg-white ai-sport-item-edit">
                        <view class="text-center color-title font14 p10">AI运动设置</view>
                        <view class="flex-all-center">
                            <view class="ai-sport-item-logo" @click="changeImage('ai_sport_logo')">
                                <image v-if="edit_ai_sport_item_data.logo" class="ai-sport-item-logo-image"
                                       :src="edit_ai_sport_item_data.logo" mode="aspectFill"/>
                                <view v-else class="ai-sport-item-logo-image flex-all-center">
                                    <uni-icons type="image" color="#bbbec4" size="60"/>
                                </view>
                            </view>
                        </view>
                        <view class="flex-all-center pt5">
                            <view class="color-light-primary font14" @click="changeImage('ai_sport_logo')">
                                {{ edit_ai_sport_item_data.logo ? '修改' : '设置' }}运动logo图
                            </view>
                        </view>
                        <view class="font12 color-sub text-center">图片尺寸建议: 400*200</view>

                        <uni-forms label-position="top" label-width="200" border>
                            <uni-forms-item label="运动类型" required>
                                <view class="forms-picker flex-kai" @click="changeAiSportTypes">
                                    <view style="width: 100%;">
                                        <text v-if="edit_ai_sport_item_data.types" style="color: #333;">
                                            {{ getAiSportTypesName(edit_ai_sport_item_data.types) }}
                                        </text>
                                        <text v-else class="color-disabled">请选择运动类型</text>
                                    </view>
                                    <view>
                                        <uni-icons type="forward" size="16" color="#dddee1"/>
                                    </view>
                                </view>
                            </uni-forms-item>

                            <!--<uni-forms-item label="运动名称">
                                <uni-easyinput v-model="edit_ai_sport_item_data.name" maxlength="20"/>
                            </uni-forms-item>-->

                            <uni-forms-item label="奖励设置" required>
                                <view v-if="edit_ai_sport_item_data.types" class="flex-row">
                                    <template>
                                        <view v-if="conf.active.ai_sport_activity_reward_mode === 'integral'"
                                              class="input-container pr5">
                                            <uni-easyinput v-model="edit_ai_sport_item_data.num"
                                                           type="number" maxlength="8"/>
                                        </view>
                                        <view v-else>1</view>
                                    </template>
                                    <view class="input-word">
                                        {{ getAiSportTypesNumName(edit_ai_sport_item_data.types) }}{{ edit_ai_sport_item_data.name }}奖励
                                    </view>
                                    <view class="input-container plr5">
                                        <uni-easyinput v-model="edit_ai_sport_item_data.reward_value"
                                                       type="number" maxlength="8"/>
                                    </view>
                                    <view class="input-word">{{ integralOrWaterUnit }}</view>

                                </view>
                                <view v-else class="color-sub font14">请选择运动类型</view>
                            </uni-forms-item>

                            <uni-forms-item label="每日最大运动次数">
                                <view v-if="edit_ai_sport_item_data.types" class="flex-row">
                                    <view class="input-container" style="width: 100px;">
                                        <uni-easyinput v-model="edit_ai_sport_item_data.submit_times"
                                                       type="number" maxlength="8"/>
                                    </view>
                                    <view class="input-word pl5">次</view>
                                </view>
                                <view v-else class="color-sub font14">请选择运动类型</view>
                            </uni-forms-item>

                            <uni-forms-item label="单次运动时间限制">
                                <view v-if="edit_ai_sport_item_data.types" class="flex-row">
                                    <view class="input-container" style="width: 100px;">
                                        <uni-easyinput v-model="edit_ai_sport_item_data.per_seconds"
                                                       type="number" maxlength="8"/>
                                    </view>
                                    <view class="input-word pl5">秒</view>
                                </view>
                                <view v-else class="color-sub font14">请选择运动类型</view>
                            </uni-forms-item>
                        </uni-forms>

                        <view class="ai-sport-item-edit-buttons flex-all-center font14">
                            <view class="bg-background color-sub" hover-class="navigator-hover"
                                  @click="$refs.aiSportItemEditPopup.close()">取消
                            </view>
                            <view class="bg-light-primary color-white" hover-class="navigator-hover"
                                  @click="aiSportItemEditConfirm">确定
                            </view>
                        </view>
                    </view>
                </uni-popup>

                <uni-popup ref="changeAiSportTypesPopup">
                    <view class="ai-sport-types-change bg-white">
                        <view class="p10 text-center color-title">选择运动类型</view>
                        <view class="ai-sport-type-list flex-row flex-wrap">
                            <view class="ai-sport-type-item flex-all-center"
                                  :class="{selected: item.selected}"
                                  v-for="item in ai_sport_types" :key="item.types"
                                  @click="selectAiSportTypes(item)">
                                <image v-if="item.logo" class="ai-sport-type-item-logo" :src="item.logo"/>
                                <view v-else class="ai-sport-type-item-no-logo">{{ item.name }}</view>
                            </view>
                        </view>
                    </view>
                    <view class="flex-all-center pt5" @click="$refs.changeAiSportTypesPopup.close()">
                        <uni-icons type="close" color="#ffffff" size="28"/>
                    </view>
                </uni-popup>


                <view class="form-item">
                    <view class="top color-content">微信运动步数兑换{{ integralOrWaterUnit }}</view>
                    <view class="bottom font16 flex-kai">

                        <picker class="view" :range="['关闭', '开启']"
                                :value="conf.active.ai_sport_extend_types.step.open"
                                @change="conf.active.ai_sport_extend_types.step.open = Number($event.detail.value)">
                            {{ ['关闭', '开启'][conf.active.ai_sport_extend_types.step.open] }}
                        </picker>
                        <view class="flex-all-center">
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </view>

                <view v-show="conf.active.ai_sport_extend_types.step.open">
                    <view class="ai-sport-list">
                        <view class="ai-sport-item flex-row">
                            <view class="item-controls flex-row">
                                <view class="p5" @click="stepSetOpen">
                                    <text class="iconfont icon-edit color-sub font18"></text>
                                </view>
                            </view>
                            <view class="ai-sport-item-logo">

                                <image v-if="conf.active.ai_sport_extend_types.step.logo"
                                       class="ai-sport-item-logo-image" mode="aspectFill"
                                       :src="conf.active.ai_sport_extend_types.step.logo"/>
                                <view v-else class="ai-sport-item-logo-image flex-all-center">
                                    <uni-icons type="image" color="#bbbec4" size="60"/>
                                </view>
                            </view>
                            <view class="item-info">
                                <view class="flex-row">
                                    <view class="item-info-label">{{ integralOrWaterUnit }}兑换:</view>
                                    <template>
                                        <view v-if="exchange_rate">
                                            {{ exchange_rate }}步兑换1{{ integralOrWaterUnit }}
                                        </view>
                                        <view v-else>未设置</view>
                                    </template>
                                </view>
                                <view class="flex-row">
                                    <view class="item-info-label">最低兑换步数:</view>
                                    <template>
                                        <view v-if="conf.active.min_num">{{ conf.active.min_num }}步</view>
                                        <view v-else>不限制</view>
                                    </template>
                                </view>
                                <view class="flex-row">
                                    <view class="item-info-label">最高兑换步数:</view>
                                    <template>
                                        <view v-if="conf.active.min_num">{{ conf.active.max_num }}步</view>
                                        <view v-else>不限制</view>
                                    </template>
                                </view>
                            </view>
                        </view>
                    </view>

                    <uni-popup ref="stepSetPopup">
                        <view class="bg-white ai-sport-item-edit">
                            <view class="text-center color-title font14 p10">微信运动步数兑换设置</view>
                            <view class="flex-all-center">
                                <view class="ai-sport-item-logo" @click="changeImage('step_logo')">
                                    <image v-if="step_set_data.logo" class="ai-sport-item-logo-image"
                                           :src="step_set_data.logo" mode="aspectFill"/>
                                    <view v-else class="ai-sport-item-logo-image flex-all-center">
                                        <uni-icons type="image" color="#bbbec4" size="60"/>
                                    </view>
                                </view>
                            </view>
                            <view class="flex-all-center pt5">
                                <view class="color-light-primary font14" @click="changeImage('step_logo')">
                                    {{ step_set_data.logo ? '修改' : '设置' }}运动logo图
                                </view>
                            </view>
                            <view class="font12 color-sub text-center">图片尺寸建议: 400*200</view>

                            <uni-forms label-position="top" label-width="200" border>
                                <uni-forms-item label="运动名称">
                                    <uni-easyinput v-model="step_set_data.name" maxlength="20"/>
                                </uni-forms-item>

                                <uni-forms-item label="兑换比例" required>
                                    <view class="flex-row">
                                        <view class="input-container pr5" style="width: 150px;">
                                            <uni-easyinput v-model="step_set_data.exchange_rate"
                                                           type="digit" maxlength="8"/>
                                        </view>
                                        <view class="input-word">步兑换1{{ integralOrWaterUnit }}</view>
                                    </view>
                                </uni-forms-item>

                                <uni-forms-item label="最低兑换步数">
                                    <view class="flex-row">
                                        <view class="input-container" style="width: 150px;">
                                            <uni-easyinput v-model="step_set_data.min_num"
                                                           type="number" maxlength="8"
                                                           placeholder="不填或填0则不限制"/>
                                        </view>
                                        <view class="input-word pl5">步</view>
                                    </view>
                                </uni-forms-item>

                                <uni-forms-item label="最高兑换步数">
                                    <view class="flex-row">
                                        <view class="input-container" style="width: 150px;">
                                            <uni-easyinput v-model="step_set_data.max_num"
                                                           type="number" maxlength="8"
                                                           placeholder="不填或填0则不限制"/>
                                        </view>
                                        <view class="input-word pl5">步</view>
                                    </view>
                                </uni-forms-item>
                            </uni-forms>

                            <view class="ai-sport-item-edit-buttons flex-all-center font14">
                                <view class="bg-background color-sub" hover-class="navigator-hover"
                                      @click="$refs.stepSetPopup.close()">取消
                                </view>
                                <view class="bg-light-primary color-white" hover-class="navigator-hover"
                                      @click="stepSetConfirm">确定
                                </view>
                            </view>
                        </view>
                    </uni-popup>
                </view>
            </template>

            <template v-if="type_id === 6">
                <view class="form-item">
                    <view class="top color-content">活动右侧图标设置</view>
                    <view class="bottom">
                        <icons-set-list :list.sync="conf.active.active_icons_set"/>
                    </view>
                </view>

                <active-image-set :active-id="form_data.active_id" :rankSet="rank_set"
                                  :logo.sync="form_data.logo"
                                  :screen-pic.sync="conf.active.screen_pic"
                                  :top-rank-banner.sync="conf.active.top_rank_banner"/>

                <view class="form-item">
                    <view class="top color-content">运动完成后是否显示'分享到微信运动'按钮</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker class="view" mode="selector" :range="['显示', '不显示']"
                                    :value="conf.active.we_run_share_button_hide"
                                    @change="conf.active.we_run_share_button_hide = Number($event.detail.value)">
                                {{ conf.active.we_run_share_button_hide ? '不' : '' }}显示
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

               <active-share-set :active-id="form_data.active_id" :rank-set="rank_set"
                                  :qrcode-logo.sync="conf.active.qrcode_logo"
                                  :share-image.sync="conf.active.share_image"
                                  :share-title.sync="conf.active.share_title"/>
            </template>

            <template v-if="type_id === 7">
                <view class="form-item">
                    <view class="top color-content">每天最大参与游戏次数</view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="conf.active.game_center.daily_times"
                               placeholder="不限次数不填即可"/>
                    </view>
                </view>


                <view class="game-list">
                    <view class="game-item" v-for="item in conf.active.game_center.game_list"
                          :key="item.types">
                        <view class="form-item">
                            <view class="top color-content">{{ item.title }}</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker
                                        class="view"
                                        :range="[
                                            {title: '开启' + item.title, value: true},
                                            {title: '关闭' + item.title, value: false}
                                        ]"
                                        range-key="title"
                                        :value="item.open ? 0 : 1"
                                        @change="gameItemOpenChange(item, $event)"
                                    >
                                        {{ item.open ? '开启' : '关闭' }}{{ item.title }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <template v-if="item.open">
                            <view class="form-item">
                                <view class="top color-content">
                                    <view>
                                        <text>{{ item.title }}{{ waterUnit }}兑换规则
                                        </text>
                                        <text class="color-error font16"> *</text>
                                    </view>
                                    <view class="font14 color-sub">
                                        如游戏获得10分兑换1{{ waterUnit }},输入框填10
                                    </view>
                                </view>
                                <view class="bottom font16">
                                    <input class="input" type="number" v-model="item.exchange_num"
                                           placeholder="请输入大于等于1,小于等于1000000的整数"/>
                                </view>
                            </view>
                        </template>
                    </view>
                </view>
            </template>

        </view>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view class="login-btn color-white text-center font18 bg-primary"
                  :disabled="loading" @click="save">
                {{ form_data.active_id ? '保存' : '创建活动' }}
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import {pinyin} from 'pinyin-pro'
import config from '@/pages/ai_sport/config'
import iconsSetList from '@/pages/ai_sport/components/icons-set-list.vue'

const AI_SPORT_DEFAULT_DATA = {
    types: '',
    logo: '',
    name: '',
    title: '',
    per_seconds: '',
    submit_times: '',
    reward_value: '',
    num: ''
}

export default {
    components: {iconsSetList},
    data() {
        return {
            loading: true,
            success: false,
            type_list: [
                {title: '基本信息', id: 1},
                {title: '活动设置', id: 3},
                {title: '运动设置', id: 8},
                {title: '扩展设置', id: 6}
            ],
            type_bar_font_size: '16px',
            type_id: 1,
            form_data: {
                active_id: '',
                name: '',
                wechat_num: '',
                mobile: '',
                organizer: '',
                content: '',
                begin_time: new Date(this._utils.getDay(0, true, '/') + ' 00:00:00').getTime(),
                end_time: new Date(this._utils.getDay(30, true, '/') + ' 23:59:59').getTime(),
                logo: ''
            },
            form_options: {
                enter_types_list: [
                    {title: '自由报名参与活动', value: 1},
                    {title: '需要输入密码才能进入活动', value: 2},
                    {title: '报名需要审核通过才能参与活动', value: 3}
                ]
            },
            conf: {
                active: {
                    enter_types: 1,
                    ai_sport_activity_reward_mode: 'integral',
                    news: {
                        news_id: '',
                        news_title: ''
                    },
                    screen_pic: '',
                    top_rank_banner: [],
                    password: '',
                    energy_unit: '能量',
                    submit_times: '',
                    max_daily_energy: '',
                    per_seconds: '',
                    game_center: {
                        daily_times: '',
                        game_list: []
                    },
                    share_title: '',
                    share_image: '',
                    qrcode_logo: '',


                    min_num: '',        // 每天最低兑换步数限制
                    max_num: '',       // 每天最高兑换步数限制
                    ai_sport_extend_types: {
                        step: {
                            open: 0,     // 是否开启步数兑换能量 open为1才能把步数设置的数据存到接口
                            name: '微信运动步数',
                            logo: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/ai-sport/images/banner/run.jpg'
                        }
                    },
                    team_required: 0,
                    ip_set: {
                        open: 0,
                        ip_city: []
                    },

                    // 活动右侧图标设置
                    active_icons_set: config.getDefaultIconsSet(),

                    integral: {
                        unit: '积分',
                        exchange_step: ''
                    },

                    we_run_share_button_hide: 0,

                    daily_ai_sports_max_integral: '',
                    activity_rules_hide: 0
                },
                AI_motion: {
                    motion_list: []
                },
                must_submit: JSON.parse(JSON.stringify(this.xwy_config.defaultMustSubmit))
            },
            rank_set: null,
            have_password: false,
            top_rank_banner_max_count: 6,
            detail_icon_conf: [
                {
                    type: 0,
                    title: '文章详情'
                }
            ],
            ai_sport_type_list: [],
            all_ai_sport_type_count: 0,
            edit_ai_sport_item_data: {},
            ai_sport_types: [],

            step_set_data: {},

            ip_set_options: [
                {value: 0, title: '关闭, 所有城市用户均可参加'},
                {value: 1, title: '开启, 只允许特定城市用户参加'}
            ],

            // 步数兑换设置，需要ai_sport_extend_types.step.open为1 才能把设置数据存到接口
            exchange_rate: 1,     // ?步兑换1能量
            create_time: 0
        }
    },

    computed: {
        integralUnit() {
            return this.conf?.active?.integral?.unit || '积分'
        },

        waterUnit() {
            return this.conf?.active?.energy_unit || '能量'
        },

        integralOrWaterUnit() {
            return this.conf?.active?.ai_sport_activity_reward_mode === 'integral' ? this.integralUnit : this.waterUnit
        },

        rewardModeOptions() {
            return [
                {value: 'integral', title: '奖励积分'},
                {value: 'water', title: '奖励能量'}
            ]
        },

        rewardModePickerData() {
            let index = this.rewardModeOptions.findIndex(item => item.value === this.conf?.active?.ai_sport_activity_reward_mode)
            if (index === -1) index = 0

            return {
                value: index,
                text: this.rewardModeOptions[index].title
            }
        },

        sportSetTips() {
            const tips = [
                '某个运动类型设置了每日最大运动次数，用户当日该运动达到设置次数后，无法继续运动。',
                '设置了每日最大运动次数(全局)，用户当日所有类型运动次数累计达到设置次数后，无法继续运动。',
                `设置了每日最大运动获得${this.waterUnit}(全局)，用户当日所有类型运动获得的${this.waterUnit}达到设置数后，无法继续运动。`,
                '如某个运动类型设置了单次运动时间限制，运动暂停，无法继续进行本次运动。如运动类型没有设置单次运动时间限制，使用全局单次运动时间限制设置。如运动类型和全局都未设置单次运动时间限制，即不限运动时间。'
            ]
            if (this.conf?.active?.ai_sport_activity_reward_mode === 'integral') tips.splice(2, 1)
            return tips
        },

        // 2025-05-06 以后新创建的AI运动活动，默认为积分奖励模式且不可切换
        rewardModeSwitchSetOpen() {
            // 创建活动不能切换
            if (!this.form_data.active_id) return false

            // 没有活动创时间，可以切换
            if (!this.create_time) return true

            // 如果不是积分奖励模式，可切换
            if (this.conf.active.ai_sport_activity_reward_mode !== 'integral') return true

            const deadline = new Date('2025-05-06 00:00:00').getTime()
            const activityCreateTime = new Date(this.create_time).getTime()

            // 活动创建时间在2025-05-06之前，可切换
            return activityCreateTime < deadline
        }
    },

    onLoad(e) {
        if (e.id) {
            this.form_data.active_id = e.id
        } else {
            this.types = Number(e.types)
            this.$nextTick(() => this.$refs.add_activity_tips.open(this.types))
            this.form_data.logo = this.xwy_config.create_active_default_logo
        }

        this.$uni.setNavigationBarTitle(e.id ? '修改活动' : `创建${e.name || '活动'}`)
        this.$uni.showLoading()

        this.$login.uniLogin(err => {

            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }


            if (e.id) return this.getMotionList()

            const evn_version = app.globalData['evn_version']

            if (evn_version === 'develop') {
                this.form_data.mobile = 12345678910
                this.form_data.wechat_num = 12345678910
                this.form_data.organizer = '伟杰测试'
            }

            // 开发版和体验版不检查重复创建活动
            if (evn_version === 'develop' || evn_version === 'trial') return this.getMotionList()

            this.checkUserCanCreateActive()
        })
    },

    methods: {
        async checkUserCanCreateActive() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/check_user_can_create_active'
            })

            uni.hideLoading()
            this.loading = false
            if (res?.status !== 1) {
                return this.$uni.showModal(res?.info || '暂时不能创建活动', {success: () => uni.navigateBack()})
            }
            await this.getMotionList()
        },

        typeIdChange(id) {
            if (id === this.type_id) return
            if (id === 7 && this.conf.active.ai_sport_activity_reward_mode === 'integral') {
                return this.$uni.showToast('奖励积分模式不支持游戏中心设置', 'none', 3000)
            }

            this.type_id = id
        },


        async getMotionList() {
            this.ai_sport_type_list = await this.xwy_api.getAiSportTypeList()
            this.all_ai_sport_type_count = this.ai_sport_type_list.length

            if (this.form_data.active_id) return this.getDetail()

            this.setDefaultAiMotion()

            uni.hideLoading()
        },

        // 创建活动默认添加两个运动类型
        setDefaultAiMotion() {
            const [sport1, sport2] = this.ai_sport_type_list
            const sportList = [sport1, sport2]
            this.conf.AI_motion.motion_list = sportList.map(item => ({
                types: item.types,
                name: item.name,
                logo: item.logo,
                reward_value: 1,
                per_seconds: '',
                submit_times: '',
                title: '',
                num: 1
            }))
        },

        getDetail() {
            this.xwy_api.getActivityDetail(this.form_data.active_id, res => {
                const details = res.data.active_details
                if (!details) {
                    return this.$uni.showModal(res?.info || '活动获取失败', {success: () => uni.navigateBack()})
                }

                this.create_time = details.create_time

                this.detailInit(details)
            })
        },


        detailInit(data) {
            this.types = data.types
            this.form_data.name = data.name
            this.form_data.begin_time = data.begin_time * 1000
            this.form_data.end_time = data.end_time * 1000
            if (data.organizer) this.form_data.organizer = data.organizer
            if (data.logo) this.form_data.logo = data.logo
            if (data.content) this.form_data.content = data.content

            const conf = data.conf
            if (conf.must_submit?.length) this.conf.must_submit = conf.must_submit

            const active = conf.active

            const reward_mode = active.ai_sport_activity_reward_mode || 'water'
            const motion_list = conf.AI_motion?.motion_list
            if (motion_list?.length) {
                this.conf.AI_motion.motion_list = motion_list.map(item => ({
                    logo: item.logo,
                    name: item.name,
                    per_seconds: item.per_seconds,
                    submit_times: item.submit_times,
                    title: item.title,
                    types: item.types,
                    reward_value: reward_mode === 'integral' ? item.integral_reward.integral : item.water,
                    num: reward_mode === 'integral' ? item.integral_reward.num : 1
                }))
            }
            this.conf.active.ai_sport_activity_reward_mode = reward_mode

            this.conf.active.enter_types = active.enter_types || 1

            if (active.energy_unit) this.conf.active.energy_unit = active.energy_unit
            if (active.news) this.conf.active.news = active.news
            if (active.screen_pic) this.conf.active.screen_pic = active.screen_pic
            if (active.screen_pic_count_down) this.conf.active.screen_pic_count_down = active.screen_pic_count_down
            if (active.screen_pic_buttom_show === 0) this.conf.active.screen_pic_buttom_show = 0
            if (active.top_rank_banner) this.conf.active.top_rank_banner = active.top_rank_banner
            if (active.password) {
                this.old_password = active.password
                this.have_password = true
            }
            if (active.submit_times) this.conf.active.submit_times = active.submit_times
            if (active.max_daily_energy) this.conf.active.max_daily_energy = active.max_daily_energy
            if (active.per_seconds) this.conf.active.per_seconds = active.per_seconds
            if (data.rank_set) {
                const rank_set = data.rank_set

                this.rank_set = rank_set

                if (rank_set?.closed_AD === 1) this.detail_icon_conf.push({type: 1, title: '文章列表'})

                // 开启了游戏中心
                if (rank_set.game_center) {
                    this.type_list.push({id: 7, title: '游戏中心'})
                    this.type_bar_font_size = '14px'
                    if (active.game_center) this.conf.active.game_center = active.game_center
                    this.getSystemGameList(active.game_center)
                }
            }

            if (active.share_title) this.conf.active.share_title = active.share_title
            if (active.share_image) this.conf.active.share_image = active.share_image
            if (active.qrcode_logo) this.conf.active.qrcode_logo = active.qrcode_logo


            // 微信运动步数兑换
            if (active.exchange_rate && reward_mode === 'water') this.exchange_rate = active.exchange_rate
            if (active.integral?.exchange_step && reward_mode === 'integral') {
                this.exchange_rate = active.integral.exchange_step
            }
            if (active.min_num) this.conf.active.min_num = active.min_num
            if (active.max_num) this.conf.active.max_num = active.max_num
            if (active.ai_sport_extend_types) {
                this.conf.active.ai_sport_extend_types = active.ai_sport_extend_types
            }

            if (active.team_required) this.conf.active.team_required = active.team_required

            if (active.ip_set) this.conf.active.ip_set = active.ip_set

            // 右侧图标自定义设置获取
            if (active.active_icons_set?.length) {
                this.conf.active.active_icons_set = config.getActiveIconsSet(active.active_icons_set, data.rank_set)
            }

            if (active.integral) this.conf.active.integral = active.integral
            if (active.we_run_share_button_hide) {
                this.conf.active.we_run_share_button_hide = active.we_run_share_button_hide
            }
            if (active.daily_ai_sports_max_integral) {
                this.conf.active.daily_ai_sports_max_integral = active.daily_ai_sports_max_integral
            }
            if (active.activity_rules_hide) this.conf.active.activity_rules_hide = active.activity_rules_hide

            uni.hideLoading()
        },

        async aiSportActivityRewardModeChange(e) {
            const item = this.rewardModeOptions[e.detail.value]
            const value = item.value
            if (this.conf.active.ai_sport_activity_reward_mode === 'water' && value === 'integral' && this.rank_set?.game_center) {
                const res = await this.$uni.showModal(`切换为奖励${item.title}后,将不支持游戏中心。是否继续?`, {
                    showCancel: true
                })
                if (!res.confirm) return
            }
            this.conf.active.ai_sport_activity_reward_mode = value
        },


        stepSetOpen() {
            const {min_num = '', max_num = '', ai_sport_extend_types} = this.conf.active
            this.step_set_data = {
                exchange_rate: this.exchange_rate,
                min_num,
                max_num,
                logo: ai_sport_extend_types.step.logo || '',
                name: ai_sport_extend_types.step.name || '微信运动步数'
            }

            this.$nextTick(() => this.$refs.stepSetPopup.open())
        },

        stepSetConfirm() {
            const {exchange_rate, min_num, max_num, logo, name} = this.step_set_data
            const exchange_rate_num = Number(exchange_rate)
            if (isNaN(exchange_rate_num) || exchange_rate_num <= 0) {
                return this.$uni.showToast('运动步数兑换比例设置有误，请重新填写', 'none', 3000)
            }
            const min = Number(min_num)
            if (isNaN(min) || min < 0) {
                return this.$uni.showToast('最低兑换步数限制设置有误，请重新填写', 'none', 3000)
            }
            const max = Number(max_num)
            if (isNaN(max) || max < 0) {
                return this.$uni.showToast('最高兑换步数限制设置有误，请重新填写', 'none', 3000)
            }
            if (min > max) return this.$uni.showToast('最低兑换步数限制不能大于最高兑换步数限制', 'none', 3000)

            this.exchange_rate = exchange_rate_num
            this.conf.active.min_num = min
            this.conf.active.max_num = max
            this.conf.active.ai_sport_extend_types.step.logo = logo
            this.conf.active.ai_sport_extend_types.step.name = name

            this.$refs.stepSetPopup.close()
        },


        getAiSportTypesName(types) {
            return this.ai_sport_type_list.find(v => v?.types === types)?.name || types
        },

        getAiSportTypesNumName(types) {
            const isCounts = this.xwy_config.AiSportNeedCountsTypes(types)
            return isCounts ? '个' : '秒'
        },

        async clearAiMotionList() {
            const res = await this.$uni.showModal('确定清空AI运动设置?', {showCancel: true})
            if (!res.confirm) return
            this.conf.AI_motion.motion_list = []
            this.$nextTick(() => this.$uni.showToast('AI运动设置已清空'))
        },

        deleteAiSportItem(index) {
            this.conf.AI_motion.motion_list.splice(index, 1)
        },

        editAiSportItem(index) {
            this.ai_sport_change_index = index
            this.edit_ai_sport_item_data = index === null ?
                JSON.parse(JSON.stringify(AI_SPORT_DEFAULT_DATA)) :
                JSON.parse(JSON.stringify(this.conf.AI_motion.motion_list[index]))

            this.$refs.aiSportItemEditPopup.open()
        },

        aiSportItemEditConfirm() {
            const data = this.edit_ai_sport_item_data
            if (!data.types) return this.$uni.showToast('请选择运动类型')
            if (!data.logo) return this.$uni.showToast('请设置运动logo图')
            if (data.reward_value !== '') data.reward_value = Number(data.reward_value)
            if (isNaN(data.reward_value)) return this.$uni.showToast('请输入正确的奖励设置')
            if (!data.reward_value) return this.$uni.showToast('请输入奖励设置')

            if (this.conf.active.ai_sport_activity_reward_mode === 'integral') {
                const num = Math.floor(data.num)
                if (isNaN(num) || num <= 0) return this.$uni.showToast('请输入正确的运动数量')
                data.num = num
            }

            const index = this.ai_sport_change_index
            const motion_list = this.conf.AI_motion.motion_list
            index === null ? motion_list.push(data) : this.$set(motion_list, index, data)

            this.$refs.aiSportItemEditPopup.close()
        },

        changeAiSportTypes() {
            const ai_sport_types = JSON.parse(JSON.stringify(this.ai_sport_type_list))
            const motion_list = this.conf.AI_motion.motion_list

            // 如果是编辑的话，当前运动类型改为没有选择，不然选了其他运动类型后无法选回这个运动类型
            const index = this.ai_sport_change_index
            let current_types = motion_list[index]?.types || null

            ai_sport_types.forEach(item => {
                item.selected = !!motion_list.find(v => v.types === item.types && v.types !== current_types)
            })

            this.ai_sport_types = ai_sport_types
            this.$refs.changeAiSportTypesPopup.open()
        },


        selectAiSportTypes(item) {
            if (item.selected) return this.$uni.showToast(`${item.name}已选择，请选择其他运动类型`)
            const {types, logo, name} = item
            this.edit_ai_sport_item_data.title = ''
            this.edit_ai_sport_item_data.types = types
            this.edit_ai_sport_item_data.name = name
            if (logo) this.edit_ai_sport_item_data.logo = logo
            this.$refs.changeAiSportTypesPopup.close()
        },


        async getSystemGameList(game_center = {}) {
            this.conf.active.game_center.daily_times = game_center.daily_times || ''

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.game_center.userGameCenter/system_support_game_list'
            })

            const game_list = res?.data?.game_list
            if (!game_list || !game_list.length) return false
            const user_game_list = game_center.game_list || []

            this.conf.active.game_center.game_list = game_list.map(item => {
                item.open = false
                item.exchange_num = ''

                const userGame = user_game_list.find(u => u.types === item.types)
                if (userGame) {
                    item.open = true
                    if (userGame.logo) item.logo = userGame.logo
                    if (userGame.exchange_num) item.exchange_num = userGame.exchange_num
                }
                return item
            })
        },

        gameItemOpenChange(item, e) {
            const value = Number(e.detail.value)
            item.open = value === 0
        },


        toSelNews() {
            this.$uni.navigateTo('/pages/news/list?type=user&is_sel=true', {
                events: {
                    selNews: data => {
                        this.conf.active.news.news_id = data.id
                        this.conf.active.news.news_title = data.title
                    }
                }
            })
        },

        deleteNews() {
            this.conf.active.news.news_id = ''
            this.conf.active.news.news_title = ''
        },


        changeImage(key) {
            if (!this.rank_set?.closed_AD) {
                const options = {
                    screen_pic: '无法设置开屏大图，请联系客服设置',
                    top_rank_banner: '无法设置排行榜轮播图，请联系客服设置'
                }
                if (options[key]) return this.$uni.showModal(options[key])
            }

            let url = '/pages/other/image_upload_or_select'
            if (this.form_data.active_id) url += `?active_id=${this.form_data.active_id}`

            this.$uni.navigateTo(url, {
                events: {
                    newImg: src => {
                        const function_list = {
                            logo: () => this.form_data.logo = src,
                            top_rank_banner: () => this.conf.active.top_rank_banner.push(src),
                            ai_sport_logo: () => this.edit_ai_sport_item_data.logo = src,
                            step_logo: () => this.step_set_data.logo = src
                        }
                        const default_function = () => this.conf.active[key] = src
                        function_list[key] ? function_list[key]() : default_function()
                    }
                }
            })
        },

        ipListItemChange(value, index) {
            this.$set(this.conf.active.ip_set.ip_city, index, value)
        },


        previewImage(urls, current = urls[0]) {
            uni.previewImage({
                urls,
                current
            })
        },


        dataCheck(data) {
            const showToast = title => {
                this.type_id = 1
                this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none')
            }

            if (!data.name) {
                showToast('请输入活动名称')
                return false
            }

            if (!data.active_id) {
                if (!data.mobile) {
                    showToast('请输入手机号')
                    return false
                }
                if (data.mobile.toString().length !== 11) {
                    showToast('手机号长度有误')
                    return false
                }
                if (!data.wechat_num) {
                    showToast('请输入微信号')
                    return false
                }
                const wechat_num = data.wechat_num.toString()
                if (wechat_num.length < 4 || wechat_num.length > 20) {
                    showToast('微信号长度有误')
                    return false
                }
                if (this._utils.isChineseChar(wechat_num)) {
                    showToast('微信号不能输入中文')
                    return false
                }
            }

            return true
        },

        setMustSubmitData() {
            const errModal = content => {
                this.type_id = 3
                this.$uni.showModal(content)
                return false
            }


            for (let i = 0; i < this.conf.must_submit.length; i++) {
                const v = this.conf.must_submit[i]
                console.log(v)
                if (!v.title) return errModal('参与活动需要填写的信息选项填写不完整，请检查。')
                v.name = pinyin(v.title, {toneType: 'none', type: 'array'}).join('_')
                if (v.types === 2) {
                    if (!v.options?.length) return errModal(`${v.title} 至少需要添加一个选项。`)

                    for (let j = 0; j < v.options.length; j++) {
                        const v1 = v.options[j]
                        if (!v1?.text) return errModal(`${v.title} 有未填写的选项，请检查。`)
                    }
                }
            }

            return true
        },

        getAiMotionSet() {
            const errAlert = content => {
                this.type_id = 8
                this.$uni.showModal(content)
                return false
            }

            const AI_motion = JSON.parse(JSON.stringify(this.conf.AI_motion))

            const list = AI_motion.motion_list
            for (let i = 0; i < list.length; i++) {
                if (!list[i].types) return errAlert(`第${i + 1}个运动设置没有选择运动类型`)

                const mode = this.conf.active.ai_sport_activity_reward_mode
                const value = Number(list[i].reward_value)
                delete list[i].reward_value
                if (mode === 'integral') {
                    list[i].integral_reward = {
                        integral: value,
                        num: list[i].num
                    }
                }
                if (mode === 'water') {
                    list[i].water = value
                    delete list[i].num
                }
            }

            return AI_motion
        },

        confCheck() {
            const showToast = (title, type_id) => {
                const durations = () => {
                    if (title.length >= 20) return 5000
                    if (title.length >= 15) return 3000
                    if (title.length >= 10) return 2000
                    return 1500
                }
                this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none', durations())

                if (type_id) this.type_id = type_id

                return false
            }

            const conf = JSON.parse(JSON.stringify(this.conf))

            const AI_motion = this.getAiMotionSet()
            if (!AI_motion) return
            conf.AI_motion = AI_motion

            const active = conf.active

            if (active.enter_types === 2) {
                const password = active.password
                if (!this.have_password && !password) return showToast('请输入活动密码', 3)
                if (password && password.length < 3) return showToast('活动密码不得少于3位', 3)
                if (password && password.length > 20) return showToast('活动密码不得大于20位', 3)

                active.password = password || this.old_password
            } else {
                active.password = 1
            }

            if (!active.submit_times) {
                delete active.submit_times
            } else if (active.submit_times % 1 !== 0) {
                return showToast('运动次数限制只能输入整数', 8)
            }

            if (!active.max_daily_energy || active.ai_sport_activity_reward_mode !== 'water') {
                delete active.max_daily_energy
            } else {
                const max_daily_energy = Math.floor(active.max_daily_energy)
                if (isNaN(max_daily_energy)) return showToast(`每日获得${this.waterUnit}上限只能输入整数`, 8)

                active.max_daily_energy = max_daily_energy
            }

            if (!active.per_seconds) {
                delete active.per_seconds
            } else if (active.per_seconds % 1 !== 0) {
                return showToast('运动时间只能输入整数', 8)
            }

            if (this.rank_set?.game_center && active.ai_sport_activity_reward_mode === 'water') {
                const game_list = active.game_center.game_list
                const new_list = []
                game_list.forEach(v => {
                    if (v.open) new_list.push(v)
                })
                for (let i = 0; i < new_list.length; i++) {
                    const item = new_list[i]
                    if (item.open) {
                        item.exchange_num = Number(item.exchange_num)
                        const e_num = item.exchange_num
                        if (isNaN(e_num) || !e_num || e_num < 1 || e_num > 1000000) {
                            return showToast(`${item.title}能量兑换值未填写或填写错误，请重新填写`, 7)
                        }
                    }
                    delete item.open
                }
                active.game_center.game_list = new_list
            } else {
                delete active.game_center
            }

            if (active.ai_sport_extend_types?.step?.open) {
                if (active.ai_sport_activity_reward_mode === 'integral') {
                    active.exchange_rate = 1
                    active.integral.exchange_step = this.exchange_rate
                } else {
                    active.exchange_rate = this.exchange_rate
                    active.integral.exchange_step = ''
                }
            } else {
                // 没有开启运动步数兑换，不要提交相关设置字段
                const deleteKeys = ['ai_sport_extend_types', 'exchange_rate', 'min_num', 'max_num']
                deleteKeys.forEach(key => delete active[key])
            }

            if (!active.we_run_share_button_hide) delete active.we_run_share_button_hide

            if (active.daily_ai_sports_max_integral) {
                const num = Number(active.daily_ai_sports_max_integral)
                if (isNaN(num) || num < 0) return this.$uni.showToast(`每日运动获得${this.integralUnit}上限设置不正确`)
                active.daily_ai_sports_max_integral = num
            } else {
                delete active.daily_ai_sports_max_integral
            }

            if (!active.activity_rules_hide) delete active.activity_rules_hide

            let conf_str = JSON.stringify(conf)
            conf_str = conf_str.replace(/·/g, '-')
            return this._utils.base64['encode'](conf_str)
        },

        save() {
            if (this.success) return false

            const data = JSON.parse(JSON.stringify(this.form_data))

            if (!this.dataCheck(data)) return false
            if (!this.setMustSubmitData()) return false

            const conf = this.confCheck()
            if (!conf) return false
            data.conf = conf

            data.pic_list = this._utils.base64['encode'](JSON.stringify(this.pic_list))
            data.begin_time /= 1000
            data.end_time /= 1000

            this.saveAjax(data)
        },

        async saveAjax(data) {
            data.types = this.types

            this.loading = true
            this.$uni.showLoading('保存中...')

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/create_active',
                data
            })

            uni.hideLoading()
            this.loading = false

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '保存失败')

            this.updatePageData()

            if (data.active_id) {
                this.$uni.showToast('保存成功', 'success')
                return this.$uni.navigateBack(1, {delay: 1000})
            }

            this.success = true
        },

        // 活动修改/创建成功，如果页面栈有详情或列表页面，更新页面数据
        updatePageData() {
            const pages = getCurrentPages()

            const update_page_list = [
                {
                    route: 'pages/ai_sport/pages/user/detail',
                    _function: page => {
                        page.$vm.getDetail(true)
                    }
                },
                {
                    route: 'pages/ai_sport/pages/admin/manage',
                    _function: page => {
                        page.$vm.getDetail()
                    }
                },
                {
                    route: 'pages/activity/user/index',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getActivityList()
                    }
                },
                {
                    route: 'pages/activity/user/activity_list',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getList()
                    }
                }
            ]

            update_page_list.forEach(v => {
                const page = pages.find(n => n.route === v.route)
                if (page) v._function(page)
            })
        },

        lookMyActivityList() {
            const list_page_route = 'pages/activity/user/activity_list'
            const pages = getCurrentPages()
            const list_page_index = pages.findIndex(v => {
                return v.route === list_page_route && v.options.type && v.options.type === 'create'
            })
            if (list_page_index === -1) return this.$uni.redirectTo(`/${list_page_route}?type=create`)

            this.$uni.navigateBack(pages.length - list_page_index - 1)
        }
    }
}
</script>

<style lang="scss">
.page {
    padding-top: 40px;
    padding-bottom: 75px;
}

.type-list {
    position: fixed;
    z-index: 9;
    width: 100vw;
    top: 0;
    left: 0;
}

.type-item {
    height: 40px;
    line-height: 40px;
    text-align: center;
    box-sizing: border-box;
}

.active-type {
    color: #007AFF;
    border-bottom: 2px solid #007AFF;
}

.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}


.form-item .textarea {
    width: 100%;
    line-height: 16px;
    padding: 10px 0;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}

.forms-picker {
    border-radius: 4px;
    border: 1px solid #e5e5e5;
    padding: 0 5px;
    height: 36px;
    line-height: 36px;
}

.input-word {
    line-height: 37px;
    color: #666;
    font-size: 14px;
}

.input-container {
    width: 80px;
    margin: 0 2px;
}

.must-submit-list {
    border-bottom: 1px dashed #eee;
}

.must-xing-ming {
    line-height: 34px;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
}

.must-rules-picker {
    line-height: 34px;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    margin-left: 5px;
    padding: 0 15px;
}

.delete-rules {
    line-height: 36px;
    padding: 0 10px;
}

.must-options-item {
    padding: 3px 0;
}


.article-item {
    border-radius: 5px;
    padding-left: 10px;
    padding-right: 5px;
    margin: 3px 0;
}

.bottom-btn-view {
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.login-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}

.save-success {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 999999;
    box-sizing: border-box;
    padding-top: 10vh;
}

.save-success .icon {
    padding-bottom: 50px;
}

.look-my-activity-view {
    padding-top: 5px;
}

.look-my-activity {
    display: inline-block;
    padding: 5px;
}

.uni-date-x {
    padding: 0 !important;
}

.uni-date__x-input {
    font-size: 16px !important;
    color: #000;
}

/* #ifdef H5 */

@media screen and (min-width: 500px) {
    .type-list {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }
}

/* #endif */


.ai-sport-list {
    .ai-sport-item {
        position: relative;
        background-color: #f8f8f8;
        border-radius: 5px;
        margin: 5px;
        padding: 10px;

        .ai-sport-item-logo {
            margin-right: 10px;
        }

        .item-controls {
            position: absolute;
            right: 0;
            top: 0;
        }

        .item-info {
            font-size: 14px;
            color: #495060;

            .item-info-label {
                white-space: nowrap;
                padding-right: 3px;
            }
        }
    }
}

$sport-logo-width: 100px;
$sport-logo-height: calc(#{$sport-logo-width} * 204 / 430);
.ai-sport-item-logo {
    image {
        display: block;
    }

    .ai-sport-item-logo-image {
        width: $sport-logo-width;
        min-width: $sport-logo-width;
        height: $sport-logo-height;
        border-radius: 5px;
    }
}

.ai-sport-item-edit, .ai-sport-types-change {
    width: 90vw;
    padding: 0 10px;
    border-radius: 10px;
    box-sizing: border-box;
}

.ai-sport-item-edit {
    .ai-sport-item-edit-buttons {
        padding: 10px;
        border-top: 1px solid #eee;

        view {
            margin: 0 10px;
            width: 100px;
            line-height: 40px;
            border-radius: 20px;
            text-align: center;
        }
    }
}

.ai-sport-types-change {
    width: 346px;
    padding: 0 0 10px;

    .ai-sport-type-list {
        padding: 5px;

        .ai-sport-type-item {
            margin: 5px;
            border: 1px solid #fff;
            border-radius: 5px;
            overflow: hidden;

            .ai-sport-type-item-logo, .ai-sport-type-item-no-logo {
                width: $sport-logo-width;
                min-width: $sport-logo-width;
                height: $sport-logo-height;
            }

            .ai-sport-type-item-no-logo {
                line-height: $sport-logo-height;
                text-align: center;
                font-size: 14px;
                background-color: #f8f8f8;
            }
        }

        .selected {
            border-color: #4cd964;
            position: relative;
        }

        .selected::before {
            content: "已选";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 16px;
            line-height: 16px;
            font-size: 12px;
            text-align: center;
            color: #fff;
            border-radius: 0 0 5px 5px;
            background-color: #4cd964;
            opacity: .7;
        }
    }
}
</style>
