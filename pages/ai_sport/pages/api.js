import xwy_api from '@/utils/api/xwy_api'

module.exports = {
    enhancedModelList: null,
    
    async getEnhancedModelList() {
        const res = await xwy_api.request({
            url: 'front.system.system_conf/front_get_value',
            data: {
                name: 'ai_motion_sports_mobile_machine_set',
                get_types: 1
            }
        })
        const enhancedModelList = res?.data?.value?.['mobile_list']
        if (enhancedModelList?.length) {
            this.enhancedModelList = enhancedModelList
            return enhancedModelList
        }
        return []
    },
    
    async isEnhancedModel(model) {
        const systemInfo = uni.getSystemInfoSync()
        const osName = (systemInfo.osName || '').toLowerCase()
        const deviceBrand = (systemInfo.deviceBrand || '').toLowerCase()
        
        if (osName === 'harmonyos' || osName === 'android') return true  // 鸿蒙和安卓系统
        
        // 不是这3个系统的话大概率是安卓、鸿蒙、套壳安卓，直接开启增强模式
        if (osName !== 'ios' && osName !== 'windows' && osName !== 'macos') return true
        
        // 由于鸿蒙系统不一定会返回系统名称,所以使用鸿蒙系统的手机要做特殊处理
        if (deviceBrand.startsWith('huawei')) return true                // 华为手机
        if (deviceBrand.startsWith('honor')) return true                 // 荣耀手机
        
        
        model ||= uni.getDeviceInfo().deviceModel
        const enhancedModelList = this.enhancedModelList || await this.getEnhancedModelList()
        return enhancedModelList.includes(model)
    }
}