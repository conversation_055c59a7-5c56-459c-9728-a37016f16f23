<template>
    <view class="page bg-background">
        <view class="list pt5 pb10">
            <view class="item bg-white" v-for="(item, index) in list" :key="index">
                <image class="logo" v-if="item.logo" :src="item.logo" mode="widthFix"/>
                <view class="color-title ptm5">{{ item.title || item.name }}</view>
                <view class="flex-kai font14" style="border-top: 1px solid #eee; padding-top: 10px;">
                    <view class="color-sub flex-row">
                        <view class="pl10" hover-class="navigator-hover" @click="lookRecord(item)">
                            <text class="iconfont icon-footnotes font14 pr5"></text>
                            <text>运动记录</text>
                        </view>
                    </view>
                    <view class="color-primary" hover-class="navigator-hover" @click="toStart(item)">
                        <text class="iconfont icon-walk font14 pr5"></text>
                        <text>开始运动</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import login from '@/utils/api/login.js'

export default {
    data() {
        return {
            active_id: '',
            list: []
        }
    },
    onLoad(e) {
        this.active_id = e.active_id
        if (e.point_id) this.point_id = Number(e.point_id)
        if (e.manual) this.manual = true

        // 是不是任务闯关活动，任务闯关活动完成运动后要回退页面，不然可以点其他运动继续去运动
        if (e.is_task) this.is_task = true

        login.uniLogin(err => {
            if (err && err.errMsg) return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            
            this.getAiMotionList()
        })
    },

    onShow() {
        // 任务闯关活动，完成任务后需要回退到活动页面，不然在运动列表页面可以继续去做其他运动
        if (this.is_task && this.sportSuccess) this.$uni.navigateBack()
    },

    methods: {
        getAiMotionList() {
            this.getOpenerEventChannel?.()?.on('AI_motion_list', list => {
                if (list?.length) this.list = list
            })
        },
        
        toStart(item) {
            let url = `./ai-sport-recognition?active_id=${this.active_id}&types=${item.types}&name=${item.title || item.name}`
            if (this.point_id) url += `&point_id=${this.point_id}`
            if (this.manual) url += '&is_manual=1'

            this.$uni.navigateTo(url, {
                events: {
                    success: () => {
                        // 任务闯关活动，运动完成后，需要回退到活动页面，不然还在这个页面可以继续去完成其他运动
                        this.sportSuccess = true
                        this.getOpenerEventChannel?.()?.emit?.('success')
                    }
                }
            })
        },
        
        
        lookRecord(item) {
            this.$uni.navigateTo(`./record_list?active_id=${this.active_id}&types=${item.types}&name=${(item.title || item.name)}`)
        }
    }
}
</script>

<style scoped>
.page {
    min-height: 100vh;
}

.item {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
}

.logo {
    width: 100%;
    height: auto;
}
</style>
