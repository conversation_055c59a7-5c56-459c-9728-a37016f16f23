<template>
    <view class="container">
        <view class="time-count flex-kai p10">
            <view>时间: {{ sportTimes }}</view>
            <view v-if="countsShow">计数: {{ sportCounts || 0 }}</view>
        </view>

        <view v-if="evn_version !== 'release'" class="time-count flex-kai p10" style="top: 40px;">
            <view>ve: {{ ve }}</view>
            <view>分数: {{ score.toFixed(2) }}</view>
            <view>帧率: {{ fps }}</view>
        </view>

        <view v-if="notAllBodyTips" class="not-all-body-tips color-white text-center">
            <text class="iconfont color-white icon-body-check" style="font-size: 100px;"></text>
            <view>{{ notAllBodyTips }}</view>
        </view>

        <view class="tips font12 color-white">
            <view class="text-center">互动小提示</view>
            <view class="pt5">手机竖屏固定放置, 调整屏幕角度, 训练者全身及双脚出现至屏幕内, 识别更精准。</view>
        </view>

        <view class="toggle-camera color-white font14 text-center" @click="onToggleCamera">
            <uni-icons type="camera" size="14" color="#ffffff"/>
            <text>翻转摄像头</text>
        </view>

        <template>
            <!--<view v-if="buttonStatus === 'standby'" class="action-button" hover-class="navigator-hover" @tap="onDelayStart">开始</view>-->
            <view v-show="buttonStatus === 'running'" class="action-button" hover-class="navigator-hover"
                  @tap="onStopCapture">停止
            </view>
            <view v-show="buttonStatus === 'stopped'">
                <view class="action-button" hover-class="navigator-hover" @click="submit">提交</view>
                <!--<view class="action-button" hover-class="navigator-hover" @click="againSport">继续</view>
                <view class="action-button action-button-submit" hover-class="navigator-hover"
                      @click="submit">提交
                </view>-->
            </view>
            
        </template>


        <camera
            id="preview"
            class="preview"
            :style="previewStyle"
            flash="off"
            :device-position="deviceKey"
            resolution="low"
            frame-size="small"
        ></camera>
        <canvas v-show="buttonStatus !== 'stopped'" class="preview graphs" type="2d" id="graphics"
                :style="previewStyle"></canvas>


        <uni-popup ref="per_seconds" type="center">
            <view class="per-seconds-popup text-center" @click="perSecondsPopupOpen = false">
                <view class="popup-close flex-all-center">
                    <uni-icons type="closeempty" color="#cccccc" size="18"/>
                </view>
                <text class="iconfont icon-alarm-clock color-warning" style="font-size: 100px;"></text>
                <view class="color-sub" style="padding: 20px 0;">
                    <view class="">请在{{ per_seconds }}秒内完成运动</view>
                    <view class="">{{ per_seconds }}秒后未完成运动将自动停止运动</view>
                </view>
                <view class="flex-all-center">
                    <view
                        class="color-primary p10"
                        @click="perSecondsPopupOpen = false"
                        hover-class="navigator-hover"
                    >知道了
                    </view>
                </view>
            </view>
        </uni-popup>

        <!-- 点击开始后的倒计时 -->

        <view v-if="start_down_time >= 0" class="start-down-time flex-all-center">
            <template v-for="(item, index) in 6">
                <view
                    v-if="index === start_down_time"
                    class="color-white flex-all-center"
                    style="width: 100vw; height: 100vh;"
                    :key="item"
                >
                    <text
                        class="start-down-time-number color-warning"
                        :style="{fontSize: start_down_time > 0 ? '300rpx' : '200rpx'}"
                    >{{ index > 0 ? index : 'GO' }}
                    </text>
                </view>
            </template>
        </view>


    </view>
</template>

<script>
const app = getApp()

const STATIC_PUBLIC_URL = 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/'
const MUSIC_BASE_URL = STATIC_PUBLIC_URL + 'music/sport/'
const SoundEffects = {
    CountDown: MUSIC_BASE_URL + 'di.mp3',
    Go: MUSIC_BASE_URL + 'go.mp3',
    Tick: MUSIC_BASE_URL + 'sportding.mp3',
    EndStart: MUSIC_BASE_URL + 'end1.mp3',
    EndEnd: MUSIC_BASE_URL + 'end2.mp3'
}

const fetchWechat = require("fetch-wechat")
const plugin = requirePlugin('aiSport')
plugin.initialize({
    modelUrl: STATIC_PUBLIC_URL + 'ai-sport/tfjs-model/tfjs-model_movenet_singlepose_lightning_4/model.json',
    fetchFunc: fetchWechat.fetchFunc(),
    // humanPointThreshold: 0.3, // 进行动作识别时，取人体关键点运动时，要求的关键点评分阈值，低于此值将忽略该点，不通过 范围：0.00-1.00，默认：0.45
    debugEnabled: true
});
// const utils = require("../utils/util")
const AiSports = requirePlugin("aiSport")

/*const PoseGraphs = AiSports.PoseGraphs,
    humanDetection = AiSports.humanDetection,
    SportBase = AiSports.sports.SportBase*/
const {PoseGraphs, humanDetection, sports: {SportBase}} = AiSports

const CameraDevice = require("../utils/camera-device")
const SportHandLift = require('../sports/sport-hand-lift')

const {windowWidth, windowHeight, pixelRatio} = uni.getSystemInfoSync()

import sportApi from './api'
import config from '@/pages/ai_sport/config'

export default {
    data() {
        return {
            types: null,
            deviceKey: "front",
            previewWidth: 480,
            previewHeight: 640,
            frameWidth: 480,
            frameHeight: 640,
            buttonStatus: 'standby',  // 按钮状态 standby: 准备, running: 运行中, stopped: 停止
            // status: 'unknown',
            fps: 0,
            // poseFps: 0,
            sportCounts: 0,
            sportTimes: '00:00',
            // sports: utils.clone(SportBase.SPORTS),
            sportIndex: 0,
            sportKey: '',
            per_seconds: 0,  // 本次运动时间限制
            start_down_time: -1,  // 开始倒计时
            allBody: false,  // 是否检测到全身
            perSecondsPopupOpen: false,
            score: 0,
            ve: 0,
            evn_version: app.globalData['evn_version']
        }
    },
    watch: {
        perSecondsPopupOpen(val) {
            val ? this.$refs.per_seconds.open() : this.$refs.per_seconds.close()
        }
    },
    computed: {
        previewStyle() {
            const {previewWidth: width, previewHeight: height} = this
            return `width: ${width}px; height: ${height}px; left: ${(windowWidth - width) / 2}px;`
        },
        notAllBodyTips() {
            if (this.allBody || this.start_down_time >= 0 || this.perSecondsPopupOpen) return ''
            if (this.buttonStatus === 'standby') return '全身出现在屏幕内自动开始运动'
            if (this.buttonStatus === 'running') return '请保持全身出现在屏幕内'
            return ''
        },
        countsShow() {
            return this.xwy_config.AiSportNeedCountsTypes(this.types)
        }
    },

    onUnload() {
        this.clearIntervalTimeout() // 退出时清除定时器
        this.onStopCapture()
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.active_id = options.active_id
        this.types = Number(options.types)
        if (options.point_id) this.point_id = Number(options.point_id)
        if (options.per_seconds) this.per_seconds = Number(options.per_seconds)
        if (options.name) this.$uni.setNavigationBarTitle(options.name)

        this.setSportTypes()
    },


    methods: {

        setSportTypes() {
            const sport_types = config.getSupportedSportTypes()

            const current_sport = sport_types.find(v => v.types === this.types)
            if (!current_sport)
                return this.$uni.showModal('暂未支持该运动', {success: () => uni.navigateBack()})

            this.sportKey = current_sport.sportKey

            this.previewInit()
            this.getSportStorage()
        },

        async getSportStorage() {
            const storage = uni.getStorageSync(`ai-sport-submit-${this.active_id}-${this.types}`)
            if (!storage) {
                await this.veSelect()
                return
            }

            const {seconds, num, point_id} = storage

            if (this.point_id && point_id !== this.point_id) {
                await this.veSelect()
                return
            }

            let content = `有未提交的运动记录，运动时间: ${seconds}秒`
            if (this.countsShow) content += `, 运动次数: ${num}`
            content += '。是否提交?'

            const res = await this.$uni.showModal(content, {
                showCancel: true,
                cancelText: '删除记录',
                confirmText: '提交记录'
            })

            if (!res.confirm) {
                this.removeSportStorage()
                await this.veSelect()
                return
            }

            this.submitSportStorage(seconds, num)
        },

        previewInit() {
            this.autoFitPreview(480, 640)
            this.initCanvas()
        },
        
        async veSelect() {
            let ve = 1
            if (this.evn_version !== 'trial') return this.initHumanDetection(ve)
            const res = await this.$uni.showModal('请选择识别引擎', {
                showCancel: true,
                cancelText: 've1',
                confirmText: 've2'
            })
            if (res.confirm) ve = 2
            await this.initHumanDetection(ve)
        },

        async initHumanDetection(ve = 1) {
            if (app.globalData['tempData'].ve === ve) {
                if (this.evn_version !== 'release') this.ve = ve
                console.log('之前已初始化成功，无需再次初始化。', ve)
                return this.humanDetectionInitComplete()
            }
            app.globalData['tempData'].ve = 1

            this.$uni.showLoading('加载中...')
            
            const isEnhancedModel = await sportApi.isEnhancedModel()
            
            humanDetection.initialize({
                ve,
                enhanced: isEnhancedModel,
                callback: err => {
                    uni.hideLoading()
                    if (err) return this.$uni.showModal(err.toString(), {title: '初始化失败'})

                    const ve = humanDetection['getVe']()
                    if (this.evn_version !== 'release') this.ve = ve
                    console.log('人体检测能力初始化成功。', ve)
                    console.log('代码是否开启增强模式。', isEnhancedModel)
                    console.log('插件是否开启增强模式。', humanDetection.isEnhanced())
                    
                    this.humanDetectionInitComplete()
                }
            });
        },

        humanDetectionInitComplete() {
            if (this.per_seconds) this.perSecondsPopupOpen = true
            this.AudioContext = uni.createInnerAudioContext()

            // 增加开合跳
            SportBase.register(SportHandLift.KEY, SportHandLift.NAME, () => new SportHandLift())

            console.log('全部可用运动：', SportBase['SPORTS'])

            //初始化运行
            this.buildSport()
        },


        autoFitPreview(width, height) {
            let rate = windowWidth / width
            if (rate * height < windowHeight) rate = windowHeight / height

            this.previewWidth = width * rate
            this.previewHeight = height * rate
            this.previewRate = rate
            this.frameWidth = width
            this.frameHeight = height
        },

        initCanvas() {
            const query = wx.createSelectorQuery()
            query.select('#graphics').fields({node: true, size: true}).exec(res => {
                const canvas = res[0].node
                const ctx = canvas.getContext('2d')
                const dpr = pixelRatio
                canvas.width = res[0].width * dpr
                canvas.height = res[0].height * dpr
                ctx.scale(dpr, dpr)

                this.canvas = canvas
                this.ctx = ctx

                //人体绘制器
                this.poseGraphs = new PoseGraphs(ctx, canvas.width, canvas.height, this.previewRate)
            })
        },

        buildSport() {
            this.sport = SportBase.create(this.sportKey)

            
            // 深蹲注入规则  后面插件升级后需要去掉(当前版本 1.5.1)。
            if (this.sportKey === 'Squat') {
                this.sport.buildRules();
                const rules = this.sport.rules;
                rules.squat.rules.push({
                    name: '腿部下蹲检查',
                    calc: '$or',
                    rules: [{
                        name: '左腿90度',
                        calc: 'match-angle',
                        angleKey: 'left_hip',
                        secondKey: 'left_shoulder',
                        thirdKey: 'left_knee',
                        angle: 80,
                        offset: this.sport.threshold
                    }, {
                        name: '右腿90度',
                        calc: 'match-angle',
                        angleKey: 'right_hip',
                        secondKey: 'right_shoulder',
                        thirdKey: 'right_knee',
                        angle: 80,
                        offset: this.sport.threshold
                    }]
                });
            }
            
            this.sport.onTick = (cnt, times) => {
                this.AudioContext.stop()
                this.AudioContext.src = SoundEffects.Tick
                this.AudioContext.play()
                this.sportCounts = cnt
                this.sportTimes = this.sport['toTimesString']() || times

                this.setSportStorage()
            }

            this.standby()
        },

        standby() {
            this.initVideo()
            this.sport.reset()
            this.camera.start()

            wx.setKeepScreenOn({
                keepScreenOn: true
            })
        },

        onTimeOut() {
            if (this.per_seconds && this.sport.times >= this.per_seconds) {
                this.onStopCapture()
                this.$uni.showModal('时间到，运动结束。')
            }
        },

        // 开始前5秒倒计时
        startCountDown() {
            let time = 5
            time += 1
            this.startCountDownInterval = setInterval(() => {
                this.AudioContext.stop()
                this.AudioContext.src = SoundEffects.CountDown
                this.AudioContext.play()

                time--
                this.start_down_time = time
                if (time === 0) {
                    this.startCountDownTimeout = setTimeout(() => {
                        this.start_down_time = -1
                        this.onStartCapture()
                        // this.updateTimes()
                        clearTimeout(this.startCountDownTimeout)
                    }, 1000)
                    this.AudioContext.src = SoundEffects.Go
                    this.AudioContext.play()
                    clearInterval(this.startCountDownInterval)
                }
            }, 1000)
        },

        // 结束前5秒倒计时
        endCountDown() {
            const countdown_seconds = 5
            if (this.per_seconds && this.sport.times === this.per_seconds - countdown_seconds) {
                let seconds = countdown_seconds
                this.endCountDownInterval = setInterval(() => {
                    this.AudioContext.stop()
                    this.AudioContext.src = seconds > 0 ? SoundEffects.EndStart : SoundEffects.EndEnd
                    this.AudioContext.play()
                    if (seconds <= 0) clearInterval(this.endCountDownInterval)
                    seconds--
                }, 1000)
            }
        },

        clearIntervalTimeout(timers = []) {
            const timerOptions = {
                startCountDownInterval: () => {
                    this.startCountDownInterval && clearInterval(this.startCountDownInterval)
                },
                startCountDownTimeout: () => {
                    this.startCountDownTimeout && clearTimeout(this.startCountDownTimeout)
                },
                endCountDownInterval: () => {
                    this.endCountDownInterval && clearInterval(this.endCountDownInterval)
                }
            }
            if (!timers?.length) timers = Object.keys(timerOptions)
            timers.forEach(timer => timerOptions[timer]())
            console.log('退出页面，清除定时器', timers)
        },


        // onSelectSport(e) {
        // 	this.sportIndex = e.detail.value;
        // 	this.sportKey = this.sports[this.sportIndex].key;
        // 	this.buildSport(); //重建分析器
        // },


        async detection(frame) {
            let ts = new Date().getTime()
            let human = await humanDetection['detectionAsync'](frame)
            ts = (new Date()).getTime() - ts

            // this.poseFps = Math.floor(1000 / ts)

            // 检测到全身的判断标准，为1的话，可能会出现某些情况或者某些设备上即使是全身入镜也无法达到标准，如果后续出现较多全身入境也无法达到彼标准的，可以将标准降低
            this.allBody = human?.score >= .5
            this.setScore(human?.score || 0)

            //无结果
            if (!human) return this.poseGraphs.clear()

            if (this.buttonStatus === 'standby' && this.allBody) await this.onDelayStart()

            // if (this.buttonStatus !== 'running') return this.poseGraphs.clear()

            //识别、绘制人体模型
            this.sport.pushing(human)
            this.poseGraphs['drawing'](human['keypoints'])
        },

        setScore(score) {
            if (this.evn_version === 'release') return
            if (this.setScoreTime && new Date().getTime() - this.setScoreTime < 1000) return
            this.score = score
            this.setScoreTime = new Date().getTime()
        },

        setFps(fps) {
            if (this.evn_version === 'release') return
            if (this.setFpsTime && new Date().getTime() - this.setFpsTime < 1000) return
            this.fps = fps
            this.setFpsTime = new Date().getTime()
        },


        initVideo() {
            if (this.camera) return

            this.camera = new CameraDevice()
            this.camera.targetFPS = 30
            this.camera.onFrame = frame => {
                this.setFps(this.camera.fps || 0)
                if (frame.width !== this.frameWidth) {
                    //重新自适应
                    this.autoFitPreview(frame.width, frame.height)
                    this.initCanvas()
                }

                this.detection(frame)
            }

            /*this.camera.onStatusChange = (val, text) => {
                if (val === -1) uni.hideLoading()
                // this.status = text
            };*/
            this.camera.onStatusChange = val => {
                if (val === -1) uni.hideLoading()
            }
        },

        onToggleCamera() {
            let dev = this.deviceKey
            this.deviceKey = dev === "back" ? "front" : "back"
            this.$uni.showToast(`已切换为${this.deviceKey === "back" ? "后置" : "前置"}摄像头`)
        },

        onStartCapture() {
            this.sport.start()
            this.sport.startTime ||= new Date().getTime()
            this.buttonStatus = 'running'
            this.sportCounts = 0
            this.sportTimes = '00:00'
            this.updateTimes()
        },

        // 实时更新运动时间
        updateTimes() {
            this.clearTimesInterval()
            this.timesInterval = null
            this.timesInterval = setInterval(() => {
                // 计时的运动类型不能调用sport.countTimes()方法，否则计时会出现错误
                if (this.countsShow) this.sport['countTimes']()
                this.sportTimes = this.sport['toTimesString']()
                /*console.log('🚀');
                console.log(this.sport['toTimesString']());
                console.log(this.sport.times);*/

                this.setSportStorage()

                this.onTimeOut()
                this.endCountDown()
            }, 1000)
        },
        clearTimesInterval() {
            this.timesInterval && clearInterval(this.timesInterval)
        },

        againSport() {
            this.sport.start()
            this.camera.start()
            this.buttonStatus = 'running'
            this.updateTimes()
        },

        async onDelayStart() {
            if (this.buttonStatus === 'running' || this.isReady) return
            this.isReady = true  // 用来避免重复执行开始方法

            /*if (this.camera) {
                const status = this.camera.getStatus()
                if (status !== "unknown" && status !== "stopped") return
            }*/

            if (this.sportTimes !== '00:00' || this.sportCounts) {
                const res = await this.$uni.showModal('上次运动记录未提交，确定重新开始运动？')
                if (!res.confirm) return
            }

            if (!this.allBody) return this.$uni.showModal('需全身出现在屏幕中才能开始运动')

            this.startCountDown()
        },


        onStopCapture() {
            if (!this.camera || this.camera.getStatus() !== 'running') return
            if (this.camera) this.camera.stop()
            this.sport.stop()
            wx.setKeepScreenOn({
                keepScreenOn: false
            })

            this.clearTimesInterval()
            this.buttonStatus = 'stopped'
            this.sportCounts = this.sport['counts']
            this.sportTimes = this.sport['toTimesString']()
            // this.setFps(this.camera.fps || 0)
            // this.status = this.camera.getStatus()

            this.$uni.showLoading('正在停止...')

            console.log('退出页面，停止运行')

            // this.submit()
        },


        setSportStorage() {
            const seconds = this.sport.times || 0, num = this.sportCounts || 0

            if (!seconds || !num) return

            uni.setStorageSync(`ai-sport-submit-${this.active_id}-${this.types}`, {
                seconds,
                num,
                point_id: this.point_id || ''
            })
        },

        removeSportStorage() {
            uni.removeStorageSync(`ai-sport-submit-${this.active_id}-${this.types}`)
        },


        submitSportStorage(seconds, num) {
            this.sportSubmit(seconds, num)
        },

        async submit() {
            let content = `运动时间: ${this.sportTimes}`
            if (this.countsShow) content += `, 运动次数: ${this.sportCounts}。确定提交?`
            const modal = await this.$uni.showModal(content, {showCancel: true})
            if (!modal.confirm) return

            await this.sportSubmit(this.sport.times || 0, this.sportCounts || 0)
        },

        async sportSubmit(seconds = 0, num = 0) {
            this.$uni.showLoading('提交中...')

            const sport_data_obj = {seconds, num}
            const sport_data_str = JSON.stringify(sport_data_obj)
            const sport_data_base64 = this._utils.base64['encode'](sport_data_str)
            const sport_data = this._utils.randomCoding() + sport_data_base64

            const data = {
                active_id: this.active_id,
                types: this.types,
                sport_data
            }
            if (this.point_id) data.point_id = this.point_id

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.ai_motion.user/submit_exchange_record',
                data
            })

            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res && res.info || '提交失败')

            this.removeSportStorage()

            const eventChannel = this.getOpenerEventChannel()
            eventChannel?.emit?.('updateSportCount')
            eventChannel?.emit?.('success') // 任务闯关活动，运动后刷新活动状态(任务是否已完成，关卡是否已完成)

            this.$uni.redirectTo(`./submit_success?info=${res.info || '提交成功'}&active_id=${this.active_id}&types=${this.types}&num=${sport_data_obj.num}&seconds=${sport_data_obj.seconds}`)
        }
    }
}
</script>

<style lang="scss">
.container {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;

    .time-count, .tips, .toggle-camera, .action-button, .not-all-body-tips {
        position: absolute;
        z-index: 99;
    }

    .time-count {
        top: 0;
        left: 0;
        width: 100%;
        box-sizing: border-box;

        view {
            background-color: rgba(0, 0, 0, .6);
            color: #fff;
            width: 100px;
            padding: 5px 0;
            text-align: center;
            border-radius: 5px;
        }
    }

    .not-all-body-tips {
        top: 30vh;
        left: 50%;
        width: 280px;
        margin-left: -140px;
        background-color: rgba(0, 0, 0, .6);
        box-sizing: border-box;
        padding: 20px;
        border-radius: 10px;
    }

    .tips {
        bottom: 100px;
        left: 10px;
        width: 100px;
        padding: 5px;
        background-color: rgba(0, 0, 0, .6);
        border-radius: 5px;
    }

    .toggle-camera {
        background-color: rgba(0, 0, 0, .6);
        width: 100px;
        padding: 5px;
        border-radius: 5px;
        bottom: 50px;
        left: 10px;
    }

    .action-button {
        width: 80px;
        height: 80px;
        text-align: center;
        line-height: 80px;
        background-color: rgba(92, 173, 255, .6);
        left: 50%;
        margin-left: -40px;
        bottom: 50px;
        border-radius: 50%;
        font-size: 24px;
        color: #fff;
    }
    
    .action-button-submit {
        width: 60px;
        height: 60px;
        line-height: 60px;
        left: calc(50% + 100px);
        bottom: 60px;
        background-color: rgba(255, 153, 0, .6);
        font-size: 20px;
    }

    .preview {
        width: 480px;
        height: 640px;
        position: absolute;
        top: 0;
        z-index: 1;
    }

    .graphs {
        z-index: 9;
        //box-shadow: 0 0 8px #CCC;
        //background-color: rgba(0, 0, 0, 0.01);
    }
}

.per-seconds-popup {
    width: 320px;
    padding: 20px 0;
    border-radius: 10px;
    position: relative;
    background-color: rgba(255, 255, 255, .8);
}

.popup-close {
    position: absolute;
    right: 0;
    top: 0;
    padding: 10px;
}

.start-down-time {
    position: fixed;
    z-index: 99;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    box-sizing: border-box;
    background-color: rgba(0, 0, 0, .7);
}

.start-down-time-number {
    font-weight: 600;
    animation: countdown_num .5s linear;
}

@keyframes countdown_num {
    from {
        opacity: 0;
        transform: scale(3);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}
</style>
