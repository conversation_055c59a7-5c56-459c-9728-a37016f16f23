<template>
    <view>
        <view v-if="!loading" class="add-success bg-white">
            <view class="icon text-center">
                <uni-icons type="checkbox-filled" size="100" color="#19be6b"/>
                <view class="color-content font16">{{ success_info }}</view>
            </view>
            <view class="flex-all-center color-white font16 text-center">
                <view v-if="weRunShareButtonShow" class="success-btn bg-success"
                      hover-class="navigator-hover" @click="shareToWeRun">分享到微信运动
                </view>

                <navigator v-else class="success-btn bg-success" open-type="navigateBack">继续运动</navigator>
            </view>
            <view class="mini-navigators flex-center text-center">
                <navigator v-if="weRunShareButtonShow" class="mini-navigator color-sub font14"
                           open-type="navigateBack">继续运动
                </navigator>

                <navigator class="mini-navigator color-sub font14" open-type="redirect"
                           :url="'./record_list?active_id=' + active_id + '&types=' + types">运动记录
                </navigator>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()

export default {
    data() {
        return {
            loading: true,
            types: 0,
            active_id: '',
            success_info: '',
            have_share_to_werun: false,
            we_run_share_button_hide: false
        }
    },

    computed: {
        weRunShareType() {
            const types = this.types
            const supportShareTypes = [
                {types: 1, shareTypeId: 4001, unit: 'number'},
                {types: 2, shareTypeId: 4003, unit: 'number'},
                {types: 3, shareTypeId: 4002, unit: 'number'}
            ]
            return supportShareTypes.find(item => item.types === types) || null
        },

        weRunValue() {
            if (!this.weRunShareType?.unit) return null
            return this[this.weRunShareType.unit]
        },

        weRunShareButtonShow() {
            const {weRunShareType, have_share_to_werun, weRunValue, we_run_share_button_hide} = this
            return !!(weRunShareType && !have_share_to_werun && weRunValue && !we_run_share_button_hide)
        }
    },

    onLoad(e) {
        this.active_id = e.active_id
        this.types = Number(e.types)
        this.success_info = e.info
        this.number = Number(e.num)
        if (e.seconds) {
            const seconds = Number(e.seconds)
            if (!isNaN(seconds) && seconds >= 60) this.time = Math.floor(seconds / 60)
        }

        this.getActivityDetails()
    },

    methods: {
        async getActivityDetails() {
            let details = app.globalData['activity_detail']

            if (!details || details.active_id !== this.active_id) {
                this.$uni.showLoading()
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.active_id
                    }
                })
                uni.hideLoading()

                details = res.data.active_details
            }

            if (details?.conf?.active?.we_run_share_button_hide) this.we_run_share_button_hide = true

            this.loading = false
        },

        shareToWeRun() {
            const {shareTypeId, unit} = this.weRunShareType
            const record = {typeId: shareTypeId}
            record[unit] = this.weRunValue

            this.$wx.shareToWeRun({
                recordList: [record],
                success: () => {
                    this.$uni.showToast('分享成功', 'success')
                    this.have_share_to_werun = true
                }
            })
        }
    }
}
</script>

<style>
.add-success {
    padding-top: 10vh;
}

.add-success .icon {
    padding-bottom: 50px;
}

.success-btn {
    width: 250px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
}

.mini-navigators {
    width: 160px;
    margin: 5px auto;
}

.mini-navigator {
    width: 50%;
    line-height: 40px;
}
</style>
