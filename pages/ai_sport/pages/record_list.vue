<template>
    <view class="page bg-background">
        <view v-if="list_count" class="flex-kai bg-white">
            <view class="color-sub font14 p10">共{{ list_count }}条运动记录</view>
            <navigator v-if="!userid" open-type="navigateBack" class="color-primary font14 p10">
                <text>去运动</text>
                <uni-icons type="forward" color="#2d8cf0"/>
            </navigator>
        </view>

        <view class="list">
            <view class="item p10 bg-white color-content" v-for="item in list" :key="item.id">
                <view class="flex-kai">
                    <view>{{ item['motion_types_text'] }}</view>
                    <view>
                        <text class="iconfont color-sub icon-time"></text>
                        <text style="padding-left: 2px;">{{ item.seconds_time || '0秒' }}</text>
                    </view>
                </view>
                <view class="flex-kai" style="padding: 5px 0;">
                    <view>数量: {{ item.num || 0 }}</view>
                    <view v-if="item.integral_num">
                        <text>{{ item.integral_num }}</text>
                        <text class="font14 color-sub" style="padding-left: 2px;">{{ integralUnit }}</text>
                    </view>
                </view>
                <view class="clear clearfix">
                    <view class="color-sub fr font12">{{ item.create_time }}</view>
                </view>
            </view>
        </view>

        <uni-load-more v-if="in_load" status="loading"/>
        <uni-load-more v-if="is_last_page && list.length > 5" status="noMore"
                       :contentText="{contentnomore: '我是有底线的'}"/>

        <view v-if="!list.length && !in_load" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub pb10">暂无运动记录</view>
            <view v-if="!userid" class="flex-all-center pt10">
                <navigator open-type="navigateBack" class="to-sport-btn text-center bg-primary color-white">
                    去运动
                </navigator>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'


export default {
    data() {
        return {
            in_load: true,
            list: [],
            load_page: 1,
            is_last_page: false,
            list_count: 0,
            userid: '',
            integralUnit: '积分'
        }
    },
    onLoad(e) {
        this.$uni.showLoading()
        this.active_id = e.active_id
        if (e.types) this.types = e.types
        if (e.userid) this.userid = e.userid
        if (e.nickname) this.$uni.setNavigationBarTitle(`${e.nickname}的运动记录`)
        login.uniLogin(err => {
            uni.hideLoading()
            if (err?.errMsg) return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})

            this.init()
        })
    },
    onReachBottom() {
        !this.is_last_page && !this.in_load && this.getList()
    },
    methods: {
        async init() {
            await this.getActiveDetails()
            await this.getList()
        },

        async getActiveDetails() {
            let details = app.globalData['activity_detail']

            if (!details || details.active_id !== this.active_id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.active_id
                    }
                })

                details = res?.data?.active_details
            }

            this.integralUnit = details?.conf?.active?.integral?.unit || '积分'
        },

        async getList() {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
            }

            this.in_load = true

            const data = {
                active_id: this.active_id,
                page: this.load_page,
                perpage: 10
            }
            if (this.types) data.types = this.types
            if (this.userid) data.userid = this.userid

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.ai_motion.user/user_exchange_list',
                data
            })

            this.in_load = false

            const res_data = res?.data?.['exchange_list']
            if (!res_data) {
                this.is_last_page = true
                return false
            }

            this.load_page++
            const list = res_data.data || []
            list.forEach(v => {
                v.seconds_time = this.formatSeconds(v.seconds)
                const delKeys = ['active_id', 'seconds', 'shopid', 'status', 'update_time', 'userid']
                delKeys.forEach(key => delete v[key])
            })
            this.list = [...this.list, ...list]
            this.is_last_page = res_data.is_last_page
            this.list_count = res_data.total
        },

        formatSeconds(seconds) {
            const result = parseInt(seconds)
            if (result < 60) return result + '秒'
            const h = Math.floor(result / 3600).toString().padStart(2, '0')
            const m = Math.floor((result / 60 % 60)).toString().padStart(2, '0')
            const s = Math.floor((result % 60)).toString().padStart(2, '0')

            if (h === '00') return `${m}分${s}秒`
            return `${h}小时${m}分${s}秒`
        }
    }
}
</script>

<style>
.page {
    min-height: 100vh;
}

.list {
    padding: 10px 0;
}

.item {
    margin: 10px;
    border-radius: 10px;
}

.to-sport-btn {
    width: 200px;
    line-height: 40px;
    border-radius: 20px;
}
</style>
