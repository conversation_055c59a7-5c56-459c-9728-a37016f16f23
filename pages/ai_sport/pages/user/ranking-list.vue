<template>
    <view class="page bg-background">

        <xwy-ad v-if="show_AD" :ad_type="3"></xwy-ad>

        <template v-if="top_rank_category_list.length > 1 && !team_id">
            <view class="type-bar flex-row bg-white">
                <view class="type-bar-item text-center font14"
                      :class="{'type-bar-item-active': item.id === type_id}"
                      v-for="item in top_rank_category_list" :key="item.id"
                      :style="{width: 100 / top_rank_category_list.length + '%'}"
                      @click="typeIdChange(item.id)">
                    {{ item.name }}
                </view>
            </view>

            <view style="height: 40px;"></view>
        </template>

        <view v-if="type_id !== 38 && top_rank_text && top_rank_text.rules" class="font12 p10"
              style="color: #e19898;" @click="copyId">{{ top_rank_text.rules }}
        </view>

        <top-banner v-if="top_rank_banner.length" :list="top_rank_banner"></top-banner>

        <template v-if="podiumList.length">
            <ranking-podium :list="podiumList"/>
        </template>

        <view v-if="myPositionNumShow" class="color-sub font14 text-center pt5">
            <template v-if="!show_AD">
                我的
                <template v-if="isTeamRanking">队伍</template>
                排名: {{ my_position_num === -1 ? '未上榜' : my_position_num }}
            </template>
            <text class="pl5" v-if="total_count">
                (共{{ total_count }}{{ isTeamRanking ? '队伍' : '人' }})
            </text>
        </view>

        <view class="list">
            <view v-for="(item, index) in list" :key="index">
                <xwy-ad v-if="show_AD && index === 0" :ad_type="66"></xwy-ad>

                <view v-if="!limit_show_num || index < limit_show_num" class="item flex-row bg-white"
                      @click="clickItem(item)">
                    <view v-if="type_id !== 38" class="index flex-all-center" @click.stop="copyUserid(item)">

                        <image class="top-3" v-if="item.rankingNumberImage" :src="item.rankingNumberImage"/>
                        <view v-else class="color-sub font14">{{ index + 1 }}</view>
                    </view>

                    <view class="flex-all-center">
                        <view v-if="item.headimg" :style="{paddingLeft: type_id === 38 ? '10px' : '0'}">
                            <image mode="aspectFill" class="headimg" :src="item.headimg"/>
                        </view>
                    </view>

                    <view class="middle">
                        <view class="name color-title ellipsis">{{ item.nickname }}</view>
                        <view v-if="isTeamRanking" class="color-sub font12 pt5">查看队内排行榜</view>
                        <view v-if="type_id === 60" class="color-sub font12 pt5">查看队伍排行榜</view>
                    </view>

                    <view v-if="item.show_value" class="right flex-column flex-all-center color-red font14">
                        <view class="right-item text-right">
                            <view>
                                <text>{{ item.value }}</text>
                                <text class="font12" style="padding-left: 2px;">{{ item.unit }}</text>
                            </view>
                            <view v-if="item.show_like" class="pt5">
                                <text class="iconfont icon-love color-red"
                                      style="position: relative; top: 1px; left: -1px;"
                                      @click.stop="like(index)">
                                </text>
                                <text v-if="item.likes" class="color-red" @click.stop="like(index)">
                                    {{ item.likes }}
                                </text>
                            </view>
                        </view>

                    </view>
                </view>

                <xwy-ad v-if="show_AD && index !== 0 && ((index + 1) % 10 === 0)" :ad_type="66"></xwy-ad>

            </view>
        </view>

        <view v-if="!list.length && !loading" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无排行数据</view>
        </view>

        <uni-load-more v-if="loading" status="loading"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && list.length > 5"
            status="noMore"
            :contentText="{contentnomore: '我是有底线的'}"
        ></uni-load-more>
        <uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>

        <uni-popup ref="like_popup" type="center">
            <view class="like_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('like_popup')">
                    <uni-icons type="close" size="24" color="#b2b3b7"/>
                </view>
                <view class="bg-primary color-white" style="padding: 30px 20px 20px;">

                    <icon :type="like_status === 'success' ? 'success' : 'clear'"
                          size="80" color="#ffffff"></icon>
                    <view class="font18">点赞{{ like_status === 'success' ? '成功' : '失败' }}</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ like_info }}
                    </view>
                </view>
                <xwy-ad v-if="popup_show && show_AD" :ad_type="3"></xwy-ad>
            </view>
            <view class="pt5">
                <xwy-ad v-if="popup_show && show_AD" :ad_type="66"></xwy-ad>
            </view>
        </uni-popup>


    </view>
</template>

<script>
const app = getApp()
import getTeamCategoryRankingApi from "@/utils/api/get-team-category-ranking"

export default {
    data() {
        return {
            id: '',
            loading: true,
            total_count: 0,
            top_rank_text: null,
            my_position_num: -1,
            list: [],
            load_page: 1,
            is_last_page: false,
            top_rank_banner: [],
            show_AD: false,
            unit: '能量',
            integralUnit: '积分',
            like_status: '',
            like_info: '',
            popup_show: false,
            limit_show_num: null,
            top_rank_category_list: [],
            type_id: 13,
            team_id: null,
            category_id: null
        }
    },

    computed: {
        isTeamRanking() {
            const teamTypes = [2, 38, 40]
            return teamTypes.includes(this.type_id)
        },

        podiumList() {
            const {type_id, list, show_AD} = this
            if (type_id === 38 || !list.length || show_AD) return []

            return this.list.slice(0, 3).map(item => ({
                headimg: item.headimg,
                nickname: item.nickname,
                value: item.show_value ? `${item.value}${item.unit}` : ''
            }))
        },

        myPositionNumShow() {
            // 队伍排行榜按分类查看，不显示用户队伍当前排名，因为接口获取的是所有的队伍排名，显示的列表是前端通过指定队伍分类ID筛选的
            if (this.type_id === 40 && this.category_id) return false

            //【38】队伍排行榜（按照设定的队伍序号的顺序从小到大排序）
            if (this.type_id === 38) return false

            //【60】队伍分类排行榜，排行榜数据是前端通过所有队伍的积分汇总到分类里面再做排名的，而且显示"我的分类排名: 1"也不行，有不想搞这个队伍分类的名称自定义设置，所以干脆就不显示了
            if (this.type_id === 60) return false

            return !!this.list.length
        }
    },

    onLoad(e) {
        this.id = e.id
        if (e.team_id) {
            this.team_id = Number(e.team_id)
            if (e.team_name) this.$uni.setNavigationBarTitle(`${e.team_name}队内排行榜`)
        }

        if (e.category_id) {
            this.type_id = 40
            this.category_id = Number(e.category_id)
            if (e.category_name) this.$uni.setNavigationBarTitle(`${e.category_name}排行榜`)
        }

        this.$uni.showLoading()
        this.$login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.init()
        })
    },

    onPullDownRefresh() {
        this.reloadList().finally(() => uni.stopPullDownRefresh())
    },

    onReachBottom() {
        if (this.is_last_page || this.loading) return
        this.load_page++
        this.loadList()
    },

    methods: {
        async init() {
            await this.getActivityDetails()
            await this.getList()
            this.loading = false
            uni.hideLoading()
        },

        async fetchActivityDetails() {
            const details = app.globalData['activity_detail']
            if (details && details.active_id === this.id) return details

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details',
                data: {
                    active_id: this.id
                }
            })
            return res?.data?.active_details
        },

        async getActivityDetails() {
            const details = await this.fetchActivityDetails()
            if (!details) return this.$uni.showModal('活动获取失败', {success: () => this.$uni.navigateBack()})

            await this.setPageData(details)
        },

        async setPageData(details) {
            const active = details.conf?.active || {}
            if (active.energy_unit) this.unit = active.energy_unit
            if (active.integral?.unit) this.integralUnit = active.integral.unit
            if (active.top_rank_banner?.length) this.top_rank_banner = active.top_rank_banner
            if (active.ai_sport_activity_reward_mode === 'integral' && !this.category_id) this.type_id = 48

            const rank_set = details.rank_set || {}
            if (rank_set.team_group_open) {
                // this.top_rank_category_list.push({id: 2, name: '队伍'})
                if (!this.team_id && !this.category_id) await this.getTypeList()
            }
            if (!rank_set.closed_AD) this.show_AD = true
            if (rank_set.limit_show_num) this.limit_show_num = rank_set.limit_show_num
        },

        async getTypeList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.step/top_rank_category_list',
                data: {
                    active_id: this.id
                }
            })

            const top_rank_category_list = res?.data?.top_rank_category_list
            if (top_rank_category_list?.length) {
                // 有今日步数排行榜的，当做是活动没有配置JSON排行榜类型，输出健步走默认排行榜类型
                if (top_rank_category_list.find(item => item.id === 1)) return
                this.type_id = top_rank_category_list[0].id
                this.top_rank_category_list = top_rank_category_list
            }
        },

        typeIdChange(id) {
            if (id === this.type_id) return
            this.type_id = id
            this.reloadList()
        },


        async loadList() {
            if (!this.loading && !this.is_last_page) {
                this.loading = true
                await this.getList()
                this.loading = false
            }
        },

        async reloadList() {
            this.load_page = 1
            this.loading = true
            await this.getList()
            this.loading = false
        },

        /**
         * @description 获取上周一的日期
         * @return {string} 上周一的日期 yyyy-MM-dd
         * */
        getLastMonday() {
            const today = new Date()
            const dayOfWeek = today.getDay()
            const diff = (dayOfWeek + 6) % 7
            today.setDate(today.getDate() - diff - 7)
            today.setHours(0, 0, 0, 0)

            return this._utils.unitTimeToDate(today.getTime())
        },


        async getList() {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
                this.my_position_num = -1
                this.total_count = 0
                this.top_rank_text = null
            }

            if (this.type_id === 60) return this.getTeamCategoryRanking()

            const data = {
                active_id: this.id,
                top_rank_types: this.type_id,
                page: this.load_page,
                perpage: 20
            }
            if (this.team_id) data.team_id = this.team_id

            if (this.category_id) {
                // data.category_id = this.category_id
                data.perpage = 1000
            }

            // 查看某日的积分排行榜
            if (this.type_id === 58) {
                // 这里是查看昨日的
                data.date = this._utils.getDay(-1, true)
            }

            // 查看某周的积分排行榜
            if (this.type_id === 59) {
                // 这里是查看上周的
                // 获取上周周一的日期
                data.date = this.getLastMonday()
            }


            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.step/top_rank_list',
                data
            })


            if (!res?.data) {
                this.is_last_page = true
                return
            }

            if (res.data.rank_types_text) this.top_rank_text = res.data.rank_types_text

            if (res.data.top_rank_list) {
                const _data = res.data.top_rank_list

                if (_data.position_num) this.my_position_num = _data.position_num

                const list_data = _data?.list

                if (!list_data) {
                    this.is_last_page = true
                    return
                }

                let list = list_data.data || []

                if (this.type_id === 40 && this.category_id) {
                    list = list.filter(item => item.team_details?.category_id === this.category_id)
                }

                const new_list = this.listDataInit(list)

                this.list = [...this.list, ...new_list]
                this.is_last_page = list_data.is_lastpage
                this.total_count = list_data.total
            } else {
                this.is_last_page = true
            }
        },

        async getTeamCategoryRanking() {
            const res = await getTeamCategoryRankingApi.getRankingList(this.id)

            const {list} = res

            this.list = this.listDataInit(list)
            this.is_last_page = true
            // this.total_count = list.length
            // this.my_position_num = position_num
            this.top_rank_text = {
                rules: this.top_rank_category_list.find(item => item.id === 60).text || '根据队伍分类的积分高低排名'
            }
        },

        listDataInit(list) {
            return list.map((item, index) => ({
                id: item.id || item.team_id || item.category_id,
                userid: item.userid || null,
                headimg: this.getHeadimg(item),
                nickname: this.getNickname(item),
                show_value: this.type_id !== 38,
                value: this.getValue(item),
                unit: this.getUnit(),
                likes: item.likes || 0,
                show_like: this.type_id === 13,
                rankingNumberImage: this.load_page === 1 && index < 3 ? `https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/no-${index + 1}.png` : ''
            }))
        },

        getValue(v) {
            // 最日、上周 积分排行榜
            if ([58, 59].includes(this.type_id)) return v.total_integral

            if (this.type_id === 38) return ''
            if (this.type_id === 40) return v.allIntegral || 0
            if (this.type_id === 48) return v.integral_all || 0
            if (this.type_id === 60) return v.integral || 0
            return v.water || 0
        },

        getUnit() {
            if ([40, 48, 58, 59, 60].includes(this.type_id)) return this.integralUnit
            return this.unit
        },

        getHeadimg(v) {
            if (this.isTeamRanking) return v.conf?.logo || v.team_details?.conf?.logo || ''
            return v.headimg || v.user_details?.headimg || ''
        },

        getNickname(v) {
            if (this.type_id === 60) return v.name
            if (this.isTeamRanking) return v.name || v.team_details?.name || ''
            const must_submit = v.must_submit || v.user_details?.must_submit
            return must_submit?.[0]?.value || ' '
        },


        async like(index) {
            const item = this.list[index]
            this.$uni.showLoading('点赞中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.likeit/like_for_friend',
                data: {
                    active_id: this.id,
                    userid: item.userid
                }
            })
            uni.hideLoading()

            let like_status = 'success',
                like_info = '点赞成功！'

            if (res?.['status']) {
                item.likes = item.likes || 0
                item.likes++
            } else {
                like_status = 'error'
                like_info = '点赞失败！'
            }
            if (res?.['info']) like_info = res['info']
            this.like_status = like_status
            this.like_info = like_info

            this.uniPopupOpen('like_popup')
        },

        uniPopupOpen(ref) {
            this.$refs[ref].open()
            this.popup_show = true
        },

        uniPopupClose(ref) {
            this.$refs[ref].close()
            this.popup_show = false
        },

        copyUserid(item) {
            const id = this.isTeamRanking ? item.id : item.userid
            uni.setClipboardData({
                data: id.toString(),
                success: () => uni.hideToast()
            })
        },

        copyId() {
            uni.setClipboardData({
                data: this.id,
                success: () => uni.hideToast()
            })
        },

        clickItem(item) {
            if (this.type_id === 60) return this.$uni.navigateTo(`./ranking-list?id=${this.id}&category_id=${item.id}&category_name=${item.nickname}`)

            if (this.isTeamRanking && item.id) this.$uni.navigateTo(`./ranking-list?id=${this.id}&team_id=${item.id}&team_name=${item.nickname}`)
        }
    }
}
</script>

<style scoped lang="scss">
@import "@/pages/ranking-list/ranking-list.scss";

.type-bar {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;

    .type-bar-item {
        height: 40px;
        line-height: 38px;
    }

    .type-bar-item-active {
        border-bottom: 2px solid #5cadff;
        color: #5cadff;
    }
}
</style>
