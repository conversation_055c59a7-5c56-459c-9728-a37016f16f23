<template>
    <view class="page bg-background">

        <screen-picture
            v-if="screen_pic_show"
            :hide="screen_pic_hide"
            :image-src="screen_pic"
            :time="screen_pic_count_down"
            :show-button="true"
            @skipScreen="skipScreen"
        />


        <activity-delete-tips v-if="error" :activeId="id"/>

        <view v-if="detail && detail.active_id">
            <image v-if="detail.logo" class="logo" mode="widthFix" :src="detail.logo"/>

            <icon-list-show
                :active-set="detail.conf.active"
                :admin="is_my_activity"
                :icon-list="right_icons_set"
                :is-joining="is_joining"
                :rank-set="rank_set"
                @iconItemClick="iconItemClick"
            />
           
            <view style="width: 100%; height: 5px;"></view>
            <template v-if="checked && is_joining && detail.conf.active.submit_times">
                <view class="text-center color-sub font14">
                    每日可运动{{ detail.conf.active.submit_times }}次，你今日已运动{{ today_sport_submit_count }}次，还可运动{{ detail.conf.active.submit_times - today_sport_submit_count }}次。
                </view>
            </template>
            
            <view class="sport-list flex-row flex-wrap">
                <view class="sport-item" v-for="(item, index) in detail.conf.AI_motion.motion_list" 
                      :key="index" hover-class="navigator-hover" @click="toStart(item)">
                    <image v-if="item.logo" class="sport-logo" :src="item.logo" mode="aspectFill"/>
                    <view v-else class="sport-no-logo ellipsis">{{ item.title || item.name }}</view>
                </view>
                <view v-if="step_open" class="sport-item" hover-class="navigator-hover" 
                      @click="toStart('step')">
                    <image v-if="step_set.logo" class="sport-logo" :src="step_set.logo" mode="aspectFill"/>
                    <view v-else class="sport-no-logo ellipsis">{{ step_set.name }}</view>
                </view>
            </view>

            <view v-if="technology_support" class="flex-all-center" style="padding-top: 30px;">

                <navigator
                    v-if="technology_support.news_id"
                    :url="'/pages/news/preview?id=' + technology_support.news_id + '&type=support'"
                    class="text-center font14 color-sub p10"
                >{{ technology_support.button_text }}</navigator>
                <view v-else class="text-center font14 color-sub p10">
                    {{ technology_support.button_text }}
                </view>
            </view>

            <view v-if="!rank_set['closed_user_center']" class="flex-all-center">
                <navigator url="/pages/user/user" class="p10 color-sub font14">
                    个人中心
                </navigator>
            </view>

            <template v-if="(!rank_set.closed_AD) && !screen_pic_show">
                <xwy-ad :activity_id="id" :ad_type="3"/>
                <xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>
            </template>



        </view>

        <view v-if="join_popup_show" class="join-popup flex-all-center bg-white" @touchmove.stop.prevent="">
            <view>
                <view class="join-popup-c bg-white">
                    <view class="text-center font18 color-content p10">
                        <template v-if="update_attend_details">修改报名信息</template>
                        <template v-else>参加活动</template>
                    </view>


                    <template v-if="must_submit.length">
                        <view v-for="(item, index) in must_submit" :key="index">

                            <input v-if="item.types === 1" class="join-input" v-model="item.value"
                                   :placeholder="'请输入' + item.title + (item.rules === 1 ? ' (必填)' : '')"/>

                            <input v-if="item.types === 3" class="join-input" v-model="item.value"
                                    type="number" maxlength="11"
                                   :placeholder="'请输入' + item.title + (item.rules === 1 ? ' (必填)' : '')"/>
                            <picker v-if="item.types === 2" :range="item.options" range-key="text" 
                                    @change="mustValueChange($event, index)">
                                <view class="join-input flex-kai">
                                    <view v-if="!item.value" class="color-sub">
                                        请选择{{ item.title }}{{ item.rules === 1 ? ' (必选)' : '' }}
                                    </view>
                                    <view v-if="item.value">{{ item.value }}</view>
                                    <text class="iconfont icon-more color-disabled font18"/>
                                </view>
                            </picker>
                        </view>
                    </template>

                    <view v-if="rank_set.team_group_open && !user_details.team_id" 
                          class="join-input flex-kai" @click="toChooseTeam">
                        <view v-if="!choose_team.id" class="color-sub">
                            <text>请选择队伍</text>
                            <text v-if="detail.conf.active.team_required" class="pl5">(必选)</text>
                        </view>
                        <view v-if="choose_team.id">{{ choose_team.name || choose_team.id }}</view>
                        <text class="iconfont icon-more color-disabled font18"/>
                    </view>


                    <view class="join-popup-btns flex-row text-center font18">
                        <view class="join-popup-btn-cancel color-sub" @click="cancelJoin">取消</view>
                        <view class="join-popup-btn-confirm color-success" @click="joinAjax">确定</view>
                    </view>
                </view>

                <template v-if="!rank_set.closed_AD">
                    <view class="pt5">
                        <xwy-ad :ad_type="66"/>
                    </view>
                    <xwy-ad :ad_type="3"/>
                </template>
            </view>


        </view>

        <active-share ref="activeShare"/>

        <uni-popup ref="activity_detail" type="center" @touchmove.stop.prevent="">
            <view v-if="detail && detail.conf && detail.conf.active" 
                  class="uni-popup-info detail-popup bg-white">


                <scroll-view :scroll-y="true" class="detail-popup-detail" 
                             style="max-height: calc(100vh - 300px); padding: 10px 0;">

                    <view class="p10 bdb-10 bg-white">
                        <view class="text-center color-sub pb5">- 活动信息 -</view>
                        <activity-logo-title-time :details="detail" hide-logo hide-share/>
                    </view>
                    
                    <view v-if="!detail.conf.active.activity_rules_hide" class="p10 bdb-10 bg-white">
                        <view class="text-center color-sub pb5">- 活动规则 -</view>

                        <view class="color-content font16">
                            活动参与方式：
                            <template v-if="detail.conf.active.enter_types === 1">
                                自由报名参与活动
                            </template>
                            <template v-if="detail.conf.active.enter_types === 2">
                                需要输入密码才能报名
                            </template>
                            <template v-if="detail.conf.active.enter_types === 3">
                                报名需要审核通过才能参与活动
                            </template>
                        </view>

                        <view v-if="detail.conf.active.submit_times" class="color-content font16">
                            每日最大运动次数: {{ detail.conf.active.submit_times }}次
                        </view>
                        
                        <view v-if="detail.conf.active.max_daily_energy && rewardMode === 'water'"
                              class="color-content font16">
                            每日获得{{ energy_unit }}上限: {{ detail.conf.active.max_daily_energy }}{{ energy_unit }}
                        </view>

                        <view v-if="detail.conf.active.daily_ai_sports_max_integral && rewardMode === 'integral'" class="color-content font16">
                            每日获得{{ integralUnit }}上限: {{ detail.conf.active.daily_ai_sports_max_integral }}{{ integralUnit }}
                        </view>
                        
                        <view v-if="detail.conf.active.per_seconds" class="color-content font16">
                            单次运动时间限制: {{ detail.conf.active.per_seconds }}秒
                        </view>

                        <view v-if="detail.conf.AI_motion && detail.conf.AI_motion.motion_list && detail.conf.AI_motion.motion_list.length" 
                              class="color-content font16" 
                              v-for="(item,index) in detail.conf.AI_motion.motion_list" :key="index">

                            <template v-if="rewardMode === 'integral'">
                                {{ item.title || item.name }}{{ item.integral_reward.num }}{{ item.types === 5 ? '秒' : '次' }}奖励{{ item.integral_reward.integral }}{{ integralUnit }}
                            </template>
                            <template v-else>
                                {{ item.title || item.name }}1{{ item.types === 5 ? '秒' : '次' }}奖励{{ item.water }}{{ energy_unit }}
                            </template>
                        </view>

                        <template v-if="step_set">
                            <view class="color-content font16">
                                <template v-if="rewardMode === 'integral'">
                                    微信运动步数{{ detail.conf.active.integral.exchange_step }}步兑换1{{ integralUnit }}
                                </template>
                                <template v-else>
                                    微信运动步数{{ step_set.exchange_rate }}步兑换1{{ energy_unit }}
                                </template>
                            </view>
                            <view v-if="step_set.min_num" class="color-content font16">
                                最低兑换步数：{{ step_set.min_num }}步
                            </view>
                            <view v-if="step_set.max_num" class="color-content font16">
                                最高兑换步数：{{ step_set.max_num }}步
                            </view>
                        </template>
                    </view>

                    <view v-if="detail.content || news_detail" class="p10 bdb-10 bg-white">
                        <view class="text-center color-sub pb5">- 活动说明 -</view>
                        <view class="color-content font16">
                            <template v-if="detail.content && !news_detail">

                                <rich-text :nodes="detail.content" space="nbsp" :selectable="true"></rich-text>
                            </template>
                            <template v-if="news_detail">
                                <template v-if="news_detail.content">
                                    <u-parse :content="news_detail.content"/>
                                </template>
                            </template>
                        </view>
                    </view>
                </scroll-view>
            </view>

            <view v-if="!loading && !rank_set.closed_AD" style="padding-top: 5px;">
                <xwy-ad :ad_type="66"/>
                <xwy-ad :ad_type="3"/>
            </view>

            <view class="flex-all-center" @click="uniPopupClose('activity_detail')">
                <uni-icons type="close" size="34" color="#ffffff"/>
            </view>
        </uni-popup>

        <uni-popup ref="my_info" type="center" @touchmove.stop.prevent="">
            <view v-if="detail && detail.conf && detail.conf.active" class="uni-popup-info bg-white p20">
                <view class="text-center color-sub pb5">- 活动报名信息 -</view>
                <view class="text-center p10">

                    <image class="headimg" mode="aspectFill" 
                           :src="headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"/>
                    <view>
                        <text class="color-primary" @click="updateHeadimg">更改头像</text>
                    </view>
                </view>


                <view class="color-content font16 ptm5" v-for="(item, index) in must_submit" :key="index" 
                      @click="updateAttendDetailShow">
                    <text>
                        {{ item.title }}：
                        <template>
                            <template v-if="item.value">{{ item.value }}</template>
                            <template v-else>
                                <template v-if="item.types === 1">未填写</template>
                                <template v-if="item.types === 2">未选择</template>
                            </template>
                        </template>
                    </text>
                    <text v-if="is_joining" class="iconfont icon-edit color-sub pl5"></text>
                </view>
                <view v-if="rank_set.team_group_open" class="color-content font16 ptm5">
                    <text>队伍：{{ (user_details.team_details && user_details.team_details.name) || (user_details.team_details && user_details.team_details.id) || '未加入队伍' }}</text>
                    <template v-if="!user_details.team_details || !user_details.team_details.id">
                        <text class="color-primary pl5" @click="updateAttendDetailShow">加入队伍</text>
                        <uni-icons type="forward" color="#2d8cf0" @click="updateAttendDetailShow"/>
                    </template>
                    <template v-if="user_details.team_details && user_details.team_details.id && detail.conf.active.allow_change_team">
                        <text class="color-primary pl5" @click="updateAttendDetailShow">更换队伍</text>
                        <uni-icons type="forward" color="#2d8cf0" @click="updateAttendDetailShow"/>
                    </template>
                </view>

                <template>

                    <navigator v-if="detail.conf.active.ai_sport_activity_reward_mode === 'integral'"
                          class="color-content font16 ptm5"
                               :url="`/pages/sign_in/integral_record_list?active_id=${id}&unit=${integralUnit}`">
                        {{ integralUnit }}：{{ user_details.integral_all || 0 }}
                    </navigator>

                    <view v-else class="color-content font16 ptm5">
                        {{ energy_unit }}：{{ user_details.water || 0 }}
                        <text v-if="today_energy" class="pl5 color-sub font12">
                            (今日获得{{ today_energy }}{{ energy_unit }})
                        </text>
                    </view>
                </template>
            </view>
            <view v-if="popup_open && !rank_set.closed_AD" style="padding-top: 5px;">
                <xwy-ad :ad_type="66"/>
                <xwy-ad :ad_type="3"/>
            </view>

            <view class="flex-all-center" @click="uniPopupClose('my_info')">
                <uni-icons type="close" size="34" color="#ffffff"/>
            </view>
        </uni-popup>


        <template v-if="password_dialog_show">
            <uni-popup
                ref="input_password"
                type="dialog"
                mode="input"
                :is-mask-click="false"
                @maskClick="copy(id)"
            >
                <uni-popup-dialog
                    mode="input"
                    title="活动密码"
                    :value="password"
                    placeholder="请输入活动密码"
                    @confirm="passwordInputConfirm"
                    @close="passwordInputClose"
                ></uni-popup-dialog>
            </uni-popup>
        </template>

        <expiration-reminder ref="expirationReminder"/>

        <uni-popup v-if="step_open" ref="stepExchangeResultPopup">
            <view class="step-exchange-result-popup text-center">
                <view class="bg-primary color-white" style="padding: 50px 20px 20px;">

                    <icon :type="step_exchange_result.result === 'success' ? 'success' : 'warn'" size="80" color="#ffffff"></icon>
                    <view class="font18">{{ step_exchange_result.result === 'success' ? step_exchange_result.info : '兑换失败' }}</view>
                </view>
                <view class="bg-white" style="padding: 20px 20px 30px;">
                    <template v-if="step_exchange_result.result === 'success'">
                        <template v-if="step_exchange_result.show_text && step_exchange_result.show_text.length">
                            <view v-for="(item, index) in step_exchange_result.show_text" :key="index"
                                  :style="'color: ' + item.color">
                                {{ item.text }}
                            </view>
                        </template>
                    </template>
                    <view v-else class="color-content">{{ step_exchange_result.info }}</view>
                </view>
            </view>
            <view class="flex-all-center" @click="$refs.stepExchangeResultPopup.close()">
                <uni-icons type="close" size="28" color="#ffffff"/>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import my_storage from '@/utils/storage.js'
import base64 from '@/utils/base64.js'
import activity_tool from '@/utils/acyivity_tool.js'
import iconListShow from '@/pages/ai_sport/components/icon-list-show.vue'
import config from '@/pages/ai_sport/config'


let interval

export default {
    components: {iconListShow},
    data() {
        return {
            evn_version: app.globalData['evn_version'],
            show_tab: false,
            from_tab: false,
            loading: true,
            screen_pic: '',
            screen_pic_show: false,
            screen_pic_hide: false,
            screen_pic_count_down: null,
            id: '',
            userid: '',
            is_my_activity: false,
            detail: {},
            user_details: {},
            error: '',
            join_popup_show: false,
            update_attend_details: false,
            headimg: '',
            username: '',
            checked: 0,
            is_joining: false,
            password: '',
            technology_support: false,
            must_submit: [],
            headimg_plugin: [],
            password_dialog_show: false,
            popup_open: false,
            news_detail: null,
            platform: uni.getSystemInfoSync().platform,
            list: [],
            list_total: 0,
            load_page: 1,
            is_last_page: false,
            like_status: '',
            like_info: '',
            today_sport_submit_count: 0,
            icon_list_show_more: false,
            step_exchange_result: {},
            choose_team: {
                id: null,
                name: ''
            },
            today_energy: 0,

            right_icons_set: [],
            rank_set: {}
        }
    },
    
    computed: {
        energy_unit() {
            return this.detail.conf?.active?.energy_unit || '能量'
        },

        integralUnit() {
            return this.detail.conf?.active?.integral?.unit || '积分'
        },

        rewardMode() {
            return this.detail.conf?.active?.ai_sport_activity_reward_mode || 'water'
        },

        integralOrWaterUnit() {
            return this.rewardMode === 'integral' ? this.integralUnit : this.energy_unit
        },
        
        step_open() {
            return this.detail.conf?.active?.ai_sport_extend_types?.step?.open
        },
        step_set() {
            if (!this.step_open) return null
            const {exchange_rate, min_num, max_num, ai_sport_extend_types} = this.detail.conf?.active
            const {name, logo} = ai_sport_extend_types.step
            return {exchange_rate, min_num, max_num, name, logo}
        }
    },
    
    onLoad(e) {

        console.log('活动详情页面路径参数', e)


        // #ifdef H5
        const a = true
        if (a) return this.$uni.showModal('请在小程序内打开', {success: () => uni.navigateBack()})
        // #endif

        if (e.from && e.from === 'tab') {
            this.from_tab = true
            this.$uni.hideHomeButton()
        }

        if (uni.getLaunchOptionsSync().scene === 1154) return this.getSimpleDetail(e.id)

        e.screen_pic ? this.screenPicShow(e.screen_pic) : this.$uni.showLoading('数据加载中...')

        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            if (this.from_tab) this.show_tab = true

            if (e.scene) return this.analysisScene(e.scene)

            if (!e.id) {
                this.loading = false
                this.error = '请指定活动id'
                return uni.hideLoading()
            }
            
            this.id = e.id
            this.userid = app.globalData['userid']
            this.getDetail()
        })
    },


    onShareAppMessage() {
        this._utils.shareCopyActivityId(this.detail)

        let url = '/pages/ai_sport/pages/user/detail?id=' + this.id
        if (this.detail?.conf?.active?.screen_pic) url += `&screen_pic=${this.detail.conf.active.screen_pic}`
        if (this.from_tab) url += '&from=tab'

        return {
            title: this.detail.conf?.active?.share_title || this.detail.name || '',
            path: url,
            imageUrl: this.detail.conf?.active?.share_image || this.detail.logo || ''
        }
    },

    onShareTimeline() {
        return {
            title: this.detail.name,
            imageUrl: this.detail.logo || ''
        }
    },

    methods: {
        toTopList() {
            uni.navigateTo({
                url: './ranking-list?id=' + this.id
            })
        },
        
        


        async getSimpleDetail(id) {
            this.$uni.showLoading('数据加载中...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details_simple',
                data: {
                    active_id: id
                }
            })
            this.loading = false
            uni.hideLoading()

            if (!res?.data?.active_details) {
                this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                return false
            }


            if (res.data.active_more_data) {
                const active_more_data = res.data.active_more_data
                this.active_more_data = active_more_data
                if (active_more_data.technology_support) {
                    this.technology_support = res.data.active_more_data.technology_support
                }
                if (active_more_data['active_conf_set']) {
                    const active_conf_set = active_more_data['active_conf_set']
                    if (active_conf_set?.headimg_plugin?.length) {
                        this.headimg_plugin = active_conf_set.headimg_plugin
                    }
                }
            }


            const detail = res.data.active_details
            this.detail = detail
            my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)

            if (detail.conf) {
                const conf = detail.conf

                if (!this.screen_pic && conf.active?.screen_pic) this.screenPicShow(conf.active.screen_pic)
                
                if (conf.must_submit) {
                    const must_submit = JSON.parse(JSON.stringify(conf.must_submit))
                    if (must_submit.length) {
                        must_submit.forEach(v => v.value = v.value || '')
                        this.must_submit = must_submit
                    }
                }
            }

            if (detail.rank_set) {
                const rank_set = detail.rank_set
                // 是否纯净版，纯净版显示tab栏，隐藏返回首页按钮
                if (rank_set['shield_other'] && !this.show_tab) this.show_tab = true
            }


            this.addLookRecords()

            if (detail.name) {
                uni.setNavigationBarTitle({
                    title: detail.name
                })
            }
        },
        
        
        screenPicShow(src) {
            // #ifdef H5
            // h5不显示开屏图
            const a = true
            if (a) return false
            // #endif
            uni.hideLoading()
            this.screen_pic = src
            this.screen_pic_count_down = 5
            this.screen_pic_show = true
            interval = setInterval(() => {
                this.screen_pic_count_down--
                if (this.screen_pic_count_down <= 0) {
                    this.skipScreen()
                }
            }, 1000)
        },
        skipScreen() {
            clearInterval(interval)
            this.screen_pic_count_down = 0
            this.screen_pic_hide = true

            const timeout = setTimeout(() => {
                this.screen_pic_show = false
                this.screen_pic_hide = false
                if (this.loading) this.$uni.showLoading('数据加载中...')
                this.passwordDialogShow()
                if (this.from_tab) this.show_tab = true
                clearTimeout(timeout)
            }, 500)
        },
        analysisScene(scene) {
            const sceneStr = decodeURIComponent(scene)
            console.log(sceneStr)
            const id = utils.getUrlParams('id', sceneStr)
            console.log('id===', id)
            if (!id) {
                uni.hideLoading()
                return this.$uni.showModal('从二维码获取id失败')
            }

            this.getActiveId(id)
        },


        async getActiveId(id) {
            const data = {
                access_token: app.globalData['access_token'],
                id
            }

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/get_long_active_id_by_short_id',
                data
            })
            if (res?.data?.['long_active_id']) {
                this.id = res.data['long_active_id']
                this.getDetail()
            } else {
                uni.hideLoading()
                xwy_api.alert(res && res.info || '长id获取失败')
            }
        },

        getDetail(just_update = false) {
            xwy_api.getActivityDetail(this.id, res => {
                if (!res?.data?.active_details) {
                    this.loading = false
                    this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                    return uni.hideLoading()
                }

                // #ifndef H5
                this.$refs.expirationReminder.open(res.data.active_details)
                // #endif

                if (res.data.active_more_data) {
                    const active_more_data = res.data.active_more_data
                    this.active_more_data = active_more_data
                    if (active_more_data.technology_support) {
                        this.technology_support = res.data.active_more_data.technology_support
                    }
                    if (active_more_data['active_conf_set']) {
                        const active_conf_set = active_more_data['active_conf_set']
                        if (active_conf_set?.headimg_plugin?.length) {
                            this.headimg_plugin = active_conf_set.headimg_plugin
                        }
                    }
                }


                const detail = res.data.active_details
                app.globalData.activity_detail = detail
                this.detail = detail
                my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)

                if (detail.conf) {
                    const conf = detail.conf

                    if (conf.active) {
                        const active = conf.active
                        if (!this.screen_pic && active.screen_pic) this.screenPicShow(active.screen_pic)
                        if (active.news?.news_id) this.changeDetailContent(active.news.news_id)

                        let right_icons_set = []
                        if (active.active_icons_set) {
                            if (active.active_icons_set.length) {
                                right_icons_set = config.getActiveIconsSet(active.active_icons_set, detail.rank_set)
                            }
                            delete active.active_icons_set
                        }
                        if (!right_icons_set.length) right_icons_set = config.getDefaultIconsSet(detail.rank_set)

                        this.right_icons_set = right_icons_set.map(item => {
                            const isDiy = !!(item.type === 'diy' && item.image)

                            return {
                                name: item.name,
                                hide: item.hide || 0,
                                image: isDiy ? item.image : '',
                                className: isDiy ? 'icon-item-image-container' : 'icon-item'
                            }
                        })
                    }

                    if (conf.must_submit) {
                        const must_submit = JSON.parse(JSON.stringify(conf.must_submit))
                        if (must_submit.length) {
                            must_submit.forEach(v => v.value = v.value || '')
                            this.must_submit = must_submit
                        }
                    }
                }

                if (detail.rank_set) {
                    const rank_set = detail.rank_set
                    // 是否纯净版，纯净版显示tab栏，隐藏返回首页按钮
                    if (rank_set['shield_other'] && !this.show_tab) {
                        this.show_tab = true
                        this.$uni.hideHomeButton()

                        // 更新纯净版缓存信息
                        utils.updateShieldOtherInfo(this.detail)
                    }
                    
                    this.rank_set = rank_set
                }


                this.addLookRecords()

                if (detail.name) {
                    uni.setNavigationBarTitle({
                        title: detail.name
                    })
                }

                if (!just_update && app.globalData['userid'] === detail.userid) {
                    this.is_my_activity = true
                }

                this.getUserStatus()

            })
        },

        iconItemClick(name) {
            const id = this.id
            const options = {
                'set': () => this.$uni.navigateTo(`../admin/manage?id=${id}`),
                'ranking_list': () => this.toTopList(),
                'user_info': () => this.uniPopupOpen('my_info'),
                'active_rule': () => this.uniPopupOpen('activity_detail'),
                'record_list': () => this.$uni.navigateTo(`../record_list?active_id=${id}`),
                'user_center': () => this.navigateTo('/pages/user/user'),
                'share': () => this.showActiveSharePopup(),
                'games_center': () => this.$uni.navigateTo(`/pages/games/games_center?id=${id}`)
            }

            options[name]?.()
        },


        addLookRecords() {
            const detail = this.detail
            const value = {
                active_id: detail.active_id,
                name: detail.name,
                types: detail.types,
                logo: detail.logo || this.xwy_config.active_default_logo,
                look_time: new Date().getTime()
            }

            if (detail.organizer) value.organizer = detail.organizer
            my_storage.addActivityLookRecords(value)
        },


        async getUserStatus() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: {
                    active_id: this.id
                }
            })

            if (res?.data?.user_details) {
                // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
                /** @namespace attend_details.team_details */
                
                const attend_details = res.data.user_details
                this.user_details = attend_details
                this.is_joining = true
                this.checked = attend_details.checked || 0


                await this.getTodaySportSubmitCount()

                this.loading = false
                uni.hideLoading()

                if (attend_details.nickname) this.username = attend_details.nickname
                if (attend_details.headimg) this.headimg = attend_details.headimg

                if (attend_details.must_submit) {
                    this.must_submit.forEach((data, index) => {
                        attend_details.must_submit.forEach(user => {
                            if (user.title === data.title) {
                                data.value = user.value
                                this.$set(this.must_submit, index, data)
                            }
                        })
                    })
                }

            } else {
                this.no_attend = true
                this.loading = false
                uni.hideLoading()
                if (this.screen_pic_show) return false
                this.passwordDialogShow()
            }
        },


        async getTodaySportSubmitCount() {
            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace res.data.today_exchange_energy_num */
            
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.ai_motion.user/sumibt_count',
                data: {
                    active_id: this.id
                }
            })

            if (res?.data?.submit_count) this.today_sport_submit_count = res.data.submit_count
            if (res?.data?.today_exchange_energy_num?.num) this.today_energy = Number(res.data.today_exchange_energy_num.num)
        },

        changeDetailContent(news_id) {
            xwy_api.ajax({
                url: "front.news/news_details",
                data: {
                    access_token: app.globalData['access_token'],
                    news_id
                },
                success: res => {
                    uni.hideLoading()
                    if (!res?.data?.['news_details']) {
                        return this.$uni.showModal(res.info || '文章内容获取失败', {
                            success: () => uni.navigateBack()
                        })
                    }

                    const detail = res.data['news_details']


                    if (detail.video_url) {
                        let video_type = 'txv_id'
                        if (detail.video_url.startsWith('http://') || detail.video_url.startsWith('https://')) {
                            video_type = 'http'
                        }
                        detail.video_type = video_type
                    }
                    
                    if (detail.content) detail.content = utils.newsContentInit(detail.content)
                    this.news_detail = detail
                }
            })
        },


        passwordDialogShow() {
            if (this.detail.conf.active.password && this.no_attend) {
                const passwordDialogShow = () => {
                    this.password_dialog_show = true
                    const password = my_storage.getActivityPassword(this.id)
                    if (password) {
                        this.password = password
                        this.checkActivityPassword(password)
                    }
                    const timeout = setTimeout(() => {
                        this.$refs.input_password.open()
                        clearTimeout(timeout)
                    }, 30)
                }

                // 体验版不需要输入密码
                if (app.globalData['evn_version'] === 'trial') {
                    return uni.showModal({
                        title: '提示',
                        content: '此活动设置了活动密码，请勿报名参与活动！！！',
                        cancelText: '进入活动',
                        confirmText: '输入密码',
                        success: res => {
                            res.confirm && passwordDialogShow()
                        }
                    })
                }
                
                passwordDialogShow()
            }
        },

        passwordInputConfirm(val) {
            if (!val) {
                // 纯净版取消输入密码出现去个人中心选项
                if (this.rank_set?.['shield_other']) {
                    return uni.showModal({
                        title: '提示',
                        content: '请输入密码',
                        cancelText: '个人中心',
                        confirmText: '重新输入',
                        success: res => {
                            if (res?.confirm) return this.$refs.input_password.open()
                            this.$uni.navigateTo('/pages/user/user')
                        }
                    })
                }

                return xwy_api.alert('请输入密码', {success: () => this.$refs.input_password.open()})
            }
            this.checkActivityPassword(val)
        },

        async checkActivityPassword(password) {
            this.$uni.showLoading('密码验证中...')

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/check_active_password',
                data: {
                    active_id: this.id,
                    password
                }
            })
            uni.hideLoading()

            if (res?.status === 1) {
                my_storage.rememberActivityPassword(this.id, password)
                this.$refs.input_password.close()
                return this.$uni.showToast('密码正确', 'success')
            }


            xwy_api.alert(res && res.info || '密码错误', {
                success: () => this.$refs.input_password.open()
            })
        },

        passwordInputClose() {
            this.copy(this.id, true)

            if (getCurrentPages().length > 1) return uni.navigateBack()

            // 纯净版并且没有上一页面，重新弹出输入密码窗口
            if (app.globalData['shop_info']?.extend_set?.shield_other_active?.active_id) {
                return xwy_api.alert('请输入活动密码', {
                    success: () => this.$refs.input_password.open()
                })
            }

            this.$uni.reLaunch('/pages/index/index')
        },


        async updateHeadimg() {
            uni.navigateTo({
                url: `/pages/other/headimg-list/headimg-list?active_id=${this.id}`,
                events: {
                    newImg: async obj => {
                        if (!obj?.src) return
                        this.$uni.showLoading('修改中...')
                        if (!obj.temp) return this.updateAttendDetail(obj.src)
                        const data = {
                            temp_data: {
                                path: obj.src
                            },
                            is_temp: 5
                        }
                        if (obj.size) data.temp_data.size = obj.size
                        const headimg = await xwy_api.uploadOneImage(data)
                        this.updateAttendDetail(headimg)
                    }
                }
            })
        },


        joinActivity() {
            if (this.loading) return
            this.join_popup_show = true
        },

        updateAttendDetailShow() {
            this.update_attend_details = true
            this.join_popup_show = true
        },

        mustValueChange(e, index) {
            const item = this.must_submit[index]
            item.value = item.options[e.detail.value].text
            this.$set(this.must_submit, index, item)
        },


        cancelJoin() {
            this.join_popup_show = false
            this.update_attend_details = false
        },

        joinAjax() {
            if (this.update_attend_details) {
                this.updateAttendDetail()
                return false
            }

            if (!this.detail.conf.active.closed_team && this.detail.conf.active.team_required && !this.choose_team.id) {
                uni.showToast({
                    title: '请选择队伍',
                    icon: 'error'
                })
                return
            }

            this.joining()
        },


        getMustSubmitData() {
            const must_submit = JSON.parse(JSON.stringify(this.must_submit))
            for (let i = 0; i < must_submit.length; i++) {
                const v = must_submit[i]
                v.options && delete v.options
                if (v.rules === 1 && v.value === '') {
                    this.$uni.showToast(`请${v.types === 2 ? '选择' : '输入'}${v.title}`)
                    return false
                }
                if (v.types === 3 && v.value && v.value.length !== 11) {
                    this.$uni.showToast(`请输入正确的${v.title}`)
                    return false
                }
            }

            let must_submit_str = JSON.stringify(must_submit)
            must_submit_str = must_submit_str.replace(/·/g, '-')
            return base64['encode'](must_submit_str)
        },

        updateAttendDetail(headimg) {
            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id
            }

            if (headimg) data.headimg = headimg
            if (this.choose_team.id) data.team_id = this.choose_team.id

            const must_submit = this.getMustSubmitData()
            if (must_submit === false) return false
            data.must_submit = must_submit

            this.$uni.showLoading('修改中...')

            xwy_api.ajax({
                url: 'front.flat.sport_step.user/update_attend_details',
                data,
                success: res => {
                    if (res?.status !== 1) return this.$uni.showModal(res?.info || '修改失败')

                    this.$uni.showToast('修改成功', 'success')

                    this.cancelJoin()
                    this.getDetail()
                }
            })
        },

        joining() {

            const data = {
                active_id: this.id,
                access_token: app.globalData['access_token']
            }


            if (this.must_submit && this.must_submit.length) {
                const must_submit = this.getMustSubmitData()
                if (must_submit === false) return false
                data.must_submit = must_submit
            }

            data.nickname = this.username
            if (this.choose_team.id) data.team_id = this.choose_team.id

            this.loading = true
            this.$uni.showLoading('报名中...')


            xwy_api.ajax({
                url: 'front.flat.sport_step.user/submit_attend_active',
                data,
                success: res => {
                    this.loading = false
                    uni.hideLoading()
                    console.log('报名活动', res)
                    if (!res.status) return this.$uni.showModal(res.info || '报名失败', {title: '报名失败'})

                    this.join_popup_show = false
                    this.$uni.showToast(res.info || '报名成功', 'success')

                    setTimeout(() => {
                        this.$uni.showLoading()
                        this.getDetail()
                    }, 1000)
                }
            })
        },


        showActiveSharePopup() {
            this.$refs.activeShare.open({
                page: 'pages/ai_sport/pages/user/detail',
                scene: 'id=' + this.detail.id,
                qrcode_logo: this.rank_set?.closed_AD && this.detail.conf?.active?.qrcode_logo
            })
        },
       
        copy(data, hide_toast = false) {
            uni.setClipboardData({
                data,
                success: () => hide_toast ? uni.hideToast() : this.$uni.showToast('复制成功', 'none', 500)
            })
        },

        uniPopupClose(ref) {
            this.popup_open = false
            this.$refs[ref].close()
        },

        uniPopupOpen(ref) {
            this.popup_open = true
            this.$refs[ref].open()
        },

        async exchangeStep() {
            const max_daily_energy = this.detail.conf.active.max_daily_energy
            if (max_daily_energy && this.today_energy >= max_daily_energy) {
                this.$uni.showToast(`兑换失败。你今日已获得${this.today_energy}，已达到每日${this.integralOrWaterUnit}获取上限。`, 'none', 3000)
                return
            }
            
            
            this.$uni.showLoading('兑换中...')
            await this.xwy_api.getWeRunData()
            // const today_step = app.globalData['today_step']
            
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exchange/exchange_step',
                data: {
                    active_id: this.id
                }
            })
            uni.hideLoading()
            
            if (res?.status === 1) {
                let show_text = res.data?.['exchange_result']?.show_text
                if (show_text?.length && this.rewardMode === 'water') {
                    show_text.forEach(v => {
                        v.text = v.text.replace(/里/g, this.integralOrWaterUnit).replace(/行走了/g, '兑换了')
                    })
                } else {
                    show_text = [{text: '兑换成功', color: '#666'}]
                }
                
                this.step_exchange_result = {
                    result: 'success',
                    info: res.info || '兑换成功',
                    show_text
                }
                
                await this.getUserStatus()
            } else {
                this.step_exchange_result = {
                    result: 'fail',
                    info: res.info || '兑换失败'
                }
            }
            
            this.$refs.stepExchangeResultPopup.open()
            
        },

        async toStart(item) {
            if (!this.is_joining) {
                // 雷子说两个窗口一起弹出来不好(刘总附议,其他同事没有表达意见)，直接弹报名的窗口就行了，不用提示未报名
                // 2024-04-10 17:34  口头表述，但企业微信群雷子有发张图片
                // this.$uni.showToast('未报名参加活动')
                return this.joinActivity()
            }
            if (!activity_tool.actionCheck({
                is_joining: this.is_joining,
                checked: this.checked,
                begin_time: this.detail.begin_time,
                end_time: this.detail.end_time
            })) return false
            
            if (item === 'step') return this.exchangeStep()
            
            const submit_times = this.detail.conf.active.submit_times,
                today_count = this.today_sport_submit_count
            if (submit_times && today_count && submit_times - today_count <= 0) {
                const tips = `每日可运动${submit_times}次，你今日已运动${today_count}次，已无运动次数。`
                return this.$uni.showModal(tips)
            }

            if (item.submit_times) {
                const user_item_today_count_res = await xwy_api.request({
                    url: 'front.flat.sport_step.ai_motion.user/sumibt_count',
                    data: {
                        active_id: this.id,
                        types: item.types
                    }
                })

                const user_item_today_count = user_item_today_count_res?.data?.submit_count || 0
                const item_submit_times = Number(item.submit_times)
                if (user_item_today_count >= item_submit_times) {
                    const tips = `${item.title || item.name}每日可运动${item_submit_times}次，你今日已运动${user_item_today_count}次，已无运动次数。`
                    return this.$uni.showModal(tips)
                }
            }

            
            const max_daily_energy = this.detail.conf.active.max_daily_energy
            if (max_daily_energy && this.today_energy >= max_daily_energy) {
                const res = await this.$uni.showModal(`你今日已获得${this.today_energy}${this.energy_unit}，已达每日上限，继续运动不会增加，是否继续运动？`, {
                    showCancel: true,
                    confirmText: '继续运动'
                })
                if (!res.confirm) return
            }


            const sport_types = config.getSupportedSportTypes()

            const current_sport = sport_types.find(v => v.types === item.types)
            if (!current_sport)
                return this.$uni.showModal('暂未支持该运动', {success: () => uni.navigateBack()})


            let url = `../ai-sport-recognition?active_id=${this.id}&types=${item.types}&name=${item.title || item.name}`
            if (this.rank_set['ai_hand_start']) url += '&is_manual=1'

            let per_seconds = this.detail.conf.active.per_seconds || 0
            if (item.per_seconds) per_seconds = item.per_seconds
            if (per_seconds) url += `&per_seconds=${per_seconds}`

            this.$uni.navigateTo(url, {
                events: {
                    updateSportCount: () => this.getUserStatus()
                }
            })
        },


        toChooseTeam() {
            this.$uni.navigateTo(`/pages/activity/admin/team_list?selteam=1&id=${this.id}`, {
                events: {
                    setTeam: team => {
                        this.choose_team.id = team.team_id
                        this.choose_team.name = team.team_name
                    }
                }
            })  
        },

    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 80px;
}

.logo {
    display: block;
    width: 100vw;
    height: auto;
}

.share-btn {
    padding: 0;
    margin: 0;
    border: none !important;
    width: 40px;
    min-width: 40px;
    height: 40px;
    line-height: 40px;
}

.bdb-10 {
    border-bottom: 10px solid #f8f8f8;
}

.icon-list {
    position: fixed;
    top: 10vh;
    right: 3vw;
    display: flex;
    flex-direction: column;

    .icon-item {
        width: 40px;
        height: 40px;
        line-height: 40px;
        border-radius: 50%;
        text-align: center;
        background-color: rgba(0, 0, 0 , .5);
        margin-bottom: 5px;

        .iconfont, .uni-icons {
            font-size: 24px !important;
            color: #fff !important;
        }
    }
}


.headimg {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}


.join-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, .7);
    z-index: 9999;
}

.join-popup-c {
    border-radius: 10px;
    overflow: hidden;
}


.join-input {
    margin: 10px;
    padding: 10px;
    border: 1px solid #bbb;
    border-radius: 5px;
    width: 70vw;
    max-width: 400px;
}

.join-popup-btns {
    border-top: 1px solid #eee;
}

.join-popup-btn-cancel, .join-popup-btn-confirm {
    width: 50%;
    box-sizing: border-box;
    line-height: 44px;
}

.join-popup-btn-cancel {
    border-right: 1px solid #eee;
}

.uni-popup-info {
    width: 280px;
    border-radius: 10px;
    overflow: hidden;
}

.detail-popup {
    width: 95vw;
    padding-bottom: 15px;
}

.sport-list {
    $one-line-item: 2;
    $item-margin: 15rpx;
    $item-width: calc((750rpx - #{$item-margin} * #{$one-line-item}) / 2 - #{$item-margin * 2});
    $item-height: calc(#{$item-width} * 204 / 430);

    padding: 5px $item-margin 0;

    .sport-item {
        margin: $item-margin;
        border-radius: 10px;
        overflow: hidden;

        // 设计的图片比例 430*204
        .sport-logo, .sport-no-logo {
            display: block;
            width: $item-width;
            height: $item-height;
            border-radius: 10px;
        }

        .sport-no-logo {
            text-align: center;
            line-height: $item-height;
            font-size: 48rpx;
            background-color: #5cadff;
            color: #fff;
        }
    }
}

.step-exchange-result-popup {
    width: 320px;
    border-radius: 10px;
    overflow: hidden;
}
</style>
