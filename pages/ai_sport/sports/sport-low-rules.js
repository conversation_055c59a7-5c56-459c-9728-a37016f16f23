/**
 * 将运动参数下调
 *
 * @yztob
 * 20240108 created.
 * 20240116 放宽跳绳运动。
 **/

/**
 * 
 * @param {String} sportKey 运动分析器KEY
 * @param {SportBase} sport 运动分析器实例
 */
function setLowRules(sportKey, sport) {
    if (!sportKey)
        throw new Error('sportKey不能为空');

    if (!sport)
        throw new Error('sport不能为空');

    const rules = sport.rules;
    console.log(rules);
    switch (sportKey) {
        // case "Rope-Skipping":
        //     //跳绳
        //     sport._pointTracker.amplitude = 0.015;
        //     rules.base.rules[1].offset = 40;
        //     rules.base.rules[2].rules[0].angle = 90;
        //     rules.base.rules[2].rules[0].offset = 80;
        //     rules.base.rules[2].rules[1].angle = 90;
        //     rules.base.rules[2].rules[1].offset = 80;
        //     break;
        // case 'Sit-Up':
        //     //仰卧起座
        //     rules.base[2].rules[0].offset = 25;
        //     rules.base[2].rules[1].offset = 25;
        //     rules.up.rules[0].offset = 20;
        //     rules.up.rules[1].offset = 20;
        //     break;
        // case 'Push-Up':
        //     //俯卧撑
        //     rules.liePose.rules[1].rules[0].offset = 20;
        //     rules.liePose.rules[1].rules[1].offset = 20;
        //     rules.liePose.rules[2].rules[0].offset = 20;
        //     rules.liePose.rules[2].rules[1].offset = 20;
        //
        //     rules.upPose.rules[1].rules[0].offset = 30;
        //     rules.upPose.rules[1].rules[1].offset = 30;
        //     rules.upPose.rules[2].rules[0].offset = 25;
        //     rules.upPose.rules[2].rules[1].offset = 25;
        //     break;
        //
        // case "Squat":
        //     //深蹲起
        //     sport.buildRules();
        //
        //     rules.squat.rules[1].rules[0].offset = 40;
        //     rules.squat.rules[1].rules[1].offset = 40;
        //     rules.squat.rules[2].rules[0].offset = 40;
        //     rules.squat.rules[2].rules[1].offset = 40;
        //
        //     break;
        case "Plank":
            console.log('🚀 PlankLowRules');
            //平板支撑
            sport.buildRules();
            rules.pose.rules[2].rules[0].offset = 40;
            rules.pose.rules[2].rules[1].offset = 40;
            rules.pose.rules[3].rules[0].offset = 40;
            rules.pose.rules[3].rules[1].offset = 40;
            rules.pose.rules[4].rules[0].offset = 50;
            rules.pose.rules[4].rules[1].offset = 50;

            break;
        // case "Squat-Horse":
        //     //马步蹲
        //     sport.buildRules();
        //     rules.pose.rules[1].rules[0].offset = 40;
        //     rules.pose.rules[1].rules[1].offset = 40;
        //     rules.pose.rules[2].rules[0].offset = 50;
        //     rules.pose.rules[2].rules[1].offset = 50;
        //     rules.pose.rules[3].rules[0].offset = 40;
        //     rules.pose.rules[3].rules[1].offset = 40;
        //     break;
        // case 'High-Knee':
        //     //高抬腿
        //     rules.highLow = 0.005;
        //     rules.leftPose.rules[0].offset = 55;
        //     rules.leftPose.rules[1].offset = 55;
        //     rules.rightPose.rules[0].offset = 55;
        //     rules.rightPose.rules[1].offset = 55;
        //     break;
    }
}

module.exports = {
    setLowRules
};