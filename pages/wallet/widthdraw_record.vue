<template>
	<view class="page bg-background">
		<view v-if="total_count" class="p10 font14 text-center">
			<view class="color-sub">共{{total_count}}条提现记录</view>
		</view>
		<view class="list">
			<view
				class="item bg-white"
				:class="{'border-bottom': index !== list.length - 1}"
				v-for="(item, index) in list" :key="index"
			>
				<view class="flex-kai">
					<view class="flex-row">
						<view
							v-if="item.conf_json && item.conf_json.user && item.conf_json.user.headimg"
							class="headimg pr10"
						>
							<image :src="item.conf_json.user.headimg" mode="aspectFill"/>
						</view>
						<view>
							<view class="color-title">{{item.memo}}</view>
							<view class="color-sub font14 pt5">{{item.create_time}}</view>
						</view>
					</view>
					<view class="flex-all-center">
						<view class="color-warning">
							<text>{{item.amount}}</text>
							<text class="font12">元</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view v-if="!list.length && !loading" class="text-center" style="padding-top: 10vh;">
		    <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
		    <view class="color-sub">暂无红包记录</view>
		</view>

		<uni-load-more v-if="loading" status="loading"></uni-load-more>
		<uni-load-more v-if="is_last_page && list.length > 8" status="noMore" :contentText="{contentnomore: '我是有底线的'}"></uni-load-more>
		<uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>

	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'

	export default {
		data() {
			return {
				list: [],
				load_page: 1,
				is_last_page: false,
				loading: true,
				total_amount: 0,
				total_count: 0
			}
		},
		onLoad(e) {
			uni.showLoading()

			login.uniLogin(err => {
				if (err && err.errMsg) {
					uni.hideLoading()
					uni.showModal({
						title: err.errTitle || '提示',
						content: err.errMsg,
						showCancel: false
					})
					return false
				}


				this.getList()
			})
		},
		onReachBottom() {
			!this.is_last_page && !this.loading && this.getList()
		},
		methods: {
			async getList() {
				if (this.load_page === 1) {
					this.list = []
					this.is_last_page = false
				}


				const data = {
					access_token: app.globalData.access_token,
					page: this.load_page,
					perpage: 20
				}

				this.loading = true

				const res = await xwy_api.request({
					url: 'front.user.redpack.user/withdraw_list',
					data
				})

				this.loading = false
				uni.hideLoading()

				this.load_page++

				if (res?.data?.redpack_amount_list) {
					const res_data = res.data.redpack_amount_list
					this.total_count = res_data.total
					this.is_last_page = res_data.is_lastpage
					const list = res_data.data || []
					this.list = [...this.list, ...list]
				} else {
					this.is_last_page = true
				}
			}
		}
	}
</script>

<style>
.page {
	min-height: 100vh;
}
.item {
	padding: 20px;
}
.border-bottom {
	border-bottom: 1px solid #dfdfdf;
}
.headimg image {
	width: 50px;
	min-width: 50px;
	height: 50px;
	border-radius: 50%;
	display: block;
}
</style>
