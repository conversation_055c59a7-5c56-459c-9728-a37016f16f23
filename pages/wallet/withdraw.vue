<template>
    <view class="p10">
        <view class="color-title">提现金额 (元)</view>
        <view class="enter flex-row font-bold">
            <text class="font34">￥</text>

            <input class="input" type="digit" :focus="true" :adjust-position="false" v-model="enter_amount"
                   maxlength="8"/>
        </view>
        <view v-if="error_tips" class="font14 color-error pt10">{{ error_tips }}</view>
        <view class="flex-row pt10 font14">
            <view class="color-content">账户余额</view>
            <view class="color-title" style="padding-left: 2px;">
                <text class="font12">￥</text>
                <text>{{ user_amount }}</text>
            </view>
            <view class="color-primary pl10 pr10" hover-class="navigator-hover"
                  @click="enter_amount = user_amount">全部提现
            </view>
        </view>
        <view v-if="tips" class="color-sub font14 pt10">{{ tips }}</view>

        <view class="withdraw flex-all-center">
            <view>

                <button
                    class="bg-green color-white"
                    hover-class="navigator-hover"
                    :disabled="error_tips"
                    @click="withdraw"
                >提现
                </button>
                <navigator class="color-sub font14 text-center p10" url="./widthdraw_record">提现记录
                </navigator>
            </view>
        </view>

        <uni-popup ref="popup" type="center">
            <view class="popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('popup')">
                    <uni-icons type="closeempty" size="24" color="#ffffff"/>
                </view>
                <view
                    class="top color-white"
                    :class="{'bg-success': status, 'bg-error': !status}"
                    style="padding: 50px 20px 20px;"
                >

                    <icon :type="status ? 'success' : 'clear'" size="80" color="#ffffff"></icon>
                    <view class="font18">提现{{ status ? '成功' : '失败' }}</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ popup_tips }}
                    </view>

                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'

export default {
    data() {
        return {
            tips: '',
            user_amount: 0,
            enter_amount: '',
            error_tips: '',
            status: '',
            popup_tips: ''
        }
    },
    watch: {
        enter_amount(val) {
            this.error_tips = ''
            const val_arr = val.toString().split('.')
            if (val_arr.length > 2 || (val_arr[1] && val_arr[1].length > 2)) {
                this.error_tips = '请输入正确的提现金额'
                return false
            }
            if (val > this.user_amount) {
                this.error_tips = '账户余额不足'
                return false
            }
        }
    },
    onLoad(e) {
        this.$uni.showLoading()

        if (e.id) this.id = e.id
        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                uni.showModal({
                    title: err['errTitle'] || '提示',
                    content: err.errMsg,
                    showCancel: false
                })
                return false
            }


            this.getData()
        })
    },
    methods: {
        async getData() {
            const res = await xwy_api.request({
                url: 'front.user.redpack.user/withdraw_tips'
            })

            uni.hideLoading()

            if (!res || !res.status) {
                xwy_api.alert(res && res.info || '余额获取失败')
                return false
            }

            if (res.data?.user_details?.['redpack_amount_left'])
                this.user_amount = res.data.user_details['redpack_amount_left']
            if (res.data?.tips) this.tips = res.data.tips
        },

        async withdraw() {
            const amount = Number(this.enter_amount)
            if (!amount) return this.$uni.showToast('请输入提现金额')

            this.$uni.showLoading('提现中...')

            const data = {amount}
            if (this.id) data.active_id = this.id
            const res = await xwy_api.request({
                url: 'front.user.redpack.user/withdraw_redpack_amount',
                data: {
                    amount
                }
            })

            uni.hideLoading()

            const status = res && res.status || 0
            this.status = status
            let info = status ? '提现成功' : '提现失败'
            if (res && res.info) info = res.info
            this.popup_tips = info
            this.$refs.popup.open()

            if (status) {
                this.user_amount = utils.accSub(this.user_amount, this.enter_amount)
                this.enter_amount = ''
                await this.getData()
            }
        },

        uniPopupClose(ref) {
            this.$refs[ref].close()
        },
    }
}
</script>

<style>
.enter {
    padding-top: 20px;
    border-bottom: 1px solid #d7d7d7;
}

.input {
    font-size: 48px;
    height: 60px;
    line-height: 50px;
    caret-color: #17c067;
    padding: 5px;
}

.withdraw {
    position: fixed;
    top: 60vh;
    left: 0;
    width: 100vw;
}

button {
    width: 250px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
}

button::after {
    border: none;
}

.popup {
    width: 280px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}
</style>
