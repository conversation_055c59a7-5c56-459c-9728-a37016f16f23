<template>
    <view class="activity-list flex-row flex-wrap">
        <!-- 活动类型列表 -->
        <template v-if="!justTemplate">
            <view v-for="(item, index) in activeGoodsList" :key="index" style="width: 50%;">
                <activity-item v-if="item.show || evnVersion === 'develop' || evnVersion === 'trial'"
                            type="active-type" :item="item" @showMemo="showMemoPopup"/>
            </view>
        </template>

        <!-- 活动模板列表 -->
        <view v-for="item in activeTemplateListComputed" :key="item.key__" style="width: 50%;">
            <activity-item type="active-template" :item="item" :just-template="justTemplate"
                           @useTemplate="handleUseTemplate" @showMemo="showMemoPopup"/>
        </view>
        
        <!-- 联系方式输入弹窗 -->
        <contact-popup ref="contactPopup" @confirm="confirmContactInfo"/>
        
        <!-- 备注信息展示弹窗 -->
        <memo-popup ref="memoPopup"/>
    </view>
</template>

<script>
const app = getApp()
import activityItem from './activity-item.vue'
import contactPopup from './contact-popup.vue'
import memoPopup from './memo-popup.vue'

export default {
    name: 'ActivityListContainer',
    
    components: {
        activityItem,
        contactPopup,
        memoPopup
    },
    
    props: {
        // 活动类型列表
        activeGoodsList: {
            type: Array,
            default: () => []
        },
        // 活动模板列表
        activeTemplateList: {
            type: Array,
            default: () => []
        },

        // 是否只显示模板
        justTemplate: {
            type: Boolean,
            default: true
        }
    },

    data() {
        return {
            evnVersion: app.globalData.evn_version,
            contact_form: {
                wechat_num: '',
                mobile: ''
            },
            currentTemplateItem: null // 当前正在使用的模板项
        }
    },

    computed: {
        activeTemplateListComputed() {
            return this.activeTemplateList.map((item, index) => ({
                ...item,
                key__: `${index}-${item.active_id}`
            }))
        }
    },
    
    methods: {
        /**
         * 显示备注信息弹窗
         * @param {Object} item - 活动项数据
         */
        showMemoPopup(item) {
            this.$refs.memoPopup.open(item)
        },
        
        // 处理子组件发出的使用模板事件
        async handleUseTemplate(item) {
            this.currentTemplateItem = item
            
            const canCreate = await this.checkUserCanCreateActive()
            if (!canCreate.status) {
                this.$uni.showModal(canCreate.info)
                return
            }

            const {confirm: useConfirm} = await this.$uni.showModal('模板使用成功后，可前往[个人中心-我创建的活动]中查看并管理活动。确定使用模板吗？', {
                showCancel: true,
                confirmText: '使用'
            })
            if (!useConfirm) return

            // 检查缓存的联系方式
            const cachedContactInfo = this.getCachedContactInfo()
            if (cachedContactInfo && (cachedContactInfo.wechat_num || cachedContactInfo.mobile)) {
                // 使用缓存的联系方式，直接继续流程
                console.log('使用缓存的联系方式:', cachedContactInfo)
                this.contact_form = cachedContactInfo
                this.proceedWithTemplate()
            } else {
                // 显示联系方式弹窗
                this.showContactPopup()
            }
        },
        
        async checkUserCanCreateActive() {
            // 开发版和体验版可重复创建活动
            if (this.evnVersion === 'develop' || this.evnVersion === 'trial') return {status: 1}

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/check_user_can_create_active'
            })
            uni.hideLoading()

            return {
                status: res?.status || 0,
                info: res?.info || '暂时不能创建活动'
            }
        },

        async copyTemplate() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin.activeSet/copy_active_tpl',
                data: {
                    active_id: this.currentTemplateItem.active_id,
                    ...this.contact_form
                }
            })

            if (res?.status !== 1) {
                this.$uni.showModal(res?.info || '模板使用失败')
                return false
            }

            return true
        },

        // 显示联系方式弹窗
        showContactPopup() {
            this.$refs.contactPopup.open()
        },

        // 关闭联系方式弹窗
        closeContactPopup() {
            this.$refs.contactPopup.close()
        },

        // 确认联系方式并继续模板使用流程
        async confirmContactInfo(contact_form) {
           this.contact_form = contact_form

            if (!await this.copyTemplate()) return

            const {confirm: jumpConfirm} = await this.$uni.showModal('模板使用成功！可前往[个人中心-我创建的活动]中查看并管理活动。', {
                title: '使用成功',
                showCancel: true,
                confirmText: '查看活动'
            })

            if (jumpConfirm) this.$uni.navigateTo('/pages/activity/user/activity_list?type=create')
        },

        // 获取缓存的联系方式
        getCachedContactInfo() {
            try {
                const cachedData = uni.getStorageSync('user_contact_info')
                if (cachedData) {
                    // 验证缓存数据的有效性
                    if (this.validateCachedContactInfo(cachedData)) {
                        return cachedData
                    } else {
                        // 缓存数据无效，清除缓存
                        console.log('缓存的联系方式数据无效，已清除')
                        uni.removeStorageSync('user_contact_info')
                        return null
                    }
                }
                return null
            } catch (error) {
                console.error('读取联系方式缓存失败:', error)
                return null
            }
        },

        // 验证缓存的联系方式数据
        validateCachedContactInfo(contactInfo) {
            if (!contactInfo || typeof contactInfo !== 'object') return false

            const { wechat_num, mobile } = contactInfo

            // 至少要有一个联系方式
            if (!wechat_num && !mobile) return false

            // 验证微信号
            if (wechat_num) {
                const wechat_str = wechat_num.toString().trim()
                if (wechat_str.length < 4 || wechat_str.length > 20) return false
                if (this._utils.isChineseChar(wechat_str)) return false
            }

            // 验证手机号
            if (mobile) {
                const mobile_str = mobile.toString().trim()
                if (mobile_str.length !== 11) return false
                if (!/^1\d{10}$/.test(mobile_str)) return false
            }

            return true
        },

        // 使用缓存数据直接继续模板使用流程
        async proceedWithTemplate() {
            if (!await this.copyTemplate()) return

            const {confirm: jumpConfirm} = await this.$uni.showModal('模板使用成功！可前往[个人中心-我创建的活动]中查看并管理活动。', {
                title: '使用成功',
                showCancel: true,
                confirmText: '查看活动'
            })

            if (jumpConfirm) this.$uni.navigateTo('/pages/activity/user/activity_list?type=create')
        }
    }
}
</script>

<style scoped>
.activity-list {
    padding: 10px 5px;
}
</style>