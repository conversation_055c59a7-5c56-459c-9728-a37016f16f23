<template>
    <view>
        <uni-popup type="center" ref="popup" :is-mask-click="false">
            <view class="contact-popup bg-white">
                <view class="text-center color-title pb10">请填写联系方式</view>
                <view class="color-content font14 text-center pt10 pb10">
                    请至少填写一种联系方式，以便提供技术支持。
                </view>

                <uni-forms label-position="top">
                    <uni-forms-item label="微信号">
                        <uni-easyinput v-model="wechat_num" placeholder="请输入微信号" maxlength="20"/>
                    </uni-forms-item>
                    <uni-forms-item label="手机号">
                        <uni-easyinput v-model="mobile" placeholder="请输入手机号" type="number"
                                       maxlength="11"/>
                    </uni-forms-item>
                </uni-forms>

                <view class="flex-all-center">
                    <view class="contact-popup-confirm-button color-white bg-light-primary text-center"
                          hover-class="navigator-hover" @click="confirm">
                        确定
                    </view>
                </view>

                <view class="flex-all-center pt10">
                    <view class="color-sub font14 p10" @click="close">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "contact-popup",
    emits: ['confirm'],


    data() {
        return {
            wechat_num: '',
            mobile: ''
        }
    },

    mounted() {

    },

    methods: {
        open() {
            this.$refs.popup.open()
        },

        close() {
            this.$refs.popup.close()
        },


        confirm() {
            if (!this.validateContactForm()) return

            // 保存联系方式到本地缓存
            this.saveContactInfoToCache()

            // 关闭弹窗
            this.close()

            this.$emit('confirm', {
                wechat_num: this.wechat_num,
                mobile: this.mobile
            })
        },

        // 验证联系方式表单
        validateContactForm() {
            const {wechat_num, mobile} = this

            // 至少填写一个联系方式
            if (!wechat_num && !mobile) {
                this.$uni.showToast('请至少填写一种联系方式')
                return false
            }

            // 验证微信号
            if (wechat_num) {
                const wechat_str = wechat_num.toString().trim()
                if (wechat_str.length < 4 || wechat_str.length > 20) {
                    this.$uni.showToast('微信号长度应为4-20位')
                    return false
                }
                if (this._utils.isChineseChar(wechat_str)) {
                    this.$uni.showToast('微信号不能包含中文字符')
                    return false
                }
            }

            // 验证手机号
            if (mobile) {
                const mobile_str = mobile.toString().trim()
                if (mobile_str.length !== 11) {
                    this.$uni.showToast('手机号必须为11位数字')
                    return false
                }
                if (!/^1\d{10}$/.test(mobile_str)) {
                    this.$uni.showToast('请输入正确的手机号格式')
                    return false
                }
            }

            return true
        },

        // 保存联系方式到缓存
        saveContactInfoToCache() {
            try {
                const contactInfo = {
                    wechat_num: this.wechat_num.toString().trim(),
                    mobile: this.mobile.toString().trim()
                }
                uni.setStorageSync('user_contact_info', contactInfo)
                console.log('联系方式已保存到缓存:', contactInfo)
            } catch (error) {
                console.error('保存联系方式缓存失败:', error)
            }
        },
    }
}
</script>

<style lang="scss">
/* 联系方式弹窗样式 */
.contact-popup {
    width: 80vw;
    max-width: 400px;
    border-radius: 10px;
    padding: 20px 20px 10px;
}

.contact-popup-confirm-button {
    width: 160px;
    line-height: 44px;
    border-radius: 22px;
}
</style>