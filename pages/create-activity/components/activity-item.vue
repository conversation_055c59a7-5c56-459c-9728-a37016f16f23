<template>
    <view class="activity-item__">
        <image class="logo" :src="item.logo || item.back_ground || ''" mode="aspectFill" @click="clickLogo"/>

        <view class="text-center pt5" :style="{color: item.tilte_color || '#1c2438'}" @click="lockMemo">
            {{ item.title }}
        </view>
        <view class="font12 ptm5 text-center ellipsis" :style="{color: item.memo_color || '#80848f'}"
              @click="lockMemo">{{ item.memo || item.content }}
        </view>

        <view v-if="!justTemplate" class="flex-center text-center font14">
            <view v-if="item.active_id || item.demo_pic"
                  class="example-btn color-content bg-white" hover-class="navigator-hover"
                  @click="viewExample">查看示例
            </view>
            <view class="create-btn color-white bg-primary" hover-class="navigator-hover" @click="toCreate">
                {{ createButtonText }}
            </view>
        </view>

    </view>
</template>

<script>
const app = getApp()

export default {
    name: "activity-item",
    emits: ['toCreate', 'useTemplate', 'showMemo'],
    props: {
        type: {
            type: String,
            default: 'active-type'
        },
        item: {
            type: Object,
            default: () => ({})
        },
        justTemplate: {
            type: Boolean,
            default: false
        }
    },

    data() {
        return {
            evn_version: app.globalData.evn_version
        }
    },

    computed: {
        createButtonText() {
            return this.type === 'active-type' ? '创建活动' : '使用模版'
        }
    },

    methods: {
        clickLogo() {
            if (this.type === 'active-type') {
                this.jump()
                return
            }

            // #ifdef MP-WEIXIN
            this.lookTemplateDemo()
            // #endif

            // #ifndef MP-WEIXIN
            this.useTemplate()
            // #endif
        },

        viewExample() {
            if (this.type === 'active-type') {
                this.lockDemo()
                return
            }

            this.lookTemplateDemo()
        },


        toCreate() {
            if (this.type === 'active-type') {
                this.jump()
                return
            }

            this.useTemplate()
        },

        jump() {
            let jump_type = 'navigateTo', url = '', error = ''

            if (this.item.types === 9999) {
                jump_type = 'navigateToMiniProgram'
            } else {
                url = this.xwy_config.getActivityPath(this.item.types, 'add')
                if (!url) error = '没有跳转类型 - ' + this.item.types
                url += `?types=${this.item.types}&name=${this.item.title}`
            }

            if (error) return this.$uni.showModal(error)

            const options = {
                navigateTo: () => this.$uni.navigateTo(url),
                navigateToMiniProgram: () => {
                    if (!this.item.appid) return this.$uni.showModal('没有配置跳转小程序的appid')
                    uni.navigateToMiniProgram({
                        appId: this.item.appid,
                        fail: err => {
                            if (err.errMsg === 'navigateToMiniProgram:fail cancel') return false
                            this.$uni.showModal(JSON.stringify(err) + 'appid: ' + this.item.appid)
                        }
                    })
                }
            }

            options[jump_type]?.()
        },

        lockDemo() {
            const {active_id: id, demo_pic: pic} = this.item

            // #ifdef MP-WEIXIN
            if (id) {
                this._utils.toActivityDetail(this.item)
                return
            }

            if (pic) this.$uni.previewImage(pic)
            // #endif


            // #ifndef MP-WEIXIN
            if (pic) {
                this.$uni.previewImage(pic)
            } else {
                if (id) {
                    this.$uni.showModal('请在小程序中查看示例')
                }
            }
            // #endif
        },

         lookTemplateDemo() {
            // #ifdef MP-WEIXIN
            const {active_id: id, active_types: types} = this.item
            if (id && types) this._utils.toActivityDetail({active_id: id, types})
            // #endif


            // #ifndef MP-WEIXIN
             this.$uni.showModal('请在小程序中查看示例')
            // #endif
        },

        lockMemo() {
            this.$emit('showMemo', this.item)
        },

        async useTemplate() {
            // 发出使用模板事件给父组件处理
            this.$emit('useTemplate', this.item)
        }
    }
}
</script>

<style lang="scss" scoped>
.activity-item__ {
    width: calc(100% - 10px);
    margin: 5px;
    border-radius: 5px;
    box-sizing: border-box;
    padding: 4px;
    background-color: #fff;

    .logo {
        width: 100%;
        height: auto;
        aspect-ratio: 1;
        border-radius: 5px;
        display: block;
    }

    .example-btn, .create-btn {
        width: calc(50% - 5px);
    }

    .example-btn {
        border: 1px solid #E4E4E4;
        line-height: 36px;
    }

    .create-btn {
        line-height: 38px;
    }
}
</style>