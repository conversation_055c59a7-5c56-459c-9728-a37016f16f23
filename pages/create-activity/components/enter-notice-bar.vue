<template>
    <view v-if="latest_company_list.length">
        <view v-for="(item, index) in latest_company_list" :key="index">
            <uni-notice-bar
                v-if="index === company_index"
                scrollable="true"
                single="true"
                showIcon="true"
                :speed="100"
                :text="`欢迎 · ${item} 入驻`"
            ></uni-notice-bar>
        </view>
    </view>
</template>

<script>
export default {
    name: "enter-notice-bar",
    data() {
        return {
            latest_company_list: [],
            company_index: 0,
        }
    },

    beforeDestroy() {
        this.clearCompanyTimeout()
    },

    mounted() {
        this.getLatestCompanyList()
    },

    methods: {
        async getLatestCompanyList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.system/latest_company_list'
            })

            const list = res?.data?.list || []
            if (!list.length) return

            this.latest_company_list = list.map(({organizer}) => organizer)

            this.companyChange()
        },

        companyChange() {
            let index = this.company_index + 1
            if (index === this.latest_company_list.length) index = 0

            this.clearCompanyTimeout()

            const time = this.calculateStayTime()

            this.companyTimeout = setTimeout(() => {
                this.company_index = index
                this.companyChange()
            }, time)
        },

        // 根据单位的字数来计算停留时间
        calculateStayTime() {
            const organizer_name = this.latest_company_list[this.company_index]
            const text_len = organizer_name.length + 6
            return text_len * 140 + 1000
        },

        clearCompanyTimeout() {
            if (this.companyTimeout) {
                clearTimeout(this.companyTimeout)
                this.companyTimeout = null
            }
        }
    }
}
</script>