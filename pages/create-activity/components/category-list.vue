<template>
    <view v-if="!isSearch" class="category-list flex-row flex-wrap">
        <view class="category-item flex-all-center bg-white color-title font14"
              v-for="(item, index) in categoryListSet" 
              :key="index"
              @click="handleCategoryJump(item)">
            {{ item.title }}
        </view>
    </view>
</template>

<script>
export default {
    name: 'CategoryList',
    
    props: {
        // 是否在搜索状态
        isSearch: {
            type: Boolean,
            default: false
        },
        // 分类列表数据
        categoryListSet: {
            type: Array,
            default: () => []
        }
    },
    
    methods: {
        // 处理分类跳转
        handleCategoryJump(item) {
            this.$emit('category-jump', item)
        }
    }
}
</script>

<style scoped>
.category-list {
    padding: 30px 5px 10px;
}

.category-item {
    width: calc((100vw - 30px) / 2);
    margin: 5px;
    height: 36px;
    border-radius: 18px;
}
</style>