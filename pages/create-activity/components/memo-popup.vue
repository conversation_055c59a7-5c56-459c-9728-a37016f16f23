<template>
    <!-- 备注信息展示弹窗 -->
    <uni-popup type="center" ref="memoPopup">
        <view class="memo-popup"
              :style="{background: item.back_ground ? `url(${item.back_ground})` : '#fff'}"
              @click="close">
            <!-- 标题 -->
            <view class="text-center color-title pb10" :style="{color: item.tilte_color || '#1c2438'}">
                {{ item.title }}
            </view>
            <!-- 内容 -->
            <view class="color-content" :style="{color: item.memo_color || '#495060'}">
                {{ item.memo || item.content }}
            </view>
            <!-- 关闭按钮 -->
            <view class="text-center color-sub pt15 font14">关闭</view>
        </view>
    </uni-popup>
</template>

<script>
export default {
    name: "memo-popup",
    data() {
        return {
            // 活动项数据
            item: {}
        }
    },
    methods: {
        /**
         * 打开弹窗
         * @param {Object} item - 活动项数据
         */
        open(item) {
            this.item = item
            this.$refs.memoPopup.open()
        },
        /**
         * 关闭弹窗
         */
        close() {
            this.$refs.memoPopup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.memo-popup {
    width: 80vw;
    border-radius: 10px;
    padding: 10px;
    background-size: 100%;
    background-repeat: no-repeat;
}
</style>
