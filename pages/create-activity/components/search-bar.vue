<template>
    <view class="search bg-white flex-kai p10" hover-class="disabled ? 'navigator-hover' : ''"
          @click="searchBarClick">
        <view class="input-view">
            <view class="search-icon left-icon flex-all-center">
                <uni-icons type="search" size="20" color="#bbbec4"/>
            </view>
            <template>
                <view v-if="disabled" class="input bg-background color-disabled">搜索活动</view>
                <input
                    v-else
                    class="input bg-background"
                    confirm-type="search"
                    v-model="search_keyword"
                    @confirm="search"
                    placeholder="搜索活动"
                    placeholder-style="color:#bbbec4"
                    auto-focus="true"
                />
            </template>
            <view class="search-icon right-icon flex-all-center font18" @click="search_keyword = ''">
                <uni-icons v-if="search_keyword" type="close" size="20" color="#bbbec4"/>
            </view>
        </view>
        <view class="search-go color-info" @click="search">搜索</view>
    </view>
</template>

<script>
export default {
    name: "search-bar",
    emits: ['search'],
    props: {
        disabled: {
            type: Boolean,
            default: true
        },
        justTemplate: {
            type: Boolean,
            default: false
        }
    },

    data() {
        return {
            search_keyword: ''
        }
    },

    methods: {
        searchBarClick() {
            if (this.disabled) {
                let url = './index?search=1'
                if (this.justTemplate) url += '&just_template=1'
                this.$uni.navigateTo(url)
            }
        },

        search() {
            if (this.disabled) return

            this.$emit('search', this.search_keyword)
        }
    }
}
</script>

<style lang="scss">
.search {
    width: 100%;
    box-sizing: border-box;
}

.search, .search .input {
    line-height: 40px;
}

.input-view {
    width: 100%;
    position: relative;
}

.search-icon {
    position: absolute;
    width: 30px;
    height: 40px;
    top: 0;
}

.left-icon {
    left: 0;
}

.right-icon {
    right: 0;
}

.search .input {
    width: 100%;
    height: 40px;
    padding: 0 30px;
    border-radius: 20px;
    box-sizing: border-box;
}

.search-go {
    width: 40px;
    min-width: 40px;
    text-align: right;
}
</style>