<template>
    <view class="page bg-background">
        <!-- 头部区域 -->
        <view class="top-view" v-if="login_success">
            <enter-notice-bar v-if="enterNoticeBarShow"/>
            <search-bar :just-template="just_template" :disabled="!is_search" @search="search"/>
        </view>

        <!-- 通知栏的高度 -->
        <view v-if="enterNoticeBarShow" style="height: 45px;"></view>

        <!-- 搜索框的高度 -->
        <view style="height: 60px;"></view>

        <!-- 搜索中状态 -->
        <loading-state v-if="in_search"/>

        <!-- 空搜索结果 -->
        <empty-search-result v-if="emptySearchResultShow"/>

        <!-- 活动列表容器 -->
        <activity-list-container 
            :just-template="just_template"
            :active-goods-list="active_goods_list"
            :active-template-list="active_template_list"/>

        <!-- 分类列表 -->
        <category-list 
            v-if="categoryListShow"
            :is-search="is_search"
            :category-list-set="category_list_set"
            @category-jump="categoryJump"/>

        <view v-if="just_template" class="create-activity-icon flex-all-center" @click="toCreateActivity">
            <uni-icons type="plusempty" size="30px" color="#ffffff"/>
        </view>
    </view>
</template>

<script>
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'

// 导入组件
import enterNoticeBar from './components/enter-notice-bar.vue'
import searchBar from './components/search-bar.vue'
import loadingState from './components/loading-state.vue'
import emptySearchResult from './components/empty-search-result.vue'
import activityListContainer from './components/activity-list-container.vue'
import categoryList from './components/category-list.vue'

// 导入mixins
import activityData from './mixins/activity-data.js'
import searchLogic from './mixins/search-logic.js'

export default {
    components: {
        enterNoticeBar,
        searchBar,
        loadingState,
        emptySearchResult,
        activityListContainer,
        categoryList
    },

    // 使用mixins复用逻辑
    mixins: [activityData, searchLogic],

    data() {
        return {
            login_success: false,
            just_template: false
        }
    },

    computed: {
        enterNoticeBarShow() {
            return !this.is_search && !this.just_template
        },

        categoryListShow() {
            return !this.is_search && !this.just_template
        }
    },

    onLoad(params) {
        // 检查是否是搜索模式
        if (params.search) this.is_search = true
        if (params.just_template) {
            this.just_template = true
            this.$uni.setNavigationBarTitle('活动模版')
        }
        
        this.$uni.showLoading('加载中...')
        
        // 登录并初始化数据
        login.uniLogin(err => {
            if (xwy_api.closed_create_active()) {
                uni.hideLoading()
                return this.$uni.showModal('不允许创建活动', {
                    success: () => this.$uni.navigateBackPage('pages/index/index')
                })
            }

            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.login_success = true

            if (this.just_template) {
                uni.hideLoading()
            } else {
                // 获取配置数据
                this.getConfSet()
            }
            // 非搜索模式下获取活动模板
            if (!this.is_search) this.getActiveTemplate()
        })
    },

    methods: {
        toCreateActivity() {
            this.$uni.navigateTo('/pages/create-activity/index')
        }
    },

    onShareAppMessage() {
        return {
            path: this.$uni.route(),
            title: '创建活动'
        }
    }
}
</script>

<style lang="scss" scoped>
.page {
    min-height: 100vh;
    padding-bottom: 150px;
    box-sizing: border-box;
}

.top-view {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 9;
}

.create-activity-icon {
    position: fixed;
    bottom: 100px;
    right: 20px;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: #2d8cf0;
    opacity: .8;
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .top-view {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }

    .create-activity-icon {
        right: calc((100vw - 500px) / 2 + 20px);
    }
}
/* #endif */
</style>
