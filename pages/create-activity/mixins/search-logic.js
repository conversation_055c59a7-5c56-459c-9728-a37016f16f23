// 搜索相关逻辑
export default {
    data() {
        return {
            // 搜索相关状态
            is_search: false,
            in_search: false,
            last_search_keyword: ''
        }
    },
    
    computed: {
        /**
         * 判断是否显示空搜索结果
         */
        emptySearchResultShow() {
            return this.is_search && 
                   !this.in_search && 
                   this.last_search_keyword && 
                   !this.active_goods_list.length && 
                   !this.active_template_list.length
        }
    },
    
    methods: {
        /**
         * 执行搜索
         * @param {string} keyword - 搜索关键词
         */
        async search(keyword = '') {
            // 清空当前列表
            this.active_template_list = []
            this.active_goods_list = []

            // 设置搜索中状态
            this.in_search = true
            
            try {
                // 并行搜索活动模板和活动类型
                const [active_template_list, active_goods_list] = await Promise.all([
                    this.getActiveTemplateList(keyword),
                    Promise.resolve(this.all_active_goods_list.filter(item => 
                        item.show && item.title.includes(keyword)
                    ))
                ])
                
                // 更新搜索结果
                this.active_template_list = active_template_list
                this.active_goods_list = active_goods_list
                this.last_search_keyword = keyword
                
            } catch (error) {
                console.error('搜索出错:', error)
                this.$uni.showToast('搜索出错，请重试')
            } finally {
                // 结束搜索状态
                this.in_search = false
            }
        },

        /**
         * 清空搜索结果
         */
        clearSearchResults() {
            this.active_template_list = []
            this.active_goods_list = []
            this.last_search_keyword = ''
        },

        /**
         * 重置搜索状态
         */
        resetSearchState() {
            this.is_search = false
            this.in_search = false
            this.last_search_keyword = ''
        }
    }
}