// 活动数据管理相关逻辑
const app = getApp()

export default {
    data() {
        return {
            // 活动数据相关状态
            active_goods_list: [],
            active_template_list: [],
            category_list_set: [],
            all_active_goods_list: [], // 用于搜索的完整活动类型列表
            evn_version: app.globalData['evn_version']
        }
    },
    
    methods: {
        /**
         * 获取活动配置数据
         */
        async getConfSet() {
            const conf_set = await this.xwy_api.getActivityTypeConfSet()

            uni.hideLoading()

            if (!conf_set) return this.$uni.redirectTo('/pages/activity/admin/add_or_edit?name=健步走活动')

            const {active_goods_list: a_list, category_list_set} = conf_set

            if (!a_list.length && !category_list_set.length)
                return this.$uni.redirectTo('/pages/activity/admin/add_or_edit?name=健步走活动')

            // 添加测试环境的任务闯关类型
            const push_types = 25
            if (app.globalData['evn_version'] !== 'release' && !a_list.some(v => v.types === push_types)) {
                a_list.unshift({
                    back_ground: "https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/window/run5.png",
                    logo: "",
                    memo: "任务闯关 任务闯关 任务闯关",
                    memo_color: "",
                    show: 1,
                    tilte_color: "",
                    title: "任务闯关",
                    types: push_types
                })
            }

            // 根据是否搜索状态设置不同的数据
            if (this.is_search) {
                this.all_active_goods_list = a_list
            } else {
                this.active_goods_list = a_list
                this.category_list_set = category_list_set
            }
        },

        /**
         * 获取活动模板数据
         */
        async getActiveTemplate() {
            this.active_template_list = await this.getActiveTemplateList()
        },

        /**
         * 获取活动模板列表
         * @param {string} title - 搜索标题
         */
        async getActiveTemplateList(title = '') {
            const res = await this.xwy_api.request({
                url: 'admin.flat.active.sport_step.AdminActiveData/flat_active_list',
                data: {
                    page: 1,
                    perpage: 1000,
                    title
                }
            })

            const list = res?.data?.list?.data || []
            return list.filter(item => {
                // show_types 【1】不限制 小程序和H5都显示 【2】只显示在小程序  【3】只显示在H5 【4】小程序和H5都不显示
                const show_types = item.show_types
                if (show_types !== 1 && show_types !== 2) return false

                const shopid = item.conf_json?.shopid || null
                return shopid === app.globalData.who
            }).map(item => ({
                active_id: item.active_id,
                active_types: item.conf_json?.active_set?.types || null,
                content: item.conf_json.content || '',
                title: item.title || '',
                logo: item.conf_json.small_app_logo || item.logo || ''
            }))
        },

        /**
         * 分类跳转处理
         * @param {object} item - 分类项
         */
        categoryJump(item) {
            const options = {
                1: () => this.$uni.navigateTo('/pages/other/webview?url=' + item.page),
                2: () => this.$uni.navigateTo('/pages/other/contact'),
            }
            if (!options[item.types]) return this.$uni.showToast('没有跳转类型 - ' + item.types)
            options[item.types]()
        }
    }
}