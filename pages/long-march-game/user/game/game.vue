<template>
    <view class="page">
        <view class="back flex-all-center" hover-class="navigator-hover" @click="pageBack">
            <text class="iconfont icon-go-back"></text>
        </view>
        
        <view v-if="backgroundAudioShow" class="background-audio flex-all-center" 
              :class="{'rotation-animation': backgroundAudioStatus === 'play'}"
              hover-class="navigator-hover" @click="changeBackgroundMusicPlayStatus">
            <text :class="'iconfont icon-background-music-' + (backgroundAudioStatus === 'play' ? 'play' : 'pause')"></text>
        </view>
        
       
        <image v-if="level_bgc_show" class="level-bg-image" :src="level_bgc" mode="aspectFill"/>

        <uni-transition :show="optionsShow" mode-class="fade">
            <view v-if="options && options.length" class="options">
                <view class="option-item" v-for="(item, index) in options" :key="index"
                      hover-class="navigator-hover" @click="chooseOption(item)">{{ item.text }}</view>
            </view>
        </uni-transition>

        <uni-transition :show="optionsDescriptionShow" mode-class="fade">
            <view class="subtitle">
                <view>{{ currentLevel.conf.options_description }}</view>
            </view>
        </uni-transition>

        <uni-transition :show="subtitleShow" mode-class="fade">
            <view class="subtitle" @click="skipSubtitle">
                <view>
                    <text space="nbsp">{{ subtitle }}</text>
                </view>
                <view class="skip-icon">
                    <text class="iconfont icon-fast-forward color-sub"></text>
                </view>
            </view>
        </uni-transition>
        
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'

export default {
    data() {
        return {
            currentLevel: null,
            level_bgc_show: false,
            sceneIndex: 0,
            subtitle: '',
            subtitleShow: false,
            optionsShow: false,
            backgroundAudioStatus: 'loading'
        }
    },
    
    computed: {
        level_bgc() {
            if (!this.currentLevel) return ''
            const scene = this.currentLevel?.conf?.scenes?.[this.sceneIndex]
            return scene?.image || ''
        },
        options() {
            return this.currentLevel?.conf?.options || []
        },
        optionsDescriptionShow() {
            return this.optionsShow && this.currentLevel?.conf?.options_description
        },
        backgroundAudioShow() {
            const status = this.backgroundAudioStatus
            return status !== 'loading' && status !== 'error'
        }
    },
    
    watch: {
        level_bgc(val) {
            this.level_bgc_show = false
            if (val) this.$nextTick(() => this.level_bgc_show = true)
        }
    },

    onLoad(params) {
        this.active_id = params.id
        this.$uni.showLoading('场景加载中...')
        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getLevelList(Number(params['level_id'] || 0))
        })
    },
    
    onUnload() {
        this.clearSubtitleInterval()
        this.destroyAudio()
    },

    methods: {
        
        async getLevelList(level_id) {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_map_point_list',
                data: {
                    active_id: this.active_id
                }
            })
            uni.hideLoading()
            
            const levelList = res.data?.['map_point_list']
            if (!levelList.length) return this.$uni.showModal('没有关卡数据', {success: () => this.back()})
            
            this.createAudio()
            
            this.levelList = levelList
            this.$nextTick(() => this.nextLevel(level_id))
        },

        createAudio() {
            this.createSubtitleAudio()
            this.createBackgroundAudio()
        },
        createSubtitleAudio() {
            this.subtitleAudio ||= uni.createInnerAudioContext()
        },
        async createBackgroundAudio() {
            let activityDetails = app.globalData['activity_detail']
            if (!activityDetails) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.active_id
                    },
                })
                activityDetails = res?.data?.active_details
            }
            
            const {type, src} = activityDetails?.conf?.active?.game_background_music
            let audioSrc = this.xwy_config.object_storage_url + 'music/sport/long-march-game/background-music.mp3'
            if (type === 0) return
            if (src) audioSrc = src
            this.backgroundAudio ||= uni.createInnerAudioContext()
            this.onBackgroundAudio()
            this.backgroundAudio.src = audioSrc
            this.backgroundAudio.loop = true
        },

        onBackgroundAudio() {
            const audio = this.backgroundAudio
            audio.onCanplay(() => {
                this.backgroundAudioStatus = 'canplay'
                audio.play()
            })
            audio.onPlay(() => this.backgroundAudioStatus = 'play')
            audio.onPause(() => this.backgroundAudioStatus = 'pause')
            audio.onStop(() => this.backgroundAudioStatus = 'stop')
            audio.onError(err => {
                this.backgroundAudioStatus = 'error'
                console.log('🎵backgroundAudio onError', err)
            })
        },

        changeBackgroundMusicPlayStatus() {
            const {backgroundAudioStatus: status, backgroundAudio: audio} = this
            status === 'play' ? audio.pause() : audio.play()
        },

        destroyAudio() {
            this.subtitleAudio && this.subtitleAudio.destroy()
            this.backgroundAudio && this.backgroundAudio.destroy()
        },
        
        clearLevelData() {
            this.clearSubtitleInterval()
            this.sceneIndex = 0
            this.subtitleShow = false
            this.subtitleIndex = 0
            this.optionsShow = false
        },
        
        nextLevel(levelId = 0) {
            this.isGameOver = false
            if (levelId === 0 && this.levelList[0]?.id) levelId = this.levelList[0].id
            const currentLevel = this.levelList.find(item => item.id === levelId)
            if (!currentLevel) return this.$uni.showModal('没有找到关卡数据', {success: () => this.back()})
            this.currentLevel = currentLevel
            
            this.clearLevelData()
            this.nextScene(this.sceneIndex)
        },
        
        
        nextScene(sceneIndex) {
            const conf = this.currentLevel?.conf
            if (sceneIndex >= conf?.scenes?.length) {
                if (this.currentLevel.sort_num === 10000) return this.theEnd()
                if (conf['is_game_over']) return this.gameOver()
                return this.showOptions()
            }

            this.sceneIndex = sceneIndex
            this.resetSubtitle()
        },
        
        resetSubtitle() {
            this.clearSubtitleInterval()
            this.subtitleShow = false
            this.subtitleIndex = 0
            this.optionsShow = false
            setTimeout(() => this.nextSubtitle(), 500)
        },
        
        nextSubtitle() {
            const {subtitleIndex, sceneIndex} = this
            const subtitles = this.currentLevel?.conf?.scenes?.[sceneIndex]?.subtitles || []
            if (subtitleIndex >= subtitles.length) {
                this.subtitleShow = false
                return this.nextScene(sceneIndex + 1)
            }
            this.showSubtitle(subtitles[subtitleIndex])
        },
        
        showSubtitle(subtitle) {
            this.clearSubtitleInterval()
            this.subtitleShow = true
            this.subtitle = ''
            let index = 0
            this.subtitleAudio.src = this.xwy_config.object_storage_url + 'music/sport/long-march-game/kakaka.mp3'
            this.subtitleAudio.loop = true
            this.subtitleAudio.play()
            this.showSubtitleInterval = setInterval(() => {
                this.subtitle = subtitle.slice(0, index)
                index++
                if (index > subtitle.length) this.clearSubtitleInterval()
            }, 80)
        },
        
        clearSubtitleInterval() {
            this.subtitleAudio.stop()
            clearInterval(this.showSubtitleInterval)
            this.showSubtitleInterval = null
        },

        skipSubtitle() {
            // 防止连续点击导致this.showSubtitleInterval定时器没有清除
            setTimeout(() => {
                if (this.showSubtitleInterval) {
                    this.clearSubtitleInterval()
                    this.subtitle = this.currentLevel?.conf?.scenes?.[this.sceneIndex]?.subtitles?.[this.subtitleIndex] || ''
                    return
                }

                this.subtitleIndex++
                this.nextSubtitle()
            }, 100)
        },

        showOptions() {
            if (!this.options.length) {
                const related_level = this.currentLevel?.conf?.related_level
                if (related_level) return this.nextLevel(related_level)
            }
            
            this.optionsShow = true
        },

        chooseOption(item) {
            this.optionsShow = false
            if (item.related_levels?.length) {
                // 因为有消失动画，所以延迟执行，立即执行的话会显示下一关卡场景的选项
                setTimeout(() => {
                    this.nextLevel(this.getLevelIdProbability(item.related_levels))
                }, 500)
            }
        },

        
        getLevelIdProbability(levels) {
            // 打乱数组，增加随机性
            for (let i = levels.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1)); // 这里必须要分号
                [levels[i], levels[j]] = [levels[j], levels[i]]
            }
            
            const total = levels.reduce((total, item) => total + Number(item.probability), 0)
            let random = Math.random() * total
            
            for (let i = 0; i < levels.length; i++) {
                random -= levels[i].probability
                if (random <= 0) return levels[i].id
            }
        },

        async theEnd() {
            this.$uni.showLoading('保存游戏进度...')
            await this.saveLevel()
            uni.hideLoading()
            const res = await this.$uni.showModal('恭喜你通关成功!', {cancelText: '返回', confirmText: '重玩'})
            res.confirm ? this.nextLevel() : this.back()
        },
        
        gameOver() {
            this.isGameOver = true
            this.saveLevel()
            this.$uni.showModal('游戏结束', {
                cancelText: '返回',
                confirmText: '重玩',
                success: res => res.confirm ? this.nextLevel() : this.back()
            })
        },

        async pageBack() {
            const res = await this.$uni.showModal('确定离开?', {showCancel: true})
            if (!res.confirm) return
            this.$uni.showLoading('保存游戏进度...')
            await this.saveLevel()
            uni.hideLoading()
            this.back()
        },
        
        back() {
            this.clearSubtitleInterval()
            if (getCurrentPages().length > 1) return this.$uni.navigateBack()
            this.$uni.navigateBackPage(`pages/long-march-game/user/details?id=${this.active_id}`)
        },
        
        
        
        async saveLevel() {
            const levelID = this.isGameOver ? 0 : this.currentLevel?.id
            console.log('保存关卡数据', levelID)
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.long_march_game.user/set_user_rushed_round',
                data: {
                    active_id: this.active_id,
                    id: levelID
                }
            })
            console.log('保存关卡数据', res)
            if (res?.status === 1) this.getOpenerEventChannel().emit?.('uploadUserDetails')
        }
    }
}
</script>

<style lang="scss">
.page {
    width: 100vw;
    height: 100vh;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
    background-color: #000;
}

.back, .background-audio {
    position: absolute;
    width: 40px;
    height: 40px;
    z-index: 99;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;

    .iconfont {
        font-size: 28px;
        color: #fff;
    }
}

.back {
    top: 10px;
    left: 10px;
}

.background-audio {
    top: 50px;
    right: 10px;
}

.rotation-animation {
    animation: rotation 5s infinite linear;
}
@keyframes rotation {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}


.level-bg-image {
    width: 100vw;
    height: 100vh;
    display: block;
    animation: bg-image-animation .5s;
}
@keyframes bg-image-animation {
    0% {
        border-radius: 50vh;
        transform: scale(.3);
    }
    100% {
        border-radius: 0;
        transform: scale(1);
    }
}

.options {
    $bottom: calc(100rpx + 20px);
    position: absolute;
    bottom: $bottom;
    left: 10px;
    width: calc(100% - 20px);
    height: calc(100vh - #{$bottom});
    padding-top: 10rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .option-item {
        background-color: rgba(255, 255, 255, 0.9);
        box-shadow: 0 0 5px #fff;
        border-radius: 5px;
        width: 50vw;
        min-height: 32rpx;
        line-height: 32rpx;
        text-align: center;
        margin-bottom: 12rpx;
        font-size: 20rpx;
    }
}

.subtitle {
    position: absolute;
    bottom: 20px;
    left: 10px;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 0 5px #fff;
    border-radius: 5px;
    width: calc(100% - 20px);
    min-height: 100rpx;
    max-height: 80vh;
    padding: 10px 30px;
    box-sizing: border-box;
    font-size: 20rpx;
    
    .skip-icon {
        width: 24rpx;
        height: 24rpx;
        line-height: 24rpx;
        text-align: center;
        position: absolute;
        right: 0;
        bottom: 0;
        animation: up-down 1.2s infinite;
        
        .iconfont {
            font-size: 20rpx;
        }
    }
    
    @keyframes up-down {
        0% {
            transform: translateY(0) rotate(90deg);
        }
        50% {
            transform: translateY(-10px) rotate(90deg);
        }
        100% {
            transform: translateY(0) rotate(90deg);
        }
    }
}

</style>