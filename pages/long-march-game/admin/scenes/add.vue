<template>
    <view class="page">
        <uni-forms label-position="top" :label-width="200">
            <uni-forms-item label="场景图片" required>
                <view @click="changeImage">

                    <image v-if="image" class="scene-image" :src="image" mode="aspectFill"/>
                    <view v-else class="scene-image flex-all-center bg-background">
                        <uni-icons type="images-filled" size="100" color="#dddee1"/>
                    </view>
                </view>
            </uni-forms-item>

            <uni-forms-item label="场景字幕">
                <view v-for="(item, index) in subtitles" :key="index" class="flex-kai pt5">
                    <view style="width: 100%;">
                        <uni-easyinput v-model="subtitles[index]" type="textarea" placeholder="请输入场景字幕" maxlength="200"/>
                    </view>
                    <view @click="deleteSubtitle(index)">
                        <uni-icons type="trash" size="18" color="#ed3f14"/>
                    </view>
                </view>
                <view class="flex-all-center pl10">
                    <view class="p10 color-light-primary" @click="addSubtitle">添加字幕</view>
                </view>
            </uni-forms-item>
        </uni-forms>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view class="save-btn color-white text-center font18 bg-primary" @click="validate">保存</view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            image: '',
            subtitles: [],
        }
    },
    

    onLoad() {
        this.getSceneInfo()
    },

    methods: {
        getSceneInfo() {
            this.getOpenerEventChannel().on('levelDetails', data => {
                this.scene_index = data.scene_index
                this.levelDetails = data.levelDetails
                if (this.scene_index !== null) {
                    const scene = this.levelDetails.conf.scenes[this.scene_index]
                    this.image = scene.image || ''
                    this.subtitles = scene.subtitles || []
                }
            })
        },

        changeImage() {
            this.$uni.navigateTo('/pages/other/image_upload_or_select', {
                events: {
                    newImg: src => {
                        this.image = src
                    }
                }
            })  
        },

        addSubtitle() {
            this.subtitles.push('')
        },

        deleteSubtitle(index) {
            this.subtitles.splice(index, 1)
        },

        validate() {
            if (this.in_save) return
            this.in_save = true
            if (!this.image) {
                this.in_save = false
                return this.$uni.showToast('请上传场景图片')
            }
            
            this.save()
        },

        async save() {
            const levelDetails = this.levelDetails
            levelDetails.conf.scenes = levelDetails.conf.scenes || []
            
            const scene = {
                image: this.image,
                subtitles: JSON.parse(JSON.stringify(this.subtitles))
            }
            scene.subtitles = scene.subtitles.filter(item => item) // 删除subtitles中的空字符串
            if (this.scene_index === null) {
                levelDetails.conf.scenes.push(scene)
            } else {
                levelDetails.conf.scenes[this.scene_index] = scene
            }
            levelDetails.conf = this._utils.base64['encode'](JSON.stringify(levelDetails.conf))

            this.$uni.showLoading('保存中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/add_update_map_point',
                data: {
                    ...levelDetails
                }
            })
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res.info || '保存失败')
            this.$uni.showToast('保存成功', 'success')
            
            this.updateLevel()
        },

        updateLevel() {
            const level = JSON.parse(JSON.stringify(this.levelDetails))
            level.conf = JSON.parse(this._utils.base64['decode'](level.conf))
            this.getOpenerEventChannel().emit('upload', level)
            this.$uni.navigateBack(1, {delay: 1000})
        }
    }
}
</script>

<style lang="scss">
.page {
    padding-bottom: 100px;
}

.uni-forms {
    padding: 10px;
}

.scene-image {
    width: calc(100vw - 20px);
    height: calc((100vw - 20px) / 2);
    border-radius: 10px;
}

.bottom-btn-view {
    position: fixed;
    z-index: 99999;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.save-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}
</style>