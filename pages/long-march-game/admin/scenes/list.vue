<template>
    <view class="page bg-background">
        <view v-if="scenes && scenes.length" class="flex-kai bg-white font14">
            <view class="color-sub p10">共{{ scenes.length }}个场景</view>
            <view class="p10" @click="toAddOrEdit(null)">
                <text class="color-light-primary">添加场景</text>
                <uni-icons type="forward" size="14" color="#5cadff"/>
            </view>
        </view>
        
        <view class="list">
            <view class="item bg-white p10 radius10" v-for="(item, index) in scenes" :key="index">
                <image v-if="item.image" class="scene-image" :src="item.image" mode="aspectFill"/>
                <view v-if="item.subtitles && item.subtitles.length" class="ellipsis--l3">
                    <view v-for="(subtitle, i) in item.subtitles" :key="i">
                        <text class="color-content font14" space="nbsp">{{ subtitle }}</text>
                    </view>
                </view>
                <view class="tools-bar clearfix clear">
                    <view class="fr flex-row">
                        <view @click="toAddOrEdit(index)" class="edit">
                            <text class="iconfont icon-edit color-sub font20"></text>
                        </view>
                        <view class="delete" @click="deleteScene(index)">
                            <text class="iconfont icon-delete color-sub font20"></text>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <view v-if="!scenes || !scenes.length" class="text-center" style="padding-top: 15vh;">
            <text class="iconfont icon-map color-border" style="font-size: 100px;"></text>
            <view class="color-sub">该关卡未添加场景</view>
            <view class="flex-all-center pt15">
                <view class="add-team-btn color-white bg-primary text-center" @click="toAddOrEdit(null)">
                    添加场景
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            scenes: []
        }
    },

    onLoad() {
        this.getLevelDetails()
    },

    methods: {
        getLevelDetails() {
            this.getOpenerEventChannel().on('levelDetails', data => {
                this.levelDetails = data
                this.scenes = data.conf.scenes || []
            })
        },

        toAddOrEdit(index = null) {
            this.$uni.navigateTo('./add', {
                success: res => {
                    res.eventChannel.emit('levelDetails', {
                        scene_index: index,
                        levelDetails: JSON.parse(JSON.stringify(this.levelDetails))
                    })
                },
                events: {
                    upload: level => {
                        this.levelDetails = level
                        this.scenes = level.conf.scenes
                        this.updateLevel()
                    }
                }
            })
        },

        async deleteScene(index) {
            const modal = await this.$uni.showModal('确认删除该场景？', {showCancel: true})
            if (!modal.confirm) return
            const scenes = JSON.parse(JSON.stringify(this.scenes))
            scenes.splice(index, 1)
            const levelDetails = JSON.parse(JSON.stringify(this.levelDetails))
            levelDetails.conf.scenes = scenes
            levelDetails.conf = this._utils.base64['encode'](JSON.stringify(levelDetails.conf))
            
            this.$uni.showLoading('删除中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/add_update_map_point',
                data: {
                    ...levelDetails
                }
            })
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res.info || '删除失败')
            this.$uni.showToast('删除成功')
            
            this.scenes = scenes
            this.levelDetails = levelDetails
            this.updateLevel()
        },

        updateLevel() {
            this.getOpenerEventChannel().emit('refreshList')
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
}

.list {
    padding: 10px 0;
    
    .item {
        margin: 0 10px 10px;

        .scene-image {
            width: calc(100vw - 40px);
            height: calc((100vw - 40px) / 2);
            border-radius: 10px;
        }

        .tools-bar {
            border-top: 1px solid #eee;
            padding-top: 10px;
            margin-top: 10px;
        }
        
        .edit, .delete {
            width: 32px;
            height: 32px;
            line-height: 32px;
            text-align: center;
            border-radius: 50%;
            border: 1px solid #eee;
        }

        .edit {
            border: 1px solid #eee;
            margin-right: 10px;
        }

    }
}

.add-team-btn {
    width: 200px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
}
</style>