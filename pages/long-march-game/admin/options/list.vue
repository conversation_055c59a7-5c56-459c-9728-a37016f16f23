<template>
    <view class="page bg-background">
        <view v-if="options && options.length" class="flex-kai bg-white font14">
            <view class="color-sub p10">共{{ options.length }}个选项</view>
            <view class="p10" @click="toAddOrEdit(null)">
                <text class="color-light-primary">添加选项</text>
                <uni-icons type="forward" size="14" color="#5cadff"/>
            </view>
        </view>
        
        <view class="flex-kai p10 m10 radius10 bg-white" @click="optionsDescriptionPopupShow">
            <view :class="'flex-row font14 ' + (options_description ? 'color-content' : 'color-sub')">
                <view style="min-width: 70px;">选项描述:</view>
                <view class="pr10 ellipsis--l3">{{ options_description || '未填写' }}</view>
            </view>
            <view>
                <text class="iconfont icon-edit color-sub"></text>
            </view>
        </view>
        
        <uni-popup ref="optionsDescriptionPopup">
            <view class="options-description-popup bg-white radius10 p10">
                <view class="text-center font16 pb10">选项描述</view>
                <uni-easyinput type="textarea" v-model="edit_options_description" 
                               placeholder="请输入选项描述， 如：“现在到了你做出决策的时候，请选择”" maxlength="100"/>
                <view class="flex-all-center pt10">
                    <view class="big-btn" @click="optionsDescriptionConfirm">保存</view>
                </view>
                <view class="flex-all-center">
                    <view class="color-sub font14 p10" @click="$refs.optionsDescriptionPopup.close()">
                        取消
                    </view>
                </view>
            </view>
        </uni-popup>
        
        <view class="options">
            <view class="option p10 radius10 bg-white" v-for="(item, index) in options" :key="index">
                <view>{{ item.text }}</view>
                <view v-if="item.related_levels && item.related_levels.length" class="pt10 pb10">
                    <view v-for="(level, level_index) in item.related_levels" :key="level_index">
                        <view class="color-sub font12">
                            <text>·</text>
                            <text class="color-content">{{ level.name }}</text>
                            <text class="pl10">{{ level.probability }}%</text>
                        </view>
                    </view>
                </view>
                <view class="tools-bar clearfix clear">
                    <view class="fr flex-row">
                        <view @click="toAddOrEdit(index)" class="edit">
                            <text class="iconfont icon-edit color-sub font20"></text>
                        </view>
                        <view class="delete" @click="deleteOption(index)">
                            <text class="iconfont icon-delete color-sub font20"></text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        
        <view v-if="!options || !options.length" class="text-center" style="padding-top: 15vh;">
            <text class="iconfont icon-map color-border" style="font-size: 100px;"></text>
            <view class="color-sub">该关卡未添加选项</view>
            <view class="flex-all-center pt15">
                <view class="big-btn" @click="toAddOrEdit(null)">添加选项</view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            options_description: '',
            edit_options_description: '',
            options: []
        }
    },

    onLoad() {
        this.getLevelDetails()
    },

    methods: {
        getLevelDetails() {
            this.getOpenerEventChannel().on('levelDetails', data => {
                this.levelDetails = data
                this.options = data.conf.options || []
                this.options_description = data.conf.options_description || ''
            })
        },

        optionsDescriptionPopupShow() {
            this.edit_options_description = this.options_description
            this.$refs.optionsDescriptionPopup.open()  
        },
        
        optionsDescriptionConfirm() {
            if (this.edit_options_description === this.options_description) {
                this.$refs.optionsDescriptionPopup.close()
                return this.$uni.showToast('保存成功')
            }
            this.options_description = this.edit_options_description
            this.levelDetails.conf.options_description = this.options_description
            this.updateOptionsDescription()
        },
        
        async updateOptionsDescription() {
            this.$uni.showLoading('保存中...')
            const levelDetails = JSON.parse(JSON.stringify(this.levelDetails))
            levelDetails.conf = this._utils.base64['encode'](JSON.stringify(levelDetails.conf))
            const res = await this.updateLevelDetails(levelDetails)
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res.info || '保存失败')
            this.$refs.optionsDescriptionPopup.close()
            this.$uni.showToast('保存成功')
            this.updateLevel()
        },
        
        async updateLevelDetails(levelDetails) {
            return this.xwy_api.request({
                url: 'front.flat.sport_step.admin/add_update_map_point',
                data: {
                    ...levelDetails
                }
            })
        },

        toAddOrEdit(index = null) {
            const MAX_OPTIONS = 6
            if (index === null && this.options.length >= MAX_OPTIONS) 
                return this.$uni.showModal(`最多添加${MAX_OPTIONS}个选项`)
            
            this.$uni.navigateTo('./add', {
                success: res => {
                    res.eventChannel.emit('levelDetails', {
                        option_index: index,
                        levelDetails: JSON.parse(JSON.stringify(this.levelDetails))
                    })
                },
                events: {
                    upload: level => {
                        this.levelDetails = level
                        this.options = level.conf.options || []
                        this.updateLevel()
                    }
                }
            })
        },

        async deleteOption(index) {
            const modal = await this.$uni.showModal('确认删除该选项？', {showCancel: true})
            if (!modal.confirm) return
            const options = JSON.parse(JSON.stringify(this.options))
            options.splice(index, 1)
            const levelDetails = JSON.parse(JSON.stringify(this.levelDetails))
            levelDetails.conf.options = options
            levelDetails.conf = this._utils.base64['encode'](JSON.stringify(levelDetails.conf))

            this.$uni.showLoading('删除中...')
            const res = await this.updateLevelDetails(levelDetails)
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res.info || '删除失败')
            this.$uni.showToast('删除成功')

            this.options = options
            this.levelDetails = levelDetails
            this.updateLevel()
        },

        updateLevel() {
            setTimeout(() => this.getOpenerEventChannel().emit('refreshList'), 1000)
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
}

.options-description-popup {
    width: 320px;
}

.options {
    padding: 10px 0;

    .option {
        margin: 0 10px 10px;

        .tools-bar {
            border-top: 1px solid #eee;
            padding-top: 10px;
            margin-top: 10px;
        }

        .edit, .delete {
            width: 32px;
            height: 32px;
            line-height: 32px;
            text-align: center;
            border-radius: 50%;
            border: 1px solid #eee;
        }

        .edit {
            border: 1px solid #eee;
            margin-right: 10px;
        }

    }
}

.big-btn {
    width: 200px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
    color: #fff;
    background-color: #2d8cf0;
    text-align: center;
}
</style>