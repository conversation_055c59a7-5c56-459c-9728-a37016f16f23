<template>
    <view class="page">
        <uni-forms label-position="top" :label-width="200">
            <uni-forms-item label="选项文字" required>
                <uni-easyinput v-model="text" placeholder="请输入选项文字 30字内" maxlength="30"/>
            </uni-forms-item>

            <uni-forms-item label="关联关卡" required>
                <view class="related-level-item p10 radius10 m10 bg-background" 
                      v-for="(item, index) in related_levels" :key="index">
                    <view class="font16">
                        <view>关卡名称: {{ item.name }}</view>
                        <view>跳转概率: {{ item.probability }}</view>
                    </view>
                    <view class="clearfix clear">
                        <view class="fr flex-row color-sub font14">
                            <view class="pr10" @click="editOption(index)">修改</view>
                            <view class="pl10" @click="related_levels.splice(index, 1)">删除</view>
                        </view>
                    </view>
                </view>

                <view v-if="related_levels.length <= 10" class="flex-all-center pl10">
                    <view class="p10 color-light-primary" @click="addOption">关联关卡</view>
                </view>
            </uni-forms-item>
        </uni-forms>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view class="save-btn color-white text-center font18 bg-primary" @click="validate">保存</view>
        </view>
        
        <uni-popup ref="edit_related_level">
            <view class="edit-related-level-popup bg-white">
                <view class="text-center">关联关卡</view>
                <view>
                    <uni-forms label-position="top" :label-width="200">
                        <uni-forms-item label="关卡" required>
                            <view class="uni-forms-picker flex-row" @click="selectLevel">
                                <view class="picker-body">
                                    <text v-if="edit_related_level.name" class="picker-value">
                                        {{ edit_related_level.name }}
                                    </text>
                                    <text v-else class="color-sub font12">请选择需要关联的关卡</text>
                                </view>
                                <view class="picker-right-icon flex-all-center">
                                    <uni-icons type="forward" size="16" color="#dddddd"/>
                                </view>
                            </view>
                        </uni-forms-item>
                        
                        <uni-forms-item label="跳转概率" required>
                            <uni-easyinput v-model="edit_related_level.probability" type="number" 
                                           placeholder="请输入跳转到该关卡概率" maxlength="3"/>
                        </uni-forms-item>
                    </uni-forms>
                </view>
                
                <view class="flex-all-center">
                    <view class="save-btn color-white text-center bg-primary" @click="saveRelatedLevel">
                        确定
                    </view>
                </view>
                <view class="flex-all-center">
                    <view class="p10 font14 color-sub" @click="$refs.edit_related_level.close()">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    data() {
        return {
            text: '',
            related_levels: [],
            edit_related_level: {}
        }
    },


    onLoad() {
        this.getSceneInfo()
    },

    methods: {
        getSceneInfo() {
            this.getOpenerEventChannel().on('levelDetails', data => {
                this.option_index = data.option_index
                this.levelDetails = data.levelDetails
                if (this.option_index !== null) {
                    const option = this.levelDetails.conf.options?.[this.option_index] || {}
                    this.text = option.text || ''
                    this.related_levels = option.related_levels || []
                }
            })
        },

        addOption() {
            this.edit_related_level_index = null
            this.edit_related_level = {
                id: '',
                name: '',
                probability: ''
            }
            this.$refs.edit_related_level.open()
        },
        
        editOption(index) {
            this.edit_related_level = JSON.parse(JSON.stringify(this.related_levels[index]))
            this.edit_related_level_index = index
            this.$refs.edit_related_level.open()  
        },

        saveRelatedLevel() {
            if (!this.edit_related_level.id) return this.$uni.showToast('请选择需要关联的关卡')
            if (!this.edit_related_level.probability) return this.$uni.showToast('请输入跳转到该关卡概率')
            
            if (this.edit_related_level_index !== null) {
                this.$set(this.related_levels, this.edit_related_level_index, this.edit_related_level)
                this.related_levels[this.edit_related_level_index] = this.edit_related_level
            } else {
                this.related_levels.push(this.edit_related_level)
            }
            this.$refs.edit_related_level.close()
        },

        selectLevel() {
            const {active_id, id: level_id} = this.levelDetails
            this.$uni.navigateTo(`../levels/list?select=1&id=${active_id}&level_id=${level_id}`, {
                events: {
                    selectLevel: level => {
                        this.edit_related_level.id = level.id
                        this.edit_related_level.name = level.name
                    }
                }
            })  
        },

        validate() {
            if (this.in_save) return
            this.in_save = true
            
            const {text, related_levels} = this
            if (!text) return this.$uni.showToast('请输入选项文字')
            if (!related_levels.length) return this.$uni.showToast('请选择关联关卡')
            
            this.save()
        },

        async save() {
            const levelDetails = this.levelDetails
            levelDetails.conf.options ||= []

            const option = {
                text: this.text,
                related_levels: this.related_levels
            }
           
            if (this.option_index === null) {
                levelDetails.conf.options.push(option)
            } else {
                levelDetails.conf.options[this.option_index] = option
            }
            levelDetails.conf = this._utils.base64['encode'](JSON.stringify(levelDetails.conf))

            this.$uni.showLoading('保存中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/add_update_map_point',
                data: {
                    ...levelDetails
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res.info || '保存失败')
            this.$uni.showToast('保存成功', 'success')

            this.updateLevel()
        },

        updateLevel() {
            const level = JSON.parse(JSON.stringify(this.levelDetails))
            level.conf = JSON.parse(this._utils.base64['decode'](level.conf))
            this.getOpenerEventChannel().emit('upload', level)
            this.$uni.navigateBack(1, {delay: 1000})
        }
    }
}
</script>

<style lang="scss">
.page {
    padding-bottom: 100px;
}

.uni-forms {
    padding: 10px;
}

.uni-forms-picker {
    font-size: 14px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    height: 35px;
    line-height: 35px;
    padding-left: 10px;

    .picker-body {
        width: 100%;
    }

    .picker-right-icon {
        width: 30px;
    }

    .picker-value {
        color: #333;
    }

    .picker-placeholder {
        color: #bbbbbb;
        font-size: 12px;
    }
}

.bottom-btn-view {
    position: fixed;
    z-index: 99;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.save-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}

.edit-related-level-popup {
    width: 300px;
    padding: 10px;
    border-radius: 10px;
}
</style>