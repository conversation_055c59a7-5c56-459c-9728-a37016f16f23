<template>
    <view class="page">
        <uni-forms label-position="top" :label-width="200">
            <uni-forms-item label="关卡名称" required>
                <uni-easyinput v-model="name" placeholder="请输入关卡名称" maxlength="30"/>
            </uni-forms-item>

            <uni-forms-item label="本关是否为终点">

                <picker :range="['是', '否']" :value="isEnd ? 0 : 1" @change="isEndChange">
                    <view class="uni-forms-picker flex-row">
                        <view class="picker-body">
                            <text class="picker-value">{{ isEnd ? '是' : '否' }}</text>
                        </view>
                        <view class="picker-right-icon flex-all-center">
                            <uni-icons type="forward" size="16" color="#dddddd"/>
                        </view>
                    </view>
                </picker>
            </uni-forms-item>

            <uni-forms-item v-if="!isEnd" label="关卡排序">
                <uni-easyinput v-model="sort_num" type="sort_num" maxlength="4" @blur="sortNumBlur"/>
            </uni-forms-item>

            <template v-if="!isEnd">
                <uni-forms-item label="本关是否结束游戏">

                    <picker :range="pickerOptions.is_game_over" range-key="label"
                            :value="getPickerValue('is_game_over')"
                            @change="pickerChange($event, 'is_game_over')">
                        <view class="uni-forms-picker flex-row">
                            <view class="picker-body">
                                <text class="picker-value">{{ getPickerLabel('is_game_over') }}</text>
                            </view>
                            <view class="picker-right-icon flex-all-center">
                                <uni-icons type="forward" size="16" color="#dddddd"/>
                            </view>
                        </view>
                    </picker>
                </uni-forms-item>

                <template v-if="!conf.is_game_over">
                    <uni-forms-item label="本关是否有选项">

                        <picker :range="pickerOptions.is_options" range-key="label"
                                :value="getPickerValue('is_options')"
                                @change="pickerChange($event, 'is_options')">
                            <view class="uni-forms-picker flex-row">
                                <view class="picker-body">
                                    <text class="picker-value">{{ getPickerLabel('is_options') }}</text>
                                </view>
                                <view class="picker-right-icon flex-all-center">
                                    <uni-icons type="forward" size="16" color="#dddddd"/>
                                </view>
                            </view>
                        </picker>
                    </uni-forms-item>

                    <uni-forms-item v-if="!conf.is_options" label="跳转到下一关卡" required>

                        <view class="uni-forms-picker flex-row" @click="selectLevel">
                            <view class="picker-body">
                                <text v-if="conf.related_level" class="picker-value">
                                    {{ conf.related_level_name }}
                                </text>
                                <text v-else class="font14 color-sub">请选择下一关卡</text>
                            </view>
                            <view class="picker-right-icon flex-all-center">
                                <uni-icons type="forward" size="16" color="#dddddd"/>
                            </view>
                        </view>
                    </uni-forms-item>
                </template>
            </template>

        </uni-forms>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view class="save-btn color-white text-center font18 bg-primary" @click="save">保存</view>
        </view>
        
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'

export default {
    data() {
        return {
            name: '',
            sort_num: '',
            conf: {
                image: '',
                scenes: [],
                options: [],
                related_level: '',
                related_level_name: '',
                is_game_over: 0,
                is_options: 1
            },

            pickerOptions: {
                is_game_over: [
                    {label: '是', value: 1},
                    {label: '否', value: 0}
                ],
                is_options: [
                    {label: '是', value: 1},
                    {label: '否', value: 0}
                ],
            }
        }
    },

    watch: {
        'conf.is_game_over': function (val) {
            if (val) {
                this.conf.is_options = 0
                this.related_level = ''
            }
        }
    },

    computed: {
        isEnd() {
            return this.sort_num === 10000
        }
    },

    onLoad(e) {
        this.$uni.showLoading()
        this.active_id = e.active_id
        if (e.point_id) this.point_id = e.point_id
        if (e['have_end']) this.haveEnd = true

        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err['errTitle'] || '提示')
            }

            this.getActivityDetail()
        })
    },
    methods: {
        async getActivityDetail() {
            let activity_detail = app.globalData['activity_detail']

            if (!activity_detail || activity_detail.active_id !== this.active_id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {active_id: this.active_id}
                })
                activity_detail = res.data.active_details
            }


            if (this.point_id) return await this.getPointDetail()
            uni.hideLoading()
        },

        async getPointDetail() {
            this.$uni.setNavigationBarTitle('编辑关卡')

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/map_point_details',
                data: {id: this.point_id}
            })

            uni.hideLoading()

            if (!res?.data?.['map_point_details']) {
                return this.$uni.showModal(res && res.info || '关卡数据获取失败', {
                    success: () => uni.navigateBack()
                })
            }

            const detail = res.data['map_point_details']

            this.name = detail.name
            if (detail.sort_num) this.sort_num = detail.sort_num
            if (detail.conf) this.conf = detail.conf
        },

        isEndChange(e) {
            const isEnd = Number(e.detail.value) === 0
            if (isEnd) {
                if (this.haveEnd) return this.$uni.showToast('已有终点，不能再添加')
                // 把旧的排序先存起来，反悔或手贱不小心设置为终点的时候用，不用重新再设置
                this.oldSet = {
                    sort_num: this.sort_num,
                    is_options: this.conf.is_options,
                    options: JSON.stringify(this.conf.options),
                    is_game_over: this.conf.is_game_over,
                    related_level: this.conf.related_level,
                    related_level_name: this.conf.related_level_name
                }

                this.conf.is_options = 0
                this.conf.options = []
                this.conf.related_level = ''
                this.conf.related_level_name = ''
                this.sort_num = 10000
                return
            }

            if (this.oldSet) {
                this.sort_num = this.oldSet.sort_num === 10000 ? 0 : this.oldSet.sort_num
                this.conf.is_options = this.oldSet.is_options
                this.conf.options = JSON.parse(this.oldSet.options)
                this.conf.is_game_over = this.oldSet.is_game_over
                this.conf.related_level = this.oldSet.related_level
                this.conf.related_level_name = this.oldSet.related_level_name
            }
            
            this.sort_num = 0
            this.conf.is_options = 1
            this.conf.options = []
            this.conf.is_game_over = 0
            this.conf.related_level = ''
            this.conf.related_level_name = ''
        },

        sortNumBlur(e) {
            const errorToast = (num, title) => {
                this.sort_num = num
                this.$uni.showToast(title)
            }

            const sort_num = Number(e.detail.value)
            if (isNaN(sort_num)) return errorToast(0, '请输入数字')
            if (sort_num < 0) return errorToast(0, '排序不能小于0')
            if (sort_num > 9999) return errorToast(9999, '排序不能大于9999')
            this.sort_num = sort_num
        },

        getPickerValue(key) {
            let index = this.pickerOptions[key].findIndex(item => item.value === this.conf[key])
            if (index === -1) index = 0
            return index
        },
        getPickerLabel(key) {
            const value = this.conf[key]
            const item = this.pickerOptions[key].find(item => item.value === value)
            return item ? item.label : ''
        },
        pickerChange(e, key) {
            this.conf[key] = this.pickerOptions[key][Number(e.detail.value)].value
        },

        selectLevel() {
            let url = `../levels/list?select=1&id=${this.active_id}`
            if (this.point_id) url += `&level_id=${this.point_id}`
            this.$uni.navigateTo(url, {
                events: {
                    selectLevel: level => {
                        this.conf.related_level = level.id
                        this.conf.related_level_name = level.name
                    }
                }
            })
        },

        previewImage: urls => uni.previewImage({urls}),

        getConfSet() {
            const conf = this.conf
            if (!conf.is_game_over && !conf.is_options && !conf.related_level && !this.isEnd) {
                return this.$uni.showToast('请选择跳转到下一关卡')
            }
            return this._utils.base64['encode'](JSON.stringify(conf))
        },

        save() {
            if (!this.name) return this.$uni.showToast('请输入关卡名称')
            const conf = this.getConfSet()
            if (!conf) return

            const value = {
                name: this.name,
                active_id: this.active_id,
                sort_num: Number(this.sort_num || 0),
                conf
            }
            if (this.point_id) value.id = this.point_id

            this.saveAjax(value)
        },

        saveAjax(data) {
            this.$uni.showLoading('保存中...')

            data.access_token = app.globalData['access_token']

            this.xwy_api.ajax({
                url: 'front.flat.sport_step.admin/add_update_map_point',
                data,
                success: res => {
                    uni.hideLoading()
                    const type_text = this.id ? '修改' : '添加'

                    if (!res.status) return this.$uni.showModal(res.info || `${type_text}失败`)

                    this.$uni.showToast(`${type_text}成功`)
                    this.getOpenerEventChannel().emit('refreshList')
                    setTimeout(() => uni.navigateBack(), 1000)
                }
            })
        },


    }
}
</script>

<style lang="scss">
.page {
    padding-bottom: 100px;
    background-color: #fff;
}

.uni-forms {
    padding: 10px;
}

.uni-forms-picker {
    font-size: 14px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    height: 35px;
    line-height: 35px;
    padding-left: 10px;

    .picker-body {
        width: 100%;
    }

    .picker-right-icon {
        width: 30px;
    }

    .picker-value {
        color: #333;
    }

    .picker-placeholder {
        color: #bbbbbb;
        font-size: 12px;
    }
}

.uni-forms-item_my_label {
    color: #606266;
    font-size: 14px;
}


.bottom-btn-view {
    position: fixed;
    z-index: 99999;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.save-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}
</style>
