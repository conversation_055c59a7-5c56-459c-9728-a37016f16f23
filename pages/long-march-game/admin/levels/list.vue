<template>
    <view class="page bg-background">
        <template v-if="!loading">

            <template v-if="list.length">
                <view class="flex-kai bg-white">
                    <view class="color-sub p10 font14">共{{ list.length }}个关卡</view>
                    <view v-if="!is_select_level" @click="toAddOrEdit(false)" class="p10">
                        <text class="color-primary font14">添加关卡</text>
                        <uni-icons type="forward" size="14" color="#2d8cf0"/>
                    </view>
                </view>

                <view class="list">
                    <view class="item bg-white" :class="{'is-end': item.sort_num === 10000}" 
                          v-for="item in list" :key="item.id">
                        <view class="is-end-tag">终点</view>
                        <view class="item-msg">
                            <view class="color-title pb5">{{ item.name }}</view>
                            
                            <view v-if="item.sort_num !== 10000" class="color-content font14">
                                排序: {{ item.sort_num }}
                            </view>
                        </view>
                        <view class="tools-bar clear clearfix">
                            <view v-if="is_select_level" class="fr flex-row">
                                <view :class="'tools-bar-left-item ' + (is_select_level_id === item.id ? 'color-sub' : 'color-light-primary')" 
                                      @click="selectLevel(item)">
                                    选择关卡
                                </view>
                            </view>
                            
                            <template v-else>
                                <view class="fl flex-row">
                                    <view class="tools-bar-left-item color-light-primary" 
                                          @click="sceneManagement(item)">
                                        场景管理
                                    </view>
                                    <view class="tools-bar-left-item color-light-primary" 
                                          v-if="item.conf.is_options" @click="optionsManagement(item)">
                                        选项管理
                                    </view>
                                </view>

                                <view class="fr flex-row">
                                    <view @click="toAddOrEdit(item.id)" class="edit">
                                        <text class="iconfont icon-edit color-sub font20"></text>
                                    </view>
                                    <view class="delete" @click="deleteItem(item.id)">
                                        <text class="iconfont icon-delete color-sub font20"></text>
                                    </view>
                                </view>
                            </template>
                        </view>
                    </view>
                </view>
            </template>

            <view v-if="!list.length" class="text-center" style="padding-top: 15vh;">
                <text class="iconfont icon-map color-border" style="font-size: 100px;"></text>
                <view class="color-sub">该活动下暂无关卡</view>
                <view v-if="!is_select_level" class="flex-all-center pt15">
                    <view class="add-team-btn color-white bg-primary text-center" 
                          @click="toAddOrEdit(false)">
                        创建关卡
                    </view>
                </view>
            </view>
        </template>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'


export default {
    data() {
        return {
            id: '',
            clearance_type: 0,
            loading: true,
            list: [],
            is_select_level: false,
            is_select_level_id: 0
        }
    },
    onLoad(e) {
        this.$uni.showLoading()

        if (e.select) {
            this.is_select_level = true
            if (e['level_id']) this.is_select_level_id = Number(e['level_id'])
        }
        this.id = e.id
        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err['errTitle'] || '提示')
            }

            this.getList()
        })
    },
    
    methods: {

        getListInit() {
            this.loading = true
            this.$uni.showLoading()
            this.list = []
            this.getList()
        },

        async getList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_map_point_list',
                data: {
                    active_id: this.id
                },
            })

            uni.hideLoading()
            this.loading = false

            if (!res.status) return

            if (res.data?.['map_point_list']?.length) this.list = res.data['map_point_list']
        },

        selectLevel(item) {
            if (this.is_select_level_id && this.is_select_level_id === item.id) {
                return this.$uni.showToast('不能关联自己')
            }
            this.getOpenerEventChannel().emit('selectLevel', {id: item.id, name: item.name})
            this.$uni.navigateBack()
        },
        
        
        getLevelDetails(item) {
            const levelDetails = JSON.parse(JSON.stringify(item))
            const deleteKeys = ['create_time', 'shopid', 'status', 'update_time', 'userid']
            deleteKeys.forEach(key => delete levelDetails[key])
            return levelDetails
        },

        sceneManagement(item) {
            this.$uni.navigateTo('../scenes/list', {
                success: res => res.eventChannel.emit('levelDetails', this.getLevelDetails(item)),
                events: {
                    refreshList: this.getListInit
                }
            })
        },

        optionsManagement(item) {
            this.$uni.navigateTo('../options/list', {
                success: res => res.eventChannel.emit('levelDetails', this.getLevelDetails(item)),
                events: {
                    refreshList: this.getListInit
                }
            })
        },

        toAddOrEdit(id) {
            let url = `add?active_id=${this.id}`
            if (id) url += `&point_id=${id}`
            
            // 如果列表已存在终点点位，并且终点点位不是点前编辑的点位，则传递参数have_end=1，不允许修改为终点点位
            const end_id = this.list.find(item => item.sort_num === 10000)?.id
            if (end_id && end_id !== id) url += '&have_end=1'
            
            uni.navigateTo({
                url,
                events: {
                    refreshList: this.getListInit
                }
            })
        },


        deleteItem(id) {
            this.deleteConfirm([id])
        },

        deleteConfirm(ids) {
            uni.showModal({
                title: '提示',
                content: `确定删除${ids.length > 1 ? '选中的' : '该'}关卡?`,
                confirmText: '删除',
                cancelColor: '#80848f',
                confirmColor: '#ed3f14',
                success: res => {
                    if (res.confirm) this.deleteAjax(ids)
                }
            })
        },

        async deleteAjax(ids) {
            uni.showLoading({
                title: '删除中...'
            })

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/del_map_point',
                data: {
                    active_id: this.id,
                    ids: ids.join(',')
                }
            })

            uni.hideLoading()
            if (res?.status !== 1) return this.$uni.showModal(res.info || '删除失败')

            this.list = this.list.filter(item => {
                return !ids.find(v => item.id === v)
            })
            this.$uni.showToast('删除成功', 'success')
        },

    }
}
</script>

<style scoped lang="scss">
.page {
    min-height: 100vh;
    padding-bottom: 10px;
    box-sizing: border-box;
}

.list {
    .item {
        margin: 10px;
        padding: 10px;
        border-radius: 10px;
        position: relative;
        overflow: hidden;

        .is-end-tag {
            display: none;
        }
        
        .item-msg {
            min-height: 55px;
        }
    }
    
    .is-end {
        .is-end-tag {
            display: block;
            position: absolute;
            right: -21px;
            top: 8px;
            width: 80px;
            line-height: 24px;
            font-size: 14px;
            text-align: center;
            color: #fff;
            background-image: linear-gradient(45deg, #f35858, #f62121);
            transform: rotate(45deg);
        }

        .item-msg {
            padding-right: 50px;
        }
    }
}


.tools-bar {
    border-top: 1px solid #eee;
    padding-top: 10px;
    
    .tools-bar-left-item {
        padding-right: 10px;
        line-height: 34px;
        font-size: 14px;
    }

    .edit, .delete {
        width: 32px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border-radius: 50%;
        border: 1px solid #eee;
    }

    .edit {
        border: 1px solid #eee;
        margin-right: 10px;
    }
}

.add-team-btn {
    width: 200px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
}
</style>
