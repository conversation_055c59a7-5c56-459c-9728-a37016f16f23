const words = '文化兴则国家强民族要坚持以人为中心统筹发展和安全系观念法治思维构建类命运共同体对美好生活的向往就是我们奋斗目标时代出卷答阅幸福不会从天降梦想自动成真只有断拼搏才能收获正山再高上攀总登顶路长走下去定到达无论面多大困难都峰勇气懈毅力直前实现忘初牢记使必须危存亡乐忧刻保警醒振精神于进行具许新历史特点伟争始终清头脑华复而努方得在征程砥砺贡献量来激励用勤劳智慧创造属字珠玑句铿锵它像一旗帜引领富习主席金犹如明灯照亮之者让携手这名言提道己更远指绿水青银话比喻阐述了态设重性导色另广流传告诉需通过辛取呼唤担当未曾讲空谈误干邦聋聩与其坐起脚踏地抓把蓝图变辉煌鞭策着尚简洁深功每听书说感见卓识仅揭示环境护可续乎察寄语他诫志入满足懂遍那么加倍温暖继扬畏艰殷切期望鼓珍惜光学升年伊回响喜悦认理诠释彼岸沿确箴塔触受党情怀近平音声调然赋予宝贵财守园南灵慰藉翅膀摘录信够抵风格接舞推个数也春雨滋润田追求际践社义核价值写壮丽篇章掷事业繁荣康拂昂'


export default function supplementaryWords(num) {
    const arr = []
    while (arr.length < num) {
        const index = Math.floor(Math.random() * words.length)
        if (!arr.includes(words[index])) arr.push(words[index])
    }
    return arr
}