<template>
    <view class="page" :style="pageStyle">
        <!--transitionClass: 题目切换时，当前题目是出现还是消失的动画-->
        <view class="couplet transition-element" :class="transitionClass">
            <view class="down-couplet" :style="{backgroundImage: `url(${up_down_link_bg});`}">
                <view v-for="(item, index) in couplet.down" :key="index" class="couplet-text-item">
                    <view v-if="item.is_space" class="couplet-text-space" :style="{borderColor: words_color}"
                          :class="{'current-space': fillCouplet === 'down' && index === fillSpaceIndex}">
                    </view>
                    <view :style="{color: words_color}">{{ item.text }}</view>
                </view>
            </view>
            <view class="candidate-words"
                  :style="{height: 200 * candidateWords.length - (40 * (candidateWords.length - 1)) + 'rpx'}">
                <view v-for="(item, index) in candidateWords" :key="item.key" class="candidate-words-item"
                      :style="{top: `-${index*40}rpx`, zIndex: candidateWords.length - index,         backgroundImage: `url(${word_bg});`}">
                    <view :class="'choose-word-' + item.key + (item.is_choose ? ' is-choose' : '')"
                          :animation="item.key === move_word_key ? candidateWordsAnimationData : null"
                          :style="{color: words_color}" @click="chooseWord(index)">
                        {{ item.word }}
                    </view>
                </view>
            </view>
            <view class="up-couplet" :style="{backgroundImage: `url(${up_down_link_bg});`}">
                <view v-for="(item, index) in couplet.up" :key="index" class="couplet-text-item">
                    <view v-if="item.is_space" class="couplet-text-space" :style="{borderColor: words_color}"
                          :class="{'current-space': fillCouplet === 'up' && index === fillSpaceIndex}"></view>
                    <view :style="{color: words_color}">{{ item.text }}</view>
                </view>
            </view>
        </view>

        <game-result-popup ref="resultPopup" :show-ad="showAD"/>
    </view>
</template>

<script>
import supplementaryWords from './confused-words'

const animation = uni.createAnimation({
    duration: 500,
    timingFunction: 'ease',
    delay: 0
})

export default {
    data() {
        return {
            transitionClass: '',  // 题目切换时，当前题目是出现还是消失的动画
            question_index: -1,
            fillCouplet: 'up',  // 当前需要填字的方框是上联还是下联
            fillSpaceIndex: -1,  // 当前需要填字的方框
            couplet: {
                up: [],
                down: []
            },
            candidateWords: [],
            showAD: false,
            bg_img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/red-small-games/fill-couplet/bg.png',
            word_bg: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/red-small-games/fill-couplet/word-bg.png',
            up_down_link_bg: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/red-small-games/fill-couplet/sxl-bg.png',
            words_color: '#000000',
            move_word_key: '',    // 点前点击的候选字，需要移动到方框内的候选字的key标识
            candidateWordsAnimationData: {},  // 候选字移动到方框内的动画对象
        }
    },

    computed: {
        pageStyle() {
            if (!this.bg_img) return ''
            return `background-image: url(${this.bg_img});`
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.point_id = params.point_id
        this.category_id = params.category_id
        this.num = Number(params.num)
        if (params.show_ad) this.showAD = true
        if (params.bg_img) this.bg_img = params.bg_img
        if (params.title) this.$uni.setNavigationBarTitle(params.title)

        this.getImageSet()
        this.getQuestionList()
    },

    methods: {
        getImageSet() {
            this.getOpenerEventChannel().once('imageSet', set => {
                let {bg_img, fill_couplet, navigation_bar} = set || {}

                if (fill_couplet) {
                    if (fill_couplet.bg_img) bg_img = fill_couplet.bg_img
                    const {up_down_link_bg, candidate_words_bg, words_color} = fill_couplet
                    if (up_down_link_bg) this.up_down_link_bg = up_down_link_bg
                    if (candidate_words_bg) this.word_bg = candidate_words_bg
                    if (words_color) this.words_color = words_color
                }


                if (navigation_bar) {
                    const {font_color = '#ffffff', bg_color = '#45454d'} = navigation_bar
                    uni.setNavigationBarColor({
                        frontColor: font_color,
                        backgroundColor: bg_color
                    })
                }

                this.bg_img = bg_img
            })
        },


        async getQuestionList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.exam.user_answer/exam_question_list',
                data: {
                    rand_category_id: this.category_id,
                    category_list: this._utils.base64['encode'](JSON.stringify([{
                        category_id: this.category_id,
                        types: 4,
                        num: this.num
                    }])),
                    only_get_question_list: 1
                }
            })
            const list_str = res?.data?.question_list
            if (!list_str) return
            const list = JSON.parse(this._utils.base64['decode'](list_str))
            // 筛选出填词题，避免填空题进来导致后续填词的时候报错
            const fill_words_list = list.filter(item => item.answer_option?.[0]?.fill_couplet)

            if (!fill_words_list.length) {
                return this.$uni.showModal('未设置题目,请联系管理员。', {success: () => this.$uni.navigateBack()})
            }

            this.question_list = fill_words_list
            this.submit_list = fill_words_list.map(item => ({question_id: item.id, user_answer: []}))
            this.nextQuestion()
        },

        nextQuestion() {
            // 因为第一题没有前一题，所以不用退出的动画
            if (this.question_index !== -1) this.triggerChange('exit')

            // 最后一题，提交答案
            const question_index = this.question_index + 1
            if (question_index > this.question_list.length - 1) return this.submit()

            const {title, answer_option} = this.question_list[question_index]

            const couplet = this.extractCouplet(title)

            const candidateWords = this.flatMapShuffleCandidateWords(answer_option).map((item, index) => ({
                word: item,
                is_choose: false,       // 是否已被选择，已被选择的不显示出来，但是位置还得占着
                key: `${question_index}-${index}`  // 用来做v-for的key以及候选字移动动画的标识
            }))

            // 切换到下一题，因为有每题切换的动画，所以要延迟，不然动画切换的时候，前后两题的内容显示的是一致的
            setTimeout(() => {
                this.question_index = question_index
                this.fillCouplet = 'up'
                this.fillSpaceIndex = couplet.up.findIndex(item => item.is_space)
                this.couplet = couplet
                this.candidateWords = candidateWords
                this.chooseWordDisabled = false
                this.triggerChange('enter')
            }, 300)
        },

        // 可以根据需求控制触发不同的动画
        triggerChange(type) {
            this.$nextTick(() => {
                this.transitionClass = type === 'enter' ? 'enter-left' : 'exit-right'
            })
        },


        /**
         * @description: 从拆分上下联
         * @param {String} title 题目
         * @return {Object}  拆分后的上下联
         */
        extractCouplet(title) {
            const [up_title, down_title] = title.split(' ')

           const processTitle = (title) => {
               return title.split('').map((char, i, arr) => {
                   if (char === '_' && arr[i - 1] === '(' && arr[i + 1] === ')') return null
                   if (char === ')' && arr[i - 1] === '_' && arr[i - 2] === '(') return null
                   if (char === '(' && arr[i + 1] === '_' && arr[i + 2] === ')') {
                       return { text: '', is_space: true, choose_key: '' }
                   }
                   return { text: char, is_space: false }
                }).filter(Boolean);
           }


            return {
                up: processTitle(up_title),
                down: processTitle(down_title)
            }
        },

        // 把所有候选字提取出来后再添加干扰字再随机排序
        flatMapShuffleCandidateWords(options) {
            // 提取所有候选字
            const candidateWords = options.flatMap(item => item.is_right.map(v => v.text))

            const maxWords = 5
            const pushWordsCount = maxWords - candidateWords.length
            if (pushWordsCount > 0) {
                const supplementary_words = supplementaryWords(pushWordsCount)
                supplementary_words.forEach(word => candidateWords.push(word))
            }

            // 随机排序
            return this._utils.shuffleArray(candidateWords)
        },


        chooseWord(index) {
            const word_item = this.candidateWords[index]
            if (word_item.is_choose) return

            if (this.chooseWordDisabled) return
            this.chooseWordDisabled = true

            this.move_word_key = word_item.key

            // 获取方框和候选字的位置，将候选字移动到方框位置
            const query = uni.createSelectorQuery().in(this)
            query.select('.couplet .current-space').boundingClientRect()
            query.select(`.choose-word-${word_item.key}`).boundingClientRect()
            query.exec(res => {
                this.setAnimation(res)

                setTimeout(() => {
                    this.candidateWordsAnimationData = {}
                    this.move_word_key = ''   // 移除动画
                    this.standbyToNextSpace(index)
                }, 500)
            })
        },

        // 将候选字移动到方框位置
        setAnimation(res) {
            const [box, word] = res
            const left = box.left - word.left
            const top = box.top - word.top

            this.candidateWordsAnimationData = animation
            animation.translateX(left).translateY(top).step({duration: 500})
            animation.translateX(0).translateY(0).step({duration: 0})
            this.candidateWordsAnimationData = animation.export()
        },

        // 准备切换到下一题
        standbyToNextSpace(index) {
            const candidate_word = this.candidateWords[index]
            this.couplet[this.fillCouplet][this.fillSpaceIndex].choose_key = candidate_word.key//标记选择的候选字的key，取消选择时需要用
            const word = candidate_word.word
            this.couplet[this.fillCouplet][this.fillSpaceIndex].text = word   // 将选中的候选字显示到方框内
            this.submit_list[this.question_index].user_answer.push(word)  // 将选择的答案(候选字)缓存起来
            this.$set(this.candidateWords[index], 'is_choose', true)   // 标记已选择的候选字

            this.toNextSpace()
        },

         toNextSpace() {
            const {fillSpaceIndex, fillCouplet, couplet: {up, down}} = this
            let nextSpace = -1
            if (fillCouplet === 'up') {
                nextSpace = up.findIndex((word, i) => word.is_space && i > fillSpaceIndex)
                if (nextSpace === -1) {
                    nextSpace = down.findIndex(word => word.is_space)
                    if (nextSpace === -1) return this.nextQuestion()
                    this.fillCouplet = 'down'
                }
            } else {
                nextSpace = down.findIndex((word, i) => word.is_space && i > fillSpaceIndex)
                if (nextSpace === -1) return this.nextQuestion()
            }


            this.chooseWordDisabled = false
            this.fillSpaceIndex = nextSpace
        },


        async submit() {
            this.$uni.showLoading('提交中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.fillWords/submit_fill_words_answer',
                data: {
                    active_id: this.active_id,
                    point_id: this.point_id,
                    answer_list: this._utils.base64['encode'](JSON.stringify(this.submit_list))
                }
            })
            uni.hideLoading()

            if (res?.status === 1) this.getOpenerEventChannel().emit('success')

            this.resultPopupShow(res)
        },

        resultPopupShow(res) {
            let status = 'success', info = '任务完成', integral = 0
            if (res?.data?.['reward_integral']) integral = res.data['reward_integral']
            if (res?.status !== 1) {
                status = 'fail'
                info = res?.info || '任务失败'
            }

            this.$refs.resultPopup.open({
                code: status === 'success' ? 1 : 0,
                integral,
                info
            })
        },
    }
}
</script>

<style lang="scss">
.page {
    width: 100%;
    min-height: 100vh;
    box-sizing: border-box;
    background-repeat: no-repeat;
    background-attachment: fixed;
    background-position: center;
    background-size: cover;
}

.couplet {
    min-height: 100vh;
    padding: 0 30rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

.up-couplet, .down-couplet {
    width: 200rpx;
    padding: 50rpx 0;
    background-size: 100% 100%;
    background-repeat: no-repeat;

    .couplet-text-item {
        width: 120rpx;
        height: 120rpx;
        margin: 0 40rpx;
        text-align: center;
        line-height: 120rpx;
        font-size: 80rpx;
        font-weight: 900;
        position: relative;

        .couplet-text-space {
            position: absolute;
            width: 100rpx;
            height: 100rpx;
            top: 10rpx;
            left: 10rpx;
            box-sizing: border-box;
            border-radius: 8rpx;
            border: 8rpx solid;
        }

        .current-space {
            animation: zoom .5s linear alternate infinite;
        }

        @keyframes zoom {
            from {
                transform: scale(1);
            }
            to {
                transform: scale(1.1);
            }
        }
    }
}

.candidate-words {
    width: 200rpx;
    padding: 0 30rpx;

    .candidate-words-item {
        width: 200rpx;
        height: 200rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 80rpx;
        font-weight: 900;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        position: relative;

        .is-choose {
            opacity: 0;
        }
    }
}


.transition-element {
    transform: translateX(-100%);
}

/* 从左边进入 */
.enter-left {
    animation: enter .3s ease-in-out forwards;
}

@keyframes enter {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* 从右边消失 */
.exit-right {
    animation: exit 0.5s ease-in-out forwards;
}

@keyframes exit {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(100%);
    }
}
</style>