<template>
    <view class="page" :style="pageStyle">
        <!--transitionClass: 题目切换时，当前题目是出现还是消失的动画-->
        <view class="transition-element" :class="transitionClass">
            <view class="fill-box flex-all-center">
                <view class="num-title">第{{ question_index + 1 }}题</view>
                <view class="fill-word-box flex-all-center flex-wrap">
                    <!--current-space: 方框放大缩小动画类名-->
                    <!--fillSpaceIndex: 当前需要填字的方框-->
                    <view v-for="(item, index) in words" :key="index"
                          :class="{'fill-word': !item.is_space, 'fill-space': item.is_space, 'current-space': index === fillSpaceIndex}" @click="cancelChoose(index)">
                        {{ item.text }}
                    </view>
                </view>
            </view>
            
            <template v-if="showAD">
                <xwy-ad :ad_type="3"></xwy-ad>
                <xwy-ad :ad_type="66"></xwy-ad>
            </template>

            <view class="candidate-words flex-all-center flex-wrap">
                <view v-for="(item, index) in candidateWords" :key="item.key" class="fill-space"
                      :animation="item.key === move_word_key ? candidateWordsAnimationData : null"
                      :class="'choose-word-' + item.key + (item.is_choose ? ' is-choose' : '')"
                      @click="chooseWord(index)">{{ item.word }}
                </view>
            </view>
        </view>


        <game-result-popup ref="resultPopup" :show-ad="showAD"/>
    </view>
</template>

<script>
import supplementaryWords from './confused-words'

const animation = uni.createAnimation({
    duration: 500,
    timingFunction: 'ease',
    delay: 0
})

export default {
    data() {
        return {
            transitionClass: '',  // 题目切换时，当前题目是出现还是消失的动画
            question_index: -1,
            fillSpaceIndex: -1,  // 当前需要填字的方框
            words: [],
            candidateWords: [],
            move_word_key: '',    // 点前点击的候选字，需要移动到方框内的候选字的key标识
            candidateWordsAnimationData: {},  // 候选字移动到方框内的动画对象
            showAD: false,
            bg_img: ''
        }
    },

    computed: {
        pageStyle() {
            if (!this.bg_img) return ''
            return `background-image: url(${this.bg_img}); background-position: center; background-size: cover; background-repeat: no-repeat;`
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.point_id = params.point_id
        this.category_id = params.category_id
        this.num = Number(params.num)
        if (params.show_ad) this.showAD = true
        if (params.bg_img) this.bg_img = params.bg_img
        if (params.title) this.$uni.setNavigationBarTitle(params.title)

        this.getQuestionList()
    },

    methods: {
        async getQuestionList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.exam.user_answer/exam_question_list',
                data: {
                    rand_category_id: this.category_id,
                    category_list: this._utils.base64['encode'](JSON.stringify([{
                        category_id: this.category_id,
                        types: 4,
                        num: this.num
                    }])),
                    only_get_question_list: 1
                }
            })
            const list_str = res?.data?.question_list
            if (!list_str) return
            const list = JSON.parse(this._utils.base64['decode'](list_str))
            // 筛选出填词题，避免填空题进来导致后续填词的时候报错
            const fill_words_list = list.filter(item => item.answer_option?.[0]?.fill_words)

            if (!fill_words_list.length) {
                return this.$uni.showModal('未设置题目,请联系管理员。', {success: () => this.$uni.navigateBack()})
            }

            this.question_list = fill_words_list

            this.nextQuestion()
        },

        cancelChoose(index) {
            const item = this.words[index]
            if (!item.is_space) return
            if (!item.text) return

            // 找到候选词，并重新把候选词标记成未选择状态
            const candidate_word_index = this.candidateWords.findIndex(v => v.key === this.words[index].choose_key)
            this.candidateWords[candidate_word_index].is_choose = false

            this.words[index].text = ''

            this.fillSpaceIndex = this.words.findIndex(word => word.is_space && word.text === '')
        },

        chooseWord(index) {
            const word_item = this.candidateWords[index]
            if (word_item.is_choose) return
            
            if (this.chooseWordDisabled) return
            this.chooseWordDisabled = true
            
            this.move_word_key = word_item.key

            // 获取方框和候选字的位置，将候选字移动到方框位置
            const query = uni.createSelectorQuery().in(this)
            query.select('.fill-word-box .current-space').boundingClientRect()
            query.select(`.choose-word-${word_item.key}`).boundingClientRect()
            query.exec(res => {
                this.setAnimation(res)

                setTimeout(() => {
                    this.candidateWordsAnimationData = {}
                    this.move_word_key = ''   // 移除动画
                    this.standbyToNextSpace(index)
                }, 500)
            })
        },

        // 将候选字移动到方框位置
        setAnimation(res) {
            const [box, word] = res
            const left = box.left - word.left
            const top = box.top - word.top

            this.candidateWordsAnimationData = animation
            animation.translateX(left).translateY(top).step({duration: 500})
            animation.translateX(0).translateY(0).step({duration: 0})
            this.candidateWordsAnimationData = animation.export()
        },

        // 准备切换到下一题
        standbyToNextSpace(index) {
            const candidate_word = this.candidateWords[index]
            this.words[this.fillSpaceIndex].choose_key = candidate_word.key//标记选择的候选字的key，取消选择时需要用
            const word = candidate_word.word

            this.words[this.fillSpaceIndex].text = word   // 将选中的候选字显示到方框内
            // this.candidateWords.splice(index, 1)     // 移除已选择的候选字
            this.$set(this.candidateWords[index], 'is_choose', true)   // 标记已选择的候选字

            this.toNextSpace()
        },

        toNextSpace() {
            const nextSpace = this.words.findIndex(word => word.is_space && word.text === '')
            if (nextSpace === -1) return this.nextQuestion()

            this.chooseWordDisabled = false
            this.fillSpaceIndex = nextSpace
        },

        // 可以根据需求控制触发不同的动画
        triggerChange(type) {
            this.$nextTick(() => {
                this.transitionClass = type === 'enter' ? 'enter-left' : 'exit-right'
            })
        },

        nextQuestion() {
            if (this.question_index !== -1) {
                // 因为第一题没有前一题，所以不用退出的动画
                this.triggerChange('exit')

                // 缓存上一题所选的答案
                this.submit_list ||= []
                this.submit_list.push({
                    question_id: this.question_list[this.question_index].id,
                    user_answer: this.words.filter(item => item.is_space).map(item => item.text)
                })
            }

            // 最后一题，提交答案
            const question_index = this.question_index + 1
            if (question_index > this.question_list.length - 1) return this.submit()

            const {title, answer_option} = this.question_list[question_index]

            const words = this.extractWords(title)

            const candidateWords = this.flatMapShuffleCandidateWords(answer_option).map((item, index) => ({
                word: item,
                is_choose: false,       // 是否已被选择，已被选择的不显示出来，但是位置还得占着
                key: `${question_index}-${index}`  // 用来做v-for的key以及候选字移动动画的标识
            }))

            // 切换到下一题，因为有每题切换的动画，所以要延迟，不然动画切换的时候，前后两题的内容显示的是一致的
            setTimeout(() => {
                this.question_index = question_index
                this.fillSpaceIndex = words.findIndex(item => item.is_space)
                this.words = words
                this.candidateWords = candidateWords
                this.chooseWordDisabled = false
                this.triggerChange('enter')
            }, 300)
        },


        /**
         * @description: 拆分题目，把题目中的文字和需要填词的标识区分开来
         * @param {String} title 题目
         * @return {Array}  拆分后的数组
         * 一(_)二(_) => [{text: '一', is_space: false},{text: '', is_space: true},  {text: '二', is_space: false}, {text: '', is_space: true}]
         */
        extractWords(title) {
            const words = []
            // "(_)"为需要填字的空格方框
            for (let i = 0; i < title.length; i++) {
                if (title[i] === '_' && title[i - 1] === '(' && title[i + 1] === ')') continue
                if (title[i] === ')' && title[i - 1] === '_' && title[i - 2] === '(') continue
                if (title[i] === '(' && title[i + 1] === '_' && title[i + 2] === ')') {
                    words.push({
                        text: '',
                        is_space: true,
                        choose_key: ''   // 用来标记选的是哪个候选字
                    })
                    continue
                }
                words.push({text: title[i], is_space: false})
            }

            return words
        },

        // 把所有候选字提取出来后再添加干扰字再随机排序
        flatMapShuffleCandidateWords(options) {
            // 提取所有候选字
            const candidateWords = options.flatMap(item => item.is_right.map(v => v.text))
            
            // 添加干扰字，候选字+干扰字=2行3行  一行7个字
            const maxWords = candidateWords.length >= 14 ? 21 : 14
            const pushWordsCount = maxWords - candidateWords.length
            if (pushWordsCount > 0) {
                const supplementary_words = supplementaryWords(pushWordsCount)
                supplementary_words.forEach(word => candidateWords.push(word))
            }
            
            // 随机排序
            return this._utils.shuffleArray(candidateWords)
        },

        async submit() {
            this.$uni.showLoading('提交中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.fillWords/submit_fill_words_answer',
                data: {
                    active_id: this.active_id,
                    point_id: this.point_id,
                    answer_list: this._utils.base64['encode'](JSON.stringify(this.submit_list))
                }
            })
            uni.hideLoading()

            if (res?.status === 1) this.getOpenerEventChannel().emit('success')

            this.resultPopupShow(res)
        },

        resultPopupShow(res) {
            let status = 'success', info = '任务完成', integral = 0
            if (res?.data?.['reward_integral']) integral = res.data['reward_integral']
            if (res?.status !== 1) {
                status = 'fail'
                info = res?.info || '任务失败'
            }

            this.$refs.resultPopup.open({
                code: status === 'success' ? 1 : 0,
                integral,
                info
            })
        },
    }
}
</script>

<style lang="scss">
.page {
    width: 100%;
    min-height: 100vh;
    padding-top: 1px;
    box-sizing: border-box;
    background-image: url(https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/bg/djdt.jpg);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-attachment: fixed;
}

.fill-word, .fill-space {
    width: 80rpx;
    height: 80rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 4rpx;
    font-size: 56rpx;
    font-weight: 600;
}

.fill-space {
    //border: 8rpx solid #171717;
    //border-radius: 8rpx;
    //background-color: #fff;
    background-image: url(https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/red-small-games/fill-couplet/tzg.jpg);
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.fill-box {
    background-color: #FADFC2;
    margin: 180rpx 30rpx 100rpx;
    padding: 60rpx 30rpx;
    border-radius: 10px;
    min-height: 380rpx;
    position: relative;

    .num-title {
        background-image: url(https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/tag/mb.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
        color: #fff;
        text-align: center;
        width: 531rpx;
        height: 126rpx;
        font-size: 48rpx;
        line-height: 108rpx;
        position: absolute;
        top: -80rpx;
        left: 50%;
        margin-left: -265.5rpx;
    }

    .fill-word-box {
        .current-space {
            animation: zoom .5s linear alternate infinite;
        }

        @keyframes zoom {
            from {
                transform: scale(1);
            }
            to {
                transform: scale(1.1);
            }
        }
    }
}

.candidate-words {
    margin: 30rpx;
    
    .fill-space {
        margin: 8rpx;
    }
    
    .is-choose {
        opacity: 0;
    }
}

.transition-element {
    transform: translateX(-100%);
}

/* 从左边进入 */
.enter-left {
    animation: enter .3s ease-in-out forwards;
}

@keyframes enter {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* 从右边消失 */
.exit-right {
    animation: exit 0.5s ease-in-out forwards;
}

@keyframes exit {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(100%);
    }
}

/* 动画完成后回到原始位置 */
.transition-element-enter-active,
.transition-element-leave-active {
    transition: transform 0.5s ease-in-out;
}

.transition-element-enter, .transition-element-leave-to {
    transform: translateX(0);
}
</style>