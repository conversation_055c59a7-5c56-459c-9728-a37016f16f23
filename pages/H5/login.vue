<template>
    <view class="page flex-all-center">
        <view class="login-main">
            <view class="color-title font18 text-center">{{type === 'login' ? '系统登录' : '账号注册' }}
            </view>
            <view>
                <view class="flex-row p10">
                    <view class="input-title pr10">登录账号</view>
                    <uni-easyinput v-model="username" maxlength="20" @confirm="loginOrRegister"/>
                </view>
                <view class="flex-row p10">
                    <view class="input-title pr10">登录密码</view>
                    <uni-easyinput type="password" v-model="password" maxlength="20"
                                   @confirm="loginOrRegister"/>
                </view>
                <view v-if="type === 'register'" class="flex-row p10">
                    <view class="input-title pr10">确认密码</view>
                    <uni-easyinput type="password" v-model="password_again" maxlength="20"
                                   @confirm="loginOrRegister"/>
                </view>
            </view>

            <view v-if="type === 'login'" class="flex-kai p10" style="height: 36px;">
                <view @click="remember_password = !remember_password">

                    <radio class="radio" :checked="remember_password"/>
                    <text class="color-content">记住密码</text>
                </view>
                <view @click="auto_login = !auto_login">

                    <radio class="radio" :checked="auto_login"/>
                    <text class="color-content">自动登录</text>
                </view>
            </view>

            <view class="flex-all-center pt15">
                <button class="login-btn" type="primary" @click="loginOrRegister">
                    {{ type === 'login' ? '登录' : '注册' }}
                </button>
            </view>

            <view v-if="registrationRequired" class="text-center font12 pt15">
                <text class="color-sub">{{ type === 'login' ? '没' : '已' }}有账号,</text>
                <text class="color-light-primary pl5" @click="typeChange">
                    {{ type === 'login' ? '注册账号' : '立即登录' }}
                </text>
            </view>
            
            <view v-if="who" class="flex-all-center">
                <view class="scan-code-login text-center" @click="loadingLoginCode">
                    <text class="iconfont icon-qr-code color-sub"></text>
                    <view class="color-sub font14">扫码登录</view>
                </view>
            </view>
        </view>
        
        <uni-popup ref="qrcode-login" :is-mask-click="false">
            <view class="qrcode-login-popup bg-white text-center">
                <view class="qrcode-login-popup-close p10" @click="qrcodeLoginPopupClose">
                    <uni-icons type="closeempty" size="18" color="#80848f"/>
                </view>
                <view class="font18 pb10 color-title">微信扫一扫登录</view>
                <view class="p10">

                    <image class="qrcode-login-popup-img" :src="login_qrcode" mode="aspectFill"/>
                </view>
                <view class="pt10 color-sub font14">有效期2分钟</view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()

export default {
    data() {
        return {
            type: 'login',
            who: '',
            username: '',
            password: '',
            password_again: '',
            auto_login: false,
            remember_password: false,
            registrationRequired: false,
            login_qrcode: ''
        }
    },

    watch: {
        auto_login(val) {
            if (val) this.remember_password = true
        },
        remember_password(val) {
            if (!val) this.auto_login = false
        }
    },

    onLoad(params) {
        if (params.who) this.who = params.who
        if (params.back_path) this.back_path = decodeURIComponent(params.back_path)
        if (params.reg) this.registrationRequired = true
        this.getStorage(params)
    },
    methods: {
        getStorage(params) {
            if (uni.getStorageSync('register_user')) {
                const register_user = uni.getStorageSync('register_user') || {}
                this.remember_password = !!register_user.remember_password
                this.auto_login = !!register_user.auto_login
                if (register_user.username) this.username = register_user.username
                if (this.remember_password && register_user.password) {
                    this.password = this._utils.base64['decode'](register_user.password)
                }
                if (this.auto_login && this.remember_password && !params.exit && !this.registrationRequired) this.login()
            }
        },

        typeChange() {
            this.type = this.type === 'login' ? 'register' : 'login'
        },

        loginOrRegister() {
            if (this.type === 'login') return this.login()
            this.register()
        },

        register() {
            const {username, password, password_again} = this
            if (!username) return this.$uni.showToast('请输入账号')
            if (!password) return this.$uni.showToast('请输入密码')
            if (!password_again) return this.$uni.showToast('请确认密码')
            if (username.length < 5 || username.length > 20) return this.$uni.showToast('账号长度为5-20位')
            if (password.length < 5 || password.length > 20) return this.$uni.showToast('密码长度为5-20位')
            if (password !== password_again) return this.$uni.showToast('两次密码不一致')

            this.registerAjax()
        },

        async registerAjax() {
            const data = {
                username: this.username,
                password: this.password
            }
            if (sessionStorage.getItem('who')) data.shopid = sessionStorage.getItem('who')
            if (this.who) data.shopid = this.who

            this.$uni.showLoading('注册中...', true)
            const res = await this.xwy_api.request({
                not_token: true,
                url: 'front.user.web.login/reg_user',
                data
            })
            uni.hideLoading()

            if (!res.status) return this.$uni.showModal(res.info || '注册失败')
            this.$uni.showToast('注册成功', 'success')
            this.type = 'login'
        },

        login() {
            if (!this.username) return this.$uni.showToast('请输入账号')
            if (!this.password) return this.$uni.showToast('请输入密码')

            this.loginAjax()
        },

        loginAjax(count = 0) {
            uni.showLoading({
                title: '登录中...',
                mask: true
            })

            const base_url = app.globalData['apihost']

            uni.request({
                url: base_url + '/' + 'front.user.login/user_login',
                method: 'POST',
                data: {
                    username: this.username,
                    password: this.password
                },
                header: {
                    'content-type': 'application/x-www-form-urlencoded'
                },
                success: res => {
                    if (res.statusCode !== 200) {
                        return this.loginError('登录请求失败', res.errMsg || '登录请求失败')
                    }

                    if (!res.data?.status) {
                        return this.loginError('登录失败', res.data?.info || '登录失败')
                    }


                    this.loginSuccess(res.data.data)
                },
                fail: err => {
                    count++
                    if (count < 3) {
                        const url1 = 'https://ja.xwykj.com/crossdomain?url=https://ja.xwykj.com'
                        const url2 = 'https://wx-cloud-api-1755522-1304148175.ap-shanghai.run.tcloudbase.com/crossdomain?url=https://wx-cloud-api-1755522-1304148175.ap-shanghai.run.tcloudbase.com'
                        app.globalData.apihost = base_url === url1 ? url2 : url1
                        sessionStorage.setItem('apihost', app.globalData['apihost'])
                        return this.loginAjax(count)
                    }
                    count = 0
                    uni.showModal({
                        title: '提示',
                        content: err.toString(),
                        showCancel: false
                    })
                },
                complete: () => uni.hideLoading()
            })
        },

        loginSuccess(data) {
            uni.showToast({
                title: '登陆成功',
                icon: 'success'
            })


            const user_details = data.user_details || {}

            if (user_details.shopid) app.globalData.who = user_details.shopid
            app.globalData.userinfo = user_details
            app.globalData.userid = user_details.userid
            app.globalData.access_token = data.access_token

            // #ifdef H5
            sessionStorage.setItem('who', user_details.shopid)
            sessionStorage.setItem('userinfo', JSON.stringify(user_details))
            sessionStorage.setItem('userid', user_details.userid)
            sessionStorage.setItem('access_token', data.access_token)
            // #endif

            // 存储用户名和密码
            const register_user = {username: this.username}
            if (this.remember_password) {
                register_user.remember_password = true
                register_user.password = this._utils.base64['encode'](this.password)
            }
            if (this.auto_login) register_user.auto_login = true
            uni.setStorageSync('register_user', register_user)

            this.toSystem()
        },


        toSystem() {
            const home_path = '/pages/activity/user/activity_list?type=create'
            // const home_path = '/pages/index/index'
            uni.reLaunch({
                url: this.back_path || home_path
            })  
        },
        

        loginError(title, content) {
            uni.showModal({
                title,
                content,
                showCancel: false
            })

        },
        
        loadingLoginCode() {
            this.$uni.showLoading('二维码加载中...')
            this.getUUID()
        },

        loadingLoginCodeError(info) {
            uni.hideLoading()
            this.$uni.showModal(info || '二维码获取失败')
        },

        async getUUID() {
            const res = await this.xwy_api.request({
                not_token: true,
                url: 'front.user.login/qr_code_uuid'
            })
            if (res?.status !== 1) return this.loadingLoginCodeError(res?.info || '二维码获取失败')
            const uuid = res.data?.uuid
            if (!uuid) return this.loadingLoginCodeError('二维码获取失败')
            this.uuid = uuid
            this.get_uuid_time = new Date().getTime()
            this.stopScanCodeLoginCheck = false

            await this.getQrcode(uuid)
        },
        
        async getQrcode(uuid) {
            const res = await this.xwy_api.request({
                not_token: true,
                url: 'front.user.wechat.qrcode/create_qr_code_direct',
                data: {
                    scene: `uuid=${uuid}`,
                    shopid: app.globalData['who'],
                    page: 'pages/other/h5-scan-code-login',
                    env_version: 'release'
                }
            })
            
            if (res?.status !== 1) return this.loadingLoginCodeError(res?.info || '二维码获取失败')
            const qrcode = res.data?.['qr_code_pic']
            if (!qrcode) return this.loadingLoginCodeError('二维码获取失败')
            this.login_qrcode = qrcode
            uni.hideLoading()
            
            this.$refs['qrcode-login'].open()
            
            await this.scanCodeLoginCheck()
        },

        

        async scanCodeLoginCheck() {
            if (this.stopScanCodeLoginCheck) return
            
            const res = await this.xwy_api.request({
                not_token: true,
                url: 'front.user.login/timed_get_token',
                data: {
                    uuid: this.uuid
                }
            })

            const token = res?.data?.token
            if (!token) {
                const now_time = new Date().getTime(),
                    get_uuid_time = this.get_uuid_time
                if (now_time - get_uuid_time > 1000 * 60 * 2) {
                    this.qrcodeLoginPopupClose()
                    return this.$uni.showModal(res?.info || '登录二维码已过期，请重新获取', {
                        showCancel: true,
                        success: res => res.confirm && this.loadingLoginCode()
                    })
                }
                
                return setTimeout(() => this.scanCodeLoginCheck(), 1000)
            }

            this.qrcodeLoginPopupClose()
            this.scanCodeLoginSuccess(token)
        },

        scanCodeLoginSuccess(token) {
            this.$uni.showToast('登录成功', 'success')

            app.globalData.access_token = token

            // #ifdef H5
            sessionStorage.setItem('access_token', token)
            // #endif
            
            this.toSystem()
        },

        qrcodeLoginPopupClose() {
            this.stopScanCodeLoginCheck = true
            this.$refs['qrcode-login'].close()
        },
        
    }
}
</script>

<style scoped lang="scss">
.page {
    min-height: calc(100vh - 50px);
    box-sizing: border-box;
}

.login-main {
    width: 300px;
}

.input-title {
    line-height: 34px;
}

.radio {
    transform: scale(.9);
    position: relative;
    top: -1px;
}

.radio .uni-radio-input {
    margin-right: 0;
}

.scan-code-login {
    margin-top: 40px;
    cursor: pointer;
    
    .iconfont {
        font-size: 50px;
    }
}

.login-btn {
    width: 200px;
    line-height: 40px;
    border-radius: 20px;
}

.qrcode-login-popup {
    padding: 20px 55px;
    border-radius: 10px;
    position: relative;
    
    .qrcode-login-popup-close {
        position: absolute;
        top: 0;
        right: 0;
        cursor: pointer;
    }
    
    .qrcode-login-popup-img {
        width: 400px;
        height: 400px;
    }
}
</style>
