<template>
	<view class="page bg-background">
		<view class="top-tips flex-kai bg-white">
			<navigator class="p10" url="/pages/user/integral_record">积分: {{integral}}</navigator>
			<view class="p10">免费次数: 0</view>
		</view>
		<view class="talk-list">
			<view v-if="!talk_list.length" class="msg-AI">
				<view class="talk-item">
					<view class="talk-content">

						<text :selectable="true" :user-select="true" space="nbsp" :decode="true">
                            你好，我是你的智能助手，你可以问我任何问题。（什么才是好的问题？例如:帮我写一篇小学作文，关于，半夜不能入睡，于是我爬到屋顶，望向宇宙星空。）
                        </text>
					</view>
				</view>
			</view>
			<view
				:class="'msg-' + item.role + ' talk-' + index"
				v-for="(item, index) in talk_list"
				:key="index"
			>
				<view class="talk-item">
					<view class="talk-content">

						<text :selectable="true" :user-select="true" space="nbsp" :decode="true">
                            {{item.content}}
                        </text>
					</view>
					<view v-if="item.role === 'AI' && index !== 0" >
						<text class="color-disabled font14 pt5 pl5" @click="copy(item.content)">
							复制内容
						</text>
					</view>
				</view>
			</view>

			<view v-if="ai_loading" class="ai-loading msg-AI">
				<view class="talk-content">
					<text class="font14 color-sub">AI思考中</text>
					<load-ani/>
				</view>
			</view>
		</view>

		<view class="input-view bg-white">

			<view class="flex-row">
				<view style="width: 100%;">
					<uni-easyinput
						type="textarea"
						v-model="content"
						autoHeight
						:maxlength="500"
						placeholder="请输入你的问题 500字以内"
					/>
				</view>
				<view style="display: flex; flex-direction: column-reverse">
					<view></view>

					<view
						:hover-class="content.length ? 'navigator-hover' : 'none'"
						class="send flex-all-center"
						:class="{
							'bg-primary': content.length && !ai_loading,
							'bg-disabled': !content.length || ai_loading
						}"
						@click="send"
					>
						<text class="iconfont icon-send color-white font24"></text>
					</view>
				</view>
			</view>
		</view>

		<!-- <uni-popup ref="popup" type="bottom" :safe-area="false">
			<view class="other-popup bg-white">
				<view class="close" @click="popupClose('popup')">
					<uni-icons type="closeempty" size="24" color="#c7c7c7"/>
				</view>
				<view class="flex-kai text-center">
					<view style="width: 50%;">
						<uni-icons type="gift" size="60" color="#f9d39f"/>
						<view class="color-sub">获取次数</view>
					</view>
					<navigator
						style="width: 50%;"
						url="./chat-history"
						@click="popupClose('popup')"
					>
						<uni-icons type="chatboxes" size="60" color="#8cde9b"/>
						<view class="color-sub">历史记录</view>
					</navigator>
				</view>
			</view>
		</uni-popup> -->

	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'

	export default {
		data() {
			return {
				// headimg: '',
				integral: 0,
				talk_list: [],
				content: '',
				ai_loading: false
			}
		},
		watch: {
			'talk_list': function(val) {
				const selector = '.talk-' + (val.length - 1)
				this.pageScrollTo(selector)
			},
			'ai_loading': function (val) {
				val && this.pageScrollTo('.ai-loading')
			}
		},

		onShareAppMessage() {
			return {
				title: 'AI智能助手'
			}
		},

		onLoad() {
			uni.showLoading({
				title: '加载中...'
			})
			login.uniLogin(err => {
			    if (err && err.errMsg) {
					uni.hideLoading()
			        uni.showModal({
			            title: err['errTitle'] || '提示',
			            content: err.errMsg,
			            showCancel: false
			        })
			        return false
			    }
				// this.headimg = app.globalData.userinfo.headimg
				this.getChatHistory()
				this.getUserIntegral()
			})
		},

		methods: {
            getUserIntegral() {
                this.xwy_api.getUserDetail(() => {
					this.integral = app.globalData['userinfo']?.integral || 0
				})
			},

			async getChatHistory() {
				const res = await this.xwy_api.request({
					url: 'front.flat.active.chat.user.chat/chat_list',
					data: {
						page: 1,
						perpage: 20,
					}
				})
				const data = res?.data?.list || {is_lastpage: true, data: []}
				this.initTalkList(data.data)
			},

			initTalkList(list) {
				list.reverse()
				const talk_list = []
				list.forEach(v => {
					const content = Array.isArray(v.content) ? v.content[0] : v.content
					if (content.content) {
						talk_list.push({
							role: 'user',
							content: content.content
						})
					}
					if (content.result?.content?.length) {
						const result = content.result.content
						talk_list.push({
							role: 'AI',
							content: Array.isArray(result) ? result.join('\n\n') : result
						})
					}
				})
				this.talk_list = talk_list
				this.scrollToEnd()
				uni.hideLoading()
			},

			scrollToEnd() {
				this.$nextTick(() => {
					const selector = '.talk-' + (this.talk_list.length - 1)
					uni.pageScrollTo({ selector })
				})
			},

			async send() {
				if (!this.content.length) return
				const content = this.content
				const data = {
					role: 'user',
					content
				}
				this.talk_list.push(data)
				this.content = ''
				await this.getAIchat(content)
			},

			async getAIchat(content) {
				this.ai_loading = true
				const res = await this.xwy_api.request({
					url: 'front.flat.active.chat.user.chat/send_query',
					data: {
						content
					}
				})

				let ai_reply = '暂时无法给你回复，请稍后再试。'
				if (!res?.status || res.status !== 1) {
					if (res?.info) ai_reply = res.info
				}
				if (res?.data?.result?.content?.length) {
					const content = res.data.result.content
					ai_reply = Array.isArray(content) ? content.join('\n\n') : content
				}

				const data = {
					role: 'AI',
					content: ai_reply
				}
				this.talk_list.push(data)
				this.ai_loading = false
				this.getUserIntegral()
			},

			pageScrollTo(selector) {
				this.$nextTick(() => {
					uni.pageScrollTo({ selector })
				})
			},

			copy(data) {
				uni.setClipboardData({
					data,
					success: () => {
						uni.showToast({
							title: '已复制',
							icon: 'none',
							duration: 1000
						})
					},
					fail: err => {
						uni.showModal({
							title: '复制失败',
							content: JSON.stringify(err),
							showCancel: false
						})
					}
				})
			},

			/*popupOpen(ref) {
				this.$refs[ref].open()
			},
			popupClose(ref) {
				this.$refs[ref].close()
			}*/
		}
	}
</script>

<style lang="scss">
.page {
	min-height: 100vh;
	padding-bottom: 150px;
	padding-top: 50px;
	box-sizing: border-box;
}
.top-tips {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	box-sizing: border-box;
	z-index: 9;
}
.msg-AI, .msg-user {
	padding: 10px;
	.talk-content {
		padding: 10px;
		display: inline-block;
		word-break: break-all;
		word-wrap: break-word;
	}
}
.msg-AI {
	padding-right: 10vw;
	.talk-content {
		background-color: #fff;
		color: #333;
		border-radius: 0 10px 10px 10px;
	}
}
.msg-user {
	display: flex;
	flex-direction: row-reverse;
	padding-left: 10vw;
	.talk-content {
		background-color: #2d8cf0;
		color: #fff;
		border-radius: 10px 0 10px 10px;
	}
}

.input-view {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100vw;
	box-sizing: border-box;
	padding: 10px 10px 20px 10px;
	border-top: 1px solid #eee;
	.send {
		width: 40px;
		min-width: 40px;
		height: 40px;
		border-radius: 50%;
		margin-left: 10px;
	}
}
.other-popup {
	padding: 30px 10px;
	border-radius: 20px 20px 0 0;
	position: relative;
}
.other-popup .close {
	position: absolute;
	padding: 10px;
	top: 0;
	right: 0;
}
</style>
