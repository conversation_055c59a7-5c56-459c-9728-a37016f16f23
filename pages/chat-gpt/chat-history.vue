<template>
    <view class="page bg-background">
        <!-- <view class="top-tips bg-white flex-kai">
            <view class="p10 color-sub font14">只保存最近{{max_count}}条对话记录</view>
            <view class="p10 color-sub font14" @click="clearAll">清空</view>
        </view> -->
        <view class="talk-list">
            <view
                :class="'msg-' + item.role + ' talk-' + index"
                v-for="(item, index) in talk_list"
                :key="index"
            >
                <view class="talk-item">
                    <view class="talk-content" @longpress="showActionSheet(index)">

                        <text :selectable="true" :user-select="true" space="nbsp" :decode="true">
                            {{ item.content }}
                        </text>
                    </view>
                </view>
                <view v-if="item.role === 'AI'">
                    <text class="color-disabled font14 pt5 pl5" @click="copy(item.content)">复制内容</text>
                </view>
            </view>
        </view>

        
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'

export default {
    data() {
        return {
            // loading: true,
            talk_list: [],
            // load_page: 1,
            // is_last_page: false
        }
    },
    onLoad() {
        uni.showLoading({
            title: '加载中...'
        })
        login.uniLogin(err => {
            uni.hideLoading()
            if (err && err.errMsg) {
                uni.showModal({
                    title: err['errTitle'] || '提示',
                    content: err.errMsg,
                    showCancel: false
                })
                return false
            }
            this.getChatHistory()
        })
    },

    // onReachBottom() {
    // 	!this.loading && !this.is_last_page && this.getChatHistory()
    // },

    methods: {
        async getChatHistory() {
            // if (this.load_page === 1) {
            // 	this.talk_list = []
            // 	this.is_last_page = false
            // }

            // this.loading = true
            const res = await this.xwy_api.request({
                url: 'front.flat.active.chat.user.chat/chat_list',
                data: {
                    page: 1,
                    perpage: 20,
                }
            })
            // this.loading = false
            // this.load_page++
            const data = res?.data?.list || {is_lastpage: true, data: []}
            // this.is_last_page = data.is_lastpage
            this.initTalkList(data.data)
        },

        initTalkList(list) {
            list.reverse()
            const talk_list = []
            list.forEach(v => {
                const content = Array.isArray(v.content) ? v.content[0] : v.content
                if (content.content) {
                    talk_list.push({
                        role: 'user',
                        content: content.content
                    })
                }
                if (content.result?.content?.length) {
                    talk_list.push({
                        role: 'AI',
                        content: content.result.content.join('\n\n')
                    })
                }
            })
            this.talk_list = talk_list
            this.scrollToEnd()
            uni.hideLoading()
        },

        scrollToEnd() {
            this.$nextTick(() => {
                const selector = '.talk-' + (this.talk_list.length - 1)
                uni.pageScrollTo({selector})
            })
        },

        showActionSheet(index) {
            uni.showActionSheet({
                itemList: ['复制', '删除'],
                success: res => {
                    if (res.tapIndex === 0) this.copy(this.talk_list[index].content)
                    if (res.tapIndex === 1) this.deleteTalkItem(index)
                }
            })
        },

        deleteTalkItem(index) {
            uni.showModal({
                title: '提示',
                content: '确定删除该记录？删除后无法恢复。',
                success: res => {
                    if (res.confirm) {
                        this.talk_list.splice(index, 1)
                        history.set(this.talk_list)
                        uni.showToast({
                            title: '已删除',
                            icon: 'none',
                            duration: 1000
                        })
                    }
                }
            })
        },

        copy(data) {
            uni.setClipboardData({
                data,
                success: () => {
                    uni.showToast({
                        title: '已复制',
                        icon: 'none',
                        duration: 1000
                    })
                },
                fail: err => {
                    uni.showModal({
                        title: '复制失败',
                        content: JSON.stringify(err),
                        showCancel: false
                    })
                }
            })
        },

        clearAll() {
            uni.showModal({
                title: '提示',
                content: '确定清空对话记录？清空后无法恢复。',
                success: res => {
                    if (res?.confirm) {
                        history.clearAll()
                        this.talk_list = history.get()
                        uni.showToast({
                            title: '已清空对话记录',
                            icon: 'success'
                        })
                    }
                }
            })
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    padding-bottom: 20px;
    padding-top: 40px;
    box-sizing: border-box;
}

.top-tips {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    z-index: 999;
}

.msg-AI, .msg-user {
    padding: 10px;

    .talk-content {
        padding: 10px;
        display: inline-block;
        word-break: break-all;
        word-wrap: break-word;
    }
}

.msg-AI {
    padding-right: 10vw;

    .talk-content {
        background-color: #fff;
        color: #333;
        border-radius: 0 10px 10px 10px;
    }
}

.msg-user {
    display: flex;
    flex-direction: row-reverse;
    padding-left: 10vw;

    .talk-content {
        background-color: #2d8cf0;
        color: #fff;
        border-radius: 10px 0 10px 10px;
    }
}
</style>
