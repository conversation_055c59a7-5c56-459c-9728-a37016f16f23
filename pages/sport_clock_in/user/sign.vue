<template>
    <view>

        <view v-if="newsDetails" class="p10">
            <u-parse :content="newsDetails"/>
        </view>

        <view v-if="activity_detail.active_id" class="sign-page bg-white">
            <view class="p10" v-if="activity_detail.conf.active.memo_required !== 0">
                <uni-easyinput type="textarea" v-model="memo" :maxlength="800"
                               :placeholder="'请填写打卡备注,800字内,' + (activity_detail.conf.active.memo_required === 2 ? '非' : '') + '必填'"
                ></uni-easyinput>
            </view>

            <diy-submit-form v-if="diyFormList" :forms.sync="diyFormList"/>

            <view v-if="activity_detail.conf.active.pic_list_required !== 0" class="p10">
                <view>
                    <text class="color-content">打卡相册</text>
                    <text v-if="activity_detail.conf.active.pic_list_required === 1" class="color-red">*
                    </text>
                    <text class="color-sub font12 pl5">{{ pic_list.length }}/{{ pic_list_max_count }}</text>
                </view>
                <view style="padding-top: 5px;">
                    <view class="flex-row flex-wrap">
                        <view class="top-rank-banner-item" v-for="(item, index) in pic_list" :key="index">
                            <image :src="item" mode="aspectFill" @click="previewImage(pic_list, item)"/>
                            <view class="del-image-item" @click.stop="pic_list.splice(index, 1)">
                                <uni-icons type="closeempty" color="#e20f04"/>
                            </view>
                        </view>
                        <view v-if="pic_list.length < pic_list_max_count" class="add-image text-center"
                              @click="changeImage('pic_list')">
                            <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                        </view>
                    </view>
                </view>
            </view>

            <view v-if="activity_detail.conf.active.video_list_required !== 0" class="p10">
                <view>
                    <text class="color-content">打卡视频</text>
                    <text v-if="activity_detail.conf.active.video_list_required === 1" class="color-red">*
                    </text>
                </view>
                <view class="pt5">
                    <view v-if="video_list.length" class="video-item-view">
                        <video class="video-item" :src="video_list[0]"></video>
                        <view class="del-image-item" @click.stop="video_list = []">
                            <uni-icons type="closeempty" color="#e20f04"/>
                        </view>
                    </view>
                    <view v-else class="choose-video flex-all-center" @click="chooseVideo">
                        <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                    </view>
                </view>
            </view>

            <view v-if="needCheckInData" class="flex-all-center" style="padding-top: 10px;">
                <view class="clock-in flex-all-center bg-primary color-white"
                      hover-class="navigator-hover" @click="clockIn">
                    {{ record_id ? '修改' : button_text || '打卡' }}
                </view>
            </view>
        </view>

        <game-result-popup ref="resultPopup"/>
    </view>
</template>

<script>
const app = getApp()
import diySubmitForm from '../components/diy-submit-form.vue'

import taskConfig from '@/pages/task-challenge/config'

export default {
    components: {diySubmitForm},
    data() {
        return {
            record_id: '',
            sport_types: null,
            activity_detail: {},
            memo: '',
            pic_list: [],
            pic_list_max_count: 6,
            video_list: [],
            // video_list_max_count: 6,
            diyFormList: null,
            button_text: '',
            news_id: '',
            newsDetails: null
        }
    },

    computed: {
        // 任务闯关活动健康打卡任务
        taskActiveSportType() {
            const types = [31, 33, 34]
            return types.includes(this.sport_types)
        },

        needCheckInData() {
            if (this.news_id) return true
            const {memo_required, pic_list_required, video_list_required} = this.activity_detail?.conf?.active || {}
            return !!memo_required || !!pic_list_required || !!video_list_required
        }
    },

    onLoad(e) {
        if (e.active_id) this.active_id = e.active_id
        if (e.types) this.sport_types = Number(e.types)
        if (e.point_id) this.point_id = Number(e.point_id)
        if (e.button_text) this.button_text = e.button_text
        if (e.news_id) this.news_id = e.news_id
        if (e['sport_name']) this.$uni.setNavigationBarTitle(`${e['sport_name']}打卡`)

        // 任务闯关活动每周运动任务参与人数限制
        if (e.job_stock) {
            this.job_stock = Number(e.job_stock)
            this.job_period = Number(e.job_period || 7)
        }

        this.$uni.showLoading('加载中...')


        this.$login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err.errTitle || '提示'})
            }

            this.getDetail()
        })
    },

    methods: {
        async getDetail() {

            if (app.globalData['edit_sign_data']) {
                const edit_sign_data = app.globalData['edit_sign_data']
                this.$uni.setNavigationBarTitle('修改打卡内容')
                this.active_id = edit_sign_data.active_id
                this.sport_types = edit_sign_data.sport_types
                this.record_id = edit_sign_data.id
                if (edit_sign_data.memo) this.memo = edit_sign_data.memo
                if (edit_sign_data.conf_json) {
                    if (edit_sign_data.conf_json.pic_list?.length) this.pic_list = edit_sign_data.conf_json.pic_list
                    if (edit_sign_data.conf_json.video_list?.length) this.video_list = edit_sign_data.conf_json.video_list
                }

                app.globalData.edit_sign_data = null
            } else {
                if (this.job_stock && !await this.stockCheck()) {
                    uni.hideLoading()
                    return
                }
            }

            let activity_detail = app.globalData['activity_detail']

            if (!activity_detail || activity_detail.active_id !== this.active_id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.active_id
                    }
                })

                activity_detail = res.data.active_details

            }

            this.activity_detail = activity_detail


            uni.hideLoading()

            if (this.news_id) {
                await this.getNews()                
            } else {
                const {memo_required, pic_list_required, video_list_required} = activity_detail.conf.active || {}
                if (!memo_required && !pic_list_required && !video_list_required) await this.clockIn()
            }

            const {weeklySportsSubmitFormDIY} = activity_detail.rank_set || {}
            const {sport_check_in_submit_diy_form} = activity_detail.conf.active || {}
            if (weeklySportsSubmitFormDIY && sport_check_in_submit_diy_form?.length) {
                this.diyFormList = sport_check_in_submit_diy_form
            }
        },

        async getNews() {
            const res = await this.xwy_api.request({
                url: "front.news/news_details",
                data: {
                    news_id: this.news_id
                }
            })

            const content = res?.data?.news_details?.content
            if (content) this.newsDetails = this._utils.newsContentInit(content)
        },


        async stockCheck() {
            const integral_types = taskConfig.getTaskIntegralTypes(this.sport_types)
            const weekDate = this._utils.getWeekDate(this._utils.getDay(0, true, '/'), true, '-')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.userActiveIntegral/get_active_reward_num',
                data: {
                    active_id: this.active_id,
                    reward_integral_types: integral_types,
                    point_id: this.point_id,
                    begin_time: `${weekDate[0]} 00:00:00`,
                    end_time: `${weekDate[weekDate.length - 1]} 23:59:59`
                }
            })

            const count = res?.data?.count || 0
            const have_stock = count < this.job_stock
            if (!have_stock) this.$uni.showModal(`当前参与人数已达每周上限${this.job_stock}人，请下周再来吧。`, {
                success: () => this.$uni.navigateBack()
            })
            return have_stock
        },

        changeImage(key) {
            uni.navigateTo({
                url: '/pages/other/image_upload_or_select?active_id=' + this.active_id,
                events: {
                    newImg: src => {
                        switch (key) {
                            case 'pic_list':
                                this.pic_list.push(src)
                                break
                        }
                    }
                }
            })
        },


        async chooseVideo() {
            const video_data = await new Promise(resolve => {
                uni.chooseVideo({
                    success: res => {
                        return resolve(res)
                    }
                })
            })

            this.$uni.showLoading('视频上传中...')
            const video_src = await this.xwy_api.uploadVideo(video_data, this.active_id)
            uni.hideLoading()

            if (!video_src) return this.$uni.showModal('视频上传失败，请重试')

            this.video_list.push(video_src)
        },

        previewImage(urls, current = urls[0]) {
            this.$uni.previewImage({
                urls,
                current
            })
        },

        getSignSubmitDiyForms() {
            if (!this.activity_detail?.rank_set?.['weeklySportsSubmitFormDIY']) return false
            if (!this.diyFormList?.length) return false

            const list = JSON.parse(JSON.stringify(this.diyFormList))
            for (let i = 0, len = list.length; i < len; i++) {
                const item = list[i]
                if (item.rules && !item.value) {
                    this.$uni.showToast(`请${item.types === 1 ? '输入' : '选择'} ${item.title}`)
                    return null
                }

                if (item.hasOwnProperty('options')) delete item.options
            }

            return list
        },

        dataCheck() {
            const {memo_required, pic_list_required, video_list_required} = this.activity_detail.conf.active
            if (memo_required === 1 && !this.memo) {
                this.$uni.showToast('请填写打卡备注', 'error')
                return false
            }
            if (pic_list_required === 1 && !this.pic_list.length) {
                this.$uni.showToast('请上传打卡相册', 'error')
                return false
            }
            if (video_list_required === 1 && !this.video_list.length) {
                this.$uni.showToast('请上传打卡视频', 'error')
                return false
            }

            const data = {
                active_id: this.active_id,
                sport_types: this.sport_types
            }

            if (this.memo) data.memo = this.memo
            if (this.record_id) data.id = this.record_id

            const conf_json = {}
            if (this.pic_list.length) conf_json.pic_list = this.pic_list
            if (this.video_list.length) conf_json.video_list = this.video_list

            const sign_submit_form = this.getSignSubmitDiyForms()
            if (sign_submit_form === null) return
            if (sign_submit_form?.length) conf_json.sign_submit_form = sign_submit_form

            data.conf_json = this._utils.base64['encode'](JSON.stringify(conf_json))


            return data
        },

        async clockIn() {
            const data = this.dataCheck()
            if (!data) return false

            this.$uni.showLoading(this.record_id ? '修改中...' : '打卡中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/submit_user_point_sign',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '失败')

            if (this.taskActiveSportType) return await this.rewardsIntegral()

            const info = res.info || '成功'
            this.$uni.showToast(info, info.length <= 7 ? 'success' : 'none')

            this.getOpenerEventChannel?.()?.emit?.('reloadList')
            this.$uni.navigateBack(1, {delay: 1000})
        },

        async rewardsIntegral() {
            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify({
                        types: this.sport_types,
                        point_id: this.point_id,
                        result: 'success'
                    }))
                }
            })
            uni.hideLoading()


            let resultCode = 1
            let info = '任务完成'
            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info = res.info
            }


            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })


            this.getOpenerEventChannel().emit('success')
        }
    }
}
</script>

<style>
.sign-page {
    width: 100vw;
    height: 100vh;
    box-sizing: border-box;
}


.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
    line-height: calc((100vw - 50px) / 3);
    margin: 5px;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}


.top-rank-banner-item {
    padding: 5px;
    position: relative;
}

.top-rank-banner-item image {
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
}

.top-rank-banner-item .del-image-item {
    right: 8px;
}

.video-item-view {
    position: relative;
}

.video-item {
    width: 100%;
}

.choose-video {
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
    margin: 5px;
    border: 1px solid #eee;
}

.clock-in {
    width: 200px;
    height: 40px;
    border-radius: 20px;
}

</style>
