<template>
	<view class="page bg-background">
		<view class="page-top">

			<view v-if="is_admin" class="type-list flex-row bg-white">
				<view
					class="type-item color-content"
					v-for="(item, index) in checked_list"
					:key="index"
					:class="{'active-type': item.checked === checked}"
					:style="{width: 'calc(100% / ' + checked_list.length + ')'}"
					@click="checkedChange(item.checked)"
				>
					{{item.title}}
				</view>
			</view>


			<template v-if="sport_type_list.length">
                <view v-if="types_show_logo" class="sport-type-list flex-row flex-wrap bg-background">
                    <view class="sport-type-item text-center" v-for="item in sport_type_list"
                          :key="item.types" @click="sportTypeChange(item.types)">
                        <view class="logo flex-all-center">
                            <image v-if="item.logo" :src="item.logo" mode="aspectFill"/>
                            <view v-else
                                  class="logo-text flex-all-center bg-light-primary color-white font18">
                                <template v-if="item.title">{{ item.title[0] }}</template>
                                <template v-else>{{ item.name[0] }}</template>
                            </view>
                        </view>
                        <view
                            class="font14 ellipsis"
                            :class="{'color-primary': item.types === sport_types, 'color-sub': item.types !== sport_types}"
                            style="padding-top: 2px;"
                        >{{ item.title || item.name }}
                        </view>
                    </view>
                </view>

                <scroll-view v-else scroll-x="true" class="type-list-scroll-view bg-white">
                    <view class="flex-row">
                        <view class="scroll-type-item" :class="{'active': item.types === sport_types}"
                              v-for="item in sport_type_list" :key="item.types"
                              @click="sportTypeChange(item.types)">
                            {{ item.title }}
                        </view>
                    </view>
                </scroll-view>
            </template>

		</view>


		<view v-if="is_admin" style="height: 40px;"></view>
		<view v-if="sport_type_list_height" :style="{height: sport_type_list_height + 'px'}"></view>


		<view v-if="list_total" class="text-center p10 color-sub font14">共{{list_total}}条打卡记录</view>

		<xwy-ad :activity_id="id" :ad_type="3"></xwy-ad>

		<view class="list">
			<view v-for="(item, index) in list" :key="index">
                <view v-if="index === 0">
                    <xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>
                </view>
                <view class="item bg-white">
                    <view class="flex-row">
                        <view v-if="is_admin" class="flex-all-center" @click="checkChange(index)">
                            <radio :checked="item.check" style="transform:scale(0.8)"/>
                        </view>
                        <view class="pr10">
                            <image
                                class="headimg"
                                :src="item.user_attend_details && item.user_attend_details.headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"
                                mode="aspectFill"
                            />
                        </view>
                        <view style="width: 100%;">
                            <view
                                v-if="item.user_attend_details && item.user_attend_details.must_submit && item.user_attend_details.must_submit.length"
                                class="color-title"
                            >{{item.user_attend_details.must_submit[0].value}}</view>
                            <view v-if="item.memo" class="color-content pt5">{{item.memo}}</view>

                            <view v-if="item.conf_json && item.conf_json.sign_submit_form && item.conf_json.sign_submit_form.length" style="padding: 5px 0;">
                                <view v-for="(form, form_i) in item.conf_json.sign_submit_form" :key="form_i">
                                    <view v-if="form.value" class="color-content font14">
                                        {{ form.title }}: {{ form.value }}
                                    </view>
                                </view>
                            </view>

                            <view
                                v-if="item.img_video_count"
                                class="img-list flex-row flex-wrap pt5"
                                :class="{
                                    'img-list-1': item.img_video_count === 1,
                                    'img-list-2': item.img_video_count === 2,
                                    'img-list-more': item.img_video_count >= 3
                                }"
                            >
                                <image
                                    class="img-item"
                                    lazy-load
                                    v-for="(img_item, img_index) in item.conf_json.pic_list"
                                    :src="img_item"
                                    :mode="item.img_video_count === 1 ? 'heightFix' : 'aspectFill'"
                                    @click.stop="previewImage(img_item, item.conf_json.pic_list)"
                                />

                                <video
                                    class="img-item"
                                    :id="'video_' + item.id + '_' + video_index"
                                    v-for="(video_item, video_index) in item.conf_json.video_list"
                                    :src="video_item"
                                    @play="videoPlay('video_' + item.id + '_' + video_index)"
                                ></video>
                            </view>

                            <view class="flex-row">
                                <view v-if="item.sport_types" class="pt5 pr5">
                                    <uni-tag
                                        :text="item.sport_types_text || item.sport_types"
                                        type="primary"
                                        size="mini"
                                        :inverted="true"
                                    />
                                </view>

                                <view v-if="!item.checked" class="pt5">
                                    <uni-tag text="待审核" type="default" size="mini" :inverted="true"/>
                                </view>
                            </view>


                            <view v-if="!is_admin" class="flex-kai">
                                <view class="color-sub font14 pt5">{{item.create_time}}</view>
                                <view @click="like(index)">
                                    <text
                                        class="iconfont icon-love color-red"
                                        style="position: relative; top: 1px; left: 2px;"
                                    ></text>
                                    <text class="pl5 color-sub font14">{{item.agree || 0}}</text>
                                </view>
                            </view>
                        </view>
                    </view>


                    <view v-if="is_admin || (is_myself && !records_not_edit)" class="item-bar flex-kai">
                        <view></view>
                        <view class="flex-row">
                            <template v-if="is_admin">
                                <view
                                    class="font14 color-content pl10 pr10"
                                    hover-class="navigator-hover"
                                    @click="changeChecked(item)"
                                >
                                    改为{{item.checked ? '待' : '已'}}审核
                                </view>
                                <view
                                    class="font14 color-content pl10"
                                    hover-class="navigator-hover"
                                    @click="delItem(item.id)"
                                >删除</view>
                            </template>
                            <template v-if="is_myself && !records_not_edit">
                                <view
                                    class="font14 color-content pl10"
                                    hover-class="navigator-hover"
                                    @click="toEditRecord(item)"
                                >修改</view>
                            </template>
                        </view>
                    </view>
                </view>

                <view v-if="index !== 0 && ((index + 1) % 10 === 0)">
                    <xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>
                </view>
            </view>
		</view>




		<uni-load-more v-if="loading" status="loading"></uni-load-more>
		<uni-load-more
			v-if="is_last_page && list.length > 5"
			status="noMore"
		    :contentText="{contentnomore: '我是有底线的'}"
		></uni-load-more>
		<uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>

		<view v-if="!loading && !list.length" class="text-center" style="padding-top: 15vh;">
			<text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
			<view class="color-sub font14">暂无打卡记录</view>
		</view>


		<template v-if="is_admin && list.length">
			<view style="height: 70px;"></view>
			<view class="bottom-bar flex-kai bg-white">
				<view class="flex-all-center" @click="checkAllChange">
					<radio :checked="check_all" style="transform:scale(0.8)"/>
					<text class="font14 color-sub" style="position: relative; left: -4px; top: 1px;">全选</text>
				</view>
				<view class="flex-row">
					<view
						class="all-change color-content font14"
						hover-class="navigator-hover"
						@click="changeCheckeds"
					>修改状态</view>
					<view style="width: 10px;"></view>
					<view
						class="all-change color-content font14"
						hover-class="navigator-hover"
						@click="dels"
					>删除</view>
				</view>
			</view>
		</template>
	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'


	export default {
		data() {
			return {
				id: '',
				is_admin: 0,
				is_myself: 0,
				loading: true,
				checked: 1,
				list: [],
				list_total: 0,
				load_page: 1,
				is_last_page: false,
				checked_list: [
					{checked: 0, title: '所有记录'},
					{checked: 2, title: '待审核'},
					{checked: 1, title: '已审核'},
				],
				check_all: false,
				sport_type_list: [],
				sport_type_list_height: 0,
                types_show_logo: true,
				sport_types: 0,
				records_not_edit: 0
			}
		},

		onLoad(e) {
			uni.showLoading({
				title: '加载中...',
			})

			this.id = e.id

			if (e.types) this.sport_types = Number(e.types)

			if (e.is_admin) {
				uni.setNavigationBarTitle({
					title: '打卡审核'
				})
				this.is_admin = 1
				this.checked = 2
			} else if (e.is_myself) {
				uni.setNavigationBarTitle({
					title: '打卡记录'
				})
				this.is_myself = 1
				this.checked = 0
			} else if (e.userid) {
				this.userid = e.userid
				this.checked = 1
				uni.setNavigationBarTitle({
					title: (e.nickname || '') + ' 打卡记录'
				})
			} else {
				uni.setNavigationBarTitle({
					title: '打卡广场'
				})
				this.checked = 1
			}

			login.uniLogin(err => {
			    if (err && err.errMsg) {
			        uni.hideLoading()
			        uni.showModal({
			            title: err.errTitle || '提示',
			            content: err.errMsg,
			            showCancel: false
			        })
			        return false
			    }
				this.getActivityDetail()
			})
		},

		onReachBottom() {
			!this.loading && !this.is_last_page && this.getList()
		},

		methods: {
			async getActivityDetail() {
				let activity_detail = app.globalData['activity_detail']

				if (!activity_detail || activity_detail.active_id !== this.id) {
					const res = await xwy_api.request({
						url: 'front.flat.sport_step.active_list/active_details',
						data: {
							active_id: this.id
						}
					})



					if (!res?.data?.active_details) return this.getList()

					activity_detail = res.data.active_details
				}


                this.not_check = activity_detail.conf.active.records_not_edit || 0
                this.records_not_edit = activity_detail.conf.active.records_not_edit || 0

                this.getSportTypeList(activity_detail)

				await this.getList()
			},

            getSportTypeList(details) {
                const all = {title: '全部', types: 0}

                // 任务闯关活动获取健康打卡行为
                if (details.types === 25) {
                    const taskList = details.conf?.active?.job_set?.job_list || []
                    const list = taskList.filter(item => [31, 33, 34].includes(item.types)).map(item => ({
                        types: item.types,
                        title: item.title
                    }))
                    if (list.length) {
                        this.sport_type_list = [all, ...list]
                        this.types_show_logo = false
                        this.sport_type_list_height = 40
                    }

                    return
                }

                if (details?.conf?.active?.ai_sport_list?.length) {
					this.sport_type_list = [all, ...details.conf.active.ai_sport_list]
					const line = Math.ceil(this.sport_type_list.length / 5)
					this.sport_type_list_height = 83 * line + 10
				}
            },


			checkedChange(checked) {
				if (checked === this.checked) return false
				this.checked = checked
				this.load_page = 1
				this.getList()
			},


			sportTypeChange(types) {
				if (types === this.sport_types) return false
				this.sport_types = types
				this.load_page = 1
				this.getList()
			},

			async getList() {
				if (this.load_page === 1) {
					this.list = []
					this.is_last_page = false
				}
				const data = {
					access_token: app.globalData.access_token,
					active_id: this.id,
					is_admin: this.is_admin,
					checked: this.checked,
					sport_types: this.sport_types,
					is_myself: this.is_myself,
					page: this.load_page,
					perpage: 10
				}

				if (this.userid) data.userid = this.userid

				this.loading = true

				const res = await xwy_api.request({
					url: 'front.flat.sport_step.sign_location.userSign/public_square_sign_list',
					data
				})

				this.loading = false
				uni.hideLoading()

				this.load_page++

				if (res?.data?.user_sign_list) {
					const data_ = res.data.user_sign_list
					this.list_total = data_.total
					this.is_last_page = data_.is_lastpage
					const list = data_.data || []

					if (list.length) {
						if (this.is_admin) this.check_all = false
						list.forEach(v => {
							if (this.is_admin) v.check = false
							const sport_types_data = this.sport_type_list.find(item => item.types === v.sport_types)
							if (sport_types_data) v.sport_types_text = sport_types_data.title || sport_types_data.name
							v.img_video_count = 0
							if (v.conf_json) {
								if (v.conf_json.pic_list && v.conf_json.pic_list.length) {
									v.img_video_count += v.conf_json.pic_list.length
								}
								if (v.conf_json.video_list && v.conf_json.video_list.length) {
									v.img_video_count += v.conf_json.video_list.length
								}
							}
						})
					}
					this.list = [...this.list, ...list]
				} else {
					this.is_last_page = true
				}

			},

			checkChange(index) {
				const item = this.list[index]
				item.check = !item.check
			},

			checkAllChange() {
				const check_all = !this.check_all
				this.check_all = check_all
				this.list.forEach(v => {
					v.check = check_all
				})
			},

			changeChecked(item) {
				uni.showModal({
					title: '提示',
					content: `确定将该记录的状态改为${item.checked ? '待' : '已'}审核?`,
					success: res => {
						if (res.confirm) this.changeCheckedAjax([item.id], item.checked ? 'un_checked' : 'checked')
					}
				})
			},

			getCheckList() {
				const ids = []
				this.list.forEach(v => {
					if (v.check) ids.push(v.id)
				})
				if (!ids.length) {
					uni.showToast({
						title: '未勾选记录',
						icon: 'error'
					})
					return false
				}

				return ids
			},

			changeCheckeds() {
				const ids = this.getCheckList()
				if (!ids) return false

				const checked_list = [
					{checked: 'checked', title: '已审核'},
					{checked: 'un_checked', title: '待审核'}
				]
				const itemList = []
				checked_list.forEach(v => {
					itemList.push(v.title)
				})

				uni.showActionSheet({
					title: '将记录状态修改为',
					itemList,
					success: res => {
						const index = res.tapIndex
						uni.showModal({
							title: '提示',
							content: `确定将选中的${ids.length}条记录状态修改为${checked_list[index].title}?`,
							success: res => {
								if (res.confirm) this.changeCheckedAjax([ids], checked_list[index].checked)
							}
						})
					}
				})
			},


			delItem(id) {
				uni.showModal({
					title: '提示',
					content: '确定删除该记录?',
					success: res => {
						if (res.confirm) this.changeCheckedAjax([id], 'del')
					}
				})
			},

			dels() {
				const ids = this.getCheckList()
				if (!ids) return false

				uni.showModal({
					title: '提示',
					content: `确定删除选中的${ids.length}条记录?`,
					success: res => {
						if (res.confirm) this.changeCheckedAjax([ids], 'del')
					}
				})
			},

			async changeCheckedAjax(id_list, act_types) {
				const act_text = act_types === 'del' ? '删除' : '修改'
				uni.showLoading({
					title: act_text + '中...',
					mask: true
				})

				const res = await xwy_api.request({
					url: 'front.flat.sport_step.sign_location.adminManage/checked_user_sign_record',
					data: {
						access_token: app.globalData.access_token,
						active_id: this.id,
						act_types,
						ids: id_list.join(',')
					}
				})

				uni.hideLoading()

				if (!res || !res.status) {
					xwy_api.alert(res && res.info || `${act_text}失败`)
					return false
				}

				const info = res.info || `${act_text}成功`
				uni.showToast({
					title: info,
					icon: info.length <= 7 ? 'error' : 'none'
				})

				this.load_page = 1
				this.getList()
			},

			async like(index) {
				const item = this.list[index]

				if (!item.checked) {
					xwy_api.alert('记录未审核通过，无法点赞。')
					return false
				}

				uni.showLoading({
					title: '点赞中...',
					mask: true
				})

				const res = await xwy_api.request({
					url: 'front.flat.sport_step.sign_location.userSign/friend_agree_vote_record',
					data: {
						access_token: app.globalData.access_token,
						id: item.id,
						active_id: this.id,
						act_types: 1
					}
				})

				uni.hideLoading()

				if (!res || !res.status) {
					xwy_api.alert(res && res.info || '点赞失败')
					return false
				}

				const title = res.info || '点赞成功'
				uni.showToast({
					title,
					icon: title.length <= 7 ? 'success' : 'none'
				})

				item.agree++
			},

			videoPlay(id) {
				this.videoContext = this.videoContext || null
				if (this.previous_video_id) {
					if (id === this.previous_video_id) return false
					this.videoContext = uni.createVideoContext(this.previous_video_id)
					this.videoContext.pause()
				}
				this.previous_video_id = id
			},

			previewImage(src, list) {
				list = list || [src]
				uni.previewImage({
					urls: list,
					current: src
				})
			},

			async toEditRecord(item) {
				app.globalData.edit_sign_data = item

				if (this.not_check) {
					uni.navigateTo({
						url: '/pages/sport_clock_in/user/sign',
						events: {
							reloadList: () => {
								this.load_page = 1
								this.getList()
							}
						}
					})
					return false
				}

				const confirm = await new Promise(resolve => {
					uni.showModal({
						title: '提示',
						content: '记录修改后将重置为待审核状态，需要管理员重新审核才能显示在打卡广场。是否继续修改?',
						confirmText: '继续修改',
						success: res => {
							return resolve(res.confirm)
						}
					})
				})

				confirm && uni.navigateTo({
					url: '/pages/sport_clock_in/user/sign',
					events: {
						reloadList: () => {
							this.load_page = 1
							this.getList()
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
.page {
	min-height: 100vh;
	box-sizing: border-box;
}

.page-top {
	position: fixed;
	z-index: 999;
	width: 100vw;
	top: 0;
	left: 0;
}

.type-item {
    height: 40px;
    line-height: 40px;
    text-align: center;
    box-sizing: border-box;

    &.active-type {
        color: #007AFF;
        border-bottom: 2px solid #007AFF;
    }
}

.sport-type-list {
    padding: 5px 0;

    .sport-type-item {
        width: calc(100vw / 5);
        padding: 5px 0;

        .logo {
            image, .logo-text {
                width: 50px;
                height: 50px;
                border-radius: 50%;
            }
        }

    }
}

.type-list-scroll-view {
    .scroll-type-item {
        white-space: nowrap;
        line-height: 40px;
        padding: 0 10px;
        color: #666;

        &.active {
            color: #5cadff;
        }
    }
}

.list {
	padding: 10px;
}
.item {
	padding: 10px;
	border-radius: 10px;
	margin: 10px 0;
}
.headimg {
	width: 40px;
	min-width: 40px;
	height: 40px;
	display: block;
	border-radius: 50%;
}
.img-list {
	position: relative;
	left: -5px;
	padding-top: 5px;
}
.img-item {
	display: block;
	margin-left: 5px;
	margin-bottom: 5px;
	border-radius: 5px;
}
.img-list-1 .img-item {
	height: 120px;
	width: auto;
	max-width: calc(100vw - 130px);
}
.img-list-1 video {
	width: 180px !important;
}
.img-list-2 .img-item {
	width: 100px;
	height: 120px;
}
.img-list-more .img-item {
	width: calc((100vw - 150px) / 3);
	height: calc((100vw - 150px) / 3);
}
.item-bar {
	margin-top: 10px;
	padding-top: 10px;
	border-top: 1px solid #eee;
}

.bottom-bar {
	position: fixed;
	left: 0;
	width: 100%;
	z-index: 9;
	border-top: 1px solid #eee;
	bottom: 0;
    padding: 5px 10px 15px;
    height: 61px;
	line-height: 40px;
	box-sizing: border-box;
}

.all-change {
	line-height: 38px;
	border-radius: 20px;
	padding: 0 15px;
	border: 1px solid #eee;
	text-align: center;
}
</style>
