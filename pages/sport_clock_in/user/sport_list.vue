<template>
    <view class="page bg-background">
        <view class="list pt5">
            <view class="item bg-white" v-for="(item, index) in list" :key="index">
                <view hover-class="navigator-hover" @click="toSign(item.types, item.title || item.name)">
                    <image class="logo" v-if="item.logo" :src="item.logo" mode="widthFix"/>
                    <view class="color-title ptm5">{{ item.title || item.name }}</view>
                </view>
                <view class="flex-kai font14" style="border-top: 1px solid #eee; padding-top: 10px;">
                    <view class="color-sub flex-row">
                        <view class="pl10" hover-class="navigator-hover" @click="toRecordList(item.types)">
                            <text class="iconfont icon-footnotes font14 pr5"></text>
                            <text>打卡记录</text>
                        </view>
                    </view>
                    <view hover-class="navigator-hover" class="color-primary"
                          @click="toSign(item.types, item.title || item.name)">
                        <text class="iconfont icon-sign-in-calendar font14 pr5"></text>
                        <text>打卡</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()

export default {
    data() {
        return {
            list: []
        }
    },

    onLoad(e) {
        uni.showLoading()
        this.active_id = e.id

        this.$login.uniLogin(err => {
            if (err && err.errMsg) return this.$uni.showModal(err.errMsg, {title: err.errTitle || '提示'})

            this.getActivityDetail()
        })
    },


    methods: {
        async getActivityDetail() {
            let activity_detail = app.globalData['activity_detail']

            if (!activity_detail || activity_detail.active_id !== this.active_id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {active_id: this.active_id}
                })

                if (!res?.data?.active_details) return

                activity_detail = res.data.active_details
            }

            this.activity_detail = activity_detail

            if (activity_detail?.conf?.active?.ai_sport_list?.length) this.list = activity_detail.conf.active.ai_sport_list

            uni.hideLoading()
        },

        toRecordList(types) {
            this.$uni.navigateTo(`/pages/sport_clock_in/user/public_sign_list?is_myself=1&id=${this.active_id}&types=${types}`)
        },

        async toSign(types, name) {
            if (!await this.clockInCheck(types, name)) return
            this.$uni.navigateTo(`./sign?sport_name=${name}&active_id=${this.active_id}&types=${types}`)
        },


        /**
         * 检查用户是否可以打卡
         *
         * @param {string} types - 活动类型
         * @param {string} name - 运动名称
         * @returns {Promise<boolean>} - 如果可以打卡返回 true，否则返回 false
         */
        async clockInCheck(types, name) {
            /**
             * 获取打卡限制类型 Number
             * 0: 不限制
             * 1: 每个运动类型只能打卡一次
             * 2: 每个运动类型每日只能打卡?次
             * */
            const sign_times_type = this.activity_detail?.conf?.active?.sign_times_type

            // 不限制，则默认可以打卡
            if (!sign_times_type) return true

            // 类型1: 检查用户运动类型是否已经打卡
            if (sign_times_type === 1) {
                // 获取用户的打卡记录数量
                const count = await this.getClockInRecordCount(types)
                // 如果没有打卡记录，则允许打卡
                if (!count) return true
                // 如果已打卡，显示提示并返回 false
                this.$uni.showToast(`${name} 已打卡`)
                return false
            }

            // 类型2: 检查用户每日打卡次数
            if (sign_times_type === 2) {
                // 获取活动设置的每日允许打卡次数，默认为1
                const daily_submit_num = this.activity_detail?.conf?.active?.['daily_submit_num'] || 1
                // 获取用户今天的打卡记录数量
                const count = await this.getClockInRecordCount(types, true)
                // 如果打卡次数小于每日限制，则允许打卡
                if (count < daily_submit_num) return true

                // 如果活动设置每日允许打卡1次，今日已经打卡，显示提示信息
                if (daily_submit_num === 1) {
                    this.$uni.showToast(`${name} 今日已打卡`)
                } else {
                    // 如果今日打卡次数已满，弹出提示框
                    await this.$uni.showModal(`${name} 每日允许打卡${daily_submit_num},你已打卡${count}次,请明天再来打卡`)
                }

                // 如果已超过每日打卡次数限制，返回 false
                return false
            }

            // 如果没有限制，允许打卡
            return true
        },

        /**
         * 获取用户的打卡记录数量
         *
         * @param {string} types - 运动类型，用于筛选打卡记录
         * @param {boolean} [getToday=false] - 是否仅获取今天的打卡记录，默认为 false
         * @returns {Promise<number>} - 返回打卡记录的总数
         */
        async getClockInRecordCount(types, getToday = false) {
            const data = {
                active_id: this.active_id,
                is_myself: 1,
                sport_types: types,
                page: 1,
                perpage: 1
            }

            // 如果需要获取今天的打卡记录
            if (getToday) {
                const today = this._utils.getDay(0, true, '-')
                data.begin_time = `${today} 00:00:00`
                data.end_time = `${today} 23:59:59`
            }

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/public_square_sign_list',
                data
            })
            uni.hideLoading()

            // 返回打卡记录的总数，如果没有数据则返回 0
            return res?.data?.['user_sign_list']?.total || 0
        }
    }
}
</script>

<style scoped lang="scss">
.page {
    min-height: 100vh;
}

.list {
    padding-bottom: 1px;

    .item {
        margin: 10px;
        padding: 10px;
        border-radius: 10px;

        .logo {
            width: 100%;
            height: auto;
        }
    }
}
</style>
