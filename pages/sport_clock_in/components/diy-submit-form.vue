<template>
    <view class="forms">
        <uni-forms :label-width="100" label-align="right">
            <uni-forms-item v-for="(item, index) in must_submit" :key="index" :label="`${item.title}:`"
                            :required="item.rules === 1">
                <uni-easyinput v-if="item.types === 1" v-model="item.value"
                               :placeholder="`请输入 ${item.title}`"/>

                <picker v-else-if="item.types === 2" :range="item.options" range-key="text"
                        @change="mustValueChange($event, index)">
                    <view class="form-picker flex-kai">
                        <view v-if="!item.value" class="color-sub">请选择 {{ item.title }}</view>
                        <view v-if="item.value">{{ item.value }}</view>
                        <text class="iconfont icon-more color-disabled font18"/>
                    </view>
                </picker>

                <view v-else-if="item.types === 3" class="datetime-picker">
                    <uni-datetime-picker type="date" :value="item.value" @change="dateChange($event, index)"
                                         :placeholder="`请选择 ${item.title}`"/>
                </view>
            </uni-forms-item>
        </uni-forms>
    </view>
</template>

<script>
export default {
    name: "diy-submit-form",
    props: ['forms'],
    data() {
        return {
            must_submit: []
        }
    },

    watch: {
        forms: {
            handler(val) {
                this.must_submit = val
            },
            deep: true,
            immediate: true
        },

        must_submit: {
            handler(val) {
                this.$emit('update:forms', val)
            },
            deep: true
        }
    },


    methods: {
        mustValueChange(e, index) {
            const item = this.must_submit[index]
            item.value = item.options[e.detail.value].text
            this.$set(this.must_submit, index, item)
        },

        dateChange(e, index) {
            const item = this.must_submit[index]
            item.value = e
            this.$set(this.must_submit, index, item)
        }
    }
}
</script>

<style lang="scss" scoped>
.forms {
    padding: 10px 10px 0;
}

.form-picker {
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    height: 34px;
    line-height: 34px;
    padding-left: 10px;
}
</style>