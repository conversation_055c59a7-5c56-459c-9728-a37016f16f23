<template>
    <view class="page">
        <view v-if="success" class="save-success bg-white">
            <view class="icon text-center">
                <uni-icons type="checkbox-filled" size="100" color="#19be6b"/>
                <view class="color-content font16">活动{{ form_data.active_id ? '修改' : '创建' }}成功！</view>
            </view>
            <view class="flex-all-center">
                <button class="bg-success color-white" style="width: 200px;" @click="lookMyActivityList">
                    查看我的活动
                </button>
            </view>
        </view>

        <view class="type-list flex-row bg-white">
            <view
                class="type-item color-content"
                v-for="(item, index) in type_list"
                :key="index"
                :class="{'active-type': item.id === type_id}"
                :style="{width: 'calc(100% / ' + type_list.length + ')'}"
                @click="type_id = item.id"
            >
                {{ item.title }}
            </view>
        </view>

        <add-activity-tips ref="add_activity_tips"/>

        <view class="form">
            <template v-if="type_id === 1">
                <view class="form-item">
                    <view class="top color-content">
                        <text>活动名称</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" v-model="form_data.name" placeholder="请输入活动名称"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">主办方</view>
                    <view class="bottom font16">
                        <input class="input" v-model="form_data.organizer"
                               placeholder="请输入主办方单位名称"/>
                    </view>
                </view>

                <template v-if="!form_data.active_id">
                    <view class="form-item">
                        <view class="top color-content">
                            <text>手机号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="number" v-model="form_data.mobile"
                                   placeholder="请输入真实手机号"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <text>微信号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" v-model="form_data.wechat_num"
                                   placeholder="请输入真实微信号"/>
                        </view>
                    </view>
                </template>

                <view class="form-item">
                    <view class="top color-content">活动开始时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.begin_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">活动结束时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.end_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view v-if="!conf.active.news.news_id" class="form-item">
                    <view class="top color-content">活动说明</view>
                    <view class="bottom font16">
				        <textarea class="textarea" maxlength="-1" auto-height="true"
                                  v-model="form_data.content" placeholder="请输入活动说明"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>活动说明文章</view>
                        <view class="font12 color-sub">
                            绑定文章后，活动说明将会显示文章内容，不会显示上面填写的活动说明
                        </view>
                    </view>
                    <view class="bottom font16">
                        <view class="flex-kai" @click="toSelNews">
                            <view class="view">
                                <view v-if="conf.active.news.news_id">
                                    {{ conf.active.news.news_title || conf.active.news.news_id }}
                                </view>
                                <view v-else class="color-sub">选择文章</view>
                            </view>
                            <view class="flex-all-center">
                                <view
                                    v-if="conf.active.news.news_id"
                                    class="color-sub font12"
                                    style="width: 30px;"
                                    @click.stop="deleteNews"
                                >解绑
                                </view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>
            </template>


            <template v-if="type_id === 3">
                <view class="form-item">
                    <view class="top color-content">活动参与方式</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker class="view" :range="form_options.enter_types_list" range-key="title"
                                    :value="form_options.enter_types_list.findIndex(v => v.value === conf.active.enter_types)"
                                    @change="conf.active.enter_types = form_options.enter_types_list[$event.detail.value].value">
                                {{
                                    form_options.enter_types_list.find(v => v.value === conf.active.enter_types).title
                                }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="conf.active.enter_types === 2" class="form-item">
                    <view class="top color-content">
                        <text>活动密码</text>
                        <text v-if="!have_password" class="color-error font16"> *</text>
                        <text class="color-sub font12 pl5">(3-20位)</text>
                    </view>
                    <view class="bottom font16">

                        <input class="input" v-model="conf.active.password" maxlength="20"
                               :placeholder="have_password ? '已设置密码，无需修改密码不用填写' : '请输入活动密码'"/>
                    </view>
                </view>


                <view class="form-item">
                    <view class="top color-content">
                        <view>参与活动需要填写的信息</view>
                        <view class="font12 color-error">注意: 第一项为排行榜显示的姓名, 无法删除</view>
                    </view>
                    <view class="bottom font16">
                        <view class="ptm5">
                            <view class="ptm5 must-submit-list"
                                  v-for="(item, index) in conf.must_submit" :key="index">
                                <view class="flex-row">
                                    <view class="flex-row">
                                        <view style="width: 120px;">
                                            <uni-easyinput v-model="item.title" placeholder="请输入内容"/>
                                        </view>

                                        <picker :range="['选填', '必填']" :value="item.rules"
                                                @change="mustItemRulesChange($event, index)">
                                            <view class="must-rules-picker">
                                                <text class="color-content font14">
                                                    {{ item.rules === 0 ? '选填' : '必填' }}
                                                </text>
                                                <text class="iconfont icon-more color-sub font14"></text>
                                            </view>
                                        </picker>


                                        <picker :range="['文本', '单选']" :value="item.types - 1"
                                                :disabled="index === 0"
                                                @change="mustItemTypesChange($event, index)">
                                            <view class="must-rules-picker">
                                                <text class="color-content font14">
                                                    {{ item.types === 1 ? '文本' : '单选' }}
                                                </text>
                                                <text
                                                    class="iconfont icon-more color-sub font14"></text>
                                            </view>
                                        </picker>
                                    </view>
                                    <view v-if="index !== 0" class="delete-rules font14 color-error"
                                          @click="conf.must_submit.splice(index, 1)">
                                        删除
                                    </view>
                                </view>

                                <view v-if="item.types === 2" class="pl10">
                                    <view class="must-options-item flex-row"
                                          v-for="(item_, index_) in item.options" :key="index_">
                                        <view class="color-sub delete-rules text-right"
                                              style="width: 20px; padding: 0 5px 0 0;">
                                            {{ index_ + 1 }}:
                                        </view>
                                        <view style="width: 200px;">
                                            <uni-easyinput :value="item_.text" placeholder="请输入内容"
                                                           @input="mustSubmitOptionsTextInput($event, index, index_)"/>
                                        </view>
                                        <view class="delete-rules">
                                            <text class="color-error font14"
                                                  @click="deleteOptionsItem(index, index_)">
                                                删除
                                            </text>
                                        </view>
                                    </view>
                                    <view class="flex-row">
                                        <view class="color-sub font14 ptm5" @click="addOption(index)">
                                            + 添加新选项
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <view class="flex-row">
                                <view class="color-primary font14 ptm5" @click="addMust">+ 添加新项</view>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">意见反馈</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker class="view" :range="form_options.close_feedback_opt" range-key="title"
                                    :value="form_options.close_feedback_opt.findIndex(v => v.value === conf.active.close_feedback)"
                                    @change="conf.active.close_feedback = form_options.close_feedback_opt[$event.detail.value].value">
                                {{ form_options.close_feedback_opt.find(v => v.value === conf.active.close_feedback).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

            </template>


            <template v-if="type_id === 5">

                <view v-if="ai_sport_type_list.length" class="form-item">
                    <view class="top color-content">运动类型设置</view>
                    <view class="bottom font16 pt5 pb5">
                        <view class="flex-kai pl10 pr10"
                              style="border: 1px solid #eee; border-radius: 5px; margin-bottom: 5px;"
                              v-for="(item, index) in conf.active.ai_sport_list" :key="index">
                            <view>

                                <picker :range="ai_sport_type_list" range-key="name"
                                        @change="aiSportTypeChange($event, index)">
                                    <view style="padding: 5px 0;">
                                        <text class="pr5 color-content">运动类型:</text>
                                        <text>{{ item.name || '请选择运动类型' }}</text>
                                    </view>
                                </picker>
                                <view class="flex-row" style="padding: 5px 0;">
                                    <text class="pr5 color-content">显示文字:</text>
                                    <input v-model="item.title" placeholder="可不填(建议不超过5字)"
                                           style="border-bottom: 1px solid #eee;"/>
                                </view>

                                <view>
                                    <view class="color-sub font14">展示logo图</view>
                                    <view>
                                        <view class="image-view" v-if="item.logo" style="width: 200px;">
                                            <image class="image-item" :src="item.logo" mode="aspectFill"
                                                   style="width: 200px; height: 120px;"
                                                   @click="previewImage([item.logo])"/>
                                            <view class="del-image-item"
                                                  @click.stop="delAiSportItemLogo(index)">
                                                <uni-icons type="closeempty" color="#e20f04"/>
                                            </view>
                                        </view>
                                        <view v-else class="add-image text-center"
                                              @click="addAiSportImg(index)">
                                            <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                                        </view>

                                    </view>
                                </view>
                            </view>
                            <view class="pt5" @click="delAiSportItem(index)">
                                <text class="iconfont icon-delete color-error font20"></text>
                            </view>
                        </view>

                        <view v-if="conf.active.ai_sport_list.length < ai_sport_type_list.length"
                              class="color-primary pt5 pb5" @click="addAiSport">
                            添加运动类型
                        </view>
                    </view>
                </view>


                <view class="form-item">
                    <view class="top color-content">打卡次数限制</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker class="view" :range="form_options.sign_times_list" range-key="title"
                                    :value="form_options.sign_times_list.findIndex(v => v.value === conf.active.sign_times_type)"
                                    @change="conf.active.sign_times_type = form_options.sign_times_list[$event.detail.value].value">
                                {{ form_options.sign_times_list.find(v => v.value === conf.active.sign_times_type).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-show="conf.active.sign_times_type === 2" class="form-item">
                    <view class="top color-content">
                        <view>每个运动类型每日打卡次数上限</view>
                        <view class="color-sub font12">
                            限制每个运动类型每日可以打卡的次数上限(不填或填0,默认为1次)。
                        </view>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="Number" v-model="conf.active.daily_submit_num"
                               placeholder="请输入次数上限"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">打卡记录是否需要审核后才能显示在广场</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker class="view" :range="form_options.not_check_list" range-key="title"
                                    :value="form_options.not_check_list.findIndex(v => v.value === conf.active.not_check)"
                                    @change="conf.active.not_check = form_options.not_check_list[$event.detail.value].value">
                                {{ form_options.not_check_list.find(v => v.value === conf.active.not_check).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">打卡备注填写设置</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker class="view"
                                    :range="form_options.memo_required_list" range-key="title"
                                    :value="form_options.memo_required_list.findIndex(v => v.value === conf.active.memo_required)"
                                    @change="conf.active.memo_required = form_options.memo_required_list[$event.detail.value].value">
                                {{ form_options.memo_required_list.find(v => v.value === conf.active.memo_required).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">打卡图片上传设置</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                :range="form_options.pic_list_required_list" range-key="title"
                                :value="form_options.pic_list_required_list.findIndex(v => v.value === conf.active.pic_list_required)"
                                @change="conf.active.pic_list_required = form_options.pic_list_required_list[$event.detail.value].value"
                            >
                                {{ form_options.pic_list_required_list.find(v => v.value === conf.active.pic_list_required).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">打卡视频上传设置</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                :range="form_options.video_list_required_list"
                                :value="form_options.video_list_required_list.findIndex(v => v.value === conf.active.video_list_required)"
                                range-key="title"
                                @change="videoListRequiredChange"
                            >
                                {{ form_options.video_list_required_list.find(v => v.value === conf.active.video_list_required).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">是否允许修改打卡记录</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker class="view"
                                :range="form_options.records_not_edit_list"
                                :value="form_options.records_not_edit_list.findIndex(v => v.value === conf.active.records_not_edit)"
                                range-key="title"
                                @change="conf.active.records_not_edit = form_options.records_not_edit_list[$event.detail.value].value"
                            >
                                {{ form_options.records_not_edit_list.find(v => v.value === conf.active.records_not_edit).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>
            </template>

            <template v-if="type_id === 2">
                <active-image-set :active-id="form_data.active_id" :rankSet="rank_set"
                                  :logo.sync="form_data.logo"
                                  :screen-pic.sync="conf.active.screen_pic"
                                  :top-rank-banner.sync="conf.active.top_rank_banner"/>
            </template>
        </view>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view class="login-btn color-white text-center font18 bg-primary" :disabled="loading"
                  @click="save">
                {{ form_data.active_id ? '保存' : '创建活动' }}
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import {pinyin} from 'pinyin-pro'


export default {
    data() {
        return {
            loading: true,
            success: false,
            type_list: [
                {title: '基本信息', id: 1},
                {title: '活动设置', id: 3},
                {title: '打卡设置', id: 5},
                {title: '活动图片', id: 2}
            ],
            type_id: 1,
            form_data: {
                name: '',
                wechat_num: '',
                mobile: '',
                organizer: '',
                content: '',
                begin_time: new Date(utils.getDay(0, true, '/') + ' 00:00:00').getTime(),
                end_time: new Date(utils.getDay(30, true, '/') + ' 23:59:59').getTime(),
                logo: ''
            },
            conf: {
                active: {
                    news: {
                        news_id: '',
                        news_title: ''
                    },
                    enter_types: 1,
                    password: '',
                    screen_pic: '',
                    top_rank_banner: [],
                    memo_required: 2,
                    pic_list_required: 2,
                    video_list_required: 0,
                    sign_times_type: 0,
                    records_not_edit: 0,
                    ai_sport_list: [],
                    not_check: 0,
                    close_feedback: 0,
                    daily_submit_num: 1
                },
                must_submit: JSON.parse(JSON.stringify(this.xwy_config.defaultMustSubmit))
            },
            top_rank_banner_max_count: 6,
            form_options: {
                enter_types_list: [
                    {title: '自由报名参与活动', value: 1},
                    {title: '需要输入密码才能进入活动', value: 2},
                    {title: '报名需要审核通过才能参与活动', value: 3}
                ],
                memo_required_list: [
                    {value: 0, title: '关闭打卡填写备注'},
                    {value: 2, title: '开启打卡填写备注'},
                    {value: 1, title: '打卡必须填写备注'}
                ],
                pic_list_required_list: [
                    {value: 0, title: '关闭打卡上传图片'},
                    {value: 2, title: '开启打卡上传图片'},
                    {value: 1, title: '打卡必须上传图片'}
                ],
                video_list_required_list: [
                    {value: 0, title: '关闭打卡上传视频'},
                    {value: 2, title: '开启打卡上传视频'},
                    {value: 1, title: '打卡必须上传视频'}
                ],
                sign_times_list: [
                    {value: 0, title: '不限制打卡次数'},
                    {value: 1, title: '每个运动类型只能打卡一次'},
                    {value: 2, title: '限制每个运动类型每天打卡次数'}
                ],
                records_not_edit_list: [
                    {value: 0, title: '是'},
                    {value: 1, title: '否'}
                ],
                not_check_list: [
                    {value: 0, title: '需要审核'},
                    {value: 1, title: '无需审核'}
                ],
                close_feedback_opt: [
                    {value: 0, title: '开启意见反馈'},
                    {value: 1, title: '关闭意见反馈'}
                ]
            },
            rank_set: {},
            pic_list: [],
            have_password: false,
            ai_sport_type_list: [],
            detail_icon_conf: [{type: 0, title: '文章详情'}],
        }
    },
    onLoad(e) {
        if (e.id) {
            this.form_data.active_id = e.id
        } else {
            this.types = Number(e.types)
            this.$nextTick(() => this.$refs.add_activity_tips.open(this.types))
            this.form_data.logo = this.xwy_config.create_active_default_logo
        }

        this.$uni.setNavigationBarTitle(e.id ? '修改活动' : `创建${e.name || '活动'}`)

        this.$uni.showLoading()


        this.$login.uniLogin(err => {

            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err.errTitle || '提示'})
            }

            if (e.id) return this.getAiSportTypeList()

            const evn_version = app.globalData['evn_version']

            if (evn_version === 'develop') {
                this.form_data.mobile = 12345678910
                this.form_data.wechat_num = 12345678910
                this.form_data.organizer = '伟杰测试'
            }

            // 开发版和体验版不检查重复创建活动
            if (evn_version === 'develop' || evn_version === 'trial') {
                return this.getAiSportTypeList()
            }

            this.checkUserCanCreateActive()
        })
    },
    methods: {

        async checkUserCanCreateActive() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/check_user_can_create_active'
            })

            console.log('检查是否能创建活动', res)
            if (!res.status) {
                uni.hideLoading()
                this.loading = false
                return this.$uni.showModal(res.info || '暂时不能创建活动', {success: () => uni.navigateBack()})
            }

            await this.getAiSportTypeList()
        },

        async getAiSportTypeList() {
            this.ai_sport_type_list = await xwy_api.getAiSportTypeList()

            if (this.form_data.active_id) return this.getDetail()

            this.loading = false
            uni.hideLoading()
        },

        addAiSport() {
            this.conf.active.ai_sport_list.push({
                types: '',
                name: '',
                title: '',
                logo: ''
            })
        },

        delAiSportItem(index) {
            this.conf.active.ai_sport_list.splice(index, 1)
        },

        aiSportTypeChange(e, index) {
            const type_data = this.ai_sport_type_list[e.detail.value]

            const have = this.conf.active.ai_sport_list.find(v => v.types === type_data.types)
            if (have) return this.$uni.showToast(('已添加该运动类型，无需重复添加。'))

            this.conf.active.ai_sport_list[index].types = type_data.types
            this.conf.active.ai_sport_list[index].name = type_data.name
        },

        addAiSportImg(index) {
            let url = '/pages/other/image_upload_or_select'
            if (this.form_data.active_id) url += `?active_id=${this.form_data.active_id}`
            this.$uni.navigateTo(url, {
                events: {
                    newImg: src => {
                        this.conf.active.ai_sport_list[index].logo = src
                    }
                }
            })
        },

        delAiSportItemLogo(index) {
            this.conf.active.ai_sport_list[index].logo = ''
        },


        getDetail() {
            this.xwy_api.getActivityDetail(this.form_data.active_id, res => {
                if (res.data?.active_details) {
                    this.detailInit(res.data.active_details)
                } else {
                    uni.hideLoading()
                    this.$uni.showModal(res?.info || '活动获取失败', {success: () => uni.navigateBack()})
                }
            })
        },


        detailInit(data) {
            this.types = data.types
            this.form_data.name = data.name
            this.form_data.begin_time = data.begin_time * 1000
            this.form_data.end_time = data.end_time * 1000
            if (data.organizer) this.form_data.organizer = data.organizer
            if (data.logo) this.form_data.logo = data.logo
            if (data.content) this.form_data.content = data.content

            this.confDataInit(data.conf)

            if (data.rank_set) {
                const rank_set = data.rank_set

                this.rank_set = rank_set


                if (rank_set.closed_AD && rank_set.closed_AD === 1) {
                    this.detail_icon_conf.push({
                        type: 1,
                        title: '文章列表'
                    })
                }
            }

            uni.hideLoading()
        },

        confDataInit(conf) {
            this.conf.must_submit = conf.must_submit

            const active = conf.active
            this.conf.active.news = active.news
            this.conf.active.enter_types = active.enter_types
            this.conf.active.screen_pic = active.screen_pic
            this.conf.active.top_rank_banner = active.top_rank_banner
            this.conf.active.memo_required = active.memo_required
            this.conf.active.pic_list_required = active.pic_list_required
            this.conf.active.video_list_required = active.video_list_required
            if (active.sign_times_type) this.conf.active.sign_times_type = active.sign_times_type
            if (active.daily_submit_num) this.conf.active.daily_submit_num = active.daily_submit_num
            if (active.records_not_edit) this.conf.active.records_not_edit = active.records_not_edit
            if (active.close_feedback) this.conf.active.close_feedback = active.close_feedback
            if (active.ai_sport_list) this.conf.active.ai_sport_list = active.ai_sport_list
            if (active.not_check) this.conf.active.not_check = active.not_check
            if (active.password) {
                this.old_password = active.password
                this.have_password = true
            }
        },


        videoListRequiredChange(e) {
            const value = this.form_options.video_list_required_list[e.detail.value].value
            if (!this.rank_set['upload_video'] && value !== 0) {
                return this.$uni.showModal('未开通视频上传功能，请联系客服开通')
            }
            this.conf.active.video_list_required = value
        },

        toSelNews() {
            this.$uni.navigateTo('/pages/news/list?type=user&is_sel=true', {
                events: {
                    selNews: data => {
                        this.conf.active.news.news_id = data.id
                        this.conf.active.news.news_title = data.title
                    }
                }
            })
        },

        deleteNews() {
            this.conf.active.news.news_id = ''
            this.conf.active.news.news_title = ''
        },


        mustItemRulesChange(e, index) {
            this.conf.must_submit[index].rules = Number(e.detail.value)
        },
        mustItemTypesChange(e, index) {
            const value = Number(e.detail.value) + 1
            this.conf.must_submit[index].types = value
            if (value === 2) {
                this.conf.must_submit[index].options = this.conf.must_submit[index].options || [{text: ''}]
            }
        },
        addMust() {
            this.conf.must_submit.push({
                name: '',
                rules: 0,
                title: '',
                types: 1
            })
        },

        addOption(index) {
            const item = this.conf.must_submit[index]
            item.options.push({text: ''})
            this.$set(this.conf.must_submit, index, item)
        },
        deleteOptionsItem(index, index_) {
            const item = this.conf.must_submit[index]
            const options = item.options

            if (options.length === 1) return this.$uni.showToast('需保留一个选项')

            options.splice(index_, 1)
            this.$set(this.conf.must_submit, index, item)
        },

        mustSubmitOptionsTextInput(e, index, index_) {
            const item = this.conf.must_submit[index]
            const options = item.options
            options[index_].text = e
            this.$set(this.conf.must_submit, index, item)
        },

        changeImage(key) {
            if (!this.rank_set?.closed_AD) {
                const options = {
                    screen_pic: '无法设置开屏大图，请联系客服设置',
                    top_rank_banner: '无法设置排行榜轮播图，请联系客服设置'
                }
                if (options[key]) return this.$uni.showModal(options[key])
            }

            let url = '/pages/other/image_upload_or_select'
            if (this.form_data.active_id) url += `?active_id=${this.form_data.active_id}`

            this.$uni.navigateTo(url, {
                events: {
                    newImg: src => {
                        switch (key) {
                            case 'logo':
                                this.form_data.logo = src
                                break
                            case 'screen_pic':
                                this.conf.active.screen_pic = src
                                break
                            case 'top_rank_banner':
                                this.conf.active.top_rank_banner.push(src)
                                break
                        }
                    }
                }
            })
        },

        previewImage(urls, current = urls[0]) {
            uni.previewImage({
                urls,
                current
            })
        },


        dataCheck(data) {
            const showToast = title => {
                this.type_id = 1
                this.$uni.showToast(title, 'none')
                return false
            }

            if (!data.name) return  showToast('请输入活动名称')

            if (!data.active_id) {
                if (!data.mobile) return showToast('请输入手机号')
                if (data.mobile.toString().length !== 11) return showToast('手机号长度有误')

                if (!data.wechat_num) return showToast('请输入微信号')

                const wechat_num = data.wechat_num.toString()
                if (wechat_num.length < 4 || wechat_num.length > 20) return showToast('微信号长度有误')

                if (this._utils.isChineseChar(wechat_num)) return showToast('微信号不能输入中文')
            }

            return true
        },

        setMustSubmitData() {
            const errModal = content => {
                this.type_id = 3
                this.$uni.showModal(content)
                return false
            }


            for (let i = 0; i < this.conf.must_submit.length; i++) {
                const v = this.conf.must_submit[i]
                console.log(v)
                if (!v.title) return errModal('参与活动需要填写的信息选项填写不完整，请检查。')

                v.name = pinyin(v.title, {toneType: 'none', type: 'array'}).join('_')
                if (v.types === 2) {
                    if (!v.options || !v.options.length) return errModal(`${v.title} 至少需要添加一个选项。`)

                    for (let j = 0; j < v.options.length; j++) {
                        const v1 = v.options[j]
                        if (!v1 || !v1.text) return errModal(`${v.title} 有未填写的选项，请检查。`)
                    }
                }
            }

            return true
        },

        sportTypeCheck() {
            if (!this.conf.active.ai_sport_list.length) return true
            for (let i = 0; i < this.conf.active.ai_sport_list.length; i++) {
                const item = this.conf.active.ai_sport_list[i]
                if (!item.types) {
                    this.type_id = 5
                    this.$uni.showToast('运动类型设置不完善，请选择运动类型')
                    return false
                }
            }

            return true
        },

        confCheck() {
            const showToast = (title, type_id = 3) => {
                this.type_id = type_id
                this.$uni.showToast(title, 'none')
                return false
            }
            const conf = JSON.parse(JSON.stringify(this.conf))
            if (conf.active.enter_types === 2) {
                if (!this.have_password && !conf.active.password) return showToast('请输入活动密码')

                if (conf.active.password && conf.active.password.length < 3) {
                    return showToast('活动密码不得少于3位')
                }
                if (conf.active.password && conf.active.password.length > 20) {
                    return showToast('活动密码不得大于20位')
                }
                conf.active.password = conf.active.password || this.old_password
            } else {
                conf.active.password = 1
            }

            if (conf.active.sign_times_type === 2) {
                const num = Math.floor(conf.active.daily_submit_num)
                if (isNaN(num)) return showToast('每日打卡次数限制设置不正确', 5)
                conf.active.daily_submit_num = num || 1
            } else {
                delete conf.active.daily_submit_num
            }

            let conf_str = JSON.stringify(conf)
            conf_str = conf_str.replace(/·/g, '-')
            return this._utils.base64['encode'](conf_str)
        },

        save() {
            if (this.success) return false

            const data = JSON.parse(JSON.stringify(this.form_data))

            if (!this.dataCheck(data)) return false
            if (!this.setMustSubmitData()) return false
            if (!this.sportTypeCheck()) return false

            const conf = this.confCheck()
            if (!conf) return false
            data.conf = conf


            data.pic_list = this._utils.base64['encode'](JSON.stringify(this.pic_list))

            data.begin_time /= 1000
            data.end_time /= 1000

            this.saveAjax(data)
        },

        async saveAjax(data) {
            data.types = this.types

            this.loading = true
            this.$uni.showLoading('保存中...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.admin/create_active',
                data
            })

            uni.hideLoading()
            this.loading = false

            if (!res || !res.status) return this.$uni.showModal(res?.info || '保存失败')

            this.updatePageData()

            if (data.active_id && this.id) {
                this.$uni.showToast('保存成功', 'success')
                return this.uni.navigateBack(1, {delay: 1000})
            }

            this.success = true
        },

        // 活动修改/创建成功，如果页面栈有详情或列表页面，更新页面数据
        updatePageData() {
            const pages = getCurrentPages()

            const update_page_list = [
                {
                    route: 'pages/sport_clock_in/user/detail',
                    _function: page => {
                        page.$vm.getDetail(true)
                    }
                },
                {
                    route: 'pages/sport_clock_in/admin/activity/manage',
                    _function: page => {
                        page.$vm.getDetail()
                    }
                },
                {
                    route: 'pages/activity/user/index',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getActivityList()
                    }
                },
                {
                    route: 'pages/activity/user/activity_list',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getList()
                    }
                }
            ]

            update_page_list.forEach(v => {
                const page = pages.find(n => n.route === v.route)
                if (page) v._function(page)
            })
        },

        lookMyActivityList() {
            const list_page_route = 'pages/activity/user/activity_list'
            const pages = getCurrentPages()
            const list_page_index = pages.findIndex(v => {
                return v.route === list_page_route && v.options.type && v.options.type === 'create'
            })
            if (list_page_index === -1) {
                uni.redirectTo({
                    url: `/${list_page_route}?type=create`
                })
                return false
            }

            const back_delta = pages.length - list_page_index - 1
            uni.navigateBack({
                delta: back_delta
            })
        }
    }
}
</script>

<style>
.page {
    padding-top: 40px;
    padding-bottom: 75px;
}

.type-list {
    position: fixed;
    z-index: 9;
    width: 100vw;
    top: 0;
    left: 0;
}

.type-item {
    height: 40px;
    line-height: 40px;
    text-align: center;
    box-sizing: border-box;
}

.active-type {
    color: #007AFF;
    border-bottom: 2px solid #007AFF;
}

.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}


.form-item .textarea {
    width: 100%;
    line-height: 16px;
    padding: 10px 0;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}

.must-submit-list {
    border-bottom: 1px dashed #eee;
}

.must-rules-picker {
    line-height: 34px;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    margin-left: 5px;
    padding: 0 15px;
}

.delete-rules {
    line-height: 36px;
    padding: 0 10px;
}

.must-options-item {
    padding: 3px 0;
}


.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
    line-height: calc((100vw - 50px) / 3);
    margin: 5px;
}

.image-item {
    width: calc(100vw - 20px);
    height: 50vw;
    border-radius: 5px;
}


.image-view {
    position: relative;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}

.bottom-btn-view {
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.login-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}

.save-success {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 999999;
    box-sizing: border-box;
    padding-top: 10vh;
}

.save-success .icon {
    padding-bottom: 50px;
}

.uni-date-x {
    padding: 0 !important;
}

.uni-date__x-input {
    font-size: 16px !important;
    color: #000;
}

/* #ifdef H5 */

@media screen and (min-width: 500px) {
    .type-list {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }


    .image-item {
        width: 480px;
        height: 200px;
    }
}

/* #endif */
</style>
