<template>
    <view class="page bg-background">
        <view v-if="recorder_bg_img" class="bg-img">

            <image :src="recorder_bg_img" mode="aspectFit"/>
        </view>

        <view class="bottom-bar bg-white">
            <view v-if="voice" style="position: relative;">
                <view>

                    <!--:max="duration_all.value" => :max="duration_all.value || 1"  解决在真机上，如果max和value都是0，value圆点会在中间问题-->
                    <!--disabled="!duration_all.value"  没有获取到总时长时，不允许被拖动-->

                    <slider
                        :max="duration_all.value || 1"
                        :value="duration_current.value"
                        :block-size="12"
                        :disabled="!duration_all.value"
                        activeColor="#2d8cf0"
                        block-color="#2d8cf0"
                        @change="seekTo($event)"
                        @changing="durationChanging()"
                    ></slider>
                </view>
                <view class="flex-kai">
                    <view class="color-sub font12 flex-all-center">
                        {{ duration_current.value_text }}
                    </view>
                    <view class="color-sub font12 flex-all-center">
                        {{ duration_all.value_text }}
                    </view>
                </view>
                
            </view>
            <view class="btns" :class="voice ? 'flex-kai' : 'flex-center'">
                <view>
                    <view class="flex-all-center small-btn" hover-class="navigator-hover" @click="back">
                        <uni-icons type="undo" size="24" color="#2d8cf0"/>
                    </view>
                    <view class="font14 color-content text-center pt10">返回</view>
                </view>

                <template v-if="voice">
                    <view v-if="play_status === 'pause'">
                        <view
                            class="big-btn flex-all-center"
                            hover-class="navigator-hover"
                            @click="audioPlay"
                        >
                            <text class="iconfont icon-play-filled color-primary font28"></text>
                        </view>
                        <view class="font14 color-content text-center pt10">播放</view>
                    </view>
                    <view v-if="play_status === 'play'">
                        <view
                            class="big-btn flex-all-center"
                            hover-class="navigator-hover"
                            @click="audioPause"
                        >
                            <text class="iconfont icon-pause-filled color-primary font28"></text>
                        </view>
                        <view class="font14 color-content text-center pt10">暂停</view>
                    </view>
                </template>

                <view>
                    <button class="flex-all-center small-btn bg-white" hover-class="navigator-hover"  open-type="share">
                        <text class="iconfont icon-share color-primary font24"></text>
                    </button>
                    <view class="font14 color-content text-center pt10">分享</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'

let innerAudioContext = null


export default {
    data() {
        return {
            imageWidth: uni.getSystemInfoSync().windowWidth - 20,
            play_status: 'pause',
            recorder_bg_img: '',
            duration_all: {
                value: 0,
                value_text: '00:00'
            },
            duration_current: {
                value: 0,
                value_text: '00:00'
            },
            active_id: '',
            voice: ''
        }
    },

    onLoad(e) {
        this.uuid = e.uuid
        this.$uni.showLoading('加载中...')

        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getDetail()
        })

    },


    onUnload() {
        this.play_status === 'play' && innerAudioContext && innerAudioContext.stop()
    },
    
    onHide() {
        this.play_status === 'play' && innerAudioContext && innerAudioContext.stop()
    },

    methods: {
        async getDetail() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.party_guess_pic.oath/oath_details',
                data: {
                    uuid: this.uuid
                }
            })
            
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '获取失败', {
                success: () => this.back()
            })

            const details = res.data?.details
            if (!details) return this.$uni.showModal('获取失败', {success: () => this.back()})

            this.active_id = details.active_id
            this.recorder_bg_img = details.conf_json?.pic || ''
            if (details.conf_json?.audio) {
                this.voice = details.conf_json.audio
                this.audioContextOn()
            }
        },

        audioContextOn() {
            innerAudioContext = uni.createInnerAudioContext()
            innerAudioContext.src = this.voice

            innerAudioContext.onCanplay(() => {
                console.log('onCanplay --- duration：', innerAudioContext.duration)
            })
            innerAudioContext.onPlay(() => {
                this.play_status = 'play'
                uni.hideLoading()
            })
            innerAudioContext.onPause(() => {
                this.play_status = 'pause'
            })
            innerAudioContext.onStop(() => {
                this.play_status = 'pause'
            })
            innerAudioContext.onEnded(() => {
                this.play_status = 'pause'
            })
            innerAudioContext.onError(err => {
                uni.hideLoading()
                console.log('onError', err)
                this.play_status = 'pause'
                const err_opt = {
                    '10001': '系统错误',
                    '10002': '网络错误',
                    '10003': '文件错误',
                    '10004': '格式错误',
                    '-1': '未知错误'
                }
                this.$uni.showModal(`播放失败 (${err.errCode}: ${err_opt[err.errCode]})`)
            })

            innerAudioContext.onTimeUpdate(() => {
                const duration = innerAudioContext.duration
                const currentTime = innerAudioContext.currentTime
                if (duration && duration !== Infinity) {
                    this.duration_all = {
                        value: Math.floor(duration),
                        value_text: this.initDuration(duration)
                    }
                }
                if (currentTime && currentTime !== Infinity) {
                    this.duration_current.value_text = this.initDuration(currentTime)
                    // 拖动的时候不改变进度条的值，不然进度条会一跳一跳
                    if (this.duration_changing) return false
                    this.duration_current.value = Math.floor(currentTime)
                }
            })
        },
        
        initDuration(duration) {
            duration = Math.floor(duration)
            let mm = Math.floor(duration / 60)
            if (mm < 10) mm = '0' + mm
            let ss = duration % 60
            if (ss < 10) ss = '0' + ss
            return mm + ':' + ss
        },

        audioPlay() {
            this.$uni.showLoading('准备播放...')

            innerAudioContext.play()
        },

        audioPause() {
            innerAudioContext.pause()
        },

        seekTo(e) {
            this.duration_changing = false
            const {value} = e.detail
            this.duration_current = {
                value,
                value_text: this.initDuration(value)
            }
            innerAudioContext.seek(value)
        },

        durationChanging() {
            this.duration_changing = true
        },


        back() {
            if (getCurrentPages().length === 1) {
                return this.$uni.redirectTo(`/pages/take-oath/list?active_id=${this.active_id}`)
            }
            this.$uni.navigateBack()
        }
    },

    onShareAppMessage() {
        return {
            title: '宣誓词',
            imageUrl: this.recorder_bg_img || '',
            path: `/pages/take-oath/details?uuid=${this.uuid}`
        }
    },
}
</script>

<style lang="scss">
.bg-img {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: calc(100vh - 160px);
    z-index: 2;

    image {
        width: 100vw;
        height: calc(100vh - 160px);
        display: block;
    }
}


.bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 99;
    width: 100vw;
    border-radius: 20px 20px 0 0;
    padding: 20px;
    /* box-shadow: 0 0 10px 3px #eee; */
    box-sizing: border-box;

    .btns {
        padding: 0 20px;

        .big-btn, .small-btn {
            border-radius: 50%;
            box-shadow: 0 0 5px 2px #eee;
            background-color: #fff;
        }

        .big-btn {
            width: 60px;
            height: 60px;
        }

        .small-btn {
            margin-top: 5px;
            width: 50px;
            height: 50px;

            &::after {
                content: "";
                border: none;
            }
        }
    }
}
</style>
