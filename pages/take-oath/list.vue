<template>
    <view class="page bg-background">
        <xwy-ad v-if="!loading" :activity_id="active_id" :ad_type="3"></xwy-ad>

        <template v-if="show_square">
            <view class="top-bar bg-background flex-all-center">
                <view class="top-bar-list bg-white flex-row">
                    <view v-for="item in [{name: '广场', value: false}, {name: '我的', value: true}]"
                          class="top-bar-item" :class="{'top-bar-active': item.value === myself}"
                          :key="item.name"  @click="myselfSwitch(item.value)">
                        {{ item.name }}
                    </view>
                </view>
            </view>
            
            <view class="w-100" style="height: 60px;"></view>
        </template>
        
        <view class="list">
            <template v-for="(item, index) in oath_list">
                <view :id="'item-' + item.id" :key="item.id" @click="lookDetails(item)">
                    
                    <view class="item bg-white" :id="item.id">
                        <view class="flex-row">
                            <view class="pr10">
                                <image class="headimg" :src="item.headimg" mode="aspectFill"/>
                            </view>
                            <view style="width: 100%;">
                                <view v-if="item.nickname" class="color-title">{{ item.nickname }}</view>


                                <view v-if="item.image" class="img-list flex-row flex-wrap pt5">

                                    <image class="img-item" :src="item.image" mode="heightFix"
                                           @click.stop="previewImage(item.image)"/>
                                </view>

                                <view v-if="item.voice" class="pt5">
                                    <view class="audio-item flex-row">
                                        <view class="flex-all-center">
                                            <view class="play-btn flex-all-center"
                                                  hover-class="navigator-hover"
                                                  @click.stop="audioPlay(item.id, item.voice)">
                                                <text class="iconfont color-primary font18"
                                                      :class="player_id === item.id ? 'icon-pause-filled' : 'icon-play-filled'"></text>
                                            </view>
                                        </view>
                                    </view>
                                </view>


                                <view class="flex-kai pt5 color-sub font14">
                                    <view>{{ item.create_time }}</view>
                                    <view v-if="myself" @click.stop="deleteItem(item.id)">删除</view>
                                </view>
                            </view>
                        </view>

                    </view>

                    <view v-if="index !== 0 && ((index + 1) % 10 === 0)" style="padding-bottom: 20px;">
                        <xwy-ad :activity_id="active_id" :ad_type="66"></xwy-ad>
                    </view>
                </view>
            </template>
        </view>

        <view v-if="loading" class="text-center">
            <load-ani :padding="current_page === 1 ? '30vh 0' : '0'"/>
        </view>

        <view v-if="!loading && !oath_list.length" class="text-center" style="padding-top: 20vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无宣誓词</view>
            <view class="submit-button text-center bg-primary color-white" hover-class="navigator-hover"
                  @click="toSubmit">提交宣誓词
            </view>
        </view>
        
        <view v-if="!loading && oath_list.length" class="fixed-add-icon flex-all-center bg-light-primary"
              hover-class="navigator-hover" @click="toSubmit">
            <uni-icons type="plusempty" size="24" color="#ffffff"/>
        </view>
    </view>
</template>

<script>
const app = getApp()
let innerAudioContext = null

export default {
    data() {
        return {
            active_id: '',
            loading: true,
            oath_list: [],
            current_page: 1,
            is_last_page: false,
            player_id: '',
            myself: true,
            show_square: false
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.$login.uniLogin(() => {
            this.init()
        })
    },

    onPullDownRefresh() {
        this.reloadOathList().finally(() => uni.stopPullDownRefresh())
    },

    onReachBottom() {
        if (this.loading || this.is_last_page) return
        this.current_page++
        this.getOathList()
    },

    onUnload() {
        this.audioStop()
    },

    onHide() {
        this.audioStop()
    },

    methods: {
        async init() {
            await this.getActiveDetails()
            await this.getUserDetails()
            await this.reloadOathList()
            this.audioContextOn()
        },
        
        async getActiveDetails() {
            let {activity_detail: details = null, userid = null} = app.globalData
            if (details?.active_id !== this.active_id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: { active_id: this.active_id }
                })
                details = res.data.active_details
            }

            if (!details) return
            
            if (details.conf?.active?.take_oath_square_open) this.show_square = true
            if (userid === details.userid && !this.show_square) this.show_square = true
            if (this.show_square) this.myself = false
        },

        async getUserDetails() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: {
                    active_id: this.active_id
                }
            })
            this.is_join = !!res?.data?.user_details?.id
            this.checked = res?.data?.user_details?.checked === 1
        },


        myselfSwitch(value) {
            if (this.myself === value) return
            this.myself = value
            this.reloadOathList()  
        },

        async reloadOathList() {
            this.current_page = 1
            this.oath_list = []
            this.is_last_page = false
            this.audioStop()
            await this.getOathList()
        },

        async getOathList() {
            const data = {
                active_id: this.active_id,
                page: this.current_page,
                perpage: 20
            }
            if (this.myself) data.myself = 1

            this.loading = true
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.party_guess_pic.oath/user_submit_list',
                data
            })
            this.loading = false

            const res_data = res?.data?.list
            if (!res_data) {
                this.is_last_page = true
                return
            }

            const {data: list = [], is_lastpage} = res_data
            const _list = this.initList(list)
            this.oath_list = this.oath_list.concat(_list)
            this.is_last_page = is_lastpage
        },

        initList(list) {
            return list.map(item => {
                return {
                    id: item.id,
                    uuid: item.uuid,
                    create_time: item.create_time,
                    headimg: item.user_details?.headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg',
                    nickname: item.user_details?.must_submit?.[0]?.value || '',
                    image: item.conf_json?.pic || '',
                    voice: item.conf_json?.audio || ''
                }
            })
        },

        async deleteItem(id) {
            const confirm = await this.$uni.showModal('确定删除?', {showCancel: true})
            if (!confirm.confirm) return
            
            this.$uni.showLoading('删除中...')
            const res = await this.xwy_api.deleteRecords(97, id)
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '删除失败')
            
            this.$uni.showToast('已删除')
            await this.reloadOathList()
        },

        audioContextOn() {
            if (!innerAudioContext) innerAudioContext = uni.createInnerAudioContext()

            innerAudioContext.onPlay(() => {
                console.log('onPlay')
            })
            innerAudioContext.onPause(() => {
                console.log('onPause')
            })
            innerAudioContext.onStop(() => {
                console.log('onStop')
            })
            innerAudioContext.onEnded(() => {
                console.log('onEnded')
                const player_id = this.player_id
                this.player_id = ''
                this.playNext(player_id)
            })
            innerAudioContext.onError(err => {
                console.log('onError', err)
                this.player_id = ''
                const err_opt = {
                    '10001': '系统错误',
                    '10002': '网络错误',
                    '10003': '文件错误',
                    '10004': '格式错误',
                    '-1': '未知错误'
                }
                this.$uni.showModal(`播放失败 (${err.errCode}: ${err_opt[err.errCode]})。${err.errMsg}`)
            })
        },
        
        // 非手动暂停播放  如 页面切换、列表重新加载等
        audioStop() {
            this.next_play_timeout && clearTimeout(this.next_play_timeout)
            if (this.player_id && innerAudioContext) {
                this.player_id = ''
                innerAudioContext.stop()
            }
        },

        playNext(player_id) {
            const list = this.oath_list
            const index = list.findIndex(v => v.id === player_id)
            if (index === -1 || index === list.length - 1) return false
            const nextAudio = list[index + 1]

            if (!nextAudio.voice) return this.playNext(nextAudio.id)

            this.next_play_timeout = setTimeout(() => {
                uni.pageScrollTo({
                    selector: '#item-' + nextAudio.id,
                    fail: err => {
                        console.log('滚动失败', err)
                    }
                })
                this.audioPlay(nextAudio.id, nextAudio.voice)
            }, 1000)
        },


        audioPlay(player_id, src) {
            this.next_play_timeout && clearTimeout(this.next_play_timeout)
            if (!this.player_id) {
                // 没有播放音频，播放点前点击音频
                this.player_id = player_id
                innerAudioContext.src = src
                return innerAudioContext.play()
            }

            if (player_id === this.player_id) {
                // 播放的是点击的音频，暂停音频播放
                this.player_id = ''
                return innerAudioContext.pause()
            }

            // 切换音频播放
            innerAudioContext.stop()
            this.player_id = player_id
            innerAudioContext.src = src
            innerAudioContext.play()
        },


        toSubmit() {
            if (!this.is_join) {
                const pages = this.$uni.pages()
                
                return this.$uni.showModal('需要报名活动才能提交宣誓词', {
                    showCancel: true,
                    confirmText: pages.length === 1 ? '返回活动' : '去报名',
                    success: res => {
                        if (res.confirm) {
                            if (pages.length === 1) {
                                return this.$uni.reLaunch(`/pages/activity/user/detail?id=${this.active_id}`)
                            }
                            const up_page = pages[pages.length - 2]
                            up_page.$vm.joinActivity()
                            this.$uni.navigateBack()
                        }
                    }
                })
            }
            
            if (!this.checked) return this.$uni.showToast('报名未通过管理员审核，无法提交宣誓词')
                
                
            
            this.$uni.navigateTo(`./submit/submit?active_id=${this.active_id}`, {
                events: {
                    reloadOathList: () => {
                        this.reloadOathList()
                        this.getOpenerEventChannel().emit('reloadOathList')
                    }
                }
            })
        },

        lookDetails(item) {
            this.$uni.navigateTo(`./details?uuid=${item.uuid}`)  
        },

        previewImage(src) {
            this.$uni.previewImage(src)
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    padding-bottom: 150px;
    box-sizing: border-box;
}

.top-bar {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 99;
    width: 100%;
    padding: 10px;
    box-sizing: border-box;

    .top-bar-list {
        padding: 4px;
        border-radius: 22px;
        
        .top-bar-item {
            width: 100px;
            text-align: center;
            line-height: 36px;
            border-radius: 18px;
            color: #333;
        }
        
        .top-bar-active {
            background-color: #5cadff;
            color: #fff;
        }
    }
}

.list {
    padding: 10px;

    .item {
        padding: 10px;
        border-radius: 10px;
        margin-bottom: 20px;

        .headimg {
            width: 40px;
            min-width: 40px;
            height: 40px;
            display: block;
            border-radius: 50%;
        }

        .audio-item {
            background-color: #f8f8f8;
            border-radius: 10px;
            padding: 10px;
            margin-bottom: 5px;

            .play-btn {
                width: 36px;
                min-width: 36px;
                height: 36px;
                box-sizing: border-box;
                border-radius: 50%;
                background-color: #fff;
                box-shadow: 0 0 5px 2px #eee;
            }
        }

        .img-list {
            position: relative;
            left: -5px;
            padding-top: 5px;

            .img-item {
                display: block;
                margin-left: 5px;
                margin-bottom: 5px;
                border-radius: 5px;
                height: 120px;
                width: auto;
                max-width: calc(100vw - 130px);
            }
        }
    }
}

.fixed-add-icon {
    position: fixed;
    bottom: 100px;
    right: 10px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

.submit-button {
    width: 200px;
    line-height: 44px;
    border-radius: 22px;
    margin: 20px auto;
}
</style>