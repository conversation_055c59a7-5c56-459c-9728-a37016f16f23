<template>
    <view class="page bg-background">
        <view class="p10">

            <image class="image" :src="image" mode="aspectFit"/>
        </view>


        <view class="bottom-bar bg-white">
            <view class="text-center color-sub font12 pb10">最大录制时长{{ max_duration_text }}</view>
            
            <uni-transition
                :mode-class="['fade', 'slide-left']"
                :show="recorder_status === 'waiting'"
            >
                <view class="flex-all-center" v-if="recorder_status === 'waiting'">
                    <view @click="startRecord">
                        <view class="big-btn flex-all-center">
                            <uni-icons type="mic" size="30" color="#2d8cf0"/>
                        </view>
                        <view class="font14 color-content text-center pt10">开始录制</view>
                    </view>
                </view>
            </uni-transition>

            <uni-transition :mode-class="['fade', 'slide-right']" :show="recorder_status !== 'waiting'">

                <view class="btns" v-if="recorder_status !== 'waiting'">

                    <view @click="rerecording">
                        <view class="flex-all-center small-btn">
                            <uni-icons type="refreshempty" size="24" color="#2d8cf0"/>
                        </view>
                        <view class="font14 color-content text-center pt10">重录</view>
                    </view>

                    <view v-if="recorder_status === 'pause'" @click="resumeRecord">
                        <view class="big-btn flex-all-center">
                            <uni-icons type="mic" size="30" color="#2d8cf0"/>
                        </view>
                        <view class="font14 color-content text-center pt10">继续录制</view>
                    </view>
                    <view v-if="recorder_status === 'start'">
                        <view class="big-btn flex-all-center" @click="pauseRecord">
                            <text class="iconfont icon-pause-filled color-primary font28"></text>
                        </view>
                        <view class="font14 color-content text-center pt10">{{ recorder_time_text }}</view>
                    </view>
                    <view @click="endRecord">
                        <view class="flex-all-center small-btn">
                            <uni-icons type="checkmarkempty" size="24" color="#2d8cf0"/>
                        </view>
                        <view class="font14 color-content text-center pt10">完成</view>
                    </view>

                </view>
            </uni-transition>
        </view>


    </view>
</template>

<script>
const app = getApp()


export default {
    data() {
        return {
            image: '',
            recorder_status: 'waiting',  // waiting: 等待中   start: 录音中 pause: 暂停
            recorder_time: 0,
            max_duration: 300,   // 最大录制时长 秒
        }
    },
    
    computed: {
        recorder_time_text() {
            const seconds = this.recorder_time
            const mm = Math.floor(seconds / 60).toString().padStart(2, '0')
            const ss = (seconds % 60).toString().padStart(2, '0')

            return `${mm}:${ss}`
        },

        max_duration_text() {
            if (this.max_duration % 60 === 0) return `${this.max_duration / 60}分钟`
            return `${this.max_duration}秒`
        }
    },
    
    onLoad(options) {
        if (options.image) this.image = options.image
        this.recorderManagerOn()
    },

    onUnload() {
        if (this.recorder_status === 'waiting') return false
        this.not_submit = true
        this.recorderManager.stop()
    },

    methods: {
        rerecording() {
            this.pauseRecord()
            uni.showModal({
                title: '提示',
                content: '确定重新录制？',
                success: res => {
                    if (res.confirm) {
                        this.not_submit = true
                        this.recorderManager.stop()
                    }
                }
            })
        },

        recorderTimeIntervalStart() {
            this.recorder_time_interval = null
            this.recorder_time_interval = setInterval(() => {
                this.recorder_time++
                if (this.recorder_time >= this.max_duration) {
                    this.timeOut()
                }
            }, 1000)
        },

        clearRecorderTimeInterval() {
            if (!this.recorder_time_interval) return
            clearInterval(this.recorder_time_interval)
        },

        timeOut() {
            this.clearRecorderTimeInterval()
            this.pauseRecord()
            this.recorder_status = 'pause'

            uni.showModal({
                title: '提示',
                content: '时间到，是否录制完成？录制完成请点击“录制完成”后提交，未录制完成可点“重新录制”进行重新录制',
                cancelText: '重新录制',
                confirmText: '录制完成',
                success: res => {
                    this.recorder_status = 'waiting'
                    res.confirm && this.endRecord()
                    if (res.cancel) {
                        this.not_submit = true
                        this.recorderManager.stop()
                    }
                }
            })
        },

        recorderManagerOn() {
            const recorderManager = uni.getRecorderManager()
            recorderManager.onStart(res => {
                uni.hideLoading()
                console.log('recorder Start', res)
                this.recorder_status = 'start'
                this.recorderTimeIntervalStart()
                // #ifdef MP-WEIXIN
                wx.enableAlertBeforeUnload({
                    message: '退出后已录制的语音不会保存哦，是否确定退出？'
                })
                // #endif
            })
            recorderManager['onResume'](() => {
                console.log('recorder Resume')
            })
            recorderManager.onPause(res => {
                console.log('recorder Pause', res)
                this.clearRecorderTimeInterval()
                this.recorder_status = 'pause'
            })
            recorderManager.onStop(res => {
                console.log('recorder Stop', res)
                this.clearRecorderTimeInterval()
                this.recorder_time = 0
                this.recorder_status = 'waiting'

                if (this.not_submit) {
                    this.not_submit = false
                    return false
                }

                this.toSubmit(res)
            })
            recorderManager.onError(err => {
                uni.hideLoading()
                console.log('recorder Error', err)
                if (err && err.errMsg && err.errMsg === "operateRecorder:fail auth deny") {
                    this.getRecorderAuth()
                }
            })
            this.recorderManager = recorderManager
        },

        getRecorderAuth() {
            uni.authorize({
                scope: 'scope.record',
                success: () => {
                    this.startRecord()
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '需要授权录音功能才能录制哦',
                        confirmText: '去授权',
                        success: res => {
                            res.confirm && uni.openSetting()
                        }
                    })
                }
            })
        },

        startRecord() {
            /*uni.showLoading({
                title: '准备录制...',
                mask: true
            })*/
            this.recorderManager.start({
                // 因为在倒计时的定时器里做了到时间结束录音，所以这里不做限制
                // 指定录音的时长，单位 ms ，如果传入了合法的 duration ，在到达指定的 duration 后会自动停止录音，最大值 600000（10 分钟）,默认值 60000（1 分钟）
                duration: 600000,
                format: 'mp3' // 音频格式，有效值 aac/mp3/wav/PCM。App默认值为mp3，小程序默认值aac
            })
        },
        resumeRecord() {
            console.log('继续录音')
            this.recorderManager.resume()
            this.recorder_status = 'start'
            this.recorderTimeIntervalStart()
        },
        pauseRecord() {
            console.log('暂停录音')
            this.recorderManager.pause()
        },
        endRecord() {
            if (this.recorder_status === 'start') this.pauseRecord()
            if (this.recorder_time < 5) return this.$uni.showToast('录制时长不能少于5秒')
            console.log('录音结束')
            this.recorderManager.stop()
            
            // #ifdef MP-WEIXIN
            wx.disableAlertBeforeUnload()
            // #endif
        },


        toSubmit(res) {
            this.getOpenerEventChannel().emit('getVoice', res)
            this.$uni.navigateBack()
        }
    }
}
</script>

<style>
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 160px;
}

.image {
    width: calc(100vw - 20px);
    height: calc(100vh - 150px);
    display: block;
}

.bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 99;
    width: 100vw;
    border-radius: 20px 20px 0 0;
    padding: 20px;
    /* box-shadow: 0 0 10px 3px #eee; */
    box-sizing: border-box;
}

.btns {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.big-btn, .small-btn {
    border-radius: 50%;
    box-shadow: 0 0 5px 2px #eee;
}

.big-btn {
    width: 60px;
    height: 60px;
}

.small-btn {
    margin-top: 5px;
    width: 50px;
    height: 50px;
}

</style>
