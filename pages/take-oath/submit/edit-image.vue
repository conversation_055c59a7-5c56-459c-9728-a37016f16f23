<template>
    <view class="page">
        <view class="text-center color-white font14" style="height: 40px; line-height: 40px;">
            <template v-if="user_image_path">提示: 单指拖动图片，双指缩放图片。</template>
        </view>
        <view class="flex-all-center" :style="{width: max_width + 'px', height: max_height + 'px'}">
            <canvas id="canvas" canvas-id="canvas"
                    :style="{width: canvas_width + 'px', height: canvas_height + 'px'}" @touchstart="touchStart" @touchmove="touchMove" @touchend="touchEnd"></canvas>
        </view>
        
        <view class="bottom-bar flex-kai">
            <view class="bottom-button color-green" @click="changeUserImage">
                {{ user_image_path ? '更换' : '上传' }}图片
            </view>
            <view class="bottom-button bg-green color-white" @click="done">完成</view>
        </view>
    </view>
</template>

<script>
const {windowWidth, windowHeight, pixelRatio} = uni.getSystemInfoSync()

export default {
    data() {
        return {
            max_width: windowWidth,
            
            // 140: 顶部提示加底部按钮的空间   60: 安卓系统会吧系统的虚拟按键也算到屏幕高度里面，要去掉，大概选60像素吧
            max_height: windowHeight - 140 - 60,
            user_image_path: '',
            canvas_width: 0,
            canvas_height: 0
        }
    },

    onLoad() {
        this.getImageData()
    },

    methods: {
        getImageData() {
            this.getOpenerEventChannel().on('imageData', res => {
                this.bg_img = res.bg_img
                if (res.user_image_data) {
                    const {path, x, y, width, height} = res.user_image_data
                    this.user_image_path = path
                    this.user_image_data = {x, y, width, height}
                }

                this.getBgInfo()
            })
        },
        
        async getBgInfo() {
            const info = await this.$uni.getImageInfo(this.bg_img)
            if (!info) {
                const tips = '活动图片加载失败，请联系活动管理员检查图片'
                return this.$uni.showModal(tips, {success: () => uni.navigateBack()})
            }

            const {path, width, height} = info
            const {max_width, max_height} = this

            let canvas_width = max_width
            let canvas_height = height * (max_width / width)
            if (canvas_height > max_height) {
                canvas_height = max_height
                canvas_width = width * (max_height / height)
            }
            
            this.bg_path = path
            this.canvas_width = canvas_width
            this.canvas_height = canvas_height
            
            this.ctx = uni.createCanvasContext('canvas')
            
            this.draw()
            
            if (!this.user_image_path) await this.changeUserImage()
        },

        async changeUserImage() {
            const path = await this.chooseImage()
            if (!path) return
            const image_info = await this.getImageInfo(path)
            if (!image_info) return this.$uni.showToast('图片获取失败，请重试')
            this.setImageData(image_info)
            this.draw()
        },

        chooseImage() {
            return new Promise(resolve => {
                uni.chooseImage({
                    count: 1,
                    success: res => resolve(res.tempFilePaths[0]),
                    fail: () => resolve(null)
                })
            })  
        },

        getImageInfo(path) {
            return new Promise(resolve => {
                uni.getImageInfo({
                    src: path,
                    success: res => resolve(res),
                    fail: () => resolve(null)
                })
            })
        },

        setImageData(info) {
            const {path, width, height} = info
            this.user_image_path = path

            const max_width = this.canvas_width / 2
            const max_height = this.canvas_height / 2

            let image_width = width, image_height = height
            if (image_width > max_width) {
                image_width = max_width
                image_height = height * (max_width / width)
            }
            if (image_height > max_height) {
                image_height = max_height
                image_width = width * (max_height / height)
            }

            this.user_image_data = {
                x: max_width - image_width / 2,
                y: max_height - image_height / 2,
                width: image_width,
                height: image_height
            }
        },

        draw() {
            this.ctx.drawImage(this.bg_path, 0, 0, this.canvas_width, this.canvas_height)
            if (this.user_image_path) {
                const {x, y, width, height} = this.user_image_data
                this.ctx.drawImage(this.user_image_path, x, y, width, height)
            }
            this.ctx.draw()
        },


        touchStart(e) {
            this.touchType = null
            this.startDistance = null
            
            if (!this.user_image_path) return
            
            const {x, y} = e.changedTouches[0]
            if (this.inImage(x, y)) {
                this.touchX = x
                this.touchY = y
                this.image_old_x = this.user_image_data.x
                this.image_old_y = this.user_image_data.y
                this.touchType = 'move'
            }
        },

        touchMove(e) {
            if (!this.user_image_path) return

            const fingers = e.changedTouches

            if (fingers.length === 2) {
                this.touchType = 'scale'
                const [{x: x1, y: y1}, {x: x2, y: y2}] = fingers
                return this.imageScale(x1, y1, x2, y2)
            }
            
            if (fingers.length === 1 && this.touchType === 'move') {
                const {x, y} = fingers[0]
                return this.imageMove(x, y)
            }
            
        },

        touchEnd() {
            this.touchType = null
            this.startDistance = null
        },

        inImage(x, y) {
            const {x: i_x, y: i_y, width: width, height: height} = this.user_image_data
            return x >= i_x && x <= i_x + width && y >= i_y && y <= i_y + height
        },

        imageMove(x, y) {
            const {touchX, touchY, image_old_x, image_old_y} = this
            this.user_image_data.x = image_old_x + x - touchX
            this.user_image_data.y = image_old_y + y - touchY
            this.draw()
        },

        imageScale(x1, y1, x2, y2) {
            const moveDistance = Math.hypot(x1 - x2, y1 - y2)
            const scale = moveDistance / (this.startDistance || moveDistance)
            this.startDistance = moveDistance
                
            const {width: old_width, height: old_height} = this.user_image_data
            
            this.user_image_data.width *= scale
            this.user_image_data.height *= scale

            this.user_image_data.x -= (this.user_image_data.width - old_width) / 2
            this.user_image_data.y -= (this.user_image_data.height - old_height) / 2
            
            this.draw()
        },


        done() {
            this.$uni.showLoading('图片保存中...')
            this.$nextTick(() => {
                setTimeout(() => {
                    uni.canvasToTempFilePath({
                        canvasId: 'canvas',
                        x: 0,
                        y: 0,
                        width: this.canvas_width,
                        height: this.canvas_height,
                        destWidth: this.canvas_width * (pixelRatio || 1),
                        destHeight: this.canvas_height * (pixelRatio || 1),
                        success: res => this.complete(res.tempFilePath),
                        fail: () => this.done()
                    }, this)
                }, 300)
            })
        },

        complete(path) {
            uni.hideLoading()
            const emit = this.getOpenerEventChannel().emit
            emit('setImage', path)
            
            let user_image_data = null
            if (this.user_image_path) {
                const {x, y, width, height} = this.user_image_data
                user_image_data = {x, y, width, height, path: this.user_image_path}
            }
            
            emit('saveUserImageData', user_image_data)
            this.$uni.navigateBack()
        }
    }
}
</script>

<style lang="scss">
.page {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    box-sizing: border-box;
    background-color: #000;
}

.bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px;
    box-sizing: border-box;
    
    .bottom-button {
        border-radius: 5px;
        width: 80px;
        line-height: 28px;
        text-align: center;
    }
}
</style>