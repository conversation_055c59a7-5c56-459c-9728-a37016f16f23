<template>
    <view class="page bg-background">
        <view class="p10">

            <image class="image" :src="image" mode="aspectFit"/>
        </view>

        <view class="bottom-bar bg-white">
            <view class="text-center font12 color-sub">请按下列步骤完成宣誓词制作</view>
            <view class="steps flex-kai">
                <view class="step-no step-no-active">
                    <uni-icons v-if="have_user_image" type="checkmarkempty" color="EF464A" size="14"/>
                    <text v-else>1</text>
                </view>
                <view class="step-line" :class="{'step-line-active': have_user_image}"></view>
                <view class="step-no" :class="{'step-no-active': have_user_image}">
                    <uni-icons v-if="have_voice" type="checkmarkempty" color="EF464A" size="14"/>
                    <text v-else>2</text>
                </view>
                <view class="step-line" :class="{'step-line-active': have_voice}"></view>
                <view class="step-no" :class="{'step-no-active': have_voice}">3</view>
            </view>
            
            <view class="flex-kai">
                <view class="bottom-item flex-all-center">
                    <view class="bottom-item-container" @click="toEditImage">
                        <view class="icon flex-all-center" hover-class="navigator-hover">
                            <uni-icons type="image-filled"/>
                        </view>
                        <view class="font12 color-content">合成照片</view>
                    </view>
                </view>

                <view class="bottom-item flex-all-center">
                    <view class="bottom-item-container" @click="toRecord">
                        <view class="icon flex-all-center" hover-class="navigator-hover">
                            <uni-icons type="mic-filled"/>
                        </view>
                        <view class="font12 color-content">录制声音</view>
                    </view>
                </view>

                <view class="bottom-item flex-all-center">
                    <view class="bottom-item-container" @click="complete">
                        <view class="icon flex-all-center" hover-class="navigator-hover">
                            <uni-icons type="paperplane-filled"/>
                        </view>
                        <view class="font12 color-content">完成制作</view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
export default {
    data() {
        return {
            image: '',
            have_user_image: false,
            have_voice: ''
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.$login.uniLogin(() => {
            this.init()
        })
    },

    methods: {
        async init() {
            await this.getActiveDetails()
        },

        async getActiveDetails() {
            let details = app.globalData?.['activity_detail']
            if (!details || details.active_id !== this.active_id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.active_id
                    }
                })

                details = res.data.active_details
            }

            const bg_img = details?.conf?.active?.take_oath_bg_img || ''
            if (!bg_img) return this.$uni.showModal('活动未设置宣誓背景图', {success: () => uni.navigateBack()})
            this.bg_img = bg_img
            this.image ||= bg_img
        },

        toEditImage() {
            this.$uni.navigateTo('./edit-image', {
                success: res => {
                    res.eventChannel.emit('imageData', {
                        bg_img: this.bg_img,
                        user_image_data: this.user_image_data || null
                    })
                },
                events: {
                    setImage: path => {
                        this.isEditImage = true
                        this.image = path
                    },
                    saveUserImageData: data => {
                        this.have_user_image = true
                        this.user_image_data = data
                    }
                }
            })
        },

        toRecord() {
            if (!this.have_user_image) return this.$uni.showToast('请先合成照片')
            this.$uni.navigateTo(`./record?image=${this.image}`, {
                events: {
                    getVoice: voice => {
                        this.have_voice = true
                        this.voice_src = voice.tempFilePath
                        this.voice_size = voice.fileSize
                    }
                }
            })
        },


        async complete() {
            if (!this.have_user_image) return this.$uni.showToast('请先合成照片')
            if (!this.have_voice) return this.$uni.showToast('请先录制声音')
            
            this.$uni.showLoading('提交中...')
            
            const data = {
                active_id: this.active_id
            }
            
            const image_src = await this.getImageSrc()
            if (!image_src) {
                uni.hideLoading()
                return this.$uni.showToast('图标提交失败，请重试')
            }
            data.pic = image_src
            
            if (this.voice_src) {
                const voice_src = await this.getVoiceSrc()
                if (!voice_src) {
                    uni.hideLoading()
                    return this.$uni.showToast('音频提交失败，请重试')
                }
                data.audio = voice_src
            }
            
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.party_guess_pic.oath/submit_oath',
                data
            })
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '提交失败')
            
            this.$uni.showToast('已提交')
            
            this.getOpenerEventChannel().emit('reloadOathList')
            
            this.$uni.navigateBack(1, {delay: 1000})
        },

        async getImageSrc() {
            const {image, active_id, isEditImage} = this
            
            if (!isEditImage) return image

            return await this.xwy_api.uploadOneImage({
                temp_data: {
                    path: image
                },
                active_id
            })
        },
        
        async getVoiceSrc() {
            return await this.xwy_api.uploadAudio({
                tempFilePath: this.voice_src,
                fileSize: this.voice_size
            }, this.active_id)
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 130px;
}

.image {
    width: calc(100vw - 20px);
    height: calc(100vh - 180px);
    display: block;
}

.bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 10px 0;
    
    .steps {
        position: relative;
        top: 6px;
        padding: 0 calc(16vw - 8px);
        width: 100%;
        box-sizing: border-box;

        .step-no {
            font-size: 14px;
            width: 16px;
            height: 16px;
            line-height: 16px;
            text-align: center;
            border-radius: 50%;
            background-color: #bbbec4;
            color: #fff;
        }
        
        .step-line {
            width: 25vw;
            height: 1px;
            margin-top: 8px;
            background-color: #d7d6d6;
        }

        .step-no-active, .step-line-active {
            background-color: #EF464A;
        }
    }

    .bottom-item {
        width: 32vw;
        text-align: center;
        box-sizing: border-box;

        .bottom-item-container {
            padding: 10px;
        }

        .icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #EF464A;
            margin: 2px auto;

            .uni-icons {
                color: #fff !important;
                font-size: 28px !important;
            }
        }
    }
}
</style>