<template>
    <view class="page">
        <!-- <view class="top">
          <image class="top-image" mode="widthFix" src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fup.enterdesk.com%2Fedpic_source%2Fef%2F12%2F5c%2Fef125c60eb4a66fb0a6cdcb1d7f22689.jpg&refer=http%3A%2F%2Fup.enterdesk.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=1642325159&t=d724dec7ad44bb8e83d1625bc6df9a43"/>
          <view class="user-info flex-row">
            <view class="nickname pr10 color-white font14">{{nickname}}</view>
            <image class="avatar" :src="avatar"/>
          </view>
          <view class="to-publish color-white" hover-class="navigator-hover" @click="toPublish">
            <text class="iconfont icon-edit color-white"></text>
            <text class="pl5">发布动态</text>
          </view>
        </view> -->


        <view v-if="!isPublic" class="search bg-white flex-kai p10">
            <view class="input-view">
                <view class="search-icon left-icon flex-all-center">
                    <uni-icons type="search" size="20" color="#bbbec4"/>
                </view>
                <input
                    class="input bg-background"
                    type="text"
                    confirm-type="search"
                    v-model="search_keyword"
                    @confirm="search()"
                    placeholder="输入用户名字搜索"
                    placeholder-style="color:#bbbec4"
                />
                <view class="search-icon right-icon flex-all-center" @click="search_keyword = ''">
                    <uni-icons v-if="search_keyword" type="close" size="20" color="#bbbec4"/>
                </view>
            </view>
            <view class="search-go color-info pl10" @click="search">搜索</view>
        </view>


        <scroll-view v-if="category_list.length" class="category-bar" :scroll-x="true"
                     :scroll-left="categoryListScrollLeft" :scroll-with-animation="true">
            <view class="flex-row">
                <view class="category-item font14"
                      :class="category_id === 0 ? 'color-light-primary' : 'color-content'"
                      @click="categoryChange(0)">全部
                    <view v-show="category_id === 0" class="category-active"></view>
                </view>
                <view class="category-item font14"
                      :class="item.category_id === category_id ? 'color-light-primary' : 'color-content'"
                      v-for="item in category_list" :key="item.category_id"
                      @click="categoryChange(item.category_id)">
                    {{ item.name }}

                    <view v-show="item.category_id === category_id" class="category-active"></view>
                </view>
            </view>
        </scroll-view>

        <view v-if="total_count && !isPublic" class="flex-kai font14" style="border-bottom: 1px solid #eee;">
            <view class="p10 color-sub">共{{ total_count }}条动态</view>
            <view v-if="free_push && !not_publish" class="p10 color-primary"
                  hover-class="navigator-hover" @click="toPublish">
                <text>发布动态</text>
                <text class="iconfont icon-more font14 color-primary"></text>
            </view>
        </view>


        <view v-if="sport_moment_type === 2" class="p10">
            <custom-waterfalls-flow
                ref="waterfallsFlowRef"
                :value="list"
                :column="2"
                :columnSpace="1.5"
                imageKey="image"
                @imageClick="imageClick"
            ></custom-waterfalls-flow>
        </view>


        <view v-if="sport_moment_type === 1" class="list">
            <view v-for="(item, index) in list" :key="index">
                
                <view class="list-item flex-row" @click="toCommentDetail(item.id, item.view_id)">
                    <view class="headimg pr10">

                        <image :src="item.headimg" :lazy-load="true"/>
                    </view>
                    <view class="right">
                        <view class="flex-kai">
                            <view class="color-title">{{ item.nickname }}</view>
                            <view v-if="item['is_top']">
                                <uni-tag text="置顶" size="mini" type="warning"></uni-tag>
                            </view>
                        </view>
                        <view class="ptm5">
                            <text class="color-content" space="nbsp">{{ item.content }}</text>
                        </view>
                        <view
                            v-if="item.pic_list && item.pic_list.length"
                            class="img-list flex-row flex-wrap"
                            :class="{
                                'img-list-1': item.pic_list.length === 1,
                                'img-list-2': item.pic_list.length === 2,
                                'img-list-more': item.pic_list.length >= 3
                            }"
                        >

                            <image
                                class="img-item"
                                :lazy-load="true"
                                v-for="(img_item, img_index) in item.pic_list"
                                :key="img_index"
                                :src="img_item"
                                :mode="item.pic_list.length === 1 ? 'heightFix' : 'aspectFill'"
                                @click.stop="previewImage(img_item, item.pic_list)"
                            />
                        </view>
                        <view
                            v-if="item.location_address"
                            class="color-sub ptm5"
                            hover-class="navigator-hover"
                            @click.stop="openLocation(item.location_address, item.lat, item.lng)"
                        >
                            <text class="iconfont icon-location-cute font12"></text>
                            <text class="font12">{{ item.location_address }}</text>
                        </view>
                        <view class="color-sub font12">
                            <uni-dateformat :date="item.create_time" :threshold="[60000, 86400000]"/>
                        </view>
                        <view class="flex-kai pt10">
                            <view>
                                <template v-if="!item.checked">
                                    <uni-tag text="待审核" type="default" size="small" inverted/>
                                </template>
                            </view>
                            <view class="flex-row color-a font12">
                                <view class="pl10 pr10" hover-class="navigator-hover" 
                                      @click.stop="like(item.view_id, item)">
                                    <uni-icons type="hand-up" size="14" color="#72ace3"/>
                                    <text>{{ item.likes }}</text>
                                </view>
                                <view v-if="allow_reply" class="pl10 pr10" hover-class="navigator-hover" 
                                      @click.stop="toReply(item.id)">
                                    <text class="iconfont icon-chat-bubble font12"></text>
                                    <text style="padding-left: 2px;">评论</text>
                                </view>
                                <view v-if="is_admin" class="pl10 pr10" hover-class="navigator-hover" 
                                      @click.stop="setTop(index)">
                                    <text class="iconfont icon-to-top font12"></text>
                                    <text style="padding-left: 2px;">
                                        {{ item['is_top'] ? '取消' : '' }}置顶
                                    </text>
                                </view>
                                <view
                                    v-if="(item.userid === userid && user_delete) || is_admin"
                                    class="pl10"
                                    hover-class="navigator-hover"
                                    @click.stop="deleteParent(item.id)"
                                >
                                    <text class="iconfont icon-delete font12"></text>
                                    <text style="padding-left: 2px;">删除</text>
                                </view>
                            </view>
                        </view>
                        <view v-if="item.child_list && item.child_list.data && item.child_list.data.length" 
                              class="child-list">
                            <view class="child-item flex-row" 
                                  v-for="(child_item, child_index) in item.child_list.data" 
                                  :key="child_index">
                                <view class="child-headimg">

                                    <image :src="child_item.headimg" :lazy-load="true"/>
                                </view>
                                <view class="pl5" style="width: 100%;">
                                    <view class="color-title font14">{{ child_item.nickname || '' }}</view>
                                    <view class="color-content font14">{{ child_item.content }}</view>
                                    <view v-if="child_item.pic_list && child_item.pic_list.length" class="flex-row">
                                        <view class="child-img-item" v-for="(img_item, img_index) in child_item.pic_list" :key="img_index">

                                            <image class="child-img-item-image"
                                                   :src="img_item" mode="aspectFill"
                                                   @click.stop="previewImage(img_item, child_item.pic_list)"/>
                                        </view>
                                    </view>
                                    <view class="flex-kai color-sub font12">
                                        <view class="ptm5">
                                            <uni-dateformat :date="child_item.create_time" 
                                                            :threshold="[60000, 86400000]"/>
                                        </view>
                                        <view class="flex-row">
                                            <!-- <view class="pl10 ptm5" hover-class="navigator-hover" @click.stop="like(child_item.view_id, child_item)">
                                              <uni-icons type="hand-up" size="14" color="#72ace3"/>
                                              <text>{{child_item.likes}}</text>
                                            </view> -->
                                            <view
                                                class="pl10 ptm5"
                                                v-if="item.userid === userid || child_item.userid === userid || is_admin"
                                                hover-class="navigator-hover"
                                                @click.stop="deleteChild(item.id, child_item.id)"
                                            >删除
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>

                        </view>
                        <view v-if="item.child_list.total > 3" class="flex-kai pt5">
                            <view></view>
                            <view class="color-primary font12">
                                <text>查看更多评论</text>
                                <text class="iconfont icon-more font12 color-primary"></text>
                            </view>
                        </view>
                    </view>
                </view>

                <xwy-ad v-if="(index + 1) % 10 === 0" :ad_type="66" :activity_id="active_id"/>
            </view>
        </view>

        <template v-if="comment_id">
            <view class="comment-bg" @click="cancelComment"></view>
            <view class="comment-view">
                <view v-if="active_id">
                    <view class="img-view">
                        <view v-if="reply_img_list.length" class="flex-row">
                            <view class="reply-img-item" v-for="(item, index) in reply_img_list" :key="index">
                                <image class="reply-img-image" mode="aspectFill" :src="item"
                                       @click="previewImage(item, reply_img_list)"/>
                                <view class="remove-reply-img flex-all-center"
                                      @click="reply_img_list.splice(index, 1)">
                                    <uni-icons type="closeempty" color="#ffffff" size="14px"/>
                                </view>
                            </view>
                        </view>
                        <view v-else @click="addReplyImg">
                            <uni-icons type="image" color="#666666" size="16px"/>
                        </view>
                    </view>
                </view>
                <view class="flex-kai">

                    <textarea
                        class="comment-input"
                        v-model="content"
                        maxlength="200"
                        :focus="true"
                        :fixed="true"
                        :cursor-spacing="20"
                        :show-confirm-bar="false"
                        :auto-height="true"
                        :placeholder="'评论 ' + list.find(v=>v.id===comment_id).nickname"
                    />
                    <view class="flex-column flex-kai">
                        <view></view>
                        <view class="comment-btn" :class="{'color-success': content, 'color-sub': !content}"
                              hover-class="navigator-hover" @click="comment">评论
                        </view>
                    </view>
                </view>
            </view>
        </template>

        <view v-if="!loading && !list.length" class="text-center" style="padding-top: 15vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub font14">{{ sportMomentName }}暂无动态</view>
            <view v-if="free_push && !not_publish" class="flex-all-center p20">
                <view class="not-btn text-center bg-primary color-white" 
                      hover-class="navigator-hover" @click="toPublish">发布动态
                </view>
            </view>
        </view>

        <uni-load-more v-if="loading && load_page > 1" status="loading"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && list.length > 5"
            status="noMore"
            :contentText="{contentnomore: '我是有底线的'}"
        ></uni-load-more>
        <uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>

        <template v-if="isPublic">
            <!--公共运动圈页面底部留空间，避免发帖和我的两个悬浮按钮遮挡页面内容。-->
            <!--偷下懒，不想在page里面判断是不是公共运动圈了了，直接这里写了-->
            <view style="width: 100%; height: 200px;"></view>
            
            <view class="add-fixed-button text-center bg-light-primary" hover-class="navigator-hover" 
                  @click="toPublish">
                <uni-icons type="plusempty" color="#ffffff" size="24"/>
                <view class="button-text color-white font12">发帖</view>
            </view>
            <view v-if="!myself" class="my-list-fixed-button text-center bg-light-primary" 
                  hover-class="navigator-hover" @click="lookMyPublish">
                <uni-icons type="person-filled" color="#ffffff" size="24"/>
                <view class="button-text color-white font12">我的</view>
            </view>
        </template>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'

export default {
    data() {
        return {
            active_id: '',
            loading: true,
            list: [],
            load_page: 1,
            is_last_page: false,
            content: '',
            comment_id: null,
            userid: '',
            total_count: 0,
            sport_moment_type: 1,
            free_push: true,  // 是否允许用户自己发布运动圈，像鲜繁客户照片合成的，必须是合成照片后，用合成的照片来发运动圈
            user_delete: true,  // 是否允许用户删除自己的运动圈
            is_admin: false,  // 是否是管理员
            not_publish: false,
            myself: false,
            allow_reply: true,
            search_keyword: '',
            sportMomentName: '运动圈',
            reply_img_list: [],
            category_list: [],
            categoryListScrollLeft: 0,
            category_id: 0,
            sport_moment_types: null
        }
    },
    
    computed: {
        isPublic() {
            return !this.active_id  
        },
        
        types() {
            // 运动圈接口，类型 types   6   是运动圈    如果是发布动态圈的 则改为   17  动态圈（第二个运动圈功能）
            if (this.sport_moment_types) return this.sport_moment_types
            return this.active_id ? 6 : 14
        }  
    },    
    
    onLoad(e) {
        let navigationBarTitle = ''
        if (e.sport_moment_name) {
            this.sportMomentName = e.sport_moment_name
            navigationBarTitle = this.sportMomentName
        }
        if (e.sport_moment_types) this.sport_moment_types = Number(e.sport_moment_types)
        if (e.active_id) this.active_id = e.active_id
        if (e.point_id) this.point_id = Number(e.point_id)
        if (e.share_task) this.share_task = true
        if (e.not_publish) this.not_publish = true
        if (e.myself) {
            this.myself = true
            navigationBarTitle = `我的${this.sportMomentName}`
        }
        
        if (e.active_id || e.myself) uni.hideShareMenu(undefined)

        if (navigationBarTitle) this.$uni.setNavigationBarTitle(navigationBarTitle)
        
        this.$uni.showLoading()
        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.userid = app.globalData['userid']

            this.getActivityDetail()
        })
    },
    
    
    onPullDownRefresh() {
        this.reloadList().finally(() => uni.stopPullDownRefresh())
    },
    
    
    onReachBottom() {
        if (this.is_last_page || this.loading) return false
        this.getList()
    },
    
    
    methods: {
        async getActivityDetail() {
            if (!this.active_id) return await this.reloadList()
            
            let activity_detail = app.globalData['activity_detail']

            if (!activity_detail || activity_detail.active_id !== this.active_id) {
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {active_id: this.active_id}
                })

                activity_detail = res.data['active_details']

            }
            if (activity_detail?.userid === this.userid) this.is_admin = true

            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace rank_set.closed_reply_sport_moment */
            const rank_set = activity_detail?.rank_set
            if (rank_set) {
                if (rank_set.together_pic) {
                    // 开启了照片合成，不能私自发运动圈，不能删除自己发布的运动圈
                    this.free_push = false
                    this.user_delete = false
                }

                // OA关闭了运动圈评论功能
                if (rank_set.closed_reply_sport_moment) this.allow_reply = false

                if (rank_set['sportsMomentCategory']) await this.getCategoryList()
            }

            const {sport_moment_release_limit, sport_moment_user_cannot_delete} = activity_detail?.conf?.active || {}

            // 是否报名活动才能发布运动圈
            if (sport_moment_release_limit) {
                const user_details = await this.getUserDetails()
                if (!user_details?.checked) this.release_limit = true
            }

            // 活动设置了不允许用户删除已发布的运动圈
            if (sport_moment_user_cannot_delete) this.user_delete = false
            
            this.activity_details = activity_detail
            this.sport_moment_type = activity_detail?.conf?.active?.sport_moment_type || 1

            await this.reloadList()
        },

        async getCategoryList() {
            const res = await this.xwy_api.getCategoryList({
                types: 43,
                active_id: this.active_id,
                page: 1,
                perpage: 1000
            })

            const list = res?.data?.category_list?.data || []

            if (!list.length) return

            this.category_list = list.map(({category_id, name}) => ({category_id, name}))
        },

        categoryChange(category_id) {
            if (this.loading || category_id === this.category_id) return
            this.category_id = category_id

            // 设置scrollLeft让当前item居中
            const query = uni.createSelectorQuery().in(this)
            query.selectAll('.category-item').boundingClientRect()
            query.select('.category-bar').boundingClientRect()
            query.exec(res => {
                // 因为“全部”是在页面结构单独写的，不在this.category_list里面，所以下标要加上“全部”的1
                const current = this.category_list.findIndex(v => v.category_id === category_id) + 1

                const [items, {width: containerWidth}] = res
                const item = items[current]
                const itemOffsetLeft = items.slice(0, current).reduce((total, curr) => total + curr.width, 0)

                this.categoryListScrollLeft = itemOffsetLeft + item.width / 2 - containerWidth / 2
            })

            this.reloadList()
        },
        
        activeTimeCheck() {
            if (!this.active_id) return true
            const {begin_time, end_time} = this.activity_details
            const begin = begin_time * 1000
            const end = end_time * 1000
            const now = Date.now()
            if (begin > now) {
                this.$uni.showToast('活动未开始')
                return false
            }
            if (end < now) {
                this.$uni.showToast('活动已结束')
                return false
            }
            return true
        },

        async search() {
            // 直接用接口的nickname来搜的话，如果出现多个同名用户，只会返回一个用户的运动圈，所以这里通过活动用户列表来搜，再从活动用户列表里面拿到用户的userid来搜
            this.$uni.showLoading()
            const search_userid_list = await this.getSearchUseridList()
            uni.hideLoading()
            
            if (this.search_keyword && !search_userid_list.length) {
                this.list = []
                this.total_count = 0
                return this.$uni.showToast('没有找到该用户')
            }

            this.search_userid_list = search_userid_list
            await this.reloadList()
        },
        
        async getSearchUseridList() {
            if (!this.search_keyword) return []
            
            const user_list = await this.getUserList()
            return user_list.map(item => item.userid)
        },
        
        async getUserList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/active_attend_user_list',
                data: {
                    active_id: this.active_id,
                    truename: this.search_keyword,
                    page: 1,
                    perpage: 1000
                }
            })
            
            return res?.data?.user_list?.data || []
        },
        
        async reloadList() {
            this.load_page = 1
            await this.getList()
        },
        

        async getList() {
            this.loading = true
            if (this.load_page === 1) {
                this.$uni.showLoading()
                this.is_last_page = false
                this.total_count = 0
                this.list = []
            }

            const data = {
                page: this.load_page,
                perpage: 20,
                types: this.types,
                pid: this.active_id,
                get_child: 1,
                is_long_id: 1
            }
            if (this.myself) data.only_myself = 1
            if (this.search_userid_list?.length) data.userids = this.search_userid_list.join(',')
            if (this.category_id) data.category_id = this.category_id

            const res = await xwy_api.request({url: 'front.user.reply/reply_view_list', data})
            uni.hideLoading()
            this.loading = false


            if (!res?.data?.['view_list']?.data?.length) {
                this.is_last_page = true
                return
            }

            const view_list = res.data['view_list']
            const list = view_list.data || []
            list.forEach(v => {
                if (v['user_attend_details']?.must_submit?.[0]?.value) {
                    v.nickname = v['user_attend_details'].must_submit[0].value
                }
                
                // OA关闭了评论功能，不需要评论列表
                if (!this.allow_reply && v.child_list) delete v.child_list
                
                if (v.child_list?.data?.length) {
                    if (v.child_list.data.length > 3) v.child_list.data.length = 3
                    v.child_list.data.forEach(child => {
                        if (child['user_attend_details']?.must_submit?.[0]?.value) {
                            child.nickname = child['user_attend_details'].must_submit[0].value
                        }
                    })
                }
                let image = '1'
                if (v.pic_list?.[0]) image = v.pic_list[0]
                v.image = image
            })
            this.list = [...this.list, ...list]
            if (data.page === 1 && this.sport_moment_type === 2) this.$refs.waterfallsFlowRef.refresh()
            this.load_page++
            this.is_last_page = view_list.is_lastpage
            this.total_count = view_list.total
        },

        imageClick(data) {
            this.toCommentDetail(data.id, data.view_id)
        },

        toCommentDetail(id, view_id) {
            let url = 'detail?id=' + id + '&view_id=' + view_id + '&active_id=' + this.active_id
            if (this.user_delete) url += '&user_delete=1'
            if (this.allow_reply) url += '&allow_reply=1'
            if (this.release_limit) url += '&release_limit=1'
            if (this.share_task) url += '&share_task=1'
            if (this.point_id) url += `&point_id=${this.point_id}`
            this.$uni.navigateTo(url, {
                events: {
                    deleteParent: () => this.reloadList(),
                    updateChildList: id => {
                        if (this.sport_moment_type === 1 && this.allow_reply) this.getChildComment(id)
                    },
                    shareSuccess: () => this.getOpenerEventChannel().emit('success')
                }
            })
        },

        openLocation(name, lat, lng) {
            if (!lat || !lng) return false
            uni.openLocation({
                latitude: Number(lat),
                longitude: Number(lng),
                name,
                fail: err => {
                    console.log('地图打开失败', err)
                    uni.showModal({
                        title: '打开失败',
                        content: JSON.stringify(err),
                        showCancel: false
                    })
                }
            })
        },

        previewImage(src, list) {
            list ||= [src]
            this.$uni.previewImage({
                urls: list,
                current: src
            })
        },
        
        async toReply(id) {
            if (this.release_limit) return this.$uni.showModal('没有参与活动或报名未通过审核，无法评论。')
            if (!this.activeTimeCheck()) return

            this.content = ''
            this.reply_img_list = []
            
            this.comment_id = id  
        },

        async comment() {
            if (!this.content) return false

            this.loading = true

            this.$uni.showLoading('评论发布中...')

            const id = this.comment_id

            const data = {
                content: this.content,
                types: this.types,
                nickname: app.globalData['userinfo'].nickname,
                headimg: app.globalData['userinfo'].headimg,
                reply_id: id
            }
            if (this.active_id) data.pid = this.active_id
            if (this.reply_img_list.length) data.pic_list = JSON.stringify(this.reply_img_list)

            const res = await xwy_api.request({url: 'front.user.reply/submit_content', data})

            this.loading = false
            uni.hideLoading()
            if (!res.status) return this.$uni.showModal(res.info || '发布失败', {title: '发布失败'})

            this.cancelComment()
            this.$uni.showToast('评论成功', 'success')
            
            await this.getChildComment(id)
        },


        cancelComment() {
            this.comment_id = null
        },

        addReplyImg() {
            this.$uni.navigateTo(`/pages/other/image_upload_or_select?active_id=${this.active_id}`, {
                events: {
                    newImg: src => this.reply_img_list.push(src)
                }
            })
        },

        async deleteParent(id) {
            const res = await this.$uni.showModal('确定删除该动态？', {showCancel: true})
            if (!res?.confirm) return

            await this.deleteComment(id)
            await this.reloadList()
        },

        async deleteChild(parent_id, id) {
            const res = await this.$uni.showModal('确定删除该评论？', {showCancel: true})
            if (!res?.confirm) return

            await this.deleteComment(id)
            await this.getChildComment(parent_id)
        },

        async deleteComment(id) {
            this.loading = true
            this.$uni.showLoading('删除中...')

            const data = {ids: id}

            if (this.is_admin) {
                data.types = this.types
                data.active_id = this.active_id
            }

            const res = await xwy_api.request({url: 'front.user.reply/reply_del', data})
            this.loading = false
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '删除失败')

            this.$uni.showToast('删除成功', 'success')
        },


        async getChildComment(id) {
            const index = this.list.findIndex(v => v.id === id)
            if (index === -1) return await this.reloadList()
            
            const res = await xwy_api.request({
                url: 'front.user.reply/reply_view_list',
                data: {
                    page: 1,
                    perpage: 3,
                    types: this.types,
                    pid: this.active_id || '',
                    reply_id: id
                }
            })

            if (!res?.data?.['view_list']) {
                this.list[index].child_list.data = []
                return
            }

            this.list[index].child_list = res.data['view_list']
        },


        async setTop(index) {
            const item = this.list[index]
            const {is_top} = item
            const tips = is_top ? '取消置顶？' : '置顶'
            this.$uni.showLoading(`正在${tips}`)

            const res = await xwy_api.request({
                url: 'front.user.reply/reply_checked',
                data: {
                    active_id: this.active_id,
                    types: this.types,
                    act_types: is_top ? 2 : 1,
                    ids: item.id
                }
            })

            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res.info || `${tips}失败`)
            
            this.$uni.showToast(`已${tips}`)

            // is_top代表原来是置顶的，现在要取消置顶，取消置顶要刷新列表数据，因为不知道这条记录取消置顶以后要放到哪里去
            if (is_top) return await this.reloadList()

            // 置顶的话，把当前记录标记为置顶，并放到列表第一位
            item.is_top = 1
            this.list.splice(index, 1)
            this.list.unshift(item)
        },


        async like(view_id, item) {
            if (!this.activeTimeCheck()) return
            this.$uni.showLoading('点赞中...')
            const res = await xwy_api.request({
                url: 'front.user.reply/view_likes',
                data: {view_id}
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '点赞失败')

            item.likes++
            this.$uni.showToast('点赞成功', 'success')
        },
        
        
        async getUserDetails() {
            if (this.is_get_user_details) return this.user_details
            
            let user_details = app.globalData['activity_user_detail']
            if (!user_details) {
                const u_detail = await xwy_api.request({
                    url: 'front.flat.sport_step.user/user_attend_details',
                    data: {active_id: this.active_id}
                })
                user_details = u_detail?.data?.user_details
            }

            this.is_get_user_details = true
            this.user_details = user_details
            return user_details
        },

        async toPublish() {
            if (!this.activeTimeCheck()) return
            const navigateTo = url => {
                this.$uni.navigateTo(url, {
                    events: {
                        uploadList: () => this.reloadList()
                    }
                })
            }
            
            if (!this.active_id) return navigateTo('publish')
            
            this.$uni.showLoading()
           
            // 获取活动详情，活动设置的运动圈发设置
            const activity_detail = this.activity_details
            
            if (this.release_limit) {
                uni.hideLoading()
                return this.$uni.showModal('没有参与活动或报名未通过审核，无法评论。')
            }
            
            // 是否设置了达到多少里程才能发布动态
            const limit_list = activity_detail?.rank_set?.['limit_sport_moment'] && activity_detail?.conf?.active?.limit_sport_moment_set_list
            
            if (limit_list?.length) {
                // 是否开启了达到多少里程才能发布动态
                const OA_open = activity_detail?.rank_set?.['limit_sport_moment']
                if (OA_open && limit_list?.length) {
                    const user_details = await this.getUserDetails()
                    const is_checked = user_details.checked
                    
                    if (!is_checked) {
                        uni.hideLoading()
                        return this.$uni.showModal('没有参与活动或报名未通过审核，无法发布动态。')
                    }
                    
                    // 获取用户发布的动态累计数量
                    const u_count = await xwy_api.request({
                        url: 'front.user.reply/submit_view_count',
                        data: {active_id: this.active_id}
                    })

                    const all_count = u_count?.data?.count || 0
                    const exchange_kilo = user_details.exchange_kilo
                    const limit_sport_moment_set_list = activity_detail.conf.active.limit_sport_moment_set_list.sort((a, b) => a.distance - b.distance)
                    let have_count = 0
                    let next_distance = null
                    let next_count = null
                    if (exchange_kilo < Number(limit_sport_moment_set_list[0].distance)) {
                        next_distance = Number(limit_sport_moment_set_list[0].distance)
                        next_count = Number(limit_sport_moment_set_list[0].count)
                    } else {
                        limit_sport_moment_set_list.forEach((v, i) => {
                            if (exchange_kilo >= Number(v.distance)) {
                                have_count = Number(v.count)
                                if (limit_sport_moment_set_list[i + 1]) {
                                    next_distance = Number(limit_sport_moment_set_list[i + 1].distance)
                                    next_count = Number(limit_sport_moment_set_list[i + 1].count)
                                }
                            }
                        })
                    }

                    if (all_count >= have_count) {
                        uni.hideLoading()
                        let tips = "暂无动态发布次数"
                        if (next_distance && next_count && (next_count - all_count > 0)) {
                            const unit = activity_detail?.conf?.active?.kilo_unit || '里'
                            tips += `, 你当前已兑换${exchange_kilo}${unit}, 兑换到${next_distance}${unit}可增加${next_count - all_count}次发布次数。`
                        }
                        return this.$uni.showModal(tips)
                    }
                }
                
            }
            
            navigateTo(`publish?active_id=${this.active_id}&sport_moment_types=${this.types}`)

            uni.hideLoading()
        },

        lookMyPublish() {
            this.$uni.navigateTo(`./list?myself=1&sport_moment_types=${this.types}`)
        }
    },
    
    onShareAppMessage() {
        return {
            title: this.sportMomentName,
            path: `/${this.$uni.route()}`
        }
    },
}
</script>

<style scoped lang="scss">
.page {
    padding-bottom: 20px;
}

.search {
    box-sizing: border-box;
    height: 60px;
    line-height: 40px;
}

.search .input {
    height: 40px;
    line-height: 40px;
}

.input-view {
    position: relative;
    width: 100%;
}

.search-icon {
    position: absolute;
    width: 30px;
    height: 40px;
    top: 0;
}

.left-icon {
    left: 0;
}

.right-icon {
    right: 0;
}

.search .input {
    /* width: calc(100% - 80px); */
    padding: 0 30px;
    border-radius: 20px;
    box-sizing: border-box;
}

.search-go {
    width: 60px;
    min-width: 60px;
    text-align: center;
}


.category-bar {
    width: 100%;
    padding: 0 10px;
    box-sizing: border-box;

    .category-item {
        display: inline-block;
        white-space: nowrap;
        height: 34px;
        line-height: 34px;
        padding: 0 10px;
        box-sizing: border-box;
        position: relative;

        .category-active {
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 16px;
            margin-left: -8px;
            height: 2px;
            background-color: #2d8cf0;
        }
    }
}

/* #ifdef H5 */
.search-go {
    padding-right: 10px;
    box-sizing: border-box;
}

@media screen and (min-width: 500px) {
    .search {
        width: 500px;
    }
    .category-bar {
        width: 500px;
    }
}

/* #endif */

.not-btn {
    width: 200px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
}

.list-item {
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.list-item:nth-last-child(1) {
    border: none;
}

.headimg image {
    width: 40px;
    min-width: 40px;
    height: 40px;
    border-radius: 50%;
    display: block;
}

.right {
    width: calc(100% - 50px);
}

.img-list {
    position: relative;
    left: -5px;
    padding-top: 5px;
}

.img-item {
    display: block;
    margin-left: 5px;
    margin-bottom: 5px;
}

.img-list-1 .img-item {
    height: 150px;
    width: auto;
}

.img-list-2 .img-item {
    width: 120px;
    height: 150px;
}

.img-list-more .img-item {
    width: calc((100vw - 85px) / 3);
    height: calc((100vw - 85px) / 3);
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .img-list-more .img-item {
        width: calc((500px - 85px) / 3);
        height: calc((500px - 85px) / 3);
    }

    .comment-view {
        width: 500px;
        left: calc(((100vw - 500px) / 2) - 10px);
    }
}

/* #endif */

.child-list {
    background-color: #f7f7f7;
    border-radius: 5px;
    margin-top: 10px;
}

.child-item {
    padding: 10px;
    border-bottom: 1px solid #e4e4e4;
}

.child-img-item {
    padding: 5px;

    .child-img-item-image {
        width: 80px;
        height: 80px;
        border-radius: 5px;
        display: block;
    }
}

.child-item:nth-last-child(1) {
    border: none;
}

.child-headimg image {
    width: 30px;
    min-width: 30px;
    height: 30px;
    border-radius: 50%;
    display: block;
}

.comment-bg {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    z-index: 99;
    overflow: hidden;
}

.comment-view {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100vw;
    background-color: #fff;
    padding: 10px;
    box-sizing: border-box;
    z-index: 999;
    border-top: 1px solid #eee;
}

.reply-img-item {
    position: relative;
    padding: 5px;

    .reply-img-image {
        display: block;
        width: 60px;
        height: 60px;
        border-radius: 5px;
    }

    .remove-reply-img {
        position: absolute;
        right: 0;
        top: 0;
        width: 20px;
        height: 20px;
        border-radius: 10px;
        box-sizing: border-box;
        border: 1px solid #fff;
        background-color: rgba(0, 0, 0, .7);
    }
}

.comment-input {
    width: calc(100% - 100px);
    background-color: #f7f7f7;
    padding: 10px;
    border-radius: 5px;
}

.comment-btn {
    width: 70px;
    line-height: 40px;
    text-align: center;
    border-radius: 5px;
}


$fixed-button-size: 50px;
.add-fixed-button, .my-list-fixed-button {
    position: fixed;
    right: 15px;
    width: $fixed-button-size;
    height: $fixed-button-size;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 999;

    .button-text {
        transform: translateY(-3px);
    }
}

.add-fixed-button {
    bottom: calc(80px + 10px + #{$fixed-button-size});
}

.my-list-fixed-button {
    bottom: 80px;
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .img-list-more .img-item {
        width: calc((500px - 85px) / 3);
        height: calc((500px - 85px) / 3);
    }

    .comment-view {
        width: 500px;
        left: calc(((100vw - 500px) / 2) - 10px);
    }
}

/* #endif */
</style>
