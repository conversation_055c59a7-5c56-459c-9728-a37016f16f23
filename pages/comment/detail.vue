<template>
    <view class="page">
        <view v-if="fromTimeline && !loading">

            <image v-if="timeline_preview_image" :src="timeline_preview_image" mode="widthFix"
                   style="width: 100vw;"/>
            <view v-else class="te"></view>
        </view>

        <view v-if="detail" class="detail">
            <xwy-ad :ad_type="3" :activity_id="detail.pid"/>
            
            <view class="flex-row p10">
                <image class="p-headimg" :src="detail.headimg"/>
                <view class="pl10" style="width: 100%;">
                    <view class="color-title">{{ detail.nickname }}</view>
                    <view class="ptm5">
                        <text class="color-content" space="nbsp">{{ detail.content }}</text>
                    </view>
                    <view
                        v-if="detail.pic_list && detail.pic_list.length"
                        class="img-list flex-row flex-wrap"
                        :class="{
                            'img-list-1': detail.pic_list.length === 1,
                            'img-list-2': detail.pic_list.length === 2,
                            'img-list-more': detail.pic_list.length >= 3
                        }"
                    >

                        <image
                            class="img-item"
                            :lazy-load="true"
                            v-for="(img_item, img_index) in detail.pic_list"
                            :key="img_index"
                            :src="img_item"
                            :mode="detail.pic_list.length === 1 ? 'heightFix' : 'aspectFill'"
                            @click.stop="previewImage(img_item, detail.pic_list)"
                        />
                    </view>
                    <view v-if="detail.location_address" class="color-sub ptm5" 
                          hover-class="navigator-hover" @click.stop="openLocation">
                        <text class="iconfont icon-location-cute font12"></text>
                        <text class="font12">{{ detail.location_address }}</text>
                    </view>
                    <view class="flex-kai">
                        <view class="color-sub font12">
                            <uni-dateformat :date="detail.create_time" :threshold="[60000, 86400000]"/>
                        </view>
                        <view class="flex-row color-sub font12">
                            <view class="pl10 pr10" hover-class="navigator-hover" 
                                  @click.stop="like(detail.view_id, detail)">
                                <uni-icons type="hand-up" size="16" color="#72ace3"/>
                                <text class="color-a">{{ detail.likes }}</text>
                            </view>
                            <button v-if="share_task" class="pl10 pr10 button-none"
                                    style="color: #72ace3;" open-type="share">
                                <text class="iconfont icon-share font14"></text>
                                <text class="font12" style="padding-left: 2px;">分享</text>
                            </button>
                            <view v-if="(detail.userid === userid && user_delete) || is_admin" class="pl10" 
                                  hover-class="navigator-hover" @click="deleteParent">
                                <text class="iconfont icon-delete font14"></text>
                                <text style="padding-left: 2px;">删除</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <template v-if="allow_reply">
            <view class="p10 flex-kai">
                <view v-if="total_count" class="color-sub font14">共{{ total_count }}条评论</view>
            </view>

            <view class="list">
                <xwy-ad :ad_type="66" :activity_id="detail.pid"/>

                <view v-for="(item, index) in list" :key="index">
                    <view class="list-item flex-row">
                        <image class="item-headimg" :src="item.headimg"/>
                        <view class="right pl10" style='width: 100%;'>
                            <view class="color-title">{{ item.nickname }}</view>
                            <view class="color-content font14">{{ item.content }}</view>
                            <view v-if="item.pic_list && item.pic_list.length" class="flex-row">
                                <view class="child-img-item"
                                      v-for="(img_item, img_index) in item.pic_list" :key="img_index">

                                    <image class="child-img-item-image"
                                           :src="img_item" mode="aspectFill"
                                           @click="previewImage(img_item, item.pic_list)"/>
                                </view>
                            </view>
                            <view class="flex-kai pt5">
                                <view class="color-sub font12">
                                    <uni-dateformat :date="item.create_time" :threshold="[60000, 86400000]"/>
                                </view>
                                <view class="flex-row color-sub font12">

                                    <view class="pl10 pr10" hover-class="navigator-hover"
                                          @click.stop="like(item.view_id, item)">
                                        <uni-icons type="hand-up" size="14" color="#72ace3"/>
                                        <text class="color-a">{{ item.likes }}</text>
                                    </view>
                                    <view v-if="detail.userid === userid || item.userid === userid" class="pl5"
                                          hover-class="navigator-hover" @click="deleteChild(item.id)">删除
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>

                    <xwy-ad v-if="(index + 1) % 10 === 0" :ad_type="66" :activity_id="detail.pid"/>
                </view>
            </view>

            <view v-if="!release_limit" class="comment-view">
                <view v-if="active_id">
                    <view class="img-view">
                        <view v-if="reply_img_list.length" class="flex-row">
                            <view class="reply-img-item" v-for="(item, index) in reply_img_list" :key="index">
                                <image class="reply-img-image" mode="aspectFill" :src="item"
                                       @click="previewImage(item, reply_img_list)"/>
                                <view class="remove-reply-img flex-all-center"
                                      @click="reply_img_list.splice(index, 1)">
                                    <uni-icons type="closeempty" color="#ffffff" size="14px"/>
                                </view>
                            </view>
                        </view>
                        <view v-else @click="addReplyImg">
                            <uni-icons type="image" color="#666666" size="16px"/>
                        </view>
                    </view>
                </view>
                <view class="flex-kai">

                    <textarea
                        class="comment-input"
                        type="text"
                        v-model="content"
                        maxlength="-1"
                        :focus="true"
                        :fixed="true"
                        :cursor-spacing="20"
                        :show-confirm-bar="false"
                        :auto-height="true"
                        placeholder="写评论..."
                    />
                    <view class="flex-column flex-kai">
                        <view></view>
                        <view class="comment-btn" :class="{'color-success': content, 'color-sub': !content}"
                              hover-class="navigator-hover" @click="comment">评论
                        </view>
                    </view>
                </view>
            </view>

            <view v-if="!loading && !list.length" class="text-center p20">
                <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
                <view class="color-sub font14">暂无评论</view>
            </view>

            <uni-load-more v-if="loading" status="loading"></uni-load-more>
            <uni-load-more
                v-if="is_last_page && list.length > 3"
                status="noMore"
                :contentText="{contentnomore: '我是有底线的'}"
            ></uni-load-more>
            <uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>

        </template>

    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'

export default {
    data() {
        return {
            id: '',
            active_id: '',
            loading: true,
            detail: null,
            list: [],
            load_page: 1,
            is_last_page: false,
            content: '',
            userid: '',
            total_count: 0,
            user_delete: false,  // 是否允许用户删除自己的运动圈
            is_admin: false,  // 是否是管理员
            allow_reply: false,  // 是否允许回复
            release_limit: false,  // 活动是否开启了没有报名不能发布运动圈，并且用户没有报名

            // 是否可以转发，从转发运动圈任务进来的才能转发
            share_task: false,
            // 是不是朋友圈预览页
            fromTimeline: false,
            // 从朋友圈进入预览页显示的预览图
            timeline_preview_image: '',
            reply_img_list: []
        }
    },
    onLoad(e) {
        if (uni.getLaunchOptionsSync().scene === 1154) {
            this.fromTimeline = true
            return this.getSimpleDetail(e.active_id)
        }

        this.id = e.id
        this.active_id = e.active_id
        if (e.point_id) this.point_id = Number(e.point_id)
        if (e.user_delete) this.user_delete = true
        if (e.allow_reply) this.allow_reply = true
        if (e.release_limit) this.release_limit = true

        if (e.share_task) this.share_task = true
        if (!this.share_task) uni.hideShareMenu()

        this.$uni.showLoading('加载中...')
        login.uniLogin(err => {
            uni.hideLoading()
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.userid = app.globalData['userid']
            
            if (e.active_id) this.getActiveDetails()

            this.getDetail(e.view_id)
        })
    },

    // 因为无法监听转发事件，如果一执行onShareAppMessage就奖励的话，很容易被用户发现不用真实转发也能获得奖励。
    // 而转发成功或取消转发时，会触发onShow，所以在onShow后执行奖励
    onShow() {
        if (!this.inShare) return
        this.inShare = false
        this.shareReward()
    },

    onPullDownRefresh() {
        if (this.loading) return
        this.load_page = 1
        this.getList()
        uni.stopPullDownRefresh()
    },
    onReachBottom() {
        if (this.is_last_page || this.loading) return false
        this.getList()
    },

    onShareAppMessage() {
        this.inShare = true

        let path = `/pages/comment/detail?id=${this.id}&view_id=${this.detail.view_id}&active_id=${this.active_id}`
        if (this.user_delete) path += `&user_delete=1`
        if (this.allow_reply) path += `&allow_reply=1`
        if (this.release_limit) path += `&release_limit=1`

        return {
            title: this.detail.content || '我的动态',
            path,
            imageUrl: this.detail?.pic_list?.[0] || this.detail.headimg || ''
        }
    },

    onShareTimeline() {
        this.shareReward()

        return {
            title: this.detail.content || '我的动态',
            imageUrl: this.detail?.pic_list?.[0] || this.detail.headimg || ''
        }
    },

    methods: {
        async getSimpleDetail(id) {
            this.$uni.showLoading('数据加载中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details_simple',
                data: {
                    active_id: id
                }
            })
            uni.hideLoading()
            this.loading = false

            const image = res?.data?.active_details?.conf?.active?.['timeline_preview_image']
            if (image) this.timeline_preview_image = image
        },

        async getActiveDetails() {
            let details = app.globalData['activity_detail']
            if (!details || details.active_id !== this.active_id) {
                this.$uni.showLoading()
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.active_id
                    }
                })
                uni.hideLoading()
                details = res?.data?.active_details
            }
            if (!details) return

            if (details.userid === app.globalData['userid']) this.is_admin = true
            this.active_details = details
        },

        activeTimeCheck() {
            if (!this.active_id || !this.active_details) return true
            const {begin_time, end_time} = this.active_details
            const begin = begin_time * 1000
            const end = end_time * 1000
            const now = Date.now()
            if (begin > now) {
                this.$uni.showToast('活动未开始')
                return false
            }
            if (end < now) {
                this.$uni.showToast('活动已结束')
                return false
            }
            return true
        },

        async getDetail(view_id) {
            const res = await this.xwy_api.request({
                url: 'front.user.reply/reply_view_details',
                data: {view_id}
            })

            /** @param detail.user_attend_details */
            const detail = res?.data?.details
            if (detail) {
                if (detail.user_attend_details?.must_submit?.[0]?.value) {
                    detail.nickname = detail.user_attend_details.must_submit[0].value
                }
                this.detail = res.data.details
            }

            await this.getList()
        },


        async getList() {
            if (!this.allow_reply) return
            
            this.loading = true
            if (this.load_page === 1) {
                this.list = []
            }

            const data = {
                page: this.load_page,
                perpage: 10,
                types: this.detail.types,
                reply_id: this.id,
                pid: this.active_id
            }

            const res = await this.xwy_api.request({url: 'front.user.reply/reply_view_list', data})
            this.loading = false
            uni.hideLoading()


            if (!res?.data?.['view_list']?.data?.length) {
                this.is_last_page = true
                return
            }

            const view_list = res.data['view_list']
            const list = view_list.data
            /** @param list.user_attend_details */
            list.forEach(v => {
                if (v.user_attend_details?.must_submit?.[0]?.value) {
                    v.nickname = v.user_attend_details.must_submit[0].value
                }
            })
            this.list = [...this.list, ...list]
            this.load_page++
            this.is_last_page = view_list.is_lastpage
            this.total_count = Number(view_list.total)
        },

        async shareReward() {
            if (!this.share_task) return
            // 只有自己的运动圈转发才能获得奖励
            if (app.globalData['userid'] !== this.detail.userid) return

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify({
                        types: 19,
                        point_id: this.point_id,
                        result: 'success'
                    }))
                }
            })

            const num = res.data.num
            if (num) this.$uni.showToast(`奖励${num}积分`)
            this.getOpenerEventChannel().emit('shareSuccess')
        },

        openLocation() {
            if (!this.detail.lat || !this.detail.lng) return false
            uni.openLocation({
                latitude: Number(this.detail.lat),
                longitude: Number(this.detail.lng),
                name: this.detail.location_address,
                fail: err => {
                    console.log('地图打开失败', err)
                    this.$uni.showModal(JSON.stringify(err), {title: '打开失败'})
                }
            })
        },

        previewImage(src, list) {
            list = list || [src]
            uni.previewImage({
                urls: list,
                current: src
            })
        },

        async deleteParent() {
            const res = await this.$uni.showModal('确定删除该动态？', {showCancel: true})
           if (!res.confirm) return
                    
            await this.deleteComment(this.detail.id)

            const eventChannel = this.getOpenerEventChannel()
            eventChannel && eventChannel.emit && eventChannel.emit('deleteParent', this.detail.id)
            this.$uni.navigateBack(1, {delay: 1000})
        },

        async like(view_id, item) {
            if (!this.activeTimeCheck()) return
            this.$uni.showLoading('点赞中...')
            const res = await this.xwy_api.request({
                url: 'front.user.reply/view_likes',
                data: {view_id}
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '点赞失败')
            
            this.$uni.showToast('点赞成功', 'success')
            item.likes++
        },

        addReplyImg() {
            this.$uni.navigateTo(`/pages/other/image_upload_or_select?active_id=${this.active_id}`, {
                events: {
                    newImg: src => this.reply_img_list.push(src)
                }
            })
        },


        async comment() {
            if (!this.content) return
            if (!this.activeTimeCheck()) return
            
            const data = {
                content: this.content,
                types: this.detail.types,
                nickname: app.globalData['userinfo'].nickname,
                headimg: app.globalData['userinfo'].headimg,
                reply_id: this.id,
                pid: this.active_id
            }
            if (this.reply_img_list.length) data.pic_list = JSON.stringify(this.reply_img_list)

            this.loading = true
            this.$uni.showLoading('评论发布中...')
            const res = await this.xwy_api.request({url: 'front.user.reply/submit_content', data})
            this.loading = false
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info ||'发布失败', {title: '发布失败'})

            this.content = ''
            this.$uni.showToast('评论成功', 'success')

            this.load_page = 1
            await this.getList()

            this.updateLispPageChildList()
            
            // 评论发布成功滚动到第一条评论列表顶部
            this.$nextTick(() => uni.pageScrollTo({selector: '.list'}))
        },


        updateLispPageChildList() {

            // 更新运动圈评论数据
            const eventChannel = this.getOpenerEventChannel()
            eventChannel && eventChannel.emit && eventChannel.emit('updateChildList', this.id)
        },

        async deleteChild(id) {
            const res = await this.$uni.showModal('确定删除该评论？', {showCancel: true})
            if (!res.confirm) return
            
            await this.deleteComment(id)

            this.updateLispPageChildList()

            setTimeout(() => {
                this.load_page = 1
                this.getList()
            }, 1000)
        },

        async deleteComment(id) {
            this.loading = true
            this.$uni.showLoading('删除中...')
            const res = await this.xwy_api.request({
                url: 'front.user.reply/reply_del',
                data: {ids: id}
            })
            this.loading = false
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info  || '删除失败', {title: '删除失败'})

            this.$uni.showToast('删除成功', 'success')
        },
    }
}
</script>

<style scoped lang="scss">
.page {
    padding-bottom: 100px;
}

.detail {
    border-bottom: 1px solid #eee;
}

.p-headimg {
    width: 40px;
    min-width: 40px;
    height: 40px;
    border-radius: 50%;
}

.img-list {
    position: relative;
    left: -5px;
    padding-top: 5px;
}

.img-item {
    display: block;
    margin-left: 5px;
    margin-bottom: 5px;
}

.img-list-1 .img-item {
    height: 150px;
    width: auto;
}

.img-list-2 .img-item {
    width: 120px;
    height: 150px;
}

.img-list-more .img-item {
    width: calc((100vw - 85px) / 3);
    height: calc((100vw - 85px) / 3);
}


.list-item {
    padding: 15px 0;
    margin: 0 15px;
    border-bottom: 1px solid #eee;
}

.list-item:nth-last-child(1) {
    border: none;
}

.child-img-item {
    padding: 5px;

    .child-img-item-image {
        width: 80px;
        height: 80px;
        border-radius: 5px;
        display: block;
    }
}

.item-headimg {
    width: 30px;
    min-width: 30px;
    height: 30px;
    border-radius: 50%;
}

.comment-view {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100vw;
    background-color: #fff;
    padding: 10px;
    box-sizing: border-box;
    z-index: 999;
    border-top: 1px solid #eee;
}

.reply-img-item {
    position: relative;
    padding: 5px;

    .reply-img-image {
        display: block;
        width: 60px;
        height: 60px;
        border-radius: 5px;
    }

    .remove-reply-img {
        position: absolute;
        right: 0;
        top: 0;
        width: 20px;
        height: 20px;
        border-radius: 10px;
        box-sizing: border-box;
        border: 1px solid #fff;
        background-color: rgba(0, 0, 0, .7);
    }
}

.comment-input {
    width: calc(100% - 100px);
    background-color: #f7f7f7;
    padding: 10px;
    border-radius: 20px;
}

.comment-btn {
    width: 70px;
    line-height: 40px;
    text-align: center;
    border-radius: 5px;
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .img-list-more .img-item {
        width: calc((500px - 85px) / 3);
        height: calc((500px - 85px) / 3);
    }

    .comment-view {
        width: 500px;
        left: calc(((100vw - 500px) / 2) - 10px);
    }
}

/* #endif */
</style>
