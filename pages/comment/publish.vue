<template>
    <view class="page">
        <picker v-if="needCategory" class="p10" :range="category_list" range-key="name"
                @change="selectCategory">
            <view class="flex-kai p10" style="border-bottom: 1px solid #eee;">
                <view>
                    <text class="iconfont icon-dating color-sub"></text>
                    <text class="color-content pl10">{{ category.name || category.id || '请选择类型' }}</text>
                </view>
                <uni-icons type="forward" size="16" color="#eeeeee"/>
            </view>
        </picker>

        <view class="content">
            <textarea v-model="content" :placeholder="placeholder" maxlength="-1"/>
        </view>

        <xwy-ad :ad_type="66" :activity_id="active_id"></xwy-ad>

        <view class="p10">
            <view class="flex-kai p10" style="border-bottom: 1px solid #eee;" @click="getLocation()">
                <view>
                    <text class="iconfont icon-map color-sub"></text>
                    <text class="color-content pl10">{{ location_address || '所在位置' }}</text>
                </view>
                <uni-icons type="forward" size="16" color="#eeeeee"/>
            </view>
            <view v-if="location_address" class="flex-kai">
                <view></view>
                <view class="color-primary font14 p10" @click="clearLocation()">不显示位置</view>
            </view>
        </view>

        <view class="images p5">
            <view class="flex-kai p10 color-sub font14">
                <view>
                    <template v-if="min_pic">最少需要上传{{ min_pic }}张照片</template>
                </view>
                <view v-if="max_img_count > 1">
                    {{ img_list.length }}/{{ max_img_count }}
                </view>
            </view>
            <view class="img-list flex-row flex-wrap">
                <view class="img-item" v-for="(item, index) in img_list" :key="index">
                    <image :src="item" mode="aspectFill" @click="previewImage(item)"/>

                    <view class="del-item flex-all-center" @click="delImageItem(index)">
                        <uni-icons type="closeempty" size="16" color="#e20f04"/>
                    </view>
                </view>

                <view v-if="img_list.length < max_img_count" class="img-item flex-all-center bg-background" 
                      @click="addImage">
                    <uni-icons type="plusempty" size="50" color="#d5d5d5"/>
                </view>
            </view>
        </view>

        <view class="publish-btn-view bg-white">
            <view class="publish-btn text-center bg-primary color-white" hover-class="navigator-hover" 
                  @click="publish">发布
            </view>
        </view>


    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'


export default {
    data() {
        return {
            sport_moment_types: null,
            active_id: '',
            content: '',
            lat: '',
            lng: '',
            location_address: '',
            img_list: [],
            placeholder: '写点什么吧... (请勿输入表情)',
            needCategory: false,
            category_list: [],
            category: {
                id: null,
                name: ''
            },
            min_pic: 0
        }
    },

    computed: {
        types() {
            // 运动圈接口，类型 types   6   是运动圈    如果是发布动态圈的 则改为   17  动态圈（第二个运动圈功能）
            if (this.sport_moment_types) return this.sport_moment_types
            return this.active_id ? 6 : 14
        },
        
        max_img_count() {
            // 默认最多上传9张图片
            const default_max = 9

            if (!this.active_id) return default_max

            // 某些活动可以单独设置最大照片数
            const options = {
                // 'c472f109d8a048a27d8f22ba4046b9b3': 1, // 这个是我的活动
                '5209727001eb3864e2c9b9607e33681e': 1, // 鲜繁
                '38534a022d57fdc63f988549febe5892': 1, // 鲜繁
            }

            return options[this.active_id] || default_max
        }
    },

    onLoad(e) {
        if (e.active_id) this.active_id = e.active_id
        if (e.point_id) this.point_id = Number(e.point_id)
        if (e.sport_moment_types) this.sport_moment_types = Number(e.sport_moment_types)

        this.setMaxImgCount()
        
        this.$uni.showLoading()
        login.uniLogin(err => {
            uni.hideLoading()
            if (err && err.errMsg) return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            if (e.active_id) this.getActiveDetails()
        })
    },
    methods: {
        async getActiveDetails() {
            let details = app.globalData['activity_detail']
            if (!details || details.active_id !== this.active_id) {
                this.$uni.showLoading()
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.active_id
                    }
                })
                uni.hideLoading()
                details = res?.data?.active_details
            }
            if (!details) return
            
            const {begin_time, end_time} = details
            const begin = begin_time * 1000
            const end = end_time * 1000
            const now = Date.now()
            if (begin > now) return this.$uni.showModal('活动未开始', {success: () => uni.navigateBack()})
            if (end < now) return this.$uni.showModal('活动已结束', {success: () => uni.navigateBack()})

            if (details.conf?.active?.sport_moment_input_placeholder) {
                this.placeholder = details.conf.active.sport_moment_input_placeholder
            }

            if (details.conf?.active?.sport_moment_just_take_photo) this.just_take_photo = true
            if (details.conf?.active?.sport_moment_min_pic) {
                const min_pic = Math.floor(details.conf.active.sport_moment_min_pic)
                if (!isNaN(min_pic) && min_pic > 0) this.min_pic = min_pic
            }

            if (details.rank_set?.['sportsMomentCategory']) await this.getCategory()
        },

        async getCategory() {
            const res = await this.xwy_api.getCategoryList({
                types: 43,
                page: 1,
                perpage: 1000,
                active_id: this.active_id
            })

            const list = res?.data?.category_list?.data

            if (!list?.length) return

            this.category_list = list.map(({category_id, name}) => ({category_id, name}))
            this.needCategory = true
        },

        selectCategory(e) {
            const {category_id, name} = this.category_list[e.detail.value]
            this.category.id = category_id
            this.category.name = name
        },
        
        setMaxImgCount() {
            // 默认最多上传9张图片
            const default_max = 9
            
            // 某些活动可以单独设置最大照片数
            const options = {
                'c472f109d8a048a27d8f22ba4046b9b3': 1, // 这个是我的活动
                '5209727001eb3864e2c9b9607e33681e': 1, // 鲜繁
                '38534a022d57fdc63f988549febe5892': 1, // 鲜繁
            }
            
            this.max_img_count = options[this.active_id] || default_max
        },
        
        getLocation() {
            if (this.lat && this.lng && this.location_address) return

            this.$uni.showLoading('位置获取中...')
            uni.getLocation({
                type: 'gcj02',
                success: res => {
                    this.lat = res.latitude
                    this.lng = res.longitude
                    this.location2address()
                },
                fail: err => {
                    uni.hideLoading()
                    console.log(err)

                    if (err?.errMsg && err.errMsg === 'getLocation:fail auth deny') {
                        return this.$uni.showModal('位置获取失败，请授权获取地理位置', {
                            confirmText: '授权',
                            success: res => {
                                if (res.confirm) uni.openSetting()
                            }
                        })
                    }

                    this.getLocationError()
                }
            })
        },

        async location2address() {
            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace res.data.city_details */
            const res = await this.xwy_api.request({
                url: 'front.system.city/get_city_details_lat_lng',
                data: {
                    lat: this.lat,
                    lng: this.lng
                }
            })
            
            uni.hideLoading()
            
            const {province = '', city = '', area = '', street = '', building = ''} = res?.data?.city_details || {}

            this.location_address = [province, city === province ? '' : city, area, street, building].filter(Boolean).join('')
        },

        getLocationError() {
            uni.hideLoading()
            this.$uni.showToast('位置获取失败')
            this.clearLocation()
        },

        clearLocation() {
            this.lat = ''
            this.lng = ''
            this.location_address = ''
        },

        addImage() {
            if (this.just_take_photo) {
                this.takePhoto()
                return
            }

            let url = '/pages/other/image_upload_or_select'
            if (this.active_id) url += `?active_id=${this.active_id}`
            this.$uni.navigateTo(url, {
                events: {
                    newImg: src => this.img_list.push(src)
                }
            })
        },

        takePhoto() {
            uni.chooseImage({
                count: 1,
                sourceType: ['camera'],
                success: res => this.uploadPhoto(res.tempFiles[0])
            })
        },

        async uploadPhoto(photo) {
            this.$uni.showLoading('上传中...')
            const src = await this.xwy_api.uploadOneImage({
                active_id: this.active_id,
                temp_data: photo
            })
            uni.hideLoading()
            if (src) this.img_list.push(src)
        },

        delImageItem(index) {
            this.img_list.splice(index, 1)
        },

        previewImage(item) {
            uni.previewImage({
                urls: this.img_list,
                current: item
            })
        },

        async publish() {
            if (this.needCategory && !this.category.id) return this.$uni.showToast('请选择类型')

            if (!this.content) return this.$uni.showToast('请输入动态内容')

            // 过滤emoji表情后没有文字内容
            const content = this._utils.replaceEmoji(this.content)
            if (!content) return this.$uni.showToast('请勿输入表情')

            if (this.min_pic && this.img_list.length < this.min_pic) {
                return this.$uni.showToast(`请至少上传${this.min_pic}张图片`)
            }

            const data = {
                content: content,
                types: this.types,
                nickname: app.globalData['userinfo'].nickname,
                headimg: app.globalData['userinfo'].headimg,
                is_long_id: 1
            }
            if (this.location_address) data.location_address = this.location_address
            if (this.lat) data.lat = this.lat
            if (this.lng) data.lng = this.lng
            if (this.img_list.length) data.pic_list = JSON.stringify(this.img_list)
            if (this.active_id) data.pid = this.active_id
            if (this.point_id) data.bind_id = this.point_id
            if (this.category.id) data.category_id = this.category.id
            await this.publishAjax(data)
        },

        async publishAjax(data) {
            this.$uni.showLoading('发布中...')
            const res = await this.xwy_api.request({url: 'front.user.reply/submit_content', data});
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '发布失败', {title: '发布失败'})

            
            const eventChannel = this.getOpenerEventChannel()
            eventChannel?.emit?.('uploadList')
            eventChannel?.emit?.('success') // 任务闯关活动，运动圈发布后刷新活动状态(任务是否已完成，关卡是否已完成)

            this.back(res?.info || '发布成功')
        },

        back(info) {
            if (!this.active_id) return this.toMyList(info)
            this.$uni.showToast('发布成功', 'success')
            this.$uni.navigateBack(1, {delay: 1000})
        },

        toMyList(info) {
            this.$uni.showToast(info, info.length > 7 ? 'none' : 'success')
            
            const list_route = 'pages/comment/list'
            const jump = () => this.$uni.redirectTo(`/${list_route}?myself=1`, {delay: 1000})

            const pages = this.$uni.pages()
            if (pages.length < 2) return jump()

            const prev_page = pages[pages.length - 2]
            const {route, options} = prev_page
            if (route === list_route && options.myself) {
                this.$uni.navigateBack(1, {delay: 1000})
            } else {
                jump()
            }
        }
    }
}
</script>

<style scoped>
.page {
    padding-bottom: 80px;
}

.content {
    padding: 10px;
}

.content textarea {
    border-radius: 10px;
    padding: 10px;
    border: 1px solid #eee;
    width: 100%;
    box-sizing: border-box;
}

.img-item, .img-item image {
    width: calc((100vw - 40px) / 3);
    height: calc((100vw - 40px) / 3);
}

.img-item {
    margin: 5px;
    position: relative;
}

.del-item {
    position: absolute;
    top: 3px;
    right: 3px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, .5);
}

.publish-btn-view {
    position: fixed;
    left: 0;
    bottom: 0;
    padding: 15px;
    width: 100vw;
    box-sizing: border-box;
}

.publish-btn {
    line-height: 40px;
    border-radius: 20px;
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .publish-btn-view {
        width: 500px;
        left: calc(((100vw - 500px) / 2) - 10px);
    }

    .img-item, .img-item image {
        width: 150px;
        height: 150px;
    }
}

/* #endif */
</style>
