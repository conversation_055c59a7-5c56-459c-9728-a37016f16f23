<template>
    <view class="page">
        <view class="top-bar flex-all-center">
<!--            <view class="days-picker flex-kai" @click="daysTypeChange">
                <view class="flex-row">
                    <view>
                        <text class="iconfont icon-calendar-1 font18 color-light-primary"></text>
                    </view>
                    <view class="pl5 color-light-primary">{{ daysTitle }}</view>
                </view>
                <view>
                    <text class="iconfont icon-more color-light-primary"></text>
                </view>
            </view>-->
            
            <view class="types-bar flex-row">
                <view class="types-item" :class="{'types-active': item.value === types}"
                      v-for="item in types_options" :key="item.value" @click="typesChange(item.value)">
                    {{ item.title }}
                </view>
            </view>
        </view>
        
        <view class="days-type-bar flex-row">
            <view class="days-type-item" :class="{'days-type-active': item.type === days_type}"
                  v-for="item in days_options" :key="item.type" @click="changeDaysType(item.type)">
                {{ item.title }}
            </view>
        </view>

        <view v-if="charts_data" class="line-chart">
            <view class="p10 color-content">
                {{ daysTitle }}{{ typesName }}变化趋势
            </view>
            <health-line-chart :types="types" :charts-data="charts_data"/>
        </view>

        <cell-statistics v-if="cell_statistics" :value="cell_statistics"/>

        <view v-if="list_load" class="text-center" style="padding-top: 35vh;">
            <load-ani/>
        </view>
        
        <template v-if="login_success && !list_load">
            <xwy-ad :ad_type="3"/>
            <xwy-ad :ad_type="66"/>
        </template>
    </view>
</template>

<script>
import healthLineChart from './components/health-line-chart.vue'
import cellStatistics from "./components/cell-statistics.vue"

export default {
    components: {healthLineChart, cellStatistics},
    data() {
        return {
            login_success: false,
            list_load: true,
            types: 1,
            types_options: [
                {value: 1, title: '血压'},
                {value: 2, title: '血糖'}
            ],
            charts_data: null,
            cell_statistics: null,
            days_type: 'day7',
            days_options: [
                {title: '近7天', type: 'day7'},
                {title: '近30天', type: 'day30'},
                {title: '本月', type: 'current month'},
                {title: '上月', type: 'last month'}
            ]
        }
    },

    computed: {
        typesName() {
            return this.types_options.find(item => item.value === this.types)?.title || ''
        },
        
        daysTitle() {
            return this.days_options.find(item => item.type === this.days_type)?.title || ''
        }
    },

    onLoad(options) {
        this.types = Number(options.types)
        this.$login.uniLogin(() => {
            this.login_success = true
            this.getDays()
            this.getRecordList()
        })
    },


    methods: {
        /*async daysTypeChange() {
            const days_options = this.days_options.filter(item => item.type !== this.days_type)
            const res = await this.$uni.showActionSheet(days_options.map(item => item.title))
            if (res && res.hasOwnProperty('tapIndex')) {
                this.days_type = days_options[res.tapIndex].type
                
                this.getDays()
                await this.getRecordList()
            }
        },*/

        changeDaysType(type) {
            if (type === this.days_type) return
            this.days_type = type

            this.getDays()
            this.getRecordList()
        },
        
        getDays() {
            const type = this.days_type
            
            const day_list = []
            const categories = []
            
            if (type === 'day7' || type === 'day30' || type === 'current month') {
                let days = 7
                if (type === 'day30') days = 30
                if (type === 'current month') days = new Date().getDate()
                
                for (let i = days - 1; i >= 0; i--) {
                    const date = this._utils.getDay(-i, true)
                    day_list.push(date)
                    const {month, day} = this._utils.getYearMonthDay(date)
                    categories.push(`${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`)
                }

                this.day_list = day_list
                this.categories = categories
                
                return
            }
            
            const {year, month} = this._utils.getYearMonthDay()
            let last_year, last_month
            if (month === 1) {
                last_year = year - 1
                last_month = 12
            } else {
                last_year = year
                last_month = month - 1
            }
            const last_day = new Date(last_year, last_month, 0).getDate()

            for (let i = 1; i <= last_day; i++) {
                day_list.push(`${last_year}-${last_month}-${i.toString().padStart(2, '0')}`)
                categories.push(`${last_month.toString().padStart(2, '0')}-${i.toString().padStart(2, '0')}`)
            }

            this.day_list = day_list
            this.categories = categories
        },

        typesChange(value) {
            if (value === this.types) return

            this.types = value
            this.getRecordList()
        },

        async getRecordList() {
            this.list_load = true
            this.charts_data = null

            const data = {
                types: this.types,
                sort_types: 2,  //【1】按照血糖血压的记录时间 从早到晚排序，最早的排前面     【2】按照血糖血压的记录时间，最近的排前面 【3】按照id最近添加排前面
                page: 1,
                perpage: 1000,
                begin_time: this.day_list[0] + ' 00:00:00',
                end_time: this.day_list[this.day_list.length - 1] + ' 23:59:59'
            }

            const family_id = uni.getStorageSync('health-management-family-id') || ''
            if (family_id) data.family_id = family_id

            this.list_load = true
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.record.highBloodPressure/blood_recods_list',
                data
            })

            const list = res?.data?.list?.data || []
            this.setRecordData(list)
            this.list_load = false
        },

        setRecordData(list) {
            const data_arr = this.day_list.map(day => {
                const day_data = list.filter(item => item.record_time.slice(0, 10) === day)
                const data = this.types === 1
                    ? {high_num: null, low_num: null, heart_num: null}
                    : {hour_0: null, hour_1: null, hour_2: null}
                
                const {month, day: day_} = this._utils.getYearMonthDay(day)
                data.date = `${month.toString().padStart(2, '0')}-${day_.toString().padStart(2, '0')}`

                if (day_data.length) {
                    if (this.types === 1) {
                        data.high_num = Math.floor(day_data.reduce((acc, v) => acc + v.high_num, 0) / day_data.length)
                        data.low_num = Math.floor(day_data.reduce((acc, v) => acc + v.low_num, 0) / day_data.length)
                        data.heart_num = Math.floor(day_data.reduce((acc, v) => acc + v.heart_num, 0) / day_data.length)
                    } else {
                        [0, 1, 2].forEach(hour => {
                            const hour_arr = day_data.filter(item => item.time_hours === hour)
                            if (hour_arr.length) {
                                data[`hour_${hour}`] = Number((hour_arr.reduce((acc, v) => acc + v.sugar, 0) / hour_arr.length).toFixed(1))
                            }
                        })
                    }
                }

                return data
            })

            this.setCellStatisticsData(data_arr)
            this.setChartsData(data_arr)
        },

        setChartsData(data_arr) {
            let series = []

            if (this.types === 1) {
                series = [
                    {name: "高压值(平均)", lineType: "solid", data: []},
                    {name: "低压值(平均)", lineType: "solid", data: []},
                    {name: "心率值(平均)", lineType: "solid", data: []}
                ]
                data_arr.forEach(item => {
                    series[0].data.push(item.high_num)
                    series[1].data.push(item.low_num)
                    series[2].data.push(item.heart_num)
                })
            } else {
                series = [
                    {name: "空腹(平均)", lineType: "solid", data: []},
                    {name: "饭后1小时(平均)", lineType: "solid", data: []},
                    {name: "饭后2小时(平均)", lineType: "solid", data: []}
                ]
                data_arr.forEach(item => {
                    series[0].data.push(item.hour_0)
                    series[1].data.push(item.hour_1)
                    series[2].data.push(item.hour_2)
                })
            }

            this.charts_data = {
                categories: this.categories,
                series
            }
        },

        setCellStatisticsData(data_arr) {
            if (this.types === 1) {
                const have_list = data_arr.filter(item => item.high_num !== null && item.low_num !== null && item.heart_num !== null)

                let high_count = 0, low_count = 0, heart_count = 0
                let high_min = null, low_min = null, heart_min = null
                let high_min_date = null, low_min_date = null, heart_min_date = null
                let high_max = 0, low_max = 0, heart_max = 0
                let high_max_date = null, low_max_date = null, heart_max_date = null

                have_list.forEach(item => {
                    high_count += item.high_num
                    low_count += item.low_num
                    heart_count += item.heart_num
                    
                    if (high_min === null) {
                        high_min = item.high_num
                        high_min_date = item.date
                    }
                    if (item.high_num < high_min) {
                        high_min = item.high_num
                        high_min_date = item.date
                    }

                    if (high_max === null) {
                        high_max = item.high_num
                        high_max_date = item.date
                    }
                    if (item.high_num > high_max) {
                        high_max = item.high_num
                        high_max_date = item.date
                    }
                    
                    
                    if (low_min === null) {
                        low_min = item.low_num
                        low_min_date = item.date
                    }
                    if (item.low_num < low_min) {
                        low_min = item.low_num
                        low_min_date = item.date
                    }

                    if (low_max === null) {
                        low_max = item.low_num
                        low_max_date = item.date
                    }
                    if (item.low_num > low_max) {
                        low_max = item.low_num
                        low_max_date = item.date
                    }
                    
                    
                    if (heart_min === null) {
                        heart_min = item.heart_num
                        heart_min_date = item.date
                    }
                    if (item.heart_num < heart_min) {
                        heart_min = item.heart_num
                        heart_min_date = item.date
                    }

                    if (heart_max === null) {
                        heart_max = item.heart_num
                        heart_max_date = item.date
                    }
                    if (item.heart_num > heart_max) {
                        heart_max = item.heart_num
                        heart_max_date = item.date
                    }
                });

                this.cell_statistics = [
                    {
                        title: `${this.daysTitle}高压值统计`,
                        values: this.createStatsValues(high_count, have_list.length, high_min, high_max, 'mmHg', 'icon-line-chart', '#73C0DE', 'icon-to-top', '#FAC858', 'icon-to-down', '#91CB74', high_max_date, high_min_date)
                    },
                    {
                        title: `${this.daysTitle}低压值统计`,
                        values: this.createStatsValues(low_count, have_list.length, low_min, low_max, 'mmHg', 'icon-line-chart', '#73C0DE', 'icon-to-top', '#FAC858', 'icon-to-down', '#91CB74', low_max_date, low_min_date)
                    },
                    {
                        title: `${this.daysTitle}心率值统计`,
                        values: this.createStatsValues(heart_count, have_list.length, heart_min, heart_max, 'bpm', 'icon-line-chart', '#73C0DE', 'icon-to-top', '#FAC858', 'icon-to-down', '#91CB74', heart_max_date, heart_min_date)
                    }
                ];
            } else {
                const cell_statistics = []
                for (let hour = 0; hour <= 2; hour++) {
                    const have = data_arr.filter(item => item[`hour_${hour}`] !== null)
                    
                    let count = 0, min = null, max = null, min_date = null, max_date = null
                    
                    have.forEach(item => {
                        const value = item[`hour_${hour}`]
                        
                        count += value
                        
                        if (min === null) {
                            min = value
                            min_date = item.date
                        }
                        if (value < min) {
                            min = value
                            min_date = item.date
                        }
                        
                        if (max === null) {
                            max = value
                            max_date = item.date
                        }
                        if (value > max) {
                            max = value
                            max_date = item.date
                        }
                    })

                    cell_statistics.push({
                        title: this.daysTitle + (hour === 0 ? '空腹' : `饭后${hour}小时`) + '血糖值统计',
                        values: [
                            {
                                title: '平均',
                                value: Number((count / have.length).toFixed(1)),
                                unit: 'mmol/L',
                                icon: 'icon-line-chart',
                                color: '#73C0DE'
                            },
                            {
                                title: '最高',
                                value: max,
                                unit: 'mmol/L',
                                icon: 'icon-to-top',
                                color: '#FAC858',
                                date: max_date
                            },
                            {
                                title: '最低',
                                value: min,
                                unit: 'mmol/L',
                                icon: 'icon-to-down',
                                color: '#91CB74',
                                date: min_date
                            }
                        ]
                    })
                }
                this.cell_statistics = cell_statistics
            }
        },

        createStatsValues(count, length, min, max, unit, avgIcon, avgColor, maxIcon, maxColor, minIcon, minColor, maxDate, minDate) {
            return [
                {
                    title: '平均',
                    value: Math.floor(count / length),
                    unit: unit,
                    icon: avgIcon,
                    color: avgColor
                },
                {title: '最高', value: max, unit: unit, icon: maxIcon, color: maxColor, date: maxDate},
                {title: '最低', value: min, unit: unit, icon: minIcon, color: minColor, date: minDate}
            ]
        }
    }
}
</script>

<style lang="scss">
$page-bg-color: #F2F7FA;

.page {
    min-height: 100vh;
    padding-bottom: 20px;
    box-sizing: border-box;
    background-color: $page-bg-color;
}

.top-bar {
    padding: 10px;
    background-color: $page-bg-color;
    position: relative;
    
    /*.days-picker {
        position: absolute;
        top: 10px;
        left: 10px;
        height: 44px;
        line-height: 44px;
    }*/
    
    .types-bar {
        background-color: #EBEDF3;
        padding: 4px;
        border-radius: 22px;

        .types-item {
            width: 120px;
            line-height: 36px;
            border-radius: 18px;
            color: #747A86;
            text-align: center;
        }

        .types-active {
            background-color: #5cadff;
            color: #fff;
        }
    }
}

.days-type-bar {
    padding: 10px;

    .days-type-item {
        padding: 0 10px;
        border-radius: 5px;
        background-color: #EBEDF3;
        color: #747A86;
        line-height: 32px;
        margin-right: 10px;

        &:last-child {
            margin-right: 0;
        }
    }

    .days-type-active {
        background-color: #5cadff;
        color: #fff;
    }
}


.line-chart {

}
</style>