<template>
    <view class="page">
        <xwy-ad v-if="login_success" :ad_type="3"/>
        
        <view class="top-bar flex-kai">
            <view class="family-logo">
                <family-select v-if="login_success" :family-id.sync="family_id"
                               @familyIdUpdate="familyIdUpdate"/>
            </view>
            <view class="types-bar flex-row">
                <view class="types-item" :class="{'types-active': item.value === types}"
                      v-for="item in types_options" :key="item.value" @click="typesChange(item.value)">
                    {{ item.title }}
                </view>
            </view>
            <view class="statistics-7-day flex-all-center" @click="to7day">
                <text class="iconfont icon-line-chart color-light-primary font20"></text>
            </view>
        </view>

        <view class="list">
            <template v-for="(item, index) in record_list">
                <view class="item" :key="item.id" @longpress="longPress(item)">
                    <record-item :types="types" :data="item"/>
                </view>
                
                <xwy-ad v-if="(index + 1) % 10 === 0" :ad_type="66"/>
            </template>
        </view>

        <view v-if="list_load" class="text-center">
            <view v-if="current_page === 1" style="width: 100%; height: 35vh;"></view>
            <load-ani/>
        </view>
        
        <view v-if="!record_list.length && !list_load" class="no-record">
            <text class="iconfont icon-empty-state color-border"></text>
            <view class="color-sub font14">无{{ typesName }}记录</view>

            <view class="add-record-button" @click="addRecordPopupOpen">记录{{ typesName }}</view>
        </view>


        <view v-if="record_list.length" class="fixed-add-record-button">
            <view class="add-record-button" @click="addRecordPopupOpen">记录{{ typesName }}</view>
        </view>

        <select-picker ref="selectPicker" :types.sync="types" @confirm="addRecord"/>
    </view>
</template>

<script>
import recordItem from './components/record-item.vue'
import selectPicker from './components/select-picker.vue'
import familySelect from './components/family-select.vue'

export default {
    components: {recordItem, selectPicker, familySelect},
    data() {
        return {
            login_success: false,
            list_load: true,
            types: 1,
            types_options: [
                {value: 1, title: '血压'},
                {value: 2, title: '血糖'}
            ],
            record_list: [],
            current_page: 1,
            is_last_page: false,
            begin_time: '',
            end_time: '',
            family_id: uni.getStorageSync('health-management-family-id') || ''
        }
    },

    computed: {
        typesName() {
            return this.types_options.find(item => item.value === this.types)?.title || ''
        }
    },

    onLoad() {
        this.$login.uniLogin(() => {
            this.login_success = true
            this.init()
        })
    },

    onReachBottom() {
        if (!this.list_load && !this.is_last_page) {
            this.current_page++
            this.getRecordList()
        }
    },

    methods: {
        async init() {
            await this.reloadRecordList()
        },

        typesChange(value) {
            if (value === this.types) return

            this.types = value
            this.reloadRecordList()
        },

        familyIdUpdate(id) {
            this.family_id = id
            this.reloadRecordList()
        },

        async reloadRecordList() {
            this.current_page = 1
            this.is_last_page = false
            this.record_list = []
            await this.getRecordList()
        },

        async getRecordList() {
            const {types, current_page, begin_time, end_time, family_id} = this

            const data = {
                types,
                sort_types: 2,  //【1】按照血糖血压的记录时间 从早到晚排序，最早的排前面     【2】按照血糖血压的记录时间，最近的排前面 【3】按照id最近添加排前面
                page: current_page,
                perpage: 20
            }
            if (begin_time && end_time) {
                data.begin_time = begin_time
                data.end_time = end_time
            }
            if (family_id) data.family_id = family_id

            this.list_load = true
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.record.highBloodPressure/blood_recods_list',
                data
            })
            this.list_load = false

            const res_data = res?.data?.list
            if (!res_data) {
                this.is_last_page = true
                return
            }

            const list = this.initRecordList(res_data.data || [])
            this.record_list = this.record_list.concat(list)
            this.is_last_page = res_data.is_lastpage
        },

        initRecordList(list) {
            const types = this.types
            return list.map(item => {
                const data = {
                    id: item.id,
                    create_time: item.create_time,
                    record_time: item.record_time,
                    memo: item.memo,
                }
                if (types === 1) {
                    data.high_num = item.high_num
                    data.low_num = item.low_num
                    data.heart_num = item.heart_num
                    data.time_area = item.time_area
                } else if (types === 2) {
                    data.sugar = item.sugar
                    data.time_hours = item.time_hours
                }

                return data
            })
        },

        addRecordPopupOpen() {
            this.$refs.selectPicker.open()
        },
        
        async addRecord(value) {
            const data = {
                types: this.types,
                record_time: value.record_time
            }
            if (value.id) data.id = value.id
            if (this.family_id) data.family_id = this.family_id
            
            if (this.types === 1) {
                data.high_num = value.highPressure
                data.low_num = value.lowPressure
                data.heart_num = value.heartRate
            } else {
                data.sugar = value.bloodSugar
                data.time_hours = value.meal_hours
            }
            
            const tips = data.id ? '修改' : '添加'
            
            this.$uni.showLoading(`记录${tips}中...`)
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.record.highBloodPressure/submit_blood_record',
                data
            })
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || `记录${tips}失败`)
            
            this.$uni.showToast(`记录已${tips}`)
            await this.reloadRecordList()
        },

        async longPress(item) {
            const res = await this.$uni.showActionSheet(['修改记录', '删除记录'])

            if (res.tapIndex === 0) return this.toEditRecord(item)
            if (res.tapIndex === 1) return this.deleteRecord(item)
        },

        toEditRecord(item) {
            this.$refs.selectPicker.open(item)
        },
        
        async deleteRecord(item) {
            const confirm = await this.$uni.showModal(`确定删除${this.typesName}记录?`, {showCancel: true})
            if (!confirm?.confirm) return 
            
            this.$uni.showLoading('记录删除中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.record.highBloodPressure/del_blood_records',
                data: {
                    ids: item.id
                }
            })
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '记录删除失败')
            
            this.$uni.showToast('记录已删除')
            await this.reloadRecordList()
        },
        
        to7day() {
            this.$uni.navigateTo(`./statistics-7-day?types=${this.types}`)
        }
    }
}
</script>

<style lang="scss">
$page-bg-color: #F2F7FA;

.page {
    min-height: 100vh;
    padding-top: 65px;
    padding-bottom: 60px;
    box-sizing: border-box;
    background-color: $page-bg-color;
}

.top-bar {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 999;
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    background-color: $page-bg-color;

    .family-logo, .statistics-7-day {
        margin-top: 4px;
        width: 36px;
        height: 36px;
        border-radius: 50%;
    }
    
    .family-logo {
        margin-left: 5px;
    }
    
    .statistics-7-day {
        margin-right: 5px;
    }

    .types-bar {
        background-color: #EBEDF3;
        padding: 4px;
        border-radius: 22px;

        .types-item {
            width: 100px;
            line-height: 36px;
            border-radius: 18px;
            color: #747A86;
            text-align: center;
        }

        .types-active {
            background-color: #5cadff;
            color: #fff;
        }
    }
}

.list {
    .item {
        margin: 15px;
        padding: 15px;
        background-color: #fff;
        border-radius: 10px;
    }
}

.add-record-button {
    line-height: 44px;
    border-radius: 22px;
    text-align: center;
    background-color: #5cadff;
    color: #fff;
}

.no-record {
    padding-top: 15vh;
    text-align: center;

    .iconfont {
        font-size: 120px;
    }

    .add-record-button {
        margin: 30px auto;
        width: 200px;
    }
}

.fixed-add-record-button {
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 999;
    width: 100%;
    padding: 10px 10px 15px;
    box-sizing: border-box;
    background-color: $page-bg-color;
}
</style>