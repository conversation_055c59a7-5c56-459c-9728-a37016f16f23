import xwy_api from '@/utils/api/xwy_api'

export default {
    familyList: null,
    async getFamilyList(reload = false) {
        if (this.familyList && !reload) return this.familyList
        
        const res = await xwy_api.request({
            url: 'front.flat.active.user.family/family_list',
            data: {
                page: 1,
                perpage: 100
            }
        })
        if (res?.status !== 1) return
        
        const list = (res?.data?.list?.data || []).map(item => ({
            id: item.id,
            family_id: item.family_id,
            nickname: item.nickname || '',
            headimg: item.headimg || ''
        }))
        this.familyList = list
        return list
    },
    
    showFamilyId: null,
    async getShowFamily() {
        const familyList = await this.getFamilyList()
        if (this.showFamilyId) {
            const family = familyList.find(item => item.id === this.showFamilyId)
            if (family) return family
        }
        return familyList[0]
    },
    
    changeShowFamily(id) {
        this.showFamilyId = id
    }
}