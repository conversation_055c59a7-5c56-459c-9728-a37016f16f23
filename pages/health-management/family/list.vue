<template>
    <view class="page">
        
        <view class="family-list">
            <view class="family-item flex-row bg-white" v-for="item in family_list" :key="item.id">
                <view class="pr10">
                    <image v-if="item.headimg" class="logo-image" :src="item.headimg" mode="aspectFill"/>
                    <view v-else class="no-logo flex-all-center font34">
                        {{ item.nickname[0] }}
                    </view>
                </view>
                <view class="w-100">
                    <view class="nickname">{{ item.nickname }}</view>
                    <view class="clear clearfix">
                        <view class="fr flex-row">
                            <view class="controls-icon flex-all-center" @click="addFamily(item)">
                                <text class="iconfont icon-edit color-sub font18"></text>
                            </view>
                            <view class="controls-icon flex-all-center" @click="deleteFamily(item)">
                                <text class="iconfont icon-delete color-sub font18"></text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        
        <view v-if="!family_list.length && !loading" class="no-family text-center">
            <text class="iconfont icon-empty-state color-border"></text>
            <view class="color-sub font14">暂未添加家庭成员</view>

            <view class="add-button" @click="addFamily(null)">添加家庭成员</view>
        </view>


        <view v-if="family_list.length" class="fixed-add-button">
            <view class="add-button" @click="addFamily(null)">添加家庭成员</view>
        </view>
        
        <view v-if="loading" class="text-center" style="padding-top: 40vh;">
            <load-ani/>
        </view>
    </view>
</template>

<script>
import familyApi from "../api/family"

export default {
    data() {
        return {
            loading: true,
            family_list: []
        }
    },

    onLoad() {
        this.getFamilyList()
    },

    methods: {
        async getFamilyList(reload = false) {
            this.family_list = []
            this.loading = true
            this.family_list = await familyApi.getFamilyList(reload)
            this.loading = false
        },

        addFamily(item = null) {
            this.$uni.navigateTo('./add', {
                success: res => {
                    if (item) res.eventChannel.emit('familyInfo', item)  
                },
                events: {
                    familyListUpdate: () => {
                        this.familyListUpdate()
                    }
                }
            })
        },
        
        async deleteFamily(item) {
            const confirm = await this.$uni.showModal(`确定删除家庭成员【${item.nickname}】?`, {showCancel: true})
            if (!confirm.confirm) return
            
            this.$uni.showLoading('删除中...')
            const res = await this.xwy_api.deleteRecords(102, item.id)
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '删除失败')
            
            this.$uni.showToast('已删除')
            this.familyListUpdate()
            
            const storage_family_ld = uni.getStorageSync('health-management-family-id')
            if (storage_family_ld && storage_family_ld === item.family_id) {
                uni.removeStorageSync('health-management-family-id')
                this.getOpenerEventChannel().emit('storageFamilyDelete')
            }
        },

        familyListUpdate() {
            this.getFamilyList(true)
            this.getOpenerEventChannel().emit('familyListUpdate')
        }
    }
}
</script>

<style lang="scss">
$page-bg-color: #F2F7FA;

.page {
    min-height: 100vh;
    padding-bottom: 60px;
    box-sizing: border-box;
    background-color: $page-bg-color;
}

.add-button {
    line-height: 44px;
    border-radius: 22px;
    text-align: center;
    background-color: #5cadff;
    color: #fff;
}

.family-list {
    padding-top: 1px;
    
    .family-item {
        padding: 15px;
        margin: 15px;
        border-radius: 10px;
        
        .logo-image, .no-logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #5cadff;
            color: #fff;
        }

        .logo-image {
            display: block;
        }
        
        .controls-icon {
            width: 34px;
            height: 34px;
            margin-left: 10px;
        }
    }
}

.no-family {
    padding-top: 15vh;
    text-align: center;

    .iconfont {
        font-size: 120px;
    }

    .add-button {
        margin: 30px auto;
        width: 200px;
    }
}

.fixed-add-button {
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 97;
    width: 100%;
    padding: 10px 10px 15px;
    box-sizing: border-box;
    background-color: $page-bg-color;
}
</style>