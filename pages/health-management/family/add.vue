<template>
    <view>
        <view class="flex-all-center pt10">

            <image v-if="headimg" class="headimg" :src="headimg" mode="aspectFill" @click="changeHeadimg"/>
            <view v-else class="headimg flex-all-center bg-background" @click="changeHeadimg">
                <uni-icons type="image" size="60" color="#c6cbe0"/>
            </view>
        </view>
        <view class="flex-all-center">
            <view class="p10" @click="changeHeadimg" hover-class="navigator-hover">
                <text class="color-primary">更换头像</text>
                <uni-icons type="forward" color="#2d8cf0"/>
            </view>
        </view>

        <view class="form">

            <view class="form-item">
                <view class="color-title">成员称呼</view>
                <view class="bottom font16">
                    <input class="input" v-model="nickname"
                           placeholder="请输入家庭成员称呼,6字内,请勿使用表情符号"/>
                </view>
            </view>
        </view>

        <view class="save flex-all-center">

            <button :disabled="loading" class="save-btn text-center bg-primary color-white" @click="save">
                保存
            </button>
        </view>

    </view>
</template>

<script>
export default {
    data() {
        return {
            loading: false,
            nickname: '',
            headimg: ''
        }
    },

    onLoad() {
        this.getFamilyInfo()
    },

    methods: {
        getFamilyInfo() {
            this.getOpenerEventChannel().on('familyInfo', info => {
                if (info?.family_id) {
                    this.family_id = info.family_id
                    this.nickname = info.nickname || ''
                    this.headimg = info.headimg || ''
                }
            }) 
        },
        
        changeHeadimg() {
            uni.navigateTo({
                url: '/pages/other/image_upload_or_select',
                events: {
                    newImg: src => {
                        this.headimg = src
                    }
                }
            })
        },
        
        async save() {
            if (!this.nickname) return this.$uni.showToast('请输入成员称呼')
            if (this.nickname.length > 6) return this.$uni.showToast('成员称呼不能超过6个字')
            
            const data = {
                nickname: this.nickname,
                headimg: this.headimg
            }
            if (this.family_id) data.family_id = this.family_id

            this.$uni.showLoading('保存中...')
            this.loading = true
            const res = await this.xwy_api.request({
                url: 'front.flat.active.user.family/create_family',
                data
            })
            this.loading = false
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '保存失败')
            
            this.$uni.showToast('保存成功', 'success')
            
            this.getOpenerEventChannel().emit('familyListUpdate')
            this.$uni.navigateBack(1, {delay: 1000})
        }
    }
}
</script>

<style lang="scss">
.headimg {
    width: 100px;
    height: 100px;
    border-radius: 50%;
}

.form {
    padding: 10px 0;

    .form-item {
        padding: 10px;

        .bottom {
            border-bottom: 1px solid #eee;

            .input {
                width: 100%;
                line-height: 40px;
                height: 40px;
            }
        }
    }
}


.save {
    padding: 30px 0;
    
    .save-btn {
        width: 200px;
        line-height: 40px;
        border-radius: 20px;
        
        &::after {
            border: none;
        }
    }
}
</style>