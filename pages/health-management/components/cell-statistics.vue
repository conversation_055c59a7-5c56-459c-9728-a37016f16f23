<template>
    <view>
        <view class="cell-statistics" v-for="item in value" :key="item.title">
            <view class="month-title color-content">{{ item.title }}</view>
            <view class="cell-list flex-row flex-wrap">
                <view class="cell-item" v-for="cell in item.values" :key="cell.title">
                    <view class="cell-title">
                        <text :class="'iconfont font14 ' + cell.icon" :style="{color: cell.color}"></text>
                        <text>{{ cell.title }}</text>
                        <text v-if="cell.date" class="font12" style="padding-left: 2px;">
                            ({{ cell.date }})
                        </text>
                    </view>
                    <view>
                        <text class="value">{{ cell.value || 0 }}</text>
                        <text class="unit">{{ cell.unit }}</text>
                    </view>
                </view>
            </view>
        </view>

    </view>
</template>

<script>
export default {
    name: "cell-statistics",
    props: {
        value: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {}
    },

    mounted() {

    },

    methods: {}
}
</script>

<style lang="scss">
.cell-statistics {
    padding: 20px 0 10px;

    .month-title {
        padding-left: 24rpx;
    }

    .cell-list {
        margin: 0 12rpx;

        .cell-item {
            width: 218rpx;
            padding: 10px;
            margin: 12rpx;
            box-sizing: border-box;
            border-radius: 5px;
            background-color: #fff;

            .cell-title {
                font-size: 14px;
                color: #80848f;
                line-height: 24px;
                padding-bottom: 10px;

                .iconfont {
                    font-size: 18px;
                    padding-right: 2px;
                    position: relative;
                    top: 1px;
                }

                .remark {
                    font-size: 12px;
                    padding-left: 2px;
                    color: #80848f;
                }
            }

            .value {
                font-size: 18px;
                font-weight: 600;
            }

            .unit {
                font-size: 12px;
                color: #80848f;
                padding-left: 2px;
            }
        }
    }
}
</style>