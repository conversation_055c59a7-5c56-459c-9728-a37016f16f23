<template>
    <view @touchmove.stop.prevent="">
        <view class="container" :class="{'show': show === 'show', 'hide': show === 'hide'}"
              @click="show = 'hide'">
            <view class="popup bg-white" @click.stop="">
                <view class="close p10" @click="show = 'hide'">
                    <uni-icons type="closeempty" size="18" color="#80848f"/>
                </view>
                <view class="text-center pb10">添加记录</view>

                <uni-datetime-picker v-model="record_time">
                    <view class="pt10 pb10 flex-row">
                        <view>测量时间: {{ recodeTimeText }}</view>
                        <uni-icons type="forward" size="16" color="#80848f"/>
                    </view>
                </uni-datetime-picker>
                
                <view v-if="types === 2">
                    <view class="meal-list flex-row">
                        <view class="meal-item" :class="{'meal-active': index === meal_hours}"
                              v-for="(item, index) in 3" :key="item" @click="meal_hours = index">
                            <template v-if="index === 0">空腹</template>
                            <template v-else>饭后{{ index }}小时</template>
                        </view>
                    </view>
                </view>
                
                <view class="picker-container">
                    <view class="flex-kai">
                        <template v-if="types === 1">
                            <view>
                                <view class="text-center color-content font14">
                                    <view>高压值</view>
                                    <view>(mmHg)</view>
                                </view>
                                <!-- 高压值选择器 -->
                                <scroll-view scroll-y="true" class="scroll-picker"
                                             :scroll-top="highPressureScrollTop"
                                             scroll-with-animation="true"
                                             @scroll="onScroll($event, 'highPressure')"
                                             @touchend="onTouchEnd('highPressure')">
                                    <view class="spacer"></view>
                                    <view v-for="(item, index) in highPressureValues" :key="index"
                                          class="picker-item"
                                          :class="{ 'selected': index === highPressureIndex, 'selected-next-door': index === highPressureIndex - 1 || index === highPressureIndex + 1 }">
                                        {{ item }}
                                    </view>
                                    <view class="spacer"></view>
                                </scroll-view>
                            </view>

                            <view>
                                <view class="text-center color-content font14">
                                    <view>低压值</view>
                                    <view>(mmHg)</view>
                                </view>
                                <!-- 低压值选择器 -->
                                <scroll-view scroll-y="true" class="scroll-picker"
                                             :scroll-top="lowPressureScrollTop"
                                             scroll-with-animation="true"
                                             @scroll="onScroll($event, 'lowPressure')"
                                             @touchend="onTouchEnd('lowPressure')">
                                    <view class="spacer"></view>
                                    <view v-for="(item, index) in lowPressureValues" :key="index"
                                          class="picker-item"
                                          :class="{ 'selected': index === lowPressureIndex, 'selected-next-door': index === lowPressureIndex - 1 || index === lowPressureIndex + 1 }">
                                        {{ item }}
                                    </view>
                                    <view class="spacer"></view>
                                </scroll-view>
                            </view>

                            <view>
                                <view class="text-center color-content font14">
                                    <view>心率值</view>
                                    <view>(bpm)</view>
                                </view>
                                <!-- 心率值选择器 -->
                                <scroll-view scroll-y="true" class="scroll-picker"
                                             :scroll-top="heartRateScrollTop"
                                             scroll-with-animation="true" @scroll="onScroll($event, 'heartRate')"
                                             @touchend="onTouchEnd('heartRate')">
                                    <view class="spacer"></view>
                                    <view v-for="(item, index) in heartRateValues" :key="index"
                                          class="picker-item"
                                          :class="{ 'selected': index === heartRateIndex, 'selected-next-door': index === heartRateIndex - 1 || index === heartRateIndex + 1 }">
                                        {{ item }}
                                    </view>
                                    <view class="spacer"></view>
                                </scroll-view>
                            </view>
                        </template>
                        <template v-else>
                            <view></view>
                            <view>
                                <view class="text-center color-content font14">
                                    <view>血糖</view>
                                    <view>(mmol/L)</view>
                                </view>
                                
                                <scroll-view scroll-y="true" class="scroll-picker"
                                             :scroll-top="bloodSugarScrollTop"
                                             scroll-with-animation="true" @scroll="onScroll($event, 'bloodSugar')"
                                             @touchend="onTouchEnd('bloodSugar')">
                                    <view class="spacer"></view>
                                    <view v-for="(item, index) in bloodSugarValues" :key="index"
                                          class="picker-item"
                                          :class="{ 'selected': index === bloodSugarIndex, 'selected-next-door': index === bloodSugarIndex - 1 || index === bloodSugarIndex + 1 }">
                                        {{ item }}
                                    </view>
                                    <view class="spacer"></view>
                                </scroll-view>
                            </view>
                            <view></view>
                        </template>
                    </view>
                    <view class="text-center font14 color-sub p10">可上下滑动选择数值</view>
                </view>
                <view class="bottom-button text-center bg-primary color-white" @click="confirm">
                    {{ id ? '确定修改' : '立即添加' }}
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const valueHeight = 60
const getNumArr = (min, max, decimal = false) => {
    const arr = []
    for (let i = min, len = max; i <= len; i++) {
        arr.push(decimal ? (i / 10).toFixed(1) : i)
    }
    return arr
}

export default {
    name: "select-picker",
    emits: ['confirm'],
    props: {
        types: {
            type: Number,
            default: 1
        }
    },
    data() {
        return {
            show: null,
            highPressureValues: getNumArr(1, 300),
            lowPressureValues: getNumArr(1, 300),
            heartRateValues: getNumArr(1, 200),
            highPressureIndex: 119, // 默认值
            lowPressureIndex: 88, // 默认值
            heartRateIndex: 69, // 默认值
            scrollTimeout: null, // 用于判断滚动结束
            highPressureScrollTop: 0,
            lowPressureScrollTop: 0,
            heartRateScrollTop: 0,
            record_time: '',

            bloodSugarValues: getNumArr(0, 333, true),
            bloodSugarIndex: 75, // 默认值
            bloodSugarScrollTop: 0,
            meal_hours: 0,
            
            id: null
        }
    },
    
    computed: {
        recodeTimeText() {
            if (!this.record_time) return ''
            const {year, month, day} = this._utils.getYearMonthDay(this.record_time.slice(0, 10))
            const time = this.record_time.slice(11)
            return `${year}年${month}月${day}日 ${time}`
        }  
    },

    mounted() {
        this.scrollToCenterAll()
    },

    methods: {
        scrollToCenterAll() {
            this.scrollToCenter('highPressure')
            this.scrollToCenter('lowPressure')
            this.scrollToCenter('heartRate')
            this.scrollToCenter('bloodSugar')
        },
        
        open(data = null) {
            this.id = null
            
            let recordTime = this._utils.unitTimeToDate(new Date().getTime(), true)
            if (data) {
                const {id, record_time, sugar, time_hours, high_num, low_num, heart_num} = data
                if (id) this.id = id
                if (record_time) recordTime = record_time
                if (sugar) {
                    const index = this.bloodSugarValues.indexOf(sugar.toFixed(1))
                    if (index > -1) this.bloodSugarIndex = index
                }
                if (time_hours || time_hours === 0) this.meal_hours = time_hours
                if (high_num) {
                    const index = this.highPressureValues.indexOf(high_num)
                    if (index > -1) this.highPressureIndex = index
                }
                if (low_num) {
                    const index = this.lowPressureValues.indexOf(low_num)
                    if (index > -1) this.lowPressureIndex = index
                }
                if (heart_num) {
                    const index = this.heartRateValues.indexOf(heart_num)
                    if (index > -1) this.heartRateIndex = index
                }

                
                this.scrollToCenterAll()
            }

            this.record_time = recordTime
            this.show = 'show'
        },

        // 滚动事件：记录滚动的实时位置
        onScroll(e, type) {
            const index = Math.round(e.detail.scrollTop / valueHeight)
            if (type === 'highPressure') this.highPressureIndex = index
            else if (type === 'lowPressure') this.lowPressureIndex = index
            else if (type === 'heartRate') this.heartRateIndex = index
            else if (type === 'bloodSugar') this.bloodSugarIndex = index

            // uni.vibrateShort(undefined)

            /*clearTimeout(this.scrollTimeout); // 清除之前的计时器
            this.scrollTimeout = setTimeout(() => {
                this.scrollToCenter(type); // 滚动结束后，触发居中操作
            }, 300); // 300ms 内没有新的滚动则认为停止*/
        },


        // 触摸结束事件，手动触发滚动结束
        onTouchEnd(type) {
            clearTimeout(this.scrollTimeout) // 清除之前的计时器
            this.scrollTimeout = setTimeout(() => {
                this.scrollToCenter(type) // 滚动结束后，触发居中操作
            }, 1000) // 1000ms 内没有新的滚动则认为停止
        },

        // 滚动到当前选择项的中间位置
        scrollToCenter(type) {
            if (type === 'highPressure') this.highPressureScrollTop = this.highPressureIndex * valueHeight
            else if (type === 'lowPressure') this.lowPressureScrollTop = this.lowPressureIndex * valueHeight
            else if (type === 'heartRate') this.heartRateScrollTop = this.heartRateIndex * valueHeight
            else if (type === 'bloodSugar') this.bloodSugarScrollTop = this.bloodSugarIndex * valueHeight
        },

        confirm() {
            const value = {
                record_time: this.record_time
            }
            if (this.id) value.id = this.id
            
            if (this.types === 1) {
                value.highPressure = this.highPressureValues[this.highPressureIndex]
                value.lowPressure = this.lowPressureValues[this.lowPressureIndex]
                value.heartRate = this.heartRateValues[this.heartRateIndex]
            } else {
                value.bloodSugar = this.bloodSugarValues[this.bloodSugarIndex]
                value.meal_hours = this.meal_hours
            }
            
            this.$emit('confirm', value)

            this.show = 'hide'
        }
    }
}
</script>

<style lang="scss" scoped>
.container {
    position: fixed;
    z-index: 9999;
    bottom: -100vh;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, .7);
    display: flex;
    flex-direction: column-reverse;
}

.show {
    animation: show .3s forwards;
}

@keyframes show {
    from {
        bottom: -100vh;
    }
    to {
        bottom: 0;
    }
}

.hide {
    animation: hide .3s forwards;
}

@keyframes hide {
    from {
        bottom: 0;
    }
    to {
        bottom: -100vh;
    }
}

.popup {
    padding: 10px;
    border-radius: 10px 10px 0 0;
    position: relative;
    
    .close {
        position: absolute;
        top: 0;
        right: 0;
    }
}
    
.meal-list {
    padding-bottom: 10px;
    
    .meal-item {
        font-size: 14px;
        color: #7E8F9B;
        line-height: 36px;
        padding: 0 10px;
        border-radius: 5px;
        white-space: nowrap;
        background-color: #ECF0F3;
        margin-right: 10px;
        
        &:last-child {
            margin-right: 0;
        }
    }
    
    .meal-active {
        background-color: #E6F4FF;
        color: #0080FF;
    }
}

$value-height: 60px;

.picker-container {
    background: linear-gradient(to bottom, #EEF7FF 0%, #fff 30%, #fff 80%, #EEF7FF 100%);
    border-radius: 5px;
    padding-top: 10px;
}

.scroll-picker {
    width: 100px;
    height: $value-height * 5; /* 控制可视区域的高度 */
    overflow: hidden;
    text-align: center;
    border-radius: 10px;

    & ::v-deep ::-webkit-scrollbar {
        display: none;
        width: 0;
        height: 0;
        color: transparent;
    }
}

.picker-item {
    height: $value-height; /* 每个选项的高度 */
    line-height: $value-height;
    font-size: 24px;
    color: #999;
}

.selected-next-door {
    font-size: 28px;
    font-weight: bold;
    color: #555;
}

.selected {
    font-size: 34px;
    color: #000;
    font-weight: bold;
}

.spacer {
    height: $value-height * 2; /* 用于让中间选项居中的间隔 */
}

.bottom-button {
    line-height: 44px;
    border-radius: 5px;
    margin: 10px 0;
}
</style>