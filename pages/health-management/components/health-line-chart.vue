<template>
    <view>
        <view class="charts-box">
            <qiun-data-charts type="line" :background="'#F2F7FA'" :ontouch="true" :opts="opts"
                              :chartData="chartsData" :disableScroll="true"/>
        </view>
    </view>
</template>

<script>
export default {
    name: "health-line-chart",
    props: {
        types: {
            type: Number,
            default: 1
        },
        chartsData: {
            type: Object,
            default: () => ({categories: []})
        }
    },
    data() {
        return {
            opts: {
                color: ["#FAC858", "#91CB74", "#73C0DE"],
                background: 'rgba(242,247,250, 1)',
                padding: [15, 10, 0, 15],
                enableScroll: this.chartsData.categories.length > 7,
                pixelRatio: uni.getSystemInfoSync().pixelRatio || 1,
                legend: {},
                xAxis: {
                    disableGrid: true,
                    itemCount: 7,
                    scrollShow: true,
                    scrollAlign: 'right'
                },
                yAxis: {
                    // disableGrid: true,
                    splitNumber: 4,
                    gridType: 'dash',
                    gridColor: '#e7e6e6',
                    /*data: [
                        {
                            min: 0,
                            max: this.types === 1 ? 200 : 20
                        }
                    ]*/
                },
                extra: {
                    line: {
                        type: "curve",
                        width: 2,
                        activeType: "hollow"
                    }
                }
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.charts-box {
    width: 100%;
    height: 200px;
}
</style>