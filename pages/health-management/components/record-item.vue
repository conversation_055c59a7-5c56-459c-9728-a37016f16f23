<template>
    <view>
        <view class="flex-kai">
            <view class="flex-row">
                <view class="font18 color-title pr10">
                    <uni-dateformat :date="data.record_time" format="yyyy年MM月dd日"/>
                </view>
                <view class="font14 color-content flex-all-center">
                    <uni-dateformat :date="data.record_time" format="hh:mm:ss"/>
                </view>
            </view>
            <view v-if="types === 1" class="blood-pressure-status"
                  :style="{backgroundColor: bloodPressureStatus.bg_color, color: bloodPressureStatus.color}">
                {{ bloodPressureStatus.title }}
            </view>
            <view v-if="types === 2" class="blood-pressure-status"
                  :style="{backgroundColor: bloodSugarStatus.bg_color, color: bloodSugarStatus.color}">
                {{ bloodSugarStatus.title }}
            </view>
        </view>

        <view v-if="types === 1" class="blood-pressure flex-kai">
            <view class="data-item">
                <text class="iconfont icon-to-top color-sub"></text>
                <text class="data-value">{{ data.high_num }}</text>
                <text class="font12 color-sub">mmHg</text>
            </view>
            <view class="data-item text-center">
                <text class="iconfont icon-to-down color-sub"></text>
                <text class="data-value">{{ data.low_num }}</text>
                <text class="font12 color-sub">mmHg</text>
            </view>
            <view class="data-item text-right">
                <text class="iconfont icon-love color-sub"></text>
                <text class="data-value">{{ data.heart_num }}</text>
                <text class="font12 color-sub">bpm</text>
            </view>
        </view>

        <view v-if="types === 2" class="blood-sugar flex-kai">
            <view class="data-item flex-row">
                <view class="icon flex-all-center">
                    <text class="iconfont icon-blood-sugar"></text>
                </view>
                <view>
                    <text class="data-value">{{ data.sugar }}</text>
                    <text class="font12 color-sub">mmol/L</text>
                </view>
            </view>
            <view class="pt10 color-content font14">
                <template v-if="!data.time_hours">空腹</template>
                <template v-else>饭后{{ data.time_hours }}小时</template>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "record-item",
    props: {
        types: {
            type: Number,
            default: 1
        },

        // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
        /** @namespace data.record_time */
        /** @namespace data.high_num */
        /** @namespace data.low_num */
        /** @namespace data.heart_num */
        /** @namespace data.sugar */
        /** @namespace data.time_hours */
        data: {
            type: Object,
            default: () => ({})
        }
    },
    
    computed: {
        bloodPressureStatus() {
            if (this.types !== 1) return null
            
            const {high_num, low_num} = this.data
            
           
            if (high_num >= 180 || low_num >= 110) {
                return {title: '重度高血压', color: '#E6424B', bg_color: '#FEEDED'}
            }

            if ((high_num >= 160 && high_num <= 179) || (low_num >= 100 && low_num <= 109)) {
                return {title: '中度高血压', color: '#E6424B', bg_color: '#FEEDED'}
            }

            if ((high_num >= 140 && high_num <= 159) || (low_num >= 90 && low_num <= 99)) {
                return {title: '轻度高血压', color: '#FF7C3A', bg_color: '#FFF2EB'}
            }
            
            if (high_num < 90 || low_num < 60) {
                return {title: '低血压', color: '#0099E5', bg_color: '#EAF4FD'}
            }
            
            return {title: '血压正常', color: '#19be6b', bg_color: '#dbfaea'}
        },

        bloodSugarStatus() {
            if (this.types !== 2) return null
            
            const {time_hours, sugar} = this.data
            
            const highSide = {title: '偏高', color: '#E6424B', bg_color: '#FEEDED'}, 
                lowSide = {title: '偏低', color: '#FF7C3A', bg_color: '#FFF2EB'},
                normalSide = {title: '正常', color: '#19be6b', bg_color: '#dbfaea'}
            
            if (time_hours === 0) {
                if (sugar > 6.1) return highSide
                if (sugar < 4.4) return lowSide
                return normalSide
            }

            if (time_hours === 1) {
                if (sugar > 9.4) return highSide
                if (sugar < 6.7) return lowSide
                return normalSide
            }
            
            if (time_hours === 2) {
                if (sugar > 7.8) return highSide
                if (sugar < 4.4) return lowSide
                return normalSide
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.blood-pressure-status {
    padding: 5px;
    border-radius: 5px;
    font-size: 12px;
}

.blood-sugar, .blood-pressure {
    padding-top: 15px;

    .data-item {
        .data-value {
            font-size: 24px;
            color: #1c2438;
            font-weight: bold;
        }
    }
}

.blood-pressure {
    .data-item {
        width: calc(100% / 3);

        .data-value {
            padding: 0 2px;
        }
    }
}

.blood-sugar {
    .data-item {
        .icon {
            width: 36px;
            height: 36px;
            margin-right: 10px;
            border-radius: 5px;
            background-color: #F0FAFF;

            .iconfont {
                font-size: 24px;
                color: #0099E5;
            }
        }

        .data-value {
            font-size: 28px;
            padding-right: 2px;
        }
    }
}
</style>