<template>
    <view>
        <view class="container">
            <view @click="family_list_show = true">
                <image v-if="activeFamily.headimg" class="logo-image" :src="activeFamily.headimg"
                       mode="aspectFill"/>
                <view v-else class="no-logo text-center flex-all-center">
                    <uni-icons v-if="activeFamily.id === 0" type="person" size="24" color="#ffffff"/>
                    <text v-else>{{ activeFamily.nickname[0] }}</text>
                </view>
            </view>
            
            <view v-show="family_list_show">
                <view class="mask" @click="family_list_show = false"></view>

                <view class="triangular-arrow"></view>

                <view class="family-list bg-white">
                    <view class="family-item flex-row" v-for="item in family_list" :key="item.id"
                          :class="{'active-family': item.family_id === family_id}"
                          hover-class="navigator-hover" @click="changeFamily(item.family_id)">
                        <view class="pr10">
                            <image v-if="item.headimg" class="logo-image" :src="item.headimg"
                                   mode="aspectFill"/>
                            <view v-else class="no-logo flex-all-center">
                                <uni-icons v-if="item.id === 0" type="person" size="24" color="#ffffff"/>
                                <text v-else>{{ item.nickname[0] }}</text>
                            </view>
                        </view>
                        <view class="nickname ellipsis">{{ item.nickname }}</view>
                    </view>

                    <view class="family-item flex-row" hover-class="navigator-hover" @click="toFamilyList">
                        <view class="pr10">
                            <view class="no-logo flex-all-center">
                                <text class="iconfont icon-team font24 color-white"></text>
                            </view>
                        </view>
                        <view class="nickname">成员管理</view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import familyApi from '../api/family'

export default {
    name: "family-select",
    emits: ['familyIdUpdate'],
    props: {
        familyId: {
            type: String,
            default: ''
        }
    },

    data() {
        return {
            family_list_show: false,
            family_id: '',
            family_list: [],
        }
    },

    watch: {
        familyId() {
            this.family_id = this.familyId
        }
    },

    computed: {
        activeFamily() {
            return this.family_list.find(item => item.family_id === this.family_id)
        }
    },

    mounted() {
        this.family_id = this.familyId
        this.getFamilyList()
    },

    methods: {
        async getFamilyList(reload = false) {
            const myself = {
                id: 0,
                family_id: '',
                nickname: '我自己',
                logo: app.globalData['userinfo']?.headimg || ''
            }
            const list = await familyApi.getFamilyList(reload)
            this.family_list = [myself, ...list]
        },

        toFamilyList() {
            this.$uni.navigateTo('./family/list', {
                events: {
                    familyListUpdate: () => {
                        this.getFamilyList(true)
                    },
                    storageFamilyDelete: () => {
                        this.changeFamily('')
                    }
                }
            })
        },

        changeFamily(family_id = '') {
            this.family_id = family_id
            
            const storage_key = 'health-management-family-id'
            if (family_id === '') {
                if (uni.getStorageSync(storage_key)) uni.removeStorageSync(storage_key)
            } else {
                uni.setStorageSync(storage_key, family_id)
            }
            this.$emit('familyIdUpdate', family_id)
            this.family_list_show = false
        }
    }
}
</script>

<style lang="scss" scoped>
.container {
    position: relative;

    .logo-image, .no-logo {
        width: 36px;
        height: 36px;
        border-radius: 18px;
        background-color: #5cadff;
        color: #fff;
    }

    .logo-image {
        display: block;
    }

    .mask {
        position: fixed;
        z-index: 9999;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background-color: rgba(0, 0, 0, 0.7);
    }

    .triangular-arrow {
        position: absolute;
        z-index: 10000;
        top: 50px;
        left: 10px;
        width: 20px;
        height: 20px;
        background-color: #fff;
        transform: rotate(45deg);
    }

    .family-list {
        position: absolute;
        z-index: 10000;
        top: 60px;
        left: -10px;
        border-radius: 10px;
        padding: 5px;

        .family-item {
            border-radius: 5px;
            padding: 5px;

            .nickname {
                width: 100px;
                line-height: 36px;
                color: #5cadff;
                font-size: 14px;
            }
        }

        .active-family {
            background-color: #d4e6f8;
        }
    }
}
</style>