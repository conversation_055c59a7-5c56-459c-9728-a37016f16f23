<template>
    <view class="page">
        <diy-index ref="diy-index"/>
        <tab-bar v-if="tab_bar_show"/>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import tabBar from '@/components/tabbar.vue'
import diyIndex from './cpmponents/diy-index.vue'

export default {
    components: {
        tabBar, diyIndex
    },
    data() {
        return {
            tab_bar_show: false,
            diy_page_loading: true
        }
    },

    onLoad() {
        this.$uni.hideHomeButton()
        this.$uni.showLoading()
        login.uniLogin(err => {
            if (err !== undefined) return
            this.tab_bar_show = true
            this.setNavigationBarTitle()
            this.getDiyPageSet()
        })
    },

    methods: {
        setNavigationBarTitle() {
            const shop_name = app.globalData['shop_info']?.['shop_set']?.['shop_details']?.shop_name
            shop_name && this.$uni.setNavigationBarTitle(shop_name)
        },

        getDiyPageSet() {
            const page_diy_set = app.globalData['shop_info']?.['shop_set']?.['page_diy_set']
            this.$nextTick(() => {
                this.diy_page_loading = false
                uni.hideLoading()
            })
            if (!page_diy_set) return
            const {index_page_set, navigation_bar_color_set} = page_diy_set
            index_page_set?.length && this.diyPageSet(index_page_set)
            navigation_bar_color_set && this.navigationBarSet(navigation_bar_color_set)
        },

        diyPageSet(data) {
            this.$refs['diy-index'].open(data)
        },

        navigationBarSet(data) {
            if (data['background_color'] || data.color) {
                uni.setNavigationBarColor({
                    frontColor: data.color && data.color === 'white' ? '#ffffff' : '#000000',
                    backgroundColor: data['background_color'] || '#ffffff'
                })
            }
        },
    },

    onShareAppMessage() {
        let path = '/pages/index/index?userid=' + app.globalData['userid']
        const active_id = app.globalData['shop_info']?.['extend_set']?.['shield_other_active']?.active_id
        if (active_id) path += `&id=${active_id}`
        return {
            path
        }
    },
}
</script>

<style lang="scss" scoped>
.page {
    padding-bottom: 100px;
}
</style>
