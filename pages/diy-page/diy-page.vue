<template>
    <view class="page bg-background">
        <diy-index ref="diy-index"></diy-index>
        <diy-tab-bar v-if="tab_bar_show" :secondary-page="true" :tab-bar-data="tab_bar_data"></diy-tab-bar>
    </view>
</template>

<script>
import login from '@/utils/api/login.js'
import diyIndex from './cpmponents/diy-index.vue'
import diyTabBar from './cpmponents/diy-tab-bar.vue'

export default {
    components: {
        diyIndex, diyTabBar
    },
    data() {
        return {
            tab_bar_show: false,
            tab_bar_data: null
        }
    },

    onLoad(params) {
        this.$uni.showLoading()
        login.uniLogin(err => {
            if (err !== undefined) return uni.hideLoading()
            this.getDiyPageSet(params.id)
        })
    },

    methods: {
        async getDiyPageSet(id) {
            const res = await this.xwy_api.request({
                url: 'front.system.pageShow/single_page_details',
                data: { id }
            })
            uni.hideLoading()

            if (res?.data?.details?.title) this.$uni.setNavigationBarTitle(res.data.details.title)
            const data = res?.data?.details?.content
            if (!data) return
            this.initPage(data)
        },

        initPage(data) {
            const { page_set, tab_bar, top_state_set } = data
            page_set?.length && this.diyPageSet(page_set)
            tab_bar?.list?.length && this.tabBarSet(tab_bar)
            top_state_set && this.navigationBarSet(top_state_set)
        },

        diyPageSet(data) {
            this.$refs['diy-index'].open(data)
        },

        tabBarSet(data) {
            this.tab_bar_data = data
            this.tab_bar_show = true
        },

        navigationBarSet(data) {
            if (data['background_color'] || data.color) {
                uni.setNavigationBarColor({
                    frontColor: data.color && data.color === 'white' ? '#ffffff' : '#000000',
                    backgroundColor: data['background_color'] || '#ffffff'
                })
            }
        },
    }
}
</script>

<style scoped lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 80px;
}
</style>
