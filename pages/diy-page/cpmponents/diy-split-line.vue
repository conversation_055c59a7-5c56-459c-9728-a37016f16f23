<template>
    <view
        :style="{
             backgroundColor: data.color,
             paddingLeft: data.padding_left + 'px',
             paddingRight: data.padding_right + 'px',
             paddingTop: data.padding_top + 'px',
             paddingBottom: data.padding_bottom + 'px'
        }"
    >
        <view
            class="line"
            :style="{
                height: data.line_width + 'px',
                backgroundColor: data.line_color,
                borderRadius: data.line_border_radius + 'px'
             }"
        ></view>
    </view>
</template>

<script>
export default {
    name: "diy-split-line",
    props: ['data']
}
</script>
