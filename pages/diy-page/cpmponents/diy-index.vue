<template>
    <view class="diy-list">
        <view class="diy-item" v-for="(item, index) in diy_list" :key="index">
            <diy-notice v-if="item.type === 1" :data="item.data"/>
            <diy-carousel v-if="item.type === 2" :data="item.data" @jump="jump"/>
            <view hover-class="navigator-hover" @click="toSearch">
                <diy-search v-if="item.type === 3" :data="item.data"/>
            </view>
            <diy-image v-if="item.type === 5" :data="item.data" @jump="jump"/>
            <diy-grid v-if="item.type === 4" :data="item.data" @jump="jump"/>
            <diy-split-line v-if="item.type === 6" :data="item.data"/>
            <diy-space v-if="item.type === 7" :data="item.data"/>
            <diy-article v-if="item.type === 9" :data="item.data"/>
            <diy-images v-if="item.type === 11" :data="item.data" @jump="jump"/>
            <diy-recent-activity v-if="item.type === 12" :data="item.data"/>
            <diy-hover-button v-if="item.type === 14" :data="item.data" @jump="jump"/>
        </view>
    </view>
</template>

<script>
    import diySearch from "./diy-search.vue"
    import diyCarousel from "./diy-carousel.vue"
    import diyNotice from "./diy-notice.vue"
    import diySplitLine from "./diy-split-line.vue"
    import diyImage from "./diy-image.vue"
    import diySpace from "./diy-space.vue"
    import diyGrid from "./diy-grid.vue"
    import diyImages from "./diy-images.vue"
    import diyRecentActivity from "./diy-recent-activity.vue"
    import diyHoverButton from "./diy-hover-button.vue"
    import diyArticle from "./diy-article.vue"
    export default {
        name: "diy-index",

        components: { 
            diySearch, 
            diyCarousel, 
            diyNotice, 
            diySplitLine, 
            diyImage, 
            diySpace, 
            diyGrid, 
            diyImages, 
            diyRecentActivity,
            diyHoverButton,
            diyArticle,
        },

        data() {
            return {
                diy_list: []
            }
        },

        methods: {
            open(data) {
                this.diy_list = data
            },

            toSearch() {
                this.$uni.navigateTo('/pages/activity/user/activity_list?type=all')
            },

            jump(data) {
                if (!data.type) return
                const fun_list = {
                    '1': () => {  // 文章详情
                        if (!data['news_id']) return
                        this.$uni.navigateTo('/pages/news/preview?id=' + data['news_id'])
                    },
                    '2': () => {  // 文章分类列表
                        let url = '/pages/news/list'
                        if (data['category_id']) url += `?category_id=${data.category_id}`
                        this.$uni.navigateTo(url)
                    },
                    '5': () => {  // 拨打电话
                        if (!data.tel) return
                        uni.makePhoneCall({
                            phoneNumber: data.tel.toString()
                        })
                    },
                    '6': () => {  // 跳转到其他小程序
                        if (!data.appid) return
                        const options = {appId: data.appid}
                        if (data.path) options.path = data.path
                        uni.navigateToMiniProgram({
                            ...options,
                            fail: err => {
                                if (err?.errMsg && err.errMsg !== 'navigateToMiniProgram:fail cancel') {
                                    this.$uni.showModal(`小程序跳转失败，appid: ${data.appid}。${options.path ? `path: ${options.path}。` : ''}${err.errMsg}`)
                                }
                            }
                        })
                    },
                    // 7 地图导航
                    '8': () => {  // 小程序首页
                        const index_route = 'pages/local-living/user/index'
                        if (this.$uni.route() === index_route) return
                        this.$uni.navigateTo('/' + index_route)
                    },
                    '9': () => this.$uni.navigateTo('/pages/user/user-center'),  // 个人中心
                    '12': () => this.$uni.navigateTo('/pages/comment/list'),     // 运动圈
                    // 20 跳转到商家
                    '25': () => this.$uni.navigateTo('/pages/other/contact'),    // 联系客服
                    '41': () => {
                        if (!data.active_id || !data.active_types) return
                        this._utils.toActivityDetail({active_id: data.active_id, types: data.active_types})
                    },
                    '104': () => {  // 个性化二级页面
                        if (!data['page_id']) return
                        this.$uni.navigateTo('/pages/diy-page/diy-page?id=' + data['page_id'])
                    },
                    '108': () => this.$uni.navigateTo('/pages/create-activity/index'),
                    '127': () => this.$uni.navigateTo('/pages/wechat-step/wechat-step-page'),
                    '128': () => this.$uni.navigateTo('/pages/activity-program/user/generate'),
                    '129': () => this.$uni.navigateTo('/pages/wechat-step/sports-poster/sports-poster'),
                    '130': () => this.$uni.navigateTo('/pages/running/user/run_list'),
                    '132': () => this.$uni.navigateTo('/pages/idea_avatar/background/background'),
                    '133': () => this.$uni.navigateTo('/pages/idea_avatar/pendant/pendant'),
                    '139': () => this.$uni.navigateTo('/pages/other/small-feature-collection'),
                    '146': () => this.$uni.navigateTo('/pages/weightHeight/user/details'),
                    '153': () => this.$uni.navigateTo('/pages/create-activity/index?just_template=1')
                }
                if (!fun_list[data.type]) {
                    this.$uni.showModal('无此跳转类型' + data.type)
                    return
                }
                fun_list[data.type]()
            }
        }
    }
</script>

<style scoped lang="scss">

</style>
