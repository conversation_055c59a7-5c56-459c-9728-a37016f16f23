<template>
    <view>
        <view v-if="last_activity" class="flex-kai p10 font14" 
              :style="{backgroundColor: data['backgroundColor']}" @click="toDetail">
            <view class="ellipsis">
                <text v-if="data['titleShow']" :style="{color: data['titleColor'], paddingRight: '5px'}">
                    {{ data['title'] }}:
                </text>
                <text :style="{color: data['textColor']}">{{ last_activity.active_name }}</text>
            </view>
            <view v-if="data['goTextShow']" class="no-wrap pl10">
                <text :style="{color: data['goTextColor']}">{{ data['goText'] }}</text>
                <uni-icons type="forward" size="14" :color="data['goTextColor']"/>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "diy-recent-activity",
    props: {
        data: {
            type: Object,
            default: () => {}
        }
    },

    data() {
        return {
            last_activity: null
        }
    },

    mounted() {
        this.getLastActivity()
    },

    methods: {
        async getLastActivity() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_list',
                data: {
                    page: 1,
                    perpage: 1,
                    get_latest_active: 1
                },
            })

            if (res.data?.user_latest_attend_details) this.last_activity = res.data.user_latest_attend_details
        },

        toDetail() {
            this._utils.toActivityDetail(this.last_activity)
        },
    }
}
</script>

<style lang="scss">

</style>
