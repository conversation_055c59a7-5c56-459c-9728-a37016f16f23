<template>
    <view class="flex-row" :class="{'flex-wrap': data['line_type'] === 'more'}" :style="containerStyle">
        <view class="item text-center" v-for="(item, index) in data.list" :key="index" :style="itemStyle" 
              @click="$emit('jump', item)">
            <view class="flex-all-center">
                <image class="image" :src="item.pic" :style="imageStyle"/>
            </view>
            <view :style="{ color: data.text_color, fontSize: data['text_font_size'] + 'px' }">
                {{item.name}}
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "diy-grid",
    props: ['data'],
    computed: {
        maxWidth() {
            const {windowWidth} = uni.getSystemInfoSync()
            let width = windowWidth
            // #ifdef H5
            if (windowWidth > 500) width = 500
            // #endif
            return width
        },
        containerStyle() {
            const {background_color, line_type} = this.data
            let style = `width: ${this.maxWidth}px; background-color: ${background_color}; `
            if (line_type === 'one') style += 'overflow-x: auto;'
            return style
        },
        itemStyle() {
            const {line_count, line_type} = this.data
            return `
                width: ${this.maxWidth / line_count}px;
                min-width: ${line_type === 'one' ? '80px' : '0'};
            `
        },
        imageStyle() {
            const {line_count, line_type, border_radius} = this.data
            const size = `${this.maxWidth / line_count - 10}px`
            const minSize = line_type === 'one' ? '60px' : 'auto'
            
            return `
                width: ${size},
                height: ${size},
                minWidth: ${minSize},
                minHeight: ${minSize},
                borderRadius: ${border_radius}
            `
        }
    }
}
</script>

<style scoped lang="scss">
.item {
    padding: 5px 0;
}
.image {
    display: block;
    max-width: 60px;
    max-height: 60px;
}
</style>
