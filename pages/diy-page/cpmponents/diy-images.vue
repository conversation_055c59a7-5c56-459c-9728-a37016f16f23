<template>
    <view class="images-show flex-row">
        <view v-for="item in images" :key="item.type" :class="item.type"
              :style="{width: item.width + 'px'}">
            <view class="images" v-for="(img, index) in item.images" :key="index"
                  :style="{height: img.height + 'px', padding: data['padding'] + 'px'}" @click="jump(img)">
                <image class="image" :src="img.src" :mode="img.mode"
                       :style="{borderRadius: data['borderRadius'] / 2 + '%'}"/>
            </view>
        </view>
    </view>
</template>

<script>

export default {
    name: "diy-images",
    emits: ['jump'],
    props: {
        data: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            
        }
    },
    computed: {
        images() {
            const data = this.data

            // const left_images = data.left.images, right_images = data.right.images, proportion = data.proportion
            // 下面代码是上面注释代码的解构赋值写法
            const {left: {images: left_images}, right: {images: right_images}, proportion} = data

            const [left_proportion, right_proportion] = proportion

            return [
                this.createImageObject('left', left_proportion, left_images),
                this.createImageObject('right', right_proportion, right_images)
            ]
        },
    },

    mounted() {

    },

    methods: {
        createImageObject(type, proportion, images) {
            let width = uni.getSystemInfoSync().windowWidth
            // #ifdef H5
            if (width > 500) width = 500
            // #endif
            return {
                type,
                width: width * (proportion / 100),
                images: images.map(v => ({
                    ...v,
                    height: width * (v.height / 100)
                }))
            }
        },

        jump(data) {
            this.$emit('jump', data)
        }
    }
}
</script>

<style lang="scss">
.images {
    width: 100%;
    box-sizing: border-box;
}

.image {
    width: 100%;
    height: 100%;
    display: block;
}
</style>