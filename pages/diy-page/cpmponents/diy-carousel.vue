<template>
    <view>

        <swiper
            v-if="swiperHeight"
            class="swiper-carousel"
            :indicator-dots="data.indicator_dots"
            :indicator-color="data.indicator_color"
            :indicator-active-color="data.indicator_active_color"
            :autoplay="data.autoplay"
            :circular="true"
            :interval="data.interval"
            :duration="data.duration"
            :style="swiperHeight"
        >
            <swiper-item v-for="(sItem, sIndex) in data.list" :key="sIndex" :style="swiperHeight">

                <image :src="sItem.src" :mode="data.image_mode" :style="swiperHeight" 
                       @click="$emit('jump', sItem)"/>
            </swiper-item>
        </swiper>
    </view>
</template>

<script>
export default {
    name: "diy-carousel",
    props: ['data'],
    computed: {
        swiperHeight() {
            const {windowWidth} = uni.getSystemInfoSync()
            let maxWidth = windowWidth
            // #ifdef H5
            if (windowWidth > 500) maxWidth = 500
            // #endif
            return `height: calc(${maxWidth}px * ${this.data.height})`
        }
    }
}
</script>

<style scoped lang="scss">
.swiper-carousel, .swiper-carousel swiper-item, .swiper-carousel image {
    width: 100%;
}
</style>
