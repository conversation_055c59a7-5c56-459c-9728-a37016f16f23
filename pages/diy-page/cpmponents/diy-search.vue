<template>
    <view
        class="search flex-kai"
        :style="{backgroundColor: data.background_color}"
    >
        <input
            class="search-input"
            type="text"
            disabled
            :placeholder="data.placeholder"
            :style="{
				        backgroundColor: data.input_background_color,
				        border: data.input_border ? `1px solid ${data.input_border_color}` : 'none',
				        borderRadius: data.input_radius + 'px'
			      }"
        />
        <view
            class="search-button color-primary"
            :style="{
                width: data.btn_width + 'px',
                minWidth: data.btn_width + 'px',
                color: data.btn_color,
                backgroundColor: data.btn_background_color,
                borderRadius: data.btn_radius + 'px',
             }"
        >
            {{ data.btn_text }}
        </view>
    </view>
</template>

<script>
export default {
    name: "search",
    props: ['data']
}
</script>

<style scoped lang="scss">
.search {
    padding: 10px;

    .search-input {
        width: 100%;
        height: 40px;
        line-height: 40px;
        padding: 0 10px;
    }

    .search-button {
        line-height: 40px;
        margin-left: 10px;
        text-align: center;
    }
}
</style>
