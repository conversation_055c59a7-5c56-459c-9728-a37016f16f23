<template>
    <view class="hover-button" :style="styles.buttonStyle" @click="$emit('jump', data)">

        <image :src="data['imageSrc']" mode="aspectFill" :style="styles.imageStyle"/>
    </view>
</template>

<script>
export default {
    name: "diy-hover-button",
    props: {
        data: {
            type: Object,
            default: () => {}
        }
    },
    computed: {
        styles() {
            const {size, right, bottom, backgroundColor, borderRadius, padding} = this.data
            const {windowWidth, windowHeight} = uni.getSystemInfoSync()
            let _right = windowWidth * (right / 100)
            // #ifdef H5
            if (windowWidth > 500) _right += (windowWidth - 500) / 2
            // #endif

            const sizeStyle = `width: ${size}px; height: ${size}px;`
            return {
                buttonStyle: `
                    ${sizeStyle}
                    right: ${_right}px;
                    bottom: ${windowHeight * (bottom / 100)}px;
                    background-color: ${backgroundColor};
                    border-radius: ${borderRadius}%;
                    padding: ${padding}px;
                `,
                imageStyle: sizeStyle
            }
        }
    }
}
</script>

<style lang="scss">
.hover-button {
    position: fixed;
    z-index: 999;
    overflow: hidden;

    .image {
        display: block;
    }
}
</style>
