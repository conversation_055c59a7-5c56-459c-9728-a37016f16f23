<template>
    <view class="tab-bar-list flex-row" v-if="show" :style="list_style"
    >
        <view
            class="tab-bar-item text-center"
            v-for="(item, index) in tab_bar_list"
            :key="index"
            :style="item_style"
            @click="jump(item)"
        >
            <image class="icon" :src="item.icon" mode="aspectFill"/>
            <view class="title font14" :style="{color: item.color}">{{item.title}}</view>
        </view>
    </view>
</template>

<script>
const app = getApp()
export default {
    name: "diy-tab-bar",
    props: {
        secondaryPage: { // 是否二级页面，二级页面的tab栏数据由页面传入组件
            default: false
        },
        tabBarData: {   // 二级页面tab栏数据
            default: null
        }
    },

    data() {
        return {
            show: false,
            list_style: '',
            item_style: '',
            tab_bar_list: []
        }
    },

    mounted() {
        this.now_route = this.$uni.route()
        this.getTabBarSet()
    },

    methods: {

        getTabBarSet() {
            const tab_bar_data = this.tabBarData || app.globalData['shop_info']?.['shop_set']?.['page_diy_set']?.['tabbar_set']
            if (!tab_bar_data || !tab_bar_data.list?.length) return
            this.show = true
            this.list_style = `background-color: ${tab_bar_data['background_color']}; border-top: 1px solid ${tab_bar_data['top_border_color']};`
            this.item_style = `width: calc(100% / ${tab_bar_data.list.length})`
            this.setTabBarList(tab_bar_data.list, tab_bar_data.color, tab_bar_data['select_color'])
        },

        setTabBarList(list, color, select_color) {
            const tab_bar_list = []
            list.forEach(v => {
                const type = Number(v.type)
                const item = {
                    title: v.title,
                    type
                }

                switch (type) {
                    case 1:  // 文章详情
                        item.jump_type = 'navigateTo'
                        item.route = 'pages/news/preview'
                        item.path = '/' + item.route + '?id=' + v['news_id']
                        break
                    
                    case 2:  // 文章分类列表
                        item.jump_type = 'navigateTo'
                        item.route = 'pages/news/list'
                        item.path = '/' + item.route
                        if (v['category_id']) item.path += `?category_id=${v.category_id}`
                        break
                    
                    case 8:
                        item.jump_type = 'reLaunch'
                        item.route = 'pages/index/index'
                        item.path = '/' + item.route
                        break

                    case 9:
                        item.jump_type = 'reLaunch'
                        item.route = 'pages/user/user'
                        item.path = '/' + item.route
                        break

                    case 25:
                        item.jump_type = 'navigateTo'
                        item.route = 'pages/other/contact'
                        item.path = '/' + item.route
                        break

                    case 104:
                        item.jump_type = 'navigateTo'
                        item.route = 'pages/diy-page/diy-page'
                        item.path = '/' + item.route + '?id=' + v['page_id']
                        break

                    default:
                        item.error = `暂不支持此跳转${type}`
                }

                // 二级页面的tab栏跳转除了首页都是从新页面打开
                if (this.secondaryPage && type !== 8) item.jump_type = 'navigateTo'

                item.icon = item.route === this.now_route ? v['selpic'] : v.pic
                item.color = item.route === this.now_route ? select_color : color

                tab_bar_list.push(item)
            })
            this.tab_bar_list = tab_bar_list
        },

        jump(item) {
            if (item.route === this.now_route) return
            if (item.error) {
                this.$uni.showModal(item.error)
                return
            }
            this.$uni[item.jump_type](item.path)
        }
    }
}
</script>

<style scoped lang="scss">
.tab-bar-list {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 99;
    .tab-bar-item {
        .icon {
            width: 30px;
            height: 30px;
            position: relative;
            top: 3px;
        }
        .title {
            position: relative;
            top: -5px;
        }
        padding-bottom: 10px;
    }
}
</style>
