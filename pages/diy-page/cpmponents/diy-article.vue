<template>
    <view :style="{'backgroundColor': data['background_color']}">
        <view v-if="data['show_title']" class="module-title p10">
            <view class="flex-kai">
                <view class="module-title-name color-title">{{ data['title'] || data['class_name'] }}</view>
                <view v-if="data['show_more']" class="look-more" @click="lookMore">
                    <text class="color-primary font14">查看更多</text>
                    <uni-icons type="forward" size="14" color="#2d8cf0"/>
                </view>
            </view>
        </view>

        <view class="list">

            <navigator class="item p10 flex-row" v-for="item in list" :key="item.news_id" 
                       :url="'/pages/news/preview?id=' + item.news_id">
                <view v-if="data['show_logo']" class="logo">
                    <image
                        v-if="item.thumb_pic"
                        class="logo-image"
                        mode="aspectFill"
                        :src="item.thumb_pic"
                    />
                    <view v-else class="no-logo flex-all-center">
                        <uni-icons type="image" color="#bbbec4" size="50"/>
                    </view>
                </view>
                <view style="width: 100%;">
                    <view
                        class="ellipsis--l2"
                        :class="{'title': data['show_logo']}"
                        :style="{
							'color': data['article_title_color'],
							'fontSize': data['article_title_size'] + 'px'
						}"
                    >{{item.title}}</view>
                    <view
                        v-if="data['show_time']"
                        class="time"
                        :style="{
							'color': data['time_color'],
							'fontSize': data['time_size'] + 'px',
							'textAlign': data['time_align']
						}"
                    >{{item.create_time}}</view>
                </view>
            </navigator>
        </view>

        <view v-if="!in_load && !list.length" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无活动</view>
        </view>
    </view>
</template>

<script>

export default {
    name: "diy-article",
    props: {
        data: Object
    },
    data() {
        return {
            list: [],
            in_load: true
        }
    },

    mounted() {
        this.getList()
    },

    methods: {
        async getList() {
            this.list = []
            this.all_list = []
            this.in_load = true
            const data = {
                page: 1,
                perpage: 20
            }
            if (this.data['class_id']) data.category_id = this.data['class_id']
            const res = await this.xwy_api.request({
                url: 'front.news/news_article_list',
                data
            })
            this.in_load = false
            this.all_list = res?.data?.['person_list']?.data || []
            this.setList()
        },

        setList() {
            this.list = this.all_list.slice(0, this.data['article_count'])
        },

        lookMore() {
            let url = '/pages/news/list'
            if (this.data['class_id']) url += `?category_id=${this.data['class_id']}`
            this.$uni.navigateTo(url)
        }
    }
}
</script>

<style scoped>
.module-title, .item {
    border-bottom: 1px solid #eee;
}

.module-title-name {
    padding-left: 5px;
}
.module-title-name::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 14px;
    background-color: #2d8cf0;
    border-radius: 2px;
    position: relative;
    top: 2px;
    left: -3px;
}

.logo {
    padding-right: 10px;
}

.logo-image, .no-logo {
    width: 100px;
    min-width: 100px;
    height: 70px;
    box-sizing: border-box;
}

.title {
    line-height: 22px;
    height: 44px;
}

.time {
    padding-top: 5px;
}
</style>
