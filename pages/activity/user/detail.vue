<template>
    <view class="page" :style="{paddingBottom: tabbar_data ? '71px' : '0', backgroundColor: page_background_color}">

        <screen-picture 
            v-if="screen_pic_show" 
            :hide="screen_pic_hide"
            :image-src="screen_pic" 
            :time="screen_pic_count_down" 
            :show-button="screen_pic_buttom_show === 1" 
            @skipScreen="skipScreen"
        />


        <activity-delete-tips v-if="error" :error="error" :activeId="id"/>

        <view v-if="detail && detail.active_id">
            <template v-if="rankSet['barrage']">
                <lff-barrage ref="lffBarrage" :activeid="id"></lff-barrage>
            </template>
            
            <view
                v-if="detail.conf && detail.conf.active && detail.conf.active.audio_src"
                class="audio flex-all-center"
                :class="{'audio_rotate': audio_play}" @click="audioControl"
            >
                <text class="iconfont icon-background-music-play font24 color-white"></text>
            </view>

            <template>
                <template v-if="stepUnlockPictureFragmentsSet">
                    <step-unlock-picture-fragments-show
                        :active-id="id"
                        :is-join="is_joining"
                        :rank-set="rankSet"
                        :activity-description-show="!detail.conf.active.activity_rules_hide || detail.content || news_detail"
                        :ranklist-show="ranklistShow"
                        :logo="detail.logo"
                        :user-mileage.sync="user_exchange_num"
                        :kilo-unit="kiloUnit"
                        :everyday-max-step="detail.conf.active.max_num"
                        :exchange-rate="detail.conf.active.exchange_rate"
                        :set="stepUnlockPictureFragmentsSet"
                        :is-admin="is_my_activity"
                        :exchange-calendar-show="exchangeCalendarShow"
                        :take-oath-show="rankSet['party_oath'] && detail.conf.active.take_oath_bg_img"
                        :arrive-then-end="!!detail.conf.active.arrive_then_end"
                        @unlock="fragmentsUnlock"
                        @toTopList="toTopList"
                        @toExchangeCalendar="toExchangeCalendar"
                        @uniPopupOpen="uniPopupOpen"
                        @showActiveSharePopup="showActiveSharePopup"
                    />
                </template>

                <template v-else-if="useDay7StepTemplate">
                    <day7-step-template
                        ref="day7StepTemplate"
                        :background-image="day7StepTemplateBackgroundImage"
                        :active-id="id"
                        :active-details="detail"
                        :is-join="is_joining"
                        :today-step="today_step"
                        :user-details="user_details"
                        :unit="kiloUnit"
                        @lookMoreStep="toExchangeCalendar"
                        @exchange="exchange"
                        @join="joinActivity"
                        @toTopList="toTopList"
                        @showActiveExplanation="uniPopupOpen('activity_detail')"
                        @showActiveSharePopup="showActiveSharePopup"
                        @toManage="toManage"
                        @showMyInfo="uniPopupOpen('my_info')"
                    />
                </template>

                <template v-else-if="diyMap">
                    <diy-map ref="diyMap" :set="diyMapSet" :active-details="detail"
                             :user-details="user_details" :active-more-data="active_more_data"
                             @clickModule="diyPageClick" @updateUserDetails="getUserStatus(true)"
                             @toJoinActivity="joinActivity"/>
                </template>

                <template v-else>
                    <view v-if="diy_show_type === 'model'" class="page-diy-model">
                        <view class="diy-model-item" v-for="(model, model_i) in page_diy_model"
                              :key="model_i">
                            <template v-if="model.show && model.type === 'carousel' && model.data">

                                <swiper
                                    :indicator-dots="model.data.indicator_dots"
                                    :indicator-color="model.data.indicator_color"
                                    :indicator-active-color="model.data.indicator_active_color"
                                    :autoplay="model.data.autoplay"
                                    :circular="true"
                                    :interval="model.data.interval"
                                    :duration="model.data.duration"
                                    :style="'height: calc(100vw * ' + model.data.height + ');'"
                                >
                                    <swiper-item
                                        v-for="(swiper_item, swiper_index) in model.data.list"
                                        :key="swiper_index"
                                        :style="'height: calc(100vw * ' + model.data.height + ');'"
                                    >

                                        <image
                                            :src="swiper_item.src"
                                            :mode="model.data.image_mode"
                                            :style="'height: calc(100vw * ' + model.data.height + '); width: 100%; display: block;'"
                                            @click="diyPageClick(swiper_item)"
                                        />
                                    </swiper-item>
                                </swiper>
                            </template>


                            <template v-if="model.show && model.type === 'image' && model.data">
                                <view>
                                    <image
                                        :src="model.data.src"
                                        mode="widthFix"
                                        style="width: 100%; display: block;"
                                        @click="diyPageClick(model.data)"
                                    />
                                </view>
                            </template>


                            <template v-if="model.show && model.type === 'notice' && model.data">
                                <uni-notice-bar
                                    :showIcon="model.data.showIcon"
                                    :speed="model.data.speed"
                                    :scrollable="model.data.scrollable"
                                    :single="model.data.single"
                                    :text="model.data.text"
                                    :background-color="model.data.background_color"
                                    :color="model.data.color"
                                    @click="diyPageClick(model.data)"
                                ></uni-notice-bar>
                            </template>


                            <template v-if="model.show && model.type === 'line' && model.data">
                                <view
                                    :style="{
                                        backgroundColor: model.data.color,
                                        paddingLeft: model.data['padding_left'] + 'px',
                                        paddingRight: model.data['padding_right'] + 'px',
                                        paddingTop: model.data['padding_top'] + 'px',
                                        paddingBottom: model.data['padding_bottom'] + 'px'
                                    }"
                                >
                                    <view class="line"
                                          :style="{
                                               height: model.data['line_width'] + 'px',
                                               backgroundColor: model.data['line_color'],
                                               borderRadius: model.data['line_border_radius'] + 'px'
                                           }"
                                    ></view>
                                </view>
                            </template>


                            <template v-if="model.type === 'space' && model.data">
                                <view :style="{height: model.data.height + 'px', backgroundColor: model.data.color}"></view>
                            </template>


                            <template v-if="model.type === 'activity_button'">

                                <!--OA关闭了进入地图按钮，但是要显示报名按钮-->
                                <view v-if="rankSet['closedMapPointNotShow'] && !is_joining"
                                      class="p10 bg-white">
                                    <view class="join-container">
                                        <view class="flex-all-center">
                                            <view class="join-btn bg-primary color-white"
                                                  @click="joinActivity">参与活动
                                            </view>
                                        </view>
                                    </view>
                                </view>

                                <view v-if="!rankSet['closedMapPointNotShow']" class="p10 bg-white">
                                    <view
                                        v-if="finished_time && !rankSet['stepRewardIntegralForMinMax']"
                                        class="color-red font14 text-center pb5"
                                        @click="uniPopupOpen('finishedPopup')"
                                    >
                                        <text>恭喜你已在</text>
                                        <uni-dateformat
                                            class="plr5"
                                            :date="finished_time * 1000"
                                            format="yyyy-MM-dd hh:mm:ss"
                                            :threshold="[0, 0]"
                                        />
                                        <text>到达终点</text>
                                    </view>

                                    <view v-if="is_simple && !time_period_step"
                                          class="join-container flex-all-center">
                                        <view
                                            v-if="!is_joining"
                                            class="join-btn bg-primary color-white"
                                            @click="joinActivity"
                                        >参与活动
                                        </view>
                                        <view
                                            v-if="is_joining && !checked"
                                            class="join-btn bg-disabled color-sub"
                                        >已参与活动，请等待管理员审核
                                        </view>
                                        <view
                                            v-if="is_joining && checked"
                                            class="join-btn bg-primary color-white"
                                            @click="showAD"
                                        >同步步数
                                        </view>
                                    </view>

                                    <template v-if="!is_simple || time_period_step">
                                        <view
                                            v-if="(rankSet.exam_open || rankSet['must_mapdetails_exchange']) && detail.conf.active.map_types !== 101"
                                            class="join-container flex-all-center"
                                        >
                                            <view @click="toMap" class="join-btn bg-primary color-white">
                                                进入活动
                                            </view>
                                        </view>

                                        <template v-else>
                                            <view
                                                v-if="detail.conf.active.enter_types === 3 && is_joining && checked === 0"
                                                class="join-container"
                                            >
                                                <view class="pb5 color-sub text-center font14">
                                                    需要管理员审核通过后才能兑换
                                                </view>
                                                <view class="flex-all-center">
                                                    <view class="join-btn bg-background color-sub">
                                                        <template
                                                            v-if="id === '4437b18798c7d8df91e8a0329ede73d9'"
                                                        >每日打卡
                                                        </template>
                                                        <template v-else>立即兑换</template>
                                                    </view>
                                                </view>
                                            </view>

                                            <view v-else class="join-container">
                                                <view v-if="today_step !== null"
                                                      class="text-center pb5 color-sub font14"
                                                      @click="getMyStep(true)">
                                                    <text class="iconfont icon-walk color-sub font14"></text>
                                                    <text class="plr5">今日行走 {{ today_step || 0 }}步</text>
                                                    <text class="iconfont icon-sync color-sub font14"></text>
                                                </view>
                                                <view class="flex-all-center">
                                                    <view v-if="is_joining"
                                                          class="join-btn bg-primary color-white"
                                                          @click="exchange(false)">
                                                        <text>{{ exchangeButtonText.text }}</text>
                                                        <text v-if="exchangeButtonText.mini_text"
                                                              class="pl5 font12">
                                                            ({{ exchangeButtonText.mini_text }})
                                                        </text>
                                                    </view>
                                                    <view
                                                        v-else
                                                        class="join-btn bg-primary color-white"
                                                        @click="joinActivity"
                                                    >参与活动
                                                    </view>
                                                </view>
                                                <view
                                                    v-if="detail.conf.active.map_types !== 101"
                                                    class="text-center ptm5 color-sub font14"
                                                    @click="toMap"
                                                >查看活动地图
                                                </view>
                                            </view>
                                        </template>
                                    </template>
                                </view>
                            </template>


                            <template v-if="model.type === 'activity_title'">
                                <view
                                    class="p10"
                                    :style="{ backgroundColor: model.data && model.data['background_color'] || '#ffffff' }"
                                >
                                    <view
                                        class="flex-kai"
                                        v-if="!(model.data && model.data['name_hide'] && model.data['organizer_hide'])"
                                    >
                                        <view>
                                            <view
                                                v-if="!model.data || !model.data['name_hide']"
                                                :style="{
                                            fontSize: (model.data && model.data['name_font'] && model.data['name_font'].size || 16) + 'px',
                                            color: model.data && model.data['name_font'] && model.data['name_font'].color || '#1c2438'
                                        }"
                                                @click="copy(id)"
                                            >{{ detail.name }}
                                            </view>
                                            <view
                                                v-if="detail.organizer && !model.data['organizer_hide']"
                                                :style="{
                                            fontSize: (model.data && model.data['organizer_font'] && model.data['organizer_font'].size || 16) + 'px',
                                            color: model.data && model.data['organizer_font'] && model.data['organizer_font'].color || '#495060'
                                        }"
                                            >主办方：{{ detail.organizer }}
                                            </view>
                                        </view>
                                        <!-- #ifndef H5 -->
                                        <view v-if="!rankSet['share_closed']" class="flex-all-center">
                                            <button
                                                class="share-btn"
                                                type="default"
                                                plain="true" size="mini"
                                                open-type="share"
                                            >
                                                <text
                                                    class="iconfont icon-share-1 font24"
                                                    :style="{ color: model.data['name_font'] && model.data['name_font'].color || '#1c2438' }"
                                                ></text>
                                            </button>
                                        </view>
                                        <!-- #endif -->
                                    </view>
                                    <view
                                        v-if="!model.data || !model.data['create_time_hide']"
                                        :style="{
                                    fontSize: (model.data && model.data['create_time_font'] && model.data['create_time_font'].size || 12) + 'px',
                                    color: model.data && model.data['create_time_font'] && model.data['create_time_font'].color || '#bbbec4'
                                }"
                                    >
                                        <text class="pr5">活动创建时间:</text>
                                        <uni-dateformat
                                            :date="detail.create_time"
                                            format="yyyy-MM-dd hh:mm:ss"
                                            :threshold="[0, 0]"
                                        />
                                    </view>
                                    <view
                                        v-if="!model.data || !model.data['begin_time_hide']"
                                        :style="{
                                    fontSize: (model.data && model.data['begin_time_font'] && model.data['begin_time_font'].size || 14) + 'px',
                                    color: model.data && model.data['begin_time_font'] && model.data['begin_time_font'].color || '#80848f'
                                }"
                                    >
                                        <text class="pr5">活动开始时间:</text>
                                        <uni-dateformat
                                            :date="detail.begin_time * 1000"
                                            format="yyyy-MM-dd hh:mm:ss"
                                            :threshold="[0, 0]"
                                        />
                                    </view>
                                    <view
                                        v-if="!model.data || !model.data['end_time_hide']"
                                        :style="{
                                    fontSize: (model.data && model.data['end_time_font'] && model.data['end_time_font'].size || 14) + 'px',
                                    color: model.data && model.data['end_time_font'] && model.data['end_time_font'].color || '#80848f'
                                }"
                                    >
                                        <text class="pr5">活动结束时间:</text>
                                        <uni-dateformat
                                            :date="detail.end_time * 1000"
                                            format="yyyy-MM-dd hh:mm:ss"
                                            :threshold="[0, 0]"
                                        />
                                    </view>
                                    <view
                                        v-if="!model.data || !model.data['all_time_hide']"
                                        :style="{
                                    fontSize: (model.data && model.data['all_time_font'] && model.data['all_time_font'].size || 14) + 'px',
                                    color: model.data && model.data['all_time_font'] && model.data['all_time_font'].color || '#80848f'
                                }"
                                    >
                                        <text class="pr5">活动时间总共:</text>
                                        <text>{{ activeDays }}</text>
                                    </view>
                                </view>

                                <free-version-tips :admin-userid="detail.userid"
                                                   :tips="active_more_data['active_page_top_tips'] || ''"/>

                            </template>


                            <template v-if="model.show && model.type === 'step_data'">
                                <view class="user-today-step bg-white flex-row"
                                      @click="diyPageClick(model.data)">

                                    <image
                                        :src="headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"/>
                                    <view class="pl10">
                                        <view style="padding: 5px 0;">
                                            <text class="color-content">{{ must_submit[0].value }}</text>
                                            <text class="iconfont icon-line-chart color-sub pl5"
                                                  @click="uniPopupOpen('step_info')"></text>
                                        </view>
                                        <view class="flex-row">
                                            <view @click.step="toExchangeCalendar">
                                                <text class="pr5 color-content">今日步数:</text>
                                                <text :class="today_step < 10000 ? 'color-success' : 'color-warning'">
                                                    {{ today_step }}
                                                </text>
                                            </view>
                                            <text class="iconfont icon-sync color-sub font14 pl5"
                                                  @click.stop="getMyStep(true)"></text>
                                        </view>
                                    </view>
                                    <view v-if="model.data.more_show" class="look-more color-primary">
                                        <text>查看更多</text>
                                        <uni-icons type="forward" color="#2d8cf0"/>
                                    </view>
                                </view>
                            </template>


                            <template v-if="model.type === 'icon_list'">
                                <view
                                    class="icon-list flex-row flex-wrap text-center bg-white"
                                    :style="{backgroundColor: model.data && model.data.background_color || '#ffffff'}"
                                >
                                    <template
                                        v-if="!rankSet['page_diy_set'] || !model.data || !model.data.list || !model.data.list.length">

                                        <view v-if="is_my_activity" class="icon-item" @click="toManage">
                                            <text class="iconfont font24 color-primary icon-setting"></text>
                                            <view class="color-sub font14 pt3">活动管理</view>
                                        </view>
                                        <view v-if="!is_joining" class="icon-item" @click="joinActivity">
                                            <text class="iconfont font24 color-primary icon-users"></text>
                                            <view class="color-sub font14 pt3">参与活动</view>
                                        </view>
                                        <view v-if="is_joining" class="icon-item"
                                              @click="uniPopupOpen('my_info')">
                                            <text
                                                class="iconfont font24 color-primary icon-personal-data"></text>
                                            <view class="color-sub font14 pt3">报名信息</view>
                                        </view>
                                        <view
                                            v-if="!detail.conf.active.activity_rules_hide || detail.content || news_detail"
                                            class="icon-item"
                                            @click="uniPopupOpen('activity_detail')"
                                        >
                                            <text class="iconfont font24 color-primary icon-feedback"></text>
                                            <view class="color-sub font14 pt3">活动说明</view>
                                        </view>


                                        <navigator
                                            class="icon-item"
                                            v-if="detail.conf.active.runOneByOne && detail.conf.active.runOneByOne.open && have_team"
                                            :url="`/pages/activity/relay-race/sort-setting?active_id=${id}&team_id=${team_id}`"
                                        >
                                            <text class="iconfont font24 color-primary icon-sort"></text>
                                            <view class="color-sub font14 pt3">接力顺序</view>
                                        </navigator>
                                        <view
                                            v-if="ranklistShow && !is_simple"
                                            class="icon-item"
                                            hover-class="navigator-hover"
                                            @click="toTopList()"
                                        >
                                            <text class="iconfont font24 color-primary icon-trophy"></text>
                                            <view class="color-sub font14 pt3">排行榜</view>
                                        </view>

                                        <view v-if="lightCityMapShow" class="icon-item"
                                              hover-class="navigator-hover" @click="toLightCityMap">
                                            <text class="iconfont font24 color-primary icon-map"></text>
                                            <view class="color-sub font14 pt3">点亮城市</view>
                                        </view>

                                        <view
                                            v-if="exchangeCalendarShow"
                                            @click="toExchangeCalendar"
                                            class="icon-item"
                                            hover-class="navigator-hover"
                                        >
                                            <text
                                                :class="'iconfont font24 color-primary icon-calendar-' + today_day"></text>
                                            <view class="color-sub font14 pt3">兑换日历</view>
                                        </view>


                                        <navigator v-if="rankSet['maxDailyStepSubmitReason']"
                                                   class="icon-item" :url="`/pages/activity/other/max-step-submit-list?my_self=1&id=${id}`">
                                            <text class="iconfont font24 color-primary icon-line-chart"></text>
                                            <view class="color-sub font14 pt3">步数补交记录</view>
                                        </navigator>

                                        <view
                                            v-if="rankSet['daily_digital_book']"
                                            class="icon-item" hover-class="navigator-hover"
                                            @click="toEveryDayCertificate">
                                            <text
                                                class="iconfont font24 color-primary icon-certificate"></text>
                                            <view class="color-sub font14 pt3">每日证书</view>
                                        </view>

                                        <template
                                            v-if="rankSet['redpack'] && !is_simple">

                                            <navigator class="icon-item"
                                                       :url="'/pages/wallet/redpack_list?id=' + id">
                                                <text
                                                    class="iconfont font24 color-primary icon-red-packet"></text>
                                                <view class="color-sub font14 pt3">
                                                    红包记录
                                                </view>
                                            </navigator>

                                            <!-- <navigator class="icon-item" url="/pages/wallet/withdraw">
                                                <uni-icons type="wallet" size="24" color="#2d8cf0"/>
                                                <view class="color-sub font14 pt3">提现</view>
                                            </navigator> -->
                                        </template>

                                        <view v-if="rankSet['gift_goods']" class="icon-item"
                                              @click="toShop(false)">
                                            <uni-icons type="shop" size="24" color="#2d8cf0"/>
                                            <view class="color-sub font14 pt3">
                                                {{
                                                    detail.conf.active.integral && detail.conf.active.integral.unit || '金币'
                                                }}商城
                                            </view>
                                        </view>


                                        <navigator v-if="dailySignShow" class="icon-item"
                                                   :url="'/pages/sign_in/sign_in?active_id=' + id">
                                            <text class="iconfont font24 color-primary icon-sign-in"></text>
                                            <view class="color-sub font14 pt3">每日签到</view>
                                        </navigator>

                                        <!-- <view
                                          v-if="!rankSet.reward_shop_closed"
                                          class="icon-item"
                                          @click="toShop(true)"
                                        >
                                          <text class="iconfont font24 color-primary icon-medal"></text>
                                          <view class="color-sub font14 pt3">荣誉奖章</view>
                                        </view> -->

                                        <view
                                            v-if="is_joining && (detail.conf.active.enter_types !== 3 || checked === 1) && rankSet.together_pic"
                                            class="icon-item"
                                            @click="toTogetherPicList()"
                                        >
                                            <uni-icons type="images" size="24" color="#2d8cf0"/>
                                            <view class="color-sub font14 pt3">照片合成</view>
                                        </view>

                                        <view
                                            v-if="is_joining && (detail.conf.active.enter_types !== 3 || checked === 1) && rankSet.AI_motion"
                                            class="icon-item"
                                            @click="toAiSportList()"
                                        >
                                            <text class="iconfont font24 color-primary icon-walk"></text>
                                            <view class="color-sub font14 pt3">AI运动</view>
                                        </view>


                                        <navigator
                                            v-if="detail.conf.active.map_types !== 101 && !detail.conf.active.certificate_close && (!detail.conf.active.runOneByOne || !detail.conf.active.runOneByOne.open) && !is_simple"
                                            class="icon-item"
                                            :url="'../other/certificate?id=' + id + '&finished_time=' + finished_time"
                                        >
                                            <text
                                                class="iconfont font24 color-primary icon-certificate"></text>
                                            <view class="color-sub font14 pt3">我的证书</view>
                                        </navigator>

                                        <view
                                            v-if="
                                        is_joining &&
                                        (detail.conf.active.enter_types !== 3 || checked === 1) &&
                                        rankSet['medal_open']  &&
                                        !is_simple
                                    "
                                            class="icon-item"
                                            hover-class="navigator-hover"
                                            @click="toMedalList"
                                        >
                                            <text class="iconfont font24 color-primary icon-medal"></text>
                                            <view class="color-sub font14 pt3">勋章墙</view>
                                        </view>

                                        <view v-if="examPrizeShow" class="icon-item"
                                              hover-class="navigator-hover" @click="toExamPrize">
                                            <template v-if="examPrizeIconDiy">

                                                <image class="icon" style="width: 24px; height: 24px;"
                                                       :src="examPrizeIconDiy.icon" mode="aspectFill"/>
                                                <view class="color-sub font14">
                                                    {{ examPrizeIconDiy.name }}
                                                </view>
                                            </template>
                                            <template v-else>
                                                <text class="iconfont font24 color-primary icon-medal"></text>
                                                <view class="color-sub font14 pt3">答题勋章</view>
                                            </template>
                                        </view>

                                        <template v-if="rankSet['lottery_open'] && !is_simple">

                                            <view class="icon-item" hover-class="navigator-hover"
                                                  @click="toLottery">
                                                <text class="iconfont font24 color-primary icon-gift"></text>
                                                <view class="color-sub font14 pt3">抽奖活动</view>
                                            </view>


                                            <navigator v-if="is_joining" class="icon-item" :url="'/pages/lottery/user/lottery_record_list?active_id=' + id">
                                                <text class="iconfont font24 color-primary icon-dating"></text>
                                                <view class="color-sub font14 pt3">中奖记录</view>
                                            </navigator>
                                        </template>


                                        <view v-if="blindBoxLotteryId" class="icon-item"
                                              hover-class="navigator-hover" @click="myBlindBoxList">
                                            <text class="iconfont font24 color-primary icon-gift"></text>
                                            <view class="color-sub font14 pt3">我的盲盒</view>
                                        </view>


                                        <template v-if="examOpen">
                                            <!--这个是默认的点位答题的-->
                                            <navigator v-if="detail.conf.active.map_types !== 101"
                                                       class="icon-item"
                                                       :url="'/pages/activity/other/answer/list?id=' + id + '&exam_show_type=' + (detail.conf.active.exam_show_type || 1)">
                                                <text
                                                    class="iconfont font24 color-primary icon-examination-paper"></text>
                                                <view class="color-sub font14 pt3">答题</view>
                                            </navigator>

                                            <!--这个是鲜繁定制开发的，每兑换1000步获得1次答题机会-->
                                            <view v-if="dailyStepUnlockExam" class="icon-item" hover-class="navigator-hover"
                                                  @click="dailyStepUnlockExamPopupShow">
                                                <text
                                                    class="iconfont font24 color-primary icon-examination-paper"></text>
                                                <view class="color-sub font14 pt3">答题</view>
                                            </view>
                                        </template>


                                        <navigator
                                            v-if="rankSet['open_sport_moment']"
                                            class="icon-item"
                                            :url="'/pages/comment/list?active_id=' + id"
                                        >
                                            <text
                                                class="iconfont font24 color-primary icon-wechat-movement"></text>
                                            <view class="color-sub font14 pt3">运动圈</view>
                                        </navigator>


                                        <view
                                            v-if="rankSet['party_oath'] && detail.conf.active.take_oath_bg_img"
                                            class="icon-item" hover-class="navigator-hover"
                                            @click="toPartyOath">
                                            <text class="iconfont font24 color-primary icon-flag"></text>
                                            <view class="color-sub font14 pt3">宣誓词</view>
                                        </view>


                                        <view v-if="study_news_category_id" class="icon-item"
                                              hover-class="navigator-hover" @click="studyNews">
                                            <text class="iconfont font24 color-primary icon-book"></text>
                                            <view class="color-sub font14 pt3">学习专区</view>
                                        </view>

                                        <view
                                            v-if="rankSet['face_swap'] && checked === 1"
                                            class="icon-item" hover-class="navigator-hover" @click="toAiFace">
                                            <uni-icons type="images" size="24" color="#2d8cf0"/>
                                            <view class="color-sub font14 pt3">AI换脸</view>
                                        </view>


                                        <view v-for="(item, index) in headimg_plugin" :key="index"
                                              class="icon-item" @click="toIdeaAvatar(item)">

                                            <image style="width: 24px; height: 24px;"
                                                   :src="item.icon || `https://apissl.xinweiyun.com/weixin/public/img/longmarch/icon_${item.key === 'chajian' ? 'guajian' : 'beijing'}.png`"/>

                                            <view class="color-sub font14" style="padding-top: 1px;">
                                                {{ item.name }}
                                            </view>
                                        </view>


                                        <view v-if="rankSet['barrage']" class="icon-item"
                                              @click="toBulletScreen()">
                                            <uni-icons type="chatboxes" size="24" color="#2d8cf0"/>
                                            <!-- <text class="iconfont font24 color-primary icon-empty-state"></text> -->
                                            <view class="color-sub font14 pt3">活动弹幕</view>
                                        </view>

                                        <view
                                            v-if="
                                        is_joining &&
                                        (detail.conf.active.enter_types !== 3 || checked === 1) &&
                                        !rankSet['closed_weekly_report'] &&
                                        !time_period_step  &&
                                        (!detail.conf.active.runOneByOne || !detail.conf.active.runOneByOne.open)
                                    "
                                            class="icon-item"
                                            @click="showWeekly()"
                                        >
                                            <text class="iconfont font24 color-primary icon-8"></text>
                                            <view class="color-sub font14 pt3">运动周报</view>
                                            <canvas
                                                class="weekly"
                                                canvas-id="weekly"
                                                id="weekly"
                                                :style="'width: ' + weekly_width + 'px; height: ' + weekly_height + 'px;'"
                                            ></canvas>
                                        </view>

                                        <template v-if="!rankSet['closedMapPointNotShow']">
                                            <view v-if="detail.conf.active.map_types !== 101 && !is_simple"
                                                  class="icon-item" @click="toMap">
                                                <text class="iconfont font24 color-primary icon-map-1"></text>
                                                <view class="color-sub font14 pt3">活动地图</view>
                                            </view>


                                            <navigator
                                                v-if="detail.conf.active.map_types !== 101 && !is_simple"
                                                class="icon-item"
                                                :url="'/pages/activity/other/point_step?id=' + id + '&unit=' + kiloUnit + '&point_knowledge_lock=' + (rankSet.point_knowledge_lock || '0')"
                                            >
                                                <text class="iconfont font24 color-primary icon-location-cute"></text>
                                                <view class="color-sub font14 pt3">地图点位</view>
                                            </navigator>
                                        </template>

                                        <template v-if="detail.conf.active.detail_icon_list">

                                            <navigator
                                                v-for="(item, index) in detail.conf.active.detail_icon_list"
                                                :key="index"
                                                class="icon-item"
                                                :url="item.type === 0 ? ('/pages/news/preview?id=' + item.id) : ('/pages/news/list?category_id=' + item.id)"
                                            >
                                                <text
                                                    :class="'iconfont font24 color-primary ' + item.icon"></text>
                                                <view class="color-sub font14 pt3">{{ item.title }}</view>
                                            </navigator>
                                        </template>


                                        <navigator
                                            v-if="!rankSet['today_history_closed']"
                                            class="icon-item"
                                            :url="'/pages/other/today_in_history' + (rankSet.closed_AD ? '?close_ad=1' : '')"
                                        >
                                            <text class="iconfont font24 color-primary icon-today"></text>
                                            <view class="color-sub font14 pt3">历史上的今天</view>
                                        </navigator>


                                        <navigator
                                            v-if="!rankSet['today_news_closed']"
                                            class="icon-item"
                                            :url="'/pages/other/today-news?active_id=' + id + (rankSet.closed_AD ? '&close_ad=1' : '')"
                                        >
                                            <text class="iconfont font24 color-primary icon-news"></text>
                                            <view class="color-sub font14 pt3">今日简讯</view>
                                        </navigator>


                                        <navigator
                                            v-if="!detail.conf.active.close_feedback"
                                            class="icon-item"
                                            :url="'/pages/activity/feedback/send?activity_id=' + id"
                                        >
                                            <text
                                                class="iconfont font24 color-primary icon-chat-bubble"></text>
                                            <view class="color-sub font14 pt3">意见反馈</view>
                                        </navigator>

                                        <view v-if="!rankSet['share_closed']"
                                              class="icon-item" @click="showActiveSharePopup">
                                            <text class="iconfont font24 color-primary icon-share"></text>
                                            <view class="color-sub font14 pt3">分享</view>
                                        </view>
                                    </template>
                                    <template v-else>
                                        <template v-for="(icon_item, icon_index) in model.data.list">
                                            <view
                                                v-if="icon_item.show"
                                                class="icon-item"
                                                :key="icon_index"
                                                :style="{width: 'calc(100% / ' + model.data['one_line_count'] + ')'}"
                                                @click="diyPageClick(icon_item)"
                                            >
                                                <image
                                                    class="icon"
                                                    :src="icon_item.image"
                                                    mode="aspectFill"
                                                    :style="{
                                                width: model.data['image_width'] + 'px',
                                                height: model.data['image_width'] + 'px',
                                                borderRadius: model.data['image_radius'] + '%'
                                            }"
                                                />
                                                <view
                                                    class="color-sub font14"
                                                    :style="{
                                                color: model.data['title_color'] || '#80848f',
                                                fontSize: (model.data['title_size'] || 14) + 'px'
                                             }"
                                                >{{ icon_item.title }}
                                                </view>
                                            </view>
                                        </template>
                                    </template>
                                </view>
                            </template>

                            <template v-if="model.show && model.type === 'text' && model.data">
                                <view :style="{
                            color: model.data.color,
                            backgroundColor: model.data.background_color,
                            fontSize: model.data['font_size'] + 'px',
                            lineHeight: model.data['line_height'],
                            paddingLeft: model.data.padding + 'px',
                            paddingRight: model.data.padding + 'px',
                            textAlign: model.data['text_align']
                        }">
                                    <text space="nbsp" style="word-break:break-all;">{{
                                            model.data.text
                                        }}
                                    </text>
                                </view>
                            </template>

                            <template v-if="model.show && model.type === 'button' && model.data">
                                <view class="flex-all-center">
                                    <button
                                        class="diy-page-button"
                                        :class="{ 'border-none': !model.data['border_show'] }"
                                        :style="{
                                    width: model.data.width + 'px',
                                    height: model.data.height + 'px',
                                    lineHeight: model.data.height + 'px',
                                    backgroundColor: model.data.bgcolor,
                                    color: model.data.text_color,
                                    fontSize: model.data['font_size'] + 'px',
                                    borderRadius: model.data.height * (model.data['border_radius'] / 100) + 'px',
                                    border: model.data['border_show'] ? `${model.data['border_width']}px ${model.data['border_style']} ${model.data['border_color']}` : 'none'
                                }"
                                        @click="diyPageClick(model.data)"
                                    >{{ model.data.text }}
                                    </button>
                                </view>
                            </template>
                        </view>


                        <xwy-ad
                            v-if="
                        !exchange_success_info &&
                        !password_dialog_show &&
                        !join_popup_show &&
                        !popup_open &&
                        !rankSet.closed_AD
                    "
                            :ad_type="4"
                        ></xwy-ad>

                        <xwy-ad v-if="!rankSet.closed_AD && !screen_pic_show" :ad_type="3"></xwy-ad>

                        <view v-if="is_simple && ranklistShow && top_list.length" class="top-list bg-white">
                            <view class="flex-kai" style="border-bottom: 1px solid #eee;">
                                <view class="top-list-title p10 color-content">排行榜</view>
                                <view class="p10 color-primary font14" @click="toTopList">
                                    <text>查看更多</text>
                                    <uni-icons type="forward" color="#2d8cf0" size="14"/>
                                </view>
                            </view>

                            <view v-for="(item, index) in top_list" :key="index"
                                  class="top-list-item flex-row bg-white">
                                <view class="top-list-index flex-all-center">

                                    <image
                                        class="top-list-top-3"
                                        v-if="index < 3"
                                        :src="'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/no-' + (index + 1) + '.png'"
                                    />
                                    <view v-else class="color-sub font14">{{ index + 1 }}</view>
                                </view>

                                <view class="flex-all-center">
                                    <view>
                                        <image v-if="item.headimg" mode="aspectFill" class="top-list-headimg"
                                               :src="item.headimg"/>
                                        <view v-else
                                              class="top-list-headimg bg-background color-sub font24 flex-all-center">
                                            {{ item.nickname[0] }}
                                        </view>
                                    </view>
                                </view>

                                <view class="top-list-middle">
                                    <view class="top-list-name color-title ellipsis">{{
                                            item.nickname
                                        }}
                                    </view>
                                </view>

                                <view class="top-list-right flex-column flex-all-center color-red font14">
                                    <view class="top-list-right-item text-right">
                                        <view>
                                            <text>{{ item.step }}</text>
                                            <text class="font12" style="padding-left: 2px;">
                                                {{ kiloUnit }}
                                            </text>
                                        </view>
                                    </view>

                                </view>
                            </view>

                            <xwy-ad v-if="!rankSet.closed_AD && top_list.length >= 10" :ad_type="66"></xwy-ad>
                        </view>


                        <view v-if="page_diy_model.length" class="pb10" style="padding-top: 30px;">
                            <view v-if="!rankSet['closed_user_center']" class="flex-all-center">
                                <navigator url="/pages/user/user" class="p10 color-sub font14">个人中心
                                </navigator>
                            </view>

                            <view v-if="technology_support" class="flex-all-center">

                                <navigator
                                    v-if="technology_support.news_id"
                                    :url="'/pages/news/preview?id=' + technology_support.news_id + '&type=support'"
                                    class="text-center font14 color-sub p10"
                                >{{ technology_support.button_text }}
                                </navigator>
                                <view v-else class="text-center font14 color-sub p10">
                                    {{ technology_support.button_text }}
                                </view>
                            </view>
                        </view>
                    </view>

                    <diy-image
                        v-if="diy_show_type === 'image' && diy_image_bg_path"
                        :width="canvas_width"
                        :height="canvas_height"
                        :bg-path="diy_image_bg_path"
                        :list="diy_image_list"
                        :step="today_step"
                        @diyPageClick="diyPageClick"
                    />
                </template>
            </template>

            <template v-if="detail.conf.active.enter_activity_popup_news && detail.conf.active.enter_activity_popup_news.id">
                <enter-activity-news-popup ref="enterActivityNewsPopup"
                                           :news-id="detail.conf.active.enter_activity_popup_news.id"/>
            </template>

        </view>

        <view
            v-if="join_popup_show"
            class="join-popup flex-all-center bg-white"
            @touchmove.stop.prevent=""
        >
            <view>
                <view class="join-popup-c bg-white">
                    <view class="text-center font18 color-content p10">
                        <template v-if="update_attend_details">修改报名信息</template>
                        <template v-else>参加活动</template>
                    </view>


                    <template v-if="must_submit.length">
                        <template v-for="(item, index) in must_submit">
                            <template
                                v-if="!update_attend_details || !detail.conf.active.must_submit_not_modifiable || !item.value">

                                <input
                                    v-if="item.types === 1 || item.types === 3"
                                    :key="index"
                                    class="join-input"
                                    v-model="item.value"
                                    :placeholder="'请输入' + item.title + (item.rules === 1 ? ' (必填)' : '')"
                                />
                                <picker
                                    v-if="item.types === 2"
                                    :range="item.options"
                                    range-key="text"
                                    @change="mustValueChange($event, index)"
                                >
                                    <view class="join-input flex-kai">
                                        <view v-if="!item.value" class="color-sub">
                                            请选择{{ item.title }}{{ item.rules === 1 ? ' (必选)' : '' }}
                                        </view>
                                        <view v-if="item.value">{{ item.value }}</view>
                                        <text class="iconfont icon-more color-disabled font18"/>
                                    </view>
                                </picker>
                            </template>
                        </template>
                    </template>


                    <navigator
                        v-if="!detail.conf.active.closed_team && (!have_team || detail.conf.active.allow_change_team)"
                        :url="'/pages/activity/admin/team_list?selteam=1&id=' + id"
                        class="join-input flex-kai"
                    >
                        <view v-if="!team_id" class="color-sub">
                            <text>请选择队伍</text>
                            <text v-if="detail.conf.active.team_required" class="pl5">(必选)</text>
                        </view>
                        <view v-if="team_id">{{ team_name || team_id }}</view>
                        <text class="iconfont icon-more color-disabled font18"/>
                    </navigator>

                    <!-- <input
                        v-if="!update_attend_details && detail.conf.active.password"
                        class="join-input"
                        password
                        v-model="password"
                        placeholder="请输入活动密码"
                    /> -->

                    <view class="join-popup-btns flex-row text-center font18">
                        <view class="join-popup-btn-cancel color-sub" @click="cancelJoin">取消</view>
                        <view class="join-popup-btn-confirm color-success" @click="joinAjax">确定</view>
                    </view>
                </view>

                <template v-if="!rankSet.closed_AD">
                    <view class="pt5">
                        <xwy-ad :ad_type="66"></xwy-ad>
                    </view>
                    <xwy-ad :ad_type="3"></xwy-ad>
                </template>
            </view>
        </view>

        <active-share ref="activeShare"/>
        

        <uni-popup ref="activity_detail" type="center" @touchmove.stop.prevent="">
            <view
                v-if="detail && detail.conf && detail.conf.active"
                class="uni-popup-info detail-popup bg-white"
            >
                <view class="popup-close" @click="uniPopupClose('activity_detail')">
                    <uni-icons type="close" size="28" color="#b2b3b7"/>
                </view>
                <scroll-view
                    scroll-y="true"
                    class="detail-popup-detail"
                    style="max-height: calc(100vh - 200px); padding: 10px 0;"
                >
                    <view v-if="!detail.conf.active.activity_rules_hide" class="p10 bdb-10 bg-white">
                        <view class="text-center color-sub pb5">- 活动规则 -</view>
                        <!-- <view v-if="detail.arrive_end_num" class="color-content font16">
							终点里程：{{detail.arrive_end_num}}{{detail.active_set.unit}}
                        </view> -->
                        <view class="color-content font16">
                            活动参与方式：
                            <template v-if="detail.conf.active.enter_types === 1">
                                {{ enter_types_1_text }}
                            </template>
                            <template v-if="detail.conf.active.enter_types === 2">
                                需要输入密码才能报名
                            </template>
                            <template v-if="detail.conf.active.enter_types === 3">
                                报名需要审核通过才能参与活动
                            </template>
                        </view>
                        
                        <view v-if="ipSetOpen" class="color-content font16">
                            <text>城市限制：{{detail.conf.active.ip_set.ip_city.join('、')}} 用户可参与活动</text>
                        </view>

                        <view v-if="joinDistanceTips" class="color-content font16">
                            位置限制：{{ joinDistanceTips }}
                        </view>

                        <view v-if="detail.conf.active.exchange_rate && !time_period_step"
                              class="color-content font16">
                            兑换比例：
                            <template>
                                <template v-if="detail.conf.active.exchange_rate === 1 && kiloUnit === '步'">
                                    统计每日行走步数
                                </template>
                                <template v-else>
                                    {{ detail.conf.active.exchange_rate }}步兑换1{{ kiloUnit }}
                                </template>
                            </template>
                        </view>
                        <template v-if="!detail.conf.active.runOneByOne || !detail.conf.active.runOneByOne.open">
                            <view v-if="time_period_step" class="color-content font16">
                                兑换规则：请在活动时间内开始记录和提交步数，活动结束后无法记录和提交。活动时间请关闭活动规则弹窗后在页面查看。
                            </view>
                            <view v-if="!time_period_step" class="color-content font16">
                                <text>兑换规则：</text>
                                <text>
                                    <template v-if="rankSet['thirty_day_auto_exchange']">
                                        自动兑换30天步数
                                    </template>
                                    <template v-else>每天兑换当天步数</template>
                                </text>
                            </view>
                            <view
                                v-if="
                                rankSet.exam_open &&
                                detail.conf.active.must_exam &&
                                detail.conf.active.must_exam.open &&
                                detail.conf.active.must_exam.open === 1
                            "
                                class="color-content font16"
                            >
                                <text v-if="detail.conf.active.must_exam.score">
                                    需要答题成绩达到{{ detail.conf.active.must_exam.score }}分才能兑换下一点位
                                </text>
                                <text v-else>需要答题才能兑换下一点位</text>
                            </view>
                            <view v-if="detail.conf.active.max_num" class="color-content font16">
                                每日兑换上限：{{ detail.conf.active.max_num }}步
                            </view>
                            <view v-if="detail.conf.active.min_num" class="color-content font16">
                                最低兑换步数：{{ detail.conf.active.min_num }}步
                            </view>
                            <view v-if="detail.conf.active.map_types !== 101" class="color-content font16">
                                到达终点是否可继续兑换：{{ detail.conf.active.arrive_then_end === 1 ? '不' : '' }}可继续兑换
                            </view>
                        </template>

                        <template v-if="detail.conf.active.runOneByOne && detail.conf.active.runOneByOne.open">
                            <view class="color-content font16">活动类型：队伍接力赛</view>
                            <view class="color-content font16">
                                每个队伍最多{{ detail.conf.active.runOneByOne.all_person }}棒接力选手
                            </view>
                            <view class="color-content font16">
                                每棒接力选手需要接力{{ detail.conf.active.runOneByOne.per_person_exchange_kilo }}{{ kiloUnit }}
                            </view>
                        </template>

                        <view
                            v-if="detail.conf.active.exchange_start_time"
                            class="color-content font16"
                        >
                            每日兑换开始时间：{{ detail.conf.active.exchange_start_time }}
                        </view>
                        <view
                            v-if="detail.conf.active.exchange_end_time && !time_period_step"
                            class="color-content font16"
                        >
                            每日兑换截止时间：{{ detail.conf.active.exchange_end_time }}
                        </view>

                        <template v-if="rankSet.exam_open">
                            <view
                                v-if="
                                    rankSet.reward_step_exam_conf &&
                                    detail.conf.active.reward_step_by_exam &&
                                    detail.conf.active.reward_step_by_exam.open &&
                                    detail.conf.active.reward_step_by_exam.open === 1
                                "
                                class="color-content font16"
                            >
                                <text>答题奖励规则：</text>
                                <template v-if="detail.conf.active.reward_step_by_exam.types === 1">
                                    <text>
                                        每{{ detail.conf.active.reward_step_by_exam.score_one_point }}分,
                                    </text>
                                    <text>
                                        奖励{{ detail.conf.active.reward_step_by_exam.reward_step }}步
                                    </text>
                                    <text
                                        v-if="detail.conf.active.reward_step_by_exam.min_score"
                                        class="font14 color-sub"
                                    >
                                        (需达到{{ detail.conf.active.reward_step_by_exam.min_score }}分才奖励)
                                    </text>
                                </template>
                                <template v-if="detail.conf.active.reward_step_by_exam.types === 2">
                                    <text>
                                        每答对1题,奖励{{ detail.conf.active.reward_step_by_exam.reward_step }}步
                                    </text>
                                </template>
                            </view>
                        </template>
                        
                        <view v-if="rankSet['person_finished_percent'] && detail.conf.active.finished_step_num"
                              class="color-content font16">
                            每日目标：{{ detail.conf.active.finished_step_num }}步
                        </view>
                    </view>

                    <view v-if="detail.content || news_detail" class="p10 bdb-10 bg-white">
                        <view class="text-center color-sub pb5">- 活动说明 -</view>
                        <view class="color-content font16">
                            <template v-if="detail.content && !news_detail">
                                <rich-text :nodes="detail.content" space="nbsp" selectable="true"></rich-text>
                            </template>
                            <template v-if="news_detail">
                                <!-- <view v-if="news_detail.video_url" class="video">
                                  <video
                                    v-if="news_detail.video_type === 'http'"
                                    :src="news_detail.video_url"
                                    style="width: 100%;"
                                  ></video>
                                  <txv-video
                                    v-if="news_detail.video_type === 'txv_id'"
                                    :vid="news_detail.video_url"
                                    playerid="playerid"
                                    :autoplay="false"
                                  ></txv-video>
                                </view> -->
                                <template v-if="news_detail.content">
                                    <u-parse :content="news_detail.content"/>
                                </template>
                            </template>
                        </view>
                    </view>
                    <xwy-ad v-if="!loading && !rankSet.closed_AD" :ad_type="66"/>
                </scroll-view>
            </view>
        </uni-popup>

        <uni-popup ref="my_info" type="center" @touchmove.stop.prevent="">
            <view v-if="detail && detail.conf && detail.conf.active" class="uni-popup-info bg-white p20">
                <view class="popup-close" @click="uniPopupClose('my_info')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="text-center color-sub pb5">- 活动报名信息 -</view>
                <view class="text-center p10">

                    <image
                        class="headimg"
                        :src="headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"
                        mode="aspectFill"
                    />
                    <view class="flex-all-center">
                        <view class="color-primary" @click="updateHeadimg">更改头像</view>
                        <template 
                            v-if="rankSet['activity_total'] && !wx_h5_openid">
                            <view class="pl10 pr10 color-sub">|</view>
                            <view class="color-primary" @click="bindWechatH5Openid">绑定提醒</view>
                        </template>
                    </view>
                </view>
                <template v-if="rankSet['gift_goods']">

                    <navigator
                        class="color-content font16 ptm5"
                        :url="'/pages/sign_in/integral_record_list?active_id=' + id + '&unit=' + (detail.conf.active.integral && detail.conf.active.integral.unit || '金币')"
                    >
                        累计{{ detail.conf.active.integral && detail.conf.active.integral.unit || '金币' }}：{{ user_details['integral_all'] || 0 }}
                    </navigator>


                    <navigator
                        v-if="!rankSet.together_pic"
                        class="color-content font16 ptm5"
                        :url="'/pages/sign_in/integral_record_list?active_id=' + id + '&unit=' + (detail.conf.active.integral && detail.conf.active.integral.unit || '金币')"
                    >
                        剩余{{ detail.conf.active.integral && detail.conf.active.integral.unit || '金币' }}：{{ user_details['integral_left'] || 0 }}
                    </navigator>
                </template>

                <!--雷子客户定制不显示累计里程-->
                <template v-if="id !== 'beb06bf8a320124c5d9d084be54d6fdd'">
                    <view v-if="!time_period_step" class="color-content font16 ptm5">
                        累计里程：{{ user_exchange_num }}{{ kiloUnit }} ({{ signdays }}天)
                    </view>
                </template>


                <navigator v-if="rankSet['maxDailyStepSubmitReason']"
                      class="color-content font16 ptm5"
                           :url="`/pages/activity/other/max-step-submit-list?my_self=1&id=${id}`">
                    步数补交记录：
                    <text class="color-light-primary">查看记录</text>
                </navigator>
                <view v-if="time_period_step && time_period_step_today_submit === 'end'" class="color-content font16 ptm5">
                    提交步数：{{ time_period_step_today_submit_step }}步
                </view>
                <view v-if="answer_score" class="color-content font16 ptm5">
                    累计答题分数：{{ answer_score }}分
                </view>

                <!--雷子客户定制不显示到达终点时间-->
                <template v-if="id !== 'beb06bf8a320124c5d9d084be54d6fdd'">
                    <view v-if="finished_time" class="color-content font16 ptm5">
                        <text>到达终点时间：</text>
                        <uni-dateformat :date="finished_time * 1000" format="yyyy-MM-dd hh:mm:ss"
                                        :threshold="[0, 0]"/>
                    </view>
                </template>


                <view v-if="!detail.conf.active.closed_team" class="color-content font16 ptm5">
                    <template>
                        <!--雷子客户活动，队伍需要显示为所在部门-->
                        <text v-if="id === '872275c91a76dc8a58ffa35033c48f24'">
                            所在部门：{{ team_name || team_id || '未加入队伍' }}
                        </text>
                        <text v-else>队伍：{{ team_name || team_id || '未加入队伍' }}</text>
                    </template>

                    <template v-if="!team_id">
                        <text class="color-primary pl5" @click="updateAttendDetailShow">加入队伍</text>
                        <uni-icons type="forward" color="#2d8cf0" @click="updateAttendDetailShow"/>
                    </template>
                    <template v-if="team_id && detail.conf.active.allow_change_team">
                        <text class="color-primary pl5" @click="updateAttendDetailShow">更换队伍</text>
                        <uni-icons type="forward" color="#2d8cf0" @click="updateAttendDetailShow"/>
                    </template>
                    <template v-if="user_details && user_details.team_id && rankSet['team_manager']">
                        <text class="color-primary pl5" @click="teamManage">队伍管理</text>
                        <uni-icons type="forward" color="#2d8cf0" @click="teamManage"/>
                    </template>
                </view>
                <view v-if="rankSet['show_team_list']" class="color-content font16 ptm5">
                    队伍里程：{{ user_details['team_details'] && user_details['team_details'].all_kilo || 0 }}{{ kiloUnit }} ({{ user_details['team_details'] && user_details['team_details'].sign_day || 0 }}天)
                </view>
                <view
                    class="color-content font16 ptm5"
                    v-for="(item, index) in must_submit"
                    :key="index"
                >
                    <text>
                        {{ item.title }}：
                        <template>
                            <template v-if="item.value">{{ item.value }}</template>
                            <template v-else>
                                <template v-if="item.types === 1">未填写</template>
                                <template v-if="item.types === 2">未选择</template>
                            </template>
                        </template>
                    </text>
                    <text
                        v-if="!detail.conf.active.must_submit_not_modifiable || !item.value"
                        class="iconfont icon-edit color-sub pl5"
                        @click="updateAttendDetailShow"
                    ></text>
                </view>

                <view v-if="user_details.registration_name" class="color-content font16 ptm5">
                    报名项目：{{ user_details.registration_name }}
                </view>

                <template v-if="popup_open && !rankSet.closed_AD">
                    <view style="position: relative; left: -10px;">
                        <xwy-ad :ad_type="66"></xwy-ad>
                    </view>
                    <xwy-ad :ad_type="3"></xwy-ad>
                </template>
            </view>
        </uni-popup>

        <uni-popup ref="step_info" type="center" @touchmove.stop.prevent="">
            <view v-if="popup_open" class="uni-popup-info step-info-popup bg-white">
                <view class="popup-close" @click="uniPopupClose('step_info')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>


                <view class="flex-center p10">
                    <view class="today-step-quanquan flex-all-center flex-column">
                        <view class="font18 color-warning font18">{{ today_step }}</view>
                        <view class="color-sub font14">今日步数</view>
                    </view>
                    <view class="kaluli-quanquan flex-all-center flex-column">
                        <view class="font18 color-warning font18">
                            {{ Number((today_step * 0.0425).toFixed(2)) }}
                        </view>
                        <view class="color-sub font14">消耗卡路里</view>
                    </view>
                </view>

                <view class="charts-box">
                    <qiun-data-charts type="column" :opts="chartOpts" :chartData="chartData"/>
                </view>

                <template v-if="!rankSet.closed_AD">
                    <xwy-ad :ad_type="66"></xwy-ad>
                    <xwy-ad :ad_type="3"></xwy-ad>
                </template>

            </view>
        </uni-popup>

        <view v-if="exchange_success_info" class="success flex-column flex-all-center text-center">
            <view class="main">
                <view class="top bg-primary color-white" style="padding: 50px 20px 20px;">
                    <icon type="success" size="80" color="#ffffff"></icon>
                    <view class="font18">{{ exchange_success_info }}</view>
                </view>
                <view class="bottom2 bg-white" style="padding: 20px 20px 30px;">
                    <!-- <view>{{exchange_success_info}}</view> -->
                    <view v-if="exchange_success_tip_text_list.length">
                        <view
                            v-for="(item, index) in exchange_success_tip_text_list"
                            :key="index"
                            :style="'color: ' + item.color"
                            class="pt5"
                        >
                            <text
                                v-if="item.is_redpack"
                                class="iconfont icon-red-packet pr5"
                                :style="'color: ' + item.color"
                            ></text>
                            <text>{{ item.text }}</text>
                            <view v-if="item.is_redpack && item.amount" class="pt5">
                                <text class="font14">￥</text>
                                <text class="font18">{{ item.amount }}</text>
                                <text class="font14">元</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <view class="pt5"></view>
            <xwy-ad v-if="!rankSet.closed_AD" :ad_type="66"></xwy-ad>
            <view class="cancel text-center">
                <icon
                    type="cancel"
                    color="#bbbec4"
                    size="28"
                    @click="exchangeSuccessPopupClose"
                />
            </view>

            <xwy-ad v-if="!rankSet.closed_AD" :ad_type="3"></xwy-ad>
        </view>

        <max-daily-step-submit-popup v-if="maxDailyStepSubmitReasonOpen" ref="maxDailyStepSubmitReason"
                                     :step="today_step" :active-id="id"
                                     @close="maxDailyStepSubmitReasonPopupClose"/>


        <template v-if="password_dialog_show">
            <uni-popup
                ref="input_password"
                type="dialog"
                mode="input"
                :is-mask-click="false"
                @maskClick="copyMapType()"
            >
                <uni-popup-dialog
                    mode="input"
                    title="活动密码"
                    :value="password"
                    placeholder="请输入活动密码"
                    @confirm="passwordInputConfirm"
                    @close="passwordInputClose"
                ></uni-popup-dialog>
            </uni-popup>
        </template>


        <view class="notice-popup">
            <uni-popup ref="notice_popup" type="center" @touchmove.stop.prevent="" :is-mask-click="false">
                <view
                    v-if="detail.conf.active.active_details_notice && detail.conf.active.active_details_notice.news_detail && detail.conf.active.active_details_notice.news_detail.id"
                    class="bg-white"
                    style="width: 320px; border-radius: 10px;"
                >
                    <view class="text-center color-title p10" style="border-bottom: 1px solid #eee;">
                        {{ detail.conf.active.active_details_notice.news_detail.title || '活动须知' }}
                    </view>
                    <scroll-view
                        scroll-y="true"
                        class="color-content"
                        style="height: calc(85vh - 170px); margin: 10px; width: 300px;"
                    >
                        <u-parse :content="detail.conf.active.active_details_notice.news_detail.content || ''"/>
                    </scroll-view>

                    <view class="flex-row pt10 pl5" style="border-top: 1px solid #eee;">
                        <view @click="noticeAgreeChange">
                            <radio
                                :checked="detail.conf.active.active_details_notice.agree"
                                style="transform: scale(.7); position: relative; top: -3px; right: -3px;"
                            />
                        </view>
                        <view class="font14 color-sub">
                            我已阅读并同意《{{ detail.conf.active.active_details_notice.news_detail.title || '活动须知' }}》。
                        </view>
                    </view>
                    <view class="flex-all-center pb10">
                        <view
                            class="color-white text-center"
                            :class="{
                            'bg-primary': detail.conf.active.active_details_notice.agree,
                            'bg-disabled': !detail.conf.active.active_details_notice.agree
                        }"
                            style="line-height: 40px; border-radius: 20px; width: 200px;"
                            @click="noticeConfirm"
                        >{{ detail.conf.active.active_details_notice.confirm_text || "确定" }}
                        </view>
                    </view>
                    <view v-if="rankSet['shield_other']" class="flex-all-center pb10">
                        <navigator class="color-sub font14 pl10 pr10" url="/pages/user/user">个人中心</navigator>
                    </view>
                </view>
            </uni-popup>
        </view>


        <template v-if="tabbar_data">
            <activity-tabbar :value.sync="tabbar_data"></activity-tabbar>
        </template>


        <iup ref="input_username_password" :labels="detail.conf.active.batch_import_label"></iup>

        <registration ref="registration" v-if="rankSet['active_pay']"/>

        <expiration-reminder ref="expirationReminder"/>

        <finished-popup 
            ref="finishedPopup" 
            v-if="detail" 
            :detail="detail" 
            :headimg="headimg"
            :must_submit="must_submit"
            :finished_num="finished_num"
            :finished_time="finished_time"
            :signdays="signdays"
            :user_exchange_num="user_exchange_num"
        />

        <today-news-popup ref="todayNewsPopup" v-if="active_more_data['active_conf_set']" :active-id="id"
                          :set="active_more_data['active_conf_set']" />

        <every-day-certificate-popup ref="everyDayCertificatePopup"/>

        <daily-step-unlock-exam-popup
            v-if="dailyStepUnlockExam"
            ref="dailyStepUnlockExamPopup"
            :active-id="id" :exam-id="detail.conf.active.must_exam_daily.exam_id"
            :per-step="detail.conf.active.must_exam_daily.per_step"
            :headimg="headimg || ''"
            :nickname="must_submit[0] && must_submit[0].value || ''"
            :max-integral="detail.conf.active.must_exam_daily.max_integral || 0"
            :max-integral-tips="active_more_data.active_conf_set.must_exam_daily_max_integral_tips || ''"
            @answerEnd="dailyStepUnlockExamEnd"
        />
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import my_storage from '@/utils/storage.js'
import base64 from '@/utils/base64.js'
import bs_socket from '@/utils/bullet_screen_socket.js'
import uniapp_api from '@/utils/api/uniapp_api'


import lffBarrage from '@/components/lff-barrage.vue'
import inputUsernamePassword from '@/components/input-username-password.vue'

import activityTabbar from '../components/activity-tabbar.vue'
import freeVersionTips from '../components/free-version-tips.vue'
import finishedPopup from '../components/finished-popup.vue'
import todayNewsPopup from '../components/today-news-popup.vue'
import everyDayCertificatePopup from '../components/every-day-certificate-popup.vue'
import diyImage from '../components/diy-image/diy-image.vue'
import defaultTemplateConfig from '../admin/detail-page-template/default-template'
import stepUnlockPictureFragmentsShow from "../components/step-unlock-picture-fragments-show.vue"
import maxDailyStepSubmitPopup from "../components/max-daily-step-submit-popup.vue"
import enterActivityNewsPopup from '../components/enter-activity-news-popup'
import diyMap from '../components/diy-map/diy-map.vue'
import day7StepTemplate from '../components/day7-step-template/day7-step-template.vue'
import dailyStepUnlockExamPopup from '../components/daily-step-unlock-exam-popup.vue'

let interval
let innerAudioContext

export default {
    components: {
        lffBarrage,
        activityTabbar,
        iup: inputUsernamePassword,
        freeVersionTips,
        finishedPopup,
        todayNewsPopup,
        everyDayCertificatePopup,
        diyImage,
        stepUnlockPictureFragmentsShow,
        maxDailyStepSubmitPopup,
        enterActivityNewsPopup,
        diyMap,
        day7StepTemplate,
        dailyStepUnlockExamPopup
    },
    data() {
        return {
            today_day: new Date().getUTCDate(),
            evn_version: app.globalData['evn_version'],
            loading: true,
            today_step: null,
            screen_pic: '',
            screen_pic_show: false,
            screen_pic_hide: false,
            screen_pic_count_down: 5,
            screen_pic_buttom_show: null,
            id: '',
            userid: '',
            is_my_activity: false,
            detail: {
                conf: {
                    active: {}
                }
            },
            user_details: {},
            audio_play: false,
            error: '',
            join_popup_show: false,
            update_attend_details: false,
            have_team: false,
            team_id: '',
            headimg: '',
            team_name: '',
            username: '',
            checked: 0,
            finished_time: '',
            finished_num: 0,
            is_joining: false,
            password: '',
            technology_support: false,
            must_submit: [],
            user_exchange_num: 0,
            answer_score: null,
            signdays: 0,
            exchange_success_info: "",
            exchange_success_tip_text_list: [],
            headimg_plugin: [],
            password_dialog_show: false,
            popup_open: false,
            news_detail: null,
            chartData: {},
            chartOpts: {
                color: [
                    "#FAC858",
                    "#EE6666",
                    "#FAC858",
                    "#EE6666",
                    "#73C0DE",
                    "#3CA272",
                    "#FC8452",
                    "#9A60B4",
                    "#ea7ccc"
                ],
                padding: [15, 10, 0, 15],
                legend: {},
                xAxis: {
                    disableGrid: true
                },
                yAxis: {
                    // disabled: true,
                    // disableGrid: true,
                    gridType: 'dash',
                    data: [
                        {
                            min: 0
                        }
                    ]
                },
                extra: {
                    column: {
                        type: "group",
                        width: 25,
                        activeBgColor: "#000000",
                        activeBgOpacity: 0.08,
                        linearType: "custom",
                        seriesGap: 5,
                        linearOpacity: 0.5,
                        barBorderCircle: true,
                        customColor: [
                            "#FA7D8D"
                        ]
                    }
                }
            },
            weekly_width: 0,
            weekly_height: 0,
            join_data: {
                username: '',
                password: ''
            },
            page_diy_model: [],
            diy_show_type: '',
            canvas_height: 0,
            diy_image_bg_path: '',
            diy_image_list: [],
            tabbar_data: null,
            page_background_color: '#f8f8f8',

            /*
            报名方式1，自由报名参与活动的设置，有的活动要个性化设置这个
            "active_create":{  在后台json里面设置
              "everyone_text":"仅限深圳总部员工参与"
            }
            在活动详情接口(front.flat.sport_step.active_list/active_details)里读取
            active_more_data.active_conf_set.active_create.everyone_text
            */
            enter_types_1_text: '自由报名参与活动',

            time_period_step: false,   // 活动是否开启了统计某一天指定时间段步数
            time_period_step_today_submit: '',   // 活动是否开启了统计某一天指定时间段步数，今天的提交状态 '' || 'start' || 'end'
            time_period_step_today_submit_step: 0,
            time_period_step_today_start_step: 0,

            is_simple: true,   // 是否简约版
            top_list: [],      // 简约版的排行榜

            wx_h5_openid: '',
            
            active_more_data: {},

            diyMapSet: {}
        }
    },
    
    computed: {
        rankSet() {
            const rankSet = this.detail?.rank_set || {}
            if (rankSet === 'null' || Array.isArray(rankSet)) return {}
            return rankSet
        },

        activeDays() {
            const { begin_time, end_time } = this.detail
            if (!begin_time || !end_time) return ''
            return this._utils.timeDifference(begin_time * 1000, end_time * 1000)
        },
        canvas_width() {
            const windowWidth = uni.getSystemInfoSync().windowWidth
            let width = windowWidth
            // #ifdef H5
            if (windowWidth > 500) width = 500
            // #endif
            return width
        },
        
        ranklistShow() {
            if (this.rankSet['closed_top_rank']) return false
            if (this.detail?.conf?.active?.only_join_user_look_ranklist) {
                if (!this.checked) return false
            }
            
            return true
        },

        exchangeCalendarShow() {
            const {is_joining, checked, detail, time_period_step} = this
            if (!is_joining) return false        // 没有参加活动
            if (time_period_step) return false   // 开启了统计某一天指定时间段步数
            const {enter_types, runOneByOne} = detail?.conf?.active || {}
            if (enter_types === 3 && checked !== 1) return false // 报名审核未通过
            if (runOneByOne?.open) return false     // 队伍接力赛
            if (this.rankSet['exchange_history_closed']) return false  // OA关闭了兑换日历
            
            return true
        },

        ipSetOpen() {
            const ip_set = this.detail?.conf?.active?.ip_set
            if (!ip_set) return false
            const {open, ip_city} = ip_set
            return open && ip_city?.length
        },
        
        joinDistanceTips() {
            const {open, location, distance} = this.detail?.conf?.active?.join_location_distance_limit || {}
            if (!open || !location || !distance) return ''
            const {latitude, longitude, name} = location
            if (!latitude || !longitude || !name) return ''
            return `在${name}附近${distance}公里内可参与活动`
        },

        lightCityMapShow() {
            const {is_joining, checked} = this
            return !!(is_joining && checked && this.rankSet['light_map_point_reward_step'])
        },

        study_news_category_id() {
            const {article_bind_exam, reward_step_by_reading} = this.rankSet
            if (!article_bind_exam && !reward_step_by_reading) return ''
            if (!this.checked) return ''
            const {study_news_set, study_news_category} = this.detail?.conf?.active || {}
            return study_news_set?.news_category?.id || study_news_category?.id || ''
        },

        exchangeButtonText() {
            const {id, time_period_step, time_period_step_today_submit, today_step, time_period_step_today_start_step, time_period_step_today_submit_step} = this
            
            if (id === '4437b18798c7d8df91e8a0329ede73d9') return {text: '每日打卡'}
            if (time_period_step) {
                if (time_period_step_today_submit === '') return {text: '开始'}
                if (time_period_step_today_submit === 'start') {
                    return {
                        text: '提交', 
                        mini_text: `${today_step - time_period_step_today_start_step}步`
                    }
                }
                if (time_period_step_today_submit === 'end') {
                    return {
                        text: '已提交',
                        mini_text: `${time_period_step_today_submit_step}步`
                    }
                }
            }
            return {text: '立即兑换'}
        },

        blindBoxLotteryId() {
            const OA_open = !!this.rankSet['mystery_box']
            if (!OA_open) return null
            const lottery_id = this.detail?.conf?.active?.blind_box_lottery?.lottery_id
            return lottery_id || null
        },


        stepUnlockPictureFragmentsSet() {
            if (!this.detail?.conf?.active?.step_unlock_picture_fragments?.open) return null
            return this.detail.conf.active.step_unlock_picture_fragments
        },

        kiloUnit() {
            return this.detail?.conf?.active?.kilo_unit || '里'
        },

        maxDailyStepSubmitReasonOpen() {
            if (!this.rankSet['maxDailyStepSubmitReason']) return false
            return !!this.detail?.conf?.active?.max_num
        },

        examOpen() {
            if (this.is_simple) return false
            if (this.detail?.conf?.active?.answer_need_read?.open) return false
            return !!this.rankSet.exam_open
        },


        examPrizeShow() {
            if (!this.is_joining) return false
            if (!this.rankSet['sport_step_prize_set']) return false
            return !!this.detail?.conf?.active?.exam_prize_set?.prize_list?.length
        },

        examPrizeIconDiy() {
            const {icon, name} = this.detail?.conf?.active?.exam_prize_icon_diy || {}
            if (!icon || !name) return false
            return {icon, name}
        },

        dailySignShow() {
            if (!this.rankSet['morning_daily_sign']) return false

            const {types = 0, auto_sign = 0} = this.detail?.conf?.active?.daily_sign || {}
            // 设置了每日签到类型 并且 没有开启自动签到
            return types && !auto_sign
        },

        diyMap() {
            return !!this.diyMapSet?.bgImg
        },


        useDay7StepTemplate() {
            // 只有设置了无需地图的才使用这个模版
            const noMap = this.detail?.conf?.active?.map_types === 101
            if (!noMap) return false
            
            // 如果OA开了除 下面这个功能 以外的功能，不能使用这个模版
            const allow_OA_list = ['closed_AD', 'set_user', 'copy_from']
            for (const key in this.rankSet) {
                // 如果OA开启的功能有一个不在允许列表里面，则不能使用这个模版
                if (!allow_OA_list.includes(key)) return false
            }

            // 活动设置使用简约版，不使用此模版
            if (this.detail?.conf?.active?.is_set_simple) return false

            return true
        },

        day7StepTemplateBackgroundImage() {
            const defaultImage = 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/diy_template/jybg.jpg'
            return this.active_more_data?.active_conf_set?.index_page?.top_image || defaultImage
        },

        dailyStepUnlockExam() {
            if (!this.rankSet.mustExamThenExchangeStep) return false
            const must_exam_daily = this.detail?.conf?.active?.must_exam_daily || {}
            if (!must_exam_daily) return false

            const {types, exam_id, per_step} = must_exam_daily
            if (!types || !exam_id || !per_step) return false
            return true
        }
    },
    
    onLoad(e) {

        console.log('活动详情页面路径参数', e)

        let isH5 = false
        // #ifdef H5
        isH5 = true
        // #endif
        if (isH5) return xwy_api.alert('请在小程序内打开', { success: () => uni.navigateBack() })

        if (uni.getLaunchOptionsSync().scene === 1154) return this.getSimpleDetail(e.id)

        uni.showLoading({
            mask: true,
            title: '数据加载中...'
        })


        let userid = '', active_id = ''
        if (e.userid) userid = e.userid
        if (e.id) active_id = e.id
        if (e.scene) {
            const uid = this.getSceneOptions(e.scene, 'uid') || this.getSceneOptions(e.scene, 'userid')
            if (uid) userid = uid
            const a_id = this.getSceneOptions(e.scene, 'id')
            if (a_id) active_id = a_id
        }


        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            if (e.scene) return this.analysisScene(e.scene)

            if (!e.id) {
                this.loading = false
                this.error = '请指定活动id'
                return uni.hideLoading()
            }


            this.id = e.id

            const {userid, userinfo: {wx_h5_openid}} = app.globalData
            this.userid = userid
            this.wx_h5_openid = wx_h5_openid

            this.getDetail()

        }, userid, active_id)
    },
    onShow() {
        if (this.detail?.conf?.active?.audio_src) innerAudioContext.play()
        this.mustExamDailyEndCheck()
    },
    onHide() {
        if (this.audio_play) innerAudioContext.pause()
    },
    onUnload() {
        if (this.detail?.conf?.active?.audio_src) innerAudioContext.destroy()
        if (bs_socket.socketTask) {
            this.unload = true
            bs_socket.socketTask.close()
        }
    },

    onShareAppMessage() {
        this._utils.shareCopyActivityId(this.detail)

        let url = '/pages/activity/user/detail?id=' + this.id + '&userid=' + this.userid
        if (this.detail?.conf?.active?.screen_pic) url += `&screen_pic=${this.detail.conf.active.screen_pic}`

        return {
            title: this.detail?.conf?.active?.share_title || this.detail?.name || '',
            path: url,
            imageUrl: this.detail?.conf?.active?.share_image || this.detail?.logo || ''
        }
    },

    onShareTimeline() {
        return {
            title: this.detail?.conf?.active?.share_title || this.detail?.name || '',
            imageUrl: this.detail?.conf?.active?.share_image || this.detail?.logo || ''
        }
    },

    methods: {
        diyPageClick(data) {
            const {type, news_id, category_id: catID, appid, tel, active_id, active_types, exam_id, lottery_id} = data
            const {detail, id, is_joining, is_my_activity, checked, finished_time} = this
            const {shield_other, closed_AD: closeAD, point_knowledge_lock, lottery_open} = this.rankSet
            const jump = url => this.$uni.navigateTo(url)

            const options = {
                'home_page': () => !shield_other && jump('/pages/index/index'),
                'user_center': () => jump('/pages/user/user'),
                'news_detail': () => news_id && jump(`/pages/news/preview?id=${news_id}`),
                'news_list': () => catID && closeAD && jump(`/pages/news/list?category_id=${catID}`),
                'small_app': () => {
                    const options = {appId: appid}
                    if (data.path) options.path = data.path
                    appid && uni.navigateToMiniProgram({
                        ...options,
                        fail: err => {
                            if (err.errMsg === 'navigateToMiniProgram:fail cancel') return
                            this.$uni.showModal(JSON.stringify(err) + 'appid: ' + appid)
                        }
                    })
                },
                'play_phone': () => {
                    tel && uni.makePhoneCall({
                        phoneNumber: tel.toString(),
                        fail: err => this.$uni.showModal(JSON.stringify(err))
                    })
                },
                'share': () => this.showActiveSharePopup(),
                'manage': () => is_my_activity && this.toManage(),
                'join_popup': () => !is_joining && this.joinActivity(),
                'user_activity_detail': () => is_joining && this.uniPopupOpen('my_info'),
                'activity_explain': () => this.uniPopupOpen('activity_detail'),
                'map_page': () => this.toMap(),
                'ranking_page': () => this.toTopList(),
                'exchange_calendar': () => checked && this.toExchangeCalendar(),
                'point_page': () => {
                    jump(`/pages/activity/other/point_step?id=${id}&unit=${this.kiloUnit}&point_knowledge_lock=${point_knowledge_lock || '0'}`)
                },
                'redpack_record': () => jump(`/pages/wallet/redpack_list?id=${id}`),
                'gift_goods': () => this.toShop(false),
                'today_sign': () => jump(`/pages/sign_in/sign_in?active_id=${id}`),
                'honor_medal': () => this.toShop(true),
                'ai_sport': () => this.toAiSportList(),
                'certificate': () => jump(`../other/certificate?id=${id}&finished_time=${finished_time}`),
                'medal_list': () => this.toMedalList(),
                'lottery': () => lottery_open && this.toLottery(),
                'lottery_record': () => jump(`/pages/lottery/user/lottery_record_list?active_id=${id}`),
                'answer_list': () => {
                    const type = detail.conf?.active?.exam_show_type || 1
                    const url = `/pages/activity/other/answer/list?id=${id}&exam_show_type=${type}`
                    jump(url)
                },
                'sport_talk': () => this.toSportTalk(),
                'biubiubiu': () => this.toBulletScreen(),
                'weekly_step': () => this.showWeekly(),
                'today_history': () => {
                    let url = '/pages/other/today_in_history'
                    if (closeAD) url += '?close_ad=1'
                    jump(url)
                },
                'today_news': () => {
                    let url = `/pages/other/today-news?active_id=${id}`
                    if (closeAD) url += '&close_ad=1'
                    jump(url)
                },
                'feedback': () => jump(`/pages/activity/feedback/send?activity_id=${id}`),
                'exchange_step': () => this.exchange(),
                'business_kilometers': () => this.toBusinessKilometersExchange(),
                'together_pic_list': () => this.toTogetherPicList(),
                'run_sport': () => this.toRunning(),
                'activity_total_ranking': () => this.toActivityTotalRanking(),
                'bind_wechat_h5_openid': () => this.bindWechatH5Openid(),
                'other_activities': () => {
                    active_id && active_types && utils.toActivityDetail({active_id, types: active_types})
                },
                'independent_exam': () => {
                    exam_id && jump(`/pages/likou_dati/pages/exam/exam_details/exam_details?exam_id=${exam_id}&independent=1`)
                },
                'independent_lottery': () => this.toIndependentLottery(lottery_id),
                'daily_step_unlock_exam_popup': () => this.dailyStepUnlockExamPopupShow(),
                'step_7_day': () => this.uniPopupOpen('step_info'),
                'headimg_plugin_chajian': () => this.diyPageHeadimgPlugin('chajian'),
                'headimg_plugin_beijing': () => this.diyPageHeadimgPlugin('beijing'),
                'light_city': () => this.toLightCityMap(),
                'study_news': () => this.studyNews(),
                'every_day_certificate': () => this.toEveryDayCertificate(),
                'face_swap': () => this.toAiFace(),
                'party_oath': () => this.toPartyOath(),
                'exam_prize': () => this.toExamPrize(),
            }

            options[type]?.()
        },
        
        toAiFace() {
            if (!this.rankSet['face_swap']) return this.$uni.showToast('活动未开启该功能')
            if (!this.is_joining) return this.$uni.showToast('未参与活动')
            if (!this.checked) return this.$uni.showToast('报名未审核通过')
            
            this.$uni.navigateTo(`/pages/other/ai-face?active_id=${this.id}`)
        },

        toPartyOath() {
            if (!this.rankSet['party_oath']) return this.$uni.showToast('活动未开启该功能')
            
            this.$uni.navigateTo(`/pages/take-oath/list?active_id=${this.id}`)
        },

        toLottery() {
            const lottery_add_step_integral = this.rankSet.lottery_add_step_integral
            const step_lottery_list = this.detail?.conf?.active?.step_lottery_list
            if (lottery_add_step_integral && step_lottery_list?.length) {
                return this.$uni.navigateTo(`/pages/lottery/user/step-lottery-list?active_id=${this.id}`, {
                    success: res => res.eventChannel.emit('stepLotteryList', step_lottery_list)
                })
            }

            this.$uni.navigateTo(`/pages/lottery/user/lottery?active_id=${this.id}`)
        },

        async toIndependentLottery(lottery_id) {
            const go = () => this.$uni.navigateTo(`/pages/lottery/user/lottery?type=just_lottery&id=${lottery_id}&active_id=${this.id}`)
            
            if (this.checked) return go()
            
            this.$uni.showLoading()
            
            const lottery_res = await this.xwy_api.request({
                url: 'front.flat.active.lottery_active.admin.manage_lottery/lottery_active_details',
                data: {
                    active_id: lottery_id
                }
            })
            
            const errModal = content => {
                uni.hideLoading()
                this.$uni.showModal(content)
            } 
            
            if (lottery_res?.status !== 1) return errModal(lottery_res?.info || '抽奖活动获取失败')
            const lottery_details = lottery_res?.data?.active_details
            if (!lottery_details) return errModal('抽奖活动获取失败')
            
            const {condition, active_list} = lottery_details.conf?.limit || {}
            if (!condition || !active_list || !active_list.length) return go()
            
            const is_join = await this.checkBindActiveExamIsJoin(active_list, condition)
            
            uni.hideLoading()
            
            if (!is_join) {
                const condition_tips = condition === 1 ? '活动或答题' : `${condition}场活动或答题`
                return this.$uni.showModal(`需要参与${condition_tips}才能抽奖`)
            }
            
            this.$uni.showToast('需要报名才能抽奖')
            await this.joinActivity()
        },
        
        async checkBindActiveExamIsJoin(active_list, condition) {
            let join_count = 0
            for (let i = 0, len = active_list.length; i < len; i++) {
                const {active_types, id} = active_list[i]
                const is_join = active_types === 2 ? await this.checkExamIsJoin(id) : await this.checkActiveIsJoin(id)
                if (is_join) join_count++
            }
            return join_count >= condition
        },
        
        async checkExamIsJoin(id) {
            const res = await this.xwy_api.request({
                url: 'front.flat.exam.user_answer/user_exam_score_list',
                data: {
                    userid: app.globalData['userid'],
                    exam_id: id,
                    page: 1,
                    perpage: 1
                }
            })
            
            return !!res?.data?.['user_score_list']?.data?.length
        },
        
        async checkActiveIsJoin(id) {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: {
                    active_id: id
                }
            })

            return res?.data?.['user_details']?.checked === 1
        },

        toBusinessKilometersExchange() {
            if (!this.xwy_config.openBusinessKilometers(this.id)) {
                return uni.showToast({ title: '活动未开启此权限', icon: 'none' })
            }

            if (!this.is_joining) return this.$uni.showToast('请先报名活动')
            if (!this.checked) return this.$uni.showToast('请等待报名审核通过')

            uni.navigateTo({
                url: `/pages/activity/other/user_exchange_list?activity_id=${this.id}&nickname=${encodeURIComponent(this.must_submit?.[0]?.value || '用户已删除')}&headimg=${encodeURIComponent(this.headimg || '')}`
            })
        },
        
        toRunning() {
            const {begin_time, end_time, conf} = this.detail
            
            const now = new Date().getTime()
            if (now < begin_time * 1000) return this.$uni.showToast('活动未开始')
            if (now > end_time * 1000) return this.$uni.showToast('活动已结束')
            
            const run_trace = conf?.active?.run_trace
            if (!run_trace || !run_trace.exchange_step || !run_trace.run_kilo)
                return this.$uni.showToast('活动跑步兑换比例设置有误')
            
            if (!this.is_joining) return this.$uni.showToast('请先报名活动')
            if (!this.checked) return this.$uni.showToast('请等待报名审核通过')
            
            this.$uni.navigateTo(`/pages/running/user/run?id=${this.id}`)  
        },

        toMap() {
            uni.navigateTo({
                url: './road-map/road_map?id=' + this.id,
                events: {
                    updateUserDetail: () => {
                        this.getUserStatus(true)
                    },
                    updateAttendDetailShow: () => {
                        this.updateAttendDetailShow()
                    }
                }
            })
        },

        toExchangeCalendar() {
            if (!this.exchangeCalendarShow) return

            const {closed_sign_pass_day, thirty_day_auto_exchange, exchange_pass_day} = this.rankSet
            let url = '/pages/wechat-step/history_step?id=' + this.id
            url += `&stime=${this.detail.begin_time}`
            url += `&etime=${this.detail.end_time}`
            url += `&closed_sign_pass_day=${closed_sign_pass_day || 0}`
            url += `&thirty_day_auto_exchange=${thirty_day_auto_exchange || 0}`
            url += `&exchange_pass_day=${exchange_pass_day || 0}`
            url += `&activity_max_num=${this.detail.conf.active.max_num || 0}`
            uni.navigateTo({url})
        },

        toSportTalk() {
            uni.navigateTo({
                url: '/pages/comment/list?active_id=' + this.id + '&admin_userid=' + this.detail.userid
            })
        },


        toTogetherPicList() {
            uni.navigateTo({
                url: '/pages/activity/other/answer/list?get_types=4&exam_show_type=2&id=' + this.id
            })
        },

        toBulletScreen() {
            let nickname = '匿名用户',
                headimg = ''

            const userinfo = app.globalData['userinfo']
            if (userinfo?.nickname) nickname = userinfo.nickname
            if (userinfo?.headimg) headimg = userinfo.headimg
            if (this.must_submit && this.must_submit.length && this.must_submit[0].value) {
                nickname = this.must_submit[0].value
            }
            if (this.headimg) headimg = this.headimg

            let url = `../other/bullet_screen?active_id=${this.id}&nickname=${nickname}&headimg=${headimg}`
            if (this.active_more_data?.['active_conf_set']?.['barrage']) {
                const barrage = this.active_more_data['active_conf_set']['barrage']
                if (barrage.background) url += `&bgi=${barrage.background}`
                if (barrage['submit_button_text']) url += `&send_text=${barrage['submit_button_text']}`
                if (barrage['navigation_bar']) {
                    url += `&navigation_bar=${JSON.stringify(barrage['navigation_bar'])}`
                }
            }
            uni.navigateTo({ url })
        },

        webSocketInit(reconnect = false) {
            bs_socket.webSocketInit(() => {
                bs_socket.socketTask.onMessage(res => {
                    console.log('【WEBSOCKET】收到消息', res.data)
                    this.receiveMessages(res.data)
                })
                bs_socket.socketTask.onOpen(res => {
                    console.log('【WEBSOCKET】', '链接成功！', res)
                    uni.hideLoading()
                    if (!reconnect) this.joinSocket()
                })
                bs_socket.socketTask.onClose(res => {
                    console.log('【WEBSOCKET】链接关闭！', res)
                    uni.hideLoading()
                    !this.unload && this.webSocketInit(true)
                })
            })
        },

        joinSocket() {
            let nickname = '匿名用户',
                headimg = ''

            const userinfo = app.globalData['userinfo']
            if (userinfo?.nickname) nickname = userinfo.nickname
            if (userinfo?.headimg) headimg = userinfo.headimg
            if (this.must_submit && this.must_submit.length && this.must_submit[0].value) {
                nickname = this.must_submit[0].value
            }
            if (this.headimg) headimg = this.headimg
            const data = {
                active_id: this.id,
                userid: this.userid,
                nickname,
                headimg,
                message: '进入活动'
            }
            bs_socket.socketTask.send({ data: JSON.stringify(data) })
        },

        receiveMessages(message) {
            message = JSON.parse(message)
            if (message.active_id !== this.id) return false
            this.$refs.lffBarrage.add({item: message})
        },


        showWeekly() {
            uni.showLoading({
                title: '周报生成中...',
                mask: true
            })

            const bg_src = 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/sportweek/sportweek.jpg'
            this.getImageInfo(bg_src, path => {
                this.getWeekStep(path)
            }, true)
        },

        getImageInfo(src, cb, is_bgimg = false) {
            uni.getImageInfo({
                src,
                success: res => {
                    console.log('图片信息: ', res)
                    // h5、app-vue 中单个尺寸过大的 canvas 在 iOS/Safari 无法绘制（具体限制尺寸未公布）。好像是超过1000像素有可能绘制不了，这里先不处理
                    if (is_bgimg) {
                        this.weekly_width = res.width
                        this.weekly_height = res.height
                    }
                    cb && cb(res.path)
                },
                fail: err => {
                    console.log('图片信息获取失败: ', err)
                    uni.hideLoading()
                    uni.showModal({
                        title: '周报生成失败',
                        content: '图片信息获取失败,src: ' + src
                    })
                }
            })
        },

        async getWeekStep(path) {
            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id,
                types: 1 //【1】获取上周的兑换历史记录  可获取上一周的运动步数记录。也同步输出了上周每一天的日期列表。
            }

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.exchange/exchange_step_list',
                data
            })

            console.log(res)

            const date_list = res?.['data']?.['last_week']?.['date_list'] || [],
                exchange_list = res?.['data']?.['exchange_list']?.data || []

            const step_list = [0, 0, 0, 0, 0, 0, 0]

            date_list.forEach((v, i) => {
                const time = new Date(v.replace(/-/g, '/')).getTime() / 1000
                exchange_list.forEach(vv => {
                    if (time === vv.exchange_date) step_list[i] = vv.step || 0
                })
            })

            this.drawWeekly(path, step_list)
        },

        drawWeekly(path, step_list) {

            const ctx = uni.createCanvasContext('weekly')

            this.drawBg(ctx, path)

            let step_count = 0

            step_list.forEach(v => {
                step_count += v
            })

            this.drawStepCount(ctx, step_count)

            this.drawQrCode(ctx, () => {
                this.drawStepList(ctx, step_list, () => {
                    this.drawUserInfo(ctx, () => {
                        this.draw(ctx)
                    })
                })
            })


        },

        drawBg(ctx, path) {
            // 画背景图
            ctx.drawImage(path, 0, 0, this.weekly_width, this.weekly_height)
        },

        drawStepCount(ctx, count) {
            ctx.setTextAlign('center')
            ctx.setFontSize(28)
            ctx.fillStyle = '#666666'
            ctx.fillText('上周累计兑换', 330, 760)
            ctx.fillText('步', 540, 760)
            ctx.setFontSize(30)
            ctx.fillStyle = '#ea494c'
            ctx.fillText(count + '', 470, 760)
        },

        drawUserInfo(ctx, cb) {
            let nickname = '匿名用户',
                headimg = ''

            const userinfo = app.globalData['userinfo']
            if (userinfo?.nickname) nickname = userinfo.nickname
            if (userinfo?.headimg) headimg = userinfo.headimg
            if (this.must_submit && this.must_submit.length && this.must_submit[0].value) {
                nickname = this.must_submit[0].value
            }
            if (this.headimg) headimg = this.headimg


            ctx.setTextAlign('center')
            ctx.setFontSize(28)
            ctx.fillStyle = '#666666'
            ctx.fillText(nickname, this.weekly_width / 2, 700)


            if (headimg) {
                this.getImageInfo(headimg, path => {
                    ctx.beginPath()
                    ctx.arc(this.weekly_width / 2, 610, 50, 0, Math.PI * 2, false)
                    ctx.clip()
                    ctx.drawImage(path, this.weekly_width / 2 - 50, 560, 100, 100)
                    ctx.restore()
                    cb && cb()
                })
                return false
            }
            cb && cb()
        },

        async drawQrCode(ctx, cb) {
            const src = await xwy_api.getWxAppCode({
                page: 'pages/activity/user/detail',
                scene: 'id=' + this.detail.id
            })
            
            if (!src) return cb?.()
            
            this.getImageInfo(src, path => {
                ctx.drawImage(path, 50, this.weekly_height - 250, 200, 200)
                cb && cb()
            })
        },

        drawStepList(ctx, step_list, cb) {
            const color_list = [
                '#51a3f6',
                '#6fd958',
                '#f5c558',
                '#ef9253',
                '#ea5f4b',
                '#e74a65',
                '#ea494c'
            ]
            const week_list = ['一', '二', '三', '四', '五', '六', '日']
            step_list.forEach((v, i) => {
                ctx.beginPath()
                ctx.setStrokeStyle(color_list[i])
                const height = v ? v * 0.01 : 10,  // 没有步数显示10像素
                    width = 30

                const left = 240 + i * width + i * 40
                ctx.rect(left, 1090 - height, width, height)
                ctx.setFillStyle(color_list[i])
                ctx.setStrokeStyle(color_list[i])
                ctx.fill()
                ctx.stroke()

                ctx.setTextAlign('center')
                ctx.setFontSize(28)
                ctx.fillStyle = '#666666'
                ctx.fillText(week_list[i], left + 15, 1130)
                ctx.setFontSize(24)
                ctx.fillText(v, left + 15, 1090 - height - 20)
            })

            cb && cb()
        },

        draw(ctx) {
            ctx.draw(true, () => {
                this.canvasToTempFilePath()
            })
        },

        canvasToTempFilePath() {
            uni.canvasToTempFilePath({
                canvasId: 'weekly',
                success: res => {
                    uni.hideLoading()
                    console.log('绘画的图片', res)
                    this.weekly_src = res.tempFilePath
                    this.previewWeekly()
                },
                fail: err => {
                    console.log('绘画失败', err)
                    this.canvasToTempFilePath()
                }
            })

        },

        previewWeekly() {
            uni.previewImage({
                urls: [this.weekly_src]
            })
        },


        async getSimpleDetail(id) {
            this.$uni.showLoading('数据加载中...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details_simple',
                data: {
                    active_id: id
                }
            })
            this.loading = false
            uni.hideLoading()

            const detail = res['data']['active_details']
            if (!detail) {
                this.error = '活动已结束或已删除'
                return
            }

            if (res['data'].active_more_data) {
                const active_more_data = res['data'].active_more_data
                this.active_more_data = active_more_data
                if (active_more_data.technology_support) {
                    this.technology_support = active_more_data.technology_support
                }
                if (active_more_data['active_conf_set']) {
                    const active_conf_set = active_more_data['active_conf_set']
                    if (active_conf_set.headimg_plugin && active_conf_set.headimg_plugin.length) {
                        this.headimg_plugin = active_conf_set.headimg_plugin
                    }
                }
            }

            this.detail = detail

            my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)


            if (detail.reward_step_by_exam) {
                detail.reward_step_by_exam = JSON.parse(detail.reward_step_by_exam)
            }

            if (detail.conf) {
                const conf = detail.conf

                if (!this.screen_pic && conf.active?.screen_pic) {
                    this.screenPicShow(conf.active.screen_pic)
                }

                if (conf.active?.audio_src) {
                    this.audioInit(conf.active.audio_src)
                }

                if (conf.must_submit) {
                    const must_submit = conf.must_submit
                    delete conf.must_submit
                    if (must_submit.length) {
                        must_submit.forEach(v => v.value = v.value || '')
                        this.must_submit = must_submit
                    }
                }
            }

            if (detail.rank_set) {
                const rank_set = detail.rank_set
                // 是否纯净版，纯净版隐藏返回首页按钮
                if (rank_set['shield_other']) this.$uni.hideHomeButton()
            }

            // 解决分享朋友圈预览没有获取到个性化活动页面配置，导致页面空白，朋友圈预览默认使用默认模版
            this.diy_show_type = 'model'
            this.setDefaultModel()

            this.addLookRecords()

            if (detail.name) this.$uni.setNavigationBarTitle(detail.name)
        },


        diyPageHeadimgPlugin(key) {
            const headimg_plugin = this.headimg_plugin || []
            const item = headimg_plugin.find(v => v.key === key)
            if (!item) return this.$uni.showToast('活动未配置该功能')
            this.toIdeaAvatar(item)
        },
        

        toIdeaAvatar(item) {
            if (!item.img_list || !item.img_list.length) {
                uni.showModal({
                    title: '提示',
                    content: '活动未配置头像图片列表，无法使用此功能',
                    showCancel: false
                })

                return false
            }

            let path = '/pages/idea_avatar/pendant/pendant'
            if (item.key === 'beijing') path = '/pages/idea_avatar/background/background'
            path += `?id=${this.id}`
            if (this.rankSet.closed_AD) path += '&closed_AD=1'

            this.$uni.navigateTo(path, {
                success: res => res.eventChannel.emit('img_list', item.img_list)
            })
        },

        toAiSportList() {
            let url = `/pages/ai_sport/pages/list?active_id=${this.id}`
            if (this.rankSet['ai_hand_start']) url += '&manual=1'
            this.$uni.navigateTo(url, {
                success: res => {
                    if (this.detail.conf?.AI_motion?.motion_list?.length) {
                        res.eventChannel.emit('AI_motion_list', this.detail.conf.AI_motion.motion_list)
                    }
                }
            })
        },

        toMedalList() {
            const { kilo_unit = '里', medal_mileage_hide = 0 } = this.detail.conf.active
            let url = `../other/medal_list?id=${this.id}&unit=${kilo_unit}`
            if (medal_mileage_hide === 1) url += '&mileage_hide=1'
            uni.navigateTo({ url })
        },

        toExamPrize() {
            if (!this.is_joining) return this.$uni.showToast('请参与活动')
            if (!this.checked) return this.$uni.showToast('报名未通过审核')
            this.$uni.navigateTo(`../other/exam-prize?active_id=${this.id}`)
        },

        myBlindBoxList() {
            let url = `/pages/other/my-blind-box-list?active_id=${this.id}&lottery_id=${this.blindBoxLotteryId}`
            if (this.is_joining) url += '&join=1'

            this.$uni.navigateTo(url, {
                success: res => {
                    if (this.is_joining && this.user_details) {
                        res.eventChannel.emit('userDetails', this.user_details)
                    }
                }
            })

        },

        toShop(not_pay = false) {
            app.globalData['tempData'].shop_integral_unit = this.detail.conf.active.integral && this.detail.conf.active.integral.unit || '金币'
            let url = '/pages/shop/goods/list?active_id=' + this.id
            if (not_pay) url = '/pages/shop/goods/list?active_id=f75280f8350db81bb200a4ed82cc8230&not_pay=1'
            uni.navigateTo({ url })
        },

        screenPicShow(src) {
            uni.hideLoading()
            this.screen_pic = src
            let screen_pic_count_down = this.detail?.conf?.active?.screen_pic_count_down
            if (screen_pic_count_down === undefined) screen_pic_count_down = 5
            if (screen_pic_count_down === '' || screen_pic_count_down === '0') screen_pic_count_down = 0
            this.screen_pic_count_down = screen_pic_count_down
            this.screen_pic_buttom_show = this.detail?.conf?.active?.screen_pic_buttom_show || 0
            this.screen_pic_show = true
            if (screen_pic_count_down === 0) return false
            interval = setInterval(() => {
                this.screen_pic_count_down--
                if (this.screen_pic_count_down <= 0) {
                    this.skipScreen()
                }
            }, 1000)
        },
        skipScreen() {
            clearInterval(interval)
            this.screen_pic_count_down = 0
            this.screen_pic_hide = true

            const timeout = setTimeout(() => {
                this.screen_pic_show = false
                this.screen_pic_hide = false
                if (this.loading) this.$uni.showLoading('数据加载中...')
                this.passwordDialogShow()
                
                this.todayNewsPopupOpen()

                if (this.rankSet['batch_import'] && this.detail.conf.active.join_type && !this.is_joining) {
                    this.showNoticePopup()
                    this.$refs.input_username_password.open({
                        detail: this.detail,
                        success: () => {
                            this.$uni.showLoading()
                            this.getDetail()
                        }
                    })
                }

                clearTimeout(timeout)
            }, 500)
        },
        analysisScene(scene) {
            const id = this.getSceneOptions(scene, 'id')
            console.log('id===', id)
            if (!id) {
                uni.hideLoading()
                return this.$uni.showModal('从二维码获取id失败')
            }

            this.getActiveId(id)
        },

        getSceneOptions(scene, key) {
            const sceneStr = decodeURIComponent(scene)
            console.log(sceneStr)
            return utils.getUrlParams(key, sceneStr)
        },

        audioInit(src) {
            innerAudioContext = uni.createInnerAudioContext()
            innerAudioContext.autoplay = true
            innerAudioContext.loop = true
            innerAudioContext.src = src

            innerAudioContext.onPlay(() => {
                this.audio_play = true
            })
            innerAudioContext.onPause(() => {
                this.audio_play = false
            })
            innerAudioContext.onError((res) => {
                console.log('背景音乐播放失败')
                console.log(res.errMsg)
                console.log(res.errCode)
            })
        },
        audioControl() {
            console.log(innerAudioContext.paused)
            const paused = innerAudioContext.paused
            paused ? innerAudioContext.play() : innerAudioContext.pause()
        },

        async getActiveId(id) {
            const data = {
                access_token: app.globalData['access_token'],
                id
            }

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/get_long_active_id_by_short_id',
                data
            })
            if (res?.['data']?.['long_active_id']) {
                this.id = res['data']['long_active_id']
                this.getDetail()
            } else {
                uni.hideLoading()
                xwy_api.alert(res && res['info'] || '长id获取失败')
            }
        },


        // just_update 管理员从这里到活动管理，再到活动修改，修改完活动以后刷新这里的页面数据
        getDetail(just_update = false) {
            xwy_api.getActivityDetail(this.id, res => {
                const details = res?.data?.active_details
                if (!details) {
                    this.loading = false
                    this.error = '活动已结束或已删除'
                    return uni.hideLoading()
                }

                if (details.rank_set) {
                    const rank_set = details.rank_set
                    // 是否纯净版，纯净版隐藏返回首页按钮
                    if (rank_set['shield_other']) {
                        // #ifdef MP-WEIXIN
                        this.$uni.hideHomeButton()
                        // #endif
                        // 更新纯净版缓存信息
                        utils.updateShieldOtherInfo(details)
                    }

                    // #ifdef MP-WEIXIN
                    if (rank_set?.['share_closed']) uni.hideShareMenu(undefined)
                    // #endif
                }


                this.addLookRecords()

                if (details.name) this.$uni.setNavigationBarTitle(details.name)

                if (!just_update && app.globalData['userid'] === details.userid) this.is_my_activity = true

                // #ifdef MP-WEIXIN
                const official_account_open = details.conf.active.official_account_open || 0
                if (official_account_open === 2) {
                    const {scene} = wx.getLaunchOptionsSync()
                    if (scene !== 1035) {
                        this.loading = false
                        this.error = '请从公众号菜单进入活动'
                        return uni.hideLoading()
                    }
                }
                // #endif

                if (!res.data['usedFreeSimple']) this.is_simple = false

                if (res.data.active_more_data) {
                    const active_more_data = res.data.active_more_data
                    this.active_more_data = active_more_data
                    if (active_more_data.technology_support) {
                        this.technology_support = active_more_data.technology_support
                    }
                    if (active_more_data['active_conf_set']) {
                        const active_conf_set = active_more_data['active_conf_set']
                        if (active_conf_set.headimg_plugin && active_conf_set.headimg_plugin.length) {
                            this.headimg_plugin = active_conf_set.headimg_plugin
                        }
                        if (active_conf_set['active_create']?.['everyone_text']) {
                            this.enter_types_1_text = active_conf_set['active_create']['everyone_text']
                        }
                    }
                }

                app.globalData.activity_detail = details

                this.detail = details

                my_storage.setActivityCloaeAdStorage(this.id, details.rank_set?.closed_AD)


                if (details.reward_step_by_exam) {
                    details.reward_step_by_exam = JSON.parse(details.reward_step_by_exam)
                }

                if (details.conf) {
                    const conf = details.conf

                    if (conf.active) {
                        const active = conf.active
                        if (!this.screen_pic && active.screen_pic) {
                            this.screenPicShow(conf.active.screen_pic)
                        }
                        if (active.audio_src) this.audioInit(conf.active.audio_src)
                        if (active.news?.news_id) this.changeDetailContent(active.news.news_id)
                        if (active.map_types === 101 && active.time_period_step) this.time_period_step = true
                        if (active.is_set_simple) this.is_simple = true
                    }

                    if (conf.must_submit) {
                        const must_submit = conf.must_submit
                        delete conf.must_submit
                        if (must_submit.length) {
                            must_submit.forEach(v => v.value = v.value || '')
                            this.must_submit = must_submit
                        }
                    }
                }

                if (just_update) return false

                this.getUserStatus()

                this.$nextTick(() => {
                    if (!this.screen_pic) this.todayNewsPopupOpen()
                })
            })
        },
        
        async getEveryDayCertificateSet() {
            if (!this.checked) return
            if (this.everyDayCertificate) return
            const res = await this.xwy_api.request({
                url: 'front.system.system_conf/json_conf_set',
                data: {
                    name: `exchange_step_daily_show_book_diy_set_${this.id}`
                }
            })
            this.everyDayCertificate = res?.data?.conf_set?.['daily_book_show_set'] || []
        },
        
        async exchangeSuccessShowEveryDayCertificate() {
            // 需要先弹每日证书再更新数据，不然更新数据的时候会先清空this.must_submit的value，有可能会导致证书没有获取到姓名
            await this.showEveryDayCertificate()
            this.getDetail()
        },
        
        async showEveryDayCertificate() {
            await this.getEveryDayCertificateSet()

            const startDate = this._utils.unitTimeToDate(this.detail.begin_time * 1000).split(' ')[0]
            const today = this._utils.getDay(0, true)
            const day = this._utils.dateDiff(startDate, today)
            
            const certificate_set = this.everyDayCertificate[day - 1]
            
            if (!certificate_set) return this.$uni.showModal('今日没有配置证书')

            this.popup_open = true
            await this.$refs.everyDayCertificatePopup.show({
                certificate_set,
                nickname: this.must_submit?.[0]?.value || '',
                close: () => this.popup_open = false
            })
        },
        
        async toEveryDayCertificate() {
            await this.getEveryDayCertificateSet()
            if (!this.everyDayCertificate?.length) return this.$uni.showModal('活动未配置每日证书数据')
            this.$uni.navigateTo(`../other/every-day-certificate`, {
                success: res => {
                    res.eventChannel.emit('certificateData', {
                        nickname: this.must_submit?.[0]?.value || '',
                        begin_time: this.detail.begin_time,
                        certificateList: this.everyDayCertificate,
                        active_id: this.id
                    })
                }
            })
        },

        showAD() {
            uni.showLoading({ title: '同步中...', mask: true })
            const time = utils.randomNum(300, 1000)
            setTimeout(() => {
                uni.hideLoading()
                this.exchange_success_info = '步数同步成功'
                this.exchange_success_tip_text_list = [{
                    color: '#666666',
                    text: '当前累计步数 ' + this.user_details.exchange_kilo + '步'
                }]
            }, time)
        },

        async getSimpleTopList() {
            if (!this.is_simple) return
            if (this.time_period_step) return
            if (this.rankSet['closed_top_rank']) return
            if (!this.ranklistShow) return
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.step/top_rank_list',
                data: {
                    active_id: this.id,
                    top_rank_types: 0,
                    page: 1,
                    perpage: 10
                }
            })

            const list = res?.data?.['top_rank_list']?.list?.data || []
            const top_list = []
            list.forEach(v => {
                top_list.push({
                    nickname: v.must_submit?.[0]?.value || '',
                    headimg: v.headimg || '',
                    step: v.exchange_step || 0
                })
            })
            this.top_list = top_list
        },


        addLookRecords() {
            const detail = this.detail
            const value = {
                active_id: detail.active_id,
                name: detail.name,
                logo: detail.logo || this.xwy_config.active_default_logo,
                types: detail.types,
                look_time: new Date().getTime()
            }

            if (detail.organizer) value.organizer = detail.organizer
            my_storage.addActivityLookRecords(value)
        },


        // just_update 是不是自动兑换后更新用户兑换里程的，是的话就不获取步数了，因为或获取步数以后又会重新自动兑换
        async getUserStatus(just_update = false) {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id
                }
            })

            if (res?.['data']?.user_details) {
                const attend_details = res.data.user_details
                this.user_details = attend_details

                app.globalData.activity_user_detail = attend_details

                this.is_joining = true
                this.checked = attend_details.checked || 0
                if (!just_update) this.getMyStep()
                if (attend_details.nickname) this.username = attend_details.nickname
                if (attend_details.headimg) this.headimg = attend_details.headimg
                if (attend_details.team_id) {
                    this.have_team = true
                    this.team_id = attend_details.team_id
                }

                if (attend_details.finished_time) {
                    
                    this.finished_time = attend_details.finished_time
                    if (res.data.finished_num) this.finished_num = res.data.finished_num
                    this.$nextTick(() => this.uniPopupOpen('finishedPopup'))
                }
                if (attend_details.exchange_kilo) {
                    this.user_exchange_num = attend_details.exchange_kilo
                }
                if (attend_details.score) this.answer_score = attend_details.score
                if (attend_details['sign_day']) this.signdays = attend_details['sign_day']
                if (attend_details['team_details'] && attend_details['team_details'].id) {
                    this.team_id = attend_details['team_details'].id
                    this.team_name = attend_details['team_details'].name
                }

                if (attend_details.must_submit) {
                    this.must_submit.forEach((active, index) => {
                        attend_details.must_submit.forEach(user => {
                            if (active.title === user.title) {
                                this.$set(this.must_submit[index], 'value', user.value)
                            }
                        })
                    })
                }

                if (this.detail?.conf?.active?.daily_sign?.auto_sign) await this.dailyAutoSign()

                await this.getRegistrationInfo()
                if (!just_update) await this.getUserStartEndStepDetails()

                if (this.detail?.conf?.active?.enter_activity_popup_news?.id && !this.enter_activity_popup_news_show) {
                    await this.$refs.enterActivityNewsPopup.open()
                    this.enter_activity_popup_news_show = true
                }

                
            } else {
                this.no_attend = true
                this.loading = false
                uni.hideLoading()

                this.getMyStep()

                if (this.screen_pic_show) return false

                const join_type = this.detail.conf.active.join_type || 0
                if (this.rankSet['batch_import'] && join_type) {
                    // 解决开启了导入账号密码登录没有弹出活动须知弹窗的问题（一起弹出，阅读须知弹窗的层级比登录弹窗高，遮住登录弹窗）
                    await this.showNoticePopup()
                    
                    this.$refs.input_username_password.open({
                        detail: this.detail,
                        success: () => {
                            this.$uni.showLoading()
                            this.getDetail()
                        }
                    })
                    return false
                }


                this.passwordDialogShow()
            }

            this.rankSet['barrage'] && this.webSocketInit()
        },

        async getRegistrationInfo() {
            if (!this.rankSet['active_pay']) return
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_pay.userBuy/check_user_have_pay_active',
                data: { active_id: this.id }
            })

            if (res?.['data']?.['buy_details']?.['price_details']?.name) {
                this.user_details.registration_name = res['data']['buy_details']['price_details'].name
            }
        },

        async dailyAutoSign(afterExchange = false) {
            /*如果开启了每日自动签到和每日自动兑换，必须在兑换完以后才能签到(不管成功失败都要签)，不认获得的积分会被覆盖
            如果开启了自动兑换，自动签到不执行，等兑换后(afterExchange)通知才能签到*/
            if (this.rankSet['one_day_auto_exchange'] && !afterExchange) return

            const now = new Date().getTime()

            const begin_time = this.detail.begin_time * 1000
            const end_time = this.detail.end_time * 1000
            if (now < begin_time || now > end_time) return

            const storageKey = `daily_auto_sign_last_date_${this.id}`
            const sign_last_date = uni.getStorageSync(storageKey)

            const today = this._utils.getDay(0, true)
            if (sign_last_date === today) return

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.dailySign/submit_daily_sign',
                data: {
                    active_id: this.id
                }
            })

            if (res?.status !== 1) return

            uni.setStorageSync(storageKey, today)
            await this.getUserStatus(true)
        },

        async getDiyMapSet() {
            this.$uni.showLoading()
            this.loading = true

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.page_set.pageDiy/active_details_page_diy_set',
                data: {active_id: this.id}
            })

            this.loading = false
            uni.hideLoading()

            const _set = res?.data?.page_diy_set?.content?.diyMap

            if (!_set) return

            if (_set.navigationBar) this.setNavigationBar(_set.navigationBar)

            this.diyMapSet = _set
        },


        async getPageDiySet() {
            uni.showLoading({ mask: true })
            this.loading = true

            this.page_diy_model = []
            this.tabbar_data = null
            let _set = {}

            const template_id = this.detail.conf.active.template_id
            if (template_id) {
                if (template_id !== defaultTemplateConfig) { // 默认模版不用获取模版配置
                    const res = await xwy_api.request({
                        url: 'front.flat.sport_step.page_set.pageDiy/tpl_details',
                        data: { id: template_id }
                    })
                    _set = res?.['data']?.['tpl_details']?.content || {}
                }
            } else if (this.rankSet['page_diy_set']) {
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.page_set.pageDiy/active_details_page_diy_set',
                    data: { active_id: this.id }
                })
                _set = res?.['data']?.['page_diy_set']?.content || {}
            }


            if (_set['navigation_bar']) {
                const bar = _set['navigation_bar']
                if (bar.page_background_color) this.page_background_color = bar.page_background_color
                this.setNavigationBar(bar)
            }

            const diy_show_type = _set['show_type'] || 'model'
            this.diy_show_type = diy_show_type

            this.setTabbar(_set.tabbar)

            if (diy_show_type === 'model') {
                if (!_set['model_list']?.length) {
                    this.setDefaultModel()
                    return false
                }

                this.page_diy_model = _set['model_list']

                this.setModelList()
            }

            if (diy_show_type === 'image') await this.setDiyImage(_set.image_model_data)
        },

        setTabbar(tabbar) {
            if (!tabbar?.list?.length) return false
            const list = []
            tabbar.list.forEach(v => {
                if (this.filterModelList(v)) list.push(v)
            })
            if (!list.length) return false
            tabbar.list = list
            app.globalData['tempData'].activity_tab_bar_data = tabbar
            this.tabbar_data = tabbar
        },

        async setDiyImage(image_model_data) {
            this.image_model_data = image_model_data
            await this.setDiyImageBg()
            await this.setDiyImageList()

            this.loading = false
            uni.hideLoading()
        },
        
        async setDiyImageBg() {
            const bg_src = this.image_model_data['background_image']

            const image_info = await uniapp_api.getImageInfo(bg_src)
            const {width, height, path} = image_info || {}
            if (!path) return this.$uni.showModal('背景图片下载失败，请检查图片地址')
            const scale = this.canvas_width / width
            const canvas_height = height * scale
            this.diy_image_scale = this.canvas_width / 375
            this.canvas_height = canvas_height
            this.diy_image_bg_path = path
        },
        
        async setDiyImageList() {
            const list = JSON.parse(JSON.stringify(this.image_model_data['list']))
            const diy_list = []
            
            list.forEach((item, index) => {
                const data = item.data || {}
                
                if (this.filterModelList(data)) {
                    item.key = `${item.data.type || item.type}_${index}`
                    data.x *= this.diy_image_scale
                    data.y *= this.diy_image_scale

                    const width = data.width || 100
                    const height = data.height || 100
                    data.width = width * this.diy_image_scale
                    data.height = height * this.diy_image_scale
                    
                    diy_list.push(item)
                }
            })
            
            this.diy_image_list = diy_list
        },
        


        setNavigationBar(data) {
            let frontColor = data.color || data.fontColor || '#000000'
            if (frontColor === 'white') frontColor = '#ffffff'
            if (frontColor === 'black') frontColor = '#000000'

            uni.setNavigationBarColor({
                frontColor,
                backgroundColor: data.background_color || data.backgroundColor || '#ffffff',
                animation: {
                    duration: 300,
                    timingFunc: 'linear'
                }
            })

            if (data.titleText) this.$uni.setNavigationBarTitle(data.titleText)
        },

        setDefaultModel() {
            // 把活动主图设置为大图
            if (this.detail.logo) {
                this.page_diy_model.push({
                    type: 'image',
                    data: {
                        src: this.detail.logo,
                        type: 0
                    }
                })
            }

            const defaultActivityTitleModel = {type: 'activity_title'}
            // OA开启了不显示活动创建时间，默认模版不显示活动创建时间，使用了模版的活动需要自己在diy里面关闭
            if (this.rankSet['not_show_create_time']) {
                defaultActivityTitleModel.data = {create_time_hide: true}
            }
            this.page_diy_model.push(defaultActivityTitleModel)
            this.page_diy_model.push({type: 'icon_list'})

            // 开启了进入活动自动兑换的，不显示活动按钮
            if (!this.detail.conf.active.enter_activity_auto_exchange) {
                this.page_diy_model.push({type: 'activity_button'})
            }

            this.page_diy_model.push({type: 'step_data'})

            this.setModelList()
        },


        // 根据不同的角色和活动权限，判断那些触发不显示
        setModelList() {
            this.page_diy_model.forEach(list_item => {
                // 暂时是否显示只判断icon_list，其他模块的通过触发事件来拦截
                if (list_item.type === 'icon_list' && list_item.data?.list?.length) {
                    list_item.data.list.forEach(v => v.show = this.filterModelList(v))
                } else {
                    list_item.show = this.filterModelList(list_item.data || {})
                }
                
                if (list_item.type === 'button') {
                    list_item.show = this.filterModelList(list_item.data || {})
                }
                
                if (list_item.type === 'step_data') {
                    const {platform} = uni.getSystemInfoSync()
                    const not_show_platform = ['mac', 'windows', 'linux']
                    list_item.show = this.is_joining && !not_show_platform.includes(platform)

                    const default_data = {
                        type: '',
                        more_show: false
                    }

                    list_item.data ||= default_data
                    const isObject = value => {
                        return value !== null && typeof value === 'object' && !Array.isArray(value)
                    }
                    if (!isObject(list_item.data) || !Object.keys(list_item.data).length) {
                        list_item.data = default_data
                    }
                }
            })
            uni.hideLoading()
            this.loading = false
        },

        filterModelList(data) {
            const rank_set = this.rankSet

            if (data.type === 'manage' && !this.is_my_activity) return false
            if (data.type === 'join_popup' && this.is_joining) return false
            if (data.type === 'user_activity_detail' && !this.is_joining) return false
            if (data.type === 'home_page' && rank_set['shield_other']) return false
            if (
                data.type === 'exchange_calendar' &&
                (!this.is_joining || (this.detail.conf.active.enter_types === 3 && !this.checked))
            ) return false
            if (data.type === 'point_page' && this.detail.conf.active.map_types === 101) return false
            if (data.type === 'redpack_record' && !rank_set['redpack']) return false
            if (data.type === 'gift_goods' && !rank_set['gift_goods']) return false
            if (data.type === 'today_sign' && !rank_set['morning_daily_sign']) return false
            if (data.type === 'honor_medal' && !rank_set['reward_shop_closed']) return false
            if (data.type === 'ai_sport' && !rank_set.AI_motion) return false
            if (
                data.type === 'certificate' &&
                (this.detail.conf.active.map_types === 101 ||
                    this.detail.conf.active.certificate_close)
            ) return false
            if (data.type === 'medal_list' && !rank_set['medal_open']) return false
            if ((data.type === 'lottery_record' || data.type === 'lottery') && !rank_set['lottery_open']) return false
            if (data.type === 'answer_list' && !rank_set.exam_open) return false
            if (data.type === 'sport_talk' && !rank_set['open_sport_moment']) return false
            if (data.type === 'biubiubiu' && !rank_set['barrage']) return false
            if (data.type === 'weekly_step' && rank_set['closed_weekly_report']) return false
            if (data.type === 'today_history' && rank_set['today_history_closed']) return false
            if (data.type === 'feedback' && this.detail.conf.active.close_feedback) return false
            if (data.type === 'together_pic_list' && !this.checked) return false
            if (data.type === 'run_sport' && (!this.checked || !rank_set['run_sport'])) return false
            if (data.type === 'bind_wechat_h5_openid') {
                const url = app.globalData['shop_info']?.['shop_set']?.['shop_conf_set']?.url || ''
                // 没有配置关注公众号的链接，不显示   已绑定，不显示
                if (!url || this.wx_h5_openid) return false
            }
            if (data.type === 'ranking_page') return this.ranklistShow
            if (data.type === 'light_city') return this.lightCityMapShow
            if (data.type === 'exchange_step') return !!this.is_joining
            if (data.type === 'exam_prize') return this.examPrizeShow
            if (data.type === 'daily_step_unlock_exam_popup') return this.dailyStepUnlockExam && this.is_joining

            return true
        },

        // 开启了进入活动无需点击自动兑换
        async autoExchange() {
            if (!this.is_joining || !this.checked) return
            if (this.time_period_step) return

            // OA开启了每天自动兑换
            const OA_open = !!this.rankSet['one_day_auto_exchange']
            // 简约版自动兑换
            const is_simple = this.is_simple
            // 活动设置了自动兑换
            const active_set = !!this.detail.conf.active.enter_activity_auto_exchange

            if (OA_open || is_simple || active_set) {
                await this.exchangeRequest()

                await this.getUserStatus(true)

                await this.dailyAutoSign(true)
            }
        },

        async exchangeRequest(exchange_date = new Date(utils.getDay(0, false, '/')).getTime() / 1000) {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.exchange/exchange_step',
                data: {
                    active_id: this.id,
                    exchange_date
                }
            })
            return res
        },

        changeDetailContent(news_id) {
            xwy_api.ajax({
                url: "front.news/news_details",
                data: {
                    access_token: app.globalData['access_token'],
                    news_id
                },
                success: res => {
                    console.log(res);
                    uni.hideLoading();

                    uni.hideLoading()
                    if (!res.data || !res.data['news_details']) {
                        uni.showModal({
                            title: '提示',
                            content: res.info || '文章内容获取失败',
                            showCancel: false,
                            success: () => uni.navigateBack()
                        })
                        return false
                    }

                    const detail = res.data['news_details']


                    if (detail.video_url) {
                        let video_type = 'txv_id'
                        if (detail.video_url.startsWith('http://') || detail.video_url.startsWith('https://')) {
                            video_type = 'http'
                        }
                        detail.video_type = video_type
                    }


                    if (detail.content) {
                        detail.content = utils.newsContentInit(detail.content)
                    }
                    this.news_detail = detail
                }
            })
        },


        passwordDialogShow() {
            if (this.detail.conf.active.password && this.no_attend) {
                // 体验版不需要输入密码
                // if (app.globalData.evn_version === 'trial' || app.globalData.evn_version === 'develop') {
                if (app.globalData['evn_version'] === 'trial') {
                    xwy_api.alert('此活动设置了活动密码，请勿报名参与活动！！！')
                    return false
                }


                this.password_dialog_show = true
                const password = my_storage.getActivityPassword(this.id)
                if (password) {
                    this.password = password
                    this.checkActivityPassword(password)
                }
                const timeout = setTimeout(() => {
                    this.$refs.input_password.open()
                    clearTimeout(timeout)
                }, 30)

                return false
            }

            this.showNoticePopup()
        },

        passwordInputConfirm(val) {
            if (!val) {
                xwy_api.alert('请输入密码', {
                    success: () => this.$refs.input_password.open()
                })

                return false
            }
            this.checkActivityPassword(val)
        },

        async checkActivityPassword(password) {
            this.$uni.showLoading('密码验证中...')

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/check_active_password',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id,
                    password
                }
            })
            uni.hideLoading()

            if (res?.['status']) {
                my_storage.rememberActivityPassword(this.id, password)
                this.$refs.input_password.close()
                uni.showToast({
                    title: '密码正确',
                    icon: 'success'
                })
                await this.showNoticePopup()
                return false
            }


            xwy_api.alert(res && res['info'] || '密码错误', {
                success: () => this.$refs.input_password.open()
            })
        },


        passwordInputClose() {
            this.copy(this.id, true)

            if (getCurrentPages().length > 1) return uni.navigateBack()

            // 纯净版并且没有上一页面，重新弹出输入密码窗口
            if (app.globalData['shop_info']?.['extend_set']?.['shield_other_active']?.active_id) {
                // xwy_api.alert('请输入活动密码', {
                // 	success: () => this.$refs.input_password.open()
                // })
                uni.showModal({
                    title: '提示',
                    content: '请输入活动密码',
                    confirmText: '输入密码',
                    cancelText: '个人中心',
                    success: res => {
                        this.$refs.input_password.open()
                        if (res.confirm) return false

                        uni.navigateTo({
                            url: '/pages/user/user'
                        })
                    }
                })
                return false
            }

            this.$uni.reLaunch('/pages/index/index')
        },


        async showNoticePopup() {
            const active_details_notice = this.detail?.conf?.active?.active_details_notice
            if (
                !this.rankSet.active_details_notice ||
                !active_details_notice?.open ||
                !active_details_notice?.news_id
            ) return
            if (this.user_details?.id) return

            uni.showLoading({
                mask: app.globalData['evn_version'] !== 'trial'
            })
            const news_detail = await xwy_api.getNewsDetail(active_details_notice.news_id)
            this.detail.conf.active.active_details_notice.agree = false
            this.detail.conf.active.active_details_notice.news_detail = news_detail
            uni.hideLoading()
            this.uniPopupOpen('notice_popup')
        },

        noticeAgreeChange() {
            const active = this.detail.conf.active
            active.active_details_notice.agree = !active.active_details_notice.agree
            this.$set(this.detail, 'conf.active', active)
        },

        noticeConfirm() {
            const {agree, news_detail} = this.detail.conf.active.active_details_notice
            if (!agree) {
                uni.showToast({
                    title: `请勾选 我已阅读并同意《${news_detail.title || '活动须知'}》`,
                    icon: 'none'
                })
                return false
            }
            this.uniPopupClose('notice_popup')
        },

        getMyStep(update = false) {

            // PC不获取步数（无法获取步数）
            const {platform} = uni.getSystemInfoSync()
            const not_show_platform = ['mac', 'windows', 'linux']
            let not_get_step = not_show_platform.includes(platform)
            
            // h5无法获取到微信运动步数
            // #ifdef H5
            not_get_step = true
            // #endif

            if (not_get_step) {
                this.loading = false
                uni.hideLoading()
                this.getPageDiySet()
                this.getDiyMapSet()
                this.getSimpleTopList()
                return false
            }


            if (app.globalData['today_step'] && !update) {
                uni.hideLoading()
                this.loading = false
                this.today_step = app.globalData['today_step']
                this.getStepDay7()
                this.autoExchange()
                this.getPageDiySet()
                this.getDiyMapSet()
                this.getSimpleTopList()
                return false
            }

            if (update) this.$uni.showLoading('步数更新中...')

            if (!update) {
                // 防止uni.loading遮挡隐私弹窗
                uni.hideLoading()
                this.loading = false
            }

            xwy_api.getWeRunData(res => {
                uni.hideLoading()
                if (update) this.$uni.showToast('步数更新成功', 'success')
                this.loading = false
                this.today_step = null
                if (res?.data?.['crypt_data']?.['stepInfoList']?.length) {
                    const step_list = res.data['crypt_data']['stepInfoList']
                    this.today_step = step_list[step_list.length - 1].step
                    this.getStepDay7()
                    this.autoExchange()
                }
                this.getPageDiySet()
                this.getDiyMapSet()
                this.getSimpleTopList()

                this.saveStep2DB()
            })
        },

        // 因为需要请求user_mobile_step_list接口后台才会把用户的微信运动步数存到数据库。晓阳客户的活动需要导出用户的微信运动步数，但是有很多人是不会进入到有请求user_mobile_step_list接口的页面的，所以他们的微信运动步数没有存在数据库里面，导致导出的时候没有微信运动步数数据，所以这里手动调用下接口，把用户的微信运动步数数据存下数据库。
        saveStep2DB() {
            if (this.id !== '3a847bc62a6eb4d951b0a86ab340575b') return
            // 怕刚解密，接口获取不到最新的步数，所以延迟1秒执行
            setTimeout(() => {
                this.xwy_api.request({url: 'front.flat.sport_step.step/user_mobile_step_list'})
            }, 1000)
        },

        getStepDay7() {
            const step_list = app.globalData['step_list']
            const step_day7 = step_list.slice(24)
            const categories = [],
                series = [
                    {
                        name: "最近7日步数",
                        textColor: '#ff9900',
                        data: []
                    }
                ]

            step_day7.forEach(v => {
                const day = new Date(v.timestamp * 1000).getDate()
                categories.push(day)
                series[0].data.push(v.step)
            })

            const chart_data = {
                categories,
                series
            }
            this.chartData = JSON.parse(JSON.stringify(chart_data))
        },

        async getUserStartEndStepDetails() {
            if (!this.time_period_step) return
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.exchange.period_time_step.userSubmit/user_start_end_step_details',
                data: { active_id: this.id }
            })

            if (res?.data?.['submit_details']) this.time_period_step_today_submit = 'start'
            if (res?.data?.['submit_details']?.['start_step']) {
                this.time_period_step_today_start_step = res.data['submit_details']['start_step']
            }
            if (res?.data?.['submit_details']?.['real_step']) {
                this.time_period_step_today_submit = 'end'
                this.time_period_step_today_submit_step = res.data['submit_details']['real_step']
            }
        },

        timePeriodStepExchange() {
            const now_time = new Date().getTime()
            const begin_time = this.detail.begin_time * 1000
            if (now_time < begin_time) {
                return uni.showToast({ title: '活动未开始', icon: 'none' })
            }
            const end_time = this.detail.end_time * 1000
            if (now_time > end_time) {
                return uni.showToast({ title: '活动已结束', icon: 'none' })
            }

            if (this.time_period_step_today_submit === 'end') return uni.showToast({
                title: `已提交${this.time_period_step_today_submit_step}步，无需重复提交`,
                icon: 'none'
            })

            if (this.time_period_step_today_submit === 'start') {
                return uni.showModal({
                    title: '提示',
                    content: '只能提交一次，是否要提交？',
                    success: res => {
                        res.confirm && this.timePeriodStepExchangeSubmit()
                    }
                })
            }
            this.timePeriodStepExchangeSubmit()
        },

        async timePeriodStepExchangeSubmit() {
            uni.showLoading({ title: '获取最新步数...', mask: true })
            await xwy_api.getWeRunData(async step_res => {
                if (step_res?.status !== 1) {
                    uni.hideLoading()
                    return xwy_api.alert(step_res?.info || '获取步数失败')
                }
                uni.showLoading({title: '提交中...', mask: true})
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.exchange.period_time_step.userSubmit/submit_start_end_step',
                    data: {active_id: this.id}
                })
                uni.hideLoading()
                if (res?.status !== 1) return xwy_api.alert(res?.info || '操作失败')
                uni.showToast({title: res.info || '操作成功', icon: 'none'})
                await this.getUserStartEndStepDetails()
            })
        },


        async mustExamDailyCheck() {
            if (!this.rankSet?.mustExamThenExchangeStep) return true

            const must_exam_daily = this.detail.conf.active.must_exam_daily
            if (!must_exam_daily) return true

            // types: 没有或0 - 兑换前需要答题   1 - 每兑换多少步获得1次答题机会
            // 这里是 兑换前需要答题，所以types有值的话就不用判断用户今天是否已答题
            let {open, types, score, exam_id} = must_exam_daily
            if (!open || !exam_id || types) return true


            if (!exam_id) return true

            score = Number(score) || 0

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exam.sportExam/user_today_exam_details',
                data: {active_id: this.id}
            })
            uni.hideLoading()

            if (res?.status !== 1) {
                const {confirm} = await this.$uni.showModal(res?.info || '请答题', {
                    showCancel: true,
                    confirmText: '去答题'
                })
                if (confirm) this.toMustExamDaily(exam_id)
                return false
            }

            return true
        },

        toMustExamDaily(exam_id) {
            this.$uni.navigateTo(`/pages/likou_dati/pages/answer/answer_question/answer_question?exam_id=${exam_id}&activityID=${this.id}&task_activity_step_answer=1`, {
                events: {
                    success: score => {
                        console.log('🚀答题成绩', score)
                        this.mustExamDailyEnd = true
                    }
                }
            })
        },

        dailyStepUnlockExamPopupShow() {
            this.$refs.dailyStepUnlockExamPopup.open()
        },

        dailyStepUnlockExamEnd() {
            this.getUserStatus(true)
        },

        // 在 onShow 调用这个方法。需要在onShow调用，不然答完题后就会立即执行this.exchange()
        mustExamDailyEndCheck() {
            if (!this.mustExamDailyEnd) return
            this.mustExamDailyEnd = false
            this.exchange()
        },


        async exchange(needUpdateStep = false) {
            if (this.$refs.expirationReminder.isExpired(this.detail)) {
                this.$refs.expirationReminder.open(this.detail)
                return false
            }

            // #ifdef H5
            const a = true
            if (a) {
                this.$uni.showModal('请在小程序内兑换')
                return false
            }
            // #endif

            if (!this.is_joining) {
                this.$uni.showToast('请先报名活动')
                return false
            }
            if (!this.checked) {
                this.$uni.showToast('请等待报名审核通过')
                return false
            }


            const team_required = this.detail.conf.active.team_required
            const user_team = this.user_details?.team_id
            const must_submit = this.must_submit
            let check = true
            for (let i = 0; i < must_submit.length; i++) {
                const item = must_submit[i]
                if (item.rules === 1 && !item.value) {
                    check = false
                    break
                }
            }
            if (team_required && !user_team) check = false
            if (!check) {
                uni.showModal({
                    title: '提示',
                    content: '报名信息不完善，请完善报名信息。',
                    cancelText: '稍后完善',
                    confirmText: '立即完善',
                    success: res => {
                        res.confirm && this.updateAttendDetailShow()
                    }
                })
                return false
            }


            if (this.time_period_step) {
                this.timePeriodStepExchange()
                return false
            }

            if (!await this.mustExamDailyCheck()) return false

            this.$uni.showLoading(needUpdateStep === 'update' ? '更新中...' : '兑换中...')

            // 7日步数模版需要更新最新的步数
            if (needUpdateStep === 'update') {
                const res = await this.xwy_api.getWeRunData()
                if (res === 'err') return false
                this.today_step = app.globalData.today_step
            }

            const exchange_date = new Date(utils.getDay(0, false, '/')).getTime() / 1000

            const res = await this.exchangeRequest(exchange_date)

            if (needUpdateStep === 'update') {
                uni.hideLoading()
                this.$uni.showToast('步数已更新')
                this.$refs.day7StepTemplate.exchangeSuccess(res)
                if (res?.status === 1) this.getDetail()
                return true
            }

            if (res?.status !== 1) {
                if (await this.maxDailyStepSubmitReasonAgainShow()) {
                    uni.hideLoading()
                    return this.$refs.maxDailyStepSubmitReason.open()
                }

                uni.hideLoading()
                this.$uni.showModal(res.info || '兑换失败', {title: '兑换失败'})
                return false
            }


            if (this.rankSet['daily_digital_book']) {
                this.exchangeSuccessShowEveryDayCertificate()
                return false
            }

            if (this.diyMap && this.$refs.diyMap) {
                this.$refs.diyMap.exchangeSuccessShow(res)
            } else {
                this.exchange_success_info = res.info || '兑换成功'
                if (res.data?.['exchange_result']?.['show_text']?.length) {
                    const show_text = res.data['exchange_result']['show_text']
                    show_text.forEach(v => {
                        if (v.text && v.text.includes('红包')) v.is_redpack = true
                    })
                    this.exchange_success_tip_text_list = show_text
                }
            }

            this.getDetail()
        },


        // 超过每日兑换步数上限后，系统会自动弹出提示框，参与职工可简要说明具体情况，可选择上传照片加以佐证
        maxDailyStepSubmitReasonShow() {
            if (!this.maxDailyStepSubmitReasonOpen) return false

            const step_max = Number(this.detail.conf.active.max_num)
            const today_step = this.today_step

            return today_step > step_max
        },

        // 超出上限后兑换，并且今天没提交过审核的，弹出步数补交弹窗
        async maxDailyStepSubmitReasonAgainShow() {
            if (!this.maxDailyStepSubmitReasonShow()) return false


            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exchange.max_step.submitReason/reason_list',
                data: {
                    myself: 1,
                    active_id: this.id,
                    page: 1,
                    perpage: 1
                }
            })

            const list = res?.data?.list?.data
            if (!list?.length) return true

            // 今天提交过，不弹
            return list[0].create_time.split(' ')[0] !== this._utils.getDay(0, true)
        },


        maxDailyStepSubmitReasonPopupClose() {
            this.uniPopupClose('maxDailyStepSubmitReason')
        },

        exchangeSuccessPopupClose() {
            this.exchange_success_info = ''
            this.exchange_success_tip_text_list = []

            if (this.maxDailyStepSubmitReasonShow()) {
                this.uniPopupOpen('maxDailyStepSubmitReason')
            }
        },


        async updateHeadimg() {
            uni.navigateTo({
                url: `/pages/other/headimg-list/headimg-list?active_id=${this.id}`,
                events: {
                    newImg: async obj => {
                        if (!obj?.src) return
                        this.$uni.showLoading('修改中...')
                        if (!obj.temp) return this.updateAttendDetail(obj.src)
                        const data = {
                            temp_data: {
                                path: obj.src
                            },
                            is_temp: 5
                        }
                        if (obj.size) data.temp_data.size = obj.size
                        const headimg = await xwy_api.uploadOneImage(data)
                        this.updateAttendDetail(headimg)
                    }
                }
            })
        },
        
        async locationDistanceCheck() {
            if (!this.joinDistanceTips) return true
            const location = await this.getLocation() || {}
            if (!location) {
                await this.$uni.showModal('位置获取失败，请检查系统及微信GPS设置和小程序授权设置')
                return false
            }
            const {latitude: user_lat, longitude: user_lng} = location
            const {location: set_location, distance: set_distance} = this.detail.conf.active.join_location_distance_limit
            const {latitude: set_lat, longitude: set_lng, name: location_name} = set_location
            
            const distance = utils.getDistance(user_lat, user_lng, set_lat, set_lng) / 1000
            if (distance > set_distance) {
                await this.$uni.showModal(`当前距离${location_name}${distance}公里，超过${set_distance}公里，无法参加活动`)
                return false
            }
            
            return true
        },
        
        async getLocation() {
            return new Promise(resolve => {
                uni.getLocation({
                    type: 'gcj02',
                    success: res => {
                        const {latitude, longitude} = res
                        const location = {latitude, longitude}
                        this.location = location
                        resolve(location)
                    },
                    fail: () => resolve(false)
                })
            })
        },

        toManage() {
            this.$uni.navigateTo(`../admin/manage?id=${this.id}`)
        },


        async joinActivity() {
            if (this.loading) return

            // #ifdef MP-WEIXIN
            const official_account_open = this.detail.conf.active.official_account_open || 0
            if (official_account_open === 1) {
                const {scene} = wx.getLaunchOptionsSync()
                if (scene !== 1035) return this.$uni.showModal('需从公众号进入活动才能报名')
            }
            // #endif
            
            if (!await this.locationDistanceCheck()) return
            

            if (this.detail.conf.active.submit) {
                const submit = this.detail.conf.active.submit
                const now_time = new Date().getTime()
                const reg = new RegExp('-', 'g')
                if (submit.begin) {
                    const begin = submit.begin.replace(reg, '/')
                    const begin_time = new Date(begin).getTime()
                    if (now_time < begin_time) {
                        xwy_api.alert('还没有到活动报名时间，请在' + submit.begin + '后再报名')
                        return false
                    }
                }
                if (submit.end) {
                    const end = submit.end.replace(reg, '/')
                    const end_time = new Date(end).getTime()
                    if (now_time > end_time) {
                        xwy_api.alert('报名截止时间为' + submit.end + '，现已超过报名截止时间，无法报名')
                        return false
                    }
                }
            }

            // 判断是否开启付费报名
            if (!this.rankSet['active_pay']) {
                this.join_popup_show = true
                return
            }

            // popup_open: true 会隐藏页面广告，因为广告会把弹窗遮挡住。这里避免打开付费弹窗时，弹窗被广告遮挡
            await this.$refs.registration.check(this.id, success => {
                if (success) {
                    this.join_popup_show = true
                } else {
                    this.popup_open = false
                }
            })

        },


        async teamManage() {
            if (app.globalData['userid'] === this.detail.userid) {
                return uni.navigateTo({
                    url: `/pages/activity/admin/team_list?is_admin=1&id=${this.id}`
                })
            }
            const team_id = this.user_details?.team_id
            if (!team_id) return uni.showToast({
                title: '还没有加入队伍',
                icon: 'none'
            })
            uni.showLoading({mask: true})
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.admin.teamManager/check_is_team_manager',
                data: {team_id}
            })
            uni.hideLoading()
            if (res?.status !== 1) return uni.showToast({
                title: '你不是队长，无法进入队伍管理',
                icon: 'none'
            })
            uni.navigateTo({
                url: `/pages/activity/admin/activity_user_list?is_team_manager=1&id=${this.id}&team_id=${team_id}`
            })
        },

        updateAttendDetailShow() {
            this.update_attend_details = true
            this.join_popup_show = true
        },

        mustValueChange(e, index) {
            const item = this.must_submit[index]
            item.value = item.options[e.detail.value].text
            this.$set(this.must_submit, index, item)
        },


        cancelJoin() {
            this.join_popup_show = false
            this.update_attend_details = false
        },

        joinAjax() {
            if (this.update_attend_details) {
                this.updateAttendDetail()
                return false
            }

            /* if (this.detail.conf.active.password && !this.password) {
				uni.showToast({
					title: '请输入活动密码',
					icon: 'error'
				})
				return false
            } */

            if (!this.detail.conf.active.closed_team && this.detail.conf.active.team_required && !this.team_id) {
                uni.showToast({
                    title: '请选择队伍',
                    icon: 'error'
                })
                return false
            }

            this.joining()
        },


        getMustSubmitData() {
            const must_submit = JSON.parse(JSON.stringify(this.must_submit))
            for (let i = 0; i < must_submit.length; i++) {
                const v = must_submit[i]
                v.options && delete v.options
                if (v.rules === 1 && v.value === '') {
                    let tips = '输入'
                    if (v.types === 2) tips = '选择'
                    this.$uni.showToast(`请${tips}${v.title}`)
                    return false
                }
                if (v.types === 3 && v.value !== '' && !/^1[3456789]\d{9}$/.test(v.value)) {
                    this.$uni.showToast(`请输入正确的${v.title}`)
                    return false
                }
            }
            console.log(must_submit)
            let must_submit_str = JSON.stringify(must_submit)
            must_submit_str = must_submit_str.replace(/·/g, '-')
            return base64['encode'](must_submit_str)
        },

        updateAttendDetail(headimg) {
            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id
            }
            if (this.team_id) data.team_id = this.team_id
            if (headimg) data.headimg = headimg

            const must_submit = this.getMustSubmitData()
            if (must_submit === false) return false
            data.must_submit = must_submit

            this.$uni.showLoading('修改中...')

            xwy_api.ajax({
                url: 'front.flat.sport_step.user/update_attend_details',
                data,
                success: res => {
                    if (res?.status !== 1) return this.$uni.showModal(res?.info || '修改失败')

                    this.$uni.showToast('修改成功', 'success')

                    const old_team_id = this.user_details?.team_id
                    if (data.team_id && (!old_team_id || data.team_id !== old_team_id)) {
                        const team_slogan = this['team_slogan_' + data.team_id]
                        if (team_slogan) this.$uni.showModal(`队伍名称: ${this.team_name}。队伍口号: ${team_slogan}`, {title: '队伍口号'})
                    }

                    this.cancelJoin()
                    this.getDetail()
                }
            })
        },


        joining() {

            const data = {
                active_id: this.id,
                access_token: app.globalData['access_token']
            }
            if (this.team_id) data.team_id = this.team_id


            if (this.must_submit && this.must_submit.length) {
                const must_submit = this.getMustSubmitData()
                if (must_submit === false) return false
                data.must_submit = must_submit
            }

            data.nickname = this.username


            this.loading = true
            this.$uni.showLoading('报名中...')


            xwy_api.ajax({
                url: 'front.flat.sport_step.user/submit_attend_active',
                data,
                success: res => {
                    this.loading = false
                    uni.hideLoading()
                    console.log('报名活动', res)
                    if (!res.status) {
                        uni.showModal({
                            title: '报名失败',
                            content: res.info || '报名失败',
                            showCancel: false
                        })
                        return false
                    }

                    this.join_popup_show = false
                    uni.showToast({
                        title: res.info || '报名成功',
                        icon: 'success'
                    })

                    setTimeout(() => {
                        uni.showLoading({
                            mask: true
                        })
                        this.getDetail()
                    }, 1000)
                }
            })
        },


        toTopList() {
            if (this.time_period_step) {
                return uni.navigateTo({
                    url: `/pages/ranking-list/ranking-list?id=${this.id}`
                })
            }

            uni.navigateTo({
                url: `/pages/activity/user/ranking_list?id=${this.id}&user_team_id=${this.user_details?.team_id || 0}`
            })
        },

        studyNews() {
            if (!this.is_joining) {
                this.$uni.showToast('未报名')
                return this.joinActivity()
            }
            if (!this.checked) return this.$uni.showToast('报名未通过审核')
            
            const {study_news_set, study_news_study_type} = this.detail.conf.active
            const study_type = study_news_set?.study_type || study_news_study_type || 0
            let url = `/pages/news/study-news-exam/list?active_id=${this.id}&category_id=${this.study_news_category_id}&study_type=${study_type}`
            
            const {article_bind_exam, reward_step_by_reading} = this.rankSet
            if (article_bind_exam) url += `&article_bind_exam=1`
            if (reward_step_by_reading) url += `&reward_step_by_reading=1`
            
            this.$uni.navigateTo(url)
        },
        

        toLightCityMap() {
            if (!this.is_joining) {
                this.$uni.showToast('未报名')
                return this.joinActivity()
            }
            if (!this.checked) return this.$uni.showToast('报名未通过审核')
            this.$uni.navigateTo(`../light-up-map/map?id=${this.id}`)
        },

        toActivityTotalRanking() {
            this.$uni.navigateTo(`/pages/activity/user/ranking_list?id=${this.id}&activity_total=1`)
        },

        async bindWechatH5Openid() {
            const {shop_info: {shop_set: {shop_conf_set: {subscribe: {url}, system_page}}}} = app.globalData
            
            if (!url) return this.$uni.showModal('未配置绑定公众号')
            if (this.wx_h5_openid) return this.$uni.showToast('已绑定，无需再次绑定')
            
            this.$uni.showLoading()
            const res = await this.xwy_api.request({url: 'front.user.user/create_copy_code_string'})
            uni.hideLoading()

            const code = res?.data?.code
            if (!code) return this.$uni.showModal(res?.info || `${this.subscribe.title}失败`)
            this.$uni.setClipboardData(code)
            
            const modal = await this.$uni.showModal(res?.info || '绑定消息提醒口令复制成功。请在公众号里粘贴进行绑定操作，口令30分钟内有效。', {
                title: system_page || '绑定公众号',
                showCancel: true,
                confirmText: '去绑定'
            })
            modal.confirm && this.$uni.navigateTo(`/pages/other/webview?url=${url}`)
        },

        showActiveSharePopup() {
            this.$refs.activeShare.open({
                page: 'pages/activity/user/detail',
                scene: 'id=' + this.detail.id + '&uid=' + this.userid,
                qrcode_logo: this.rankSet.closed_AD && this.detail.conf?.active?.qrcode_logo
            })
        },



        todayNewsPopupOpen() {
            if (!this.active_more_data?.['active_conf_set']) return
            this.$refs.todayNewsPopup?.open()
        },
        

        copyMapType() {
            const map_type_list = [
                {"title": "手绘地图", "type": 102},
                {"title": "长征地图", "type": 1},
                {"title": "冬奥会地图", "type": 2},
                {"title": "亚运会地图", "type": 3},
                {"title": "自定义地图", "type": 100},
                {"title": "无需设置地图", "type": 101}
            ]

            const active_type = map_type_list.find(v => v.type === this.detail.conf.active.map_types);
            if (!active_type) return false;

            const copy_data = `活动id: ${this.id}; 活动地图类型: ${active_type.title}(${active_type.type})`;
            this.copy(copy_data, true);
        },

        fragmentsUnlock() {
            if (!this.is_joining) return this.joinActivity()
            if (!this.checked) return this.$uni.showToast('报名未通过审核')
            this.exchange()
        },



        copy(data, hide_toast = false) {
            uni.setClipboardData({
                data,
                success: () => hide_toast ? uni.hideToast() : this.$uni.showToast('复制成功', 'none', 500)
            })
        },

        uniPopupClose(ref) {
            this.popup_open = false
            this.$refs[ref].close()
        },

        uniPopupOpen(ref) {
            this.popup_open = true
            this.$refs[ref].open()
        }

    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
}

.logo {
    display: block;
    width: 100vw;
    height: auto;
}

.share-btn {
    padding: 0;
    margin: 0;
    border: none !important;
    width: 40px;
    min-width: 40px;
    height: 40px;
    line-height: 40px;
}

.bdb-10 {
    border-bottom: 10px solid #f8f8f8;
}


.icon-item {
    padding: 10px 0;
    width: calc(100% / 4);
    /* border-top: 1px solid #eee; */
    box-sizing: border-box;
}

.weekly {
    position: fixed;
    top: -9999px;
    left: -9999px;
}

.headimg {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}

.user-today-step {
    position: relative;
    padding: 10px;
}

.user-today-step image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
}

.user-today-step .look-more {
    position: absolute;
    right: 10px;
    bottom: 16px;
}

.step-info-popup {
    width: 320px !important;
    padding: 10px 0;
}

.charts-box {
    padding: 40px 0 30px 20px;
    width: 260px;
    height: 200px;
}

.join-btn {
    width: 250px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 20px;
}

.join-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, .7);
    z-index: 9999;
}

.join-popup-c {
    border-radius: 10px;
    overflow: hidden;
}


.join-input {
    margin: 10px;
    padding: 10px;
    border: 1px solid #bbb;
    border-radius: 5px;
    width: 70vw;
    max-width: 400px;
}

.join-popup-btns {
    border-top: 1px solid #eee;
}

.join-popup-btn-cancel, .join-popup-btn-confirm {
    width: 50%;
    box-sizing: border-box;
    line-height: 44px;
}

.join-popup-btn-cancel {
    border-right: 1px solid #eee;
}




.uni-popup-info {
    position: relative;
    width: 280px;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    z-index: 999;
    left: 50%;
    bottom: -5px;
    margin-left: -12px;
}

.detail-popup {
    width: 95vw;
    padding-bottom: 15px;
}

.success {
    position: fixed;
    z-index: 99;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, .7);
}

.success .main {
    width: 300px;
    border-radius: 10px;
    overflow: hidden;
}

.success .close {
    margin: 20px 50px 0;
    line-height: 34px;
    border-radius: 17px;
}

.today-step-quanquan, .kaluli-quanquan {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    border: 12px solid #f90;
}

.audio {
    position: fixed;
    border: 1px solid #fff;
    top: 20px;
    right: 20px;
    background-color: rgba(0, 0, 0, .5);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    z-index: 9;
}

.audio_rotate {
    animation: rotate 2s linear infinite;
}

@keyframes rotate {
    100% {
        transform: rotate(360deg);
    }
}

.diy-page-button {
    box-sizing: content-box;
    margin: 0 !important;
    padding: 0 !important;
}

.diy-page-button::after {
    content: "";
    border: none;
}

.uni-noticebar { /* 去除公告栏插件自带样式 */
    margin-bottom: 0 !important;
}

#diy_canvas {
    position: fixed;
    left: -200vw;
}

.pt3 {
    padding-top: 3px;
}

.top-list {
    padding-bottom: 20px;
    margin-top: 30px;

    .top-list-title {
        position: relative;
        padding-left: 20px;
    }
    .top-list-title::after {
        content: "";
        position: absolute;
        top: 13px;
        left: 10px;
        width: 4px;
        height: 16px;
        background-color: #2d8cf0;
    }

    .top-list-index {
        width: 40px;
        min-width: 40px;
    }

    .top-list-top-3 {
        width: 30px;
        height: 30px;
        display: block;
    }

    .top-list-item {
        margin: 10px;
        padding: 10px 10px 10px 0;
        border-radius: 10px;
        box-shadow: 0 0 10px #eee;
    }

    .top-list-headimg {
        width: 60px;
        min-width: 60px;
        height: 60px;
        border-radius: 50%;
    }

    .top-list-middle {
        width: 100%;
        padding-left: 10px;
        overflow: hidden;
        box-sizing: border-box;
    }

    .top-list-name {
        line-height: 35px;
    }

    .top-list-right {
        width: 100px;
        min-width: 100px;
    }

    .top-list-right-item {
        width: 100px;
    }
}

//加大活动须知弹窗的z-index，必须要比导入账号密码登录的弹窗高，要遮住登录弹窗
.notice-popup .uni-popup {
    z-index: 999;
}
</style>
