<template>
    <view class="page bg-background" :style="{paddingBottom: tabbar_data ? '80px' : '10px'}">
        <view v-if="!is_look_team && type_list.length > 1" style="height: 40px;"></view>

        <xwy-ad v-if="!closed_AD && id" :activity_id="id" :ad_type="3"></xwy-ad>

        <template v-if="!is_look_team && type_list.length > 1">
            <view v-if="type_list.length <= 4" class="type-bar bg-white flex-row">
                <view
                    class="type-item text-center font14 color-content"
                    :class="{'activate-type': index === type_id_index}"
                    :style="'width: calc(100% / ' + type_list.length + ');'"
                    v-for="(item, index) in type_list"
                    :key="index"
                    @click="changeType(index)"
                >{{ item.name }}
                </view>
            </view>

            <view v-else class="bg-white type-bar">
                <scroll-view scroll-x="true" style="width: 100vw;">
                    <view class="flex-row">
                        <view
                            class="type-item text-center font14 color-content"
                            :class="{'activate-type': index === type_id_index}"
                            style="width: 100px; min-width: 100px; display: inline-block;"
                            v-for="(item, index) in type_list"
                            :key="index"
                            @click="changeType(index)"
                        >{{ item.name }}
                        </view>
                    </view>
                </scroll-view>
            </view>
        </template>


        <!--雷子客户的活动5f9a44d3027e4c7ff9c69d75febed541，字体需要大一点-->
        <view
            v-if="top_rank_text && top_rank_text.rules"
            :class="['p10', id === '5f9a44d3027e4c7ff9c69d75febed541' ? 'font14' : 'font12']"
            style="color: #e19898;"
            @click="copyId"
        >{{ top_rank_text.rules }}
        </view>

        <top-banner v-if="top_rank_banner.length" :list="top_rank_banner"></top-banner>


        <template v-if="top_rank_search && (type_id === 0 || type_id === 8)">
            <ranking-search :search_keyword.sync="search_keyword" @search="search"/>
        </template>

        <view v-if="type_id === 1 && every_day_toprank" class="flex-kai">
            <view></view>

            <picker mode="date" :value="get_day" :end="today" @change="getDayChange">
                <view class="p10 color-primary">
                    <text class="iconfont icon-sign-in-calendar"></text>
                    <text class="pl5">{{ get_day }}</text>
                </view>
            </picker>
        </view>

        <template v-if="type_list.find(item => item.id === 56)">
            <view v-show="type_id === 56" class="clear clearfix p10">

                <picker class="fr" mode="date" :value="date56" start="2020-01-01" end="2038-12-31"
                        fields="month" @change="date56Change">
                    <view class="date-picker flex-row">
                        <view>
                            <text :class="['iconfont color-sub', 'icon-calendar-' + date56YearMonth.month]"></text>
                        </view>
                        <view class="text-center date-picker-year-month color-content">
                            {{ date56YearMonth.year }}年{{ date56YearMonth.month }}月
                        </view>
                        <view>
                            <text class="iconfont icon-more color-disabled"></text>
                        </view>
                    </view>
                </picker>
            </view>
        </template>

        <view v-if="list.length && closed_AD && !isSearch" class="podium flex-row">
            <view class="top--2 bg-white">
                <view v-if="list.length >= 2">
                    <view class="headimg-view">
                        <template v-if="!isTeamRank">
                            <image @click="clickItem(list[1])" class="headimg" :src="list[1].headimg"/>
                            <view class="top-icon">
                                <text class="iconfont icon-crown-2"></text>
                            </view>
                        </template>

                        <template v-else>
                            <template v-if="list[1].headimg">
                                <image
                                    mode="aspectFill"
                                    @click="clickItem(list[1])"
                                    class="headimg"
                                    :src="list[1].headimg"
                                />
                                <view class="top-icon">
                                    <text class="iconfont icon-crown-2"></text>
                                </view>
                            </template>
                            <template v-else>
                                <view class="team-headimg">
                                    <text class="iconfont icon-crown-2"></text>
                                </view>
                            </template>
                        </template>
                    </view>
                    <view class="ellipsis color-title font14">{{ list[1].user_name }}</view>
                    <view class="font12 color-red">
                        <template v-if="type_id === 0 || type_id === 2 || type_id === 102">
                            {{ list[1].average_kilo || list[1].all_kilo || list[1].exchange_kilo || 0 }}{{ unit }}
                        </template>
                        <template v-if="type_id === 1 || type_id === 22 || type_id === 23 || type_id === 24 || type_id === 25 || type_id === 56">
                            {{ list[1].step || list[1]['allStep'] || 0 }}步
                        </template>
                        <template v-if="type_id === 7">{{ list[1].score || 0 }}分</template>
                        <template v-if="type_id === 8">{{ list[1].likes || 0 }}赞</template>
                        <template v-if="type_id === 18">{{ list[1]['share_friends'] || 0 }}</template>
                        <template v-if="type_id === 19">
                            <uni-dateformat :date="list[1].create_time" format="hh:mm:ss"></uni-dateformat>
                        </template>
                        <template v-if="type_id === 20">
                            {{ list[1]['integral_all'] || 0 }}{{ integral_unit }}
                        </template>
                        <template v-if="type_id === 26 || type_id === 27">
                            {{ list[1]['average_num'] || 0 }}步
                        </template>
                        <template v-if="type_id === 32 || type_id === 53">
                            <uni-dateformat :date="list[1].finished_time * 1000" format="yyyy-MM-dd hh:mm:ss"
                                            :threshold="[0, 0]"/>
                        </template>
                        <template v-if="type_id === 33 || type_id === 34">
                            {{ list[1]['percent_num'] || 0 }}
                        </template>
                        <template v-if="type_id === 35">{{ list[1]['finished_percent'] || 0 }}%</template>
                        <template v-if="type_id === 37">{{ list[1]['real_step'] || 0 }}步</template>
                        <template v-if="type_id === 39">{{ list[1]['multi_score'] }}分</template>
                        <template v-if="type_id === 40">
                            {{ list[1]['allIntegral'] }}{{ integral_unit }}
                        </template>
                        <template v-if="type_id === 51">{{ list[1]['sign_day'] }}天</template>
                        <template v-if="type_id === 49">
                            {{ list[1]['total_integral'] }}{{ integral_unit }}
                        </template>
                        <template v-if="type_id === 52 || type_id === 54">
                            {{ list[1].finished_count }}次
                        </template>
                        <template v-if="type_id === 57">{{ list[1]['finished_percent'] || 0 }}天</template>
                    </view>
                </view>
            </view>
            <view class="top--1 bg-white">
                <view>
                    <view class="headimg-view">
                        <template v-if="!isTeamRank">
                            <image @click="clickItem(list[0])" class="headimg" :src="list[0].headimg"/>
                            <view class="top-icon">
                                <text class="iconfont icon-crown-1"></text>
                            </view>
                        </template>

                        <template v-else>
                            <template v-if="list[0].headimg">
                                <image
                                    mode="aspectFill"
                                    @click="clickItem(list[0])"
                                    class="headimg"
                                    :src="list[0].headimg"
                                />
                                <view class="top-icon">
                                    <text class="iconfont icon-crown-1"></text>
                                </view>
                            </template>
                            <template v-else>
                                <view class="team-headimg">
                                    <text class="iconfont icon-crown-1"></text>
                                </view>
                            </template>
                        </template>
                    </view>

                    <view class="ellipsis color-title font14">{{ list[0].user_name }}</view>
                    <view class="font12 color-red">
                        <template v-if="type_id === 0 || type_id === 2 || type_id === 102">
                            {{ list[0].average_kilo || list[0].all_kilo || list[0].exchange_kilo || 0 }}{{ unit }}
                        </template>
                        <template v-if="type_id === 1 || type_id === 22 || type_id === 23 || type_id === 24 || type_id === 25 || type_id === 56">
                            {{ list[0].step || list[0]['allStep'] || 0 }}步
                        </template>
                        <template v-if="type_id === 7">{{ list[0].score || 0 }}分</template>
                        <template v-if="type_id === 8">{{ list[0].likes || 0 }}赞</template>
                        <template v-if="type_id === 18">{{ list[0]['share_friends'] || 0 }}</template>
                        <template v-if="type_id === 19">
                            <uni-dateformat :date="list[0].create_time" format="hh:mm:ss"></uni-dateformat>
                        </template>
                        <template v-if="type_id === 20">
                            {{ list[0]['integral_all'] || 0 }}{{ integral_unit }}
                        </template>
                        <template v-if="type_id === 26 || type_id === 27">
                            {{ list[0]['average_num'] || 0 }}步
                        </template>
                        <template v-if="type_id === 32 || type_id === 53">
                            <uni-dateformat :date="list[0].finished_time * 1000" format="yyyy-MM-dd hh:mm:ss"
                                            :threshold="[0, 0]"/>
                        </template>
                        <template v-if="type_id === 33 || type_id === 34">
                            {{ list[0]['percent_num'] || 0 }}
                        </template>
                        <template v-if="type_id === 35">{{ list[0]['finished_percent'] || 0 }}%</template>
                        <template v-if="type_id === 37">{{ list[0]['real_step'] || 0 }}步</template>
                        <template v-if="type_id === 39">{{ list[0]['multi_score'] }}分</template>
                        <template v-if="type_id === 40">
                            {{ list[0]['allIntegral'] }}{{ integral_unit }}
                        </template>
                        <template v-if="type_id === 51">{{ list[0]['sign_day'] }}天</template>
                        <template v-if="type_id === 49">
                            {{ list[0]['total_integral'] }}{{ integral_unit }}
                        </template>
                        <template v-if="type_id === 52 || type_id === 54">
                            {{ list[0].finished_count }}次
                        </template>
                        <template v-if="type_id === 57">{{ list[0]['finished_percent'] || 0 }}天</template>
                    </view>
                </view>
            </view>
            <view class="top--3 bg-white">
                <view v-if="list.length >= 3">
                    <view class="headimg-view">
                        <template v-if="!isTeamRank">
                            <image @click="clickItem(list[2])" class="headimg" :src="list[2].headimg"/>
                            <view class="top-icon">
                                <text class="iconfont icon-crown-3"></text>
                            </view>
                        </template>

                        <template v-else>
                            <template v-if="list[2].headimg">
                                <image
                                    mode="aspectFill"
                                    @click="clickItem(list[2])"
                                    class="headimg"
                                    :src="list[2].headimg"
                                />
                                <view class="top-icon">
                                    <text class="iconfont icon-crown-3"></text>
                                </view>
                            </template>
                            <template v-else>
                                <view class="team-headimg">
                                    <text class="iconfont icon-crown-3"></text>
                                </view>
                            </template>
                        </template>
                    </view>

                    <view class="ellipsis color-title font14">{{ list[2].user_name }}</view>
                    <view class="font12 color-red">
                        <template v-if="type_id === 0 || type_id === 2 || type_id === 102">
                            {{ list[2].average_kilo || list[2].all_kilo || list[2].exchange_kilo || 0 }}{{ unit }}
                        </template>
                        <template
                            v-if="type_id === 1 || type_id === 22 || type_id === 23 || type_id === 24 || type_id === 25 || type_id === 56">
                            {{ list[2].step || list[2]['allStep'] || 0 }}步
                        </template>
                        <template v-if="type_id === 7">{{ list[2].score || 0 }}分</template>
                        <template v-if="type_id === 8">{{ list[2].likes || 0 }}赞</template>
                        <template v-if="type_id === 18">{{ list[2]['share_friends'] || 0 }}</template>
                        <template v-if="type_id === 19">
                            <uni-dateformat :date="list[2].create_time" format="hh:mm:ss"></uni-dateformat>
                        </template>
                        <template v-if="type_id === 20">
                            {{ list[2]['integral_all'] || 0 }}{{ integral_unit }}
                        </template>
                        <template v-if="type_id === 26 || type_id === 27">
                            {{ list[2]['average_num'] || 0 }}步
                        </template>
                        <template v-if="type_id === 32 || type_id === 53">
                            <uni-dateformat :date="list[2].finished_time * 1000" format="yyyy-MM-dd hh:mm:ss"
                                            :threshold="[0, 0]"/>
                        </template>
                        <template v-if="type_id === 33 || type_id === 34">
                            {{ list[2]['percent_num'] || 0 }}
                        </template>
                        <template v-if="type_id === 35">{{ list[2]['finished_percent'] || 0 }}%</template>
                        <template v-if="type_id === 37">{{ list[2]['real_step'] || 0 }}步</template>
                        <template v-if="type_id === 39">{{ list[2]['multi_score'] }}分</template>
                        <template v-if="type_id === 40">
                            {{ list[2]['allIntegral'] }}{{ integral_unit }}
                        </template>
                        <template v-if="type_id === 51">{{ list[2]['sign_day'] }}天</template>
                        <template v-if="type_id === 49">
                            {{ list[2]['total_integral'] }}{{ integral_unit }}
                        </template>
                        <template v-if="type_id === 52 || type_id === 54">
                            {{ list[2].finished_count }}次
                        </template>
                        <template v-if="type_id === 57">{{ list[2]['finished_percent'] || 0 }}天</template>
                    </view>
                </view>
            </view>
        </view>


        <view v-if="list.length && !isSearch" class="color-sub font14 text-center pt5">
            <template>
                <!-- 免费(未去广告)活动不显示用户/队伍当前排名  - 2022-12-06 17:32:05 -->
                <template v-if="closed_AD">
                    <text>我的{{ isTeamRank ? '队伍' : '' }}排名：</text>
                    <text>{{ my_position_num === -1 ? '未上榜' : my_position_num }}</text>
                    <text class="pl5" v-if="total_count">
                        (共{{ total_count }}{{ isTeamRank ? '队伍' : '人' }})
                    </text>
                </template>
                <template v-else>
                    <text class="pl5" v-if="total_count">
                        共{{ total_count }}{{ isTeamRank ? '队伍' : '人' }}
                    </text>
                </template>
            </template>
            <text v-if="type_id === 30">完成接力</text>
        </view>

        <view class="list">
            <view v-for="(item, index) in list" :key="index">
                <xwy-ad v-if="index === 0 && !closed_AD" :activity_id="id" :ad_type="66"></xwy-ad>

                <view v-if="index < 10 || show_all || !show_rewarded_video_ad || type_id === 19"
                      class="item flex-row bg-white">
                    <view class="index flex-all-center" @click.stop="copyUserid(item)">
                        <template v-if="!isSearch">

                            <image class="top-3" v-if="index < 3"
                                   :src="'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/no-' + (index + 1) + '.png'"/>
                            <view v-else class="color-sub font14">{{ index + 1 }}</view>
                        </template>
                    </view>

                    <view v-if="item.headimg" class="flex-all-center">
                        <image @click="clickItem(item)" class="headimg" :src="item.headimg"/>
                    </view>


                    <view class="middle">
                        <view class="name color-title ellipsis">
                            {{ item.user_name }}
                            <text v-if="type_id === 0 && item.finished_time && id === '5f9a44d3027e4c7ff9c69d75febed541'"
                                  class="iconfont icon-flag color-red" style="padding-left: 30px;"></text>
                        </view>
                        <view v-if="type_id === 2 && item.conf && item.conf.team_slogan"
                              class="color-content font14 flex-row">
                            <view style="width: 70px; min-width: 70px;">队伍口号:</view>
                            <view>{{ item.conf.team_slogan }}</view>
                        </view>
                        <view class="other-msg color-sub font12 flex-row">
                            <view
                                class="pr10"
                                v-if="type_id === 0 || type_id === 2 || type_id === 102"
                                @click.stop="toAnswerRecords(item.userid, item.nickname, item.headimg)"
                            >累计{{ item['sign_day'] || 0 }}天
                            </view>
                            <view
                                v-if="(type_id === 2 || type_id === 40) && (!just_look_my_team_top || user_team_id === item.id || user_team_id === item.team_id)"
                                class="pl10 pr10"
                                @click="clickItem(item)"
                            >
                                <!--雷子客户的活动，“队内排行”前面要加“查看”2字-->
                                <template v-if="id === '79e0836e3dad59acaa76130fd08f57ab'">查看</template>
                                队内排行
                            </view>
                            <view v-if="type_id === 1" class="color-sub">
                                {{ item['update_time'] || item.create_time }}
                            </view>

                            <!--51:累计兑换天数排行榜需要显示用户里程-->
                            <view v-if="type_id === 51" class="color-sub">
                                {{ item.exchange_kilo || 0 }}{{ unit }}
                            </view>
                        </view>

                        <!--32: 到达终点时间先后排行榜-->
                        <view
                            v-if="(type_id === 0 || type_id === 30 || type_id === 39 || type_id === 52 || type_id === 54) && item.finished_time"
                            class="font12 color-sub">
                            <template v-if="type_id === 30">到达终点时间：</template>
                            <uni-dateformat :date="item.finished_time * 1000" format="yyyy-MM-dd hh:mm:ss"
                                            :threshold="[0, 0]"/>
                        </view>
                        <view v-if="type_id === 32" class="font12 color-sub">
                            {{ item.exchange_kilo || 0 }}{{ unit }}
                        </view>
                        <view v-if="type_id === 53" class="font12 color-sub">
                            {{ item.average_kilo || 0 }}{{ unit }}
                        </view>
                    </view>

                    <view v-if="type_id !== 30" class="right flex-column flex-all-center color-red font14">
                        <view class="right-item text-right" :class="{'filter': item.filter}"
                              @click="showItemDetail(index)">
                            <template v-if="type_id === 0 || type_id === 2 || type_id === 102">
                                {{ item.average_kilo || item.all_kilo || item.exchange_kilo || 0 }}{{ unit }}
                            </template>
                            <template v-if="type_id === 1 || type_id === 22 || type_id === 23 || type_id === 24 || type_id === 25 || type_id === 56">
                                {{ item.step || item['allStep'] || 0 }}步
                            </template>
                            <template v-if="type_id === 7">{{ item.score || 0 }}分</template>
                            <template v-if="type_id === 8">
                                <view>{{ item.likes || 0 }}赞</view>
                                <view>
                                    <text class="iconfont icon-love color-red"
                                          @click.stop="like(index)"></text>
                                </view>
                            </template>
                            <template v-if="type_id === 18">{{ item['share_friends'] || 0 }}</template>

                            <!-- 最后一致决定，早起排行榜只显示兑换时间，不显示步数，因为步数是今日兑换的总步数而不是第一次兑换时的步数，今日排行榜那里可以查看到今日步数，早起排行榜再显示怕会对用户造成干扰 -->
                            <view v-if="type_id === 19">
                                <text class="iconfont icon-alarm-clock color-sub font14"
                                      style="padding-right: 3px;"></text>
                                <uni-dateformat :date="item.create_time" format="hh:mm:ss"></uni-dateformat>
                            </view>
                            <template v-if="type_id === 20">
                                {{ item['integral_all'] || 0 }}{{ integral_unit }}
                            </template>
                            <template v-if="type_id === 26 || type_id === 27">
                                {{ item['average_num'] || 0 }}步
                            </template>
                            <template v-if="type_id === 32 || type_id === 53">
                                <uni-dateformat :date="item.finished_time * 1000"
                                                format="yyyy-MM-dd hh:mm:ss" :threshold="[0, 0]"/>
                            </template>
                            <template v-if="type_id === 33 || type_id === 34">
                                {{ item['percent_num'] || 0 }}
                            </template>
                            <template v-if="type_id === 35">{{ item['finished_percent'] || 0 }}%</template>
                            <template v-if="type_id === 37">{{ item['real_step'] || 0 }}步</template>
                            <template v-if="type_id === 39">{{ item['multi_score'] }}分</template>
                            <template v-if="type_id === 40">
                                {{ item['allIntegral'] }}{{ integral_unit }}
                            </template>
                            <template v-if="type_id === 51">{{ item['sign_day'] }}天</template>
                            <template v-if="type_id === 49">
                                {{ item['total_integral'] }}{{ integral_unit }}
                            </template>
                            <template v-if="type_id === 52 || type_id === 54">
                                {{ item.finished_count }}次
                            </template>
                            <template v-if="type_id === 57">{{ item['finished_percent'] || 0 }}天</template>
                        </view>
                        <view v-if="type_id === 0 && !closed_likes" class="right-item text-right pt5">
                            <text
                                class="iconfont icon-love"
                                :class="{'color-red': item.likes, 'color-sub': !item.likes}"
                                style="position: relative; top: 1px"
                                @click.stop="like(index)"
                            ></text>
                            <text v-if="item.likes" style="padding-left: 2px;" @click.stop="like(index)">
                                {{ item.likes }}
                            </text>
                        </view>
                    </view>
                </view>

                <xwy-ad
                    v-if="!closed_AD && (index + 1) % 10 === 0 && (show_all || !show_rewarded_video_ad)"
                    :activity_id="id"
                    :ad_type="66"
                ></xwy-ad>

            </view>

            <view v-if="!show_all && !init_load && show_rewarded_video_ad && list.length > 10 && type_id !== 19" class="flex-all-center">
                <view class="color-primary" @click="showAll">查看更多</view>
            </view>

        </view>

        <view v-if="!list.length && !more_loading && !init_load" class="text-center"
              style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无排行数据</view>
        </view>

        <uni-load-more v-if="more_loading" status="loading"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && list.length > 5 && (!closed_AD && show_all)"
            status="noMore"
            :contentText="{contentnomore: '我是有底线的'}"
        ></uni-load-more>
        <uni-load-more v-if="!is_last_page && !more_loading && !init_load" status="more"></uni-load-more>


        <uni-popup ref="like_popup" type="center">
            <view class="like_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('like_popup')">
                    <uni-icons type="close" size="24" color="#b2b3b7"/>
                </view>
                <view class="bg-primary color-white" style="padding: 30px 20px 20px;">

                    <icon size="80" color="#ffffff"
                          :type="like_status === 'success' ? 'success' : 'clear'"></icon>
                    <view class="font18">点赞{{ like_status === 'success' ? '成功' : '失败' }}</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ like_info }}
                    </view>
                </view>
                <xwy-ad v-if="!closed_AD && popup_show" :activity_id="id" :ad_type="3"></xwy-ad>
            </view>
            <view class="pt5">
                <xwy-ad v-if="!closed_AD && popup_show" :activity_id="id" :ad_type="66"></xwy-ad>
            </view>
        </uni-popup>

        <template v-if="tabbar_data">
            <activity-tabbar :value.sync="tabbar_data"></activity-tabbar>
        </template>


    </view>
</template>

<script>
const app = getApp()
import login from '../../../utils/api/login.js'
import xwy_api from '../../../utils/api/xwy_api.js'
import utils from '../../../utils/utils.js'

import activityTabbar from "@/pages/activity/components/activity-tabbar.vue"

const getTheMonthFirstDay = () => {
    const {year, month} = utils.getYearMonthDay()
    return `${year}-${month.toString().padStart(2, '0')}-01`
}

export default {
    components: {activityTabbar},
    data() {
        return {
            id: '',
            is_look_team: false,
            type_list: [],
            type_bar_item_style: '',
            type_id: -1,
            type_id_index: -1,
            total_count: 0,
            top_rank_text: null,
            init_load: true,
            unit: '里',
            my_position_num: -1,
            list: [],
            load_page: 1,
            is_last_page: false,
            more_loading: false,
            top_rank_banner: [],
            closed_AD: false,
            popup_show: false,
            like_status: '',
            like_info: '',
            get_day: utils.getDay(0, true),
            today: utils.getDay(0, true),
            every_day_toprank: false,
            just_look_my_team_top: false,
            user_team_id: '',
            show_all: false,
            show_rewarded_video_ad: false,
            closed_likes: false,
            integral_unit: '金币',

            tabbar_data: null,
            search_keyword: '',
            top_rank_search: false,
            date56: getTheMonthFirstDay(),

            // 是不是通过搜索框搜索，如果是搜索的话，不显示名次
            isSearch: false
        }
    },

    computed: {
        isTeamRank() {
            const teamTypes = [2, 25, 27, 30, 34, 40, 53, 54]
            return teamTypes.includes(this.type_id)
        },


        date56YearMonth() {
            const [year = '', month = ''] = this.date56.split('-')
            return {year: Number(year), month: Number(month)}
        }
    },

    onLoad(e) {
        e.show_tab && this.$uni.hideHomeButton()

        this.id = e.id
        if (e.user_team_id) this.user_team_id = Number(e.user_team_id)
        if (e.team_id) {
            this.team_id = e.team_id
            this.is_look_team = true
            this.type_id = Number(e.types || 0)
            if (e.parent_type_id) this.parent_type_id = Number(e.parent_type_id)
            if (e.team_name) this.$uni.setNavigationBarTitle(e.team_name + ' 排行榜')
        }
        if (e.activity_total) {
            this.activity_total = true
            this.$uni.setNavigationBarTitle('活跃度排行榜')
        }
        this.$uni.showLoading()
        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getActivityData()

            e.show_tab && this.setTabBar()
        })
    },
    onPullDownRefresh() {
        if (this.init_load || this.more_loading) return uni.stopPullDownRefresh()

        this.search()
        uni.stopPullDownRefresh()
    },
    onReachBottom() {
        if (!this.init_load && !this.more_loading && !this.is_last_page) this.getList()
    },
    methods: {
        async getActivityData() {

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details',
                data: {
                    active_id: this.id
                }
            })

            const activity_detail = res?.['data']?.['active_details'] || {}


            this.activity_detail = activity_detail

            if (activity_detail.conf.active.kilo_unit) this.unit = activity_detail.conf.active.kilo_unit
            if (activity_detail.rank_set?.closed_AD) this.closed_AD = true
            if (activity_detail.rank_set?.every_day_toprank) this.every_day_toprank = true
            if (activity_detail.rank_set?.top_rank_search) this.top_rank_search = true
            if (activity_detail.conf.active.just_look_my_team_top) this.just_look_my_team_top = true
            if (activity_detail.conf.active.closed_likes) this.closed_likes = true
            if (activity_detail.conf.active.integral?.unit) this.integral_unit = activity_detail.conf.active.integral.unit

            if (activity_detail.conf.active.like_limit) await this.getUserDetails()

            this.top_rank_conf_set = res?.data?.active_more_data?.active_conf_set?.['toprank_list']

            this.getTypeList()
            this.createRewardedVideoAd()
            this.setBanner()
        },

        async getUserDetails() {
            if (!this.activity_detail?.conf?.active?.like_limit) return
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: {
                    active_id: this.id
                }
            })
            this.user_details = res?.data.user_details || null
        },


        setTabBar() {
            const tab_bar_data = app.globalData['tempData'].activity_tab_bar_data
            if (tab_bar_data) this.tabbar_data = tab_bar_data
        },

        createRewardedVideoAd() {
            let isH5 = false
            // #ifdef H5
            isH5 = true
            // #endif

            if (this.closed_AD || isH5 || this.activity_detail.rank_set?.limit_show_num) return

            this.show_rewarded_video_ad = true
            let adid = null
            if (app.globalData['shop_info']?.['shop_set']?.ad_list?.length) {
                const ad_list = app.globalData['shop_info']['shop_set'].ad_list
                const RewardedVideoAd = ad_list.find(v => v['ad_types'] === 2)
                if (RewardedVideoAd && RewardedVideoAd['ad_id']) adid = RewardedVideoAd['ad_id']
            }

            if (adid === null) return false

            this.RewardedVideoAd = uni.createRewardedVideoAd({
                adUnitId: adid
            })

            this.RewardedVideoAd.onLoad(() => {
                uni.hideLoading()
                console.log('onload')
            })
            this.RewardedVideoAd.onError(err => {
                uni.hideLoading()
                console.log('onError', err)
                this.RewardedVideoAdError = true
            })
            this.RewardedVideoAd.onClose(res => {
                console.log('onClose', res)
                if (!res['isEnded']) return false
                if (this.RewardedVideoAdType === 'show_all') this.show_all = true
                if (this.RewardedVideoAdType === 'show_item_detail') this.list[this.show_item_detail_index].filter = false
            })
        },


        showAll() {
            if (!this.RewardedVideoAd || this.RewardedVideoAdError) {
                this.show_all = true
                return false
            }
            this.RewardedVideoAdType = 'show_all'
            this.RewardedVideoAd.show()
        },

        showItemDetail(index) {
            if (!this.list[index].filter) return false
            if (!this.RewardedVideoAd || this.RewardedVideoAdError) {
                this.list[index].filter = false
                return false
            }
            this.RewardedVideoAdType = 'show_item_detail'
            this.show_item_detail_index = index
            this.RewardedVideoAd.show()
        },


        setBanner() {
            if (this.activity_detail.conf?.active?.top_rank_banner?.length) {
                this.top_rank_banner = this.activity_detail.conf.active.top_rank_banner
            }
        },

        changeType(index) {
            if (index === this.type_id_index) return
            this.type_id_index = index
            const item = this.type_list[index]
            this.type_id = item.id
            this.top_rank_text = null
            if (item.text) {
                this.top_rank_text = {
                    rules: item.text,
                    top_rank_name: item.name
                }
            }
            this.search_keyword = ''
            this.category_id = item.category_id || ''
            this.show_all = false
            this.search()
        },

        getTypeList() {
            if (this.team_id) return this.getList()
            xwy_api.ajax({
                url: 'front.flat.sport_step.step/top_rank_category_list',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id
                },
                success: res => {
                    let type_list = [
                        {name: '个人排行榜', id: 0},
                        {name: '今日排行榜', id: 1},
                        {name: '早起排行榜', id: 19},
                        {name: '队伍排行榜', id: 2}
                    ]
                    if (res?.data?.['top_rank_category_list']) type_list = res.data['top_rank_category_list']
                    // type_list.push({name: '个人日均', id: 26})
                    // type_list.push({name: '队伍日均', id: 27})
                    // type_list.push({name: '达标率排行榜', id: 35})

                    // 活动设置了关闭点赞功能，就不显示点赞排行榜
                    const like_type_index = type_list.findIndex(v => v.id === 8)
                    if (this.closed_likes && like_type_index !== -1) {
                        type_list.splice(like_type_index, 1)
                    }

                    if (this.activity_total) {
                        const new_type_list = []
                        type_list.forEach(v => {
                            if (v.activity_total) new_type_list.push(v.activity_total)
                        })
                        type_list = new_type_list
                    }

                    this.type_list = type_list
                    this.changeType(0)
                }
            })
        },


        getDayChange(e) {
            console.log(e)
            const value = e.detail.value
            if (value === this.get_day) return
            this.get_day = e.detail.value
            this.search()
        },

        date56Change(e) {
            this.date56 = e.detail.value
            this.search()
        },


        search() {
            this.load_page = 1
            this.getList()
        },

        async getList() {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
                this.my_position_num = -1
                this.total_count = 0
                if (!this.init_load) this.init_load = true
                this.$uni.showLoading()
            } else {
                this.more_loading = true
            }


            const data = {
                active_id: this.id,
                top_rank_types: this.type_id,
                page: this.load_page,
                perpage: 20
            }
            if (this.search_keyword) data.nickname = this.search_keyword

            // 是不是搜索
            this.isSearch = !!data.nickname

            if (this.category_id) data.category_id = this.category_id // 队伍排行榜分类

            // 针对部分付费用户开启，今日排行榜可查看任意一天的步数排名数据。开启了此功能的，就可以在这里传任意一天的日期，查询这一天的步数排行榜。日期时间格式 2022-05-22
            if (this.type_id === 1) {
                data.exchange_date = this.get_day
            }

            if (this.is_look_team) data.team_id = this.team_id

            if (this.type_id === 56) {
                const {year, month} = this.date56YearMonth
                data.year = year
                data.month = month
            }

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.step/top_rank_list',
                data,
            })

            console.log('排行榜数据 id' + this.type_id + '：', res)
            if (this.load_page === 1) {
                this.init_load = false
                uni.hideLoading()
            } else {
                this.more_loading = false
            }


            if (!res || !res.data) {
                this.is_last_page = true
                return
            }


            if (res.data?.rank_types_text) {
                if (!this.top_rank_text) this.top_rank_text = res.data.rank_types_text
            }

            this.getTeamRankingTopRankText()

            if (res.data['top_rank_list']) {
                if (res.data['top_rank_list']['position_num']) this.my_position_num = res.data['top_rank_list']['position_num']

                const data = res.data['top_rank_list']
                let list = data.list.data || []
                // 有些类型的排行榜数据列表是在list里面，有些是在data里面
                if (!list.length && Array.isArray(data.list)) list = data.list || []

                if (this.type_id === 52 || this.type_id === 54) {
                    // 到达终点次数排行榜，需要通过用户的总里程来计算到达终点的次数
                    await this.getEndMileage()
                }

                list.forEach(v => {
                    v.user_name = this.getUserName(v)
                    v.headimg = v.headimg || v.user_details?.headimg || ''
                    if (this.isTeamRank) {
                        v.headimg = v.conf?.logo || v['team_details']?.conf?.logo || ''
                    }

                    // 只保留两位小数
                    if (this.id === '5648e28ca6e94f739633d144b7335f9b') {
                        if (v.exchange_kilo) v.exchange_kilo = Number(v.exchange_kilo.toFixed(2))
                        if (v.average_kilo) v.average_kilo = Number(v.average_kilo.toFixed(2))
                        if (v.all_kilo) v.all_kilo = Number(v.all_kilo.toFixed(2))
                    }

                    // if (i > 2 && this.show_rewarded_video_ad && this.type_id !== 19) v.filter = true

                    // 达标率只保留两位小数
                    if (v['finished_percent']) v['finished_percent'] = Math.floor(v['finished_percent'] * 100) / 100

                    // 广晟，活跃度排行榜需要固定显示为两位小数，0 => 0.00% 1.3 => 1.30%
                    if (this.type_id === 33 || this.type_id === 34) {
                        const percent_num = v.percent_num || 0
                        v.percent_num = percent_num.toFixed(2) + '%'
                    }

                    if (this.type_id === 39) {
                        // 因为这个排行榜类型接口不会分页返回，会一页返回所有排行榜数据，所以要尽量保证设置到data的数据足够精简
                        const keepKeys = ['finished_time', 'headimg', 'id', 'multi_score', 'user_name', 'userid']
                        Object.keys(v).forEach(key => {
                            if (!keepKeys.includes(key)) delete v[key]
                        })
                    }

                    if (this.type_id === 52) {
                        // 到达终点次数排行榜，需要通过用户的总里程来计算到达终点的次数
                        v.finished_count = Math.floor(v.exchange_kilo / this.endMileage)
                    }

                    if (this.type_id === 54) {
                        // 到达终点次数排行榜，需要通过用户的总里程来计算到达终点的次数
                        v.finished_count = Math.floor(v.average_kilo / this.endMileage)
                    }

                })


                const this_list = [...this.list, ...list]

                // 避免标签超出dom数量或者data超出上限，限制最多只能1000条
                if (this_list.length > 1000) this_list.length = 1000

                this.list = this_list

                let is_last_page = data.list.is_lastpage
                // 队伍活跃度排行榜接口没有做分页，因为只有广晟在用，并且创建的队伍比较少
                if (this.type_id === 34) is_last_page = true
                this.is_last_page = is_last_page

                this.total_count = data.list.total
                this.load_page++
            } else {
                this.is_last_page = true
            }
        },

        getTeamRankingTopRankText() {
            if (!this.is_look_team || !this.top_rank_conf_set?.length || !this.parent_type_id) return

            const item = this.top_rank_conf_set.find(item => item.id === this.parent_type_id)
            if (item['team_text']) this.top_rank_text = {
                rules: item['team_text']
            }
        },


        async getEndMileage() {
            if (this.endMileage) return
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_map_point_list',
                data: {active_id: this.id}
            })
            const pointList = res?.data?.map_point_list || []
            if (!pointList) {
                this.endMileage = 25000
                return
            }
            this.endMileage = pointList[pointList.length - 1].min_num || 25000
        },

        getUserName(item) {
            const type_id = this.type_id

            const nameTypes = [2, 30, 53, 54]
            if (nameTypes.includes(type_id)) return item.name || ''

            const teamTypes = [25, 27, 34, 40]
            if (teamTypes.includes(type_id)) return item['team_details']?.name || ''

            if (type_id === 33) return item.user_details?.must_submit?.[0]?.value || ''

            return item.must_submit?.[0]?.value || ''
        },


        clickItem(item) {
            const {id, type_id, activity_detail, just_look_my_team_top, user_team_id} = this
            const notJumpTypes = [25, 30, 34]   // 队伍排行榜不查看队员兑换详情
            if (notJumpTypes.includes(type_id)) return

            // 活动开启了不允许查看用户兑换记录
            if (type_id !== 2 && activity_detail.rank_set?.['closed_user_exchange_list']) return

            let nickname = ''
            if (item.must_submit?.[0]?.value) nickname = encodeURIComponent(item.must_submit[0].value)
            let headimg = ''
            if (item.headimg) headimg = encodeURIComponent(item.headimg)
            let url = `/pages/activity/other/user_exchange_list?activity_id=${id}&userid=${item.userid}&nickname=${nickname}&headimg=${headimg}&types=${type_id}`
            if (type_id === 2 || type_id === 40) {
                const team_id = item.id || item.team_id

                // 设置了只能查看自己队伍的队内排名
                if (just_look_my_team_top && user_team_id !== team_id) return

                let types = 0
                if (type_id === 40) types = 20
                url = `/pages/activity/user/ranking_list?id=${id}&team_id=${team_id}&team_name=${item.name || item.team_details?.name || ''}&types=${types}&parent_type_id=${type_id}`
            }
            uni.navigateTo({url})
        },

        async like(index) {
            if (this.type_id !== 0 && this.type_id !== 8) return false

            if (this.activity_detail?.conf?.active?.like_limit) {
                if (!this.user_details?.id) return this.$uni.showToast('参与活动后才能点赞')
                if (!this.user_details?.checked) return this.$uni.showToast('需要管理员审核通过后才能点赞')
            }

            const item = this.list[index]

            this.$uni.showLoading('点赞中...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.likeit/like_for_friend',
                data: {
                    active_id: this.id,
                    userid: item.userid
                }
            })
            uni.hideLoading()

            let like_status = 'success',
                like_info = '给好友点赞成功！'

            if (res && res['status']) {
                item.likes = item.likes || 0
                item.likes++
            } else {
                like_status = 'error'
                like_info = '给好友点赞失败！'
            }
            if (res?.['info']) like_info = res['info']
            this.like_status = like_status
            this.like_info = like_info

            this.uniPopupOpen('like_popup')
        },

        uniPopupOpen(ref) {
            this.$refs[ref].open()
            this.popup_show = true
        },

        uniPopupClose(ref) {
            this.$refs[ref].close()
            this.popup_show = false
        },

        copyUserid(item) {
            const id = this.type_id === 2 ? item.id : item.userid
            uni.setClipboardData({
                data: id.toString(),
                success: () => uni.hideToast()
            })
        },

        copyId() {
            uni.setClipboardData({
                data: this.id,
                success: () => uni.hideToast()
            })
        },

        toAnswerRecords(userid, nickname, headimg) {
            if (!this.exam_open) return false
            uni.navigateTo({
                url: `../other/answer/record_list?id=${this.id}&userid=${userid}&nickname=${nickname}&headimg=${headimg}`
            })
        }
    }
}
</script>

<style scoped lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
}

.type-bar {
    position: fixed;
    z-index: 9999;
    top: 0;
    border: 0;
    width: 100vw;
    height: 40px;
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .type-bar {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }
}

/* #endif */
.type-item {
    height: 40px;
    line-height: 40px;
    box-sizing: border-box;
}

.activate-type {
    color: #2d8cf0;
    border-bottom: 2px solid #2d8cf0;
}


.podium {
    padding: 10px;
    align-items: flex-end;
}

.top--1, .top--2, .top--3 {
    width: calc((100vw - 20px) / 3);
    text-align: center;
    padding: 10px;
    box-sizing: border-box;
    height: 130px;
}

.top--1 .headimg-view, .top--2 .headimg-view, .top--3 .headimg-view {
    position: relative;
    padding-top: 10px;
    margin: 0 auto;
}

.top--1 .top-icon, .top--2 .top-icon, .top--3 .top-icon {
    position: absolute;
}

.team-headimg {
    padding-bottom: 10px;
}

.top--1 {
    border-radius: 10px 10px 0 0;
    border-left: 1px solid #f8f8f8;
    border-right: 1px solid #f8f8f8;
    height: 160px;
    /* background: linear-gradient(#ecfec9, #fce888); */
}

.top--1 .headimg-view {
    width: 66px;
    height: 66px;
}

.top--1 .headimg {
    width: 62px;
    height: 62px;
    border: 2px solid #fbd561;
}

.top--1 .top-icon {
    right: -12px;
    top: -8px;
    transform: rotate(37deg);
}

.top--1 .top-icon .iconfont {
    color: #fbd561;
    font-size: 28px;
}

.top--1 .team-headimg .iconfont {
    color: #fbd561;
    font-size: 36px;
}

.top--2 {
    height: 135px;
    border-radius: 10px 0 0 0;
    border-right: 1px solid #f8f8f8;
    /* background: linear-gradient(#e9fcff, #c7e0f5); */
}

.top--2 .headimg-view {
    width: 64px;
    height: 64px;
}

.top--2 .headimg {
    border: 2px solid #b3c8d9;
}

.top--2 .top-icon {
    right: -12px;
    top: -6px;
    transform: rotate(40deg);
}

.top--2 .top-icon .iconfont {
    color: #b3c8d9;
    font-size: 26px;
}

.top--2 .team-headimg .iconfont {
    color: #b3c8d9;
    font-size: 34px;
}

.top--3 {
    border-left: 1px solid #f8f8f8;
    border-radius: 0 10px 0 0;
    /* background: linear-gradient(#f0e9d7, #f0eabb); */
}

.top--3 .headimg-view {
    width: 62px;
    height: 62px;
}

.top--3 .headimg {
    width: 58px;
    height: 58px;
    min-width: 58px;
    border: 2px solid #eec68d;
}

.top--3 .top-icon {
    right: -10px;
    top: -3px;
    transform: rotate(45deg);
}

.top--3 .top-icon .iconfont {
    color: #eec68d;
    font-size: 24px;
}

.top--3 .team-headimg .iconfont {
    color: #eec68d;
    font-size: 32px;
}

.index {
    width: 40px;
    min-width: 40px;
}

.top-3 {
    width: 30px;
    height: 30px;
    display: block;
}

.item {
    margin: 10px;
    padding: 10px 10px 10px 0;
    border-radius: 10px;
}

.headimg {
    width: 60px;
    min-width: 60px;
    height: 60px;
    border-radius: 50%;
}

.middle {
    width: 100%;
    padding-left: 10px;
    overflow: hidden;
    box-sizing: border-box;
}

.name {
    line-height: 35px;
}

.other-msg {
    line-height: 25px;
}

.right {
    width: 100px;
    min-width: 100px;
}

.right-item {
    width: 100px;
}

.filter {
    filter: blur(3px);
}

.like_popup {
    width: 300px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}

.date-picker {
    background-color: #fff;
    padding: 10px;
    border-radius: 10px;

    .date-picker-year-month {
        width: 100px;
    }
}
</style>
