<template>
    <view class="page bg-background" :class="{'pt-60': page_type !== 'join' && page_type !== 'look_record'}">
        <view v-if="page_type !== 'join' && page_type !== 'look_record'" class="search bg-white flex-kai p10">
            <view class="input-view">
                <view class="search-icon left-icon flex-all-center">
                    <uni-icons type="search" size="20" color="#bbbec4"/>
                </view>
                <input
                    class="input bg-background"
                    type="text"
                    confirm-type="search"
                    v-model="search_keyword"
                    @confirm="search"
                    placeholder="输入你要搜索的活动"
                    placeholder-style="color:#bbbec4"
                    :auto-focus="search_input_focus"
                />
                <view class="search-icon right-icon flex-all-center" @click="search_keyword = ''">
                    <uni-icons v-if="search_keyword" type="close" size="20" color="#bbbec4"/>
                </view>
            </view>
            <view class="search-go color-info pl10" @click="search">搜索</view>
        </view>


        <view class="flex-kai bg-white">
            <view class="color-sub p10">共{{ total_count }}个活动</view>

            <view v-if="have_del" class="flex-all-center">
                <navigator class="p10" url="/pages/activity/user/activity_list?type=delete">
                    <text class="color-sub font14">已删除/作废活动</text>
                    <!-- <uni-icons type="forward" size="14" color="#80848f"/> -->
                </navigator>
            </view>

            <view v-if="justLookVip" class="color-content" style="height: 42px; line-height: 42px;"
                  @click="lookVipSwitch">
                <radio :checked="just_look_vip"/>
                只看付费
            </view>

            <view v-if="(page_type === 'all' || page_type === 'create') && !closed_create_active"
                  class="color-primary p10" hover-class="navigator-hover" @click="addActivity">
                <text>创建活动</text>
                <uni-icons type="forward" color="#2d8cf0"/>
            </view>
        </view>


        <scroll-view v-if="activeTypeList.length > 1" class="category-bar" scroll-x="true"
                     :scroll-left="activeTypesScrollLeft" scroll-with-animation="true">
            <view class="flex-row">
                <view class="category-item font14"
                      :class="activeTypes === 0 ? 'color-light-primary' : 'color-content'"
                      @click="activeTypesChange(0)">全部
                    <view v-show="activeTypes === 0" class="category-active"></view>
                </view>
                <view class="category-item font14"
                      :class="item.types === activeTypes ? 'color-light-primary' : 'color-content'"
                      v-for="item in activeTypeList" :key="item.types"
                      @click="activeTypesChange(item.types)">
                    {{ item.title }}

                    <view v-show="item.types === activeTypes" class="category-active"></view>
                </view>
            </view>
        </scroll-view>


        <view class="list">
            <view v-for="(item, index) in list" :key="item.active_id">
                <view class="list-item bg-white">
                    <view class="flex-row" hover-class="navigator-hover" @click="toDetail(item)">
                        <image class="logo" :src="item.logo || active_default_logo" mode="aspectFill"/>
                        <view class="right">
                            <view class="color-title font18 ellipsis--l2">{{ item.name || '' }}</view>
                            <view v-if="item.organizer" class="color-content pt5 ellipsis">
                                主办方：{{ item.organizer }}
                            </view>
                            <view class="pt5">
                                <activity-type-tag :type="item.types"/>
                                <text v-if="item['rank_set_shield_other']"
                                      class="iconfont icon-VIP pl5 color-warning"></text>
                                <text v-if="item['rank_set_closed_AD']"
                                      class="iconfont icon-ad-free pl5 color-success"></text>
                                <text v-if="item.need_password"
                                      class="iconfont icon-lock pl5 color-error"></text>
                            </view>
                            <view class="color-red">
                                <template v-if="item.status === -1">活动已删除</template>
                                <template v-if="item.status === -2">活动已关闭</template>
                            </view>
                            <view v-if="item.look_time" class="font12 color-sub pt5">
                                <text>浏览时间：</text>
                                <uni-dateformat :date="item.look_time" format="yyyy-MM-dd hh:mm:ss"
                                                :threshold="[60000, 86400000]"/>
                            </view>
                        </view>
                    </view>
                    <view v-if="page_type === 'create' && item.status === 1"
                          class="tools-bar clearfix clear">
                        <view v-if="is_select" class="fr p10 color-light-primary"
                              @click="selectActive(item)">选择活动
                        </view>

                        <view v-else class="fr flex-row">
                            <view class="edit" hover-class="navigator-hover"
                                  @click.stop="toEdit(item.active_id, item.types)">
                                <text class="iconfont icon-edit color-sub font20"></text>
                            </view>

                            <navigator class="edit" hover-class="navigator-hover"
                                       @click.stop="toManage(item.active_id, item.types)">
                                <text class="iconfont icon-setting color-sub font20"></text>
                            </navigator>
                            <view v-if="item.status !== -1" class="delete"
                                  @click.stop="deleteItem(item.active_id)">
                                <text class="iconfont icon-delete color-sub font20"></text>
                            </view>
                        </view>
                    </view>
                </view>
                <xwy-ad v-if="index !== 0 && ((index + 1) % 10 === 0)" :ad_type="66"></xwy-ad>
            </view>
        </view>


        <view v-if="!loading && !list.length" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无活动</view>
            <view class="flex-all-center p20">
                <navigator v-if="page_type === 'join'" class="not-btn text-center bg-primary color-white"
                           url="/pages/activity/user/activity_list?type=all">参加活动
                </navigator>
                <view
                    v-if="(page_type === 'all' || page_type === 'create') && !closed_create_active"
                    class="not-btn text-center bg-primary color-white"
                    hover-class="navigator-hover"
                    @click="addActivity"
                >创建活动
                </view>
            </view>
        </view>

        <uni-load-more v-if="loading && load_page > 1" status="loading"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && list.length > 5"
            status="noMore"
            :contentText="{contentnomore: '我是有底线的'}"
        ></uni-load-more>
        <uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>

        <view
            v-if="(page_type === 'all' || page_type === 'create') && !closed_create_active"
            class="add-activity flex-all-center bg-primary"
            hover-class="navigator-hover"
            @click="addActivity"
        >
            <uni-icons type="plusempty" size="30" color="#ffffff"/>
        </view>

        <!-- #ifndef H5 -->
        <button v-if="page_type === 'create'" class="contact bg-green" open-type="contact"
                @click="copy('积分', false)">
            <text class="iconfont icon-chat-bubble color-white font34"></text>
        </button>
        <!-- #endif -->


    </view>
</template>

<script>
const app = getApp()
import my_storage from '../../../utils/storage.js'
import activityTypeTag from '../components/activity-type-tag.vue'

const {who, evn_version} = app.globalData

export default {
    components: {
        activityTypeTag
    },
    data() {
        return {
            loading: true,
            page_type: 'all',
            list: [],
            load_page: 1,
            is_last_page: false,
            search_keyword: '',
            total_count: 0,
            search_input_focus: false,
            closed_create_active: false,
            have_del: false,
            active_default_logo: this.xwy_config.active_default_logo,
            is_select: false,
            select_point: false,
            types: 0,
            who,
            evn_version,
            just_look_vip: false,

            activeTypeList: [],
            activeTypes: 0,
            activeTypesScrollLeft: 0
        }
    },

    computed: {
        justLookVip() {
            const {page_type, who, evn_version} = this
            return page_type === 'all' && who === 288 && evn_version !== 'release'
        }
    },


    onLoad(e) {
        this.$uni.showLoading('加载中...')
        if (e['to_search']) this.search_input_focus = true
        if (e.type) this.page_type = e.type
        if (e.select) this.is_select = true
        if (e.select_point) this.select_point = true
        if (e.types) this.types = Number(e.types)

        const titleMap = {
            'all': '活动广场',
            'join': '我参加的活动',
            'create': '我创建的活动',
            'look_record': '我的足迹'
        }
        const title = titleMap[this.page_type]
        if (title) uni.setNavigationBarTitle({title})

        this.$login.uniLogin(err => {
            this.closed_create_active = this.xwy_api.closed_create_active()
            if (err && err.errMsg) return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})

            this.loading = false
            uni.hideLoading()

            if (this.page_type === 'look_record') return this.getLookRecords()

            this.getList()
        })
    },


    onShow() {
        if (this.page_type === 'look_record') {
            this.getLookRecords()
        }
    },


    onReachBottom() {
        if (!this.loading && !this.is_last_page && this.page_type !== 'look_record') {
            this.getList()
        }
    },


    methods: {
        addActivity() {
            this.$uni.navigateTo('/pages/create-activity/index')
        },

        getLookRecords() {
            this.list = my_storage.activityLookRecords()
            if (this.list.length) this.total_count = this.list.length
            this.is_last_page = true
        },

        lookVipSwitch() {
            this.just_look_vip = !this.just_look_vip
            this.search()
        },

        search() {
            this.load_page = 1
            this.getList()
        },

        getList() {
            if (this.loading) return
            if (this.load_page === 1) {
                this.$uni.showLoading('加载中...')
                this.list = []
                this.is_last_page = false
                this.activeTypes = 0
            }
            this.loading = true

            const data = {
                access_token: app.globalData['access_token'],
                page: this.load_page,
                perpage: 10,
                keyword: this.search_keyword.replace(/(^\s*)|(\s*$)/g, "")
            }

            if (this.page_type === 'join') data.is_attend = 1
            if (this.page_type === 'create' || this.page_type === 'delete') {
                data.my_self = 1
                data.perpage = 1000
            }
            if (this.types) data.types = this.types

            if (this.just_look_vip) {
                data.perpage = 1000
                data.from_admin = 1
            }

            this.xwy_api.ajax({
                url: 'front.flat.sport_step.active_list/active_list',
                data,
                success: res => {
                    uni.hideLoading()
                    this.loading = false
                    if (!res.status) {
                        this.is_last_page = true
                        return this.$uni.showModal(res.info || '获取失败')
                    }
                    this.load_page++
                    if (res?.data?.active_list) {
                        let list = res.data.active_list.data || []

                        if (this.just_look_vip) {
                            list = list.filter(item => item.rank_set)
                        }

                        list.forEach(v => {
                            if (v.active_details) {
                                v.types = v.active_details.types
                                v.name = v.active_details.name || ''
                                v.organizer = v.active_details.organizer || ''
                                v.logo = v.active_details.logo || ''
                                v.status = v.active_details.status || 1
                                if (v.active_details.rank_set) v.rank_set = v.active_details.rank_set
                                if (v.active_details.conf) v.conf = v.active_details.conf

                                delete v.active_details
                            }

                            if (v.rank_set) {
                                Object.keys(v.rank_set).forEach(key => {
                                    const v_key = `rank_set_${key}`
                                    v[v_key] = v.rank_set[key]
                                })
                                delete v.rank_set
                            }


                            if (v.conf) {
                                if (v.conf === "string") {
                                    try {
                                        v.conf = JSON.parse(v.conf)
                                    } catch (e) {
                                        console.log('JSON.parse(v.conf)错误', v.conf, e)
                                        v.conf = {}
                                    }
                                }
                                if (v.conf.active?.enter_types === 2) v.need_password = true

                                delete v.conf
                            }
                        })

                        this.list = [...this.list, ...list]
                        this.is_last_page = res.data.active_list.is_lastpage
                        this.total_count = res.data.active_list.total || 0

                        if (this.page_type === 'create' || this.page_type === 'delete') {
                            const all_list = this.setList(list)
                            this.list = all_list
                            this.total_count = this.list.length

                            if (this.page_type === 'create') {
                                this.all_list = all_list
                                this.getActiveTypes()
                            }
                        }
                    }
                }
            })
        },

        async getActiveTypes() {
            const haveTypes = this.all_list.map(item => item.types)
            if (haveTypes.length <= 1) return

            const conf_set = await this.xwy_api.getActivityTypeConfSet()

            if (!conf_set) return false

            const {active_goods_list} = conf_set

            if (active_goods_list.length) {
                this.activeTypeList = 0
                this.activeTypesScrollLeft = 0

                this.activeTypeList = active_goods_list.filter(item => haveTypes.includes(item.types)).map(item => ({
                    title: item.title,
                    types: item.types
                }))
            }
        },

        activeTypesChange(types) {
            if (this.loading || types === this.activeTypes) return
            this.activeTypes = types

            // 设置scrollLeft让当前item居中
            const query = uni.createSelectorQuery().in(this)
            query.selectAll('.category-item').boundingClientRect()
            query.select('.category-bar').boundingClientRect()
            query.exec(res => {
                // 因为“全部”是在页面结构单独写的，不在this.activeTypeList，所以下标要加上“全部”的1
                const current = this.activeTypeList.findIndex(v => v.types === types) + 1

                const [items, {width: containerWidth}] = res
                const item = items[current]
                const itemOffsetLeft = items.slice(0, current).reduce((total, curr) => total + curr.width, 0)

                this.activeTypesScrollLeft = itemOffsetLeft + item.width / 2 - containerWidth / 2
            })

            this.list = types ? this.list = this.all_list.filter(item => item.types === types) : this.all_list
            this.total_count = this.list.length
        },

        setList(list) {
            let have_del = false
            const new_list = list.filter(item => {
                const status = item.active_details ? item.active_details.status : item.status
                if (status === -1 || status === -2) {
                    if (this.page_type === 'create') have_del = true
                    return this.page_type === 'delete'
                }
                return this.page_type === 'create'
            })

            if (this.page_type === 'create' && have_del) this.have_del = true

            return new_list
        },


        selectActive(item) {
            if (this.select_point) {
                this.$uni.navigateTo(`/pages/activity/admin/point_list?id=${item.active_id}&select=1`, {
                    events: {
                        selectPoint: point => {
                            this.getOpenerEventChannel().emit('selectPoint', point)
                            this.$uni.navigateBack()
                        }
                    }
                })
                return
            }

            this.getOpenerEventChannel().emit('selectActive', {
                active_id: item.active_id,
                name: item.name || '',
                active_types: item.types
            })
            this.$uni.navigateBack()
        },

        toDetail(data) {
            this._utils.toActivityDetail(data)
        },

        toEdit(id, types) {
            let url = this.xwy_config.getActivityPath(types, 'add')
            url += `?id=${id}`
            this.$uni.navigateTo(url)
        },

        toManage(id, types) {
            let url = this.xwy_config.getActivityPath(types, 'manage')
            url += `?id=${id}`
            this.$uni.navigateTo(url)
        },

        deleteItem(id) {
            if (id === '24e6c2bd9b8c300885fde3b8a79213db')
                return this.$uni.navigateTo('/pages/ai_sport/new_file')

            this.deleteConfirm([id])
        },

        async deleteConfirm(ids) {
            const res = await this.$uni.showModal(`确定删除${ids.length > 1 ? '选中的' : '该'}活动?`, {
                confirmText: '删除',
                cancelColor: '#80848f',
                confirmColor: '#ed3f14',
                showCancel: true
            })
            if (res.confirm) await this.deleteAjax(ids)
        },

        async deleteAjax(ids) {
            this.$uni.showLoading('删除中...')

            const data = {
                ids: ids.join(',')
            }
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/del_active',
                data
            })
            uni.hideLoading()
            if (!res || !res.status) {
                this.$uni.showModal(res && res.info ? res.info : '删除失败，请重试!')
                return
            }

            this.$uni.showToast('已删除')

            this.load_page = 1
            this.getList()
        },

        copy(data, showTips = false) {
            uni.setClipboardData({
                data,
                success: () => {
                    if (showTips) {
                        uni.showToast({
                            title: '复制成功'
                        })
                    } else {
                        uni.hideToast()
                    }
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 10px;
}

.pt-60 {
    padding-top: 60px;
}

.search {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
}

.search, .search .input {
    height: 40px;
    line-height: 40px;
}

.input-view {
    position: relative;
}

.search-icon {
    position: absolute;
    width: 30px;
    height: 40px;
    top: 0;
}

.left-icon {
    left: 0;
}

.right-icon {
    right: 0;
}

.search .input {
    width: calc(100vw - 90px);
    padding: 0 30px;
    border-radius: 20px;
    box-sizing: border-box;
}

.search-go {
    width: 60px;
    min-width: 60px;
    text-align: right;
}


/* #ifdef H5 */
.search-go {
    padding-right: 10px;
    box-sizing: border-box;
}

@media screen and (min-width: 500px) {
    .search {
        width: 500px;
        left: calc(((100vw - 500px) / 2) - 10px);
    }

    .search .input {
        width: 440px;
    }
}

/* #endif */

.not-btn {
    width: 200px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
}

.list {
    padding-top: 5px;
}

.list-item {
    margin: 10px;
    padding: 10px;
    border-bottom: 1px solid #eee;
    border-radius: 10px;
}

.list-item .logo {
    display: block;
    width: 130px;
    height: 80px;
    border-radius: 5px;
    min-width: 130px;
}

.list-item .right {
    width: calc(100% - 130px);
    padding-left: 10px;
    box-sizing: border-box;
}

.tools-bar {
    border-top: 1px solid #eee;
    padding-top: 10px;
    margin-top: 10px;
}

.edit, .delete {
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 50%;
}

.edit {
    border: 1px solid #eee;
    margin-right: 10px;
}

.delete {
    border: 1px solid #eee;
}

.add-activity, .contact {
    position: fixed;
    right: 10px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    opacity: .7;
}

.category-bar {
    width: 100%;
    padding: 0 10px;
    box-sizing: border-box;

    .category-item {
        display: inline-block;
        white-space: nowrap;
        height: 34px;
        line-height: 34px;
        padding: 0 10px;
        box-sizing: border-box;
        position: relative;

        .category-active {
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 16px;
            margin-left: -8px;
            height: 2px;
            background-color: #2d8cf0;
        }
    }
}


/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .add-activity {
        right: calc(100vw - 500px - ((100vw - 500px) / 2)) !important;
    }

    .category-bar {
        width: 500px;
    }
}

/* #endif */

.add-activity {
    bottom: 80px;
}

.contact {
    bottom: 150px;
    line-height: 50px;
    padding: 0;
    margin: 0;
    border: none;
}

.contact::after {
    border: none;
}
</style>
