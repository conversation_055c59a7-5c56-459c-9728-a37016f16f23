<template>
    <view class="page">
        <view class="today-step flex-all-center">
            <image class="bg-img" :src="top_image_src" mode="widthFix"/>
            <image v-if="show_today_step" class="zhuanquanquan"
                   src="http://www.xinweiyun.com/weixin/editor/attached/image/weixin_235/20201117/20201117173605_730.png"/>
            <view v-if="show_today_step" class="step-num color-white text-center flex-all-center"
                  @click="toHistoryStep">
                <view v-if="today_step === 'loading'">
                    <uni-load-more color="#ffffff" status="loading"
                                   :contentText="{contentrefresh: '步数获取中'}"></uni-load-more>
                </view>
                <view v-else-if="today_step === null">
                    <view>步数获取失败</view>
                    <view>请下拉获取</view>
                </view>
                <view v-else>
                    <view>今日步数</view>
                    <view class="font34">{{ today_step }}</view>
                </view>
            </view>
        </view>

        <uni-notice-bar
            v-if="notice_data"
            :text="notice_data.text"
            :background-color="notice_data.background_color"
            :color="notice_data.color"
            :single="notice_data.single"
            :scrollable="notice_data.scrollable"
            :showIcon="notice_data.showIcon"
            :speed="notice_data.speed"
        />

        <view class="activity-list">

            <view class="activity-lable-title flex-kai p10">
                <view class="flex-row">
                    <view class="color-title pr10">最新活动</view>
                    <navigator class="color-primary pl10 pr10"
                               url="/pages/activity/user/activity_list?type=join">
                        <text>我参加的</text>
                        <uni-icons type="forward" color="#2d8cf0"/>
                    </navigator>
                </view>

                <view v-if="!closed_create_active" class="color-primary" hover-class="navigator-hover"
                      @click="addActivity">
                    <text>创建活动</text>
                    <uni-icons type="forward" color="#2d8cf0"/>
                </view>
            </view>


            <view v-if="user_latest_attend_details" class="p10 font14 flex-row color-sub">
                <view>最近参加的活动:</view>
                <view class="flex-row pl5" hover-class="navigator-hover"
                      @click="toDetail(user_latest_attend_details)">
                    <view class="ellipsis" style="max-width: calc(100vw - 220px);">
                        {{ user_latest_attend_details.active_name }}
                    </view>
                    <view>
                        <text class="pl10">立即前往</text>
                        <uni-icons type="forward" size="14" color="#80848f"/>
                    </view>
                </view>
            </view>

            <view v-if="show_platform_population" class="flex-kai p10">
                <view>
                    <view v-if="active_num" class="flex-row">
                        <view class="beat-list">
                            <view v-for="(item, index) in 4" class="beat" :key="index"
                                  :style="{animationDelay: `${index * 100}ms`}"></view>
                        </view>
                        <view class="color-sub font14" style="line-height: 30px;">{{ active_num }}人正在参与</view>
                    </view>
                </view>
                <navigator v-if="list.length" class="search bg-white flex-kai"
                           url="activity_list?to_search=1">
                    <view class="input-view">
                        <view class="search-icon left-icon flex-all-center">
                            <uni-icons type="search" size="18" color="#bbbec4"/>
                        </view>
                        <input class="input bg-background" disabled placeholder="搜索活动"
                               placeholder-style="color:#bbbec4;font-size: 14px"/>
                    </view>
                </navigator>
            </view>

            <template v-if="!activity_list_loading">
                <view v-if="!list.length" class="text-center" style="padding-top: 10vh;">
                    <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
                    <view class="color-sub">暂无活动</view>
                </view>

                <view class="activity-item flex-row" v-for="item in list" :key="item.id"
                      hover-class="navigator-hover" @click="toDetail(item)">
                    <image class="logo" :src="item.logo || active_default_logo" mode="aspectFill"/>
                    <view class="right">
                        <view class="color-title font18 ellipsis--l2">{{ item.name }}</view>
                        <view v-if="item.organizer" class="color-content pt5 ellipsis">
                            主办方：{{ item.organizer }}
                        </view>
                        <view class="pt5">
                            <activity-type-tag :type="item.types"/>
                            <text v-if="item.rank_set && item.rank_set['shield_other']"
                                  class="iconfont icon-VIP pl5 color-warning"></text>
                            <text v-if="item.rank_set && item.rank_set.closed_AD === 1"
                                  class="iconfont icon-ad-free pl5 color-success"></text>
                            <text v-if="item.conf && item.conf.active && item.conf.active.enter_types === 2"
                                  class="iconfont icon-lock pl5 color-error"></text>
                        </view>
                    </view>
                </view>

                <uni-load-more v-if="more_loading" status="loading"></uni-load-more>
                <uni-load-more v-if="is_last_page && list.length > 3" status="noMore"
                               :contentText="{contentnomore: '我是有底线的'}"></uni-load-more>
                <uni-load-more v-if="!is_last_page && !more_loading && !activity_list_loading" status="more">
                </uni-load-more>
            </template>
        </view>

        <view v-if="!closed_create_active" class="add-activity flex-all-center bg-primary"
              hover-class="navigator-hover" @click="addActivity">
            <uni-icons type="plusempty" size="30" color="#ffffff"/>
        </view>

        <tabbar v-if="show_tab"></tabbar>

        
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'

import activityTypeTag from '../components/activity-type-tag.vue'
import tabbar from '@/components/tabbar.vue'

export default {
    components: {
        tabbar,
        activityTypeTag
    },
    data() {
        return {
            show_tab: false,
            today_step: 'loading',
            active_num: null,
            activity_list_loading: true,
            list: [],
            load_page: 1,
            is_last_page: false,
            more_loading: false,
            user_latest_attend_details: null,
            notice_data: null,
            closed_create_active: false,
            show_today_step: false,
            show_platform_population: false,
            top_image_src: '',
            active_default_logo: this.xwy_config.active_default_logo
        }
    },
    onShareAppMessage() {
        let path = '/pages/index/index?userid=' + app.globalData['userid']
        const active_id = app.globalData['shop_info']?.['extend_set']?.['shield_other_active']?.active_id
        if (active_id) path += `&id=${active_id}`
        return {
            path
        }
    },
    onLoad(e) {
        let from_tab = false
        if (e.from && e.from === 'tab') {
            from_tab = true
            this.$uni.hideHomeButton()
        }

        login.uniLogin(err => {
            this.closed_create_active = xwy_api.closed_create_active()

            if (err && err.errMsg)
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})


            if (app.globalData['shop_info']?.['shop_set']?.['shop_details']?.shop_name) {
                uni.setNavigationBarTitle({
                    title: app.globalData['shop_info']['shop_set']['shop_details'].shop_name
                })
            }
            
            let show_today_step = true
            let top_image_src_ = 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/diy_template/banner.jpg'
            
            let show_platform_population = true
            
            if (app.globalData['shop_info']?.['extend_set']?.['conf_set']?.['index_page']) {
                const { closed_top_sport, closed_top_total_person, top_image_src } = app.globalData['shop_info']['extend_set']['conf_set']['index_page']
                
                if (closed_top_sport) show_today_step = false
                if (top_image_src) top_image_src_ = top_image_src
                
                if (closed_top_total_person) show_platform_population = false
            }

            // #ifndef MP-WEIXIN
            // 只有微信小程序才显示今日步数
            show_today_step = false
            // #endif
            this.show_today_step = show_today_step
            this.top_image_src = top_image_src_
            this.show_today_step && this.getMyStep()
            
            this.show_platform_population = show_platform_population

            this.show_tab = from_tab
            this.setIndexConf()

            this.getActivityList()

        })
    },
    onPullDownRefresh() {
        this.load_page = 1
        this.is_last_page = false
        this.today_step = 'loading'
        this.activity_list_loading = true


        this.getMyStep(true)
        this.getActivityList()
        uni.stopPullDownRefresh()
    },
    onReachBottom() {
        if (!this.activity_list_loading && !this.more_loading && !this.is_last_page)
            this.getActivityList(true)
    },
    methods: {
        addActivity() {
            this.$uni.navigateTo('/pages/create-activity/index')
        },

        toHistoryStep() {
            if (this.today_step !== 'loading' && this.today_step !== null)
                this.$uni.navigateTo('/pages/wechat-step/history_step')
        },

        setIndexConf() {
            if (app.globalData['shop_info']?.['shop_set']?.['page_diy_set']?.['index_page_set']) {
                const index_page_set = app.globalData['shop_info']['shop_set']['page_diy_set']['index_page_set']
                if (index_page_set && index_page_set.length) {
                    const notice_conf = index_page_set.find(v => v.type === 1)
                    if (notice_conf && notice_conf.data) this.notice_data = notice_conf.data
                }
            }
        },

        getMyStep(update = false) {
            if (app.globalData['today_step'] && !update)
                return this.setTodayStep(app.globalData['today_step'])

            // PC不获取步数（无法获取步数）
            const system_info = uni.getSystemInfoSync()
            const platform = system_info.platform
            if (platform && (platform === 'mac' || platform === 'windows' || platform === 'linux')) {
                this.today_step = null
                return
            }

            xwy_api.getWeRunData(res => {
                if (res?.data?.['crypt_data']?.['stepInfoList']?.length) {
                    const step_list = res.data['crypt_data']['stepInfoList']
                    const today_step = step_list[step_list.length - 1].step

                    return this.setTodayStep(today_step)
                }

                this.today_step = null

            })
        },

        setTodayStep(step) {
            const step_count = 20
            const one_step = Math.floor(step / step_count)
            let today_step = 0
            const interval = setInterval(() => {
                today_step += one_step
                if (today_step > step) {
                    today_step = step
                    this.today_step = today_step
                    clearInterval(interval)
                }
                this.today_step = today_step
            }, 50)
        },


        getActivityList() {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
            }
            if (this.load_page > 1) this.more_loading = true

            xwy_api.ajax({
                url: 'front.flat.sport_step.active_list/active_list',
                data: {
                    // types: 2,
                    access_token: app.globalData['access_token'],
                    page: this.load_page,
                    perpage: 10,
                    get_total_active_time: 1,
                    get_latest_active: 1
                },
                success: res => {
                    if (res?.status !== 1) {
                        this.is_last_page = true
                        return uni.showModal({
                            title: '最新活动获取失败',
                            content: res.info || '最新活动获取失败',
                            showCancel: false
                        })
                    }

                    if (res.data?.user_latest_attend_details)
                        this.user_latest_attend_details = res.data.user_latest_attend_details

                    if (res.data.active_num) this.active_num = res.data.active_num

                    const data = res.data['active_list']
                    if (!data) {
                        this.is_last_page = true
                        return false
                    }
                    this.is_last_page = data.is_lastpage || false
                    const list = data.data || []

                    this.list = [...this.list, ...list]
                    this.load_page++
                    if (this.activity_list_loading) this.activity_list_loading = false
                    if (this.more_loading) this.more_loading = false
                    if (data.total) this.total = data.total
                }
            })
        },

        toDetail(data) {
            utils.toActivityDetail(data)
        },
    }
}
</script>

<style>
.page {
    min-height: 100vh;
    padding-bottom: 80px;
    box-sizing: border-box;
}

.today-step {
    position: relative;
    width: 100vw;
    overflow: hidden;
    /* #ifdef H5 */
    max-width: 500px;
    /* #endif */
}

.bg-img {
    width: 100%;
}

.zhuanquanquan {
    width: 250px;
    height: 250px;
    position: absolute;
    top: -130px;
    right: -130px;
    animation: rotate 20s linear infinite;
    z-index: 2;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.step-num {
    position: absolute;
    width: 130px;
    height: 130px;
    border-radius: 50%;
    border: 10px solid rgba(255, 255, 255, .5);
    animation: up-down 3s linear infinite;
    z-index: 2;
    top: calc(50% - 70px);
}

@keyframes up-down {
    0% {
        top: calc(50% - 70px);
    }
    33% {
        top: calc(50% - 75px);
    }
    66% {
        top: calc(50% - 65px);
    }
    100% {
        top: calc(50% - 70px);
    }
}

.uni-noticebar {
    margin-bottom: 0 !important;
}

.beat-list {
    position: relative;
    top: -10px;
    margin-right: 2px;
    display: flex;
    flex-direction: row;
    align-items: flex-end;
}

.beat {
    width: 3px;
    height: 16px;
    margin-right: 1px;
    background-color: #59c6ff;
    animation-name: beat;
    animation-duration: 600ms;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
}

@keyframes beat {
    0% {
        height: 16px;
    }
    50% {
        height: 3px;
    }
    100% {
        height: 16px;
    }
}

.activity-list {
    padding-top: 10px;
}

.activity-lable-title {
    border-bottom: 1px solid #eee;
}

.activity-item {
    padding: 20px 10px;
    border-bottom: 1px solid #eee;
}

.activity-item .logo {
    display: block;
    width: 130px;
    height: 80px;
    border-radius: 5px;
    min-width: 130px;
}

.activity-item .right {
    width: calc(100% - 130px);
    padding-left: 10px;
    box-sizing: border-box;
}

.search {

}

.search, .search .input {
    height: 30px;
    line-height: 30px;
}

.input-view {
    position: relative;
}

.search-icon {
    position: absolute;
    width: 30px;
    height: 30px;
    top: 0;
}

.left-icon {
    left: 0;
}

.search .input {
    width: 150px;
    padding: 0 30px;
    border-radius: 20px;
    box-sizing: border-box;
}

.add-activity {
    position: fixed;
    bottom: 130px;
    right: 10px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    opacity: .7;
}
</style>
