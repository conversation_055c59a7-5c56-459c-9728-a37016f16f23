<template>
    <view>
        <uni-popup ref="popup" :is-mask-click="false" @touchmove.stop.prevent="">
            <view class="main text-center">
                <view class="top bg-primary color-white" style="padding: 50px 20px 20px;">
                    <icon type="success" size="80" color="#ffffff"></icon>
                    <view class="font18">{{ exchange_success_info }}</view>
                </view>
                <view class="bg-white" style="padding: 20px 20px 30px;">
                    <!-- <view>{{exchange_success_info}}</view> -->
                    <view v-if="exchange_success_tip_text_list.length">
                        <view v-for="(item, index) in exchange_success_tip_text_list" :key="index"
                              :style="'color: ' + item.color">
                            <text v-if="item.is_redpack" class="iconfont icon-red-packet pr5"
                                  :style="'color: ' + item.color"></text>
                            <text>{{ item.text }}</text>
                            <view v-if="item.is_redpack && item.amount" class="pt5">
                                <text class="font14">￥</text>
                                <text class="font18">{{ item.amount }}</text>
                                <text class="font14">元</text>
                            </view>
                        </view>
                    </view>
                    <template v-if="nextPoint && pointDetails.name">
                        <view class="pt15">{{ arrivalPointTips }}</view>
                        <view class="p10">
                            <button v-if="pointDetails.news_id" class="button-style bg-primary"
                                    @click="exchangeSuccessLookPointPopup">
                                {{ lookNewsButtonText }}
                            </button>

                            <button v-if="examButtonShow" class="button-style bg-warning"
                                    @click="toExamDetail">
                                前往答题
                            </button>

                            <button v-if="lotteryButtonShow" class="button-style bg-error" @click="toLottery">
                                前往抽奖
                            </button>

                            <button v-if="sportMomentButtonShow" class="button-style bg-error"
                                    @click="toComment">
                                前往运动圈打卡
                            </button>

                            <button v-if="togetherPicButtonShow" class="button-style bg-error"
                                    @click="toTogetherPic">
                                照片合成
                            </button>

                            <button v-if="redpackButtonShow" class="button-style bg-red" @click="getRedpack">
                                抢红包
                            </button>

                            <button v-if="certificateButtonShow" class="button-style bg-light-primary"
                                    @click="lookCertificate">
                                查看证书
                            </button>
                        </view>
                    </template>
                </view>

                <xwy-ad v-if="adOpen" :ad_type="3"></xwy-ad>
            </view>

            <view class="pt5"></view>
            <xwy-ad v-if="adOpen" :ad_type="66"></xwy-ad>
            <view v-if="exchange_success_popup_close_icon_show" class="cancel text-center">
                <icon type="cancel" color="#ffffff" size="28" @click="popupClose()"/>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "exchange-success-popup",
    emits: ['exchangeSuccessLookPointPopup', 'toExamDetail', 'toLottery', 'toTogetherPic', 'getRedpack'],
    props: {
        activeDetails: {
            type: Object,
            default: () => ({})
        }
    },

    data() {
        return {
            nextPoint: false,
            isEndPoint: false,
            pointDetails: {},
            exchange_success_info: '',
            exchange_success_tip_text_list: [],
            exchange_success_popup_close_icon_show: true
        }
    },

    computed: {
        adOpen() {
            return !this.activeDetails.rank_set?.closed_AD
        },

        arrivalPointTips() {
            const describe = this.activeDetails.active_id === '3645c9194f8eb9ef0b875e5a770ec4a7' ? '重回' : '已到达'
            return `恭喜你${describe} ${this.pointDetails.name }`
        },

        lookNewsButtonText() {
            if (this.activeDetails.active_id === '3645c9194f8eb9ef0b875e5a770ec4a7') return '查看校史大事'
            return '查看点位知识'
        },

        examButtonShow() {
            // 雷子的客户6160553994bddaddc461002e0068aee5，兑换成功弹窗不显示答题入口
            if (this.activeDetails.active_id === '6160553994bddaddc461002e0068aee5') return false

            if (!this.pointDetails.exam_id) return false

            // 开启了需要阅读文章才能答题，这里不显示入口
            if (this.activeDetails.conf?.active?.answer_need_read?.open) return false

            return true
        },

        lotteryButtonShow() {
            if (this.isEndPoint) return false
            return !!this.pointDetails.conf?.lottery?.lottery_id
        },

        sportMomentButtonShow() {
            // 开启了运动圈功能，到达终点后显示去运动圈打卡
            if (!this.activeDetails.rank_set.open_sport_moment) return false

            // 开了照片合成的不显示，因为照片合成的不能私自发运动圈
            if (this.activeDetails.rank_set.together_pic) return false

            return this.isEndPoint
        },

        togetherPicButtonShow() {
            return this.activeDetails.rank_set?.together_pic && this.pointDetails.conf?.together_pic?.open
        },

        redpackButtonShow() {
            if (!this.activeDetails.rank_set?.redpack) return false
            if (!this.pointDetails.conf?.redpack_rules?.open) return false
            const {types, open} = this.activeDetails.conf?.active?.redpack_rules || {}
            return open && types === 1
        },

        certificateButtonShow() {
            if (this.activeDetails.conf?.active?.certificate_close) return false
            return this.isEndPoint
        }
    },

    mounted() {
        this.exchange_success_popup_close_icon_show = this.xwy_config.mapPageExchangeSuccessPopupCloseIconShow(this.activeDetails.active_id)
    },

    methods: {
        open(res, pointDetails, nextPoint, isEndPoint) {
            this.pointDetails = pointDetails
            this.nextPoint = nextPoint
            this.isEndPoint = isEndPoint
            
            this.exchange_success_info = res.info || '兑换成功'
            if (res.data?.['exchange_result']?.['show_text']?.length) {
                const show_text = res.data['exchange_result']['show_text']
                show_text.forEach(v => {
                    if (v.text && v.text.includes('红包')) v.is_redpack = true
                })
                this.exchange_success_tip_text_list = show_text
            }
            
            this.$refs.popup.open()
        },

        popupClose() {
            this.$refs.popup.close()
        },

        // 兑换成功弹窗点击查看点位知识介绍
        exchangeSuccessLookPointPopup() {
            this.$emit('exchangeSuccessLookPointPopup', this.pointDetails)
            // this.popupClose()
        },

        toExamDetail() {
            this.$emit('toExamDetail', this.pointDetails)
            this.popupClose()
        },

        toLottery() {
            this.$emit('toLottery', this.pointDetails)
            this.popupClose()
        },

        toComment() {
            this.$uni.navigateTo('/pages/comment/list?active_id=' + this.id)
            this.popupClose()
        },

        toTogetherPic() {
            this.$emit('toTogetherPic', this.pointDetails.id)
        },

        getRedpack() {
            this.$emit('getRedpack', this.pointDetails.id)
        },

        lookCertificate() {
            this.$uni.navigateTo(`/pages/activity/other/certificate?id=${this.activeDetails.active_id}`)
        },
    }
}
</script>

<style lang="scss">
.main {
    width: 300px;
    border-radius: 10px;
    overflow: hidden;
}

.button-style {
    line-height: 40px;
    border-radius: 20px;
    color: #ffffff;
    margin-bottom: 5px;
}
</style>