<template>
    <view>
        <!--用来计算最终所有文字所需的高度，不用显示在界面上-->
        <view class="subtitle-hide">
            <text space="nbsp">{{ set.text }}</text>
        </view>
        
        
        <uni-popup ref="popup" @touchmove.stop.prevent="" :is-mask-click="false">
            <view class="popup" :style="backgroundStyle" @click="skipSubtitle">

                <scroll-view class="subtitle" scroll-y="true" :scroll-into-view="scrollBottomId"
                             scroll-with-animation="true" :style="{height: subtitleHeight + 'px'}">
                    <text :style="{color: set.text_set.color || '#000'}" space="nbsp">{{ subtitle }}</text>
                    <view :id="'scroll-bottom' + subtitleIndex" style="width: 100%; height: 1px;"></view>
                </scroll-view>
                
                <view v-if="!subtitleShowEnd" class="skip-container flex-all-center">
                    <view class="skip-icon">
                        <text class="iconfont icon-fast-forward font24"
                              :style="{color: set.text_set.color || '#000'}">
                        </text>
                    </view>
                </view>
                
                <view class="flex-all-center p10">
                    <view class="confirm-button color-white text-center bg-warning"
                          :style="confirmButtonStyle" @click.stop="popupClose">
                        {{ set.button.text || '关闭' }}
                    </view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "map-no-exchange-tips-popup",
    props: ['set'],
    data() {
        return {
            subtitle: '',
            subtitleHeight: 200,
            subtitleShowEnd: false,
            subtitleIndex: -1,
            scrollBottomId: ''
        }
    },
    
    computed: {
        backgroundStyle() {
            const {color = '#fff', image = ''} = this.set?.background
            return `background-color: ${color}; background-image: url(${image});`
        },
        
        confirmButtonStyle() {
            const {text_color = '#fff', bg_color = '#f90'} = this.set?.button
            return `color: ${text_color}; background-color: ${bg_color}; opacity: ${this.subtitleShowEnd ? 1 : 0}`
        }
    },

    mounted() {

    },

    methods: {
        async open() {
            this.subtitleAudio ||= uni.createInnerAudioContext()
            
            await this.setSubtitleHeight()

            this.subtitleMove()
            this.$refs.popup.open()
            
        },
        
        async setSubtitleHeight() {
            if (this.set?.background?.image) {
                const img_info = await this.$uni.getImageInfo(this.set.background.image)
                const {width, height} = img_info
                const popupWidth = uni.getWindowInfo().windowWidth * .9 // 窗口大小为90vw
                const popupHeight = height * popupWidth / width
                
                // 减去padding-top、padding-bottom、底部按钮的高度、按钮和内容的间隙
                this.subtitleHeight = popupHeight - 40 - 10 - 60 - 20
                return
            }

            return new Promise(resolve => {
                uni.createSelectorQuery().in(this).select(".subtitle-hide").boundingClientRect(({height}) => {
                    if (height) this.subtitleHeight = height
                    resolve()
                }).exec()
            })
        },
        
        subtitleMove() {
            const subtitle = this.set.text
            this.clearSubtitleInterval()
            this.subtitleShow = true
            this.subtitle = ''
            let index = 0
            this.subtitleAudio.src = this.xwy_config.object_storage_url + 'music/sport/long-march-game/kakaka.mp3'
            this.subtitleAudio.loop = true
            this.subtitleAudio.volume = 0.1
            this.subtitleAudio.play()
            this.showSubtitleInterval = setInterval(() => {
                this.subtitle = subtitle.slice(0, index)
                index++
                this.scroll2Bottom(index)
                if (index > subtitle.length) this.showEnd()
            }, 100)
        },

        scroll2Bottom(index) {
            this.subtitleIndex = index
            this.$nextTick(() => {
                this.scrollBottomId = 'scroll-bottom' + this.subtitleIndex
            })
        },

        clearSubtitleInterval() {
            this.subtitleAudio && this.subtitleAudio.stop()
            clearInterval(this.showSubtitleInterval)
            this.showSubtitleInterval = null
        },

        skipSubtitle() {
            if (this.subtitleShowEnd) return
            // 防止连续点击导致this.showSubtitleInterval定时器没有清除
            setTimeout(() => {
                if (this.showSubtitleInterval) {
                    this.showEnd()
                    this.subtitle = this.set.text
                    
                    this.scroll2Bottom(this.subtitle.length)
                }
            }, 100)
        },

        showEnd() {
            this.subtitleShowEnd = true
            this.clearSubtitleInterval()
            this.destroyAudio()
        },

        destroyAudio() {
            this.subtitleAudio && this.subtitleAudio.destroy()
        },
        
        popupClose() {
            if (this.subtitleShowEnd) this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.subtitle-hide, .popup {
    width: 90vw;
    box-sizing: border-box;
}

.subtitle-hide, .subtitle {
    word-break: break-all;
    word-wrap: break-word;
    font-size: 17px;
    line-height: 24px;
}

.subtitle-hide {
    position: fixed;
    top: -1000vh;
    padding: 0 20px;
}

.popup {
    position: relative;
    padding: 40px 20px 10px;
    border-radius: 10px;
    margin-bottom: 10vh;
    background-size: 100% auto;
    background-repeat: no-repeat;
    
    .subtitle {
        /*最大高度不能导致弹窗超出屏幕宽度。
        减去padding-top、padding-bottom、底部按钮的高度、按钮和内容的间隙、减去弹窗上下留一点间距*/
        max-height: calc(90vh - 40px - 10px - 60px - 20px - 4vh);
    }

    ::v-deep ::-webkit-scrollbar {
        display: none;
        width: 0;
        height: 0;
        color: transparent;
    }

    .skip-container {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 40px;
        height: 40px;

        .skip-icon {
            animation: up-down 1.2s linear infinite;
        }


        @keyframes up-down {
            0% {
                transform: translateY(0) rotate(90deg);
            }
            25% {
                transform: translateY(-5px) rotate(90deg);
            }
            50% {
                transform: translateY(0) rotate(90deg);
            }
            75% {
                transform: translateY(5px) rotate(90deg);
            }
            100% {
                transform: translateY(0) rotate(90deg);
            }
        }
    }

    .confirm-button {
        min-width: 120px;
        padding: 0 10px;
        margin-top: 20px;
        line-height: 40px;
        border-radius: 20px;
    }
}

</style>