<template>
    <view>
        <uni-popup ref="point_detail" type="center"
                   @touchmove.stop.prevent="" @maskClick="pointDetailsPopupClose">
            <view class="point-detail-popup"
                  :style="{backgroundColor: map_point_set.content.bg_color}">
                <view class="point-detail-title text-center flex-center"
                      :style="{backgroundColor: map_point_set.top_title.line_color, color: map_point_set.top_title.text_color}">
                    <view v-for="(item, index) in map_point_popup_set" :key="index"
                          class="point-detail-popup-tab font14"
                          :style="show_point_detail_type === item.id ? `color: ${map_point_set.top_title.text_actived_color}; border-bottom: 2px solid ${map_point_set.top_title.text_actived_color}` : ''"
                          @click="pointDetailTypeChange(item.id)">{{ item.name }}
                    </view>
                </view>


                <scroll-view class="point-detail-content p10 color-content" scroll-y="true">

                    <template v-if="show_point_detail_type === 2">
                        <view v-if="read_reward_count_down !== null" class="flex-kai">
                            <view></view>
                            <view>
                                <text class="iconfont icon-alarm-clock color-disabled"></text>
                                <text class="color-sub" style="padding-left: 2px;">
                                    {{ read_reward_count_down }}
                                </text>
                            </view>
                        </view>

                        <view v-if="show_article.title" class="text-center pb5"
                              :style="{color: map_point_set.content.text_color}">
                            {{ show_article.title }}
                        </view>

                        <view v-if="show_article.content"
                              :style="{color: map_point_set.content.text_color}">
                            <u-parse :content="show_article.content"/>
                        </view>

                        <template
                            v-if="activeDetails.conf.active.point_news_to_details_page_show || (activeDetails.conf.active.answer_need_read && activeDetails.conf.active.answer_need_read.open)">
                            <view class="text-center" style="padding-top: 50px;">
                                <image
                                    src="https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/window/answer.png"
                                    mode="widthFix" style="width: 200px;"/>
                            </view>
                            <view class="flex-center pt15">
                                <view class="bg-light-primary color-white text-center"
                                      style="width: 200px; line-height: 40px; border-radius: 20px;"
                                      @click="pointPopupToReadPointNews">
                                    查看点位知识
                                </view>
                            </view>
                        </template>
                    </template>
                    <template v-if="show_point_detail_type === 3">
                        <view class="text-center" style="padding-top: 50px;">
                            <image
                                src="https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/window/answer.png"
                                mode="widthFix" style="width: 200px;"/>
                        </view>
                        <view class="flex-center pt15">
                            <view class="bg-warning color-white text-center"
                                  style="width: 200px; line-height: 40px; border-radius: 20px;"
                                  @click="toExamDetail">
                                {{ map_point_popup_button_text.exam }}
                            </view>
                        </view>
                    </template>
                    <template v-if="show_point_detail_type === 4">
                        <view class="text-center" style="padding-top: 50px;">
                            <image
                                src="https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/window/Luckydraw.png"
                                mode="widthFix" style="width: 200px;"/>
                        </view>
                        <view class="flex-center pt15">
                            <view class="bg-success color-white text-center"
                                  style="width: 200px; line-height: 40px; border-radius: 20px;"
                                  @click="toLottery">
                                {{ map_point_popup_button_text.lottery }}
                            </view>
                        </view>
                    </template>
                    <template v-if="show_point_detail_type === 5">
                        <view class="text-center" style="padding-top: 50px;">
                            <text class="iconfont icon-red-packet color-red" style="font-size: 100px;"></text>
                            <view v-if="redpackCheckErrorInfo && redpackCheckErrorInfo !== 'loading'"
                                  class="color-sub font14 pt15">
                                {{ redpackCheckErrorInfo }}
                            </view>
                            <view v-if="!redpackCheckErrorInfo" class="flex-center pt15">
                                <view class="bg-red color-white text-center"
                                      style="width: 200px; line-height: 40px; border-radius: 20px;"
                                      @click="getRedpack">
                                    {{ map_point_popup_button_text.redpack }}
                                </view>
                            </view>
                        </view>
                    </template>

                    <template v-if="show_point_detail_type === 6">
                        <view v-if="point_user_list.loading" class="color-sub font14 text-center">
                            加载中...
                        </view>
                        <view v-if="!point_user_list.loading && point_user_list.total"
                              class="color-sub font14 text-center">
                            共{{ point_user_list.total }}{{ isTeamMap ? '支队伍' : '位用户' }}到达{{ pointDetails.name }}
                        </view>
                        <view class="flex-row flex-wrap">
                            <view class="user-list-item text-center"
                                  v-for="(item, index) in point_user_list.list" :key="index">
                                <template>
                                    <image v-if="isTeamMap" mode="aspectFill"
                                           :src="item.conf && item.conf.logo || ''"/>
                                    <image v-else :src="item.headimg || ''" mode="aspectFill"/>
                                </template>
                                <view class="color-content ellipsis font14">
                                    <template v-if="isTeamMap">{{ item.name || '' }}</template>
                                    <template v-else>{{ item.must_submit[0].value || '' }}</template>
                                </view>
                            </view>
                        </view>
                        <view v-if="!point_user_list.list.length && !point_user_list.loading"
                              class="text-center" style="padding-top: 50px;">
                            <text class="iconfont icon-empty-state color-border"
                                  style="font-size: 100px;"></text>
                            <view class="color-sub">
                                暂无{{ isTeamMap ? '队伍' : '用户' }}到达{{ pointDetails.name }}
                            </view>
                        </view>
                    </template>

                    <template v-if="show_point_detail_type === 7">
                        <view class="text-center" style="padding-top: 50px;">
                            <uni-icons type="images" color="#ff9900" size="100"/>
                        </view>
                        <template>
                            <view
                                v-if="pointDetails['point_lock_state'] && pointDetails['point_lock_state']['have_together_pic']"
                                class="text-center color-sub font14"
                            >
                                点位照片已合成
                            </view>
                            <view v-else class="flex-center pt15">
                                <view class="bg-warning color-white text-center"
                                      style="width: 200px; line-height: 40px; border-radius: 20px;"
                                      @click="toTogetherPic">
                                    {{ map_point_popup_button_text.together_pic }}
                                </view>
                            </view>
                        </template>
                    </template>
                </scroll-view>
            </view>
            <view class="flex-all-center">
                <view @click="pointDetailsPopupClose">
                    <uni-icons type="close" size="24" color="#ffffff"/>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "point-popup",
    emits: ['updateUserDetails', 'toTogetherPic', 'toLottery', 'toReadPointNews', 'toExamDetail', 'getRedpack'],
    props: {
        activeDetails: {
            type: Object,
            default: () => ({})
        },
        activeMoreData: {
            type: Object,
            default: () => ({})
        },
        userDetails: {
            type: Object,
            default: () => ({})
        },
        redpackCheckErrorInfo: {
            type: String,
            default: ''
        }
    },

    data() {
        return {
            pointDetails: {},
            map_point_popup_set: [],
            map_point_set: {
                content: {
                    bg_color: '#ffffff',
                    text_color: '#495060'
                },
                top_title: {
                    line_color: "#ffffff",
                    text_actived_color: "#e20f04",
                    text_color: "#1c2438"
                }
            },
            show_point_detail_type: -1,
            map_point_popup_button_text: {
                exam: '',
                lottery: '',
                redpack: '',
                together_pic: '',
            },
            inPoint: false,
            point_user_list: {
                list: [],
                load_page: 1,
                is_last_page: false,
                loading: false,
                total: 0
            },
            show_article: {},
            read_reward_count_down: null,
        }
    },

    computed: {
        isTeamMap() {
            return !!this.activeDetails?.rank_set?.['show_team_list']
        },
    },

    mounted() {
        this.getMapPointPopupColorSet()
        this.getMapPointPopupSet()
    },

    methods: {
        open(pointDetails, in_point = false) {
            this.pointDetails = pointDetails
            this.inPoint = in_point

            this.setPopupTitleList()

            this.$refs.point_detail.open()
        },

        getMapPointPopupSet() {
            const default_map_point_popup_set = [
                {"id": 2, "name": "点位知识"},
                {"id": 3, "name": "答题", "button_text": "答题"},
                {"id": 4, "name": "抽奖", "button_text": "抽奖"},
                {"id": 5, "name": "抢红包", "button_text": "抢红包"},
                {"id": 6, "name": this.isTeamMap ? "队伍列表" : "用户列表"},
                {"id": 7, "name": "照片合成", "button_text": "照片合成"}

            ]
            let map_point_popup_set = this.activeMoreData?.active_conf_set?.active_conf_set?.map_point_popup_set
            map_point_popup_set ||= default_map_point_popup_set

            const map_point_popup_set_all = []
            map_point_popup_set.forEach(v => {
                if (v.id === 2) map_point_popup_set_all.push(v)
                if (v.id === 3 && this.activeDetails.rank_set?.exam_open) {
                    this.map_point_popup_button_text.exam = v.button_text || v.name || '去答题'
                    map_point_popup_set_all.push(v)
                }
                if (v.id === 4 && this.activeDetails.rank_set?.['lottery_open']) {
                    this.map_point_popup_button_text.lottery = v.button_text || v.name || '去抽奖'
                    map_point_popup_set_all.push(v)
                }
                if (v.id === 5 && this.activeDetails.rank_set?.['redpack'] && this.activeDetails.conf.active.redpack_rules?.open && this.activeDetails.conf.active.redpack_rules?.types === 1) {
                    this.map_point_popup_button_text.redpack = v.button_text || v.name || '抢红包'
                    map_point_popup_set_all.push(v)
                }
                if (v.id === 6) map_point_popup_set_all.push(v)
                if (v.id === 7 && this.activeDetails.rank_set?.['together_pic']) {
                    this.map_point_popup_button_text.together_pic = v.button_text || v.name || '照片合成'
                    map_point_popup_set_all.push(v)
                }
            })

            this.map_point_popup_set_all = map_point_popup_set_all
        },

        getMapPointPopupColorSet() {
            const {content = {}, top_title = {}} = this.activeMoreData?.active_conf_set?.map_point_set || {}
            if (content.bg_color) this.map_point_set.content.bg_color = content.bg_color
            if (content.text_color) this.map_point_set.content.text_color = content.text_color
            if (top_title.line_color) this.map_point_set.top_title.line_color = top_title.line_color
            if (top_title.text_actived_color)
                this.map_point_set.top_title.text_actived_color = top_title.text_actived_color
            if (top_title.text_color) this.map_point_set.top_title.text_color = top_title.text_color
        },

        setPopupTitleList() {
            if (!this.map_point_popup_set_all?.length) return
            const map_point_popup_set_all = this.map_point_popup_set_all
            const map_point_popup_set = []
            const conf = this.pointDetails.conf || {}
            map_point_popup_set_all.forEach(v => {
                if (v.id === 1 && this.pointDetails.record_id) {
                    // v.optionFunction = () => this.getPointTeamList(this.pointDetails.record_id)
                    map_point_popup_set.push(v)
                }
                if (v.id === 2 && this.pointDetails.page_id) {
                    v.optionFunction = () => this.getArticleDetail()
                    map_point_popup_set.push(v)
                }
                if (v.id === 6) {
                    v.optionFunction = () => this.showPointUserList()
                    map_point_popup_set.push(v)
                }
                // 答题、抽奖、红包、照片合成需要到达点位才能显示
                if (this.inPoint) {
                    if (v.id === 3 && this.pointDetails.exam_id) {
                        // 开启了需要阅读点位知识才能答题，弹窗不显示答题
                        if (!this.activeDetails.conf.active.answer_need_read?.open) map_point_popup_set.push(v)
                    }
                    if (v.id === 4 && conf.lottery?.lottery_id && conf.lottery.must_exam !== 1) {
                        map_point_popup_set.push(v)
                    }
                    if (v.id === 5 && conf.redpack_rules?.open && conf.redpack_rules?.types === 1) {
                        this.checkPointRepackStatus()
                        map_point_popup_set.push(v)
                    }
                    if (v.id === 7 && conf.together_pic?.open) map_point_popup_set.push(v)
                }
            })
            this.map_point_popup_set = map_point_popup_set
            if (!map_point_popup_set.length) return
            this.show_point_detail_type = map_point_popup_set[0].id
            map_point_popup_set[0].optionFunction?.()
        },




        async getRedpack() {
            this.$emit('getRedpack', this.pointDetails.id)
        },

        showPointUserList() {
            this.point_user_list.load_page = 1

            this.getPointUserList()
            this.$refs.point_detail.open()
        },

        async getPointUserList() {
            if (this.point_user_list.load_page === 1) {
                this.point_user_list.list = []
                this.point_user_list.is_last_page = false
                this.point_user_list.total = 0
            }
            this.point_user_list.loading = true

            const data = {
                active_id: this.activeDetails.active_id,
                point_id: this.pointDetails.id,
                page: this.point_user_list.load_page,
                perpage: 30
            }
            if (this.isTeamMap) data.is_team = 1

            // 晓阳客户的活动，最多显示400个
            if (this.activeDetails.active_id === '8c5f14ca8bd4e9e41771d04fee26ea1f') data.perpage = 400

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.mapPoint/point_user_list',
                data
            })

            this.point_user_list.loading = false

            const res_data = res?.data?.user_list
            if (!res_data) {
                this.point_user_list.is_last_page = true
                return
            }

            this.point_user_list.is_last_page = res_data.is_lastpage
            this.point_user_list.total = res_data.total
            const list = res_data.data || []
            this.point_user_list.list = [...this.point_user_list.list, ...list]
        },

        pointDetailTypeChange(type) {
            if (type === this.show_point_detail_type) return
            this.clearReadRewardCountDownInterval()
            this.show_point_detail_type = type
            const types = {
                2: () => this.getArticleDetail(),
                5: () => this.checkPointRepackStatus(),
                6: () => this.showPointUserList()
            }
            types[type] && types[type]()
        },

        toLottery() {
            this.$emit('toLottery', this.pointDetails)
        },


        async getArticleDetail() {
            if (!this.pointDetails.page_id) return

            // 设置了必须进入到文章详情才能查看点位知识介绍
            if (this.activeDetails.conf.active.point_news_to_details_page_show) return
            // 设置了必须要阅读点位知识才能答题，需要进入到文章详情页面阅读并答题
            if (this.activeDetails.conf.active.answer_need_read?.open) return

            if (this.show_article.news_id !== this.pointDetails.page_id) {
                this.show_article = {}

                this.$uni.showLoading('加载中...')
                const res = await this.xwy_api.request({
                    url: 'front.news/news_details',
                    data: {
                        news_id: this.pointDetails.page_id
                    }
                })
                uni.hideLoading()

                if (res?.status !== 1) {
                    this.$uni.showModal(res?.info || '点位详情获取失败')
                    return
                }

                const details = res.data?.news_details

                if (details.content) details.content = this._utils.newsContentInit(details.content)

                this.show_article = details
            }

            this.$refs.point_detail.open()

            this.setReadRewardCountDown()
        },


        setReadRewardCountDown() {
            if (!this.inPoint) return

            if (!this.activeDetails.rank_set?.reading_reward) return
            if (!this.show_article?.category_id) return
            const set = this.activeDetails.conf.active.reading_reward
            if (!set?.open) return
            if (!set?.rules?.length) return
            const target_set = set.rules.find(v => v.category_id === this.show_article.category_id)
            if (!target_set) return


            this.clearReadRewardCountDownInterval()

            this.time_to_start_reading = Math.floor(new Date().getTime() / 1000) // 记录开始阅读的时间
            let seconds = 0
            this.readRewardCountDownInterval = setInterval(() => {
                seconds++
                this.read_reward_count_down = seconds
                if (seconds === target_set.seconds) {
                    this.getRewards()
                    this.clearReadRewardCountDownInterval()
                }
            }, 1000)
        },

        clearReadRewardCountDownInterval() {
            if (this.readRewardCountDownInterval) {
                clearInterval(this.readRewardCountDownInterval)
                this.readRewardCountDownInterval = null
                this.read_reward_count_down = null
            }
        },

        async getRewards() {
            if (this.pointDetails?.point_lock_state?.have_reading) return
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.integral.reading/reading_reward_active_integral ',
                data: {
                    news_id: this.show_article.news_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](`${this.activeDetails.active_id}+${this.time_to_start_reading}`),
                }
            })

            if (res?.status === 1) this.$emit('updateUserDetails')
        },

        pointPopupToReadPointNews() {
            this.$emit('toReadPointNews', this.pointDetails)
        },


        toExamDetail() {
            this.$emit('toExamDetail', this.pointDetails)
        },

        toTogetherPic() {
            this.$emit('toTogetherPic', this.pointDetails.id)
        },

        pointDetailsPopupClose() {
            this.clearReadRewardCountDownInterval()
            this.$refs.point_detail.close()
        },
    }
}
</script>

<style lang="scss" scoped>
image {
    display: inline-block;
}

.point-detail-popup {
    width: 320px;
    border-radius: 10px;
    overflow: hidden;
}

.point-detail-title .point-detail-popup-tab {
    height: 40px;
    line-height: 40px;
    box-sizing: border-box;
    padding: 0 5px;
}

.point-detail-content {
    height: calc(100vh - 200px);
    box-sizing: border-box;
    position: relative;
}


.user-list-item {
    width: 75px;
    padding: 10px 0;
}

.user-list-item image {
    width: 50px;
    height: 50px;
    border-radius: 50%;
}
</style>