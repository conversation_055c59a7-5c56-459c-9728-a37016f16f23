<template>
    <view class="all-tips flex-row">
        <view class="flex-all-center pr5">

            <image class="user-headimg" v-if="headimg" :src="headimg" mode="aspectFill"/>
        </view>
        <view v-if="userName">{{ userName }}，</view>
        <view>
            已在“{{ activeName }}”活动中累计行走{{ days }}天,
            当前已走到 {{ pointName }}
        </view>
    </view>
</template>

<script>
export default {
    name: "top-cumulative-walk-tips",
    props: {
        userName: {
            type: String,
            default: ''
        },
        headimg: {
            type: String,
            default: ''
        },
        activeName: {
            type: String,
            default: ''
        },
        days: {
            type: Number,
            default: 0
        },
        pointName: {
            type: String,
            default: ''
        }
    }
}
</script>

<style lang="scss">

.all-tips {
    white-space: nowrap;
    word-break: break-all;
    height: 30px;
    line-height: 30px;
    padding: 0 10px;
    box-sizing: border-box;
    border-radius: 15px;
    background-color: rgba(0, 0, 0, .7);
    color: #fff;
    animation: rolling 10s infinite linear;
    position: fixed;
    top: 5px;
    left: 100vw;
    z-index: 9;
}

@keyframes rolling {
    from {
        left: 100vw;
    }
    to {
        left: -1000px;
    }
}

.all-tips .user-headimg {
    width: 22px;
    height: 22px;
    border-radius: 50%;
}
</style>