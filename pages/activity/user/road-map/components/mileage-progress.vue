<template>
    <view class="mileage-progress flex-row">
        <view class="progress">
            <view class="progress-active bg-red" :style="{width: progressWidth}"></view>
        </view>
        <view class="completed-text font12 pl10 color-sub">已完成{{ progressText || 0 }}%</view>
    </view>
</template>

<script>
export default {
    name: "mileage-progress",
    props: ['userMileage', 'activeEndMileage'],
    computed: {
        progress() {
            if (this.userMileage === 0) return 0
            let progress = this.userMileage / this.activeEndMileage
            if (progress < 0) progress = 0
            if (progress > 1) progress = 1
            return progress
        },

        progressText() {
            return Math.floor(this.progress * 100)
        },

        progressWidth() {
            return this.progress * 100 + '%'
        },
    }
}
</script>

<style lang="scss" scoped>
.mileage-progress {
    padding-top: 5px;

    $height: 10px;

    .progress, .progress-active {
        height: $height;
        border-radius: $height * .5;
    }

    .progress {
        width: calc(100% - 80px);
        background-color: #f8f8f8;
    }

    .progress-active {
        background: linear-gradient(to right, #f6a09c, #e20f04);
    }

    .completed-text {
        height: $height;
        line-height: $height;
    }
}
</style>