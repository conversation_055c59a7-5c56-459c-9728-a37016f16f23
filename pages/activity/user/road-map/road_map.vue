<template>
    <view class="page bg-background">
        <template v-if="detail && detail.conf && detail.conf.active.map_types">
            <xwy-ad v-if="detail.rank_set.closed_AD !== 1" :ad_type="3"></xwy-ad>

            <top-cumulative-walk-tips 
                v-if="top_cumulative_walk_tips_show"
                :headimg="headimg"
                :user-name="user_name"
                :active-name="detail.name"
                :days="all_sign_days"
                :point-name="my_position['current_point_details'].name"
            />


            <map
                v-if="need_map"
                id="map"
                class="road-map"
                :style="{height: diyButton.show ? '100vh' : (map_height + 'px')}"
                :longitude="116.39752"
                :latitude="39.908743"
                :scale="map_set && map_set.scale || 10"
                :min-scale="map_set && map_set.min_scale || 3"
                :max-scale="map_set && map_set.max_scale || 20"
                :markers="map_set && map_set.markers"
                :polyline="map_set && map_set.polyline"
                :enable-satellite="enableSatellite"
                :rotate="detail.conf && detail.conf.active && detail.conf.active.map_rotate || 0"
                @callouttap="calloutTap"
                @markertap="markerTap($event)"
                @regionchange="regionChange"
            ></map>
            
            <view v-if="enableSatelliteUserSwitch" class="enable-satellite-switch flex-all-center" 
                  :style="{top: map_height - 80 + 'px'}" @click="enableSatelliteSwitch">
                <text :class="'iconfont color-white font28 icon-' + (enableSatellite ? 'map-1' : 'earth')"></text>
            </view>


            <view v-if="detail.conf.active.map_types === 101">
                <image v-if="detail.thumb" class="logo" mode="widthFix" :src="detail.thumb"/>
                <view class="p10 bg-white">
                    <view>
                        <view class="color-title font18">{{ detail.name }}</view>
                        <view v-if="detail['creator_details'] && detail['creator_details'].organizer"
                              class="color-content font16">
                            主办方：{{ detail['creator_details'].organizer }}
                        </view>
                    </view>
                </view>
            </view>

            <view v-if="detail.conf.active.map_types === 102 && my_position && my_position['current_point_details'] && my_position['current_point_details'].map_pic"
                  :style="'padding-bottom: ' + (bottom_height - 20) + 'px;'" class="big-image">

                <template v-if="map_set && map_set.markers && map_set.markers.length && detail.rank_set">
                    <!--开启了照片合成的显示星星-->
                    <template v-if="detail.rank_set.together_pic">
                        <view class="star-list-item flex-kai" v-for="item in map_set.markers" :key="item.id"
                              :style="{top: `${item.margin_top / scale + item.conf.point_img_detail.point_height / scale - 16}px`, left: `${item.conf.point_img_detail.margin_left / scale + item.conf.point_img_detail.point_width / scale - 50}px`}"
                              @click="calloutTap({markerId: item.id})">
                            <template
                                v-if="my_position && my_position['current_point_details'] && my_position['current_point_details'].id && my_position['current_point_details'].id >= item.id">
                                <text
                                    v-for="i in ((item['point_lock_state']['have_exam'] || 0) + (item['point_lock_state']['have_reading'] || 0) + (item['point_lock_state']['have_together_pic'] || 0))"
                                    :key="i" class="iconfont icon-star-filled" style="color: #fed150">
                                </text>
                                <text
                                    v-for="i in (3 - (item['point_lock_state']['have_exam'] || 0) - (item['point_lock_state']['have_reading'] || 0) - (item['point_lock_state']['have_together_pic'] || 0))"
                                    :key="i" class="iconfont icon-star-filled" style="color: #dbdbdb">
                                </text>
                            </template>
                        </view>
                    </template>

                    <!--设置了答题后点位图片需要覆盖-->
                    <template v-if="detail.rank_set['stepRewardIntegralForMinMax']">
                        <view v-for="item in map_set.markers" :key="item.id" style="position: absolute;"
                              :style="{top: `${item.margin_top / scale}px`, left: `${item.conf.point_img_detail.margin_left / scale}px`}" @click="calloutTap({markerId: item.id})">
                            <image v-if="item.conf.and_drawn_map_with_answer_picture && item['point_lock_state']['have_exam']"
                                   :src="item.conf.and_drawn_map_with_answer_picture"
                                   style="display: block;"
                                   :style="{width: `${(item.conf.point_img_detail.point_width || 0) / scale}px`, height: `${(item.conf.point_img_detail.point_height || 0) / scale}px`}"/>
                        </view>
                    </template>
                </template>

                <image :src="my_position['current_point_details'].map_pic" mode="widthFix"
                       @load="imageLoad" @click="clickImage"/>
            </view>

            <map-no-exchange-tips-popup ref="noExchangeTipsPopup"
                                        v-if="detail.conf.active.map_page_no_exchange_tips && detail.conf.active.map_page_no_exchange_tips.open && detail.conf.active.map_page_no_exchange_tips.text"
                                        :set="detail.conf.active.map_page_no_exchange_tips"/>

        </template>
        
        <view v-if="diyButton.show" class="diy-button-container flex-all-center">
            <view class="flex-all-center" :style="diyButton.style" @click="clickExchange">
                <view class="flex-all-center">{{ diyButton.text }}</view>
            </view>
        </view>


        <view v-if="!diyButton.show" class="detail bg-white" ref="detail"
              :style="{bottom: tabbar_data ? '70px' : '0'}">
            <view class="exchange-view">
                <view class="flex-all-center">
                    <view v-if="exchange_status === 'not_begin'" class="step-num font12">活动未开始</view>
                    <view v-else-if="exchange_status === 'ended'" class="step-num font12">活动已结束</view>
                    <view v-else-if="exchange_status === 'no_status'" class="step-num font12">未审核</view>
                    <template v-else>
                        <view v-if="is_joining === 'false'" class="step-num font12" style="opacity: 0;">
                        </view>
                        <view v-else class="step-num font12">{{ can_exchange_step || '无可换步数' }}</view>
                    </template>
                </view>
                <view v-if="is_joining === 'false'"
                      class="exchange-btn flex-column flex-all-center bg-red color-white text-center"
                      hover-class="navigator-hover" @click="toJoinActivity">
                    <view class="exchange-btn-text font12">参与活动</view>
                    <view class="exchange-btn-go font28">GO</view>
                </view>
                <view v-if="is_joining === 'true' && exchange_status === 'normal'"
                      class="exchange-btn flex-column flex-all-center bg-red color-white text-center"
                      hover-class="navigator-hover" @click="clickExchange">
                    <view class="exchange-btn-text font12">兑换里程</view>
                    <view class="exchange-btn-go font28">GO</view>
                </view>
                <view v-if="is_joining === 'true' && exchange_status !== 'normal'"
                      class="exchange-btn flex-column flex-all-center bg-disabled color-white text-center">
                    <view class="exchange-btn-text font12">兑换里程</view>
                    <view class="exchange-btn-go font28">GO</view>
                </view>
            </view>
            <view v-if="is_joining === 'false' && exchange_status === 'normal'"
                  class="color-red font14 text-center pt5" @click="toJoinActivity">
                未参与活动，立即参与
                <uni-icons type="forward" size="14" color="#e20f04"/>
            </view>
            <view class="color-red font14 text-center pt5" v-if="exchange_status === 'no_status'">
                需管理员审核通过后才能兑换
            </view>
            <view class="flex-kai">
                <view v-if="my_position && detail.map_types !== 101">
                    <view v-if="my_position['current_point_details'] && my_position['current_point_details'].name" class="color-content"
                          @click="calloutTap({markerId: my_position['current_point_details'].id})">
                        <template v-if="is_relay_race || isTeamMap">队伍</template>
                        当前已走到
                        <text class="color-primary pl5">
                            {{ my_position['current_point_details'].name }}
                        </text>
                    </view>
                    <view v-if="my_position['next_point_details'] && my_position['next_point_details'].name && my_position['next_point_details'].max_num"
                          class="color-content color-sub font12">
                        距离下一站
                        {{ my_position['next_point_details'].name }}
                        还差
                        {{ accSub(my_position['next_point_details'].min_num, all_exchange_num) }}
                        {{ detail.conf.active.kilo_unit || '里' }}
                    </view>
                </view>


                <navigator v-if="open_business_kilometers"
                           :url="`/pages/activity/other/user_exchange_list?activity_id=${id}&nickname=${encodeURIComponent(user_name || '')}&headimg=${encodeURIComponent(headimg || '')}`">
                    <text class="iconfont icon-aeroplane font14 color-primary"></text>
                    <text class="font14 color-primary" style="padding-left: 2px;">个人里程兑换详情</text>
                </navigator>
            </view>

            <mileage-progress :user-mileage.sync="mileageProgress.current"
                              :active-end-mileage.sync="mileageProgress.all"/>

            <view class="grid-list flex-row flex-wrap">
                <template v-if="!is_relay_race">
                    <view class="grid-item text-center" @click="getMyStep(true)">
                        <view class="color-sub font14">{{ exchange_map.can_exchange_step }}</view>
                        <view class="color-title">
                            <text>{{ can_exchange_step || 0 }}</text>
                            <text class="step-update-icon iconfont icon-sync color-primary"></text>
                        </view>
                    </view>
                    <view class="grid-item text-center">
                        <view class="color-sub font14">{{ exchange_map.today }}</view>
                        <view class="color-title">{{ today_exchange_step }}</view>
                    </view>
                    <view v-if="topRankShow" hover-class="navigator-hover" @click="toTopList()"
                               class="grid-item text-center">
                        <view class="color-sub font14">
                            <text>{{ exchange_map.top_rank_num }}</text>
                            <uni-icons v-if="detail.rank_set && detail.rank_set.closed_AD"
                                       type="forward" color="#80848f" size="14"/>
                        </view>
                        <template>
                            <!-- 免费(未去广告)活动不显示用户当前排名  - 2022-12-06 17:26:23 -->
                            <view v-if="detail.rank_set && detail.rank_set.closed_AD" class="color-red">
                                {{ top_rank_num >= 1 ? top_rank_num : '暂无排名' }}
                            </view>
                            <view v-else class="color-sub">
                                <text class="font14">查看排行榜</text>
                                <uni-icons type="forward" color="#80848f" size="14"/>
                            </view>
                        </template>
                    </view>

                    <view class="grid-item text-center">
                        <view class="color-sub font14">{{ exchange_map.rate_tips }}</view>
                        <view class="color-title">
                            {{ detail.conf.active.exchange_rate || '?' }}步=1{{ detail.conf.active.kilo_unit || '里' }}
                        </view>
                    </view>
                </template>


                <navigator v-if="current_baton && team_id" class="grid-item text-center"
                           :url="`/pages/activity/relay-race/sort-setting?active_id=${id}&team_id=${team_id}`">
                    <view class="color-sub font14">当前接力到</view>
                    <view class="color-title">第 {{ current_baton }} 棒</view>
                </navigator>

                <view class="grid-item text-center">
                    <view class="color-sub font14">{{ exchange_map.total_kilo }}</view>
                    <view class="color-title">
                        {{ all_exchange_num }}{{ detail.conf.active.kilo_unit || '里' }}
                    </view>
                </view>
                <view class="grid-item text-center">
                    <view class="color-sub font14">{{ exchange_map.total_day }}</view>
                    <view class="color-title">{{ all_sign_days || 0 }}</view>
                </view>
            </view>
        </view>


        <view v-if="exchange_success_info" class="success flex-column flex-all-center">
            <view class="main text-center">
                <view class="top bg-primary color-white" style="padding: 50px 20px 20px;">
                    <icon type="success" size="80" color="#ffffff"></icon>
                    <view class="font18">{{ exchange_success_info }}</view>
                </view>
                <view class="bottom2 bg-white" style="padding: 20px 20px 30px;">
                    <!-- <view>{{exchange_success_info}}</view> -->
                    <view v-if="exchange_success_tip_text_list.length">
                        <view v-for="(item, index) in exchange_success_tip_text_list" :key="index"
                              :style="'color: ' + item.color">
                            <text v-if="item.is_redpack" class="iconfont icon-red-packet pr5"
                                  :style="'color: ' + item.color"></text>
                            <text>{{ item.text }}</text>
                            <view v-if="item.is_redpack && item.amount" class="pt5">
                                <text class="font14">￥</text>
                                <text class="font18">{{ item.amount }}</text>
                                <text class="font14">元</text>
                            </view>
                        </view>
                    </view>
                    <template v-if="next_point && my_position['current_point_details'].name">
                        <view class="pt15">
                            恭喜你{{ id === '3645c9194f8eb9ef0b875e5a770ec4a7' ? '重回' : '已到达' }}
                            {{ my_position['current_point_details'].name }}
                        </view>
                        <view class="p10">
                            <button v-if="my_position['current_point_details'].news_id"
                                    class="bg-primary color-white mb5"
                                    style="line-height: 40px; border-radius: 20px;"
                                    @click="exchangeSuccessLookPointPopup">
                                <template v-if="id === '3645c9194f8eb9ef0b875e5a770ec4a7'">
                                    查看校史大事
                                </template>
                                <template v-else>查看点位知识</template>
                            </button>

                            <!--雷子的客户6160553994bddaddc461002e0068aee5，兑换成功弹窗不显示答题入口-->
                            <template v-if="id !== '6160553994bddaddc461002e0068aee5'">
                                <button v-if="my_position['current_point_details'].exam_id && (!detail.conf.active.answer_need_read || !detail.conf.active.answer_need_read.open)"
                                    class="bg-warning color-white mb5"
                                    style="line-height: 40px; border-radius: 20px;"
                                    @click="toExamDetail(my_position['current_point_details'])">
                                前往答题
                            </button>
                            </template>

                            <button v-if="my_position['current_point_details'].conf &&
                                          my_position['current_point_details'].conf.lottery &&
                                          my_position['current_point_details'].conf.lottery.lottery_id &&
                                          !end_point"
                                    class="bg-error color-white"
                                    style="line-height: 40px; border-radius: 20px;"
                                    @click="toLottery(my_position['current_point_details'])">
                                前往抽奖
                            </button>

                            <!--开启了运动圈功能，到达终点后显示去运动圈打卡。但是开了照片合成的不显示，因为照片合成的不能私自发运动圈-->
                            <button v-if="end_point &&
                                          detail.rank_set &&
                                          detail.rank_set['open_sport_moment'] &&
                                          !detail.rank_set.together_pic"
                                    class="bg-error color-white"
                                    style="line-height: 40px; border-radius: 20px;"
                                    @click="toComment">
                                前往运动圈打卡
                            </button>

                            <button v-if="detail.rank_set.together_pic &&
                                          my_position['current_point_details'].conf &&
                                          my_position['current_point_details'].conf.together_pic &&
                                          my_position['current_point_details'].conf.together_pic.open"
                                    class="bg-error color-white"
                                    style="line-height: 40px; border-radius: 20px;"
                                    @click="toTogetherPic(my_position['current_point_details'].id)">
                                照片合成
                            </button>

                            <button v-if="detail.rank_set &&
                                          detail.rank_set['redpack'] &&
                                          detail.conf.active.redpack_rules &&
                                          detail.conf.active.redpack_rules.open &&
                                          detail.conf.active.redpack_rules.types === 1 &&
                                          my_position['current_point_details'].conf &&
                                          my_position['current_point_details'].conf.redpack_rules &&
                                          my_position['current_point_details'].conf.redpack_rules.open"
                                    class="bg-red color-white"
                                    style="line-height: 40px; border-radius: 20px;"
                                    @click="getRedpack(my_position['current_point_details'].id)">
                                抢红包
                            </button>

                            <button v-if="!detail.conf.active.certificate_close && end_point"
                                    class="bg-light-primary color-white"
                                    style="line-height: 40px; border-radius: 20px;"
                                    @click="lookCertificate">
                                查看证书
                            </button>
                        </view>
                    </template>
                </view>

                <xwy-ad v-if="detail.rank_set.closed_AD !== 1" :ad_type="3"></xwy-ad>
            </view>

            <view class="pt5"></view>
            <xwy-ad v-if="detail.rank_set.closed_AD !== 1" :ad_type="66"></xwy-ad>
            <view v-if="exchange_success_popup_close_icon_show" class="cancel text-center">
                <icon type="cancel" color="#bbbec4" size="28" @click="exchangePopupHide()"/>
            </view>
        </view>

        <uni-popup ref="point_detail" type="center" 
                   @touchmove.stop.prevent="" @maskClick="pointDetailsPopupClose">
            <view class="uni__popup point-detail-popup"
                  :style="{backgroundColor: map_point_set.content.bg_color}">
                <view class="point-detail-title text-center flex-center"
                      :style="{backgroundColor: map_point_set.top_title.line_color,
                               color: map_point_set.top_title.text_color}">
                    <view v-for="(item, index) in map_point_popup_set" :key="index"
                          class="point-detail-popup-tab font14"
                          :style="show_point_detail_type === item.id ? `color: ${map_point_set.top_title.text_actived_color}; border-bottom: 2px solid ${map_point_set.top_title.text_actived_color}` : ''"
                          @click="pointDetailTypeChange(item.id)">{{ item.name }}
                    </view>
                </view>

                <view v-if="show_point_detail_type === 2 && show_article.mp3" 
                      class="point-details-audio-view clear clearfix">
                    <view class="point-details-audio fr pr10" @click="pointArticleAudioPlayChange">
                        <text class="color-light-primary font14" style="padding: 0 2px;">听全文</text>
                        <text :class="'iconfont color-light-primary font14 ' + (pointArticleAudioStatus === 'play' ? 'icon-pause-filled' : 'icon-play-filled')"></text>
                    </view>
                </view>
                
                <scroll-view class="point-detail-content p10 color-content" scroll-y="true">
                    
                    <template v-if="show_point_detail_type === 2">
                        <view v-if="read_reward_count_down !== null" class="flex-kai">
                            <view></view>
                            <view>
                                <text class="iconfont icon-alarm-clock color-disabled"></text>
                                <text class="color-sub" style="padding-left: 2px;">
                                    {{ read_reward_count_down }}
                                </text>
                            </view>
                        </view>
                        
                        <view v-if="show_article.title" class="text-center pb5"
                              :style="{color: map_point_set.content.text_color}">
                            {{ show_article.title }}
                        </view>
                        <!-- <view v-if="show_article.video_url">
                          <video
                            class="video"
                            v-if="show_article.video_type === 'http'"
                            :src="show_article.video_url"
                            style="width: 100%;"
                          ></video>
                          <txv-video
                            v-if="show_article.video_type === 'txv_id'"
                            :vid="show_article.video_url"
                            playerid="playerid"
                            :autoplay="false"
                          ></txv-video>
                        </view> -->
                        <view v-if="show_article.content"
                              :style="{color: map_point_set.content.text_color}">
                            <u-parse :content="show_article.content"/>
                        </view>

                        <template
                            v-if="detail.conf.active.point_news_to_details_page_show || (detail.conf.active.answer_need_read && detail.conf.active.answer_need_read.open)">
                            <view class="text-center" style="padding-top: 50px;">
                                <image src="https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/window/answer.png"
                                       mode="widthFix" style="width: 200px;"/>
                            </view>
                            <view class="flex-center pt15">
                                <view class="bg-light-primary color-white text-center"
                                      style="width: 200px; line-height: 40px; border-radius: 20px;"
                                      @click="pointPopupToReadPointNews">
                                    查看点位知识
                                </view>
                            </view>
                        </template>
                    </template>
                    <template v-if="show_point_detail_type === 3">
                        <view class="text-center" style="padding-top: 50px;">
                            <image src="https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/window/answer.png"
                                   mode="widthFix" style="width: 200px;"/>
                        </view>
                        <view class="flex-center pt15">
                            <view class="bg-warning color-white text-center"
                                  style="width: 200px; line-height: 40px; border-radius: 20px;"
                                  @click="toExamDetail(point_detail)">
                                {{ map_point_popup_button_text.exam }}
                            </view>
                        </view>
                    </template>
                    <template v-if="show_point_detail_type === 4">
                        <view class="text-center" style="padding-top: 50px;">
                            <image src="https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/window/Luckydraw.png"
                                   mode="widthFix" style="width: 200px;"/>
                        </view>
                        <view class="flex-center pt15">
                            <view class="bg-success color-white text-center"
                                  style="width: 200px; line-height: 40px; border-radius: 20px;"
                                  @click="toLottery(point_detail)">
                                {{ map_point_popup_button_text.lottery }}
                            </view>
                        </view>
                    </template>
                    <template v-if="show_point_detail_type === 5">
                        <view class="text-center" style="padding-top: 50px;">
                            <text class="iconfont icon-red-packet color-red" style="font-size: 100px;"></text>
                            <view v-if="redpack_check_error_info && redpack_check_error_info !== 'loading'"
                                  class="color-sub font14 pt15">
                                {{ redpack_check_error_info }}
                            </view>
                            <view v-if="!redpack_check_error_info" class="flex-center pt15">
                                <view class="bg-red color-white text-center"
                                      style="width: 200px; line-height: 40px; border-radius: 20px;"
                                      @click="getRedpack(point_detail.id)">
                                    {{ map_point_popup_button_text.redpack }}
                                </view>
                            </view>
                        </view>
                    </template>

                    <template v-if="show_point_detail_type === 6">
                        <view v-if="point_user_list.loading" class="color-sub font14 text-center">
                            加载中...
                        </view>
                        <view v-if="!point_user_list.loading && point_user_list.total"
                              class="color-sub font14 text-center">
                            共{{ point_user_list.total }}{{ isTeamMap ? '支队伍' : '位用户' }}到达{{ point_detail.name }}
                        </view>
                        <view class="flex-row flex-wrap">
                            <view class="user-list-item text-center"
                                  v-for="(item, index) in point_user_list.list" :key="index">
                                <template>
                                    <image v-if="isTeamMap" mode="aspectFill" 
                                           :src="item.conf && item.conf.logo || ''"/>
                                    <image v-else :src="item.headimg || ''" mode="aspectFill"/>
                                </template>
                                <view class="color-content ellipsis font14">
                                    <template v-if="isTeamMap">{{ item.name || '' }}</template>
                                    <template v-else>{{ item.must_submit[0].value || '' }}</template>
                                </view>
                            </view>
                        </view>
                        <view v-if="!point_user_list.list.length && !point_user_list.loading"
                              class="text-center" style="padding-top: 50px;">
                            <text class="iconfont icon-empty-state color-border"
                                  style="font-size: 100px;"></text>
                            <view class="color-sub">
                                暂无{{ isTeamMap ? '队伍' : '用户' }}到达{{ point_detail.name }}
                            </view>
                        </view>
                    </template>

                    <template v-if="show_point_detail_type === 7">
                        <view class="text-center" style="padding-top: 50px;">
                            <uni-icons type="images" color="#ff9900" size="100"/>
                        </view>
                        <template>
                            <view v-if="point_detail['point_lock_state'] && point_detail['point_lock_state']['have_together_pic']"
                                  class="text-center color-sub font14">点位照片已合成
                            </view>
                            <view v-else class="flex-center pt15">
                                <view class="bg-warning color-white text-center"
                                      style="width: 200px; line-height: 40px; border-radius: 20px;"
                                      @click="toTogetherPic(point_detail.id)">
                                    {{ map_point_popup_button_text.together_pic }}
                                </view>
                            </view>
                        </template>
                    </template>
                </scroll-view>
                <view class="flex-all-center">
                    <view @click="pointDetailsPopupClose">
                        <uni-icons type="close" size="24"
                                   :color="map_point_set.content.text_color"/>
                    </view>
                </view>
            </view>
        </uni-popup>

        <template v-if="tabbar_data">
            <activity-tabbar :value.sync="tabbar_data"></activity-tabbar>
        </template>

        <blind-box-lottery v-if="blindBoxLotteryId" ref="blindBoxLottery"
                           :lottery-id="blindBoxLotteryId" :active-id="id"/>

        <max-daily-step-submit-popup v-if="maxDailyStepSubmitReasonOpen" ref="maxDailyStepSubmitReason"
                                     :step="today_step" :active-id="id"
                                     @close="maxDailyStepSubmitReasonPopupClose"/>

        <expiration-reminder ref="expirationReminder"/>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import base64 from "@/utils/base64"

import activityTabbar from "@/pages/activity/components/activity-tabbar.vue"
import topCumulativeWalkTips from "./components/top-cumulative-walk-tips.vue"
import mapNoExchangeTipsPopup from './components/map-no-exchange-tips-popup.vue'
import maxDailyStepSubmitPopup from '../../components/max-daily-step-submit-popup.vue'
import mileageProgress from './components/mileage-progress.vue'
const windowHeight = uni.getSystemInfoSync().windowHeight

export default {
    components: {
        maxDailyStepSubmitPopup,
        activityTabbar,
        topCumulativeWalkTips,
        mapNoExchangeTipsPopup,
        mileageProgress
    },
    data() {
        return {
            id: '',
            map_height: `${windowHeight}px`,
            bottom_height: 0,
            image_height: 0,
            loading: true,
            today_step: null,
            can_exchange_step: 0,
            detail: {
                conf: {
                    active: {}
                },
                rank_set: {},
            },
            map_set: null,
            my_position: false,
            all_exchange_num: 0,
            all_sign_days: 0,
            top_rank_num: -1,
            exchange_status: '',
            exchange_success_info: "",
            exchange_success_tip_text_list: [],
            show_article: {},
            today_exchange_step: 0,
            is_joining: '',
            next_point: false,
            // 兑换后是否到达终点
            end_point: false,
            // 1显示到达该点位的队伍列表； 2显示点位知识
            show_point_detail_type: -1,
            point_team_list: [],
            point_detail: null,
            who: null,
            // 用户是否到达这个点
            in_point: false,
            redpack_check_error_info: 'loading',
            point_user_list: {
                list: [],
                load_page: 1,
                is_last_page: false,
                loading: false,
                total: 0
            },
            user_name: '',
            headimg: '',
            open_business_kilometers: false,
            exchange_map: {
                can_exchange_step: '可兑换步数',
                today: '今日兑换步数',
                top_rank_num: '排名',
                rate_tips: '兑换比例',
                total_kilo: '累计行走里程',
                total_day: '累计行走天数'
            },

            read_reward_count_down: null,
            scale: 1,

            tabbar_data: null,
            map_point_popup_set: [],
            map_point_popup_button_text: {
                exam: '',
                lottery: '',
                redpack: '',
                together_pic: '',
            },
            map_point_set: {
                content: {
                    bg_color: '#ffffff',
                    text_color: '#495060'
                },
                top_title: {
                    line_color: "#ffffff",
                    text_actived_color: "#e20f04",
                    text_color: "#1c2438"
                }
            },
            exchange_success_popup_close_icon_show: true,

            // 是否是接力赛
            is_relay_race: false,
            current_baton: null,
            team_id: null,
            pointArticleAudioStatus: null,
            enableSatelliteUserSwitch: false,
            enableSatellite: false,

            // 雷子客户定制开发，每天解锁点位的，当前解锁的点位数
            rushed_round: 0,

            // 队伍里程，如果OA开了队伍平均数，那么这里就是平均里程，否则就是总里程
            teamKilo: 0
        }
    },

    computed: {
        need_map: function() {
            const map_types = this.detail?.conf?.active?.map_types || -1
            const need_map_types = [1, 2, 3, 4, 100]
            return need_map_types.includes(map_types)
        },
        
        isTeamMap() {
            return !!this.detail?.rank_set?.['show_team_list']
        },

        top_cumulative_walk_tips_show() {
            const {detail, my_position, all_sign_days, is_relay_race, isTeamMap} = this
            const {map_types, map_page_top_scroll_tips_hide} = detail?.conf?.active || {}
            if (map_page_top_scroll_tips_hide) return false  // 关闭了 不显示
            if (map_types === 101) return false  // 无需地图 不显示
            if (!all_sign_days) return false // 没有累计天数 不显示
            if (!my_position?.['current_point_details']?.name) return false // 没有当前到达点位 不显示
            if (is_relay_race) return false  // 开启接力 不显示
            if (isTeamMap) return false  // 队伍健步走不需要显示个人到达哪里
            
            return true
        },
        
        diyButton() {
            const {map_types, map_page_just_show_diy_exchange_button: show = 0, map_page_diy_exchange_button_set: set = null} = this.detail?.conf?.active || {}
            
            const OA_OPEN = this.detail?.rank_set?.['mapPointDetailsButtonDIY'] === 1

            return {
                show: OA_OPEN && show === 1 && map_types !== 101,
                text: set?.text || '兑换里程',
                style: set ? `color: ${set.text_color}; background-color: ${set.button_color}; width: ${set.width}px; height: ${set.height}px; border-radius: ${set.height / 2}px;` : ''
            }
        },

        blindBoxLotteryId() {
            const OA_open = !!this.detail?.rank_set?.['mystery_box']
            if (!OA_open) return null
            const lottery_id = this.detail?.conf?.active?.blind_box_lottery?.lottery_id
            return lottery_id || null
        },

        maxDailyStepSubmitReasonOpen() {
            if (!this.detail?.rank_set?.['maxDailyStepSubmitReason']) return false
            return !!this.detail?.conf?.active?.max_num
        },

        endPointMileage() {
            const list = this.map_set?.markers?.filter(item => !item.copy_id)
            if (!list?.length) return 0
            return list[list.length - 1].min_num || 0
        },

        mileageProgress() {
            // 雷子定制开发的活动，每天步数解锁点位的，点位没有里程的，用用户当前解锁的点位数和点位总数来计算
            if (this.detail.rank_set?.stepRewardIntegralForMinMax) {
                return {
                    current: this.rushed_round,
                    all: this.map_set?.markers?.length || 0
                }
            }

            // 队伍地图
            if (this.isTeamMap) {
                return {
                    current: this.teamKilo,
                    all: this.endPointMileage
                }
            }

            return {
                current: this.all_exchange_num,
                all: this.endPointMileage
            }
        },

        // 是否显示排名
        topRankShow() {
            // 雷子客户的活动 因为他排行榜配置的只有实际步数排行榜 和今日排行榜，这里显示的排名是总里程的排名所以地图页这里的排名和排行榜是对不上的
            if (this.id === 'b21d2f22e6dc8676d5f45ab96cdf1d57') return false

            // OA关闭了排行榜
            return !this.detail?.rank_set?.closed_top_rank

        }
    },

    onUnload() {
        this.clearReadRewardCountDownInterval()
        this.pointArticleAudioDestroy()
        
        // 停止未兑换弹窗的打字机效果
        this.$refs.noExchangeTipsPopup?.showEnd()
    },

    onLoad(e) {
        e.show_tab && this.$uni.hideHomeButton()
        
        this.createMapContext()
        
        this.$uni.showLoading('数据加载中...')
        this.id = e.id
        this.open_business_kilometers = this.xwy_config.openBusinessKilometers(e.id)
        this.exchange_success_popup_close_icon_show = this.xwy_config.mapPageExchangeSuccessPopupCloseIconShow(e.id)

        login.uniLogin(err => {

            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.who = app.globalData['who']

            this.getMyStep()

            e.show_tab && this.setTabBar()
        })
    },


    methods: {
        createMapContext() {
            this.MapContext = uni.createMapContext('map')  
        },
        
        setTabBar() {
            const tab_bar_data = app.globalData['tempData'].activity_tab_bar_data
            if (tab_bar_data) this.tabbar_data = tab_bar_data
        },


        accSub(num1, num2) {
            return utils.accSub(num1, num2)
        },
        toJoinActivity() {
            const pages = getCurrentPages()
            const up_page = pages[pages.length - 2]
            up_page.$vm.joinActivity()
            uni.navigateBack()
        },

        async getRedpack(point_id) {
            uni.showLoading({
                mask: true
            })

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.userReward/arrive_point_rush_redpack',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id,
                    point_id
                }
            })

            uni.hideLoading()

            if (!res || !res['status']) {
                xwy_api.alert(res && res['info'] || '领取失败')
                return false
            }

            this.exchangePopupHide()
            await this.checkPointRepackStatus(point_id)
            xwy_api.alert(res && res['info'] || '领取成功')
        },

        lookCertificate() {
            this.$uni.navigateTo(`/pages/activity/other/certificate?id=${this.id}&finished_time=${this.user_details?.finished_time || 0}`)
        },

        async checkPointRepackStatus(point_id) {
            this.redpack_check_error_info = 'loading'
            uni.showLoading({
                mask: true
            })
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.userReward/check_point_rush_redpack_amount',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id,
                    point_id
                }
            })
            uni.hideLoading()
            if (!res || !res['status']) {
                this.redpack_check_error_info = res && res['info'] || '无法抢红包'
                return false
            }

            this.redpack_check_error_info = ''
        },

        pointDetailTypeChange(type) {
            if (type === this.show_point_detail_type) return false
            this.pointArticleAudioPause()
            this.clearReadRewardCountDownInterval()
            this.show_point_detail_type = type
            const types = {
                2: () => this.getArticleDetail(this.point_detail['page_id']),
                5: () => this.checkPointRepackStatus(this.point_detail.id),
                6: () => this.showPointUserList(this.point_detail.id)
            }
            types[type] && types[type]()
        },

        pointDetailsPopupClose() {
            this.pointArticleAudioPause()
            this.clearReadRewardCountDownInterval()
            this.uniPopupClose('point_detail')
        },

        showPointUserList(point_id) {
            this.point_user_list.load_page = 1
            this.point_user_list.point_id = point_id

            this.getPointUserList()
            this.$refs.point_detail.open()
        },

        async getPointUserList() {
            if (this.point_user_list.load_page === 1) {
                this.point_user_list.list = []
                this.point_user_list.is_last_page = false
                this.point_user_list.total = 0
            }
            this.point_user_list.loading = true

            const data = {
                active_id: this.id,
                point_id: this.point_user_list.point_id,
                page: this.point_user_list.load_page,
                perpage: 30
            }
            if (this.isTeamMap) data.is_team = 1

            // 晓阳客户的活动，最多显示400个
            if (this.id === '8c5f14ca8bd4e9e41771d04fee26ea1f') data.perpage = 400

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.mapPoint/point_user_list',
                data
            })

            this.point_user_list.loading = false

            const res_data = res?.['data']?.user_list
            if (!res_data) {
                this.point_user_list.is_last_page = true
                return false
            }

            this.point_user_list.is_last_page = res_data.is_lastpage
            this.point_user_list.total = res_data.total
            const list = res_data.data || []
            this.point_user_list.list = [...this.point_user_list.list, ...list]
        },

        regionChange(e) {
            const {type, causedBy, detail} = e || {}
            
            // 监听地图缩放，如果缩放值小于设置值，点位信息不在地图上显示，地图上只显示路线
            if (type !== 'end' || causedBy !== 'scale') return
            const scale = detail?.scale
            if (!scale) return
            
            const scale_set = this.detail?.conf?.active?.hide_point_scale
            if (!scale_set) return
            const show = scale >= scale_set
            this.map_set?.markers?.forEach(v => {
                if (!v.conf?.map_hide_point) {
                    if (v.callout) v.callout.display = show ? 'ALWAYS' : 'BYCLICK'
                    this.$set(v, 'alpha', show ? 1 : 0)
                }
            })
        },

        markerTap(e) {
            if (e.detail.markerId === 999999999) {
                // 如果是复制的区间标记点位，点位id等于当前到达点位id
                e.detail.markerId = this.my_position.current_point_details.id
            }
            this.calloutTap(e)
        },

        calloutTap(e) {
            this.exchangePopupHide()
            this.show_point_detail_type = -1
            this.in_point = false
            const markerId = e.markerId || e.detail.markerId // 微信小程序是e.markerId h5是e.detail.markerId
            const point_detail = this.map_set?.markers?.find(v => v.id === markerId)
            if (!point_detail) return false
            if (point_detail.conf?.map_hide_point) return  // 隐藏点位不弹窗
            this.pointArticleAudioStatus = null

            // 是否已经到达该点位
            let my_position_point_id = false
            if (this.my_position?.['current_point_details']) {
                const point_id = this.my_position['current_point_details'].point_id
                if (point_id || point_id === 0) my_position_point_id = point_id
            }
            const in_point = my_position_point_id !== false && my_position_point_id >= point_detail.point_id;

            this.in_point = in_point

            // 活动是否配置了需要达到点位才能查看点位知识
            const must_in_point = this.detail && this.detail.rank_set && this.detail.rank_set.point_knowledge_lock && this.detail.rank_set.point_knowledge_lock === 1


            if (must_in_point && !in_point) {
                return uni.showModal({
                    title: '提示',
                    content: '需要到达该点位才能查看哦',
                    showCancel: false
                })
            }

            this.point_detail = null
            this.point_detail = point_detail

            if (!this.map_point_popup_set_all?.length) return
            const map_point_popup_set_all = this.map_point_popup_set_all
            const map_point_popup_set = []
            const conf = point_detail.conf || {}
            map_point_popup_set_all.forEach(v => {
                if (v.id === 1 && point_detail['record_id']) {
                    v.optionFunction = () => this.getPointTeamList(point_detail['record_id'])
                    map_point_popup_set.push(v)
                }
                if (v.id === 2 && point_detail['page_id']) {
                    v.optionFunction = () => this.getArticleDetail(point_detail['page_id'])
                    map_point_popup_set.push(v)
                }
                if (v.id === 6) {
                    v.optionFunction = () => this.showPointUserList(point_detail.id)
                    map_point_popup_set.push(v)
                }
                // 答题、抽奖、红包、照片合成需要到达点位才能显示
                if (in_point) {
                    if (v.id === 3 && point_detail.exam_id) {
                        // 开启了需要阅读点位知识才能答题，弹窗不显示答题
                        if (!this.detail.conf.active.answer_need_read?.open) map_point_popup_set.push(v)
                    }
                    if (v.id === 4 && conf.lottery?.lottery_id && conf.lottery.must_exam !== 1) {
                        map_point_popup_set.push(v)
                    }
                    if (v.id === 5 && conf.redpack_rules?.open && conf.redpack_rules?.types === 1) {
                        this.checkPointRepackStatus(point_detail.id)
                        map_point_popup_set.push(v)
                    }
                    if (v.id === 7 && conf.together_pic?.open) map_point_popup_set.push(v)
                }
            })
            this.map_point_popup_set = map_point_popup_set
            if (!map_point_popup_set.length) return
            this.show_point_detail_type = map_point_popup_set[0].id
            this.$refs.point_detail.open()
            map_point_popup_set[0].optionFunction?.()
        },

        // 超过每日兑换步数上限后，系统会自动弹出提示框，参与职工可简要说明具体情况，可选择上传照片加以佐证
        maxDailyStepSubmitReasonShow() {
            if (!this.maxDailyStepSubmitReasonOpen) return false

            const step_max = Number(this.detail.conf.active.max_num)
            const today_step = this.today_step

            return today_step > step_max
        },


        // 超出上限后兑换，并且今天没提交过审核的，弹出步数补交弹窗
        async maxDailyStepSubmitReasonAgainShow() {
            if (!this.maxDailyStepSubmitReasonShow()) return false


            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exchange.max_step.submitReason/reason_list',
                data: {
                    myself: 1,
                    active_id: this.id,
                    page: 1,
                    perpage: 1
                }
            })

            const list = res?.data?.list?.data
            if (!list?.length) return true

            // 今天提交过，不弹
            return list[0].create_time.split(' ')[0] !== this._utils.getDay(0, true)
        },


        maxDailyStepSubmitReasonPopupClose() {
            this.$refs.maxDailyStepSubmitReason.close()
        },

        exchangePopupHide() {
            //（修复关闭点位知识弹窗后会弹出步数补交弹窗）
            // 用来判断是关闭兑换成功的弹窗，兑换成功后才能弹出步数补交，点位里面的关闭不用
            const exchange_success = !!this.exchange_success_info

            this.exchange_success_info = ''
            this.exchange_success_tip_text_list = []

            if (exchange_success && this.maxDailyStepSubmitReasonShow()) {
                this.$refs.maxDailyStepSubmitReason.open()
            }
        },

        getArticleDetail(id) {
            if (!id) return false

            // 设置了必须进入到文章详情才能查看点位知识介绍
            if (this.detail.conf.active.point_news_to_details_page_show) return
            // 设置了必须要阅读点位知识才能答题，需要进入到文章详情页面阅读并答题
            if (this.detail.conf.active.answer_need_read?.open) return

            this.show_article = {}

            this.$uni.showLoading('加载中...')

            const errModal = (content = '文章详情获取失败') => {
                uni.hideLoading()
                uni.showModal({
                    title: '提示',
                    content,
                    showCancel: false
                })
            }

            xwy_api.ajax({
                url: "front.news/news_details",
                data: {
                    access_token: app.globalData['access_token'],
                    news_id: id
                },
                success: res => {
                    console.log(res)
                    uni.hideLoading()

                    if (!res.status) {
                        errModal(res.info)
                        return false
                    }

                    const detail = res.data['news_details']
                    if (detail.thumb_pic) this.logo = detail.thumb_pic

                    /*if (detail.video_url) {
                        let video_type = 'txv_id'
                        if (detail.video_url.startsWith('http://') || detail.video_url.startsWith('https://')) {
                            video_type = 'http'
                        }
                        detail.video_type = video_type
                    }*/


                    if (detail.content) {
                        detail.content = utils.newsContentInit(detail.content)
                    }

                    this.show_article = detail
                    this.$refs.point_detail.open()


                    this.setReadRewardCountDown()
                    this.setArticleMusic(detail?.mp3)
                }
            })
        },

        setArticleMusic(src) {
            if (!src) return
            this.pointArticleAudioStatus = null
            this.pointArticleAudioContext ||= uni.createInnerAudioContext()
            // 不加时间戳的话，如果音频地址播放过，不会触发onCanplay，导致无法播放
            this.pointArticleAudioContext.src = `${src}?t=${new Date().getTime()}`

            this.pointArticleAudioContextOn()
        },
        
        pointArticleAudioContextOn() {
            this.pointArticleAudioContext.onCanplay(() => {
                console.log('文章音频 可以播放')
                this.pointArticleAudioStatus = 'canplay'
            })
            this.pointArticleAudioContext.onPlay(() => {
                this.pointArticleAudioStatus = 'play'
                console.log('文章音频 开始播放')
            })
            this.pointArticleAudioContext.onError((res) => {
                this.pointArticleAudioStatus = 'error'
                console.log('文章音频 错误: ', res)
            })
            this.pointArticleAudioContext.onStop(() => {
                this.pointArticleAudioStatus = 'stop'
                console.log('文章音频 停止播放')
            })
            this.pointArticleAudioContext.onPause(() => {
                this.pointArticleAudioStatus = 'pause'
                console.log('文章音频 暂停播放')
            })
        },

        pointArticleAudioPlayChange() {
            const audio = this.pointArticleAudioContext
            const status = this.pointArticleAudioStatus
            if (!audio || !status) return this.$uni.showToast('音频加载中，请稍后再试')
            if (status === 'error') return this.$uni.showToast('音频加载失败，无法播放')
            audio.paused ? audio.play() : audio.pause()
        },

        pointArticleAudioPause() {
            // 不能使用stop()，不知道什么原因，stop()后会触发onCanplay，导致音频重新播放
            this.pointArticleAudioContext?.pause()
        },

        pointArticleAudioDestroy() {
            if (!this.pointArticleAudioContext) return
            this.pointArticleAudioPause()
            this.pointArticleAudioContext.destroy()
            this.pointArticleAudioContext = null
        },

        setReadRewardCountDown() {
            if (!this.detail.rank_set?.['reading_reward']) return
            if (!this.show_article?.category_id) return
            const set =  this.detail.conf.active.reading_reward
            if (!set?.open) return
            if (!set?.rules?.length) return
            const target_set = set.rules.find(v => v.category_id === this.show_article.category_id)
            if (!target_set) return


            this.clearReadRewardCountDownInterval()

            this.time_to_start_reading = Math.floor(new Date().getTime() / 1000) // 记录开始阅读的时间
            let seconds = 0
            this.readRewardCountDownInterval = setInterval(() => {
                seconds++
                this.read_reward_count_down = seconds
                if (seconds === target_set.seconds) {
                    this.getRewards()
                    // this.clearReadRewardCountDownInterval()
                }
            }, 1000)
        },

        clearReadRewardCountDownInterval() {
            if (this.readRewardCountDownInterval) {
                clearInterval(this.readRewardCountDownInterval)
                this.readRewardCountDownInterval = null
                this.read_reward_count_down = null
            }
        },

        async getRewards() {
            if (this.point_detail?.['point_lock_state']?.['have_reading']) return
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.integral.reading/reading_reward_active_integral ',
                data: {
                    news_id: this.show_article.news_id,
                    sign: utils.randomCoding() + base64['encode'](`${this.id}+${this.time_to_start_reading}`),
                }
            })
            if (res?.status === 1) this.getMapSet(() => {})
        },

        // 兑换成功弹窗点击查看点位知识介绍
        exchangeSuccessLookPointPopup() {
            const pointDetails = this.my_position['current_point_details']
            if (this.detail.conf.active.point_news_to_details_page_show) {
                return this.toReadPointNews(pointDetails)
            }
            this.calloutTap({markerId: pointDetails.id})
        },

        pointPopupToReadPointNews() {
            this.toReadPointNews(this.point_detail)
        },

        toReadPointNews(pointDetails) {
            const {id: point_id, news_id, exam_id} = pointDetails
            let url = `/pages/news/preview?id=${news_id}&active_id=${this.id}`

            if (this.detail.conf.active.answer_need_read?.open && exam_id) {
                let seconds = Number(this.detail.conf.active.answer_need_read.seconds)
                if (isNaN(seconds)) seconds = 0
                url += `&answer_need_read=1&point_id=${point_id}&exam_id=${exam_id}&answer_need_read_seconds=${seconds}`
            }

            this.$uni.navigateTo(url, {
                events: {
                    success: () => this.getOpenerEventChannel?.()?.emit?.('updateUserDetail')
                }
            })
        },

        toExamDetail(detail) {
            this.exchangePopupHide()
            if (this.is_joining === 'false') return this.noJoinTipsModal('未参与活动，无法答题。')

            this.$uni.navigateTo(`/pages/likou_dati/pages/exam/exam_details/exam_details?exam_id=${detail.exam_id}&active_id=${this.id}&point_id=${detail.id}`, {
                events: {
                    success: () => this.getMapSet()
                }
            })
            
            
            // 雷子客户的活动，从答题成绩页点返回到地图页面，要隐藏点位知识弹窗
            if (this.id === '79e0836e3dad59acaa76130fd08f57ab') this.uniPopupClose('point_detail')
        },

        toLottery(detail) {
            this.exchangePopupHide()

            this.$uni.navigateTo('/pages/lottery/user/lottery?id=' + detail.conf.lottery.lottery_id + '&active_id=' + this.id + '&point_id=' + detail.id)
        },

        toTogetherPic(point_id) {
            uni.navigateTo({
                url: `/pages/activity/together-pic/make-pictures?id=${this.id}&point_id=${point_id}`,
                events: {
                    refresh: () => {
                        // 发布成功要关闭弹窗，不然弹窗里面的点位数据没有刷新，获取不到用户是否合成过照片
                        this.pointDetailsPopupClose()
                        this.exchangePopupHide()
                        this.getMapSet(() => {})
                    }
                }
            })
        },

        toComment() {
            this.exchangePopupHide()
            this.$uni.navigateTo('/pages/comment/list?active_id=' + this.id)
        },

        uniPopupClose(ref) {
            this.$refs[ref].close()
        },
        getMyStep(update = false) {
            //#ifdef H5
            const flat = true
            if (flat) return this.getActivityDetail()
            //#endif


            // PC不获取步数（无法获取步数）
            const platform = uni.getSystemInfoSync().platform
            if (platform && (platform === 'mac' || platform === 'windows' || platform === 'linux'))
                return this.getActivityDetail()


            if (app.globalData['today_step'] && !update) {
                this.today_step = app.globalData['today_step'];
                return this.getActivityDetail()
            }

            this.$uni.showLoading(update ? '步数更新中...' : '数据加载中...')

            xwy_api.getWeRunData(res => {
                uni.hideLoading()
                this.today_step = null
                if (res?.data?.['crypt_data']?.['stepInfoList']?.length) {
                    const step_list = res.data['crypt_data']['stepInfoList']
                    this.today_step = step_list[step_list.length - 1].step
                }

                if (res === 'err') {
                    uni.showModal({
                        title: '步数获取失败',
                        content: '请确保已开启微信运动并在小程序内授权',
                        showCancel: false
                    })
                }

                this.getActivityDetail()
            })


        },


        getActivityDetail() {
            xwy_api.getActivityDetail(this.id, async res => {
                if (res.data?.['active_details']) {
                    this.detail = res.data['active_details']
                    if (this.detail.conf?.active?.runOneByOne?.open) this.is_relay_race = true

                    /* // 无需地图不需要获取点位列表
                    if (detail.conf?.active?.map_types && detail.conf.active.map_types === 101) {
                      this.getUserActivityData()
                      return false;
                    } */
                    
                    this.setEnableSatellite()
                    await this.getMapSet()
                } else {
                    this.loading = false
                    uni.hideLoading()
                    this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                }


                this.getExchangeMapSet(res)
                this.getMapPointPopupSet(res)
                this.getMapPointPopupColorSet(res)
            })
        },


        setEnableSatellite() {
            this.$nextTick(() => {
                const {map_types, satellite} = this.detail?.conf?.active || {}
                if (!map_types || map_types === 101 || map_types === 102 || !satellite) return

                if (satellite === 1) {
                    this.enableSatellite = true
                    return
                }
                
                this.enableSatelliteUserSwitch = true
                this.enableSatellite = uni.getStorageSync(`map_enable_satellite_${this.id}`) || false
            })
        },

        enableSatelliteSwitch() {
            this.enableSatellite = !this.enableSatellite
            const storageKey = `map_enable_satellite_${this.id}`
            this.enableSatellite ? uni.setStorageSync(storageKey, true) : uni.removeStorageSync(storageKey)
        },
        

        getExchangeMapSet(res) {
            const exchange_map = res?.data?.active_more_data?.['active_conf_set']?.exchange_map
            if (exchange_map) {
                if (exchange_map.can_exchange_step) this.exchange_map.can_exchange_step = exchange_map.can_exchange_step
                if (exchange_map.today) this.exchange_map.today = exchange_map.today
                if (exchange_map.top_rank_num) this.exchange_map.top_rank_num = exchange_map.top_rank_num
                if (exchange_map.rate_tips) this.exchange_map.rate_tips = exchange_map.rate_tips
                if (exchange_map.total_kilo) this.exchange_map.total_kilo = exchange_map.total_kilo
                if (exchange_map.total_day) this.exchange_map.total_day = exchange_map.total_day
            }
        },

        getMapPointPopupSet(res) {
            const default_map_point_popup_set = [
                {
                    "id": 2,
                    "name": "点位知识"
                },
                {
                    "id": 3,
                    "name": "答题",
                    "button_text": "答题"
                },
                {
                    "id": 4,
                    "name": "抽奖",
                    "button_text": "抽奖"
                },
                {
                    "id": 5,
                    "name": "抢红包",
                    "button_text": "抢红包"
                },
                {
                    "id": 6,
                    "name": this.isTeamMap ? "队伍列表" : "用户列表"
                },
                {
                    "id": 7,
                    "name": "照片合成",
                    "button_text": "照片合成"
                }

            ]
            let map_point_popup_set = res?.data?.active_more_data?.['active_conf_set']?.['map_point_popup_set']
            map_point_popup_set ||= default_map_point_popup_set

            const map_point_popup_set_all = []
            map_point_popup_set.forEach(v => {
                if (v.id === 2) map_point_popup_set_all.push(v)
                if (v.id === 3 && this.detail.rank_set?.exam_open) {
                    this.map_point_popup_button_text.exam = v.button_text || v.name || '去答题'
                    map_point_popup_set_all.push(v)
                }
                if (v.id === 4 && this.detail.rank_set?.['lottery_open']) {
                    this.map_point_popup_button_text.lottery = v.button_text || v.name || '去抽奖'
                    map_point_popup_set_all.push(v)
                }
                if (v.id === 5 && this.detail.rank_set?.['redpack'] && this.detail.conf.active.redpack_rules?.open && this.detail.conf.active.redpack_rules?.types === 1) {
                    this.map_point_popup_button_text.redpack = v.button_text || v.name || '抢红包'
                    map_point_popup_set_all.push(v)
                }
                if (v.id === 6) map_point_popup_set_all.push(v)
                if (v.id === 7 && this.detail.rank_set?.['together_pic']) {
                    this.map_point_popup_button_text.together_pic = v.button_text || v.name || '照片合成'
                    map_point_popup_set_all.push(v)
                }
            })

            this.map_point_popup_set_all = map_point_popup_set_all
        },

        getMapPointPopupColorSet(res) {
            const {content = {}, top_title = {}} = res?.data?.active_more_data?.['active_conf_set']?.map_point_set || {}
            if (content.bg_color) this.map_point_set.content.bg_color = content.bg_color
            if (content.text_color) this.map_point_set.content.text_color = content.text_color
            if (top_title.line_color) this.map_point_set.top_title.line_color = top_title.line_color
            if (top_title.text_actived_color)
                this.map_point_set.top_title.text_actived_color = top_title.text_actived_color
            if (top_title.text_color) this.map_point_set.top_title.text_color = top_title.text_color
        },


        showNoExchangeTipsPopup() {
            const {open, text} = this.detail?.conf?.active?.map_page_no_exchange_tips || {}
            if (!open || !text) return
            
            this.$nextTick(() => this.$refs.noExchangeTipsPopup.open())
        },

        getMyPosition(data) {
            const daily_rushed_round_num = data.active_details?.conf?.active?.integral?.exchange_rules?.rushing_set?.daily_rushed_round_num

            if (!daily_rushed_round_num) return data.my_position || false

            // 记住起点不计算在内
            const rushed_round = data.attend_details?.['rushed_round'] || 0
            return {
                current_point_details: data.map_set?.markers?.[rushed_round] || {}
            }
        },

        async getMapSet(cb = () => {}) {
            const data = {
                active_id: this.id
            }
            if (this.detail?.rank_set?.['show_team_list']) data.is_team = 1

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_map_set',
                data
            })

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '地图点位获取失败')

            const map_set = res?.data?.map_set || {},
                attend_details = res?.data?.attend_details || false,
                my_position = this.getMyPosition(res.data),
                today_exchange_step = res?.data?.today_exchange_step || 0


            if (this.detail.conf.active.map_types !== 101 && !map_set) {
                return this.$uni.showModal(res.info || '地图点位获取失败')
            }

            // 队伍里程
            const team = attend_details?.team_details
            if (team?.id) {
                this.teamKilo = this.detail?.rank_set?.team_average ? team.average_kilo : team.all_kilo
            }


            // 如果有地图设置数据则不用移动地图中心点，只有刚进入地图才移动地图中心点
            if (!this.map_set && map_set.longitude && map_set.longitude) {
                this.MapContext.moveToLocation({
                    longitude: map_set.longitude,
                    latitude: map_set.latitude
                })
            }

            if (this.detail.conf.active.polyline_hide) map_set.polyline = []

            if (res.data['total_num']) this.top_rank_num = res?.data?.['total_num'] || 0

            this.getUserActivityData(attend_details, my_position, today_exchange_step, cb)

            if (res.data.attend_details) {
                this.user_details = res.data.attend_details
                if (res.data.attend_details?.must_submit?.[0]?.value) {
                    this.user_name = res.data.attend_details.must_submit[0].value
                }
                if (res.data.attend_details?.headimg) {
                    this.headimg = res.data.attend_details.headimg
                }

                if (!res.data.attend_details?.exchange_step) this.showNoExchangeTipsPopup()

                this.getRelayRaceCurrentBaton()
            } else {
                this.showNoExchangeTipsPopup()
            }


            const new_map_set = this.betweenTwoPointsCompute(map_set, my_position)

            if (new_map_set.markers) {
                // 过滤隐藏不显示的点位
                // new_map_set.markers = new_map_set.markers.filter(v => !v.conf?.map_hide_point)

                // 隐藏的点位不显示标记点
                new_map_set.markers.forEach(v => {
                    if (v.conf?.map_hide_point) {
                        v.iconPath = 'http://www.xinweiyun.com/weixin/editor/pics/long_march/dot-null.png'
                        v.width = 1
                        v.height = 1
                        v.alpha = 0
                        v.callout = {
                            content: '',
                            padding: 0,
                            display: 'BYCLICK'
                        }
                    }
                })
            }


            const user_point_map_center = res?.data?.active_details?.conf?.active?.user_point_map_center

            // 如果有地图设置数据则不用移动地图中心点，只有刚进入地图才移动地图中心点
            if (!this.map_set && my_position['current_point_details'] && user_point_map_center) {
                // 开启了以用户达到的点位为地图中心点
                const {latitude, longitude} = my_position['current_point_details'] || {}
                if (latitude && longitude) {
                    this.MapContext.moveToLocation({
                        longitude: longitude,
                        latitude: latitude
                    })
                }
            }

            this.map_set = new_map_set
        },

        // 显示用户在地图两个点位之间的位置计算方法
        betweenTwoPointsCompute(map_set, my_position) {
            if (!this.detail?.conf?.active?.map_between_two_points) return map_set // 没有开启此功能

            // 隐藏不显示点位连线，无法计算用户在两个点之间的距离
            if (this.detail?.conf?.active?.polyline_hide) return map_set
            
            const markers = map_set?.markers  // 标记数组
            if (!markers?.length) return map_set

            const nowP_id = my_position?.['current_point_details']?.id // 当前点位id
            if (!nowP_id) return map_set

            const nowP_index = markers.findIndex(v => v.id === nowP_id)  // 当前点位在markers的下标
            if (nowP_index === -1 || nowP_index === markers.length - 1) return map_set

            const nowP = markers[nowP_index]  // 当前点位
            if (nowP.interval_end) return map_set// 达到区间终点点位，区间终点点位到下一点位是不连线的，所以不用计算

            const nextP = markers[markers.findIndex(v => v.id === nowP.id) + 1] // 下一个点位
            const all_exchange_num = this.all_exchange_num || 0  // 总里程
            const point_m = all_exchange_num - nowP.min   // 从当前点位开始走了多少里程
            const point_all_m = nextP.min - nowP.min        // 当前点位到下一点位的里程数
            const point_m_percent = point_m / point_all_m   // 两个点位之间走了多少



            // 计算两个坐标点的区间经纬度
            const coordinateCalculation = (lat1, lng1, lat2, lng2, percent) => {
                const deltaLng = lng2 - lng1
                const deltaLat = lat2 - lat1
                const quarterDeltaLng = deltaLng * percent
                const quarterDeltaLat = deltaLat * percent
                const quarterLng = lng1 + quarterDeltaLng
                const quarterLat = lat1 + quarterDeltaLat
                return {
                    latitude: quarterLat,
                    longitude: quarterLng
                }
            }

            // 计算两个坐标点的区间经纬度
            const percent_location = coordinateCalculation(nowP.latitude, nowP.longitude, nextP.latitude, nextP.longitude, point_m_percent)

            const push_marker = JSON.parse(JSON.stringify(nowP))  // 复制一份当前点位给计算出来的点位

            // 更改复制出来的点位的坐标
            push_marker.lat = percent_location.latitude
            push_marker.latitude = percent_location.latitude
            push_marker.lng = percent_location.longitude
            push_marker.longitude = percent_location.longitude
            
            // 如果达到点位是隐藏点位，需要把计算出来的坐标的隐藏点位属性改为不隐藏
            if (push_marker.conf?.map_hide_point) push_marker.conf.map_hide_point = 0
            
            // 晓阳客户的活动，计算出来的两个点之间的位置标记点，标记点图片显示特定图片
            if (this.id === 'efa5e7552620fbbd5203aef470d20b49') {
                push_marker.iconPath = 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/longmarch/map20240424sdgs/people.png'
                push_marker.width = 40
                push_marker.height = 40
            }

            // 修改id，因为markersid重复会出问题
            push_marker.copy_id = push_marker.id
            push_marker.id = 999999999
            
            if (push_marker.callout) delete push_marker.callout  // 不用显示点位名称

            markers.push(push_marker)

            // 修改当前坐标的iconPath信息
            if (nowP_index !== 0) {
                // 一般用上一个点的iconPath信息，因为下一个点还没到，
                // 有的活动已走过的点位和没有到的点位iconPath图片是不一样的
                const preP = markers[nowP_index - 1]
                nowP.iconPath = preP.iconPath
                nowP.width = preP.width
                nowP.height = preP.height
            } else {
                // 没有上一点位才用下一点位的iconPath信息
                nowP.iconPath = nextP.iconPath
                nowP.width = nextP.width
                nowP.height = nextP.height
            }


            // 添加一条踩红线，覆盖原来灰色的线
            const polyline = map_set.polyline
            const line_color = polyline[0].color || '#FF0000'
            const line_width = polyline[0].width || 5
            polyline.push({
                color: line_color,
                width: line_width,
                points: [{latitude: nowP.latitude, longitude: nowP.longitude}, percent_location]
            })
            
            return map_set
        },



        // 获取当前接力赛接力到第几棒
        getRelayRaceCurrentBaton() {
            if (!this.user_details?.team_id || !this.is_relay_race) return
            this.team_id = this.user_details.team_id
            this.xwy_api.ajax({
                url: 'front.flat.sport_step.stepRunOneByOne/team_sort_user_list',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id,
                    team_id: this.user_details.team_id,
                    get_current_num: 1
                },
                success: res => {
                    if (res?.data?.current_num) this.current_baton = res.data.current_num
                }
            })
        },

        getUserActivityData(attend_details, my_position, today_exchange_step, cb) {

            this.loading = false
            uni.hideLoading()

            if (attend_details) this.attend_details = attend_details

            // 先吧兑换成功到达下一点位的标识重置
            this.next_point = false

            if (my_position) {
                if (cb && my_position['current_point_details']?.id) {
                    if (
                        !this.my_point_id ||
                        this.my_point_id !== my_position['current_point_details'].id
                    ) this.next_point = true
                }

                this.my_position = my_position
                this.my_point_id = my_position['current_point_details']?.id || null
            }

            this.today_exchange_step = today_exchange_step
            let can_exchange_step = this.today_step - today_exchange_step
            if (!can_exchange_step || can_exchange_step < 0) can_exchange_step = 0
            this.can_exchange_step = can_exchange_step

            if (this.is_relay_race) {
                // 接力赛显示队伍的总里程和累计兑换天数
                if (attend_details?.['team_details']?.all_kilo) {
                    this.all_exchange_num = attend_details['team_details'].all_kilo
                }
                if (attend_details?.['team_details']?.sign_day) {
                    this.all_sign_days = attend_details['team_details'].sign_day
                }
            } else {
                if (attend_details.exchange_kilo) this.all_exchange_num = attend_details.exchange_kilo
                if (attend_details['sign_day']) this.all_sign_days = attend_details['sign_day']
            }

            this.rushed_round = attend_details.rushed_round || 0

            this.exchangeWhether()
            cb && cb()
        },


        setMapHeight() {
            if (this.diyButton.show) return
            setTimeout(() => {
                const query = uni.createSelectorQuery().in(this)
                query.select('.detail').boundingClientRect(data => {
                    const tab_height = this.tabbar_data ? 70 : 0
                    this.map_height = windowHeight - data['height'] - tab_height + 20
                    this.bottom_height = data['height']
                }).exec()
            }, 300)
        },


        exchangeWhether() {
            // 判断是否可以正常兑换 normal正常可兑换、not_begin未开始、ended已结束
            let exchange_status = 'normal'
            const now_time = new Date().getTime()
            if (now_time < this.detail.begin_time * 1000) exchange_status = 'not_begin'
            if (now_time > this.detail.end_time * 1000) exchange_status = 'ended'
            if (!this.can_exchange_step || this.can_exchange_step <= 0) exchange_status = 'not_step'
            let is_joining = 'false'
            if (this.attend_details?.id) {
                is_joining = 'true'
                if (this.attend_details.checked === 0 && this.detail.conf.active.enter_types === 3) {
                    // 未审核
                    exchange_status = 'no_status'
                }
            }


            this.exchange_status = exchange_status
            this.is_joining = is_joining

            this.setMapHeight()

            return exchange_status
        },

        imageLoad(e) {
            const {width} = e.detail
            let {windowWidth} = uni.getSystemInfoSync()
            // #ifdef H5
            if (windowWidth > 500) windowWidth = 500
            // #endif
            const scale = width / windowWidth
            this.scale = scale

            const point_details = this.my_position?.['current_point_details']
            if (!point_details) return
            let top = point_details.margin_top
            const scroll_position = point_details.conf?.point_img_detail?.scroll_position
            if (scroll_position || scroll_position === 0) top = scroll_position
            if (!top) return


            const scrollTop = top / scale

            // 不延迟的话，无法滚动，估计是图片还没在页面上渲染完毕，高度还没计算出来
            const timeout = setTimeout(() => {
                this.$nextTick(() => {
                    uni.pageScrollTo({
                        scrollTop,
                        complete() {
                            clearTimeout(timeout)
                        }
                    })
                })
            }, 500)
        },

        clickImage(e) {
            const point_id = this.clickImgPoint(e.detail)

            if (point_id || point_id === 0) {
                this.calloutTap({markerId: point_id})
                return false
            }
            this.previewImage(this.my_position['current_point_details'].map_pic)
        },


        clickImgPoint(detail) {
            if (!detail || !detail.x || !detail.y || !this.map_set?.markers?.length) return false

            const markers = this.map_set.markers

            for (let i = 0; i < markers.length; i++) {
                const item = markers[i]
                if (
                    item.margin_top !== '' &&
                    item.conf?.point_img_detail &&
                    item.conf.point_img_detail.margin_left !== '' &&
                    item.conf.point_img_detail.point_height !== '' &&
                    item.conf.point_img_detail.point_width !== ''
                ) {
                    const top = item.margin_top,
                        left = item.conf.point_img_detail.margin_left,
                        width = item.conf.point_img_detail.point_width,
                        height = item.conf.point_img_detail.point_height
                    if (this.isInPoint(detail.x, detail.y, top, left, width, height)) {
                        return item.id
                    }
                }
            }
            return false
        },

        isInPoint(x, y, top, left, width, height) {
            x *= this.scale
            y *= this.scale
            return x >= left && x <= left + width && y >= top && y <= top + height
        },

        previewImage(url) {
            uni.previewImage({
                urls: [url]
            })
        },

        toTopList() {
            uni.navigateTo({
                url: `/pages/activity/user/ranking_list?id=${this.id}&user_team_id=${this.attend_details?.team_id || 0}`
            })
        },
        
        noJoinTipsModal(tips) {
            this.$uni.showModal(tips, {
                showCancel: true,
                confirmText: '参与活动',
                success: res => res.confirm && this.toJoinActivity()
            })
        },

        clickExchange() {
            if (this.loading) return

            const exchange_status = this.exchangeWhether()
            
            if (this.is_joining === 'false') return this.noJoinTipsModal('未参与活动，无法兑换。')
            
            if (exchange_status === 'not_begin') {
                uni.showToast({
                    title: '活动还未开始，请在活动开始后再来兑换吧',
                    icon: 'none'
                })
                this.loading = false
                return false
            }
            if (exchange_status === 'ended') {
                uni.showToast({
                    title: '活动已结束，无法继续兑换',
                    icon: 'none'
                })
                this.loading = false
                return false
            }
            if (exchange_status === 'not_step') {
                uni.showToast({
                    title: '步数不够，无法兑换，请多走两步再来试试吧',
                    icon: 'none'
                })
                this.loading = false
                return false
            }
            if (exchange_status === 'no_status') {
                uni.showToast({
                    title: '需要管理员审核通过后才能兑换',
                    icon: 'none'
                })
                this.loading = false
                return false
            }

            let check = true
            const team_required = this.detail.conf.active.team_required
            const user_team = this.user_details?.team_id
            const must_submit = this.detail.conf.must_submit
            if (must_submit?.length) {
                must_submit.forEach(v => {
                    if (this.user_details?.must_submit?.length) {
                        this.user_details.must_submit.forEach(user => {
                            if (user.name === v.name && user.value) v.value = user.value
                        })
                    }
                })
                for (let i = 0; i < must_submit.length; i++) {
                    const item = must_submit[i]
                    if (item.rules === 1 && !item.value) {
                        check = false
                        break
                    }
                }
            }

            if (team_required && !user_team) check = false
            if (!check) {
                uni.showModal({
                    title: '提示',
                    content: '报名信息不完善，请完善报名信息。',
                    cancelText: '稍后完善',
                    confirmText: '立即完善',
                    success: res => {
                        if (res.confirm) {
                            uni.navigateBack()
                            const eventChannel = this.getOpenerEventChannel()
                            eventChannel?.emit && eventChannel.emit('updateAttendDetailShow')
                        }
                    }
                })
                return
            }

            this.exchange()

        },

        async exchange() {
            this.loading = true
            this.$uni.showLoading('兑换中...')

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exchange/exchange_step',
                data: {
                    active_id: this.id,
                    exchange_date: new Date(utils.getDay(0, false, '/')).getTime() / 1000
                }
            })

            if (res?.status !== 1) {
                if (await this.maxDailyStepSubmitReasonAgainShow()) {
                    this.loading = false
                    uni.hideLoading()
                    return this.$refs.maxDailyStepSubmitReason.open()
                }

                this.loading = false
                uni.hideLoading()

                const need_exam = !!res?.data?.['need_exam']

                const confirm = await this.$uni.showModal(res?.info || '兑换失败', {
                    showCancel: need_exam,
                    title: '兑换失败',
                    confirmText: need_exam ? '前往答题' : '确定',
                })

                if (need_exam && confirm.confirm) {
                    const {exam_id,id } = this.my_position['current_point_details']
                    this.$uni.navigateTo(`/pages/likou_dati/pages/exam/exam_details/exam_details?exam_id=${exam_id}&active_id=${this.id}&point_id=${id}`)
                }

                return
            }

            const eventChannel = this.getOpenerEventChannel()
            eventChannel?.emit?.('updateUserDetail')

            await this.getMapSet()

            uni.hideLoading()
            this.end_point = this.isEndPoint()
            console.log(this.end_point)
            this.exchange_success_info = res.info || '兑换成功';
            if (res.data?.['exchange_result']?.['show_text']?.length) {
                const show_text = res.data['exchange_result']['show_text']
                show_text.forEach(v => {
                    if (v.text && v.text.includes('红包')) v.is_redpack = true
                })
                this.exchange_success_tip_text_list = show_text
            }

            if (this.next_point && this.blindBoxLotteryId) {
                this.$refs.blindBoxLottery?.show(this.my_position?.current_point_details?.id)
            }
        },

        isEndPoint() {
            /*// 只有这些活动才需要 兑换到达终点的弹窗，前往抽奖的按钮改成前往运动圈打卡
            const id_list = [
                '36a14fed2e2ced045f7c02275f46be7e',
                // '24e6c2bd9b8c300885fde3b8a79213db'
            ]
            if (!id_list.includes(this.id)) return false*/


            const my_point_id = this.my_position?.['current_point_details']?.id
            if (!my_point_id) return false
            const markers = this.map_set?.markers
            if (!markers?.length) return false
            const end_point_id = markers.at(-1).id
            return my_point_id === end_point_id
        }
    }
}
</script>

<style scoped>

.page {
    min-height: 100vh;
}


.logo {
    display: block;
    width: 100vw;
    height: auto;
}

.road-map {
    width: 100vw;
}

.enable-satellite-switch {
    width: 40px;
    height: 40px;
    position: fixed;
    right: 10px;
    z-index: 99;
    background-color: rgba(0, 0, 0, .7);
    border-radius: 50%;
}

.big-image {
    position: relative;
}
.star-list-item {
    position: absolute;
    width: 50px;
}
.big-image image {
    width: 100vw;
}

.diy-button-container {
    position: fixed;
    left: 0;
    bottom: 30px;
    width: 100vw;
    z-index: 8;
}

.detail {
    position: fixed;
    left: 0;
    width: 100vw;
    box-sizing: border-box;
    padding: 30px 10px 15px;
    border-radius: 20px 20px 0 0;
    z-index: 8;
}

.exchange-view {
    position: absolute;
    top: -65px;
    left: 50%;
    margin-left: -35px;
}

.exchange-btn {
    width: 70px;
    height: 70px;
    border-radius: 50%;
}

.step-num {
    background-color: rgba(0, 0, 0, .7);
    color: #fff;
    line-height: 20px;
    height: 20px;
    padding: 2px 5px;
    border-radius: 12px;
    margin-bottom: 3px;
}

.exchange-btn-text, .exchange-btn-go {
    font-weight: bold;
}

.exchange-btn-text {
    position: relative;
    top: 6px;
}

/* .exchange-btn-go {
  position: relative;
  top: -3px;
} */

.grid-item {
    width: calc(100% / 3);
    box-sizing: border-box;
    padding: 10px 5px;
    line-height: 24px;
}

.step-update-icon {
    position: relative;
    top: -5px;
    left: 5px;
}

.success {
    position: fixed;
    z-index: 9;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, .7);
}

.success .main {
    width: 300px;
    border-radius: 10px;
    overflow: hidden;
}

.uni__popup {
    position: relative;
}

.point-detail-popup {
    width: 320px;
    border-radius: 10px;
    overflow: hidden;
}

/*.point-detail-popup .video {
    height: 170px;
}*/

.point-detail-title .point-detail-popup-tab {
    height: 40px;
    line-height: 40px;
    box-sizing: border-box;
    padding: 0 5px;
}

.point-detail-content {
    height: calc(100vh - 200px);
    box-sizing: border-box;
    position: relative;
}


.user-list-item {
    width: 75px;
    padding: 10px 0;
}

.user-list-item image {
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .road-map {
        width: 500px;
    }

    .logo {
        width: 500px;
    }

    .big-image image {
        width: 500px;
    }

    .detail {
        width: 500px;
        left: calc(((100vw - 500px) / 2) - 7px);
    }
}

/* #endif */
</style>
