<template>
    <view class="page bg-background">
        <activity-delete-tips v-if="error" :activeId="id"/>

        <view v-if="detail && detail.active_id">
            <!-- #ifndef H5 -->
            <image v-if="detail.logo" class="logo" mode="widthFix" :src="detail.logo"/>
            <!-- #endif -->
            <view class="bg-white p10">
                <view class="color-title font18" @click="copy(id)">{{ detail.name }}</view>
                <view v-if="detail.organizer" class="color-content font16">主办方：{{ detail.organizer }}</view>
            </view>

            <template v-if="!rankSet.closed_AD">
                <xwy-ad v-if="!popup_show" :ad_type="4"></xwy-ad>
                <xwy-ad :ad_type="3"></xwy-ad>
            </template>


            <view class="tools bg-white">
                <view class="title flex-kai">
                    <view class="color-title">活动管理</view>
                    <navigator v-if="document_show" url="/pages/other/activity-document?types=2">
                        <text class="iconfont icon-word-file color-light-primary font14"></text>
                        <text class="font14 color-light-primary" style="padding-left: 2px;">活动帮助文档</text>
                    </navigator>
                </view>
                <view class="list flex-row flex-wrap text-center">

                    <navigator :url="'./add_or_edit?id=' + id" class="item">
                        <text class="iconfont icon-edit color-primary font34"></text>
                        <view class="font14 color-content">活动修改</view>
                    </navigator>


                    <template v-if="!detail.conf.active.closed_team">

                        <navigator :url="'team_list?is_admin=1&id=' + id" class="item">
                            <text class="iconfont icon-team color-primary font34"></text>
                            <view class="font14 color-content">队伍管理</view>
                        </navigator>


                        <navigator
                            class="item"
                            v-if="rankSet.team_category"
                            :url="'/pages/category/list?types=19&active_id=' + id"
                        >
                            <text class="iconfont icon-dating color-primary font34"></text>
                            <view class="font14 color-content">队伍分类</view>
                        </navigator>
                    </template>


                    <navigator :url="'activity_user_list?id=' + id" class="item">
                        <text class="iconfont icon-users color-primary font34"></text>
                        <view class="font14 color-content">活动用户</view>
                    </navigator>


                    <navigator :url="'/pages/activity/admin/blacklist?id=' + id" class="item">
                        <text class="iconfont icon-blacklist color-primary font34"></text>
                        <view class="font14 color-content">黑名单用户</view>
                    </navigator>


                    <navigator v-if="rankSet.maxDailyStepSubmitReason"
                               :url="'/pages/activity/other/max-step-submit-list?is_admin=1&id=' + id" class="item">
                        <text class="iconfont icon-line-chart color-primary font34"></text>
                        <view class="font14 color-content">步数补交审核</view>
                    </navigator>

                    <view
                        v-if="detail.conf.active.map_types === 100 || detail.conf.active.map_types === 102"
                        class="item"
                        @click="toPointList"
                        hover-class="navigator-hover"
                    >
                        <text class="iconfont icon-map color-primary font34"></text>
                        <view class="font14 color-content">点位管理</view>
                    </view>

                    <navigator v-if="rankSet.medal_open" url="medal/list" class="item">
                        <text class="iconfont icon-medal color-primary font34"></text>
                        <view class="font14 color-content">勋章管理</view>
                    </navigator>


                    <!--这个是排行榜前?名的红包奖励记录-->

                    <navigator
                        v-if="rankSet.reward_team_person"
                        :url="'./reward-team-person/reward-record?id=' + id"
                        class="item"
                    >
                        <text class="iconfont icon-red-packet color-primary font34"></text>
                        <view class="font14 color-content">红包记录</view>
                    </navigator>


                    <view class="item" hover-class="navigator-hover" @click="newsManage">
                        <text class="iconfont icon-writing font34 color-primary"></text>
                        <view class="font14 color-content">文章管理</view>
                    </view>


                    <navigator v-if="rankSet.closed_AD" class="item"
                               :url="'/pages/category/list?types=8&active_id=' + id">
                        <text class="iconfont icon-dating font34 color-primary"></text>
                        <view class="font14 color-content">文章分类</view>
                    </navigator>
                    
                    <view class="item" @click="copyActivityPages">
                        <text class="iconfont icon-copy color-primary font34"></text>
                        <view class="font14 color-content">复制路径</view>
                    </view>

                    <!-- #ifndef H5 -->
                    <view class="item" @click="showWebUrl">
                        <text class="iconfont icon-screen color-primary font34"></text>
                        <view class="font14 color-content">web端管理</view>
                    </view>
                    <!-- #endif -->

                    <view v-if="rankSet.page_diy_set" class="item" @click="showDiyPageUrl">
                        <text class="iconfont icon-layout color-primary font34"></text>
                        <view class="font14 color-content">DIY活动页面</view>
                    </view>


                    <navigator
                        v-if="!detail.conf.active.time_period_step && !is_simple"
                        class="item"
                        :url="'./detail-page-template/list?id=' + id"
                    >
                        <uni-icons type="color" size="34" color="#2d8cf0"/>
                        <view class="font14 color-content">模版市场</view>
                    </navigator>


                    <view v-if="who === 288 && evn_version !== 'release' && rankSet.closed_AD" class="item"
                          @click="copyDiyMapSetLink">
                        <text class="iconfont icon-screen color-primary font34"></text>
                        <view class="font14 color-content">DIY点位</view>
                    </view>


                    <!--由于只有一个活动(广晟)使用了这个功能,并且这个活动开启了活跃度排行榜功能(rankSet.activity_total),所以开启了活跃度排行榜的活动就显示公众号消息推送设置-->

                    <navigator v-if="rankSet.activity_total"
                               :url="'/pages/other/wechat-message-push-set/set?id=' + id" class="item">
                        <uni-icons type="notification" size="34" color="#2d8cf0"/>
                        <view class="font14 color-content">消息推送设置</view>
                    </navigator>

                    <!--<navigator
                        v-if="open_business_kilometers"
                        class="item"
                        :url="'/pages/activity/business-kilometers/user-list?id=' + id"
                    >
                        <text class="iconfont icon-aeroplane color-primary font34"></text>
                        &lt;!&ndash;雷子说到时候用户会改的，先让我随便写 2023-07-18 17:42:38 企业微信私聊&ndash;&gt;
                        <view class="font14 color-content">出差里程兑换用户名单</view>
                    </navigator>-->


                    <navigator class="item" 
                               :url="'/pages/other/AI-analysis-report?type=active&active_id=' + id">
                        <text class="iconfont icon-data-summary color-primary font34"></text>
                        <view class="font14 color-content">AI活动报告</view>
                    </navigator>
                    

                    <navigator
                        v-if="!detail.conf.active.close_feedback"
                        :url="'/pages/activity/feedback/list?id=' + id"
                        class="item"
                    >
                        <text class="iconfont icon-chat-bubble color-primary font34"></text>
                        <view class="font14 color-content">用户反馈</view>
                    </navigator>

                    <navigator class="item" url="/pages/other/contact">
                        <uni-icons type="chatboxes" size="34" color="#2d8cf0"/>
                        <view class="font14 color-content">联系客服</view>
                    </navigator>



                    <view class="item" @click="showActiveSharePopup">
                        <text class="iconfont icon-share color-primary font34"></text>
                        <view class="font14 color-content">转发分享</view>
                    </view>
                </view>
            </view>


            <view v-if="rankSet.active_pay" class="tools bg-white">
                <view class="title color-title">报名项目</view>
                <view class="list flex-row flex-wrap text-center">

                    <navigator class="item" :url="'/pages/registration/admin/project/list?id=' + id">
                        <text class="iconfont icon-dating color-primary font34"></text>
                        <view class="font14 color-content">报名项目管理</view>
                    </navigator>


                    <navigator class="item" :url="'/pages/registration/admin/registration-record?id=' + id">
                        <text class="iconfont icon-create-ticket color-primary font34"></text>
                        <view class="font14 color-content">付费记录</view>
                    </navigator>

                    <view class="item" hover-class="navigator-hover" @click="registrationRecordsExport">
                        <text class="iconfont icon-export-excel color-primary font34"></text>
                        <view class="font14 color-content">付费记录导出</view>
                    </view>
                </view>
            </view>

            <view v-if="rankSet.batch_import" class="tools bg-white">
                <view class="title color-title">活动用户导入</view>
                <view class="list flex-row flex-wrap text-center">
                    <view hover-class="navigator-hover" class="item" @click="importUsers">
                        <text class="iconfont icon-import color-primary font34"></text>
                        <view class="font14 color-content">批量导入</view>
                    </view>


                    <navigator class="item" :url="'./import_users/user_list?id=' + id">
                        <text class="iconfont icon-users color-primary font34"></text>
                        <view class="font14 color-content">用户名单</view>
                    </navigator>

                    <view hover-class="navigator-hover" class="item" @click="downloadImportUsersTemplate">
                        <text class="iconfont icon-export-excel color-primary font34"></text>
                        <view class="font14 color-content">模版下载</view>
                    </view>
                </view>

            </view>


            <view class="tools bg-white">
                <view class="title color-title">数据导出</view>
                <view class="list flex-row flex-wrap text-center">
                    <view class="item" @click="exportRankingData(false, detail.conf.active.time_period_step ? 28 : null)">
                        <text class="iconfont icon-trophy color-primary font34"></text>
                        <view class="font14 color-content">个人排行榜</view>
                    </view>

                    <template v-if="!detail.conf.active.closed_team">
                        <view class="item" @click="exportRankingData(true, null)">
                            <text class="iconfont icon-team color-primary font34"></text>
                            <view class="font14 color-content">队伍排行榜</view>
                        </view>

                        <view class="item" @click="exportWithinTheTeamRankingSelectTeam()">
                            <text class="iconfont icon-team color-primary font34"></text>
                            <view class="font14 color-content">队内排行榜</view>
                        </view>
                    </template>

                    <template v-if="rankSet.activity_total">
                        <view class="item" @click="exportActivityTotalRankingPopupShow(false)">
                            <text class="iconfont icon-hot color-primary font34"></text>
                            <view class="font14 color-content">个人活跃度排行榜</view>
                        </view>
                        <view class="item" @click="exportActivityTotalRankingPopupShow(true)">
                            <text class="iconfont icon-hot color-primary font34"></text>
                            <view class="font14 color-content">队伍活跃度排行榜</view>
                        </view>
                    </template>
                    
                    <view v-if="rankSet.multi_top_rank_finishedAndScore"
                          class="item" @click="exportRankingData(false, 39)">
                        <text class="iconfont icon-data-summary color-primary font34"></text>
                        <view class="font14 color-content">综合排行榜</view>
                    </view>

                    <view v-if="rankSet.gift_goods || rankSet.together_pic" class="item"
                          @click="exportRankingData(false, 20)">
                        <text class="iconfont icon-integral color-primary font34"></text>
                        <view class="font14 color-content">{{ integralUnit }}排行榜</view>
                    </view>

                    <view v-if="rankingJsonConfig['types_49']" class="item"
                          @click="exportMonthIntegralRanking">
                        <text class="iconfont icon-integral color-primary font34"></text>
                        <view class="font14 color-content">月{{ integralUnit }}排行榜</view>
                    </view>

                    <view v-if="rankSet.real_step" class="item" @click="exportRealStepRanking">
                        <text class="iconfont icon-walk color-primary font34"></text>
                        <view class="font14 color-content">实际运动步数排行榜</view>
                    </view>

                    <template v-if="rankSet.teamIntegralTotalByMonth">
                        <view class="item" @click="exportTeamMonthIntegral">
                            <text class="iconfont icon-integral color-primary font34"></text>
                            <view class="font14 color-content">月队伍{{ integralUnit }}汇总</view>
                        </view>
                        <export-team-month-integral ref="exportTeamMonthIntegral" :active-id="id"
                                                    :unit="integralUnit"/>
                    </template>

                    <view class="item" hover-class="navigator-hover" @click="exportActivityUserList">
                        <text class="iconfont icon-users color-primary font34"></text>
                        <view class="font14 color-content">活动用户</view>
                    </view>


                    <navigator v-if="rankSet.week_month_total" class="item"
                               :url="'export_total_step?id=' + id + '&name=' + detail.name + '&begin_time=' + detail.begin_time + '&end_time=' + detail.end_time">
                        <text class="iconfont icon-data-summary color-primary font34"></text>
                        <view class="font14 color-content">汇总数据</view>
                    </navigator>


                    <navigator
                        v-if="rankSet.week_month_total && rankSet.week_month_total_mobile_step"
                        class="item"
                        :url="'export_total_step?is_mobile_step=1&id=' + id + '&name=' + detail.name + '&begin_time=' + detail.begin_time + '&end_time=' + detail.end_time"
                    >
                        <text class="iconfont icon-walk color-primary font34"></text>
                        <view class="font14 color-content">微信运动步数</view>
                    </navigator>

                    <view v-if="rankSet.open_sport_moment" class="item" hover-class="navigator-hover"
                          @click="exportSportMoment">
                        <text class="iconfont icon-wechat-movement color-primary font34"></text>
                        <view class="font14 color-content">运动圈数据</view>
                    </view>


                    <navigator v-if="rankSet.real_step" class="item"
                               :url="'export_total_step?is_real_step=1&id=' + id + '&name=' + detail.name + '&begin_time=' + detail.begin_time + '&end_time=' + detail.end_time">
                        <text class="iconfont icon-footnotes color-primary font34"></text>
                        <view class="font14 color-content">实际运动步数</view>
                    </navigator>


                    <navigator v-if="!detail.conf.active.close_feedback" class="item"
                               :url="'/pages/other/export-feedback?active_id=' + id">
                        <text class="iconfont icon-chat-bubble color-primary font34"></text>
                        <view class="font14 color-content">用户反馈</view>
                    </navigator>

                    <navigator class="item" url="./export_record">
                        <text class="iconfont icon-export-excel color-primary font34"></text>
                        <view class="font14 color-content">导出记录</view>
                    </navigator>
                </view>

            </view>

            <view v-if="exam_open" class="tools bg-white">
                <!-- <view class="tools bg-white"> -->
                <view class="title color-title">答题管理</view>
                <view class="list flex-row flex-wrap text-center">
                    <navigator class="item" url="/pages/likou_dati/pages/exam/exam_list/exam_list">
                        <text class="iconfont icon-examination-paper color-primary font34"></text>
                        <view class="font14 color-content">我的考卷</view>
                    </navigator>
                    <navigator
                        class="item"
                        url="/pages/likou_dati/pages/question/question_bank_list/question_bank_list"
                    >
                        <text class="iconfont icon-dictionary color-primary font34"></text>
                        <view class="font14 color-content">我的题库</view>
                    </navigator>
                    <navigator class="item" url="/pages/likou_dati/pages/question/category_list/category_list">
                        <text class="iconfont icon-dating color-primary font34"></text>
                        <view class="font14 color-content">题库分类</view>
                    </navigator>
                    <navigator
                        class="item"
                        url="/pages/likou_dati/pages/question/batch_import_question/explain/explain"
                    >
                        <text class="iconfont icon-import color-primary font34"></text>
                        <view class="font14 color-content">批量导题</view>
                    </navigator>
                    <!-- 开启了累计答题分数功能才能导出答题排行榜数据----雷子提的
                    这个答题排行榜导出的是否可以做个判断，没有开启累计答题分数功能的就不显示这个答题排行榜。现在那些开了答题功能的都会有这个排行榜，但点导出就导出不了，要在开启下累计答题得分功能才可以导出。客户又不愿意重新在付费了
                    2022年10月08日15:47:43 -->
                    <view v-if="rankSet.sum_exam_score" class="item" @click="exportExamData()">
                        <text class="iconfont icon-trophy color-primary font34"></text>
                        <view class="font14 color-content">导出排行榜</view>
                    </view>
                </view>

            </view>


            <view v-if="rankSet.lottery_open || rankSet.mystery_box" class="tools bg-white">
                <view class="title color-title">抽奖活动管理</view>
                <view class="list flex-row flex-wrap text-center">
                    <view class="item" hover-class="navigator-hover" @click="lotteryManage">
                        <text class="iconfont icon-dating font34 color-primary"></text>
                        <view class="font14 color-content">活动列表</view>
                    </view>

                    <template v-if="rankSet.lottery_open">
                        <navigator
                            class="item"
                            :url="'/pages/lottery/user/lottery_record_list?active_id=' + id + '&is_admin=1'"
                        >
                            <text class="iconfont icon-gift font34 color-primary"></text>
                            <view class="font14 color-content">抽奖记录</view>
                        </navigator>
                        <view class="item" hover-class="navigator-hover" @click="exportLotteryRecord">
                            <text class="iconfont icon-export-excel font34 color-primary"></text>
                            <view class="font14 color-content">导出抽奖记录</view>
                        </view>
                    </template>
                </view>
            </view>

            <view v-if="rankSet.gift_goods" class="tools bg-white">
                <view class="title color-title">商城管理</view>
                <view class="list flex-row flex-wrap text-center">
                    <view class="item" @click="toShopManage">
                        <uni-icons type="shop" size="34" color="#2d8cf0"/>
                        <view class="font14 color-content">商品管理</view>
                    </view>


                    <navigator
                        class="item"
                        :url="`/pages/shop/order/order_list?is_admin=1&active_id=${id}&unit=${integralUnit}`"
                    >
                        <uni-icons type="list" size="34" color="#2d8cf0"/>
                        <view class="font14 color-content">订单记录</view>
                    </navigator>

                    <template v-if="rankSet.import_delivery_num">
                        <navigator class="item" :url="`/pages/shop/logistics-tracking/list?active_id=${id}`">
                            <text class="iconfont icon-logistics font34 color-primary"></text>
                            <view class="font14 color-content">物流单号</view>
                        </navigator>

                        <view class="item" @click="downloadLogisticsTrackingImportTemplate">
                            <text class="iconfont icon-export-excel font34 color-primary"></text>
                            <view class="font14 color-content">物流单号导入模版下载</view>
                        </view>
                    </template>
                </view>
            </view>


            <!--这个是兑换成功或到达点位发放的红包-->
            <view v-if="rankSet.redpack" class="tools bg-white">
                <view class="title color-title">
                    <text class="color-title">红包管理</text>
                    <text class="font14 color-sub pl5">(余额: {{ detail.redpack_amount }}元)</text>
                </view>
                <view class="list flex-row flex-wrap text-center">

                    <navigator class="item" :url="'/pages/wallet/redpack_list?is_admin=1&id=' + id">
                        <text class="iconfont icon-red-packet font34 color-primary"></text>
                        <view class="font14 color-content">红包记录</view>
                    </navigator>
                    <view class="item" hover-class="navigator-hover" @click="exportRedpackRecord">
                        <text class="iconfont icon-export-excel font34 color-primary"></text>
                        <view class="font14 color-content">导出红包记录</view>
                    </view>
                </view>
            </view>
        </view>


        <active-share ref="activeShare"/>



        <uni-popup ref="integralInsufficientTipsPopup" type="center">
            <view class="export_ranking_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('integralInsufficientTipsPopup')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top" style="padding: 50px 20px 20px;">
                    <text class="iconfont icon-integral color-warning" style="font-size: 80px;"></text>
                    <view class="font14 color-sub">{{ integralInsufficientTips }}</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <button
                        open-type="contact"
                        class="export_ranking-btn bg-green color-white font16"
                        @click="contactCustomerServiceToRechargeIntegral"
                    >联系客服</button>
                </view>
            </view>
        </uni-popup>
        

        <uni-popup ref="not_export_ranking_tips" type="center">
            <view class="export_ranking_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('not_export_ranking_tips')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top" style="padding: 50px 20px 20px;">
                    <text class="iconfont icon-export-excel color-green" style="font-size: 80px;"></text>
                    <view class="font14 color-sub">未开通导出数据功能</view>
                    <view class="font14 color-sub">请联系客服开通</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <button
                        open-type="contact"
                        class="export_ranking-btn bg-green color-white font16"
                        @click="uniPopupClose('not_export_ranking_tips')"
                    >联系客服开通</button>
                </view>
            </view>
        </uni-popup>

        <uni-popup ref="not_export_exam_tips" type="center">
            <view class="export_ranking_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('not_export_exam_tips')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top" style="padding: 50px 20px 20px;">
                    <text class="iconfont icon-export-excel color-green" style="font-size: 80px;"></text>
                    <view class="font14 color-sub">未开通导出答题排行榜数据功能</view>
                    <view class="font14 color-sub">请联系客服开通</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <button
                        open-type="contact"
                        class="export_ranking-btn bg-green color-white font16"
                        @click="uniPopupClose('not_export_exam_tips')"
                    >联系客服开通</button>
                </view>
            </view>
        </uni-popup>


        <uni-popup ref="export_ranking_success" type="center">
            <view class="export_ranking_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('export_ranking_success')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top bg-primary color-white" style="padding: 50px 20px 20px;">
                    <icon type="success" size="80" color="#ffffff"></icon>
                    <view class="font18">导出成功</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ export_ranking_success_tips }}
                    </view>
                    <view
                        class="export_ranking-btn bg-info color-white"
                        hover-class="navigator-hover"
                        @click="copyDownloadSrc(false)"
                    >复制下载地址</view>
                </view>
            </view>
        </uni-popup>

        <uni-popup ref="copy_web_src" type="center">
            <view class="export_ranking_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('copy_web_src')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top bg-primary color-white" style="padding: 50px 20px 20px;">
                    <text class="iconfont icon-screen color-white" style="font-size: 80px;"></text>
                    <view class="font18">{{ copy_web_src.title }}</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ copy_web_src.content }}
                    </view>
                    <view
                        class="export_ranking-btn bg-info color-white"
                        hover-class="navigator-hover"
                        @click="copy(copy_web_src.src)"
                    >复制地址</view>
                </view>
            </view>
        </uni-popup>

        <web-admin-src-copy-popup ref="web-admin-src-copy-popup"/>
    </view>
</template>

<script>
const app = getApp()
import login from '../../../utils/api/login.js'
import xwy_api from '../../../utils/api/xwy_api.js'
import my_storage from '../../../utils/storage.js'

export default {
    data() {
        return {
            loading: true,
            id: '',
            detail: {},
            error: '',
            export_ranking_success_tips: '',
            web_base_url: '',
            exam_open: false,
            popup_show: false,
            copy_web_src: {
                src: '',
                title: '',
                content: ''
            },

            // 出差公里数
            open_business_kilometers: false,
            // 出差公里数

            is_simple: true,
            document_show: this.xwy_config.showActivitySpecificationDocument(2),

            integralInsufficientTips: '',
            rankingJsonConfig: {},
            who: app.globalData.who,
            evn_version: app.globalData.evn_version
        }
    },

    computed: {
        integralUnit() {
            return this.detail?.conf?.active?.integral?.unit || '金币'
        },

        rankSet() {
            let rank_set = this.detail?.rank_set || {}
            if (rank_set === 'null' || Array.isArray(rank_set)) rank_set = {}
            return rank_set
        }
    },
    
    onShareAppMessage() {
        let path = '/pages/activity/user/detail?id=' + this.id + '&userid=' + app.globalData['userid']
        if (this.detail.conf.active.screen_pic) path += `&screen_pic=` + (this.detail.conf.active.screen_pic || '')
        return {
            title: this.detail.name,
            path,
            imageUrl: this.detail.logo || ''
        }
    },
    onLoad(e) {
        console.log('活动详情页面路径参数', e)
        uni.showLoading({ mask: true })
        if (!e.id) {
            this.loading = false
            uni.hideLoading()
            this.error = '请指定活动id'
            return false
        }

        this.id = e.id
        this.open_business_kilometers = this.xwy_config.openBusinessKilometers(e.id)

        login.uniLogin(err => {
            this.loading = false
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            if (app.globalData['shop_info']?.['shop_set']?.static_url) this.web_base_url = app.globalData['shop_info']['shop_set'].static_url


            this.getDetail()
        })
    },
    methods: {


        downloadImportUsersTemplate() {
            this.importUsersTemplate('download-template')
        },
        importUsers() {
            this.importUsersTemplate('import-excel')
        },
        async importUsersTemplate(url) {
            let isH5 = false
            // #ifdef H5
            isH5 = true
            // #endif

            if (isH5) return this.$uni.navigateTo(`/pages/other/batch_import/${url}?id=${this.id}`)

            const res = await this.$uni.showModal('请在web端使用此功能')
            res.confirm && this.web_base_url && this.showWebUrl()
        },


        getDetail() {
            xwy_api.getActivityDetail(this.id, res => {
                uni.hideLoading()
                if (!res?.data?.['usedFreeSimple']) this.is_simple = false
                if (res.data?.['active_details']) {
                    const detail = res.data['active_details']
                    app.globalData.activity_detail = detail
                    my_storage.setActivityCloaeAdStorage(this.id, this.rankSet.closed_AD)
                    this.detail = detail
                    if (this.rankSet.exam_open) this.exam_open = true

                    this.getRankCategorySet(res.data.active_more_data?.active_conf_set?.['toprank_list'])
                } else {
                    this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                }
            })
        },

        getRankCategorySet(list) {
            if (!list?.length) return

            const rankingJsonConfig = {}
            list.forEach(item => {
                rankingJsonConfig[`types_${item.id}`] = true
            })
            this.rankingJsonConfig = rankingJsonConfig
        },

        toPointList() {
            uni.navigateTo({
                url: 'point_list?id=' + this.id
            })
        },


        lotteryManage() {
            let url = "/pages/lottery/admin/lottery/list"

            /**
             * mystery_box: 盲盒抽奖
             * page_diy_set: diy活动页面，用户判断是否显示抽奖活动关联其他活动或答题
             * lottery_add_step_integral: 步数抽奖或积分抽奖
             * redpack: 红包抽奖，奖品可以设置为红包
             * lotteryForUserIntegral: 抽奖奖品可以设置为积分
             * */
            const {mystery_box, page_diy_set, lottery_add_step_integral, redpack, lotteryForUserIntegral} = this.rankSet

            let params = []
            if (mystery_box) params.push('mystery_box=1')
            if (page_diy_set) params.push('page_diy_set=1')
            if (lottery_add_step_integral) params.push('lottery_add_step_integral=1')
            if (redpack) params.push('redpack=1')
            if (lotteryForUserIntegral) params.push('lotteryForUserIntegral=1')
            if (params.length) url += `?${params.join('&')}`

            this.$uni.navigateTo(url)
        },

        uniPopupOpen(ref) {
            this.popup_show = true
            this.$refs[ref].open()
        },

        uniPopupClose(ref) {
            this.popup_show = false
            this.$refs[ref].close()
        },

        contactCustomerServiceToRechargeIntegral() {
            this.$uni.setClipboardData('积分')
            this.uniPopupClose('integralInsufficientTipsPopup')
        },

        exportMonthIntegralRanking() {
            this.$uni.navigateTo(`/pages/other/export-month-integral-ranking?active_id=${this.id}`)
        },

        exportTeamMonthIntegral() {
            this.$refs.exportTeamMonthIntegral.open()
        },

        exportRealStepRanking() {
            this.$uni.navigateTo(`/pages/activity/admin/export-real-step-ranking?active_id=${this.id}`)
        },

        exportExamData() {
            const export_exam_excel_open = this.rankSet.sum_exam_score
            if (!export_exam_excel_open) {
                // 没有开通功能的提示
                this.uniPopupOpen('not_export_exam_tips')
                return false
            }

            this.exportExamDataAjax()
        },

        exportExamDataAjax() {
            uni.showLoading({
                title: '导出中...',
                mask: app.globalData['evn_version'] !== 'trial'
            })

            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id
            }

            xwy_api.ajax({
                url: 'front.flat.sport_step.export.admin_active/export_user_total_score',
                data,
                success: res => {
                    uni.hideLoading()
                    console.log('导出答题排行榜数据', res)
                    if (res?.status !== 1) return this.$uni.showModal(res.info || '导出失败')


                    this.export_ranking_success_tips = res.info || '导出成功'
                    this.export_ranking_src = res.data.url
                    this.copyDownloadSrc(true)
                    this.uniPopupOpen('export_ranking_success')


                    const title = `导出活动【${this.detail.name}】答题排行榜`;
                    my_storage.setExportExcelRecord({url: res.data.url, title})
                }
            })
        },

        exportActivityUserList() {
            this.$uni.navigateTo(`/pages/other/export-activity-user-list?active_id=${this.id}&active_name=${this.detail.name}`, {
                events: {
                    onePageExportSuccess: data => {
                        this.export_ranking_success_tips = data.info || '导出成功'
                        this.export_ranking_src = data.url
                        this.copyDownloadSrc(true)
                        this.uniPopupOpen('export_ranking_success')
                    }
                }
            })
        },

        exportRankingData(is_team, types = null) {
            const individual_leaderboard = !is_team && !types // 个人排行榜(不是队伍排行榜并且没有types)
            // 免费导出，individual_leaderboard个人排行榜  types === 21导出活动用户名单
            const freeExport = individual_leaderboard || types === 21
            if (!freeExport) {
                // 个人排行榜不用判断是否开通导出功能，接口会判断，没有开通会自动扣积分
                const export_top_rank_excel_open = this.rankSet.export_top_rank_excel
                if (!export_top_rank_excel_open) return this.uniPopupOpen('not_export_ranking_tips')
            }

            if (types) return this.exportRankingDataAjax(is_team, types)
            is_team ? this.exportRankingDataAjax(is_team) : this.checkPersonCount(is_team)
        },

        async checkPersonCount(is_team) {
            uni.showLoading({
                mask: app.globalData['evn_version'] !== 'trial'
            })

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.export.admin_active/active_attend_person_count',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id
                }
            })

            uni.hideLoading()

            if (res?.['data']?.attend_person_count && res.data.attend_person_count > 3000) {
                uni.navigateTo({
                    url: `./export_rank?is_team=${is_team}&active_id=${this.id}&name=${this.detail.name}`
                })
                return false
            }
            this.exportRankingDataAjax(is_team)
        },

        exportRankingDataAjax(is_team, types = null) {

            uni.showLoading({
                title: '导出中...',
                mask: app.globalData['evn_version'] !== 'trial'
            })

            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id
            }
            if (is_team) data.is_team = 1
            if (types) data.types = types

            xwy_api.ajax({
                url: 'front.flat.sport_step.export.admin_active/export_user_total_rank',
                data,
                success: res => {
                    uni.hideLoading()
                    console.log('导出排行榜数据', res)
                    if (res?.status !== 1) {
                        const info = res?.info || '导出失败'
                        if (info.includes('积分')) {
                            this.integralInsufficientTips = info
                            return this.uniPopupOpen('integralInsufficientTipsPopup')
                        }
                        return this.$uni.showModal(info)
                    }


                    this.export_ranking_success_tips = res.info || '导出成功'
                    this.export_ranking_src = res.data.url
                    this.copyDownloadSrc(true)
                    this.uniPopupOpen('export_ranking_success')


                    let title = `导出活动【${this.detail.name}】${is_team ? '队伍' : '个人'}排行榜`
                    if (types) {
                        const title_options = {
                            20: `${this.integralUnit}排行榜`,
                            21: '活动用户名单',
                            39: '综合排行榜',
                        }
                        if (title_options[types]) {
                            title = `导出活动【${this.detail.name}】${title_options[types]}`
                        }
                    }
                    my_storage.setExportExcelRecord({url: res.data.url, title})
                }
            })
        },

        exportWithinTheTeamRankingSelectTeam() {
            const export_top_rank_excel_open = this.rankSet.export_top_rank_excel
            if (!export_top_rank_excel_open) return this.uniPopupOpen('not_export_ranking_tips')

            uni.navigateTo({
                url: `./team_list?selteam=1&is_manage=1&id=${this.id}`,
                events: {
                    setTeam: data => {
                        this.exportWithinTheTeamRanking(data.team_id, data.team_name)
                    }
                }
            })
        },

        async exportWithinTheTeamRanking(team_id, team_name = '') {
            if (!team_id) return
            uni.showLoading({
                title: '导出中...',
                mask: app.globalData['evn_version'] !== 'trial'
            })
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.export.admin_active/export_user_total_rank',
                data: {
                    active_id: this.id,
                    team_id,
                    page: 1,
                    perpage: 3000,
                }
            })

            uni.hideLoading()
            if (res?.['status'] !== 1) return uni.showModal({
                title: '导出失败',
                content: res?.['info'] || '导出失败',
                showCancel: false
            })

            this.export_ranking_success_tips = res['info'] || '导出成功'
            this.export_ranking_src = res['data'].url
            this.copyDownloadSrc(true)
            this.uniPopupOpen('export_ranking_success')

            const title = `导出活动【${this.detail.name}】队伍【${team_name}】队内排行榜`
            my_storage.setExportExcelRecord({url: res['data'].url, title})
        },

        async registrationRecordsExport() {
            uni.showLoading({
                title: '导出中...',
                mask: app.globalData['evn_version'] !== 'trial'
            })
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_pay.adminManage/user_submit_buy_records_list',
                data: {
                    active_id: this.id,
                    need_excel: 1,
                    page: 1,
                    perpage: 3000
                }
            })
            uni.hideLoading()
            if (res?.['status'] !== 1) return uni.showModal({
                title: '导出失败',
                content: res?.['info'] || '导出失败',
                showCancel: false
            })

            this.export_ranking_success_tips = res['info'] || '导出成功'
            this.export_ranking_src = res['data'].url
            this.copyDownloadSrc(true)
            this.uniPopupOpen('export_ranking_success')


            const title = `导出活动【${this.detail.name}】付费报名记录`
            my_storage.setExportExcelRecord({url: res['data'].url, title})
        },

        async exportLotteryRecord() {
            uni.showLoading({
                title: '导出中...',
                mask: app.globalData['evn_version'] !== 'trial'
            })

            const res = await xwy_api.request({
                url: 'front.flat.active.lottery_active.user.lottery/lottery_records_list',
                data: {
                    is_admin: 1,
                    active_id: this.id,
                    need_excel: 1,
                    get_all_prize: 1
                }
            })

            uni.hideLoading()

            this.export_ranking_success_tips = res['info'] || '导出成功'
            this.export_ranking_src = res['data'].url
            this.copyDownloadSrc(true)
            this.uniPopupOpen('export_ranking_success')


            const title = `导出活动【${this.detail.name}】抽奖记录`;
            my_storage.setExportExcelRecord({url: res['data'].url, title})
        },


        exportActivityTotalRankingPopupShow(isTeam = false) {
            let url = `./export-activity-total-ranking?id=${this.id}`
            if (isTeam) url += '&is_team=1'
            this.$uni.navigateTo(url)
        },

        async downloadLogisticsTrackingImportTemplate() {
            let isH5 = false
            // #ifdef H5
            isH5 = true
            // #endif

            if (isH5) {
                // window.location.href = 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/wbfile/importtrackingnumber.xlsx'

                fetch('https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/wbfile/importtrackingnumber.xlsx')
                    .then(res => res.blob())
                    .then(blob => {
                        const url = URL.createObjectURL(blob)
                        const a = document.createElement('a')
                        a.href = url
                        a.download = '物流单号导入模版.xlsx'
                        document.body.appendChild(a)
                        a.click()
                        document.body.removeChild(a)
                        URL.revokeObjectURL(url)
                    })

                return
            }

            const {confirm} = await this.$uni.showModal('请在web端使用此功能')
            confirm && this.web_base_url && this.showWebUrl()
        },
        
        copyDownloadSrc(hide = false) {
            uni.setClipboardData({
                data: this.export_ranking_src,
                success: () => hide ? uni.hideToast() : this.$uni.showToast('复制成功', 'success', 1000)
            })
        },

        async exportRedpackRecord() {
            uni.showLoading({
                mask: app.globalData['evn_version'] !== 'trial'
            })

            const res = await xwy_api.request({
                url: 'front.user.redpack.user/redpack_amount_list',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id,
                    types: 11,
                    page: 1,
                    perpage: 3000,
                    is_admin: 1,
                    need_excel: 1
                }
            })
            uni.hideLoading()
            const total = res?.['data']?.['redpack_amount_list']?.total || 0
            if (total === 0) {
                xwy_api.alert('暂无红包记录')
                return false
            }

            if (total > 3000) {
                uni.navigateTo({
                    url: `/pages/wallet/export_rank?active_id=${this.id}&name=${this.detail.name}&person_count=${total}`
                })
                return false
            }

            this.export_ranking_success_tips = res['info'] || '导出成功'
            this.export_ranking_src = res['data'].url
            this.copyDownloadSrc(true)
            this.uniPopupOpen('export_ranking_success')


            const title = `导出活动【${this.detail.name}】红包记录`;
            my_storage.setExportExcelRecord({url: res['data'].url, title})
        },


        async exportSportMoment() {
            uni.showLoading({ mask: true })
            const res = await xwy_api.request({
                url: 'front.user.reply/reply_view_list',
                data: {
                    page: 1,
                    perpage: 1000,
                    types: 6,
                    pid: this.id,
                    // get_child: 1,
                    is_long_id: 1,
                    need_excel: 1,
                    reply_id: 0     // 不导出评论
                }
            })
            uni.hideLoading()
            if (!res['status'] || res['status'] !== 1) {
                xwy_api.alert(res?.['info'] || '导出失败')
                return
            }

            const count = res?.['data']?.all_count || 0
            if (count > 1000) {
                uni.navigateTo({
                    url: `/pages/wallet/export_rank?active_id=${this.id}&name=${this.detail.name}&person_count=${count}&type=sport_moment`
                })
                return
            }

            this.export_ranking_success_tips = res['info'] || '导出成功'
            this.export_ranking_src = res['data'].url
            this.copyDownloadSrc(true)
            this.uniPopupOpen('export_ranking_success')


            const title = `导出活动【${this.detail.name}】运动圈数据`;
            my_storage.setExportExcelRecord({url: res['data'].url, title})
        },

        showActiveSharePopup() {
            this.$refs.activeShare.open({
                page: 'pages/activity/user/detail',
                scene: 'id=' + this.detail.id + '&uid=' + app.globalData['userid'],
                qrcode_logo: this.rankSet.closed_AD && this.detail.conf?.active?.qrcode_logo
            })
        },


        showWebUrl() {
            this.$refs['web-admin-src-copy-popup'].open({
                successCallback: () => this.popup_show = true,
                closeCallback: () => this.popup_show = false
            })
        },

        showDiyPageUrl() {
            if (!this.web_base_url) {
                xwy_api.alert('没有配置【static_url】，无法使用web端管理')
                return false
            }

            const url = this.web_base_url + 'web/wx-cloud-api/pages/admin_manage/diy_activity_detail_page/#/pages/index?access_token=' + app.globalData['access_token'] + '&active_id=' + this.id

            // #ifdef H5
            window.open(url, '_blank')
            // #endif

            // #ifndef H5
            this.copy_web_src = {
                title: '已生成设置地址',
                content: '已生成DIY活动页面设置网页地址，请复制后使用浏览器打开。',
                src: url
            }
            this.$refs.copy_web_src.open()
            this.popup_show = true
            uni.setClipboardData({
                data: url,
                success: () => uni.hideToast()
            })
            // #endif
        },

        copyDiyMapSetLink() {
            if (!this.web_base_url) {
                this.$uni.showModal('没有配置【static_url】，无法使用web端管理')
                return
            }

            const url = this.web_base_url + 'web/wx-cloud-api/pages/admin_manage/step-diy-map?access_token=' + app.globalData['access_token'] + '&active_id=' + this.id

            // #ifdef H5
            window.open(url, '_blank')
            // #endif

            // #ifndef H5
            this.copy_web_src = {
                title: '已生成设置地址',
                content: '已生成DIY地图设置网页地址，请复制后使用浏览器打开。',
                src: url
            }
            this.$refs.copy_web_src.open()
            this.popup_show = true
            uni.setClipboardData({
                data: url,
                success: () => uni.hideToast()
            })
            // #endif
        },

        toShopManage() {
            // #ifndef H5
            uni.showModal({
                title: '提示',
                content: '请在web端管理商品',
                showCancel: false,
                success: res => {
                    if (res.confirm && this.web_base_url) this.showWebUrl()
                }
            })
            // #endif

            // #ifdef H5
            window.open(this.web_base_url + '/web/wx-cloud-api/pages/admin_manage/goods#/pages/goods/list?access_token=' + app.globalData['access_token'])
            // #endif
        },

        newsManage() {
            let url = `/pages/news/list?type=user&active_id=${this.id}`
            const {closed_AD, article_bind_exam, reward_step_by_reading} = this.rankSet
            if (closed_AD) url += '&vip=1'
            if (article_bind_exam) url += '&article_bind_exam=1'
            if (reward_step_by_reading) url += '&reward_step_by_reading=1'
            this.$uni.navigateTo(url)  
        },

        copyActivityPages() {
            let data = '活动小程序路径：pages/activity/user/detail?id=' + this.id
            if (this.detail.screen_pic) data += `&screen_pic=` + this.detail.screen_pic
            if (app.globalData['shop_info']?.['extend_set']?.['conf_set']?.appid) {
                data = `小程序appid：${app.globalData['shop_info']['extend_set']['conf_set'].appid} \n ${data}`
            }
            uni.setClipboardData({
                data,
                success() {
                    uni.hideToast()
                    uni.showModal({
                        title: '复制成功',
                        content: '小程序路径地址复制成功，请粘贴到公众号自定义菜单设置里',
                        showCancel: false
                    })
                }
            })
        },

        copy(data) {
            uni.setClipboardData({
                data,
                success: () => this.$uni.showToast('复制成功', 'none', 500)
            })
        }
    }
}
</script>

<style scoped lang="scss">
.page {
    min-height: 100vh;
    padding-bottom: 15px;
    box-sizing: border-box;
}

.logo {
    display: block;
    width: 100vw;
    height: auto;
}


.tools {
    margin: 10px;
    border-radius: 10px;
    overflow: hidden;
}

.tools .title {
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.item {
    width: calc(100% / 4);
    padding: 10px 1px;
    box-sizing: border-box;
}

.export_ranking_popup {
    width: 280px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}

.export_ranking-btn {
    line-height: 40px;
    border-radius: 20px;
}

.export_ranking-btn::after {
    border: none;
}

</style>
