<template>
    <view class="page bg-background">
        <template v-if="!loading">
            <template v-if="list.length">
                <view class="top-bar">
                    <view class="search-bar bg-white">
                        <view class="search-bar-search-icon flex-all-center">
                            <uni-icons type="search" size="18" color="#bbbec4"/>
                        </view>
                        <view class="search-input-container">
                            <input type="text" class="search-input" v-model="keyword"
                                   placeholder="输入点位名称搜索" maxlength="30"/>
                        </view>
                        <view v-if="keyword" class="search-bar-clear-icon flex-all-center"
                              @click="keyword = ''">
                            <uni-icons type="clear" size="18" color="#bbbec4"/>
                        </view>
                    </view>

                    <view class="flex-kai bg-white">
                        <view class="color-sub p10 font14">共{{ list.length }}个点位</view>
                        <view @click="toAddOrEdit(false)" class="color-primary p10">
                            <text>添加点位</text>
                            <uni-icons type="forward" color="#2d8cf0"/>
                        </view>
                    </view>
                </view>


                <view class="font14 p10" style="color: #e19898;">
                    提示: 最后一个点位，请设置里程最小值与最大值一致，比如终点为25000，则最后一个点位的里程设置为
                    最小值(25000) 最大值(25000)。
                </view>

                <view class="list">
                    <view class="item bg-white" v-for="item in searchList" :key="item.id">
                        <view style="padding-bottom: 10px;">
                            <view class="color-title pb5">{{ item.name }}</view>
                            <view class="color-content font14">
                                里程: {{ item.min_num }}(不含) - {{ item.max_num }}(含)
                            </view>
                            <view class="color-content font14" 
                                  v-if="activity_detail.rank_set && activity_detail.rank_set.redpack">
                                红包余额: {{ item.redpack_amount }}元
                            </view>
                            <view class="color-content font14">排序: {{ item.sort_num }}</view>
                        </view>
                        <view class="tools-bar flex-kai">
                            <view class="flex-row">
                                <view v-if="select" class="color-primary font14 pt5 pr10"
                                      hover-class="navigator-hover" @click="selectPoint(item)">
                                    选择点位
                                </view>
                                <template v-else>

                                    <navigator
                                        class="color-primary font14 pt5 pr10"
                                        v-if="activity_detail.rank_set && activity_detail.rank_set.redpack"
                                        :url="'/pages/wallet/redpack_list?is_admin=1&id=' + id + '&point_id=' + item.id"
                                    >
                                        红包记录
                                    </navigator>
                                </template>
                            </view>
                            <view class="flex-row">
                                <view @click="toAddOrEdit(item.id)" class="edit">
                                    <text class="iconfont icon-edit color-sub font20"></text>
                                </view>
                                <view class="delete" @click="deleteItem(item.id)">
                                    <text class="iconfont icon-delete color-sub font20"></text>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </template>

            <view v-if="!list.length" class="text-center" style="padding-top: 15vh;">
                <text class="iconfont icon-map color-border" style="font-size: 100px;"></text>
                <view class="color-sub">该活动下暂无点位</view>
                <view class="flex-all-center pt15">
                    <view class="add-team-btn color-white bg-primary text-center"
                          @click="toAddOrEdit(false)">创建点位
                    </view>
                </view>
            </view>
        </template>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'

export default {
    data() {
        return {
            id: '',
            loading: true,
            list: [],
            activity_detail: {},
            keyword: '',
            select: false
        }
    },

    computed: {
        searchList() {
            if (!this.keyword) return this.list
            return this.list.filter(item => item.name.includes(this.keyword))
        }
    },


    onLoad(e) {
        this.$uni.showLoading()

        this.id = e.id
        if (e.select) this.select = true

        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err.errTitle || '提示'})
            }

            this.getActivityDetail()
        })
    },
    
    onPullDownRefresh() {
        if (this.loading) return uni.stopPullDownRefresh()
        this.getListInit().finally(() => uni.stopPullDownRefresh())
    },
    
    methods: {
        async getActivityDetail() {
            let activity_detail = app.globalData['activity_detail']

            if (!activity_detail) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.id
                    }
                })

                activity_detail = res.data.active_details

            }

            this.activity_detail = activity_detail
            await this.getListInit()
        },

        async getListInit() {
            this.loading = true
            this.$uni.showLoading()
            this.list = []
            await this.getList()
            uni.hideLoading()
            this.loading = false
        },

        async getList() {
            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace res.data.map_point_list */
            
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_map_point_list',
                data: {
                    active_id: this.id
                }
            })

            this.list = res?.data?.map_point_list || []
        },

        toAddOrEdit(id) {
            let url = `add_or_edit_point?active_id=${this.id}`
            if (id) url += `&point_id=${id}`
            this.$uni.navigateTo(url)
        },


        selectPoint(item) {
            this.getOpenerEventChannel().emit('selectPoint', item)
            this.$uni.navigateBack()
        },


        deleteItem(id) {
            this.deleteConfirm([id])
        },

        async deleteConfirm(ids) {
            const res = await this.$uni.showModal(`确定删除${ids.length > 1 ? '选中的' : '该'}点位?`, {
                confirmText: '删除',
                cancelColor: '#80848f',
                confirmColor: '#ed3f14',
            })
            if (res.confirm) await this.deleteAjax(ids)
        },

        async deleteAjax(ids) {
            this.$uni.showLoading('删除中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/del_map_point',
                data: {
                    active_id: this.id,
                    ids: ids.join(',')
                }
            })
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '删除失败')

            this.list = this.list.filter(item => !ids.find(v => item.id === v))
            this.$uni.showToast('已删除')
        },

    }
}
</script>

<style scoped lang="scss">
.page {
    min-height: 100vh;
    padding-bottom: 10px;
    padding-top: 102px;
    box-sizing: border-box;
}

.top-bar {
    position: fixed;
    z-index: 99;
    top: 0;
    left: 0;
    width: 100%;
    box-sizing: border-box;
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .top-bar {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }
}
/* #endif */

.search-bar {
    position: relative;
    padding: 10px 10px 0;

    $input-height: 40px;

    .search-bar-search-icon, .search-bar-clear-icon {
        position: absolute;
        top: 10px;
        width: $input-height;
        height: $input-height;
    }

    .search-bar-search-icon {
        left: 10px;
    }

    .search-bar-clear-icon {
        right: 10px;
    }

    .search-input-container {
        padding: 0 $input-height;
        background-color: #f8f8f8;
        border-radius: calc(#{$input-height} / 2);

        .search-input {
            width: 100%;
            height: $input-height;
            line-height: $input-height;
        }
    }
}

.item {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
}

.tools-bar {
    border-top: 1px solid #eee;
    padding-top: 10px;
}

.edit, .delete {
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 50%;
}

.edit {
    border: 1px solid #eee;
    margin-right: 10px;
}

.delete {
    border: 1px solid #eee;
}

.add-team-btn {
    width: 200px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
}
</style>
