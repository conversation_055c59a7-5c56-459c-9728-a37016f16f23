<template>
    <view class="page bg-background">
        <view class="text-center p10 bg-white"></view>
        <view class="blacklist">
            <view class="user-item bg-white flex-row" v-for="item in blacklist" :key="item.id">
                <view v-if="item.headimg" class="pr10">
                    <image class="headimg" :src="item.headimg" mode="aspectFill"/>
                </view>
                <view style="width: 100%;">
                    <view class="color-title" :class="{'pt5': item.headimg}">
                        {{ item.nickname || '未知用户' }}
                    </view>
                    <view class="clear clearfix" style="padding-top: 2px;">
                        <view class="fr color-light-primary font14" @click="deBlacklist(item)">
                            移出黑名单
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <view v-if="!loading && !blacklist.length" class="text-center" style="padding-top: 20vh;">
            <text class="iconfont icon-blacklist color-divider" style="font-size: 100px;"></text>
            <view class="color-sub font14">暂无黑名单用户</view>
        </view>

        <view v-if="loading" class="text-center pb10">
            <view v-if="current_page === 1" style="padding-top: 20vh;"></view>
            <load-ani/>
            <view v-if="current_page === 1" class="color-sub font14 pt10">黑名单加载中</view>
        </view>
    </view>
</template>

<script>
import xwy_api from "@/utils/api/xwy_api";

export default {
    data() {
        return {
            loading: true,
            blacklist: [],
            current_page: 1,
            is_last_page: false,
            total: 0
        }
    },

    onLoad(params) {
        this.id = params.id
        this.getBlacklist()
    },

    onReachBottom() {
        if (this.loading || this.is_last_page) return
        this.current_page++
        this.getBlacklist()
    },

    methods: {
        blacklistDataInit() {
            this.blacklist = []
            this.is_last_page = false
            this.total = 0
        },

        async getBlacklist() {
            if (this.current_page === 1) this.blacklistDataInit()
            this.loading = true
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.adminUser/black_user_list',
                data: {
                    active_id: this.id,
                    page: this.current_page,
                    perpage: 20
                }
            })
            this.loading = false
            const res_data = res?.data?.list
            if (!res_data) {
                this.is_last_page = true
                return
            }

            if (res_data.data?.length) this.blacklist = this.blacklist.concat(res_data.data)
            this.is_last_page = res_data.is_lastpage
            this.total = res_data.total
        },

        async deBlacklist(item) {
            const tips = `确定将${item.nickname || '用户'}移出黑名单？`
            const modal = await this.$uni.showModal(tips, {showCancel: true})
            if (!modal.confirm) return

            this.$uni.showLoading('正在移出黑名单...')
            const res = await xwy_api.deleteRecords(76, item.id)
            uni.hideLoading()
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '移出黑名单失败')
            this.$uni.showToast('移出黑名单成功', 'success')

            this.load_page = 1
            await this.getBlacklist()
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 15px;
}
.user-item {
    margin: 15px;
    padding: 10px;
    border-radius: 5px;

    .headimg {
        width: 50px;
        min-width: 50px;
        height: 50px;
        border-radius: 50%;
        display: block;
    }
}
</style>
