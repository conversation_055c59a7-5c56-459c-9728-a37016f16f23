<template>
    <view class="page">
        <view v-if="success" class="save-success">
            <view class="icon text-center">
                <uni-icons type="checkbox-filled" size="100" color="#19be6b"/>
                <view class="color-content font16">活动{{ id ? '修改' : '创建' }}成功！</view>
            </view>
            <view class="flex-all-center">
                <button class="bg-success color-white" style="width: 200px;" @click="lookMyActivityList">
                    查看我的活动
                </button>
            </view>
        </view>


        <view v-else>

            <scroll-view
                class="scroll-view bg-white"
                :scroll-into-view="'scroll-into-view-' + type_id"
                scroll-with-animation="true"
                scroll-x="true"
            >
                <view class="flex-row">
                    <view
                        :id="'scroll-into-view-' + item.id"
                        v-for="(item, index) in type_list"
                        :key="index"
                        class="scroll-view-item color-content"
                        :class="{'active-type': item.id === type_id}"
                        @click="type_id = item.id"
                    >{{ item.title }}
                    </view>
                </view>
            </scroll-view>

            <add-activity-tips ref="add_activity_tips"/>

            <view class="form">
                <template v-if="type_id === 1">
                    <view class="form-item">
                        <view class="top color-content">
                            <text>活动名称</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="text" v-model="name" placeholder="请输入活动名称"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">主办方</view>
                        <view class="bottom font16">
                            <input class="input" v-model="organizer" placeholder="请输入主办方单位名称"/>
                        </view>
                    </view>

                    <view v-if="!id" class="form-item">
                        <view class="top color-content">
                            <text>手机号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="number" maxlength="11" v-model="tel"
                                   placeholder="请输入真实手机号"/>
                        </view>
                    </view>

                    <view v-if="!id" class="form-item">
                        <view class="top color-content">
                            <text>微信号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" v-model="wechat_num" placeholder="请输入真实微信号"/>
                        </view>
                    </view>


                    <view class="form-item">
                        <view class="top color-content">活动开始时间</view>
                        <view class="bottom font16">
                            <view class="view" style="padding: 0;">
                                <uni-datetime-picker type="datetime" v-model="startDay" end="2038-12-31"
                                                     :border="false" :clear-icon="false"/>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">活动结束时间</view>
                        <view class="bottom font16">
                            <view class="view" style="padding: 0;">
                                <uni-datetime-picker type="datetime" v-model="endDay" end="2038-12-31"
                                                     :border="false" :clear-icon="false"/>
                            </view>
                        </view>
                    </view>


                    <view v-if="!time_period_step && !is_simple" class="form-item">
                        <view class="top color-content">步数兑换比例</view>
                        <view class="bottom font16">

                            <input class="input" type="digit" maxlength="7" v-model="exchange_rate"
                                   :placeholder="`兑换比例,多少步兑换1${unit || '步'},不填默认10步兑换1${unit || '步'}`"/>
                        </view>
                    </view>

                    <template v-if="open_business_kilometers">
                        <view class="form-item">
                            <view class="top color-content">出差里程兑换比例</view>
                            <view class="bottom font16">

                                <input class="input" type="digit" maxlength="7" v-model="business_trip.rate"
                                       :placeholder="`多少公里兑换1${unit || '里'},不填默认1公里兑换1${unit || '里'}`"/>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">省内出差里程兑换比例</view>
                            <view class="bottom font16">

                                <input class="input" type="digit" maxlength="7"
                                       v-model="business_trip.province_rate"
                                       :placeholder="`多少公里兑换1${unit || '里'},不填默认使用“出差里程兑换比例”`"/>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">省外出差里程兑换比例</view>
                            <view class="bottom font16">

                                <input class="input" type="digit" maxlength="7"
                                       v-model="business_trip.country_rate"
                                       :placeholder="`多少公里兑换1${unit || '里'},不填默认使用“出差里程兑换比例”`"/>
                            </view>
                        </view>
                    </template>

                    <template v-if="rank_set['run_sport']">
                        <view class="form-item">
                            <view class="top color-content">跑步运动里程兑换比例</view>
                            <view class="bottom font16 flex-row pt5 pb5">
                                <view class="inline-text">跑步</view>
                                <view style="max-width: 80px; padding: 0 2px;">
                                    <uni-easyinput type="number" :maxlength="5"
                                                   v-model="run_trace.run_kilo"/>
                                </view>
                                <view class="inline-text">公里 兑换里程</view>
                                <view style="max-width: 80px; padding: 0 2px;">
                                    <uni-easyinput type="number" :maxlength="5"
                                                   v-model="run_trace.exchange_step"/>
                                </view>
                                <view class="inline-text">{{ unit }}</view>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">
                                <view>
                                    <text>跑步运动配速限制</text>
                                    <text class="color-sub font14 pl5">(单位: 秒)</text>
                                </view>
                                <view class="color-sub font12">
                                    提交跑步运动记录时, 如配速低于设置配速, 无法提交
                                    (正常人跑步，跑一公里所需要的时间为333秒，即5.5分钟)
                                </view>
                            </view>
                            <view class="bottom font16">
                                <input class="input" type="number" v-model="pace_limit"
                                       placeholder="请输入配速限制"/>
                            </view>
                        </view>
                    </template>


                    <view v-if="!time_period_step && !is_simple" class="form-item">
                        <view class="top color-content">里程单位</view>
                        <view class="bottom font16">
                            <input class="input" v-model="unit" placeholder="里程单位,不填默认单位为:里"/>
                        </view>
                    </view>

                    <view v-if="!news_id" class="form-item">
                        <view class="top color-content">活动说明</view>
                        <view class="bottom font16">
                            <textarea class="textarea" maxlength="-1" auto-height="true" v-model="content"
                                      placeholder="请输入活动说明"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <view>活动说明文章</view>
                            <view class="font12 color-sub">
                                绑定文章后，活动说明将会显示文章内容，不会显示上面填写的活动说明
                            </view>
                        </view>
                        <view class="bottom font16">
                            <view class="flex-kai" @click="toSelNews('content')">
                                <view class="view">
                                    <view v-if="news_id">{{ news_title || news_id }}</view>
                                    <view v-else class="color-sub">选择文章</view>
                                </view>
                                <view class="flex-all-center">
                                    <view v-if="news_id" class="color-sub font12" style="width: 30px;"
                                          @click.stop="deleteNews('content')">解绑
                                    </view>
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>
                </template>

                <template v-if="type_id === 2">
                    <view class="form-item">
                        <view class="top color-title">
                            <view>
                                <text>活动主图</text>
                                <text class="font12 color-sub pl5">(设置活动缩略图及活动详情顶部图片)</text>
                            </view>
                            <view class="font12 color-sub">图片尺寸建议: 750*430</view>
                        </view>
                        <view style="padding-top: 5px;">
                            <view class="image-view" v-if="thumb">

                                <image class="image-item" :src="thumb" mode="aspectFill"
                                       @click="previewImage([thumb])"/>
                                <view class="del-image-item" @click.stop="thumb = ''">
                                    <uni-icons type="closeempty" color="#e20f04"/>
                                </view>
                            </view>


                            <navigator v-else class="add-image text-center"
                                       :url="'/pages/other/image_upload_or_select?key=thumb&active_id=' + (id || '')">
                                <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                            </navigator>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-title">
                            <text>活动页面开屏大图</text>
                            <view class="font12 color-sub">图片尺寸建议: 780*1387</view>
                        </view>
                        <view style="padding-top: 5px;">
                            <view class="image-view" v-if="screen_pic">

                                <image class="image-item" :src="screen_pic" mode="aspectFill"
                                       @click="previewImage([screen_pic])"/>
                                <view class="del-image-item" @click.stop="screen_pic = ''">
                                    <uni-icons type="closeempty" color="#e20f04"/>
                                </view>
                            </view>
                            <view v-else class="add-image text-center" @click="setScreenPic">
                                <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                            </view>

                        </view>
                    </view>


                    <view v-if="screen_pic" class="form-item">
                        <view class="top color-content">开屏大图进入活动倒计时</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    :range="screen_pic_count_down_options"
                                    :value="screen_pic_count_down_options.findIndex(v => v.value === screen_pic_count_down)"
                                    range-key="title"
                                    @change="screen_pic_count_down = screen_pic_count_down_options[$event.detail.value].value"
                                >
                                    {{ screen_pic_count_down_options.find(v => v.value === screen_pic_count_down).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-if="screen_pic" class="form-item">
                        <view class="top color-content">
                            <view>开屏大图进入活动按钮</view>
                            <view class="color-sub font12">
                                按钮显示文字及颜色不可修改。如需自定义按钮文字及颜色，请自行在图片上增加，并选择不显示按钮。
                            </view>
                        </view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    :range="['显示', '不显示']"
                                    :value="screen_pic_buttom_show === 1 ? 0 : 1"
                                    @change="screen_pic_buttom_show = $event.detail.value ===  '0' ? 1 : 0"
                                >
                                    {{ screen_pic_buttom_show === 1 ? '显示' : '不显示' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-title">
                            <text>排行榜顶部轮播图</text>
                            <view class="font12 color-sub">图片尺寸建议: 640*272</view>
                        </view>
                        <view style="padding-top: 5px;">
                            <view class="flex-row flex-wrap">
                                <view class="top-rank-banner-item" v-for="(item, index) in top_rank_banner"
                                      :key="index">
                                    <image :src="item" mode="aspectFill"
                                           @click="previewImage(top_rank_banner, item)"/>
                                    <view class="del-image-item"
                                          @click.stop="top_rank_banner.splice(index, 1)">
                                        <uni-icons type="closeempty" color="#e20f04"/>
                                    </view>
                                </view>
                                <view v-if="top_rank_banner.length < top_rank_banner_max_count"
                                      class="add-image text-center" @click="addTopRankBanner">
                                    <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                                </view>
                            </view>
                        </view>
                    </view>
                </template>


                <template v-if="type_id === 3">
                    <view class="form-item">
                        <view class="top color-content">活动参与方式</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    :range="enter_types_opt"
                                    :value="enter_types_index"
                                    range-key="title"
                                    @change="enter_typesChange($event)"
                                >
                                    {{ enter_types_opt[enter_types_index].title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-if="enter_types === 2" class="form-item">
                        <view class="top color-content">
                            <text>活动密码</text>
                            <text v-if="!have_password" class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">

                            <input class="input" v-model="password"
                                   :placeholder="have_password ? '已设置密码，无需修改密码不用填写' : '请输入活动密码 最少3位字符'"/>
                        </view>
                    </view>

                    <template v-if="rank_set.batch_import && enter_types === 4">
                        <view class="form-item">
                            <view class="top color-content">参与活动账号输入提示</view>
                            <view class="bottom font16">
                                <input class="input" placeholder="默认为“请输入账号”"
                                       v-model="batch_import_label.username"/>
                            </view>
                        </view>
                        <view class="form-item">
                            <view class="top color-content">参与活动密码输入提示</view>
                            <view class="bottom font16">
                                <input class="input" placeholder="默认为“请输入密码”"
                                       v-model="batch_import_label.password"/>
                            </view>
                        </view>
                    </template>


                    <must-submit-set :must-submit.sync="must_submit"/>


                    <view class="form-item">
                        <view class="top color-content">参与活动信息填写后是否允许修改</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    :range="must_submit_not_modifiable_options"
                                    :value="must_submit_not_modifiable_options.findIndex(v => v.value === must_submit_not_modifiable)"
                                    range-key="title"
                                    @change="must_submit_not_modifiable = must_submit_not_modifiable_options[$event.detail.value].value"
                                >
                                    {{ must_submit_not_modifiable_options.find(v => v.value === must_submit_not_modifiable).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>


                    <view class="form-item">
                        <view class="top color-content">活动城市限制功能</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    :range="ip_set_options"
                                    :value="ip_set_options.findIndex(v => v.value === ip_set.open)"
                                    range-key="title"
                                    @change="ip_set.open = ip_set_options[$event.detail.value].value"
                                >
                                    {{ ip_set_options.find(v => v.value === ip_set.open).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-if="ip_set.open" class="form-item">
                        <view class="top color-content">
                            <text>允许参加活动的城市</text>
                            <text class="color-sub font12 pl5">(如: 深圳市)</text>
                        </view>
                        <view class="bottom font16">
                            <view class="flex-row ptm5"
                                  v-for="(item, index) in ip_set.ip_city.length" :key="item">
                                <uni-easyinput v-model="ip_set.ip_city[index]"/>
                                <view class="font14 color-error pl5" style="line-height: 36px;"
                                      @click="ip_set.ip_city.splice(index, 1)">删除
                                </view>
                            </view>

                            <view class="flex-all-center">
                                <view class="font14 color-primary ptm5" @click="ip_set.ip_city.push('')">
                                    添加城市
                                </view>
                            </view>
                        </view>
                    </view>


                    <view class="form-item">
                        <view class="top color-content">活动位置距离限制功能</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    :range="location_distance_limit_options"
                                    :value="location_distance_limit_options.findIndex(v => v.value === join_location_distance_limit.open)"
                                    range-key="title"
                                    @change="join_location_distance_limit.open = location_distance_limit_options[$event.detail.value].value"
                                >
                                    {{ location_distance_limit_options.find(v => v.value === join_location_distance_limit.open).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-show="join_location_distance_limit.open">
                        <view class="form-item">
                            <view class="top color-content">
                                <text>活动中心点位置</text>
                                <text class="color-error font16"> *</text>
                            </view>
                            <view class="bottom font16">
                                <view class="flex-kai" @click="chooseJoinLocation">
                                    <view class="view">
                                        <view v-if="join_location_distance_limit.location.name">
                                            {{ join_location_distance_limit.location.name }}
                                        </view>
                                        <view v-else class="color-sub">请选择活动中心点位置</view>
                                    </view>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">
                                <view>
                                    <text>活动位置距离限制</text>
                                    <text class="color-sub font14 pl5">(单位: 公里)</text>
                                </view>
                                <view class="color-sub font12">设置在活动中心点位置多少公里内才能参与活动
                                </view>
                            </view>
                            <view class="bottom font16">
                                <input class="input" type="number" placeholder="请输入活动位置距离限制"
                                       v-model="join_location_distance_limit.distance"/>
                            </view>
                        </view>
                    </view>

                </template>

                <template v-if="type_id === 5">
                    <view class="form-item">
                        <view class="top color-content">地图类型</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker class="view" :range="map_type_list" range-key="title"
                                        :value="map_types_index" @change="mapTypesChange($event)">
                                    {{ map_types_title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-show="map_types !== 101">
                        <view class="form-item">
                            <view class="top color-content">
                                <view>进入地图横幅滚动提示</view>
                                <view class="color-sub font14">已兑换用户进入地图页面顶部横幅滚动提示</view>
                            </view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker class="view" :range="['开启', '关闭']"
                                            :value="map_page_top_scroll_tips_hide"
                                            @change="map_page_top_scroll_tips_hide = Number($event.detail.value)">
                                        {{ map_page_top_scroll_tips_hide ? '关闭' : '开启' }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">
                                <view>进入地图提示弹窗</view>
                                <view class="color-sub font14">
                                    未参与活动或未兑换用户进入地图页面时会弹出该提示窗
                                </view>
                            </view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker class="view" :range="['关闭', '开启']"
                                            :value="map_page_no_exchange_tips.open"
                                            @change="map_page_no_exchange_tips.open = Number($event.detail.value)">
                                        {{ map_page_no_exchange_tips.open ? '开启' : '关闭' }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view v-show="map_page_no_exchange_tips.open" class="form-item">
                            <view class="top color-content">进入地图弹窗提示语</view>
                            <view class="bottom pt10 pb10">
                                <map-page-no-exchange-tips-set type="text"
                                                               :set.sync="map_page_no_exchange_tips"/>
                            </view>
                        </view>

                        <view v-show="map_page_no_exchange_tips.open" class="form-item">
                            <view class="top color-content">进入地图弹窗样式设置</view>
                            <view class="bottom pt10 pb10">
                                <map-page-no-exchange-tips-set type="style"
                                                               :set.sync="map_page_no_exchange_tips"/>
                            </view>
                        </view>
                    </view>


                    <template v-if="map_types === 101">
                        <view class="form-item">
                            <view class="top color-content">统计指定时间段步数功能</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker class="view" :range="['关闭', '开启']" :value="time_period_step"
                                            @change="timePeriodStepChange($event)">
                                        {{ time_period_step ? '开启' : '关闭' }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>


                        <step-unlock-picture-fragments-set :set.sync="step_unlock_picture_fragments"
                                                           :unit="unit"/>
                    </template>

                    <template v-if="map_types !== 101 && map_types !== 102">
                        <view class="form-item">
                            <view class="top color-content">地图展示方式</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker class="view" :range="satellite_options" range-key="title"
                                            :value="satellite_options.findIndex(v => v.value === satellite)"
                                            @change="pickerChange($event, 'satellite')">
                                        {{ satellite_options.find(v => v.value === satellite).title || '' }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <template v-if="map_types === 100">
                            <view class="form-item">
                                <view class="top color-content">地图默认缩放等级</view>
                                <view class="bottom font16">

                                    <slider :min="3" :max="20" :value="scale" :step=".1"
                                            activeColor="#2f8cec" block-color="#2f8cec" show-value="true"
                                            @change="scale = $event.detail.value">
                                    </slider>
                                </view>
                            </view>

                            <view class="form-item">
                                <view class="top color-content">地图最小缩放等级</view>
                                <view class="bottom font16">

                                    <slider :min="3" :max="20" :value="min_scale" :step=".1"
                                            activeColor="#2f8cec" block-color="#2f8cec" show-value="true"
                                            @change="min_scale = $event.detail.value">
                                    </slider>
                                </view>
                            </view>

                            <view class="form-item">
                                <view class="top color-content">地图最大缩放等级</view>
                                <view class="bottom font16">

                                    <slider :min="3" :max="20" :value="max_scale" :step=".1"
                                            activeColor="#2f8cec" block-color="#2f8cec" show-value="true"
                                            @change="max_scale = $event.detail.value">
                                    </slider>
                                </view>
                            </view>
                        </template>

                        <view v-if="this.map_types !== 100 && this.map_types !== 101" class="form-item">
                            <view class="top color-content">
                                <view>地图缩放隐藏点位信息设置</view>
                                <view class="color-sub font12">地图缩放比例小于设置值时，点位信息将隐藏</view>
                            </view>
                            <view class="bottom font16">

                                <slider :min="3" :max="20" :value="hide_point_scale" :step=".1"
                                        activeColor="#2f8cec" block-color="#2f8cec" show-value="true"
                                        @change="hide_point_scale = Number($event.detail.value)">
                                </slider>
                            </view>
                        </view>

                        <view v-if="map_types === 100" class="form-item">
                            <view class="top color-content">地图中心点坐标</view>
                            <view class="bottom font16">
                                <view class="flex-kai" @click="chooseLocation">
                                    <view class="view">
                                        <view v-if="map_center_point_lat && map_center_point_lng">
                                            {{ map_center_point_lat }},{{ map_center_point_lng }}
                                        </view>
                                        <view v-else class="color-sub">请选择地图中心点坐标</view>
                                    </view>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>


                        <view class="form-item">
                            <view class="top color-content">进入地图自动聚焦到用户当前到达的点位</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker class="view" :range="['否', '是']" data-isnumber="1"
                                            :value="user_point_map_center"
                                            @change="pickerChange($event, 'user_point_map_center')">
                                        {{ user_point_map_center ? '是' : '否' }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">地图旋转角度</view>
                            <view class="bottom font16">
                                <input class="input" type="number" maxlength="3" v-model="map_rotate"
                                       placeholder="请输入0-360之间的整数"/>
                            </view>
                        </view>


                        <view class="form-item">
                            <view class="top color-content">点位是否连线显示</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker
                                        class="view"
                                        :range="polyline_hide_opt"
                                        :value="polyline_hide_opt.findIndex(v => v.value === polyline_hide)"
                                        range-key="title"
                                        @change="polyline_hide = polyline_hide_opt[$event.detail.value].value"
                                    >
                                        {{ polyline_hide_opt.find(v => v.value === polyline_hide).title }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view v-if="!polyline_hide" class="form-item">
                            <view class="top color-content">是否显示用户在两个点位之间的位置</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker class="view" mode="selector" :range="['否', '是']"
                                            :value="map_between_two_points"
                                            @change="map_between_two_points = Number($event.detail.value)">
                                        {{ map_between_two_points ? '是' : '否' }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </template>

                    <template v-if="rank_set['mapPointDetailsButtonDIY'] && map_types !== 101">
                        <view class="form-item">
                            <view class="top color-content">地图页面底部展示方式</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker class="view" mode="selector"
                                            :range="['默认展示兑换详情及兑换按钮', '只展示自定义兑换按钮']"
                                            :value="map_page_just_show_diy_exchange_button"
                                            @change="map_page_just_show_diy_exchange_button = Number($event.detail.value)">
                                        {{ map_page_just_show_diy_exchange_button ? '只展示自定义兑换按钮' : '默认展示兑换详情及兑换按钮' }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view v-if="map_page_just_show_diy_exchange_button" class="form-item">
                            <view class="top color-content">自定义兑换按钮设置</view>
                            <view class="bottom">
                                <map-page-diy-exchange-button-set
                                    :set.sync="map_page_diy_exchange_button_set"/>
                            </view>
                        </view>
                    </template>
                </template>

                <template v-if="type_id === 6">
                    <view class="form-item">
                        <view class="top color-content">报名开始时间</view>
                        <view class="bottom font16">
                            <view class="view" style="padding: 0;">
                                <uni-datetime-picker type="datetime" v-model="submit.begin" end="2038-12-31"
                                                     :border="false" :clear-icon="true"/>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">报名截止时间</view>
                        <view class="bottom font16">
                            <view class="view" style="padding: 0;">
                                <uni-datetime-picker type="datetime" v-model="submit.end" end="2038-12-31"
                                                     :border="false" :clear-icon="true"/>
                            </view>
                        </view>
                    </view>


                    <template v-if="!is_simple">
                        <view class="form-item">
                            <view class="top color-content">每天步数兑换上限</view>
                            <view class="bottom font16">
                                <input class="input" type="number" maxlength="8" v-model="max_num"
                                       placeholder="单个用户每天兑换步数上限,不填默认不设上限"/>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">最低兑换步数</view>
                            <view class="bottom font16">
                                <input class="input" type="number" maxlength="8" v-model="min_num"
                                       placeholder="低于设置的步数无法兑换,0或不填代表不限制"/>
                            </view>
                        </view>
                        <view class="form-item">
                            <view class="top color-content">每天活动兑换开始时间</view>
                            <view class="bottom font16">
                                <view class="flex-kai">
                                    <view class="view">
                                        <picker-time v-if="exchange_start_time" :text="exchange_start_time"
                                                     :time.sync="exchange_start_time"/>
                                        <picker-time v-else :time.sync="exchange_start_time"></picker-time>
                                    </view>
                                    <view class="flex-all-center">
                                        <view v-if="exchange_start_time" class="color-sub font12"
                                              style="width: 90px;" @click.stop="exchange_start_time = ''">
                                            设为 00:00:00
                                        </view>
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">每天活动兑换截止时间</view>
                            <view class="bottom font16">
                                <view class="flex-kai">
                                    <view class="view">
                                        <picker-time v-if="exchange_end_time" :time.sync="exchange_end_time"
                                                     :text="exchange_end_time"
                                                     @timeChange="timeChange($event, 'exchange_end_time')"/>
                                        <picker-time v-else :time.sync="exchange_end_time" text="23:59:59"
                                                     @timeChange="timeChange($event, 'exchange_end_time')"/>
                                    </view>
                                    <view class="flex-all-center">
                                        <view v-if="exchange_end_time" class="color-sub font12"
                                              style="width: 90px;" @click.stop="setExchangeEndTimeNoLimit">
                                            设为 23:59:59
                                        </view>
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </template>

                    <view v-if="rank_set['person_finished_percent'] || rank_set['personal_finished_days']"
                          class="form-item">
                        <view class="top color-content">每日达标步数</view>
                        <view class="bottom font16">
                            <input class="input" type="number" v-model="finished_step_num"
                                   placeholder="请输入每日达标步数"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">进入活动自动兑换步数</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    :range="['关闭', '开启']"
                                    :value="enter_activity_auto_exchange"
                                    data-isnumber="1"
                                    @change="pickerChange($event, 'enter_activity_auto_exchange')"
                                >
                                    {{ enter_activity_auto_exchange ? '开启' : '关闭'}}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <view>是否开启简约版活动</view>
                            <view class="color-sub font12">
                                <view>开启后, 用户进入活动自动兑换步数, 活动页面底部显示排行榜数据。</view>
                                <view>
                                    <text>注意: 开启简约版活动后，用户将</text>
                                    <text v-if="map_types !== 101">无法查看地图点位，</text>
                                    <text>无法使用</text>
                                    <text v-if="rank_set.exam_open">答题、</text>
                                    <text v-if="rank_set['lottery_open']">抽奖、</text>
                                    <text v-if="rank_set['medal_open']">勋章、</text>
                                    <text>证书功能</text>
                                </view>
                            </view>
                        </view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    :range="['关闭简约版活动', '开启简约版活动']"
                                    :value="is_set_simple"
                                    data-isnumber="1"
                                    @change="pickerChange($event, 'is_set_simple')"
                                >
                                    {{ is_set_simple === 0 ? '关闭简约版活动' : '开启简约版活动' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">是否开启队伍功能</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    :range="['开启队伍功能', '关闭队伍功能']"
                                    :value="closed_team"
                                    data-isnumber="1"
                                    @change="pickerChange($event, 'closed_team')"
                                >
                                    {{ closed_team === 0 ? '开启队伍功能' : '关闭队伍功能' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <template v-if="closed_team === 0">
                        <view class="form-item">
                            <view class="top color-content">报名时队伍是否必选</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker class="view" :range="['非必选', '必选']" :value="team_required"
                                            data-isnumber="1"
                                            @change="pickerChange($event, 'team_required')">
                                        {{ team_required === 1 ? '' : '非' }}必选
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">是否允许用户切换队伍</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker
                                        class="view"
                                        :range="['不允许用户切换队伍', '允许用户切换队伍']"
                                        :value="allow_change_team"
                                        data-isnumber="1"
                                        @change="pickerChange($event, 'allow_change_team')"
                                    >
                                        {{ allow_change_team === 1 ? '' : '不' }}允许用户切换队伍
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">是否允许用户创建队伍</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker
                                        class="view"
                                        :range="user_create_team_opt"
                                        :value="user_create_team_opt.findIndex(v => v.value === user_create_team)"
                                        range-key="title"
                                        @change="user_create_team = user_create_team_opt[$event.detail.value].value"
                                    >
                                        {{ user_create_team_opt.find(v => v.value === user_create_team).title }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view v-if="rank_set['admin_set_team_limit_person'] && user_create_team"
                              class="form-item">
                            <view class="top color-content">
                                <view>人数上限</view>
                                <view class="color-sub font12">设置用户创建队伍的人数上限，队伍人数不能超过设置的人数上限。不填或填0则不限制队伍人数。</view>
                            </view>
                            <view class="bottom font16">
                                <input class="input" type="number" maxlength="5" v-model="team_limit_person"
                                       placeholder="请输入队伍的人数上限"/>
                            </view>
                        </view>


                        <view class="form-item">
                            <view class="top color-content">是否加入了队伍才能查看该队排行榜</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker
                                        class="view"
                                        :range="just_look_my_team_top_opt"
                                        :value="just_look_my_team_top_opt.findIndex(v => v.value === just_look_my_team_top)"
                                        range-key="title"
                                        @change="just_look_my_team_top = just_look_my_team_top_opt[$event.detail.value].value"
                                    >
                                        {{ just_look_my_team_top_opt.find(v => v.value === just_look_my_team_top).title }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </template>

                    <view class="form-item">
                        <view class="top color-content">活动类型</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker class="view" :range="['个人健步走', '队伍接力赛']" data-isnumber="1"
                                        :value="runOneByOne.open"
                                        @change="pickerChange($event, 'runOneByOne_open')">
                                    {{ runOneByOne.open === 1 ? '队伍接力赛' : '个人健步走' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <template v-if="runOneByOne.open">
                        <view class="form-item">
                            <view class="top color-content">
                                <text>每一棒接力赛的队员的兑换里程</text>
                                <text class="font14 color-sub pl5">(单位: {{ unit || '步' }})</text>
                            </view>
                            <view class="bottom font16">
                                <input class="input" type="number" maxlength="8"
                                       v-model="runOneByOne.per_person_exchange_kilo"/>
                            </view>
                        </view>
                        <view class="form-item">
                            <view class="top color-content">
                                <text>每个队伍接力棒数</text>
                                <text class="font14 color-sub pl5">
                                    (最多可设置99棒，请确保 接力棒数*每一棒里程 大于或等于 地图终点里程)
                                </text>
                            </view>
                            <view class="bottom font16">
                                <input class="input" type="number" maxlength="2"
                                       v-model="runOneByOne.all_person"/>
                            </view>
                        </view>
                        <view class="form-item">
                            <view class="top color-content">接力顺序设置方式</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker
                                        class="view"
                                        :range="['只能管理员来设置接力顺序', '参赛用户可以选择自己的接力顺序']"
                                        :value="runOneByOne.user_select_team"
                                        data-isnumber="1"
                                        @change="pickerChange($event, 'runOneByOne_user_select_team')"
                                    >
                                        {{ runOneByOne.user_select_team === 1 ? '参赛用户可以选择自己的接力顺序' : '只能管理员来设置接力顺序' }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>
                        <!--<view class="form-item">
                            <view class="top color-content">
                                <text>每名队员最大出场次数</text>
                                <text class="font14 color-sub pl5">(0或不填不限制次数)</text>
                            </view>
                            <view class="bottom font16">
                                <input
                                    class="input"
                                    type="number"
                                    maxlength="8"
                                    v-model="runOneByOne.user_select_max"
                                />
                            </view>
                        </view>-->
                    </template>


                    <template v-if="rank_set.active_details_notice">
                        <view class="form-item">
                            <view class="top color-content">
                                <view>是否开启活动阅读须知</view>
                                <view class="color-sub font14">开启后，用户需要阅读并同意才能进入活动</view>
                            </view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker
                                        class="view"
                                        :range="active_details_notice_open_opt"
                                        :value="active_details_notice_open_opt.findIndex(v => v.value === active_details_notice.open)"
                                        range-key="title"
                                        @change="active_details_notice.open = active_details_notice_open_opt[$event.detail.value].value"
                                    >
                                        {{ active_details_notice_open_opt.find(v => v.value === active_details_notice.open).title }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view v-if="active_details_notice.open" class="form-item">
                            <view class="top color-content">阅读须知文章</view>
                            <view class="bottom font16">
                                <view class="flex-kai" @click="toSelNews('notice')">
                                    <view class="view">
                                        <view v-if="active_details_notice.news_id">
                                            {{ active_details_notice.news_title || active_details_notice.news_id }}
                                        </view>
                                        <view v-else class="color-sub">选择文章</view>
                                    </view>
                                    <view class="flex-all-center">
                                        <view
                                            v-if="active_details_notice.news_id"
                                            class="color-sub font12"
                                            style="width: 30px;"
                                            @click.stop="deleteNews('notice')"
                                        >解绑
                                        </view>
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view v-if="active_details_notice.open" class="form-item">
                            <view class="top color-content">阅读须知确定按钮文字</view>
                            <view class="bottom font16">
                                <input class="input" type="text" maxlength="10"
                                       v-model="active_details_notice.confirm_text"
                                       placeholder="不填默认为“确定”"/>
                            </view>
                        </view>
                    </template>


                    <view v-if="open_mp3" class="form-item">
                        <view class="top color-content">
                            <text>背景音乐</text>
                            <text class="color-sub font12 pl5">(格式: m4a、wav、mp3、aac)</text>
                        </view>
                        <view class="bottom font16">
                            <textarea class="textarea" maxlength="-1" auto-height="true" v-model="audio_src"
                                      placeholder="进入活动会自动播放背景音乐"/>
                        </view>
                    </view>


                    <!--兑换前需要答题-->
                    <template v-if="exam_open && rank_set.mustExamThenExchangeStep && map_types === 101">
                        <view class="form-item">
                            <view class="top color-content">兑换前需要答题 / 步数答题</view>
                            <view class="bottom font16">
                                <view class="flex-kai">
                                    <picker 
                                        class="view" 
                                        :range="['关闭', '开启']" 
                                        :value="must_exam_daily.open"
                                        @change="must_exam_daily.open = Number($event.detail.value)"
                                    >
                                        {{ must_exam_daily.open === 1 ? '开启' : '关闭' }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">
                                <view>答题类型</view>
                                <view class="color-sub font12">
                                    兑换前需要答题: 每天需要答题并答题分数不低于指定分数才能兑换步数。
                                </view>
                                <view class="color-sub font12">步数答题: 每天兑换指定步数可获得答题机会。</view>
                            </view>
                            <view class="bottom font16">
                                <view class="flex-kai">
                                    <picker
                                        class="view"
                                        :range="['兑换前需要答题', '步数答题']"
                                        :value="must_exam_daily.types"
                                        @change="must_exam_daily.types = Number($event.detail.value)"
                                    >
                                        {{ must_exam_daily.types === 1 ? '步数答题' : '兑换前需要答题' }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>
                        
                        <template v-if="must_exam_daily.open">
                            <view class="form-item">
                                <view class="top color-content">兑换前答题绑定的考卷</view>
                                <view class="bottom font16">
                                    <view class="flex-kai" @click="toSelExam('must_exam_daily')">
                                        <view class="view">
                                            <view v-if="must_exam_daily.exam_id">
                                                {{ must_exam_daily.exam_name || must_exam_daily.exam_id }}
                                            </view>
                                            <view v-else class="color-sub">绑定考卷</view>
                                        </view>
                                        <view class="flex-all-center">
                                            <uni-icons type="forward" color="#80848f"/>
                                        </view>
                                    </view>
                                </view>
                            </view>
                            
                            <view v-if="!must_exam_daily.types" class="form-item">
                                <view class="top color-content">
                                    <view>兑换前答题分数限制</view>
                                    <view class="color-sub font12">答题分数大于等于设置分数才算答题成功，不填或填0不限制。</view>
                                </view>
                                <view class="bottom font16">
                                    <input class="input" type="digit" v-model="must_exam_daily.score"
                                           placeholder="请输入分数限制"/>
                                </view>
                            </view>

                            <template v-if="must_exam_daily.types === 1">
                                <view class="form-item">
                                    <view class="top color-content">
                                        <view>答题步数设置</view>
                                        <view class="color-sub font12">
                                            每兑换多少步可获得1次答题机会。如设置1000，则兑换1000步可获得1次答题机会，兑换2000步获得一次答题机会...。
                                        </view>
                                    </view>
                                    <view class="bottom font16">
                                        <input class="input" type="number" v-model="must_exam_daily.per_step"
                                               placeholder="请输入步数"/>
                                    </view>
                                </view>

                                <view class="form-item">
                                    <view class="top color-content">每天答题奖励的积分上限</view>
                                    <view class="bottom font16">
                                        <input class="input" type="digit"
                                               v-model="must_exam_daily.max_integral"
                                               placeholder="请输入积分上限"/>
                                    </view>
                                </view>
                            </template>
                        </template>
                    </template>


                    <template v-if="exam_open && rank_set['mapPointMustGoToArticle']">
                        <view class="form-item">
                            <view class="top color-content">是否需要阅读点位知识文章后才能答题</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker
                                        class="view"
                                        :range="['无需阅读点位知识即可答题', '需要阅读点位知识才能答']"
                                        :value="answer_need_read.open"
                                        data-isnumber="1"
                                        @change="answer_need_read.open = Number($event.detail.value)"
                                    >
                                        {{ answer_need_read.open ? '需要阅读点位知识才能答' : '无需阅读点位知识即可答题' }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view v-if="answer_need_read.open" class="form-item">
                            <view class="top color-content">
                                <text>需要阅读点位知识文章多少秒才能去答题</text>
                                <text class="color-sub font12 pl5">(单位: 秒)</text>
                            </view>
                            <view class="bottom font16">
                                <input class="input" type="number" maxlength="4"
                                       v-model="answer_need_read.seconds" placeholder="请输入时间"/>
                            </view>
                        </view>
                    </template>

                    <template v-if="exam_open && map_types !== 101">
                        <view class="form-item">
                            <view class="top color-content">是否必须答题才能兑换下一点位</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker
                                        class="view"
                                        :range="['无需答题即可兑换下一点位', '需要答题才能兑换下一点位']"
                                        :value="must_exam_open"
                                        data-isnumber="1"
                                        @change="pickerChange($event, 'must_exam_open')"
                                    >
                                        <template v-if="must_exam_open && must_exam_open === 1">
                                            需要答题才能兑换下一点位
                                        </template>
                                        <template v-else>无需答题即可兑换下一点位</template>
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view v-if="must_exam_open && must_exam_open === 1" class="form-item">
                            <view class="top color-content">需要答题达到多少分才能兑换下一点位</view>
                            <view class="bottom font16">
                                <input class="input" type="digit" maxlength="5" v-model="must_exam_score"
                                       placeholder="不限制分数填0或不填即可"/>
                            </view>
                        </view>
                    </template>

                    <template v-if="reward_step_exam_conf">
                        <view class="form-item">
                            <view class="top color-content">是否开启答题奖励步数</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker
                                        class="view"
                                        :range="['开启答题奖励步数', '关闭答题奖励步数']"
                                        :value="reward_step_by_exam.open ? reward_step_by_exam.open - 1 : 1"
                                        @change="examAddStepChange($event)"
                                    >
                                        {{ reward_step_by_exam.open && reward_step_by_exam.open === 1 ? '开启答题奖励步数' : '关闭答题奖励步数' }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <template v-if="reward_step_by_exam.open && reward_step_by_exam.open === 1">
                            <view class="form-item">
                                <view class="top color-content">答题奖励步数方式</view>
                                <view class="bottom font16">
                                    <view class="flex-kai">

                                        <picker
                                            class="view"
                                            :range="['按照分数奖励步数', '按照题目正确率奖励步数']"
                                            :value="reward_step_by_exam.types ? reward_step_by_exam.types - 1 : 1"
                                            @change="examAddStepTypeChange($event)"
                                        >
                                            {{ reward_step_by_exam.types && reward_step_by_exam.types === 1 ? '按照分数奖励步数' : '按照题目正确率奖励步数' }}
                                        </picker>
                                        <view class="flex-all-center">
                                            <uni-icons type="forward" color="#80848f"/>
                                        </view>
                                    </view>
                                </view>
                            </view>

                            <template v-if="reward_step_by_exam.types === 1">
                                <view class="form-item">
                                    <view class="top color-content">答题奖励规则</view>
                                    <view class="bottom font16">
                                        <view class="flex-row ptm5">
                                            <view>每</view>
                                            <view class="reward_step_by_exam_input" style="width: 80px;">
                                                <input class="text-center" type="digit" maxlength="5"
                                                       v-model="reward_step_by_exam.score_one_point"/>
                                            </view>
                                            <view>分，奖励</view>
                                            <view class="reward_step_by_exam_input" style="width: 80px;">
                                                <input class="text-center" type="digit" maxlength="7"
                                                       v-model="reward_step_by_exam.reward_step"/>
                                            </view>
                                            <view>步</view>
                                        </view>
                                    </view>
                                </view>
                                <view class="form-item">
                                    <view class="top color-content">
                                        <text>答题奖励分数限制</text>
                                        <text class="font12 color-sub pl5">
                                            (达到设置分数才奖励,0或不填则不限制)
                                        </text>
                                    </view>
                                    <view class="bottom font16">
                                        <input class="input" type="digit" maxlength="6"
                                               v-model="reward_step_by_exam.min_score"
                                               placeholder="如:设置60分,则答题成绩达到60分才奖励"/>
                                    </view>
                                </view>
                            </template>

                            <template v-if="reward_step_by_exam.types === 2">
                                <view class="form-item">
                                    <view class="top color-content">答题奖励规则</view>
                                    <view class="bottom font16">
                                        <view class="flex-row ptm5">
                                            <view>每答对1题，奖励</view>
                                            <view class="reward_step_by_exam_input" style="width: 100px;">
                                                <input class="text-center" type="digit" maxlength="7"
                                                       v-model="reward_step_by_exam.reward_step"/>
                                            </view>
                                            <view>步</view>
                                        </view>
                                    </view>
                                </view>
                            </template>
                        </template>
                    </template>


                    <view v-if="exam_open && map_types !== 101" class="form-item">
                        <view class="top color-content">答题列表展示方式</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    :range="['图片展示(一行两个)', '列表展示(一行一个)']"
                                    :value="exam_show_type ? exam_show_type - 1 : 0"
                                    @change="exam_show_type = Number($event.detail.value) + 1"
                                >
                                    <!--
                                    文字展示(一行一个)改为列表展示(一行一个)
                                    鲜繁 for 2023-07-04 企业微信内部群
                                    -->
                                    {{ exam_show_type && exam_show_type === 2 ? '列表展示(一行一个)' : '图片展示(一行两个)' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>


                    <template v-if="rank_set['redpack']">
                        <view class="form-item">
                            <view class="top color-content">红包设置</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker
                                        class="view"
                                        :range="redpack_open_opt"
                                        :value="redpack_open_opt.findIndex(v => v.value === redpack_rules.open)"
                                        range-key="title"
                                        @change="redpack_rules.open = redpack_open_opt[$event.detail.value].value"
                                    >
                                        {{ redpack_open_opt.find(v => v.value === redpack_rules.open).title }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">红包领取方式</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker
                                        class="view"
                                        :range="redpack_type_opt"
                                        :value="redpack_type_opt.findIndex(v => v.value === redpack_rules.types)"
                                        range-key="title"
                                        @change="redpack_rules.types = redpack_type_opt[$event.detail.value].value"
                                    >
                                        {{ redpack_type_opt.find(v => v.value === redpack_rules.types).title }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view v-if="redpack_rules.types === 2" class="form-item">
                            <view class="top color-content">
                                <text>兑换时获得红包的概率</text>
                                <text class="color-error font16"> *</text>
                                <text class="font12 color-sub pl5">(百分比)</text>
                            </view>
                            <view class="bottom font16">
                                <input class="input" type="digit" maxlength="5"
                                       v-model="redpack_rules.rand_num"
                                       placeholder="请输入0到100以内的数字"/>
                            </view>
                        </view>

                        <view v-if="redpack_rules.types === 2" class="form-item">
                            <view class="top color-content">
                                <text>单个红包金额</text>
                                <text class="color-error font16"> *</text>
                                <text class="pl5 color-sub font12">(单位: 元。)</text>
                            </view>
                            <view class="bottom font16 pt5 pb5">
                                <view class="flex-row">
                                    <view style="width: 100px;">
                                        <uni-easyinput type="digit" maxlength="6"
                                                       v-model="redpack_rules.amount.min"/>
                                    </view>
                                    <view class="pl10 pr10 color-sub flex-all-center">-</view>
                                    <view style="width: 100px;">
                                        <uni-easyinput type="digit" maxlength="6"
                                                       v-model="redpack_rules.amount.max"/>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </template>


                    <template v-if="rank_set['gift_goods']">
                        <view class="form-item">
                            <view class="top color-content">
                                <text>积分商城积分单位名称</text>
                                <text class="font12 color-sub pl5">(默认: {{ default_integral_unit }})</text>
                            </view>
                            <view class="bottom font16">

                                <input class="input" type="text" v-model="integral_set.unit"
                                       :placeholder="'不填默认为 ' + default_integral_unit"/>
                            </view>
                        </view>
                        <view class="form-item">
                            <view class="top color-content">
                                <text>步数兑换奖励{{ integralUnit }}规则</text>
                                <text class="font12 color-sub pl5">(不填或填0不奖励)</text>
                            </view>
                            <view class="bottom font16">
                                <view class="flex-row ptm5">
                                    <view>每兑换</view>
                                    <view class="reward_step_by_exam_input" style="width: 100px;">
                                        <input class="text-center" type="digit" maxlength="8"
                                               v-model="integral_set.exchange_step"/>
                                    </view>
                                    <view>步奖励1{{ integralUnit }}</view>
                                </view>
                            </view>
                        </view>
                        <view v-if="exam_open" class="form-item">
                            <view class="top color-content">
                                <view>答题奖励{{ integralUnit }}规则</view>
                                <view class="font12 color-sub pl5">
                                    分数不填或填0不奖励，奖励{{ integralUnit }}不填或填0默认为1
                                </view>
                            </view>
                            <view class="bottom font16">
                                <view class="flex-row ptm5">
                                    <view>每获得</view>
                                    <view class="reward_step_by_exam_input" style="width: 80px;">
                                        <input class="text-center" type="digit" maxlength="5"
                                               v-model="integral_set.exam_reward"/>
                                    </view>
                                    <view>分奖励</view>
                                    <view class="reward_step_by_exam_input" style="width: 80px;">
                                        <input class="text-center" type="digit" maxlength="5"
                                               v-model="integral_set.exam_reward_num"/>
                                    </view>
                                    <view>{{ integralUnit }}</view>
                                </view>
                            </view>
                        </view>
                    </template>

                    <view v-if="rank_set.registerRewardIntegral" class="form-item">
                        <view class="top color-content">报名奖励{{ integralUnit }}</view>
                        <view class="bottom font16">
                            <input class="input" type="number" placeholder="不填或填0不奖励"
                                   v-model="integral_set.register_integral" maxlength="8"/>
                        </view>
                    </view>

                    <view v-if="rank_set['together_pic']" class="form-item">
                        <view class="top color-content">
                            <text>合成一张照片奖励{{ integralUnit }}</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="number" placeholder="不填或填0不奖励"
                                   v-model="integral_set.together_pic_reward_num" maxlength="8"/>
                        </view>
                    </view>

                    <template v-if="rank_set['morning_daily_sign']">
                        <view class="form-item">
                            <view class="top color-content">每日签到类型设置</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker
                                        class="view"
                                        mode="selector"
                                        :range="dailySignTypesOptions"
                                        :value="dailySignTypesOptions.findIndex(v => v.value === daily_sign.types)"
                                        range-key="title"
                                        @change="daily_sign.types = dailySignTypesOptions[$event.detail.value].value"
                                    >
                                        {{ dailySignTypesOptions.find(v => v.value === daily_sign.types).title }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">
                                <view>每日自动签到</view>
                                <view class="color-sub font12">开启后, 用户每日进入活动自动签到, 并且活动页面不显示每日签到入口</view>
                            </view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker class="view" :range="['关闭', '开启']"
                                            :value="daily_sign.auto_sign"
                                            @change="daily_sign.auto_sign = Number($event.detail.value)">
                                        {{ daily_sign.auto_sign ? '开启' : '关闭' }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view v-if="daily_sign.types === 1" class="form-item">
                            <view class="top color-content">每日签到奖励{{ integralUnit }}数</view>
                            <view class="bottom font16">
                                <input class="input" type="number" v-model="daily_sign.integral"
                                       maxlength="8"/>
                            </view>
                        </view>

                        <view v-if="daily_sign.types === 2" class="form-item">
                            <view class="top color-content">周期签到奖励{{ integralUnit }}数</view>
                            <view class="bottom font16">
                                <view class="p5 mb5 flex-row"
                                      style="border: 1px solid #eee; border-radius: 5px;"
                                      v-for="(item, index) in daily_sign.circle_set" :key="index">
                                    <view class="flex-row pr5">
                                        <view class="pr5 font14 color-content" style="line-height: 36px;">
                                            第{{ index + 1 }}天奖励:
                                        </view>
                                        <uni-easyinput v-model="item.integral" type="number" :maxlength="8"
                                                       style="width: 120px;"/>
                                        <view class="pl5 font14 color-content" style="line-height: 36px;">
                                            {{ integralUnit }}
                                        </view>
                                    </view>
                                    <view
                                        v-if="daily_sign.circle_set.length > 1"
                                        class="font14 color-error"
                                        style="line-height: 36px;"
                                        @click="daily_sign.circle_set.splice(index, 1)"
                                    >删除
                                    </view>
                                </view>

                                <view
                                    v-if="daily_sign.circle_set.length < 7"
                                    class="color-primary font14 pb5"
                                    style="display: inline-block;"
                                    @click="daily_sign.circle_set.push({integral: ''})"
                                >添加
                                </view>
                            </view>
                        </view>
                    </template>

                    <template v-if="rank_set.reading_reward">
                        <view class="form-item">
                            <view class="top color-content">阅读文章奖励{{ integralUnit }}功能</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker class="view" :range="['关闭奖励', '开启奖励']"
                                            :value="reading_reward.open"
                                            @change="reading_reward.open = Number($event.detail.value)">
                                        {{ reading_reward.open ? '开启奖励' : '关闭奖励' }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content pb10">阅读文章奖励{{ integralUnit }}规则设置</view>
                            <view class="bottom font16">
                                <view class="p5 mb5" style="border: 1px solid #eee; border-radius: 5px;"
                                      v-for="(item, index) in reading_reward.rules" :key="index" >
                                    <view
                                        class="flex-row mb5 pl10 pr10"
                                        style="border: 1px solid #eee; border-radius: 5px; height: 36px; line-height: 36px;"
                                        hover-class="navigator-hover"
                                        @click="changeReadingRewardItemNewsCategory(index)"
                                    >
                                        <view style="width: 100%">
                                            <view v-if="item.category_id" class="color-title ellipsis">
                                                {{ item.name || item.category_id }}
                                            </view>
                                            <view v-else class="color-sub font14">请选择需要奖励的文章分类
                                            </view>
                                        </view>
                                        <view class="text-right" style="min-width: 30px;">
                                            <uni-icons type="forward" color="#bbbec4"/>
                                        </view>
                                    </view>
                                    <view class="flex-row">
                                        <view class="font14 color-content pr5" style="line-height: 36px;">
                                            阅读
                                        </view>
                                        <uni-easyinput v-model="item.seconds" type="number" :maxlength="8"
                                                       style="width: 60px;"/>
                                        <view class="font14 color-content plr5" style="line-height: 36px;">
                                            秒奖励
                                        </view>
                                        <uni-easyinput v-model="item.integral.min" type="number"
                                                       :maxlength="8" style="width: 60px;"/>
                                        <view class="color-content plr5" style="line-height: 36px;">-</view>
                                        <uni-easyinput v-model="item.integral.max" type="number"
                                                       :maxlength="8" style="width: 60px;"/>
                                        <view class="font14 color-content pl5" style="line-height: 36px;">
                                            {{ integralUnit }}
                                        </view>
                                    </view>
                                    <view class="clearfix clear">
                                        <view class="fr color-error font14 p10" hover-class="navigator-hover"
                                              @click="reading_reward.rules.splice(index, 1)">
                                            删除这个奖励规则
                                        </view>
                                    </view>
                                </view>
                                <view class="clearfix clear">
                                    <view
                                        class="fr color-light-primary font14 p10"
                                        hover-class="navigator-hover"
                                        @click="reading_reward.rules.push({name: '', category_id: '', seconds: '', integral: {min: '', max: ''}})"
                                    >添加奖励规则
                                    </view>
                                </view>
                            </view>
                        </view>
                    </template>

                    <view v-if="rank_set['mapPointMustGoToArticle']" class="form-item">
                        <view class="top color-content">
                            <view>点位知识弹窗显示文章详情</view>
                            <view class="font12 color-sub">
                                设置为不显示，点位知识弹窗不显示文章详情，需要进入到文章详情页面才能查看点位知识文章。
                            </view>
                        </view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker class="view" :range="['显示', '不显示']"
                                        :value="point_news_to_details_page_show"
                                        @change="point_news_to_details_page_show = Number($event.detail.value)">
                                    {{ point_news_to_details_page_show ? '不' : '' }}显示
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <template
                        v-if="rank_set && (rank_set['article_bind_exam'] || rank_set['reward_step_by_reading'])">
                        <view class="form-item">
                            <view class="top color-content">学习专区文章分类</view>
                            <view class="bottom font16">
                                <view class="flex-kai" @click="bindStudyNewsCategory">
                                    <view class="view">
                                        <view v-if="study_news_set.news_category.id">
                                            {{ study_news_set.news_category.title || study_news_set.news_category.id }}
                                        </view>
                                        <view v-else class="color-sub">选择文章分类</view>
                                    </view>
                                    <view class="flex-all-center">
                                        <view v-if="study_news_set.news_category.id" class="color-sub font12"
                                              style="width: 30px;" @click.stop="deBindStudyNewsCategory">
                                            解绑
                                        </view>
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">学习专区规则设置</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker
                                        class="view"
                                        mode="selector"
                                        :range="['按文章顺序学习', '按文章学习时间学习']"
                                        :value="study_news_set.study_type"
                                        @change="study_news_set.study_type = Number($event.detail.value)"
                                    >
                                        按文章{{ study_news_set.study_type === 1 ? '学习时间' : '顺序' }}学习
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view v-if="rank_set['reward_step_by_reading']" class="form-item">
                            <view class="top color-content">
                                <text>学习奖励步数设置</text>
                                <text class="font12 color-sub pl5">(不填或填0不奖励)</text>
                            </view>
                            <view class="bottom font16">
                                <view class="flex-row ptm5">
                                    <view>随机奖励</view>
                                    <view class="reward_step_by_exam_input" style="width: 80px;">
                                        <input class="text-center" type="Number" maxlength="5"
                                               v-model="study_news_set.reward_step.min"/>
                                    </view>
                                    <view>-</view>
                                    <view class="reward_step_by_exam_input" style="width: 80px;">
                                        <input class="text-center" type="Number" maxlength="5"
                                               v-model="study_news_set.reward_step.max"/>
                                    </view>
                                    <view>步</view>
                                </view>
                            </view>
                        </view>
                    </template>


                    <view class="form-item">
                        <view class="top color-content">活动规则</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="activity_rules_hide_opt"
                                    :value="activity_rules_hide_opt.findIndex(v => v.value === activity_rules_hide)"
                                    range-key="title"
                                    @change="activity_rules_hide = activity_rules_hide_opt[$event.detail.value].value"
                                >
                                    {{ activity_rules_hide_opt.find(v => v.value === activity_rules_hide).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">意见反馈</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="close_feedback_opt"
                                    :value="close_feedback_opt.findIndex(v => v.value === close_feedback)"
                                    range-key="title"
                                    @change="close_feedback = close_feedback_opt[$event.detail.value].value"
                                >
                                    {{ close_feedback_opt.find(v => v.value === close_feedback).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <template v-if="map_types !== 101">
                        <view class="form-item">
                            <view class="top color-content">到达终点弹窗</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker
                                        class="view"
                                        mode="selector"
                                        :range="close_end_popup_opt"
                                        :value="close_end_popup_opt.findIndex(v => v.value === close_end_popup)"
                                        range-key="title"
                                        @change="close_end_popup = close_end_popup_opt[$event.detail.value].value"
                                    >
                                        {{ close_end_popup_opt.find(v => v.value === close_end_popup).title }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">到达终点是否可继续兑换</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker
                                        class="view"
                                        mode="selector"
                                        :range="['到达终点可继续兑换', '到达终点不可兑换']"
                                        :value="arrive_then_end"
                                        data-isnumber="1"
                                        @change="pickerChange($event, 'arrive_then_end')"
                                    >
                                        {{ arrive_then_end === 0 ? '到达终点可继续兑换' : '到达终点不可兑换' }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>


                        <template v-if="rank_set['multi_top_rank_finishedAndScore']">
                            <view class="form-item">
                                <view class="top color-content">到达终点分数奖励设置</view>
                                <view class="bottom font16 pt10 pb10"
                                      @click="$refs.scoreAndFinishedSetPopup.open()">
                                    <view v-if="score_and_finished_have_set">
                                        最先到达终点获得 {{ score_and_finished.max }}分,
                                        每后一名减 {{ score_and_finished.minus_score }}分,
                                        最多减至 {{ score_and_finished.min }}分
                                    </view>

                                    <view class="clear clearfix">
                                        <view class="fr color-light-primary">
                                            <text>{{ score_and_finished_have_set ? '修改' : '设置' }}</text>
                                            <uni-icons type="forward" color="#5cadff"/>
                                        </view>
                                    </view>
                                </view>
                            </view>

                            <score-and-finished-set-popup ref="scoreAndFinishedSetPopup"
                                                          :set.sync="score_and_finished"/>
                        </template>


                        <view class="form-item">
                            <view class="top color-content">活动证书</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker
                                        class="view"
                                        mode="selector"
                                        :range="certificate_close_opt"
                                        :value="certificate_close_opt.findIndex(v => v.value === certificate_close)"
                                        range-key="title"
                                        @change="certificate_close = certificate_close_opt[$event.detail.value].value"
                                    >
                                        {{ certificate_close_opt.find(v => v.value === certificate_close).title }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </template>

                    <!--碎片解锁，即使设置了无需题图，也可以设置到达终点还能继续兑换-->
                    <view v-if="map_types === 101 && step_unlock_picture_fragments.open"
                          class="form-item">
                        <view class="top color-content">到达终点是否可继续兑换</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="['到达终点可继续兑换', '到达终点不可兑换']"
                                    :value="arrive_then_end"
                                    data-isnumber="1"
                                    @change="pickerChange($event, 'arrive_then_end')"
                                >
                                    {{ arrive_then_end === 0 ? '到达终点可继续兑换' : '到达终点不可兑换' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>


                    <view class="form-item">
                        <view class="top color-content">是否已参加活动用户才能查看排行榜</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="['否，所有用户都能查看排行榜', '是，只有已参加活动的用户才能查看排行榜']"
                                    :value="only_join_user_look_ranklist"
                                    data-isnumber="1"
                                    @change="pickerChange($event, 'only_join_user_look_ranklist')"
                                >
                                    {{ only_join_user_look_ranklist === 1 ? '是，只有已参加活动的用户才能查看排行榜' : '否，所有用户都能查看排行榜' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-if="rank_set && rank_set.closed_AD" class="form-item">
                        <view class="top color-content">排行榜点赞功能</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="closed_likes_opt"
                                    :value="closed_likes_opt.findIndex(v => v.value === closed_likes)"
                                    range-key="title"
                                    @change="closed_likes = closed_likes_opt[$event.detail.value].value"
                                >
                                    {{ closed_likes_opt.find(v => v.value === closed_likes).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-if="!closed_likes" class="form-item">
                        <view class="top color-content">点赞限制</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="['所有用户都能点赞', '只有参与了活动的用户才能点赞']"
                                    :value="like_limit || 0"
                                    @change="like_limit = Number($event.detail.value)"
                                >
                                    {{ like_limit ? '只有参与了活动的用户才能点赞' : '所有用户都能点赞' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-if="rank_set && rank_set['light_map_point_reward_step']" class="form-item">
                        <view class="top color-content">点亮城市奖励步数规则</view>
                        <view class="bottom font16">
                            <view class="flex-row ptm5">
                                <view>每点亮1个城市，奖励</view>
                                <view class="reward_step_by_exam_input" style="width: 100px;">
                                    <input class="text-center" type="number" maxlength="7"
                                           v-model="light_city_reward_step"/>
                                </view>
                                <view>步</view>
                            </view>
                        </view>
                    </view>


                    <view class="form-item">
                        <view class="top color-content">活动详情页面图标扩展</view>
                        <view class="bottom font16 pt5 pb5">
                            <view
                                class="flex-kai pl10 pr10"
                                style="border: 1px solid #eee; border-radius: 5px; margin-bottom: 5px;"
                                v-for="(item, index) in detail_icon_list"
                                :key="index"
                            >
                                <view>
                                    <view style="padding: 5px 0;" @click="selIcon(index)">
                                        <text class="color-content pr5">显示图标:</text>
                                        <text :class="'iconfont font28 color-primary ' + item.icon"></text>
                                        <text class="pl5 color-sub font12">(点击更换)</text>
                                    </view>

                                    <picker :range="detail_icon_conf" range-key="title"
                                            @change="detailIconItemChange($event, index)">
                                        <view style="padding: 5px 0;">
                                            <text class="pr5 color-content">跳转类型:</text>
                                            <text>{{ detail_icon_conf[item.type].title }}</text>
                                        </view>
                                    </picker>
                                    <view class="flex-row" style="padding: 5px 0;">
                                        <text class="pr5 color-content">显示文字:</text>
                                        <input type="text" v-model="item.title" placeholder="6个字以内"
                                               style="border-bottom: 1px solid #eee;"/>
                                    </view>
                                    <view style="padding: 5px 0;" @click="bindNewsOrCategory(index)">
                                        <text class="pr5 color-content">
                                            绑定{{ item.type === 0 ? '文章' : '分类' }}:
                                        </text>
                                        <template>
                                            <text v-if="item.id">{{ item.name }}</text>
                                            <text class="color-sub" v-else>
                                                请选择{{ item.type === 0 ? '文章' : '分类' }}
                                            </text>
                                        </template>
                                    </view>
                                </view>
                                <view class="pt5" @click="delDetailIcon(index)">
                                    <text class="iconfont icon-delete color-error font20"></text>
                                </view>
                            </view>

                            <view class="color-primary pt5 pb5" @click="addDetailIcon">添加扩展</view>
                        </view>
                    </view>


                    <view v-if="ai_sport_type_list.length" class="form-item">
                        <view class="top color-content">AI运动</view>
                        <view class="bottom font16 pt5 pb5">
                            <view
                                class="flex-kai pl10 pr10"
                                style="border: 1px solid #eee; border-radius: 5px; margin-bottom: 5px;"
                                v-for="(item, index) in ai_sport_list"
                                :key="index"
                            >
                                <view>

                                    <picker :range="ai_sport_type_list" range-key="name"
                                            @change="aiSportTypeChange($event, index)">
                                        <view style="padding: 5px 0;">
                                            <text class="pr5 color-content">运动类型:</text>
                                            <text>{{ item.name || '请选择运动类型' }}</text>
                                        </view>
                                    </picker>
                                    <view class="flex-row" style="padding: 5px 0;">
                                        <text class="pr5 color-content">显示文字:</text>
                                        <input v-model="item.title" placeholder="可不填"
                                               style="border-bottom: 1px solid #eee;"/>
                                    </view>

                                    <view>
                                        <view class="color-sub font14">展示logo图</view>
                                        <view>
                                            <view class="image-view" v-if="item.logo">
                                                <image
                                                    class="image-item"
                                                    :src="item.logo"
                                                    mode="aspectFill"
                                                    @click="previewImage([item.logo])"
                                                    style="width: 200px; height: 120px;"
                                                />
                                                <view class="del-image-item"
                                                      @click.stop="delAiSportItemLogo(index)">
                                                    <uni-icons type="closeempty" color="#e20f04"/>
                                                </view>
                                            </view>
                                            <view v-else class="add-image text-center"
                                                  @click="addAiSportImg(index)">
                                                <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                                            </view>

                                        </view>
                                    </view>
                                </view>
                                <view class="pt5" @click="delAiSportItem(index)">
                                    <text class="iconfont icon-delete color-error font20"></text>
                                </view>
                            </view>

                            <view class="color-primary pt5 pb5" @click="addAiSport">添加运动类型</view>
                        </view>
                    </view>

                    <template v-if="rank_set['open_sport_moment']">
                        <view class="form-item">
                            <view class="top color-content">运动圈动态发布限制</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker class="view" :range="['不限制', '报名活动后才能发布运动圈']"
                                            :value="sport_moment_release_limit"
                                            @change="sport_moment_release_limit = Number($event.detail.value)">
                                        {{ sport_moment_release_limit ? '报名活动后才能发布运动圈' : '不限制' }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view v-if="rank_set['limit_sport_moment']" class="form-item limit_sport_moment">
                            <view class="top color-content">
                                <view>运动圈动态发布数量限制</view>
                                <view class="color-sub font14">
                                    左边输入框填写到达的里程数({{ unit || '步' }})，右边输入框填写累计可发布运动圈数量。
                                    如达到10000{{ unit || '步' }}累计获得3条运动圈发布机会，左侧填10000，右侧填3。
                                </view>
                            </view>
                            <view class="bottom font16 pt5 pb5">
                                <view class="p5 mb5 flex-row"
                                      v-for="(item, index) in limit_sport_moment_set_list" :key="index">
                                    <view class="flex-row pr5">
                                        <uni-easyinput v-model="item.distance" type="number" :maxlength="20"
                                                       style="width: 120px;" placeholder="到达里程"/>
                                        <view style="width: 5px;"></view>
                                        <uni-easyinput v-model="item.count" type="number" :maxlength="20"
                                                       style="width: 120px;" placeholder="累计发布数"/>
                                    </view>
                                    <view class="font14 color-error" style="line-height: 36px;"
                                          @click="limit_sport_moment_set_list.splice(index, 1)">
                                        删除
                                    </view>
                                </view>

                                <view class="color-primary font14 pb5" style="display: inline-block;"
                                      @click="limit_sport_moment_set_list.push({distance: '', count: ''})">
                                    添加
                                </view>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">运动圈展示方式</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker
                                        class="view"
                                        mode="selector"
                                        :range="sport_moment_type_opt"
                                        :value="sport_moment_type_opt.findIndex(v => v.value === sport_moment_type)"
                                        range-key="title"
                                        @change="sport_moment_type = sport_moment_type_opt[$event.detail.value].value"
                                    >
                                        {{ sport_moment_type_opt.find(v => v.value === sport_moment_type).title }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </template>


                    <!--排行榜前几名奖励-->
                    <template v-if="rank_set['reward_team_person']">
                        <view v-for="(item, index) in reward_amount.person_team_set" :key="index">
                            <view class="form-item">
                                <view class="top color-content">
                                    {{ item.types === 1 ? '个人' : '队伍' }}昨日排行奖励
                                </view>
                                <view class="bottom font16">
                                    <view class="flex-kai">

                                        <picker class="view" :range="['关闭', '开启']" :value="item.open"
                                                @change="reward_amount.person_team_set[index].open = Number($event.detail.value)">
                                            {{ item.open ? '开启' : '关闭' }}
                                        </picker>
                                        <view class="flex-all-center">
                                            <uni-icons type="forward" color="#80848f"/>
                                        </view>
                                    </view>
                                </view>
                            </view>

                            <view v-if="item.open" class="form-item">
                                <view class="top color-content pb5">
                                    <template v-if="item.types === 1">
                                        <text>个人昨日排行奖励规则</text>
                                        <text class="color-sub font12 pl5">(最低金额不得低于0.3元)</text>
                                    </template>
                                    <template v-if="item.types === 2">
                                        <view>队伍昨日排行奖励规则</view>
                                        <view class="color-sub font12">
                                            队伍内每人获得的红包奖励, 最低金额不得低于0.3元
                                        </view>
                                    </template>
                                </view>
                                <view class="bottom font16 clear clearfix">
                                    <view class="p5 mb5 flex-kai"
                                          v-for="(_item, _index) in item.reward" :key="_index"
                                          style="border: 1px solid #eee; border-radius: 5px;">
                                        <view class="flex-row pr5">
                                            <view class="pr5 font14 color-content"
                                                  style="line-height: 36px;">
                                                第{{ _index + 1 }}名:
                                            </view>
                                            <uni-easyinput v-model="_item.amount.min" type="digit"
                                                           :maxlength="8" style="width: 100px;"/>
                                            <view class="plr5 color-content" style="line-height: 36px;">-
                                            </view>
                                            <uni-easyinput v-model="_item.amount.max" type="digit"
                                                           :maxlength="8" style="width: 100px;"/>
                                            <view class="pl5 font14 color-content"
                                                  style="line-height: 36px;">元
                                            </view>
                                        </view>
                                        <view
                                            v-if="item.reward.length > 1"
                                            class="font14 color-error"
                                            style="line-height: 36px;"
                                            @click="reward_amount.person_team_set[index].reward.splice(index, 1)"
                                        >删除
                                        </view>
                                    </view>

                                    <view class="color-primary font14 p5 fr"
                                          @click="reward_amount.person_team_set[index].reward.push(JSON.parse(JSON.stringify(default_reward_amount_item)))">
                                        添加
                                    </view>
                                </view>
                            </view>
                        </view>
                    </template>
                    <!--排行榜前几名奖励-->

                    <view v-if="rank_set['medal_open']" class="form-item">
                        <view class="top color-content">勋章列表是否显示勋章里程数</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="medal_mileage_hide_options"
                                    :value="medal_mileage_hide_options.findIndex(v => v.value === medal_mileage_hide)"
                                    range-key="title"
                                    @change="medal_mileage_hide = medal_mileage_hide_options[$event.detail.value].value"
                                >
                                    {{ medal_mileage_hide_options.find(v => v.value === medal_mileage_hide).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>


                    <view v-if="rank_set['must_subscribe']" class="form-item">
                        <view class="top color-content">公众号打开活动设置</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker class="view" mode="selector"
                                        :range="official_account_open_options" :value="official_account_open"
                                        @change="official_account_open = Number($event.detail.value)">
                                    {{ official_account_open_options[official_account_open] }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <view>进入活动弹窗提示</view>
                            <view class="font12 color-sub">
                                用户报名后,每次进入活动页面弹窗显示绑定的文章。
                            </view>
                        </view>
                        <view class="bottom font16">
                            <view class="flex-kai" @click="toSelNews('enter_activity_popup_news')">
                                <view class="view">
                                    <view v-if="enter_activity_popup_news.id">
                                        {{ enter_activity_popup_news.title || enter_activity_popup_news.id }}
                                    </view>
                                    <view v-else class="color-sub">选择文章</view>
                                </view>
                                <view class="flex-all-center">
                                    <view v-if="enter_activity_popup_news.id" class="color-sub font12"
                                          style="width: 30px;"
                                          @click.stop="deleteNews('enter_activity_popup_news')">解绑
                                    </view>
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>


                    <view v-if="rank_set['mystery_box']" class="form-item">
                        <view class="top color-content">盲盒抽奖活动绑定</view>
                        <view class="bottom font16">
                            <view class="flex-kai" @click="bindBlindBoxLottery">
                                <view class="view">
                                    <view v-if="blind_box_lottery.lottery_id">
                                        {{ blind_box_lottery.title || blind_box_lottery.lottery_id }}
                                    </view>
                                    <view v-else class="color-sub">选择盲盒抽奖活动</view>
                                </view>
                                <view class="flex-all-center">
                                    <view v-if="blind_box_lottery.lottery_id" class="color-sub font12"
                                          style="width: 30px;" @click.stop="deBindBlindBoxLottery">
                                        解绑
                                    </view>
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-if="id && rank_set['lottery_open'] && rank_set.lottery_add_step_integral"
                          class="form-item">
                        <view class="top color-content">步数抽奖活动绑定</view>
                        <view class="bottom font16">
                            <view class="flex-kai" v-for="(item, index) in step_lottery_list"
                                  :key="item.lottery_id">
                                <view class="view">{{ item.title }}</view>
                                <view class="flex-all-center">
                                    <view class="color-sub font12" style="width: 30px;"
                                          @click="deBindStepLottery(index)">解绑
                                    </view>
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>

                            <view class="flex-all-center">
                                <view class="p10 font14 color-light-primary" @click="bindStepLottery">
                                    绑定步数抽奖活动
                                    <uni-icons type="forward" size="14" color="#5cadff"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-if="rank_set['lotteryForUserIntegral']" class="form-item">
                        <view class="top color-content">每次抽奖消耗的{{ integralUnit }}</view>
                        <view class="bottom font16">

                            <input class="input" type="Number" :placeholder="`请输入${integralUnit}数`"
                                   v-model="lottery_cost_integral"/>
                        </view>
                    </view>

                    <template v-if="rank_set['sport_step_prize_set']">
                        <view class="form-item">
                            <view class="top color-content">答题勋章设置</view>
                            <view class="bottom font16">
                                <exam-medal-set :set.sync="exam_prize_set" :active-id="id"/>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">自定义答题勋章显示图标及名称</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker
                                        class="view"
                                        :range="['使用系统默认图标及名称', '自定义设置图标及名称']"
                                        :value="exam_prize_icon_diy_open"
                                        @change="exam_prize_icon_diy_open = Number($event.detail.value)"
                                    >
                                        {{ exam_prize_icon_diy_open ? '自定义设置图标及名称' : '使用系统默认图标及名称' }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <template v-if="exam_prize_icon_diy_open">
                            <view class="form-item">
                                <view class="top color-title">答题勋章显示图标</view>
                                <view style="padding-top: 5px;">
                                    <view class="image-view" v-if="exam_prize_icon_diy.icon">

                                        <image class="image-item" mode="aspectFill"
                                               :src="exam_prize_icon_diy.icon"
                                               @click="previewImage([exam_prize_icon_diy.icon])"/>
                                        <view class="del-image-item"
                                              @click.stop="exam_prize_icon_diy.icon = ''">
                                            <uni-icons type="closeempty" color="#e20f04"/>
                                        </view>
                                    </view>
                                    <view v-else class="add-image text-center" @click="setExamPrizeIcon">
                                        <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                                    </view>

                                </view>
                            </view>

                            <view class="form-item">
                                <view class="top color-content">答题勋章显示名称</view>
                                <view class="bottom font16">
                                    <input class="input" v-model="exam_prize_icon_diy.name" maxlength="10"
                                           placeholder="请输入答题勋章显示名称"/>
                                </view>
                            </view>

                        </template>
                    </template>


                    <template v-if="rank_set['party_oath']">
                        <view class="form-item">
                            <view class="top color-content">宣誓词记录查看限制</view>
                            <view class="bottom font16">
                                <view class="flex-kai">

                                    <picker
                                        class="view"
                                        mode="selector"
                                        :range="['只能查看自己的记录', '可以查看所有用户记录']"
                                        :value="take_oath_square_open"
                                        @change="take_oath_square_open ^= 1"
                                    >
                                        {{ take_oath_square_open ? '可以查看所有用户记录' : '只能查看自己的记录' }}
                                    </picker>
                                    <view class="flex-all-center">
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-title">宣誓词背景图</view>
                            <view style="padding-top: 5px;">
                                <view class="image-view" v-if="take_oath_bg_img">

                                    <image class="image-item" :src="take_oath_bg_img" mode="aspectFill"
                                           @click="previewImage([take_oath_bg_img])"/>
                                    <view class="del-image-item" @click.stop="take_oath_bg_img = ''">
                                        <uni-icons type="closeempty" color="#e20f04"/>
                                    </view>
                                </view>

                                <navigator
                                    v-else
                                    class="add-image text-center"
                                    :url="`/pages/other/image_upload_or_select?key=take_oath_bg_img&active_id=${id}`"
                                >
                                    <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                                </navigator>

                            </view>
                        </view>

                    </template>

                    <template v-if="rank_set['stepRewardIntegralForMinMax']">
                        <view class="form-item">
                            <view class="top color-content">第一个点位解锁步数</view>
                            <view class="bottom font16">
                                <input class="input" type="Number" placeholder="请输入解锁步数"
                                       v-model="exchange_rules.rushing_set.min_step_num"/>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">第二个点位开始的解锁步数</view>
                            <view class="bottom font16">
                                <input class="input" type="Number" placeholder="请输入解锁步数"
                                       v-model="exchange_rules.rushing_set.per_round_step_num"/>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">每天解锁的点位上限</view>
                            <view class="bottom font16">
                                <input class="input" type="Number" placeholder="请输入上限数"
                                       v-model="exchange_rules.rushing_set.daily_rushed_round_num"/>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">每天解锁的答题上限</view>
                            <view class="bottom font16">
                                <input class="input" type="Number" placeholder="请输入上限数"
                                       v-model="exchange_rules.rushing_set.daily_exam_times"/>
                            </view>
                        </view>


                        <view class="form-item">
                            <view class="top color-content">步数奖励倍数值</view>
                            <view class="bottom font16">
                                <input class="input" type="Number" placeholder="请输入步数"
                                       v-model="exchange_rules.per_step"/>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">步数奖励规则</view>
                            <view class="bottom font16">
                                <unlock-points-daily-list-set :list.sync="exchange_rules.rules_list"
                                                         :unit="integralUnit"/>
                            </view>
                        </view>
                    </template>


                    <active-share-set :active-id="id" :rank-set="rank_set"
                                  :qrcode-logo.sync="qrcode_logo"
                                  :share-image.sync="share_image"
                                  :share-title.sync="share_title"/>
                </template>

            </view>


            <view class="bottom-btn-view bg-white flex-all-center">
                <view class="login-btn color-white text-center font18 bg-primary"
                      :disabled="loading" @click="save">
                    {{ id ? '保存' : '创建活动' }}
                </view>
            </view>
        </view>


    </view>
</template>

<script>
const app = getApp()
import login from '../../../utils/api/login.js'
import xwy_api from '../../../utils/api/xwy_api.js'
import utils from '../../../utils/utils.js'
import base64 from '../../../utils/base64.js'
import {pinyin} from 'pinyin-pro'

import scoreAndFinishedSetPopup from '../components/score-and-finished-set-popup.vue'
import mapPageDiyExchangeButtonSet from "../components/map-page-diy-exchange-button-set.vue"
import mapPageNoExchangeTipsSet from '../components/map-page-no-exchange-tips-set.vue'
import stepUnlockPictureFragmentsSet from '../components/step-unlock-picture-fragments-set.vue'
import examMedalSet from '../components/exam-medal-set.vue'
import unlockPointsDailyListSet from '../components/unlock-points-daily-list-set.vue'

const default_reward_amount_item = {
    name: '', // 第1名 通过数组的下标来设置，不允许手动修改
    amount: {
        min: '',
        max: ''
    }
}


export default {
    components: {
        mapPageDiyExchangeButtonSet,
        scoreAndFinishedSetPopup,
        mapPageNoExchangeTipsSet,
        stepUnlockPictureFragmentsSet,
        examMedalSet,
        unlockPointsDailyListSet
    },

    data() {
        return {
            success: false,
            id: '',
            loading: true,
            type_list: [
                {title: '基本信息', id: 1},
                {title: '活动设置', id: 3},
                {title: '扩展设置', id: 6},
                {title: '地图设置', id: 5},
                {title: '活动图片', id: 2}
            ],

            type_id: 1,
            map_type_list: [
                {type: 1, title: '长征地图'},
                // {type: 2, title: '冬奥会地图'},
                // {type: 3, title: '亚运会地图'},
                {type: 4, title: '中国地图'},
                {type: 100, title: '自定义地图'},
                {type: 101, title: '无需设置地图'}
            ],
            rank_set: {},
            map_types: 101,
            name: '',
            organizer: '',
            content: '',
            tel: '',
            wechat_num: '',
            startDay: utils.getDay(0, true) + ' 00:00:00',
            endDay: utils.getDay(30, true) + ' 23:59:59',
            unit: '步',
            max_num: '',
            min_num: '',
            exchange_rate: 1,
            thumb: '',
            arrive_end_num: '',
            arrive_then_end: 0,
            scale: 5,
            min_scale: 3,
            max_scale: 20,
            hide_point_scale: 3,
            map_center_point_lat: '',
            map_center_point_lng: '',
            screen_pic: '',
            open_mp3: 0,
            audio_src: '',
            enter_types: 1,
            enter_types_opt: [
                {title: '自由报名参与活动', value: 1},
                {title: '需要输入密码才能进入活动', value: 2},
                {title: '报名需要审核通过才能参与活动', value: 3}
            ],
            enter_types_index: 0,
            password: '',
            have_password: false,
            closed_team: 1,
            exchange_end_time: '',
            exchange_start_time: '',
            must_submit: JSON.parse(JSON.stringify(this.xwy_config.defaultMustSubmit)),
            exam_open: 0,
            reward_step_exam_conf: false,
            must_exam_open: 0,
            must_exam_score: '',
            reward_step_by_exam: {
                open: 2,
                types: 1,
                min_score: '',
                score_one_point: '',
                reward_step: ''
            },
            map_rotate: 0,
            satellite: 0,
            satellite_options: [
                {value: 0, title: '2D平面地图'},
                {value: 1, title: '卫星地图'},
                {value: 2, title: '用户自行切换'}
            ],
            user_point_map_center: 0,
            top_rank_banner: [],
            top_rank_banner_max_count: 6,
            allow_change_team: 0,
            team_required: 0,
            detail_icon_conf: [
                {
                    type: 0,
                    title: '文章详情'
                }
            ],
            detail_icon_list: [],
            news_id: '',
            news_title: '',
            ai_sport_type_list: [],
            ai_sport_list: [],
            integral_set: {
                exchange_step: '',
                exam_reward_num: 1,
                exam_reward: '',
                unit: '',
                together_pic_reward_num: '',  // 合成照片奖励的积分数额
                register_integral: '',   // 报名奖励积分
            },
            default_integral_unit: '金币',
            redpack_rules: {
                types: 1,
                rand_num: 50,
                open: 0,
                amount: {
                    min: 1,
                    max: 2
                }
            },
            redpack_type_opt: [
                {value: 1, title: '到达点位可领取红包'},
                {value: 2, title: '兑换成功后领取红包'}
            ],
            redpack_open_opt: [
                {value: 1, title: '开启红包发放'},
                {value: 0, title: '暂停红包发放'}
            ],
            polyline_hide: 0,
            polyline_hide_opt: [
                {value: 0, title: '点位连线显示'},
                {value: 1, title: '点位不连线'}
            ],
            map_between_two_points: 0,
            certificate_close: 0,
            certificate_close_opt: [
                {value: 0, title: '开启活动证书'},
                {value: 1, title: '关闭活动证书'}
            ],
            just_look_my_team_top: 0,
            just_look_my_team_top_opt: [
                {value: 0, title: '非本队队员也能查看队内排行榜'},
                {value: 1, title: '只有本队队员才能查看队内排行榜'}
            ],
            daily_sign: {
                types: 0,
                integral: '',
                circle_set: [],
                auto_sign: 0
            },
            submit: {
                begin: '',
                end: ''
            },
            activity_rules_hide: 0,
            activity_rules_hide_opt: [
                {value: 0, title: '活动页面显示活动规则'},
                {value: 1, title: '活动页面不显示活动规则'}
            ],
            close_feedback: 0,
            close_feedback_opt: [
                {value: 0, title: '开启意见反馈'},
                {value: 1, title: '关闭意见反馈'}
            ],
            user_create_team: 0,
            user_create_team_opt: [
                {value: 0, title: '不允许用户创建队伍'},
                {value: 1, title: '允许用户创建队伍'}
            ],
            // 用户创建爱队伍的人数上限设置
            team_limit_person: '',
            active_details_notice: {
                open: 0,
                news_id: '',
                news_title: '',
                confirm_text: '确定'
            },
            active_details_notice_open_opt: [
                {value: 1, title: '开启'},
                {value: 0, title: '关闭'}
            ],
            screen_pic_count_down: 5,
            screen_pic_count_down_options: (function () {
                const list = [{value: 0, title: '关闭倒计时'}]
                for (let i = 1; i <= 30; i++) {
                    list.push({value: i, title: i + '秒'})
                }
                return list
            })(),
            screen_pic_buttom_show: 1,
            close_end_popup: 0,
            close_end_popup_opt: [
                {value: 0, title: '开启活动页面到达终点弹窗'},
                {value: 2, title: '只显示一次到达终点弹窗'},
                {value: 1, title: '关闭活动页面到达终点弹窗'}
            ],
            like_limit: 0,
            closed_likes: 0,
            closed_likes_opt: [
                {value: 0, title: '开启排行榜点赞功能'},
                {value: 1, title: '关闭排行榜点赞功能'}
            ],
            limit_sport_moment_set_list: [
                {
                    distance: '',
                    count: ''
                }
            ],
            sport_moment_release_limit: 0,
            share_image: '',
            share_title: '',
            qrcode_logo: '',
            must_submit_not_modifiable: 0,
            must_submit_not_modifiable_options: [
                {value: 0, title: '填写后可以修改'},
                {value: 1, title: '填写后不可修改'}
            ],
            sport_moment_type: 1,
            sport_moment_type_opt: [
                {value: 1, title: '列表展示'},
                {value: 2, title: '照片墙展示'}
            ],
            exam_show_type: 1,

            // 出差公里数
            open_business_kilometers: false,
            business_trip: {
                rate: '',
                province_rate: '',
                country_rate: ''
            },
            // 出差公里数


            // 排行榜前几名奖励
            default_reward_amount_item: JSON.parse(JSON.stringify(default_reward_amount_item)),
            reward_amount: {
                person_team_set: [
                    {
                        open: 1,
                        types: 1, // 1 个人奖励 2 队伍奖励
                        reward: [
                            JSON.parse(JSON.stringify(default_reward_amount_item))
                        ]
                    },
                    {
                        open: 1,
                        types: 2, // 1 个人奖励 2 队伍奖励
                        reward: [
                            JSON.parse(JSON.stringify(default_reward_amount_item))
                        ]
                    }
                ]
            },
            // 排行榜前几名奖励


            // 阅读文章奖励积分
            reading_reward: {
                open: 1,
                rules: [
                    {
                        name: '',
                        category_id: '',
                        seconds: '',
                        integral: {min: '', max: ''}
                    }
                ]
            },
            // 阅读文章奖励积分

            medal_mileage_hide: 0,
            medal_mileage_hide_options: [
                {
                    value: 0,
                    title: '显示勋章里程数'
                },
                {
                    value: 1,
                    title: '隐藏勋章里程数'
                }
            ],

            time_period_step: 0,

            is_simple: true, // 是否简约版
            is_set_simple: 0, // 用户是否将活动设置简约版（管理员手动设置，活动并非简约版）

            // 接力赛
            runOneByOne: {
                open: 0,
                per_person_exchange_kilo: '',
                user_select_team: 0,
                user_select_max: 1,
                all_person: ''
            },
            // 接力赛

            // 跑步运动轨迹
            run_trace: {
                run_kilo: '',
                exchange_step: ''
            },
            pace_limit: '',
            // 跑步运动轨迹

            finished_step_num: '', //达标率

            only_join_user_look_ranklist: 0, // 是否只有已参加活动的用户才能查看排行榜

            ip_set: {
                open: 0,
                ip_city: []
            },
            ip_set_options: [
                {value: 0, title: '关闭, 所有城市用户均可参加'},
                {value: 1, title: '开启, 只允许特定城市用户参加'}
            ],

            // 最先到达终点的得 max 500分，每后一名减 minus_score 1分，最多减至 min 300分
            score_and_finished: {min: '', max: '', minus_score: 1},


            light_city_reward_step: '',

            take_oath_bg_img: '',
            take_oath_square_open: 0,

            official_account_open: 0,
            official_account_open_options: [
                '不限制',
                '限制只能从公众号菜单进入才能报名',
                '限制只能从公众号菜单进入才能进入活动'
            ],

            study_news_set: {
                news_category: {
                    id: '',
                    title: ''
                },
                study_type: 0,
                reward_step: {
                    min: '',
                    max: ''
                }
            },

            map_page_just_show_diy_exchange_button: 0,
            map_page_diy_exchange_button_set: {
                text_color: 'rgba(255, 255, 255, 1)',
                button_color: 'rgba(226,15,4, 1)',
                text: '兑换里程',
                width: 80,
                height: 80
            },

            map_page_no_exchange_tips: {
                open: 1,
                ...JSON.parse(JSON.stringify(this.xwy_config.long_march_no_exchange_default_tips_set))
            },

            map_page_top_scroll_tips_hide: 0,

            location_distance_limit_options: [
                {value: 0, title: '关闭, 所有用户均可参加'},
                {value: 1, title: '开启, 只允活动位置范围内的用户参加'}
            ],
            join_location_distance_limit: {
                open: 0,
                location: {
                    latitude: '',
                    longitude: '',
                    name: ''
                },
                distance: ''
            },

            blind_box_lottery: {
                lottery_id: '',
                title: ''
            },

            step_lottery_list: [],

            // 健步走步数兑换解锁图片碎片
            step_unlock_picture_fragments: {
                open: 0,
                image_src: '',
                col: '',
                row: '',
                mileage: ''
            },
            // 健步走步数兑换解锁图片碎片

            batch_import_label: {
                username: '请输入账号',
                password: '请输入密码'
            },

            // 用户报名后每次进入活动弹窗文章配置
            enter_activity_popup_news: {
                title: '',
                id: ''
            },

            enter_activity_auto_exchange: 0,

            // 需要阅读点位知识才能答题
            answer_need_read: {
                open: 0,
                seconds: ''
            },

            // 点位文章是否需要进入到文章详情才能查看，设置了这个功能，点位知识弹窗只显示查看文章的按钮
            point_news_to_details_page_show: 0,


            // 答题勋章
            exam_prize_set: {
                types: 1,
                prize_list: []
            },
            exam_prize_icon_diy_open: 0,
            exam_prize_icon_diy: {
                icon: '',
                name: ''
            },

            exchange_rules: {
                types: 1,
                per_step: '',
                rules_list: [
                    // 其中：大于min，小于等于max，  num 奖励的积分数  per_step 每一千步为一个区间，兑换步数必需是一千的倍数。
                    /*{"min":4999,"max":5000,"num":4,"text":"每日达到五千步整可一次性获得4积分"},
                    {"min":5000,"max":6000,"num":4,"text":"5000-10000，每1000步得4积分"},*/
                ],

                rushing_set: {
                    //每天最多解锁几个点位
                    daily_rushed_round_num: '',
                    //每天最多解锁几个答题。
                    daily_exam_times: '',
                    //必需大于等于多少步才能解锁第一个点位
                    min_step_num: '',
                    //超过daily_rushed_round_num步以后，必需每多少步解锁下一个点位
                    per_round_step_num: ''
                }
            },

            // 抽奖需要口积分
            lottery_cost_integral: '',

            must_exam_daily: {
                open: 0,      // 功能是否开启 0: 不开启   1: 开启
                exam_id: '',  // 绑定的考卷ID
                exam_name: '',// 绑定的考卷名称
                types: 0,     // 默认不设置 0代表是必需先答题才能兑换步数    【1】代表 步数解锁答题次数


                // 这里是每天需要答题后才能兑换的设置
                score: 0,      // 分数 大于等于这个分数才算答题成功 0或不填不限制分数
                // 这里是每天需要答题后才能兑换的设置

                // 这里是每天兑换步数解锁答题的设置
                max_integral: '',  // 每天答题奖励的积分上限
                per_step: '',      // 每多少步可以获得1次答题机会
                // 这里是每天兑换步数解锁答题的设置
            }
        }
    },
    watch: {
        enter_types: function (val) {
            this.enter_types_index = this.enter_types_opt.findIndex(v => v.value === val)
        }
    },

    computed: {
        map_types_title() {
            return this.map_type_list.find(v => v.type === this.map_types)?.title || ''
        },
        map_types_index() {
            let index = this.map_type_list.findIndex(v => v.type === this.map_types)
            if (index === -1) index = 0
            return index
        },
        score_and_finished_have_set() {
            const {min, max, minus_score} = this.score_and_finished
            return min !== '' && max !== '' && minus_score !== ''
        },

        integralUnit() {
            return this.integral_set?.unit || this.default_integral_unit
        },

        dailySignTypesOptions() {
            const unit = this.integralUnit
            return [
                {value: 0, title: `关闭每日${unit}签到`},
                {value: 1, title: `每日签到奖励固定${unit}`},
                {value: 2, title: `周期签到、连续签到，每日签到${unit}奖励不同`}
            ]
        },
    },

    onLoad(e) {
        if (e.id) {
            this.id = e.id
            this.open_business_kilometers = this.xwy_config.openBusinessKilometers(e.id)
        } else {
            this.types = Number(e.types)
            this.$nextTick(() => this.$refs.add_activity_tips.open(this.types))
            this.thumb = this.xwy_config.create_active_default_logo
        }

        this.$uni.setNavigationBarTitle(e.id ? '修改活动' : `创建${e.name || '活动'}`)


        // 又不做简约版活动了，这里把简约版的设置去掉
        this.setIsNotSimple()

        this.$uni.showLoading()

        login.uniLogin(err => {

            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }


            if (e.id) return this.getDetail()

            const evn_version = app.globalData['evn_version']

            if (evn_version === 'develop') {
                this.tel = ***********
                this.wechat_num = ***********
                this.organizer = '伟杰测试'
            }

            // 开发版和体验版不检查重复创建活动
            if (evn_version === 'develop' || evn_version === 'trial') return uni.hideLoading()

            this.checkUserCanCreateActive()
        })
    },


    methods: {
        async checkUserCanCreateActive() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/check_user_can_create_active'
            })
            console.log('检查是否能创建活动', res)
            uni.hideLoading()
            this.loading = false

            if (res?.status !== 1) return this.$uni.showModal(res.info || '暂时不能创建活动', {
                success: () => this.$uni.navigateBack()
            })
        },

        getDetail() {
            const errModal = content => {
                uni.hideLoading()
                this.$uni.showModal(content || '活动获取失败', {success: () => this.$uni.navigateBack()})
            }

            xwy_api.getActivityDetail(this.id, res => {
                if (res?.status !== 1) return errModal(res?.info)

                // 是不是简约版
                if (!res?.data?.['usedFreeSimple']) this.setIsNotSimple()

                const details = res.data?.['active_details']
                if (!details) return errModal(res.info)
                this.detailInit(details)
            })
        },

        setIsNotSimple() {
            this.is_simple = false
            this.exchange_end_time = '22:00:00'
            // this.type_list.splice(this.type_list.length - 1, 0, {title: '地图设置', id: 5})
        },


        detailInit(data) {
            this.types = data.types
            const start_date = utils.timestampToTime(data.begin_time)
            const end_date = utils.timestampToTime(data.end_time)
            uni.hideLoading()
            this.name = data.name
            if (data.organizer) this.organizer = data.organizer
            this.startDay = start_date
            this.endDay = end_date

            if (data.logo) this.thumb = data.logo

            if (data.content) this.content = data.content

            if (data.rank_set) {
                const rank_set = data.rank_set

                this.rank_set = rank_set

                if (rank_set.reward_step_exam_conf && rank_set.reward_step_exam_conf === 1) {
                    this.reward_step_exam_conf = true
                }

                // 开启手绘地图
                if (rank_set['open_map_pic'] && rank_set['open_map_pic'] === 1) {
                    this.map_type_list = [{type: 102, title: '手绘地图'}, ...this.map_type_list]
                }

                if (rank_set.closed_AD && rank_set.closed_AD === 1) {
                    this.detail_icon_conf.push({
                        type: 1,
                        title: '文章列表'
                    })
                }

                // 开启答题
                if (rank_set.exam_open) this.exam_open = rank_set.exam_open

                if (rank_set.open_mp3) this.open_mp3 = rank_set.open_mp3

                if (rank_set.AI_motion) this.getAiSportTypeList()

                if (rank_set['batch_import']) {
                    this.enter_types_opt.push({
                        value: 4,
                        title: '只能使用管理员提前导入的名单报名'
                    })
                }
            }

            if (data.conf) {
                const conf = data.conf

                if (conf.active) {
                    const active = conf.active
                    if (active.enter_types) this.enter_types = active.enter_types

                    if (active.exchange_rate) this.exchange_rate = active.exchange_rate
                    if (active.arrive_end_num) this.arrive_end_num = active.arrive_end_num
                    if (active.password) {
                        this.have_password = true
                        this.old_password = active.password
                    }
                    if (active.map_center_point && active.map_center_point.coordinates) {
                        this.map_center_point_lng = active.map_center_point.coordinates[0]
                        this.map_center_point_lat = active.map_center_point.coordinates[1]
                    }
                    if (active.screen_pic) this.screen_pic = active.screen_pic
                    if (active.scale) this.scale = active.scale
                    if (active.min_scale) this.min_scale = active.min_scale
                    if (active.max_scale) this.max_scale = active.max_scale
                    if (active.hide_point_scale) this.hide_point_scale = active.hide_point_scale
                    if (active.top_rank_banner) this.top_rank_banner = active.top_rank_banner


                    if (active.kilo_unit) this.unit = active.kilo_unit
                    if (active.satellite) this.satellite = active.satellite
                    if (active.map_rotate) this.map_rotate = active.map_rotate
                    if (active.user_point_map_center) this.user_point_map_center = active.user_point_map_center
                    this.closed_team = active.closed_team || 0
                    if (active.allow_change_team) this.allow_change_team = active.allow_change_team
                    if (active.team_required) this.team_required = active.team_required
                    if (active.max_num) this.max_num = active.max_num
                    if (active.min_num) this.min_num = active.min_num
                    if (active.arrive_then_end) this.arrive_then_end = active.arrive_then_end
                    this.exchange_end_time = active.exchange_end_time
                    if (active.exchange_start_time) this.exchange_start_time = active.exchange_start_time
                    if (active.audio_src) this.audio_src = active.audio_src

                    if (active.map_types) this.map_types = active.map_types

                    if (active.reward_step_by_exam) {
                        this.reward_step_by_exam = active.reward_step_by_exam
                    }

                    if (active.detail_icon_list) this.detail_icon_list = active.detail_icon_list
                    if (active.news) {
                        if (active.news.news_id) this.news_id = active.news.news_id
                        if (active.news.news_title) this.news_title = active.news.news_title
                    }

                    if (active.must_exam) {
                        if (active.must_exam.open) this.must_exam_open = active.must_exam.open
                        if (active.must_exam.score) this.must_exam_score = active.must_exam.score
                    }

                    if (active.must_exam_daily) {
                        active.must_exam_daily.types ||= 0
                        active.must_exam_daily.max_integral ||= ''
                        active.must_exam_daily.per_step ||= ''

                        this.must_exam_daily = active.must_exam_daily
                    }

                    if (active.integral) {
                        const integral = active.integral
                        integral.exam_reward_num ||= 1
                        integral.register_integral ||= ''

                        if (integral.exchange_rules) {
                            integral.exchange_rules.rushing_set ||= {}
                            integral.exchange_rules.rushing_set.daily_exam_times ||= ''
                            this.exchange_rules = integral.exchange_rules
                            delete integral.exchange_rules
                        }

                        this.integral_set = integral
                    }
                    if (active.redpack_rules) this.redpack_rules = active.redpack_rules
                    if (active.polyline_hide) this.polyline_hide = active.polyline_hide
                    if (active.map_between_two_points) this.map_between_two_points = active.map_between_two_points
                    if (active.certificate_close) this.certificate_close = active.certificate_close
                    if (active.just_look_my_team_top) this.just_look_my_team_top = active.just_look_my_team_top
                    if (active.daily_sign) {
                        active.daily_sign.auto_sign ||= 0
                        this.daily_sign = active.daily_sign
                    }
                    if (active.submit) this.submit = active.submit
                    if (active.activity_rules_hide) this.activity_rules_hide = active.activity_rules_hide
                    if (active.close_feedback) this.close_feedback = active.close_feedback
                    if (active.close_end_popup) this.close_end_popup = active.close_end_popup
                    if (active.like_limit) this.like_limit = active.like_limit
                    if (active.closed_likes && data.rank_set?.closed_AD) this.closed_likes = active.closed_likes
                    if (active.user_create_team) this.user_create_team = active.user_create_team
                    if (active.team_limit_person) this.team_limit_person = active.team_limit_person

                    // 把白名单导入功能设置和活动参与方式设置融合在一起，但是两个字段还是单独保存的，当用户选择使用白名单功能时join_type=1，活动参与方式enter_types显示为4
                    if (active.join_type) this.enter_types = 4
                    if (active.active_details_notice) this.active_details_notice = active.active_details_notice

                    let screen_pic_count_down = active.screen_pic_count_down
                    if (screen_pic_count_down === undefined) screen_pic_count_down = 5
                    if (screen_pic_count_down === '' || screen_pic_count_down === '0') screen_pic_count_down = 0
                    this.screen_pic_count_down = screen_pic_count_down
                    if (active.screen_pic_buttom_show === 0) this.screen_pic_buttom_show = 0


                    if (data.rank_set?.['open_sport_moment']) {
                        const set_list = active?.limit_sport_moment_set_list
                        if (data.rank_set?.['limit_sport_moment'] && set_list?.length) {
                            this.limit_sport_moment_set_list = set_list
                        }
                        if (active.sport_moment_release_limit) {
                            this.sport_moment_release_limit = active.sport_moment_release_limit
                        }
                    }


                    this.sport_moment_type = active.sport_moment_type || 1

                    if (active.qrcode_logo) this.qrcode_logo = active.qrcode_logo
                    if (active.share_image) this.share_image = active.share_image
                    if (active.share_title) this.share_title = active.share_title
                    if (active.must_submit_not_modifiable) this.must_submit_not_modifiable = active.must_submit_not_modifiable
                    if (active.exam_show_type) this.exam_show_type = Number(active.exam_show_type)
                    if (active.business_trip) this.business_trip = active.business_trip

                    // 跑步运动
                    if (data.rank_set) {
                        if (active.run_trace) this.run_trace = active.run_trace
                        if (active.pace_limit) this.pace_limit = active.pace_limit
                    }

                    // 排行榜前几名奖励
                    if (active.reward_amount) this.reward_amount = active.reward_amount
                    // 排行榜前几名奖励

                    // 阅读文章奖励积分
                    if (active.reading_reward) this.reading_reward = active.reading_reward
                    // 阅读文章奖励积分

                    if (active.medal_mileage_hide) this.medal_mileage_hide = active.medal_mileage_hide
                    if (active.time_period_step) this.time_period_step = active.time_period_step

                    if (active.runOneByOne) this.runOneByOne = active.runOneByOne
                    if (active.is_set_simple) this.is_set_simple = active.is_set_simple
                    if (active.finished_step_num) this.finished_step_num = active.finished_step_num
                    if (active.only_join_user_look_ranklist) this.only_join_user_look_ranklist = active.only_join_user_look_ranklist

                    if (active.template_id) this.template_id = active.template_id


                    if (active.ip_set) this.ip_set = active.ip_set

                    if (active.score_and_finished) this.score_and_finished = active.score_and_finished

                    if (active.light_city_reward_step) this.light_city_reward_step = active.light_city_reward_step

                    if (active.take_oath_bg_img) this.take_oath_bg_img = active.take_oath_bg_img
                    if (active.take_oath_square_open) this.take_oath_square_open = active.take_oath_square_open

                    if (active.official_account_open) this.official_account_open = active.official_account_open

                    let study_news_set = {}
                    if (active['study_news_category']) {
                        study_news_set.news_category = active['study_news_category']
                    }
                    if (active['study_news_study_type']) {
                        study_news_set.study_type = active['study_news_study_type']
                    }
                    if (active.study_news_set) study_news_set = active.study_news_set
                    study_news_set.reward_step ||= {min: '', max: ''}
                    study_news_set.news_category ||= {id: '', title: ''}
                    study_news_set.study_type ||= 0
                    this.study_news_set = study_news_set

                    // diy地图页面兑换按钮
                    if (active.map_page_just_show_diy_exchange_button) {
                        this.map_page_just_show_diy_exchange_button = active.map_page_just_show_diy_exchange_button
                    }
                    if (active.map_page_diy_exchange_button_set) {
                        this.map_page_diy_exchange_button_set = active.map_page_diy_exchange_button_set
                    }

                    if (active.map_page_no_exchange_tips) {
                        this.map_page_no_exchange_tips = active.map_page_no_exchange_tips
                    }

                    if (active.join_location_distance_limit) {
                        this.join_location_distance_limit = active.join_location_distance_limit
                    }

                    if (active.map_page_top_scroll_tips_hide) {
                        this.map_page_top_scroll_tips_hide = active.map_page_top_scroll_tips_hide
                    }

                    if (active.blind_box_lottery) this.blind_box_lottery = active.blind_box_lottery

                    if (active.step_unlock_picture_fragments) {
                        this.step_unlock_picture_fragments = active.step_unlock_picture_fragments
                    }

                    if (active.batch_import_label) this.batch_import_label = active.batch_import_label

                    if (active.step_lottery_list) this.step_lottery_list = active.step_lottery_list

                    if (active.enter_activity_popup_news) {
                        this.enter_activity_popup_news = active.enter_activity_popup_news
                    }

                    if (active.enter_activity_auto_exchange) {
                        this.enter_activity_auto_exchange = active.enter_activity_auto_exchange
                    }

                    if (active.answer_need_read) this.answer_need_read = active.answer_need_read
                    if (active.point_news_to_details_page_show) {
                        this.point_news_to_details_page_show = active.point_news_to_details_page_show
                    }

                    if (active.exam_prize_set) this.exam_prize_set = active.exam_prize_set
                    if (active.exam_prize_icon_diy) {
                        this.exam_prize_icon_diy = active.exam_prize_icon_diy
                        this.exam_prize_icon_diy_open = 1
                    }

                    if (active.lottery_cost_integral) {
                        this.lottery_cost_integral = active.lottery_cost_integral
                    }
                }

                if (conf.must_submit) {
                    this.must_submit = conf.must_submit
                }


                if (conf.AI_motion?.motion_list?.length) {
                    this.ai_sport_list = conf.AI_motion.motion_list
                }
            }
        },


        async getAiSportTypeList() {
            this.ai_sport_type_list = await xwy_api.getAiSportTypeList()
        },


        addAiSport() {
            this.ai_sport_list.push({
                types: '',
                name: '',
                title: '',
                logo: ''
            })
        },

        delAiSportItem(index) {
            this.ai_sport_list.splice(index, 1)
        },

        aiSportTypeChange(e, index) {
            const type_data = this.ai_sport_type_list[e.detail.value]

            for (let i = 0; i < this.ai_sport_list.length; i++) {
                const item = this.ai_sport_list[i]
                if (item.types === type_data.types) {
                    this.$uni.showModal('已添加该运动类型，无需重复添加。')
                    return
                }
            }

            this.ai_sport_list[index].types = type_data.types
            this.ai_sport_list[index].name = type_data.name
        },

        addAiSportImg(index) {
            let url = '/pages/other/image_upload_or_select'
            if (this.id) url += `?active_id=${this.id}`
            this.$uni.navigateTo(url, {
                events: {
                    newImg: src => {
                        this.ai_sport_list[index].logo = src
                    }
                }
            })
        },

        delAiSportItemLogo(index) {
            this.ai_sport_list[index].logo = ''
        },


        toSelExam(type) {
            if (!this.rank_set.exam_open) {
                this.$uni.showModal('未开通答题功能，请联系客服开通。')
                return
            }

            this.$uni.navigateTo('/pages/likou_dati/pages/exam/exam_list/exam_list?in_select=true', {
                events: {
                    updateExam: data => {
                        if (type === 'must_exam_daily') {
                            this.must_exam_daily.exam_id = data.id
                            this.must_exam_daily.exam_name = data.title
                        }
                    }
                }
            })
        },


        addDetailIcon() {
            this.detail_icon_list.push({
                icon: 'icon-dating',
                title: '',
                type: 0,
                id: '',
                name: ''
            })
        },
        delDetailIcon(index) {
            this.detail_icon_list.splice(index, 1)
        },
        selIcon(index) {
            this.$uni.navigateTo('/pages/other/icon-list/icon-list', {
                events: {
                    selIcon: class_name => {
                        this.detail_icon_list[index].icon = class_name
                    }
                }
            })
        },
        detailIconItemChange(e, index) {
            this.detail_icon_list[index].id = ''
            this.detail_icon_list[index].name = ''
            this.detail_icon_list[index].type = this.detail_icon_conf[e.detail.value].type
        },

        toSelNews(type) {
            this.$uni.navigateTo('/pages/news/list?type=user&is_sel=true', {
                events: {
                    selNews: data => {
                        switch (type) {
                            case 'content':
                                this.news_id = data.id
                                this.news_title = data.title
                                break
                            case 'notice':
                                this.active_details_notice.news_id = data.id
                                this.active_details_notice.news_title = data.title
                                break
                            case 'enter_activity_popup_news':
                                this.enter_activity_popup_news.id = data.id
                                this.enter_activity_popup_news.title = data.title
                                break
                        }
                    }
                }
            })
        },

        deleteNews(type) {
            switch (type) {
                case 'content':
                    this.news_id = ''
                    this.news_title = ''
                    break
                case 'notice':
                    this.active_details_notice.news_id = ''
                    this.active_details_notice.news_title = ''
                    break
                case 'enter_activity_popup_news':
                    this.enter_activity_popup_news.id = ''
                    this.enter_activity_popup_news.title = ''
                    break
            }
        },

        bindNewsOrCategory(index) {
            const item = this.detail_icon_list[index]
            if (item.type === 0) this.bindNews(index)
            if (item.type === 1) this.bindCategory(index)
        },
        bindNews(index) {
            this.$uni.navigateTo('/pages/news/list?type=user&is_sel=true', {
                events: {
                    selNews: data => {
                        this.detail_icon_list[index].id = data.id
                        this.detail_icon_list[index].name = data.title
                    }
                }
            })
        },
        bindCategory(index) {
            this.$uni.navigateTo('/pages/category/list?types=8&is_sel=1', {
                events: {
                    selCategory: data => {
                        this.detail_icon_list[index].id = data.id
                        this.detail_icon_list[index].name = data.name
                    }
                }
            })
        },

        bindStudyNewsCategory() {
            this.$uni.navigateTo('/pages/category/list?types=8&is_sel=1', {
                events: {
                    selCategory: data => {
                        this.study_news_set.news_category.id = data.id
                        this.study_news_set.news_category.title = data.name
                    }
                }
            })
        },

        deBindStudyNewsCategory() {
            this.study_news_set.news_category.id = ''
            this.study_news_set.news_category.title = ''
        },

        bindBlindBoxLottery() {
            this.$uni.navigateTo('/pages/lottery/admin/lottery/list?mystery_box=1&select_blind_box=1', {
                events: {
                    selectBlindBox: lottery => {
                        this.blind_box_lottery.lottery_id = lottery.lottery_id
                        this.blind_box_lottery.title = lottery.title
                    }
                }
            })
        },

        deBindBlindBoxLottery() {
            this.blind_box_lottery.lottery_id = ''
            this.blind_box_lottery.title = ''
        },

        bindStepLottery() {
            this.$uni.navigateTo(`/pages/lottery/admin/lottery/list?active_id=${this.id}&select_step=1`, {
                events: {
                    selectStepLottery: lottery => {
                        if (this.step_lottery_list.find(v => v.lottery_id === lottery.lottery_id)) {
                            return setTimeout(() => {
                                this.$uni.showToast('该抽奖活动已绑定，请勿重复绑定', 'none', 3000)
                            }, 300)
                        }
                        this.step_lottery_list.push(lottery)
                    }
                }
            })
        },

        deBindStepLottery(index) {
            this.step_lottery_list.splice(index, 1)
        },


        // 更改阅读奖励积分绑定的文章分类
        changeReadingRewardItemNewsCategory(index) {
            // same_activity 选择的分类id必须和活动绑定，否则无法选择
            this.$uni.navigateTo('/pages/category/list?types=8&is_sel=1&same_activity=1&active_id=' + this.id, {
                events: {
                    selCategory: data => {
                        // 因为showToast是全局的，navigateTo的events是立马执行，这样就会导致events在页面还未返回的时候执行，showToast在上个页面显示，所以这里延迟执行，等返回到本页面再显示showToast
                        setTimeout(() => {
                            const i = this.reading_reward.rules.findIndex(v => v.category_id === data.id)
                            if (i !== -1) {
                                return this.$uni.showToast(`文章分类${data.name}已绑定设置${i + 1}，请重新选择`, 'none', 3000)
                            }

                            this.reading_reward.rules[index].category_id = data.id
                            this.reading_reward.rules[index].name = data.name
                        }, 500)
                    }
                }
            })
        },

        chooseLocation() {
            const obj = {
                success: res => {
                    this.map_center_point_lat = res.latitude
                    this.map_center_point_lng = res.longitude
                },
                fail: err => {
                    console.log(err)

                    if (err.errMsg === 'chooseLocation:fail:not supported') {
                        return this.$uni.showModal('不支持电脑端使用，请在手机微信打开')
                    }

                    if (err.errMsg === 'chooseLocation:fail auth deny') {
                        return this.$uni.showModal('请授权小程序获取你的位置信息', {
                            showCancel: true,
                            confirmText: '立即授权',
                            success: res => res.confirm && uni.openSetting()
                        })
                    }


                    if (err.errMsg !== 'chooseLocation:fail cancel') {
                        return this.$uni.showModal(JSON.stringify(err))
                    }
                }
            }
            if (this.map_center_point_lat && this.map_center_point_lng) {
                obj.latitude = this.map_center_point_lat * 1
                obj.longitude = this.map_center_point_lng * 1
            }
            uni.chooseLocation(obj)
        },


        chooseJoinLocation() {
            const obj = {
                success: res => {
                    console.log(res);
                    const {latitude, longitude, name, address} = res
                    if (!name && !address) {
                        return this.$uni.showModal('请在位置列表中选择一个位置再确定', {
                            success: () => this.chooseJoinLocation()
                        })
                    }
                    this.join_location_distance_limit.location = {
                        name: name || address || '',
                        latitude,
                        longitude
                    }
                },
                fail: err => {
                    console.log(err)

                    if (err.errMsg === 'chooseLocation:fail:not supported') {
                        return this.$uni.showModal('不支持电脑端使用，请在手机微信打开')
                    }

                    if (err.errMsg === 'chooseLocation:fail auth deny') {
                        return this.$uni.showModal('请授权小程序获取你的位置信息', {
                            showCancel: true,
                            confirmText: '立即授权',
                            success: res => res.confirm && uni.openSetting()
                        })
                    }


                    if (err.errMsg !== 'chooseLocation:fail cancel') {
                        return this.$uni.showModal(JSON.stringify(err))
                    }
                }
            }

            const {latitude, longitude} = this.join_location_distance_limit.location
            if (latitude && longitude) {
                obj.latitude = Number(latitude)
                obj.longitude = Number(longitude)
            }
            uni.chooseLocation(obj)
        },


        pickerChange(e, key) {
            let value = e.detail.value
            if (e.currentTarget.dataset['isnumber']) value = Number(value)

            if (key === 'runOneByOne_user_select_team') this.runOneByOne.user_select_team = value

            if (key === 'runOneByOne_open') {
                if (value && this.closed_team) return this.$uni.showModal('请先开启队伍功能。')
                this.runOneByOne.open = value
                return
            }

            if (key === 'closed_team') {
                if (value === 0 && !this.rank_set?.['team_group_open']) {
                    return this.$uni.showModal('请联系客服开启队伍功能。')
                }
                // 关闭了队伍功能，把报名时队伍必选关掉
                if (value === 1) this.team_required = 0
            }

            if (key === 'satellite') {
                this.satellite = this.satellite_options[value]?.value || 0
                return
            }

            this[key] = value
        },

        timeChange(e, key = '') {
            if (key === 'exchange_end_time') {
                if (!this.rank_set['export_top_rank_excel']) {
                    const set_sec = this.time2sec(e),
                        hour22_sec = this.time2sec('22:00:00')
                    if (set_sec > hour22_sec) this.notVipExchangeEndTime22()
                }
            }
        },

        time2sec(time) {
            if (time !== null) {
                const [hour, min, sec] = time.split(":")
                return Number(hour * 3600) + Number(min * 60) + Number(sec)
            }
        },


        setExchangeEndTimeNoLimit() {
            if (this.rank_set['export_top_rank_excel']) {
                this.exchange_end_time = ''
                return
            }
            this.notVipExchangeEndTime22()
        },

        notVipExchangeEndTime22() {
            this.exchange_end_time = '22:00:00'
            return this.$uni.showModal('每天活动兑换截止时间最多只能设置到22点，可联系客服开通自由设置兑换截止时间。')
        },

        mapTypesChange(e) {
            const index = Number(e.detail.value)
            if (this.map_type_list[index].type !== 101 && this.is_simple) {
                return this.$uni.showModal('不支持设置地图，如需设置地图请联系客服')
            }

            const map_types = this.map_type_list[index].type
            this.map_types = map_types

            if (map_types === 101) {
                this.close_end_popup = 1
                this.certificate_close = 1
            } else {
                this.time_period_step = 0
            }

            const tips_text = this.map_page_no_exchange_tips.text
            const default_tips = this.xwy_config.long_march_no_exchange_default_tips_set.text
            if (map_types === 101) {
                // 无需地图不用设置
                this.map_page_no_exchange_tips.open = 0
                this.map_page_no_exchange_tips.text = ''
            } else if (map_types === 1) {
                // 长征地图，如果没有设置，自动设置为长征默认提示语
                this.map_page_no_exchange_tips.text ||= default_tips
            } else {
                // 不是长征地图，如果提示语和长征地图默认提示语一样，设置为空
                if (tips_text === default_tips) this.map_page_no_exchange_tips.text = ''
            }
        },

        timePeriodStepChange(e) {
            const value = Number(e.detail.value)
            if (value === 1) {
                const startDay = this.startDay, endDay = this.endDay
                const start_day = startDay.split(' ')[0], end_day = endDay.split(' ')[0]
                if (start_day !== end_day) return this.$uni.showModal(`开启该功能会自动将活动结束时间更改为 ${start_day} 23:59:59，确定开启吗？`, {
                    success: res => {
                        if (res.confirm) {
                            this.endDay = start_day + ' 23:59:59'
                            this.time_period_step = value
                            this.$uni.showToast('功能已开启，如需修改活动时间请到“基本信息”修改')
                        }
                    }
                })
            }
            this.time_period_step = value
        },

        enter_typesChange(e) {
            // 把白名单导入功能设置和活动参与方式设置融合在一起，但是两个字段还是单独保存的，当用户选择使用白名单功能时enter_types=1，白名单功能开启join_type=1
            this.enter_types = this.enter_types_opt[e.detail.value].value
        },

        setScreenPic() {
            if (this.rank_set.closed_AD) {
                let url = '/pages/other/image_upload_or_select?key=screen_pic'
                if (this.id) url += `&active_id=${this.id}`
                return this.$uni.navigateTo(url)
            }
            this.$uni.showModal('无法设置开屏大图，请联系客服设置')
        },

        setExamPrizeIcon() {
            this.$uni.navigateTo(`/pages/other/image_upload_or_select?active_id=${this.id}`, {
                events: {
                    newImg: src => {
                        this.exam_prize_icon_diy.icon = src
                    }
                }
            })
        },

        setShareImage() {
            let url = '/pages/other/image_upload_or_select?key=share_image'
            if (this.id) url += `&active_id=${this.id}`
            this.$uni.navigateTo(url)
        },

        setQrcodeImage() {
            let url = '/pages/other/image_upload_or_select?key=qrcode_logo'
            if (this.id) url += `&active_id=${this.id}`
            this.$uni.navigateTo(url)
        },


        previewImage(urls, current = urls[0]) {
            this.$uni.previewImage({
                urls,
                current
            })
        },

        addTopRankBanner() {
            if (!this.rank_set?.closed_AD) return this.$uni.showModal('无法设置排行榜轮播图，请联系客服设置')

            let url = '/pages/other/image_upload_or_select'
            if (this.id) url += `?active_id=${this.id}`
            this.$uni.navigateTo(url, {
                events: {
                    newImg: src => {
                        this.top_rank_banner.push(src)
                    }
                }
            })
        },

        examAddStepChange(e) {
            const value = Number(e.detail.value) + 1
            this.reward_step_by_exam.open = value
            if (value === 1 && !this.reward_step_by_exam.types) this.reward_step_by_exam.types = 1
        },

        examAddStepTypeChange(e) {
            this.reward_step_by_exam.types = Number(e.detail.value) + 1
        },


        getDetailIconList() {
            if (!this.detail_icon_list.length) return ''

            const showToast = content => {
                this.type_id = 6
                this.$uni.showToast(content, 'none', 3000)
                return false
            }

            const detail_icon_list = JSON.parse(JSON.stringify(this.detail_icon_list))

            for (let i = 0; i < detail_icon_list.length; i++) {
                const v = detail_icon_list[i]
                if (!v.title || !v.id) return showToast('活动详情页面图标扩展填写不完整，请检查。')
                if (v.title.length > 6) return showToast('活动详情页面图标扩展显示文字请控制在6个字内。')
            }

            return detail_icon_list
        },

        getMustSubmitData() {
            const showToast = content => {
                this.type_id = 3
                this.$uni.showToast(content, 'none', 3000)
                return false
            }

            const must_submit = JSON.parse(JSON.stringify(this.must_submit))

            for (let i = 0; i < must_submit.length; i++) {
                const v = must_submit[i]
                if (!v.title) return showToast('参与活动需要填写的信息选项填写不完整，请检查。')
                v.name = pinyin(v.title, {toneType: 'none', type: 'array'}).join('_')

                if (v.types === 2) {
                    if (!v.options?.length) return showToast(`${v.title} 至少需要添加一个选项。`)
                    for (let j = 0; j < v.options.length; j++) {
                        const v1 = v.options[j]
                        if (!v1?.text) return showToast(`${v.title} 有未填写的选项，请检查。`)
                    }
                }
            }

            return must_submit
        },

        getRewardStepByExam() {
            const showToast = content => {
                this.type_id = 6
                this.$uni.showToast(content, 'none', 3000)
                return false
            }

            const reward_step_by_exam = JSON.parse(JSON.stringify(this.reward_step_by_exam))
            if (!reward_step_by_exam.open || reward_step_by_exam.open !== 1) return ''
            if (reward_step_by_exam.types === 1 && !reward_step_by_exam.score_one_point) {
                return showToast('答题奖励步数规则不完整，分数未填。')
            }
            if (!reward_step_by_exam.reward_step) {
                return showToast('答题奖励步数规则不完整，请填写奖励步数。')
            }
            reward_step_by_exam.reward_step = Number(reward_step_by_exam.reward_step)
            reward_step_by_exam.score_one_point = Number(reward_step_by_exam.score_one_point)
            if (reward_step_by_exam.min_score) reward_step_by_exam.min_score = Number(reward_step_by_exam.min_score)


            return reward_step_by_exam
        },


        checkLimitSportMomentSetList() {
            const list = JSON.parse(JSON.stringify(this.limit_sport_moment_set_list))
            for (let i = 0; i < list.length; i++) {
                const item = list[i]
                if (!item.distance || !item.count) {
                    this.type_id = 6
                    this.$nextTick(() => {
                        uni.pageScrollTo({
                            selector: '.limit_sport_moment'
                        })
                    })
                    this.$uni.showToast('运动圈动态发布限制填写不完整')
                    return false
                }
            }
            return list
        },

        save() {
            if (this.success) return

            const showToast = (title, type_id, duration) => {
                if (type_id) this.type_id = type_id
                this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none', duration || 1500)
            }

            if (!this.name) return showToast('请输入活动名称', 1)

            if (!this.id) {
                if (!this.tel) return showToast('请输入手机号', 1)
                if (this.tel.toString().length !== 11) return showToast('手机号长度有误', 1)

                if (!this.wechat_num) return showToast('请输入微信号', 1)
                const wechat_num = this.wechat_num.toString()
                if (wechat_num.length < 4 || wechat_num.length > 20) return showToast('微信号长度有误', 1)
                if (this._utils.isChineseChar(wechat_num)) return showToast('微信号不能输入中文', 1)
            }

            if (this.map_types === 100) {
                let scale = Number(this.scale)
                scale *= 10
                scale = Math.floor(scale)
                scale /= 10

                this.scale = scale

                let min_scale = Number(this.min_scale)
                min_scale *= 10
                min_scale = Math.floor(min_scale)
                min_scale /= 10
                if (min_scale > scale) return showToast('地图最小缩放等级不能大于地图默认缩放等级', 5, 2000)
                this.min_scale = min_scale

                let max_scale = Number(this.max_scale)
                max_scale *= 10
                max_scale = Math.floor(max_scale)
                max_scale /= 10
                if (max_scale < scale) return showToast('地图最大缩放等级不能小于地图默认缩放等级', 5, 2000)
                this.max_scale = max_scale

            }

            // 开启了指定时间段功能，活动开始结束时间需为同一天
            if (this.time_period_step) {
                const startDay = this.startDay.split(' ')[0], endDay = this.endDay.split(' ')[0]
                if (startDay !== endDay) {
                    this.type_id = 1
                    return this.$uni.showModal('开启了指定时间段功能，活动开始结束时间需为同一天')
                }
            }

            const reg = new RegExp('-', 'g')
            const start_day = this.startDay.replace(reg, '/')
            const end_day = this.endDay.replace(reg, '/')


            const data = {
                name: this.name,
                begin_time: new Date(start_day).getTime() / 1000,
                end_time: new Date(end_day).getTime() / 1000,
                content: this.content,
                organizer: this.organizer,
                logo: this.thumb
            }
            if (this.id) data.active_id = this.id
            if (this.tel) data.mobile = this.tel
            if (this.wechat_num) data.wechat_num = this.wechat_num


            const conf = {
                active: {
                    map_types: this.map_types,
                    arrive_then_end: this.arrive_then_end,
                    enter_types: this.enter_types,
                    closed_team: this.closed_team,
                    exchange_rate: this.exchange_rate || 1,
                    kilo_unit: this.unit || '步',
                    allow_change_team: this.allow_change_team,
                    exchange_end_time: this.exchange_end_time || 0,
                    is_set_simple: this.is_set_simple
                }
            }

            if (this.rank_set['morning_daily_sign'] && this.daily_sign.types) {
                conf.active.daily_sign = this.daily_sign
            }

            // 把白名单导入功能设置和活动参与方式设置融合在一起，但是两个字段还是单独保存的，当用户选择使用白名单功能时join_type=1，活动参与方式保存为自由报名enter_types=1
            if (this.enter_types === 4) {
                conf.active.enter_types = 1
                conf.active.join_type = 1
            }

            if (this.close_end_popup) conf.active.close_end_popup = this.close_end_popup
            if (this.closed_likes) conf.active.closed_likes = this.closed_likes
            if (this.like_limit) conf.active.like_limit = this.like_limit
            if (this.user_create_team) {
                conf.active.user_create_team = this.user_create_team
                if (this.rank_set['admin_set_team_limit_person'] && this.team_limit_person) {
                    const num = Math.floor(this.team_limit_person)
                    if (isNaN(num) || num < 0) return this.$uni.showToast('请输入正确的队伍人数限制')
                    conf.active.team_limit_person = num
                }
            }
            if (this.close_feedback) conf.active.close_feedback = this.close_feedback
            if (this.activity_rules_hide) conf.active.activity_rules_hide = this.activity_rules_hide
            if (this.just_look_my_team_top) conf.active.just_look_my_team_top = this.just_look_my_team_top
            if (this.certificate_close) conf.active.certificate_close = this.certificate_close
            if (this.polyline_hide) conf.active.polyline_hide = this.polyline_hide
            if (this.map_between_two_points) conf.active.map_between_two_points = this.map_between_two_points
            if (this.audio_src) conf.active.audio_src = this.audio_src
            if (this.team_required) conf.active.team_required = this.team_required
            if (this.exam_show_type) conf.active.exam_show_type = Number(this.exam_show_type)

            if (this.rank_set?.exam_open) {
                conf.active.must_exam = {
                    open: this.must_exam_open,
                    score: this.must_exam_score === '' ? '' : Number(this.must_exam_score)
                }
            }
            
            // 兑换前需要答题配置
            if (this.rank_set?.mustExamThenExchangeStep && this.must_exam_daily.open) {
                const set = JSON.parse(JSON.stringify(this.must_exam_daily))
                if (!set.exam_id) return showToast('请绑定兑换前答题的考卷', 3)
                if (!set.types) {
                    const score = Number(set.score)
                    if (isNaN(score) || score < 0) return showToast('请输入正确的兑换前答题分数限制', 3)
                    set.score = score
                }
                if (set.types === 1) {
                    const max_integral = Math.floor(set.max_integral)
                    if (isNaN(max_integral) || max_integral < 0) {
                        return showToast(`请输入正确的每日答题获得${this.integralUnit}上限`, 3)
                    }
                    set.max_integral = max_integral

                    const per_step = Math.floor(set.per_step)
                    if (isNaN(per_step) || per_step <= 0) {
                        return showToast(`请输入正确的解锁答题步数设置`, 3)
                    }
                    set.per_step = per_step
                }
                conf.active.must_exam_daily = set
            }

            if (this.map_types === 100) {
                conf.active.scale = this.scale
                conf.active.min_scale = this.min_scale
                conf.active.max_scale = this.max_scale
            }

            if (this.map_types !== 101) conf.active.hide_point_scale = this.hide_point_scale

            if (this.exchange_start_time) conf.active.exchange_start_time = this.exchange_start_time
            if (this.map_center_point_lat && this.map_center_point_lng) {
                conf.active.map_center_point = {
                    type: 'Point',
                    coordinates: [this.map_center_point_lng, this.map_center_point_lat]
                }
            }
            if (this.min_num || this.min_num === 0) conf.active.min_num = Number(this.min_num)
            if (this.max_num || this.max_num === 0) conf.active.max_num = Number(this.max_num)
            if (this.enter_types === 2) {
                if (!this.have_password && !this.password) return showToast('请输入活动密码', 3)
                if (this.password && this.password.length < 3) return showToast('活动密码不得少于3位', 3)
                if (this.password && this.password.length > 20) return showToast('活动密码不得大于20位', 3)
                if (this.old_password) conf.active.password = this.old_password
                if (this.password) conf.active.password = this.password
            } else {
                conf.active.password = 1
            }
            if (this.screen_pic) {
                conf.active.screen_pic = this.screen_pic
                conf.active.screen_pic_count_down = this.screen_pic_count_down
                conf.active.screen_pic_buttom_show = this.screen_pic_buttom_show
            }
            if (this.arrive_end_num) conf.active.arrive_end_num = this.arrive_end_num
            if (this.news_id) {
                conf.active.news = {
                    news_id: this.news_id,
                    news_title: this.news_title || ''
                }
            }
            if (this.rank_set['gift_goods']) {
                const integral_set = {...this.integral_set}
                if (!integral_set.unit) integral_set.unit = this.default_integral_unit
                if (!integral_set.exam_reward_num) integral_set.exam_reward_num = 1

                if (this.rank_set.registerRewardIntegral) {
                    const register_integral = Number(integral_set.register_integral)
                    if (isNaN(register_integral) || register_integral < 0) {
                        return showToast('报名奖励积分填写不正确', 3)
                    }
                    integral_set.register_integral = register_integral
                }

                conf.active.integral = integral_set
            }
            if (this.qrcode_logo) conf.active.qrcode_logo = this.qrcode_logo
            if (this.share_image) conf.active.share_image = this.share_image
            if (this.share_title) conf.active.share_title = this.share_title

            // 出差里程兑换
            if (this.open_business_kilometers) {
                let {rate, province_rate, country_rate} = this.business_trip
                rate = Number(rate) || 1
                conf.active.business_trip = {
                    rate,
                    province_rate: Number(province_rate) || rate,
                    country_rate: Number(country_rate) || rate
                }
            }
            // 出差里程兑换


            // 跑步运动
            if (this.rank_set?.['run_sport']) {
                const run_trace = JSON.parse(JSON.stringify(this.run_trace))
                run_trace.run_kilo = Number(run_trace.run_kilo)
                run_trace.exchange_step = Number(run_trace.exchange_step)

                const isNotNum = num => isNaN(num) || num <= 0
                if (isNotNum(run_trace.run_kilo) || isNotNum(run_trace.exchange_step)) {
                    return showToast('跑步运动里程兑换比例填写不正确', 1, 3000)
                }
                conf.active.run_trace = run_trace

                if (this.pace_limit) {
                    const pace_limit = Math.floor(Number(this.pace_limit))
                    if (isNaN(pace_limit) || pace_limit < 0) {
                        return showToast('跑步运动配速限制填写不正确', 1, 3000)
                    }
                    conf.active.pace_limit = pace_limit
                }
            }
            // 跑步运动

            const submit = this.submit
            if (submit.begin && submit.begin.length < 15) submit.begin += '00:00:00'
            if (submit.end && submit.end.length < 15) submit.end += '23:59:59'
            conf.active.submit = submit


            const detail_icon_list = this.getDetailIconList()
            if (detail_icon_list === false) return false
            conf.active.detail_icon_list = detail_icon_list

            if (this.rank_set['redpack']) {
                const rules = this.redpack_rules
                if (rules.open && rules.types === 2) {
                    const min = Number(rules.amount.min)
                    const max = Number(rules.amount.max)
                    if (isNaN(min) || !min || isNaN(max) || !max) return showToast('请输入正确的红包金额', 6)
                    if (min > max) return showToast('红包最小金额不得大于最大金额', 6)
                }
                conf.active.redpack_rules = rules
            }

            const must_submit_data = this.getMustSubmitData()
            if (must_submit_data === false) return false
            if (must_submit_data) conf.must_submit = must_submit_data

            if (this.reward_step_exam_conf) {
                const reward_step_by_exam = this.getRewardStepByExam()
                if (reward_step_by_exam === false) return false
                if (reward_step_by_exam) conf.active.reward_step_by_exam = reward_step_by_exam
            }

            if (this.top_rank_banner.length) {
                // conf.top_rank_banner = base64.encode(JSON.stringify(this.top_rank_banner))
                conf.active.top_rank_banner = this.top_rank_banner
            }

            if (this.rank_set?.active_details_notice) {
                conf.active.active_details_notice = this.active_details_notice
            }


            if (this.template_id) conf.active.template_id = this.template_id


            if (this.ai_sport_list.length) {
                conf.AI_motion = {
                    motion_list: this.ai_sport_list
                }
            }


            if (this.rank_set?.['open_sport_moment']) {
                conf.active.sport_moment_type = this.sport_moment_type

                // 到达某个里程才能获得运动圈发布机会
                if (this.rank_set['limit_sport_moment']) {
                    const limit_sport_moment_set_list = this.checkLimitSportMomentSetList()
                    if (!limit_sport_moment_set_list) return
                    if (limit_sport_moment_set_list.length) {
                        conf.active.limit_sport_moment_set_list = limit_sport_moment_set_list
                    }
                }

                if (this.sport_moment_release_limit) {
                    conf.active.sport_moment_release_limit = this.sport_moment_release_limit
                }
            }
            if (this.must_submit_not_modifiable) conf.active.must_submit_not_modifiable = 1

            // 排行榜前几名奖励
            if (this.rank_set?.['reward_team_person']) {
                const reward_amount = JSON.parse(JSON.stringify(this.reward_amount))
                for (let i = 0; i < reward_amount.person_team_set.length; i++) {
                    const item = reward_amount.person_team_set[i]
                    if (item.open) {
                        let types_name = '个人'
                        if (item.types === 2) types_name = '队伍'
                        for (let j = 0, len = item.reward.length; j < len; j++) {
                            const v = item.reward[j]
                            v.name = `第${j + 1}名`
                            v.amount.min = Number(v.amount.min)
                            v.amount.max = Number(v.amount.max)
                            if (isNaN(v.amount.min) || isNaN(v.amount.max)) {
                                return showToast(`${types_name}昨日排行奖励规则，${v.name}金额填写不正确`, 6, 3000)
                            }
                            if (v.amount.min && !v.amount.max) v.amount.max = v.amount.min
                            if (v.amount.max && !v.amount.min) v.amount.min = v.amount.max
                            if (v.amount.min < 0.3) {
                                return showToast(`奖励规则最低金额不能低于0.3元，${types_name}昨日排行${v.name}金额低于0.3元，请重新设置`, 6, 3000)
                            }
                            if (v.amount.min > v.amount.max) {
                                return showToast(`${types_name}昨日排行奖励规则，${v.name}最小金额不得大于最大金额`, 6, 3000)
                            }
                        }
                    }

                }
                conf.active.reward_amount = reward_amount
            }
            // 排行榜前几名奖励


            // 阅读文章奖励积分
            if (this.rank_set?.reading_reward) {
                const reading_reward = JSON.parse(JSON.stringify(this.reading_reward))
                if (reading_reward.open) {
                    const category_id_list = []
                    for (let i = 0, len = reading_reward.rules.length; i < len; i++) {
                        const item = reading_reward.rules[i]
                        if (category_id_list.includes(item.category_id)) {
                            return showToast(`阅读文章奖励${this.integralUnit}规则设置有多个设置选择了相同的文章分类${item.name}，请重新配置`, 6, 5000)
                        }
                        category_id_list.push(item.category_id)
                        if (!item.category_id) {
                            return showToast(`阅读文章奖励${this.integralUnit}规则第${i + 1}个设置未选择文章分类`, 6, 3000)
                        }
                        item.seconds = Number(item.seconds)
                        if (isNaN(item.seconds) || item.seconds === 0) {
                            return showToast(`阅读文章奖励${this.integralUnit}规则第${i + 1}个阅读时长填写有误，请重新填写`, 6, 3000)
                        }
                        item.integral.min = Number(item.integral.min)
                        item.integral.max = Number(item.integral.max)
                        if (item.integral.min && !item.integral.max) item.integral.max = item.integral.min
                        if (item.integral.max && !item.integral.min) item.integral.min = item.integral.max
                        if (isNaN(item.integral.min) || isNaN(item.integral.max) || item.integral.min === 0 || item.integral.max === 0) {
                            return showToast(`阅读文章奖励${this.integralUnit}规则第${i + 1}个奖励${this.integral_set?.unit || this.default_integral_unit}填写有误，请重新填写`, 6, 5000)
                        }
                        if (item.integral.min > item.integral.max) {
                            return showToast(`阅读文章奖励${this.integralUnit}规则第${i + 1}个奖励${this.integral_set?.unit || this.default_integral_unit}填写有误，最小值不得大于最大值，请重新填写`, 6, 5000)
                        }
                    }
                }
                conf.active.reading_reward = reading_reward
            }
            // 阅读文章奖励积分

            if (this.rank_set['medal_open']) conf.active.medal_mileage_hide = this.medal_mileage_hide


            // 接力赛
            const {
                open: runOneByOne_open,
                user_select_team,
                per_person_exchange_kilo,
                user_select_max,
                all_person
            } = this.runOneByOne
            if (runOneByOne_open || user_select_team || per_person_exchange_kilo || user_select_max !== '1' || all_person) {
                conf.active.runOneByOne = this.runOneByOne
                if (runOneByOne_open) {
                    if (!per_person_exchange_kilo) return showToast('请设置每一棒接力队员的兑换里程', 6)
                    if (!all_person) return showToast('请设置每个队伍的接力棒数', 6)
                }

            }
            // 接力赛


            // 达标率 || 达标天数
            if (this.rank_set['person_finished_percent'] || this.rank_set['personal_finished_days']) {
                const finished_step_num = Math.floor(this.finished_step_num)
                if (isNaN(finished_step_num) || finished_step_num <= 0) {
                    return showToast('每日达标步数设置有误，请填写正确的步数', 6, 3000)
                }
                conf.active.finished_step_num = finished_step_num
            }


            if (this.satellite) conf.active.satellite = this.satellite
            if (this.user_point_map_center) conf.active.user_point_map_center = this.user_point_map_center

            if (this.map_rotate) {
                const rotate = Math.floor(this.map_rotate)
                if (isNaN(rotate) || rotate < 0 || rotate > 360) {
                    return showToast('地图旋转角度设置有误，请填写0-360之间的整数', 5, 3000)
                }
                conf.active.map_rotate = rotate
            }

            if (this.only_join_user_look_ranklist) conf.active.only_join_user_look_ranklist = 1

            if (this.ip_set.open) conf.active.ip_set = this.ip_set

            if (this.rank_set['multi_top_rank_finishedAndScore'] && this.score_and_finished_have_set) {
                conf.active.score_and_finished = this.score_and_finished
            }

            if (this.light_city_reward_step && this.rank_set?.['light_map_point_reward_step']) {
                const num = Number(this.light_city_reward_step)
                if (isNaN(num)) return showToast('点亮城市奖励步数规则填写有误，请填写数字', 6, 3000)
                conf.active.light_city_reward_step = num
            }

            if (this.rank_set?.['party_oath']) {
                if (this.take_oath_bg_img) conf.active.take_oath_bg_img = this.take_oath_bg_img
                if (this.take_oath_square_open) conf.active.take_oath_square_open = this.take_oath_square_open
            }

            if (this.rank_set?.['must_subscribe'] && this.official_account_open) {
                conf.active.official_account_open = this.official_account_open
            }

            const {article_bind_exam, reward_step_by_reading} = this.rank_set || {}
            if (article_bind_exam || reward_step_by_reading) {
                const study_news_set = JSON.parse(JSON.stringify(this.study_news_set))
                if (reward_step_by_reading) {
                    const min = Math.floor(study_news_set.reward_step.min)
                    const max = Math.floor(study_news_set.reward_step.max)
                    if (isNaN(min) || isNaN(max) || min < 0 || max < 0) {
                        return showToast('学习奖励步数设置有误，请检查', 6, 3000)
                    }
                    if (min > max) return showToast('学习奖励步数设置有误，最低步数不能大于最高步数', 6, 3000)
                    study_news_set.reward_step.min = min
                    study_news_set.reward_step.max = max
                } else {
                    delete study_news_set.reward_step
                }
                conf.active.study_news_set = study_news_set
            }

            // diy地图页面兑换按钮
            if (this.rank_set['mapPointDetailsButtonDIY'] && this.map_page_just_show_diy_exchange_button) {
                conf.active.map_page_just_show_diy_exchange_button = this.map_page_just_show_diy_exchange_button
                conf.active.map_page_diy_exchange_button_set = this.map_page_diy_exchange_button_set
            }

            conf.active.map_page_no_exchange_tips = this.map_page_no_exchange_tips

            if (this.join_location_distance_limit.open) {
                const set = JSON.parse(JSON.stringify(this.join_location_distance_limit))
                const {name, latitude, longitude} = set.location
                if (!name || !latitude || !longitude) return showToast('请设置活动中心点位置', 3, 2000)

                const distance = Number(set.distance)
                if (isNaN(distance) || distance <= 0) return showToast('请输入正确的活动位置距离限制', 3, 3000)
                set.distance = distance

                conf.active.join_location_distance_limit = set
            }

            if (this.map_page_top_scroll_tips_hide) {
                conf.active.map_page_top_scroll_tips_hide = this.map_page_top_scroll_tips_hide
            }

            if (this.rank_set['mystery_box'] && this.blind_box_lottery.lottery_id) {
                conf.active.blind_box_lottery = this.blind_box_lottery
            }

            if (this.map_types === 101) {
                if (this.time_period_step && this.step_unlock_picture_fragments.open) {
                    this.type_id = 5
                    return this.$uni.showModal('不能同时开启【统计指定时间段步数功能】和【步数解锁拼图碎片功能】，请关闭其中一个。')
                }
                if (this.time_period_step) conf.active.time_period_step = this.time_period_step

                if (this.step_unlock_picture_fragments.open) {
                    if (!this.step_unlock_picture_fragments.image_src) return showToast('请设置碎片图片', 5)
                    if (!this.step_unlock_picture_fragments.col || !this.step_unlock_picture_fragments.row) {
                        return showToast('请设置拼图碎片的数量', 5)
                    }
                    if (!this.step_unlock_picture_fragments.mileage) return showToast('请设置解锁碎片的里程数', 5)
                    conf.active.step_unlock_picture_fragments = this.step_unlock_picture_fragments
                }
            }

            if (this.rank_set.batch_import && this.enter_types === 4) {
                conf.active.batch_import_label = {
                    username: this.batch_import_label.username || '请输入账号',
                    password: this.batch_import_label.password || '请输入密码'
                }
            }


            if (this.step_lottery_list.length) conf.active.step_lottery_list = this.step_lottery_list

            if (this.enter_activity_popup_news.id) {
                conf.active.enter_activity_popup_news = this.enter_activity_popup_news
            }


            if (this.enter_activity_auto_exchange) {
                conf.active.enter_activity_auto_exchange = this.enter_activity_auto_exchange
            }

            if (this.exam_open && this.rank_set['mapPointMustGoToArticle'] && this.answer_need_read.open) {
                conf.active.answer_need_read = this.answer_need_read
            }

            if (this.point_news_to_details_page_show && this.rank_set['mapPointMustGoToArticle']) {
                conf.active.point_news_to_details_page_show = this.point_news_to_details_page_show
            }

            if (this.rank_set['sport_step_prize_set']) {
                if (this.exam_prize_set.prize_list?.length) conf.active.exam_prize_set = this.exam_prize_set
                if (this.exam_prize_icon_diy_open) conf.active.exam_prize_icon_diy = this.exam_prize_icon_diy
            }

            if (this.rank_set['stepRewardIntegralForMinMax']) {
                conf.active.integral ||= {}

                const exchange_rules = JSON.parse(JSON.stringify(this.exchange_rules))

                const min_step_num = Math.floor(exchange_rules.rushing_set.min_step_num)
                if (isNaN(min_step_num) || min_step_num < 0) {
                    return this.$uni.showToast('第一个点位解锁步数 填写不正确')
                }
                exchange_rules.rushing_set.min_step_num = min_step_num

                const per_round_step_num = Math.floor(exchange_rules.rushing_set.per_round_step_num)
                if (isNaN(per_round_step_num) || per_round_step_num < 0) {
                    return this.$uni.showToast('第二个点位开始的解锁步数 填写不正确')
                }
                exchange_rules.rushing_set.per_round_step_num = per_round_step_num

                const per_step = Math.floor(exchange_rules.per_step)
                if (isNaN(per_step) || per_step < 0) return this.$uni.showToast('步数奖励倍数值 填写不正确')
                exchange_rules.per_step = per_step

                const daily_rushed_round_num = Math.floor(exchange_rules.rushing_set.daily_rushed_round_num)
                if (isNaN(daily_rushed_round_num) || daily_rushed_round_num < 0) {
                    return this.$uni.showToast('每天解锁的点位上限 填写不正确')
                }
                exchange_rules.rushing_set.daily_rushed_round_num = daily_rushed_round_num

                const daily_exam_times = Math.floor(exchange_rules.rushing_set.daily_exam_times)
                if (isNaN(daily_exam_times) || daily_exam_times < 0) {
                    return this.$uni.showToast('每天解锁的答题上限 填写不正确')
                }
                exchange_rules.rushing_set.daily_exam_times = daily_exam_times

                conf.active.integral.exchange_rules = exchange_rules
            }

            if (this.rank_set['lotteryForUserIntegral'] && this.lottery_cost_integral) {
                const integral = Number(this.lottery_cost_integral)
                if (isNaN(integral) || integral < 0) {
                    return this.$uni.showToast(`每次抽奖消耗的${this.integralUnit} 填写不正确`)
                }
                conf.active.lottery_cost_integral = integral
            }

            let conf_str = JSON.stringify(conf)
            conf_str = conf_str.replace(/·/g, '-')
            data.conf = base64['encode'](conf_str)

            this.saveAjax(data)
        },

        saveAjax(data) {

            data.access_token = app.globalData['access_token']
            data.types = this.types;


            this.$uni.showLoading(this.id ? '保存中...' : '活动创建中...',)

            xwy_api.ajax({
                url: 'front.flat.sport_step.admin/create_active',
                data,
                success: res => {
                    uni.hideLoading()
                    if (!res.status) return this.$uni.showModal(res.info || '保存失败请重试',)


                    this.updatePageData()

                    if (this.id) {
                        this.$uni.showToast('保存成功', 'success')
                        return this.$uni.navigateBack(1, {delay: 1000})
                    }

                    this.success = true
                }
            })


        },


        // 活动修改/创建成功，如果页面栈有详情或列表页面，更新页面数据
        updatePageData() {
            const pages = getCurrentPages()

            const update_page_list = [
                {
                    route: 'pages/activity/user/detail',
                    _function: page => {
                        page.$vm.getDetail(true)
                    }
                },
                {
                    route: 'pages/activity/admin/manage',
                    _function: page => {
                        page.$vm.getDetail()
                    }
                },
                {
                    route: 'pages/activity/user/index',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getActivityList()
                    }
                },
                {
                    route: 'pages/activity/user/activity_list',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getList()
                    }
                }
            ]

            update_page_list.forEach(v => {
                const page = pages.find(n => n.route === v.route)
                if (page) v._function(page)
            })
        },

        lookMyActivityList() {
            const list_page_route = 'pages/activity/user/activity_list'
            const pages = getCurrentPages()
            const list_page_index = pages.findIndex(v => {
                return v.route === list_page_route && v['options'].type && v['options'].type === 'create'
            })
            if (list_page_index === -1) return this.$uni.redirectTo(`/${list_page_route}?type=create`)

            this.$uni.navigateBack(pages.length - list_page_index - 1)
        }
    }
}
</script>

<style lang="scss">
.page {
    padding-top: 50px;
    padding-bottom: 100px;
    background-color: #fff;
}

.scroll-view {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    width: 100vw;
    padding: 0 10px;
    border-bottom: 10px solid #f8f8f8;
    box-sizing: border-box;

    .scroll-view-item {
        padding: 0 6px;
        line-height: 40px;
        height: 40px;
        white-space: nowrap;
        box-sizing: border-box;
    }

    .active-type {
        color: #007AFF;
        border-bottom: 2px solid #007AFF;
    }
}


.form {
    padding: 10px 0;

    .form-item {
        padding: 10px;

        .bottom {
            border-bottom: 1px solid #eee;

            .textarea {
                width: 100%;
                line-height: 16px;
                padding: 10px 0;
            }

            .inline-text {
                line-height: 36px;
                color: #333;
                font-size: 14px;
            }

            .input {
                width: 100%;
                line-height: 40px;
                height: 40px;
            }

            .view {
                padding: 8px 0;
                width: 100%;
            }
        }
    }
}


.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
    line-height: calc((100vw - 50px) / 3);
    margin: 5px;
}

.image-item {
    width: calc(100vw - 20px);
    height: 50vw;
    border-radius: 5px;
}


.image-view {
    position: relative;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}


.top-rank-banner-item {
    padding: 5px;
    position: relative;

    image {
        width: calc((100vw - 50px) / 3);
        height: calc((100vw - 50px) / 3);
        border-radius: 5px;
    }

    .del-image-item {
        right: 8px;
    }
}


.reward_step_by_exam_input {
    border-bottom: 1px solid #eee;
    margin: 0 5px;
}

.bottom-btn-view {
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.login-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}


.save-success {
    padding-top: 10vh;

    .icon {
        padding-bottom: 50px;
    }
}


/* #ifdef H5 */

@media screen and (min-width: 500px) {
    .scroll-view {
        width: 500px;
        left: calc(((100vw - 500px) / 2) - 10px);
    }

    .image-item {
        width: 480px;
        height: 200px;
    }

    .top-rank-banner-item image, .add-image {
        width: 150px;
        height: 150px;
        line-height: 150px;
    }
}

/* #endif */
</style>
