<template>
	<view class="page bg-background">
		<view class="tips p10 bg-white color-sub font14 text-center">
			<text>展示2小时内的数据导出记录</text>
			<text class="pl5" v-if="list.length">(共{{list.length}}条记录)</text>
		</view>

		<view class="list">
			<view class="item bg-white" v-for="(item, index) in list" :key="index" @click="copyUrl(item.url)">
				<view class="color-title">{{item.title}}</view>
				<view class="color-sub font14 pt5" style="padding-bottom: 10px;">
					<uni-dateformat :date="item.add_time" format="yyyy-MM-dd hh:mm:ss" :threshold="[0, 0]"/>
				</view>
				<view class="flex-kai" style="border-top: 1px solid #eee;">
					<view></view>
					<view class="font14 color-primary" style="padding-top: 10px;">复制下载地址</view>
				</view>
			</view>
		</view>

		<view v-if="!list.length" class="text-center" style="padding-top: 10vh;">
			<text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
			<view class="color-sub">2小时内没有导出任何数据哦~</view>
		</view>

    
	</view>
</template>

<script>
	import my_storage from '../../../utils/storage.js'

	export default {
		data() {
			return {
				list: my_storage.getExportExcelRecord()
			}
		},
		methods: {
			copyUrl(url) {
				uni.setClipboardData({
				    data: url,
				    success() {
				        uni.showToast({
				            title: '复制成功',
				            icon: 'success',
				            duration: 1000
				        })
				    }
				})
			}
		}
	}
</script>

<style>
.page {
	min-height: 100vh;
}
.list {
	padding-bottom: 20px;
}
.item {
	margin: 10px;
	padding: 10px;
	border-radius: 10px;
}
</style>
