<template>
    <view class="page">
        <view class="p10">
            <image class="logo" mode="widthFix" v-if="logo" :src="logo" @click="previewLogo" />
        </view>
        <view class="use flex-all-center">
            <view
                class="use-btn color-white bg-primary text-center"
                hover-class="navigator-hover"
                @click="use"
            >使用该模版</view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import base64 from '@/utils/base64.js'
import defaultTemplate from './default-template'
export default {
    data() {
        return {
            logo: ''
        }
    },

    onLoad(params) {
        this.id = params.id
        this.template_id = params.template_id
        this.getTemplateDetails()
    },

    methods: {
        async getTemplateDetails() {
            const id = this.template_id
            if (!id || id === defaultTemplate.id) return this.getDefaultTemplateDetails()


            this.$uni.showLoading('加载中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.page_set.pageDiy/tpl_details',
                data: { id }
            })
            uni.hideLoading()
            const template_info = res?.data?.['tpl_details']
            if (template_info.title) {
                this.template_title = template_info.title
                uni.setNavigationBarTitle({ title: template_info.title || '模版' })
                this.logo = template_info.logo
            }
        },

        getDefaultTemplateDetails() {
            this.template_title = defaultTemplate.title
            uni.setNavigationBarTitle({ title: defaultTemplate.title })
            this.logo = defaultTemplate.logo
        },

        use() {
            const template_title = this.template_title || ''
            uni.showModal({
                title: '提示',
                content: `确定使用${template_title}${template_title.endsWith('模版') ? '' : '模版'}？模版使用成功无法撤销。`,
                success: res => res.confirm && this.useTemplate()
            })
       },

       async useTemplate() {
           uni.showLoading({
               title: '使用中...',
               mask: true
           })
           let details = app.globalData['activity_detail']

           if (!details) {
               const res = await this.xwy_api.request({
                   url: 'front.flat.sport_step.active_list/active_details',
                   data: {active_id: this.id}
               })

               details = res.data['active_details']
               if (!details) {
                   uni.hideLoading()
                   return this.xwy_api.alert(res?.info || '活动数据获取失败。模版使用失败!')
               }
           }


           details.conf.active.template_id = this.template_id
           const save_data = {
               active_id: this.id,
               content: details.content || '',
               logo: details.logo || '',
               name: details.name || '',
               organizer: details.organizer || '',
               begin_time: details.begin_time || '',
               end_time: details.end_time || '',
               types: details.types,
               conf: base64['encode'](JSON.stringify(details.conf))
           }

           const res = await this.xwy_api.request({
               url: 'front.flat.sport_step.admin/create_active',
               data: save_data
           })

           uni.hideLoading()

           if (res?.status !== 1) return this.xwy_api.alert(res?.info || '模版使用失败!')

           this.xwy_api.alert('模版使用成功！需重新进入小程序才能查看新模版。', { title: '模版使用成功'})
       },

       previewLogo() {
           uni.previewImage({
               urls: [this.logo]
           })
       }
    }
}
</script>

<style lang="scss">
.page {
    padding-bottom: 100px;
}
.logo {
    width: 100%;
}
.use {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100vw;
    padding: 15px 0;
    background-color: rgba(255, 255, 255, .9);
    border-radius: 10px 10px 0 0;

    .use-btn {
        width: 300px;
        line-height: 44px;
        border-radius: 22px;
    }
}
</style>
