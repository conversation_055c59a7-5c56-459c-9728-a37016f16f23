<template>
    <view>
        <view class="template-list flex-row flex-wrap">
            <view
                class="template-item text-center"
                v-for="item in template_list"
                :key="item.id"
                hover-class="navigator-hover"
                @click="toDetails(item.id)"
            >
                <image :src="item.logo" mode="aspectFill"/>
                <view class="color-title ellipsis">{{ item.title }}</view>
                <!--<view class="flex-center pt5">
                    <view class="color-sub" @click="previewLogo(item.logo)">预览</view>
                    <view class="color-primary" @click="use(item)">使用</view>
                </view>-->
            </view>
        </view>

        <view v-if="loading" class="text-center">
            <view v-if="current_page === 1" style="width: 100%; height: 30vh;"></view>
            <load-ani></load-ani>
        </view>

        <view v-if="!template_list.length && !loading" class="text-center" style="padding-top: 15vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无可用模版</view>
        </view>
    </view>
</template>

<script>
import login from '@/utils/api/login.js'
import defaultTemplate from './default-template'
export default {
    data() {
        return {
            loading: true,
            template_list: [],
            current_page: 1,
            is_last_page: false
        }
    },

    onLoad(params) {
        this.id = params.id
        login.uniLogin(() => {
            this.init()
        })
    },

    onReachBottom() {
        this.loadTemplateList()
    },

    methods: {
        async init() {
            await this.getTemplateList()
            this.loading = false
        },

        async loadTemplateList() {
            if (this.loading || this.is_last_page) return
            this.loading = true
            await this.getTemplateList()
            this.loading = false
        },

        async getTemplateList() {
            if (this.current_page === 1) {
                this.template_list = []
                this.is_last_page = false
            }
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.page_set.pageDiy/tpl_list',
                data: {
                    page: this.current_page,
                    perpage: 20
                }
            })

            const data = res?.data?.['tpl_list']
            if (!data) {
                this.is_last_page = true
                return
            }

            const list = data.data || []
            const new_list = this.setListData(list)
            this.template_list = this.current_page === 1 ? [defaultTemplate, ...new_list] : [...this.template_list, ...new_list]
            this.is_last_page = data.is_lastpage
            this.current_page++
        },

        setListData(list) {
            const newList = []
            list.forEach(v => {
                newList.push({
                    id: v.id,
                    title: v.title,
                    logo: v.logo
                })
            })
            return newList
        },

        toDetails(id) {
            uni.navigateTo({
                url: `./details?id=${this.id}&template_id=${id}`
            })
        }
    }
}
</script>

<style lang="scss">
.template-list {
    padding: 15rpx;

    .template-item {
        width: 220rpx;
        padding: 10px 10rpx;

        image {
            width: 220rpx;
            height: 480rpx;
            border-radius: 5px;
        }
    }
}
</style>
