<template>
    <view class="page">

        <view class="type-list flex-row bg-white">
            <view
                class="type-item text-center color-content"
                v-for="(item, index) in type_list"
                :key="index"
                :style="'width: calc(100% / ' + type_list.length + ');'"
                :class="{'active-type': item.id === type_id}"
                @click="changeType(item.id)"
            >{{ item.title }}
            </view>
        </view>


        <view class="tips font14">可按照天、周、半个月、一个月 导出任意指定时间段的用户步数排行榜数据。</view>


        <uni-calendar
            v-if="type_id === 1 || type_id === 2 || type_id === 4"
            :lunar="true"
            :showMonth="false"
            :selected="selected"
            :startDate="min_year + '-1-1'"
            :endDate="endDate"
            @change="calenderChange"
        />


        <template v-if="type_id === 3">
            <view class="year flex-all-center">
                <view
                    class="flex-all-center"
                    style="width: 40px;height: 40px;"
                    @click="changeYear($event, '-')"
                >
                    <text
                        class="iconfont icon-back font18"
                        :class="{'color-sub': sel_year === min_year}"
                    ></text>
                </view>
                <picker :range="all_year" :value="sel_year_index" @change="changeYear($event, 'sel')">
                    <view class="flex-all-center font18" style="width: 80px;height: 40px;">
                        {{ sel_year }}
                    </view>
                </picker>
                <view
                    class="flex-all-center"
                    style="width: 40px;height: 40px;"
                    @click="changeYear($event, '+')"
                >
                    <text
                        class="iconfont icon-more font18"
                        :class="{'color-sub': sel_year === max_year}"
                    ></text>
                </view>
            </view>
            <view class="month flex-row flex-wrap">
                <!-- 这里循环为什么要item和index呢，因为在不同环境item是不一样的，开发工具上是从0开始，H5里						面，item是从1开始的。。。所以要用index，因为index在不同环境下都是一致从0开始的 -->
                <view
                    class="month-item flex-all-center"
                    v-for="(item, index) in 12"
                    :key="index"
                    @click="monthChange(index + 1)"
                >
                    <text
                        class="font24 color-content"
                        :class="{'month-active font28': index + 1 === sel_month}"
                    >{{ index + 1 }}
                    </text>
                    <text class="color-sub" :class="{'month-active': index + 1 === sel_month}">月</text>
                </view>
            </view>
        </template>

        <view class="text-center pt15 color-content">
            <template v-if="type_id === 1">
                <text>已选</text>
                <text class="color-primary pl5 font18">{{ sel_day }}</text>
            </template>
            <template v-if="type_id === 2 || type_id === 4">
                <text>已选</text>
                <text class="color-primary plr5 font18">{{ selected[0].date }}</text>
                <text>到</text>
                <text class="color-primary pl5 font18">{{ selected[selected.length - 1].date }}</text>
            </template>
            <template v-if="type_id === 3">
                <text>已选</text>
                <text class="color-primary plr5 font18">{{ start_day }}</text>
                <text>到</text>
                <text class="color-primary pl5 font18">{{ end_day }}</text>
            </template>
        </view>


        <view class="export-btn-view bg-white">
            <view
                class="export-btn text-center bg-primary color-white"
                hover-class="navigator-hover"
                @click="exportData"
            >导出数据
            </view>
        </view>

        <uni-popup ref="not_export_tips" type="center">
            <view class="uni_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('not_export_tips')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top" style="padding: 20px; padding-top: 50px;">
                    <text class="iconfont icon-export-excel color-green" style="font-size: 80px;"></text>
                    <view class="font14 color-sub">未开通导出汇总数据功能</view>
                    <view class="font14 color-sub">请联系客服开通</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <button
                        open-type="contact"
                        class="uni_popup-btn bg-green color-white font16"
                        @click="uniPopupClose('not_export_tips')"
                    >联系客服开通
                    </button>
                </view>
            </view>
        </uni-popup>

        <uni-popup ref="export_success" type="center">
            <view class="uni_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('export_success')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top bg-primary color-white" style="padding: 20px; padding-top: 50px;">
                    <icon type="success" size="80" color="#ffffff"></icon>
                    <view class="font18">导出成功</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ export_success_tips }}
                    </view>
                    <view
                        class="uni_popup-btn bg-info color-white"
                        hover-class="navigator-hover"
                        @click="copyDownloadSrc(false)"
                    >复制下载地址
                    </view>
                </view>
            </view>
        </uni-popup>

        
    </view>
</template>

<script>
const app = getApp()
import login from '../../../utils/api/login.js'
import xwy_api from '../../../utils/api/xwy_api.js'
import utils from '../../../utils/utils.js'
import my_storage from '../../../utils/storage.js'

// 可以选择的最大和最小年份
const min_year = 2021  // 因为是2021年才开始做的，所以数据是从2021年才开始记录的
const max_year = utils.getYearMonthDay().year

export default {
    data() {
        return {
            type_list: [
				{title: '1-7天', id: 0},
                // {title: '日', id: 1},
                // {title: '周', id: 2},
                {title: '半月', id: 4},
                {title: '一月', id: 3}
            ],
            type_id: null,
            id: '',
            detail: {},
            selected: [],
            // endDate: `${utils.getYearMonthDay().year}-${utils.getYearMonthDay().month}-31`,
            endDate: utils.getDay(),
            sel_day: utils.getDay(),
            sel_year: max_year,
            sel_year_index: 0,
            sel_month: utils.getYearMonthDay().month,
            all_year: (function () {
                const year_list = []
                let year = utils.getYearMonthDay().year
                while (year >= min_year) {
                    year_list.push(year)
                    year--
                }
                return year_list
            })(),
            min_year,
            max_year,
            start_day: '',
            end_day: '',
            export_success_tips: ''
        }
    },
    onLoad(e) {
        uni.showLoading({
            mask: app.globalData.evn_version === 'trial' ? false : true
        })
        this.id = e.id
		this.active_name = e.active_name
		this.begin_time = e.begin_time
		this.end_time = e.end_time
        this.week_month_total = Number(e.week_month_total)
        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                uni.showModal({
                    title: err.errTitle || '提示',
                    content: err.errMsg,
                    showCancel: false
                })
                return false
            }
            uni.hideLoading()

			this.changeType(4)
        })
    },
    methods: {

        changeType(id) {
            if (id === this.type_id) return false
			if (id === 0) {
				uni.navigateTo({
					url: './export_total_step?id=' + this.id + '&name=' + this.active_name + '&begin_time=' + this.begin_time + '&end_time=' + this.end_time
				})
				return false
			}
            this.selected = []
            this.type_id = id
            if (id === 2 || id === 4) this.setSelected()
            if (id === 3) this.currentMonthStartEnd()
        },

        calenderChange(e) {
            const day = e.fulldate
            this.sel_day = day
            if (this.type_id === 2 || this.type_id === 4) this.setSelected(day)
        },

        setSelected(sel_day = this.sel_day) {
            const selected = []

            // 选择周
            if (this.type_id === 2) {
                const week_day = utils.getWeekDate(sel_day)
                console.log(week_day)
                week_day.forEach(v => selected.push({date: v, info: '已选'}))
            }

            // 选择半个月
            if (this.type_id === 4) {
                const {year, month, day} = utils.getYearMonthDay(sel_day)
                // 上半月(1-15号)
                let start_day = 1
                let end_day = 15
                // 下半月(16号到当月最后一天)
                if (day > 15) {
                    start_day = 16
                    end_day = new Date(year, month, 0).getDate()
                }
                for (let i = start_day; i <= end_day; i++) {
                    selected.push({date: `${year}-${month}-${i}`, info: '已选'})
                }
            }

            this.selected = selected
        },

        changeYear(e, type) {
            let index, sel_year
            if (type === 'sel') {
                index = Number(e.detail.value)
                sel_year = this.all_year[index]
            }
            if (type === '+' || type === '-') {
                sel_year = this.sel_year
                if (type === '+') sel_year++
                if (type === '-') sel_year--
                if (sel_year > max_year) sel_year = max_year
                if (sel_year < min_year) sel_year = min_year
                index = this.all_year.findIndex(v => sel_year === v)
            }

            this.sel_year = sel_year
            this.sel_year_index = index
            this.currentMonthStartEnd()
        },

        monthChange(month) {
            this.sel_month = month
            this.currentMonthStartEnd()
        },

        currentMonthStartEnd() {
            const month_day_count = new Date(this.sel_year, this.sel_month, 0).getDate()
            const year_month = this.sel_year + '-' + this.sel_month
            this.start_day = year_month + '-1'
            this.end_day = year_month + '-' + month_day_count
        },

        getExportSubmitData() {
            const data = {
                active_id: this.id,
                types: this.type_id,
                date_start: this.sel_day,
                date_end: this.sel_day
            }
            if (this.type_id === 2 || this.type_id === 4) {
                data.date_start = this.selected[0].date
                data.date_end = this.selected[this.selected.length - 1].date
            }
            if (this.type_id === 3) {
                data.date_start = this.start_day
                data.date_end = this.end_day
            }

            // 日期年月补0
            data.date_start = this.dateFill0(data.date_start)
            data.date_end = this.dateFill0(data.date_end)
            return data
        },

        // 时间补月日补0或者去掉0
        dateFill0(date, type = true) {
            let concat_opt = '-';
            let date_arr = date.split('-');
            if (date_arr.length <= 1) {
                date_arr = date.split('/');
                concat_opt = '/'
            }
            if (date_arr.length !== 3) {
                uni.showModal({
                    title: '提示',
                    content: '请传入正确的日期格式 ' + date,
                    showCancel: false
                })
                return date
            }

            if (type) {
                if (date_arr[1].length === 1) date_arr[1] = '0' + date_arr[1];
                if (date_arr[2].length === 1) date_arr[2] = '0' + date_arr[2];
            } else {
                date_arr[1] = Number(date_arr[1])
                date_arr[2] = Number(date_arr[2])
            }

            return `${date_arr[0]}${concat_opt}${date_arr[1]}${concat_opt}${date_arr[2]}`
        },

        async exportData() {
            if (this.week_month_total === 0) {
                this.uniPopupOpen('not_export_tips')
                return false
            }

            uni.showLoading({
                mask: app.globalData.evn_version === 'trial' ? false : true,
                title: '导出中...'
            })

			const data = this.getExportSubmitData()
			data.access_token = app.globalData.access_token

			const res = await xwy_api.request({
				url: 'front.flat.sport_step.export.admin_active/export_exchange_step_to_excel',
				data
			})
			uni.hideLoading()

			if (!res || !res.status || !res.data || !res.data.url) {
			    uni.showModal({
			        title: '导出失败',
			        content: res.info || '导出失败',
			        showCancel: false
			    })
			    return false
			}

			this.excel_dowload_src = res.data.url
			this.export_success_tips = res.info || '数据已导出，请复制此地址在浏览器打开下载Excel报表。'
			this.uniPopupOpen('export_success')
			this.copyDownloadSrc(true)

			let day_tips = `${data.date_start}至${data.date_end}`
			if (data.date_start === data.date_end) day_tips = data.date_start
			my_storage.setExportExcelRecord({
				url: res.data.url,
				title: `导出活动【${this.active_name}】${day_tips} 用户数据`
			})
        },

        copyDownloadSrc(hide = false) {
            uni.setClipboardData({
                data: this.excel_dowload_src,
                success: () => hide ? uni.hideToast() : this.$uni.showToast('复制成功', 'success', 1000)
            })
        },

        uniPopupOpen(ref) {
            this.$refs[ref].open()
        },

        uniPopupClose(ref) {
            this.$refs[ref].close()
        },
    }
}
</script>

<style>
.page {
    padding-top: 50px;
}

.type-list {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    z-index: 9999;
}

.type-item {
    height: 40px;
    line-height: 40px;
    box-sizing: border-box;
}

.active-type {
    border-bottom: 3px solid #2d8cf0;
    color: #2d8cf0;
}

.tips {
    padding: 10px;
    color: #e19898;
    background-color: rgba(0, 0, 0, .1);
}

.year {
    border-bottom: 1px solid #eee;
}

.month-item {
    width: calc(100% / 4);
    height: 80px;
    /* border: 1px solid #eee; */
    box-sizing: border-box;
}

.month-active {
    color: #2d8cf0 !important;
}

.export-btn-view {
    position: fixed;
    left: 0;
    bottom: 0;
    padding: 15px;
    width: 100vw;
    box-sizing: border-box;
}

.export-btn {
    line-height: 40px;
    border-radius: 20px;
}

.uni_popup {
    width: 280px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}

.uni_popup-btn {
    line-height: 40px;
    border-radius: 20px;
}

.uni_popup-btn::after {
    border: none;
}

/* 日历插入数据右上角原点的样式 */
.uni-calendar-item__weeks-box-circle {
    opacity: 0;
}

/* #ifdef H5 */

@media screen and (min-width: 500px) {
    .type-list, .export-btn-view {
        width: 500px;
        left: calc(((100vw - 500px) / 2) - 10px);
    }
}

/* #endif */
</style>
