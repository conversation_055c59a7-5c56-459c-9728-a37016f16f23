<template>
    <view>
        <view class="p10">
            <uni-easyinput
                type="textarea"
                v-model="value"
                :clearable="false"
                placeholder="在此批量输入队伍名称,以换行隔开"
                :maxlength="-1"
            ></uni-easyinput>
        </view>

        <view class="pl10 pr10 flex-kai">
            <view class="color-primary font14" @click="showExample()">查看示例</view>
            <view class="color-sub font14" @click="clearValue()">清空文本框</view>
        </view>

        <view class="p10">
            <uni-easyinput
                v-model="limit_person"
                type="number"
                placeholder="输入队伍人数限制，不填则不限制人数"
            ></uni-easyinput>
        </view>

        <view class="p10 font14 color-content">
            <view>说明:</view>
            <view>1、请在上方文本框内批量输入队伍名称，以换行隔开。</view>
            <view>2、点击"查看示例"可在文本框内显示示例文本，但会覆盖原来输入的内容。</view>
            <view>3、批量导入队伍暂时仅支持导入队伍名称和批量设置队伍人数限制，如需其它设置，请批量导入后自行到队伍管理设置。</view>
        </view>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view
                class="login-btn color-white text-center font18 bg-primary"
                @click="check"
            >导入</view>
        </view>

        <uni-popup ref="popup" type="center">
            <view class="popup bg-white p10">
                <view class="color-title text-center">请核对导入队伍信息</view>
                <view class="color-sub ptm5">共{{ team_list.length }}个队伍</view>
                <view class="color-content ptm5" style="max-height: 50vh;overflow: auto;">
                    <view v-for="(item, index) in team_list" :key="index">
                        {{ index + 1 }}、{{ item }}
                    </view>
                </view>
                <view class="flex-center p10">
                    <view class="color-sub" @click="popupHidden()">取消</view>
                    <view class="color-primary" @click="check_ok()">导入</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import base64 from '../../../utils/base64.js'


export default {
    data() {
        return {
            value: '',
            team_list: [],
            limit_person: ''
        }
    },
    onLoad(e) {
        uni.showLoading({ mask: true })
        if (!e.id) {
            uni.hideLoading()
            this.error = '请指定活动id'
            return false
        }

        this.team_count = e.team_count ? Number(e.team_count) : 0
        this.id = e.id
        login.uniLogin(err => {
            uni.hideLoading()
            if (err && err.errMsg) return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
        })
    },

    methods: {
        showExample() {
            if (this.value) {
                uni.showModal({
                    title: '提示',
                    content: '示例文本会覆盖文本框内容，确定查看示例？',
                    success: res => {
                        if (res.confirm) this.value = '第一队\n第二队\n第三队'
                    }
                })
                return false
            }
            this.value = '第一队\n第二队\n第三队'
        },

        clearValue() {
            if (!this.value) return false
            uni.showModal({
                title: '提示',
                content: '确定清空文本框？',
                success: res => {
                    if (res.confirm) this.value = ''
                }
            })
        },

        check() {
            if (!this.value) {
                return uni.showToast({
                    title: '请输入队伍名称',
                    icon: 'error'
                })
            }

            const limit_person = Number(this.limit_person)
            if (isNaN(limit_person)) {
                return uni.showToast({
                    title: '队伍人数限制必须输入数字',
                    icon: 'none'
                })
            }

            this.team_list = this.value.split(/\n/)
            this.$refs.popup.open()
        },

        check_ok() {
            this.addTeam()
            this.popupHidden()
        },

        popupHidden() {
            this.$refs.popup.close()
        },

        async addTeam() {
            uni.showLoading({
                title: '导入中...',
                mask: true
            })
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/create_team_group_batch',
                data: {
                    active_id: this.id,
                    batch_team_group_data: this.get_batch_team_group_data()
                }
            })

            uni.hideLoading()

            if (!res || !res.status) {
                this.xwy_api.alert(res && res.info || '导入失败')
                return false
            }

            const title = res && res.info || '导入成功'

            uni.showToast({
                title,
                icon: title.length < 8 ? 'success' : 'none'
            })

            const eventChannel = this.getOpenerEventChannel()
            eventChannel && eventChannel.emit('uploadList')
            const timeout = setTimeout(() => {
                uni.navigateBack()
                clearTimeout(timeout)
            }, 1000)
        },

        get_batch_team_group_data() {
            const list = [],
                value2list = this.value.split(/\n/)

            // const value_list_reverse = value2list.reverse()
            value2list.forEach((v, i) => {
                const item = {name: v, sort_num: this.team_count + i + 1}
                if (this.limit_person && !isNaN(this.limit_person)) item['limit_person'] = this.limit_person
                list.push(item)
            })
            let list_str = JSON.stringify(list)
            list_str = list_str.replace(/·/g, '-') // 这个base64的js，如果遇到字符串里面有·，会导致报错
            return base64['encode'](list_str)
        }


    }
}
</script>

<style>
textarea {
    height: calc(100vh - 340px) !important;
}

.popup {
    border-radius: 10px;
    width: 80vw;
    max-width: 400px;
}

.bottom-btn-view {
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.login-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}

</style>
