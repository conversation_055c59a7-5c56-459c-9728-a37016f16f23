<template>
    <view>

        <picker class="p10" mode="date" fields="month" :value="datePickerValue"
                :start="pickerDate.start" :end="pickerDate.end" @change="yearMonthChange">
            <view class="picker-view flex-kai">
                <view>
                    <text class="color-content">导出月份:</text>
                    <text class="color-title pl10">{{ year }}年{{ month }}月</text>
                </view>
                <view>
                    <uni-icons type="forward" color="#bbbec4"/>
                </view>
            </view>
        </picker>

        <view v-if="exportDirectly" class="flex-all-center" style="padding: 100px;">
            <view class="export-button bg-primary color-white text-center" @click="exportRankList(1)">
                导出
            </view>
        </view>

        <template v-if="!exportDirectly">
            <view class="font14 p10" style="color: #e19898;">
                <view>
                    活动用户共{{ attend_person_count }}人,
                    需要分{{ totalPage }}次导出,
                    每次导出{{ exportedAtOneTime }}条排行榜数据。
                </view>
            </view>

            <!--<view v-if="totalPage > 1" class="p10 font14 color-content">
                <text>每次导出</text>
                <text class="plr5">{{exportedAtOneTime}}</text>
                <text>条排行榜数据</text>
                <text class="pl10 color-primary" @click="">修改</text>
            </view>-->

            <view class="pb10">
                <view class="export-item p10 flex-kai" v-for="(item, index) in totalPage" :key="item">
                    <view class="color-title">
                        <text class="color-sub">{{ index + 1 }}、</text>
                        <text>{{ index * exportedAtOneTime + 1 }}</text>
                        <text class="plr5">-</text>
                        <text>
                            <template v-if="index + 1 === totalPage">{{ attend_person_count }}</template>
                            <template v-else>{{ (index + 1) * exportedAtOneTime }}</template>
                        </text>
                    </view>
                    <view class="flex-kai color-light-primary" @click="exportItem(index + 1)">导出</view>
                </view>
            </view>
        </template>
        
    </view>
</template>

<script>
import openExcelFile from "@/utils/open-excel-file"

export default {
    data() {
        return {
            isTeam: false,
            year: new Date().getFullYear(),
            month: new Date().getMonth() + 1,
            attend_person_count: 0,
            pickerDate: {
                start: '2024-03-01',
                end: this._utils.getDay(0, true)
            },
            exportedAtOneTime: 1000
        }
    },
    
    computed: {
        datePickerValue() {
            return `${this.year}-${this.month.toString().padStart(2, '0')}-01`
        },
        exportDirectly() {
            return this.isTeam || this.attend_person_count <= this.exportedAtOneTime
        },
        totalPage() {
            return Math.ceil(this.attend_person_count / this.exportedAtOneTime)
        }
    },
    
    onLoad(params) {
        this.id = params.id
        if (params.is_team) this.isTeam = true
        this.$uni.setNavigationBarTitle(`导出${this.isTeam ? '队伍' : '个人'}活跃度排行榜`)
        this.$login.uniLogin(() => {
            !this.isTeam && this.getPeopleTotal()
        })
    },

    methods: {
        async getPeopleTotal() {
            this.$uni.showLoading('加载中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.export.admin_active/active_attend_person_count',
                data: {
                    active_id: this.id
                }
            })

            uni.hideLoading()
            this.attend_person_count = res.data.attend_person_count
        },

        yearMonthChange(e) {
            const [year, month] = e.detail.value.split('-')
            this.year = year
            this.month = Number(month)
        },

        exportItem(page) {
            this.exportRankList(page)
        },

        async exportRankList(page = 1) {
            this.$uni.showLoading('导出中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.step/top_rank_list',
                data: {
                    active_id: this.id,
                    top_rank_types: this.isTeam ? 34 : 33,
                    year: this.year,
                    month: this.month,
                    page,
                    perpage: this.exportedAtOneTime
                }
            })
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '导出失败')
            
            let list = res.data?.['top_rank_list']?.list?.data || []
            if (this.isTeam) list = res.data?.['top_rank_list']?.list || []
            const {tableData, tableName} = this.excelDataProcessing(list, page)

            openExcelFile.openDocument(tableData, tableName)
        },

        excelDataProcessing(list, page) {
            const date = `${this.year}年${this.month}月`
            const type = this.isTeam ? '队伍' : '个人'
            const start = (page - 1) * this.exportedAtOneTime + 1
            let pages = ''
            if (!this.exportDirectly) {
                const end = start + list.length - 1
                pages = `第${start}-${end}名`
            }
            const  tableName = `${date}${type}活跃度排行榜${pages}`
            
            let nameKey = this.isTeam ? '队伍名称' :  ''
            const tBody = list.map((v, i) => {
                if (!this.isTeam && !nameKey) nameKey = v.user_details?.must_submit?.[0]?.title || ''
                const name = this.isTeam ? v['team_details']?.name : v.user_details?.must_submit?.[0]?.value
                return [start + i, name || '', v['percent_num'] || 0]
            })
            const tHead = ['排名', nameKey || '姓名', '达标率']
            const tableData = [tHead, ...tBody]
            return {tableData, tableName}
        }
    }
}
</script>

<style lang="scss">
.picker-view {
    margin: 10px;
    padding: 10px;
    border-radius: 5px;
    background-color: #f8f8f8;
}

.export-button {
    width: 200px;
    line-height: 44px;
    border-radius: 22px;
}

.export-item {
    margin: 10px;
    border-radius: 5px;
    background-color: #f8f8f8;
}
</style>