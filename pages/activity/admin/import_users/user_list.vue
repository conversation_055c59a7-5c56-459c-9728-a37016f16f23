<template>
    <view class="page bg-background">
        <view class="top-view">
            <view class="search bg-white flex-kai p10">
                <view class="input-view">
                    <picker :range="search_key_opt" range-key="title"
                            @change="search_key = search_key_opt[$event.detail.value].key">
                        <view class="search-key color-sub font12">
                            按{{ searchKeyLabel }}搜索
                            <uni-icons type="forward" size="12" color="#80848f"/>
                        </view>
                    </picker>
                    <view class="search-icon left-icon flex-all-center">
                        <uni-icons type="search" size="20" color="#bbbec4"/>
                    </view>


                    <input
                        class="input bg-background"
                        type="text"
                        confirm-type="search"
                        v-model="search_keyword"
                        @confirm="search"
                        :placeholder="'输入' + searchKeyLabel + '搜索'"
                        placeholder-style="color:#bbbec4"
                    />
                    <view class="search-icon right-icon flex-all-center" @click="search_keyword = ''">
                        <uni-icons v-if="search_keyword" type="close" size="20" color="#bbbec4"/>
                    </view>
                </view>
                <view class="search-go color-info" @click="search">搜索</view>
            </view>


        </view>


        <view v-if="total_count" class="color-sub text-center font14" style="padding-top: 10px;">
            共{{ total_count }}名用户
        </view>

        <view class="list">
            <view class="item bg-white" v-for="(item, index) in list" :key="index" @click="checkItem(item)">

                <view class="flex-row">

                    <view v-if="in_batch_operation" class="flex-all-center pl10">
                        <radio :checked="item.is_check"/>
                    </view>
                    <view class="flex-all-center">
                        <view class="pl10">
                            <image v-if="item.headimg" class="headimg" :src="item.headimg"/>
                        </view>
                    </view>

                    <view class="middle">
                        <view class="color-title">登录名: {{ item.username }}</view>

                        <template v-if="item.conf_json && item.conf_json.length">
                            <view
                                v-for="(item_, index_) in item.conf_json"
                                :key="index_"
                                class="color-content"
                            >
                                <template v-if="item_.value">{{ item_.title }}: {{ item_.value || '' }}
                                </template>
                            </view>
                        </template>

                        <view v-if="item.team_name" class="color-content">队伍: {{ item.team_name }}</view>

                        <view class="color-title font14 color-sub">导入时间: {{ item.create_time }}</view>
                    </view>


                </view>
                <view class="item-opt-bar flex-kai">
                    <view class="flex-row">


                    </view>
                    <view class="flex-row font14 color-sub">
                        <view class="opt-item plr5" @click.stop="editUser(item)">修改</view>
                        <view class="opt-item plr5" @click.stop="deleteUser(item)">删除</view>
                    </view>
                </view>
            </view>
        </view>


        <view v-if="list.length" class="bottom-bar flex-kai bg-white">
            <view>
                <view v-if="in_batch_operation" @click="checkAllChange">
                    <radio :checked="is_check_all"/>
                    <!-- <text class="color-sub font14" v-if="is_check_all">取消</text> -->
                    <text class="color-sub font14">全选</text>
                </view>
            </view>
            <view class="flex-row">
                <template v-if="in_batch_operation">
                    <view class="batch-btn color-error" @click="batchDelete">批量删除</view>
                    <view class="batch-btn color-sub" @click="cancelBatch">取消</view>
                </template>
                <template v-else>
                    <view class="batch-btn color-warning" @click="deleteAll">清空名单</view>
                    <view class="batch-btn color-warning" @click="in_batch_operation = true">批量操作</view>
                </template>
            </view>
        </view>

        <view v-if="!list.length && !more_loading && !init_load" class="text-center"
              style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">
                暂无用户
            </view>
        </view>

        <uni-load-more v-if="more_loading" status="loading"></uni-load-more>
        <uni-load-more v-if="is_last_page && list.length > 5" status="noMore"
                       :contentText="{contentnomore: '我是有底线的'}"></uni-load-more>
        <uni-load-more v-if="!is_last_page && !more_loading && !init_load" status="more"></uni-load-more>

        <edit-user ref="editUser" :active-id="id" :team-open="team_open" :submit-must="submitMust"
                   @updateList="search"/>
    </view>
</template>

<script>
import editUser from './edit.vue'

const app = getApp()


export default {
    components: {editUser},

    data() {
        return {
            id: '',
            list_type: '',
            total_count: 0,
            init_load: true,
            list: [],
            load_page: 1,
            is_last_page: false,
            more_loading: false,
            search_keyword: '',
            activity_detail: {},
            search_key_opt: [
                {key: 'username', title: '用户名'},
                {key: 'nickname', title: '姓名'},
                {key: 'userid', title: '会员号'}
            ],
            search_key: 'username',
            in_batch_operation: false,
            is_check_all: false,
            team_open: false
        }
    },


    computed: {
        searchKeyLabel() {
            return this.search_key_opt.find(v => v.key === this.search_key)?.title || ''
        },
        submitMust() {
            return this.activity_detail.conf?.must_submit || []
        }
    },


    onLoad(e) {
        this.id = e.id
        this.$uni.showLoading()
        this.$login.uniLogin(() => this.getActivityDetail())
    },


    onPullDownRefresh() {
        if (this.init_load || this.more_loading) {
            uni.stopPullDownRefresh()
            return false
        }
        this.load_page = 1
        this.getList()
        uni.stopPullDownRefresh()
    },

    onReachBottom() {
        if (!this.init_load && !this.more_loading && !this.is_last_page) this.getList()
    },

    methods: {
        async getActivityDetail() {
            let activity_detail = app.globalData.activity_detail

            if (!activity_detail) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.id
                    }
                })

                activity_detail = res.data.active_details

            }
            if (activity_detail.conf.active.closed_team === 0) this.team_open = true

            this.activity_detail = activity_detail
            this.getList()
        },


        search() {
            this.load_page = 1
            this.getList()
        },

        getList() {
            if (this.load_page === 1) {
                this.total_count = 0
                this.list = []
                this.is_last_page = false
                if (!this.init_load) this.init_load = true
                this.$uni.showLoading()
            } else {
                this.more_loading = true
            }

            const data = {
                access_token: app.globalData.access_token,
                active_id: this.id,
                page: this.load_page,
                perpage: 20
            }

            if (this.search_keyword) data[this.search_key] = this.search_keyword

            this.xwy_api.ajax({
                url: 'front.flat.sport_step.adminUser/active_batch_import_user_list ',
                data,
                success: res => {
                    if (this.load_page === 1) {
                        this.init_load = false
                        uni.hideLoading()
                    } else {
                        this.more_loading = false
                    }


                    this.load_page++

                    if (!res.status) {
                        this.is_last_page = true
                        return false
                    }
                    this.is_check_all = false

                    const data = res.data.user_list

                    const list = data.data || []
                    const list_ = list.map(v => ({
                        is_check: false,
                        id: v.id || '',
                        username: v.username || '',
                        headimg: v.headimg || '',
                        team_id: v.team_details?.id || '',
                        team_name: v.team_details?.name || '',
                        conf_json: v.conf_json || [],
                        create_time: v.create_time || ''
                    }))
                    this.list = [...this.list, ...list_]
                    this.is_last_page = data.is_lastpage
                    this.total_count = data.total
                }
            })


        },

        checkItem(item) {
            if (!this.in_batch_operation) return false
            item.is_check = !item.is_check
            if (item.is_check) {
                this.inspectCheckAll()
            } else {
                if (this.is_check_all) this.is_check_all = false
            }
        },

        // 循环数据列表，检查是否全选
        inspectCheckAll() {
            this.is_check_all = this.list.every(value => value.is_check)
        },

        checkAllChange() {
            this.is_check_all = !this.is_check_all
            this.list.forEach(v => v.is_check = this.is_check_all)
        },


        cancelBatch() {
            this.in_batch_operation = false
            if (this.is_check_all) this.is_check_all = false
            this.list.forEach(v => v.is_check = false)
        },

        getCheckId() {
            return this.list.filter(v => v.is_check).map(v => v.id).join(',')
        },

        editUser(item) {
            this.$refs.editUser.open(item)
        },


        async deleteUser(item) {
            const {confirm} = await this.$uni.showModal('删除后无法找回，确定删除？', {showCancel: true})
            if (confirm) this.deleteUsers(item.id)
        },


        async batchDelete() {
            const ids = this.getCheckId()
            if (!ids) {
                this.$uni.showToast('请选择需要删除的名单')
                return
            }

            const {confirm} = await this.$uni.showModal('删除后无法找回，确定删除？', {showCancel: true})
            if (confirm) this.deleteUsers(ids)
        },

        async deleteAll() {
            const {confirm} = await this.$uni.showModal('清空后无法找回，确定清空？', {showCancel: true})
            if (confirm) this.deleteUsers('clear_all')
        },

        async deleteUsers(ids) {
            this.$uni.showLoading('删除中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.adminUser/del_import_user',
                data: {
                    active_id: this.id,
                    ids
                }
            })
            uni.hideLoading()

            if (!res?.status) {
                this.$uni.showModal(res?.info || '删除失败')
                return
            }

            this.$uni.showToast('已删除')

            this.load_page = 1
            this.getList()
        }
    }
}
</script>

<style scoped>
.page {
    min-height: 100vh;
    padding-top: 60px;
    padding-bottom: 30px;
    box-sizing: border-box;
}

.top-view {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 999;
}


.search, .search .input {
    height: 40px;
    line-height: 40px;
}

.input-view {
    position: relative;
    width: 100%;
    height: 40px;
}

.search-key {
    position: absolute;
    left: 5px;
    top: 0;
    width: 90px;
    border-right: 1px solid #fff;
}

.search-icon {
    position: absolute;
    width: 30px;
    height: 40px;
    top: 0;
}

.left-icon {
    left: 105px;
}

.right-icon {
    right: 0;
}

.search .input {
    /* width: calc(100% - 80px); */
    padding-right: 30px;
    padding-left: 135px;
    border-radius: 20px;
    box-sizing: border-box;
}

.search-go {
    width: 50px;
    height: 40px;
    min-width: 50px;
    text-align: right;
}


.item {
    margin: 10px;
    padding: 10px 10px 10px 0;
    border-radius: 10px;
}

.headimg {
    width: 50px;
    min-width: 50px;
    height: 50px;
    border-radius: 50%;
    display: block;
}

.middle {
    width: 100%;
    padding-left: 10px;
    overflow: hidden;
    box-sizing: border-box;
}

.item-opt-bar {
    border-top: 1px solid #eee;
    margin-top: 10px;
    padding-top: 10px;
}

.opt-item {
    border: 1px solid #80848f;
    line-height: 22px;
    border-radius: 12px;
    margin-left: 5px;
}


.bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 10px 10px 20px;
    box-sizing: border-box;
    border-top: 1px solid #eee;
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .bottom-bar {
        width: 500px;
        left: calc(((100vw - 500px) / 2) - 10px);
    }
}

/* #endif */
.batch-btn {
    border: 1px solid #eee;
    line-height: 24px;
    border-radius: 13px;
    margin-left: 5px;
    padding: 0 5px;
    font-size: 14px;
}
</style>
