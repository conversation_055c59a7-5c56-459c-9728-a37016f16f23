<template>
    <view>
        <uni-popup ref="editPopup" type="center">
            <view class="edit-popup-container__ bg-white">
                <view class="font18 text-center p10 b-bottom">修改用户信息</view>
                <view style="padding: 10px;">
                    <uni-forms :modelValue="edit_form" label-width="80px" label-align="right">
                        <uni-forms-item label="登录名:" name="username">
                            <uni-easyinput v-model="edit_form.username" placeholder="请输入登录名"/>
                        </uni-forms-item>
                        <uni-forms-item label="密码:" name="password">
                            <uni-easyinput v-model="edit_form.password" placeholder="留空则不修改密码"/>
                        </uni-forms-item>
                        <uni-forms-item v-if="teamOpen" label="队伍:" name="team_name">
                            <view @click="selectTeam" class="picker-view flex-kai">
                                <view class="flex-row">{{ edit_form.team_name || '选择队伍' }}</view>
                                <uni-icons type="forward" size="16" color="#80848f"/>
                            </view>
                        </uni-forms-item>
                        <template v-if="edit_form.conf_json && edit_form.conf_json.length">
                            <uni-forms-item v-for="(item, index) in edit_form.conf_json" :key="index"
                                            :label="item.title + ':'">
                                <picker v-if="item.types === 2" :range="item.options" range-key="text"
                                        @change="mustPickerChange($event, index)">
                                    <view class="picker-view flex-kai">
                                        <view>{{ item.value || `请选择${item.title}` }}</view>
                                        <uni-icons type="forward" size="16" color="#80848f"/>
                                    </view>
                                </picker>
                                <uni-easyinput v-else v-model="item.value"
                                               :placeholder="`请输入${item.title}`"/>
                            </uni-forms-item>
                        </template>
                    </uni-forms>
                </view>

                <view class="flex-all-center">
                    <view class="confirm-button" @click="submitEdit">保存</view>
                </view>

                <view class="flex-all-center">
                    <view class="color-sub font14 p10" @click="$refs.editPopup.close()">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: 'EditPopup',
    emits: ['updateList'],
    props: {
        activeId: {
            type: String,
            default: ''
        },
        teamOpen: {
            type: Boolean,
            default: false
        },
        submitMust: {
            type: Array,
            default: []
        }
    },

    data() {
        return {
            edit_form: {
                id: '',
                username: '',
                password: '',
                team_id: '',
                team_name: '',
                conf_json: []
            },
        }
    },

    methods: {
        open(item) {
            const edit_form = JSON.parse(JSON.stringify(item));
            this.edit_form.id = edit_form.id;
            this.edit_form.username = edit_form.username;
            this.edit_form.team_id = edit_form.team_id || '';
            this.edit_form.team_name = edit_form.team_name || '';
            this.edit_form.conf_json = this.setConfJson(edit_form.conf_json);
            this.edit_form.password = '';

            this.$refs.editPopup.open();
        },

        setConfJson(conf_json) {
            return this.submitMust.map(item => {
                const conf_item = conf_json.find(v => v.title === item.title);
                return {
                    ...item,
                    value: conf_item?.value || ''
                }
            })
        },

        selectTeam() {
            uni.navigateTo({
                url: `/pages/activity/admin/team_list?selteam=1&is_manage=1&id=${this.id}`,
                events: {
                    setTeam: (data) => {
                        this.edit_form.team_id = data.team_id;
                        this.edit_form.team_name = data.team_name;
                    }
                }
            });
        },

        mustPickerChange(e, index) {
            const item = this.edit_form.conf_json[index];
            item.value = item.options[e.detail.value].text
            this.$set(this.edit_form.conf_json, index, item)
        },

        getConfJson() {
            const conf_json = JSON.parse(JSON.stringify(this.edit_form.conf_json))
            const conf_json__ = conf_json.map(item => {
                if (item.hasOwnProperty('options')) delete item.options;
                return item
            })
            return this._utils.base64.encode(JSON.stringify(conf_json__))
        },

        async submitEdit() {
            uni.showLoading({
                title: '保存中...',
                mask: true
            });

            const data = {
                active_id: this.activeId,
                id: this.edit_form.id,
                username: this.edit_form.username,
                team_id: this.edit_form.team_id || '',
                conf_json: this.getConfJson()
            };

            if (this.edit_form.password) {
                data.password = this.edit_form.password;
            }

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.adminUser/change_import_user_data',
                data
            });

            uni.hideLoading();

            if (!res?.status) {
                this.$uni.showModal(res?.info || '修改失败');
                return
            }

            uni.showToast({
                title: '修改成功',
                icon: 'success'
            });

            this.$refs.editPopup.close();

            this.$emit('updateList');
        },
    }
}
</script>

<style lang="scss" scoped>
.edit-popup-container__ {
    width: 90vw;
    max-width: 400px;
    border-radius: 10px;

    .picker-view {
        padding: 0 10px;
        border-radius: 5px;
        line-height: 34px;
        border: 1px solid #eee;
    }

    .confirm-button {
        width: 150px;
        line-height: 44px;
        text-align: center;
        border-radius: 22px;
        background-color: #5cadff;
        color: #fff;
    }
}
</style>