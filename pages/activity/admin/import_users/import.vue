<template>
	<view>
		<view class="excel-icon text-center" hover-class="navigator-hover">
			<text class="iconfont icon-export-excel color-primary font34"></text>
			<view>选择文件</view>
		</view>
	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'

	export default {
		data() {
			return {

			}
		},

		onLoad(e) {
		    console.log('活动详情页面路径参数', e)
		    uni.showLoading()
		    if (!e.active_id) {
		        this.loading = false
		        uni.hideLoading()
		        this.error = '请指定活动id'
		        return false
		    }

		    this.active_id = e.active_id
		    login.uniLogin(err => {
		        this.loading = false
		        if (err && err.errMsg) {
		            uni.hideLoading()
		            uni.showModal({
		                title: err.errTitle || '提示',
		                content: err.errMsg,
		                showCancel: false
		            })
		            return false
		        }

				this.excel_js()

		        uni.hideLoading()
		    })
		},


		methods: {
			excel_js() {
			    var script = document.createElement('script')

				const static_url = app.globalData.shop_info?.shop_set?.static_url || 'https://prod-0g479j60184f120d-1304148175.tcloudbaseapp.com/'
			    let url = `${static_url}web/wx-cloud-api/lib/excel/xlsx.core.min.js`
			    script.src = url;
			    document.body.appendChild(script);
			},



		}
	}
</script>

<style>

</style>
