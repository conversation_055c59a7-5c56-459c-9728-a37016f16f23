<template>
    <view>
        <uni-calendar
            :date="calendar_date"
            :lunar="true"
            :range="true"
            :showMonth="false"
            :startDate="startDate"
            :endDate="endDate"
            @change="calenderChange"
        />

        <view class="font12 p10" style="color: #e19898;">
            <view>可导出1到{{ max_day }}天用户每一天的数据。</view>
            <view>日历仅支持选择当月日期，如需导出跨月数据，请点击下面已选日期进行切换。</view>
            <view v-if="total_page > 1">
                <view>每次最多可导出1000人数据，超过1000人需要分批次导出。</view>
                <view>
                    活动用户共{{ attend_person_count }}人，需要分{{ total_page }}次导出，每次导出{{ perpage }}用户数据。
                </view>
            </view>
        </view>

        <view class="text-center pt15">
            <text class="pr5 color-content">已选:</text>

            <picker mode="date" :start="startDate" :end="endDate" :value="sel_start_date" 
                    style="display: inline-block;" @change="selDayChange($event, 'sel_start_date')">
                <view class="color-primary">{{ sel_start_date }}</view>
            </picker>
            <text class="plr5 color-sub">至</text>

            <picker mode="date" :start="startDate" :end="endDate" :value="sel_end_date" 
                    style="display: inline-block;" @change="selDayChange($event, 'sel_end_date')">
                <view class="color-primary">{{ sel_end_date }}</view>
            </picker>
            <text class="pl5 color-sub">(共{{ sel_all_day }}天)</text>
        </view>


        <view v-if="total_page > 1" class="pt15">
            <view class="p10 flex-kai" v-for="(item, index) in total_page" :key="item">
                <view class="color-title">
                    <text class="color-sub">{{ index + 1 }}、</text>
                    <text>{{ index * perpage + 1 }}</text>
                    <text class="plr5">-</text>
                    <text>
                        <template v-if="index + 1 === total_page">{{ attend_person_count }}</template>
                        <template v-else>{{ (index + 1) * perpage }}</template>
                    </text>
                </view>
                <view class="flex-kai" style="width: 155px;">
                    <view>
                        <text v-if="src_list[index]" class="color-disabled">已导出</text>
                        <text v-else class="color-primary" @click="exportData(index)">导出</text>
                    </view>
                    <view :class="{'color-primary': src_list[index], 'color-disabled': !src_list[index]}" 
                          @click="copyDownloadSrc(src_list[index])">复制下载地址
                    </view>
                </view>
            </view>
        </view>


        <view v-if="total_page === 1" class="export-btn-view bg-white">
            <view class="export-btn text-center bg-primary color-white" hover-class="navigator-hover" 
                  @click="exportData(0)">导出数据
            </view>
        </view>

        <uni-popup ref="export_success" type="center">
            <view class="uni_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('export_success')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top bg-primary color-white" style="padding: 50px 20px 20px;">
                    <icon type="success" size="80" color="#ffffff"></icon>
                    <view class="font18">导出成功</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ export_success_tips }}
                    </view>
                    <view class="uni_popup-btn bg-info color-white" hover-class="navigator-hover" 
                          @click="copyDownloadSrc(download_src, false)">复制下载地址
                    </view>
                </view>
            </view>
        </uni-popup>


    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import my_storage from '@/utils/storage.js'


export default {
    data() {
        return {
            startDate: utils.getDay(),
            endDate: utils.getDay(),
            calendar_date: utils.getDay(),
            sel_start_date: utils.getDay(),
            sel_end_date: utils.getDay(),
            sel_all_day: 1,
            total_page: 0,
            perpage: 1000,
            src_list: [],
            export_success_tips: '',
            download_src: '',
            attend_person_count: 0,
            max_day: 31,  // 导出最大天数，限制最多只能导任意7天内的数据
        }
    },
    onLoad(e) {
        this.id = e.id
        this.active_name = e.name
        if (e.is_mobile_step) this.is_mobile_step = true
        if (e.is_real_step) this.is_real_step = true

        this.initDate(e.begin_time, e.end_time)

        this.$uni.showLoading()

        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getPeopleTotal()
        })
    },
    methods: {
        initDate(begin_time, end_time) {
            begin_time *= 1000
            end_time *= 1000
            const startDate = utils.unitTimeToDate(begin_time),
                endDate = utils.unitTimeToDate(end_time)

            const _time = new Date().getTime()

            let sel_day = utils.getDay()

            if (_time < begin_time) sel_day = startDate
            if (_time > end_time) sel_day = endDate


            this.startDate = startDate
            this.endDate = endDate
            this.calendar_date = sel_day
            this.sel_start_date = sel_day
            this.sel_end_date = sel_day
            this.dateDiff()
        },


        dateDiff() {
            this.sel_all_day = utils.dateDiff(this.sel_start_date, this.sel_end_date)
            if (this.sel_all_day > this.max_day) {
                this.$uni.showModal('导出天数超过' + this.max_day + '天，请重新选择导出天数')
            }
        },

        async getPeopleTotal() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.export.admin_active/active_attend_person_count',
                data: {
                    active_id: this.id
                }
            })

            uni.hideLoading()

            const attend_person_count = res.data.attend_person_count
            this.attend_person_count = attend_person_count

            this.total_page = Math.ceil(attend_person_count / this.perpage)
        },

        async exportData(index) {
            if (this.sel_all_day > this.max_day) {
                return this.$uni.showModal('导出天数超过' + this.max_day + '天，请重新选择导出天数')
            }


            this.$uni.showLoading('导出中...')

            const data = {
                active_id: this.id,
                date_start: this.sel_start_date,
                date_end: this.sel_end_date,
                page: index + 1,
                perpage: this.perpage
            }
            if (this.is_mobile_step) data.is_mobile_step = 1
            if (this.is_real_step) data.is_real_step = 1

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.export.admin_active/get_user_exchange_step_total_this_day',
                data
            })

            uni.hideLoading()

            if (!res?.data?.url) return this.$uni.showModal(res?.info || '导出失败')

            this.exportSuccess(index, res, data)
        },

        exportSuccess(index, res, data) {
            this.src_list[index] = res.data.url
            this.download_src = res.data.url
            this.export_success_tips = res.info || '数据已导出，请复制此地址在浏览器打开下载Excel报表。'
            this.uniPopupOpen('export_success')
            this.copyDownloadSrc(res.data.url, true)

            let day_tips = `${data.date_start}至${data.date_end}`
            if (data.date_start === data.date_end) day_tips = data.date_start
            let page_tips = `第${index * data.perpage + 1}条至第${(index + 1) * data.perpage}条`
            if (index + 1 === this.total_page) page_tips = `第${index * data.perpage + 1}条至第${this.attend_person_count}条`
            my_storage.setExportExcelRecord({
                url: res.data.url,
                title: `导出活动【${this.active_name}】${day_tips} ${page_tips} 数据`
            })
        },

        calenderChange(e) {
            this.src_list = []
            if (e.range?.data?.length) {
                const sel_day = e.range.data
                this.sel_start_date = sel_day[0]
                this.sel_end_date = sel_day[sel_day.length - 1]
                return this.dateDiff()
            }
            this.sel_start_date = e.fulldate
            this.sel_end_date = e.fulldate
            this.dateDiff()
        },

        selDayChange(e, key) {
            this[key] = e.detail.value
            this.dateDiff()
        },

        uniPopupOpen(ref) {
            this.$refs[ref].open()
        },

        uniPopupClose(ref) {
            this.$refs[ref].close()
        },

        copyDownloadSrc(src, hide = false) {
            uni.setClipboardData({
                data: src,
                success: () => hide ? uni.hideToast() : this.$uni.showToast('复制成功', 'success', 1000)
            })
        },
    }
}
</script>

<style>
.export-btn-view {
    position: fixed;
    left: 0;
    bottom: 0;
    padding: 15px;
    width: 100vw;
    box-sizing: border-box;
}

.export-btn {
    line-height: 40px;
    border-radius: 20px;
}

.uni_popup {
    width: 280px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}

.uni_popup-btn {
    line-height: 40px;
    border-radius: 20px;
}

.uni_popup-btn::after {
    border: none;
}


.uni-calendar__backtoday {
    display: none !important;
}
</style>
