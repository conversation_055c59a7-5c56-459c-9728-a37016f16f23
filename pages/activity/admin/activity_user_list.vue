<template>
    <view class="page bg-background">
        <view class="top-view">
            <view class="search bg-white flex-kai p10">
                <uni-easyinput class="input bg-background" prefix-icon="search" confirm-type="search"  v-model="search_keyword" @confirm="search()" placeholder="输入用户名字搜索" trim="all" :input-border="false"/>
                <view class="search-go color-info pl10" @click="search">搜索</view>
            </view>

            <view v-if="team_open && !params_team_id" class="flex-kai bg-white font14 p10">
                <view class="color-content ellipsis" style="width: 100%;" @click="teamSearch()">
                    <text class="pr5">按队伍搜索:</text>
                    <text :class="{'color-sub': !search_team_name}">
                        {{ search_team_name || '未指定队伍' }}
                    </text>
                    <uni-icons type="forward" color="#80848f" size="14"/>
                </view>
                <view v-if="search_team_name" class="color-primary text-right" style="min-width: 80px;"
                      @click="clearTeam()">不指定队伍
                </view>
            </view>

            <view v-if="enter_types === 3" class="top-tab bg-white flex-row">
                <view
                    class="top-tab-item text-center color-sub"
                    :class="{'active-top-bar': item.id === list_type}"
                    :style="'width: calc(100% / ' + top_tab_list.length + ');'"
                    v-for="(item, index) in top_tab_list"
                    :key="index"
                    @click="listTypeChange(item.id)"
                >{{ item.title }}
                </view>
            </view>
        </view>

        <view v-if="team_open && !params_team_id" style="height: 40px;"></view>
        <view v-if="enter_types === 3" style="height: 40px;"></view>

        <view v-if="total_count" class="color-sub text-center font14" style="padding-top: 10px;">
            共{{ total_count }}名用户
        </view>

        <view v-if="captained_list && captained_list.length" class="p10 color-sub font14">
            <text>队长：</text>
            <text v-for="(item, index) in captained_list" :key="item.id">
                {{ item.nickname }}
                <template v-if="index !== captained_list.length - 1">、</template>
            </text>
        </view>

        <view class="list">
            <view class="item bg-white" v-for="(item, index) in list" :key="item.id"
                  @click="checkItem(item)">

                <view class="flex-row">
                    <view v-if="in_batch_operation" class="flex-all-center pl10">
                        <radio :checked="item.is_check"/>
                    </view>
                    <view @click.stop="toExchangeRecord(item)">
                        <view class="pl10">
                            <image v-if="item.headimg" class="headimg" :src="item.headimg" mode="aspectFill"/>
                        </view>
                    </view>

                    <view class="middle">
                        <view class="flex-kai ptm5">
                            <view>
                                <text class="color-title">
                                    {{ item.must_submit && item.must_submit[0] && item.must_submit[0].value || ' ' }}
                                </text>
                                <text class="font14 color-sub pl5" v-if="item.is_admin || item.is_captained">
                                    <template v-if="item.is_admin">(管理员)</template>
                                    <template v-if="item.is_captained">(队长)</template>
                                </text>
                                <text class="iconfont icon-edit color-sub plr5"
                                      @click="editName(item)"></text>
                            </view>
                            <view v-if="enter_types === 3" class="pl10" style="min-width: 60px;">
                                <uni-tag :text="item.checked ? '审核通过' : '待审核'" size="mini"
                                         :type="item.checked ? 'success' : 'warning'" inverted></uni-tag>
                            </view>
                        </view>

                        <view v-if="types === 7" class="pb10 font14 color-sub">
                            票数: {{ item.water || 0 }}票
                        </view>


                        <view v-if="rank_set['export_top_rank_excel'] && item.must_submit && item.must_submit.length" class="pb5">
                            <view v-for="(m, m_index) in item.must_submit" :key="m_index">
                                <view v-if="m_index > 0 && (m.value || m.value === 0)"
                                      class="color-content font14">
                                    {{ m.title }}: {{ m.value || '' }}
                                </view>
                            </view>
                        </view>

                        <view class="color-sub font14">
                            <view v-if="types === 2 && rank_set['admin_change_user_data']">
                                累计兑换 {{ item.exchange_kilo }}{{ kilo_unit }}
                            </view>
                            <view v-if="types === 2 && rank_set['admin_change_user_data']">
                                累计兑换 {{ item['sign_day'] || 0 }}天
                            </view>
                            <view v-if="team_open && item['team_details'] && item['team_details'].name">
                                所属队伍: {{ item['team_details'].name }}
                            </view>
                            <template v-if="item['user_conf_data'] && item['user_conf_data'].must_submit && item['user_conf_data'].must_submit.length">
                                <view v-for="(item_, index_) in item['user_conf_data'].must_submit"
                                      :key="index_" class="font14 color-sub">
                                    <template v-if="item_.value">
                                        {{ item_.title }}: {{ item_.value || '' }}
                                    </template>
                                </view>
                            </template>
                            <view  v-if="rank_set.gift_goods || types === 25">
                                {{ integral_unit }}: {{ item.integral_all || 0 }}
                            </view>
                            <view class="color-sub font12">
                                <uni-dateformat :date="item.create_time" format="yyyy-MM-dd hh:mm:ss"/>
                                <text class="pl5">加入本活动</text>
                            </view>
                        </view>
                    </view>

                    <!-- <view class="right flex-column flex-all-center color-red font14">
                      <view class="right-item text-right">
                        {{ item.exchange_num ? item.exchange_num / 100 : 0 }}{{ kilo_unit }}
                      </view>
                    </view> -->
                </view>
                <view v-if="!is_select" class="item-opt-bar clearfix clear">
                    <view class="flex-row flex-wrap fl">

                        <navigator class="color-primary font14 pr10 pl10" v-if="rank_set['redpack']"
                                   :url="'/pages/wallet/redpack_list?is_admin=1&id=' + id + '&userid=' + item.userid">红包记录
                        </navigator>

                        <template v-if="types === 7">
                            <template v-if="!item.userid">
                                <view class="color-primary font14 pr5 pl5" hover-class="navigator-hover"
                                      @click="editCandidateInfo(item)">修改信息
                                </view>


                                <navigator class="color-primary font14 pr5 pl5"
                                           :url="'/pages/vote/user/submit?is_admin=1&active_id=' + id + '&user_record_id=' + item.id">
                                    添加作品
                                </navigator>


                                <navigator class="color-primary font14 pr5 pl5"
                                           :url="'/pages/vote/user/works_list?is_admin=1&id=' + id + '&user_record_id=' + item.id">
                                    ta的作品
                                </navigator>
                            </template>

                            <view v-if="rank_set['admin_change_user_data']"
                                  class="color-primary font14 pr5 pl5" hover-class="navigator-hover"
                                  @click="changeVoteBallots(item)">
                                修改票数
                            </view>
                        </template>
                        <template v-if="types === 5">

                            <navigator class="color-primary font14 pr5 pl5"
                                       :url="'/pages/clock_in/user/public_sign_list?is_admin=1&id=' + id + '&userid=' + item.userid">打卡审核
                            </navigator>
                        </template>
                        <template v-if="types === 21">

                            <navigator class="color-primary font14 pr5 pl5"
                                       :url="'/pages/clock_in/user/public_sign_list?is_admin=1&type=21&id=' + id + '&userid=' + item.userid">集卡审核
                            </navigator>
                        </template>
                        <template v-if="types === 9">

                            <navigator class="color-primary font14 pr5 pl5"
                                       :url="'/pages/ai_sport/pages/record_list?active_id=' + id + '&userid=' + item.userid + '&nickname=' + (item.must_submit && item.must_submit.length && item.must_submit[0].value || '')">
                                运动记录
                            </navigator>
                        </template>


                        <navigator v-if="rank_set.gift_goods || types === 25"
                                   class="color-primary font14 pr5 pl5"
                                   :url="`/pages/sign_in/integral_record_list?active_id=${id}&unit=${integral_unit}&userid=${item.userid}`">
                            {{ integral_unit }}记录
                        </navigator>

                        <view v-if="adminSubmitBodyData" class="color-primary font14 pr5 pl5"
                              @click="lookBodyData(item)">身体数据
                        </view>
                    </view>
                    <view class="flex-row flex-wrap font14 color-sub fr">
                        <template
                            v-if="is_admin && !item.is_admin && item.checked && rank_set['team_manager']">
                            <view v-if="item.is_captained" class="opt-item plr5"
                                  @click.stop="deleteCaptained(item)">删除队长
                            </view>
                            <view v-else class="opt-item plr5" @click.stop="setCaptained(item)">设为队长</view>
                        </template>
                        <!--<view
                            v-if="open_business_kilometers"
                            class="opt-item plr5"
                            @click.stop="setBusinessUser(item)"
                        >设置为出差用户</view>-->

                        <template v-if="rank_set['admin_change_user_data'] && item.checked">
                            <view v-if="types === 2" class="opt-item plr5"
                                  @click="editExchangeDataShow(index)">
                                修改里程/积分
                            </view>

                            <!--任务闯关活动、AI运动活动不用判断开启积分商城，因为这两个活动不开积分商城也有积分-->
                            <view v-if="types === 9 || types === 25 || rank_set.gift_goods"
                                  class="opt-item plr5" @click="addOrSubtractIntegral(item.userid)">
                                增加/扣减积分
                            </view>
                        </template>

                        <view v-if="types === 12" class="opt-item plr5" @click="lookRunRecord(item)">
                            运动记录
                        </view>

                        <view v-if="team_open && is_admin" class="opt-item plr5"
                              @click.stop="changeTeam(item)">修改队伍
                        </view>
                        <view v-if="enter_types === 3" class="opt-item plr5"
                              @click.stop="changeUserStatus(item)">修改状态
                        </view>
                        <view class="opt-item plr5" @click.stop="deleteUser(item)">删除用户</view>
                        <view class="opt-item plr5" @click.stop="ZSB(item)">加黑名单</view>
                    </view>
                </view>

                <view v-if="is_select && item.checked" class="item-opt-bar clear clearfix">
                    <view class="fr">
                        <view class="color-light-primary font14" @click.stop="selectUser(item)">选择</view>
                    </view>
                </view>
            </view>
        </view>

        <view v-if="list.length && !is_select" class="bottom-bar flex-kai bg-white">
            <view>
                <view v-if="in_batch_operation" @click="checkAllChange">

                    <radio :checked="is_check_all"/>
                    <!-- <text class="color-sub font14" v-if="is_check_all">取消</text> -->
                    <text class="color-sub font14">全选</text>
                </view>
            </view>
            <view class="flex-row">
                <template v-if="in_batch_operation">
                    <view v-if="team_open && is_admin" class="batch-btn color-primary"
                          @click="changeTeam">修改队伍
                    </view>
                    <view v-if="enter_types === 3" class="batch-btn color-warning"
                          @click="batchChangeStatus">修改状态
                    </view>
                    <view class="batch-btn color-error" @click="batchDelete">批量删除</view>
                    <view class="batch-btn color-sub" @click="cancelBatch">取消</view>
                </template>
                <template v-else>
                    <view class="batch-btn color-warning" @click="in_batch_operation = true">批量操作</view>
                </template>
            </view>
        </view>

        <view v-if="!list.length && !more_loading && !init_load" class="text-center"
              style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">
                暂无
                <template v-if="list_type === ''">活动</template>
                <template v-if="list_type === 1">审核通过</template>
                <template v-if="list_type === 4">待审核</template>
                用户
            </view>
        </view>

        <uni-load-more v-if="more_loading" status="loading"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && list.length > 5"
            status="noMore"
            :contentText="{contentnomore: '我是有底线的'}"
        ></uni-load-more>
        <uni-load-more v-if="!is_last_page && !more_loading && !init_load" status="more"></uni-load-more>


        <uni-popup ref="exchange_data_popup" @touchmove.stop.prevent="">
            <view class="exchange-data-popup bg-white text-center">
                <view class="exchange-data-popup-title color-sub">
                    修改{{ edit_exchange_temp_data.nickname }}兑换数据
                </view>
                <view v-if="edit_exchange_temp_data.nickname || edit_exchange_temp_data.headimg" class="pb10">

                    <image v-if="edit_exchange_temp_data.headimg" :src="edit_exchange_temp_data.headimg"
                           mode="aspectFill"/>
                    <view class="color-title pb10">{{ edit_exchange_temp_data.nickname }}</view>
                </view>
                <uni-forms label-width="100" label-align="right">
                    <uni-forms-item label="累计兑换里程:">
                        <uni-easyinput v-model="edit_exchange_temp_data.exchange_kilo" type="digit"/>
                    </uni-forms-item>
                    <uni-forms-item label="累计兑换天数:">
                        <uni-easyinput v-model="edit_exchange_temp_data.sign_day" type="number"/>
                    </uni-forms-item>
                    <uni-forms-item label="累计积分:">
                        <uni-easyinput v-model="edit_exchange_temp_data.integral_all" type="digit"/>
                    </uni-forms-item>
                    <uni-forms-item label="剩余积分:">
                        <uni-easyinput v-model="edit_exchange_temp_data.integral_left" type="digit"/>
                    </uni-forms-item>
                </uni-forms>

                <view class="flex-all-center">
                    <view class="exchange-data-popup-button bg-primary color-white"
                          hover-class="navigator-hover" @click="editExchangeDataConfirm">
                        修改
                    </view>
                </view>
                <view class="flex-all-center">
                    <view class="color-sub font12 p10" @click="$refs.exchange_data_popup.close()">
                        取消修改
                    </view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
import login from '../../../utils/api/login.js'
import xwy_api from '../../../utils/api/xwy_api.js'


export default {

    data() {
        return {
            id: '',
            activity_detail: {},
            top_tab_list: [
                {title: '所有用户', id: ''},
                {title: '审核通过', id: 1},
                {title: '待审核', id: 9},
            ],
            list_type: '',
            total_count: 0,
            init_load: true,
            list: [],
            load_page: 1,
            is_last_page: false,
            more_loading: false,
            search_keyword: '',
            in_batch_operation: false,
            is_check_all: false,
            team_open: false,
            search_team_id: '',
            search_team_name: '',
            rank_set: {},
            kilo_unit: '里',
            types: 2,

            // 出差公里数
            // open_business_kilometers: false,
            // 出差公里数

            edit_exchange_temp_data: {
                nickname: '',
                headimg: '',
                exchange_kilo: '',
                sign_day: ''
            },

            params_team_id: '',
            is_select: false,
            is_admin: false,
            is_team_manager: false,
            captained_list: null,
            enter_types: 0,
            integral_unit: '积分'
        }
    },

    computed: {
        adminSubmitBodyData() {
            if (this.types !== 25) return false
            return !!this.activity_detail?.conf?.active?.admin_submit_body_data
        }
    },

    onLoad(e) {
        this.id = e.id
        if (e.is_team_manager) this.is_team_manager = true
        if (e.team_id) {
            this.params_team_id = e.team_id
            this.list_type = 1
            this.$uni.setNavigationBarTitle('队员名单')
        }
        if (e.is_select) this.is_select = true
        // this.open_business_kilometers = this.xwy_config.openBusinessKilometers(e.id)

        uni.showLoading({
            mask: true
        })
        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getActivityDetail()
        })
    },


    onPullDownRefresh() {
        if (this.init_load || this.more_loading) {
            uni.stopPullDownRefresh()
            return false
        }
        this.load_page = 1
        this.getList()
        uni.stopPullDownRefresh()
    },


    onReachBottom() {
        if (!this.init_load && !this.more_loading && !this.is_last_page) this.getList()
    },


    methods: {
        async getActivityDetail() {
            let activity_detail = app.globalData['activity_detail']

            if (!activity_detail || activity_detail.active_id !== this.id) {
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.id
                    }
                })

                activity_detail = res['data']['active_details']

            }

            if (activity_detail?.types) this.types = activity_detail.types

            if (activity_detail?.rank_set) this.rank_set = activity_detail.rank_set

            if (this.types === 2) {
                // 健步走活动没有关闭队伍功能
                this.team_open = activity_detail?.conf?.active?.closed_team === 0
            } else {
                // 其他活动OA开启了队伍功能
                this.team_open = this.rank_set?.team_group_open === 1
            }

            if (activity_detail?.conf?.active?.kilo_unit) {
                this.kilo_unit = activity_detail.conf.active.kilo_unit
            }

            if (activity_detail?.conf?.active?.integral?.unit) {
                this.integral_unit = activity_detail.conf.active.integral.unit
            }

            this.is_admin = app.globalData['userid'] === activity_detail.userid

            this.enter_types = activity_detail.conf.active.enter_types

            this.activity_detail = activity_detail
            await this.getList()
        },

        teamSearch() {
            this.$uni.navigateTo(`./team_list?is_manage=true&selteam=true&id=${this.id}`, {
                events: {
                    setTeam: data => {
                        this.search_team_id = data.team_id
                        this.search_team_name = data.team_name
                        this.search()
                    }
                }
            })
        },


        clearTeam() {
            this.search_team_id = ''
            this.search_team_name = ''
            this.search()
        },

        listTypeChange(id) {
            if (id === this.list_type) return
            this.list_type = id
            this.search()
        },

        search() {
            this.load_page = 1
            this.getList()
        },

        async getList() {
            if (this.load_page === 1) {
                this.total_count = 0
                this.list = []
                this.is_last_page = false
                if (!this.init_load) this.init_load = true
                this.$uni.showLoading()

                if (this.params_team_id && !this.captained_list && this.rank_set['team_manager']) {
                    return this.getCaptainedList()
                }
            } else {
                this.more_loading = true
            }

            const data = {
                active_id: this.id,
                page: this.load_page,
                perpage: 20
            }

            if (this.list_type !== '') data.checked = this.list_type
            if (this.search_keyword) data.truename = this.search_keyword
            if (this.search_team_id) data.team_id = this.search_team_id
            if (this.params_team_id) data.team_id = this.params_team_id

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/active_attend_user_list',
                data
            })

            if (this.load_page === 1) {
                this.init_load = false
                uni.hideLoading()
            } else {
                this.more_loading = false
            }


            this.load_page++

            if (!res.status) {
                this.is_last_page = true
                return
            }

            const res_data = res.data.user_list
            const list = res_data.data || []

            list.forEach(v => {
                v.is_check = false
                if (this.params_team_id && this.rank_set['team_manager']) {
                    const captained = this.captained_list.find(v_ => v_.userid === v.userid)
                    if (captained) {
                        v.is_captained = true
                        v.captained_id = captained.id
                    }
                    v.is_admin = v.userid === this.activity_detail.userid
                }
            })
            if (this.in_batch_operation) this.is_check_all = false

            this.list = [...this.list, ...list]
            this.is_last_page = res_data.is_lastpage
            this.total_count = res_data.total
        },

        async getCaptainedList() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.admin.teamManager/team_manager_user_list',
                data: {
                    active_id: this.id,
                    team_id: this.params_team_id,
                    page: 1,
                    perpage: 100
                }
            })
            this.captained_list = res?.data?.list?.data || []
            await this.getList()
        },

        async editName(item, name = '') {
            const must_submit = JSON.parse(JSON.stringify(item.must_submit))
            const old_name = must_submit?.[0]?.value
            name ||= old_name
            const modal = await this.$uni.showModal(name, {
                title: '修改姓名',
                showCancel: true,
                editable: true,
                placeholderText: '请输入姓名'
            })
            if (modal.cancel) return
            const new_name = modal.content
            if (!new_name) {
                this.$uni.showToast('请输入姓名')
                return this.editName(item, '')
            }
            if (new_name === old_name) return this.$uni.showToast('姓名相同, 未修改')

            must_submit[0].value = new_name
            this.$uni.showLoading('修改中...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.admin/batch_change_user',
                data: {
                    active_id: this.id,
                    act_types: 5,        // 5 修改活动里用户的拓展实名信息
                    ids: item.id,
                    must_submit: this._utils.base64['encode'](JSON.stringify(must_submit))
                }
            })
            uni.hideLoading()
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '修改失败')
            item.must_submit = must_submit
            this.$uni.showToast('修改成功', 'success')
        },

        async changeVoteBallots(item) {
            const res = await this.$uni.showModal(item.water.toString(), {
                title: '请输入票数',
                showCancel: true,
                editable: true,
                placeholderText: '请输入票数'
            })
            if (!res.confirm) return

            const num = Math.floor(res.content)
            if (num === item.water) return

            if (isNaN(num) || num < 0) {
                this.$uni.showToast('请输入正确的票数')
                await this.changeVoteBallots(item)
                return
            }

            const tips = `确定将${item.must_submit?.[0]?.value || '用户'}的票数改为${num}?`
            const {confirm} = await this.$uni.showModal(tips, {showCancel: true})

            if (confirm) await this.voteBallotsChange(item, num)
        },

        async voteBallotsChange(item, num) {
            this.$uni.showLoading('更改中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin.vote/change_vote_num',
                data: {
                    active_id: this.id,
                    attend_id: item.id,
                    voteNum: num
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '修改失败')
            this.$uni.showToast('已修改', 'success')
            item.water = num
        },

        editCandidateInfo(item) {
            app.globalData.tempData = app.globalData['tempData'] || {}
            app.globalData['tempData'].candidateInfo = item
            this.$uni.navigateTo(`/pages/vote/admin/add_candidate?id=${item.id}&active_id=${this.id}`, {
                events: {
                    updateList: () => {
                        this.search()
                    }
                }
            })
        },

        toExchangeRecord(item) {
            if (this.types !== 2) return
            let nickname = ''
            if (item.must_submit?.[0]?.value) nickname = encodeURIComponent(item.must_submit[0].value)
            let headimg = ''
            if (item.headimg) headimg = encodeURIComponent(item.headimg)
            this.$uni.navigateTo(`/pages/activity/other/user_exchange_list?activity_id=${this.id}&userid=${item.userid}&nickname=${nickname}&headimg=${headimg}`)
        },


        checkItem(item) {
            if (!this.in_batch_operation) return false
            item.is_check = !item.is_check
            if (item.is_check) {
                this.inspectCheckAll()
            } else {
                if (this.is_check_all) this.is_check_all = false
            }
        },

        // 循环数据列表，检查是否全选
        inspectCheckAll() {
            this.is_check_all = this.list.every(value => value.is_check)
        },

        checkAllChange() {
            this.is_check_all = !this.is_check_all
            this.list.forEach(v => v.is_check = this.is_check_all)
        },


        cancelBatch() {
            this.in_batch_operation = false
            if (this.is_check_all) this.is_check_all = false
            this.list.forEach(v => v.is_check = false)
        },

        setCaptained(item) {
            if (this.captained_list?.length >= 100) return xwy_api.alert('队长人数已达上限(100人)')
            const name = item.must_submit?.[0]?.value || '该队员'
            const team_name_tips = item['team_details']?.name ? `${item['team_details'].name}的` : ''
            uni.showModal({
                title: '设置队长',
                content: `确定将${name}设置为${team_name_tips}队长？`,
                success: res => {
                    res.confirm && this.setCaptainedAjax(item.userid)
                }
            })
        },

        async setCaptainedAjax(userid) {
            this.$uni.showLoading('设置中...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.admin.teamManager/set_user_team_manager',
                data: {
                    active_id: this.id,
                    team_id: this.params_team_id,
                    userid
                }
            })
            uni.hideLoading()
            if (res?.status !== 1) return xwy_api.alert(res?.info || '设置失败')
            this.$uni.showToast('队长设置成功', 'success')

            this.uploadTeamMemberList()
        },

        deleteCaptained(item) {
            const name = item.must_submit?.[0]?.value || '该队员'

            uni.showModal({
                title: '删除队长',
                content: `确定将${name}设置为普通队员？`,
                success: res => {
                    res.confirm && this.deleteCaptainedAjax(item.captained_id)
                }
            })
        },

        async deleteCaptainedAjax(id) {
            this.$uni.showLoading('设置中...')
            const res = await xwy_api.deleteRecords(70, id)
            uni.hideLoading()
            if (res?.status !== 1) return xwy_api.alert(res?.info || '设为普通队员失败')
            this.$uni.showToast('已设为普通队员', 'success')

            this.uploadTeamMemberList()
        },

        uploadTeamMemberList() {
            this.captained_list = null
            this.load_page = 1
            setTimeout(() => {
                this.getList()
            }, 1000)
        },

        lookBodyData(item) {
            const name = item.must_submit?.[0]?.value || ''
            const userid = item.userid
            if (!userid) return this.$uni.showToast('没有获取到会员的会员号')

            this.$uni.navigateTo(`/pages/task-challenge/admin/body-data-record?active_id=${this.id}&userid=${userid}&nickname=${name}`)
        },


        lookRunRecord(item) {
            const name = item.must_submit?.[0]?.value || ''
            const userid = item.userid
            if (!userid) return this.$uni.showToast('没有获取到会员的会员号')
            this.$uni.navigateTo(`/pages/running/user/run_list?id=${this.id}&nickname=${name}&userid=${userid}`)
        },


        async addOrSubtractIntegral(userid) {
            const modal = await this.$uni.showModal('', {
                title: '请输入增加或扣减的积分数，扣积分使用负数',
                showCancel: true,
                editable: true,
                placeholderText: '请输入增加或扣减的积分数'
            })
            if (!modal.confirm) return

            const num = Number(modal.content)
            if (isNaN(num) || num === 0) {
                this.$uni.showToast('请输入正确的积分数')
                return this.addOrSubtractIntegral(userid)
            }

            this.$uni.showLoading('修改中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin.activeSet/admin_add_user_integral',
                data: {
                    active_id: this.id,
                    userid,
                    num,
                    memo: '管理员修改积分'
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '修改失败')
            this.$uni.showToast('修改成功', 'success')
        },


        editExchangeDataShow(index) {
            const item = this.list[index]
            const {userid, exchange_kilo, sign_day, integral_all, integral_left} = item
            this.edit_exchange_temp_data = {
                userid,
                exchange_kilo,
                sign_day,
                integral_all,
                integral_left,
                nickname: item.must_submit?.[0]?.value || '',
                headimg: item.headimg || '',
                index
            }
            this.$refs.exchange_data_popup.open()
        },

        editExchangeDataConfirm() {
            const {
                nickname,
                exchange_kilo,
                sign_day,
                index,
                integral_all,
                integral_left
            } = this.edit_exchange_temp_data
            const new_exchange_kilo = Number(exchange_kilo)
            if (isNaN(new_exchange_kilo)) return uni.showToast({title: '里程数只能填写数字', icon: 'none'})

            const new_sign_day = Math.floor(sign_day)
            if (isNaN(new_sign_day)) return uni.showToast({title: '天数只能填写整数', icon: 'none'})

            const new_integral_all = Number(integral_all)
            if (isNaN(new_integral_all)) return this.$uni.showToast('累计积分只能填写数字')

            const new_integral_left = Number(integral_left)
            if (isNaN(new_integral_left)) return this.$uni.showToast('剩余积分只能填写数字')

            const {
                exchange_kilo: old_exchange_kilo = 0,
                sign_day: old_sign_day = 0,
                integral_all: old_integral_all = 0,
                integral_left: old_integral_left = 0
            } = this.list[index]
            if (new_exchange_kilo === old_exchange_kilo && new_sign_day === old_sign_day && new_integral_all === old_integral_all && new_integral_left === old_integral_left) {
                return this.$uni.showToast('数据未做修改')
            }


            const kilo_difference = Number((new_exchange_kilo - old_exchange_kilo).toFixed(2))
            const day_difference = new_sign_day - old_sign_day
            const i_all_difference = Number((new_integral_all - old_integral_all).toFixed(2))
            const i_left_difference = Number((new_integral_left - old_integral_left).toFixed(2))


            uni.showModal({
                title: '提示',
                content: `确定将 ${nickname}的累计兑换里程从${old_exchange_kilo}${this.kilo_unit}修改为${exchange_kilo}${this.kilo_unit}(${kilo_difference >= 0 ? '增加' : '减少'}${Math.abs(kilo_difference)}${this.kilo_unit})?
                累计兑换天数从${old_sign_day}修改为${sign_day}天(${day_difference >= 0 ? '增加' : '减少'}${Math.abs(day_difference)}天)?累计积分从${old_integral_all}修改为${new_integral_all}(${i_all_difference >= 0 ? '增加' : '减少'}${Math.abs(i_all_difference)}积分)?剩余积分从${old_integral_left}修改为${new_integral_left}(${i_left_difference >= 0 ? '增加' : '减少'}${Math.abs(i_left_difference)}积分)?`,
                success: res => {
                    res.confirm && this.editExchangeDataSubmit(index, new_exchange_kilo, new_sign_day, new_integral_all, new_integral_left)
                }
            })
        },
        async editExchangeDataSubmit(index, exchange_kilo, sign_day, integral_all, integral_left) {
            uni.showLoading({title: '修改中...', mask: true})
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.adminUser/change_user_attend_data',
                data: {
                    active_id: this.id,
                    userid: this.edit_exchange_temp_data.userid,
                    exchange_kilo,
                    sign_day,
                    integral_all,
                    integral_left
                }
            })
            uni.hideLoading()
            if (res?.status !== 1) return xwy_api.alert(res?.info || '修改失败')
            uni.showToast({title: '修改成功', icon: 'success'})
            this.list[index].exchange_kilo = exchange_kilo
            this.list[index].sign_day = sign_day
            this.list[index].integral_all = integral_all
            this.list[index].integral_left = integral_left
            this.$refs.exchange_data_popup.close()
        },

        changeTeam(item) {
            if (this.in_batch_operation && !this.getCheckId()) {
                return this.$uni.showToast('请选择需要修改队伍的用户')
            }

            this.team_id = ''
            this.team_name = ''
            this.team_item_data = item

            this.$uni.navigateTo(`./team_list?is_manage=true&selteam=true&showExitTeam=true&id=${this.id}`, {
                events: {
                    setTeam: data => {
                        this.team_id = data.team_id
                        this.team_name = data.team_name
                        setTimeout(() => {
                            this.changeTeamBack(this.team_item_data, data.team_id, data.team_name)
                            this.team_item_data = null
                            this.team_id = ''
                            this.team_name = ''
                        }, 500)
                    }
                }
            })
        },

        changeTeamBack(item, team_id, team_name) {
            if (!this.in_batch_operation && item?.['team_details']?.id && team_id === item['team_details'].id) {
                return this.$uni.showToast('修改失败！选择的队伍与用户的原队伍相同', 'none', 2000)
            }

            const tips = this.in_batch_operation ? `确定将选中用户的队伍修改成 ${team_name || '未选择队伍'}?` : `确定将用户 ${item.must_submit && item.must_submit[0] && item.must_submit[0].value || ''} 的队伍修改成 ${team_name || '未选择队伍'}?`

            uni.showModal({
                title: '提示',
                content: tips,
                success: res => {
                    if (res.confirm) {
                        const ids = this.in_batch_operation ? this.getCheckId() : item.id
                        this.change(ids, 2, team_id || '')
                    }
                }
            })
        },

        getCheckId() {
            const list = []
            this.list.forEach(v => v.is_check && list.push(v.id))
            return list.join(',')
        },

        batchChangeStatus() {
            const ids = this.getCheckId()
            if (!ids) return this.$uni.showToast('请选择需要修改状态的用户', 'none', 2000)

            const itemList = ['审核通过', '待审核']

            uni.showActionSheet({
                itemList,
                success: res => {
                    uni.showModal({
                        title: '提示',
                        content: `是否将选中用户的状态改为 ${itemList[res.tapIndex]}`,
                        success: res_ => {
                            if (res_.confirm) {
                                let status = 1
                                if (res.tapIndex === 1) status = 4
                                console.log(status)
                                this.change(ids, status)
                            }
                        }
                    })
                }
            })
        },


        changeUserStatus(item) {
            let status_title = '审核通过'
            let status = 1
            if (item.checked && item.checked === 1) {
                status_title = '待审核'
                status = 4
            }

            uni.showModal({
                title: '提示',
                content: `是否将用户 ${item.must_submit && item.must_submit[0] && item.must_submit[0].value || ''} 的状态改为 ${status_title}`,
                success: res => {
                    if (res.confirm) this.change(item.id, status)
                }
            })
        },

        batchDelete() {
            const ids = this.getCheckId()
            if (!ids) return this.$uni.showToast('请选择需要删除的用户')

            uni.showModal({
                title: '提示',
                content: `确定将选中用户移除出本活动？用户移除后无法找回。`,
                success: res => {
                    if (res.confirm) this.change(ids, 3)
                }
            })
        },

        deleteUser(item) {
            uni.showModal({
                title: '提示',
                content: `确定将用户 ${item.must_submit && item.must_submit[0] && item.must_submit[0].value || ''} 移除出本活动？用户移除后无法找回。`,
                success: res => {
                    if (res.confirm) this.change(item.id, 3)
                }
            })
        },

        change(ids, type, team_id) {
            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id,
                ids: ids,
                act_types: type
            }

            if (team_id) data.team_id = team_id
            if (this.is_team_manager) {
                data.is_team_manager = 1
                data.team_id = this.params_team_id
            }

            const type_title = type === 3 ? '删除' : '修改'
            this.$uni.showLoading(`${type_title}中...`)

            xwy_api.ajax({
                url: 'front.flat.sport_step.admin/batch_change_user',
                data,
                success: res => {
                    uni.hideLoading()
                    if (!res.status) return this.$uni.showModal(res.info || '修改失败')

                    this.$uni.showToast(`已${type_title}`)

                    setTimeout(() => {
                        this.load_page = 1
                        this.getList()
                    }, 800)
                }
            })

        },


        async ZSB(item) {
            const tips = `确定将${item.must_submit?.[0]?.value || '用户'}加入黑名单？加入黑名单后, 将会清除用户在本活动的所有数据并将用户移除出本活动。加入黑名单的用户将无法参与本活动。是否继续？`
            const modal = await this.$uni.showModal(tips, {showCancel: true})
            if (!modal.confirm) return

            this.$uni.showLoading('正在加入黑名单...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.adminUser/set_user_black_list',
                data: {
                    active_id: this.id,
                    userid: item.userid
                }
            })
            uni.hideLoading()
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '加入黑名单失败')
            this.$uni.showToast('加入黑名单成功', 'success')
            setTimeout(() => {
                this.load_page = 1
                this.getList()
            }, 1000)
        },


        selectUser(item) {
            const nickname = item.must_submit?.[0]?.value || ''
            const OEC = this.getOpenerEventChannel()
            OEC?.emit?.('selectUser', {userid: item.userid, nickname})
            uni.navigateBack()
        }
    }
}
</script>

<style scoped lang="scss">
.page {
    min-height: 100vh;
    padding-top: 60px;
    padding-bottom: 100px;
    box-sizing: border-box;
}

.top-view {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 999;
}

.top-tab-item {
    height: 40px;
    line-height: 40px;
    box-sizing: border-box;
}

.active-top-bar {
    color: #2D8CF0;
    border-bottom: 2px solid #2D8CF0;
}

.search .input {
    width: calc(100% - 70px);
    border-radius: 18px;
}

.search-go {
    width: 60px;
    min-width: 60px;
    text-align: right;
    line-height: 36px;
}


/* #ifdef H5 */
.search-go {
    padding-right: 10px;
    box-sizing: border-box;
}

@media screen and (min-width: 500px) {
    .top-view {
        width: 500px;
        left: calc(((100vw - 500px) / 2) - 10px);
    }

    .search .input {
        width: 440px;
    }
}

/* #endif */


.item {
    margin: 10px;
    padding: 10px 10px 10px 0;
    border-radius: 10px;
}

.headimg {
    width: 50px;
    min-width: 50px;
    height: 50px;
    border-radius: 50%;
    display: block;
}

.middle {
    width: 100%;
    padding-left: 10px;
    overflow: hidden;
    box-sizing: border-box;
}

/*.right {
    width: 100px;
    min-width: 100px;
}

.right-item {
    width: 100px;
}*/

.item-opt-bar {
    border-top: 1px solid #eee;
    margin-top: 10px;
    padding-top: 10px;
}

.opt-item {
    //border: 1px solid #80848f;
    line-height: 22px;
    border-radius: 12px;
    //margin-left: 5px;
}


.bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 10px 10px 20px;
    box-sizing: border-box;
    border-top: 1px solid #eee;
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .bottom-bar {
        width: 500px;
        left: calc(((100vw - 500px) / 2) - 10px);
    }
}

/* #endif */
.batch-btn {
    border: 1px solid #eee;
    line-height: 24px;
    border-radius: 13px;
    margin-left: 5px;
    padding: 0 5px;
    font-size: 14px;
}

.exchange-data-popup {
    width: 80vw;
    max-width: 400px;
    padding: 10px;
    border-radius: 10px;

    .exchange-data-popup-title {
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
        margin-bottom: 10px;
    }

    image {
        width: 80px;
        height: 80px;
        border-radius: 50%;
    }

    .exchange-data-popup-button {
        width: 200px;
        line-height: 44px;
        border-radius: 22px;
        margin-top: 20px;
    }
}
</style>
