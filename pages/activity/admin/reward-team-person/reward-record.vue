<template>
    <view>
        <view v-if="record_total_number" class="color-sub p10 text-center font14">
            共{{ record_total_number }}条记录
        </view>
        <view class="record-list">
            <view class="record-item flex-row" v-for="(item, index) in record_list" :key="index">
                <image class="avatar" mode="aspectFill" :src="item.headimg"/>
                <view class="msg-info pl10">
                    <view class="color-title">{{ item.nickname || '用户已删除' }}</view>
                    <view v-if="item.memo" class="color-content font14 pt5">{{ item.memo }}</view>
                    <view v-if="item.create_time" class="color-sub font16">{{ item.create_time }}</view>
                </view>
                <view class="flex-all-center">
                    <view class="amount color-warning text-right">
                        <text class="font18">{{ (item.amount || 0).toFixed(2) }}</text>
                        <text class="font14">元</text>
                    </view>
                </view>
            </view>
        </view>

        <view v-if="loading" class="text-center">
            <view v-if="current_page === 1" style="width: 100%; height: 30vh;"></view>
            <load-ani></load-ani>
        </view>

        <view v-if="!record_list.length && !loading" class="text-center" style="padding-top: 15vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无红包记录</view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            loading: true,
            record_list: [],
            current_page: 1,
            is_last_page: false,
            record_total_number: 0
        }
    },

    onLoad(params) {
        this.id = params.id
        this.getRecord()
    },

    onReachBottom() {
        !this.loading && !this.is_last_page && this.getRecord()
    },

    methods: {

        async getRecord() {
            this.loading = true
            if (this.current_page === 1) {
                this.record_list = []
                this.is_last_page = false
                this.record_total_number = 0
            }
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin.redpack/active_top_rank_redpack_records',
                data: {
                    active_id: this.id,
                    page: this.current_page,
                    perpage: 20
                }
            })
            this.loading = false
            const res_data = res?.data?.list
            if (!res_data) {
                this.is_last_page = true
                return
            }
            const list = res_data.data || []
            this.record_list = [...this.record_list, ...list]
            this.is_last_page = res_data.is_lastpage
            this.current_page++
            this.record_total_number = res_data.total
        }
    }
}
</script>

<style lang="scss">
.record-item {
    padding: 10px;
    margin: 0 10px 10px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0 10px #eee;

    .avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: block;
    }

    .msg-info {
        width: calc(100% - 100px - 60px);
    }

    .amount {
        width: 100px;
    }
}
</style>
