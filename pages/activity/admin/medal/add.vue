<template>
	<view>
		<view class="form">
			<view class="form-item">
			    <view class="top color-content">
			        <text>勋章名称</text>
			        <text class="color-error font16"> *</text>
			    </view>
			    <view class="bottom font16">
			        <input class="input" type="text" v-model="name" placeholder="请输入勋章名称"/>
			    </view>
			</view>

			<view class="form-item">
			    <view class="top color-title">
			        <text>勋章图片</text>
					<text class="color-error font16"> *</text>
			    </view>
			    <view style="padding-top: 5px;">
			        <view class="image-view" v-if="logo">
			            <image class="image-item" :src="logo" mode="widthFix" @click="previewImage([logo])"/>
			            <view class="del-image-item" @click.stop="logo = ''">
			                <uni-icons type="closeempty" color="#e20f04"/>
			            </view>
			        </view>
			        <navigator
						v-else
						class="add-image flex-all-center"
						:url="'/pages/other/image_upload_or_select?key=logo&active_id=' + id"
					>
			            <uni-icons type="plusempty" size="48" color="#eeeeee"/>
			        </navigator>
			    </view>
			</view>




		</view>

		<view class="bottom-btn-view bg-white flex-all-center">
		    <view
		        class="login-btn color-white text-center font18 bg-primary"
		        @click="addMedal"
		    >{{ id ? '保存' : '创建' }}
		    </view>
		</view>
	</view>
</template>

<script>
	const app = getApp()
	import login from '../../../../utils/api/login.js'
	import xwy_api from '../../../../utils/api/xwy_api.js'
	import base64 from '../../../../utils/base64.js'


	export default {
		data() {
			return {
				name: '',
				id: '',
				logo: ''
			}
		},
		onLoad(e) {
			this.id = e.active_id;
			let navigationBarTitle = '添加勋章'
		    if (e.id) {
		        navigationBarTitle = '修改勋章信息'
				this.id = e.id
				this.getMedalDetail()
		    }
			uni.setNavigationBarTitle({
			    title: navigationBarTitle
			})
		},

		methods: {
			async getMedalDetail() {
				uni.showLoading({
					title: '加载中...',
					mask: app.globalData.evn_version === 'trial' ? false : true
				})

				const data = {
					category_id: this.id,
					access_token: app.globalData.access_token
				}
				const res = await xwy_api.request({url: 'front.user.category/category_details', data})

				uni.hideLoading()

				if (res?.data?.category_details) {
					const detail = res.data.category_details
					this.name = detail.name
					this.logo = detail.logo
				}
			},


			addMedal() {
				if (!this.name) {
					uni.showToast({
						title: '请输入勋章名称',
						icon: 'error'
					})
					return false
				}

				if (!this.logo) {
					uni.showToast({
						title: '请上传勋章图片',
						icon: 'error'
					})
					return false
				}

				const data = {
					access_token: app.globalData.access_token,
				    name: this.name,
					types: 7,
					logo: this.logo
				}
				if (this.id) data.id = this.id



				this.addMedalAjax(data);
			},

			addMedalAjax(data) {
			    uni.showLoading({
			        title: '保存中...',
					mask: app.globalData.evn_version === 'trial' ? false : true
			    })


				xwy_api.ajax({
					url: 'front.user.category/create_category',
					data,
					success: res => {
						console.log(res)
						if (!res.status) {
						    uni.showModal({
						        title: '提示',
						        content: res.info || '保存失败',
						        showCancel: false
						    })
						    return false
						}

						uni.showToast({
						    title: res.info || '保存成功',
						    icon: 'success'
						})


						const eventChannel = this.getOpenerEventChannel();
						eventChannel && eventChannel.emit('uploadList')
						const timeout = setTimeout(() => {
							uni.navigateBack();
							clearTimeout(timeout)
						}, 1000)
					}
				})


			},

			previewImage(urls, current = urls[0]) {
			    console.log(urls)
			    uni.previewImage({
			        urls,
					current
			    })
			},
		}
	}
</script>

<style>
.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.add-image {
    width: 200px;
    height: 200px;
    border: 1px solid #eee;
    box-sizing: border-box;
    border-radius: 5px;
	margin: 5px;
}

.image-item {
    width: calc(100vw - 20px);
    border-radius: 5px;
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .image-item {
        width: 480px;
    }
}
/* #endif */


.image-view {
    position: relative;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}


.bottom-btn-view {
    position: fixed;
    z-index: 99999;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.login-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}

</style>
