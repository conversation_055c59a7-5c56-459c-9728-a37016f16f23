<template>
    <view class="page bg-background">

		<template v-if="list.length">
			<view class="flex-kai bg-white">
				<view class="color-sub p10 font14">共{{ count_num }}个勋章</view>
				<view class="color-primary p10" @click="addMedal('')">
					<text>添加勋章</text>
					<uni-icons type="forward" color="#2d8cf0"/>
				</view>
			</view>

			<view class="list ptm5">
				<view class="item bg-white" v-for="(item, index) in list" :key="index">
					<view class="flex-row">
						<image class="logo" :src="item.logo" mode="aspectFill"/>
						<view class="pl10 color-title font18">{{item.name}}</view>
					</view>
					<view class="tools-bar flex-kai">
						<view style="line-height: 34px;">

						</view>
						<view class="flex-row">
							<view class="edit" @click="addMedal(item.category_id)">
								<text class="iconfont icon-edit color-sub font20"></text>
							</view>
							<view class="delete" @click="deleteItem(item.category_id)">
								<text class="iconfont icon-delete color-sub font20"></text>
							</view>

							<button
								style="margin-left: 10px;"
								v-if="is_sel_medal"
								size="mini"
								class="bg-primary color-white"
								@click="seleteMedal(item)"
							>选择此勋章
							</button>
						</view>
					</view>
				</view>
			</view>
		</template>

		<view v-if="!loading && !list.length" class="text-center" style="padding-top: 10vh;">
			<text class="iconfont icon-medal color-border" style="font-size: 100px;"></text>
			<view class="color-sub">暂无勋章</view>
			<view v-if="!is_sel_medal" class="flex-all-center pt15">
				<button class="add-team-btn color-white bg-primary" @click="addMedal('')">添加勋章</button>
			</view>
		</view>

		<uni-load-more v-if="loading" status="loading"></uni-load-more>
		<uni-load-more v-if="is_last_page && list.length > 5" status="noMore" :contentText="{contentnomore: '我是有底线的'}"></uni-load-more>
		<uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>

    </view>
</template>

<script>
const app = getApp()
import login from '../../../../utils/api/login.js'
import xwy_api from '../../../../utils/api/xwy_api.js'


export default {
    data() {
        return {
            is_sel_medal: false,  // 是不是进来选择队伍的
            loading: true,
            list: [],
			load_page: 1,
			is_last_page: false,
            count_num: 0
        }
    },
    onLoad(e) {
        uni.showLoading()
        if (e.sel) {
            this.is_sel_medal = true
            uni.setNavigationBarTitle({
                title: '选择勋章'
            })
        }
        this.id = e.id
        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                uni.showModal({
                    title: err.errTitle || '提示',
                    content: err.errMsg,
                    showCancel: false
                })
                return false
            }


            this.getList()
        })
    },
    onPullDownRefresh() {
        if (!this.loading) this.getListInit()
        uni.stopPullDownRefresh()
    },

	onReachBottom() {
		if (!this.loading && !this.is_last_page) this.getList()
	},

    methods: {
        search() {
            this.getListInit()
        },


        getListInit() {
            this.loading = true
            this.load_page = 1
			uni.showLoading({
				title: '加载中...',
				mask: true
			})
            this.getList()
        },

        async getList() {
			if (this.load_page === 1) {
				this.list = [];
				this.is_last_page = false
			}

            const res = await xwy_api.getCategoryList({
                types: 7,
                my_self: 1,
                page: this.load_page,
                perpage: 20
			})
            uni.hideLoading()
            this.loading = false

            if (res?.status !== 1) {
                this.is_last_page = true
                return
            }

            const data = res?.data?.category_list

            if (!data) {
                if (this.load_page === 1) this.count_num = 0
                this.is_last_page = true
                return
            }

            const list = data.data || []
            this.load_page++
            this.list = [...this.list, ...list]
            this.is_last_page = data.is_lastpage
            this.count_num = data.total || 0
        },


        addMedal(id) {
			let url = './add'
			if (id) url += `?id=${id}`

			uni.navigateTo({
				url,
				events: {
					uploadList: () => {
						this.loading = true
						this.load_page = 1
						this.getList()
					}
				}
			})
        },



        deleteItem(id) {
            this.deleteConfirm([id])
        },

        deleteConfirm(ids) {
            uni.showModal({
                title: '提示',
                content: `确定删除${ids.length > 1 ? '选中的' : '该'}勋章?`,
                confirmText: '删除',
                cancelColor: '#80848f',
                confirmColor: '#ed3f14',
                success: res => {
                    if (res.confirm) this.deleteAjax(ids)
                }
            })
        },

        deleteAjax(ids) {
            uni.showLoading({
                title: '删除中...',
				mask: app.globalData.evn_version === 'trial' ? false : true
            })

			xwy_api.ajax({
				url: 'front.user.category/category_del',
				data: {
					access_token: app.globalData.access_token,
					ids: ids.join(',')
				},
				success: res => {
					if (!res.status) {
					    uni.showToast({
					        title: res.info || '删除失败',
					        icon: 'error'
					    })
					    return false
					}


					uni.showToast({
					    title: '删除成功',
					    icon: 'success'
					})

					const timeout = setTimeout(() => {
						this.getListInit()
						clearTimeout(timeout)
					}, 1000)
				}
			})
        },

        seleteMedal(item) {
			const eventChannel = this.getOpenerEventChannel();
			eventChannel && eventChannel.emit('selMedal', {name: item.name, id: item.category_id, logo: item.logo})
			uni.navigateBack()
        },

    }
}
</script>

<style scoped>
.page {
    min-height: 100vh;
    padding-bottom: 10px;
    box-sizing: border-box;
}


.list {

}

.item {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
}

.logo {
	width: 50px;
	height: 50px;
	border-radius: 5px;
	display: block;
}

.tools-bar {
    border-top: 1px solid #eee;
	margin-top: 5px;
    padding-top: 10px;
}

.edit, .delete {
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 50%;
}

.edit {
    border: 1px solid #eee;
    margin-right: 10px;
}

.delete {
    border: 1px solid #eee;
}

.add-team-btn {
    width: 200px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
}

/* #ifdef H5 */

@media screen and (min-width: 500px) {
    .search {
        width: 500px;
        left: calc(((100vw - 500px) / 2) - 10px);
    }

    .search .input {
        width: calc(500px - 65px);
    }
}

/* #endif */
</style>
