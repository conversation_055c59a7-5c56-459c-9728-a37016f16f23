<template>
    <view>
        <view class="form">
            <view class="form-item">
                <view class="top color-content">
                    <text>队伍名称</text>
                    <text class="color-error font16"> *</text>
                </view>
                <view class="bottom font16">
                    <input class="input" type="text" v-model="team_name" placeholder="请输入队伍名称"/>
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">加入队伍是否需要密码</view>
                <view class="bottom font16">
                    <view class="flex-kai">

                        <picker
                            class="view"
                            mode="selector"
                            :range="need_password_opt"
                            :value="need_password_opt.findIndex(v => v.value === need_password)"
                            range-key="title"
                            @change="need_password = need_password_opt[$event.detail.value].value"
                        >
                            {{ need_password === 1 ? '需要密码' : '无需密码' }}
                        </picker>
                        <view class="flex-all-center">
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </view>
            </view>

            <view v-if="need_password === 1" class="form-item">
                <view class="top color-content">队伍密码</view>
                <view class="bottom font16">
                    <input
                        class="input"
                        type="text"
                        v-model="password"
                        :placeholder="have_password ? '已设置密码，无需修改密码不用填写' : '设置密码后,需要输入正确密码才可加入队伍'"
                    />
                </view>
            </view>

            <view v-if="show_limit_person_set" class="form-item">
                <view class="top color-content">人数限制</view>
                <view class="bottom font16">
                    <input class="input" type="number" maxlength="5" v-model="limit_person"
                           placeholder="队伍人数上限,不限制人数不填即可"/>
                </view>
            </view>

            <view v-if="teamSloganOpen" class="form-item">
                <view class="top color-content">队伍口号</view>
                <view class="bottom font16">
                    <input class="input" type="text" v-model="team_slogan" placeholder="请输入队伍口号"/>
                </view>
            </view>

            <view v-if="team_category_open" class="form-item">
                <view class="top color-content">队伍分类</view>
                <view class="bottom font16">
                    <view class="flex-kai" @click="selectCategory">
                        <view class="view">
                            <view v-if="category_id">{{ category_name || category_id }}</view>
                            <view v-else class="color-sub">绑定队伍分类</view>
                        </view>
                        <view class="flex-all-center">
                            <view
                                v-if="category_id"
                                class="color-sub font12"
                                style="width: 30px;"
                                @click.stop="deleteCategory"
                            >解绑
                            </view>
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </view>
            </view>

            <view v-if="!is_user" class="form-item">
                <view class="top color-content">队伍序号</view>
                <view class="bottom font16">
                    <input
                        class="input"
                        type="number"
                        :maxlength="5"
                        v-model="sort_num"
                        placeholder="数字越小排在越前(不填默认0)"
                    />
                </view>
            </view>

            <view class="form-item">
                <view class="top color-title">队伍logo</view>
                <view style="padding-top: 5px;">
                    <view class="image-view" v-if="logo">
                        <image
                            class="image-item"
                            :src="logo"
                            mode="aspectFill"
                            @click="previewImage([logo])"
                        />
                        <view class="del-image-item" @click.stop="logo = ''">
                            <uni-icons type="closeempty" color="#e20f04"/>
                        </view>
                    </view>

                    <navigator
                        v-else
                        class="add-image text-center"
                        :url="'/pages/other/image_upload_or_select?key=logo&active_id=' + (id || '')"
                    >
                        <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                    </navigator>
                </view>
            </view>
        </view>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view
                class="login-btn color-white text-center font18 bg-primary"
                @click="addTeam"
            >{{ team_id ? '保存' : '创建' }}
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import xwy_api from '../../../utils/api/xwy_api.js'
import base64 from '../../../utils/base64.js'


export default {
    data() {
        return {
            id: '',
            team_name: '',
            limit_person: '',
            team_id: '',
            sort_num: '',
            need_password: 0,
            need_password_opt: [
                {value: 0, title: '无需密码'},
                {value: 1, title: '需要密码'}
            ],
            password: '',
            have_password: false,
            is_user: false,
            logo: '',
            category_id: '',
            category_name: '',
            team_category_open: false,

            teamSloganOpen: false,
            team_slogan: '',
            show_limit_person_set: true
        }
    },
    onLoad(e) {
        if (e.is_user) this.is_user = true
        this.id = e.active_id
        this.teamSloganOpen = this.xwy_config.teamSloganOpen(e.active_id)
        if (e.category_id && e.category_name) {
            this.category_id = e.category_id
            this.category_name = e.category_name
        }

        let navigationBarTitle = '添加队伍'
        if (e.team_id) {
            navigationBarTitle = '修改队伍信息'
            this.team_id = e.team_id
        }
        this.$uni.setNavigationBarTitle(navigationBarTitle)

        this.$login.uniLogin(() => {
            this.init()
        })


    },

    methods: {
        async init() {
            this.$uni.showLoading()
            await this.getActiveDetails()
            if (this.team_id) await this.getTeamDetail()
            uni.hideLoading()
        },

        async getActiveDetails() {
            let details = app.globalData['activity_detail']

            if (!details || details.active_id !== this.id) {
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.id
                    }
                })

                details = res?.['data']?.['active_details'] || {}
            }

            if (!details) return

            if (details?.rank_set?.['team_category']) {
                // 在线报名活动队伍分类和队伍不用绑定，所有队伍都能选择所有分类
                if (details.types !== 17) this.team_category_open = true
            }

            // 活动设置了用户创建队伍的人数上限，用户创建队伍不能设置人数上限
            if (this.is_user && details.conf.active.team_limit_person) this.show_limit_person_set = false
        },

        async getTeamDetail() {
            this.$uni.showLoading('加载中...')

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/team_group_details',
                data: { team_id: this.team_id }
            })

            uni.hideLoading()
            const team_detail = res.data['team_group_details']
            if (team_detail) {
                this.team_name = team_detail.name

                if (team_detail.limit_person) this.limit_person = team_detail.limit_person
                if (team_detail.sort_num) this.sort_num = team_detail.sort_num
                if (team_detail.conf) {
                    const conf = team_detail.conf
                    if (conf.password) {
                        this.have_password = true
                        this.need_password = 1
                        this.old_password = conf.password
                    }
                    if (conf.logo) this.logo = conf.logo
                    if (conf.team_slogan) this.team_slogan = conf.team_slogan
                }

                if (team_detail['category_details']) {
                    this.category_id = team_detail['category_details'].id
                    this.category_name = team_detail['category_details'].name
                }
            }
        },

        selectCategory() {
            uni.navigateTo({
                url: '/pages/category/list?types=19&is_sel=1&active_id=' + this.id,
                events: {
                    selCategory: category => {
                        this.category_id = category.id
                        this.category_name = category.name
                    }
                }
            })
        },
        deleteCategory() {
            this.category_id = ''
            this.category_name = ''
        },

        addTeam() {
            if (!this.team_name) return this.$uni.showToast('请输入队伍名称', 'error')

            const data = {
                access_token: app.globalData['access_token'],
                name: this.team_name,
                active_id: this.id,
                sort_num: this.sort_num || 0,
                limit_person: Number(this.limit_person),
                category_id: this.category_id,
            }
            if (isNaN(data.limit_person)) return this.$uni.showToast('人数限制请输入数字', 'error' )
            if (data.limit_person && data.limit_person > 99999) this.$uni.showModal('人数限制不能超过99999人')

            if (this.team_id) {
                data.id = this.team_id
                data.is_admin = 1   // 管理员才能修改队伍信息
            }

            const conf = {}
            if (this.logo) conf.logo = this.logo

            if (this.need_password === 1) {
                if (!this.password && !this.have_password) 
                    return this.$uni.showModal('请输入队伍密码，或将队伍设置成 无需密码')
                
                if (this.old_password) conf.password = this.old_password
                if (this.password) {
                    if (this.password.length < 3) return this.$uni.showModal('队伍密码不得少于3个字符')
                    conf.password = this.password
                }
            } else {
                if (this.have_password) conf.cancel_password = 1
            }
            
            if (this.team_slogan) conf.team_slogan = this.team_slogan

            let conf_str = JSON.stringify(conf)
            conf_str = conf_str.replace(/·/g, '-')
            data.conf = base64['encode'](conf_str)

            this.addTeamAjax(data)
        },

        addTeamAjax(data) {
            this.$uni.showLoading('保存中...')

            xwy_api.ajax({
                url: 'front.flat.sport_step.admin/create_update_team_group',
                data,
                success: res => {
                    uni.hideLoading()
                    if (!res.status) return this.$uni.showModal(res.info || '保存失败')

                    this.$uni.showToast(res.info || '保存成功', 'success')

                    this.getOpenerEventChannel?.()?.emit('uploadList')
                    this.$uni.navigateBack(1, { delay: 1000 })
                }
            })
        },


        previewImage(urls, current = urls[0]) {
            uni.previewImage({
                urls,
                current
            })
        },
    }
}
</script>

<style>
.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}

.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
    width: 120px;
    height: 120px;
    border-radius: 5px;
    line-height: 120px;
    margin: 5px;
}

.image-item {
    width: 120px;
    height: 120px;
    border-radius: 5px;
}

.image-view {
    position: relative;
    width: 120px;
}

.del-image-item {
    position: absolute;
    right: 5px;
    top: 5px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}

.bottom-btn-view {
    position: fixed;
    z-index: 99999;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.login-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}

</style>
