<template>
    <view class="page">
        <view class="font14 p10" style="color: #e19898;">
            提示: 最后一个点位，请设置里程最小值与最大值一致，比如终点为25000，则最后一个点位的里程设置为 最小值(25000)
            最大值(25000)。
        </view>
        <view class="form">
            <view class="form-item">
                <view class="top color-content">
                    <text>点位名称</text>
                    <text class="color-error font16"> *</text>
                </view>
                <view class="bottom font16">
                    <input class="input" type="text" v-model="name" :maxlength="30" placeholder="请输入点位名称"/>
                </view>
            </view>

            <template v-if="needMileage">
                <view class="form-item">
                    <view class="top color-content">
                        <text>最小里程数</text>
                        <text class="color-error font16"> *</text>
                        <text class="color-sub font12 pl5">(不含设置的里程数)</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="digit" v-model="min_num" maxlength="8"
                               placeholder="请输入点位最小里程数"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <text>最大里程数</text>
                        <text class="color-error font16"> *</text>
                        <text class="color-sub font12 pl5">(含设置的里程数)</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="digit" v-model="max_num" maxlength="8"
                               placeholder="请输入点位最大里程数"/>
                    </view>
                </view>
            </template>


            <template v-if="map_type !== 102">
                <view class="form-item">
                    <view class="top color-content">
                        <text>点位坐标</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view class="bottom font16">
                        <view class="flex-kai" @click="chooseLocation">
                            <view class="view">
                                <view v-if="lat && lng">{{ lat }},{{ lng }}</view>
                                <view v-else class="color-sub">请选择点位坐标</view>
                            </view>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <template v-if="userid === 132 || userid === 2019753">
                    <view class="form-item">
                        <view class="top color-content">输入点位坐标 - 纬度</view>
                        <view class="bottom font16">
                            <input class="input" type="digit" v-model="lat" maxlength="20"/>
                        </view>
                    </view>
                    <view class="form-item">
                        <view class="top color-content">输入点位坐标 - 经度</view>
                        <view class="bottom font16">
                            <input class="input" type="digit" v-model="lng" maxlength="20"/>
                        </view>
                    </view>
                </template>
            </template>

            <view class="form-item">
                <view class="top color-content">点位排序</view>
                <view class="bottom font16">
                    <input
                        class="input"
                        type="number"
                        v-model="sort_num"
                        :maxlength="5"
                        placeholder="地图点位排序,数字越小排在越前(不填默认0)"
                    />
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">文章介绍</view>
                <view class="bottom font16">
                    <view class="flex-kai" @click="toSelNews">
                        <view class="view">
                            <view v-if="news_id">{{ news_title || news_id }}</view>
                            <view v-else class="color-sub">选择文章</view>
                        </view>
                        <view class="flex-all-center">
                            <view
                                v-if="news_id"
                                class="color-sub font12"
                                style="width: 30px;"
                                @click.stop="deleteNews"
                            >解绑
                            </view>
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">答题考试</view>
                <view class="bottom font16">
                    <view class="flex-kai" @click="toSelExam">
                        <view class="view">
                            <view v-if="exam_id">{{ exam_title || exam_id }}</view>
                            <view v-else class="color-sub">选择考卷</view>
                        </view>
                        <view class="flex-all-center">
                            <view
                                v-if="exam_id"
                                class="color-sub font12"
                                style="width: 30px;"
                                @click.stop="deleteExam"
                            >解绑
                            </view>
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </view>
            </view>


            <template>
                <view class="form-item">
                    <view class="top color-content">抽奖设置</view>
                    <view class="bottom font16">
                        <view class="flex-kai" @click="toSelLottery">
                            <view class="view">
                                <view v-if="lottery.lottery_id">{{ lottery_title || lottery.lottery_id }}</view>
                                <view v-else class="color-sub">关联抽奖活动</view>
                            </view>
                            <view class="flex-all-center">
                                <view
                                    v-if="lottery.lottery_id"
                                    class="color-sub font12"
                                    style="width: 30px;"
                                    @click.stop="deleteLottery"
                                >解绑
                                </view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="rank_set.exam_open && rank_set['lottery_open']" class="form-item">
                    <view class="top color-content">抽奖方式</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                mode="selector"
                                :range="['到达点位即可抽奖', '到达点位并答题才能参与抽奖']"
                                :value="lottery.must_exam"
                                @change="lotteryModeChange($event)"
                            >
                                <template v-if="lottery.must_exam && lottery.must_exam === 1">
                                    到达点位并答题才能参与抽奖
                                </template>
                                <template v-else>到达点位即可抽奖</template>
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="lottery.must_exam === 1" class="form-item">
                    <view class="top color-content">
                        <text>抽奖分数</text>
                        <text class="font12 color-sub pl5">(答题分数大于等于填写的分数才可抽奖)</text>
                    </view>
                    <view class="bottom font16">
                        <input
                            class="input"
                            type="digit"
                            :maxlength="3"
                            v-model="lottery.score_min"
                            placeholder="0或不填代表不限制分数"
                        />
                    </view>
                </view>
            </template>

            <template v-if="map_type === 102">
                <view class="form-item">
                    <view class="top color-content">
                        <text>点位手绘地图图片</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view style="padding-top: 5px;">
                        <view class="image-view" v-if="iconPath">
                            <image
                                class="image-item"
                                :src="iconPath"
                                mode="aspectFill"
                                @click="previewImage([iconPath])"
                            />
                            <view class="del-image-item" @click.stop="iconPath = ''">
                                <uni-icons type="closeempty" color="#e20f04"/>
                            </view>
                        </view>
                        <view v-else class="add-image text-center" @click="selectImage('iconPath')">
                            <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <text>点位滚动位置</text>
                        <text class="pl5 color-sub font12">(单位:px)</text>
                    </view>
                    <view class="bottom font16">
                        <input
                            class="input"
                            type="digit"
                            v-model="scroll_position"
                            :maxlength="5"
                            placeholder="单位:px"
                        />
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <text>当前点位与图片顶部的距离</text>
                        <text class="pl5 color-sub font12">(单位:px)</text>
                    </view>
                    <view class="bottom font16">
                        <input
                            class="input"
                            type="digit"
                            v-model="margin_top"
                            :maxlength="5"
                            placeholder="单位:px"
                        />
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <text>当前点位与图片左边的距离</text>
                        <text class="pl5 color-sub font12">(单位:px)</text>
                    </view>
                    <view class="bottom font16">
                        <input
                            class="input"
                            type="digit"
                            v-model="margin_left"
                            :maxlength="5"
                            placeholder="单位:px"
                        />
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <text>当前点位的宽度</text>
                        <text class="pl5 color-sub font12">(单位:px)</text>
                    </view>
                    <view class="bottom font16">
                        <input
                            class="input"
                            type="digit"
                            v-model="point_width"
                            :maxlength="5"
                            placeholder="单位:px"
                        />
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <text>当前点位的高度</text>
                        <text class="pl5 color-sub font12">(单位:px)</text>
                    </view>
                    <view class="bottom font16">
                        <input
                            class="input"
                            type="digit"
                            v-model="point_height"
                            :maxlength="5"
                            placeholder="单位:px"
                        />
                    </view>
                </view>

                <view v-if="rank_set['stepRewardIntegralForMinMax']" class="form-item">
                    <view class="top color-content">已答题图片</view>
                    <view style="padding-top: 5px;">
                        <view class="image-view" v-if="and_drawn_map_with_answer_picture">

                            <image class="image-item" mode="aspectFill"
                                   :src="and_drawn_map_with_answer_picture"
                                   @click="previewImage([and_drawn_map_with_answer_picture])"/>
                            <view class="del-image-item" @click.stop="and_drawn_map_with_answer_picture = ''">
                                <uni-icons type="closeempty" color="#e20f04"/>
                            </view>
                        </view>

                        <view v-else class="add-image text-center"
                              @click="selectImage('and_drawn_map_with_answer_picture')">
                            <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                        </view>
                    </view>
                </view>
            </template>

            <view v-if="rank_set['medal_open']" class="form-item">
                <view class="top color-content">点位勋章</view>
                <view style="padding-top: 5px;">
                    <view class="image-view" v-if="medal_logo">
                        <image
                            class="image-item"
                            :src="medal_logo"
                            mode="aspectFill"
                            @click="previewImage([medal_id])"
                        />
                        <view class="del-image-item" @click.stop="medal_logo = '', medal_id = ''">
                            <uni-icons type="closeempty" color="#e20f04"/>
                        </view>
                    </view>
                    <view v-else class="add-image text-center" @click.stop="selMedal">
                        <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                    </view>
                </view>
            </view>

            <template v-if="rank_set['redpack'] && redpack_type === 1">
                <view class="form-item">
                    <view class="top color-content">红包设置</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                mode="selector"
                                :range="redpack_open_opt"
                                :value="redpack_open_opt.findIndex(v => v.value === redpack_rules.open)"
                                range-key="title"
                                @change="redpack_rules.open = redpack_open_opt[$event.detail.value].value"
                            >
                                {{ redpack_open_opt.find(v => v.value === redpack_rules.open).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>


                <view class="form-item">
                    <view class="top color-content">
                        <text>单个红包金额</text>
                        <text class="color-error font16"> *</text>
                        <text class="pl5 color-sub font12">(单位: 元。)</text>
                    </view>
                    <view class="bottom font16 pt5 pb5">
                        <view class="flex-row">
                            <view style="width: 100px;">
                                <uni-easyinput
                                    type="digit"
                                    maxlength="6"
                                    v-model="redpack_rules.amount.min"
                                ></uni-easyinput>
                            </view>
                            <view class="pl10 pr10 color-sub flex-all-center">-</view>
                            <view style="width: 100px;">
                                <uni-easyinput
                                    type="digit"
                                    maxlength="6"
                                    v-model="redpack_rules.amount.max"
                                ></uni-easyinput>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view class="top color-content">
                            <text>点位红包余额</text>
                            <text class="color-error font16"> *</text>
                            <text class="pl5 color-sub font12">(单位: 元。)</text>
                        </view>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="digit" v-model="redpack_amount" :maxlength="8"/>
                    </view>
                </view>
            </template>

            <template v-if="rank_set['together_pic']">
                <view class="form-item">
                    <view class="top color-content">是否开启点位照片合成</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                mode="selector"
                                :range="['关闭', '开启']"
                                :value="together_pic.open"
                                @change="together_pic.open = Number($event.detail.value)"
                            >
                                {{ together_pic.open === 1 ? '开启' : '关闭' }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <template v-if="together_pic.open">
                    <view class="form-item">
                        <view class="top color-content">
                            <text>照片合成背景图</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view style="padding-top: 5px;">
                            <view class="image-view" v-if="together_pic.background_src">
                                <image
                                    class="image-item"
                                    :src="together_pic.background_src"
                                    mode="aspectFill"
                                    @click="previewImage([together_pic.background_src])"
                                />
                                <view class="del-image-item" @click.stop="together_pic.background_src = ''">
                                    <uni-icons type="closeempty" color="#e20f04"/>
                                </view>
                            </view>
                            <view v-else class="add-image text-center" @click.stop="changeTogetherBackgroundSrc">
                                <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <text>用户图片距背景图顶部的距离</text>
                            <text class="color-error font16"> *</text>
                            <text class="pl5 color-sub font12">(单位:px)</text>
                        </view>
                        <view class="bottom font16">
                            <input
                                class="input"
                                type="digit"
                                v-model="together_pic.top"
                                :maxlength="5"
                                placeholder="单位:px"
                            />
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <text>用户图片距背景图左边的距离</text>
                            <text class="color-error font16"> *</text>
                            <text class="pl5 color-sub font12">(单位:px)</text>
                        </view>
                        <view class="bottom font16">
                            <input
                                class="input"
                                type="digit"
                                v-model="together_pic.left"
                                :maxlength="5"
                                placeholder="单位:px"
                            />
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <text>用户图片的长度</text>
                            <text class="color-error font16"> *</text>
                            <text class="pl5 color-sub font12">(单位:px)</text>
                        </view>
                        <view class="bottom font16">
                            <input
                                class="input"
                                type="digit"
                                v-model="together_pic.width"
                                :maxlength="5"
                                placeholder="单位:px"
                            />
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <text>用户图片的高度</text>
                            <text class="color-error font16"> *</text>
                            <text class="pl5 color-sub font12">(单位:px)</text>
                        </view>
                        <view class="bottom font16">
                            <input
                                class="input"
                                type="digit"
                                v-model="together_pic.height"
                                :maxlength="5"
                                placeholder="单位:px"
                            />
                        </view>
                    </view>
                </template>
            </template>


            <view v-if="rank_set['polyline_cut']" class="form-item">
                <view class="top color-content">
                    <view>是否区间终点</view>
                    <view class="color-sub font12">如果设置为区间终点，在地图上，这个点位和下个点位之间不连线</view>
                </view>
                <view class="bottom font16">
                    <view class="flex-kai">

                        <picker
                            class="view"
                            mode="selector"
                            :range="['否', '是']"
                            :value="interval_end"
                            @change="interval_end = Number($event.detail.value)"
                        >
                            {{ interval_end === 1 ? '是' : '否' }}
                        </picker>
                        <view class="flex-all-center">
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </view>
            </view>

            <!--只有晓阳这个客户的商户号才能设置在地图上不显示点位，因为在地图上不显示点位会出现很多麻烦问题-->
            <!--已经设置了隐藏点位的点位可以显示这个设置，重新将点位显示出来-->
            <view v-if="who === 46199 || map_hide_point"
                  class="form-item">
                <view class="top color-content">是否在地图上显示该点位</view>
                <view class="bottom font16">
                    <view class="flex-kai">

                        <picker
                            class="view"
                            mode="selector"
                            :range="['显示', '隐藏']"
                            :value="map_hide_point"
                            @change="map_hide_point = Number($event.detail.value)"
                        >
                            {{ map_hide_point === 1 ? '隐藏' : '显示' }}
                        </picker>
                        <view class="flex-all-center">
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view class="save-btn color-white text-center font18 bg-primary" @click="save">保存</view>
        </view>


    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import base64 from '@/utils/base64.js'

export default {
    data() {
        return {
            map_type: 1,
            active_id: '',
            name: '',
            min_num: '',
            max_num: '',
            lat: '',
            lng: '',
            exam_id: '',
            exam_title: '',
            sort_num: '',
            shopid: '',
            news_id: '',
            news_title: '',
            iconPath: '',
            scroll_position: '',
            margin_top: '',
            margin_left: '',
            point_width: '',
            point_height: '',
            medal_id: '',
            medal_logo: '',
            lottery_title: '',
            lottery: {
                lottery_id: '',
                must_exam: 0,
                score_min: '',
                times: 1
            },
            rank_set: {},
            redpack_type: 0,
            redpack_rules: {
                types: 1,
                open: 0,
                amount: {
                    min: 1,
                    max: 2
                }
            },
            redpack_open_opt: [
                {value: 1, title: '开启红包发放'},
                {value: 0, title: '暂停红包发放'}
            ],
            redpack_amount: 100,


            // 照片合成发运动圈
            together_pic: {
                open: 1,
                background_src: '',
                top: '',
                left: '',
                width: '',
                height: ''
            },
            // 照片合成发运动圈

            interval_end: 0,   // 是否区间终点，用来两个点之间不连线的参数，晓阳客户定制

            map_hide_point: 0,   // 是否在地图上隐藏不显示

            who: app.globalData.who,
            userid: app.globalData.userid,

            // 手绘地图点位已答题的图片
            and_drawn_map_with_answer_picture: ''
        }
    },

    computed: {
        // 雷子客户定制开发的功能，解锁点位按照每天的步数来解锁，所有点位不用设置里程
        needMileage() {
            return !this.rank_set['stepRewardIntegralForMinMax']
        }
    },

    onLoad(e) {

        uni.showLoading({
            mask: true
        })
        this.active_id = e.active_id
        if (e.point_id) this.point_id = e.point_id

        login.uniLogin(err => {
            if (err && err.errMsg) {
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getActivityDetail()


        })
    },
    methods: {
        getActivityDetail() {
            xwy_api.getActivityDetail(this.active_id, res => {
                if (res.data?.['active_details']) {
                    const detail = res.data['active_details']
                    this.map_type = detail.conf.active.map_types
                    if (detail.rank_set) this.rank_set = detail.rank_set
                    if (detail.conf.active.redpack_rules?.types) {
                        this.redpack_type = detail.conf.active.redpack_rules.types
                    }

                    if (this.point_id) {
                        this.getPointDetail()
                        return false
                    }
                    uni.hideLoading()
                } else {
                    xwy_api.alert(res.info || '活动详情获取失败。')
                }
            })
        },

        async getPointDetail() {
            uni.setNavigationBarTitle({
                title: '修改点位'
            })

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/map_point_details',
                data: { id: this.point_id }
            });

            uni.hideLoading()

            if (!res || !res.status || !res.data || !res.data['map_point_details']) {
                uni.showModal({
                    title: '提示',
                    content: res && res.info || '点位获取失败',
                    showCancel: false,
                    success: () => uni.navigateBack()
                })

                return false
            }

            const detail = res.data['map_point_details']

            this.name = detail.name
            if (detail.exam_id) this.exam_id = detail.exam_id
            if (detail.exam_details?.exam_name) this.exam_title = detail.exam_details.exam_name
            if (detail.news_id) this.news_id = detail.news_id
            if (detail.news_title) this.news_title = detail.news_title
            if (detail.lat) this.lat = detail.lat
            if (detail.lng) this.lng = detail.lng
            this.min_num = detail.min_num || 0
            this.max_num = detail.max_num || 0
            if (detail.sort_num) this.sort_num = detail.sort_num
            if (detail.map_pic) this.iconPath = detail.map_pic
            if (detail.margin_top || detail.margin_top === 0) this.margin_top = detail.margin_top
            if (detail['medal_details']?.id) {
                this.medal_id = detail['medal_details'].id
                this.medal_logo = detail['medal_details'].logo
            }
            if (detail.conf) {
                const conf = detail.conf
                if (conf.lottery) this.lottery = conf.lottery
                if (detail['lottery_details']?.title) this.lottery_title = detail['lottery_details'].title
                if (conf.point_img_detail) {
                    const p_i_d = conf.point_img_detail
                    if (p_i_d.margin_left !== '') this.margin_left = p_i_d.margin_left
                    if (p_i_d.point_width !== '') this.point_width = p_i_d.point_width
                    if (p_i_d.point_height !== '') this.point_height = p_i_d.point_height
                    if (p_i_d.scroll_position || p_i_d.scroll_position === 0)
                        this.scroll_position = p_i_d.scroll_position
                }
                if (conf.redpack_rules) this.redpack_rules = conf.redpack_rules
                if (conf.together_pic) this.together_pic = conf.together_pic
                if (conf.interval_end) this.interval_end = conf.interval_end
                if (conf.map_hide_point) this.map_hide_point = conf.map_hide_point
                if (conf.and_drawn_map_with_answer_picture) {
                    this.and_drawn_map_with_answer_picture = conf.and_drawn_map_with_answer_picture
                }
            }
            if (detail.redpack_amount) this.redpack_amount = detail.redpack_amount
        },

        lotteryModeChange(e) {
            this.lottery.must_exam = Number(e.detail.value)
        },

        chooseLocation() {
            const obj = {
                success: res => {
                    this.lat = res.latitude
                    this.lng = res.longitude
                },
                fail: err => {
                    console.log(err)
                    if (err.errMsg === 'chooseLocation:fail:not supported') {
                        uni.showModal({
                            title: '提示',
                            content: '不支持电脑端使用，请在手机微信打开',
                            showCancel: false
                        })
                        return false
                    }
                    if (err.errMsg !== 'chooseLocation:fail cancel') {
                        uni.showModal({
                            title: '地图拉起失败',
                            content: JSON.stringify(err),
                            showCancel: false
                        })
                        return false
                    }
                }
            }
            if (this.lat && this.lng) {
                obj.latitude = this.lat * 1
                obj.longitude = this.lng * 1
            }
            uni.chooseLocation(obj)
        },

        toSelNews() {
            uni.navigateTo({
                url: '/pages/news/list?type=user&is_sel=true',
                events: {
                    selNews: data => {
                        this.news_id = data.id
                        this.news_title = data.title
                    }
                }
            })
        },

        deleteNews() {
            this.news_id = ''
            this.news_title = ''
        },

        toSelExam() {
            if (!this.rank_set.exam_open) {
                uni.showModal({
                    title: '提示',
                    content: '未开通答题功能，请联系客服开通。',
                    showCancel: false
                })
                return false
            }

            uni.navigateTo({
                url: '/pages/likou_dati/pages/exam/exam_list/exam_list?in_select=true',
                events: {
                    updateExam: data => {
                        this.exam_id = data.id
                        this.exam_title = data.title
                    }
                }
            })
        },

        deleteExam() {
            this.exam_id = ''
            this.exam_title = ''
        },

        toSelLottery() {
            if (!this.rank_set['lottery_open']) {
                uni.showModal({
                    title: '提示',
                    content: '未开通抽奖功能，请联系客服开通。',
                    showCancel: false
                })
                return false
            }

            uni.navigateTo({
                url: '/pages/lottery/admin/lottery/list?is_sel=1',
                events: {
                    selLottery: data => {
                        this.lottery.lottery_id = data.id
                        this.lottery_title = data.title
                    }
                }
            })
        },

        deleteLottery() {
            this.lottery.lottery_id = ''
            this.lottery_title = ''
        },

        selMedal() {
            uni.navigateTo({
                url: './medal/list?sel=1',
                events: {
                    selMedal: data => {
                        this.medal_id = data.id
                        this.medal_logo = data.logo
                    }
                }
            })
        },

        previewImage(urls) {
            console.log(urls)
            uni.previewImage({
                urls
            })
        },

        changeTogetherBackgroundSrc() {
            this.selectImage('together_pic_background_src')
        },

        selectImage(key) {
            this.$uni.navigateTo(`/pages/other/image_upload_or_select?active_id=${this.active_id}`, {
                events: {
                    newImg: src => {
                        if (key === 'together_pic_background_src') {
                            this.together_pic.background_src = src
                        } else {
                            this[key] = src
                        }
                    }
                }
            })
        },

        save() {
            const errorToast = title => {
                uni.showToast({
                    title,
                    icon: title.length > 7 ? 'none' : 'error'
                })
            }

            if (!this.name) {
                errorToast('请输入点位名称')
                return false
            }

            let min_num = 0, max_num = 0

            if (this.needMileage) {
                min_num = Number(this.min_num || 0)
                max_num = Number(this.max_num || 0)


                if (!min_num && min_num !== 0) {
                    errorToast('请输入最小里程')
                    return false
                }
                if (!max_num && max_num !== 0) {
                    errorToast('请输入最大里程')
                    return false
                }
                if (max_num < min_num) {
                    errorToast('最大里程不可小于最小里程数')
                    return false
                }
                if (min_num > 9999999 || min_num > 9999999) {
                    errorToast('里程数不可大于9999999')
                    return false
                }
            }

            if (this.map_type !== 102 && (!this.lat || !this.lng)) {
                errorToast('请选择点位坐标')
                return false
            }
            if (this.map_type === 102 && !this.iconPath) {
                errorToast('请上传手绘地图图片')
                return false
            }

            const value = {
                name: this.name,
                active_id: this.active_id,
                min_num,
                max_num,
                map_pic: this.iconPath,
                sort_num: this.sort_num ? this.sort_num * 1 : 0,
                news_id: this.news_id || '',
                exam_id: this.exam_id || '',
                lat: this.lat,
                lng: this.lng,
                medal_id: this.medal_id || '',
                margin_top: this.margin_top === '' ? '' : Number(this.margin_top)
            }
            const conf = {
                lottery: this.lottery,
                point_img_detail: {
                    margin_left: this.margin_left === '' ? '' : Number(this.margin_left),
                    point_width: this.point_width === '' ? '' : Number(this.point_width),
                    point_height: this.point_height === '' ? '' : Number(this.point_height),
                    scroll_position: this.scroll_position === '' ? '' : Number(this.scroll_position)
                }
            }
            if (this.rank_set['redpack'] && this.redpack_type === 1) {
                const rules = this.redpack_rules
                if (rules.open) {
                    const min = Number(rules.amount.min)
                    const max = Number(rules.amount.max)
                    if (isNaN(min) || !min || isNaN(max) || !max) {
                        errorToast('请输入正确的红包金额')
                        return false
                    }
                    if (min > max) {
                        errorToast('红包最小金额不得大于最大金额')
                        return false
                    }
                }
                conf.redpack_rules = rules
            }
            value.redpack_amount = this.redpack_amount

            if (this.rank_set.together_pic && this.together_pic.open) {
                conf.together_pic = JSON.parse(JSON.stringify(this.together_pic))
                if (!conf.together_pic.background_src) {
                    errorToast('请上传照片合成背景图')
                    return false
                }
                conf.together_pic.top = Number(conf.together_pic.top)
                if (isNaN(conf.together_pic.top)) {
                    errorToast('照片合成顶部距离设置必须为数字，请重新设置')
                    return false
                }
                conf.together_pic.left = Number(conf.together_pic.left)
                if (isNaN(conf.together_pic.left)) {
                    errorToast('照片合成顶部距离设置必须为数字，请重新设置')
                    return false
                }
                conf.together_pic.width = Number(conf.together_pic.width)
                if (isNaN(conf.together_pic.width) || conf.together_pic.width <= 0) {
                    errorToast('用户图片长度设置必须为大于0的数字，请重新设置')
                    return false
                }
                conf.together_pic.height = Number(conf.together_pic.height)
                if (isNaN(conf.together_pic.height) || conf.together_pic.height <= 0) {
                    errorToast('用户图片高度设置必须为大于0的数字，请重新设置')
                    return false
                }
            }

            if (this.interval_end) conf.interval_end = 1
            if (this.map_hide_point) conf.map_hide_point = 1

            if (this.rank_set['stepRewardIntegralForMinMax'] && this.map_type === 102) {
                conf.and_drawn_map_with_answer_picture = this.and_drawn_map_with_answer_picture
            }

            let conf_str = JSON.stringify(conf)
            conf_str = conf_str.replace(/·/g, '-')
            value.conf = base64['encode'](conf_str)


            if (this.point_id) value.id = this.point_id

            this.saveAjax(value)
        },

        saveAjax(data) {
            uni.showLoading({
                mask: true
            })

            data.access_token = app.globalData['access_token']

            xwy_api.ajax({
                url: 'front.flat.sport_step.admin/add_update_map_point',
                data,
                success: res => {
                    console.log('添加修改点位', res);
                    uni.hideLoading()
                    const type_text = this.point_id ? '修改' : '添加'

                    if (!res.status) {
                        uni.showModal({
                            title: '提示',
                            content: res.info || `${type_text}失败`,
                            showCancel: false
                        })
                        return false;
                    }

                    uni.showToast({
                        icon: 'none',
                        title: `${type_text}成功`
                    })
                    const pages = getCurrentPages()
                    const up_page = pages[pages.length - 2]
                    up_page.$vm.getListInit()
                    setTimeout(() => uni.navigateBack(), 500)
                }
            })
        },


    }
}
</script>

<style scoped>
.page {
    padding-bottom: 100px;
    background-color: #fff;
}

.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}


/*.form-item .textarea {
    width: 100%;
    line-height: 16px;
    padding: 10px 0;
}*/

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}

.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
    width: 100px;
    height: 100px;
    border-radius: 5px;
    line-height: 95px;
    margin: 5px calc((100% - 20px - (100px * 3)) / 6);
}


.image-item {
    width: 100%;
    max-height: 200px;
    border-radius: 5px;
}

.image-view {
    width: 100%;
    position: relative;
    display: inline-block;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}


.bottom-btn-view {
    position: fixed;
    z-index: 99999;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.save-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}
</style>
