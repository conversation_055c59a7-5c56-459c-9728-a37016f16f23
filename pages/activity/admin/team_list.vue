<template>
    <view class="page bg-background">
        <view class="top-view">
            <view class="search bg-white flex-kai p10">
                <view class="input-view">
                    <view class="search-icon left-icon flex-all-center">
                        <uni-icons type="search" size="20" color="#bbbec4"/>
                    </view>
                    <input
                        class="input bg-background"
                        type="text"
                        confirm-type="search"
                        v-model="search_keyword"
                        @confirm="search()"
                        placeholder="输入你要搜索的队伍名称"
                        placeholder-style="color:#bbbec4"
                    />
                    <view class="search-icon right-icon flex-all-center" @click="search_keyword = ''">
                        <uni-icons v-if="search_keyword" type="close" size="20" color="#bbbec4"/>
                    </view>
                </view>
                <view class="color-info pl10" @click="search">搜索</view>
            </view>

            <view v-if="team_category_list.length" class="type-bar bg-white pl10 pr10">

                <scroll-view :scroll-x="true" style="width: calc(100vw - 20px);">
                    <view class="flex-row">
                        <view
                            class="type-item text-center font14 color-content"
                            :class="{'activate-type': item.category_id === category_id}"
                            v-for="item in team_category_list"
                            :key="item.category_id"
                            @click="changeCategory(item.category_id)"
                        >{{ item.name }}
                        </view>
                    </view>
                </scroll-view>
            </view>
        </view>

        <view style="width: 100%;" :style="{ height: team_category_list.length ? '105px' : '60px'}"></view>

        <view v-if="is_sel_team && user_create_team" class="flex-kai">
            <view class="color-sub p10 font14">没有找到队伍，可以点击右侧创建队伍</view>
            <view class="color-primary p10" @click="addOne(true)">
                <text>创建队伍</text>
                <uni-icons type="forward" color="#2d8cf0"/>
            </view>
        </view>

        <template v-if="list.length">
            <view v-if="!is_sel_team" class="flex-kai">
                <view class="flex-row">
                    <view class="color-sub p10 font14">共{{ team_count }}个队伍</view>
                    <view class="color-light-primary p10 font14" @click="notFullSwitch">
                        {{ not_full ? '取消' : '' }}优先显示未满员队伍
                    </view>
                </view>
                <view class="color-primary p10" @click="addTeam()">
                    <text>创建队伍</text>
                    <uni-icons type="forward" color="#2d8cf0"/>
                </view>
            </view>

            <view class="list ptm5">
                <view class="item bg-white flex-row" v-for="(item, index) in list" :key="index">
                    <view v-if="!is_sel_team" class="flex-all-center pr10" @click="checkItem(item)">
                        <radio :checked="item.is_check"/>
                    </view>
                    <view style="width: 100%;">
                        <view class="flex-row" style="width: 100%;">
                            <view v-if="item.conf && item.conf.logo" class="pr10">
                                <image class="logo-image" :src="item.conf.logo" mode="aspectFill"/>
                            </view>
                            <view style="width: 100%;">
                                <view class="color-title">
                                    <text>{{ item.name }}</text>
                                    <text v-if="item.conf && item.conf.password"
                                          class="iconfont icon-lock color-error pl5 font14"
                                          style="position: relative; top: -2px;">
                                    </text>
                                </view>

                                <template v-if="item.id">
                                    <view class="color-sub font14 pt5">
                                        <view v-if="item.limit_person">
                                            <text>限{{ item.limit_person }}人</text>
                                            <text class="pl10">
                                                剩余: {{ item.limit_person - item['attend_person'] }}人
                                            </text>
                                        </view>
                                        <view v-else>队伍人数: {{ item['attend_person'] }}人</view>
                                    </view>
                                    <view class="color-sub font14 pt5" v-if="!is_sel_team">
                                        排序：{{ item.sort_num }}
                                    </view>
                                </template>
                            </view>
                        </view>
                        <view style="height: 10px;"></view>
                        <view class="tools-bar clear clearfix">
                            <view v-if="!is_sel_team" class="fl flex-row" style="line-height: 34px;">
                                <view v-if="!item.is_exit" class="color-sub font14 pr5"
                                      hover-class="navigator-hover" @click="lookTeamMember(item.id)">队员名单
                                </view>

                                <navigator v-if="runOneByOneOpen" class="color-sub font14 pl5"
                                           :url="`/pages/activity/relay-race/sort-setting?active_id=${id}&team_id=${item.id}&is_admin=1`">
                                    接力顺序
                                </navigator>
                                <view v-if="active_types === 5" class="color-sub font14 pr5"
                                      hover-class="navigator-hover" @click="lookTeamClockRecord(item)">
                                    打卡记录
                                </view>
                            </view>
                            <view class="fr flex-row">
                                <template v-if="!is_sel_team">
                                    <view class="edit" @click="addEditTeam(item.id, false)">
                                        <text class="iconfont icon-edit color-sub font20"></text>
                                    </view>
                                    <view v-if="active_types === 2" class="edit" 
                                          @click="updateTeamData(item.id, item.name)">
                                        <text class="iconfont icon-sync color-sub font20"></text>
                                    </view>
                                    <view class="delete" @click="deleteItem(item.id)">
                                        <text class="iconfont icon-delete color-sub font20"></text>
                                    </view>
                                </template>
                                <template v-else>
                                    <button size="mini" class="bg-primary color-white"
                                            @click="selectTeam(item)">选择此队伍
                                    </button>
                                </template>
                            </view>
                        </view>
                    </view>
                </view>
            </view>


            <view v-if="!is_sel_team" class="bottom-bar flex-kai bg-white">
                <view>
                    <view @click="checkAllChange">

                        <radio :checked="is_check_all"/>
                        <text class="color-sub font14">全选</text>
                    </view>
                </view>
                <view class="flex-row">
                    <view class="batch-btn color-content pr10" @click="batchUpdateTeamData">批量更新</view>
                    <view class="batch-btn color-error pl10" @click="batchDelete">批量删除</view>
                </view>
            </view>
        </template>

        <view v-if="!loading && !list.length" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无队伍</view>
            <view v-if="!is_sel_team" class="flex-all-center pt15">
                <button class="add-team-btn color-white bg-primary" @click="addTeam()">创建队伍</button>
            </view>
        </view>

        <uni-load-more v-if="loading" status="loading"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && list.length > 5"
            status="noMore"
            :contentText="{contentnomore: '我是有底线的'}"
        ></uni-load-more>
        <uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>


        <uni-popup ref="popup" type="bottom" :safe-area="false">
            <view class="popup bg-white">
                <view class="flex-row" style="padding-bottom: 10px;">
                    <view class="text-center" style="width: 50%;" @click="addOne(false)">
                        <uni-icons type="plus" size="30" color="#2d8cf0"/>
                        <view class="color-sub font14">单个创建</view>
                    </view>
                    <view class="text-center" style="width: 50%;" @click="addMore()">
                        <uni-icons type="cloud-upload" size="30" color="#2d8cf0"/>
                        <view class="color-sub font14">批量创建</view>
                    </view>
                </view>
                <view class="popup-close text-center color-sub" @click="popupHidden()">取消</view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'


export default {
    data() {
        return {
            is_sel_team: false,  // 是不是进来选择队伍的
            id: '',
            loading: true,
            list: [],
            load_page: 1,
            is_last_page: false,
            team_count: 0,
            search_keyword: '',
            is_check_all: false,
            user_create_team: false,
            team_category_list: [],
            category_id: '',
            runOneByOneOpen: false,
            is_admin: false,
            not_full: 0,
            active_types: 2
        }
    },


    onLoad(e) {
        if (e.is_admin) this.is_admin = true
        if (e.active_types) this.active_types = Number(e.active_types)
        this.$uni.showLoading()
        if (e['selteam']) {
            this.is_sel_team = true
            this.$uni.setNavigationBarTitle('选择队伍')
            if (e.showExitTeam) this.showExitTeam = true
        }
        if (e.is_manage) this.is_manage = true
        this.id = e.id
        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getActivityDetails()
            this.getList()
        })
    },

    onPullDownRefresh() {
        if (!this.loading) this.getListInit()
        uni.stopPullDownRefresh()
    },

    onReachBottom() {
        if (!this.loading && !this.is_last_page) this.getList()
    },

    methods: {
        async getActivityDetails() {
            let details = app.globalData['activity_detail']

            if (!details) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {active_id: this.id}
                })

                details = res.data['active_details']
            }

            if (details?.conf?.active?.runOneByOne?.open) this.runOneByOneOpen = true

            if (details?.conf?.active?.user_create_team) this.user_create_team = true

        },

        async getTeamCategory() {
            const res = await this.xwy_api.getCategoryList({
                active_id: this.id,
                types: 19,
                page: 1,
                perpage: 100
            })
            const list = res?.data?.category_list?.data
            if (list?.length) {
                list.unshift({category_id: '', name: '全部'})
                this.team_category_list = list
            }
        },

        changeCategory(id) {
            if (id === this.category_id || this.loading) return
            this.category_id = id
            this.getListInit()
        },

        notFullSwitch() {
            this.not_full ^= 1
            this.search()
        },


        search() {
            this.getListInit()
        },


        getListInit() {
            this.loading = true
            this.load_page = 1
            this.$uni.showLoading('加载中...')
            this.getList()
        },

        getList() {
            this.loading = true

            if (this.load_page === 1) {
                this.list = []
                if (this.showExitTeam) {
                    this.list.push({
                        name: '不设置任何队伍',
                        id: '',
                        is_exit: true
                    })
                }
                this.is_last_page = false
            }

            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id,
                name: this.search_keyword,
                category_id: this.category_id,
                page: this.load_page,
                perpage: 99
            }
            if (this.not_full) data.not_full = 1

            this.xwy_api.ajax({
                url: 'front.flat.sport_step.active_list/team_group_list',
                data,
                success: res => {
                    uni.hideLoading()
                    this.loading = false

                    if (!res.status) {
                        this.is_last_page = true
                    }

                    const data = res.data['team_group_list']
                    const list = data.data
                    if (list.length) this.is_check_all = false
                    list.forEach(v => v.is_check = false)
                    this.load_page++
                    this.list = [...this.list, ...list]
                    this.is_last_page = data.is_lastpage
                    this.team_count = data.total || 0
                }
            })
        },


        addOne(is_user = false) {
            this.addEditTeam('', is_user)
            this.popupHidden()
        },

        addMore() {
            uni.navigateTo({
                url: './add_teams?id=' + this.id + '&team_count=' + this.team_count,
                events: {
                    uploadList: () => {
                        this.loading = true
                        this.load_page = 1
                        this.getList()
                    }
                }
            })
            this.popupHidden()
        },

        addTeam() {
            this.$refs.popup.open()
        },

        popupHidden() {
            this.$refs.popup.close()
        },

        lookTeamMember(id) {
            this.$uni.navigateTo(`/pages/activity/admin/activity_user_list?id=${this.id}&team_id=${id}`)
        },

        lookTeamClockRecord(team) {
            this.$uni.navigateTo(`/pages/clock_in/user/public_sign_list?id=${this.id}&team_id=${team.id}&team_name=${team.name}&is_admin=1`)
        },

        addEditTeam(id, is_user) {
            let url = './add_edit_team?active_id=' + this.id
            if (id) url += `&team_id=${id}`
            if (is_user) url += '&is_user=1'
            if (!id && this.category_id) {
                const category_name = this.team_category_list.find(v => v.category_id === this.category_id)?.name
                url += `&category_id=${this.category_id}&category_name=${category_name}`
            }

            this.$uni.navigateTo(url, {
                events: {
                    uploadList: () => {
                        this.loading = true
                        this.load_page = 1
                        this.getList()
                    }
                }
            })
        },

        async batchUpdateTeamData() {
            const ids = this.getCheckId()
            const length = ids.length
            if (!ids?.length) return this.$uni.showToast('请选择队伍')
            const confirm = await this.$uni.showModal(`确定更新${ids.length}个队伍的里程数据`, {
                showCancel: true
            })
            if (!confirm.confirm) return
            
            let success_count = 0, fail_count = 0
            for (let i = 0, len = length; i < len; i++) {
                this.$uni.showLoading(`${i + 1}/${length} 更新中...`)
                const res = await this.updateTeamDataAjax(ids[i])
                uni.hideLoading()
                if (res?.status === 1) {
                    success_count++
                } else {
                    fail_count++
                }
            }
            
            if (fail_count === 0) return this.$uni.showToast('更新完成', 'success')
            await this.$uni.showModal(`更新完成，成功${success_count}个，失败${fail_count}个`)
        },

        async updateTeamData(id, name) {
            const confirm = await this.$uni.showModal(`是否要重新计算【${name}】队伍里程数据`, {showCancel: true})
            if (!confirm.confirm) return
            
            this.$uni.showLoading('计算中...')
            const res = await this.updateTeamDataAjax(id)
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '计算失败，请重试')

            this.$uni.showToast('计算成功', 'success')
        },

        updateTeamDataAjax(id) {
            return this.xwy_api.request({
                url: 'front.flat.sport_step.admin/fixed_team_group_data',
                data: {team_id: id}
            })
        },

        deleteItem(id) {
            this.deleteConfirm([id])
        },

        checkItem(item) {
            item.is_check = !item.is_check
            if (item.is_check) return this.inspectCheckAll()
            this.is_check_all = false
        },

        // 循环数据列表，检查是否全选
        inspectCheckAll() {
            this.is_check_all = this.list.every(value => value.is_check)
        },

        checkAllChange() {
            this.is_check_all = !this.is_check_all
            this.list.forEach(v => v.is_check = this.is_check_all)
        },

        getCheckId() {
            return this.list.filter(v => v.is_check).map(v => v.id)
        },

        batchDelete() {
            const ids = this.getCheckId()
            if (!ids?.length) return this.$uni.showToast('请选择需要删除的队伍')

            this.deleteConfirm(ids)
        },

        async deleteConfirm(ids) {
            const res = await this.$uni.showModal(`确定删除${ids.length > 1 ? '选中的' : '该'}队伍?`, {
                showCancel: true,
                confirmText: '删除',
                cancelColor: '#80848f',
                confirmColor: '#ed3f14',
            })
            if (res.confirm) this.deleteAjax(ids)
        },

        deleteAjax(ids) {
            this.$uni.showLoading('删除中...')

            this.xwy_api.ajax({
                url: 'front.flat.sport_step.admin/team_group_del',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id,
                    ids: ids.join(','),
                    is_admin: 1  // 管理员才能删除队伍
                },
                success: res => {
                    if (res?.status !== 1) return this.$uni.showModal(res?.info || '删除失败')

                    this.$uni.showToast('已删除', 'success')

                    const timeout = setTimeout(() => {
                        this.getListInit()
                        clearTimeout(timeout)
                    }, 1000)
                }
            })


        },

        async selectTeam(team) {
            if (this.is_manage) return this.back(team)
            
            if (!team.conf?.password) return await this.verifyPasswordAndPeopleNumber(team)
            
            const res = await this.$uni.showModal('', {
                title: '请输入队伍密码',
                showCancel: true,
                editable: true,
                placeholderText: '请输入队伍密码',
            })
            if (!res.confirm) return
            
            const content = res.content
            if (!content) {
                this.$uni.showToast('请输入密码')
                return this.selectTeam(team)
            }
            await this.verifyPasswordAndPeopleNumber(team, res.content)
        },

        async verifyPasswordAndPeopleNumber(team, password = null) {
            const data = {
                active_id: this.id,
                team_id: team.id
            }
            if (password) data.password = password

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.user/check_team_password',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '队伍密码错误')

            this.back(team)
        },

        back(team) {
            this.getOpenerEventChannel?.()?.emit?.('setTeam', {team_id: team.id, team_name: team.name})
            
            const pages = getCurrentPages()
            const up_page = pages[pages.length - 2]
            const {$vm} = up_page
            $vm.team_id = team.id
            $vm.team_name = team.name
            if (team.conf?.team_slogan) $vm['team_slogan_' + team.id] = team.conf.team_slogan
            uni.navigateBack()
        }
    }
}
</script>

<style scoped>
.page {
    min-height: 100vh;
    padding-bottom: 60px;
    box-sizing: border-box;
}

.top-view {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9;
}

.search, .search .input {
    height: 40px;
    line-height: 40px;
}

.input-view {
    position: relative;
}

.search-icon {
    position: absolute;
    width: 30px;
    height: 40px;
    top: 0;
}

.left-icon {
    left: 0;
}

.right-icon {
    right: 0;
}

.search .input {
    width: calc(100vw - 65px);
    padding: 0 30px;
    border-radius: 20px;
    box-sizing: border-box;
}

.type-bar {
    border: 0;
    height: 40px;
    border-bottom: 5px solid #f8f8f8;
}


.type-item {
    height: 40px;
    line-height: 40px;
    padding: 0 10px;
    box-sizing: border-box;
    display: inline-block;
    white-space: nowrap;
}

.activate-type {
    color: #2d8cf0;
    border-bottom: 2px solid #2d8cf0;
}

.item {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
}

.logo-image {
    width: 50px;
    height: 50px;
    border-radius: 5px;
    display: block;
}

.tools-bar {
    border-top: 1px solid #eee;
    padding-top: 10px;
}

.edit, .delete {
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 50%;
}

.edit {
    border: 1px solid #eee;
    margin-right: 10px;
}

.delete {
    border: 1px solid #eee;
}

.add-team-btn {
    width: 200px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
}

.bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 10px 10px 20px;
    box-sizing: border-box;
    border-top: 1px solid #eee;
}

/* #ifdef H5 */

@media screen and (min-width: 500px) {
    .search {
        width: 500px;
        left: calc(((100vw - 500px) / 2) - 10px);
    }

    .search .input {
        width: calc(500px - 65px);
    }
}

/* #endif */

.popup {
    padding-top: 10px;
    border-radius: 10px 10px 0 0;
}

.popup-close {
    padding-bottom: 20px;
    padding-top: 10px;
    border-top: 1px solid #eee;
}
</style>
