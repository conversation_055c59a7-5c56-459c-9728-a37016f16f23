<template>
    <view class="page">
        

        <map id="map" class="map" :show-location="true" :enable-satellite="enableSatellite"
             :scale="map_scale.scale" :min-scale="map_scale.min" :max-scale="map_scale.max"
             :latitude="map_center.latitude" :longitude="map_center.longitude"/>

        <view class="go-button-fixed color-white" @click="lightUpMap">
            <view>点亮</view>
            <view>地图</view>
        </view>
        
        <view class="map-controls">
            <view class="map-control flex-all-center" @click="enableSatelliteChange">
                <text class="iconfont color-white font24"
                      :class="enableSatellite ? 'icon-map-1' : 'icon-earth'"></text>
            </view>
            <view class="map-control flex-all-center" @click="moveToLocation">
                <uni-icons type="location" size="24" color="#ffffff"/>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
export default {
    data() {
        return {
            map_center: {
                latitude: 30.675715,
                longitude: 104.677734
            },

            enableSatellite: false,
            map_scale: {
                scale: 3,
                min: 3,
                max: 20
            },
        }
    },

    onLoad(params) {
        this.paramsHandle(params)
        this.$login.uniLogin(() => this.init(params))
    },

    methods: {
        paramsHandle(params) {
            if (params.id) this.id = params.id
        },

        async init() {
            this.createMapContext()
            this.setMapSatellite()
            await this.getCityList()

            await this.reloadUserData()
        },

        createMapContext() {
            this.mapContext = uni.createMapContext('map', this)
            this.autoMoveToLocation()
        },

        async autoMoveToLocation() {
            const location = await this.getLocation()
            if (!location) return
            this.map_scale.scale = 8
            this.mapContext.moveToLocation({
                latitude: location.latitude,
                longitude: location.longitude
            })
        },

        setMapSatellite() {
            if (uni.getStorageSync(`enable_satellite_${this.id}`)) this.enableSatellite = true
        },

        enableSatelliteChange() {
            this.enableSatellite = !this.enableSatellite
            
            const key = `enable_satellite_${this.id}`
            this.enableSatellite ? uni.setStorageSync(key, true) : uni.removeStorageSync(key)
        },

        moveToLocation() {
            this.mapContext.moveToLocation()
        },

        async reloadUserData() {
            await this.getActiveLightCity()
            this.setMapParams()
        },

        async getCityList() {
            const res = await this.xwy_api.request({
                url: 'front.user.account.cityArea/city_area_list',
                data: {
                    level: 2
                }
            })
            const list = res?.data?.province_city_area_list || []
            this.city_list = list.map(item => {
                const data = {
                    level: 2,
                    areaCode: Number(item.areaCode),
                    areaName: item.areaName,
                    id: item.id,
                    parentId: item.parentId,
                    light: false
                }

                if (item.center) {
                    const center_arr = item.center.split(',')
                    data.center = {
                        longitude: Number(center_arr[0]),
                        latitude: Number(center_arr[1])
                    }
                }

                return data
            })
        },
        
        
        async getActiveLightCity() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.light_map.user/user_city_list',
                data: {
                    active_id: this.id,
                    page: 1,
                    perpage: 100000,
                    get_all: 1
                }
            })

            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace list.city_area_id */
            const list = res?.data?.list?.data || []
            this.active_light_city_list = list.map(item => ({
                city_area_id: item.city_area_id
            }))
        },


        setMapParams() {
            const markers = []

            const {city_list, active_light_city_list: lightList = []} = this

            const light_list = Array.from(new Set(lightList.map(item => item.city_area_id))).map(id => {
                return { city_area_id: id }
            })

            light_list.forEach(item => {
                const city = city_list.find(v => v.id === item.city_area_id)
                if (city && city.level && city.level === 2 && city.center) {
                    markers.push({
                        id: city.areaCode,
                        latitude: city.center.latitude,
                        longitude: city.center.longitude,
                        title: city.areaName,
                        width: 40,
                        height: 40,
                        iconPath: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/red_flag.png',
                    })
                }
            })

            this.mapContext.addMarkers({
                markers,
                clear: true
            })
        },
        

        async lightUpMap() {
            this.$uni.showLoading('定位中...')
            const location = await this.getLocation()
            if (!location) {
                uni.hideLoading()
                return this.$uni.showModal('定位失败，请查看授权设置是否授权位置信息', {
                    showCancel: true,
                    confirmText: '授权设置',
                    success: res => res.confirm && uni.openSetting()
                })
            }

            uni.hideLoading()
            
            this.$uni.showLoading('点亮中...')
            
            const city = await this.getCity(location)

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.light_map.user/submit_user_city_data',
                data: {
                    active_id: this.id,
                    city_area_id: city.id
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '点亮失败')
            
            this.$uni.showToast(`已点亮 ${city.areaName}`)
            this.moveToLocation()
            await this.reloadUserData()
        },
        
        async getCity({latitude, longitude}) {
            const res = await this.xwy_api.request({
                url: 'front.system.city/get_city_details_lat_lng',
                data: {
                    lat: latitude,
                    lng: longitude
                }
            })
            
            const city_name = res?.data?.['city_details']?.city
            if (city_name) {
                const city = this.city_list.find(item => item.areaName === city_name)
                if (city) return city
            }
            
            return this.searchProximateCity(latitude, longitude)
        },

        searchProximateCity(latitude, longitude) {
            let min_distance = Infinity
            let city = null

            this.city_list.forEach(item => {
                const distance = this._utils.getDistance(latitude, longitude, item.center.latitude, item.center.longitude)
                if (distance < min_distance) {
                    min_distance = distance
                    city = item
                }
            })

            return city
        },
        
        async getLocation() {
            if (this.location) return this.location
            return new Promise(resolve => {
                uni.getLocation({
                    type: 'gcj02',
                    success: res => {
                        const {latitude, longitude} = res
                        const location = {latitude, longitude}
                        this.location = location
                        resolve(location)
                    },
                    fail: () => resolve(false)
                })
            })
        }
    }
}
</script>

<style lang="scss">
.go-button-fixed {
    position: fixed;
    z-index: 99;
    left: 50vw;
    bottom: 80px;
    margin-left: -40px;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: #5cadff;
    box-shadow: 0 0 10px #5cadff;

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.charts-box, .map {
    width: 100vw;
    height: 100vh;
}

.map-controls {
    position: fixed;
    z-index: 99;
    right: 10px;
    bottom: 100px;
    
    .map-control {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, .7);
        margin-bottom: 10px;
    }
}
</style>