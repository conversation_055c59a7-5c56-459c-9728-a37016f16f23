<template>
    <view class="page">
        <view class="user-info text-center">

            <image class="headimg" :src="headimg"/>
            <view class="font18 pt5 color-title">{{ nickname }}</view>
            <view v-if="unlock_count" class="font12 color-sub">已解锁 {{ unlock_count }} 枚勋章</view>
        </view>

        <view v-if="!loading && !medal_list.length" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-medal color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无勋章</view>
        </view>

        <view class="list flex-row flex-wrap">
            <view class="item text-center" v-for="(item, index) in medal_list" :key="index"
                  @click="preview(item)">
                <image class="logo" :class="{'logo-lock': item['is_lock']}"
                       :src="item['medal_details'].logo" mode="aspectFill"/>
                <view v-if="!mileage_hide" class="font12 color-sub">{{ item.min_num }}{{ unit }}</view>
                <view class="color-title">{{ item['medal_details'].name }}</view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()

export default {
    data() {
        return {
            loading: true,
            unit: '里',
            medal_list: [],
            nickname: '',
            headimg: '',
            unlock_count: 0,
            mileage_hide: false
        }
    },

    onLoad(e) {
        this.$uni.showLoading('数据加载中...')
        this.id = e.id
        if (e.unit) this.unit = e.unit
        if (e.mileage_hide) this.mileage_hide = true

        this.$login.uniLogin(err => {
            if (err && err.errMsg) {
                this.loading = false
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }


            this.getMedalList()
        })
    },

    methods: {
        async getMedalList() {
            const data = {active_id: this.id}
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/point_medal_list',
                data
            })
            this.loading = false
            uni.hideLoading()

            const medal_list = res.data['point_exam_list']
            if (medal_list?.length) {
                this.medal_list = medal_list

                this.unlock_count = medal_list.filter(v => v['is_lock'] === 0).length
            }

            const user_detail = res?.data?.['user_attend_details']
            const {nickname, headimg} = app.globalData['userinfo']

            this.nickname = user_detail?.must_submit?.[0]?.value || nickname || ''
            this.headimg = user_detail?.headimg || headimg || ''

        },

        preview(item) {
            if (item['is_lock'] === 1) {
                return this.$uni.showToast(`需要到达 ${item.name} 才能解锁${item['medal_details'].name}`)
            }

            uni.previewImage({
                urls: [item['medal_details'].logo]
            })
        }
    }
}
</script>

<style>
.page {
    min-height: 100vh;
}

.user-info {
    padding: 30px 10px;
}

.headimg {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}

.list {
    padding: 5px;
    border-radius: 10px 10px 0 0;
    position: relative;
    top: -10px;
}

.item {
    position: relative;
    padding: 15px 5px;
    width: calc((100vw - 10px - (10px * 3)) / 3);
}

.logo {
    width: 80px;
    height: 80px;
}

.logo-lock {
    filter: grayscale(1);
}
</style>
