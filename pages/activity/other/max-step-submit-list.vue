<template>
    <view class="page bg-background">
        <view class="checked-options flex-row bg-white">
            <view class="checked-item flex-all-center" :class="{'active-checked': checked === item.value}"
                  v-for="item in checkedOptions" :key="item.value"
                  @click="checkedSwitch(item.value)">
                <view class="checked-item-title color-content font14">{{ item.title }}</view>
            </view>
        </view>

        <view class="list">
            <view class="item bg-white" v-for="item in list" :key="item.id">
                <view class="flex-kai">
                    <view class="color-title font18">{{ item.nickname }}</view>
                    <view v-if="checked === 0 && item.checked_title" :class="['tag', `tag-${item.checked}`]">
                        {{ item.checked_title }}
                    </view>
                </view>
                <view v-if="item.memo" class="color-content pt5">{{ item.memo }}</view>
                <view class="color-content font14 pt5">
                    微信运动步数{{ item.real_step }}步, 可额外增加{{ item.add_step }}步。
                </view>

                <view v-if="item.pic_list.length" class="pic-list flex-row flex-wrap pt5">
                    <image class="pic-item" v-for="(src, index) in item.pic_list" :key="index" :src="src"
                           mode="aspectFill" @click="lookPic(item.pic_list, src)"/>
                </view>

                <view class="font14 color-sub pt5">{{ item.create_time }}</view>

                <view v-if="is_admin" class="action-bar clear clearfix">
                    <view class="fr color-light-primary font14 p10" @click="checkedChange(item)">
                        更改审核状态
                    </view>
                </view>
            </view>
        </view>

        <view v-show="loading" class="text-center w-100"
              :style="{paddingTop: load_page === 1 ? '25vh' : '0'}">
            <load-ani/>
            <view class="color-sub font14">加载中</view>
        </view>

        <view v-if="!loading && !list.length" class="text-center" style="padding-top: 20vh;">
            <text class="iconfont icon-empty-state color-divider" style="font-size: 100px;"></text>
            <view class="color-sub font14">暂无记录</view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            is_admin: false,
            load_page: 1,
            is_last_page: false,
            list: [],
            loading: true,
            checked: 0,
            checkedOptions: [
                {value: 0, title: '全部'},
                {value: 1, title: '待审核'},
                {value: 2, title: '审核通过'},
                {value: 3, title: '审核不通过'}
            ]
        }
    },

    onReachBottom() {
        if (this.is_last_page || this.loading) return
        this.load_page++
        this.getList()
    },

    onLoad(params) {
        this.active_id = params.id
        if (params.is_admin) this.is_admin = true
        if (params.my_self) this.my_self = true

        this.getList()
    },

    methods: {
        checkedSwitch(value) {
            if (value === this.checked) return
            this.checked = value
            this.reloadList()
        },

        reloadList() {
            this.load_page = 1
            this.list = []
            this.is_last_page = false
            this.getList()
        },

        async getList() {
            const data = {
                active_id: this.active_id,
                page: this.load_page,
                perpage: 20
            }

            if (this.my_self) data.myself = 1
            if (this.checked) data.checked = this.checked

            this.loading = true
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exchange.max_step.submitReason/reason_list',
                data
            })
            this.loading = false

            const _data = res?.data?.list
            if (!_data) {
                this.is_last_page = true
                return
            }

            const newList = this.processListData(_data.data || [])

            this.list = [...this.list, ...newList]
            this.is_last_page = _data.is_lastpage
        },

        processListData(data) {
            return data.map(item => ({
                id: item.id,
                userid: item.userid,
                nickname: item.nickname || '',
                memo: item.memo || '',
                checked: item.checked,
                checked_title: this.checkedOptions.find(v => v.value === item.checked)?.title || '',
                real_step: item.real_step,
                add_step: item.add_step,
                create_time: item.create_time,
                pic_list: item.conf_json?.pic_list || []
            }))
        },

        async checkedChange(item) {
            const checkedOptions = this.checkedOptions.filter(v => v.value !== 0 && v.value !== item.checked)
            const itemList = checkedOptions.map(v => v.title)
            const title = '选择需要更改的状态'
            const c_res = await this.$uni.showActionSheet(itemList, {title, alertText: title})
            if (c_res.tapIndex === undefined) return

            this.$uni.showLoading('修改中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exchange.max_step.submitReason/admin_checked_reason',
                data: {
                    id: item.id,
                    checked: checkedOptions[c_res.tapIndex].value
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '修改失败')
            this.$uni.showToast('已修改', 'success')

            this.reloadList()
        },

        lookPic(urls, current) {
            this.$uni.previewImage({urls, current})
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
}

.checked-options {
    .checked-item {
        width: 25%;

        .checked-item-title {
            padding: 0 10px;
            height: 40px;
            line-height: 40px;
            box-sizing: border-box;
        }

        &.active-checked {
            .checked-item-title {
                color: #2d8cf0;
                border-bottom: 2px solid #2d8cf0;
            }
        }
    }
}

.list {
    padding: 1px 0;

    .item {
        margin: 20rpx;
        padding: 20rpx;
        border-radius: 10px;

        .tag {
            padding: 0 5px;
            border-radius: 4px;
            font-size: 12px;
            height: 16px;
            line-height: 16px;

            &.tag-1 {
                color: #80848f;
                border: 1px solid #80848f;
            }

            &.tag-2 {
                color: #19be6b;
                border: 1px solid #19be6b;
            }

            &.tag-3 {
                color: #ed3f14;
                border: 1px solid #ed3f14;
            }
        }

        .pic-list {
            .pic-item {
                display: block;
                margin: 11rpx;
                width: 200rpx;
                height: 200rpx;
            }
        }

        .action-bar {
            margin-top: 10px;
            border-top: 1px solid #eee;
        }
    }
}
</style>