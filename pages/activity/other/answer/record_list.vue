<template>
	<view class="page bg-background">
		<view class="user-info flex-row bg-white">
		    <image class="user-headimg" :src="user_headimg"/>
		    <view class="user-nickname">
		        <view class="color-title">{{ user_nickname }}</view>
		        <view class="color-sub font14">累计答题 {{ total_count }}次</view>
		    </view>
		</view>

		<view class="list">
			<template v-for="(item, index) in list">
				<view class="item bg-white" :key="index">
					<view class="flex-kai">
						<view class="color-title">{{item.exam_info.exam_name}}</view>
						<view class="text-right" style="width: 80px; min-width: 80px;">
							<text class="font18 color-error">
								<template v-if="item.score">{{Number(item.score.toFixed(2))}}</template>
								<template v-else>0</template>
							</text>
							<text class="font12 color-sub">分</text>
						</view>
					</view>
					<view class="flex-kai pt5">
						<view class="font12 color-sub pt5">
							<uni-dateformat :date="item.addtime * 1000" format="yyyy-MM-dd hh:mm:ss" :threshold="[0, 0]"/>
						</view>
						<view class="flex-row">
							<view v-if="item.user_answer_time">
								<!-- <text class="iconfont icon-red-packet color-red font14"></text> -->
								<text class="font12 color-sub">用时</text>
								<text class="color-sub font14" style="padding-left: 2px;"> {{item.user_answer_time}} </text>
								<text class="font12 color-sub">秒</text>
							</view>
							<view class="pl10" v-if="item.reward_step">
								<text class="iconfont icon-footnotes color-warning font14"></text>
								<text class="color-sub font14" style="padding-left: 2px;">{{item.reward_step}}</text>
								<text class="font12 color-sub">步</text>
							</view>
						</view>
					</view>
				</view>
			</template>
		</view>

		<view v-if="!loading && !list.length" class="text-center" style="padding-top: 10vh;">
		    <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
		    <view class="color-sub">暂无答题记录</view>
		</view>

		<uni-load-more v-if="loading && load_page > 1" status="loading"></uni-load-more>
		<uni-load-more v-if="is_last_page && list.length > 10" status="noMore" :contentText="{contentnomore: '我是有底线的'}"></uni-load-more>
		<uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>
	</view>
</template>

<script>
	const app = getApp()
	import login from '../../../../utils/api/login.js'
	import xwy_api from '../../../../utils/api/xwy_api.js'
	import utils from '../../../../utils/utils.js'


	export default {
		data() {
			return {
				list: [],
				loading: true,
				load_page: 1,
				is_last_page: false,
				total_count: 0,
				user_headimg: '',
				user_nickname: ''
			}
		},
		onLoad(e) {
			console.log(e)
		    this.id = e.id

		    uni.showLoading({
		        mask: true
		    })
		    login.uniLogin(err => {
		        if (err && err.errMsg) {
		            uni.hideLoading()
		            uni.showModal({
		                title: err.errTitle || '提示',
		                content: err.errMsg,
		                showCancel: false
		            })
		            return false
		        }

				this.userid = e.userid || app.globalData.userid
				this.user_nickname = e.nickname || app.globalData.userinfo.nickname
				this.user_headimg = e.headimg || app.globalData.userinfo.headimg

		        this.getList()
		    })
		},
		onReachBottom() {
			if (!this.loading && !this.is_last_page) this.getList()
		},
		methods: {
			async getList() {
				this.loading = true
				if (this.load_page === 1) {
					this.list = []
					this.is_last_page = false
				}


				const data = {
					url: 'flat/exam/my_exam_result_list',
					access_token: app.globalData.access_token,
					who: app.globalData.who,
					userid: this.userid,
					active_id: this.id,
					p: this.load_page || 1,
					perpage: 20
				}

				const res = await xwy_api.request({url: 'jump', data});
				uni.hideLoading();
				this.loading = false;

				const exam_data = res?.data?.exam_list;

				if (!exam_data) {
					this.is_last_page = true;
					return false;
				}

				const list = exam_data.data || [];
				this.list = [...this.list, ...list];
				this.is_last_page = exam_data.is_lastpage;
				this.load_page++;
				this.total_count = exam_data.total || 0;

			},
		}
	}
</script>

<style scoped>
	.page {
		min-height: 100vh;
		padding-bottom: 20px;
		box-sizing: border-box;
	}
	.user-info {
	    padding: 20px 10px;
	}

	.user-headimg {
	    width: 60px;
	    height: 60px;
	    border-radius: 50%;
	    display: block;
	}

	.user-nickname {
	    padding-left: 10px;
	    line-height: 30px;
	}
	.list {
		padding-top: 15px;
	}
	.item {
		padding: 15px;
		margin: 0 10px 10px 10px;
		border-radius: 10px;
	}
</style>
