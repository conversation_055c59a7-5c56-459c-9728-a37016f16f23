<template>
    <view class="page">
        <view v-if="current_exam" class="text-center p10 font14">
            <text class="color-sub">当前解锁到：</text>
            <text class="color-light-primary" @click="toExamDetail(current_exam)">
                {{ current_exam.exam_details.exam_name }}
            </text>
        </view>


        <!--雷子客户定制开发，答题列表一行显示5个，并且不显示考卷名称 beb06bf8a320124c5d9d084be54d6fdd-->
        <view v-if="get_types === 1 && exam_show_type === 1" class="list flex-row flex-wrap"
              :class="{'list-show-5': id === 'beb06bf8a320124c5d9d084be54d6fdd'}">
            <view class="item" v-for="item in list" :key="item.id" @click="toExamDetail(item)">
                <view class="logo">
                    <template>
                        <image v-if="item.exam_details.logo" class="logo-img" mode="aspectFill" 
                               :src="item.exam_details.logo"/>
                        <view v-else class="no-logo-img flex-all-center bg-background">
                            <text class="iconfont icon-image color-disabled"></text>
                        </view>
                    </template>
                    <view v-if="item['exam_lock'] === 1" class="lock flex-all-center">
                        <text class="iconfont font34 icon-lock color-white"></text>
                    </view>
                </view>
                <view v-if="id !== 'beb06bf8a320124c5d9d084be54d6fdd'" class="text-center">{{ item.exam_details.exam_name }}</view>
            </view>
        </view>

        <view v-if="get_types === 2 || exam_show_type === 2" class="new-list">
            <view v-for="item in list" class="new-item flex-kai" :key="item.id">
                <view class="color-title pl10">
                    <template v-if="get_types === 1">{{ item.exam_details.exam_name }}</template>
                    <template v-if="get_types === 2">{{ item['lottery_details'].title }}</template>
                    <template v-if="get_types === 4">{{ item.name }}</template>
                </view>
                <view @click="clickItem(item)">
                    <view
                        v-if="item['exam_lock'] || item['lottery_lock'] || item['together_pic_lock'] || item['have_together_pic']"
                        class="button-lock bg-background color-disabled"
                    >
                        <text v-if="!item['have_together_pic']" class="iconfont icon-lock"></text>
                        <text>{{ item['have_together_pic'] ? '已合成' : '待解锁' }}</text>
                    </view>
                    <view v-else class="button-unlock color-white bg-warning">
                        <template v-if="get_types === 1">去答题</template>
                        <template v-if="get_types === 2">去抽奖</template>
                        <template v-if="get_types === 4">去合成</template>
                    </view>
                </view>
            </view>
        </view>

        <view v-if="!loading && !list.length" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">
                <template v-if="get_types === 1">暂无答题</template>
                <template v-if="get_types === 2">暂无抽奖</template>
            </view>
        </view>

    </view>
</template>

<script>
const app = getApp()
import login from '../../../../utils/api/login.js'
import xwy_api from '../../../../utils/api/xwy_api.js'


export default {
    data() {
        return {
            id: '',
            list: [],
            loading: true,
            get_types: 1,
            exam_show_type: 1,
            current_exam: null
        }
    },
    onLoad(e) {
        this.id = e.id
        if (e.get_types) this.get_types = Number(e.get_types)
        if (e.exam_show_type) this.exam_show_type = Number(e.exam_show_type)
        if (e.compare_types) this.compare_types = e.compare_types
        if (e.new) this.new = true // 新的抽奖跳转逻辑，健步走抽奖改成先跳转到抽奖详情，再从抽奖详情到本页面选择其他抽奖活动
        let title = '答题'
        if (this.get_types === 2) title = '抽奖'
        if (this.get_types === 4) title = '照片合成'
        uni.setNavigationBarTitle({
            title
        })
        uni.showLoading({
            mask: true
        })
        login.uniLogin(err => {
            if (err && err.errMsg) {
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getList()
        })
    },
    methods: {
        async getList() {
            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id
            }

            if (this.get_types && this.get_types !== 1) data.get_types = this.get_types
            if (this.compare_types) data.compare_types = this.compare_types

            const res = await xwy_api.request({url: 'front.flat.sport_step.active_list/point_exam_list', data});
            uni.hideLoading();
            this.loading = false;
            if (res?.['data']?.['point_exam_list']?.length) {
                if (this.get_types === 1) {
                    res['data']['point_exam_list'].forEach(v => {
                        if (v.exam_details.logo === '/pages/likou_dati/static/default.jpg') {
                            v.exam_details.logo = ''
                        }
                    })
                }
                this.list = res['data']['point_exam_list']
                this.getCurrentExam()
            }

        },

        // 获取用户当前解锁到哪一个考卷
        getCurrentExam() {
            if (this.get_types !== 1) return
            const list = this.list
            let index = list.findIndex(v => v['exam_lock'])
            if (index === 0) return
            if (index === -1) index = list.length
            index -= 1
            this.current_exam = list[index]
        },


        clickItem(item) {
            const functions = {
                '1': 'toExamDetail',
                '2': 'toLotter',
                '4': 'toTogetherPic'
            }
            this[functions[this.get_types]](item)
        },

        toExamDetail(item) {
            if (item['exam_lock'] && item['exam_lock'] === 1) {
                uni.showToast({
                    title: `需要到达 ${item.name || ''} 才能解锁哦！`,
                    icon: 'none'
                })
                return false
            }

            let url = '/pages/likou_dati/pages/exam/exam_details/exam_details?exam_id=' + item.exam_id + '&active_id=' + this.id + '&point_id=' + item.id


            uni.navigateTo({
                url
            })
        },

        toLotter(item) {
            if (item['lottery_lock'] && item['lottery_lock'] === 1) {
                uni.showToast({
                    title: `需要到达 ${item.name || ''} 才能解锁哦！`,
                    icon: 'none'
                })
                return false
            }

            if (this.new) {
                const eventChannel = this.getOpenerEventChannel()
                eventChannel.emit('changeLotteryDetails', {
                    id: item['lottery_details'].active_id,
                    point_id: item.id
                })
                uni.navigateBack()
                return
            }

            uni.navigateTo({
                url: '/pages/lottery/user/lottery?id=' + item['lottery_details'].active_id + '&active_id=' + this.id + '&point_id=' + item.id
            })
        },

        toTogetherPic(item) {
            if (item['together_pic_lock'] && item['together_pic_lock'] === 1) {
                return uni.showToast({
                    title: `需要到达 ${item.name || ''} 才能解锁哦！`,
                    icon: 'none'
                })
            }
            if (item['have_together_pic']) {
                return uni.showToast({
                    title: '已合成，无需重复合成',
                    icon: 'none'
                })
            }

            uni.navigateTo({
                url: `/pages/activity/together-pic/make-pictures?id=${this.id}&point_id=${item.id}`,
                events: {
                    refresh: () => {
                        this.getList()
                    }
                }
            })
        }

    }
}
</script>

<style scoped lang="scss">
.list {
    padding: 5px;

    .item {
        width: 50%;
        padding: 10px 5px;
        box-sizing: border-box;

        .logo {
            position: relative;

            .logo-img, .no-logo-img {
                width: 100%;
                height: 100px;
                border-radius: 5px;
            }

            .logo-img {
                display: block;
            }

            .no-logo-img {
                .iconfont {
                    font-size: 80px;
                }
            }
        }

        .lock {
            width: 44px;
            height: 44px;
            border-radius: 5px;
            position: absolute;
            top: 50%;
            left: 50%;
            margin-top: -22px;
            margin-left: -22px;
            background-color: rgba(0, 0, 0, .5);
        }
    }
}

.list-show-5 {
    padding: 10rpx;

    .item {
        padding: 20rpx 10rpx;
        width: 20%;

        .logo-img, .no-logo-img {
            width: 120rpx !important;
            height: 120rpx !important;
        }

        .no-logo-img {
            .iconfont {
                font-size: 80rpx !important;
            }
        }

        .lock {
            width: 60rpx;
            height: 60rpx;
            margin-top: -30rpx;
            margin-left: -30rpx;
        }
    }
}

.new-list {
    padding-top: 1px;
    padding-bottom: 20px;
}
.new-item {
    margin: 10px;
    padding: 10px;
    height: 40px;
    line-height: 40px;
    border-radius: 30px;
    background-color: #fff;
    box-shadow: 0 0 5px 2px #eee;
}
.button-lock, .button-unlock {
    width: 100px;
    min-width: 100px;
    text-align: center;
    height: 40px;
    line-height: 40px;
    box-sizing: border-box;
    border-radius: 20px;
}
.button-lock {
    line-height: 38px;
    border: 1px solid #bbbec4;
}
.button-lock .iconfont {
    padding-right: 3px;
}
</style>
