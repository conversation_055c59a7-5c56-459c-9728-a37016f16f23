<template>
    <view class="page">

        <view v-if="img_src && have_light" class="p10 font14 text-center" style="color: #e19898;">
            恭喜你已获得{{ prize_name }}。
        </view>


        <view class="big-img-view">

            <image class="big-img" :class="{'filter': !have_light}" :src="img_src" mode="widthFix"
                   @click="previewImage(img_src)"/>
            <view v-if="!have_light" class="no-end flex-all-center color-white">
                <view class="text-center">
                    <uni-icons type="locked" size="100" color="#ffffff"/>
                    <view>未获得</view>
                </view>
            </view>
        </view>

        <view class="font18 text-center p10 color-title">{{ prize_name }}</view>

        <view v-if="img_src && have_light" class="text-center font14 color-sub">
            点击可预览、转发、保存
        </view>

        <view class="flex-all-center">
            <view class="bottom-button bg-light-primary color-white text-center"
                  hover-class="navigator-hover" @click="toExam">
                去答题
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            img_src: '',
            have_light: 0,
            min_score: 0,
            prize_name: '',
            exam_id: ''
        }
    },

    onLoad(e) {
        this.active_id = e.active_id

        this.$uni.showLoading('数据加载中...')

        this.$login.uniLogin(err => {
            const {errMsg, errTitle = '提示'} = err || {}
            if (errMsg) return this.$uni.showModal(errMsg, {title: errTitle})

            this.init()
        })
    },
    methods: {
        async init() {
            await this.getData()
        },


        async getData() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exam.sportExam/exam_light_prize_list',
                data: {
                    active_id: this.active_id
                }
            })
            uni.hideLoading()

            const item = res?.data?.['light_prize_list']?.[0]
            if (!item?.pic) return this.$uni.showModal('活动未配置答题勋章', {success: () => uni.navigateBack()})


            this.img_src = item.pic
            this.have_light = item.have_light
            this.min_score = item.min_score
            this.prize_name = item.prize_name
            this.exam_id = item.exam_id
        },

        toExam() {
            const {active_id, exam_id, have_light, min_score, img_src, prize_name} = this
            this.$uni.navigateTo(`/pages/likou_dati/pages/exam/exam_details/exam_details?exam_prize=1&exam_id=${exam_id}&active_id=${active_id}&have_light=${have_light}&min_score=${min_score}&img_src=${img_src}&prize_name=${prize_name}`, {
                events: {
                    lightUp: () => this.getData()
                }
            })
        },

        previewImage(url) {
            this.$uni.previewImage(url)
        }
    }
}
</script>

<style>
.page {
    padding-bottom: 30px;
}

.big-img-view {
    position: relative;
    margin: 10px;
}

.big-img {
    display: block;
    width: 100%;
    height: auto;
}


.filter {
    filter: blur(1px);
}

.no-end {
    position: absolute;
    background-color: rgba(0, 0, 0, .5);
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
}

.bottom-button {
    margin-top: 30px;
    width: 250px;
    line-height: 44px;
    border-radius: 22px;
}
</style>
