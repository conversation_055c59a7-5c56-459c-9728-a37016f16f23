<template>
    <view
        class="page bg-background"
        :style="{ paddingBottom: look_mode === 'travel' && my_self ? '150px' : '20px' }"
    >
        <view class="user-info flex-row bg-white">
            <image class="user-headimg" :src="headimg"/>
            <view class="user-nickname">
                <view class="color-title pb5">{{ nickname }}</view>
                <view class="color-content font14" v-if="team_name">队伍: {{ team_name }}</view>
                <view v-if="look_mode === 'exchange'" class="color-sub font14">
                    累计
                    <text @click="changeGodModel">兑换</text>
                    {{ total_count }}天
                </view>
                <view class="flex-kai" v-if="look_mode === 'travel'">
                    <view class="color-sub font14 pt5">共{{ travel_total }}条出差记录</view>
                    <view v-if="travel_total && is_admin" class="p5" @click="exportTravelExchangeRecord">
                        <text class="color-primary font14">导出记录</text>
                        <uni-icons type="forward" size="14" color="#2d8cf0"/>
                    </view>
                </view>
                <navigator
                    v-if="feedback_open"
                    class="feedback"
                    :url="'/pages/activity/feedback/send?activity_id=' + id + '&nickname=' + nickname"
                >
                    <text class="iconfont icon-info color-warning font24"></text>
                </navigator>
            </view>
        </view>

        <view v-if="user_travel" class="look-mode-list flex-row">
            <view
                class="look-mode-item text-center bg-white font14 color-sub"
                v-for="item in look_mode_list"
                :key="item.value"
                :class="{ 'look-mode-active': look_mode === item.value }"
                @click="changeLookMode(item.value)"
            > {{ item.title }}</view>
        </view>


        <view v-if="look_mode === 'exchange' && !user_travel " class="p10 m10 radius10 bg-white">
            <view class="color-content pb10">最近{{ types === 37 ? '' : '兑换' }}步数</view>
            <step-bar-chart :step-list="day7step"/>
            <view v-if="day7statistics" class="pt10 color-sub font14 text-center">{{ day7statistics }}</view>
        </view>

        <view v-if="look_mode === 'exchange'" class="list">

            <xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>
            <template v-for="(item, index) in list">
                <view class="item flex-kai bg-white" :key="index">
                    <view class="color-sub">
                        <text>{{ index + 1 }}、</text>
                        <uni-dateformat :date="item.exchange_date * 1000" format="yyyy-MM-dd" :threshold="[0, 0]"/>
                        <view v-if="god_model">
                            <uni-dateformat
                                :date="item['update_time']"
                                format="yyyy-MM-dd hh:mm:ss"
                                :threshold="[0, 0]"
                            />
                        </view>
                    </view>
                    <view>
                        <text :class="['font18', getStepClass(item)]">
                            {{ types === 37 ? item['real_step'] : item.step }}
                        </text>
                        <text class="color-sub font12">步</text>
                    </view>
                </view>

                <xwy-ad v-if="(index + 1) % 10 === 0" :activity_id="id" :ad_type="66"></xwy-ad>
            </template>
        </view>

        <view v-if="look_mode === 'travel'" class="list">
            <view class="travel-item" v-for="item in list" :key="item.id" @click="clickItem(item)">
                <view class="flex-kai">
                    <view class="left-city">
                        <view class="ellipsis color-title">{{ item.province }}{{ item.city }}</view>
                        <view
                            v-if="userid === item.userid && item.address_name"
                            class="ellipsis color-content font14 pt5"
                        >{{ item.address_name}}
                        </view>
                    </view>
                    <view class="text-center" style="height: 30px; overflow: hidden">
                        <view v-if="item.exchange_types" class="exchange-types font14">
                            <text v-if="item.exchange_types === 1" class="color-error">省内</text>
                            <text v-if="item.exchange_types === 2" class="color-primary">省外</text>
                        </view>
                        <text class="journey-icon iconfont icon-go-up-to"></text>
                    </view>
                    <view class="right-city">
                        <view>
                            <view v-if="item.status === '已完成'">
                                <view v-if="item.conf_json" class="ellipsis color-title">
                                    {{ item.conf_json.province || ''}}{{ item.conf_json.city || '' }}
                                </view>
                                <view v-else class="font14 color-content">未记录到终点城市</view>
                            </view>
                            <view v-else class="font14 color-content">行程未完成</view>
                        </view>
                        <view
                            v-if="userid === item.userid && item.conf_json && item.conf_json.address_name"
                            class="ellipsis font14 color-content pt5"
                        >
                            <template v-if="item.status === '已完成'" >
                                {{ item.conf_json.address_name}}
                            </template>
                        </view>
                    </view>
                </view>
                <view class="time flex-kai color-sub font12 pt10">
                    <view>
                        <template v-if="item.create_time">{{ item.create_time }}</template>
                    </view>
                    <view class="text-right">
                        <template v-if="item['update_time']">{{ item['update_time'] }}</template>
                    </view>
                </view>

                <view class="pt10 clear clearfix">
                    <view v-if="item.distance" class="color-content font14 fl">
                        出差公里: {{ Number((item.distance / 1000).toFixed(2)) }}公里
                    </view>
                    <view
                        v-if="item.exchange_kilo"
                        class="color-content font14 fr"
                        @click="editExchangeKilo(item)"
                    >兑换里程: {{ item.exchange_kilo }}{{ unit }}
                    </view>

                    <view
                        class="to-complete-button bg-light-primary color-white font14 text-center fr"
                        v-if="my_self && item.status === '未完成'"
                    >去完成</view>
                </view>
            </view>
        </view>


        <view v-if="!list.length && !loading && is_last_page" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view v-if="look_mode === 'exchange'" class="color-sub">暂无兑换记录</view>
            <template v-if="look_mode === 'travel'">
                <view class="color-sub pb10 font14">暂无出差记录</view>
                <view v-if="my_self" class="flex-all-center pt15">
                    <view
                        class="add-record-button color-white bg-primary"
                        hover-class="navigator-hover"
                        @click="addRecord"
                    >添加记录</view>
                </view>
            </template>
        </view>

        <uni-load-more v-if="loading" status="loading"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && list.length > 10"
            status="noMore"
            :contentText="{contentnomore: '我是有底线的'}"
        ></uni-load-more>
        <uni-load-more v-if="!is_last_page && !loading && list.length" status="more"></uni-load-more>


        <view
            class="travel-exchange-button-view flex-all-center"
            v-if="look_mode === 'travel' && my_self && list.length"
        >
            <view class="travel-exchange-button" @click="addRecord">
                {{ uncompleted_record ? '完成' : '开始' }}
            </view>
        </view>

        <template v-if="look_mode === 'travel' && travel_total && is_admin">
            <uni-popup ref="export_travel_popup">
                <view class="bg-white p10" style="width: 80vw; border-radius: 10px;">
                    <view class="text-center">导出出差记录</view>
                    <view class="pt10 color-sub font14 text-center">
                        共{{travel_total}}条记录，需要分{{Math.ceil(travel_total / export_travel_once_perpage)}}次导出，每次导出{{export_travel_once_perpage}}条
                    </view>
                    <view class="p10">
                        <scroll-view scroll-y style="height: 50vh;">
                            <view
                                v-for="(item, index) in Math.ceil(travel_total / export_travel_once_perpage)"
                                :key="item"
                                class="flex-kai pt10 pb10"
                                :style="{borderTop: index === 0 ? '' : '1px dashed #eee'}"
                            >
                                <view>
                                    {{index + 1}}、{{index * export_travel_once_perpage + 1}}-{{((index + 1) * export_travel_once_perpage) > travel_total ? travel_total : (index + 1) * export_travel_once_perpage}}
                                </view>
                                <view class="color-primary" hover-class="navigator-hover"
                                      @click="exportTravelExchangeRecordAjax(index + 1)">下载</view>
                            </view>
                        </scroll-view>
                    </view>
                </view>
                <view class="text-center pt5" @click="$refs.export_travel_popup.close()">
                    <text class="iconfont icon-wrong color-white font24"></text>
                </view>
            </uni-popup>

            <uni-popup ref="export_popup" type="center">
                <view class="export-popup text-center bg-white">
                    <view class="popup-close" @click="$refs.export_popup.close()">
                        <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                    </view>
                    <view class="top bg-primary color-white" style="padding: 50px 20px 20px;">
                        <icon type="success" size="80" color="#ffffff"></icon>
                        <view class="font18">导出成功</view>
                    </view>
                    <view class="bg-white color-info" style="padding: 20px;">
                        <view class="color-content text-center font14" style="padding-bottom: 20px;">
                            {{ export_popup_tips }}
                        </view>
                        <view
                            class="export-popup-btn bg-info color-white"
                            hover-class="navigator-hover"
                            @click="copyDownloadSrc(false)"
                        >复制下载地址</view>
                    </view>
                </view>
            </uni-popup>
        </template>
    </view>
</template>

<script>
import my_storage from "@/utils/storage"

const app = getApp()
import login from '../../../utils/api/login.js'

export default {
    data() {
        return {
            id: '',
            nickname: '',
            headimg: '',
            loading: true,
            list: [],
            total_count: 0,
            load_page: 1,
            is_last_page: false,
            god_model: false,
            feedback_open: false,

            // 出差里程兑换
            open_business_kilometers: false,
            user_travel: false,
            look_mode_list: [
                { title: '步数兑换记录', value: 'exchange' },
                { title: '出差里程兑换记录', value: 'travel' },
                { title: '跑步运动记录', value: 'run' }
            ],
            look_mode: 'exchange',
            my_self: false,
            unit: '里',
            travel_total: 0,
            uncompleted_record: null,
            // 出差里程兑换

            exchangeRecordsShowTeamName: false,
            team_name: '',
            userid: '',
            is_admin: false,
            export_popup_tips: '',
            export_travel_once_perpage: 1000,
            day7step: [],
            types: ''
        }
    },
    
    computed: {
        day7statistics() {
            const {day7step} = this
            const days = day7step.length
            if (!days) return ''
            const step_count = day7step.reduce((a, b) => a + b.step, 0)
            const step_avg = Math.floor(step_count / days)
            return `最近${days}天累计${step_count}步 平均每天${step_avg}步`
        }  
    },
    
    onLoad(e) {
        if (e.nickname) this.nickname = decodeURIComponent(e.nickname)
        if (e.headimg) this.headimg = decodeURIComponent(e.headimg)
        this.id = e.activity_id
        if (e.userid) {
            this.look_userid = e.userid
        } else {
            this.my_self = true
            this.look_mode = 'travel'
        }
        if (e.look_mode) this.look_mode = e.look_mode
        if (e.types) this.types = Number(e.types)


        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                this.loading = false
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.init()
        })
    },
    
    watch: {
        nickname: function (val) {
            val && this.$uni.setNavigationBarTitle(val)
        },
    },
    
    onReachBottom() {
        this.loadList()
    },
    methods: {
        async init() {
            this.userid = app.globalData['userid']
            this.open_business_kilometers = this.xwy_config.openBusinessKilometers(this.id)
            this.exchangeRecordsShowTeamName = this.xwy_config.exchangeRecordsShowTeamName(this.id)
            await this.getActivityData()
            await this.checkQualification()
            await this.getList()
        },

        async getActivityData() {
            let activity_detail = app.globalData['activity_detail']

            if (!activity_detail) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: { active_id: this.id }
                })

                activity_detail = res?.['data']?.['active_details'] || {}

            }

            if (!activity_detail?.conf?.active?.close_feedback) this.feedback_open = true
            if (activity_detail?.conf?.active?.kilo_unit && activity_detail.conf.active.kilo_unit !== this.unit) {
                this.unit = activity_detail.conf.active.kilo_unit
            }

            if (activity_detail?.userid && activity_detail.userid === app.globalData['userid']) {
                this.is_admin = true
            }
        },

        async checkQualification() {
            if (!this.open_business_kilometers) return
            this.user_travel = true  // 所有参与活动的用户都有出差里程兑换权限
            /*const data = { active_id: this.id }
            if (this.look_userid) data.userid = this.look_userid
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exchange.business_exchange_kilo.userSubmit/check_is_business_trip_user',
                data
            })
            this.user_travel = res?.status === 1
            if (this.my_self && !this.user_travel && this.look_mode === 'travel') {
                this.look_mode = 'exchange'
                this.xwy_api.alert(res?.info || '没有出差里程兑换权限')
            }*/
        },

        changeGodModel() {
            this.god_model_timeout && clearTimeout(this.god_model_timeout)
            this.god_model_click_count = this.god_model_click_count || 0
            this.god_model_click_count++
            this.god_model_timeout = setTimeout(() => {
                this.god_model_click_count = 0
                console.log('定时器结束')
                clearTimeout(this.god_model_timeout)
            }, 2000)
            if (this.god_model_click_count >= 3) {
                this.god_model = !this.god_model
                this.god_model_click_count = 0
                clearTimeout(this.god_model_timeout)
            }
        },


        changeLookMode(value) {
            if (value === 'run') {
                return this.$uni.navigateTo(`/pages/running/user/run_list?id=${this.id}&userid=${this.look_userid || this.userid}&nickname=${this.nickname}`)
            }
            
            if (this.look_mode === value || this.loading) return
            this.look_mode = value
            this.load_page = 1
            this.getList()
        },

        listDataInit() {
            this.travel_total = 0
            this.total_count = 0
            this.list = []
            this.is_last_page = false
        },

        async loadList() {
            if (this.is_last_page || this.loading) return
            await this.getList()
        },

        async reloadList() {
            this.load_page = 1
            await this.getList()
        },

        async getList() {
            this.loading = true
            if (this.load_page === 1) this.listDataInit()
            if (this.look_mode === 'exchange') {
                await this.getExchangeList()
            } else {
                await this.getTravelList()
            }
            this.loading = false
        },

        getStepClass(item) {
            const step = this.types === 37 ? item['real_step'] : item.step
            return step < 10000 ? 'color-success' : 'color-warning'
        },

        async getExchangeList() {
            const data = {
                active_id: this.id,
                page: this.load_page,
                perpage: 20
            }
            if (this.open_business_kilometers) data.get_team_details = 1
            if (this.look_userid) data.userid = this.look_userid
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exchange/exchange_step_list',
                data
            })

            if (res?.data?.['team_details']?.name) this.team_name = res.data['team_details'].name
            if (res?.data?.['exchange_list']) {
                const data = res.data['exchange_list']
                const list = data.data || []
                list.forEach(item => {
                    item.exchange_date = this._utils.beijingTimestamp2Local(item.exchange_date)
                })
                if (this.load_page === 1) this.getDay7Step(list)
                this.list = [...this.list, ...list]
                this.total_count = data.total || 0
                this.is_last_page = data.is_lastpage
                this.load_page++
            } else {
                this.is_last_page = true
            }
        },

        getDay7Step(list) {
            this.day7step = []
            let stepList = list.map(item => {
                return {
                    timestamp: item.exchange_date,
                    // 37: 用户实际运动步数，从用户实际运动步数排行榜进来的，要显示记录的实际步数
                    step: this.types === 37 ? item['real_step'] : item.step
                }
            })
            if (stepList.length > 7) stepList = stepList.slice(0, 7)
            if (stepList.length)stepList = stepList.reverse()
            this.day7step = stepList || []
        },

        async getTravelList() {
            const request_data = {
                active_id: this.id,
                page: this.load_page,
                perpage: 20
            }
            if (this.look_userid) request_data.userid = this.look_userid
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exchange.business_exchange_kilo.userSubmit/user_submit_list',
                data: request_data
            })

            this.load_page++
            const data = res?.data?.list
            if (!data) {
                this.is_last_page = true
                return
            }
            const list = this.listInit(data.data || [])
            this.list = this.list.concat(list)
            this.uncompleted_record = this.list.find(v => v.status === '未完成') || null
            this.travel_total = data.total
            this.is_last_page = data.is_lastpage
        },


        listInit(list) {
            const new_list = []
            list.forEach(v => {
                const startExtractRegionInfo = this._utils.extractRegionInfo(v.address)
                const item = {
                    status: v.conf_json?.lat && v.conf_json?.lng ? '已完成' : '未完成',
                    province: startExtractRegionInfo?.province || '',
                    city: startExtractRegionInfo?.city || '',
                    address_name: v.address.split('----------')[1] || '',
                    ...v
                }
                let exchange_types = v.exchange_types || 0
                if (v.conf_json?.address) {
                    const extractRegionInfo = this._utils.extractRegionInfo(v.conf_json.address)
                    item.conf_json.province = extractRegionInfo?.province || ''
                    item.conf_json.city = extractRegionInfo?.city || ''
                    item.conf_json.address_name = v.conf_json.address.split('----------')[1] || ''
                    if (exchange_types === 0) {
                        exchange_types = item.province === item.conf_json.province ? 1 : 2
                    }
                }
                item.exchange_types = exchange_types
                new_list.push(item)
            })
            return new_list
        },

        addRecord() {
            if (this.uncompleted_record) {
                this.completedRecord(this.uncompleted_record)
                // uni.showModal({
                //     title: '提示',
                //     content: '您有未完成的记录，无法添加新记录。是否结束未完成的记录？',
                //     confirmText: '去完成',
                //     success: (res) => {
                //         res.confirm && this.completedRecord(this.uncompleted_record)
                //     }
                // })
                // return
            }
            this.toExchangePage()
        },

        completedRecord(item) {
            uni.setStorageSync('kilometer_exchange_uncompleted_record', item)
            this.toExchangePage(item.id)
        },

        clickItem(item) {
            if (item.status === '未完成' && this.my_self) this.completedRecord(item)
        },

        toExchangePage(id = null) {
            let url = `/pages/activity/business-kilometers/kilometer-exchange?id=${this.id}`
            if (id) url += `&record_id=${id}`
            uni.navigateTo({
                url,
                events: {
                    reloadList: () => this.reloadList()
                }
            })
        },

        editExchangeKilo(item) {
            if (!this.is_admin) return
            uni.showModal({
                title: '修改里程',
                content: item.exchange_kilo.toString(),
                editable: true,
                placeholderText: '请输入修改的里程数',
                success: res => {
                    if (!res.confirm) return
                    const new_kilo = Number(res.content)
                    if (isNaN(new_kilo)) {
                        this.editExchangeKilo(item, res.content)
                        return uni.showToast({ title: '只能输入数字', icon: 'none' })
                    }
                    this.editKilo(item, new_kilo)
                }
            })
        },

        async editKilo(item, exchange_kilo) {
            uni.showLoading({ title: '修改中', mask: true })
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin.activeSet/business_record_change',
                data: {
                    active_id: this.id,
                    id: item.id,
                    exchange_kilo
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.xwy_api.alert(res?.info || '修改失败')
            item.exchange_kilo = exchange_kilo
            uni.showToast({
                title: res.info || '修改成功',
                icon: !res.info || res.info < 8 ? 'success' : 'none'
            })
        },

        exportTravelExchangeRecord() {
            if (this.travel_total <= this.export_travel_once_perpage) {
                return this.exportTravelExchangeRecordAjax()
            }
            this.$refs.export_travel_popup.open()
        },

        async exportTravelExchangeRecordAjax(page = null) {
            this.$uni.showLoading('导出中...')
            const once_perpage = this.export_travel_once_perpage
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exchange.business_exchange_kilo.userSubmit/user_submit_list',
                data: {
                    active_id: this.id,
                    userid: this.look_userid || this.userid,
                    need_excel: 1,
                    page: page || 1,
                    perpage: once_perpage
                }
            })
            uni.hideLoading()
            if (res?.status !== 1) return this.xwy_api.alert(res?.info || '导出失败')
            const download_src = res?.data?.url
            if (!download_src) return this.xwy_api.alert('导出失败')

            let title = `导出【${this.nickname}】出差兑换记录`;
            if (page) title = `导出【${this.nickname}】第${page * once_perpage + 1}-${((page + 1) * once_perpage) > this.travel_total ? this.travel_total : ((page + 1) * once_perpage)}条出差兑换记录`
            my_storage.setExportExcelRecord({url: res['data'].url, title})

            this.export_popup_tips = res.info || '导出成功'
            this.export_download_src = download_src
            this.$refs.export_popup.open()
            this.copyDownloadSrc(true)
        },

        copyDownloadSrc(hide = false) {
            uni.setClipboardData({
                data: this.export_download_src,
                success: () => hide ? uni.hideToast() : this.$uni.showToast('复制成功', 'success', 1000)
            })
        },
    }
}
</script>

<style scoped lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
}

.user-info {
    position: relative;
    padding: 20px 10px;
}

.feedback {
    position: absolute;
    top: 10px;
    right: 10px;
}

.user-headimg {
    width: 60px;
    min-width: 60px;
    height: 60px;
    border-radius: 50%;
    display: block;
}

.user-nickname {
    padding-left: 10px;
    width: 100%;
}

.look-mode-list {
    margin: 10px;
    border-radius: 5px;
    overflow: hidden;
    .look-mode-item {
        width: 50%;
        line-height: 34px;
    }
    .look-mode-active {
        background-color: #5cadff !important;
        color: #fff !important;
    }
}

.list {
    padding-top: 10px;
}

.item {
    margin: 0 10px 15px;
    padding: 20px;
    border-radius: 10px;
}

.travel-item {
    margin: 0 10px 20px;
    padding: 10px;
    border-radius: 10px;
    background-color: #fff;
    box-shadow: 0 0 10px #eee;
    //.exchange-types {
    //    position: relative;
    //    top: 8px;
    //}
    .journey-icon {
        width: 40px;
        text-align: center;
        font-size: 34px;
        color: #d3d1d1;
        position: relative;
        top: -18px;
    }
    .left-city, .right-city {
        width: calc(50% - 20px);
    }
    .right-city {
        text-align: right;
    }
    .time {
        position: relative;
        top: -5px;
    }
    .to-complete-button {
        width: 80px;
        height: 36px;
        line-height: 36px;
        border-radius: 18px;
    }
}
.add-record-button {
    width: 200px;
    line-height: 44px;
    border-radius: 22px;
}
.add-record-mini-button {
    width: 120px;
    height: 30px;
    line-height: 30px;
    border-radius: 16px;
    border: 1px solid #5cadff;
}

.travel-exchange-button-view {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 20px 0;
    background-color: #f8f8f8;
}
.travel-exchange-button {
    width: 100px;
    height: 100px;
    line-height: 100px;
    border-radius: 50%;
    text-align: center;
    background-color: #5cadff;
    color: #fff;
    font-size: 24px;
}

.export-popup {
    width: 280px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}

.export-popup-btn {
    line-height: 40px;
    border-radius: 20px;
}

.export-popup-btn::after {
    border: none;
}
</style>
