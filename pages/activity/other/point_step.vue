<template>
    <view>
        <view v-if="map_list.length" class="font14 color-sub text-center pt15">
            本活动共{{ map_list.length }}个点位
        </view>


        <view class="list">
            <template v-for="(item, index) in map_list">
                <xwy-ad v-if="index === 0" :activity_id="id" :ad_type="66"></xwy-ad>
                <view
                    v-if="item.name"
                    class="item"
                    :key="index"
                    @click="toNewsDetail(item['page_id'], item.point_id)"
                >
                    <view class="icon flex-all-center bg-white" @click.stop="lookMapPic(item)">
                        <text class="iconfont icon-map color-sub font20"></text>
                    </view>
                    <view>
                        <text class="color-sub pr5 font14">{{ index + 1 }}.</text>
                        <text class="color-title">{{ item.name }}</text>
                        <text v-if="item.exam_id"
                              class="iconfont color-sub icon-examination-paper pl5 font14"></text>
                    </view>
                    <view class="color-sub font14 ptm5">
                        {{ item.min_num }}{{ unit }} - {{ item.max_num }}{{ unit }}(含)
                    </view>
                    <view v-if="item['finished_person_count']" class="color-sub font14">
                        <text class="iconfont icon-walk color-sub font14"></text>
                        <text class="pl5">{{ item['finished_person_count'] }}人已完成</text>
                    </view>
                </view>
                <xwy-ad v-if="index !== 0 && ((index + 1) % 10 === 0)" :activity_id="id"
                        :ad_type="66"></xwy-ad>
            </template>
        </view>

        <view v-if="!loading && !map_list.length" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">本活动暂未设置点位</view>
        </view>
    </view>
</template>

<script>
const app = getApp()

import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'

export default {
    data() {
        return {
            id: '',
            loading: true,
            map_list: [],
            unit: '里'
        }
    },
    onLoad(e) {
        if (e.point_knowledge_lock && e.point_knowledge_lock === '1') this.point_knowledge_lock = true
        this.$uni.showLoading('数据加载中...')
        this.id = e.id
        if (e.unit) this.unit = e.unit
        login.uniLogin(err => {
            if (err && err.errMsg) {
                this.loading = false
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getMapSet()
        })
    },
    methods: {
        getMapSet() {
            xwy_api.ajax({
                url: 'front.flat.sport_step.active_list/active_map_set',
                data: {
                    active_id: this.id,
                    access_token: app.globalData['access_token'],
                    get_finished_count: 1
                },
                success: res => {
                    this.loading = false
                    uni.hideLoading()
                    if (res.data?.map_set?.markers?.length) {
                        this.map_list = res.data.map_set.markers

                        let my_position_point_id = false
                        if (res.data.my_position?.['current_point_details']) {
                            const point_id = res.data.my_position['current_point_details'].point_id
                            if (point_id || point_id === 0) my_position_point_id = point_id
                        }
                        this.my_position_point_id = my_position_point_id
                    }
                }
            })


        },

        lookMapPic(item) {
            if (item.map_pic) return this.$uni.previewImage(item.map_pic)
            this.toNewsDetail(item['page_id'], item.point_id)
        },

        toNewsDetail(id, point_id) {
            if (!id) return false

            const point_detail = this.map_list.find(v => v.point_id === point_id)
            const in_point = this.my_position_point_id !== false && this.my_position_point_id >= point_detail.point_id
            // 活动是否配置了需要达到点位才能查看点位知识
            if (this.point_knowledge_lock && !in_point) {
                uni.showModal({
                    title: '提示',
                    content: '需要到达该点位才能查看哦',
                    showCancel: false
                })
                return false;
            }

            uni.navigateTo({
                url: '/pages/news/preview?id=' + id
            })
        }
    }
}
</script>

<style>
.list {
    padding: 10px 10px 10px 30px;
}

/* .item:nth-child(1) {
  padding-top: 0;
}
.item:nth-last-child(1) {
  padding-bottom: 0;
} */
.item {
    position: relative;
    padding: 20px 10px 20px 30px;
    border-left: 1px solid #2B85E4;
}

.icon {
    position: absolute;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 1px solid #80848f;
    top: 50%;
    margin-top: -16px;
    left: 0;
    margin-left: -16px;
}
</style>
