<template>
    <view class="page bg-background">
        <view class="list">
            <view class="item flex-kai bg-white" v-for="item in all_day" :key="item.day">
                <view class="color-title">第 {{ item.day }} 天</view>
                <view class="look-button color-white font14" :class="item.lock ? 'bg-disabled' : 'bg-warning'"
                      hover-class="navigator-hover" @click="look(item)">
                    {{ item.lock ? '未解锁' : '查看' }}
                </view>
            </view>
        </view>

        <every-day-certificate-popup ref="everyDayCertificatePopup"/>
    </view>
</template>

<script>
import everyDayCertificatePopup from "../components/every-day-certificate-popup.vue"

export default {
    components: {everyDayCertificatePopup},
    data() {
        return {
            all_day: []
        }
    },

    onLoad() {
        this.getCertificateData()
    },

    methods: {
        getCertificateData() {
            this.getOpenerEventChannel().once('certificateData', async ({nickname, begin_time, certificateList, active_id}) => {
                const records = await this.getExchangeRecord(active_id, certificateList.length)

                const startTime = begin_time
                this.certificateList = certificateList
                this.all_day = certificateList.map((item, index) => ({
                    day: index + 1,
                    lock: !records.includes(startTime + index * 86400),
                }))
                this.nickname = nickname || ''
            })

        },

        async getExchangeRecord(active_id, max) {
            this.$uni.showLoading('加载中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exchange/exchange_step_list',
                data: {
                    active_id,
                    page: 1,
                    perpage: 1000
                }
            })
            uni.hideLoading()
            
            const list = res?.data?.['exchange_list']?.data || []
            return list.reverse().slice(0, max).map(item => item.exchange_date)
        },

        look(item) {
            if (item.lock) return this.$uni.showToast(`第${item.day}天 未解锁`)

            this.$refs.everyDayCertificatePopup.show({
                certificate_set: this.certificateList[item.day - 1],
                nickname: this.nickname
            })
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
}

.list {
    padding-top: 1px;
    padding-bottom: 20px;

    .item {
        margin: 15px;
        padding: 15px;
        height: 40px;
        line-height: 40px;
        border-radius: 35px;

        .look-button {
            width: 100px;
            line-height: 40px;
            border-radius: 20px;
            text-align: center;
        }
    }
}
</style>