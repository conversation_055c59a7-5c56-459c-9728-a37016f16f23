<template>
    <view class="page" :style="bg_style">
		<view class="message-list">
			<view
				class="message-item flex-row"
				v-for="(item, index) in message_list"
				:key="item.key"
				:style="[
					{top: item.top + 'px'},
					{animationDuration: animationDuration + 's'}
				]"
			>
				<image v-if="item.headimg" class="headimg" :src="item.headimg"/>
				<view class="message-data flex-row" :class="{'user-message': item.userid === userid}">
					<view class="nickname pr5" v-if="item.nickname">{{item.nickname}}:</view>
					<view class="message-text" v-if="item.message">{{item.message}}</view>
				</view>
			</view>
		</view>

		<view class="send" :style="'bottom: ' + send_input_bottom + 'px;'">
			<view class="send-main flex-row">
				<image v-if="headimg" class="headimg" :src="headimg"/>
				<input
					class="send-input"
					v-model="message_content"
					:maxlength="20"
					:adjust-position="false"
					:cursor-spacing="20"
					placeholder="说点什么吧(限20字内哦)..."
					@confirm="sendEnterMessage()"
					@keyboardheightchange="keyboardheightchange"
					@blur="send_input_bottom = 0"
				></input>
				<button class="send-btn" @click="sendEnterMessage()">{{send_text}}</button>
			</view>
		</view>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import utils from '@/utils/utils.js'
import bs_socket from '@/utils/bullet_screen_socket.js'

export default {
    data() {
        return {
			active_id: '',
            message_list: [],
			message_content: '',
			headimg: '',
			nickname: '',
			userid: '',
			send_text: '发送',
			bg_style: '',
			animationDuration: 15,
			send_input_bottom: 0
        }
    },
    onLoad(e) {
		this.active_id = e.active_id
		this.headimg = e.headimg
		this.nickname = e.nickname
		if (e.send_text) this.send_text = e.send_text
		if (e.navigation_bar) {
			const {background_color, text_color} = JSON.parse(e.navigation_bar)
			uni.setNavigationBarColor({
				frontColor: text_color || "#000000",
				backgroundColor: background_color || "#ffffff"
			})
		}
		if (e.bgi) {
			this.bg_style = `background-image: url(${e.bgi});`
		}

		uni.showLoading({
			title: '弹幕准备中...',
			mask: true
		})
		login.uniLogin(err => {
			if (err && err.errMsg) {
				uni.hideLoading()
				uni.showModal({
					title: err.errTitle || '提示',
					content: err.errMsg,
					showCancel: false
				})
				return false
			}

			this.userid = app.globalData.userid

			if (bs_socket.socketTask) {
				uni.hideLoading()
				this.webSocketReady()
				return false
			}

			this.webSocketInit()

		})
    },



    methods: {
		webSocketInit(reconnect = false) {
			bs_socket.webSocketInit(() => {
				this.webSocketReady(reconnect)
			})
		},

		webSocketReady(reconnect = false) {
			bs_socket.socketTask.onMessage(res => {
			    console.log('【WEBSOCKET】收到消息', res.data)
				this.receiveMessages(res.data)
			})
			bs_socket.socketTask.onOpen(res => {
			    console.log('【WEBSOCKET】', '链接成功！', res)
				uni.hideLoading()
			})
			bs_socket.socketTask.onClose(res => {
			    console.log('【WEBSOCKET】链接关闭！', res)
				uni.hideLoading()
				this.webSocketInit(true)
			})
		},

		sendMessage(data) {
			if (!data.headimg) data.headimg = this.headimg
			if (!data.nickname) data.nickname = this.nickname
			data.active_id = this.active_id
			data.userid = this.userid
			bs_socket.socketTask.send({
				data: JSON.stringify(data)
			})
			this.message_content = ''
		},


		sendEnterMessage() {
			if (!this.message_content) return false
			this.sendMessage({message: this.message_content})
		},

		receiveMessages(message) {
			message = JSON.parse(message)

			if (message.active_id !== this.active_id) return false

			// 随机top值
			message.top = this.randomNum()


			 // 用来计算消息的存储时间，存储超过一定时间就删掉，避免页面数据过大
			message.time = new Date().getTime()

			// 页面dom循环的key，用index的话，删除了前面的消息，后面消息的index就会和前面的一样，可能导致消息在页面上显示不出来。直接用时间戳做key的话，有几率会出现一样的key，加上随机数的话重复的几率就会小很多
			message.key = message.time.toString() + message.top.toString()

			this.message_list.push(message)

			this.resetMessageList()
		},

		randomNum() {
			let {windowHeight} = uni.getSystemInfoSync()
			let num = utils.randomNum(10, windowHeight - 200)

			// 前后两条消息不能相差太小，避免叠在一起
			const space = 50
			if (this.previous_top && Math.abs(num - this.previous_top) < space) {
				console.log('重新计算')
				return this.randomNum()
			}

			this.previous_top = num
			return num
		},

		// 重置消息列表，删除已经走过的消息
		resetMessageList() {
			const now_time = new Date().getTime()
			const timeout = this.animationDuration * 1000
			this.message_list.forEach((v, i) => {
				const time_difference = now_time - v.time
				if (time_difference > timeout) this.message_list.splice(i, 1)
			})
		},

		keyboardheightchange(e) {
			console.log(e)
			this.send_input_bottom = e.detail.height
		}

    }
}
</script>

<style scoped>
	.page {
		width: 100%;
		height: 100vh;
		background-color: #fff;
		background-repeat: no-repeat;
		background-position: center;
		background-size: cover;
	}
	.message-list {
		position: relative;
	}
	.message-item {
		position: absolute;
		left: -10000%;
		animation: rigth2left linear;
	}
	@keyframes rigth2left {
		from {left: 100%}
		to {left: -100%}
	}

	.headimg {
		border-radius: 50%;
		display: block;
	}
	.message-item .headimg {
		width: 32px;
		height: 32px;
		min-width: 32px;
		margin-right: 4px;
	}
	.message-data {
		height: 32px;
		line-height: 32px;
		font-size: 14px;
		border-radius: 16px;
		color: #333;
		/* border: 1px solid #fff; */
		background-color: rgba(255, 255, 255, .8);
		padding: 0 10px;
		box-sizing: border-box;
	}
	.user-message {
		/* color: #f6dc6d; */
		/* border: 1px solid #f6dc6d; */
	}
	.nickname, .message-text {
		white-space: nowrap;
	}
	.send {
		position: fixed;
		left: 0;
		width: 100%;
		box-sizing: border-box;
		padding: 15px 10px;
		background-color: rgba(255, 255, 255, .8);
	}
	.send-main {
		border: 1px solid #f6dc6d;
		padding: 5px;
		border-radius: 23px;
	}
	.send-main .headimg {
		width: 34px;
		height: 34px;
		min-width: 34px;
	}
	.send-input {
		color: #333;
		height: 34px;
		line-height: 34px;
		width: 100%;
		margin: 0 5px;
		padding: 0 5px;
		box-sizing: border-box;
		border-bottom: 1px solid #666666;
	}
	.send-btn {
		background-color: #f6dc6d;
		color: #000;
		line-height: 34px;
		border-radius: 17px;
		padding: 0;
		width: 70px;
		min-width: 70px;
		text-align: center;
		font-size: 14px;
	}
</style>
