<template>
	<view class="page">
		<view class="canvas-container" :style="'width: ' + canvas_width + 'px; height: ' + canvas_height + 'px;'">
			<canvas
				class="canvas"
				canvas-id="canvas"
				id="canvas"
				:style="'width: ' + canvas_width + 'px; height: ' + canvas_height + 'px;'"
				@error="canvasError"
			></canvas>


		</view>


		<template v-if="active_types === 2">
            <view v-if="img_src && finished_time && !closed_top_title_tips"
                  class="p10 font14" style="color: #e19898;">
                恭喜你已在<uni-dateformat :date="finished_time * 1000" format="yyyy-MM-dd hh:mm:ss" />到达终点,特颁此证以兹鼓励。
            </view>


            <view class="big-img-view">
                <image class="big-img" :class="{'filter': !finished_time}" :src="img_src" mode="widthFix" @click="previewImage(img_src)"/>
                <view v-if="!finished_time" class="no-end flex-all-center color-white">
                    <view class="text-center">
                        <uni-icons type="locked" size="100" color="#ffffff"/>
                        <view>到达终点才能解锁</view>
                    </view>
                </view>
            </view>

            <view v-if="img_src && finished_time" class="text-center font14 color-sub">
                证书已生成，点击证书可预览、转发、保存
            </view>
        </template>

        <template v-if="active_types === 12">
            <view v-if="img_src && achieved_run_target && !closed_top_title_tips"
                  class="p10 font14 text-center" style="color: #e19898;">
                恭喜你已到达 {{ certificate_mileage }}km, 特颁此证以兹鼓励。
            </view>


            <view class="big-img-view">

                <image class="big-img" :class="{'filter': !achieved_run_target}" :src="img_src"
                       mode="widthFix" @click="previewImage(img_src)"/>
                <view v-if="!achieved_run_target" class="no-end flex-all-center color-white">
                    <view class="text-center">
                        <uni-icons type="locked" size="100" color="#ffffff"/>
                        <view>到达 {{ certificate_mileage }}km 才能解锁</view>
                    </view>
                </view>
            </view>

            <view v-if="img_src && achieved_run_target" class="text-center font14 color-sub">
                证书已生成，点击证书可预览、转发、保存
            </view>
        </template>

        <template v-if="active_types === 21">
            <view v-if="img_src && collectingCards && !closed_top_title_tips"
                  class="p10 font14 text-center" style="color: #e19898;">
                恭喜你已集完卡, 特颁此证以兹鼓励。
            </view>

            <view class="big-img-view">

                <image class="big-img" :class="{'filter': !collectingCards}" :src="img_src"
                       mode="widthFix" @click="previewImage(img_src)"/>
                <view v-if="!collectingCards" class="no-end flex-all-center color-white">
                    <view class="text-center">
                        <uni-icons type="locked" size="100" color="#ffffff"/>
                        <view>集完所有卡才能解锁</view>
                    </view>
                </view>
            </view>

            <view v-if="img_src && collectingCards" class="text-center font14 color-sub">
                证书已生成，点击证书可预览、转发、保存
            </view>
        </template>

        <template v-if="active_types === 25">
            <view v-if="img_src && all_tasks_completed"
                  class="p10 font14 text-center" style="color: #e19898;">
                恭喜你已完成全部任务, 特颁此证以兹鼓励。
            </view>

            <view class="big-img-view">

                <image class="big-img" :class="{'filter': !all_tasks_completed}" :src="img_src"
                       mode="widthFix" @click="previewImage(img_src)"/>
                <view v-if="!all_tasks_completed" class="no-end flex-all-center color-white">
                    <view class="text-center">
                        <uni-icons type="locked" size="100" color="#ffffff"/>
                        <view>完成全部任务才能解锁</view>
                    </view>
                </view>
            </view>

            <view v-if="img_src && all_tasks_completed" class="text-center font14 color-sub">
                证书已生成，点击证书可预览、转发、保存
            </view>
        </template>

		<xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>
		<xwy-ad :activity_id="id" :ad_type="3"></xwy-ad>
	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'

	let ctx = null

	export default {
		data() {
			return {
				id: '',
				finished_time: 0,
				img_src: '',
				canvas_width: 0,
				canvas_height: 0,
                closed_top_title_tips: false,
                active_types: 2,   // 默认是健步走活动的证书
                achieved_run_target: false,
                certificate_mileage: 0,
                // 是否集卡活动集完卡
                collectingCards: false,

                // 任务闯关活动是否所有任务都已完成
                all_tasks_completed: false
			}
		},
		onReady() {
			ctx = uni.createCanvasContext('canvas')
		},
		onLoad(e) {
			this.id = e.id
            if (e.active_types) this.active_types = Number(e.active_types)
			if (e.finished_time) this.finished_time = Number(e.finished_time)

			this.$uni.showLoading('数据加载中...')

			login.uniLogin(err => {
                const {errMsg, errTitle = '提示'} = err || {}
				if (errMsg) return this.$uni.showModal(errMsg, {title: errTitle})
                
				this.init()
			})
		},
		methods: {
			canvasError(e) {
				console.log('canvas_error:', e)
			},
            
            async init() {
                // 健步走活动需要查询用户是否到达终点
                if (this.active_types === 2) await this.getFinishedTime()

                // 跑步活动需要查看用户累计公里数是否到达目标
                if (this.active_types === 12) await this.reachTheTargetOrNot()

                // 查询用户是否集完所有卡
                if (this.active_types === 21) await this.collectingCardsCheck()

                // 查询用户是否完成所有任务
                if (this.active_types === 25) await this.tasksCompleteCheck()

                await this.getData()
            },

            async getFinishedTime() {
                // 页面参数已传到达终点时间，不需要再次获取
                if (this.finished_time) return

                const user = await this.getUserActiveDetails()
                this.finished_time = user.finished_time || 0
            },

            async collectingCardsCheck() {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.sign_location.userSign/user_sign_light_map_point',
                    data: {active_id: this.id}
                })
                const list = res?.data?.['sign_light_res'] || []

                this.collectingCards = list.every(item => item.have_sign_light)
            },
            
            async reachTheTargetOrNot() {
                const details = await this.fetchActivityDetails()
                const certificate_mileage = details?.conf?.active?.certificate_mileage || 0
                this.certificate_mileage = certificate_mileage
                const user_mileage = await this.getUserMileage()
                if (user_mileage >= certificate_mileage * 1000) this.achieved_run_target = true
            },

            async fetchActivityDetails() {
                const details = app['globalData']['activity_detail']
                if (details && details.active_id === this.id) return details

                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.id
                    }
                })
                return res?.['data']?.['active_details']
            },
            
            async getUserMileage() {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.running.user/running_list',
                    data: {
                        page: 1,
                        perpage: 1,
                        active_id: this.id,
                        only_myself: 1
                    }
                })
                
                return res?.data?.['running_list']?.data?.[0]?.['user_total_data']?.meter || 0
            },

            async tasksCompleteCheck() {
                // 查询是否报名
                const user = await this.getUserActiveDetails()
                if (!user?.checked) return

                // 获取活动关卡列表
                const levelList = await this.getLevelList()
                if (!levelList?.length) return

                // 查询用户是否到达最后一关
                const userLevelIndex = user.rushed_round || 0
                if (userLevelIndex < levelList.length - 1) return

                // 查询用户是都完成最后一关的所有任务
                const lastLevelId = levelList[levelList.length - 1].id
                const lastLevelCompleted = await this.levelTasksCompleteCheck(lastLevelId)
                if (!lastLevelCompleted) return

                this.all_tasks_completed = true
            },

            async getUserActiveDetails() {
                if (this.userDetails) return this.userDetails

                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.user/user_attend_details',
                    data: {
                        active_id: this.id
                    }
                })

                const userDetails = res?.data?.user_details || null
                if (userDetails) this.userDetails = userDetails

                return res?.data?.user_details || null
            },

            async getLevelList() {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_map_point_list',
                    data: {
                        active_id: this.id
                    }
                })

                return res?.data?.map_point_list || []
            },

            async levelTasksCompleteCheck(id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.job_list.jobState/job_finished_state_list',
                    data: {
                        active_id: this.id,
                        point_id: id
                    }
                })

                const taskList = res?.data?.['job_finished_check']?.job_list
                if (!taskList?.length) return false

                return taskList.every(item => item.finished_details?.have_finished)
            },

			async getData() {
				this.$uni.showLoading('证书数据获取中...')
                
				const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.user/arrive_end_reward_books_confset', 
                    data: {
                        active_id: this.id
                    }
                })

                const {books_confset} = res?.data
                if (!books_confset) return this.$uni.showModal('活动未配置证书', {
                    success: () => uni.navigateBack()
                })

                const {background_image, text, closed_top_title_tips} = books_confset
                if (closed_top_title_tips) this.closed_top_title_tips = true
                
                if (!background_image) return this.$uni.showModal('活动未配置证书图片', {
                    success: () => uni.navigateBack()
                })
                
				
                
                const image_info = await this.$uni.getImageInfo(background_image)
                const {path, width, height} = image_info || {}
                if (!path) return this.$uni.showModal('证书背景图片获取失败', {
                    success: () => uni.navigateBack()
                })

                this.canvas_width = width
                this.canvas_height = height

                this.data_list = text || []
                
                this.drawCanvas(path)
			},

			drawCanvas(path) {
				this.$uni.showLoading('证书生成中...')

				// 画背景图
				ctx.drawImage(path, 0, 0, this.canvas_width, this.canvas_height)

				const data_list = this.data_list

				// 画证书里面的文字和图片等内容
				data_list.forEach((v, i)=> {
					v = this.initDataItem(v)

					const is_last = i === data_list.length - 1

					if (v.type === 'text') this.drawText(v, is_last)
					if (v.type === 'line') this.drawLine(v, is_last)
					if (v.type === 'image') this.drawImage(v, is_last)
				})
			},

			initDataItem(v) {
				v.left ||= 0
				v.top ||= 0
				v.color ||= '#000000'

				v.align ||= 'left'
				if (v.align === 'right') v.right ||= 0

				// 线条和图片的居中和居右
				if (v.type === 'line' || v.type === 'image') {
					v.width ||= 0
					v.height ||= 0
					if (v.align === 'center') v.left = this.canvas_width / 2 - v.width / 2
					if (v.align === 'right') v.left = this.canvas_width - v.right - v.width
				}

				return v
			},

			drawText(v, is_last) {
				if (v.align === 'center') v.left = this.canvas_width / 2
				if (v.align === 'right') v.left = this.canvas_width - v.right
				ctx.setTextAlign(v.align)
				ctx.setFontSize(v.size || 16)
				ctx.fillStyle = v.color
				ctx.fillText(v.text, v.left, v.top)

				is_last && this.draw()
			},

			drawLine(v, is_last) {
				ctx.beginPath()
				ctx.setStrokeStyle(v.color)
				ctx.setLineWidth(v.height || 1)
				ctx.moveTo(v.left, v.top)
				ctx.lineTo(v.left + v.width, v.top)
				ctx.stroke()

				is_last && this.draw()
			},

			async drawImage(v, is_last) {
                const {src, left, top, width, height} = v
                const image_info = await this.$uni.getImageInfo(src)
                if (image_info?.path) ctx.drawImage(image_info.path, left, top, width, height)
                is_last && this.draw()
			},

			draw() {
				ctx.draw(true, () => {
					this.canvasToTempFilePath()
				})
			},

			canvasToTempFilePath() {
				uni.canvasToTempFilePath({
					canvasId: 'canvas',
					success: res => {
						uni.hideLoading()
						console.log('绘画的图片', res)
						this.img_src = res.tempFilePath
					},
					fail: err => {
						console.log('绘画失败', err)
						this.canvasToTempFilePath()
					}
				})

			},

			previewImage(url) {
				this.$uni.previewImage(url)
			}
		}
	}
</script>

<style>
.page {
    padding-bottom: 30px;
}

	.canvas-container {
		position: fixed;
		top: -999999px;
		left: -999999px;
	}
	.big-img-view {
		position: relative;
		margin: 10px;
	}
	.big-img {
		display: block;
		width: 100%;
		height: auto;
	}


	.filter {
		filter: blur(1px);
	}
	.no-end {
		position: absolute;
		background-color: rgba(0, 0, 0, .5);
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
	}
</style>
