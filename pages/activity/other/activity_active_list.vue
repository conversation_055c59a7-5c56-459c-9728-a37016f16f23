<template>
	<view class="page bg-background">
		<view class="flex-row bg-white">
			<view
				class="type-bar-item"
				:class="{'type-active': look_type === 'all'}"
				@click="changeLookType('all')"
			>所有活动</view>
			<view
				class="type-bar-item"
				@click="changeLookType('14')"
				:class="{'type-active': look_type === '14'}"
			>赛事评分</view>
		</view>

		<view class="p10 color-sub font14 text-center">
			{{look_type === '14' ? type_14_count : active_count}} 场活动，
			总计 {{look_type === '14' ? type_14_times : allTimes}} 次
		</view>
		<view class="list pt5">
			<view
				class="list-item bg-white"
				hover-class="navigator-hover"
				v-for="(item, index) in list"
				:key="item.active_id"
				@click="toDetail(item.active_details)"
			>
				<text class="color-sub">{{index + 1}}、</text>
				<text class="color-title">{{item.active_details.name}}</text>
				<text v-if="item.active_details.rank_set && item.active_details.rank_set.closed_AD">【付】</text>
				<text class="pl5 pr5">{{item.exchange_times}}</text>
				<activity-type-tag :type="item.active_details.types"></activity-type-tag>
			</view>
		</view>

		<view v-if="!list.length && !loading" class="text-center" style="padding-top: 10vh;">
		    <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
		    <view class="color-sub">暂无记录</view>
		</view>
	</view>
</template>

<script>
	import activityPagePathConfig from "@/config/activity-page-path"

  const app = getApp()
	import login from '../../../utils/api/login.js'
	import xwy_api from '../../../utils/api/xwy_api.js'
	import utils from '../../../utils/utils.js'

	import activityTypeTag from '../components/activity-type-tag.vue'

	export default {
		components: {
			activityTypeTag
		},
		data() {
			return {
				loading: true,
				look_type: 'all',
				list: [],
				type_14_list: [],
				active_count: 0,
				allTimes: 0,
				type_14_count: 0,
				type_14_times: 0
			}
		},
		onLoad() {
			uni.showLoading({
				mask: true
			})
			login.uniLogin(err => {
			    if (err && err.errMsg) {
					this.loading = false
			        uni.hideLoading()
			        uni.showModal({
			            title: err.errTitle || '提示',
			            content: err.errMsg,
			            showCancel: false
			        })
			        return false
			    }

				this.getList()
			})
		},
		methods: {
			changeLookType(type) {
				if (this.look_type === type) return
				this.look_type = type
				this.list = []
				this.list = type === '14' ? this.type_14_list : this.all_list
			},

			async getList() {
				const data = {
					access_token: app.globalData.access_token
				}
				const res = await xwy_api.request({url: 'front.flat.sport_step.active_list/flat_active_list', data})
				this.loading = false
				uni.hideLoading()
				if (!res || !res.data || !res.data.active_list || !res.data.active_list.length) return false
				let list = res.data.active_list
				const type_14_list = []
				let type_14_count = 0,
					type_14_times = 0
				list.forEach(v => {
					if (v.active_details.types === 14) {
						type_14_list.push(v)
						type_14_count++
						type_14_times += v.exchange_times
					}
				})
				if (type_14_list.length > 100) type_14_list.length = 100
				this.type_14_list = type_14_list
				this.type_14_count = type_14_count
				this.type_14_times = type_14_times

				if (list.length > 100) list.length = 100
				this.list = list
				this.all_list = list
				if (res.data.active_count) this.active_count = res.data.active_count
				if (res.data.allTimes) this.allTimes = res.data.allTimes
			},

			toDetail(data) {
				// 刚开发的时候赛事评分活动是不允许跳转到活动详情的，因为不是本平台的活动，没有他的详情页面
				// 后面刘总说让他跳，跳过去可以复制活动id和查看活动时间 2023-04-13 15:05:03
				// if (data.types === 14) {
				// 	uni.showToast({
				// 		title: '赛事评分活动，无法跳转',
				// 		icon: 'none'
				// 	})
				// 	return
				// }


          /**
           * 2023-11-14 11:33:14
           * 刘总说签名活动跳转到详情页面后一直在转
           * 由于不是健步走的活动类型也会在这里显示，所以不是健步走的活动就不跳转到活动详情，直接复制活动id
           */
          let url = activityPagePathConfig.getActivityPath(data.types, 'details')
          // 因为没有找到路径会默认返回健步走活动的路径，不想改获取路径的方法嘞，所以这里判断下，如果返回的是健步走的活动详情路径，并且活动类型不是健步走的，就确定不是本平台的活动
          if (url === '/pages/activity/user/detail' && data.types !== 2) {
            return uni.setClipboardData({
              data: data.active_id,
              success: () => {
                uni.showToast({
                  title: `不是本平台的活动类型(活动类型: ${data.types})，无法跳转。已复制活动id。`,
                  icon: 'none',
                    duration: 3000
                })
              }
            })
          }

          if (data.active_id) this.$uni.setClipboardData(data.active_id, '活动ID已复制')

          utils.toActivityDetail(data)
			},
		}
	}
</script>

<style>
.page {
	min-height: 100vh;
	padding-bottom: 20px;
	box-sizing: border-box;
}
.type-bar-item {
	width: 50%;
	line-height: 40px;
	height: 40px;
	box-sizing: border-box;
	color: #666;
	text-align: center;
}
.type-active {
	color: #2d8cf0;
	border-bottom: 2px solid #2d8cf0;
}
.list-item {
	padding: 20px 10px;
	border-radius: 10px;
	margin: 10px;
}
</style>
