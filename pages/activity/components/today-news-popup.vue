<template>
    <view>
        <uni-popup ref="popup" @touchmove.prevent.stop="">
            <view class="popup-container">
                <view class="news-title">{{ title }}</view>

                <scroll-view :scroll-y="true" class="news-content">
                    <u-parse :content="content"/>
                </scroll-view>
            </view>
            <view class="close-container" @click="popupClose">
                <uni-icons type="close" size="28" color="#ffffff"/>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "today-news-popup",
    props: ['activeId', 'set'],
    data() {
        return {
            title: '',
            content: ''
        }
    },

    methods: {
        open() {
            const index_pop = this.set?.['index_pop']
            if (!index_pop) return
            
            const {pageid, display_times} = index_pop
            if (!pageid || !display_times) return
            this.display_times = display_times

            // 0：不弹，     1：每次弹       2：每天一次     
            if (display_times === 2) {
                const viewedToday = this.todayViewedCheck()
                if (viewedToday) return
            }

            this.getNewsDetails(pageid)
        },

        todayViewedCheck() {
            const storage = uni.getStorageSync('viewedToday')
            if (!storage) return false
            const look_day = storage[this.activeId]
            if (!look_day) return false
            const today = this._utils.getDay(0, true)
            return today === look_day
        },

        async getNewsDetails(news_id) {
            const news = await this.xwy_api.getNewsDetail(news_id)
            const content = news?.content
            if (!content) return

            this.title = news.title
            this.content = content

            this.popupOpen()
            this.setViewDate()
        },

        setViewDate() {
            if (this.display_times !== 2) return
            
            const storage = uni.getStorageSync('viewedToday') || {}
            storage[this.activeId] = this._utils.getDay(0, true)
            uni.setStorageSync('viewedToday', storage)
        },

        popupOpen() {
            this.$refs.popup.open()
        },

        popupClose() {
            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.popup-container {
    width: 90vw;
    max-width: 400px;
    border-radius: 10px;
    padding: 15px;
    box-sizing: border-box;
    background-color: #fff;
}

.news-title {
    text-align: center;
    padding-bottom: 10px;
}

.news-content {
    height: 70vh;
}

.close-container {
    text-align: center;
}
</style>