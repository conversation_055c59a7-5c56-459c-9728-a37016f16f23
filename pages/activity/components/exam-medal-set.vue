<template>
    <view>
        <view class="show">
            <view class="list">
                <view class="item flex-row" v-for="(item, index) in list" :key="index">
                    <view class="logo">
                        <image class="logo-image" :src="item.pic" mode="aspectFill"/>
                    </view>
                    <view class="info">
                        <view class="color-content">勋章名称: {{ item.prize_name }}</view>
                        <view class="color-content">考卷名称: {{ item.exam_name }}</view>
                        <view class="color-content">最低分数: {{ item.min_score }}分</view>
                    </view>

                    <view class="action-bar flex-row">
                        <view class="action-item flex-all-center" @click="editItem(index)">
                            <text class="iconfont icon-edit color-sub"></text>
                        </view>
                        <view class="action-item flex-all-center" @click="list.splice(index, 1)">
                            <text class="iconfont icon-delete color-sub"></text>
                        </view>
                    </view>
                </view>
            </view>

            <view v-if="!list.length" class="flex-all-center">
                <view class="color-light-primary p10" @click="addItem">添加勋章</view>
            </view>
        </view>

        <uni-popup ref="popup">
            <view class="set-popup bg-white">
                <view class="text-center">设置</view>


                <view style="padding: 20px 0;">
                    <view class="flex-all-center">
                        <view class="set-logo" @click="changeLogo">

                            <image v-if="editData.pic" class="set-logo-image"
                                   :src="editData.pic" mode="aspectFill"/>
                            <view v-else class="set-logo-image flex-all-center">
                                <uni-icons type="image" color="#bbbec4" size="60"/>
                            </view>
                        </view>
                    </view>
                    <view class="flex-all-center pt5">
                        <view class="color-light-primary font14" @click="changeLogo">
                            {{ editData.pic ? '修改' : '设置' }}勋章图
                        </view>
                    </view>
                </view>

                <uni-forms>
                    <uni-forms-item label="勋章名称:">
                        <uni-easyinput v-model="editData.prize_name" maxlength="20"
                                       placeholder="请输入勋章名称"/>
                    </uni-forms-item>
                    <uni-forms-item label="绑定考卷:">
                        <view class="form-view flex-kai" @click="bindExam">
                            <view>
                                <text v-if="editData.exam_id">
                                    {{ editData.exam_name || editData.exam_id }}
                                </text>
                                <text v-else class="color-sub font14">请绑定考卷</text>
                            </view>
                            <view class="flex-row">
                                <view v-if="editData.exam_id" class="color-disabled font14">更换</view>
                                <view class="flex-all-center">
                                    <uni-icons type="right" color="#bbbec4" size="14px"/>
                                </view>
                            </view>
                        </view>
                    </uni-forms-item>
                    <uni-forms-item label="最低分数:">
                        <uni-easyinput type="digit" v-model="editData.min_score" maxlength="5"
                                       placeholder="请设置最低分数"/>
                        <view class="color-sub font12 pt5">需要答题成绩达到多少分才能解锁勋章。</view>
                    </uni-forms-item>
                </uni-forms>

                <view class="confirm-button color-white bg-light-primary text-center"
                      hover-class="navigator-hover" @click="confirm">确定
                </view>

                <view class="flex-all-center">
                    <view class="p5 color-sub font12" @click="$refs.popup.close()">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "exam-medal-set",
    props: ['set', 'activeId'],
    data() {
        return {
            types: 1,
            list: [],
            editData: {
                exam_id: "",
                exam_name: "",
                prize_name: "",
                pic: "",
                min_score: 0
            }
        }
    },

    watch: {
        set: {
            handler(val) {
                this.dataUpdate(val)
            },
            deep: true
        },

        types() {
            this.updateSet()
        },

        list: {
            handler() {
                this.updateSet()
            },
            deep: true
        }
    },

    mounted() {
        this.dataUpdate(this.set)
    },

    methods: {
        dataUpdate(val) {
            this.types = val.types
            this.list = val.prize_list
        },

        updateSet() {
            this.$emit('update:set', {
                types: this.types,
                prize_list: this.list
            })
        },

        addItem() {
            this.editIndex = null
            this.addOrEditItem()
        },

        editItem(index) {
            this.editIndex = index
            this.addOrEditItem(index)

        },

        addOrEditItem() {
            const item = this.editIndex === null ? {} : this.list[this.editIndex]

            this.editData = {
                exam_id: item.exam_id || "",
                exam_name: item.exam_name || "",
                prize_name: item.prize_name || "",
                pic: item.pic || "",
                min_score: item.min_score || 0
            }

            this.$nextTick(() => this.$refs.popup.open())
        },

        changeLogo() {
            this.$uni.navigateTo(`/pages/other/image_upload_or_select?active_id=${this.activeId}`, {
                events: {
                    newImg: src => {
                        this.editData.pic = src
                    }
                }
            })
        },

        bindExam() {
            this.$uni.navigateTo('/pages/likou_dati/pages/exam/exam_list/exam_list?in_select=true', {
                events: {
                    updateExam: data => {
                        this.editData.exam_id = data.id
                        this.editData.exam_name = data.title
                    }
                }
            })
        },

        confirm() {
            const {exam_id, prize_name, pic} = this.editData

            if (!pic) return this.$uni.showToast('请设置勋章图')
            if (!prize_name) return this.$uni.showToast('请输入勋章名称')
            if (!exam_id) return this.$uni.showToast('请绑定考卷')

            const min_score = Number(this.editData.min_score)
            if (isNaN(min_score) || min_score < 0) return this.$uni.showToast('请输入正确的最低分数')

            const data = {
                exam_id,
                exam_name: this.editData.exam_name,
                prize_name,
                pic,
                min_score
            }

            this.editIndex === null ? this.list.push(data) : this.$set(this.list, this.editIndex, data)

            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.show {
    .list {
        .item {
            margin: 5px;
            padding: 10px;
            border-radius: 5px;
            background-color: #f8f8f8;
            position: relative;

            .action-bar {
                position: absolute;
                top: 0;
                right: 0;

                .action-item {
                    width: 32px;
                    height: 32px;
                    border-radius: 16px;
                    margin: 5px;
                    background-color: #fff;
                }
            }

            .logo {
                .logo-image {
                    width: 60px;
                    height: 60px;
                    border-radius: 5px;
                }
            }

            .info {
                padding-left: 10px;
            }
        }
    }
}

.set-popup {
    padding: 10px;
    border-radius: 10px;
    width: 80vw;
    max-width: 400px;

    .set-logo {
        image {
            display: block;
        }

        .set-logo-image {
            width: 60px;
            height: 60px;
            border-radius: 5px;
        }
    }

    .form-view {
        border: 1px solid #DCDFE6;
        color: #333;
        height: 34px;
        line-height: 34px;
        border-radius: 4px;
        padding: 0 10px;
    }

    .confirm-button {
        width: 120px;
        line-height: 40px;
        border-radius: 20px;
        margin: 20px auto 0;
    }
}
</style>