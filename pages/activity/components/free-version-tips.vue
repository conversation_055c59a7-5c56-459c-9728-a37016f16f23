<template>
    <view>
        <navigator class="tips-container" v-if="show" url="/pages/other/contact">{{ tips }}</navigator>
    </view>
</template>

<script>
export default {
    name: "free-version-tips",
    props: {
        adminUserid: {
            type: Number,
            default: 0
        },
        tips: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            show: false
        }
    },

    mounted() {
        this.showCheck()
    },

    methods: {
        showCheck() {
            if (!this.tips) return
            const userid = getApp().globalData['userid']
            if (!userid || !this.adminUserid || userid !== this.adminUserid) return
            this.show = true
        }
    }
}
</script>

<style lang="scss">
.tips-container {
    padding: 10px;
    color: #e19898;
    background-color: #f8f8f8;
}
</style>