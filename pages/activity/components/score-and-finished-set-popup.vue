<template>
    <view>
        <uni-popup ref="scoreAndFinishedSetPopup" @touchmove.stop.prevent="">
            <view class="score-and-finished-set-popup bg-white radius10 p10">
                <view class="text-center p10">到达终点分数奖励设置</view>
                <uni-forms label-position="top" label-width="300">
                    <uni-forms-item label="最先到达终点获得的分数">
                        <uni-easyinput type="digit" :maxlength="10" trim="all"
                                       v-model="score_and_finished_set.max"/>
                    </uni-forms-item>
                    <uni-forms-item label="每后一名减的分数">
                        <uni-easyinput type="digit" :maxlength="10" trim="all"
                                       v-model="score_and_finished_set.minus_score"/>
                    </uni-forms-item>
                    <uni-forms-item label="最多减至的分数">
                        <uni-easyinput type="digit" :maxlength="10" trim="all"
                                       v-model="score_and_finished_set.min"/>
                    </uni-forms-item>
                </uni-forms>
                <view class="bottom-button flex-center p10">
                    <view class="cancel bg-background color-sub" @click="close">取消</view>
                    <view class="confirm bg-light-primary color-white" @click="confirm">确定</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "score-and-finished-set-popup",
    props: ['set'],
    data() {
        return {
            score_and_finished_set: {
                max: '',
                min: '',
                minus_score: ''
            }
        }
    },

    mounted() {
        this.score_and_finished_set = this.set
    },

    methods: {
        open() {
            this.$refs.scoreAndFinishedSetPopup.open()
        },

        close() {
            this.$refs.scoreAndFinishedSetPopup.close()
        },

        confirm() {
            const set = this.setCheck()
            if (!set) return

            this.$emit('update:set', set)
            this.close()
        },

        setCheck() {
            const errToast = msg => {
                this.$uni.showToast(msg, 'none', 3000)
                return false
            }

            let {max, min, minus_score} = this.score_and_finished_set
            if (max === '') return errToast('请输入 最先到达终点获得分数')
            if (minus_score === '') return errToast('请输入 每后一名减的分数')
            if (min === '') return errToast('请输入 最多减至的分数')

            max = Number(max)
            min = Number(min)
            minus_score = Number(minus_score)

            if (isNaN(max)) return errToast('最先到达终点获得分数 必须为数字')
            if (isNaN(minus_score)) return errToast('每后一名减的分数 必须为数字')
            if (isNaN(min)) return errToast('最多减至的分数 必须为数字')

            if (max < 0) return errToast('最先到达终点获得分数 不能为负数')
            if (minus_score < 0) return errToast('每后一名减的分数 不能为负数')
            if (min < 0) return errToast('最多减至的分数 不能为负数')

            if (min > max) return errToast('最多减至的分数 不能大于 最先到达终点获得分数')

            return {max, min, minus_score}
        }
    }
}
</script>

<style lang="scss">
.score-and-finished-set-popup {
    width: 300px;

    .bottom-button {
        .cancel, .confirm {
            width: 120px;
            text-align: center;
            line-height: 40px;
            border-radius: 20px;
            margin: 0 15px;
        }
    }
}
</style>