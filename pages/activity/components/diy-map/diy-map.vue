<template>
    <view class="diy-map__">
        <view class="diy-map-container">
            <image class="diy-map-bg-img" :src="set.bgImg" mode="widthFix"/>

            <image v-for="(point, index) in pointModuleList" :key="index" :src="point.src"
                   :style="point.style" @click="clickPoint(point)"/>

            <image v-for="(item, index) in imageModuleList" :key="index" :src="item.src" :style="item.style"
                   @click="clickModule(item)"/>

            <view v-for="(item, index) in spaceModuleList" :key="index" :style="item.style"
                  @click="clickModule(item)"></view>

            <image v-for="(item, index) in fixedImageModuleList" :key="index" :src="item.src"
                   :style="item.style" @click="clickModule(item)"/>
        </view>

        <point-popup
            ref="pointPopup"
            :active-details="activeDetails"
            :active-more-data="activeMoreData"
            :user-details="userDetails"
            :redpack-check-error-info="redpackCheckErrorInfo"
            @updateUserDetails="updateUserDetails"
            @toReadPointNews="toReadPointNews"
            @toExamDetail="toExamDetail"
            @toLottery="toLottery"
            @getRedpack="getRedpack"
            @toTogetherPic="toTogetherPic"
        />

        <exchange-success-popup
            ref="exchangeSuccessPopup"
            :active-details="activeDetails"
            @exchangeSuccessLookPointPopup="exchangeSuccessLookPointPopup"
            @toExamDetail="toExamDetail"
            @toLottery="toLottery"
            @getRedpack="getRedpack"
            @toTogetherPic="toTogetherPic"
        />
    </view>
</template>

<script>
import pointPopup from '@/pages/activity/user/road-map/components/point-popup/point-popup.vue'
import exchangeSuccessPopup
    from '@/pages/activity/user/road-map/components/exchange-success-popup/exchange-success-popup.vue'

export default {
    name: "diy-map",
    components: {
        pointPopup,
        exchangeSuccessPopup
    },
    emits: ['clickModule', 'updateUserDetails', 'toJoinActivity'],
    props: {
        set: {
            type: Object,
            default: () => ({})
        },
        activeDetails: {
            type: Object,
            default: () => ({})
        },
        userDetails: {
            type: Object,
            default: () => ({})
        },
        activeMoreData: {
            type: Object,
            default: () => ({})
        }
    },

    data() {
        return {
            pointModuleList: [],
            imageModuleList: [],
            spaceModuleList: [],
            fixedImageModuleList: [],

            // 获取红包的错误信息，需要传递给点位弹窗的子组件
            redpackCheckErrorInfo: ''
        }
    },

    watch: {
        userDetails: {
            handler() {
                this.getMapInfo()
            },
            deep: true
        },
        activeDetails: {
            handler() {
                this.setPageElement()
            },
            deep: true
        }
    },

    computed: {
        activeId() {
            return this.activeDetails?.active_id || ''
        }
    },

    mounted() {
        this.calculateRatio()
        this.getMapInfo()
    },

    methods: {
        calculateRatio() {
            // PC端设置的时候，使用的是375px的设计稿，所以这里的设计稿大小是375px
            const designSize = 375
            const {windowWidth} = uni.getWindowInfo()
            this.ratio = windowWidth / designSize
        },

        async getMapInfo() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_map_set',
                data: {
                    active_id: this.activeId
                }
            })

            this.pointList = res?.data?.map_set?.markers
            this.myPointId = res?.data?.my_position?.current_point_details?.id || null

            this.setPageElement()
        },

        setPageElement() {
            const ratio = this.ratio

            const moduleList = JSON.parse(JSON.stringify(this.set.moduleList || []))

            const pointModuleList = [], imageModuleList = [], spaceModuleList = [], fixedImageModuleList = []

            moduleList.forEach(item => {
                const {width, height, x, y, radius} = item

                item.style = `position: ${item.type === 'fixed-image' ? 'fixed' : 'absolute'}; width: ${width * ratio}px; height: ${height * ratio}px; top: ${y * ratio}px; left: ${x * ratio}px; border-radius: ${radius}%;`

                if (item.type === 'point') {
                    pointModuleList.push(item)
                } else if (item.type === 'image') {
                    imageModuleList.push(item)
                } else if (item.type === 'space') {
                    spaceModuleList.push(item)
                } else if (item.type === 'fixed-image') {
                    fixedImageModuleList.push(item)
                }

                delete item.width
                delete item.height
                delete item.x
                // delete item.y  // 不删了，要用来做滚动
                delete item.radius
                delete item.type

                if (item.type !== 'point') {
                    item.type = item.trigger_type
                    delete item.trigger_type
                }
            })


            this.setPointModule(pointModuleList)
            this.setImageModule(imageModuleList)
            this.setSpaceModule(spaceModuleList)
            this.setFixedImageModule(fixedImageModuleList)
        },

        setImageModule(imageModuleList) {
            this.imageModuleList = imageModuleList.filter(item => this.$parent.filterModelList(item))
        },

        setSpaceModule(spaceModuleList) {
            this.spaceModuleList = spaceModuleList.filter(item => this.$parent.filterModelList(item))
        },

        setFixedImageModule(fixedImageModuleList) {
            this.fixedImageModuleList = fixedImageModuleList.filter(item => this.$parent.filterModelList(item))
        },

        setPointModule(pointModuleList) {
            const myPointIndex = this.pointList.findIndex(item => item.id === this.myPointId)
            this.pointModuleList = pointModuleList.map(item => {
                const {point_id, after_point_image, before_point_image, current_point_image, style} = item

                const index = this.pointList.findIndex(i => i.id === item.point_id)
                let userPointStatus = ''

                let src = item.src
                if (index < myPointIndex) {
                    // 已经走过的点位
                    src = before_point_image || src
                    userPointStatus = 'before'
                } else if (index > myPointIndex) {
                    // 还没到的点位
                    src = after_point_image || src
                    userPointStatus = 'after'
                } else {
                    // 当前走到的点位
                    userPointStatus = 'in'
                    src = current_point_image || this.userDetails?.headimg || src
                    this.scrollToMyPoint(item.y)
                }

                return {point_id, src, style, userPointStatus}
            })
        },

        scrollToMyPoint(y) {
            const {windowHeight} = uni.getWindowInfo()
            const scrollTop = y * this.ratio - windowHeight / 2
            this.$nextTick(() => {
                uni.pageScrollTo({
                    scrollTop,
                    duration: 300
                })
            })
        },

        clickPoint(point) {
            // 上次一弹窗的红包获取状态
            this.redpackCheckErrorInfo = ''

            const pointDetails = this.pointList.find(item => item.id === point.point_id)
            const inPoint = point.userPointStatus === 'in' || point.userPointStatus === 'before'
            this.$refs.pointPopup.open(pointDetails, inPoint)
        },

        clickModule(item) {
            this.$emit('clickModule', item)
        },

        updateUserDetails() {
            this.$emit('updateUserDetails')
        },

        toJoinActivity() {
            this.$emit('toJoinActivity')
        },

        async exchangeSuccessShow(res) {
            const myPointId = this.myPointId
            await this.getMapInfo()
            const newMyPointId = this.myPointId

            const nextPoint = myPointId !== newMyPointId

            const newPointIndex = this.pointList.findIndex(item => item.id === newMyPointId)
            const point = this.pointList[newPointIndex]

            const endPointIndex = this.pointList.length - 1
            const isEndPoint = newPointIndex === endPointIndex
            this.$refs.exchangeSuccessPopup.open(res, point, nextPoint, isEndPoint)
        },


        exchangeSuccessLookPointPopup(pointDetails) {
            if (this.activeDetails.conf.active.point_news_to_details_page_show) {
                return this.toReadPointNews(pointDetails)
            }

            const item = this.pointModuleList.find(item => item.point_id === pointDetails.id)
            this.clickPoint(item)
        },


        toReadPointNews(pointDetails) {
            const {id: point_id, news_id, exam_id} = pointDetails
            let url = `/pages/news/preview?id=${news_id}&active_id=${this.activeDetails.active_id}`

            if (this.activeDetails.conf.active.answer_need_read?.open && exam_id) {
                let seconds = Number(this.activeDetails.conf.active.answer_need_read.seconds)
                if (isNaN(seconds)) seconds = 0
                url += `&answer_need_read=1&point_id=${point_id}&exam_id=${exam_id}&answer_need_read_seconds=${seconds}`
            }

            this.$uni.navigateTo(url, {
                events: {
                    success: () => this.updateUserDetails()
                }
            })
        },

        toExamDetail(pointDetails) {
            if (!this.userDetails?.id) return this.noJoinTipsModal('未参与活动，无法答题。')

            this.$uni.navigateTo(`/pages/likou_dati/pages/exam/exam_details/exam_details?exam_id=${pointDetails.exam_id}&active_id=${this.activeDetails.active_id}&point_id=${pointDetails.id}`, {
                events: {
                    success: () => this.getMapInfo()
                }
            })


            // 雷子客户的活动，从答题成绩页点返回到地图页面，要隐藏点位知识弹窗
            if (this.activeDetails.active_id === '79e0836e3dad59acaa76130fd08f57ab') this.$refs.exchangeSuccessPopup.popupClose()
        },

        noJoinTipsModal(tips) {
            this.$uni.showModal(tips, {
                showCancel: true,
                confirmText: '参与活动',
                success: res => res.confirm && this.toJoinActivity()
            })
        },

        toLottery(pointDetails) {
            if (!pointDetails.conf?.lottery?.lottery_id) {
                this.$uni.showToast('没有配置抽奖')
                return
            }

            this.$uni.navigateTo(`/pages/lottery/user/lottery?id=${pointDetails.conf.lottery.lottery_id}&active_id=${this.activeDetails.active_id}&point_id=${pointDetails.id}`)
        },

        toTogetherPic(point_id) {
            uni.navigateTo({
                url: `/pages/activity/together-pic/make-pictures?id=${this.activeDetails.active_id}&point_id=${point_id}`,
                events: {
                    refresh: () => {
                        // 发布成功要关闭弹窗，不然弹窗里面的点位数据没有刷新，获取不到用户是否合成过照片
                        this.$refs.pointPopup.pointDetailsPopupClose()
                        this.$refs.exchangeSuccessPopup.popupClose()
                        this.getMapInfo()
                    }
                }
            })
        },


        async getRedpack(point_id) {
            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.userReward/arrive_point_rush_redpack',
                data: {
                    active_id: this.activeDetails.active_id,
                    point_id
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) {
                this.$uni.showModal(res?.info || '领取失败')
                return
            }

            await this.checkPointRepackStatus(point_id)
            this.$uni.showModal(res?.info || '领取成功')
        },

        async checkPointRepackStatus(point_id) {
            this.redpackCheckErrorInfo = 'loading'

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.userReward/check_point_rush_redpack_amount',
                data: {
                    active_id: this.activeDetails.active_id,
                    point_id
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) {
                this.redpackCheckErrorInfo = res?.info || '无法抢红包'
                return
            }

            this.redpackCheckErrorInfo = ''
        },

    }
}
</script>

<style lang="scss">
.diy-map__ {
    image {
        display: block;
    }

    .diy-map-container {
        position: relative;

        .diy-map-bg-img {
            width: 100%;
            display: block;
        }
    }
}
</style>