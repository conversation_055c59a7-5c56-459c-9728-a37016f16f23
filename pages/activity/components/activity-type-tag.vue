<template>
	<view class="activity-type-tag">
		<text 
			v-if="type_data"
			class="tag- font12"
			:style="{'color': type_data.tilte_color || '#80848f', 'border-color': type_data.tilte_color || '#80848f'}"
		>{{type_data.title}}</text>
	</view>
</template>

<script>
	import xwy_api from '@/utils/api/xwy_api.js'
	export default {
		name:"activity-type-tag",
		props: {
			type: {
				default: null
			}
		},
		data() {
			return {
				type_data: null
			}
		},
		mounted() {
			this.getCOnfSet()
		},
		methods: {
			async getCOnfSet() {
				const conf_set = await xwy_api.getActivityTypeConfSet()
				
				if (!conf_set) return false
				
				const {active_goods_list} = conf_set
				
				const type_data = active_goods_list.find(v => v.types === this.type)
				if (type_data) this.type_data = type_data
			}
		}
	}
</script>

<style>
.activity-type-tag {
	display: inline-block;
}
.tag- {
	border: 1px solid;
	padding: 0 2px;
	border-radius: 2px;
}
</style>
