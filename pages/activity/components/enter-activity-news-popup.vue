<template>
    <view>
        <uni-popup ref="popup" :is-mask-click="false" @touchmove.stop.prevent="">
            <view class="enter-activity-news-popup">
                <view class="news-title">{{ title }}</view>
                <scroll-view class="news-content" scroll-y="true">
                    <u-parse :content="content"/>
                </scroll-view>
                <view class="confirm-button-container flex-all-center">
                    <view class="confirm-button" @click="$refs.popup.close()">确定</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "enter-activity-news-popup",
    props: ['newsId'],
    data() {
        return {
            title: '',
            content: ''
        }
    },

    methods: {
        async open() {
            if (!this.newsId) return

            await this.getNewsContent()

            this.$nextTick(() => this.$refs.popup.open())
        },

        async getNewsContent() {
            const res = await this.xwy_api.request({
                url: 'front.news/news_details',
                data: {news_id: this.newsId}
            })

            const details = res?.data?.['news_details']
            if (!details) return

            this.title = details.title || ''
            this.content = this._utils.newsContentInit(details.content || '')
        }
    }
}
</script>

<style lang="scss" scoped>
.enter-activity-news-popup {
    width: 90vw;
    background-color: #fff;
    border-radius: 10px;

    .news-title {
        padding: 10px;
        text-align: center;
    }

    .news-content {
        max-height: 70vh;
        padding: 10px;
        margin-bottom: 10px;
        box-sizing: border-box;
    }

    .confirm-button-container {
        padding: 10px;

        .confirm-button {
            width: 200px;
            line-height: 44px;
            border-radius: 22px;
            text-align: center;
            background-color: #5cadff;
            color: #fff;
        }
    }
}
</style>