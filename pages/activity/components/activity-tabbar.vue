<template>
    <view class="tabbar flex-row" :style="tabBarStyle">
        <view
            class="item text-center"
            hover-class="navigator-hover"
            v-for="(item, index) in list"
            :key="index"
            :style="'width: calc(100% / ' + list.length + ');'"
            @click="diyPageClick(item, index)"
        >
            <image class="icon" :src="index === active ? item.selpic : item.pic"/>
            <view class="title font14" :style="'color: ' + (index === active ? select_color : color) + ';'">
                {{ item.title }}
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "activity-tabbar",
    props: {
        value: Object
    },
    data() {
        return {
            background_color: this.value.background_color || "#ffffff",
            color: this.value.color || "#80848f",
            select_color: this.value.select_color || "#0caef4",
            top_border_color: this.value.top_border_color || "#eeeeee",
            list: this.value.list || []
        }
    },
    
    computed: {
        active() {
            if (!this.xwy_config.activityTabBarInterconnect(this.$parent.id)) return -1
            const {options: {activity_total}, route} = this.$uni.page()
            const routeBase = 'pages/activity/user/'
            const routeTypeMap = {
                [`${routeBase}detail`]: 'activity_details_page',
                [`${routeBase}road-map/road_map`]: 'map_page',
                [`${routeBase}ranking_list`]: activity_total ? 'activity_total_ranking' : 'ranking_page'
            }
            return this.list.findIndex(v => v.type === routeTypeMap[route])
        },

        tabBarStyle() {
            return `
                background-color: ${this.background_color}; 
                border-top: 1px solid ${this.top_border_color}
            `
        }
    },
    
    methods: {
        diyPageClick(data, index) {
            if (index === this.active) return

            // 活动详情、路线图、排行榜、活跃度排行榜 4个页面tab栏联动(如后期需要联动其他页面，需要到对应页面配置tab栏组件及数据，配置详情参考路线图和排行榜页面)，但是需要在上面的配置代码配置活动id
            if (this.xwy_config.activityTabBarInterconnect(this.$parent.id)) {
                const interactive_pages_type = ['activity_details_page', 'map_page', 'ranking_page', 'activity_total_ranking']
                if (interactive_pages_type.includes(data.type)) return this.tabInteractive(data)

                // 从地图也和排行榜页面点击tab栏其他选项，返回活动详情页面并执行对应选项
                const pages = getCurrentPages()
                const details_page_route = 'pages/activity/user/detail'
                const index = pages.findIndex(v => v.route === details_page_route)
                this.$uni.navigateBack(pages.length - index - 1)
                return pages[index]['$vm'].diyPageClick(data)
            }

            this.$parent.diyPageClick(data)
        },

        tabInteractive(data) {
            const jump_route_list = {
                activity_details_page: {
                    route: 'pages/activity/user/detail',
                    params: `?id=${this.$parent.id}`
                },
                map_page: {
                    route: 'pages/activity/user/road-map/road_map',
                    params: `?show_tab=1&id=${this.$parent.id}`
                },
                ranking_page: {
                    route: 'pages/activity/user/ranking_list',
                    params: `?show_tab=1&id=${this.$parent.id}&user_team_id=${this.$parent.user_details?.team_id || 0}`
                },
                activity_total_ranking: {
                    route: 'pages/activity/user/ranking_list',
                    params: `?show_tab=1&id=${this.$parent.id}&user_team_id=${this.$parent.user_details?.team_id || 0}&activity_total=1`
                }
            }
            
            const pages = getCurrentPages()
            const { route, params } = jump_route_list[data.type]
            const paramsObj = this.params2obj(params)
            const index = pages.findIndex(v => v.route === route && this.rankingDiff(paramsObj, v.options))
            if (index !== -1) return this.$uni.navigateBack(pages.length - index - 1)

            this.$uni.navigateTo(`/${route}${params}`)
        },

        // 将字符串参数转为对象参数  ?id=1&name=2 => { id: 1, name: 2 }
        params2obj(params) {
            params.replace(/^\?/, '')
            const obj = {}
            params.split('&').forEach(v => {
                const [key, value] = v.split('=')
                obj[key] = value
            })
            return obj
        },

        rankingDiff(options1, options2) {
            // 为了解决普通排行榜和活跃度排行榜相互切换问题，通过是否有activity_total参数来判断
            // 如果目标页面和页面栈的某个页面参数都没有activity_total(普通排行榜)或者都有activity_total(活跃度排行榜)，则认为是同一个页面
            const key = 'activity_total'
            return options1.hasOwnProperty(key) === options2.hasOwnProperty(key)
            
            // 由于有的页面返回是获取不到某些参数的，所以不能对比options是否一样
        }
    }
}
</script>

<style scoped>
.tabbar {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100vw;
    z-index: 50;
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .tabbar {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }
}
/* #endif */

.item {
    padding-bottom: 15px;
}

.icon {
    width: 30px;
    height: 30px;
    position: relative;
    top: 3px;
}
</style>
