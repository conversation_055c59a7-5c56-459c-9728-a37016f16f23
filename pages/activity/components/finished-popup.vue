<template>
    <view v-if="show">
        <uni-popup ref="finished" type="center" @touchmove.stop.prevent="">
            <view>
                <view :class="reachTheEndPopupBackgroundImage.class_name">

                    <image class="finished-bg" :src="reachTheEndPopupBackgroundImage.img"/>
                    <view class="user-finished-detail">
                        <view v-if="headimgShow || nicknameShow" class="text-center">
                            <template v-if="headimgShow">

                                <image class="headimg-image" :src="headimg" mode="aspectFill"/>
                            </template>
                            <template v-if="nicknameShow">
                                <view class="color-title">{{ must_submit[0].value }}</view>
                            </template>
                        </view>
                        <view v-if="finishedNumShow" class="text-center color-content pt15">
                            <text>您是第</text>
                            <text class="color-red plr5">{{ finished_num }}</text>
                            <text>位到达终点的用户</text>
                        </view>
                        <view class="pt15 color-content">
                            <view v-if="reachTheEndPopupDataShow.kilo || reachTheEndPopupDataShow.signdays">
                                <template v-if="reachTheEndPopupDataShow.kilo">
                                    <text>总里程:</text>
                                    <text class="color-red pl5">{{ user_exchange_num }}</text>
                                    <text>{{ detail['conf'].active.kilo_unit || '里' }}</text>
                                    <text v-if="reachTheEndPopupDataShow.signdays" class="pl5">
                                        ({{ signdays }}天)
                                    </text>
                                </template>
                                <text v-if="!reachTheEndPopupDataShow.kilo && reachTheEndPopupDataShow.signdays">
                                    累计天数: {{ signdays }}天
                                </text>
                            </view>
                            <view v-if="reachTheEndPopupDataShow.finished_time" class="pt5">
                                <text>到达时间:</text>
                                <uni-dateformat
                                    class="pl5 color-red"
                                    :date="finished_time * 1000"
                                    format="yyyy-MM-dd hh:mm:ss"
                                    :threshold="[0, 0]"
                                />
                            </view>
                        </view>
                    </view>
                </view>
                <view class="flex-all-center" v-if="!detail['rank_set'] || !detail['rank_set'].closed_AD">
                    <xwy-ad :ad_type="66"></xwy-ad>
                    <xwy-ad :ad_type="3"></xwy-ad>
                </view>
                <view class="text-center">
                    <text class="iconfont icon-wrong color-white font24" @click="close"></text>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "finished-popup",
    props: {
        headimg: {
            type: String,
            default: ''
        },
        detail: {
            type: Object,
            default: () => {
                return {}
            }
        },
        must_submit: {
            type: Array,
            default: () => {
                return []
            }
        },
        finished_num: {
            type: Number,
            default: 0
        },
        finished_time: {
            type: Number | String,
            default: 0
        },
        signdays: {
            type: Number,
            default: 0
        },
        user_exchange_num: {
            type: Number,
            default: 0
        }
    },
    
    computed: {
        headimgShow() {
            return this.headimg && this.reachTheEndPopupDataShow.headimg
        },
        nicknameShow() {
            return this.reachTheEndPopupDataShow.nickname && this.must_submit?.[0]?.value
        },
        finishedNumShow() {
            return this.reachTheEndPopupDataShow.finished_num && this.detail?.conf?.active?.arrive_then_end
        }
    },
    
    data() {
        return {
            show: false,
            reachTheEndPopupDataShow: {},
            reachTheEndPopupBackgroundImage: {}
        }
    },

    methods: {
        open() {
            const {close_end_popup = 0} = this.detail?.conf?.active || {}
            if (!this.finished_time || close_end_popup === 1) return

            // 只弹一次到达终点弹窗
            if (close_end_popup === 2 && uni.getStorageSync(`finished_popup_${this.detail.active_id}`)) return

            this.getReachTheEndPopupData()
            this.show = true
            this.$nextTick(() => {
                if (close_end_popup === 2) uni.setStorageSync(`finished_popup_${this.detail.active_id}`, true)
                this.$refs.finished.open()
            })
        },

        close() {
            this.$refs.finished.close()
            this.$nextTick(() => {
                this.show = false
            })
        },
        
        getReachTheEndPopupData() {
            const id = this.detail.active_id
            this.reachTheEndPopupDataShow = this.xwy_config.reachTheEndPopupDataShow(id)
            this.reachTheEndPopupBackgroundImage = this.xwy_config.reachTheEndPopupBackgroundImage(id)
        }
    }
}
</script>

<style lang="scss">
.finished-main, .finished-main-2 {
    position: relative;
    
    .user-finished-detail {
        position: absolute;
    }
}

.finished-main {
    width: 320px;
    height: 400px;

    .finished-bg {
        width: 320px;
        height: 400px;
        display: block;
    }

    .user-finished-detail {
        position: absolute;
        top: 120px;
        left: 35px;
        width: 243px;
    }

    .headimg-image {
        width: 80px;
        height: 80px;
        border-radius: 50%;
    }
}

.finished-main-2 {
    width: 320px;
    height: 409px;

    .finished-bg {
        width: 320px;
        height: 409px;
    }
    
    .user-finished-detail {
        width: 235px;
        top: 295px;
        left: 50px;
        text-align: center;
    }
}


</style>