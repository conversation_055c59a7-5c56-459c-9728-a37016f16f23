<template>
    <view class="day7-step-template-cumulative-data__container flex-kai">
        <view class="cumulative-data-item__">
            <view class="bg___"></view>
            <view class="data___">
                <view class="pb10">累计天数</view>
                <view class="flex-kai">
                    <view>
                        <text class="iconfont icon-walk"></text>
                    </view>
                    <view class="value___">
                        <text>{{ days }}</text>
                        <text class="unit___">天</text>
                    </view>
                </view>
            </view>
        </view>

        <view class="cumulative-data-item__">
            <view class="bg___"></view>
            <view class="data___">
                <view class="pb10">累计运动</view>
                <view class="flex-kai">
                    <view>
                        <text class="iconfont icon-walk"></text>
                    </view>
                    <view class="value___">
                        <text>{{ kilometerText }}</text>
                        <text class="unit___">{{ unit }}</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "cumulative-data",
    props: ['days', 'kilometer', 'unit'],
    computed: {
        kilometerText() {
            let kilometer = this.kilometer
            if (kilometer >= 10000) kilometer = Math.floor(kilometer / 1000) / 10 + '万'
            return kilometer
        }
    }
}
</script>

<style lang="scss" scoped>
.day7-step-template-cumulative-data__container {
    .cumulative-data-item__ {
        position: relative;
        width: calc(50% - 20rpx);

        .bg___ {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
            width: 100%;
            height: 100%;
            border-radius: 10px;
            background-color: #94CCF8;
            transform: rotate(-5deg);
        }

        .data___ {
            position: relative;
            z-index: 2;
            background-color: #D3ECF7;
            padding: 30rpx;
            border-radius: 10px;
            font-weight: bold;

            .iconfont {
                font-size: 36rpx;
                color: #94CCF8;
            }

            .value___ {
                font-size: 38rpx;
                color: #F72841;

                .unit___ {
                    font-size: 28rpx;
                    padding-left: 2px;
                }
            }
        }
    }
}
</style>