<template>
    <view class="right-icons__container">
        <view v-if="isAdmin" class="right-icons__item" @click="$emit('toManage')">
            <text class="iconfont icon-setting"></text>
        </view>

        <view v-if="isJoining" class="right-icons__item" @click="$emit('showMyInfo')">
            <text class="iconfont icon-personal-data"></text>
        </view>

        <view v-if="ranklistShow" class="right-icons__item" @click="$emit('toTopList')">
            <text class="iconfont icon-trophy"></text>
        </view>

        <view v-if="activeExplanationShow" class="right-icons__item" @click="$emit('showActiveExplanation')">
            <text class="iconfont icon-feedback"></text>
        </view>

        <view v-if="showShare" class="right-icons__item" @click="$emit('showActiveSharePopup')">
            <text class="iconfont icon-share"></text>
        </view>
    </view>
</template>

<script>
const app = getApp()

export default {
    name: "right-icons-container",
    emits: ['toManage', 'showMyInfo', 'toTopList', 'showActiveExplanation', 'showActiveSharePopup'],
    props: ['activeDetails', 'userDetails'],

    data() {
        return {}
    },

    computed: {
        isAdmin() {
            return app.globalData.userid === this.activeDetails?.userid
        },

        isJoining() {
            return !!this.userDetails?.id
        },

        ranklistShow() {
            if (this.activeDetails?.rank_set?.closed_top_rank) return false
            if (this.activeDetails?.conf?.active?.only_join_user_look_ranklist) {
                if (!this.userDetails?.checked) return false
            }

            return true
        },

        activeExplanationShow() {
            return !this.activeDetails?.conf?.active?.activity_rules_hide || this.activeDetails?.content || this.activeDetails?.conf?.active?.news?.news_id
        },

        showShare() {
             return !this.activeDetails?.rankSet?.share_closed
        }
    }
}
</script>

<style lang="scss" scoped>
.right-icons__container {
    position: fixed;
    right: 30rpx;
    top: 5vh;

    .right-icons__item {
        width: 40px;
        height: 40px;
        line-height: 40px;
        border-radius: 50%;
        text-align: center;
        background-color: rgba(0, 0, 0, .5);
        margin-bottom: 5px;

        .iconfont {
            font-size: 24px !important;
            color: #fff !important;
        }
    }
}
</style>