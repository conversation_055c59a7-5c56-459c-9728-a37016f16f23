<template>
    <view class="day7-step-template__container" :style="{backgroundImage: `url(${backgroundImage})`}">
        <view class="data-view__container">
            <right-icons-container
                :active-details="activeDetails"
                :user-details="userDetails"
                @toManage="$emit('toManage')"
                @showMyInfo="$emit('showMyInfo')"
                @toTopList="$emit('toTopList')"
                @showActiveExplanation="$emit('showActiveExplanation')"
                @showActiveSharePopup="$emit('showActiveSharePopup')"
            />

            <today-step-container :today-step="todayStep" :is-join="isJoin" :day-min-step="dayMinStep"
                                  @exchange="exchange" @join="$emit('join')"/>

            <view style="height: 20px;"></view>

            <day7-step-container ref="day7StepContainer" :active-id="activeId"
                                 @lookMoreStep="$emit('lookMoreStep')"/>

            <view style="height: 20px;"></view>

            <cumulative-data :days="exchangeDays" :kilometer="exchangeKilo" :unit="unit"/>

            <view v-if="showAd" class="pt15">
                <xwy-ad :ad_type="66" :activity_id="activeId"/>
            </view>
        </view>
    </view>
</template>

<script>
import todayStepContainer from './today-step-container.vue'
import day7StepContainer from './day7-step-container.vue'
import cumulativeData from './cumulative-data.vue'
import rightIconsContainer from './right-icons-container.vue'

export default {
    name: "day7-step-template",
    components: {todayStepContainer, day7StepContainer, cumulativeData, rightIconsContainer},
    emits: ['lookMoreStep', 'exchange', 'join', 'toManage', 'showMyInfo', 'toTopList', 'showActiveExplanation', 'showActiveSharePopup'],
    props: {
        activeId: {
            type: String,
            default: ''
        },
        activeDetails: {
            type: Object,
            default: () => {}
        },
        todayStep: {
            type: [Number, null],
            default: null
        },
        userDetails: {
            type: Object,
            default: () => {}
        },
        unit: {
            type: String,
            default: '步'
        },
        isJoin: {
            type: Boolean,
            default: false
        },
        backgroundImage: {
            type: String,
            default: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/diy_template/jybg.jpg'
        }
    },

    computed: {
        exchangeDays() {
            return this.userDetails?.sign_day || 0
        },

        exchangeKilo() {
            return this.userDetails?.exchange_kilo || 0
        },

        dayMinStep() {
            return this.activeDetails?.conf?.active?.min_num || 0
        },

        showAd() {
            return !this.activeDetails?.rank_set?.closed_AD
        }
    },
    
    watch: {
        isJoin(val, oldVal) {
            if (oldVal === false && val === true) this.exchange()
        }
    },

    methods: {
        exchange() {
            this.$emit('exchange', 'update')
        },

        exchangeSuccess(res) {
            // 兑换失败，并且不是提示步数没有增加的，需要提示用户兑换失败的原因
            if (res?.status !== 1 && !res?.data?.notPop) {
                this.$uni.showModal(res?.info || '兑换失败')
                return
            }
            this.$refs.day7StepContainer.getWeekExchangeStep()
        }
    }
}
</script>

<style lang="scss" scoped>
.day7-step-template__container {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    position: relative;
    /*背景图片宽度100%，高度截取*/
    background-size: 100% auto;
    background-repeat: repeat;
    background-position: center top;

    .data-view__container {
        position: absolute;
        left: 12rpx;
        bottom: 20px;
        width: 726rpx;
        padding: 30rpx 16rpx;
        box-sizing: border-box;
        border-radius: 10px;
        background-color: #ffffff;
    }
}
</style>