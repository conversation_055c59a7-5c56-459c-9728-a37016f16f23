<template>
    <view class="today-step__container">
        <view class="color-title font24 font-bold">今日步数</view>
        <view class="flex-kai pt10">
            <view :class="['font-bold ', todayStep >= 10000 ? 'color-warring' : 'color-green']"
                :style="{ opacity: todayStep === null ? 0 : 1 }">
                <text class="font28">{{ todayStep }}</text>
                <text>步</text>
            </view>
            <view class="flex-all-center">
                <view v-if="isJoin" class="exchange-button" @click="$emit('exchange')">更新步数</view>
                <view v-else class="exchange-button" @click="$emit('join')">参与活动</view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "today-step-container",
    emits: ['exchange', 'join'],
    props: ['todayStep', 'isJoin', 'dayMinStep']
}
</script>

<style lang="scss" scoped>
.exchange-button {
    width: 120px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
    text-align: center;
    color: #fff;
    background: linear-gradient(to right, #F93658, #FA5E1C);
    box-shadow: 0 4px 8px rgba(250, 94, 28, 0.5);
}
</style>