<template>
    <view class="day7-step__container">
        <view class="year-month">{{ year }}年{{ month }}月</view>
        <view class="day7-step__list flex-kai">
            <view class="step-day__item" :class="{'step-day__item-have-step': item.step}"
                  v-for="item in weekStep" :key="item.timestamp">
                <view class="step-day__item-date">{{ item.day }}</view>
                <view :class="['step-day__item-step ', todayStep >= 10000 ? 'color-warring' : 'color-green']">
                    {{ item.step || '' }}
                </view>
            </view>
        </view>

        <view class="flex-all-center">
            <view class="look-more-step__" @click="$emit('lookMoreStep')">
                <text class="font14">查看更多</text>
                <uni-icons type="forward" size="14" color="#ffffff"/>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "day7-step-container",
    props: ['activeId'],
    emits: ['lookMoreStep'],
    data() {
        return {
            year: 0,
            month: 0,
            weekStep: this._utils.getWeekDate(this._utils.getDay(0, true, '/'), true, '-').map(date => ({
                date,
                day: this._utils.getYearMonthDay(date).day,
                step: 0,
                timestamp: new Date(date.replace(/-/g, '/')).getTime()
            })),
        }
    },

    mounted() {
        this.getYearMonth()
        this.getWeekExchangeStep()
    },

    methods: {
        getYearMonth() {
            const {year, month} = this._utils.getYearMonthDay()
            this.year = year
            this.month = month
        },

        async getWeekExchangeStep() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exchange/exchange_step_list',
                data: {
                    active_id: this.activeId,
                    page: 1,
                    perpage: new Date().getDay() || 7
                }
            })

            const stepList = res?.data?.exchange_list?.data || []

            stepList.forEach(item => {
                item.exchange_date = this._utils.beijingTimestamp2Local(item.exchange_date)
            })

            stepList.forEach(item => {
                const date = this.weekStep.find(v => v.timestamp === item.exchange_date * 1000)
                if (date) date.step = item.step
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.day7-step__container {
    background-color: #bedcfd;
    border-bottom: 4px solid #93CBF6;
    border-radius: 10px;
    overflow: hidden;

    .year-month {
        text-align: center;
        color: #fff;
        background-color: #7bb5f3;
        padding: 6px 0;
    }

    .day7-step__list {
        padding: 10px 8rpx;

        .step-day__item {
            text-align: center;
            background-color: #fff;
            border-radius: 10px;
            padding: 10px 0;
            width: calc(100% / 7 - 10rpx);

            .step-day__item-date {
                color: #35241f;
                padding-bottom: 5px;
                font-size: 36rpx;
                font-weight: bold;
            }

            .step-day__item-step {
                font-size: 24rpx;
            }

            &.step-day__item-have-step {
                border: 4rpx solid #93CBF6;
                margin-top: -2rpx;
            }
        }
    }

    .look-more-step__ {
        color: #ffffff;
        padding: 0 10px 10px;
    }
}
</style>