<template>
    <view>
        <uni-popup ref="popup">
            <view class="daily-step-unlock-exam-popup__ bg-white radius10 text-center">
                <view class="daily-step-unlock-exam-popup__user-info__headimg">
                    <image v-if="headimg" class="daily-step-unlock-exam-popup__user-info__headimg-img"
                           :src="headimg" mode="aspectFill"/>
                </view>
                <view class="color-title font18 pt5">{{ nickname || '' }}</view>
                <view class="color-content pt10">今日答题: {{ useCounts }}/{{ allCounts }}</view>

                <view class="daily-step-unlock-exam-popup__button-container flex-all-center">
                    <view class="daily-step-unlock-exam-popup__button" hover-class="navigator-hover"
                          @click="toExam">
                        去答题
                    </view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "daily-step-unlock-exam-popup",
    emits: ['answerEnd'],
    props: {
        activeId: {
            type: String,
            default: ''
        },
        examId: {
            type: String,
            default: ''
        },
        perStep: {
            type: Number,
            default: 0
        },
        headimg: {
            type: String,
            default: ''
        },
        nickname: {
            type: String,
            default: ''
        },
        maxIntegral: {
            type: Number,
            default: 0
        },
        maxIntegralTips: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            allCounts: 0,
            useCounts: 0
        }
    },


    methods: {
        async open() {
            this.$uni.showLoading()
            const today_step = await this.getUserTodayExchangeStep()
            this.allCounts = Math.floor(today_step / this.perStep)

            if (!this.allCounts) {
                uni.hideLoading()
                this.$uni.showToast(`需兑换 ${this.perStep}步 才能答题`)
                return
            }

            this.useCounts = await this.getUserTodayAnsweredCounts()
            uni.hideLoading()


            this.$refs.popup.open()
        },

        async getUserTodayExchangeStep() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exchange/exchange_step_list',
                data: {
                    active_id: this.activeId,
                    exchange_date: this._utils.getDay(0, true)
                }
            })

            return res?.data?.exchange_list?.data?.[0]?.step || 0
        },

        async getUserTodayAnsweredCounts() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exam.sportExam/user_today_exam_details',
                data: {
                    active_id: this.activeId,
                    get_today_exam_count: 1
                }
            })

            return res?.data?.have_exam_count || 0
        },

        async toExam() {
            if (this.useCounts >= this.allCounts) {
                this.$uni.showToast('暂无答题次数，快去兑换步数吧')
                return
            }

            if (this.maxIntegral && this.maxIntegralTips) {
                const todayAnsweredIntegral = await this.getUserTodayAnsweredIntegral()
                if (todayAnsweredIntegral && todayAnsweredIntegral >= this.maxIntegral) {
                    const {confirm} = await this.$uni.showModal(this.maxIntegralTips, {showCancel: true})
                    if (!confirm) return
                }
            }

            this.$uni.navigateTo(`/pages/likou_dati/pages/answer/answer_question/answer_question?exam_id=${this.examId}&activityID=${this.activeId}&task_activity_step_answer=1`, {
                events: {
                    success: () => this.$emit('answerEnd')
                }
            })

            this.$refs.popup.close()
        },

        async getUserTodayAnsweredIntegral() {
            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.userActiveIntegral/active_integral_list',
                data: {
                    active_id: this.activeId,
                    integral_types: 9,   // 只查答题获得的积分
                    date: this._utils.getDay(0, true),  // 只查今天的积分
                    total_integral: 1,   // 让接口返回积分总数
                    not_get_list: 1      // 不需要返回积分明细列表
                }
            })
            uni.hideLoading()

            return res?.data?.total_integral || 0
        }
    }
}
</script>

<style lang="scss" scoped>
.daily-step-unlock-exam-popup__ {
    width: 600rpx;
    max-width: 400px;
    padding: 30rpx;
    box-sizing: border-box;

    .daily-step-unlock-exam-popup__user-info__headimg {
        width: 160rpx;
        height: 160rpx;
        border-radius: 50%;
        overflow: hidden;
        margin: 0 auto;
        background-color: #f8f8f8;

        .daily-step-unlock-exam-popup__user-info__headimg-img {
            width: 160rpx;
            height: 160rpx;
            border-radius: 50%;
            display: block;
        }
    }

    .daily-step-unlock-exam-popup__button-container {
        padding-top: 20px;

        .daily-step-unlock-exam-popup__button {
            width: 160px;
            height: 44px;
            line-height: 44px;
            border-radius: 22px;
            background-color: #5cadff;
            color: #fff;
            text-align: center;
        }
    }
}
</style>