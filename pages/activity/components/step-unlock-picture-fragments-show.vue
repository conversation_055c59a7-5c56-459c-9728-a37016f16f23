<template>
    <view class="supfs">
        <!--最顶部放一个高度1px的容器，避免没有logo的时候下面.supfs-container塌陷-->
        <view style="min-height: 1px;">
            <image class="supfs-logo" v-if="logo" :src="logo" mode="widthFix"/>
        </view>

        <view class="supfs-container">
            <view class="font18 color-title">步数解锁拼图</view>
            <view class="color-sub font14 pt5 pb10">
                {{ set.mileage }}{{ kiloUnit }}解锁1张碎片, 解锁{{ allCount }}张碎片完成拼图
            </view>

            <view class="supfs-image-container flex-row flex-wrap" @click="lookImage">
                <view class="supfs-image-item-container" v-for="(item, index) in imageMapList" :key="item.id"
                      :style="item.containerStyle">
                    <image class="supfs-image-item" :src="set.image_src" mode="aspectFill"
                           :class="{'supfs-image-lock': index >= unlockCount}"
                           :style="item.imageStyle"/>
                </view>
            </view>

            <view class="supfs-unlock-container flex-row">
                <template v-if="unlockCount < allCount">
                    <view class="supfs-unlock-count flex-all-center">
                        <view class="color-sub text-center">
                            <view>{{ unlockCount }}/{{ allCount }}</view>
                            <view>已解锁</view>
                        </view>
                    </view>
                    <view class="supfs-unlock-button-container flex-all-center">
                        <view class="w-100">
                            <view v-if="everydayMaxCount" class="text-center color-sub font14 pb5">
                                一天最多解锁{{ everydayMaxCount }}张碎片
                            </view>
                            <view class="supfs-unlock-button" hover-class="navigator-hover"
                                  @click="unlock">解锁
                            </view>
                        </view>
                    </view>
                </template>
                <view v-else class="supfs-unlock-button-container flex-all-center" style="padding-left: 0;">
                    <view class="supfs-unlock-button" style="width: 200px;" @click="completedUnlock">已完成</view>
                </view>
            </view>
        </view>


        <view class="supfs-icon-list">

            <navigator v-if="isAdmin" class="supfs-icon-item" :url="'../admin/manage?id=' + activeId">
                <text class="iconfont icon-setting"></text>
            </navigator>

            <view v-if="isJoin" class="supfs-icon-item" hover-class="navigator-hover"
                  @click="$emit('uniPopupOpen', 'my_info')">
                <text class="iconfont icon-personal-data"></text>
            </view>

            <view v-if="activityDescriptionShow"
                  class="supfs-icon-item" hover-class="navigator-hover"
                  @click="$emit('uniPopupOpen', 'activity_detail')">
                <text class="iconfont icon-feedback"></text>
            </view>

            <view v-if="ranklistShow" class="supfs-icon-item" hover-class="navigator-hover"
                  @click="$emit('toTopList')">
                <text class="iconfont icon-trophy"></text>
            </view>

            <view v-if="exchangeCalendarShow" class="supfs-icon-item" hover-class="navigator-hover"
                  @click="$emit('toExchangeCalendar')">
                <text :class="'iconfont icon-calendar-' + today_day"></text>
            </view>


            <navigator v-if="rank_set['open_sport_moment']" class="supfs-icon-item"
                       :url="'/pages/comment/list?active_id=' + activeId">
                <text class="iconfont icon-wechat-movement"></text>
            </navigator>


            <navigator v-if="takeOathShow" class="supfs-icon-item"
                       :url="'/pages/take-oath/list?active_id=' + activeId">
                <text class="iconfont icon-flag"></text>
            </navigator>


            <navigator v-if="rank_set['face_swap'] && isJoin" class="supfs-icon-item"
                       :url="'/pages/other/ai-face?active_id=' + activeId">
                <uni-icons type="images" size="24" color="#ffffff"/>
            </navigator>

            <navigator v-if="!rank_set['closed_user_center']" class="supfs-icon-item" url="/pages/user/user">
                <uni-icons type="person" size="24" color="#ffffff"/>
            </navigator>

            <view v-if="!rank_set['share_closed']" class="supfs-icon-item" hover-class="navigator-hover"
                  @click="$emit('showActiveSharePopup')">
                <text class="iconfont icon-share"></text>
            </view>
        </view>
    </view>
</template>

<script>
/**
 * @description 步数解锁拼图用户端显示组件
 * @property {String}    activeId                活动id
 * @property {Object}    rankSet                 OA开启的功能
 * @property {Boolean}   isJoin                  是否参与活动
 * @property {String}    logo                    活动主图
 * @property {Number}    userMileage             用户当前里程
 * @property {Object}    set                     拼图碎片设置信息
 * @property {Number}    set.row                 拼图碎片行数
 * @property {Number}    set.col                 拼图碎片列数
 * @property {String}    set.image_src           拼图碎片图片地址
 * @property {String}    set.mileage             多少里程解锁1碎片
 * @property {Number}    everydayMaxStep         活动设置的每日兑换步数上限
 * @property {Number}    exchangeRate            兑换比例
 * @property {Number}    kiloUnit                里程单位
 * @property {Boolean}   isAdmin                 是不是活动管理员
 * @property {Boolean}   ranklistShow            是否显示排行榜入口
 * @property {Boolean}   exchangeCalendarShow    是否显示兑换日历入口
 * @property {Boolean}   activityDescriptionShow 是否显示活动说明入口
 * @property {Boolean}   takeOathShow            是否显示宣誓词入口
 * @property {Boolean}   arriveThenEnd           是否解锁完所有碎片不能继续兑换
 * @event unlock               点击解锁按钮
 * @event uniPopupOpen         弹窗打开
 * @event toTopList            点击排行榜入口
 * @event toExchangeCalendar   点击兑换日历入口
 * @event showActiveSharePopup 点击分享入口
 */

export default {
    name: "step-unlock-picture-fragments-show",
    emits: ['unlock', 'uniPopupOpen', 'toTopList', 'toExchangeCalendar','showActiveSharePopup'],
    props: ['activeId', 'rankSet', 'isJoin', 'logo', 'userMileage', 'set', 'everydayMaxStep', 'exchangeRate', 'kiloUnit', 'isAdmin', 'ranklistShow', 'exchangeCalendarShow', 'activityDescriptionShow', 'takeOathShow', 'arriveThenEnd'],
    data() {
        return {
            imageMapList: [],
            today_day: new Date().getUTCDate()
        }
    },

    computed: {
        // 已解锁的碎片数量
        unlockCount() {
            return Math.floor(this.userMileage / this.set.mileage)
        },

        // 碎片总数 行*列
        allCount() {
            return this.set.col * this.set.row
        },

        // 每天可兑换的碎片数量上限 = 每日兑换步数上限 / 兑换比例 / 一张碎片需要的里程
        everydayMaxCount() {
            if (!this.everydayMaxStep) return null
            return Math.floor(this.everydayMaxStep / this.exchangeRate / this.set.mileage)
        },
        
        rank_set() {
            return this.rankSet || {}
        }
    },

    watch: {
        unlockCount(val) {
            this.showUnlockFragmentsPopup(val)
        }
    },

    mounted() {
        this.initImageMap()
    },

    methods: {
        async initImageMap() {
            const {row, col} = this.set
            const imgInfo = await this.$uni.getImageInfo(this.set.image_src)
            const {width, height} = imgInfo
            const imgWidth = uni.getWindowInfo().windowWidth - 40
            const imgHeight = height * (imgWidth / width)

            // 向下取整，避免四舍五入后超出最大的大小导致样式错乱
            const fragmentWidth = Math.floor(imgWidth / col)
            const fragmentHeight = Math.floor(imgHeight / row)

            this.imageMapList = Array.from({length: this.allCount}, (_, i) => ({
                id: i,
                containerStyle: `width: ${fragmentWidth}px; height: ${fragmentHeight}px;`,
                imageStyle: `
                     width: ${imgWidth}px;
                    height: ${imgHeight}px;
                      left: -${i % col * fragmentWidth}px;
                       top: -${Math.floor(i / col) * fragmentHeight}px;
                `
            }))
        },

        completedUnlock() {
            console.log(this.arriveThenEnd);
            if (this.arriveThenEnd) return
            this.$emit('unlock')
        },


        unlock() {
            // 兑换前已解锁的碎片数量，用于兑换成功后计算新解锁多少张碎片
            this.unlockBeforeCount = this.unlockCount
            this.$emit('unlock')
        },

        showUnlockFragmentsPopup(count) {
            if (this.unlockBeforeCount === undefined) return
            const newUnlockCount = count - this.unlockBeforeCount
            if (newUnlockCount > 0) this.$uni.showToast(`新解锁${newUnlockCount}张碎片`)
        },

        lookImage() {
            this.$uni.previewImage(this.set.image_src)
        }
    }
}
</script>

<style lang="scss" scoped>
.supfs {
    min-height: 100vh;
    background-color: #FFD28B;
    padding-bottom: 10px;
    box-sizing: border-box;
}

.supfs-logo {
    width: 100%;
    display: block;
}

.supfs-container {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
    background-color: #FEE4C1;
}

.supfs-image-container {
    border-radius: 10px;
    overflow: hidden;
}

.supfs-image-item-container {
    border: 1px solid #FEE4C1;
    box-sizing: border-box;
    overflow: hidden;

    .supfs-image-item {
        display: block;
        position: relative;
    }

    .supfs-image-lock {
        filter: grayscale(1);
    }
}

.supfs-unlock-container {
    padding-top: 20px;

    .supfs-unlock-count {
        width: 80px;
        min-width: 80px;
        height: 80px;
        border-radius: 50%;
        border: 2px solid #FFD28B;
        box-sizing: border-box;
        box-shadow: inset 0 0 20px #FFD28B;
    }

    .supfs-unlock-button-container {
        height: 80px;
        width: 100%;
        padding-left: 20px;
        box-sizing: border-box;

        .supfs-unlock-button {
            line-height: 44px;
            border-radius: 22px;
            text-align: center;
            color: #fff;
            background-color: #EC783D;
        }
    }
}

.supfs-icon-list {
    position: fixed;
    top: 60px;
    right: 10px;

    .supfs-icon-item {
        width: 40px;
        height: 40px;
        text-align: center;
        line-height: 40px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.5);
        margin-bottom: 5px;

        .iconfont {
            font-size: 24px;
            color: #fff;
        }
    }
}
</style>