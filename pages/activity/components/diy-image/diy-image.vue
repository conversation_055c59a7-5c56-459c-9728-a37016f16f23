<template>
    <view class="container" :style="containerSizeStyle">
        <image class="image" :src="bgPath" :style="containerSizeStyle"/>

        <view v-for="item in list" :key="item.key" class="item"
              :style="{top: item.data.y + 'px', left: item.data.x + 'px'}"
              @click="diyPageClick(item.data)">
            <today-step v-if="item.type === 'step'" :data="item.data" :step="step"/>
            <view v-if="item.type === 'space'"
                  :style="{width: item.data.width + 'px', height: item.data.height + 'px'}"></view>
            <image v-if="item.type === 'image'" class="image" :src="item.data.src"
                   :style="{width: item.data.width + 'px', height: item.data.height + 'px'}"/>
        </view>
    </view>
</template>

<script>
import todayStep from './today-step.vue'
export default {
    name: "diy-image",
    components: {todayStep},
    emits: ['diyPageClick'],
    props: ['width', 'height', 'bgPath', 'list', 'step'],
    
    computed: {
        containerSizeStyle() {
            return `width: ${this.width}px; height: ${this.height}px;`
        }
    },
    
    methods: {
        diyPageClick(data) {
            this.$emit('diyPageClick', data)
        }
    }
}
</script>

<style lang="scss" scoped>
.container {
    position: relative;
}

.image {
    display: block;
}

.item {
     position: absolute;
 }
</style>