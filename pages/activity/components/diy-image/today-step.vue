<template>
    <view class="step-container text-center flex-all-center" :style="style.container">
        <view class="top-bottom-text" :style="style.topText">{{ data.top_text || '' }}</view>
        <view :style="style.step">{{ step || 0 }}</view>
        <view class="top-bottom-text" :style="style.bottomText">{{ data.bottom_text || '' }}</view>
    </view>
</template>

<script>
// 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
/** @namespace data.top_text */
/** @namespace data.bottom_text */

export default {
    name: "today-step",
    props: ['data', 'step'],
    computed: {
        style() {
            const {width, height, bg_color, step_size, step_color, top_text_color, top_text_size, bottom_text_color, bottom_text_size} = this.data
            
            const container = `width: ${width}px; height: ${height}px; background-color: ${bg_color}`
            
            const stepHeight = step_size + 4
            const step = `color: ${step_color}; font-size: ${step_size}px; height: ${stepHeight}px; line-height: ${stepHeight}px;`
            
            const topHeight = top_text_size + 4
            const topText = `color: ${top_text_color}; font-size: ${top_text_size}px; height: ${topHeight}px; line-height: ${topHeight}px; top: ${height / 2 - stepHeight / 2 - topHeight}px;`
            
            const bottomHeight = bottom_text_size + 4
            const bottomText = `color: ${bottom_text_color}; font-size: ${bottom_text_size}px; height: ${bottomHeight}px; line-height: ${bottomHeight}px; bottom: ${height / 2 - stepHeight / 2 - bottomHeight}px;`
            
            return {
                container,
                topText,
                step,
                bottomText
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.step-container {
    border-radius: 50%;
    position: relative;
    
    .top-bottom-text {
        position: absolute;
        left: 0;
        width: 100%;
    }
}
</style>