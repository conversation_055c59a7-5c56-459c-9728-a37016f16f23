<template>
    <view>
        <view class="canvas-container"
              :style="'width: ' + canvas_width + 'px; height: ' + canvas_height + 'px;'">
            <canvas
                class="canvas"
                canvas-id="canvas"
                id="canvas"
                :style="'width: ' + canvas_width + 'px; height: ' + canvas_height + 'px;'"
                @error="canvasError"
            ></canvas>

            <uni-popup ref="popup" @touchmove.stop.prevent="" :is-mask-click="false"
                       mask-background-color="rgba(0,0,0,0.7)">

                <image class="big-img" :src="img_src" mode="widthFix" @click="previewImage"/>
                <view class="flex-all-center p10">
                    <view class="save-button text-center bg-light-primary color-white" @click="saveImage">
                        <uni-icons type="download" size="16" color="#ffffff"/>
                        <text class="pl5">保存图片</text>
                    </view>
                </view>
                <view class="close flex-all-center">
                    <uni-icons type="close" size="28" color="#ffffff" @click="close"/>
                </view>
            </uni-popup>
        </view>
    </view>
</template>

<script>
let ctx = null

export default {
    name: "every-day-certificate-popup",
    data() {
        return {
            img_src: '',
            canvas_width: 0,
            canvas_height: 0,
        }
    },

    mounted() {
        ctx = uni.createCanvasContext('canvas', this)
    },

    methods: {
        canvasError(e) {
            console.log('canvas_error:', e)
        },

        close() {
            if (this.closeFn) this.closeFn()
            this.$refs.popup.close()
        },

        async show(data) {
            const {nickname, certificate_set, close} = data
            const {background_image, text} = certificate_set || {}
            if (!background_image) {
                close()
                return this.$uni.showModal('证书数据获取失败')
            }

            const image_info = await this.$uni.getImageInfo(background_image)
            const {path, width, height} = image_info || {}
            if (!path) {
                close()
                return this.$uni.showModal('证书背景图片获取失败')
            }

            this.closeFn = close

            this.nickname = nickname

            this.canvas_width = width
            this.canvas_height = height

            this.data_list = text || []

            this.drawCanvas(path)
        },

        drawCanvas(path) {
            this.$uni.showLoading('图片生成中...')

            // 画背景图
            ctx.drawImage(path, 0, 0, this.canvas_width, this.canvas_height)

            const data_list = this.data_list

            // 画证书里面的文字和图片等内容
            data_list.forEach((v, i) => {
                v = this.initDataItem(v)

                const is_last = i === data_list.length - 1

                if (v.type === 'text') this.drawText(v, is_last)
                if (v.type === 'line') this.drawLine(v, is_last)
                if (v.type === 'image') this.drawImage(v, is_last)
            })
        },

        initDataItem(v) {
            v.left ||= 0
            v.top ||= 0
            v.color ||= '#000000'

            v.align ||= 'left'
            if (v.align === 'right') v.right ||= 0

            // 线条和图片的居中和居右
            if (v.type === 'line' || v.type === 'image') {
                v.width ||= 0
                v.height ||= 0
                if (v.align === 'center') v.left = this.canvas_width / 2 - v.width / 2
                if (v.align === 'right') v.left = this.canvas_width - v.right - v.width
            }

            return v
        },

        drawText(v, is_last) {
            if (v['is_nickname']) v.text = this.nickname
            if (v.align === 'center') v.left = this.canvas_width / 2
            if (v.align === 'right') v.left = this.canvas_width - v.right
            ctx.setTextAlign(v.align)
            ctx.setFontSize(v.size || 16)
            ctx.fillStyle = v.color
            ctx.fillText(v.text, v.left, v.top)

            is_last && this.draw()
        },

        drawLine(v, is_last) {
            ctx.beginPath()
            ctx.setStrokeStyle(v.color)
            ctx.setLineWidth(v.height || 1)
            ctx.moveTo(v.left, v.top)
            ctx.lineTo(v.left + v.width, v.top)
            ctx.stroke()

            is_last && this.draw()
        },

        async drawImage(v, is_last) {
            const {src, left, top, width, height} = v
            const image_info = await this.$uni.getImageInfo(src)
            if (image_info?.path) ctx.drawImage(image_info.path, left, top, width, height)
            is_last && this.draw()
        },

        draw() {
            ctx.draw(true, () => {
                setTimeout(() => {
                    this.canvasToTempFilePath()
                }, 300)
            })
        },

        canvasToTempFilePath() {
            uni.canvasToTempFilePath({
                canvasId: 'canvas',
                success: res => {
                    uni.hideLoading()
                    console.log('绘画的图片', res)
                    this.img_src = res.tempFilePath
                    this.$refs.popup.open()
                },
                fail: err => {
                    console.log('绘画失败', err)
                    this.canvasToTempFilePath()
                }
            }, this)

        },

        async saveImage() {
            uni.saveImageToPhotosAlbum({
                filePath: this.img_src,
                success: () => this.$uni.showToast('已保存')
            })
        },

        previewImage() {
            this.$uni.previewImage(this.img_src)
        }
    }
}
</script>

<style lang="scss" scoped>
.canvas-container {
    position: fixed;
    top: -999999px;
    left: -999999px;
}

.big-img {
    display: block;
    width: 80vw;
    height: auto;
    border-radius: 5px;
}

.save-button {
    width: 150px;
    line-height: 44px;
    border-radius: 22px;
}
</style>