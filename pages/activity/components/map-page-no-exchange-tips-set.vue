<template>
    <view>
        <template v-if="type === 'text'">
            <view @click="textEditOpen">
                <view class="ellipsis--l3">
                    <text space="nbsp">{{ set.text }}</text>
                </view>
                <view class="clear clearfix">
                    <view class="fr" hover-class="navigator-hover">
                        <text class="iconfont icon-edit color-light-primary font14"></text>
                        <text class="color-light-primary font14" style="padding-left: 2px;">编辑</text>
                    </view>
                </view>
            </view>

            <uni-popup ref="textEditPopup" @touchmove.stop.prevent="">
                <view class="tips-edit-popup bg-white">
                    <view class="textarea-container">
                    <textarea class="text-area" v-model="set_.text" maxlength="500"
                              placeholder="请输入提示文本 500字内"/>
                    </view>

                    <view class="confirm-button bg-light-primary color-white text-center"
                          @click="textEditConfirm">确定
                    </view>
                    <view class="flex-all-center pt5">
                        <view class="color-sub font12" @click="$refs.textEditPopup.close()">取消</view>
                    </view>
                </view>
            </uni-popup>
        </template>

        <template v-if="type === 'style'">
            <view class="data-show bg-background">
                <view class="text-center font12 color-sub pb10">
                    同时设置背景颜色和背景图片,背景图片会覆盖背景颜色。
                </view>

                <view class="data-item flex-row">
                    <view class="label">背景图片:</view>
                    <view>
                        <view v-if="set_.background.image" class="pl10 pr10 color-light-primary"
                              @click="previewImage(set_.background.image)">查看
                        </view>
                        <view v-else class="color-sub">未设置</view>
                    </view>
                </view>
                
                <view class="data-item flex-row">
                    <view class="label">背景颜色:</view>
                    <view class="color-block" :style="{backgroundColor: set_.background.color}"></view>
                </view>
                
                <view class="data-item flex-row">
                    <view class="label">文字颜色:</view>
                    <view class="color-block" :style="{backgroundColor: set_.text_set.color}"></view>
                </view>

                <view class="data-item flex-row">
                    <view class="label" style="line-height: 40px;">关闭按钮:</view>
                    <view :style="previewButtonStyle" class="preview-button">
                        <view>{{ set_.button.text }}</view>
                    </view>
                </view>


                <view class="clear clearfix">
                    <view class="fr" hover-class="navigator-hover" @click="setPopupShow">
                        <text class="iconfont icon-edit color-light-primary font14"></text>
                        <text class="color-light-primary font14" style="padding-left: 2px;">编辑</text>
                    </view>
                </view>

            </view>


            <uni-popup ref="setEditPopup">
                <view class="set-popup bg-white">
                    <view class="text-center pb10">设置</view>

                    <uni-forms label-width="100" label-align="right">
                        <uni-forms-item label="背景图片:">
                            <view class="background-image" v-if="set_data.background.image">
                                <image class="image" mode="aspectFill" :src="set_data.background.image"
                                       @click="previewImage(set_data.background.image)"/>
                                <view class="clear-image flex-all-center" @click="clearBackgroundImage">
                                    <uni-icons type="closeempty" size="24" color="#ffffff"/>
                                </view>
                            </view>
                            <view v-else class="add-background-image flex-all-center"
                                  @click="addBackgroundImage">
                                <uni-icons type="plus" size="34" color="#80848f"/>
                            </view>
                        </uni-forms-item>
                        
                        <uni-forms-item label="文字颜色:">
                            <view class="flex-row" @click="changeColor('text_color')">
                                <view class="color-block" :style="{backgroundColor: set_data.text_set.color}"></view>
                                <view class="flex-all-center pl10">
                                    <text class="iconfont icon-edit color-sub"></text>
                                </view>
                            </view>
                        </uni-forms-item>
                        
                        
                        <uni-forms-item label="背景颜色:">
                            <view class="flex-row" @click="changeColor('bg_color')">
                                <view class="color-block" :style="{backgroundColor: set_data.background.color}"></view>
                                <view class="flex-all-center pl10">
                                    <text class="iconfont icon-edit color-sub"></text>
                                </view>
                            </view>
                        </uni-forms-item>

                        <uni-forms-item label="关闭按钮文字:">
                            <uni-easyinput v-model="set_data.button.text" maxlength="10"/>
                        </uni-forms-item>

                        <uni-forms-item label="按钮文字颜色:">
                            <view class="flex-row" @click="changeColor('button_text_color')">
                                <view class="color-block"
                                      :style="{backgroundColor: set_data.button.text_color}"></view>
                                <view class="flex-all-center pl10">
                                    <text class="iconfont icon-edit color-sub"></text>
                                </view>
                            </view>
                        </uni-forms-item>

                        <uni-forms-item label="按钮颜色:">
                            <view class="flex-row" @click="changeColor('button_bg_color')">
                                <view class="color-block"
                                      :style="{backgroundColor: set_data.button.bg_color}"></view>
                                <view class="flex-all-center pl10">
                                    <text class="iconfont icon-edit color-sub"></text>
                                </view>
                            </view>
                        </uni-forms-item>

                    </uni-forms>

                    <view class="confirm-button color-white bg-light-primary text-center"
                          hover-class="navigator-hover" @click="setConfirm">确定</view>

                    <view class="flex-all-center">
                        <view class="p5 color-sub font12" @click="$refs.setEditPopup.close()">取消</view>
                    </view>
                </view>
            </uni-popup>

            <color-picker ref="colorPicker" :color="colorPickerColorData" @confirm="colorConfirm"/>
        </template>
    </view>
</template>

<script>
import colorPicker from './t-color-picker.vue'

export default {
    name: "map-page-no-exchange-tips-set",
    components: {colorPicker},
    props: {
        type: {
            type: String,
            default: 'text'
        },
        set: {
            type: Object,
            default: () => ({text: ''})
        }
    },
    
    data() {
        return {
            set_: this.set,
            set_data: {},
            colorPickerColorData: null
        }
    },
    
    computed: {
        previewButtonStyle() {
            const {text_color, bg_color} = this.set_.button
            return `color: ${text_color}; background-color: ${bg_color};`
        }  
    },
    
    watch: {
        set: {
            deep: true,
            handler(set) {
                this.set_ = set
            }
        }  
    },

    mounted() {

    },

    methods: {
        textEditOpen() {
            this.text = this.set.text
            this.$nextTick(() => this.$refs.textEditPopup.open())
        },

        setPopupShow() {
            this.set_data = JSON.parse(JSON.stringify(this.set_))
            this.$refs.setEditPopup.open()
        },

        // "rgba(0, 0, 0, 0)" => {r: 0, g: 0, b: 0, a: 0}
        rgbaStringToObject(rgbaString) {
            const rgbaRegex = /rgba?\((\d+),\s*(\d+),\s*(\d+),\s*(\d*\.?\d+)\)/
            const match = rgbaString.match(rgbaRegex)

            if (match) {
                return {
                    r: parseInt(match[1], 10),
                    g: parseInt(match[2], 10),
                    b: parseInt(match[3], 10),
                    a: parseFloat(match[4])
                }
            }

            return {r: 0, g: 0, b: 0, a: 0} // 如果字符串不符合 rgba 格式，则返回 {r: 0, g: 0, b: 0, a: 0}
        },

        changeColor(key) {
            this.change_color_key = key
            
            let color = ''
            if (key === 'text_color') color = this.set_data.text_set.color
            if (key === 'bg_color') color = this.set_data.background.color
            if (key === 'button_text_color') color = this.set_data.button.text_color
            if (key === 'button_bg_color') color = this.set_data.button.bg_color
            
            this.colorPickerColorData = this.rgbaStringToObject(color)
            this.$nextTick(() => this.$refs.colorPicker.open())
        },

        colorConfirm(e) {
            const {r, g, b, a} = e.rgba
            const color = `rgba(${r}, ${g}, ${b}, ${a})`
            
            const key = this.change_color_key

            if (key === 'text_color') this.$set(this.set_data.text_set, 'color', color)
            if (key === 'bg_color') this.$set(this.set_data.background, 'color', color)
            if (key === 'button_text_color') this.$set(this.set_data.button, 'text_color', color)
            if (key === 'button_bg_color') this.$set(this.set_data.button, 'bg_color', color)
        },

        addBackgroundImage() {
            this.$uni.navigateTo('/pages/other/image_upload_or_select', {
                events: {
                    newImg: src => this.$set(this.set_data.background, 'image', src)
                }
            })  
        },
        
        clearBackgroundImage() {
            this.$set(this.set_data.background, 'image', '')
        },

        previewImage(src) {
            this.$uni.previewImage(src)  
        },

        setConfirm() {
            this.set_ = this.set_data
            this.$emit('update:set', this.set_)
            this.$refs.setEditPopup.close()
        },

        textEditConfirm() {
            this.$emit('update:set', this.set_)
            this.$refs.textEditPopup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.tips-edit-popup {
    width: 90vw;
    padding: 10px;
    border-radius: 10px;
}

.textarea-container {
    padding: 10px;
    border: 1px solid #E5E5E5;
    border-radius: 5px;
    
    .text-area {
        width: 100%;
        height: 300px;
    }
}

.confirm-button {
    width: 150px;
    line-height: 40px;
    border-radius: 20px;
    margin: 10px auto;
}

.data-show {
    position: relative;
    padding: 10px;
    box-sizing: border-box;
    border-radius: 10px;

    .data-item {
        line-height: 30px;

        .label {
            width: 80px;
            min-width: 80px;
            text-align: right;
            padding-right: 5px;
        }

        .color-block {
            margin-top: 2px;
            width: 24px;
            height: 24px;
            border: 1px solid #000;
            border-radius: 5px;
        }
        
        .preview-button {
            min-width: 120px;
            padding: 0 10px;
            line-height: 40px;
            border-radius: 20px;
            text-align: center;
        }
    }

    .control-icon {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
    }
}

.set-popup {
    padding: 10px;
    border-radius: 10px;
    width: 80vw;
    max-width: 400px;

    .background-image, .background-image .image, .add-background-image {
        width: 100px;
        height: 100px;
    }
    
    .background-image, .add-background-image {
        border-radius: 5px;
        overflow: hidden;
    }
    
    .background-image {
        position: relative;
        
        .image {
            display: block;
        }
        
        .clear-image {
            position: absolute;
            top: 5px;
            right: 5px;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, .5);
        }
    }
    
    .add-background-image {
        box-sizing: border-box;
        background-color: #f8f8f8;
    }

    .color-block {
        margin-top: 2px;
        width: 60px;
        height: 30px;
        border: 1px solid #000;
        border-radius: 5px;
    }

    .confirm-button {
        width: 120px;
        margin: 20px auto 0;
    }
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .tips-edit-popup {
        width: 450px;
    }
}
/* #endif */
</style>