<template>
    <view>
        <uni-popup ref="popup" @touchmove.prevent.stop="" :is-mask-click="false">
            <view class="popup bg-white">
                <view class="text-center p10">步数提交</view>
                <view class="form-item">
                    <uni-easyinput type="textarea" v-model="memo" :disabled="isSubmitting"
                                   placeholder="请详细说明超出步数限制的原因"/>
                </view>

                <view class="form-item">
                    <view class="label">图片佐证:</view>
                    <view class="image-list flex-row flex-wrap">
                        <view class="image-container" v-for="(item, index) in pic_list" :key="index">
                            <image class="image" :src="item.path" mode="aspectFill"
                                   @click="lookImage(item.path)"/>
                            <view class="remove-image flex-all-center" @click="removeImage(index)">
                                <uni-icons type="closeempty" size="20" color="#e20f04"/>
                            </view>
                        </view>
                        <view v-if="pic_list.length < max_pic" class="add-image flex-all-center bg-background"
                              hover-class="navigator-hover" @click="addImage">
                            <uni-icons type="plusempty" size="120rpx" color="#dddee1"/>
                        </view>
                    </view>
                </view>

                <view class="flex-all-center">
                    <view class="confirm-button bg-light-primary color-white text-center"
                          :class="{'disabled': isSubmitting}"
                          hover-class="navigator-hover" @click="submit">
                        {{ submitButtonText }}
                    </view>
                </view>
                <view class="flex-all-center">
                    <view class="close-text color-sub p10 font14" @click="manualClose">关闭</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>

export default {
    name: "max-daily-step-submit-popup",
    emits: ['close'],
    props: {
        step: {
            type: Number,
            default: 0
        },
        activeId: {
            type: String,
            default: ''
        }
    },

    data() {
        return {
            memo: '',
            pic_list: [],
            max_pic: 6,
            isSubmitting: false
        }
    },

    computed: {
        submitButtonText() {
            return this.isSubmitting ? '提交中...' : '提交';
        }
    },

    methods: {
        open() {
            this.$refs.popup.open()
        },

        close() {
            this.$refs.popup.close()
        },

        async manualClose() {
            if (this.isSubmitting) return

            const res = await this.$uni.showModal('关闭后无法提交超出限制步数说明，确认关闭？', {showCancel: true})
            if (!res.confirm) return
            this.$emit('close')
        },

        removeImage(index) {
            if (this.isSubmitting) return
            this.pic_list.splice(index, 1)
        },

        addImage() {
            if (this.isSubmitting) return

            uni.chooseImage({
                count: this.max_pic - this.pic_list.length,
                success: res => {
                    const img_big_size = this.xwy_config.activeUserUploadImageSizeLimit(this.activeId || '')   // 图片最大大小限制，单位M
                    const size_check = this._utils.checkImageSize(res.tempFiles, img_big_size * 1024 * 1024)
                    if (!size_check) return this.$uni.showModal('单张图片大小超过' + img_big_size + 'M，无法使用')
                    this.pic_list = this.pic_list.concat(res.tempFiles)
                }
            })
        },

        lookImage(current) {
            if (this.isSubmitting) return

            this.$uni.previewImage({
                current,
                urls: this.pic_list.map(item => item.path)
            })
        },

        async submit() {
            if (this.isSubmitting) return

            if (!this.memo.trim()) return this.$uni.showToast('请填写说明原因')

            this.isSubmitting = true
            const pic_list = await this.getPicList()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exchange.max_step.submitReason/submit_sport_reason',
                data: {
                    active_id: this.activeId,
                    memo: this.memo,
                    pic_list
                }
            })
            this.isSubmitting = false

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '提交失败')
            this.$uni.showToast('已提交, 等待管理员审核。')

            this.$emit('close')
        },

        async getPicList() {
            if (!this.pic_list.length) return ''

            const list = []
            for (let i = 0, len = this.pic_list.length; i < len; i++) {
                const src = await this.xwy_api.uploadOneImage({
                    temp_data: this.pic_list[i],
                    active_id: this.activeId
                })
                if (src) list.push(src)
            }

            return this._utils.base64['encode'](JSON.stringify(list))
        }
    }
}
</script>

<style lang="scss" scoped>
.popup {
    width: 690rpx;
    padding: 20rpx;
    box-sizing: border-box;
    border-radius: 5px;

    .form-item {
        padding: 10rpx;

        .label {
            color: #495060;
            padding-bottom: 5px;
            font-size: 14px;
        }
    }
}

.image-list {
    min-height: 420rpx;

    .image-container, .image, .add-image {
        width: 200rpx;
        height: 200rpx;
        display: block;
        border-radius: 5px;
    }

    .image-container, .add-image {
        margin: 5rpx;
    }

    .image-container {
        position: relative;

        .remove-image {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, .5);
        }
    }

    .add-image {
        text-align: center;
        line-height: 200rpx;
    }
}

.confirm-button {
    width: 500rpx;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;

    &.disabled {
        opacity: 0.7;
        pointer-events: none;
    }
}
</style>