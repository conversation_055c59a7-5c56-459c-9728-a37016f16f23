<template>
    <view>
        <view>
            <view class="flex-kai item" v-for="(item, index) in listData" :key="index">
                <view class="item-text font14">
                    大于{{ item.min }}步 并 小于等于{{ item.max }}步, 奖励{{ item.num }}{{ unit }}
                </view>

                <view class="flex-row pl10">
                    <view class="set-icon flex-all-center" @click="editRule(item, index)">
                        <text class="iconfont icon-edit color-sub"></text>
                    </view>
                    <view class="set-icon flex-all-center" @click="listData.splice(index, 1)">
                        <text class="iconfont icon-delete color-sub"></text>
                    </view>
                </view>
            </view>

            <view class="flex-all-center">
                <view class="color-light-primary p10" @click="addRule">添加规则</view>
            </view>
        </view>

        <uni-popup ref="popup">
            <view class="set-popup bg-white">
                <view class="text-center pb10">规则设置</view>

                <uni-forms label-width="110" label-align="right">
                    <uni-forms-item label="最低步数(不含):">
                        <uni-easyinput v-model="editData.min"/>
                    </uni-forms-item>

                    <uni-forms-item label="最高步数(含):">
                        <uni-easyinput v-model="editData.max"/>
                    </uni-forms-item>

                    <uni-forms-item :label="`奖励${unit}:`">
                        <uni-easyinput v-model="editData.num"/>
                    </uni-forms-item>
                </uni-forms>

                <view class="confirm-button color-white bg-light-primary text-center"
                      hover-class="navigator-hover" @click="confirm">确定
                </view>

                <view class="flex-all-center">
                    <view class="p5 color-sub font12" @click="$refs.popup.close()">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "unlock-points-daily-list-set",
    props: ['list', 'unit'],
    data() {
        return {
            listData: [],
            editData: {}
        }
    },

    watch: {
        list: {
            handler(val) {
                this.listData = JSON.parse(JSON.stringify(val))
            },
            immediate: true,
            deep: true
        },

        listData: {
            handler(val) {
                this.$emit('update:list', val)
            },
            deep: true
        }
    },

    methods: {
        addRule() {
            this.editIndex = -1
            this.editData = {
                min: '',
                max: '',
                num: ''
            }
            this.$refs.popup.open()
        },

        editRule(item, index) {
            this.editIndex = index
            this.editData = JSON.parse(JSON.stringify(item))
            this.$refs.popup.open()
        },

        confirm() {
            const min = Math.floor(this.editData.min)
            if (isNaN(min) || min < 0) return this.$uni.showToast('最低步数填写不正确')

            const max = Math.floor(this.editData.max)
            if (isNaN(max) || max < 0) return this.$uni.showToast('最高步数填写不正确')

            if (min > max) return this.$uni.showToast('最低步数不能高于最高步数')

            const num = Number(this.editData.num)
            if (isNaN(num) || num < 0) return this.$uni.showToast(`奖励${this.unit}填写不正确`)

            const data = {min, max, num, text: ''}

            if (this.editIndex === -1) {
                this.listData.push(data)
            } else {
                // #ifdef H5
                // 在H5上，使用$set、splice替换都会导致浏览器卡死，最后只能使用$forceUpdate了
                this.listData[this.editIndex] = data
                this.$forceUpdate()
                // #endif

                // #ifndef H5
                this.$set(this.listData, this.editIndex, data)
                // #endif
            }
            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.item {
    padding: 2px 0;
    $size: 32px;

    .item-text {
        line-height: $size;
    }

    .set-icon {
        background-color: #f8f8f8;
        width: $size;
        height: $size;
        border-radius: 50%;
        margin: 0 5px;
    }
}

.set-popup {
    padding: 10px;
    border-radius: 10px;
    width: 90vw;
    max-width: 400px;

    .confirm-button {
        width: 120px;
        line-height: 40px;
        border-radius: 20px;
        margin: 20px auto 0;
    }
}
</style>