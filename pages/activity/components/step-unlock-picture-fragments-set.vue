<template>
    <view>
        <view class="form-item">
            <view class="top color-content">步数解锁拼图碎片</view>
            <view class="bottom font16">
                <view class="flex-kai">

                    <picker class="view" :range="['关闭', '开启']" :value="set_data.open"
                            @change="openSwitch">
                        {{ set_data.open ? '开启' : '关闭' }}
                    </picker>
                    <view class="flex-all-center">
                        <uni-icons type="forward" color="#80848f"/>
                    </view>
                </view>
            </view>
        </view>

        <view v-show="set_data.open">
            <view class="form-item">
                <view class="top color-title">
                    <text>拼图图片</text>
                    <text class="color-error font16"> *</text>
                </view>
                <view style="padding-top: 5px;">
                    <view class="image-view" v-if="set_data.image_src">

                        <image class="image-item" mode="aspectFill"
                               :src="set_data.image_src"
                               @click="previewImage([set_data.image_src])"/>
                        <view class="del-image-item"
                              @click.stop="set_data.image_src = ''">
                            <uni-icons type="closeempty" color="#e20f04"/>
                        </view>
                    </view>
                    <view v-else class="add-image text-center"
                          @click="setPictureFragmentsImage">
                        <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                    </view>
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">
                    <text>拼图碎片数量设置</text>
                    <text class="color-error font16"> *</text>
                </view>
                <view class="bottom font16" @click="$refs.popup.open()">
                    <view class="flex-kai">
                        <view class="view">
                            <text v-if="set_data.row && set_data.col">
                                {{ set_data.col }}*{{ set_data.row }} ({{ set_data.col * set_data.row }})
                            </text>
                            <text v-else class="color-sub">请设置</text>
                        </view>
                        <view class="flex-all-center">
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">
                    <text>解锁每张碎片的里程</text>
                    <text class="pl5 color-sub font14">(单位: {{ unit || '里' }})</text>
                    <text class="color-error font16"> *</text>
                </view>
                <view class="bottom font16">
                    <input class="input" maxlength="8" placeholder="请填写里程数"
                           v-model="set_data.mileage"/>
                </view>
            </view>
        </view>

        <uni-popup ref="popup">
            <view class="set-popup bg-white">
                <view class="text-center pb10">拼图碎片数量设置</view>

                <uni-forms label-position="top" label-width="200">
                    <uni-forms-item label="列数:">
                        <uni-easyinput type="number" v-model="col" maxlength="2"
                                       placeholder="请输入2-20的整数"/>
                    </uni-forms-item>

                    <uni-forms-item label="行数:">
                        <uni-easyinput type="number" v-model="row" maxlength="2"
                                       placeholder="请输入2-20的整数"/>
                    </uni-forms-item>
                </uni-forms>

                <view class="confirm-button color-white bg-light-primary text-center"
                      hover-class="navigator-hover" @click="confirm">确定
                </view>

                <view class="flex-all-center">
                    <view class="p5 color-sub font12" @click="$refs.popup.close()">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "step-unlock-picture-fragments-set",
    props: ['set', 'unit'],
    data() {
        return {
            set_data: {},
            col: '',
            row: ''
        }
    },

    watch: {
        set: {
            handler(val) {
                this.set_data = val
            },
            deep: true,
            immediate: true
        },

        set_data: {
            handler(val) {
                this.$emit('update:set', val)
                this.row = val.row
                this.col = val.col
            },
            deep: true,
            immediate: true
        }
    },

    methods: {
        openSwitch(e) {
            this.set_data.open = Number(e.detail.value)
        },

        setPictureFragmentsImage() {
            let url = '/pages/other/image_upload_or_select'
            if (this.id) url += `?active_id=${this.id}`
            this.$uni.navigateTo(url, {
                events: {
                    newImg: src => this.$set(this.set_data, 'image_src', src)
                }
            })
        },

        confirm() {
            const row = Math.floor(this.row)
            const col = Math.floor(this.col)
            if (row < 2 || row > 20 || col < 2 || col > 20) {
                return this.$uni.showToast('列数、行数必须在2-20之间')
            }

            this.set_data.row = row
            this.set_data.col = col
            this.$refs.popup.close()
        },

        previewImage() {
            this.$uni.previewImage(this.set.image_src)
        }
    }
}
</script>

<style lang="scss">
.set-popup {
    padding: 10px;
    border-radius: 10px;
    width: 80vw;
    max-width: 400px;

    .confirm-button {
        width: 120px;
        line-height: 40px;
        border-radius: 20px;
        margin: 20px auto 0;
    }
}
</style>