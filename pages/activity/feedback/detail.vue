<template>
	<view>
		<template v-if="detail">
			<view v-if="detail.send_user_details" class="user-info flex-row">
				<image class="headimg" :src="detail.send_user_details.headimg"/>
				<view class="flex-center flex-column pl10 font18">
					<template v-if="!detail.user_attend_details || !detail.user_attend_details.must_submit || !detail.user_attend_details.must_submit.length">
						{{detail.send_user_details.nickname}}
					</template>
					<template v-else>
						{{detail.user_attend_details.must_submit[0].title === '姓名' ? '' : (detail.user_attend_details.must_submit[0].title) + ': '}}{{detail.user_attend_details.must_submit[0].value}}
					</template>
				</view>
			</view>

			<view class="color-sub p10 font14">{{detail.create_time}}</view>
			<view class="color-title p10 font18">{{detail.title}}</view>
			<view class="color-content p10 font16">{{detail.content}}</view>
		</template>
	</view>
</template>

<script>
	const app = getApp()
	import login from '../../../utils/api/login.js'
	import xwy_api from '../../../utils/api/xwy_api.js'
	import utils from '../../../utils/utils.js'


	export default {
		data() {
			return {
				detail: null
			}
		},
		onLoad(e) {
			uni.showLoading({
				mask: true
			})
		    login.uniLogin(err => {
		        if (err && err.errMsg) {
		            uni.showModal({
		                title: err.errTitle || '提示',
		                content: err.errMsg,
		                showCancel: false
		            })
		            return false
		        }

		        this.getDetail(e.id)
		    })
		},
		methods: {
			async getDetail(id) {
				const data = {
					access_token: app.globalData.access_token,
					id
				}
				const res = await xwy_api.request({url: 'front.user.message/message_details', data})
				uni.hideLoading()
				if (!res || !res.data || !res.data.message_details) {
					xwy_api.alert(res.info || '获取失败')
					return false
				}
				this.detail = res.data.message_details
			}
		}
	}
</script>

<style>
.user-info {
	padding: 10px;
}
.user-info .headimg {
	display: inline-block;
	width: 50px;
	height: 50px;
	border-radius: 50%;
}
</style>
