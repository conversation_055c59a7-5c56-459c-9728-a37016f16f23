<template>
    <view class="page bg-background">
        <view v-if="total_count" class="text-center p10 color-sub font14">共{{total_count}}条记录</view>

        <view class="list">
            <template v-for="(item, index) in list">
				<xwy-ad v-if="index === 0" :activity_id="id" :ad_type="66"></xwy-ad>

                <navigator class="item bg-white" :key="index" :url="'./detail?id=' + item.id" @click="read(index)">
					<view class="unread bg-red" v-if="!item.read_time"></view>
					<view class="color-title" v-if="item.title">标题: {{item.title}}</view>
					<view class="color-content pt5 font14" v-if="item.content">反馈内容: {{item.content}}</view>
                    <view class="color-sub pt5 font12">{{item.create_time}}</view>
					<view class="tools-bar flex-kai">
						<view></view>
						<view class="delete" @click.stop="deleteItem(item.id)">
						    <text class="iconfont icon-delete color-sub font20"></text>
						</view>
					</view>
                </navigator>

				<xwy-ad v-if="index !== 0 && ((index + 1) % 10 === 0)" :activity_id="id" :ad_type="66"></xwy-ad>
            </template>
        </view>

        <view v-if="!list.length && !loading" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无记录</view>
        </view>

        <uni-load-more v-if="loading" status="loading"></uni-load-more>
        <uni-load-more v-if="is_last_page && list.length > 10" status="noMore"
                       :contentText="{contentnomore: '我是有底线的'}"></uni-load-more>
        <uni-load-more v-if="!is_last_page && !loading && list.length" status="more"></uni-load-more>
    </view>
</template>

<script>
const app = getApp()
import login from '../../../utils/api/login.js'
import xwy_api from '../../../utils/api/xwy_api.js'
import utils from '../../../utils/utils.js'

export default {
    data() {
        return {
            id: '',
            loading: true,
            list: [],
            total_count: 0,
            load_page: 1,
            is_last_page: false
        }
    },
    onLoad(e) {
        this.id = e.id


        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                this.loading = false
                uni.showModal({
                    title: err.errTitle || '提示',
                    content: err.errMsg,
                    showCancel: false
                })
                return false
            }

            this.getList()
        })
    },
    onReachBottom() {
        if (!this.loading && !this.is_last_page && this.list.length) this.getList()
    },
    methods: {

        getList() {
            this.loading = true
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
            }

			xwy_api.ajax({
				url: 'front.user.message/message_list',
				data: {
					access_token: app.globalData.access_token,
					active_id: this.id,
                    page: this.load_page,
                    perpage: 20
				},
				success: res => {
					this.loading = false
					if (res?.data?.message_list) {
					    const data = res.data.message_list
					    const list = data.data || []
					    this.list = [...this.list, ...list]
					    this.total_count = data.total || 0
					    this.is_last_page = data.is_lastpage
					    this.load_page++
					} else {
					    this.is_last_page = true
					}
				}
			})
        },

		read(index) {
			this.list[index].read_time = true
		},

		deleteItem(id) {
		    this.deleteConfirm([id])
		},

		deleteConfirm(ids) {
		    uni.showModal({
		        title: '提示',
		        content: `确定删除${ids.length > 1 ? '选中的' : '该'}反馈?`,
		        confirmText: '删除',
		        cancelColor: '#80848f',
		        confirmColor: '#ed3f14',
		        success: res => {
		            if (res.confirm) this.deleteAjax(ids)
		        }
		    })
		},

		deleteAjax(ids) {
		    uni.showLoading({
		        title: '删除中...',
				mask: true
		    })

			xwy_api.ajax({
				url: 'front.user.message/del_message',
				data: {
					access_token: app.globalData.access_token,
					ids: ids.join(',')
				},
				success: res => {
					if (!res.status) {
					    uni.showToast({
					        title: res.info || '删除失败',
					        icon: 'error'
					    })
					    return false
					}


					uni.showToast({
					    title: '删除成功',
					    icon: 'success'
					})

					const timeout = setTimeout(() => {
						this.load_page = 1
						this.getList()
						clearTimeout(timeout)
					}, 1000)
				}
			})


		},
    }
}
</script>

<style scoped>
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 20px;
}


.list {
    padding-top: 10px;
}

.item {
	position: relative;
    margin: 10px;
    margin-top: 0;
    padding: 20px;
    border-radius: 10px;
}

.unread {
	position: absolute;
	top: 10px;
	left: 10px;
	width: 8px;
	height: 8px;
	border-radius: 50%;
}
.tools-bar {
    border-top: 1px solid #eee;
	margin-top: 5px;
    padding-top: 5px;
}
.delete {
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 50%;
	border: 1px solid #eee;
}
</style>
