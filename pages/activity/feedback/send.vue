<template>
	<view class="page">
		<view class="form">

		    <view class="form-item">
		        <view class="top color-title">标题</view>
		        <view class="bottom font16">
		            <input class="input" v-model="title"/>
		        </view>
		    </view>

		    <view class="form-item">
		        <view class="top color-title flex-kai">
					<view>
						<text>反馈内容</text>
						<text class="color-red">*</text>
					</view>
					<view v-if="showCommonScripts" class="color-primary" @click="textModelListShow">
                        常用脚本
                    </view>
				</view>
		        <view class="bottom font16">
		            <textarea
						class="textarea"
						maxlength="-1"
						auto-height
						v-model="content"
						placeholder="必填"
					/>
		        </view>
		    </view>


		</view>



		<view class="save flex-all-center">
			<button :disabled="loading" class="save-btn text-center bg-primary color-white" @click="save">反馈</button>
		</view>

		<uni-popup ref="text_model_list" type="center">
			<view class="text-model-list p10 bg-white">
				<view class="text-model-list-close" @click="closePopup('text_model_list')">
					<text class="iconfont icon-wrong color-sub font18"></text>
				</view>
				<view class="color-sub pb5">
					<text>常用脚本</text>
					<text class="font14">(点击可替换反馈内容)</text>
				</view>
				<view class="list">
					<view
						class="item color-content"
						v-for="(item, index) in text_model_list"
						:key="index"
						@click="setContent(item)"
					>
						{{item}}
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	const app = getApp()
	import login from '../../../utils/api/login.js'
	import xwy_api from '../../../utils/api/xwy_api.js'

	export default {
		data() {
			return {
				loading: true,
                activity_id: '',
				title: '',
				content: '',
				text_model_list: []
			}
		},

        computed: {
            showCommonScripts() {
                const noShowIds = ['5a56536807e0970200f7954a6a0e11cd', 'e152b18ac28433bfb0bb779d8dad82f5']
                return !noShowIds.includes(this.activity_id)
            }
        },

		onLoad(e) {
			this.activity_id = e.activity_id
			if (e.nickname) {
				this.title = '用户步数异常'
				this.content = '用户 ' + e.nickname + ' 步数异常'
			}
			uni.showLoading({
				mask: true
			})
			login.uniLogin(err => {
				if (err && err.errMsg) {
					uni.hideLoading()
					this.loading = false
					uni.showModal({
						title: err.errTitle || '提示',
						content: err.errMsg,
						showCancel: false
					})
				}

				this.getTextModelList()
			})
		},
		methods: {
			async getTextModelList() {
				if (app.globalData.text_model_list) {
					uni.hideLoading()
					this.loading = false
					this.text_model_list = app.globalData.text_model_list
					return false
				}

				const data = {
					access_token: app.globalData.access_token
				}
				const res = await xwy_api.request({url: 'front.user.message/reply_content_set', data})

				uni.hideLoading()
				this.loading = false

				if (res && res.data && res.data.content_list && res.data.content_list.length) {
					const text_model_list = res.data.content_list
					this.text_model_list = text_model_list
					app.globalData.text_model_list = text_model_list
				}
			},

			textModelListShow() {
				this.$refs.text_model_list.open()
			},

			setContent(text) {
				this.content = text
				this.closePopup('text_model_list')
			},

			closePopup(ref) {
				this.$refs[ref].close()
			},

			async save() {
				if (!this.content) {
					uni.showToast({
						title: '请填写反馈内容',
						icon: 'error'
					})
					return false
				}

				this.loading = true
				uni.showLoading({
					title: '反馈中...',
					mask: true
				})

				const data = {
					access_token: app.globalData.access_token,
					title: this.title,
					content: this.content,
					active_id: this.activity_id
				}

				const res = await xwy_api.request({url: 'front.user.message/send_message', data})
				this.loading = false
				uni.hideLoading()
				if (!res || !res.status) {
					uni.showModal({
						title: '提示',
						content: res && res.info || '反馈失败',
						showCancel: false
					})
					return false
				}

				const title = res.info || '反馈成功!'
				uni.showToast({
					title: res.info || '反馈成功!',
					icon: title.length < 8 ? 'success' : 'none'
				})

				const timeout = setTimeout(() => {
					uni.navigateBack()
					clearTimeout(timeout)
				}, 1500)
			}
		}
	}
</script>

<style scoped>
	.page {
		padding-bottom: 80px;
	}
	.form {
	    padding: 10px 0;
	}

	.form-item {
	    padding: 10px;
	}


	.form-item .bottom {
	    border-bottom: 1px solid #eee;
	}


	.form-item .textarea {
	    width: 100%;
	    line-height: 16px;
	    padding: 10px 0;
	}

	.form-item .input {
	    width: 100%;
	    line-height: 40px;
	    height: 40px;
	}

	.form-item .view {
	    padding: 8px 0;
	    width: 100%;
	}

	.item {
		padding: 5px 0;
	}

	.save {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100vw;
		box-sizing: border-box;
		padding: 15px 0;
	}
	.save-btn {
		width: 200px;
		line-height: 40px;
		border-radius: 20px;
	}
	.save-btn::after {
		border: none;
	}

	.text-model-list {
		position: relative;
		margin: 0 20px;
		border-radius: 10px;
	}
	.text-model-list-close {
		position: absolute;
		right: 10px;
		top: 10px;
	}
	.text-model-list .list {
		height: 70vh;
		overflow: auto;
	}
</style>
