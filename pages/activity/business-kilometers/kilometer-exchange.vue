<template>
    <view>
        
    </view>
</template>

<script>
export default {
    data() {
        return {
            status: 'not started', // not started: 未开始, started: 已开始, ended: 已结束
            user_location: {
                latitude: null,
                longitude: null,
                start_address: '',
                end_address: ''
            }
        }
    },

    onLoad(params) {
        this.id = params.id
        if (params.record_id) {
            this.record_id = Number(params.record_id)
            this.getRecord()
        } else {
            this.getLocation()
        }
    },

    methods: {
        getRecord() {
            const uncompleted_record = uni.getStorageSync('kilometer_exchange_uncompleted_record')
            this.user_location.latitude = Number(uncompleted_record.lat)
            this.user_location.longitude = Number(uncompleted_record.lng)
            this.start_province = uncompleted_record.province
            this.status = 'started'
            this.complete()
        },

        complete() {
            uni.showModal({
                title: '提示',
                content: '完成后将记录终点信息，并无法修改。确定已到达终点？',
                success: res => {
                    res.confirm ? this.getLocation() : uni.navigateBack()
                }
            })
        },

        getLocation() {
            uni.getLocation({
                type: 'gcj02',
                success: res => {
                    const { latitude, longitude } = res
                    if (this.status === 'started') {
                        // 如果是到达终点点完成，判断起终点的距离，距离必须大于0，不然不允许提交
                        const distance = this._utils.getDistance(this.user_location.latitude, this.user_location.longitude, latitude, longitude)
                        if (distance === 0) return this.xwy_api.alert('起点和终点相同，无法提交', {
                            success: () => uni.navigateBack()
                        })
                    }
                    this.user_location.latitude = latitude
                    this.user_location.longitude = longitude
                    this.chooseLocation()
                },
                fail: () => {
                    this.xwy_api.alert('位置获取失败，请检查手机是否开启定位功能、是否授权小程序使用你的位置信息', {
                        success: () => uni.navigateBack()
                    })
                }
            })
        },

        chooseLocation() {
            const user_latitude = this.user_location.latitude
            const user_longitude = this.user_location.longitude
            uni.chooseLocation({
                latitude: user_latitude,
                longitude: user_longitude,
                success: res => {
                    console.log(res)
                    const { latitude, longitude, address, name } = res
                    if (this._utils.getDistance(user_latitude, user_longitude, latitude, longitude) > 500) {
                        // 选择的位置与定位距离不能超过500米
                        return uni.showModal({
                            title: '提示',
                            content: '选择的位置与当前距离不能超过500米，请重新选择',
                            confirmText: '重新选择',
                            success: res => res.confirm ? this.chooseLocation() : uni.navigateBack()
                        })
                    }
                    const city = this._utils.extractRegionInfo(address)?.city
                    if (!city) return uni.showModal({
                        title: '提示',
                        content: '城市信息获取失败，请重新选择地址',
                        confirmText: '重新选择',
                        success: res => {
                            res.confirm ? this.chooseLocation() : uni.navigateBack()
                        }
                    })
                    const address_key = this.status === 'not started' ? 'start_address' : 'end_address'
                    this.user_location[address_key] = address + '----------' + name

                    this.submit()
                },
                fail: err => {
                    if (err?.errMsg === 'chooseLocation:fail cancel') uni.navigateBack()
                }
            })
        },

        async submit() {
            uni.showLoading({
                title: '提交中...',
                mask: true
            })
            const address_key = this.status === 'not started' ? 'start_address' : 'end_address'
            const data = {
                active_id: this.id,
                lat: this.user_location.latitude,
                lng: this.user_location.longitude,
                address: this.user_location[address_key],
                // exchange_types: 0, // 默认是未知省内还是省外
            }
            if (this.start_province) {
                data.exchange_types = 1 // 到达终点，默认是省内
                if (this._utils.extractRegionInfo(data.address)?.province !== this.start_province) {
                    data.exchange_types = 2    // 如果起点终点不是同一个省，则为省外出差
                }
            }
            if (this.record_id) data.id = this.record_id
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exchange.business_exchange_kilo.userSubmit/user_submit_lata_lng',
                data
            })
            uni.hideLoading()
            if (res?.status !== 1) return this.xwy_api.alert(res?.info || '提交失败，请重试', {
                success: () => uni.navigateBack()
            })

            let info = res?.info || '提交成功'
            uni.showToast({
                title: info,
                icon: info.length <= 7 ? 'success' : 'none'
            })

            this.status = this.status === 'not started' ? 'started' : 'ended'
            if (this.status === 'started' && res?.data?.['res']) this.record_id = res?.data?.['res']

            this.reloadListPage()
        },

        reloadListPage() {
            const OEC = this.getOpenerEventChannel()
            OEC?.emit?.('reloadList')
            uni.navigateBack()
        }
    }
}
</script>
