<template>
    <view>
        <view class="total flex-kai font14">
            <view class="p10 color-sub">共{{ total }}名出差用户</view>
            <view class="p10" hover-class="navigator-hover" @click="toAddUser">
                <text class="color-primary">添加出差用户</text>
                <uni-icons type="forward" color="#2d8cf0" size="14"/>
            </view>
        </view>

        <view class="list">
            <view class="item flex-row" v-for="item in list" :key="item.id">
                <view v-if="item['user_attend_details'] && item['user_attend_details'].headimg" class="pr10">
                    <image class="headimg" :src="item['user_attend_details'].headimg"/>
                </view>
                <view style="width: 100%;">
                    <view class="color-title">
                        {{ item['user_attend_details'] && item['user_attend_details'].must_submit && item['user_attend_details'].must_submit[0] && item['user_attend_details'].must_submit[0].value || '该用户已删除' }}
                    </view>
                    <view class="flex-kai">

                        <navigator
                            class="color-light-primary p10 font14"
                            style="padding-left: 0;"
                            :url="`/pages/activity/other/user_exchange_list?activity_id=${item.active_id}&nickname=${encodeURIComponent(item['user_attend_details'] && item['user_attend_details'].must_submit && item['user_attend_details'].must_submit[0] && item['user_attend_details'].must_submit[0].value || '该用户已删除')}&headimg=${encodeURIComponent(item['user_attend_details'] && item['user_attend_details'].headimg || '')}&userid=${item.userid}&look_mode=travel`"
                        >查看出差记录</navigator>
                        <view
                            class="color-sub p10 font14"
                            style="padding-right: 0;"
                            hover-class="navigator-hover"
                            @click="removeAsk(item)"
                        >取消设置</view>
                    </view>
                </view>
            </view>
        </view>

        <view v-if="loading" class="text-center">
            <view v-if="current_page === 1" style="height: 35vh;"></view>
            <load-ani></load-ani>
        </view>

        <view v-if="!list.length && !loading" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub pb10 font14">暂无出差用户</view>
            <view class="flex-all-center pt15">
                <view
                    class="add-user-button color-white bg-primary"
                    hover-class="navigator-hover"
                    @click="toAddUser"
                >添加出差用户</view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            loading: true,
            list: [],
            current_page: 1,
            is_last_page: false,
            total: 0
        }
    },

    onLoad(params) {
        this.id = params.id
        this.init()
    },

    onReachBottom() {
        this.loadList()
    },

    methods: {
        async init() {
            await this.getList()
            this.loading = false
        },

        listDataInit() {
            this.list = []
            this.is_last_page = false
        },

        async loadList() {
            if (this.is_last_page || this.loading) return
            this.loading = true
            await this.getList()
            this.loading = false
        },

        async reloadList() {
            this.current_page = 1
            this.loading = true
            await this.getList()
            this.loading = false
        },

        async getList() {
            if (this.current_page === 1) this.listDataInit()

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exchange.business_exchange_kilo.adminManage/business_user_list',
                data: {
                    active_id: this.id,
                    page: this.current_page,
                    perpage: 20
                }
            })

            this.current_page++
            const data = res?.data?.user_list
            if (!data) {
                this.is_last_page = true
                return
            }
            this.list = this.list.concat(data.data || [])
            this.total = data.total
            this.is_last_page = data.is_lastpage
        },

        toAddUser() {
            uni.navigateTo({
                url: '/pages/activity/admin/activity_user_list?id=' + this.id,
                events: {
                    reload: () => this.reloadList()
                }
            })
        },

        removeAsk(item) {
            const name = item['user_attend_details']?.must_submit?.[0]?.value || ''
            uni.showModal({
                title: '提示',
                content: `确定取消用户${name}的出差兑换权限？`,
                success: res => {
                    res.confirm && this.remove(item.id)
                }
            })
        },
        async remove(id) {
            uni.showLoading({
                title: '取消中...',
                mask: true
            })
            const res = await this.xwy_api.deleteRecords(60, id)

            uni.hideLoading()
            if (res?.status !== 1) return this.xwy_api.alert(res?.info || '取消失败')
            const info = res?.info || '取消成功'
            uni.showToast({
                title: info,
                icon: info.length <= 7 ? 'success' : 'none'
            })

            await this.reloadList()
        }
    }
}
</script>

<style lang="scss">
.total {
    background-color: #fff;
    border-bottom: 1px solid #eee;
}
.item {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
    background-color: #fff;
    box-shadow: 0 0 10px #eee;
    .headimg {
        width: 60px;
        min-width: 60px;
        height: 60px;
        min-height: 60px;
        border-radius: 50%;
        display: block;
    }
}
.add-user-button {
    width: 200px;
    line-height: 44px;
    border-radius: 22px;
}
</style>
