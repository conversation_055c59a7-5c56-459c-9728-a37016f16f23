<template>
    <view class="page bg-background">
        <xwy-ad v-if="!close_ad" :ad_type="3"/>

        <view class="type-options flex-row">
            <view class="type-item" :class="{'type-active': item.value === type_value}"
                  v-for="item in type_options" :key="item.value" @click="typeChange(item.value)">
                {{ item.label }}
            </view>
        </view>

        <view style="width: 100%; height: 40px;"></view>

        <view v-if="type_value === 0" class="text-center font14 color-sub pt10">
            <template>共{{ baton_total }}棒，</template>
            <template v-if="my_baton">你在第{{ my_baton }}棒，</template>
            <template>当前接力到第{{ current_num }}棒</template>
        </view>
        <view class="sort-list">
            <view v-if="!close_ad" class="sort-item" style="padding: 0;">
                <xwy-ad :ad_type="66"/>
            </view>

            <template v-for="(item, index) in sort_list">
                <view class="sort-item bg-white" :class="{'current_baton': index + 1 === current_num}"
                      :key="item.sort_num">
                    <view>
                        <text class="color-sub">第 {{ item.sort_num }} 棒</text>
                        <template v-if="current_num === item.sort_num">
                            <text class="font14 color-light-primary pl5 iconfont icon-walk"></text>
                            <text class="font14 color-light-primary">(当前)</text>
                        </template>
                    </view>
                    <view class="user-info">
                        <view v-if="item.userid" class="flex-kai">
                            <view class="flex-row">
                                <view v-if="item.headimg" class="pr10">
                                    <image class="headimg" :src="item.headimg" mode="aspectFill"/>
                                </view>
                                <view v-if="item.name" class="user-name color-title">{{ item.name }}</view>
                            </view>
                            <view v-if="item.sort_num <= current_num" class="color-warning">
                                <text>{{ item.exchange_kilo }}</text>
                                <text class="font12">{{ unit }}</text>
                            </view>
                        </view>
                        <view v-else class="color-sub">无接力选手</view>
                    </view>
                    <view class="action-bar clearfix clear">
                        <view class="fr flex-row color-light-primary">
                            <view v-if="is_admin" class="flex-row">
                                <view v-if="!item.userid" hover-class="navigator-hover"
                                      @click="selectPlayer(item.sort_num)">选择接力选手</view>

                                <!--有设置接力选手并且还没有跑的才能删除。如果是当前接力棒，选手还没开始跑，也可以删除-->
                                <view
                                    v-if="item.userid &&
                                    ((item.sort_num > current_num) ||
                                    (item.sort_num === current_num && !item.exchange_kilo))" hover-class="navigator-hover"
                                    @click="deletePlayerConfirm(item.id, item.name, item.sort_num)">
                                    删除该选手
                                </view>
                            </view>
                            <view v-else>
                                <view
                                    v-if="!item.userid && user_select_team && (!user_select_max || user_select_total < user_select_max)"
                                    hover-class="navigator-hover"
                                    @click="joinRelayRace(item.sort_num)"
                                >我来接力
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <view v-if="!close_ad && (index + 1) % 10 === 0" class="sort-item" style="padding: 0;">
                    <xwy-ad :ad_type="66"/>
                </view>
            </template>
        </view>
    </view>
</template>

<script>
const app = getApp()
export default {
    data() {
        return {
            is_admin: false,
            all_sort_list: [],
            baton_total: 0,
            current_num: 0,
            user_select_team: false,
            user_select_max: 1,
            user_select_total: 0,
            unit: '里',
            close_ad: true,
            my_baton: null,
            type_options: [
                { label: '全部', value: 0 },
                { label: '已接力位置', value: 1 },
                { label: '未接力位置', value: 2 }
            ],
            type_value: 0
        }
    },
    
    computed: {
        sort_list() {
            if (this.type_value === 0) return this.all_sort_list
            if (this.type_value === 1) return this.all_sort_list.filter(v => v.userid)
            if (this.type_value === 2) return this.all_sort_list.filter(v => !v.userid)
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.team_id = params.team_id
        if (params.is_admin) this.is_admin = true

        this.init()
    },

    methods: {
        async init() {
            uni.showLoading({ title: '加载中' })
            await this.getActivityDetails()
            await this.getSortSettingList()
            uni.hideLoading()
        },

        async getActivityDetails() {
            let activity_details = app.globalData['activity_detail'] || null
            if (!activity_details) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.active_id
                    }
                })
                activity_details = res.data.active_details
            }

            if (!activity_details?.rank_set?.closed_AD) this.close_ad = false
            if (activity_details?.conf?.active) {
                const active = activity_details.conf.active
                if (active.kilo_unit) this.unit = active.kilo_unit
                if (active.runOneByOne) {
                    const { all_person, user_select_team, user_select_max } = active.runOneByOne
                    if (all_person) this.baton_total = Number(all_person)
                    if (user_select_team) this.user_select_team = true
                    if (user_select_max) this.user_select_max = Number(user_select_max)
                }
            }
        },

        async getSortSettingList(not_jump_current = false) {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.stepRunOneByOne/team_sort_user_list',
                data: {
                    active_id: this.active_id,
                    team_id: this.team_id,
                    get_current_num: 1,
                    page: 1,
                    perpage: this.baton_total
                }
            })

            if (res?.data?.current_num) this.current_num = res.data.current_num
            const list = res?.data?.user_list?.data || []
            this.setSortList(list, not_jump_current)
        },

        setSortList(list, not_jump_current) {
            const all_sort_list = []
            let user_select_total = 0
            
            for (let i = 1, len = this.baton_total; i <= len; i++) {
                const item = {sort_num: i}
                const baton = list.find(v => v.sort_num === i)
                if (baton) {
                    item.id = baton.id
                    item.sort_num = baton.sort_num
                    item.userid = baton.userid
                    item.name = baton.user_details?.must_submit?.[0]?.value || ''
                    item.headimg = baton.user_details?.headimg || ''
                    item.exchange_kilo = baton.user_details?.exchange_kilo || 0

                    if (app.globalData['userid'] === baton.userid) {
                        user_select_total++
                        if (this.user_select_max === 1) this.my_baton = baton.sort_num
                    }
                }
                all_sort_list.push(item)
            }
            
            
            this.user_select_total = user_select_total
            this.all_sort_list = all_sort_list

            if (!not_jump_current) {
                this.$nextTick(() => {
                    const query = uni.createSelectorQuery().in(this)
                    query.select('.current_baton').boundingClientRect(data => {
                        uni.pageScrollTo({
                            scrollTop: data['top'] - 50
                        })
                    }).exec()
                })
            }
        },

        typeChange(value) {
            if (value === this.type_value) return
            this.type_value = value
            uni.pageScrollTo({
                scrollTop: 0
            })
        },

        joinRelayRace(sort) {
            uni.showModal({
                title: '提示',
                content: `确定接力第${sort}棒吗？`,
                success: res => {
                    res.confirm && this.setSort(sort)
                }
            })
        },

        async setSort(sort, userid = null) {
            const data = {
                active_id: this.active_id,
                team_id: this.team_id,
                sort_num: sort
            }
            if (userid) data.userid = userid

            uni.showLoading(undefined)
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.stepRunOneByOne/create_set_team_sort',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.xwy_api.alert(res?.info || '接力失败')
            uni.showToast({
                title: '接力成功',
                icon: 'success'
            })
            await this.getSortSettingList(true)
        },

        selectPlayer(sort) {
            uni.navigateTo({
                url: `/pages/activity/admin/activity_user_list?id=${this.active_id}&team_id=${this.team_id}&is_select=1`,
                events: {
                    selectUser: user => {
                        setTimeout(() => {
                            uni.showModal({
                                title: '提示',
                                content: `确定将${user.nickname}设置为第${sort}棒的选手吗？`,
                                success: res => res.confirm && this.setSort(sort, user.userid)
                            })
                        }, 300)
                    }
                }
            })
        },

        deletePlayerConfirm(id, name, sort) {
            uni.showModal({
                title: '提示',
                content: `确定删除第${sort}棒的选手${name}吗？`,
                success: res => {
                    res.confirm && this.deletePlayer(id)
                }
            })
        },

        async deletePlayer(id) {
            uni.showLoading({ title: '删除中', mask: true })
            const res = await this.xwy_api.deleteRecords(69, id)
            if (res?.status !== 1) return this.xwy_api.alert(res?.info || '删除失败')
            uni.showToast({
                title: '删除成功',
                icon: 'success'
            })
            await this.getSortSettingList(true)
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
}

.type-options {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    z-index: 99;
    .type-item {
        width: calc(100% / 3);
        box-sizing: border-box;
        height: 40px;
        line-height: 40px;
        text-align: center;
        color: #666;
    }
    .type-active {
        color: #1e88e5;
        border-bottom: 2px solid #1e88e5;
    }
}

.sort-list {
    padding-top: 1px;
    padding-bottom: 15px;
}
.sort-item {
    margin: 20px;
    padding: 10px;
    border-radius: 5px;
    .user-info {
        padding: 10px 0;
    }

    $headimg-size: 50px;
    .headimg {
        width: $headimg-size;
        min-width: $headimg-size;
        height: $headimg-size;
        border-radius: 50%;
    }
    .user-name {
        line-height: $headimg-size;
    }
}
</style>
