<template>
    <view class="page">
        <canvas
            id="canvas"
            canvas-id="canvas"
            class="canvas"
            :style="{ width: canvas_width + 'px', height: canvas_height + 'px' }"
        ></canvas>
        <image
            class="result-image"
            v-if="result_image"
            :src="result_image"
            mode="widthFix"
            @click="previewResultImage()"
        />

        <view class="btns text-center flex-kai">
            <view
                class="choose-image-btn color-primary"
                hover-class="navigation-hover"
                @click="chooseImage"
            >{{ user_path ? '重新选图' : '选择图片'}}
            </view>
            <view
                class="confirm-btn color-white bg-primary"
                hover-class="navigation-hover"
                @click="confirm"
            >发布
            </view>
        </view>

        
    </view>
</template>

<script>
const app = getApp()
export default {
    data() {
        return {
            canvas_width: 0,
            canvas_height: 0,
            result_image: '',
            user_path: ''
        }
    },

    onLoad(params) {
        this.loading = true
        uni.showLoading({
            title: '加载中...',
            mask: true
        })
        this.id = params.id
        this.point_id = params.point_id
        this.getPointDetails()
    },

    methods: {
        async getPointDetails() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/map_point_details',
                data: { id: this.point_id }
            })

            const details = res?.data?.['map_point_details']
            if (!details) {
                uni.hideLoading()
                this.loading = false
                return this.xwy_api.alert('点位详情获取失败', { success: () => uni.navigateBack() })
            }
            const together_pic = details.conf.together_pic
            if (!together_pic?.open) {
                uni.hideLoading()
                this.loading = false
                return this.xwy_api.alert('该点位未配置照片合成功能', { success: () => uni.navigateBack() })
            }
            if (!together_pic?.background_src) {
                uni.hideLoading()
                this.loading = false
                return this.xwy_api.alert('该点位照片合成未配置背景图', { success: () => uni.navigateBack() })
            }
            const { background_src, top, left, width, height } = together_pic
            this.getBackgroundImageInfo(background_src, top, left, width, height)
        },

        getBackgroundImageInfo(background_src, top, left, width_, height_) {
            uni.getImageInfo({
                src: background_src,
                success: res => {
                    const max = 1000
                    const { width, height } = res
                    let canvas_width = width, canvas_height = height
                    let scale = 1
                    if (canvas_width > max) {
                        scale = max / width
                        canvas_height = height * scale
                        canvas_width = max
                    }
                    if (canvas_height > max) {
                        scale = max / height
                        canvas_width = width * scale
                        canvas_height = max
                    }
                    this.canvas_width = canvas_width
                    this.canvas_height = canvas_height
                    this.scale = scale

                    this.top = (top || 0) * scale
                    this.left = (left || 0) * scale
                    this.width = (width_ || 0) * scale
                    this.height = (height_ || 0) * scale
                    this.background_path = res.path
                    this.ctx = uni.createCanvasContext('canvas', this)
                    this.drawCanvas()
                },
                fail: err => {
                    console.log('图片信息获取失败: ', err)
                    uni.hideLoading()
                    this.loading = false
                    this.xwy_api.alert('背景图片信息获取失败', { success: () => uni.navigateBack() })
                }
            })
        },

        chooseImage() {
            uni.chooseImage({
                count: 1,
                success: res => {
                    this.user_path = res.tempFilePaths[0]
                    this.drawCanvas()
                },
                fail: err => console.log(err)
            })
        },

        drawCanvas() {
            this.loading = true
            if (this.user_path) {
                uni.showLoading({
                    title: '',
                    mask: true
                })
            }
            this.ctx.drawImage(this.background_path, 0, 0, this.canvas_width, this.canvas_height)
            if (this.user_path) {
                this.ctx.drawImage(this.user_path, this.left, this.top, this.width, this.height)
            }
            this.draw()
        },

        draw() {
            this.ctx.draw(true, () => {
                this.canvasToTempFilePath()
            })
        },

        canvasToTempFilePath() {
            uni.canvasToTempFilePath({
                canvasId: 'canvas',
                // fileType: 'jpg',
                // quality: 0.1,
                success: res => {
                    uni.hideLoading()
                    this.loading = false
                    console.log('绘画的图片', res)
                    if (this.user_path) uni.showToast({ title: '已合成，快发布吧', icon: 'none' })
                    this.result_image = res.tempFilePath
                },
                fail: err => {
                    console.log('绘画失败', err)
                    this.canvasToTempFilePath()
                }
            })
        },

        previewResultImage() {
            uni.previewImage({
                urls: [this.result_image]
            })
        },

        confirm() {
            if (!this.user_path) return uni.showToast({ title: '请先选择图片', icon: 'error' })
            if (this.loading) return uni.showToast({ title: '正在操作中，请稍后', icon: 'none' })
            this.publish()
        },

        async publish() {
            this.loading = true
            uni.showLoading({
                title: '发布中...',
                mask: true
            })
            const src = await this.xwy_api.uploadOneImage({
                active_id: this.id,
                temp_data: { path: this.result_image },
                name: '活动合成照片',
                is_temp: 12
            })

            if (!src) {
                uni.hideLoading()
                this.loading = false
                return uni.showToast({ title: '图片保存失败，请重试', icon: 'none' })
            }

            const res = await this.xwy_api.request({
                url: 'front.user.reply/submit_content',
                data: {
                    content: ' ',
                    types: 6,
                    nickname: app.globalData['userinfo']?.nickname || '',
                    headimg: app.globalData['userinfo']?.headimg || '',
                    pid: this.id,
                    bind_id: this.point_id,
                    pic_list: JSON.stringify([src]),
                    is_long_id: 1
                }
            })

            if (res?.status !== 1) {
                uni.hideLoading()
                this.loading = false
                return uni.showToast({ title: res?.msg || '发布失败，请重试', icon: 'none' })
            }

            // 调用奖励接口，发布运动圈接口不会自动奖励，需要手动调用接口奖励
            await this.xwy_api.request({
                url: 'front.flat.sport_step.integral.togetherPic/together_pic_reward_integral',
                data: {
                    active_id: this.id,
                    point_id: this.point_id
                }
            })

            uni.hideLoading()
            this.loading = false

            uni.showToast({ title: '发布成功', icon: 'success' })
            const OBE = this.getOpenerEventChannel()
            OBE?.emit?.('refresh')
            setTimeout(() => {
                uni.navigateBack()
            }, 1000)
        }
    }
}
</script>

<style lang="scss">
.page {
    padding-bottom: 80px;
}
.canvas {
    position: fixed;
    top: -9999999px;
}
.result-image {
    width: 100%;
    height: auto;
    display: block;
}
.btns {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    border-top: 1px solid #eee;
    border-radius: 10px 10px 0 0;
    padding: 30rpx;
    box-sizing: border-box;
    .choose-image-btn, .confirm-btn {
        width: 330rpx;
        height: 44px;
        line-height: 44px;
        box-sizing: border-box;
        border-radius: 22px;
    }
    .choose-image-btn {
        line-height: 42px;
        border: 1px solid #2d8cf0;
    }
}
</style>
