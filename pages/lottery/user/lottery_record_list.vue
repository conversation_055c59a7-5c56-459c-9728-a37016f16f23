<template>
	<view class="page">
		<view class="top-bar flex-row bg-white">
			<view
				class="top-bar-item text-center"
				:class="{'top-bar-active': item.id === is_lottery}"
				v-for="(item, index) in is_lottery_list" :key="index"
				:style="'width: calc(100% / ' + is_lottery_list.length + ')'"
				@click="isLotteryChange(item.id)"
			>{{item.title}}</view>
		</view>

		<view class="search bg-white flex-kai p10">
			<view class="input-view">

				<picker :range="search_key_opt" range-key="title" @change="search_key = search_key_opt[$event.detail.value].key">
					<view class="searck-key color-sub font12">
						按{{search_key_opt.find(v => v.key === search_key).title}}搜索
						<uni-icons type="forward" size="12" color="#80848f"/>
					</view>
				</picker>
				<view class="search-icon left-icon flex-all-center">
					<uni-icons type="search" size="20" color="#bbbec4"/>
				</view>
				<input
					class="input bg-background"
					type="text"
					confirm-type="search"
					v-model="search_keyword"
					@confirm="search"
					placeholder="在此输入搜索"
					placeholder-style="color:#bbbec4"
				/>
				<view class="search-icon right-icon flex-all-center" @click="search_keyword = ''">
					<uni-icons v-if="search_keyword" type="close" size="20" color="#bbbec4"/>
				</view>
			</view>
			<view class="search-go color-info" @click="search">搜索</view>
		</view>

		<view v-if="total_count" class="ptm5 text-center font14 color-sub">共{{total_count}}条记录</view>

		<view class="list">
			<view class="list-item color-content" v-for="(item, index) in list" :key="index">
				<view v-if="is_admin">
					<text>抽奖用户:</text>
					<text class="pl5">
            {{ item.user_details && item.user_details.must_submit && item.user_details.must_submit[0] && item.user_details.must_submit[0].value || item.nickname || '用户已删除' }}
          </text>
				</view>
				<view>
					<text>活动名称:</text>
					<text class="pl5">{{item.conf_json.lottery_active_name}}</text>
				</view>
				<view>
					<text>抽奖状态:</text>
					<template>
						<text v-if="item.is_lottery" class="pl5">已中奖</text>
						<text v-else class="pl5 color-sub">未中奖</text>
					</template>
				</view>
				<view v-if="item.conf_json.get_redpack_amount">
                    <text>红包金额:</text>
                    <text class="pl5">{{ item.conf_json.get_redpack_amount }}元</text>
                </view>
				<template v-if="item.is_lottery">
					<view>
						<text>获得奖品:</text>
						<text class="pl5">{{item.conf_json.lottery_name}}</text>
					</view>
					<template v-if="item.conf_json.prize_types === 0">
                        <view @click="copy(item.conf_json.sn_code)">
                            <text>中奖编码:</text>
                            <text class="pl5">{{ item.conf_json.sn_code }}</text>
                            <text class="pl5 iconfont icon-copy color-sub"></text>
                        </view>
                        <view>
                            <text>兑奖状态:</text>
                            <template>
                                <text v-if="item.have_send" class="pl5">已兑奖</text>
                                <text v-else class="pl5 color-error">未兑奖</text>
                            </template>
                        </view>
                    </template>
				</template>
				<view class="color-sub">
					<text>抽奖时间:</text>
					<text class="pl5">{{item.create_time}}</text>
				</view>
				<view v-if="is_admin" class="flex-kai">
					<view></view>
					<view class="flex-row">
						<view v-if="item.is_lottery" class="color-primary pr10" @click="recordIsLotteryChange(index)">更改兑奖状态</view>
						<view class="color-error" @click="deleteRecord(item.record_id)">删除记录</view>
					</view>
				</view>
			</view>
		</view>

		<view v-if="!loading && !list.length" class="text-center" style="padding-top: 10vh;">
			<text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
			<view class="color-sub">暂无抽奖记录</view>
		</view>

		<uni-load-more v-if="loading && load_page > 1" status="loading"></uni-load-more>
		<uni-load-more v-if="is_last_page && list.length > 5" status="noMore" :contentText="{contentnomore: '我是有底线的'}"></uni-load-more>
		<uni-load-more v-if="!is_last_page && !loading && list.length" status="more"></uni-load-more>

	</view>
</template>

<script>
	const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'


	export default {
		data() {
			return {
				loading: true,
				id: '',
				list: [],
				load_page: 1,
				is_last_page: false,
				total_count: 0,
				is_lottery_list: [
					{id: 0, title: '全部'},
					{id: 1, title: '已中奖'},
					{id: 2, title: '未中奖'}
				],
				is_lottery: 1,  //【1】只查看已中奖的记录 【2】只查看未中奖的记录
				is_admin: 0,
				search_key_opt: [
					{key: 'nickname', title: '用户昵称'},
					{key: 'sn_code', title: '中奖编码'}
				],
				search_key: 'nickname',
				search_keyword: ''
			}
		},
		onLoad(e) {
			if (e.is_admin) this.is_admin = 1
			this.active_id = e.active_id
			uni.showLoading({
				title: '加载中...',
				mask: true
			})

			login.uniLogin(err => {
				if (err && err.errMsg) {
					this.loading = false
					uni.hideLoading()
					uni.showModal({
						title: err.errTitle || '提示',
						content: err.errMsg,
						showCancel: false
					})
					return false
				}

				this.getList()
			})
		},

		onReachBottom() {
			if (!this.is_last_page && !this.loading && this.list.length) this.getList()
		},

		methods: {
			isLotteryChange(id) {
				if (id === this.is_lottery) return false
				this.is_lottery = id
				this.load_page = 1
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.getList()
			},

			search() {
				this.load_page = 1
				this.getList()
			},

			async getList() {
				if (this.load_page === 1) {
					this.total_count = 0
					this.is_last_page = false
					this.list = []
				}
				this.loading = true

				const data = {
					access_token: app.globalData.access_token,
					active_id: this.active_id,
					is_lottery: this.is_lottery,
					is_admin: this.is_admin,
					page: this.load_page,
					perpage: 20,
                    get_all_prize: 1
				}
				if (this.search_keyword) data[this.search_key] = this.search_keyword

				const res = await xwy_api.request({
					url: 'front.flat.active.lottery_active.user.lottery/lottery_records_list',
					data
				})

				this.loading = false
				uni.hideLoading()

				if (res?.data?.lottery_record_list) {
					const data = res.data.lottery_record_list
					const list = data.data || []
					this.list = [...this.list, ...list]
					this.is_last_page = data.is_lastpage
					this.total_count = data.total
					this.load_page++
				} else {
					this.is_last_page = true
				}
			},

			deleteRecord(id) {
				uni.showModal({
					title: '提示',
					content: `确定删除该记录？`,
					success: res => {
						if (res.confirm) this.deleteRecordAjax(id)
					}
				})
			},

			async deleteRecordAjax(id) {
				uni.showLoading({
					title: '删除中...',
					mask: true
				})
				const res = await xwy_api.deleteRecords(1, id)
				if (!res?.status) {
					xwy_api.alert(res?.info || '删除失败')
					return false
				}
				uni.showToast({
					title: res.info || '删除成功',
					icon: 'success'
				})
				this.search()
			},

			recordIsLotteryChange(index) {
				const item = this.list[index]
				uni.showModal({
					title: '提示',
					content: `确定将中奖编码为 ${item.conf_json.sn_code} 的记录更改为 ${item.have_send ? '未兑奖' : '已兑奖'}？`,
					success: res => {
						if (res.confirm) this.isLotteryChangeAjax(index)
					}
				})
			},

			async isLotteryChangeAjax(index) {
				uni.showLoading({
					title: '更改中...',
					mask: true
				})

				const item = this.list[index]

				const res = await xwy_api.request({
					url: 'front.flat.active.lottery_active.admin.manage_lottery/admin_set_lottery_goods',
					data: {
						access_token: app.globalData.access_token,
						lottery_active_id: item.active_id,
						have_send: item.have_send ? 2 : 1,
						ids: item.record_id
					}
				})

				if (!res || !res.status) {
					xwy_api.alert(res && res.info || '更改失败')
					return false
				}

				uni.showToast({
					title: '更改成功',
					icon: 'success'
				})

				this.list[index].have_send = item.have_send ? 0 : 1
			},

			copy(data) {
				uni.setClipboardData({
					data,
					success: () => {
						uni.showToast({
							title: '中奖编码已复制',
							icon: 'success'
						})
					}
				})
			}
		}
	}
</script>

<style>
	.page {
		padding-top: 100px;
		padding-bottom: 15px;
	}
	.top-bar, .search {
		position: fixed;
		width: 100%;
		top: 0;
		left: 0;
	}
	.top-bar-item {
		height: 40px;
		line-height: 40px;
		box-sizing: border-box;
	}
	.top-bar-active {
		color: #2d8cf0;
		border-bottom: 2px solid #2d8cf0;
	}

	.search {
		top: 40px;
		box-sizing: border-box;
	}
	.search, .search .input {
		height: 40px;
		line-height: 40px;
	}
	.input-view {
		position: relative;
		width: 100%;
		height: 40px;
	}
	.searck-key {
		position: absolute;
		left: 5px;
		top: 0;
		width: 100px;
		border-right: 1px solid #fff;
	}
	.search-icon {
		position: absolute;
		width: 30px;
		height: 40px;
		top: 0;
	}
	.left-icon{
		left: 105px;
	}
	.right-icon {
		right: 0;
	}
	.search .input {
		/* width: calc(100% - 80px); */
		padding-right: 30px;
		padding-left: 135px;
		border-radius: 20px;
		box-sizing: border-box;
	}
	.search-go {
		width: 50px;
		height: 40px;
		min-width: 50px;
		text-align: right;
	}

	.list-item {
		padding: 20px 10px;
		border-bottom: 1px solid #eee;
		line-height: 24px;
	}
</style>
