<template>
    <view class="page">
        <view class="page-main">
            <template v-if="detail">
                <view v-if="lucky_draw_method === 'wheel'">
                    <LuckyWheel
                        ref="myLucky"
                        width="300px"
                        height="300px"
                        :blocks="[{
                            padding: '24px',
                            background: '#ef9050',
                            imgs: [{
                                src: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/window/almost-lottery__bg.png',
                                width: '300px',
                                height: '300px',
                                rotate: false
                            }]
						}]"
                        :prizes="prizes"
                        :buttons="[{
                            radius: '50px',
                            imgs: [{
                                src: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/window/almost-lottery__action.png',
                                top: '-50px'
                            }]
                        }]"
                        :defaultConfig="{gutter: '1px'}"
                        @start="startCallBack"
                        @end="endCallBack"
                    />

                </view>

                <view v-if="lucky_draw_method === 'grid'">
                    <LuckyGrid
                        ref="myLucky"
                        width="300px"
                        height="300px"
                        :blocks="[{background: '#e7c683', padding: '10px', borderRadius: '10px'}]"
                        :buttons="[
                            {
                                x: 1,
                                y: 1,
                                background: '#fe6820',
                                fonts: [
                                    { text: '开始', fontColor: '#ffffff', fontSize: '24px', top: '16px' },
                                    { text: '抽奖', fontColor: '#ffffff', fontSize: '24px', top: '44px' }
                                ]
                            }
                        ]"
                        :prizes="prizes"
                        @start="startCallBack"
                        @end="endCallBack"
                    />
                </view>

                <view class="p10">
                    <view class="flex-all-center">

                        <navigator class="color-sub p5"
                                   :url="'/pages/lottery/user/lottery_record_list?active_id=' + active_id">
                            抽奖记录
                        </navigator>
                    </view>
                </view>


                <view class="p10 color-content" style="padding-top: 30px;">
                    <!--雷子客户定制开发，不显示这个 beb06bf8a320124c5d9d084be54d6fdd-->
                    <view v-if="active_id !== 'beb06bf8a320124c5d9d084be54d6fdd'">
                        总共可抽奖{{ detail.all_times }}次 (每天可抽{{ detail.everyd_times }}次)。
                    </view>
                    <view v-if="detail.content" class="pt15">
                        <text decode="true" space="nbsp">{{ detail.content }}</text>
                    </view>
                </view>
            </template>


            <view v-if="lottery_list.length > 1" class="more-list" @click="lookMoreLottery">
                <view>更</view>
                <view>多</view>
                <view>抽</view>
                <view>奖</view>
            </view>
        </view>

    </view>
</template>

<script>
const app = getApp()
import LuckyWheel from '../components/@lucky-canvas/uni/lucky-wheel.vue' // 大转盘
import LuckyGrid from '../components/@lucky-canvas/uni/lucky-grid.vue' // 九宫格

const no_prize_data = {
    name: '谢谢参与',  // 由于有客户要求自定义，最后大家决定改为“谢谢参与”比较中性
    img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/window/xiexiehuigu.png'
}

export default {
    components: {
        LuckyWheel,
        LuckyGrid
    },
    data() {
        return {
            prizes: [],
            detail: null,
            active_id: '',
            lottery_list: [],
            lucky_draw_method: ''
        }
    },
    onLoad(e) {
        this.active_id = e.active_id
        this.type = e.type || 'point'
        if (e.id) this.id = e.id
        if (e.point_id) this.point_id = e.point_id
        this.$uni.showLoading('加载中...')

        this.$login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getLotteryList()
        })
    },
    methods: {
        // 点击抽奖按钮触发回调
        async startCallBack() {
            if (this.in_play) return  // 防止连续点击
            if (this.lottery_list.length) {
                const lottery_detail = this.lottery_list.find(v => v['lottery_details'].active_id === this.id)
                if (lottery_detail?.['lottery_lock'] === 1) {
                    return this.$uni.showModal(`需要到达 ${lottery_detail.name || ''} 才能抽奖哦！`)
                }
            }
            if (!this.prizes.length) return this.$uni.showModal('该活动暂未设置奖品')

            const integral = Number(this.activeDetails?.conf?.active?.lottery_cost_integral)
            if (integral) {
                const unit = this.activeDetails?.conf?.active?.integral?.unit
                const res = await this.$uni.showModal(`抽奖需要消耗${integral}${unit}, 确定抽奖?`, {showCancel: true})
                if (!res.confirm) return
            }

            // 先开始旋转
            this.$refs.myLucky.play()

            this.in_play = true

            const res = await this.startLottery()

            if (res?.status !== 1) {
                await this.$uni.showModal(res?.info || '抽奖失败')
                return this.$refs.myLucky.init()
            }

            const result = res.data?.['lottery_result']
            const details = result?.data?.['lottery_details']

            if (!details) {
                return this.$refs.myLucky.init()
            }

            let lucky_info = result.info || '恭喜'
            if (result.data.redpack_details?.info) lucky_info = result.data.redpack_details.info
            this.lucky_info = lucky_info

            const prize_id = details.id
            if (!prize_id) {
                let index = 0
                if (this.lucky_draw_method === 'grid') index = this.prizes.findIndex(v => !v.id)
                return this.$refs.myLucky.stop(index)
            }

            let prize_index = this.prizes.findIndex(v => prize_id === v.id)
            prize_index = prize_index < 0 ? 0 : prize_index
            this.$refs.myLucky.stop(prize_index)
        },

        async startLottery() {
            this.lucky_info = ''

            const type_options = {
                just_lottery: {
                    url: 'front.flat.active.lottery_active.user.lottery/lottery_act',
                    data: {
                        lottery_active_id: this.id,
                        sport_step_active_id: this.active_id,
                        active_id: this.active_id,
                        point_id: this.point_id
                    }
                },
                word: {
                    url: 'front.flat.active.collect_word.user.collectWord/together_lottery',
                    data: {
                        active_id: this.active_id
                    }
                },
                point: {
                    url: 'front.flat.sport_step.lottery/arrive_point_lottery',
                    data: {
                        active_id: this.active_id,
                        point_id: this.point_id
                    }
                },
                sign: {
                    url: 'front.flat.sport_step.lottery/sign_submit_num_lottery',
                    data: {
                        active_id: this.active_id,
                        lottery_id: this.id
                    }
                }
            }

            return this.xwy_api.request(type_options[this.type])
        },


        // 抽奖结束触发回调
        endCallBack(prize) {
            this.in_play = false
            if (!this.lucky_info) return
            this.$uni.showModal(this.lucky_info, {title: prize.id ? '中奖啦' : '未中奖'})
        },

        async getActivityDetails() {
            if (!this.active_id) return

            let details = app.globalData['activity_detail']
            if (!details || details.active_id !== this.active_id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.active_id
                    }
                })
                details = res?.data?.active_details
            }

            this.activeDetails = details
        },

        async getLotteryList() {
            await this.getActivityDetails()

            if (this.type === 'word' || this.id) return await this.getLotteryDetail()

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/point_exam_list',
                data: {
                    active_id: this.active_id,
                    get_types: 2
                }
            })
            const lottery_list = res?.data?.['point_exam_list'] || []
            if (!lottery_list.length) {
                uni.hideLoading()
                return this.$uni.showModal('本活动暂未绑定抽奖活动', {success: () => uni.navigateBack()})
            }

            this.lottery_list = lottery_list
            let lottery_id = lottery_list[0]['lottery_details'].active_id
            let point_id = lottery_list[0].id
            const reverse_lottery_list = lottery_list.slice().reverse()
            const unlock = reverse_lottery_list.find(v => v['lottery_lock'] !== 1)
            if (unlock) {
                lottery_id = unlock['lottery_details'].active_id
                point_id = unlock.id
            }
            this.id = lottery_id
            this.point_id = point_id
            await this.getLotteryDetail()
        },

        lookMoreLottery() {
            uni.navigateTo({
                url: '/pages/activity/other/answer/list?get_types=2&new=1&id=' + this.active_id,
                events: {
                    changeLotteryDetails: data => {
                        if (data.id === this.id && data.point_id === this.point_id) return
                        uni.showLoading({mask: true})
                        this.id = data.id
                        this.point_id = data.point_id
                        this.getLotteryDetail()
                    }
                }
            })
        },

        async getLotteryDetail() {
            this.detail = null

            const res = await this.xwy_api.request({
                url: 'front.flat.active.lottery_active.admin.manage_lottery/lottery_active_details',
                data: {active_id: this.id}
            })

            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '抽奖活动配置获取失败', {
                success: () => uni.navigateBack()
            })

            const data_ = res?.data
            const details = data_?.active_details
            if (!details) return this.$uni.showModal('抽奖活动配置获取失败', {success: () => uni.navigateBack()})

            this.lucky_draw_method = details.conf?.lucky_draw_method || 'wheel'
            this.detail = details

            this.$uni.setNavigationBarTitle(this.detail.title)

            const prize_list = data_['prize_list'] || []
            if (!prize_list.length) {
                return this.$uni.showModal('该活动暂未设置奖品', {success: () => uni.navigateBack()})
            }

            this.initPrize(prize_list)
        },

        initPrize(prize_list) {
            this.prizes = []
            if (!prize_list.length) return

            if (this.lucky_draw_method === 'wheel') this.initWheelPrize(prize_list)
            if (this.lucky_draw_method === 'grid') this.initGridPrize(prize_list)
        },

        initWheelPrize(prize_list) {
            const setImgs = src => [{src, top: '40%', width: '40px', height: '40px'}]

            const prizes = [{
                fonts: [{text: no_prize_data.name, fontSize: '14px', top: '10%'}],
                background: '#ffffff',
                imgs: setImgs(no_prize_data.img)
            }]

            prize_list.forEach((v, i) => {
                const item = {
                    id: v.id,
                    fonts: [{text: v.name, fontSize: '14px', top: '10%'}],
                    background: i % 2 === 0 ? '#f8d2a0' : '#ffffff',
                    imgs: setImgs(v.pic || no_prize_data.img)
                }

                prizes.push(item)
            })
            this.prizes = prizes
        },

        initGridPrize(prize_list) {
            const maxPrizeLength = 7
            const prizeLength = prize_list.length
            if (prizeLength > maxPrizeLength) prize_list = prize_list.slice(0, maxPrizeLength)
            const no_prizes = {name: no_prize_data.name}
            prize_list.push(...new Array(maxPrizeLength + 1 - prizeLength).fill(no_prizes))

            // 打乱数组，避免后面追加的“谢谢惠顾”排在一起
            if (prizeLength < maxPrizeLength) prize_list = this._utils.shuffleArray(prize_list)

            const positions = [
                {x: 0, y: 0},
                {x: 1, y: 0},
                {x: 2, y: 0},
                {x: 2, y: 1},
                {x: 2, y: 2},
                {x: 1, y: 2},
                {x: 0, y: 2},
                {x: 0, y: 1}
            ]

            this.prizes = prize_list.map((v, i) => ({
                x: positions[i].x,
                y: positions[i].y,
                id: v.id || '',
                fonts: [{text: v.name, fontSize: '14px', top: '60px'}],
                background: '#ffffff',
                imgs: [{
                    src: v.pic || no_prize_data.img,
                    top: '15px',
                    width: '40px',
                    height: '40px'
                }],

                // 九宫格奖品样式去掉圆角，要方的
                // 雷子 for 2023-07-04 口头说的
                borderRadius: '0px'
            }))
        }
    }
}
</script>

<style>
.page {
    min-height: 100vh;
    box-sizing: border-box;
    background-color: #fde3ac;
    background-image: url("https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/window/luckydraw_bg.jpg");
    background-size: 100%;
    background-repeat: no-repeat;
}

.page-main {
    position: relative;
    padding-top: calc((480 / 780) * 100%);
    /*margin-top: calc((280 / 780) * 100%);*/
}

.more-list {
    position: absolute;
    right: 0;
    top: 270px;
    text-align: center;
    line-height: 16px;
    font-size: 14px;
    background-color: #feb62e;
    color: #fff;
    padding: 10px;
    border-radius: 10px 0 0 10px;
}
</style>
