<template>
    <view class="page bg-background">
        <view v-if="step !== null" class="my-step bg-white p10">
            <text class="color-content">我的步数:</text>
            <text class="step-value color-success font18">{{ step }}</text>
            <text class="color-sub font12">步</text>
        </view>

        <view class="lottery-list">
            <view class="lottery-item flex-kai" v-for="item in lotteryList" :key="item.lottery_id">
                <view class="lottery-info">
                    <view class="color-title">{{ item.title }}</view>
                    <view class="font14 color-sub pt5">{{ item.min }}步 - {{ item.max }}步 可抽奖</view>
                </view>
                <view :class="item.unlock ? 'button-unlock' : 'button-lock'" @click="toLottery(item)">
                    <text v-if="!item.unlock" class="iconfont icon-lock"></text>
                    <text>去抽奖</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            step: null,
            lotteryList: []
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.init()
    },

    methods: {
        async init() {
            this.$uni.showLoading()
            this.step = await this.getTodayExchangeStep()
            this.lotteryList = await this.getLotteryList()
            uni.hideLoading()
        },

        async getTodayExchangeStep() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.exchange/exchange_step_list',
                data: {
                    active_id: this.active_id,
                    exchange_date: this._utils.getDay(0, true)
                }
            })

            return res?.data?.['exchange_list']?.data?.[0]?.step || 0
        },

        async getLotteryList() {
            return new Promise(resolve => {
                try {
                    const eventChannel = this.getOpenerEventChannel()
                    if (!eventChannel) {
                        console.warn('无法获取事件通道')
                        return resolve([])
                    }

                    // 设置超时处理
                    const timeout = setTimeout(() => {
                        console.warn('获取抽奖列表超时')
                        resolve([])
                    }, 3000) // 3秒超时

                    eventChannel.once('stepLotteryList', list => {
                        clearTimeout(timeout)
                        if (!list || !Array.isArray(list)) {
                            console.warn('获取到的抽奖列表无效')
                            return resolve([])
                        }
                        resolve(list.map(item => ({
                            ...item,
                            unlock: this.step >= item.min && this.step < item.max
                        })))
                    })
                } catch (error) {
                    console.error('获取抽奖列表出错:', error)
                    resolve([])
                }
            })
        },

        toLottery(item) {
            if (!item.unlock) return this.$uni.showModal(`今日步数需要在${item.min}步到${item.max}之间步才能抽奖。你今日步数为${this.step}步, 不符合抽奖条件。`)

            this.$uni.navigateTo(`/pages/lottery/user/lottery?type=just_lottery&id=${item.lottery_id}&active_id=${this.active_id}`)
        }
    }
}
</script>

<style lang="scss" scoped>
.page {
    min-height: 100vh;
    box-sizing: border-box;
}

.my-step {
    .step-value {
        padding-left: 4px;
        padding-right: 2px;
    }
}

.lottery-item {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
    background-color: #fff;
    box-shadow: 0 0 5px 2px #eee;
}

.button-lock, .button-unlock {
    width: 100px;
    min-width: 100px;
    margin-left: 10px;
    text-align: center;
    height: 40px;
    line-height: 40px;
    box-sizing: border-box;
    border-radius: 20px;
}

.button-unlock {
    background-color: #ff9900;
    color: #fff;
}

.button-lock {
    background-color: #f8f8f8;
    color: #bbbec4;
    line-height: 38px;
    border: 1px solid #bbbec4;
}
.button-lock .iconfont {
    padding-right: 3px;
}
</style>