<template>
    <view class="page">
        <view v-if="success" class="save-success">
            <view class="icon text-center">
                <uni-icons type="checkbox-filled" size="100" color="#19be6b"/>
                <view class="color-content font16">活动{{ id ? '修改' : '创建' }}成功！</view>
            </view>
            <view class="flex-all-center">
                <button class="bg-success color-white" style="width: 200px;" @click="lookMyActivityList">
                    查看我的活动
                </button>
            </view>
        </view>

        <template v-else>
            <view class="form">
                <view class="form-item">
                    <view class="top color-content">
                        <text>活动名称</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="text" v-model="title" placeholder="请输入活动名称"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">活动开始时间</view>
                    <view class="bottom font16">
                        <view class="view flex-row">
                            <uni-datetime-picker
                                :value="begin_time"
                                :border="false"
                                :clear-icon="false"
                                @change="begin_time = $event"
                            />
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">活动结束时间</view>
                    <view class="bottom font16">
                        <view class="view flex-row">
                            <uni-datetime-picker
                                :value="end_time"
                                :border="false"
                                :clear-icon="false"
                                @change="end_time = $event"
                            />
                        </view>
                    </view>
                </view>

                <!--盲盒抽奖时不能设置红包抽奖，因为盲盒抽奖的奖品类型是2，红包抽奖的奖品类型是1，不能共存-->
                <view v-if="redpack && !mystery_box" class="form-item">
                    <view class="top color-content">
                        <view>红包抽奖</view>
                        <view class="color-sub font12">开启后抽奖可以抽红包</view>
                    </view>
                    <view class="bottom font16">

                        <picker :range="['关闭', '开启']" :value="redpack_open"
                                @change="redpack_open = Number($event.detail.value)">
                            <view class="view flex-kai">
                                <view>{{ redpack_open ? '开启' : '关闭' }}</view>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </picker>
                    </view>
                </view>

                <view v-show="conf.lottery_type === 'normal'" class="form-item">
                    <view class="top color-content">抽奖方式</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="option_config.lucky_draw_method"
                                range-key="title"
                                :value="option_config.lucky_draw_method.findIndex(v => v.value === conf.lucky_draw_method)"
                                @change="conf.lucky_draw_method = option_config.lucky_draw_method[$event.detail.value].value"
                            >
                                {{ option_config.lucky_draw_method.find(v => v.value === conf.lucky_draw_method).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form">
                    <view class="form-item">
                        <view class="top color-content">
                            <text>总抽奖次数</text>
                            <text class="color-error font16">*</text>
                            <text class="color-sub font12 pl5">(每人总共可抽奖次数)</text>
                        </view>
                        <view class="bottom font16">
                            <input
                                class="input"
                                type="number"
                                v-model="all_times"
                                placeholder="活动总共可抽奖的次数"
                            />
                        </view>
                    </view>
                </view>

                <view class="form">
                    <view class="form-item">
                        <view class="top color-content">
                            <text>日抽奖次数</text>
                            <text class="color-error font16">*</text>
                            <text class="color-sub font12 pl5">(每人每天可抽奖次数)</text>
                        </view>
                        <view class="bottom font16">
                            <input
                                class="input"
                                type="number"
                                v-model="everyd_times"
                                placeholder="活动单位用户每日可抽奖的次数"
                            />
                        </view>
                    </view>
                </view>

                <!-- <view class="form-item">
                    <view class="top color-content">是否显示奖品</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                mode="selector"
                                :range="['不显示奖品', '显示奖品']"
                                :value="show_prize"
                                data-isnumber="1"
                                @change="pickerChange($event, 'show_prize')"
                            >
                                {{ show_prize === 0 ? '不显示奖品' : '显示奖品' }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view> -->

                <view class="form-item">
                    <view class="top color-content">中奖限制</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="option_config.lotteryedNotLottery"
                                range-key="title"
                                :value="option_config.lotteryedNotLottery.findIndex(v => v.value === conf.lotteryedNotLottery)"
                                @change="conf.lotteryedNotLottery = option_config.lotteryedNotLottery[$event.detail.value].value"
                            >
                                {{ option_config.lotteryedNotLottery.find(v => v.value === conf.lotteryedNotLottery).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>


                <view v-if="option_config.lottery_type.length > 1" class="form-item">
                    <view class="top color-content">活动类型</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                :range="option_config.lottery_type"
                                range-key="title"
                                :value="option_config.lottery_type.findIndex(v => v.value === conf.lottery_type)"
                                @change="lotteryTypeChange"
                            >
                                {{ option_config.lottery_type.find(v => v.value === conf.lottery_type).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <template v-if="page_diy_set && conf.lottery_type === 'normal'">
                    <view class="form-item">
                        <view class="top color-content">
                            <view>{{ activeTypesText }} 关联</view>
                            <view class="color-sub font12">关联后，需要参与关联的{{ activeTypesText }}才能抽奖。</view>
                        </view>
                        <view class="bottom font16">
                            <view class="pt10">
                                <view class="forms-picker flex-kai" style="margin-top: 10px;"
                                      v-for="(item, index) in conf.limit.active_list" :key="item">
                                    <view style="width: 100%; color: #333;">
                                        <text :class="'active-exit-tag active-exit-tag-' + item.active_types">
                                            {{ option_config.active_types.find(v => v.value === item.active_types).title }}
                                        </text>
                                        <text>{{ item.name }}</text>
                                    </view>
                                    <view @click="conf.limit.active_list.splice(index, 1)">
                                        <uni-icons type="close" size="16" color="#dddee1"/>
                                    </view>
                                </view>
                            </view>

                            <view class="flex-all-center">
                                <view class="p10" @click="bindNewActiveExam">
                                    <text class="color-light-primary font14">关联{{ activeTypesText }}</text>
                                    <uni-icons type="forward" size="14" color="#5cadff"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <view>{{ activeTypesText }} 抽奖限制</view>
                            <view class="color-sub font12">
                                需要参与已关联的多少个{{ activeTypesText }}才能抽奖
                            </view>
                        </view>
                        <view class="bottom font16">
                            <input
                                class="input"
                                type="number"
                                maxlength="2"
                                v-model="conf.limit.condition"
                                placeholder="请输入需要参与的活动/答题数量"
                            />
                        </view>
                    </view>
                </template>


                <view v-if="conf.lottery_type === 'blind_box'" class="form-item">
                    <view class="top color-content">
                        <view>点位关联</view>
                        <view class="color-sub font12">关联点位后，用户到达关联的点位后可抽一次盲盒。</view>
                    </view>
                    <view class="bottom font16">
                        <view class="pt10">
                            <view class="forms-picker flex-kai" style="margin-top: 10px;"
                                  v-for="(item, index) in conf.limit.active_list" :key="item">
                                <view style="width: 100%; color: #333;">
                                    <text class="active-exit-tag">点位</text>
                                    <text>{{ item.name }}</text>
                                </view>
                                <view @click="conf.limit.active_list.splice(index, 1)">
                                    <uni-icons type="close" size="16" color="#dddee1"/>
                                </view>
                            </view>
                        </view>

                        <view class="flex-all-center">
                            <view class="p10" @click="bindPoint">
                                <text class="color-light-primary font14">关联点位</text>
                                <uni-icons type="forward" size="14" color="#5cadff"/>
                            </view>
                        </view>
                    </view>
                </view>


                <view v-if="conf.lottery_type === 'step_lottery'" class="form-item">
                    <view class="top color-content">
                        <view>活动关联</view>
                        <view class="color-sub font12">关联活动后，用户在关联活动的步数达到条件即可抽奖。</view>
                    </view>
                    <view class="bottom font16">
                        <view class="pt10">
                            <view class="forms-picker flex-kai" style="margin-top: 10px;"
                                  v-for="(item, index) in conf.limit.active_list" :key="item">
                                <view class="w-100" style="color: #333;">
                                    <view>
                                        <text class="active-exit-tag">活动</text>
                                        <text>{{ item.name }}</text>
                                    </view>
                                    <view class="flex-row pb10">
                                        <view>日步数</view>
                                        <view class="plr5" style="width: 80px;">
                                            <uni-easyinput v-model="item.min" maxlength="6"/>
                                        </view>
                                        <view> - </view>
                                        <view class="plr5" style="width: 80px;">
                                            <uni-easyinput v-model="item.max" maxlength="6"/>
                                        </view>
                                        <view>可抽奖</view>
                                    </view>
                                </view>
                                <view @click="conf.limit.active_list.splice(index, 1)">
                                    <uni-icons type="close" size="16" color="#dddee1"/>
                                </view>
                            </view>
                        </view>

                        <view class="flex-all-center">
                            <view class="p10" @click="bindActive">
                                <text class="color-light-primary font14">关联活动</text>
                                <uni-icons type="forward" size="14" color="#5cadff"/>
                            </view>
                        </view>
                    </view>
                </view>

                <template v-if="conf.lottery_type === 'task_lottery'">
                    <view class="form-item">
                        <view class="top color-content">
                            <view>活动关联</view>
                            <view class="color-sub font12">关联后，用户每天完成任务后可抽奖。</view>
                        </view>
                        <view class="bottom font16">
                            <view class="pt10">
                                <view class="forms-picker flex-kai" style="margin-top: 10px;"
                                      v-for="(item, index) in conf.limit.active_list" :key="item">
                                    <view style="width: 100%; color: #333;">
                                        <text class="active-exit-tag active-exit-tag-2">活动</text>
                                        <text>{{ item.name }}</text>
                                    </view>
                                    <view @click="conf.limit.active_list.splice(index, 1)">
                                        <uni-icons type="close" size="16" color="#dddee1"/>
                                    </view>
                                </view>
                            </view>

                            <view class="flex-all-center">
                                <view class="p10" @click="bindTaskActive">
                                    <text class="color-light-primary font14">关联活动</text>
                                    <uni-icons type="forward" size="14" color="#5cadff"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <view>抽奖限制</view>
                            <view class="color-sub font12">需要完成多少个任务才能抽奖</view>
                        </view>
                        <view class="bottom font16">
                            <input
                                class="input"
                                type="number"
                                maxlength="2"
                                v-model="conf.limit.condition"
                                placeholder="请输入需要完成的任务数量"
                            />
                        </view>
                    </view>
                </template>

                <view v-show="conf.lottery_type !== 'blind_box'" class="form-item">
                    <view class="top color-content">活动说明</view>
                    <view class="bottom font16">

                        <textarea
                            class="textarea"
                            maxlength="-1"
                            :auto-height="true"
                            v-model="content"
                            placeholder="请输入活动说明"
                        />
                    </view>
                </view>

                <view class="bottom-btn-view bg-white flex-all-center">
                    <view class="login-btn color-white text-center font18 bg-primary" @click="saveCheck">
                        {{ id ? '保存' : '创建活动' }}
                    </view>
                </view>
            </view>
        </template>
    </view>
</template>

<script>
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import base64 from '@/utils/base64.js'

export default {
    data() {
        return {
            success: false,
            id: '',
            title: '',
            begin_time: utils.getDay(0, true) + ' 00:00:00',
            end_time: utils.getDay(365, true) + ' 23:59:59',
            all_times: 100,
            everyd_times: 1,
            content: '',
            show_prize: 1,
            conf: {
                // normal: 普通抽奖    blind_box: 盲盒抽奖 盲盒抽奖需要绑定点位，兑换到达点位后接口自动抽检返回抽奖结果task_lottery: 任务抽奖 任务闯关活动完成多少个任务后可以抽奖  step_lottery: 步数抽奖 健步走活动步数达到多少步可以抽奖
                lottery_type: 'normal',
                lucky_draw_method: 'wheel',
                lotteryedNotLottery: 0,   //【1】代表开启了，已中奖的人再抽奖无法中奖。   不设置代表不判断
                limit: {
                    active_list: [],
                    condition: ''
                },
                bind_id: ''
            },
            option_config: {
                lottery_type: [{title: '普通抽奖', value: 'normal'}],
                lucky_draw_method: [
                    {title: '大转盘', value: 'wheel'},
                    {title: '九宫格', value: 'grid'}
                ],
                lotteryedNotLottery: [
                    {title: '不限制，可重复中奖', value: 0},
                    {title: '限制，每位用户只能中奖一次', value: 1}
                ],
                // active_types 活动类型 1代表是活动   2代表是考卷 3 健步走活动到达某个点位可抽盲盒
                active_types: [
                    {value: 1, title: '活动'},
                    {value: 2, title: '答题'},
                    // {value: 3, title: '点位'}
                ]
            },

            // 从开启了盲盒抽奖的活动活动管理一步步跳转进来的才能把类型设置为盲盒抽奖
            mystery_box: false,
            page_diy_set: false,
            lottery_add_step_integral: false,
            task_lottery: false,
            redpack: false,
            redpack_open: 0
        }
    },

    computed: {
        activeTypesText() {
            return this.option_config.active_types.map(item => item.title).join('/')
        }
    },


    onLoad(e) {
        this.$uni.setNavigationBarTitle(e.id ? '修改抽奖活动' : '创建抽奖活动')

        this.$uni.showLoading('加载中...')

        if (e.mystery_box) {
            this.mystery_box = true
            this.option_config.lottery_type.push({title: '盲盒抽奖', value: 'blind_box'})
        }

        if (e.lottery_add_step_integral) {
            this.lottery_add_step_integral = true
            this.option_config.lottery_type.push({title: '步数抽奖', value: 'step_lottery'})
        }

        if (e.task_lottery) {
            this.task_lottery = true
            this.option_config.lottery_type.push({title: '任务抽奖', value: 'task_lottery'})
        }

        if (e.page_diy_set) this.page_diy_set = true
        if (e.redpack) this.redpack = true

        login.uniLogin(err => {
            if (err && err.errMsg) {
                this.loading = false
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            if (e.id) {
                this.id = e.id
                return this.getDetail()
            }

            uni.hideLoading()
        })
    },
    methods: {
        async getDetail() {
            const res = await xwy_api.request({
                url: 'front.flat.active.lottery_active.admin.manage_lottery/lottery_active_details',
                data: {
                    active_id: this.id
                }
            })

            uni.hideLoading()

            if (!res?.['status'] || !res['data']?.['active_details']) {
                return this.$uni.showModal(res && res['info'] || '活动获取失败', {
                    success: () => this.$uni.navigateBack()
                })
            }

            const detail = res['data']['active_details']

            this.detailInit(detail)
        },
        detailInit(detail) {
            if (detail.title) this.title = detail.title
            if (detail.begin_time) this.begin_time = utils.timestampToTime(detail.begin_time)
            if (detail.end_time) this.end_time = utils.timestampToTime(detail.end_time)
            if (detail.all_times) this.all_times = detail.all_times
            if (detail.everyd_times) this.everyd_times = detail.everyd_times
            if (detail.content) this.content = detail.content
            if (detail.conf) {
                const conf = detail.conf
                if (conf.lucky_draw_method) this.conf.lucky_draw_method = detail.conf.lucky_draw_method
                if (conf.lotteryedNotLottery) this.conf.lotteryedNotLottery = detail.conf.lotteryedNotLottery
                if (conf.limit) {
                    this.conf.limit = detail.conf.limit
                    this.limitLotteryQuery(conf.lottery_type)
                }
                if (conf.lottery_type) this.conf.lottery_type = conf.lottery_type
                if (conf.bind_id) {
                    this.conf.bind_id = conf.bind_id
                    this.redpack_open = 1
                }
            }
            this.show_prize = detail.show_prize ? 1 : 0
        },

        limitLotteryQuery(lottery_type) {
            if (lottery_type === 'blind_box') {
                this.mystery_box = true
                if (!this.option_config.lottery_type.find(item => item.value === 'blind_box')) {
                    this.option_config.lottery_type.push({title: '盲盒抽奖', value: 'blind_box'})
                }
            } else if (lottery_type === 'step_lottery') {
                this.lottery_add_step_integral = true
                if (!this.option_config.lottery_type.find(item => item.value === 'step_lottery')) {
                    this.option_config.lottery_type.push({title: '步数抽奖', value: 'step_lottery'})
                }
            } else if (lottery_type === 'task_lottery') {
                this.task_lottery = true
                if (!this.option_config.lottery_type.find(item => item.value === 'task_lottery')) {
                    this.option_config.lottery_type.push({title: '任务抽奖', value: 'task_lottery'})
                }
            } else if (this.conf.limit.active_list?.length) {
                this.page_diy_set = true
            }
        },


        /*pickerChange(e, key) {
            let value = e.detail.value
            if (e.currentTarget.dataset['isnumber']) value = Number(value)
            this[key] = value
        },*/


        lotteryTypeChange(e) {
            const type = this.option_config.lottery_type[e.detail.value].value
            if (type === this.conf.lottery_type) return

            this.conf.lottery_type = type
            this.conf.limit.active_list = []
            this.conf.limit.condition = ''
        },


        async bindNewActiveExam() {
            const itemList = this.option_config.active_types.map(item => `关联${item.title}`)
            const res = await this.$uni.showActionSheet(itemList, {
                title: '请选择需要关联的类型'
            })
            const index = res.tapIndex
            if (index === undefined) return

            let path = '/pages/activity/user/activity_list?type=create&select=1'
            if (index === 1) path = '/pages/likou_dati/pages/exam/exam_list/exam_list?in_select=true'
            if (index === 2) path += '&select_point=1&types=2'

            this.$uni.navigateTo(path, {
                events: {
                    updateExam: data => this.addBindActiveExam(2, data),
                    selectActive: data => this.addBindActiveExam(1, data),
                    selectPoint: data => this.addBindActiveExam(3, data)
                }
            })
        },

        bindPoint() {
            this.$uni.navigateTo('/pages/activity/user/activity_list?type=create&select=1&select_point=1&types=2', {
                events: {
                    selectPoint: data => this.addBindActiveExam(3, data)
                }
            })
        },

        bindActive() {
            this.$uni.navigateTo(`/pages/activity/user/activity_list?type=create&select=1&types=2`, {
                events: {
                    selectActive: data => this.addBindActiveExam(4, data)
                }
            })
        },

        bindTaskActive() {
            this.$uni.navigateTo(`/pages/activity/user/activity_list?type=create&select=1&types=25`, {
                events: {
                    selectActive: data => this.addBindActiveExam(5, data)
                }
            })
        },

        addBindActiveExam(active_types, data) {
            const id_options = {
                1: () => data.active_id,
                2: () => data.id,
                3: () => data.id,
                4: () => data.active_id,
                5: () => data.active_id
            }
            const id = id_options[active_types]()
            
            const is_repeat = this.conf.limit.active_list.some(item => item.id === id)
            if (is_repeat) return setTimeout(() => {
                const type = this.option_config.active_types.find(v => v.value === active_types).title
                this.$uni.showModal(`该${type}已关联。`, {title: '关联失败'})
            }, 300)


            const name_options = {
                1: () => data.name,
                2: () => data.title,
                3: () => data.name,
                4: () => data.name,
                5: () => data.name
            }

            const item = {
                id,
                name: name_options[active_types](),
                active_types,
                sport_types: (active_types === 1 || active_types === 4) ?  data.active_types : 0
            }
            if (active_types === 4) {
                item.set_types = 1 // 【1】按照当天的步数进行判断参与抽奖   【2】按照用户的积分数进行抽奖
                item.min = ''
                item.max = ''
            }
            this.conf.limit.active_list.push(item)
        },
        

        errorToast(title) {
            if (!title) return this.$uni.showToast(title, title.length < 8 ? 'error' : 'none')
        },

        saveCheck() {
            if (!this.title) return this.errorToast('请输入活动名称')
            if (!this.all_times || this.all_times === '0') return this.errorToast('请输入总抽奖次数')
            if (!this.everyd_times || this.everyd_times === '0') return this.errorToast('请输入日抽奖次数')

            this.save()
        },
        
        
        getConfLimit() {
            const limit = this.conf.limit
            let condition = Math.floor(limit.condition)
            if (isNaN(condition)) {
                this.$uni.showModal(`${this.activeTypesText} 抽奖限制 设置有误`)
                return false
            }
            if (this.conf.lottery_type === 'normal' && condition > limit.active_list.length) {
                this.$uni.showModal(`${this.activeTypesText} 抽奖限制 设置有误，设置数量(${condition})大于已关联的数量(${limit.active_list.length})`)
                return false
            }

            /*盲盒抽奖需要把抽奖条件设置 222
            盲盒抽奖条件设置 222代表是抽盲盒的，系统会自动查询当前用户是否到达当前抽奖发起的点位id，以及这个点位用户是否已抽过奖。 */
            if (this.conf.lottery_type === 'blind_box') condition = 222

            if (this.conf.lottery_type === 'step_lottery') {
                condition = 111
                /*if (!limit.active_list.length) {
                    this.$uni.showToast('请关联活动')
                    return false
                }*/
                for (let i = 0, len = limit.active_list.length; i < len; i++) {
                    const min = Number(limit.active_list[i].min)
                    const max = Number(limit.active_list[i].max)
                    if (isNaN(min) || isNaN(max) || min < 0 || max < 0 || min > max) {
                        this.$uni.showToast('步数设置有误')
                        return false
                    }
                    limit.active_list[i].min = min
                    limit.active_list[i].max = max
                }
            }
            
            return {
                condition,
                active_list: limit.active_list
            }
        },

        getConfSet() {
            const conf = {
                lucky_draw_method: this.conf.lucky_draw_method
            }
            if (this.conf.lotteryedNotLottery) conf.lotteryedNotLottery = this.conf.lotteryedNotLottery
            if (this.conf.lottery_type !== 'normal') conf.lottery_type = this.conf.lottery_type

            const limit = this.getConfLimit()
            if (!limit) return
            if (limit.active_list?.length) conf.limit = limit
            if (this.redpack_open && this.conf.bind_id) conf.bind_id = this.conf.bind_id
            
            return conf
        },

        async createExam() {
            const res = await this.xwy_api.request({
                url: 'front.flat.exam.examPapers/create_exam_papers',
                data: {
                    exam_name: `红包抽奖答题_${new Date().getTime()}`
                }
            })
            const exam_id = res?.data?.exam_id
            if (!exam_id) return await this.createExam()
            return exam_id
        },

        async save() {
            const conf = this.getConfSet()
            if (!conf) return

            this.$uni.showLoading(this.id ? '修改中...' : '创建中...')

            if (this.redpack && this.redpack_open && !conf.bind_id) conf.bind_id = await this.createExam()

            const data = {
                title: this.title,
                begin_time: this.begin_time,
                end_time: this.end_time,
                all_times: this.all_times,
                everyd_times: this.everyd_times,
                content: this.content,
                show_prize: this.show_prize,
                conf: base64['encode'](JSON.stringify(conf))
            }
            if (this.id) data.active_id = this.id

            const res = await xwy_api.request({
                url: 'front.flat.active.lottery_active.admin.manage_lottery/create_lottery_active',
                data
            })

            uni.hideLoading()

            if (!res?.['status']) return this.$uni.showModal(res?.['info'] || (this.id ? '修改失败' : '创建失败'))

            this.success = true

            this.getOpenerEventChannel().emit('reloadList')

        },

        lookMyActivityList() {
            const list_page_route = 'pages/lottery/admin/lottery/list'
            const pages = getCurrentPages()
            const list_page_index = pages.findIndex(v => v.route === list_page_route)
            if (list_page_index === -1) return this.$uni.redirectTo(`/${list_page_route}`)

            this.$uni.navigateBack(pages.length - list_page_index - 1)
        }
    }
}
</script>

<style>
.page {
    padding-bottom: 80px;
}

.save-success {
    padding-top: 10vh;
}

.save-success .icon {
    padding-bottom: 50px;
}


.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}


.form-item .textarea {
    width: 100%;
    line-height: 16px;
    padding: 10px 0;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.forms-picker {
    border-radius: 4px;
    border: 1px solid #e5e5e5;
    padding: 0 5px;
    line-height: 36px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}

.bottom-btn-view {
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.login-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}

.active-exit-tag {
    font-size: 12px;
    color: #5cadff;
    border: 1px solid #5cadff;
    border-radius: 2px;
    padding: 0 2px;
    margin-right: 5px;
}

.active-exit-tag-2 {
    color: #f90;
    border: 1px solid #f90;
}
</style>
