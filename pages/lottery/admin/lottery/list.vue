<template>
    <view class="page bg-background">
        <view v-if="total_count" class="flex-kai bg-white">
            <view class="color-sub p10">共{{ total_count }}个活动</view>
            <view class="color-primary p10" hover-class="navigator-hover" @click="toEdit(null)">
                <text>创建活动</text>
                <uni-icons type="forward" color="#2d8cf0"/>
            </view>
        </view>

        <view class="list">
            <view class="item bg-white" v-for="item in list" :key="item.active_id">
                <view>
                    <text v-if="item.is_blind_box" class="title-tag blind-box-title-tag">盲盒抽奖</text>
                    <text v-if="item.is_step_lottery" class="title-tag step-lottery-title-tag">步数抽奖</text>
                    <text v-if="item.is_task_lottery" class="title-tag task-lottery-title-tag">任务抽奖</text>
                    <text v-if="item.is_redpack" class="title-tag redpack-title-tag">红包</text>
                    <text class="color-title">{{ item.title }}</text>
                </view>
                <view v-if="item.is_redpack" class="pt10">
                    <view v-if="item.redpack_info">
                        红包总额 {{ item.redpack_info.amount_all }}元, 余额 {{ item.redpack_info.amount_left }}元
                    </view>
                    <view v-else class="color-light-primary" @click="showRedpackInfo(item)">显示红包余额</view>
                </view>
                <view v-if="item.is_redpack && item.bind_id" class="pt10 color-light-primary"
                      @click="copyBindId(item.bind_id)">
                    复制考卷ID
                </view>
                <view class="tools-bar flex-kai">
                    <view>
                        <view v-if="is_sel" class="color-primary" @click="selItem(item)">关联抽奖</view>
                        <view v-if="select_blind_box" class="color-primary" @click="selectBlindBox(item)">
                            绑定抽奖
                        </view>
                        <view v-if="select_step" class="color-primary" @click="selectStepLottery(item)">
                            绑定抽奖
                        </view>
                        <view v-if="select_task" class="color-primary" @click="selectTaskLottery(item)">
                            绑定抽奖
                        </view>
                    </view>
                    <view class="flex-row">
                        <view class="plr10 color-sub font14" hover-class="navigator-hover"
                              @click="toEdit(item.active_id)">
                            <text class="iconfont icon-edit color-sub font14 pr5"></text>修改
                        </view>

                        <view class="plr10 color-sub font14" hover-class="navigator-hover"
                              @click="toPrizeList(item)">
                            <text class="iconfont icon-gift color-sub font14 pr5"></text>奖品设置
                        </view>
                        <view class="pl10 color-sub font14" hover-class="navigator-hover"
                              @click.stop="deleteItem(item.active_id, item.title)">
                            <text class="iconfont icon-delete color-sub font14 pr5"></text>删除
                        </view>
                    </view>
                </view>
            </view>
        </view>


        <view v-if="!loading && !list.length" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无抽奖活动</view>
            <view class="flex-all-center p20">
                <view class="not-btn text-center bg-primary color-white" hover-class="navigator-hover"
                      @click="toEdit(null)">
                    创建抽奖活动
                </view>
            </view>
        </view>

        <uni-load-more v-if="loading && load_page > 1" status="loading"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && list.length > 5"
            status="noMore"
            :contentText="{contentnomore: '我是有底线的'}"
        ></uni-load-more>
        <uni-load-more v-if="!is_last_page && !loading && list.length" status="more"></uni-load-more>
    </view>
</template>

<script>
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'

export default {
    data() {
        return {
            loading: true,
            list: [],
            load_page: 1,
            is_last_page: false,
            total_count: 0,
            is_sel: false,
            select_blind_box: false,
            select_step: false,
            select_task: false,
            redpack: false
        }
    },
    onLoad(e) {
        const {is_sel, mystery_box, select_blind_box, select_step, lottery_add_step_integral, page_diy_set, active_id, task_lottery, select_task, redpack, lotteryForUserIntegral} = e

        if (is_sel) this.is_sel = true
        if (mystery_box) {
            this.mystery_box = true
            if (select_blind_box) this.select_blind_box = true
        }
        if (select_step) this.select_step = true
        if (page_diy_set) this.page_diy_set = true
        if (lottery_add_step_integral) this.lottery_add_step_integral = true
        if (redpack) this.redpack = true
        if (active_id) this.active_id = active_id
        if (task_lottery) this.task_lottery = task_lottery
        if (select_task) this.select_task = true
        if (lotteryForUserIntegral) this.lotteryForUserIntegral = true

        this.$uni.showLoading('加载中...')

        login.uniLogin(err => {
            if (err && err.errMsg) {
                this.loading = false
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getList()
        })
    },

    onReachBottom() {
        if (!this.is_last_page && !this.loading && this.list.length) this.getList()
    },

    methods: {
        async getList() {
            if (this.load_page === 1) {
                this.is_last_page = false
                this.list = []
            }

            this.$uni.showLoading('加载中...')
            this.loading = true

            const res = await xwy_api.request({
                url: 'front.flat.active.lottery_active.admin.manage_lottery/lottery_active_list',
                data: {
                    my_self: 1,
                    page: this.load_page,
                    perpage: 10
                }
            })

            this.loading = false
            uni.hideLoading()

            const data_ = res?.['data']?.['active_list']
            if (!data_) return false
            const list = this.listInit(data_.data)
            this.list = [...this.list, ...list]
            this.is_last_page = data_.is_lastpage
            this.load_page++
            this.total_count = data_.total
        },

        listInit(list) {
            return list.map(item => {
                const data = {
                    active_id: item.active_id,
                    title: item.title,
                    is_blind_box: item.conf?.lottery_type === 'blind_box',
                    is_step_lottery: item.conf?.lottery_type === 'step_lottery',
                    is_task_lottery: item?.conf?.lottery_type === 'task_lottery'
                }

                if (item.conf?.bind_id) {
                    data.bind_id = item.conf.bind_id
                    data.is_redpack = true
                    data.redpack_info = null
                }

                if (data.is_step_lottery || data.is_task_lottery) {
                    data.activeList = item.conf?.limit?.active_list || []
                }

                return data
            })
        },

        toEdit(id) {
            let url = './edit'
            const params = []
            if (id) params.push(`id=${id}`)
            if (this.mystery_box) params.push('mystery_box=1')
            if (this.lottery_add_step_integral) params.push('lottery_add_step_integral=1')
            if (this.page_diy_set) params.push('page_diy_set=1')
            if (this.task_lottery) params.push('task_lottery=1')
            if (this.redpack) params.push('redpack=1')
            if (params.length) url += `?${params.join('&')}`

            this.$uni.navigateTo(url, {
                events: {
                    reloadList: () => {
                        this.load_page = 1
                        this.getList()
                    }
                }
            })
        },

        copyBindId(id) {
            this.$uni.setClipboardData(id, '已复制')
        },

        async showRedpackInfo(item) {
            if (!item.bind_id) return

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.exam.examPapers/get_exam_papers_details',
                data: {
                    exam_id: item.bind_id
                }
            })
            uni.hideLoading()

            item.redpack_info = {
                amount_all: res?.data?.amount_all || 0,
                amount_left: res?.data?.amount_left || 0
            }
        },

        selItem(item) {
            const eventChannel = this.getOpenerEventChannel()
            eventChannel.emit('selLottery', {id: item.active_id, title: item.title})
            uni.navigateBack()
        },

        selectBlindBox(item) {
            if (!item.is_blind_box) return this.$uni.showToast('当前选择的抽奖活动不是盲盒抽奖活动，请重新选择', 'none', 3000)

            this.getOpenerEventChannel().emit('selectBlindBox', {lottery_id: item.active_id, title: item.title})
            this.$uni.navigateBack(1, {delay: 1000})
        },

        selectStepLottery(item) {
            if (!item.is_step_lottery) {
                return this.$uni.showToast('当前选择的抽奖活动不是步数抽奖活动，请重新选择', 'none', 3000)
            }

            const activeList = item.activeList || []
            const activeData = activeList.find(v => v.id === this.active_id)
            if (!activeData) {
                return this.$uni.showModal('当前选择的抽奖活动没有关联本活动，请重新选择或到抽奖活动里面关联本活动')
            }

            this.getOpenerEventChannel().emit('selectStepLottery', {
                lottery_id: item.active_id,
                title: item.title,
                min: Number(activeData.min),
                max: Number(activeData.max)
            })
            this.$uni.navigateBack()
        },

        selectTaskLottery(item) {
            if (!item.is_task_lottery) {
                return this.$uni.showToast('当前选择的抽奖活动不是任务抽奖活动，请重新选择', 'none', 3000)
            }

            const activeList = item.activeList || []
            const activeData = activeList.find(v => v.id === this.active_id)
            if (!activeData) {
                return this.$uni.showModal('当前选择的抽奖活动没有关联本活动，请重新选择或到抽奖活动里面关联本活动')
            }

            this.getOpenerEventChannel().emit('selectTaskLottery', {
                lottery_id: item.active_id,
                title: item.title
            })
            this.$uni.navigateBack()
        },

        toPrizeList(item) {
            let url = `../prize/list?id=${item.active_id}`
            if (item.is_blind_box) url += '&is_blind_box=1'
            if (this.lotteryForUserIntegral) url += '&lotteryForUserIntegral=1'
            this.$uni.navigateTo(url)
        },

        async deleteItem(id, title) {
            const res = await this.$uni.showModal(`确定删除 ${title} ?`, {showCancel: true})
            if (res.confirm) await this.delAjax(id)
        },

        async delAjax(ids) {
            this.$uni.showLoading('删除中...')
            const res = await xwy_api.request({
                url: 'front.flat.active.lottery_active.admin.manage_lottery/del_lottery_active',
                data: {ids}
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '删除失败')

            this.$uni.showToast('已删除')
            this.load_page = 1
            await this.getList()
        }
    }
}
</script>

<style>
.plr10 {
    padding-left: 10px;
    padding-right: 10px;
}

.page {
    min-height: 100vh;
}

.list {
    padding-top: 5px;
}

.item {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
}

.title-tag {
    font-size: 12px;
    border-radius: 2px;
    padding: 0 2px;
    margin-right: 5px;
}

.step-lottery-title-tag {
    color: #19be6b;
    border: 1px solid #19be6b;
}

.blind-box-title-tag {
    color: #f90;
    border: 1px solid #f90;
}

.task-lottery-title-tag {
    color: #5cadff;
    border: 1px solid #5cadff;
}

.redpack-title-tag {
    color: #e20f04;
    border: 1px solid #e20f04;
}

.tools-bar {
    border-top: 1px solid #eee;
    padding-top: 10px;
    margin-top: 10px;
}

.not-btn {
    width: 200px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
}
</style>
