<template>
    <view class="page bg-background">
        <view v-if="list.length" class="flex-kai bg-white">
            <view class="color-sub p10">共{{ list.length }}个奖品</view>
            <view class="color-primary p10" hover-class="navigator-hover" @click="addPrize">
                <text>添加奖品</text>
                <uni-icons type="forward" color="#2d8cf0"/>
            </view>
        </view>

        <view class="font14 p10" style="color: #e19898;">
            <view>奖品设置说明：</view>
            <view v-if="lucky_draw_method === 'wheel'">
                1、大转盘抽奖活动奖品建议不超过8个。否则抽奖转盘上展示拥挤。
            </view>
            <view v-if="lucky_draw_method === 'grid'">
                1、九宫格抽奖活动奖不得超过7个。如奖品设置超过7个，抽奖界面上只会显示7个奖品，其他奖品不显示。
            </view>
            <view>
                2、个人的中奖概率不是按照所有奖品的中奖率之和。如果需要较为容易中奖，可设置奖品的中奖率为80%以上。
            </view>
        </view>

        <view class="list">
            <view class="item bg-white" v-for="item in list" :key="item.id">
                <view class="flex-row">
                    <view v-if="item.pic" class="pr10">
                        <image class="pic-image" :src="item.pic"/>
                    </view>
                    <view>
                        <view class="color-title">{{ item.name }}</view>
                        <view class="color-content font14 pt5">
                            <text>中奖概率: {{ item.percent }}%</text>
                            <text class="pl10 pr10">总数: {{ item.all_counts }}</text>
                            <text>剩余: {{ item.left_counts }}</text>
                        </view>
                        <view v-if="item.types === 1 && item.conf_set" class="pt5 color-sub font14">
                            <text>红包金额: {{ item.conf_set.min }}元</text>
                            <template v-if="item.conf_set.min !== item.conf_set.max">
                                <text style="padding: 0 2px;">-</text>
                                <text>{{ item.conf_set.max }}元</text>
                            </template>
                        </view>
                    </view>
                </view>
                <view class="tools-bar flex-kai">
                    <view></view>
                    <view class="flex-row">
                        <view @click.stop="toEditItem(item)" class="edit">
                            <text class="iconfont icon-edit color-sub font20"></text>
                        </view>
                        <view v-if="item.status !== -1" class="delete"
                              @click.stop="deleteItem(item.id, item.name)">
                            <text class="iconfont icon-delete color-sub font20"></text>
                        </view>
                    </view>
                </view>
            </view>
        </view>


        <view v-if="!loading && !list.length" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无奖品</view>
            <view class="flex-all-center p20">
                <view class="not-btn text-center bg-primary color-white" hover-class="navigator-hover"
                      @click="addPrize">添加奖品
                </view>
            </view>
        </view>

    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'

export default {
    data() {
        return {
            loading: true,
            id: '',
            list: [],
            lucky_draw_method: '',
            redpack_bind_id: ''
        }
    },
    onLoad(e) {
        this.id = e.id
        if (e.is_blind_box) this.is_blind_box = true
        if (e.lotteryForUserIntegral) this.lotteryForUserIntegral = true
        this.$uni.showLoading('加载中...')

        login.uniLogin(err => {
            if (err && err.errMsg) {
                this.loading = false
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getList()
        })
    },
    methods: {
        async getList() {
            this.list = []

            this.$uni.showLoading('加载中...')
            this.loading = true
            const res = await xwy_api.request({
                url: 'front.flat.active.lottery_active.admin.manage_lottery/lottery_active_details',
                data: {
                    active_id: this.id
                }
            })
            uni.hideLoading()
            this.loading = false

            const details = res?.['data']?.['active_details']
            if (!details) {
                return this.$uni.showModal('抽奖活动信息获取失败，请重试', {success: () => uni.navigateBack()})
            }
            if (details.conf?.bind_id) this.redpack_bind_id = details.conf.bind_id

            this.lucky_draw_method = details.conf?.lucky_draw_method || 'wheel'
            const list = res?.['data']?.['prize_list'] || []
            if (!list.length) return false
            this.list = list
        },

        addPrize() {
            if (this.lucky_draw_method === 'grid' && this.list.length >= 7 && !this.is_blind_box) {
                return this.$uni.showToast('九宫格抽奖最多添加7个奖品')
            }

            this.toAddOrEdit(null)
        },

        toEditItem(item) {
            this.toAddOrEdit(item)
        },

        toAddOrEdit(item = null) {
            let url = `./edit?active_id=${this.id}`
            if (item) url += `&id=${item.id}`
            if (this.is_blind_box) url += '&is_blind_box=1'
            if (this.redpack_bind_id) url += `&redpack_bind_id=${this.redpack_bind_id}`
            if (this.lotteryForUserIntegral) url += `&lotteryForUserIntegral=${this.lotteryForUserIntegral}`

            this.$uni.navigateTo(url, {
                success: res => {
                    if (item) res.eventChannel.emit('details', JSON.parse(JSON.stringify(item)))
                },
                events: {
                    reloadList: () => this.getList()
                }
            })
        },

        async deleteItem(id, name) {
            const res = await this.$uni.showModal(`确定删除 ${name} ?`, {showCancel: true})
             if (res.confirm) await this.delAjax(id)
        },

        async delAjax(ids) {
            this.$uni.showLoading('删除中...')
            const res = await xwy_api.request({
                url: 'front.flat.active.lottery_active.admin.manage_lottery/del_prize',
                data: {
                    active_id: this.id,
                    ids
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '删除失败')

            this.$uni.showToast('已删除')
            await this.getList()
        }
    }
}
</script>

<style>
.page {
    min-height: 100vh;
}

.list {
    padding-top: 5px;
}

.item {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
}

.pic-image {
    display: block;
    width: 50px;
    height: 50px;
}

.tools-bar {
    border-top: 1px solid #eee;
    padding-top: 10px;
    margin-top: 10px;
}

.edit, .delete {
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 50%;
}

.edit {
    border: 1px solid #eee;
    margin-right: 10px;
}

.delete {
    border: 1px solid #eee;
}

.not-btn {
    width: 200px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
}
</style>
