<template>
    <view class="page">
        <view class="font14 p10" style="color: #e19898;">
            <view>奖品设置说明：</view>
            <view>
                个人的中奖概率不是按照所有奖品的中奖率之和。如果需要较为容易中奖，可设置奖品的中奖率为80%以上。
            </view>
        </view>
        <view class="form">
            <view class="form-item">
                <view class="top color-content">
                    <text>奖品名称</text>
                    <text class="color-error font16"> *</text>
                </view>
                <view class="bottom font16">
                    <input class="input" type="text" v-model="name" placeholder="请输入奖品名称"/>
                </view>
            </view>

            <view v-if="typesOptions.length > 1" class="form-item">
                <view class="top color-content">奖品类型</view>
                <view class="bottom font16">

                    <picker :range="typesOptions" range-key="name" :value="typesPickerData.value"
                            @change="types = typesOptions[$event.detail.value].types">
                        <view class="view flex-kai">
                            <view>{{ typesPickerData.name }}</view>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </picker>
                </view>
            </view>

            <view v-show="types === 1">
                <view class="form-item">
                    <view class="top color-content">红包金额类型</view>
                    <view class="bottom font16">

                        <picker :range="['固定金额', '随机金额']" :value="redpack_amount_is_random"
                                @change="redpack_amount_is_random = Number($event.detail.value)">
                            <view class="view flex-kai">
                                <view>{{ redpack_amount_is_random ? '随机' : '固定' }}金额</view>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </picker>
                    </view>
                </view>

                <view v-if="!redpack_amount_is_random" class="form-item">
                    <view class="top color-content">
                        <text>红包金额</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="digit" v-model="redpack_amount"
                               placeholder="请输入红包金额"/>
                    </view>
                </view>

                <template v-if="redpack_amount_is_random">
                    <view class="form-item">
                        <view class="top color-content">
                            <text>最低红包金额</text>
                            <text class="color-error font16"> *</text>
                            <text class="color-sub font12 pl5">(单位: 元)</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="digit" v-model="redpack_amount_random.min"
                                   placeholder="请输入红包金额"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <text>最高红包金额</text>
                            <text class="color-error font16"> *</text>
                            <text class="color-sub font12 pl5">(单位: 元)</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="digit" v-model="redpack_amount_random.max"
                                   placeholder="请输入红包金额"/>
                        </view>
                    </view>
                </template>
            </view>

            <view v-show="types === 3">
                <view class="form-item">
                    <view class="top color-content">积分类型</view>
                    <view class="bottom font16">

                        <picker :range="['固定积分', '随机积分']" :value="redpack_amount_is_random"
                                @change="redpack_amount_is_random = Number($event.detail.value)">
                            <view class="view flex-kai">
                                <view>{{ redpack_amount_is_random ? '随机' : '固定' }}积分</view>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </picker>
                    </view>
                </view>

                <view v-if="!redpack_amount_is_random" class="form-item">
                    <view class="top color-content">
                        <text>积分数</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="digit" v-model="redpack_amount"
                               placeholder="请输入奖品的积分数"/>
                    </view>
                </view>

                <template v-if="redpack_amount_is_random">
                    <view class="form-item">
                        <view class="top color-content">
                            <text>最小积分数</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="digit" v-model="redpack_amount_random.min"
                                   placeholder="请输入奖品的最小积分数"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <text>最大积分数</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="digit" v-model="redpack_amount_random.max"
                                   placeholder="请输入奖品的最大积分数"/>
                        </view>
                    </view>
                </template>
            </view>


            <view class="form-item">
                <view class="top color-content">
                    <text>中奖率</text>
                    <text class="color-error font16"> *</text>
                    <text class="color-sub font12 pl5">(0% - 100%)</text>
                </view>
                <view class="bottom font16">
                    <input class="input" type="digit" v-model="percent"
                           placeholder="如: 中奖率10%请填10"/>
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">
                    <text>奖品总数量</text>
                    <text class="color-error font16"> *</text>
                </view>
                <view class="bottom font16">
                    <input class="input" type="number" v-model="all_counts" placeholder="奖品总数量"/>
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">
                    <text>奖品剩余量</text>
                    <text class="color-error font16"> *</text>
                </view>
                <view class="bottom font16">
                    <input class="input" type="number" v-model="left_counts"
                           placeholder="奖品剩余量"/>
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">
                    <text>奖品图片</text>
                    <template>
                        <text v-if="is_blind_box" class="color-error font16"> *</text>
                        <text v-else class="color-sub font12 pl5">(请上传正方形图片，非正方形图片会压缩或拉伸)</text>
                    </template>
                </view>
                <view style="padding-top: 5px;">
                    <view class="image-view" v-if="pic">

                        <image class="image-item" :src="pic" @click="previewImage([pic])"/>
                        <view class="del-image-item" @click.stop="pic = ''">
                            <uni-icons type="closeempty" color="#e20f04"/>
                        </view>
                    </view>

                    <navigator v-else class="add-image text-center"
                               :url="'/pages/other/image_upload_or_select?key=pic&active_id=' + active_id">
                        <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                    </navigator>
                </view>
            </view>

            <view class="bottom-btn-view bg-white flex-all-center">
                <view class="login-btn color-white text-center font18 bg-primary" @click="saveCheck">
                    保存
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'

export default {
    data() {
        return {
            active_id: '',
            id: '',
            name: '',
            percent: '',
            all_counts: 100,
            left_counts: 100,
            pic: '',
            is_blind_box: false,
            redpack_bind_id: '',
            types: 0,
            typesOptions: [
                {name: '普通奖品', types: 0}
            ],
            redpack_amount_is_random: 0,
            redpack_amount: '',
            redpack_amount_random: {
                min: '',
                max: ''
            }
        }
    },

    computed: {
        typesPickerData() {
            let index = this.typesOptions.findIndex(item => item.types === this.types)
            if (index === -1) index = 0
            return {
                value: index,
                name: this.typesOptions[index].name
            }
        }
    },

    onLoad(e) {
        this.active_id = e.active_id
        if (e.is_blind_box) this.is_blind_box = true
        if (e.redpack_bind_id) {
            this.redpack_bind_id = e.redpack_bind_id
            this.typesOptions.push({name: '现金红包', types: 1})
        }
        if (e.lotteryForUserIntegral) {
            this.lotteryForUserIntegral = true
            this.typesOptions.push({name: '积分奖励', types: 3})
        }

        this.$uni.showLoading('加载中...')

        login.uniLogin(err => {
            uni.hideLoading()

            if (err && err.errMsg) {
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            if (e.id) {
                this.id = e.id
                this.getDetail()
            }

        })
    },
    methods: {
        getDetail() {
            this.getOpenerEventChannel().on('details', details => {
                this.name = details.name
                this.percent = details.percent
                this.all_counts = details.all_counts
                this.left_counts = details.left_counts
                this.pic = details.pic
                this.types = details.types

                if (details.types === 1 || details.types === 3) {
                    const {min = '', max = ''} = details.conf_set || {}
                    this.redpack_amount_random.min = min
                    this.redpack_amount_random.max = max
                    this.redpack_amount_is_random = min !== max
                    if (!this.redpack_amount_is_random) this.redpack_amount = min
                }
            })
        },


        previewImage(urls) {
            uni.previewImage({
                urls
            })
        },

        saveCheck() {
            if (!this.name) {
                this.$uni.showToast('请输入奖品名称')
                return false
            }

            const percent = Number(this.percent)
            if (isNaN(percent)) {
                this.$uni.showToast('请输入正确的中奖率')
                return false
            }
            if (percent < 0 || percent > 100) {
                this.$uni.showToast('请输入正确的中奖率(0-100)')
                return false
            }

            if (!this.all_counts || this.all_counts === '0') {
                this.$uni.showToast('请输入奖品总数量')
                return false
            }
            if (Number(this.left_counts) > Number(this.all_counts)) {
                this.$uni.showToast('奖品剩余数量不得大于奖品总数量')
                return false
            }

            if (this.is_blind_box && !this.pic) {
                this.$uni.showToast('盲盒抽奖必须上传奖品图片')
                return false
            }

            this.save()
        },

        getConfSet() {
            const types = this.types
            if (types !== 1 && types !== 3) return ''

            const conf_set = {min: 0, max: 0}

            if (this.redpack_amount_is_random) {
                const min = Math.floor(this.redpack_amount_random.min * 100) / 100
                const max = Math.floor(this.redpack_amount_random.max * 100) / 100

                if (min <= 0 || max <= 0 || isNaN(min) || isNaN(max)) {
                    this.$uni.showToast(`请输入正确的${types === 1 ? '红包金额' : '积分数'}`)
                    return false
                }
                if (min > max) {
                    const tips = types === 1 ? '红包最低金额不能高于红包最高金额' : '最小积分数不能高于最大积分数'
                    this.$uni.showToast(tips, 'none', 3000)
                    return false
                }

                conf_set.min = min
                conf_set.max = max
            } else {
                const redpack_amount = Math.floor(this.redpack_amount * 100) / 100
                if (redpack_amount <= 0 || isNaN(redpack_amount)) {
                    this.$uni.showToast(`请输入正确的${types === 1 ? '红包金额' : '积分数'}`)
                    return false
                }

                conf_set.min = redpack_amount
                conf_set.max = redpack_amount
            }

            return this._utils.base64['encode'](JSON.stringify(conf_set))
        },

        async save() {
            const data = {
                active_id: this.active_id,
                name: this.name,
                percent: this.percent,
                all_counts: this.all_counts,
                left_counts: this.left_counts || 0,
                pic: this.pic
            }
            if (this.id) data.id = this.id

            // 标记是盲盒抽奖的奖品
            if (this.is_blind_box) data.types = 2
            if (this.types) data.types = this.types
            if (data.types === 1 && this.redpack_bind_id) data.bind_id = this.redpack_bind_id

            const conf_set = this.getConfSet()
            if (conf_set === false) return
            if (conf_set) data.conf_set = conf_set

            this.$uni.showLoading('保存中...')
            const res = await xwy_api.request({
                url: 'front.flat.active.lottery_active.admin.manage_lottery/create_prize',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '保存失败')

            this.$uni.showToast('已保存')

            this.getOpenerEventChannel().emit('reloadList')
            this.$uni.navigateBack(1, {delay: 1000})
        },
    }
}
</script>

<style>
.page {
    padding-bottom: 80px;
}

.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}

.form-item .bottom {
    border-bottom: 1px solid #eee;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
    width: 100px;
    height: 100px;
    border-radius: 5px;
    line-height: 95px;
    margin: 5px calc((100vw - 20px - (100px * 3)) / 6);
}


.image-item {
    width: 200px;
    height: 200px;
    border-radius: 5px;
}

.image-view {
    position: relative;
    display: inline-block;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}

.bottom-btn-view {
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.login-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}
</style>
