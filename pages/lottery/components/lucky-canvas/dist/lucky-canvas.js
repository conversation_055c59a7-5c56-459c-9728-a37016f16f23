/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};function e(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}var i=function(){return i=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},i.apply(this,arguments)};function n(t,e,i,n){return new(i||(i=Promise))((function(r,o){function a(t){try{c(n.next(t))}catch(t){o(t)}}function s(t){try{c(n.throw(t))}catch(t){o(t)}}function c(t){var e;t.done?r(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(a,s)}c((n=n.apply(t,e||[])).next())}))}function r(t,e){var i,n,r,o,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(i)throw new TypeError("Generator is already executing.");for(;a;)try{if(i=1,n&&(r=2&o[0]?n.return:o[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,o[1])).done)return r;switch(n=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,n=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(r=a.trys,(r=r.length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){a.label=o[1];break}if(6===o[0]&&a.label<r[1]){a.label=r[1],r=o;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(o);break}r[2]&&a.ops.pop(),a.trys.pop();continue}o=e.call(t,a)}catch(t){o=[6,t],n=0}finally{i=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}function o(t,e){for(var i=0,n=e.length,r=t.length;i<n;i++,r++)t[r]=e[i];return t}var a="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function s(t,e,i){return t(i={path:e,exports:{},require:function(t,e){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==e&&i.path)}},i.exports),i.exports}var c,u,h=function(t){return t&&t.Math==Math&&t},l=h("object"==typeof globalThis&&globalThis)||h("object"==typeof window&&window)||h("object"==typeof self&&self)||h("object"==typeof a&&a)||function(){return this}()||Function("return this")(),f=function(t){try{return!!t()}catch(t){return!0}},d=!f((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),p=Function.prototype.call,g=p.bind?p.bind(p):function(){return p.apply(p,arguments)},v={}.propertyIsEnumerable,m=Object.getOwnPropertyDescriptor,y={f:m&&!v.call({1:2},1)?function(t){var e=m(this,t);return!!e&&e.enumerable}:v},w=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},b=Function.prototype,S=b.bind,C=b.call,z=S&&S.bind(C),x=S?function(t){return t&&z(C,t)}:function(t){return t&&function(){return C.apply(t,arguments)}},O=x({}.toString),k=x("".slice),I=function(t){return k(O(t),8,-1)},E=l.Object,T=x("".split),A=f((function(){return!E("z").propertyIsEnumerable(0)}))?function(t){return"String"==I(t)?T(t,""):E(t)}:E,P=l.TypeError,_=function(t){if(null==t)throw P("Can't call method on "+t);return t},W=function(t){return A(_(t))},$=function(t){return"function"==typeof t},j=function(t){return"object"==typeof t?null!==t:$(t)},R=function(t){return $(t)?t:void 0},F=function(t,e){return arguments.length<2?R(l[t]):l[t]&&l[t][e]},D=x({}.isPrototypeOf),H=F("navigator","userAgent")||"",L=l.process,M=l.Deno,B=L&&L.versions||M&&M.version,N=B&&B.v8;N&&(u=(c=N.split("."))[0]>0&&c[0]<4?1:+(c[0]+c[1])),!u&&H&&(!(c=H.match(/Edge\/(\d+)/))||c[1]>=74)&&(c=H.match(/Chrome\/(\d+)/))&&(u=+c[1]);var X=u,G=!!Object.getOwnPropertySymbols&&!f((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&X&&X<41})),U=G&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,q=l.Object,Y=U?function(t){return"symbol"==typeof t}:function(t){var e=F("Symbol");return $(e)&&D(e.prototype,q(t))},K=l.String,V=l.TypeError,J=function(t){if($(t))return t;throw V(function(t){try{return K(t)}catch(t){return"Object"}}(t)+" is not a function")},Q=l.TypeError,Z=Object.defineProperty,tt=function(t,e){try{Z(l,t,{value:e,configurable:!0,writable:!0})}catch(i){l[t]=e}return e},et=l["__core-js_shared__"]||tt("__core-js_shared__",{}),it=s((function(t){(t.exports=function(t,e){return et[t]||(et[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.19.2",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),nt=l.Object,rt=function(t){return nt(_(t))},ot=x({}.hasOwnProperty),at=Object.hasOwn||function(t,e){return ot(rt(t),e)},st=0,ct=Math.random(),ut=x(1..toString),ht=function(t){return"Symbol("+(void 0===t?"":t)+")_"+ut(++st+ct,36)},lt=it("wks"),ft=l.Symbol,dt=ft&&ft.for,pt=U?ft:ft&&ft.withoutSetter||ht,gt=function(t){if(!at(lt,t)||!G&&"string"!=typeof lt[t]){var e="Symbol."+t;G&&at(ft,t)?lt[t]=ft[t]:lt[t]=U&&dt?dt(e):pt(e)}return lt[t]},vt=l.TypeError,mt=gt("toPrimitive"),yt=function(t,e){if(!j(t)||Y(t))return t;var i,n,r=null==(i=t[mt])?void 0:J(i);if(r){if(void 0===e&&(e="default"),n=g(r,t,e),!j(n)||Y(n))return n;throw vt("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var i,n;if("string"===e&&$(i=t.toString)&&!j(n=g(i,t)))return n;if($(i=t.valueOf)&&!j(n=g(i,t)))return n;if("string"!==e&&$(i=t.toString)&&!j(n=g(i,t)))return n;throw Q("Can't convert object to primitive value")}(t,e)},wt=function(t){var e=yt(t,"string");return Y(e)?e:e+""},bt=l.document,St=j(bt)&&j(bt.createElement),Ct=function(t){return St?bt.createElement(t):{}},zt=!d&&!f((function(){return 7!=Object.defineProperty(Ct("div"),"a",{get:function(){return 7}}).a})),xt=Object.getOwnPropertyDescriptor,Ot={f:d?xt:function(t,e){if(t=W(t),e=wt(e),zt)try{return xt(t,e)}catch(t){}if(at(t,e))return w(!g(y.f,t,e),t[e])}},kt=l.String,It=l.TypeError,Et=function(t){if(j(t))return t;throw It(kt(t)+" is not an object")},Tt=l.TypeError,At=Object.defineProperty,Pt={f:d?At:function(t,e,i){if(Et(t),e=wt(e),Et(i),zt)try{return At(t,e,i)}catch(t){}if("get"in i||"set"in i)throw Tt("Accessors not supported");return"value"in i&&(t[e]=i.value),t}},_t=d?function(t,e,i){return Pt.f(t,e,w(1,i))}:function(t,e,i){return t[e]=i,t},Wt=x(Function.toString);$(et.inspectSource)||(et.inspectSource=function(t){return Wt(t)});var $t,jt,Rt,Ft=et.inspectSource,Dt=l.WeakMap,Ht=$(Dt)&&/native code/.test(Ft(Dt)),Lt=it("keys"),Mt=function(t){return Lt[t]||(Lt[t]=ht(t))},Bt={},Nt=l.TypeError,Xt=l.WeakMap;if(Ht||et.state){var Gt=et.state||(et.state=new Xt),Ut=x(Gt.get),qt=x(Gt.has),Yt=x(Gt.set);$t=function(t,e){if(qt(Gt,t))throw new Nt("Object already initialized");return e.facade=t,Yt(Gt,t,e),e},jt=function(t){return Ut(Gt,t)||{}},Rt=function(t){return qt(Gt,t)}}else{var Kt=Mt("state");Bt[Kt]=!0,$t=function(t,e){if(at(t,Kt))throw new Nt("Object already initialized");return e.facade=t,_t(t,Kt,e),e},jt=function(t){return at(t,Kt)?t[Kt]:{}},Rt=function(t){return at(t,Kt)}}var Vt,Jt={set:$t,get:jt,has:Rt,enforce:function(t){return Rt(t)?jt(t):$t(t,{})},getterFor:function(t){return function(e){var i;if(!j(e)||(i=jt(e)).type!==t)throw Nt("Incompatible receiver, "+t+" required");return i}}},Qt=Function.prototype,Zt=d&&Object.getOwnPropertyDescriptor,te=at(Qt,"name"),ee={EXISTS:te,PROPER:te&&"something"===function(){}.name,CONFIGURABLE:te&&(!d||d&&Zt(Qt,"name").configurable)},ie=s((function(t){var e=ee.CONFIGURABLE,i=Jt.get,n=Jt.enforce,r=String(String).split("String");(t.exports=function(t,i,o,a){var s,c=!!a&&!!a.unsafe,u=!!a&&!!a.enumerable,h=!!a&&!!a.noTargetGet,f=a&&void 0!==a.name?a.name:i;$(o)&&("Symbol("===String(f).slice(0,7)&&(f="["+String(f).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!at(o,"name")||e&&o.name!==f)&&_t(o,"name",f),(s=n(o)).source||(s.source=r.join("string"==typeof f?f:""))),t!==l?(c?!h&&t[i]&&(u=!0):delete t[i],u?t[i]=o:_t(t,i,o)):u?t[i]=o:tt(i,o)})(Function.prototype,"toString",(function(){return $(this)&&i(this).source||Ft(this)}))})),ne=Math.ceil,re=Math.floor,oe=function(t){var e=+t;return e!=e||0===e?0:(e>0?re:ne)(e)},ae=Math.max,se=Math.min,ce=Math.min,ue=function(t){return(e=t.length)>0?ce(oe(e),9007199254740991):0;var e},he=function(t){return function(e,i,n){var r,o=W(e),a=ue(o),s=function(t,e){var i=oe(t);return i<0?ae(i+e,0):se(i,e)}(n,a);if(t&&i!=i){for(;a>s;)if((r=o[s++])!=r)return!0}else for(;a>s;s++)if((t||s in o)&&o[s]===i)return t||s||0;return!t&&-1}},le={includes:he(!0),indexOf:he(!1)},fe=le.indexOf,de=x([].push),pe=function(t,e){var i,n=W(t),r=0,o=[];for(i in n)!at(Bt,i)&&at(n,i)&&de(o,i);for(;e.length>r;)at(n,i=e[r++])&&(~fe(o,i)||de(o,i));return o},ge=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],ve=ge.concat("length","prototype"),me={f:Object.getOwnPropertyNames||function(t){return pe(t,ve)}},ye={f:Object.getOwnPropertySymbols},we=x([].concat),be=F("Reflect","ownKeys")||function(t){var e=me.f(Et(t)),i=ye.f;return i?we(e,i(t)):e},Se=function(t,e){for(var i=be(e),n=Pt.f,r=Ot.f,o=0;o<i.length;o++){var a=i[o];at(t,a)||n(t,a,r(e,a))}},Ce=/#|\.prototype\./,ze=function(t,e){var i=Oe[xe(t)];return i==Ie||i!=ke&&($(e)?f(e):!!e)},xe=ze.normalize=function(t){return String(t).replace(Ce,".").toLowerCase()},Oe=ze.data={},ke=ze.NATIVE="N",Ie=ze.POLYFILL="P",Ee=ze,Te=Ot.f,Ae=function(t,e){var i,n,r,o,a,s=t.target,c=t.global,u=t.stat;if(i=c?l:u?l[s]||tt(s,{}):(l[s]||{}).prototype)for(n in e){if(o=e[n],r=t.noTargetGet?(a=Te(i,n))&&a.value:i[n],!Ee(c?n:s+(u?".":"#")+n,t.forced)&&void 0!==r){if(typeof o==typeof r)continue;Se(o,r)}(t.sham||r&&r.sham)&&_t(o,"sham",!0),ie(i,n,o,t)}},Pe=Object.keys||function(t){return pe(t,ge)},_e=d?Object.defineProperties:function(t,e){Et(t);for(var i,n=W(e),r=Pe(e),o=r.length,a=0;o>a;)Pt.f(t,i=r[a++],n[i]);return t},We=F("document","documentElement"),$e=Mt("IE_PROTO"),je=function(){},Re=function(t){return"<script>"+t+"<\/script>"},Fe=function(t){t.write(Re("")),t.close();var e=t.parentWindow.Object;return t=null,e},De=function(){try{Vt=new ActiveXObject("htmlfile")}catch(t){}var t,e;De="undefined"!=typeof document?document.domain&&Vt?Fe(Vt):((e=Ct("iframe")).style.display="none",We.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(Re("document.F=Object")),t.close(),t.F):Fe(Vt);for(var i=ge.length;i--;)delete De.prototype[ge[i]];return De()};Bt[$e]=!0;var He=Object.create||function(t,e){var i;return null!==t?(je.prototype=Et(t),i=new je,je.prototype=null,i[$e]=t):i=De(),void 0===e?i:_e(i,e)},Le=gt("unscopables"),Me=Array.prototype;null==Me[Le]&&Pt.f(Me,Le,{configurable:!0,value:He(null)});var Be=function(t){Me[Le][t]=!0},Ne=le.includes;Ae({target:"Array",proto:!0},{includes:function(t){return Ne(this,t,arguments.length>1?arguments[1]:void 0)}}),Be("includes"),Ae({target:"Object",stat:!0,forced:!d,sham:!d},{defineProperty:Pt.f});var Xe=gt("match"),Ge=l.TypeError,Ue=function(t){if(function(t){var e;return j(t)&&(void 0!==(e=t[Xe])?!!e:"RegExp"==I(t))}(t))throw Ge("The method doesn't accept regular expressions");return t},qe={};qe[gt("toStringTag")]="z";var Ye="[object z]"===String(qe),Ke=gt("toStringTag"),Ve=l.Object,Je="Arguments"==I(function(){return arguments}()),Qe=Ye?I:function(t){var e,i,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(i=function(t,e){try{return t[e]}catch(t){}}(e=Ve(t),Ke))?i:Je?I(e):"Object"==(n=I(e))&&$(e.callee)?"Arguments":n},Ze=l.String,ti=function(t){if("Symbol"===Qe(t))throw TypeError("Cannot convert a Symbol value to a string");return Ze(t)},ei=gt("match"),ii=x("".indexOf);Ae({target:"String",proto:!0,forced:!function(t){var e=/./;try{"/./"[t](e)}catch(i){try{return e[ei]=!1,"/./"[t](e)}catch(t){}}return!1}("includes")},{includes:function(t){return!!~ii(ti(_(this)),ti(Ue(t)),arguments.length>1?arguments[1]:void 0)}});var ni,ri,oi=le.indexOf,ai=x([].indexOf),si=!!ai&&1/ai([1],1,-0)<0,ci=!!(ri=[]["indexOf"])&&f((function(){ri.call(null,ni||function(){throw 1},1)}));Ae({target:"Array",proto:!0,forced:si||!ci},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return si?ai(this,t,e)||0:oi(this,t,e)}});var ui=x(x.bind),hi=Array.isArray||function(t){return"Array"==I(t)},li=function(){},fi=[],di=F("Reflect","construct"),pi=/^\s*(?:class|function)\b/,gi=x(pi.exec),vi=!pi.exec(li),mi=function(t){if(!$(t))return!1;try{return di(li,fi,t),!0}catch(t){return!1}},yi=!di||f((function(){var t;return mi(mi.call)||!mi(Object)||!mi((function(){t=!0}))||t}))?function(t){if(!$(t))return!1;switch(Qe(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}return vi||!!gi(pi,Ft(t))}:mi,wi=gt("species"),bi=l.Array,Si=function(t,e){return new(function(t){var e;return hi(t)&&(e=t.constructor,(yi(e)&&(e===bi||hi(e.prototype))||j(e)&&null===(e=e[wi]))&&(e=void 0)),void 0===e?bi:e}(t))(0===e?0:e)},Ci=x([].push),zi=function(t){var e=1==t,i=2==t,n=3==t,r=4==t,o=6==t,a=7==t,s=5==t||o;return function(c,u,h,l){for(var f,d,p=rt(c),g=A(p),v=function(t,e){return J(t),void 0===e?t:ui?ui(t,e):function(){return t.apply(e,arguments)}}(u,h),m=ue(g),y=0,w=l||Si,b=e?w(c,m):i||a?w(c,0):void 0;m>y;y++)if((s||y in g)&&(d=v(f=g[y],y,p),t))if(e)b[y]=d;else if(d)switch(t){case 3:return!0;case 5:return f;case 6:return y;case 2:Ci(b,f)}else switch(t){case 4:return!1;case 7:Ci(b,f)}return o?-1:n||r?r:b}},xi={forEach:zi(0),map:zi(1),filter:zi(2),some:zi(3),every:zi(4),find:zi(5),findIndex:zi(6),filterReject:zi(7)}.find,Oi=!0;"find"in[]&&Array(1).find((function(){Oi=!1})),Ae({target:"Array",proto:!0,forced:Oi},{find:function(t){return xi(this,t,arguments.length>1?arguments[1]:void 0)}}),Be("find"),Array.prototype.includes||Object.defineProperty(Array.prototype,"includes",{value:function(t,e){if(null==this)throw new TypeError('"this" is null or not defined');var i=Object(this),n=i.length>>>0;if(0===n)return!1;var r,o,a=0|e,s=Math.max(a>=0?a:n-Math.abs(a),0);for(;s<n;){if((r=i[s])===(o=t)||"number"==typeof r&&"number"==typeof o&&isNaN(r)&&isNaN(o))return!0;s++}return!1}}),String.prototype.includes||(String.prototype.includes=function(t,e){return"number"!=typeof e&&(e=0),!(e+t.length>this.length)&&-1!==this.indexOf(t,e)}),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(t){if(null==this)throw new TypeError('"this" is null or not defined');var e=Object(this),i=e.length>>>0;if("function"!=typeof t)throw new TypeError("predicate must be a function");for(var n=arguments[1],r=0;r<i;){var o=e[r];if(t.call(n,o,r,e))return o;r++}}});var ki=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];var n=typeof t;return"object"!==n?e.includes(n):null===n?e.includes("null"):(Array.isArray(t)&&e.includes("array"),e.includes("object"))},Ii=function(t,e){for(var i=0,n=e.split(".");i<n.length;i++){var r=t[n[i]];if(!ki(r,"object","array"))return r;t=r}return t},Ei=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},Ti=function(t){return[].filter.call(t,(function(t){return"\n"!==t})).join("")},Ai=function(t){if("string"!=typeof t)return!1;if("transparent"===(t=t.toLocaleLowerCase().trim()))return!1;if(/^rgba/.test(t)){var e=/([^\s,]+)\)$/.exec(t);if(0===(null===(i=e)?0:"object"==typeof i?NaN:"number"==typeof i?i:"string"==typeof i?"%"===i[i.length-1]?Number(i.slice(0,-1))/100:Number(i):NaN))return!1}var i;return!0},Pi=function(t){var e,i=(null===(e=t.padding)||void 0===e?void 0:e.replace(/px/g,"").split(" ").map((function(t){return~~t})))||[0],n=0,r=0,o=0,a=0;switch(i.length){case 1:n=r=o=a=i[0];break;case 2:n=r=i[0],o=a=i[1];break;case 3:n=i[0],o=a=i[1],r=i[2];break;default:n=i[0],r=i[1],o=i[2],a=i[3]}var s={paddingTop:n,paddingBottom:r,paddingLeft:o,paddingRight:a};for(var c in s)s[c]=Object.prototype.hasOwnProperty.call(t,c)&&ki(t[c],"string","number")?~~String(t[c]).replace(/px/g,""):s[c];return[n,r,o,a]},_i=function(t){var e=[],i=t.map((function(t){return Number(t)})).reduce((function(t,i){if(i>0){var n=t+i;return e.push(n),n}return e.push(NaN),t}),0),n=Math.random()*i;return e.findIndex((function(t){return n<=t}))},Wi=function(t,e,i,n){void 0===n&&(n=1/0),n<=0&&(n=1/0);for(var r="",o=[],a=t.measureText("...").width,s=0;s<e.length;s++){r+=e[s];var c=t.measureText(r).width,u=i(o);if(n===o.length+1&&(c+=a),u<0)return o;if(c>u&&(o.push(r.slice(0,-1)),r=e[s]),n===o.length)return o[o.length-1]+="...",o}return r&&o.push(r),o.length||o.push(e),o},$i=function(t){return Math.PI/180*t},ji=function(t,e,i,n,r,o){t.beginPath();var a,s,c=$i(90/Math.PI/i*o),u=n+c,h=r-c;t.arc(0,0,i,u,h,!1),t.lineTo.apply(t,(a=(n+r)/2,s=o/2/Math.abs(Math.sin((n-r)/2)),[+(Math.cos(a)*s).toFixed(8),+(Math.sin(a)*s).toFixed(8)])),t.closePath()},Ri=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];var n=e[0],r=e[1],o=e[2],a=e[3],s=e[4],c=Math.min(o,a),u=Math.PI;s>c/2&&(s=c/2),t.beginPath(),t.moveTo(n+s,r),t.lineTo(n+s,r),t.lineTo(n+o-s,r),t.arc(n+o-s,r+s,s,-u/2,0),t.lineTo(n+o,r+a-s),t.arc(n+o-s,r+a-s,s,0,u/2),t.lineTo(n+s,r+a),t.arc(n+s,r+a-s,s,u/2,u),t.lineTo(n,r+s),t.arc(n+s,r+s,s,u,-u/2),t.closePath()},Fi=function(t,e,i,n,r,o){var a=document.createElement("canvas"),s=a.getContext("2d"),c=t.width,u=t.height;a.width=c,a.height=u;var h=c/n;return Ri(s,e*h,i*h,n*h,r*h,o*h),s.clip(),s.drawImage(t,0,0,c,u),a},Di=function(t,e){var i=document.createElement("canvas"),n=i.getContext("2d"),r=t.width,o=t.height;i.width=r,i.height=o,n.drawImage(t,0,0,r,o);var a=n.getImageData(0,0,r,o),s=a.data,c=function(t,e){e=e||t/3;for(var i=Math.ceil(t),n=e*e,r=2*n,o=1/(2*Math.PI*n),a=[],s=0,c=-i;c<=i;c++)for(var u=-i;u<=i;u++){var h=o*Math.exp(-(c*c+u*u)/r);a.push(h),s+=h}for(var l=0;l<a.length;l++)a[l]/=s;return a}(e);return s.length,c.length,console.log(a),n.putImageData(a,0,0),i},Hi=function(){function t(){this.subs=[]}return t.prototype.addSub=function(t){this.subs.includes(t)||this.subs.push(t)},t.prototype.notify=function(){this.subs.forEach((function(t){t.update()}))},t}(),Li="__proto__"in{};function Mi(t,e,i,n){Object.defineProperty(t,e,{value:i,enumerable:!!n,writable:!0,configurable:!0})}var Bi=Array.prototype,Ni=Object.create(Bi);["push","pop","shift","unshift","sort","splice","reverse"].forEach((function(t){Ni[t]=function(){for(var e=[],i=0;i<arguments.length;i++)e[i]=arguments[i];var n=Bi[t].apply(this,e),r=this.__luckyOb__;return["push","unshift","splice"].includes(t)&&r.walk(this),r.dep.notify(),n}}));var Xi=function(){function t(t){this.dep=new Hi,Mi(t,"__luckyOb__",this),Array.isArray(t)&&(Li?t.__proto__=Ni:Object.getOwnPropertyNames(Ni).forEach((function(e){Mi(t,e,Ni[e])}))),this.walk(t)}return t.prototype.walk=function(t){Object.keys(t).forEach((function(e){Ui(t,e,t[e])}))},t}();function Gi(t){if(t&&"object"==typeof t)return"__luckyOb__"in t?t.__luckyOb__:new Xi(t)}function Ui(t,e,i){var n=new Hi,r=Object.getOwnPropertyDescriptor(t,e);if(!r||!1!==r.configurable){var o=r&&r.get,a=r&&r.set;o&&!a||2!==arguments.length||(i=t[e]);var s=Gi(i);Object.defineProperty(t,e,{get:function(){var e=o?o.call(t):i;return Hi.target&&(n.addSub(Hi.target),s&&s.dep.addSub(Hi.target)),e},set:function(e){e!==i&&(i=e,o&&!a||(a?a.call(t,e):i=e,s=Gi(e),n.notify()))}})}}var qi=0,Yi=function(){function t(t,e,i,n){void 0===n&&(n={}),this.id=qi++,this.$lucky=t,this.expr=e,this.deep=!!n.deep,this.getter="function"==typeof e?e:function(t){t+=".";for(var e=[],i="",n=0;n<t.length;n++){var r=t[n];if(/\[|\./.test(r))e.push(i),i="";else{if(/\W/.test(r))continue;i+=r}}return function(t){return e.reduce((function(t,e){return t[e]}),t)}}(e),this.cb=i,this.value=this.get()}return t.prototype.get=function(){Hi.target=this;var t=this.getter.call(this.$lucky,this.$lucky);return this.deep&&function(t){var e=function(t){ki(t,"array","object")&&Object.keys(t).forEach((function(i){var n=t[i];e(n)}))};e(t)}(t),Hi.target=null,t},t.prototype.update=function(){var t=this.get(),e=this.value;this.value=t,this.cb.call(this.$lucky,t,e)},t}(),Ki=function(){function t(t,e){var i=this;this.version="1.7.temp",this.htmlFontSize=16,this.rAF=function(){},this.boxWidth=0,this.boxHeight=0,"string"==typeof t?t={el:t}:1===t.nodeType&&(t={el:"",divElement:t}),t=t,this.config=t,this.data=e,t.flag||(t.flag="WEB"),t.el&&(t.divElement=document.querySelector(t.el)),t.divElement&&(t.canvasElement=document.createElement("canvas"),t.divElement.appendChild(t.canvasElement)),t.canvasElement&&(t.ctx=t.canvasElement.getContext("2d"),t.canvasElement.setAttribute("package","<EMAIL>"),t.canvasElement.addEventListener("click",(function(t){return i.handleClick(t)}))),this.ctx=t.ctx,this.initWindowFunction(),this.config.ctx||console.error("无法获取到 CanvasContext2D"),window&&"function"==typeof window.addEventListener&&window.addEventListener("resize",function(t,e){void 0===e&&(e=300);var i=null;return function(){for(var n=this,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];i||(i=setTimeout((function(){t.apply(n,r),clearTimeout(i),i=null}),e))}}((function(){return i.resize()}),300)),window&&"function"==typeof window.MutationObserver&&new window.MutationObserver((function(){i.resize()})).observe(document.documentElement,{attributes:!0})}return t.prototype.resize=function(){var t,e;null===(e=(t=this.config).beforeResize)||void 0===e||e.call(t),this.setHTMLFontSize(),this.setDpr(),this.resetWidthAndHeight(),this.zoomCanvas()},t.prototype.initLucky=function(){if(this.resize(),!this.boxWidth||!this.boxHeight)return console.error("无法获取到宽度或高度")},t.prototype.handleClick=function(t){},t.prototype.setHTMLFontSize=function(){window&&(this.htmlFontSize=+window.getComputedStyle(document.documentElement).fontSize.slice(0,-2))},t.prototype.clearCanvas=function(){var t=[this.boxWidth,this.boxHeight],e=t[0],i=t[1];this.ctx.clearRect(-e,-i,2*e,2*i)},t.prototype.setDpr=function(){var t=this.config;t.dpr||(window?window.dpr=t.dpr=window.devicePixelRatio||1:t.dpr||console.error(t,"未传入 dpr 可能会导致绘制异常"))},t.prototype.resetWidthAndHeight=function(){var t=this.config,e=this.data,i=0,n=0;t.divElement&&(i=t.divElement.offsetWidth,n=t.divElement.offsetHeight),this.boxWidth=this.getLength(e.width||t.width)||i,this.boxHeight=this.getLength(e.height||t.height)||n,t.divElement&&(t.divElement.style.overflow="hidden",t.divElement.style.width=this.boxWidth+"px",t.divElement.style.height=this.boxHeight+"px")},t.prototype.zoomCanvas=function(){var t=this.config,e=this.ctx,i=t.canvasElement,n=t.dpr,r=[this.boxWidth*n,this.boxHeight*n],o=r[0],a=r[1];i&&(i.width=o,i.height=a,i.style.width=o+"px",i.style.height=a+"px",i.style["transform-origin"]="left top",i.style.transform="scale("+1/n+")",e.scale(n,n))},t.prototype.initWindowFunction=function(){var t=this.config;if(window)return this.rAF=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)},t.setTimeout=window.setTimeout,t.setInterval=window.setInterval,t.clearTimeout=window.clearTimeout,void(t.clearInterval=window.clearInterval);if(t.rAF)this.rAF=t.rAF;else if(t.setTimeout){var e=t.setTimeout;this.rAF=function(t){return e(t,16.7)}}else this.rAF=function(t){return setTimeout(t,16.7)}},t.prototype.loadImg=function(t,e,i){var n=this;return void 0===i&&(i="$resolve"),new Promise((function(r,o){if(t||o("=> '"+e.src+"' 不能为空或不合法"),"WEB"!==n.config.flag)return e[i]=r,void(e.$reject=o);var a=new Image;a.onload=function(){return r(a)},a.onerror=function(){return o("=> '"+e.src+"' 图片加载失败")},a.src=t}))},t.prototype.drawImage=function(t,e){for(var i,n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var a=this.config,s=a.flag,c=a.dpr;if(["WEB","MP-WX"].includes(s))i=e;else{if(!["UNI-H5","UNI-MP","TARO-H5","TARO-MP"].includes(s))return console.error("意料之外的 flag, 该平台尚未兼容!");i=e.path}return 8===n.length&&(n=n.map((function(t,e){return e<4?t*c:t}))),t.drawImage.apply(t,o([i],n))},t.prototype.getLength=function(t){return ki(t,"number")?t:ki(t,"string")?this.changeUnits(t):0},t.prototype.changeUnits=function(t,e){var i=this;return void 0===e&&(e=1),Number(t.replace(/^([-]*[0-9.]*)([a-z%]*)$/,(function(t,n,r){var o={"%":function(t){return t*(e/100)},px:function(t){return 1*t},rem:function(t){return t*i.htmlFontSize},vw:function(t){return t/100*window.innerWidth}}[r];if(o)return o(n);var a=i.config.unitFunc;return a?a(n,r):n})))},t.prototype.computedWidthAndHeight=function(t,e,i,n){if(!e.width&&!e.height)return[t.width,t.height];if(e.width&&!e.height){var r=this.getWidth(e.width,i);return[r,t.height*(r/t.width)]}if(!e.width&&e.height){var o=this.getHeight(e.height,n);return[t.width*(o/t.height),o]}return[this.getWidth(e.width,i),this.getHeight(e.height,n)]},t.prototype.getWidth=function(t,e){return ki(t,"number")?t:ki(t,"string")?this.changeUnits(t,e):0},t.prototype.getHeight=function(t,e){return ki(t,"number")?t:ki(t,"string")?this.changeUnits(t,e):0},t.prototype.getOffsetX=function(t,e){return void 0===e&&(e=0),(e-t)/2},t.prototype.getOffscreenCanvas=function(t,e){if(!Ei(this,"_offscreenCanvas")){if(!window||!window.document)return console.error("无法创建离屏Canvas!");this._offscreenCanvas=document.createElement("canvas")}var i=this.config.dpr,n=this._offscreenCanvas;n.width=(t||300)*i,n.height=(e||150)*i;var r=n.getContext("2d");return r.clearRect(0,0,t,e),r.scale(i,i),r.dpr=i,{_offscreenCanvas:n,_ctx:r}},t.prototype.$clip=function(t){for(var e=this,i=[],n=1;n<arguments.length;n++)i[n-1]=arguments[n];var r=i.map((function(t){return e.getLength(t)}));return Fi.apply(void 0,o([t],r))},t.prototype.$opacity=function(t,e){return function(t,e){var i=document.createElement("canvas"),n=i.getContext("2d"),r=t.width,o=t.height;if(i.width=r,i.height=o,"string"==typeof n.filter)n.filter="opacity("+e+"%)",n.drawImage(t,0,0,r,o);else{n.drawImage(t,0,0,r,o);for(var a=n.getImageData(0,0,r,o),s=a.data,c=s.length,u=0;u<c;u+=4){var h=s[u+3];0!==h&&(s[u+3]=h*e/100)}n.putImageData(a,0,0)}return i}(t,this.getLength(e))},t.prototype.$blur=function(t,e){return Di(t,this.getLength(e))},t.prototype.$set=function(t,e,i){t&&"object"==typeof t&&Ui(t,e,i)},t.prototype.$computed=function(t,e,i){var n=this;Object.defineProperty(t,e,{get:function(){return i.call(n)}})},t.prototype.$watch=function(t,e,i){void 0===i&&(i={}),"object"==typeof e&&(e=(i=e).handler);var n=new Yi(this,t,e,i);return i.immediate&&e.call(this,n.value),function(){}},t}(),Vi=function(t,e,i,n){return t>=n&&(t=n),i*(t/=n)*t+e},Ji=function(t,e,i,n){return t>=n&&(t=n),-i*(t/=n)*(t-2)+e},Qi=function(t){function o(e,i){var n,r=t.call(this,e,{width:i.width,height:i.height})||this;return r.blocks=[],r.prizes=[],r.buttons=[],r.defaultConfig={},r.defaultStyle={},r._defaultConfig={},r._defaultStyle={},r.Radius=0,r.prizeRadius=0,r.prizeDeg=0,r.prizeRadian=0,r.rotateDeg=0,r.maxBtnRadius=0,r.startTime=0,r.endTime=0,r.stopDeg=0,r.endDeg=0,r.FPS=16.6,r.step=0,r.ImageCache={blocks:[],prizes:[],buttons:[]},r.initData(i),r.initWatch(),r.initComputed(),null===(n=e.beforeCreate)||void 0===n||n.call(r),r.init(),r}return e(o,t),o.prototype.resize=function(){var e,i;t.prototype.resize.call(this),this.Radius=Math.min(this.boxWidth,this.boxHeight)/2,this.ctx.translate(this.Radius,this.Radius),this.draw(),null===(i=(e=this.config).afterResize)||void 0===i||i.call(e)},o.prototype.initLucky=function(){this.Radius=0,this.prizeRadius=0,this.prizeDeg=0,this.prizeRadian=0,this.rotateDeg=0,this.maxBtnRadius=0,this.startTime=0,this.endTime=0,this.stopDeg=0,this.endDeg=0,this.FPS=16.6,this.prizeFlag=-1,this.step=0,t.prototype.initLucky.call(this)},o.prototype.initData=function(t){this.$set(this,"width",t.width||"300px"),this.$set(this,"height",t.height||"300px"),this.$set(this,"blocks",t.blocks||[]),this.$set(this,"prizes",t.prizes||[]),this.$set(this,"buttons",t.buttons||[]),this.$set(this,"defaultConfig",t.defaultConfig||{}),this.$set(this,"defaultStyle",t.defaultStyle||{}),this.$set(this,"startCallback",t.start),this.$set(this,"endCallback",t.end)},o.prototype.initComputed=function(){var t=this;this.$computed(this,"_defaultConfig",(function(){return i({gutter:"0px",offsetDegree:0,speed:20,speedFunction:"quad",accelerationTime:2500,decelerationTime:2500,stopRange:0},t.defaultConfig)})),this.$computed(this,"_defaultStyle",(function(){return i({fontSize:"18px",fontColor:"#000",fontStyle:"sans-serif",fontWeight:"400",background:"rgba(0,0,0,0)",wordWrap:!0,lengthLimit:"90%"},t.defaultStyle)}))},o.prototype.initWatch=function(){var t=this;this.$watch("width",(function(e){t.data.width=e,t.resize()})),this.$watch("height",(function(e){t.data.height=e,t.resize()})),this.$watch("blocks",(function(e){t.initImageCache()}),{deep:!0}),this.$watch("prizes",(function(e){t.initImageCache()}),{deep:!0}),this.$watch("buttons",(function(e){t.initImageCache()}),{deep:!0}),this.$watch("defaultConfig",(function(){return t.draw()}),{deep:!0}),this.$watch("defaultStyle",(function(){return t.draw()}),{deep:!0}),this.$watch("startCallback",(function(){return t.init()})),this.$watch("endCallback",(function(){return t.init()}))},o.prototype.init=function(){var t,e;return n(this,void 0,void 0,(function(){var i;return r(this,(function(n){switch(n.label){case 0:return this.initLucky(),i=this.config,null===(t=i.beforeInit)||void 0===t||t.call(this),this.draw(),this.draw(),[4,this.initImageCache()];case 1:return n.sent(),null===(e=i.afterInit)||void 0===e||e.call(this),[2]}}))}))},o.prototype.initImageCache=function(){var t=this;return new Promise((function(e){var i={blocks:t.blocks.map((function(t){return t.imgs})),prizes:t.prizes.map((function(t){return t.imgs})),buttons:t.buttons.map((function(t){return t.imgs}))};Object.keys(i).forEach((function(n){var r=i[n],o=[];r&&r.forEach((function(e,i){e&&e.forEach((function(e,r){o.push(t.loadAndCacheImg(n,i,n,r))}))})),Promise.all(o).then((function(){t.draw(),e()}))}))}))},o.prototype.handleClick=function(t){var e,i=this.ctx;i.beginPath(),i.arc(0,0,this.maxBtnRadius,0,2*Math.PI,!1),i.isPointInPath(t.offsetX,t.offsetY)&&0===this.step&&(null===(e=this.startCallback)||void 0===e||e.call(this,t))},o.prototype.loadAndCacheImg=function(t,e,i,o){return n(this,void 0,void 0,(function(){var n=this;return r(this,(function(r){return[2,new Promise((function(r,a){var s=n[t][e];if(s&&s.imgs){var c=s.imgs[o];if(c){var u=n.ImageCache;u[i][e]||(u[i][e]=[]),n.loadImg(c.src,c).then((function(t){"function"==typeof c.formatter&&(t=c.formatter.call(n,t)),u[i][e][o]=t,r()})).catch((function(i){console.error(t+"["+e+"].imgs["+o+"] "+i),a()}))}}}))]}))}))},o.prototype.draw=function(){var t,e,i=this,n=this,r=n.config,o=n.ctx,a=n._defaultConfig,s=n._defaultStyle;null===(t=r.beforeDraw)||void 0===t||t.call(this,o),o.clearRect(-this.Radius,-this.Radius,2*this.Radius,2*this.Radius),this.prizeRadius=this.blocks.reduce((function(t,e,n){return Ai(e.background)&&(o.beginPath(),o.fillStyle=e.background,o.arc(0,0,t,0,2*Math.PI,!1),o.fill()),e.imgs&&e.imgs.forEach((function(e,r){var a=i.ImageCache.blocks;if(a&&a[n]){var s=a[n][r];if(s){var c=i.computedWidthAndHeight(s,e,2*t,2*t),u=c[0],h=c[1],l=[i.getOffsetX(u),i.getHeight(e.top,2*t)-t],f=l[0],d=l[1];o.save(),e.rotate&&o.rotate($i(i.rotateDeg)),i.drawImage(o,s,f,d,u,h),o.restore()}}})),t-i.getLength(e.padding&&e.padding.split(" ")[0])}),this.Radius),this.prizeDeg=360/this.prizes.length,this.prizeRadian=$i(this.prizeDeg);var c=$i(this.rotateDeg-90+this.prizeDeg/2+a.offsetDegree),u=function(t){return i.getOffsetX(o.measureText(t).width)},h=function(t,e,n){var r=t.lineHeight||s.lineHeight||t.fontSize||s.fontSize;return i.getHeight(t.top,e)+(n+1)*i.getLength(r)};o.save(),this.prizes.forEach((function(t,e){var n=c+e*i.prizeRadian,r=i.prizeRadius-i.maxBtnRadius,l=t.background||s.background;Ai(l)&&(o.fillStyle=l,ji(o,i.maxBtnRadius,i.prizeRadius,n-i.prizeRadian/2,n+i.prizeRadian/2,i.getLength(a.gutter)),o.fill());var f=Math.cos(n)*i.prizeRadius,d=Math.sin(n)*i.prizeRadius;o.translate(f,d),o.rotate(n+$i(90)),t.imgs&&t.imgs.forEach((function(t,n){var a=i.ImageCache.prizes;if(a&&a[e]){var s=a[e][n];if(s){var c=i.computedWidthAndHeight(s,t,i.prizeRadian*i.prizeRadius,r),u=c[0],h=c[1],l=[i.getOffsetX(u),i.getHeight(t.top,r)],f=l[0],d=l[1];i.drawImage(o,s,f,d,u,h)}}})),t.fonts&&t.fonts.forEach((function(t){var e=t.fontColor||s.fontColor,n=t.fontWeight||s.fontWeight,c=i.getLength(t.fontSize||s.fontSize),l=t.fontStyle||s.fontStyle,f=Object.prototype.hasOwnProperty.call(t,"wordWrap")?t.wordWrap:s.wordWrap,d=t.lengthLimit||s.lengthLimit,p=t.lineClamp||s.lineClamp;o.fillStyle=e,o.font=n+" "+(c>>0)+"px "+l;var g=String(t.text);(f?Wi(o,Ti(g),(function(e){var n=2*((i.prizeRadius-h(t,r,e.length))*Math.tan(i.prizeRadian/2))-i.getLength(a.gutter);return i.getWidth(d,n)}),p):g.split("\n")).filter((function(t){return!!t})).forEach((function(e,i){o.fillText(e,u(e),h(t,r,i))}))})),o.rotate($i(360)-n-$i(90)),o.translate(-f,-d)})),o.restore(),this.buttons.forEach((function(t,e){var n=i.getHeight(t.radius,i.prizeRadius);i.maxBtnRadius=Math.max(i.maxBtnRadius,n),Ai(t.background)&&(o.beginPath(),o.fillStyle=t.background,o.arc(0,0,n,0,2*Math.PI,!1),o.fill()),t.pointer&&Ai(t.background)&&(o.beginPath(),o.fillStyle=t.background,o.moveTo(-n,0),o.lineTo(n,0),o.lineTo(0,2*-n),o.closePath(),o.fill()),t.imgs&&t.imgs.forEach((function(t,r){var a=i.ImageCache.buttons;if(a&&a[e]){var s=a[e][r];if(s){var c=i.computedWidthAndHeight(s,t,2*n,2*n),u=c[0],h=c[1],l=[i.getOffsetX(u),i.getHeight(t.top,n)],f=l[0],d=l[1];i.drawImage(o,s,f,d,u,h)}}})),t.fonts&&t.fonts.forEach((function(t){var e=t.fontColor||s.fontColor,r=t.fontWeight||s.fontWeight,a=i.getLength(t.fontSize||s.fontSize),c=t.fontStyle||s.fontStyle;o.fillStyle=e,o.font=r+" "+(a>>0)+"px "+c,String(t.text).split("\n").forEach((function(e,i){o.fillText(e,u(e),h(t,n,i))}))}))})),null===(e=r.afterDraw)||void 0===e||e.call(this,o)},o.prototype.carveOnGunwaleOfAMovingBoat=function(){var t=this,e=t._defaultConfig,i=t.prizeFlag,n=t.prizeDeg,r=t.rotateDeg;this.endTime=Date.now();for(var o=this.stopDeg=r,a=e.speed,s=(Math.random()*n-n/2)*this.getLength(e.stopRange),c=0,u=0,h=0;++c;){var l=360*c-i*n-r-e.offsetDegree+s-n/2,f=Ji(this.FPS,o,l,e.decelerationTime)-o;if(f>a){this.endDeg=a-u>f-a?l:h;break}h=l,u=f}},o.prototype.play=function(){var t,e;0===this.step&&(this.startTime=Date.now(),this.prizeFlag=void 0,this.step=1,null===(e=(t=this.config).afterStart)||void 0===e||e.call(t),this.run())},o.prototype.stop=function(t){if(0!==this.step&&3!==this.step){if(!t&&0!==t){var e=this.prizes.map((function(t){return t.range}));t=_i(e)}t<0?(this.step=0,this.prizeFlag=-1):(this.step=2,this.prizeFlag=t%this.prizes.length)}},o.prototype.run=function(t){var e;void 0===t&&(t=0);var i=this,n=i.rAF,r=i.step,o=i.prizeFlag,a=i.stopDeg,s=i.endDeg,c=i._defaultConfig,u=c.accelerationTime,h=c.decelerationTime,l=c.speed;if(0!==r){if(-1!==o){3!==r||this.endDeg||this.carveOnGunwaleOfAMovingBoat();var f=Date.now()-this.startTime,d=Date.now()-this.endTime,p=this.rotateDeg;if(1===r||f<u){this.FPS=f/t;var g=Vi(f,0,l,u);g===l&&(this.step=2),p+=g%360}else 2===r?(p+=l%360,void 0!==o&&o>=0&&(this.step=3,this.stopDeg=0,this.endDeg=0)):3===r?(p=Ji(d,a,s,h),d>=h&&(this.step=0)):this.stop(-1);this.rotateDeg=p,this.draw(),n(this.run.bind(this,t+1))}}else null===(e=this.endCallback)||void 0===e||e.call(this,this.prizes.find((function(t,e){return e===o}))||{})},o.prototype.conversionAxis=function(t,e){var i=this.config;return[t/i.dpr-this.Radius,e/i.dpr-this.Radius]},o}(Ki),Zi=function(t){function a(e,i){var n,r=t.call(this,e,{width:i.width,height:i.height})||this;return r.rows=3,r.cols=3,r.blocks=[],r.prizes=[],r.buttons=[],r.defaultConfig={},r.defaultStyle={},r.activeStyle={},r._defaultConfig={},r._defaultStyle={},r._activeStyle={},r.cellWidth=0,r.cellHeight=0,r.startTime=0,r.endTime=0,r.currIndex=0,r.stopIndex=0,r.endIndex=0,r.demo=!1,r.timer=0,r.FPS=16.6,r.step=0,r.prizeFlag=-1,r.cells=[],r.ImageCache={blocks:[],prizes:[],buttons:[]},r.initData(i),r.initWatch(),r.initComputed(),null===(n=e.beforeCreate)||void 0===n||n.call(r),r.init(),r}return e(a,t),a.prototype.resize=function(){var e,i;t.prototype.resize.call(this),this.draw(),null===(i=(e=this.config).afterResize)||void 0===i||i.call(e)},a.prototype.initLucky=function(){this.cellWidth=0,this.cellHeight=0,this.startTime=0,this.endTime=0,this.currIndex=0,this.stopIndex=0,this.endIndex=0,this.demo=!1,this.timer=0,this.FPS=16.6,this.prizeFlag=-1,this.step=0,t.prototype.initLucky.call(this)},a.prototype.initData=function(t){this.$set(this,"width",t.width||"300px"),this.$set(this,"height",t.height||"300px"),this.$set(this,"rows",Number(t.rows)||3),this.$set(this,"cols",Number(t.cols)||3),this.$set(this,"blocks",t.blocks||[]),this.$set(this,"prizes",t.prizes||[]),this.$set(this,"buttons",t.buttons||[]),this.$set(this,"button",t.button),this.$set(this,"defaultConfig",t.defaultConfig||{}),this.$set(this,"defaultStyle",t.defaultStyle||{}),this.$set(this,"activeStyle",t.activeStyle||{}),this.$set(this,"startCallback",t.start),this.$set(this,"endCallback",t.end)},a.prototype.initComputed=function(){var t=this;this.$computed(this,"_defaultConfig",(function(){var e=i({gutter:5,speed:20,accelerationTime:2500,decelerationTime:2500},t.defaultConfig);return e.gutter=t.getLength(e.gutter),e.speed=e.speed/40,e})),this.$computed(this,"_defaultStyle",(function(){return i({borderRadius:20,fontColor:"#000",fontSize:"18px",fontStyle:"sans-serif",fontWeight:"400",background:"rgba(0,0,0,0)",shadow:"",wordWrap:!0,lengthLimit:"90%"},t.defaultStyle)})),this.$computed(this,"_activeStyle",(function(){return i({background:"#ffce98",shadow:""},t.activeStyle)}))},a.prototype.initWatch=function(){var t=this;this.$watch("width",(function(e){t.data.width=e,t.resize()})),this.$watch("height",(function(e){t.data.height=e,t.resize()})),this.$watch("blocks",(function(e){t.initImageCache()}),{deep:!0}),this.$watch("prizes",(function(e){t.initImageCache()}),{deep:!0}),this.$watch("buttons",(function(e){t.initImageCache()}),{deep:!0}),this.$watch("rows",(function(){return t.init()})),this.$watch("cols",(function(){return t.init()})),this.$watch("defaultConfig",(function(){return t.draw()}),{deep:!0}),this.$watch("defaultStyle",(function(){return t.draw()}),{deep:!0}),this.$watch("activeStyle",(function(){return t.draw()}),{deep:!0}),this.$watch("startCallback",(function(){return t.init()})),this.$watch("endCallback",(function(){return t.init()}))},a.prototype.init=function(){var t,e;return n(this,void 0,void 0,(function(){var i;return r(this,(function(n){switch(n.label){case 0:return this.initLucky(),i=this.config,null===(t=i.beforeInit)||void 0===t||t.call(this),this.draw(),[4,this.initImageCache()];case 1:return n.sent(),null===(e=i.afterInit)||void 0===e||e.call(this),[2]}}))}))},a.prototype.initImageCache=function(){var t=this;return new Promise((function(e){var i=t.buttons.map((function(t){return t.imgs}));t.button&&i.push(t.button.imgs);var n={blocks:t.blocks.map((function(t){return t.imgs})),prizes:t.prizes.map((function(t){return t.imgs})),buttons:i};Object.keys(n).forEach((function(i){var r=n[i],o=[];r&&r.forEach((function(e,n){e&&e.forEach((function(e,r){o.push(t.loadAndCacheImg(i,n,i,r))}))})),Promise.all(o).then((function(){t.draw(),e()}))}))}))},a.prototype.handleClick=function(t){var e=this,i=this.ctx;o(o([],this.buttons),[this.button]).forEach((function(n){var r;if(n){var o=e.getGeometricProperty([n.x,n.y,n.col||1,n.row||1]),a=o[0],s=o[1],c=o[2],u=o[3];i.beginPath(),i.rect(a,s,c,u),i.isPointInPath(t.offsetX,t.offsetY)&&0===e.step&&("function"==typeof n.callback&&n.callback.call(e,n),null===(r=e.startCallback)||void 0===r||r.call(e,t,n))}}))},a.prototype.loadAndCacheImg=function(t,e,i,o){return n(this,void 0,void 0,(function(){var n=this;return r(this,(function(r){return[2,new Promise((function(r,a){var s=n[t][e];if("buttons"===t&&!n.buttons.length&&n.button&&(s=n.button),s&&s.imgs){var c=s.imgs[o];if(c){var u=n.ImageCache;u[i][e]||(u[i][e]=[]);var h=[n.loadImg(c.src,c),c.activeSrc&&n.loadImg(c.activeSrc,c,"$activeResolve")];Promise.all(h).then((function(t){var a=t[0],s=t[1],h=c.formatter;"function"==typeof h&&(a=h.call(n,a),s&&(s=h.call(n,s))),u[i][e][o]={defaultImg:a,activeImg:s},r()})).catch((function(i){console.error(t+"["+e+"].imgs["+o+"] "+i),a()}))}}}))]}))}))},a.prototype.draw=function(){var t,e,i=this,n=this,r=n.config,a=n.ctx,s=n._defaultConfig,c=n._defaultStyle,u=n._activeStyle;null===(t=r.beforeDraw)||void 0===t||t.call(this,a),a.clearRect(0,0,this.boxWidth,this.boxHeight),this.cells=o(o([],this.prizes),this.buttons),this.button&&this.cells.push(this.button),this.cells.forEach((function(t){t.col=t.col||1,t.row=t.row||1})),this.prizeArea=this.blocks.reduce((function(t,e,n){var r=t.x,o=t.y,s=t.w,u=t.h,h=Pi(e),l=h[0],f=h[1],d=h[2],p=h[3],g=e.borderRadius?i.getLength(e.borderRadius):0,v=e.background||c.background;return Ai(v)&&(a.fillStyle=i.handleBackground(r,o,s,u,v),Ri(a,r,o,s,u,g),a.fill()),e.imgs&&e.imgs.forEach((function(t,e){var c=Ii(i.ImageCache,"blocks."+n+"."+e+".defaultImg");if(c){var h=i.computedWidthAndHeight(c,t,s,u),l=h[0],f=h[1],d=[i.getOffsetX(l,s),i.getHeight(t.top,u)],p=d[0],g=d[1];i.drawImage(a,c,r+p,o+g,l,f)}})),{x:r+d,y:o+l,w:s-d-p,h:u-l-f}}),{x:0,y:0,w:this.boxWidth,h:this.boxHeight}),this.cellWidth=(this.prizeArea.w-s.gutter*(this.cols-1))/this.cols,this.cellHeight=(this.prizeArea.h-s.gutter*(this.rows-1))/this.rows,this.cells.forEach((function(t,e){var n=i.getGeometricProperty([t.x,t.y,t.col,t.row]),o=n[0],s=n[1],h=n[2],l=n[3],f=!1;(void 0===i.prizeFlag||i.prizeFlag>-1)&&(f=e===i.currIndex%i.prizes.length>>0);var d=f?u.background:t.background||c.background;if(Ai(d)){var p=(f?u.shadow:t.shadow||c.shadow).replace(/px/g,"").split(",")[0].split(" ").map((function(t,e){return e<3?Number(t):t}));4===p.length&&(a.shadowColor=p[3],a.shadowOffsetX=p[0]*r.dpr,a.shadowOffsetY=p[1]*r.dpr,a.shadowBlur=p[2],p[0]>0?h-=p[0]:(h+=p[0],o-=p[0]),p[1]>0?l-=p[1]:(l+=p[1],s-=p[1])),a.fillStyle=i.handleBackground(o,s,h,l,d);var g=i.getLength(t.borderRadius?t.borderRadius:c.borderRadius);Ri(a,o,s,h,l,g),a.fill(),a.shadowColor="rgba(0, 0, 0, 0)",a.shadowOffsetX=0,a.shadowOffsetY=0,a.shadowBlur=0}var v="prizes";e>=i.prizes.length&&(v="buttons",e-=i.prizes.length),t.imgs&&t.imgs.forEach((function(t,n){var r=i.ImageCache[v];if(r[e]){var c=r[e][n];if(c){var u=f&&c.activeImg||c.defaultImg;if(u){var d=i.computedWidthAndHeight(u,t,h,l),p=d[0],g=d[1],m=[o+i.getOffsetX(p,h),s+i.getHeight(t.top,l)],y=m[0],w=m[1];i.drawImage(a,u,y,w,p,g)}}}})),t.fonts&&t.fonts.forEach((function(t){var e=f&&u.fontStyle?u.fontStyle:t.fontStyle||c.fontStyle,n=f&&u.fontWeight?u.fontWeight:t.fontWeight||c.fontWeight,r=f&&u.fontSize?i.getLength(u.fontSize):i.getLength(t.fontSize||c.fontSize),d=f&&u.lineHeight?u.lineHeight:t.lineHeight||c.lineHeight||t.fontSize||c.fontSize,p=Object.prototype.hasOwnProperty.call(t,"wordWrap")?t.wordWrap:c.wordWrap,g=t.lengthLimit||c.lengthLimit,v=t.lineClamp||c.lineClamp;a.font=n+" "+(r>>0)+"px "+e,a.fillStyle=f&&u.fontColor?u.fontColor:t.fontColor||c.fontColor;var m=[],y=String(t.text);if(p){var w=i.getWidth(g,h);m=Wi(a,Ti(y),(function(){return w}),v)}else m=y.split("\n");m.forEach((function(e,n){a.fillText(e,o+i.getOffsetX(a.measureText(e).width,h),s+i.getHeight(t.top,l)+(n+1)*i.getLength(d))}))}))})),null===(e=r.afterDraw)||void 0===e||e.call(this,a)},a.prototype.handleBackground=function(t,e,i,n,r){var o=this.ctx;return r.includes("linear-gradient")&&(r=function(t,e,i,n,r,o){var a=/linear-gradient\((.+)\)/.exec(o)[1].split(",").map((function(t){return t.trim()})),s=a.shift(),c=[0,0,0,0];if(s.includes("deg")){var u=function(t){return Math.tan(t/180*Math.PI)};(s=s.slice(0,-3)%360)>=0&&s<45?c=[e,i+r,e+n,i+r-n*u(s-0)]:s>=45&&s<90?c=[e,i+r,e+n-r*u(s-45),i]:s>=90&&s<135?c=[e+n,i+r,e+n-r*u(s-90),i]:s>=135&&s<180?c=[e+n,i+r,e,i+n*u(s-135)]:s>=180&&s<225?c=[e+n,i,e,i+n*u(s-180)]:s>=225&&s<270?c=[e+n,i,e+r*u(s-225),i+r]:s>=270&&s<315?c=[e,i,e+r*u(s-270),i+r]:s>=315&&s<360&&(c=[e,i,e+n,i+r-n*u(s-315)])}else s.includes("top")?c=[e,i+r,e,i]:s.includes("bottom")?c=[e,i,e,i+r]:s.includes("left")?c=[e+n,i,e,i]:s.includes("right")&&(c=[e,i,e+n,i]);var h=t.createLinearGradient.apply(t,c.map((function(t){return t>>0})));return a.reduce((function(t,e,i){var n=e.split(" ");return 1===n.length?t.addColorStop(i,n[0]):2===n.length&&t.addColorStop.apply(t,n),t}),h)}(o,t,e,i,n,r)),r},a.prototype.carveOnGunwaleOfAMovingBoat=function(){var t=this,e=t._defaultConfig,i=t.prizeFlag,n=t.currIndex;this.endTime=Date.now();for(var r=this.stopIndex=n,o=e.speed,a=0,s=0,c=0;++a;){var u=this.prizes.length*a+i-r,h=Ji(this.FPS,r,u,e.decelerationTime)-r;if(h>o){this.endIndex=o-s>h-o?u:c;break}c=u,s=h}},a.prototype.play=function(){var t,e;0===this.step&&(this.startTime=Date.now(),this.prizeFlag=void 0,this.step=1,null===(e=(t=this.config).afterStart)||void 0===e||e.call(t),this.run())},a.prototype.stop=function(t){if(0!==this.step&&3!==this.step){if(!t&&0!==t){var e=this.prizes.map((function(t){return t.range}));t=_i(e)}t<0?(this.step=0,this.prizeFlag=-1):(this.step=2,this.prizeFlag=t%this.prizes.length)}},a.prototype.run=function(t){var e;void 0===t&&(t=0);var i=this,n=i.rAF,r=i.step,o=i.prizes,a=i.prizeFlag,s=i.stopIndex,c=i.endIndex,u=i._defaultConfig,h=u.accelerationTime,l=u.decelerationTime,f=u.speed;if(0!==r){if(-1!==a){3!==r||this.endIndex||this.carveOnGunwaleOfAMovingBoat();var d=Date.now()-this.startTime,p=Date.now()-this.endTime,g=this.currIndex;if(1===r||d<h){this.FPS=d/t;var v=Vi(d,.1,f-.1,h);v===f&&(this.step=2),g+=v%o.length}else 2===r?(g+=f%o.length,void 0!==a&&a>=0&&(this.step=3,this.stopIndex=0,this.endIndex=0)):3===r?(g=Ji(p,s,c,l),p>=l&&(this.step=0)):this.stop(-1);this.currIndex=g,this.draw(),n(this.run.bind(this,t+1))}}else null===(e=this.endCallback)||void 0===e||e.call(this,this.prizes.find((function(t,e){return e===a}))||{})},a.prototype.getGeometricProperty=function(t){var e=t[0],i=t[1],n=t[2],r=void 0===n?1:n,o=t[3],a=void 0===o?1:o,s=this.cellWidth,c=this.cellHeight,u=this._defaultConfig.gutter,h=[this.prizeArea.x+(s+u)*e,this.prizeArea.y+(c+u)*i];return r&&a&&h.push(s*r+u*(r-1),c*a+u*(a-1)),h},a.prototype.conversionAxis=function(t,e){var i=this.config;return[t/i.dpr,e/i.dpr]},a}(Ki),tn=function(t){function o(e,i){var n,r=t.call(this,e,{width:i.width,height:i.height})||this;return r.blocks=[],r.prizes=[],r.slots=[],r.defaultConfig={},r._defaultConfig={},r.defaultStyle={},r._defaultStyle={},r.endCallback=function(){},r.cellWidth=0,r.cellHeight=0,r.cellAndSpacing=0,r.widthAndSpacing=0,r.heightAndSpacing=0,r.FPS=16.6,r.scroll=[],r.stopScroll=[],r.endScroll=[],r.startTime=0,r.endTime=0,r.step=0,r.prizeFlag=void 0,r.ImageCache={blocks:[],prizes:[]},r.initData(i),r.initWatch(),r.initComputed(),null===(n=e.beforeCreate)||void 0===n||n.call(r),r.init(),r}return e(o,t),o.prototype.resize=function(){var e,i;t.prototype.resize.call(this),this.draw(),null===(i=(e=this.config).afterResize)||void 0===i||i.call(e)},o.prototype.initLucky=function(){this.cellWidth=0,this.cellHeight=0,this.cellAndSpacing=0,this.widthAndSpacing=0,this.heightAndSpacing=0,this.FPS=16.6,this.scroll=[],this.stopScroll=[],this.endScroll=[],this.startTime=0,this.endTime=0,this.prizeFlag=void 0,this.step=0,t.prototype.initLucky.call(this)},o.prototype.initData=function(t){this.$set(this,"width",t.width||"300px"),this.$set(this,"height",t.height||"300px"),this.$set(this,"blocks",t.blocks||[]),this.$set(this,"prizes",t.prizes||[]),this.$set(this,"slots",t.slots||[]),this.$set(this,"defaultConfig",t.defaultConfig||{}),this.$set(this,"defaultStyle",t.defaultStyle||{}),this.$set(this,"endCallback",t.end)},o.prototype.initComputed=function(){var t=this;this.$computed(this,"_defaultConfig",(function(){var e=i({mode:"vertical",rowSpacing:0,colSpacing:5,speed:20,direction:1,accelerationTime:2500,decelerationTime:2500},t.defaultConfig);return e.rowSpacing=t.getLength(e.rowSpacing),e.colSpacing=t.getLength(e.colSpacing),e})),this.$computed(this,"_defaultStyle",(function(){return i({borderRadius:0,fontColor:"#000",fontSize:"18px",fontStyle:"sans-serif",fontWeight:"400",background:"rgba(0,0,0,0)",wordWrap:!0,lengthLimit:"90%"},t.defaultStyle)}))},o.prototype.initWatch=function(){var t=this;this.$watch("width",(function(e){t.data.width=e,t.resize()})),this.$watch("height",(function(e){t.data.height=e,t.resize()})),this.$watch("blocks",(function(e){t.initImageCache()}),{deep:!0}),this.$watch("prizes",(function(e){t.initImageCache()}),{deep:!0}),this.$watch("slots",(function(e){t.drawOffscreenCanvas(),t.draw()}),{deep:!0}),this.$watch("defaultConfig",(function(){return t.draw()}),{deep:!0}),this.$watch("defaultStyle",(function(){return t.draw()}),{deep:!0}),this.$watch("endCallback",(function(){return t.init()}))},o.prototype.init=function(){var t,e;return n(this,void 0,void 0,(function(){var i;return r(this,(function(n){switch(n.label){case 0:return this.initLucky(),i=this.config,null===(t=i.beforeInit)||void 0===t||t.call(this),this.drawOffscreenCanvas(),this.draw(),[4,this.initImageCache()];case 1:return n.sent(),null===(e=i.afterInit)||void 0===e||e.call(this),[2]}}))}))},o.prototype.initImageCache=function(){var t=this;return new Promise((function(e){var i={blocks:t.blocks.map((function(t){return t.imgs})),prizes:t.prizes.map((function(t){return t.imgs}))};Object.keys(i).forEach((function(n){var r=i[n],o=[];r&&r.forEach((function(e,i){e&&e.forEach((function(e,r){o.push(t.loadAndCacheImg(n,i,n,r))}))})),Promise.all(o).then((function(){t.drawOffscreenCanvas(),t.draw(),e()}))}))}))},o.prototype.loadAndCacheImg=function(t,e,i,o){return n(this,void 0,void 0,(function(){var n=this;return r(this,(function(r){return[2,new Promise((function(r,a){var s=n[t][e];if(s&&s.imgs){var c=s.imgs[o];if(c){var u=n.ImageCache;u[i][e]||(u[i][e]=[]),n.loadImg(c.src,c).then((function(t){"function"==typeof c.formatter&&(t=c.formatter.call(n,t)),u[i][e][o]=t,r()})).catch((function(i){console.error(t+"["+e+"].imgs["+o+"] "+i),a()}))}}}))]}))}))},o.prototype.drawOffscreenCanvas=function(){var t=this,e=this._defaultConfig,i=this._defaultStyle,n=this.drawBlocks(),r=n.w,o=n.h,a=this.prizes.length,s=this.displacementWidthOrHeight(),c=s.cellWidth,u=s.cellHeight,h=s.widthAndSpacing,l=s.heightAndSpacing,f=new Array(a).fill(void 0).map((function(t,e){return e})),d=0,p=0;this.slots.forEach((function(e,i){void 0===t.scroll[i]&&(t.scroll[i]=0),e.order=e.order||f;var n=e.order.length;d=Math.max(d,r+h*n),p=Math.max(p,o+l*n)}));var g=this.getOffscreenCanvas(d,p),v=g._offscreenCanvas,m=g._ctx;this._offscreenCanvas=v,this.slots.forEach((function(n,r){var o=c*r,a=u*r,s=0,f=function(t,e){for(var i={},n=[],r=0;r<t.length;r++)i[r]=t[r];for(r=0;r<e.length;r++){var o=i[e[r]];o&&(n[r]=o)}return n}(t.prizes,n.order);if(f.length){f.forEach((function(r,f){if(r){var d=n.order[f],p=h*f+e.colSpacing/2,g=l*f+e.rowSpacing/2,v=t.displacement([o,g,l],[p,a,h]),y=v[0],w=v[1],b=v[2];s+=b;var S=r.background||i.background;if(Ai(S)){var C=t.getLength(Ei(r,"borderRadius")?r.borderRadius:i.borderRadius);Ri(m,y,w,c,c,C),m.fillStyle=S,m.fill()}r.imgs&&r.imgs.forEach((function(e,i){var n=Ii(t.ImageCache,"prizes."+d+"."+i);if(n){var r=t.computedWidthAndHeight(n,e,c,u),o=r[0],a=r[1],s=[y+t.getOffsetX(o,c),w+t.getHeight(e.top,u)],h=s[0],l=s[1];t.drawImage(m,n,h,l,o,a)}})),r.fonts&&r.fonts.forEach((function(e){var n=e.fontStyle||i.fontStyle,r=e.fontWeight||i.fontWeight,o=t.getLength(e.fontSize||i.fontSize),a=e.lineHeight||i.lineHeight||e.fontSize||i.fontSize,s=Object.prototype.hasOwnProperty.call(e,"wordWrap")?e.wordWrap:i.wordWrap,h=e.lengthLimit||i.lengthLimit,l=e.lineClamp||i.lineClamp;m.font=r+" "+(o>>0)+"px "+n,m.fillStyle=e.fontColor||i.fontColor;var f=[],d=String(e.text);if(s){var p=t.getWidth(h,c);f=Wi(m,Ti(d),(function(){return p}),l)}else f=d.split("\n");f.forEach((function(i,n){m.fillText(i,y+t.getOffsetX(m.measureText(i).width,c),w+t.getHeight(e.top,u)+(n+1)*t.getLength(a))}))}))}}));for(var d=t.displacement([o,0,c,s],[0,a,s,u]),g=d[0],y=d[1],w=d[2],b=d[3],S=s;S<p+s;){var C=t.displacement([g,S],[S,y]),z=C[0],x=C[1];t.drawImage(m,v,g,y,w,b,z,x,w,b),S+=s}}}))},o.prototype.drawBlocks=function(){var t=this,e=this;e.config;var i=e.ctx;e._defaultConfig;var n=e._defaultStyle;return this.prizeArea=this.blocks.reduce((function(e,r,o){var a=e.x,s=e.y,c=e.w,u=e.h,h=Pi(r),l=h[0],f=h[1],d=h[2],p=h[3],g=r.borderRadius?t.getLength(r.borderRadius):0,v=r.background||n.background;return Ai(v)&&(Ri(i,a,s,c,u,g),i.fillStyle=v,i.fill()),r.imgs&&r.imgs.forEach((function(e,n){var r=Ii(t.ImageCache,"blocks."+o+"."+n);if(r){var h=t.computedWidthAndHeight(r,e,c,u),l=h[0],f=h[1],d=[t.getOffsetX(l,c),t.getHeight(e.top,u)],p=d[0],g=d[1];t.drawImage(i,r,a+p,s+g,l,f)}})),{x:a+d,y:s+l,w:c-d-p,h:u-l-f}}),{x:0,y:0,w:this.boxWidth,h:this.boxHeight})},o.prototype.draw=function(){var t,e=this,i=this,n=i.config,r=i.ctx;i._defaultConfig,i._defaultStyle,null===(t=n.beforeDraw)||void 0===t||t.call(this,r),r.clearRect(0,0,this.boxWidth,this.boxHeight);var o=this.drawBlocks(),a=o.x,s=o.y,c=o.w,u=o.h;if(this._offscreenCanvas){var h=this,l=h.cellWidth,f=h.cellHeight,d=h.cellAndSpacing,p=h.widthAndSpacing,g=h.heightAndSpacing;this.slots.forEach((function(t,i){var n=d*t.order.length,o=e.displacement(-(u-g)/2,-(c-p)/2),h=e.scroll[i]+o;h<0&&(h=h%n+n),h>n&&(h%=n);var v=e.displacement([l*i,h,l,u],[h,f*i,c,f]),m=v[0],y=v[1],w=v[2],b=v[3],S=e.displacement([a+p*i,s,l,u],[a,s+g*i,c,f]),C=S[0],z=S[1],x=S[2],O=S[3];e.drawImage(r,e._offscreenCanvas,m,y,w,b,C,z,x,O)}))}},o.prototype.carveOnGunwaleOfAMovingBoat=function(){var t=this,e=this,i=e._defaultConfig,n=e.prizeFlag,r=e.cellAndSpacing;this.endTime=Date.now(),this.slots.forEach((function(e,o){var a=e.order;if(a.length)for(var s=e.speed||i.speed,c=e.direction||i.direction,u=a.findIndex((function(t){return t===n[o]})),h=r*a.length,l=t.stopScroll[o]=t.scroll[o],f=0;++f;){var d=r*u+h*f*c-l,p=Ji(t.FPS,l,d,i.decelerationTime)-l;if(Math.abs(p)>s){t.endScroll[o]=d;break}}}))},o.prototype.play=function(){0===this.step&&(this.startTime=Date.now(),this.prizeFlag=void 0,this.step=1,this.run())},o.prototype.stop=function(t){var e;if(0!==this.step&&3!==this.step){if("number"==typeof t)this.prizeFlag=new Array(this.slots.length).fill(t);else{if(!ki(t,"array"))return this.stop(-1),console.error("stop() 无法识别的参数类型 "+typeof t);if(t.length!==this.slots.length)return this.stop(-1),console.error("stop(["+t+"]) 参数长度的不正确");this.prizeFlag=t}(null===(e=this.prizeFlag)||void 0===e?void 0:e.includes(-1))?(this.prizeFlag=[],this.step=0):this.step=2}},o.prototype.run=function(t){var e,i,n=this;void 0===t&&(t=0);var r=this,o=r.rAF,a=r.step,s=r.prizeFlag,c=r._defaultConfig,u=r.cellAndSpacing,h=r.slots,l=c.accelerationTime,f=c.decelerationTime;if(0!==this.step||(null==s?void 0:s.length)!==h.length){if(void 0===s||s.length){3!==this.step||this.endScroll.length||this.carveOnGunwaleOfAMovingBoat();var d=Date.now()-this.startTime,p=Date.now()-this.endTime;h.forEach((function(e,i){var r=e.order;if(r&&r.length){var o=u*r.length,g=Math.abs(e.speed||c.speed),v=e.direction||c.direction,m=0,y=n.scroll[i];if(1===a||d<l){n.FPS=d/t;var w=Vi(d,0,g,l);w===g&&(n.step=2),m=(y+w*v)%o}else if(2===a)m=(y+g*v)%o,(null==s?void 0:s.length)===h.length&&(n.step=3,n.stopScroll=[],n.endScroll=[]);else if(3===a&&p){var b=n.stopScroll[i],S=n.endScroll[i];m=Ji(p,b,S,f),p>=f&&(n.step=0)}n.scroll[i]=m}})),this.draw(),o(this.run.bind(this,t+1))}}else{for(var g=s[0],v=0;v<h.length;v++){var m=h[v],y=s[v];if(!(null===(e=m.order)||void 0===e?void 0:e.includes(y))||g!==y){g=-1;break}}null===(i=this.endCallback)||void 0===i||i.call(this,this.prizes.find((function(t,e){return e===g}))||void 0)}},o.prototype.displacement=function(t,e){return"horizontal"===this._defaultConfig.mode?e:t},o.prototype.displacementWidthOrHeight=function(){var t=this._defaultConfig.mode,e=this.slots.length,i=this._defaultConfig,n=i.colSpacing,r=i.rowSpacing,o=this.prizeArea||this.drawBlocks();o.x,o.y;var a,s,c=o.w,u=o.h,h=0,l=0;return"horizontal"===t?(l=this.cellHeight=(u-r*(e-1))/e,h=this.cellWidth=l):(h=this.cellWidth=(c-n*(e-1))/e,l=this.cellHeight=h),a=this.widthAndSpacing=this.cellWidth+n,s=this.heightAndSpacing=this.cellHeight+r,this.cellAndSpacing="horizontal"===t?a:s,{cellWidth:h,cellHeight:l,widthAndSpacing:a,heightAndSpacing:s}},o}(Ki);export{Zi as LuckyGrid,Qi as LuckyWheel,tn as SlotMachine};
//# sourceMappingURL=index.esm.js.map
