{"name": "lucky-canvas", "version": "1.7.26", "description": "一个基于原生 js 的（大转盘 / 九宫格 / 老虎机）抽奖插件", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "unpkg": "dist/index.umd.js", "jsdelivr": "dist/index.umd.js", "types": "types/index.d.ts", "scripts": {"dev": "rollup --config rollup.config.dev.js -w", "build": "rollup --config rollup.config.build.js"}, "homepage": "https://100px.net", "bugs": "https://github.com/LuckDraw/lucky-canvas/issues", "repository": {"type": "git", "url": "git+https://github.com/LuckDraw/lucky-canvas.git", "directory": "packages/lucky-canvas"}, "author": "ldq <<EMAIL>>", "license": "Apache-2.0", "files": ["dist", "types", "index.js"], "keywords": ["大转盘抽奖", "九宫格抽奖", "老虎机抽奖", "抽奖插件", "js抽奖", "移动端抽奖", "canvas抽奖"], "devDependencies": {"@babel/core": "^7.12.3", "@babel/preset-env": "^7.12.1", "@babel/plugin-transform-runtime": "^7.16.4", "@babel/runtime": "^7.16.3", "core-js": "^3.19.2", "@rollup/plugin-commonjs": "^16.0.0", "@rollup/plugin-eslint": "^8.0.1", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^10.0.0", "@rollup/plugin-typescript": "^6.1.0", "@typescript-eslint/parser": "^4.14.0", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.1", "eslint": "^7.18.0", "eslint-plugin-prettier": "^3.3.1", "prettier": "^2.2.1", "rollup": "^2.33.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-livereload": "^2.0.0", "rollup-plugin-serve": "^1.1.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-delete": "^2.0.0", "rollup-plugin-dts": "^3.0.2", "rollup-plugin-typescript2": "^0.30.0", "tslib": "^2.3.1", "typescript": "^4.0.5"}, "dependencies": {}}