<template>
    <view>
        <view v-if="started" class="top-msg-container clearfix clear">
            <view class="top-msg top-msg-left fl">
                <view class="top-msg-icon">
                    <view class="top-msg-inner-ring">
                        <text class="iconfont icon-alarm-clock"></text>
                    </view>
                </view>
                <view class="top-msg-num">{{ countdownTime }}</view>
            </view>

            <view class="top-msg top-msg-right fr">
                <view class="top-msg-icon">
                    <view class="top-msg-inner-ring">

                        <image mode="aspectFit" :src="grainImg"/>
                    </view>
                </view>
                <view class="top-msg-num">
                    {{ count }}
                    <template v-if="!per_integral">/{{ targetCount }}</template>
                </view>
            </view>
        </view>


        <slz v-if="initComplete" ref="game" :seconds="seconds" :food-percent="foodPercent"
             :zd-img="boomImg" :jl-img="grainImg" :begin-img="bgImg" :lz-img="peopleImg"
             :speed="speed" :init-speed="initSpeed" :max-speed="maxSpeed" :aver-speed="averSpeed"
             :seconds-drop="secondsDrop"
             @timeUpdate="timeUpdate" @countUpdate="countUpdate" @gameOver="gameOver"/>

        <uni-popup ref="gameTips" :is-mask-click="false">
            <view class="game-tips">
                <view class="game-tips-title">游戏说明</view>
                <view class="game-tips-content color-content">
                    <view>
                        <text>按住</text>

                        <image mode="heightFix" :src="peopleImg" style="padding: 0 2px;"/>
                        <text>左右移动接</text>

                        <image mode="heightFix" :src="grainImg"/>
                        <text>。</text>
                    </view>

                    <view>
                        <text>接到</text>

                        <image mode="heightFix" :src="boomImg"/>
                        <text>，</text>

                        <image mode="heightFix" :src="grainImg"/>
                        <text>减1，时间减少1秒，</text>

                        <image mode="heightFix" :src="grainImg"/>
                        <text>掉落速度减缓。</text>
                    </view>
                    <view>
                        倒计时{{ seconds }}秒结束后，
                        <template>
                            <template v-if="per_integral">
                                <text>每接到1个</text>

                                <image mode="heightFix" :src="grainImg"/>
                                <text>，奖励1积分。</text>
                            </template>
                            <template v-else>
                                <text>收集到的</text>

                                <image mode="heightFix" :src="grainImg"/>
                                <text>不少于{{ targetCount }}，即可获得奖励。</text>
                            </template>
                        </template>
                    </view>

                    <view v-if="gameRules">
                        <text space="nbsp">{{ gameRules }}</text>
                    </view>
                </view>

                <view class="p10">
                    <view class="start-game-button" @click="gameStart">开始游戏</view>
                </view>
            </view>

            <view v-if="showAD" class="flex-all-center pt5">
                <xwy-ad :ad_type="3"></xwy-ad>
                <xwy-ad :ad_type="66"></xwy-ad>
            </view>
        </uni-popup>

        <game-result-popup ref="resultPopup" :show-ad="showAD"/>
    </view>
</template>

<script>
import slz from './slz.vue'

export default {
    components: {slz},
    data() {
        return {
            initComplete: false,
            seconds: 60,
            countdownTime: 60,
            count: 0,
            targetCount: 20,
            started: false,
            per_integral: 0,
            grainImg: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/grain/icon/lsb.png',
            boomImg: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/grain/icon/zd.png',
            peopleImg: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/grain/icon/ylc.png',
            bgImg: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/grain/icon/bg.jpg',
            foodPercent: 50,
            gameRules: '',
            showAD: false,
            speed: 10,
            initSpeed: 10,
            maxSpeed: 5,
            averSpeed: 2,
            secondsDrop: 1
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.point_id = Number(params.point_id)
        this.per_integral = Number(params.per_integral)
        if (params.seconds) {
            const seconds = Math.floor(params.seconds)
            this.seconds = seconds
            this.countdownTime = seconds
        }
        if (params.target_grain) this.targetCount = Math.floor(params.target_grain)
        if (params.title) this.$uni.setNavigationBarTitle(params.title)
        
        if (params.show_ad) this.showAD = true

        this.getJsonSet()
        
        this.initComplete = true
        
        this.$nextTick(() => this.$refs.gameTips.open())
    },
    
    onUnload() {
        this.$refs.game?.exit()
    },

    methods: {
        getJsonSet() {
            this.getOpenerEventChannel().once('jsonSet', jsonSet => {
                if (jsonSet) {
                    const {bg_img, boom_img, people, food, food_percent, navigation_bar, game_rules, speed, init_speed, max_speed, aver_speed, seconds_drop} = jsonSet
                    if (bg_img) this.bgImg = bg_img
                    if (boom_img) this.boomImg = boom_img
                    if (people) this.peopleImg = people
                    if (food) this.grainImg = food
                    if (food_percent) this.foodPercent = food_percent
                    if (game_rules) this.gameRules = game_rules
                    if (speed) this.speed = speed
                    if (init_speed) this.initSpeed = init_speed
                    if (max_speed) this.maxSpeed = max_speed
                    if (aver_speed) this.averSpeed = aver_speed
                    if (seconds_drop) this.secondsDrop = seconds_drop

                    if (navigation_bar) {
                        const {font_color = '#000000', bg_color = '#000000'} = navigation_bar
                        uni.setNavigationBarColor({
                            frontColor: font_color,
                            backgroundColor: bg_color
                        })
                    }
                }
            })
        },

        timeUpdate(seconds) {
            this.countdownTime = seconds
        },

        countUpdate(count) {
            this.count = count
        },

        gameStart() {
            this.started = true
            this.$refs.gameTips.close()
            this.$refs.game.start()
        },

        gameOver() {
            this.started = false

            const sign = {
                types: 12,
                point_id: this.point_id
            }

            if (this.per_integral) {
                sign.result = this.count > 0 ? 'success' : 'fail'
                sign.count = this.count
            } else {
                sign.result = this.count >= this.targetCount ? 'success' : 'fail'
            }

            this.submitResult(sign)
        },

        async submitResult(sign) {
            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify(sign))
                }
            })
            uni.hideLoading()


            let resultCode = sign.result === 'success' ? 1 : 0
            let info = resultCode === 1 ? '任务完成' : '任务失败'
            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info = res.info
            }

            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })


            this.getOpenerEventChannel().emit('success')
        }
    }
}
</script>

<style lang="scss" scoped>
$popup-bgi: "https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/task_challenge/style1/sty1.png";
$popup-title-bgi: "https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/task_challenge/style1/sty2.png";

.game-tips {
    width: 90vw;
    position: relative;
    background-image: url(#{$popup-bgi});
    background-size: 100% 100%;
    background-repeat: no-repeat;

    .game-tips-title {
        position: relative;
        top: -22px;
        left: 50%;
        margin-left: -90px;
        width: 180px;
        height: 44px;
        line-height: 44px;
        text-align: center;
        color: #fff;
        background-image: url(#{$popup-title-bgi});
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }

    .game-tips-content {
        padding: 0 20px;
        line-height: 30px;

        image {
            height: 30px;
            vertical-align: text-bottom;
        }
    }

    .start-game-button {
        width: 150px;
        line-height: 40px;
        border-radius: 20px;
        text-align: center;
        font-size: 18px;
        background-color: #ff985e;
        color: #fff;
        margin: 10px auto;
    }
}

.top-msg-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    padding: 15px 3vw 10px calc(3vw + 15px);
    box-sizing: border-box;
    z-index: 9;

    .top-msg {
        position: relative;
        display: flex;
        flex-direction: row;

        .top-msg-icon {
            position: absolute;
            left: -15px;
            top: -3px;
            border-radius: 50%;
            padding: 3px;

            .top-msg-inner-ring {
                width: 30px;
                height: 30px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 50%;

                .iconfont {
                    color: #fff;
                    font-size: 22px;
                }

                image {
                    width: 22px;
                    height: 22px;
                    display: block;
                }
            }
        }

        .top-msg-num {
            background-color: rgba(0, 0, 0, .3);
            color: #fff;
            height: 30px;
            border-radius: 0 15px 15px 0;
            padding: 0 15px 0 26px;
            min-width: 60px;
            font-size: 18px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .top-msg-left {
        .top-msg-icon {
            background-color: #5cadff;
            box-shadow: 0 0 5px #2d8cf0;

            .top-msg-inner-ring {
                background-color: #2d8cf0;
            }
        }

        .top-msg-num {
            box-shadow: 0 0 10px rgba(45, 140, 240, .2) inset;
        }
    }

    .top-msg-right {
        .top-msg-icon {
            background-color: #fce230;
            box-shadow: 0 0 5px #ffb400;

            .top-msg-inner-ring {
                background-color: #ffb400;
            }
        }

        .top-msg-num {
            box-shadow: 0 0 10px rgba(255, 180, 0, .2) inset;
        }
    }
}
</style>