<template>
	<view class='content relative'>

		<view v-if="activeIndex != 1" class="relative width100 height100">
			<image class="bgImg" :src="beginImg" mode="widthFix"></image>
		</view>

		<view class="width100 height100 f" :class="activeIndex == 1 ? '' : 'fixed-box'">
			<view class="img-box f-d-c"
				:style="{'width': IMG.w ? `${IMG.w}px` : '100%', 'height': IMG.h ? `${IMG.h * 2}px` : '100%', 'top': IMG.y ? `${IMG.y}px` : 0}">
				<image class="img" :style="{'margin-top': index == 1 ? '-1px' : ''}" v-for="(item,index) in bg_list"
					:key="index" :src="beginImg" mode="widthFix">
				</image>
			</view>
			


			<view style="width: 15%"></view>
            


			<view class="height100 width70 way relatvie" style="z-index: 1;">
				<view v-if="wp_list.length" class="width100 height100 relative">
					<view class="wp colorfff" :style="{'top': `${item.y}px`, 'left': `${item.x}px`}"
						v-for="(item,index) in wp_list" :key="index">
						<image :src="item.img" mode="widthFix"></image>
					</view>
				</view>

				<!-- 龙舟 -->
				<view class="lz" :style="{'left': `${lz.x}px`, 'top': `${lz.y}px`}" @touchmove="move">
					<image :src="lzImg" mode="aspectFill"></image>
				</view>
			</view>



			<view style="width: 15%"></view>

		</view>
	</view>
</template>

<script>
	export default {
        emits: ['timeUpdate', 'countUpdate', 'gameOver'],
        props: {
            seconds: {
                type: Number,
                default: 60
            },
            zdImg: {
                type: String,
                default: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/grain/icon/zd.png'
            },
            jlImg: {
                type: String,
                default: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/grain/icon/lsb.png'
            },
            beginImg: {
                type: String,
                default: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/grain/icon/bg.jpg'
            },
            lzImg: {
                type: String,
                default: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/grain/icon/ylc.png'
            },
            foodPercent: {
                type: Number,
                default: 50
            },

            // 开始速度 数字越小 速度越快
            speed: {
                type: Number,
                default: 10
            },
            // 撞到炸弹后收到的速度惩罚 speed回到initSpeed
            initSpeed: {
                type: Number,
                default: 10
            },
            // 速度上限
            maxSpeed: {
                type: Number,
                default: 5
            },
            // 平均多少S加1速度
            averSpeed: {
                type: Number,
                default: 2
            },
            // 每一秒掉落多少个粮食/炸弹
            secondsDrop: {
                type: Number,
                default: 1
            }
        },
        
		data() {
			return {
				params: {},
				activeIndex: -1, // -1 未开始 1游戏中 2已结束

				time: this.seconds, // 游戏时间
				jlCount: 0, // 获得的粮食个数

				bg: {}, // 龙舟行驶道路
				lz: {}, // 龙舟
				IMG: {}, // 背景图 宽高

				wp_list: [], // 掉下来的物品

				bg_list: [], // 游戏中 背景图片 需要两张相同的图片

                gameSpeed: 10
			}
		},
        
        watch: {
            time(val) {
                this.$emit('timeUpdate', val)
            },
            jlCount(val) {
                this.$emit('countUpdate', val)
            }
        },
        
        
        mounted() {
			this.init()
		},
		
		
		onReady() {
			this.$nextTick(() => this.get_wh())
		},

		methods: {
			async init() {
                this.gameSpeed = this.speed
				this.bg_list = [this.beginImg, this.beginImg]
				this.get_bgImg_params()
			},

			get_bgImg_params() {
				uni.getImageInfo({
					src: this.beginImg,
					success: res => {
						const w = uni.getWindowInfo().windowWidth
						const rd = w / res.width;
						const obj = {
							w,
							h: parseInt(res.height * rd),
						}
						obj.y = -obj.h
						this.IMG = obj
					},
					fail: res => console.log('fail', res),
				})
			},


			game_start() {
				this.$nextTick(() => this.start());
			},

			get_wh() {
				const qy = uni.createSelectorQuery().in(this);
				qy.selectAll(`.way, .lz`).boundingClientRect(e => {
					const bg = e[0],
						lz = e[1];

					this.bg = {
						w: bg.width,
						h: bg.height,
						x: bg.left,
						y: bg.top,
					}
					this.lz = {
						w: lz.width,
						h: lz.height,
						x: bg.left + bg.width / 2 - lz.width / 2,
						y: bg.height * .9 - lz.height,
					}
				}).exec()
			},


			move(e) {
				// this.lz.x = e.detail.x
				const opt = e.touches[0]
				const x = opt.clientX;

				const bgX = this.bg.x,
					bgW = this.bg.w,
					lzW = this.lz.w;

				if (x - lzW / 2 < bgX) {
					this.lz.x = bgX
					return
				}
				if (x + lzW / 2 > bgX + bgW) {
					this.lz.x = bgX + bgW - lzW
					return
				}

				this.lz.x = x - lzW / 2
			},


			start() {
				this.activeIndex = 1
				this.get_wp_list()
				this.bg_move()
				this.wp_move()
				this.judgeWpTimer = setInterval(() => this.judge_wp(), 100)
				this.getTimeTimer = setInterval(() => this.get_time(), 1000)
			},

			

			bg_move() {
				if (this.activeIndex != 1) return
				this.IMG.y += 2
				if (this.IMG.y >= 0) this.IMG.y = -this.IMG.h + 3
				setTimeout(() => this.bg_move(), this.gameSpeed * 2)
			},

			get_time() {
				if (this.time === 0) {
					this.activeIndex = 2
					this.stop()
					return
				}
				this.time--;
				if (this.time % this.averSpeed === 0 && this.gameSpeed > this.maxSpeed) this.gameSpeed--
			},

			get_wp_list() {
				if (this.activeIndex != 1 || this.time < 2) return
				// const spd = Math.ceil(Math.random() * 2) * 1000 * this.gameSpeed / this.initSpeed;
                let spd = Math.ceil(1000 / this.secondsDrop)
                if (spd < 300) spd = 300
                this.get_wp()
				setTimeout(() => this.get_wp_list(), spd)
			},


            /**
             * 根据设定的食物百分比，随机返回一个物品类型。
             *
             * 如果 `foodPercent` 为 0，则直接返回炸弹类型 ('zd')。
             * 如果 `foodPercent` 为 100，则直接返回粮食类型 ('jl')。
             * 否则，根据随机生成的数值和设定的百分比，决定返回炸弹 ('zd') 还是粮食 ('jl')。
             *
             * @returns {string} 'zd' 表示炸弹，'jl' 表示粮食
             */
            BoomOrFood() {
                const percent = this.foodPercent

                if (percent === 0) return 'zd'
                if (percent === 100) return 'jl'

                const random = Math.random() * 100

                // 如果随机数大于等于设定的百分比，返回炸弹类型，否则返回粮食类型，比如设置80 那么随机数大于80则是炸弹，小于等于80则是粮食
                return random > percent ? 'zd' : 'jl'
            },

			get_wp() {
				const bgW = this.bg.w,
					bgH = this.bg.h;

				const x = Math.floor(Math.random() * (bgW - this.lz.w))

                const type = this.BoomOrFood()

				const obj = {
					type,
					x,
					y: 0,
				}

				switch (type) {
					case 'zd':
						obj.img = this.zdImg
						obj.color = `#F56C6C`
						break;
					case 'jl':
						obj.img = this.jlImg
						obj.color = `#67C23A`
						break;
				}

				this.wp_list.push(obj)
			},

			wp_move() {
				if (this.activeIndex != 1) return
				this.wp_list.forEach((val, idx) => val.y += 2)
				setTimeout(() => this.wp_move(), this.gameSpeed)
			},

			judge_wp() {
				const list = this.xwyLib.copyAry(this.wp_list)
				if (!this.xwyLib.isArray(list)) return

				const lz_y_min = this.lz.y,
					lz_y_max = this.lz.y + this.lz.h; // Y轴会被龙舟获取的区域
				const lz_x_min = this.lz.x,
					lz_x_max = this.lz.x + this.lz.w; // X轴会被龙舟获取的区域

				const idx = list.findIndex(val => {
					let y = val.y + this.lz.w
					// 先判断有没有进入龙舟Y轴范围
					if (y > lz_y_min && y - this.lz.w < lz_y_max) {
						// 然后判断物品和龙舟是否X轴相交
						const wp_min = val.x + this.bg.x,
							wp_max = val.x + this.lz.w + this.bg.x;
						if ((wp_min <= lz_x_min && wp_max >= lz_x_min) || (wp_max >=
								lz_x_max && wp_min <=
								lz_x_max)) {
							return true
						}
					}
				})

				if (idx >= 0) { // 判断获取到了粮食还是炸弹
					const itm = list[idx];
					switch (itm.type) {
						case 'zd':
							this.get_zd()
							break;
						case 'jl':
							this.get_jl()
							break;
					}
					list.splice(idx, 1)
				}
				
				const index = list.findIndex(val => val.y > this.bg.h)
				if (index >= 0) list.splice(index, 1)
				this.wp_list = list;
			},


			// 捡到炸弹
			get_zd() {
                uni.vibrateLong?.() || uni.vibrateShort?.()
                
				this.gameSpeed = this.initSpeed;
				this.time -= 2
                
                if (this.jlCount > 0) this.jlCount -= 1
                // this.jlCount = 0
                
				if (this.time <= 0) this.time = 0
			},

			// 捡到粮食
			get_jl() {
                uni.vibrateShort?.() || uni.vibrateLong?.()
				this.jlCount++
			},

			stop() {
                this.getTimeTimer && clearInterval(this.getTimeTimer)
                this.judgeWpTimer && clearInterval(this.judgeWpTimer)
                this.$emit('gameOver')
			},

            exit() {
                this.getTimeTimer && clearInterval(this.getTimeTimer)
                this.judgeWpTimer && clearInterval(this.judgeWpTimer)
            }
		},
	}
</script>

<style scoped lang='scss'>
@import "./style.scss";

	.content {
		width: 100%;
		height: 100vh;
		background-color: #f8f8f8;
		overflow: hidden;
	}

	.bgImg {
		position: absolute;
		z-index: 0;
	}

	.way {
		overflow: hidden;
	}

	.lz {
		position: absolute;
		width: 13vw;
		max-width: calc(640px * .13);
		height: 26vw;
		max-height: calc(640px * .26);
	}

	.wp {
		position: absolute;
		width: 13vw;
		height: 13vw;
		max-width: calc(640px * .13);
		max-height: calc(640px * .13);
	}

	.fixed-box {
		z-index: -1;
		opacity: 0;
	}

	
	.img-box {
		position: absolute;
		left: 0;
		z-index: 0;
	}
</style>