.relative{
    position: relative;
}

.width100 {
    width: 100%;
}

.height100 {
    height: 100%;
}

.f {
    display: flex;
}

.f-d-c {
    display: flex;
    flex-direction: column;
}

.f-d-c-j-a-c {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.f-j-sb-a-e {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}

.org_bgcolor {
    background-color: #FAAA4F !important;
    color: #fff !important;
}

.radius_3 {
    border-radius: .3rem;
}

.radius_5 {
    border-radius: .5rem;
}

.p_7 {
    padding: .7rem !important;
}

.p7_0 {
    padding: .7rem 0 !important;
}

.p0_7 {
    padding: 0 .7rem !important;
}

.width30 {
    width: 30%;
}

.width35 {
    width: 35%;
}

.width50 {
    width: 50% !important;
}

.width60 {
    width: 60%;
}

.width70 {
    width: 70%;
}

.text-center {
    text-align: center;
}

.bgfff{
    background: #fff;
}

.mb2 {
    margin-bottom: 2rem !important;
}

.p1 {
    padding: 1rem !important;
}

.p5_0 {
    padding: .5rem 0 !important;
}

.pt_7 {
    padding-top: .7rem !important;
}

.pl_2 {
    padding-left: .2rem !important;
}

.font1 {
    font-size: 1rem !important;
}

.font1_1 {
    font-size: 1.1rem !important;
}

.font_9 {
    font-size: .9rem !important;
}

.color555 {
    color: #555 !important;
}

.colorfff {
    color: #fff !important;
}

.wht_shadow_01 {
    box-shadow: 0 0 3px 1px rgba(255, 255, 255, 0.1);
}

image {
    width: 100%;
    height: 100%;
}