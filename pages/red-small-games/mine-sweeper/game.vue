<template>
    <view class="page" :style="pageStyle">
        <view class="top-msg-container clearfix clear">
            <view v-if="needTime" class="top-msg top-msg-left fl">
                <view class="top-msg-icon">
                    <view class="top-msg-inner-ring">
                        <text class="iconfont icon-time"></text>
                    </view>
                </view>
                <view class="top-msg-num">{{ time }}</view>
            </view>

            <view class="top-msg top-msg-right fr">
                <view class="top-msg-icon">
                    <view class="top-msg-inner-ring">
                        <text class="iconfont icon-bomb"></text>
                    </view>
                </view>
                <view class="top-msg-num">
                    <text class="font14 pr5">剩余:</text>
                    {{ boomLeftNum }}
                </view>
            </view>
        </view>

        <view class="mine-sweeper-container"
              :style="{height: `calc(${showAD ? '80vh' : '100vh'} - 150px)`}">
            <!--组件渲染格子的时候横竖坐标是反的，所以这里width和height要传相反的-->
            <mine-sweeper ref="mineSweeper" style="width: 100%; height: 100%" :boom-num="boomNum"
                          :width="heightCount" :height="widthCount" @blockTap="gameStart"
                          @init="gameInit" @result="gameResult" @boomNumUpdate="boomNumUpdate"/>
        </view>
        
        <template v-if="isInitSuccess">
            <view v-if="isGameStart" class="game-button" @click="back">返回
            </view>
            <view v-else class="game-button game-start-button" @click="gameStart">开始</view>
        </template>

        <template v-if="showAD">
            <xwy-ad :ad_type="3"/>
            <xwy-ad :ad_type="66"/>
        </template>


        <game-result-popup ref="resultPopup" :show-ad="showAD"/>
    </view>
</template>

<script>
import mineSweeper from '../components/mine-sweeper.vue'

export default {
    components: {mineSweeper},
    data() {
        return {
            /*mapSize: [
                [8, 8], [16, 16], [16, 30]
            ],
            boomNum: [10, 40, 99],
            levelChoose: 0,*/
            widthCount: 8,
            heightCount: 8,
            boomNum: 10,
            boomLeftNum: 10,
            isInitSuccess: false,
            isGameStart: false,
            needTime: false,
            time: null,
            resultCode: null,
            rewardIntegral: null,
            resultInfo: null,
            showAD: false,
            bg_img: ''
        }
    },

    computed: {
        pageStyle() {
            if (!this.bg_img) return ''
            return `background-image: url(${this.bg_img}); background-position: center; background-size: cover; background-repeat: no-repeat;`
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.point_id = Number(params.point_id)
        if (params.row) this.widthCount = Math.floor(params.row)
        if (params.col) this.heightCount = Math.floor(params.col)
        if (params['boom_num']) {
            const boomNum = Math.floor(params['boom_num'])
            this.boomNum = boomNum
            this.boomLeftNum = boomNum
        }
        if (params.seconds) {
            this.needTime = true
            this.time = Math.floor(params.seconds)
        }
        if (params.show_ad) this.showAD = true
        if (params.title) this.$uni.setNavigationBarTitle(params.title)
        if (params.bg_img) this.bg_img = params.bg_img
    },
    
    onUnload() {
        this.clearCountdownInterval()
    },

    methods: {
        async back() {
            if (!this.isGameStart) return this.$uni.navigateBack()
            
            this.clearCountdownInterval()
            
            const res = await this.$uni.showModal('游戏已开始，确定返回吗？', {
                showCancel: true,
                cancelText: '继续游戏',
                cancelColor: '#576B95',
                confirmColor: '#80848f'
            })
            
            if (res.confirm) return this.$uni.navigateBack()
            
            this.countdown()
        },
        
        gameStart() {
            if (this.isGameStart) return

            this.isGameStart = true
            this.$refs.mineSweeper.gameStart()
            
            this.countdown()
        },

        countdown() {
            if (!this.needTime) return
            
           this.countdownInterval = setInterval(() => {
              this.time--
              if (this.time === 0) this.timeOut()
           }, 1000)
        },

        timeOut() {
            this.gameResult({code: 0, msg: 'timeout'})
        },
        
        clearCountdownInterval() {
            this.countdownInterval && clearInterval(this.countdownInterval)
            this.countdownInterval = null
        },
        
        gameInit() {
            this.isInitSuccess = true
        },
        gameResult(e) {
            this.clearCountdownInterval()
            this.submitResult(e.code)
        },
        boomNumUpdate(e) {
            this.boomLeftNum = e
        },
        
        async submitResult(code) {
            if (this.inSubmit) return
            this.inSubmit = true
            
            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify({
                        types: 10,
                        point_id: this.point_id,
                        result: code === 1 ? 'success' : 'fail'
                    }))
                }
            })
            uni.hideLoading()

            
            let resultCode = code
            let info = resultCode === 1 ? '任务完成' : '任务失败'
            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info = res.info
            }

            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })
            this.inSubmit = false

            this.getOpenerEventChannel().emit('success')
        }
    }
}
</script>

<style lang="scss">
.page {
    width: 100%;
    height: 100vh;
    box-sizing: border-box;
    background-image: url(https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/red-small-games/mine-sweeper/bg.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-attachment: fixed;
}

.top-msg-container {
    padding: 15px 3vw 10px calc(3vw + 15px);

    .top-msg {
        position: relative;
        display: flex;
        flex-direction: row;

        .top-msg-icon {
            position: absolute;
            left: -15px;
            top: -3px;
            border-radius: 50%;
            padding: 3px;

            .top-msg-inner-ring {
                width: 30px;
                height: 30px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 50%;

                .iconfont {
                    color: #fff;
                    font-size: 22px;
                }
            }
        }

        .top-msg-num {
            background-color: rgba(0, 0, 0, .3);
            color: #fff;
            height: 30px;
            border-radius: 0 15px 15px 0;
            padding: 0 15px 0 26px;
            min-width: 60px;
            font-size: 18px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .top-msg-left {
        .top-msg-icon {
            background-color: #5cadff;
            box-shadow: 0 0 5px #2d8cf0;

            .top-msg-inner-ring {
                background-color: #2d8cf0;
            }
        }

        .top-msg-num {
            box-shadow: 0 0 10px rgba(45, 140, 240, .2) inset;
        }
    }

    .top-msg-right {
        .top-msg-icon {
            background-color: #fce230;
            box-shadow: 0 0 5px #ffb400;

            .top-msg-inner-ring {
                background-color: #ffb400;
            }
        }

        .top-msg-num {
            box-shadow: 0 0 10px rgba(255, 180, 0, .2) inset;
        }
    }
}

.mine-sweeper-container {
    width: 100%;
}

.game-button, .back-button {
    width: 253px;
    height: 53px;
    line-height: 48px;
    font-size: 18px;
    color: #fff;
    text-align: center;
    background-image: url(https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/guessing_songs/anc.png);
    background-size: 100%;
    background-repeat: no-repeat;
    margin: 10px auto;
}

.game-start-button {
    animation: zoom .7s linear alternate infinite;
}

@keyframes zoom {
    from {
        transform: scale(1);
    }
    to {
        transform: scale(1.1);
    }
}
</style>