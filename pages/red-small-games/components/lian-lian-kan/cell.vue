<template>
    <view class="block" :class="classNames" :style="{width: blockSize + 'px', height: blockSize + 'px'}">
        <view v-if="isLine" :class="lineClass">
            <text v-if="showStar" class="iconfont icon-icon-star-filled"></text>
        </view>
        <image v-if="image" class="image" mode="scaleToFill" :src="image"
               :style="{width: blockSize + 'px', height: blockSize + 'px'}"/>
    </view>
</template>

<script>
export default {
    name: "cell",
    props: ['isSelected', 'isBlank', 'image', 'lineClass', 'isLine', 'blockSize', 'isShort'],
    computed: {
        classNames() {
            const classNames = []
            if (this.isSelected) classNames.push('selected') // 选中
            if (this.isBlank) classNames.push('blank') // 空白
            if (this.isShort) classNames.push('short') // 震动，两个相同的无法相连时
            return classNames
        },

        showStar() {
            return this.lineClass.includes('line-start') || this.lineClass.includes('line-end')
        }
    }
}
</script>

<style lang="scss">
.block {
    position: relative;
    padding: 1px;
}

.selected {
    width: 100%;
    height: 100%;
    border-radius: 4px;
    background: repeating-conic-gradient(from var(--range), #0ff, #f0f, #0ff, #ff0, #0f0);
    animation: rotating 2s linear infinite;
}

@property --range {
    initial-value: 0deg;
    syntax: '<angle>';
    inherits: false;
}

@keyframes rotating {
    0% {
        --range: 0deg;
    }
    100% {
        --range: 360deg;
    }
}

.short {
    animation: short .5s linear;
}

@keyframes short {
    0% {
        transform: rotate(10deg);
    }
    25% {
        transform: rotate(-5deg);
    }
    50% {
        transform: rotate(0);
    }
    75% {
        transform: rotate(5deg);
    }
    100% {
        transform: rotate(0);
    }
}

.line {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    
    //$color: red;
    $color: #ffc107;
    
    &:before, &:after {
        content: '';
        background-color: $color;
    }

    &:before, &:after, .iconfont {
        display: block;
        position: absolute;
    }

    .iconfont {
        color: $color;
    }
}

.line.line-l:before, .line.line-r:before {
    width: calc(50% + 4px);
    //height: 4px;
    height: 2px;
    top: 50%;
    right: 50%;
    //margin-top: -2px;
    margin-top: -1px;
}

.line.line-r:before {
    //right: -4px;
    right: -5px;
}

.line.line-l.line-r:before {
    width: calc(100% + 8px);
    left: -4px;
    right: -4px;
}

.line.line-t:after, .line.line-b:after {
    //width: 4px;
    width: 2px;
    height: calc(50% + 4px);
    left: 50%;
    bottom: 50%;
    //margin-left: -2px;
    margin-left: -1px;
}

.line.line-b:after {
    bottom: -4px;
}

.line.line-t.line-b:after {
    height: calc(100% + 8px);
    top: -4px;
    bottom: -4px;
}

.line.line-t.line-r:before, .line.line-b.line-r:before {
    width: calc(50% + 6px);
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px;
}

.line.line-t.line-l:before, .line.line-b.line-l:before {
    width: calc(50% + 6px);
    //margin-right: -2px;
    margin-right: -1px;
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
}

.line .iconfont {
    position: absolute;
}

.line.line-start.line-l,
.line.line-start.line-r,
.line.line-end.line-l,
.line.line-end.line-r {
    .iconfont {
        width: calc(20% + 4px);
        right: 50%;
    }
}

.line.line-start.line-r, .line.line-end.line-r {
    .iconfont {
        left: 50%;
    }
}

.line.line-start.line-l,
.line.line-start.line-r,
.line.line-end.line-l,
.line.line-end.line-r {
    .iconfont {
        right: 50%;
        top: 50%;
        margin-top: -8px;
        margin-right: -8px;
    }
}

.line.line-start.line-r, .line.line-end.line-r {
    .iconfont {
        margin-left: -8px;
        left: 50%;
    }
}


.line.line-start.line-t,
.line.line-start.line-b,
.line.line-end.line-t,
.line.line-end.line-b {
    .iconfont {
        height: calc(20% + 4px);
        bottom: 50%;
    }
}

.line.line-start.line-b, .line.line-end.line-b {
    .iconfont {
        top: 50%;
    }
}

.line.line-start.line-t,
.line.line-start.line-b,
.line.line-end.line-t,
.line.line-end.line-b {
    .iconfont {
        bottom: 50%;
        left: 50%;
        margin-left: -8px;
        margin-bottom: -8px;
    }
}

.line.line-start.line-b, .line.line-end.line-b {
    .iconfont {
        margin-top: -8px;
        top: 50%;
    }
}
</style>