<template>
    <view class="container">
        <view class="contentMap">
            <view class="placeInRow" v-for="(row,i) in mask" :key="i">
                <view class="content" v-for="(block,j) in row" :key="j">
                    <view v-if="block === 1" class="block" :style="sizeStyle.block">
                        <view v-if="maps[i][j] > 0" @tap="setMask(i, j, tapType)"
                              :class="{'color-light-primary': maps[i][j] === 1, 'color-green': maps[i][j] === 2, 'color-error': maps[i][j] >= 3}"
                              :style="sizeStyle.font">
                            {{ maps[i][j] }}
                        </view>
                        <view v-else-if="maps[i][j] === 0"></view>
                        <view v-else>
                            <image :style="sizeStyle.image"
                                   src="https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/red-small-games/mine-sweeper/boom.png"/>
                        </view>
                    </view>
                    <view v-else-if="block === 0" class="block mask" :style="sizeStyle.block"
                          @tap="setMask(i, j, tapType)"
                    ></view>
                    <view v-else-if="block === -1" class="block mask" :style="sizeStyle.block"
                          @tap="setMask(i, j, tapType)">
                        <image  :style="sizeStyle.image"
                                src="https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/red-small-games/mine-sweeper/flag.png"/>
                    </view>
                    <view v-else-if="block === 2" class="block mask" :style="sizeStyle.block">
                        <image  :style="sizeStyle.image"
                                src="https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/red-small-games/mine-sweeper/error.png"/>
                    </view>
                </view>
            </view>
        </view>

        <view class="open-flat-switch flex-center">
            <template v-if="isGameStart">
                <view class="tap-type-button" :class="{'tap-type-button-active': tapType === 'open'}"
                      @click="tapType = 'open'">翻开
                </view>
                <view class="tap-type-button" :class="{'tap-type-button-active': tapType === 'mask'}"
                      @click="tapType = 'mask'">插旗
                </view>
            </template>
        </view>
    </view>
</template>

<script>
/**
 * gwh 简单扫雷
 * @description 扫雷游戏简单Demo
 * @tutorial
 * @property {Number} width 扫雷地图宽
 * @property {Number} height 扫雷地图高
 * @property {Number} boomNum 雷个数
 * @property {Function} @init 地图初始化监听，返回游戏地图：-1表示雷，0-9表示周围有几个雷
 * @property {Function} @result 游戏结束监听, code=1成功，其余失败
 * */
export default {
    props: {
        width: {
            type: Number,
            default: 8
        },
        height: {
            type: Number,
            default: 8
        },
        boomNum: {
            type: Number,
            default: 10,
        }
    },
    watch: {
        width() {
            this.initMap()
        },
        height() {
            this.initMap()
        },
        boomNum() {
            this.initMap()
        }
    },
    data() {
        return {
            maps: [],
            mask: [],
            booms: [],
            isGameStart: false,
            isGameOver: false,
            isGameSuccess: false,
            lastAction: '',
            tapType: 'open',
            sizeStyle: {}
        }
    },
    
    mounted() {
        this.setBlockSize()
        this.initMap()
    },
    
    
    methods: {
        setBlockSize() {
            uni.createSelectorQuery().in(this).select(".container")
                .boundingClientRect(({width, height}) => {
                    const widthSize = Math.floor(width * .95 / this.height) - 2
                    const heightSize = Math.floor((height - 60) * .95 / this.width) - 2
                    const size = Math.min(widthSize, heightSize)

                    const imageSize = Math.floor(size * .7)
                    let fontSize = Math.floor(size * .8)
                    // if (fontSize > 34) fontSize = 34
                    this.sizeStyle = {
                        block: `width: ${size}px; height: ${size}px;`,
                        image: `width: ${imageSize}px; height: ${imageSize}px;`,
                        font: `font-size: ${fontSize}px;`
                    }
            }).exec()
        },
        
        gameStart() {
            this.isGameStart = true
        },
        
        getRestBoomNum() {
            let boomNum = this.boomNum
            try {
                let maskNum = 0, shownNum = 0;
                for (let i = 0; i < this.width; i++) {
                    for (let j = 0; j < this.height; j++) {
                        if (this.mask[i][j] === -1) maskNum++
                        if (this.mask[i][j] === 1) shownNum++
                    }
                }
                
                this.$nextTick(() => {
                    if (shownNum + this.booms.length === this.width * this.height && !this.isGameSuccess) {
                        this.isGameSuccess = true
                        for (let i = 0; i < this.width; i++) {
                            for (let j = 0; j < this.height; j++) {
                                if (this.mask[i][j] === 0 && this.maps[i][j] === -1) {
                                    this.$set( this.mask[i], j, -1)
                                }
                            }
                        }
                        this.$emit('result', {code: 1, msg: 'success'})
                    }
                })

                boomNum = this.booms.length - maskNum
            } catch (e) {}
            
            this.leftBoomNum = boomNum
            this.$emit('boomNumUpdate', boomNum)
        },
        
        
        initMap() {
            this.maps = []
            this.mask = []
            this.leftBoomNum = this.boomNum
            this.isGameOver = false
            this.isGameSuccess = false
            this.isGameStart = false
            this.booms = []
            for (let i = 0; i < this.width; i++) {
                this.maps.push([])
                this.mask.push([])
                for (let j = 0; j < this.height; j++) {
                    this.maps[i].push(0)
                    this.mask[i].push(0)
                }
            }
            const initBooms = []
            while (initBooms.length < this.boomNum) {
                const xy = [
                    Math.floor(Math.random() * this.width),
                    Math.floor(Math.random() * this.height)
                ]
                let hasSame = false
                for (let b = 0; b < initBooms.length; b++) {
                    if (initBooms[b][0] === xy[0] && initBooms[b][1] === xy[1]) {
                        hasSame = true
                        break
                    }
                }
                if (!hasSame) {
                    initBooms.push(xy)
                    this.maps[xy[0]][xy[1]] = -1
                }
            }
            this.booms = initBooms
            for (let i = 0; i < this.width; i++) {
                for (let j = 0; j < this.height; j++) {
                    if (this.maps[i][j] !== -1) {
                        let boomSum = 0
                        if (i > 0) {
                            if (j > 0 && this.maps[i - 1][j - 1] === -1) boomSum++
                            if (this.maps[i - 1][j] === -1) boomSum++
                            if (j < this.height - 1 && this.maps[i - 1][j + 1] === -1) boomSum++
                        }
                        if (i < this.width - 1) {
                            if (j > 0 && this.maps[i + 1][j - 1] === -1) boomSum++
                            if (this.maps[i + 1][j] === -1) boomSum++
                            if (j < this.height - 1 && this.maps[i + 1][j + 1] === -1) boomSum++
                        }
                        if (j > 0 && this.maps[i][j - 1] === -1) boomSum++
                        if (j < this.height - 1 && this.maps[i][j + 1] === -1) boomSum++
                        this.maps[i][j] = boomSum
                    }
                }
            }
            this.$emit('init', {maps: this.maps})
        },
        setMask(i, j, action) {
            // action 可以是 open 或 mask
            this.lastAction = action;
            if (this.isGameOver || this.isGameSuccess) return
            
            this.$emit('blockTap')
            
            if (action === 'open') {
                if (this.maps[i][j] === -1) {
                    for (let b = 0; b < this.booms.length; b++) {
                        const theBoomXY = this.booms[b]
                        if (this.mask[theBoomXY[0]][theBoomXY[1]] !== -1) {
                            this.$set(this.mask[theBoomXY[0]], [theBoomXY[1]], 1)
                        }
                    }
                    this.isGameOver = true
                    for (let i = 0; i < this.width; i++) {
                        for (let j = 0; j < this.height; j++) {
                            if (this.mask[i][j] === -1 && this.maps[i][j] !== -1) {
                                this.$set(this.mask[i], j, 2)
                            }
                        }
                    }
                    
                    this.$emit('result', {code: 9, msg: 'failed'})
                } else {
                    this.canIOpen(i, j)
                }
            } else {
                if (this.mask[i][j] === 0) {
                    if (this.leftBoomNum !== 0) this.$set(this.mask[i], [j], -1)
                } else if (this.mask[i][j] === -1) {
                    this.$set(this.mask[i], [j], 0)
                }
            }
            
            this.getRestBoomNum()
        },
        
        
        canIOpen(i, j, level = 0) {
            if (this.lastAction !== 'open') return
            
            if (this.maps[i][j] === -1) {
                if (level <= 1) this.setMask(i, j, 'open')
                return
            }
            
            this.mask[i][j] = 1
            let boomSum = 0
            if (i > 0) {
                if (j > 0 && this.mask[i - 1][j - 1] === -1) boomSum++
                if (this.mask[i - 1][j] === -1) boomSum++
                if (j < this.height - 1 && this.mask[i - 1][j + 1] === -1) boomSum++
            }
            if (i < this.width - 1) {
                if (j > 0 && this.mask[i + 1][j - 1] === -1) boomSum++
                if (this.mask[i + 1][j] === -1) boomSum++
                if (j < this.height - 1 && this.mask[i + 1][j + 1] === -1) boomSum++
            }
            if (j > 0 && this.mask[i][j - 1] === -1) boomSum++
            if (j < this.height - 1 && this.mask[i][j + 1] === -1) boomSum++
            // console.log(boomSum)
            if (this.maps[i][j] <= boomSum && this.maps[i][j] !== -1) {
                if (i > 0) {
                    if (j > 0 && this.mask[i - 1][j - 1] === 0) this.canIOpen(i - 1, j - 1, level + 1)
                    if (this.mask[i - 1][j] === 0) this.canIOpen(i - 1, j, level + 1)
                    if (j < this.height - 1 && this.mask[i - 1][j + 1] === 0) this.canIOpen(i - 1, j + 1, level + 1)
                }
                if (i < this.width - 1) {
                    if (j > 0 && this.mask[i + 1][j - 1] === 0) this.canIOpen(i + 1, j - 1, level + 1)
                    if (this.mask[i + 1][j] === 0) this.canIOpen(i + 1, j, level + 1)
                    if (j < this.height - 1 && this.mask[i + 1][j + 1] === 0) this.canIOpen(i + 1, j + 1, level + 1)
                }
                if (j > 0 && this.mask[i][j - 1] === 0) this.canIOpen(i, j - 1, level + 1)
                if (j < this.height - 1 && this.mask[i][j + 1] === 0) this.canIOpen(i, j + 1, level + 1)
            }

            this.$set(this.mask, i, this.mask[i])
        }
    }
}
</script>

<style lang="scss" scoped>
.container {
    width: 100%;
    height: 100%;
}

.contentMap {
    width: 100%;
    overflow: hidden;
}

.placeInRow {
    display: flex;
    flex-direction: row;
    justify-content: center;
}

.block {
    background-color: #d5d5d5;
    border: #f8f8f8 solid 1px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.mask {
    /*background-color: #e6e6e6;
    box-shadow: 2px 2px 5px 5px #bcbcbc inset;*/
    border: #f8f8f8 solid 1px;
    background-image: url(https://pic.52112.com/180327/180327_80/iNjyNmqBcX_small.jpg);
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.open-flat-switch {
    height: 60px;

    .tap-type-button {
        width: 100px;
        line-height: 40px;
        border-radius: 5px;
        text-align: center;
        margin: 10px;
        background-color: #f8f8f8;
        color: #666;
    }

    .tap-type-button-active {
        background-color: #5cadff;
        color: #fff;
    }
}
</style>
