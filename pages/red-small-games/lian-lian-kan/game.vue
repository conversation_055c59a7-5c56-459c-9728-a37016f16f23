<template>
    <view class="page" :style="pageStyle">
        <view class="top-msg-container clearfix clear">
            <view v-if="needTime" class="top-msg top-msg-left fl">
                <view class="top-msg-icon">
                    <view class="top-msg-inner-ring">
                        <text class="iconfont icon-alarm-clock"></text>
                    </view>
                </view>
                <view v-if="time !== null" class="top-msg-num">{{ timeText }}</view>
            </view>

            <view class="top-msg top-msg-right fr">
                <view class="top-msg-icon">
                    <view class="top-msg-inner-ring">
                        <text class="iconfont icon-grid"></text>
                    </view>
                </view>
                <view v-if="leftCount !== null" class="top-msg-num">
                    {{ leftCount }}/{{ allCount }}
                </view>
            </view>
        </view>
        
        <lian-lian-kan-game v-if="initComplete" :images="images" :col="col" :row="row" :block-size="blockSize"
                            @itemTap="itemTap" @complete="complete"/>


        <game-result-popup ref="resultPopup" :show-ad="showAD"/>


        <uni-popup ref="gameTips" :is-mask-click="false">
            <view class="game-tips bg-white">
                <view class="game-tips-title">游戏说明</view>
                <view class="game-tips-content color-content">
                    <view>
                        <template v-if="needTime">左上角为倒计时，</template>
                        右上角为图标剩余数和总数。
                    </view>
                    <view>
                        <template v-if="needTime">{{ time }}秒内</template>
                        连完所有图标奖励{{ integral }}积分。
                    </view>
                </view>

                <view class="p10">
                    <view class="start-game-button" @click="GO">开始游戏</view>
                </view>
            </view>

            <view v-if="showAD" class="flex-all-center pt5">
                <xwy-ad :ad_type="3"></xwy-ad>
                <xwy-ad :ad_type="66"></xwy-ad>
            </view>
        </uni-popup>


        <template v-if="showAD">
            <xwy-ad :ad_type="3"/>
            <xwy-ad :ad_type="66"/>
        </template>
    </view>
</template>

<script>
import lianLianKanGame from '../components/lian-lian-kan/lian-lian-kan-game.vue'
import config from '@/pages/task-challenge/config'

export default {
    components: {lianLianKanGame},

    data() {
        return {
            initComplete: false,
            images: [],
            col: config.lianLianKanDefaultSet.col,
            row: config.lianLianKanDefaultSet.row,
            allCount: null,
            leftCount: null,
            needTime: false,
            time: null,
            showAD: false,
            bg_img: '',
            integral: 0
        }
    },

    onLoad() {
        this.getGameData()
    },
    
    computed: {
        timeText() {
            if (!this.time) return '00:00'
            let mm = 0, ss = this.time
            if (ss >= 60) {
                mm = Math.floor(ss / 60)
                ss = ss % 60
            }
            return `${mm < 10 ? '0' + mm : mm}:${ss < 10 ? '0' + ss : ss}`
        },

        blockSize() {
            const {windowWidth, windowHeight} = uni.getWindowInfo()
            const width = windowWidth / (this.col + 2)
            // 如果显示广告，要给广告留20%的位置
            const maxHeight = windowHeight * (this.showAD ? .8 : 1) - 55
            const height = maxHeight / (this.row + 2)
            return Math.min(width, height) - 2
        },

        pageStyle() {
            if (!this.bg_img) return ''
            return `background-image: url(${this.bg_img}); background-position: center; background-size: cover; background-repeat: no-repeat;`
        }
    },
    
    onUnload() {
        this.clearCountdownInterval()
    },

    methods: {
        getGameData() {
            this.getOpenerEventChannel().once('data', data => {
                this.active_id = data.active_id
                this.point_id = Number(data.point_id)
                this.integral = data.integral || 0

                if (data.row) this.row = Math.floor(data.row)
                if (data.col) this.col = Math.floor(data.col)
                this.allCount = this.row * this.col
                this.leftCount = this.allCount

                if (data.img_count)this.img_count = Math.floor(data.img_count)

                if (data.seconds) {
                    this.needTime = true
                    this.time = Math.floor(data.seconds)
                }
                if (data.title) this.$uni.setNavigationBarTitle(data.title)
                if (data.show_ad) this.showAD = true
                if (data.bg_img) this.bg_img = data.bg_img
                if (data.navigation_bar) uni.setNavigationBarColor({
                    ...data.navigation_bar,
                    fail: err => console.log(err)
                })

                this.init()
            })
        },

        async init() {
            await this.getImageList()
            this.initComplete = true
            this.$refs.gameTips.open()
        },

        GO() {
            this.$refs.gameTips.close()
        },

        async getImageList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/link_game_system_set',
                data: {
                    active_id: this.active_id
                }
            })

            const list = res?.data?.system_set?.['logo_list']
            if (!list?.length) return this.$uni.showModal('未配置连连看图标')

            const n = this.img_count || config.lianLianKanDefaultSet.img_count
            this.images = n >= list.length ? list : this._utils.shuffleArray(list).slice(0, n)
        },

        complete(leftCount) {
            this.leftCount = leftCount
            if (leftCount === 0) this.gameResult({code: 1, msg: 'success'})
        },

        itemTap() {
            if (this.gameStart) return

            this.gameStart = true
            this.countdown()
        },

        countdown() {
            if (!this.needTime) return

            this.countdownInterval = setInterval(() => {
                this.time--
                if (this.time === 0) this.timeOut()
            }, 1000)
        },

        timeOut() {
            this.gameResult({code: 0, msg: 'timeout'})
        },

        clearCountdownInterval() {
            this.countdownInterval && clearInterval(this.countdownInterval)
            this.countdownInterval = null
        },

        gameResult(e) {
            this.clearCountdownInterval()
            this.submitResult(e.code)
        },

        async submitResult(code) {
            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify({
                        types: 11,
                        point_id: this.point_id,
                        result: code === 1 ? 'success' : 'fail'
                    }))
                }
            })
            uni.hideLoading()

           
            let resultCode = code
            let info = resultCode === 1 ? '任务完成' : '任务失败'
            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info = res.info
            }


            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })
            

            this.getOpenerEventChannel().emit('success')
        }
    }
}
</script>

<style lang="scss">
.page {
    width: 100%;
    height: 100vh;
    box-sizing: border-box;
    background-image: url(https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/red-small-games/lian-lian-kan/bg.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-attachment: fixed;
}

.top-msg-container {
    padding: 15px 3vw 10px calc(3vw + 15px);

    .top-msg {
        position: relative;
        display: flex;
        flex-direction: row;

        .top-msg-icon {
            position: absolute;
            left: -15px;
            top: -3px;
            border-radius: 50%;
            padding: 3px;

            .top-msg-inner-ring {
                width: 30px;
                height: 30px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 50%;

                .iconfont {
                    color: #fff;
                    font-size: 22px;
                }
            }
        }

        .top-msg-num {
            background-color: rgba(0, 0, 0, .3);
            color: #fff;
            height: 30px;
            border-radius: 0 15px 15px 0;
            padding: 0 15px 0 26px;
            min-width: 60px;
            font-size: 18px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .top-msg-left {
        .top-msg-icon {
            background-color: #5cadff;
            box-shadow: 0 0 5px #2d8cf0;

            .top-msg-inner-ring {
                background-color: #2d8cf0;
            }
        }

        .top-msg-num {
            box-shadow: 0 0 10px rgba(45, 140, 240, .2) inset;
        }
    }

    .top-msg-right {
        .top-msg-icon {
            background-color: #fce230;
            box-shadow: 0 0 5px #ffb400;

            .top-msg-inner-ring {
                background-color: #ffb400;
            }
        }

        .top-msg-num {
            box-shadow: 0 0 10px rgba(255, 180, 0, .2) inset;
        }
    }
}

.game-tips {
    width: 90vw;
    border-radius: 10px;

    .game-tips-title {
        line-height: 44px;
        text-align: center;
    }

    .game-tips-content {
        padding: 0 20px;
        line-height: 30px;
    }

    .start-game-button {
        width: 150px;
        line-height: 40px;
        border-radius: 20px;
        text-align: center;
        font-size: 18px;
        background-color: #ff985e;
        color: #fff;
        margin: 10px auto;
    }
}
</style>