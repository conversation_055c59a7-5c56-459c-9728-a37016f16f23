<template>
    <view>
        <xwy-ad :ad_type="3" :activity_id="id"></xwy-ad>
        <xwy-ad :ad_type="66" :activity_id="id"></xwy-ad>

        <view v-if="list.length" class="text-center color-sub font14 pt5">
            <text>共{{ list.length }}关</text>
            <text v-if="!selectLevel">，</text>
            <template v-if="!selectLevel">
                <text v-if="current_level_index === -1">所有关卡都已完成</text>
                <text v-else>当前已到 {{ list[current_level_index].title }}</text>
            </template>
        </view>

        <view v-if="list.length" class="list">
            <view v-for="(item, index) in list" :key="item.id">
                <view class="item" hover-class="navigator-hover" @click="go(item, index)">
                    <view class="flex-row">
                        <image class="logo" v-if="item.logo" :src="item.logo" mode="aspectFill"/>
                        <view style="width: 100%;">
                            <view class="title color-title">{{ item.title }}</view>
                            <view class="clear clearfix">
                                <view class="fr pt10">
                                    <view v-if="selectLevel"
                                          class="go-btn text-center bg-light-primary color-white">
                                        选择关卡
                                    </view>
                                    <template v-else>
                                        <view v-if="item.have_rushed">
                                            <text class="iconfont icon-completed color-red"
                                                  style="font-size: 44px;"></text>
                                        </view>
                                        <view
                                            v-if="!item.have_rushed && index === current_level_index"
                                            class="go-btn text-center bg-warning color-white"
                                        >去闯关
                                        </view>
                                        <view
                                            v-if="!item.have_rushed && index !== current_level_index"
                                            class="go-btn go-lock text-center bg-background color-disabled"
                                        >
                                            <text class="iconfont icon-lock"></text>
                                            <text>未解锁</text>
                                        </view>
                                    </template>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>


                <xwy-ad v-if="(index + 1) % 20 === 0" :ad_type="66" :activity_id="id"></xwy-ad>
            </view>
        </view>

        <view class="text-center" v-if="!loading && !list.length" style="padding-top: 15vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">活动未设置关卡</view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            loading: true,
            id: '',
            list: [],
            current_level_index: -1,
            selectLevel: false
        }
    },

    onLoad(params) {
        this.id = params.id
        this.cat_id = params.cat_id
        this.unit = params.unit
        this.game_type = params.game_type || 0
        if (params.select) this.selectLevel = true
        this.init()
    },

    methods: {
        async init() {
            uni.showLoading({ title: '加载中...' })
            await this.getLevelList()
            this.loading = false
            uni.hideLoading()
        },

        async getLevelList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.integral.treasureHunting/round_list_user_rushed_state',
                data: {
                    category_id: this.cat_id,
                    active_id: this.id
                }
            })
            const list = res?.data?.['round_list']
            if (!list?.length) return
            const new_list = []
            let current_level_index = -1 // 当前关卡
            list.forEach((v, i) => {
                const have_rushed = v.have_rushed || 0  // 是否已闯关
                if (!have_rushed && current_level_index === -1) current_level_index = i
                const item = {
                    id: v.id,
                    title: v.title,
                    logo: v.answer_option?.logo || '',
                    have_rushed
                }
                new_list.push(item)
            })
            this.list = new_list
            // 全部都闯过了，当前关卡为最后一 关
            this.current_level_index = current_level_index
        },

        async go(item, index) {
            if (this.selectLevel) return this.selectLevelConfirm(item)

            if (item.have_rushed) {
                const res = await this.$uni.showModal('该关卡已闯过，重复闯关没有奖励，是否继续？', {
                    confirmText: '继续',
                    showCancel: true
                })
                if (!res.confirm) return
            }
            if (index !== this.current_level_index && !item.have_rushed) return this.goLock()
            this.$uni.navigateTo(`./look-for?active_id=${this.id}&id=${item.id}&unit=${this.unit}&game_type=${this.game_type}`, {
                events: {
                    refresh: () => {
                        this.getLevelList()
                        this.getOpenerEventChannel().emit('upDateUserDetails')
                    }
                },
                success: res => res.eventChannel.emit('levelList', this.list)
            })
        },

        goLock() {
            uni.showToast({ title: '请先完成上一关卡', icon: 'none' })
        },

        selectLevelConfirm(item) {
            this.getOpenerEventChannel().emit('selectLevel', item)
            this.$uni.navigateBack()
        }
    }
}
</script>

<style lang="scss">
.list {
    padding-top: 1px;
    .item {
        margin: 20px 10px;
        padding: 10px;
        border-radius: 10px;
        box-shadow: 0 0 10px #ccc;
        .logo {
            width: 120px;
            min-width: 120px;
            height: 80px;
            border-radius: 10px;
            display: block;
            margin-right: 10px;
        }
        .go-btn {
            width: 100px;
            line-height: 40px;
            border-radius: 20px;
        }
        .go-lock {
            line-height: 38px;
            border: 1px solid #d9dade;
        }
        .go-lock .iconfont {
            padding-right: 3px;
        }
    }
}
</style>
