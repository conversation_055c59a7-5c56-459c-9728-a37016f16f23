<template>
    <view>
        <navigator class="back flex-all-center" open-type="navigateBack">
            <text class="iconfont color-white icon-go-back font28"></text>
        </navigator>


        <view v-if="countdown > 0 || gotCountShow" class="top-msg-container flex-row" :style="topMsgStyle">
            <view v-if="countdown" class="top-msg top-msg-left">
                <view class="top-msg-icon">
                    <view class="top-msg-inner-ring">
                        <text class="iconfont icon-alarm-clock"></text>
                    </view>
                </view>
                <view class="top-msg-num">{{ countdownText }}</view>
            </view>

            <view v-if="countdown > 0 && gotCountShow" style="width: 30px;"></view>

            <view v-if="gotCountShow" class="top-msg top-msg-right">
                <view class="top-msg-icon">
                    <view class="top-msg-inner-ring">
                        <text class="iconfont icon-grid"></text>
                    </view>
                </view>
                <view class="top-msg-num">
                    {{ target_list.filter(item => item.got_it).length }}/{{ target_list.length }}
                </view>
            </view>
        </view>


        <view v-if="news_content && !news_show" class="news-icon flex-all-center"
              hover-class="navigator-hover" @click="finishNewsPopupShow(true)">
            <text class="iconfont icon-book font24 color-white"></text>
        </view>

        <view v-if="big_tips_show" class="big-tips flex-all-center color-white font34"
              @touchmove.stop.prevent="" @click="bigTipsHide">
            {{ startTips }}
        </view>

        <view class="big-image-container">

            <image v-if="big_image" class="big-image" mode="widthFix" :src="big_image"
                   @load="bigImageLoad($event)" @click="clickBigImage"/>

            <template v-if="target_list.length && game_type === 1">
                <view class="target-circle-item" v-for="(item, index) in target_list" :key="index"
                      :style="{width: item.width / scale + 'px', height: item.height / scale + 'px', top: item.y / scale + 'px', left: item.x / scale + 'px', display: item.got_it ? 'block' : 'none'}">
                </view>
            </template>

            <view v-if="find_empty" class="find-empty" :style="findEmptyStyle">
                <uni-icons type="closeempty" size="60" color="#ff0000"/>
            </view>
        </view>


        <view v-if="target_list.length && game_type === 0" class="target-list flex-all-center">
            <view class="target-item" v-for="(item, index) in target_list" :key="index">
                <uni-transition :mode-class="['fade', 'slide-bottom']" :show="!item.got_it">
                    <image class="target-image" :src="item.image" mode="aspectFit"/>
                </uni-transition>
            </view>
        </view>


        <view class="finish" :class="finish && news_show ? 'flex-kai' : 'flex-center'"
              @touchmove.stop.prevent="" @click="finishNewsPopupClose" :animation="finish_news_animation">
            <view v-if="finish" class="result" @click.stop="">

                <image :src="'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/guessing_songs/' + (result_status === 1 ? 'jbb.png': 'jblk.png')"/>
                <view class="result-title color-warning font24">
                    闯关{{ result_status === 1 ? '成功' : '失败' }}
                </view>
                <template v-if="result_status === 1">
                    <view class="result-time pt5">
                        <text class="iconfont icon-alarm-clock color-sub"></text>
                        <text class="pl5 color-content">用时{{ time }}秒</text>
                    </view>
                    <view v-if="integral" class="pt5">
                        <text class="iconfont icon-integral color-sub"></text>
                        <text class="pl5 color-content">获得{{ integral }}{{ unit }}</text>
                    </view>
                </template>
                <view v-if="finish_info" class="color-sub font14 pt5 font12">{{ finish_info }}</view>
            </view>

            <view v-if="news_show" class="news" @click.stop="">

                <scroll-view class="news-content" :scroll-y="true">
                    <u-parse :content="news_content"/>
                    <view class="text-center font14 color-disabled pt10">--- 完 ---</view>
                </scroll-view>
            </view>

            <view class="bottom-button-container flex-center">
                <view class="close-or-next-button">关闭</view>
                <view v-if="nextLevelId" class="close-or-next-button" @click="toNextLevel">下一关</view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            id: '',
            big_image: '',
            target_list: [],
            big_tips_show: false,
            time: 0,
            finish: false,
            news_show: false,
            news_content: '',
            finish_news_animation: {},
            integral: 0,
            finish_info: '',
            unit: '',
            game_type: 0,
            nextLevelId: null,
            scale: 1,
            gotCountShow: false,
            start_tips: '',
            result_status: 1,
            countdown: 0,
            topMsgStyle: '',
            find_empty: false,
            findEmptyStyle: ''
        }
    },

    computed: {
        startTips() {
            if (this.start_tips) return this.start_tips
            return this.game_type === 1 ? `在图中找出${this.target_list.length}处不同元素` : '在图中找出下列元素'
        },

        countdownText() {
            if (!this.countdown) return '00:00'
            let mm = 0, ss = this.countdown
            if (ss >= 60) {
                mm = Math.floor(ss / 60)
                ss = ss % 60
            }
            return `${mm < 10 ? '0' + mm : mm}:${ss < 10 ? '0' + ss : ss}`
        }
    },

    onUnload() {
        this.clearCountdownInterval()
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.id = Number(params.id)
        this.unit = params.unit
        if (params.game_type) this.game_type = Number(params.game_type)
        if (params.time) this.all_time = Number(params.time)

        this.setTopMsgStyle()

        // 从闯关过来的，只玩这一关的，不需要关卡列表
        if (params['from_task']) {
            this.from_task = true
            if (params.point_id) this.point_id = Number(params.point_id)
            return this.init()
        }

        this.getLevelList()
    },

    methods: {
        setTopMsgStyle() {
            const {width, height, top, right} = uni.getMenuButtonBoundingClientRect()
            const windowWidth = uni.getWindowInfo().windowWidth
            this.topMsgStyle = `top: ${height - 30 + top}px; right: ${windowWidth - right + width + 20}px;`
        },


        getLevelList() {
            this.getOpenerEventChannel().on('levelList', list => {
                this.level_list = list
                this.init()
            })
        },

        async toNextLevel() {
            if (!this.nextLevelId) return

            /*const res = await this.$uni.showModal('确定进入下一关？', {showCancel: true})
            if (!res?.confirm) return*/

            this.id = this.nextLevelId
            await this.init()
        },

        async init() {
            uni.showLoading({title: '加载中...'})

            this.nextLevelId = this.getNextLevelId()
            this.resetLevelData()

            await this.getDetails()
            uni.hideLoading()
            this.big_tips_show = true
        },

        getNextLevelId() {
            if (!this.level_list?.length) return null
            const current_level_index = this.level_list.findIndex(item => item.id === this.id)
            const next_level_index = current_level_index + 1
            if (next_level_index >= this.level_list.length) return ''
            return this.level_list[next_level_index]?.id || null
        },

        resetLevelData() {
            this.target_list = []
            this.big_image = ''
            this.news_content = ''
            this.time = 0
            this.finish = false
            this.news_show = false
            this.integral = 0
            this.finish_info = ''
            this.result_status = 1
        },

        bigTipsHide() {
            this.big_tips_show = false
            if (this.game_type === 1) this.gotCountShow = true
            this.start_time = new Date().getTime()
            this.countdownStarts()
        },

        clearCountdownInterval() {
            if (this.countdownInterval) {
                clearInterval(this.countdownInterval)
                this.countdownInterval = null
            }
        },

        countdownStarts() {
            if (!this.all_time) return
            this.clearCountdownInterval()

            this.countdown = this.all_time
            this.countdownInterval = setInterval(() => {
                this.countdown--
                if (this.countdown === 0) {
                    this.clearCountdownInterval()
                    this.timeout()
                }
            }, 1000)
        },

        timeout() {
            this.result_status = 0
            this.rewards()
        },

        async getDetails() {
            const res = await this.xwy_api.request({
                url: 'front.flat.exam.questionBank/get_question_bank_details',
                data: {id: this.id}
            })
            if (res?.data?.title) uni.setNavigationBarTitle({title: this.title})
            const answer_option = res?.data?.['answer_option']
            if (answer_option?.target_list?.length) {
                const target_list = []
                answer_option.target_list.forEach(v => {
                    target_list.push({
                        ...v,
                        got_it: false
                    })
                })
                this.target_list = target_list
            }
            if (answer_option?.big_image) this.big_image = answer_option.big_image
            this.start_tips = answer_option?.start_tips || ''
            if (answer_option?.news_id) await this.getNews(answer_option.news_id)
        },

        bigImageLoad(e) {
            const {width} = e.detail
            let {windowWidth} = uni.getSystemInfoSync()
            this.scale = width / windowWidth
        },

        clickBigImage(e) {
            const x = e.detail.x * this.scale
            const y = e.detail.y * this.scale
            const target_list = this.target_list

            let in_target = false

            for (let i = 0; i < target_list.length; i++) {
                const item = target_list[i]
                if (x >= item.x && x <= item.x + item.width && y >= item.y && y <= item.y + item.height) {
                    in_target = true
                    if (item.got_it) continue
                    if (this.game_type === 0) this.$uni.showToast('找到了')
                    item.got_it = true
                    this.checkAllGotIt()
                }
            }

            if (!in_target) {
                this.findEmptyStyle = `left: ${e.detail.x - 30}px; top: ${e.detail.y - 30}px;`
                this.find_empty = false
                clearTimeout(this.findEmptyTimeout)
                this.$nextTick(() => {
                    this.find_empty = true
                })
                this.findEmptyTimeout = setTimeout(() => {
                    this.find_empty = false
                }, 1000)
            }
        },

        // 检查target_list数组里面的每一项是否都找到了
        checkAllGotIt() {
            this.target_list.every(item => item.got_it) && this.rewards()
        },

        rewardsRequest() {
            if (this.from_task) {
                return this.xwy_api.request({
                    url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                    data: {
                        active_id: this.active_id,
                        sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify({
                            types: 14,
                            point_id: this.point_id,
                            result: this.result_status === 1 ? 'success' : 'fail'
                        }))
                    }
                })
            }

            return this.xwy_api.request({
                url: 'front.flat.sport_step.integral.treasureHunting/hunting_result',
                data: {
                    active_id: this.active_id,
                    point_id: this.id,
                    // 为了防止用户前端恶意调用接口，因此约定签名内容及寻宝的时间传值按照如下值：活动id+寻宝用时，单位秒然后进行base64编码，并在第一位加上一个干扰字符。
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](`${this.active_id}+${this.time}`)
                    // 这里活动id和时间的连接符先改成","，刘总接口写错了，应该是用"+"的，写成","了，后台代码发版以后要用回"+"
                    // sign: this._utils.randomCoding() + this._utils.base64['encode'](`${this.active_id},${time}`)
                }
            })
        },


        async rewards() {
            this.clearCountdownInterval()
            if (this.game_type === 1) this.gotCountShow = false
            this.time = Math.ceil((new Date().getTime() - this.start_time) / 1000)

            this.$uni.showLoading()
            const res = await this.rewardsRequest()
            uni.hideLoading()

            this.finishNewsPopupShow(false)
            if (res?.status !== 1) {
                this.result_status = 0
                if (res?.info) this.finish_info = res.info
                // return uni.showToast({ title: res?.info || '领取失败', icon: 'none' })
                return
            }
            // let tips = '闯关成功！'
            const num = res.data?.num
            if (num) {
                this.integral = num
                // tips += `获得${num}${this.unit}`
            }
            // uni.showToast({ title: tips, icon: 'none' })
            this.getOpenerEventChannel().emit('refresh')
            this.getOpenerEventChannel().emit('success')
            // setTimeout(() => {
            //     uni.navigateBack()
            // }, 1000)
        },

        async getNews(news_id) {
            const res = await this.xwy_api.request({
                url: 'front.news/news_details',
                data: {news_id}
            })

            const news_content = res?.data?.['news_details']?.content
            if (news_content) this.news_content = news_content
        },

        finishNewsPopupShow(just_news = false) {
            if (!just_news) this.finish = true
            if (this.news_content) this.news_show = true
            if (!this.finish && !this.news_show) return
            const animation = uni.createAnimation({
                duration: 300,
                timingFunction: 'ease',
            })
            this.animation = animation
            animation.top(0).backgroundColor('rgba(0, 0, 0, .3').step()
            this.finish_news_animation = animation.export()
        },

        finishNewsPopupClose() {
            const animation = uni.createAnimation({
                duration: 300,
                timingFunction: 'ease',
            })
            this.animation = animation
            animation.top('-110vh').backgroundColor('rgba(0, 0, 0, 0').step()
            this.finish_news_animation = animation.export()
            setTimeout(() => {
                this.finish = false
                this.news_show = false
            }, 300)

            if (this.from_task) this.$uni.navigateBack()
        }
    }
}
</script>

<style lang="scss">
$back-z-index: 6; // 返回按钮,暂定为层级最高，防止被其他元素遮挡
$finish-z-index: 5; // 结果区域，层级不得高于返回按钮，且不得低于目标元素
$target-z-index: 4; // 目标元素，层级不得高于结果区域，且不得低于提示区域
$big-tips-z-index: 3; // 提示区域，层级不得高于目标元素
$news-icon-z-index: 2; // 文章图标，层级不得高于提示区域


$target-size: 70rpx;

.back {
    position: fixed;
    top: 10px;
    left: 10px;
    width: 40px;
    height: 40px;
    z-index: $back-z-index;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
}


.top-msg-container {
    position: fixed;
    z-index: $back-z-index;

    .top-msg {
        position: relative;
        display: flex;
        flex-direction: row;

        .top-msg-icon {
            position: absolute;
            left: -15px;
            top: -3px;
            border-radius: 50%;
            padding: 3px;

            .top-msg-inner-ring {
                width: 30px;
                height: 30px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 50%;

                .iconfont {
                    color: #fff;
                    font-size: 22px;
                }
            }
        }

        .top-msg-num {
            background-color: rgba(0, 0, 0, .3);
            color: #fff;
            height: 30px;
            border-radius: 0 15px 15px 0;
            padding: 0 15px 0 26px;
            min-width: 60px;
            font-size: 18px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .top-msg-left {
        .top-msg-icon {
            background-color: #5cadff;
            box-shadow: 0 0 5px #2d8cf0;

            .top-msg-inner-ring {
                background-color: #2d8cf0;
            }
        }

        .top-msg-num {
            box-shadow: 0 0 10px rgba(45, 140, 240, .2) inset;
        }
    }

    .top-msg-right {
        .top-msg-icon {
            background-color: #fce230;
            box-shadow: 0 0 5px #ffb400;

            .top-msg-inner-ring {
                background-color: #ffb400;
            }
        }

        .top-msg-num {
            box-shadow: 0 0 10px rgba(255, 180, 0, .2) inset;
        }
    }
}

.news-icon {
    position: fixed;
    bottom: calc(#{$target-size} + 25px);
    right: 20px;
    z-index: $news-icon-z-index;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.5);
}

.big-tips {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: $big-tips-z-index;
    background-color: rgba(0, 0, 0, 0.7);
    letter-spacing: 2px;
    text-align: center;
    padding: 0 8vw;
    box-sizing: border-box;
}

.big-image-container {
    position: relative;
    z-index: 1;

    .big-image {
        width: 100vw;
        display: block;
    }

    .target-circle-item {
        border-radius: 50%;
        box-sizing: border-box;
        border: 5px solid #f00;
    }

    .target-circle-item, .find-empty {
        position: absolute;
        z-index: 2;
        transform: scale(1);
        animation: lessen .3s ease-out;
    }

    @keyframes lessen {
        from {
            transform: scale(1.5);
        }
        to {
            transform: scale(1);
        }
    }
}

.target-list {
    position: fixed;
    bottom: 15px;
    left: 5vw;
    z-index: $target-z-index;
    width: 90vw;
    pointer-events: none;

    .target-item {
        width: $target-size;
        padding: 5px;

        .target-image {
            width: $target-size;
            height: $target-size;
            display: block;
        }
    }
}

.finish {
    position: fixed;
    top: -110vh;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: $finish-z-index;
    padding: 40rpx;
    box-sizing: border-box;

    .result, .news {
        width: calc(50% - 20rpx);
        height: 100%;
        border-radius: 10px;
        box-sizing: border-box;
        border: 2px solid #f6d3a8;
        background-color: rgba(255, 255, 255, .9);
    }

    .result {
        text-align: center;
        padding-top: 10rpx;

        image {
            width: 120rpx;
            height: 120rpx;
            margin-bottom: 10rpx;
        }
    }

    .news {
        padding: 20rpx;

        .news-content {
            height: 100%;
        }
    }

    .bottom-button-container {
        position: absolute;
        bottom: calc(40rpx - 24px - 5px);
        width: 160px;
        left: 50%;
        margin-left: -80px;
    }

    .close-or-next-button {
        width: 60px;
        line-height: 24px;
        border-radius: 12px;
        background-color: rgba(255, 255, 255, .7);
        text-align: center;
        font-size: 14px;
        color: #666;
        margin: 0 10px;
    }
}
</style>
