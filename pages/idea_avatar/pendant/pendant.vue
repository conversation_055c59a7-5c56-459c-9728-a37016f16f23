<template>
	<view class="container">
		<image class="container-bg" mode="aspectFill" src="http://www.xinweiyun.com/weixin/public/img/longmarch/idea_avatar_bg.jpg"/>
		<view class="avatar-container" @touchstart="touchStart" @touchend="touchEnd" @touchmove="touchMove">
			<!-- <view class="border"></view> -->
			<view class="avatar-bg-border">
				<image @touchstart="touchAvatarBg" class="avatar-bg" id="avatar-bg" :src="avatarPath"/>
			</view>
			<image
				v-if="currentMaskId > -1"
				class="mask"
				:class="{ 'mask-with-border': showBorder }"
				id="mask"
				:src="maskPic"
				:style="{ top: maskCenterY - maskSize / 2 - 2 + 'px', left: maskCenterX - maskSize / 2 - 2 + 'px', transform: 'rotate(' + rotate + 'deg)' + 'scale(' + scale + ')' + 'rotateY(' + rotateY + 'deg)' }"
			/>
			<view class="handle" :class="{ 'hide-handle': !showBorder }" :style="{ top: handleCenterY - 10 + 'px', left: handleCenterX - 10 + 'px' }">
				<image class="handle-img" id="handle" :src="dragImg"/>
			</view>
		</view>
		<view class="cans">
			<canvas class="cans-id-mask" canvas-id="cans-id-mask" />
		</view>
		<view class="action-wrapper">
			<view class="action">
				<button class="action-btn" open-type="chooseAvatar"  @chooseavatar="getAvatar">我的头像</button>
			</view>
			<view class="action">
				<button class="action-btn" @click="chooseImage">选择图片</button>
			</view>
			<view class="action">
				<button class="action-btn" @click="draw">保存头像</button>
			</view>
			<!-- <view class="action">
				<button class="action-btn" open-type="share">分享朋友</button>
			</view> -->
		</view>

        <view v-if="showAd" class="pt15">
            <xwy-ad v-if="interstitialAdShow" :ad_type="3" :activity_id="active_id"></xwy-ad>
            <xwy-ad :ad_type="3" :activity_id="active_id"></xwy-ad>
            <xwy-ad :ad_type="66" :activity_id="active_id"></xwy-ad>
        </view>

		<view class="ornaments-tabs">
			<scroll-view class="ornaments-scroll-view" scroll-y="true" :scroll-left="scrollLeft" @scroll="scroll">
				<view class="img-wrap" v-for="(item, index) in imgList" :key="index">
					<image class="img-list" :src="item" @click="changeMask(item)"/>
				</view>
			</scroll-view>
		</view>


        <view class="pageLoading" v-show="loading">
            <full-loading bgColor="#ffffff"/>
        </view>
	</view>
</template>


<script>
const app = getApp()
import login from '../../../utils/api/login.js'
import utils from '../../../utils/utils.js'
import ideaAvatarApi from '../api'
import fullLoading from "../components/full-loading.vue"

const dragImg = require("../static/drag.svg")

export default {
    components: {fullLoading},
	data() {
		return {
            loading: true,
			dragImg,
			cansWidth: 260, // 宽度 px
			cansHeight: 260, // 高度 px
			maskCenterX: wx.getSystemInfoSync().windowWidth / 2,
			maskCenterY: 200,
			cancelCenterX: wx.getSystemInfoSync().windowWidth / 2 - 50 - 2,
			cancelCenterY: 200,
			handleCenterX: wx.getSystemInfoSync().windowWidth / 2 + 50 - 2,
			handleCenterY: 250,
			maskSize: 100,
			scale: 1,
			rotate: 0,
			rotateY: 0,
			mask_center_x: wx.getSystemInfoSync().windowWidth / 2,
			mask_center_y: 200,
			cancel_center_x: wx.getSystemInfoSync().windowWidth / 2 - 50 - 2,
			cancel_center_y: 200,
			handle_center_x: wx.getSystemInfoSync().windowWidth / 2 + 50 - 2,
			handle_center_y: 250,
			scaleCurrent: 1,
			rotateCurrent: 0,
			touch_target: "",
			start_x: 0,
			start_y: 0,
			scrollLeft: 0,
			old: {
				scrollLeft: 0
			},
			imgList: [],
			avatarPath: "",
			maskPic: "",
			currentMaskId: -1,
			showBorder: false,
            showAd: false,
            interstitialAdShow: false,
            active_id: ''
		}
	},
	onLoad(params) {
        if (params.id) {
            this.active_id = params.id
            uni.hideShareMenu(undefined)
        }
		login.uniLogin(() => {
            if (!params.closed_AD) this.showAd = true
            
			this.getAvatarPath()
			this.getList()
		})
	},

    onShareAppMessage() {
        return {
            title: '头像换挂件',
            path: '/pages/idea_avatar/pendant/pendant'
        }
    },

	methods: {
		getAvatarPath() {
            const headimg = app.globalData['userinfo'].headimg
			const avatarPath = utils.headimgHD(headimg)
			this.downloadAvatar(avatarPath)
		},

		downloadAvatar(src) {
			uni.downloadFile({
				url: src,
				success: res => {
					this.avatarPath = res.tempFilePath
				},
                fail: () => this.$uni.showToast('头像加载超时', 'error'),
				complete: () => {
					this.loading = false
				}
			})
		},

        getImgListEvent() {
            return new Promise(resolve => {
                this.getOpenerEventChannel().on('img_list', img_list => {
                    console.log('1、', img_list?.[0]?.list);
                    resolve(img_list?.[0]?.list || null)
                })
            })
        },
        
		// 获取挂件
		async getList() {
            let img_list
            if (this.active_id) img_list = await this.getImgListEvent()
            console.log('2、', img_list);
            if (!img_list?.length) {
                img_list = await ideaAvatarApi.getPendantImgList(this.active_id || '')
            }
            
            if (!img_list?.length) return this.$uni.showModal('获取挂件失败', {
                success: () => uni.navigateBack()
            })

            this.imgList = img_list
            this.changeMask(this.imgList[0])
		},



        getAvatar(e) {
            this.avatarPath = e.detail.avatarUrl
		},

		// 选择图片
		chooseImage() {
			uni.chooseImage({
				count: 1,
				sourceType: ["album", "camera"],
				success: res => {
					this.avatarPath = res.tempFilePaths[0]
				}
			})
		},
		// 滚动
		scroll: function(e) {
			this.old.scrollLeft = e.detail.scrollLeft
		},

		// 选择挂件
		changeMask(item) {
			this.$uni.showLoading('切换中...')
            this.showBorder = true
			uni.downloadFile({
				url: item,
				success: res => {
                    this.maskPic = res.tempFilePath
                    this.currentMaskId = 1
				},
                fail: () => this.$uni.showToast('图像加载超时', 'error'),
				complete: () => uni.hideLoading()
			})
		},
		// 设置挂件位置
		touchStart(e) {
            const id = e.target.id
            let touch_target = ""
            if (id === "mask" || id === "handle") touch_target = id
            if (touch_target === "mask") this.showBorder = true
            this.touch_target = touch_target

            if (this.touch_target !== "") {
                this.start_x = e.touches[0].clientX
                this.start_y = e.touches[0].clientY
            }
		},
		touchMove(e) {
			const current_x = e.touches[0].clientX,
                  current_y = e.touches[0].clientY
			const moved_x = current_x - this.start_x,
                  moved_y = current_y - this.start_y

            if (this.touch_target === "mask") {
				this.maskCenterX = this.maskCenterX + moved_x
				if (this.maskCenterX < 110) {
					this.maskCenterX = 110
				} else if (this.maskCenterX > 264) {
					this.maskCenterX = 264
				}
				this.maskCenterY = this.maskCenterY + moved_y
				if (this.maskCenterY < 86) {
					this.maskCenterY = 86
				} else if (this.maskCenterY > 240) {
					this.maskCenterY = 240
				}
				this.cancelCenterX = this.cancelCenterX + moved_x
				this.cancelCenterY = this.cancelCenterY + moved_y
				this.handleCenterX = this.handleCenterX + moved_x
				if (this.handleCenterX < 158) {
					this.handleCenterX = 158
				} else if (this.handleCenterX > 312) {
					this.handleCenterX = 312
				}
				this.handleCenterY = this.handleCenterY + moved_y
				if (this.handleCenterY < 188) {
					this.handleCenterY = 188
				} else if (this.handleCenterY > 341) {
					this.handleCenterY = 341
				}
			}
            if (this.touch_target === "handle") {
				this.handleCenterX = this.handleCenterX + moved_x
				this.handleCenterY = this.handleCenterY + moved_y
				this.cancelCenterX = 2 * this.maskCenterX - this.handleCenterX
				this.cancelCenterY = 2 * this.maskCenterY - this.handleCenterY
				let diff_x_before = this.handle_center_x - this.mask_center_x
				let diff_y_before = this.handle_center_y - this.mask_center_y
				let diff_x_after = this.handleCenterX - this.mask_center_x
				let diff_y_after = this.handleCenterY - this.mask_center_y
				let distance_before = Math.sqrt(diff_x_before * diff_x_before + diff_y_before * diff_y_before)
				let distance_after = Math.sqrt(diff_x_after * diff_x_after + diff_y_after * diff_y_after)
				let angle_before = (Math.atan2(diff_y_before, diff_x_before) / Math.PI) * 180
				let angle_after = (Math.atan2(diff_y_after, diff_x_after) / Math.PI) * 180
				this.scale = (distance_after / distance_before) * this.scaleCurrent
				this.rotate = angle_after - angle_before + this.rotateCurrent
			}
			this.start_x = current_x
			this.start_y = current_y
		},
		touchEnd() {
			this.mask_center_x = this.maskCenterX
			this.mask_center_y = this.maskCenterY
			this.cancel_center_x = this.cancelCenterX
			this.cancel_center_y = this.cancelCenterY
			this.handle_center_x = this.handleCenterX
			this.handle_center_y = this.handleCenterY
			this.touch_target = ""
			this.scaleCurrent = this.scale
			this.rotateCurrent = this.rotate
		},
		// 不显示border
		touchAvatarBg() {
			this.showBorder = false
		},
		// 绘制头像
		draw() {
			const that = this
            if (this.currentMaskId === -1) return this.$uni.showToast('还没选择挂件哦')

			let scale = this.scale
			let rotate = this.rotate
			let mask_center_x = this.mask_center_x
			let mask_center_y = this.mask_center_y
			const query = wx.createSelectorQuery()
			query.select("#avatar-bg").boundingClientRect()
			query.exec(res => {
				mask_center_x = mask_center_x - res[0].left
				mask_center_y = mask_center_y - res[0].top
				const context = wx.createCanvasContext("cans-id-mask")
				const mask_size = 100 * scale

				context.clearRect(0, 0, this.cansWidth, this.cansHeight)
				context.drawImage(this.avatarPath, 0, 0, this.cansWidth, this.cansHeight)
				context.translate(mask_center_x, mask_center_y)
				context.rotate((rotate * Math.PI) / 180)
				context.drawImage(this.maskPic, -mask_size / 2, -mask_size / 2, mask_size, mask_size)
				context.draw(false, () => this.saveCansToTemp())
			})
		},
		// 保存临时路径
		saveCansToTemp() {
			uni.canvasToTempFilePath({
                x: 0,
                y: 0,
                height: this.cansHeight,
                width: this.cansWidth,
                destWidth: this.cansWidth * 3,
                destHeight: this.cansHeight * 3,
                canvasId: "cans-id-mask",
                success: res => this.saveImageToPhotosAlbum(res.tempFilePath)
            }, this)
		},

        saveImageToPhotosAlbum(filePath) {
            uni.saveImageToPhotosAlbum({
                filePath,
                success: () => this.$uni.showToast('已保存到系统相册'),
                fail: err => {
                    if (err?.errMsg && err.errMsg === 'saveImageToPhotosAlbum:fail cancel') return
                    this.$uni.showToast('保存失败', 'error')
                },
                complete: () => {
                    // 保存后显示插屏广告
                    setTimeout(() => {
                        this.interstitialAdShow = false
                        this.$nextTick(() => this.interstitialAdShow = true)
                    }, 1500)
                }
            })
        },
	}
}
</script>

<style lang="scss">
@import "./pendant.scss";
</style>
