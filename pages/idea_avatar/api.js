import xwy_api from '@/utils/api/xwy_api'

module.exports = {
    imgList: [],
    
    async getImgList(active_id = '') {
        if (this.imgList?.length) return this.imgList
        const data = {}
        if (active_id) data.active_id = active_id
        const res = await xwy_api.request({
            url: 'front.user.image.face_swap.user/headimg_change_tpl_list',
            data
        })
        const imgList = res.data?.['tpl_list']?.headimg_plugin || []
        this.imgList = imgList
        return imgList
    },
    
    
    async getPendantImgList(active_id = '') {
        const img_list = await this.getImgList(active_id)
        return img_list.find(v => v.key === 'chajian')?.img_list?.[0]?.list || []
    },
    
    async getBackgroundImageList(active_id = '') {
        const img_list = await this.getImgList(active_id)
        return img_list.find(v => v.key === 'beijing')?.img_list || []
    }
}