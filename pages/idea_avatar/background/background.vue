<template>
    <view class="content">
        <image :src="background" class="all-back"/>
        <view class="top-content">
            <scroll-view scroll-x :show-scrollbar="false" class="scroll-view">
                <view class="top-title">
                    <view class="title-unit" :class="{ 'title-select': item.selected }"
                          v-for="(item, index) in list" :key="index" @click="itemClick(item)">
                        {{ item.category_name }}
                    </view>
                </view>
            </scroll-view>
            <scroll-view scroll-x :show-scrollbar="false" class="scroll-view">
                <view class="image-div">
                    <image
                        :class="{ 'image-margin': index !== 0 }"
                        @click="imageClick(item, index)"
                        v-for="(item, index) in imageList"
                        :src="item"
                        :key="index"
                    />
                </view>
            </scroll-view>
        </view>

        <view class="image-card">
            <view class="photo-main-view">
                <!--  -->
                <view class="avatar-div " id="avatar-container">
                    <image class="img" id="avatar-img" :src="avatarImage"/>

                    <view class="empty-view " v-if="!avatarImage"></view>

                    <image class="avatar-default " :src="currentFrame" v-if="currentFrame"/>
                </view>

                <view class="ctlbtn">
                    <!-- <view class="icon-div btn-margin">
                        <view class="icon-zuo iconfont" v-if="showSwitch(-1)" @click="switchAvatar(-1)"></view>
                        <view class="icon-you iconfont" v-if="showSwitch(1)" @click="switchAvatar(1)"></view>
                    </view> -->
                    <button class="action-btn btn-margin" open-type="chooseAvatar" 
                            @chooseavatar="getAvatar">获取头像
                    </button>
                    <view class="action-btn btn-margin" @click="chooseImage()">选择图片</view>
                    <view class="action-btn btn-primary" @click="draw()">保存头像</view>
                </view>
            </view>
        </view>

        <view v-if="showAd" class="pt15">
            <xwy-ad v-if="interstitialAdShow" :ad_type="3" :activity_id="active_id"></xwy-ad>
            <xwy-ad :ad_type="3" :activity_id="active_id"></xwy-ad>
            <xwy-ad :ad_type="66" :activity_id="active_id"></xwy-ad>
        </view>

        <view class="hideCanvasView">
            <canvas
                class="hideCanvas"
                id="default_PosterCanvasId"
                canvas-id="default_PosterCanvasId"
                style="width: 300px; height: 300px;"
            ></canvas>
        </view>
        <view class="pageLoading" v-show="loading">
            <full-loading bgColor="#ffffff"/>
        </view>


    </view>
</template>

<script>
import fullLoading from "../components/full-loading.vue"

const app = getApp()
import login from '../../../utils/api/login.js'
import utils from '../../../utils/utils.js'
import ideaAvatarApi from '../api'

export default {
    components: {
        fullLoading
    },
    data() {
        return {
            canvasId: 'default_PosterCanvasId',
            avatarImage: '',
            currentFrame: '',
            currentIndex: 0,
            list: [],
            imageList: [],
            background: "",
            loading: true,
            showAd: false,
            interstitialAdShow: false,
            active_id: ''
        };
    },
    onLoad(params) {
        if (params.id) {
            this.active_id = params.id
            uni.hideShareMenu(undefined)
        }

        login.uniLogin(() => {
            if (!params.closed_AD) this.showAd = true
            
            this.getAvatarPath()
            this.getList()
        })
    },
    
    onShareAppMessage() {
        return {
            title: '头像换背景',
            path: '/pages/idea_avatar/background/background'
        }
    },

    methods: {
        getAvatarPath() {
            const headimg = app.globalData['userinfo'].headimg
            const avatarPath = utils.headimgHD(headimg)
            this.downloadAvatar(avatarPath)
        },

        downloadAvatar(src) {
            uni.downloadFile({
                url: src,
                success: res => {
                    this.avatarImage = res.tempFilePath
                },
                fail: () => this.$uni.showToast('头像加载超时', 'error'),
                complete: () => {
                    this.loading = false
                }
            })
        },

        getImgListEvent() {
            return new Promise(resolve => {
                this.getOpenerEventChannel().on('img_list', img_list => resolve(img_list || null))
            })
        },

        async getList() {
            let img_list
            if (this.active_id) img_list = await this.getImgListEvent()
            if (!img_list?.length) img_list = await ideaAvatarApi.getBackgroundImageList(this.active_id || '')

            if (!img_list?.length) return this.$uni.showModal('获取挂件失败', {
                success: () => uni.navigateBack()
            })

            this.list = img_list
            this.itemClick(this.list[0])
        },


        /*switchAvatar(num) {
            let currentType = this.list.findIndex(data => data.selected)
            if (num > 0) {
                if (this.currentIndex === this.imageList.length - 1) {
                    this.imageList = this.list[currentType + 1].imgList
                    this.list.forEach(data => {
                        data.selected = false
                    });
                    this.list[currentType + 1].selected = true
                    this.currentIndex = 0
                    this.currentFrame = this.imageList[this.currentIndex]
                    this.background = this.list[currentType + 1].background
                } else {
                    this.currentIndex++
                    this.currentFrame = this.imageList[this.currentIndex]
                }
            } else {
                if (this.currentIndex > 0) {
                    this.currentIndex--
                    this.currentFrame = this.imageList[this.currentIndex]
                } else {
                    this.imageList = this.list[currentType - 1].imgList
                    this.list.forEach(data => {
                        data.selected = false
                    })
                    this.list[currentType - 1].selected = true
                    this.currentIndex = this.imageList.length - 1
                    this.currentFrame = this.imageList[this.currentIndex]
                    this.background = this.list[currentType - 1].background
                }
            }
        },
        showSwitch(val) {
            if (this.list.length > 0) {
                let currentType = this.list.findIndex(data => data.selected)
                let res =
                    (val < 0 && currentType <= 0 && this.currentIndex <= 0) ||
                    (val > 0 && currentType >= this.list.length - 1 && this.currentIndex >= this.imageList.length - 1)
                return !res
            } else {
                return false
            }
        },*/

        draw() {
            const context = wx.createCanvasContext(this.canvasId)
            context.drawImage(this.avatarImage, 0, 0, 300, 300)
            context.drawImage(this.currentFrame, 0, 0, 300, 300)

            context.draw(false, () => this.saveCansToTemp())

        },

        // 保存临时路径
        saveCansToTemp() {
            uni.canvasToTempFilePath({
                x: 0,
                y: 0,
                height: 300,
                width: 300,
                destWidth: 300 * 3,
                destHeight: 300 * 3,
                canvasId: this.canvasId,
                success: res => this.saveImageToPhotosAlbum(res.tempFilePath),
                fail: err => console.log(err)
            }, this)
        },

        saveImageToPhotosAlbum(filePath) {
            uni.saveImageToPhotosAlbum({
                filePath,
                success: () => this.$uni.showToast('已保存到系统相册'),
                fail: err => {
                    if (err?.errMsg && err.errMsg === 'saveImageToPhotosAlbum:fail cancel') return
                    this.$uni.showToast('保存失败', 'error')
                },
                complete: () => {
                    // 保存后显示插屏广告
                    setTimeout(() => {
                        this.interstitialAdShow = false
                        this.$nextTick(() => this.interstitialAdShow = true)
                    }, 1500)
                }
            })
        },

        imageClick(item, index) {
            this.currentIndex = index
            this.downloadImage(item)
        },
        itemClick(item) {
            this.currentIndex = 0
            this.list.forEach(data => {
                data.selected = false;
            });
            item.selected = true
            this.imageList = item.list
            this.downloadImage(this.imageList[0])
            this.background = item.background
        },

        downloadImage(src) {
            this.$uni.showLoading('加载中...')

            uni.downloadFile({
                url: src,
                success: res => {
                    this.currentFrame = res.tempFilePath
                },
                fail: () => this.$uni.showToast('加载超时', 'error'),
                complete: () => {
                    this.loading = false
                    uni.hideLoading()
                }
            })
        },

        getAvatar(e) {
            this.avatarImage = e.detail.avatarUrl
        },

        // 选择图片
        chooseImage() {
            uni.chooseImage({
                count: 1,
                sourceType: ["album", "camera"],
                success: res => {
                    this.avatarImage = res.tempFilePaths[0]
                }
            })
        },
    }
};
</script>

<style lang="scss">
@import "./background.scss";
</style>
