<template>
    <view class="flex-row">
        <view v-show="nicknameShow">
            <text class="color-content font14">{{ nickname || '' }}</text>
            <text class="iconfont icon-edit color-content font14 pl10 pr10" @click="toEditNickname"></text>
        </view>
        <uni-icons class="pr10" :type="nicknameShow ? 'eye-slash' : 'eye'" color="#80848f" size="14px"
                   @click="nicknameShow = !nicknameShow"/>
    </view>
</template>

<script>
const app = getApp()

export default {
    name: "nickname-show",
    data() {
        return {
            nickname: app.globalData['userinfo'].nickname,
            nicknameShow: false
        }
    },

    methods: {
        toEditNickname() {
            this.$uni.navigateTo('/pages/user/user_detail?neet_back=1', {
                events: {
                    back: () => {
                        this.nickname = app.globalData['userinfo'].nickname
                    }
                }
            })
        }
    }
}
</script>