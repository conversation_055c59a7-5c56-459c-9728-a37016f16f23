<template>
    <view class="year-month-switch flex-kai font14">
        <view class="flex-row">
            <view class="color-sub">{{ title }}</view>
            <navigator class="color-light-primary pl10" url="/pages/wechat-step/history-step-list">
                历史步数
                <text class="iconfont icon-more color-light-primary font14"></text>
            </navigator>
        </view>
        
        <view class="capsule flex-row text-center">
            <view v-for="item in types" :key="item.type" 
                  :class="['capsule-item', item.type === current ? 'bg-primary color-white' : 'color-light-primary']"
                  @click="yearMonthSwitch(item.type)">
                {{ item.text }}
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "year-month-switch",
    props: {
        title: {
            type: String,
            default: ""
        },
        current: {
            type: String,
            default: "month"
        }
    },
    data() {
        return {
            types: [
                {
                    type: "month",
                    text: "月步数"
                },
                {
                    type: "year",
                    text: "年步数"
                }
            ]
        }
    },

    mounted() {

    },

    methods: {
        yearMonthSwitch(type) {
            if (type === this.current) return
            this.$emit("yearMonthSwitch")
        }
    }
}
</script>

<style lang="scss">
.year-month-switch {
    $height: 32px;
    line-height: $height;

    .capsule {
        width: 150px;
        height: $height;
        border-radius: calc(#{$height} / 2);
        overflow: hidden;
        border: 1px solid #2d8cf0;

        .capsule-item {
            width: 50%;
        }
    }
}
</style>