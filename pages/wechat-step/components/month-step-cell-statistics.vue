<template>
    <view class="step-cell-statistics">
        <view class="clear clearfix">
            <view v-if="month" class="month-title color-content fl">
                <template v-if="year !== this_year">{{ year || '' }}年</template>
                {{ month || '' }}月 步数统计
            </view>

            <view class="fr">
                <nickname-show/>
            </view>
        </view>
        <view class="cell-list flex-row flex-wrap">
            <view v-for="(item, index) in cellList" :key="index" class="cell-item" 
                  @click="clickCellItem(item.trigger)">
                <view class="cell-title">
                    <text :class="'iconfont ' + item.icon" :style="{color: item.color}"></text>
                    <text>{{ item.title }}</text>
                    <text v-if="item.remark" class="remark">{{ item.remark }}</text>
                    <view v-if="item.questionMark" class="question-mark">?</view>
                </view>
                <view>
                    <text class="value">{{ item.value }}</text>
                    <text class="unit">{{ item.unit }}</text>
                </view>
            </view>
        </view>
        
        
        <view v-if="averageReport" class="average-report-ellipsis ellipsis--l3" 
              @click="averageReportPopupOpen">
            <text class="color-content font14">{{ averageReport }}</text>
        </view>
        
        <uni-popup ref="reportPopup" @touchmove.stop.prevent="">
            <view class="report-popup bg-white radius10">
                <view class="text-center color-title pb10">日均步数分析</view>
                

                <scroll-view class="report-content" :scroll-y="true">
                    <view class="stop-show">
                        <view class="step-container">
                            <view class="stop-content text-center">
                                <view class="color-sub font12">{{ month }}月日均步数</view>
                                <view>
                                    <template>
                                        <text v-if="averageStep >= 10000"
                                              class="step-value font24 color-warning">
                                            {{ averageStep }}
                                        </text>
                                        <text v-else class="step-value font24 color-success">
                                            {{ averageStep }}
                                        </text>
                                    </template>
                                    
                                    <text class="color-sub font12">步</text>
                                </view>
                            </view>
                        </view>
                    </view>
                    
                    <view class="report-content-text">
                        <text class="color-content">{{ averageReport }}</text>
                    </view>
                </scroll-view>
            </view>
            <view class="flex-all-center" @click="popupClose('reportPopup')">
                <uni-icons type="close" size="28" color="#ffffff"/>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import nicknameShow from './nickname-show.vue'

export default {
    name: "month-step-cell-statistics",
    components: {nicknameShow},
    props: ['allStep', 'year', 'month', 'stepList', 'report'],
    emits: ['reportShow', 'reportHide'],
    data() {
        return {
            this_year: new Date().getFullYear()
        }  
    },
    
    computed: {
        allStepText() {
            const step = this.allStep
            return step >= 10000 ? `${Math.floor(step / 1000) / 10}万` : `${step}`
        },
        
        averageStep() {
            if (!this.allStep || !this.stepList?.length) return 0
            return Math.floor(this.allStep / this.stepList.length)
        },
        
        minDay() {
            const step_list = this.stepList || []
            if (!step_list.length) return {day: 1, step: 0}
            const min_step = Math.min(...step_list.map(item => item.info))
            const { date, info: step } = step_list.find(v => v.info === min_step)
            return {
                day: Number(date.split('-')[2]),
                step
            }
        },
        
        maxDay() {
            const step_list = this.stepList || []
            if (!step_list.length) return {day: 1, step: 0}
            const max_step = Math.max(...step_list.map(item => item.info))
            const { date, info: step } = step_list.find(v => v.info === max_step)
            return {
                day: Number(date.split('-')[2]),
                step
            }
        },

        kilometer() {
            return this._utils.step2kilometer(this.allStep)
        },

        carbonEmission() {
            const kilometer2carbon_emission = 270   // 汽车1公里碳排放(克)
            return Math.floor(this.allStep / kilometer2carbon_emission)
        },
        
        averageReport() {
            const {finally_text, step_list} = this.report || {}
            if (!step_list?.length) return ''
            const step = this.averageStep
            
            const report = step_list.find(item => step >= item.min && step <= item.max)?.tips
            if (!report) return ''
            return `日均${step}步。${report}\n\n${finally_text || ''}`
        },
        
        cellList() {
            const {allStepText, kilometer, carbonEmission, averageStep, maxDay, minDay} = this
            
            return [
                {
                    title: '步数',
                    icon: 'icon-wechat-movement',
                    color: '#5C6BE9',
                    value: allStepText,
                    unit: '步'
                },
                {
                    title: '里程',
                    icon: 'icon-rocket',
                    color: '#0BB56E',
                    value: kilometer,
                    unit: '公里'
                },
                {
                    title: '减碳',
                    icon: 'icon-earth',
                    color: '#FBA755',
                    value: carbonEmission,
                    unit: '克'
                },
                {
                    title: '日均',
                    icon: 'icon-footnotes',
                    color: '#815BFB',
                    value: averageStep,
                    unit: '步',
                    questionMark: true,
                    trigger: 'averageReportPopupOpen'
                },
                {
                    title: '最高',
                    remark: `(${maxDay.day}号)`,
                    icon: 'icon-to-top',
                    color: '#51AAF1',
                    value: maxDay.step || 0,
                    unit: '步'
                },
                {
                    title: '最低',
                    remark: `(${minDay.day}号)`,
                    icon: 'icon-to-down',
                    color: '#F5575B',
                    value: minDay.step || 0,
                    unit: '步'
                }
            ]
        }
    },

    methods: {
        clickCellItem(trigger) {
            trigger && this[trigger]()
        },
        
        averageReportPopupOpen() {
            if (!this.averageReport) return
            this.popupOpen('reportPopup')
        },
        
        popupOpen(ref) {
            this.$emit('reportShow')
            this.$refs[ref].open()
        },
        
        popupClose(ref) {
            this.$refs[ref].close()
            this.$emit('reportHide')
        }
    }
}
</script>

<style lang="scss">
.step-cell-statistics {
    padding: 20px 0 10px;

    .month-title {
        padding-left: 24rpx;
    }

    .cell-list {
        margin: 0 12rpx;

        .cell-item {
            width: 218rpx;
            padding: 10px;
            margin: 12rpx;
            box-sizing: border-box;
            border-radius: 5px;
            background-color: #fff;
            
            .cell-title {
                font-size: 14px;
                color: #80848f;
                line-height: 24px;
                padding-bottom: 10px;

                .iconfont {
                    font-size: 18px;
                    padding-right: 2px;
                    position: relative;
                    top: 1px;
                }

                .remark {
                    font-size: 12px;
                    padding-left: 2px;
                    color: #80848f;
                }
                
                .question-mark {
                    display: inline-block;
                    font-size: 12px;
                    color: #bbbec4;
                    width: 14px;
                    height: 14px;
                    line-height: 14px;
                    text-align: center;
                    border: 1px solid #bbbec4;
                    border-radius: 8px;
                    position: relative;
                    top: -5px;
                    left: 2px;
                    transform: scale(.8);
                }
            }
            
            .value {
                font-size: 18px;
                font-weight: 600;
            }
            
            .unit {
                font-size: 14px;
                color: #80848f;
                padding-left: 2px;
            }
        }
    }
    
    .average-report-ellipsis {
        margin: 10px 24rpx;
        border: 10px solid #fff;
        border-radius: 5px;
        background-color: #fff;
    }
}

.report-popup {
    padding: 10px;
    width: 85vw;
    max-width: 400px;

    .report-content {
        height: 70vh;

        .report-content-text {
            line-height: 26px;
        }
    }
    
    $step-size: 160px;
    .stop-show {
        width: $step-size;
        height: calc(#{$step-size} / 2);
        margin: 30px auto;
        overflow: hidden;

        .step-container {
            width: $step-size;
            height: $step-size;
            border-radius: 50%;
            border: 12px solid #f90;
            box-sizing: border-box;

            .stop-content {
                height: calc(#{$step-size} / 2 - 12px);
                display: flex;
                flex-direction: column-reverse;
                
                .step-value {
                    font-weight: 600;
                }
            }
        }
    }
}
</style>