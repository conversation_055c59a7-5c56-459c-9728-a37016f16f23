<template>
    <view class="month-step-chart bg-white">
        <view class="p10">
            <text class="color-title">每日{{ activeId ? '兑换' : '' }}步数趋势</text>
            <text class="font12 color-sub pl5">({{ year }}年{{ month }}月)</text>
        </view>

        <!--<view class="charts-box">
            <qiun-data-charts type="mix" :opts="opts" :chartData="chartData" :disableScroll="true" :ontouch="true"/>
        </view>-->
        <canvas canvas-id="myid" id="myid" class="charts" @tap="tap" @touchstart="touchstart"
                @touchmove="touchmove" @touchend="touchend"/>
    </view>
</template>

<script>
// 使用原生方法才能自定义showToolTip弹窗内容
import uCharts from "@/uni_modules/qiun-data-charts/js_sdk/u-charts/u-charts"
const uChartsInstance = {}

export default {
    name: "month-step-chart",

    props: ['activeId', 'stepList', 'year', 'month'],

    data() {
        return {
            step_list: [],
            chartData: {},
            opts: {
                color: ["#3CA272"],
                background: 'rgba(255,255,255, 1)',
                padding: [15, 10, 10, 0],
                enableScroll: true,
                legend: {
                    show: false
                },
                xAxis: {
                    disableGrid: true,
                    itemCount: 7,
                    scrollShow: true,
                    scrollAlign: 'left',
                    fontSize: 12,
                    scrollColor: '#3CA272'
                },
                yAxis: {
                    disabled: false,
                    disableGrid: true,
                    splitNumber: 5,
                    gridType: "dash",
                    dashLength: 4,
                    gridColor: "#CCCCCC",
                    padding: 10,
                    showTitle: false,
                    data: [
                        {
                            disabled: true,
                            position: "left",
                            title: '步',
                            fontSize: 12,
                            min: 0
                        },
                        {
                            disabled: true
                        }
                    ]
                },
                extra: {
                    mix: {
                        column: {
                            width: 20,
                            barBorderCircle: true
                        }
                    },
                    tooltip: {
                        legendShow: false
                    }
                }
            },

            cWidth: uni.upx2px(750),
            cHeight: uni.upx2px(500)
        }
    },

    watch: {
        stepList: {
            handler(newVal) {
                this.setChartData(newVal)
            },
            deep: true,
            immediate: true
        }
    },

    computed: {
        monthDays() {
            const {year, month} = this
            const days = new Date(year, month, 0).getDate()
            const pad0 = num => num.toString().padStart(2, '0')
            const _month = pad0(month)
            return Array.from({length: days}, (_, i) => ({
                date: `${year}-${_month}-${pad0(i + 1)}`,
                day: i + 1
            }))
        }
    },

    methods: {
        setChartData(data) {
            const step_list = this.monthDays.map(item => ({
                ...item,
                step: data.find(i => i.date === item.date)?.info || 0
            }))

            let res = {
                categories: step_list.map(item => `${this.month}/${item.day}`),
                series: [
                    {
                        name: "",
                        type: "column",
                        data: step_list.map(item => ({
                            value: item.step,
                            color: item.step >= 10000 ? '#ff9900' : '#02b148'
                        }))
                    },

                    {
                        name: "",
                        type: "line",
                        style: "curve",
                        disableLegend: true,
                        data: step_list.map(item => item.step)
                    },

                    // 讨论通过不要曲面图 - 企业微信大群 2025-06-11 18:37
                    /*{
                        name: "",
                        type: "area",
                        style: "curve",
                        color: '#3CA272',
                        data: step_list.map(item => item.step)
                    },*/
                ]
            }
            this.chartData = JSON.parse(JSON.stringify(res))

            this.drawCharts(res)
        },

        drawCharts(data) {
            const ctx = uni.createCanvasContext('myid', this)
            uChartsInstance['myid'] = new uCharts({
                type: "mix",
                context: ctx,
                width: this.cWidth,
                height: this.cHeight,
                categories: data.categories,
                series: data.series,

                ...this.opts
            });
        },

        tap(e) {
            // 格式化ToolTip
            let isSet = false
            uChartsInstance[e.target.id].showToolTip(e, {
                formatter: (item, category) => {
                    /*console.log(item)
                    console.log(category)
                    console.log(index)
                    console.log(opts)*/

                    // 因为混合图有多个数据，这里只要显示一个就行了
                    if (isSet) return ''
                    isSet = true

                    const week = this._utils.getWeek(`${this.year}/${category}`)
                    return `${category} (${week}): ${item.data.value}步`
                }
            })
        },

        touchstart(e) {
            uChartsInstance[e.target.id].scrollStart(e)
        },
        touchmove(e) {
            uChartsInstance[e.target.id].scroll(e)
        },
        touchend(e) {
            uChartsInstance[e.target.id].scrollEnd(e)
            uChartsInstance[e.target.id].touchLegend(e)
        }
    }
}
</script>

<style lang="scss" scoped>
.month-step-chart {
    margin-top: 10px;
    padding-bottom: 10px;
}

.charts-box {
    width: 100%;
    height: 300px;
}

.charts {
    width: 750rpx;
    height: 500rpx;
}
</style>