<template>
    <uni-popup ref="popup" type="center" :is-mask-click="false">
        <view class="popup-container">
            <!-- 弹窗内容 -->
            <view class="popup-content">
                <!-- 标题 -->
                <view class="title">订阅同步步数消息提醒</view>
                <view class="subtitle">避免忘记同步步数</view>

                <view class="color-sub font14">每月月末发送消息提醒同步每月步数</view>

                <!-- 剩余次数显示 -->
                <view class="remaining-section">
                    <text class="remaining-text">你还可以接收</text>
                    <text class="remaining-count">{{ remainingTimes }}</text>
                    <text class="remaining-unit">次</text>
                    <text class="remaining-text">提醒</text>
                </view>


                <!-- 操作按钮 -->
                <view class="action-btn" @click="requestSubscribeMessage">增加提醒次数</view>
            </view>
        </view>

        <!-- 关闭按钮 -->
        <view class="flex-all-center">
            <view class="close-btn" @click="close">
                <uni-icons type="close" size="34" color="#ffffff"/>
            </view>
        </view>
    </uni-popup>
</template>

<script>
export default {
    name: "subscribe-message-popup",

    props: {
        templateId: {
            type: String,
            required: true
        },
        templateType: {
            type: Number,
            required: true
        }
    },

    data() {
        return {
            remainingTimes: 0
        }
    },

    mounted() {
        this.queryRemainingTimes()
    },

    methods: {
        // 显示弹窗
        show() {
            this.$refs.popup.open()
        },

        // 关闭弹窗
        close() {
            this.$refs.popup.close()
        },

        // 查询剩余订阅次数
        async queryRemainingTimes() {
            const res = await this.xwy_api.request({
                url: 'front.user.wechat.subscribe.subscribeTemp/subscribe_details',
                data: {
                    temp_id: this.templateId
                }
            })

            const times = res?.data?.details?.times

            // 这里一定不能使用this.remainingTimes = times || 0
            // 因为如果是更新的时候，接口没有查询到会将之前的次数覆盖为0
            if (times) this.remainingTimes = times
        },

        // 请求订阅消息权限
        requestSubscribeMessage() {
            if (this.inRequest) return
            this.inRequest = true

            wx.requestSubscribeMessage({
                tmplIds: [this.templateId],
                success: res => {
                    console.log('requestSubscribeMessage success', res)
                    if (res[this.templateId] === 'accept') this.subscribeMessageSuccess()
                },
                fail: err => {
                    console.error('requestSubscribeMessage error', err)
                    this.$uni.showModal(err?.errMsg || '订阅失败，请重试', {title: '订阅失败'})
                },
                complete: () => {
                    this.inRequest = false
                }
            })
        },

        // 订阅成功处理
        async subscribeMessageSuccess() {
            this.$uni.showLoading('订阅中...')
            const res = await this.xwy_api.request({
                url: 'front.user.wechat.subscribe.subscribeTemp/subscribe_temp',
                data: {
                    temp_id: this.templateId,
                    types: this.templateType
                }
            })

            if (res?.status !== 1) {
                uni.hideLoading()
                await this.$uni.showModal(res?.info || '订阅失败，请重试', {title: '订阅失败'})
                return
            }

            await this.queryRemainingTimes()

            uni.hideLoading()
        }
    }
}
</script>

<style lang="scss" scoped>
.popup-container {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 0;
    position: relative;
    width: 600rpx;
    max-width: 90vw;
}

.close-btn {
    padding: 5px;
}

.popup-content {
    padding: 60rpx 40rpx 40rpx;
    text-align: center;
}

.title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 16rpx;
    line-height: 1.4;
}

.subtitle {
    font-size: 36rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 60rpx;
    line-height: 1.4;
}

.remaining-section {
    margin-bottom: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 8rpx;

    .remaining-text {
        font-size: 32rpx;
        color: #666666;
    }

    .remaining-count {
        font-size: 72rpx;
        font-weight: bold;
        color: #52C41A;
        margin: 0 8rpx;
    }

    .remaining-unit {
        font-size: 32rpx;
        color: #666666;
    }
}

.action-btn {
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: #ffffff;
    font-size: 32rpx;
    font-weight: bold;
    padding: 28rpx 0;
    border-radius: 12rpx;
    text-align: center;

    &:active {
        opacity: 0.8;
    }
}
</style>