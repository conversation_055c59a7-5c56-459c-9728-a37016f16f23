<template>
    <view>
        <view class="statistics bg-white">
            <view class="p10">
                <text class="color-title">{{ activeId ? '兑换' : '' }}步数分布统计</text>
                <text class="font12 color-sub pl5">({{ year }}年{{ month }}月)</text>
            </view>

            <qiun-data-charts type="pie" :opts="opts" :chartData="chartData"/>

            <view class="statistics-item flex-kai" v-for="(item, index) in stepDaysList" :key="index"
                  :style="{color: item.color}">
                <view class="statistics-item-title font12 text-right" @click="showStepTips(item)">
                    <text>{{ item.title }}</text>
                    <text class="iconfont icon-question-mark-up font12 color-sub"></text>
                </view>
                <view class="statistics-item-line-bg bg-background">
                    <view class="statistics-item-line-count"
                          :style="{width: `calc(100% / ${days} * ${item.value})`, backgroundColor: item.color}"
                    ></view>
                </view>
                <view class="statistics-item-count">
                    <text>{{ item.value }}</text>
                    <text class="font12">天</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
// const COLORS = ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"]
const COLORS = ["#EE6666", "#FC8452", "#FAC858", "#1890FF", "#3CA272", "#91CB74", "#73C0DE"]

export default {
    name: "step-distribution",
    props: ['activeId', 'year', 'month', 'list', 'days'],
    data() {
        return {
            chartData: {},
            opts: {
                legend: {
                    show: false
                },
                // color: COLORS,
                padding: [5, 5, 5, 5],
                enableScroll: false,
                extra: {
                    pie: {
                        activeOpacity: 0.5,
                        activeRadius: 10,
                        offsetAngle: 0,
                        labelWidth: 15,
                        border: true,
                        borderWidth: 2,
                        borderColor: "#FFFFFF",
                        // linearType: "custom"
                    }
                }
            },

            stepDaysList: []
        }
    },

    watch: {
        list: {
            handler(value) {
                this.setData(value)
            },
            deep: true,
            immediate: true
        }
    },

    methods: {
        setData(value) {
            const chartData = [], stepDaysList = []

            value.forEach((item, index) => {
                chartData.push({
                    name: item.title,
                    value: item.value,
                    labelText: `${item.value}天`,
                    color: COLORS[index]
                })

                stepDaysList.push({...item, color: COLORS[index]})
            })

            this.$nextTick(() => {
                this.chartData = {
                    series: [
                        {
                            data: chartData
                        }
                    ]
                }

                this.stepDaysList = stepDaysList
            })
        },

        showStepTips(item) {
            this.$uni.showModal(item.tips + (item.value ? ` (${item.value}天)` : ''), {title: item.title})
        }
    }
}
</script>

<style lang="scss" scoped>
.statistics {
    margin-top: 10px;

    .statistics-item {
        padding: 0 10px;
        height: 32px;
        line-height: 32px;

        .statistics-item-title {
            width: 80px;
        }

        .statistics-item-line-bg, .statistics-item-line-count {
            height: 16px;
            border-radius: 8px;
        }

        .statistics-item-line-bg {
            width: calc(100% - 125px);
            margin: 8px 0;
            position: relative;
        }

        .statistics-item-line-count {
            position: absolute;
            top: 0;
            left: 0;
        }

        .statistics-item-count {
            width: 35px;
        }
    }

    .charts-box {
        width: 100%;
        height: 300px;
    }
}
</style>