<template>
    <view class="page bg-background">
        <view class="user-info flex-row bg-white">
            <view v-if="headimg" class="headimg">

                <image class="headimg-image" :src="headimg" mode="aspectFill"/>
            </view>
            <view>
                <view class="ellipsis color-title">{{ nickname || '' }}</view>
                <view class="pt5">
                    <view class="color-sub font14">
                        <template>
                            <template v-if="year && month">{{ year }}年{{ month }}月</template>
                            <template v-else>近30天</template>
                        </template>
                        累计
                        <template>
                            <template v-if="day_30_step > 10000">
                                {{ Number(day_30_step / 10000).toFixed(1) }}<text class="font12">万</text>
                            </template>
                            <template v-else>{{ day_30_step }}</template>
                        </template>
                        <text class="font12">步</text>
                        <template v-if="day_30_kilometer">
                            （约{{ day_30_kilometer }}<text class="font12">公里</text>）
                        </template>
                    </view>
                    <view v-if="day_30_average_step" class="color-sub font14">
                        平均每天行走{{ day_30_average_step }}<text class="font12">步</text>
                    </view>
                </view>
            </view>
        </view>
        
        <xwy-ad v-if="!loading" :ad_type="3"/>
        
        <view class="clearfix clear p10" style="padding-bottom: 0;">
            <view class="calendar-filter bg-white fr flex-row" @click="toChangeMonth">
                <view class="icon-container flex-all-center">
                    <text :class="['iconfont', 'icon-calendar-' + (month || 12), 'font18', year && month ? 'color-light-primary' : 'color-sub']"></text>
                </view>
                <view class="date-container flex-all-center">
                    <view v-if="year && month" class="color-light-primary">{{ year }}年{{ month }}月</view>
                    <view v-else class="color-sub font14">指定月份</view>
                </view>
                <template>
                    <view v-if="year && month"  class="icon-container flex-all-center"
                          @click.stop="clearYearMonth">
                        <text class="iconfont icon-wrong color-sub"></text>
                    </view>
                    <view v-else class="icon-container flex-all-center">
                        <text class="iconfont icon-more color-disabled font18"></text>
                    </view>
                </template>
            </view>
        </view>

        <uni-popup ref="yearMonthPopup" type="bottom" :safe-area="false">
            <view class="year-month-popup bg-white">
                <view class="year-switch flex-all-center">
                    <view class="pl10 pr10" @click="yearChange('--')">
                        <uni-icons type="back" size="18"
                                   :color="popup_year === years[0] ? '#bbbec4' : '#80848f'"/>
                    </view>

                    <picker :range="years" :value="years.findIndex(v => v === popup_year)"
                            @change="yearChange($event)">
                        <view class="text-center" style="width: 80px;">{{ popup_year }}</view>
                    </picker>
                    <view class="pl10 pr10" @click="yearChange('++')">
                        <uni-icons type="forward" size="18"
                                   :color="popup_year === years[years.length - 1] ? '#bbbec4' : '#80848f'"/>
                    </view>
                </view>

                <view class="flex-row flex-wrap pb10">
                    <view
                        class="month-step-item text-center"
                        hover-class="navigator-hover"
                        v-for="month in months"
                        :key="month"
                        @click="lookMonthStep(month)"
                    >
                        <view class="pb5 color-content">
                            <text class="font34 color-light-primary">{{ month }}</text>
                            <text class="color-sub">月</text>
                        </view>
                    </view>
                </view>
                
                <view class="flex-all-center">
                    <view class="p10 color-sub font14" @click="$refs.yearMonthPopup.close()">关闭</view>
                </view>
            </view>
        </uni-popup>
        
        <view class="step-list">
            <view v-for="(item, index) in step_list" :key="index">
                <view class="step-item">
                    <view>
                        <text @click="changeGodModel">{{ item.date }}</text>
                        <text class="font12 color-sub pl5">{{ item.week }}</text>
                    </view>
                    
                    <view class="step-item-info bg-white clear clearfix">
                        <view class="step-item-info-step fl">
                            <text class="font24" :style="{color: item.color}">{{ item.step }}</text>
                            <text class="unit">步</text>
                        </view>
                        
                        <view class="step-item-info-other fr text-right">
                            <view class="color-content">
                                {{ item.kilometer }} <text class="unit">公里</text>
                            </view>
                            <view class="color-content">
                                {{ item.calorie }} <text class="unit">千卡</text>
                            </view>
                            <view v-if="god_model" class="color-sub font12">{{ item.create_time }}</view>
                        </view>
                    </view>
                </view>

                <xwy-ad v-if="(index + 1) % 10 === 0" :ad_type="66"/>
            </view>
        </view>
        
        
        <view v-if="loading" class="text-center">
            <view v-if="current_page === 1" style="height: 30vh;"></view>
            <load-ani/>
        </view>

        <view v-if="!loading && !step_list.length" class="text-center" style="padding-top: 15vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub font14">暂无步数记录</view>
        </view>
    </view>
</template>

<script>
const app = getApp()

export default {
    data() {
        return {
            loading: true,
            step_list: [],
            current_page: 1,
            is_last_page: false,
            nickname: '',
            headimg: '',
            day_30_step: 0,
            day_30_average_step: 0,
            day_30_kilometer: 0,
            years: [],
            months: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
            year: null,
            month: null,
            popup_year: null,
            popup_month: null,
            god_model: false
        }
    },

    onLoad() {
        this.getYears()
        this.$login.uniLogin(() => {
            this.getUserInfo()
            this.reloadStepList()
        })
    },
    
    onReachBottom() {
        if (this.loading || this.is_last_page) return
        this.current_page++
        this.getStepList()
    },

    methods: {
        changeGodModel() {
            this.god_model_timeout && clearTimeout(this.god_model_timeout)
            this.god_model_click_count = this.god_model_click_count || 0
            this.god_model_click_count++
            this.god_model_timeout = setTimeout(() => {
                this.god_model_click_count = 0
                clearTimeout(this.god_model_timeout)
            }, 2000)
            if (this.god_model_click_count >= 3) {
                this.god_model = !this.god_model
                this.god_model_click_count = 0
                clearTimeout(this.god_model_timeout)
            }
        },

        getYears() {
            const startYear = 2022
            const endYear = new Date().getFullYear()
            this.years = Array.from({length: endYear - startYear + 1}, (v, i) => startYear + i)  
        },
        
        getUserInfo() {
            const {nickname, headimg} = app.globalData['userinfo']
            this.nickname = nickname || ''
            this.headimg = headimg || ''
        },

        toChangeMonth() {
            let popup_year = this.popup_year || this.year, popup_month = this.popup_month || this.month
            if (!popup_year || !popup_month) {
                const {year, month} = this._utils.getYearMonthDay()
                popup_year = year
                popup_month = month
            }
                
            this.popup_year = popup_year
            this.popup_month = popup_month
            this.$nextTick(() => this.$refs.yearMonthPopup.open())
        },

        yearChange(e) {
            if (e === '++') {
                if (this.popup_year === this.years[this.years.length - 1]) return false
                this.popup_year++
            } else if (e === '--') {
                if (this.popup_year === this.years[0]) return false
                this.popup_year--
            } else {
                this.popup_year = this.years[e.detail.value]
            }
        },

        lookMonthStep(month) {
            this.popup_month = month
            this.year = this.popup_year
            this.month = month
            this.$refs.yearMonthPopup.close()
            
            this.reloadStepList()
        },

        clearYearMonth() {
            this.year = null
            this.month = null

            this.reloadStepList()
        },
        
        async reloadStepList() {
            this.current_page = 1
            this.is_last_page = false
            this.step_list = []
            
            await this.getStepList()
        },
        
        async getStepList() {
            this.loading = true

            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace res.data.mobile_step_list */
            
            const data = {
                page: this.current_page,
                perpage: 30
            }
            if (this.year && this.month) {
                data.year = this.year
                data.month = this.month
            }
            
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.step/user_mobile_step_list',
                data
            })
            this.loading = false
            
            const _data = res?.data?.mobile_step_list
            if (!_data) {
                this.is_last_page = true
                return
            }

            let day_30_step = 0, day_30_day = 0
            const today = new Date(this._utils.getDay(0, true, '/')).getTime() / 1000
            const one_day = 24 * 60 * 60
            const last_day = today - one_day * 30
            
            const daily_step_goal = Number(uni.getStorageSync('daily_step_goal') || this.xwy_config.daily_step_goal)
            
            let list = (_data.data || []).map(item => {
                const {num, date_time} = item
                
                // 因为只要最近30天的，每页加载30条数据，所以第一页就已经够30天了
                if (this.current_page === 1) {
                    if (date_time >= last_day || (data.year && data.month)) {
                        day_30_step += num
                        day_30_day ++
                    }
                }
                
                const thisYear = new Date().getFullYear()
                const date = new Date(date_time * 1000)
                const year = date.getFullYear(), month = date.getMonth() + 1, day = date.getDate()
                
                return {
                    step: num,
                    calorie: this._utils.step2calorie(num),
                    kilometer: this._utils.step2kilometer(num, 2),
                    date: `${thisYear === year ? '' : year + '年'}${month}月${day}日`,
                    color: num < daily_step_goal ? '#19be6b' : '#ff9900',
                    week: this._utils.getWeek(date),
                    create_time: item.create_time
                }
            })
            
            // 按年月查询的，需要从1号开始显示
            if (data.year && data.month) list = list.reverse()
            
            this.step_list = this.step_list.concat(list)
            this.is_last_page = _data.is_lastpage

            if (this.current_page === 1) {
                this.day_30_step = day_30_step
                this.day_30_kilometer = this._utils.step2kilometer(day_30_step, 2)
                this.day_30_average_step = Math.floor(day_30_step / day_30_day)
            }
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
}

.user-info {
    padding: 15px 10px;
    
    $headimg-size: 70px;
    
    .headimg, .headimg-image {
        width: $headimg-size;
        height: $headimg-size;
        border-radius: 50%;
        display: block;
    }

    .headimg {
        padding-right: 10px;
    }
}

.calendar-filter {
    border-radius: 10px;
    line-height: 20px;
    
    $size: 36px;
    
    .icon-container {
        width: $size;
        height: $size;
    }
    
    .date-container {
        height: $size;
    }
}


.year-month-popup {
    padding: 10px 0;
    .year-switch {
        height: 40px;
        line-height: 40px;
    }
    .month-step-item {
        width: calc(100% / 4);
        padding: 10px 0;
        box-sizing: border-box;
    }
}


.unit {
    font-size: 12px;
    padding-left: 2px;
    color: #80848f;
}

.step-list {
    .step-item {
        padding: 15px;
        
        .step-item-info {
            margin: 8px 0;
            padding: 15px;
            border-radius: 10px;
            
            .step-item-info-step {
                line-height: 44px;
            }
            
            .step-item-info-other {
                line-height: 22px;
            }
        }
    }
}
</style>