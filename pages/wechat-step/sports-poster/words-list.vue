<template>
    <view>
        <view class="p10">
            <uni-easyinput v-model="words" type="textarea" placeholder="请输入或选择短语"/>
        </view>
        <view class="list">
            <view class="item" v-for="(item, index) in list" :key="index">
                <view class="color-content" @click="words = item">{{ item }}</view>
            </view>
        </view>

        <view class="confirm">
            <view class="bg-primary color-white text-center" hover-class="navigator-hover" @click="confirm">
                确定
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
export default {
    data() {
        return {
            words: '',
            list: []
        }
    },
    onLoad(params) {
        this.$uni.showLoading()
        if (params.words) this.words = decodeURIComponent(params.words)
        this.getClockingMoodConfig()
    },
    methods: {
        async getClockingMoodConfig() {
            if (app.globalData['tempData'].sportsPosterConfig?.wordsList) {
                this.list = app.globalData['tempData'].sportsPosterConfig.wordsList
                return uni.hideLoading()
            }

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.dailySign/morning_pic_share_set'
            })

            const words_list = res?.data?.['words_set']?.words_list || [],
                pic_list = res?.data?.['pic_set'] || []

            this.list = words_list

            app.globalData.sportsPosterConfig = {
                imageList: pic_list,
                wordsList: words_list
            }

            uni.hideLoading()
        },

        confirm() {
            this.getOpenerEventChannel?.()?.emit?.('wordsChange', this.words)
            uni.navigateBack()
        }
    }
}
</script>

<style>
.list {
    height: calc(100vh - 200px);
    overflow: auto;
}

.item {
    padding: 20px 10px;
}

.confirm {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 20px 10px;
    box-sizing: border-box;
}

.confirm view {
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
}
</style>
