<template>
    <view>
        <view class="list flex-row flex-wrap">
            <view class="item bg-background flex-all-center" hover-class="navigator-hover"
                  @click="chooseImage">
                <uni-icons type="plusempty" color="#cecece" size="30"/>
            </view>
            
            <view class="item" v-for="(item, index) in list" :key="index" hover-class="navigator-hover"
                  @click="confirm(item)">
                <image class="image" :src="item" mode="aspectFill"/>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
export default {
    data() {
        return {
            list: []
        }
    },
    onLoad() {
        this.$uni.showLoading()
        this.getClockingMoodConfig()
    },
    methods: {
        async getClockingMoodConfig() {
            if (app.globalData['tempData'].sportsPosterConfig?.imageList) {
                this.list = app.globalData['tempData'].sportsPosterConfig.imageList
                return uni.hideLoading()
            }
            
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.dailySign/morning_pic_share_set'
            })

            const words_list = res?.data?.['words_set']?.words_list || [],
                pic_list = res?.data?.['pic_set'] || []
            
            this.list = pic_list
            
            app.globalData.sportsPosterConfig = {
                imageList: pic_list,
                wordsList: words_list
            }
            
            uni.hideLoading()
        },
        
        
        chooseImage() {
            uni.chooseImage({
                count: 1,
                success: res => this.confirm(res.tempFilePaths[0])
            })
        },

        confirm(src) {
            this.getOpenerEventChannel?.()?.emit('imageChange', src)
            uni.navigateBack()
        }
    }
}
</script>

<style>
.item {
    margin: 5px;
}

.item, .image {
    width: calc((100vw - 30px) / 3);
    height: calc((100vw - 30px) / 3);
    border-radius: 5px;
}

.image {
    display: block;
}
</style>
