<template>
    <view>
        <canvas class="canvas" canvas-id="canvas" id="canvas"
                :style="{width: background_original_size.width + 'px', height: background_original_size.height + 'px'}"
        ></canvas>
        
        <view class="poster-container" :style="{color: words_color}" @click="preview">
            <view class="edit-button" @click.stop="$refs.setPopup.open()">
                <text class="iconfont font14 icon-edit color-white"></text>
                <text class="font14 pl5 color-white">编辑</text>
            </view>
            

            <image class="background-image" :src="background_image" mode="widthFix" 
                   @load="backgroundImageLoad" 
                   :style="{width: background_size.width + 'px', height: background_size.height + 'px'}"/>
            
            <view v-show="headimgShow">

                <image class="headimg-image" :src="headimg" mode="scaleToFill"/>
            </view>
            
            <view v-show="open_set.date" class="date">{{ date }}</view>
            
            <view v-show="words" class="words">{{ words }}</view>
            
            <view v-show="open_set.today_step" class="today-step">
                <view class="font18">今日步数</view>
                <view class="font20">{{ today_step }}</view>
            </view>

            <view v-show="open_set.month_step" class="month-step">
                <view class="font16">本月总步数</view>
                <view class="font18">{{ month_step }}</view>
            </view>

            <view v-show="open_set.average_step" class="average-step">
                <view class="font16">本月平均步数</view>
                <view class="font18">{{ average_step }}</view>
            </view>


            <image v-if="qrcode_src" class="qrcode-image" :src="qrcode_src" mode="scaleToFill"/>
        </view>

        <view class="font14 color-sub text-center">点击图片预览，预览可长按保存到相册或转发给好友</view>

        <view class="save-image-button color-white bg-light-primary text-center"
              hover-class="navigator-hover" @click="saveImage">
            保存海报到相册
        </view>

        <uni-popup ref="setPopup" type="center">
            <view class="set-popup">
                <view class="text-center">海报设置</view>
                <view class="flex-row p10">
                    <view class="set-label-short">文案</view>
                    <view class="words-set" @click="wordsChange">{{ words }}</view>
                </view>
                <view class="flex-row p10">
                    <view class="set-label-short">图片</view>

                    <image class="bg-set-image" :src="background_image" mode="aspectFill"/>
                    <view class="flex-all-center" @click="backgroundChange">
                        <view class="color-primary pl10">更换</view>
                    </view>
                </view>
                <view class="flex-row p10">
                    <view class="set-label-long">头像</view>
                    <view>

                        <switch color="#2d8cf0" :checked="open_set.headimg"
                                @change="open_set.headimg = $event.detail.value"/>
                    </view>
                </view>
                <view class="flex-row p10">
                    <view class="set-label-long">日期</view>
                    <view>

                        <switch color="#2d8cf0" :checked="open_set.date"
                                @change="open_set.date = $event.detail.value"/>
                    </view>
                </view>
                <view class="flex-row p10">
                    <view class="set-label-long">今日步数</view>
                    <view>

                        <switch color="#2d8cf0" :checked="open_set.today_step"
                                @change="open_set.today_step = $event.detail.value"/>
                    </view>
                </view>
                <view class="flex-row p10">
                    <view class="set-label-long">本月总步数</view>
                    <view>

                        <switch color="#2d8cf0" :checked="open_set.month_step"
                                @change="open_set.month_step = $event.detail.value"/>
                    </view>
                </view>
                <view class="flex-row p10">
                    <view class="set-label-long">本月平均步数</view>
                    <view>

                        <switch color="#2d8cf0" :checked="open_set.average_step"
                                @change="open_set.average_step = $event.detail.value"/>
                    </view>
                </view>
                
                <view class="flex-row p10">
                    <view class="set-label-long">文字颜色</view>
                    <view class="flex-row flex-wrap">
                        <view class="color-item" v-for="(item, index) in color_list" :key="index" 
                              :style="{'backgroundColor': item, 'borderColor': item === words_color ? '#02b148' : item}" 
                              @click="words_color = item">
                        </view>
                    </view>
                </view>
            </view>
            
            <view class="flex-all-center" @click="$refs.setPopup.close()">
                <uni-icons type="close" size="28" color="#ffffff"/>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const DEFAULT_BACKGROUND_IMAGE_SET = {
    src: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/chickensoup/bg1.jpg',
    getSize() {
        const width = 600, height = 900
        const maxWidth = uni.getSystemInfoSync().windowWidth - 20
        const scale = width / maxWidth
        const show_height = height / scale
        console.log(maxWidth, show_height);
        return {
            width: maxWidth,
            height: show_height
        }
    }
}

const app = getApp()
export default {
    data() {
        return {
            background_image: DEFAULT_BACKGROUND_IMAGE_SET.src,
            background_size: DEFAULT_BACKGROUND_IMAGE_SET.getSize(),
            background_original_size: {
                width: 0,
                height: 0
            },
            words: '个人的价值，应当看他贡献了什么，而不应当看他取得了什么。',
            headimg: '',
            date: this._utils.getDay(0, true, '.'),
            qrcode_src: '',
            open_set: {
                headimg: true,
                date: true,
                today_step: true,
                month_step: true,
                average_step: true
            },
            color_list: ['#ffffff', '#000000', '#e20f04', '#ff86aa', '#2d8cf0', '#ff9900'],
            words_color: '#ffffff',
            today_step: 0,
            month_step: 0,
            average_step: 0,
            poster_path: ''
        }
    },
    
    watch: {
        background_image: 'resetPosterPath',
        words: 'resetPosterPath',
        words_color: 'resetPosterPath',
        open_set: {
            handler: 'resetPosterPath',
            deep: true
        }
    },
    
    computed: {
        headimgShow() {
            return !!(this.open_set.headimg && this.headimg)
        }
    },

    onLoad() {
        this.$login.uniLogin(() => {
            this.init()
        })
    },

    methods: {
        init() {
            this.getHeadimg()
            this.getQrcode()
            this.getWechatStepData()
        },

        resetPosterPath() {
            this.poster_path = ''
        },

        getHeadimg() {
            this.headimg = app.globalData['userinfo'].headimg || ''
        },

        async getQrcode() {
            const qr_pic = await this.xwy_api.getWxAppCode({page: 'pages/index/index'})
            this.qrcode_src = qr_pic || ''
        },

        async getWechatStepData() {
            const {year, month} = this._utils.getYearMonthDay()

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.step/user_mobile_step_list',
                data: {
                    year: year,
                    month: month,
                    page: 1,
                    perpage: new Date(year, month, 0).getDate()
                }
            })

            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace list.date_time */
            
            const list = res?.data?.mobile_step_list?.data || []
           
            const today = new Date(this._utils.getDay(0, true, '/')).getTime() / 1000
            const month_step = list.reduce((sum, v) => {
                if (v.date_time === today) this.today_step = v.num
                return sum + v.num
            }, 0)
            this.month_step = month_step
            this.average_step = Math.floor(month_step / list.length)
        },

        backgroundImageLoad(e) {
            const {width, height} = e.detail
            const maxWidth = uni.getSystemInfoSync().windowWidth - 20
            const scale = width / maxWidth
            const imageHeight = height / scale
            this.background_size = {
                width: maxWidth,
                height: imageHeight
            }
            this.background_original_size = {width, height}
            this.scale = scale
        },

        backgroundChange() {
            this.$uni.navigateTo('./background-list', {
                events: {
                    imageChange: src => this.background_image = src
                }
            })  
        },

        wordsChange() {
            this.$uni.navigateTo(`./words-list?words=${encodeURIComponent(this.words)}`, {
                events: {
                    wordsChange: words => this.words = words
                }
            })
        },
        
        async getImagePath(src) {
            const res = await this.$uni.getImageInfo(src)
            return res.path
        },

        async generatePoster() {
            if (this.poster_path) return this.poster_path
            
            this.$uni.showLoading('海报生成中...')
            const filePath = await this.getCanvasPath()
            uni.hideLoading()
            
            this.poster_path = filePath
            return filePath
        },
        
        async saveImage() {
            const filePath = await this.generatePoster()
            
            uni.saveImageToPhotosAlbum({
                filePath,
                success: () => this.$uni.showToast('已保存')
            })
        },

        async preview() {
            const path = await this.generatePoster()
            
            this.$uni.previewImage(path)
        },
        
        async getCanvasPath() {
            this.ctx ||= uni.createCanvasContext('canvas')
            
            await this.drawBackgroundImage()
            await this.drawHeadimg()
            this.drawDate()
            this.drawWords()
            this.drawStep()
            await this.drawQrcode()
            
            return await this.draw()
        },
        
        async drawBackgroundImage() {
            const path = await this.getImagePath(this.background_image)
            this.ctx.drawImage(path, 0, 0, this.background_original_size.width, this.background_original_size.height)
        },

        drawArcImage(path, x, y, size) {
            this.ctx.save()  // 保存绘图上下文状态
            this.ctx.beginPath()
            this.ctx.arc(x + size / 2, y + size / 2, size / 2, 0, 2 * Math.PI)
            this.ctx.clip()
            this.ctx.drawImage(path, x, y, size, size)
            this.ctx.restore() // 恢复之前保存的绘图上下文状态
        },
        
        async drawHeadimg() {
            const {scale, headimgShow, headimg} = this
            if (!headimgShow) return
            
            const path = await this.getImagePath(headimg)
            const size = 80 * scale, top = 15 * scale, left = 15 * scale
            this.drawArcImage(path, left, top, size)
        },
        
        async drawQrcode() {
            const {scale, qrcode_src, background_original_size} = this
            if (!qrcode_src) return
            
            const {width, height} = background_original_size
            
            const path = await this.getImagePath(qrcode_src)
            const size = 120 * scale
            const right = 15 * scale
            const bottom = 15 * scale
            const top = height - size - bottom
            const left = width - size - right
            this.drawArcImage(path, left, top, size)
        },
        
        drawDate() {
            const {scale, date, open_set: {date: date_show}, background_original_size: {width}} = this
            if (!date_show || !date) return
            const font_size = 16 * scale, padding = 15 * scale
            this.drawText(width, font_size, date, 'right', 0, padding, padding + font_size)
        },

        drawWords() {
            const {scale, words, background_original_size: {width}} = this
            if (!words) return
            
            const font_size = 20 * scale, padding = 15 * scale
            const one_line_text = Math.floor((width - padding * 2) / font_size)
            const line_height = font_size + 10 * scale
            const top = 140 * scale

            words.match(new RegExp(`.{1,${one_line_text}}`, 'g')).forEach((str, i) => {
                this.drawText(width, font_size, str, 'left', padding, 0, top + line_height * i);
            })
        },

        drawStep() {
            const {today_step, month_step, average_step, open_set, background_original_size, scale} = this
            const {today_step: today_step_show, month_step: month_step_show, average_step: average_step_show} = open_set
            const {width, height} = background_original_size
            
            if (today_step_show) {
                const text_font_size = 18 * scale, step_font_size = 20 * scale, left = 15 * scale
                const today_data_top = height - 130 * scale
                this.drawText(width, text_font_size, '今日步数', 'left', left, 0, today_data_top)
                this.drawText(width, step_font_size, today_step, 'left', left, 0, today_data_top + step_font_size + 5 * scale)
            }

            const month_text_font_size = 16 * scale, month_step_font_size = 18 * scale, month_data_top = height - 60 * scale
            if (month_step_show) {
                const left = 15 * scale
                this.drawText(width, month_text_font_size, '本月总步数', 'left', left, 0, month_data_top)
                this.drawText(width, month_step_font_size, month_step, 'left', left, 0, month_data_top + month_step_font_size + 5 * scale)

            }
            
            if (average_step_show) {
                const left = 120 * scale
                this.drawText(width, month_text_font_size, '本月平均步数', 'left', left, 0, month_data_top)
                this.drawText(width, month_step_font_size, average_step, 'left', left, 0, month_data_top + month_step_font_size + 5 * scale)
            }
        },

        drawText(width, size, text, align, left, right, top) {
            if (align === 'center') left = width / 2
            if (align === 'right') left = width - right
            this.ctx.setTextAlign(align)
            this.ctx.setFontSize(size)
            this.ctx.fillStyle = this.words_color
            this.ctx.fillText(text, left, top)
        },
        
        draw() {
            return new Promise(resolve => {
                setTimeout(() => {
                    this.ctx.draw(false, async () => {
                        const path = await this.canvasToTempFilePath()
                        resolve(path)
                    })
                }, 300)
            })
            
        },

        canvasToTempFilePath() {
            // const {width, height} = this.background_original_size
            // const pixelRatio = uni.getSystemInfoSync()?.pixelRatio || 3
            
            return new Promise(resolve => {
                setTimeout(() => {
                    uni.canvasToTempFilePath({
                        canvasId: 'canvas',
                        // width,
                        // height,
                        // destWidth: width * pixelRatio,
                        // destHeight: height * pixelRatio,
                        quality: 1,
                        success: res => {
                            console.log('绘画的图片', res)
                            resolve(res.tempFilePath)
                        },
                        fail: async err => {
                            console.log('绘画失败', err)
                            return await this.canvasToTempFilePath()
                        }
                    }, this)
                }, 300)
            })
        }
    }
}
</script>

<style lang="scss">
.canvas {
    position: fixed;
    top: -999999px;
    left: -999999px;
}

.poster-container {
    position: relative;
    margin: 10px;
    
    .background-image {
        border-radius: 10px;
        display: block;
    }
    
    .edit-button, .headimg-image, .date, .words, .qrcode-image, .today-step, .month-step, .average-step {
        position: absolute;
    }
    
    .edit-button {
        padding: 5px 10px;
        position: absolute; 
        right: 15px; 
        top: 40px; 
        background-color: rgba(0, 0, 0, .7); 
        border-radius: 5px;
    }
    
    .headimg-image, .qrcode-image {
        border-radius: 50%;
        display: block;
    }
    
    .headimg-image {
        top: 15px;
        left: 15px;
        width: 80px;
        height: 80px;
    }
    
    .date {
        top: 15px;
        right: 15px;
        font-size: 16px;
    }
    
    .words {
        top: 120px;
        left: 15px;
        font-size: 20px;
        width: calc(100% - 30px);
    }
    
    .today-step {
        left: 15px;
        bottom: 90px;
    }

    .month-step {
        left: 15px;
        bottom: 30px;
    }

    .average-step {
        left: 120px;
        bottom: 30px;
    }
    
    .qrcode-image {
        right: 15px;
        bottom: 15px;
        width: 120px;
        height: 120px;
    }
}

.save-image-button {
    width: 200px;
    line-height: 44px;
    border-radius: 5px;
    margin: 10px auto;
}

.set-popup {
    width: 85vw;
    padding: 10px;
    border-radius: 10px;
    background-color: #fff;
    
    .set-label-short, .set-label-long {
        color: #495060;
    }
    
    .set-label-short {
        min-width: 40px;
    }

    .set-label-long {
        min-width: 105px;
    }
    
    .bg-set-image {
        width: 60px; 
        height: 60px; 
        border-radius: 5px;
    }
    
    .words-set {
        width: 100%;
        padding: 5px;
        box-sizing: border-box;
        border: 1px solid #c0c0c0; 
        border-radius: 5px;
    }
    
    .color-item {
        width: 20px; 
        height: 20px; 
        border-radius: 50%; 
        box-sizing: border-box; 
        margin-right: 8px; 
        border: 2px solid #02b148;
    }
}
</style>