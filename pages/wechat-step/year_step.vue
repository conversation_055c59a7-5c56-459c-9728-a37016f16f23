<template>
	<view>
        <view class="bg-white p10">
            <year-month-switch :title="`${year}年步数`" current="year" @yearMonthSwitch="backMonthStep"/>
        </view>

		<view class="year flex-all-center">
			<view class="pl10 pr10" @click="yearChange('--')">
				<uni-icons
					type="back"
					size="18"
					:color="year === min_year ? '#bbbec4' : '#80848f'"
				/>
			</view>

			<picker
				:range="year_list"
				:value="year_list.findIndex(v => v === year)"
				@change="yearChange($event)"
			>
				<view class="text-center" style="width: 80px;">{{year}}</view>
			</picker>
			<view class="pl10 pr10" @click="yearChange('++')">
				<uni-icons
					type="forward"
					size="18"
					:color="year === max_year ? '#bbbec4' : '#80848f'"
				/>
			</view>
		</view>

		<view class="flex-row flex-wrap pb10">
			<view
				class="month-step-item text-center"
				hover-class="navigator-hover"
				v-for="(item, index) in step_list"
				:key="index"
				@click="lookMonthStep(item.month)"
			>
				<view class="pb5 color-content">
					<text class="font18">{{item.month}}</text>
					<text class="font12">月</text>
				</view>
				<view class="color-warning">
					<text v-if="item.all_step >= 10000">
						{{Math.trunc(item.all_step / 10000) + '万'}}
					</text>
					<text v-if="item.all_step < 10000">{{item.all_step}}</text>
					<text class="font12">步</text>
				</view>
			</view>
		</view>

		<view class="flex-all-center">
			<view class="p10 color-light-primary" @click="exportMonthData">
				<text>步数导出</text>
				<uni-icons type="forward" color="#5cadff" size="16"/>
			</view>
		</view>

		<template v-if="is_init && !popup_open">
			<xwy-ad :ad_type="4"></xwy-ad>
			<xwy-ad :ad_type="3"></xwy-ad>
		</template>

		<view class="p20 pt0 color-sub" style="line-height: 24px;">

			<view class="flex-row">
				<view class="flex-all-center pr10">
					<view class="bg-primary" style="width: 14px; height: 14px;"></view>
				</view>
				<view class="font14">年累计行走</view>
				<view class="pl5 color-primary font18">
					<text v-if="step_count >= 10000">
						{{Math.trunc(step_count / 10000) + '万'}}
					</text>
					<text v-if="step_count < 10000">{{step_count}}</text>
				</view>
				<view class="pl5 font12">步</view>
			</view>

			<view class="flex-row">
				<view class="flex-all-center pr10">
					<view class="bg-light-primary" style="width: 14px; height: 14px;"></view>
				</view>
				<view class="font14">年累计行走</view>
				<view class="pl5 color-light-primary font18">{{kilom}}</view>
				<view class="pl5 font12">公里</view>
			</view>

			<view class="flex-row">
				<view class="flex-all-center pr10">
					<view class="bg-green" style="width: 14px; height: 14px;"></view>
				</view>
				<view class="font14">年累计减少碳排放</view>
				<view class="pl5 color-green font18">{{carbon_emission}}</view>
				<view class="pl5 font12">克</view>
			</view>

			<view class="flex-row">
				<view class="flex-all-center pr10">
					<view class="bg-warning" style="width: 14px; height: 14px;"></view>
				</view>
				<view class="font14">日均步数最高月份:</view>
				<view class="color-warning font18 pl5">{{max_step_month.average_step}}</view>
				<view class="pl5 font12">步</view>
				<view>（{{max_step_month.month}}月）</view>
			</view>

			<view v-if="step_count" class="flex-row">
				<view class="flex-all-center pr10">
					<view class="bg-sub" style="width: 14px; height: 14px;"></view>
				</view>
				<view class="font14">日均步数最低月份:</view>
				<view class="color-sub font18 pl5">{{min_step_month.average_step}}</view>
				<view class="pl5 font12">步</view>
				<view>（{{min_step_month.month}}月）</view>
			</view>
		</view>


		<view style="padding-bottom: 20px;">
			<template v-for="(item, index) in step_list">
				<view
					class="step-proportion "
					v-if="year !== to_year || item.month <= to_month"
					:key="index"
				>
					<view class="flex-row">
						<view class="step-proportion-month color-sub font14">{{item.month}}月</view>
						<view class="flex-kai" style="width: 100%;">
							<view class="progress bg-background" style="width: 100%;">
								<view
									class="progress"
									:style="{
										width: `calc(100% * ${item.proportion})`,
										backgroundColor: `rgba(255, 153, 0, ${item.proportion})`
									}"
								>
									<view class="step-text color-content">
										<text>{{item.all_step}}</text>
										<text class="font12">步</text>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view v-if="item.average_step" style="padding-left: 35px;">
						<text class="font12 pl10 color-sub">日均</text>
						<text class="pl5 font12 color-sub">{{item.average_step}}</text>
						<text class="font12 color-sub">步</text>
					</view>
				</view>
			</template>
		</view>

		<!-- <view class="p10">
			<view
				class="month-step-item-2 flex-kai bg-background p10"
				v-for="(item, index) in step_list"
				:key="index"
			>
				<view class="color-content flex-all-center">{{item.month}}月</view>
				<view class="text-right">
					<view class="color-warning">
						<text class="font18">{{item.all_step}}</text>
						<text class="font12">步</text>
					</view>
					<view v-if="item.average_step" class="color-sub pt5">
						<text class="font12">日均</text>
						<text class="font14 pl5">{{item.average_step}}</text>
						<text class="font12">步</text>
					</view>
				</view>
			</view>
		</view> -->

		<uni-popup ref="export_month_tips" type="center">
			<view class="export_popup text-center bg-white">
				<view class="popup-close" @click="uniPopupClose('export_month_tips')">
					<uni-icons type="closeempty" size="24" color="#b2b3b7"/>
				</view>
				<view class="top" style="padding: 30px 10px 10px;">
					<text class="iconfont icon-export-excel color-green" style="font-size: 80px;"></text>
					<view class="font14 color-sub">导出运动步数数据需要{{export_month_integral.cost}}积分</view>
					<view class="font14 color-sub">当前积分{{user_integral}}</view>
				</view>
				<view class="bg-white color-info" style="padding: 20px;">
					<template>
						<view
							v-if="user_integral >= export_month_integral.cost"
							class="export-btn bg-info color-white"
							hover-class="navigator-hover"
							@click="showSelectMonthPopup"
						>导出</view>
						<button
							v-else
							open-type="contact"
							class="export_ranking-btn bg-green color-white font16"
							@click="copy('积分')"
						>获取积分</button>
					</template>
				</view>
			</view>
		</uni-popup>


		<uni-popup ref="select_month" type="center">
			<view class="selete-month-popup bg-white text-center">
				<view class="popup-close" @click="uniPopupClose('select_month')">
					<uni-icons type="closeempty" size="24" color="#b2b3b7"/>
				</view>
				<view class="color-sub p10">请选择需要导出步数的月份</view>
				<view class="flex-row flex-wrap">
					<template v-for="(item, index) in step_list">
						<view
							class="month-step-item"
							:key="item.month"
							@click="exportConfirm(item.month)"
						>
							<text :class="'iconfont font34  icon-calendar-' + item.month + (year === to_year && item.month > to_month ? ' color-disabled' : ' color-light-primary')"></text>
							<!-- <view class="pb5 color-content">
								<text class="font18">{{item.month}}</text>
								<text class="font12">月</text>
							</view>
							<view class="color-warning">
								<text v-if="item.all_step >= 10000">
									{{Math.trunc(item.all_step / 10000) + '万'}}
								</text>
								<text v-if="item.all_step < 10000">{{item.all_step}}</text>
								<text class="font12">步</text>
							</view> -->
						</view>
					</template>
				</view>
			</view>
		</uni-popup>

		<uni-popup ref="export_month_success_tips" type="center">
			<view class="export_popup text-center">
				<view class="popup-close" @click="uniPopupClose('export_month_success_tips')">
					<uni-icons type="closeempty" size="24" color="#b2b3b7"/>
				</view>
				<view class="top bg-primary" style="padding: 30px 10px 10px;">
					<icon type="success" size="80" color="#ffffff"></icon>
					<view class="font18 color-white">导出成功</view>
				</view>
				<view class="bg-white color-info" style="padding: 20px;">
					<view class="color-content text-center font14" style="padding-bottom: 20px;">{{export_month_success_tips}}</view>
					<view
						class="export-btn bg-info color-white"
						hover-class="navigator-hover"
						@click="copyDownloadSrc(false)"
					>复制下载地址</view>
				</view>
			</view>
		</uni-popup>

    
	</view>
</template>

<script>
    const app = getApp()
	import login from '@/utils/api/login.js'
	import xwy_api from '@/utils/api/xwy_api.js'
	import utils from '@/utils/utils.js'
	import my_storage from '@/utils/storage.js'
    import YearMonthSwitch from "./components/year-month-switch.vue"


	export default {
        components: {YearMonthSwitch},
		data() {
			return {
				is_init: false,
				year_list: [],
				year: utils.getYearMonthDay().year,
				step_list: [],
				step_count: 0,
				min_year: 2022,
				max_year: utils.getYearMonthDay().year,
				to_year: utils.getYearMonthDay().year,
				to_month: utils.getYearMonthDay().month,
				carbon_emission: 0,
				kilom: 0,
				max_step_month: {
					month: 1,
					average_step: 0
				},
				min_step_month: {
					month: 1,
					average_step: 0
				},
				export_month_integral: {
					cost: 0
				},
				export_month_success_tips: '',
				user_integral: 0,
                popup_open: false
			}
		},

		onLoad(e) {
			this.getYearList()
			uni.showLoading({
				title: '数据加载中...',
				mask: true
			})
			login.uniLogin(err => {
				this.is_init = true
				if (err && err.errMsg) {
					uni.hideLoading()
					uni.showModal({
						title: err['errTitle'] || '提示',
						content: err.errMsg,
						showCancel: false
					})
					return false
				}

				this.getYearStep()
				this.getCostRules()
				this.getNewIntegral()
			})
		},

		methods: {

			async getCostRules() {
				const integral_set = await xwy_api.getIntegralSet()
				if (!integral_set) return false

				const export_month_integral = integral_set.find(v => v.types === 2)
				if (export_month_integral && export_month_integral.cost) {
					this.export_month_integral = export_month_integral
				}
			},

			getNewIntegral() {
				xwy_api.getUserDetail(() => {
					this.user_integral = app.globalData['userinfo'].integral || 0
				})
			},

			getYearList() {
				const year_list = []
				for (let i = this.min_year; i <= this.max_year; i++) {
					year_list.push(i)
				}
				this.year_list = year_list
			},

			stepReset() {
				const list = []
				for (let i = 1; i <= 12; i++) {
					list.push({month: i, all_step: 0, average_step: 0, proportion: 0})
				}
				this.step_list = list
				this.step_count = 0
			},

			async getYearStep() {
				uni.showLoading({
					title: '数据加载中...',
					mask: true
				})

				this.stepReset()
				const res = await xwy_api.request({
					url: 'front.flat.sport_step.totalUserStep/total_user_mobile_step',
					data: {
						year: this.year
					}
				})

				uni.hideLoading()

				if (res?.data?.['total_result']?.length) {
					const list = res.data['total_result']
					// 获取最大步数，并取整到万，如 21034 => 22000  256 => 10000
					const max_step = Math.ceil(Math.max(...list.map(item => item.all_step)) / 10000) * 10000
					console.log(max_step)
					let count = 0
					list.forEach(v => {
						count += v.all_step
						const month = this.step_list[v.month - 1]
						month.all_step = v.all_step

						let month_day = new Date(this.year, v.month, 0).getDate() // 该月的天数
						if (this.year === this.to_year && v.month === this.to_month) {
							// 如果是本月，平均步数不除整月
							month_day = new Date().getDate()
						}
						month.average_step = Math.floor(v.all_step / month_day)
						month.proportion = v.all_step / max_step
					})
					this.step_count = count

					this.computedStep(count)
				}
			},

			computedStep(count) {
                const kilom = this._utils.step2kilometer(count)
				const kilom2carbon_emission = 270   // 汽车1公里碳排放(克)
				const carbon_emission = kilom * kilom2carbon_emission
				this.kilom = kilom
				this.carbon_emission = Math.trunc(carbon_emission)


				const step_list = JSON.parse(JSON.stringify(this.step_list))
				// 如果是今年的话，还没到的月份不计算在内
				let new_list = []
				if (this.year === this.to_year) {
					step_list.forEach(v => {
						if (v.month <= this.to_month) new_list.push(v)
					})
				} else {
					new_list = step_list
				}

				const max_step_month_data = new_list.reduce((max, obj) => (max.average_step > obj.average_step ? max : obj))
				this.max_step_month.month = max_step_month_data.month
				this.max_step_month.average_step = max_step_month_data.average_step
				const min_step_month_data = new_list.reduce((min, obj) => (min.average_step < obj.average_step ? min : obj))
				this.min_step_month.month = min_step_month_data.month
				this.min_step_month.average_step = min_step_month_data.average_step
			},

			yearChange(e) {
				if (e === '++') {
					if (this.year === this.max_year) return false
					this.year++
				} else if (e === '--') {
					if (this.year === this.min_year) return false
					this.year--
				} else {
					this.year = this.year_list[e.detail.value]
				}
				this.getYearStep()
			},

			lookMonthStep(month) {
                const yearStepPage = 'pages/wechat-step/history_step'
                if (getCurrentPages().find(v => v.route === yearStepPage)) {
                    const eventChannel = this.getOpenerEventChannel()
                    eventChannel && eventChannel.emit('monthSwitch', {year: this.year, month})
                    return this.$uni.navigateBackPage(yearStepPage)
                }

                this.$uni.navigateTo(`/${yearStepPage}?year=${this.year}&month=${month}`)
			},
            

            backMonthStep() {
                const yearStepPage = 'pages/wechat-step/history_step'
                if (getCurrentPages().find(v => v.route === yearStepPage)) {
                    return this.$uni.navigateBackPage(yearStepPage)
                }
                this.$uni.navigateTo(`/${yearStepPage}`)
            },

			exportMonthData() {
				if (!this.export_month_integral.cost) {
					this.exportMonthDataAjax()
					return false
				}

				this.uniPopupOpen('export_month_tips')
			},

			showSelectMonthPopup() {
				this.uniPopupClose('export_month_tips')
				this.uniPopupOpen('select_month')
			},

			exportConfirm(month) {
				if (this.year === this.to_year && month > this.to_month) {
					uni.showToast({
						title: `无法导出${this.year}年${month}月步数`,
						icon: 'none'
					})
					return
				}
				uni.showModal({
					title: '提示',
					content: `确定导出${this.year}年${month}月的微信运动步数？`,
					confirmText: '导出',
					success: res => {
						if (res.confirm) this.exportMonthDataAjax(month)
					}
				})
			},

			exportMonthDataAjax(month) {
				uni.showLoading({
					mask: true
				})

				xwy_api.ajax({
					url: 'front.flat.sport_step.export.user_step/export_user_step_one_month',
					data: {
						access_token: app.globalData['access_token'],
						year: this.year,
						month
					},
					success: res => {
						uni.hideLoading()
						console.log('导出运动步数据', res)
						if (!res.status) {
							uni.showModal({
								title: '导出失败',
								content: res.info || '导出失败',
								showCancel: false
							})
							return false
						}
						this.export_month_integral.cost && this.uniPopupClose('select_month')

						this.getNewIntegral()
						this.export_month_success_tips = res.info || '导出成功'
						this.export_month_src = res.data.url
						this.copyDownloadSrc(true)
						this.uniPopupOpen('export_month_success_tips')

						my_storage.setExportExcelRecord({
							url: res.data.url,
							title: `导出 ${this.year}年${month}月 微信运动步数`
						})
					}
				})

			},

			copyDownloadSrc(hide = false) {
				uni.setClipboardData({
					data: this.export_month_src,
          success: () => hide ? uni.hideToast() : this.$uni.showToast('复制成功', 'success', 1000)
				})
			},

			uniPopupOpen(ref) {
                this.popup_open = true
				this.$refs[ref].open()
			},

			uniPopupClose(ref) {
				this.$refs[ref].close()
                this.popup_open = false
			},

			copy(text) {
				uni.setClipboardData({
					data: text,
					success: () => uni.hideToast()
				})
			},
		}
	}
</script>

<style lang="scss">
.year {
	height: 40px;
	line-height: 40px;
}
.month-step-item {
	width: calc(100% / 4);
	padding: 10px 0;
	box-sizing: border-box;
}

.month-step-item-2 {
	border-radius: 10px;
	margin-bottom: 10px;
}

$progressHeight: 24px;
.step-proportion {
	padding: 5px;
	.step-proportion-month {
		width: 40px;
		padding-right: 10px;
		text-align: right;
	}
	.progress {
		line-height: $progressHeight;
		// min-width: 10px;
		border-radius: 5px;
		height: $progressHeight;
		position: relative;
		.step-text {
			position: absolute;
			white-space: nowrap;
			top: 0;
			left: 15px;
			height: $progressHeight;
		}
	}
}

.export_popup, .selete-month-popup {
	position: relative;
	width: 300px;
	border-radius: 10px;
	overflow: hidden;
}
.popup-close {
	position: absolute;
	padding: 5px;
	right: 0;
	top: 0;
}
.export-btn {
	line-height: 40px;
	border-radius: 20px;
}
</style>
