<template>
    <view class="page bg-background">
        <view v-if="pc_tips" class="p10 font12 text-center" style="color:#e19898;">{{ pc_tips }}</view>
        <!-- #ifndef MP-WEIXIN -->
        <view class="p10 font12 text-center" style="color:#e19898;">
            提示: 网页端无法获取微信运动步数, 请在手机微信小程序上使用。
        </view>
        <!-- #endif -->

        <view v-if="shield_type !== 'active'" class="user-info flex-row bg-white">
            <view v-if="headimg" class="headimg">
                <image class="headimg-image" :src="headimg" mode="aspectFill"/>
            </view>
            <view>
                <view class="ellipsis color-title">{{ nickname || '' }}</view>
                <view class="font14 color-sub" @click="shieldSet">
                    <text
                        :class="'iconfont font14 color-sub icon-' + (shield_type === 'step' ? 'switch' : 'setting')"></text>
                    <text style="padding-left: 2px;">{{ shield_type === 'step' ? '切换' : '设为首页' }}</text>
                </view>
            </view>
        </view>

        <view v-if="top_image_src" class="top-image">

            <image class="big-image" :src="top_image_src" mode="widthFix"/>
            <image class="rotate"
                   src="http://www.xinweiyun.com/weixin/editor/attached/image/weixin_235/20201117/20201117173605_730.png"/>

            <view class="step-num color-white text-center flex-all-center">
                <view v-if="today_step === 'loading' || step_in_reload">
                    <uni-load-more color="#ffffff" status="loading"
                                   :contentText="{contentrefresh: '步数更新中'}"/>
                </view>
                <view v-else>
                    <view>今日步数</view>
                    <view class="font34">{{ today_step }}</view>
                </view>
            </view>
        </view>

        <navigator v-if="wechatEnv" url="./history_step" class="p10 font12" style="color:#e19898;">
            {{ personalSportStepTips }}
        </navigator>

        <!-- #ifdef MP-WEIXIN -->
        <subscribe-message-popup v-if="personalSportStepTempId" ref="subscribeMessagePopup"
                                 :template-type="1" :template-id="personalSportStepTempId"/>
        <!-- #endif -->

        <view class="today-step">
            <view class="flex-kai">
                <view class="flex-row">
                    <view class="color-content">今日步数</view>
                    <view :class="{'step-reload': today_step === 'loading' || step_in_reload}"
                          class="step-reload-icon flex-all-center" style="width: 16px; height: 16px;"
                          @click="reloadStep">
                        <text v-if="wechatEnv && !isPc" class="iconfont icon-sync color-sub"></text>
                    </view>
                </view>

                <!-- #ifdef MP-WEIXIN -->
                <view v-if="personalSportStepTempId" class="subscribe-message-btn"
                      @click="$refs.subscribeMessagePopup.show()">
                    <text class="iconfont icon-wechat color-success font14"></text>
                    <text class="pl5 color-success font14">订阅提醒</text>
                </view>
                <!-- #endif -->
            </view>

            <view class="step-chart">
                <view class="progress-bar" :style="progressStyle"></view>
                <view class="progress-gap bg-white"></view>
                <view class="filled-progress"></view>
                <view class="step-content bg-white text-center">
                    <view class="font14 color-sub">{{ daily_step_goal }}步达标</view>
                    <view class="color-light-primary">
                        <view v-if="today_step === 'loading' || step_in_reload"
                              class="step-reload-icon step-reload p5">
                            <text class="iconfont icon-sync color-sub"></text>
                        </view>
                        <template v-else>
                            <text class="font20">{{ today_step }}</text>
                            <text class="font14">步</text>
                        </template>
                    </view>
                </view>
            </view>

            <view class="flex-kai font14">
                <view class="color-sub">
                    <text>每日目标: {{ daily_step_goal }}步</text>
                    <text class="iconfont color-sub icon-edit pl5 font14"
                          @click="changeTarget(daily_step_goal + '')"></text>
                </view>
                <view class="color-light-primary">
                    <text>达标率: {{ ~~completeness }}</text>
                    <text class="font12">%</text>
                </view>
            </view>
        </view>

        <view class="calorie">
            <view @click="shareToWeRun" style="display: inline-block;">
                <text class="iconfont icon-footnotes color-green"></text>
                <text class="color-content pl5 pr5">今日步数</text>
                <text class="color-green font18">{{ today_step === 'loading' ? 0 : today_step }}</text>
                <text class="color-sub font12">步</text>
            </view>
            <view>
                <uni-icons type="fire" color="#ff9900"/>
                <text class="color-content pl5 pr5">消耗能量</text>
                <text class="color-warning font18">{{ calorie }}</text>
                <text class="color-sub font12">千卡</text>
            </view>

            <!-- #ifdef MP-WEIXIN -->
            <button class="button-none calorie-share-werun" style="margin-top: 0;" open-type="share">
                <text class="color-sub iconfont icon-share font14 color-sub"></text>
                <text class="pl5 font14 color-sub">邀请好友</text>
            </button>
            <!-- #endif -->
        </view>

        <navigator class="day-7-step" url="./history_step">
            <view class="flex-kai">
                <view class="color-content pb10">最近7天步数</view>
                <view>
                    <text class="color-sub font14">查看更多</text>
                    <uni-icons type="forward" size="14" color="#80848f"></uni-icons>
                </view>
            </view>
            <step-bar-chart :step-list="day7step" :target-step="daily_step_goal"/>
        </navigator>

        <view v-if="function_list.length" class="function-list">
            <view class="flex-row">
                <navigator class="function-item text-center" v-for="(item, index) in function_list"
                           :key="index" :style="{width: `${100 / function_list.length}%`}" :url="item.url">
                    <view class="function-item-icon flex-all-center color-white"
                          :style="{backgroundColor: item.color}">
                        <text v-if="item.icon.type === 'iconfont'"
                              :class="'iconfont ' + item.icon.name"></text>
                        <uni-icons v-if="item.icon.type === 'uni'" :type="item.icon.name"
                                   size="30" color="#ffffff"/>
                    </view>
                    <view class="color-sub font12">{{ item.name }}</view>
                </navigator>
            </view>
        </view>

        <view v-if="login_success" class="m10">
            <xwy-ad :ad_type="3"/>
            <xwy-ad :ad_type="4"/>
        </view>

        <view class="flex-all-center pb10">
            <button class="button-none p10 font14 color-sub" open-type="contact">提点意见</button>
        </view>

        <!--        <navigator class="sport-center-enter flex-all-center" url="/pages/sports-center/record-list">-->
        <navigator class="sport-center-enter flex-all-center" url="/pages/other/small-feature-collection">
            <uni-icons type="plusempty" size="28" color="#ffffff"/>
        </navigator>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import stepBarChart from '@/components/step-bar-chart/step-bar-chart.vue'
import subscribeMessagePopup from './components/subscribe-message-popup.vue'

export default {
    components: {stepBarChart, subscribeMessagePopup},
    data() {
        return {
            // #ifdef MP-WEIXIN
            wechatEnv: true,
            // #endif

            login_success: false,
            top_image_src: '',
            today_step: 'loading',
            step_in_reload: false,
            daily_step_goal: Number(uni.getStorageSync('daily_step_goal') || this.xwy_config.daily_step_goal),
            function_list: [],
            day7step: [],
            nickname: app.globalData['userinfo']?.nickname || '',
            headimg: app.globalData['userinfo']?.headimg || '',
            shield_type: null,
            personalSportStepTempId: '',
            personalSportStepTips: '登录本系统，将获取你最近30天内的运动步数。'
        }
    },

    computed: {
        isPc() {
            const osName = (uni.getSystemInfoSync().osName || '').toLowerCase()
            return osName === 'windows' || osName === 'mac'
        },
        pc_tips() {
            return this.isPc ? '提示: 电脑端无法获取微信运动步数, 请在手机上使用。' : ''
        },
        completeness() {
            if (!this.today_step || this.today_step === 'loading') return 0
            return this.today_step / this.daily_step_goal * 100
        },
        progressStyle() {
            const _color = '#19be6b'
            const targetColor = '#ff9900'
            const color = this.completeness >= 100 ? targetColor : _color
            const startColor = `${color}ff`,
                endColor = `${color}80`,
                fillColor = '#f7f7f7',
                progress = this.completeness / 2 // 因为圆环只有半圆所以要除以2

            return `background: conic-gradient(from -90deg, ${startColor} ${progress / 2}%, ${endColor} ${progress}%, ${fillColor} ${progress}% 100%)`
        },

        calorie() {
            if (!this.today_step) return 0
            return this._utils.step2calorie(this.today_step)
        }
    },

    onLoad() {
        login.uniLogin(() => this.init())
    },

    methods: {
        init() {
            this.login_success = true
            this.getShield()
            this.getUserInfo()
            this.getToPImage()
            this.setFunctionList()
            this.getStep()
            this.getPersonalSportStepTempId()
        },

        getPersonalSportStepTempId() {
            const {
                personalSportStepTips,
                personalSportStepTempId
            } = app.globalData.shop_info?.extend_set?.conf_set || {}
            if (personalSportStepTempId) this.personalSportStepTempId = personalSportStepTempId
            if (personalSportStepTips) this.personalSportStepTips = personalSportStepTips
        },

        getShield() {
            const shield = app.globalData['shop_info']?.['extend_set']?.['shield_other_active']
            if (shield.active_id) {
                let shield_type = 'active'
                if (shield.active_id === 'personal_wechat_mobile_step') {
                    shield_type = 'step'
                    this.$uni.hideHomeButton()
                }
                this.shield_type = shield_type
            }
        },

        getUserInfo() {
            const {nickname, headimg} = app.globalData['userinfo']
            this.nickname = nickname || ''
            this.headimg = headimg || ''
        },

        getToPImage() {
            this.top_image_src = app.globalData['shop_info']?.['extend_set']?.['conf_set']?.['index_page']?.top_image_src || 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/diy_template/banner.jpg'
        },

        setFunctionList() {
            const function_list = [
                {
                    icon: {type: 'iconfont', name: 'icon-footnotes'},
                    name: '历史步数',
                    url: '/pages/wechat-step/history-step-list',
                    color: '#6ac6c7'
                },
                {
                    icon: {type: 'iconfont', name: 'icon-calendar-month'},
                    name: '月度步数',
                    url: '/pages/wechat-step/history_step',
                    color: '#58A9FB'
                },
                {
                    icon: {type: 'iconfont', name: 'icon-calendar-year'},
                    name: '年度步数',
                    url: '/pages/wechat-step/year_step',
                    color: '#F05862'
                },
                {
                    icon: {type: 'iconfont', name: 'icon-data-summary'},
                    name: 'AI运动报告',
                    url: '/pages/other/AI-analysis-report?type=user_step',
                    color: '#19be6b'
                },
                {
                    icon: {type: 'uni', name: 'image-filled'},
                    name: '运动海报',
                    url: '/pages/wechat-step/sports-poster/sports-poster',
                    color: '#ED9C4F'
                }
            ]

            // 纯净版不显示运动海报，因为有的付费用户想要自定义里面的背景图
            const shield_other_active_id = app.globalData['shop_info']?.extend_set?.shield_other_active?.active_id
            if (shield_other_active_id) function_list.splice(2, 1)
            this.function_list = function_list
        },

        reloadStep() {
            if (this.step_in_reload || this.today_step === 'loading') return
            this.step_in_reload = true
            this.getStep(true)
        },

        async getStep(reload = false) {
            if (!this.wechatEnv || this.isPc) {
                this.step_in_reload = false
                this.today_step = 0
                return
            }

            if (reload) {
                this.today_step = 0
                this.day7step = []
            }
            let stepList = []
            if (app.globalData['step_list']?.length && !reload) stepList = app.globalData['step_list']
            if (!stepList.length) {
                const res = await this.xwy_api.getWeRunData()
                if (reload && this.step_in_reload) this.step_in_reload = false
                stepList = res?.data?.['crypt_data']?.['stepInfoList']
                if (!stepList?.length) return

            }

            if (!stepList.length) return
            this.setTodayStep(stepList)
            this.set7dayStep(stepList)
        },

        setTodayStep(stepList) {
            const timestamp = new Date().setHours(0, 0, 0, 0) / 1000
            const step = stepList.find(item => item.timestamp === timestamp)?.step || 0
            const step_count = 50
            const one_step = Math.floor(step / step_count)
            let today_step = 0
            const interval = setInterval(() => {
                today_step += one_step
                if (today_step > step) {
                    today_step = step
                    this.today_step = today_step
                    clearInterval(interval)
                }
                this.today_step = today_step
            }, 10)
        },

        set7dayStep(stepList) {
            const today = new Date().setHours(0, 0, 0, 0) / 1000
            this.day7step = [...Array(7)].map((_, i) => {
                const timestamp = today - i * 24 * 60 * 60;
                return stepList.find(item => item.timestamp === timestamp) || {timestamp, step: 0};
            }).reverse()
        },

        async changeTarget(target) {
            const min = 1000, max = 100000
            const res = await this.$uni.showModal(target, {
                title: '请输入目标步数',
                showCancel: true,
                editable: true,
                placeholderText: `${min} - ${max}`
            })
            if (!res.confirm) return

            const errToast = (msg, content) => {
                this.$uni.showToast(msg)
                this.changeTarget(content)
            }

            const input_target = res.content
            if (!input_target) return errToast('请输入目标步数', '')
            if (isNaN(input_target)) return errToast('请输入正确的步数', '')
            if (Number(input_target) < min) return errToast(`目标步数不能小于${min}`, input_target)
            if (Number(input_target) > max) return errToast(`目标步数不能大于${max}`, input_target)
            if (Number(input_target) === this.daily_step_goal) return

            this.daily_step_goal = input_target
            uni.setStorageSync('daily_step_goal', input_target)
            this.$uni.showToast(`每日目标步数已修改为 ${input_target} 步`)
        },

        getShareToWeRunCalorie() {
            let calorie = this.calorie
            const calorieStorage = uni.getStorageSync('today-step-calorie')
            if (calorieStorage) {
                const today = new Date().setHours(0, 0, 0, 0)
                if (calorieStorage?.timestamp === today) {
                    const in_stock_calorie = calorieStorage?.calorie || 0
                    calorie -= in_stock_calorie
                }
            }

            if (calorie <= 0) {
                this.$uni.showToast('能量消耗无增加')
                return false
            }

            return calorie
        },

        saveShareToWeRunCalorie() {
            uni.setStorageSync('today-step-calorie', {
                timestamp: new Date().setHours(0, 0, 0, 0),
                calorie: this.calorie
            })
        },

        shareToWeRun() {
            // #ifdef MP-WEIXIN


            if (!this.calorie) return

            const calorie = this.getShareToWeRunCalorie()
            if (!calorie) return

            const record = {
                typeId: 1001,
                calorie
            }

            this.$wx.shareToWeRun({
                recordList: [record],
                success: () => {
                    this.saveShareToWeRunCalorie()
                    this.$uni.showToast('分享成功', 'success')
                }
            })


            // #endif
        },

        async shieldSet() {
            const type = this.shield_type
            const modal_content = type === 'step' ? '恢复默认首页后，每次打开小程序将打开小程序首页页面' : '设为首页后，每次打开小程序将直接打开本页面'
            const modal_title = type === 'step' ? '恢复默认首页' : '设为首页'
            const modal = await this.$uni.showModal(modal_content, {title: modal_title, showCancel: true})
            if (!modal.confirm) return

            let url = 'front.flat.sport_step.user/index_page_set',
                data = {page_types: 1}
            if (type === 'step') {
                url = 'front.flat.sport_step.user/clear_user_active_cache'
                data = {}
            }

            this.$uni.showLoading('设置中...')
            const res = await this.xwy_api.request({url, data})
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '设置失败')


            await this.$uni.showModal(`${modal_title}成功，请重启小程序`, {
                title: '设置成功',
                confirmText: '立即重启'
            })

            wx.restartMiniProgram({
                path: '/pages/index/index'
            })
        },


    },

    onShareAppMessage() {
        return {
            title: '快来看看你的步数吧！',
            path: '/pages/wechat-step/wechat-step-page'
        }
    },
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    padding: 1px 0 15px;
    box-sizing: border-box;
}

.user-info {
    padding: 15px;
    box-sizing: border-box;

    $headimg-size: 60px;
    line-height: 30px;

    .headimg, .headimg-image {
        width: $headimg-size;
        height: $headimg-size;
        border-radius: 50%;
        display: block;
    }

    .headimg {
        padding-right: 10px;
    }
}

.top-image {
    position: relative;
    overflow: hidden;

    .big-image {
        width: 100%;
    }

    .rotate {
        width: 250px;
        height: 250px;
        position: absolute;
        top: -130px;
        right: -130px;
        animation: rotate 20s linear infinite;
        z-index: 2;
    }

    .step-num {
        position: absolute;
        width: 130px;
        height: 130px;
        border-radius: 50%;
        border: 10px solid rgba(255, 255, 255, .5);
        animation: up-down 3s linear infinite;
        z-index: 2;
        left: 50%;
        margin-left: -75px;
        top: calc(50% - 70px);
    }

    @keyframes up-down {
        0% {
            top: calc(50% - 70px);
        }
        33% {
            top: calc(50% - 75px);
        }
        66% {
            top: calc(50% - 65px);
        }
        100% {
            top: calc(50% - 70px);
        }
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.today-step, .function-list, .ad-view, .day-7-step, .calorie {
    background-color: #fff;
    border-radius: 10px;
    padding: 10px;
    margin: 10px;
}

$step-chart-size: 200px;
$step-progress-size: 15px;
$progress-gap-size: 8px;
$filled-progress: 8px;
.today-step {
    .step-reload-icon {
        position: relative;
        top: -3px;
        left: 2px;
    }

    .step-reload {
        animation: rotate 1.5s linear infinite;
    }

    .step-chart {
        width: $step-chart-size;
        height: calc(#{$step-chart-size} / 2);
        overflow: hidden;
        margin: 10px auto;
        position: relative;

        .progress-bar {
            position: absolute;
            top: 0;
            left: 0;
            width: $step-chart-size;
            height: $step-chart-size;
            border-radius: 50%;
        }

        .progress-gap {
            position: absolute;
            top: $step-progress-size;
            left: $step-progress-size;
            border-radius: 50%;
            width: $step-chart-size - $step-progress-size * 2;
            height: $step-chart-size - $step-progress-size * 2;
        }

        .filled-progress {
            position: absolute;
            top: $step-progress-size + $progress-gap-size;
            left: $step-progress-size + $progress-gap-size;
            border-radius: 50%;
            width: $step-chart-size - $step-progress-size * 2 - $progress-gap-size * 2;
            height: $step-chart-size - $step-progress-size * 2 - $progress-gap-size * 2;
            background-color: #82E1E3;
        }

        .step-content {
            $size: $step-chart-size - $step-progress-size * 2 - $progress-gap-size * 2 - $filled-progress * 2;
            position: absolute;
            top: $step-progress-size + $progress-gap-size + $filled-progress;
            left: $step-progress-size + $progress-gap-size + $filled-progress;
            width: $size;
            height: $size;
            border-radius: 50%;
            box-sizing: border-box;
            padding-top: calc(#{$size} / 2 - 45px);
        }
    }
}

.calorie {
    position: relative;
}

.calorie-share-werun {
    position: absolute;
    top: 10px;
    right: 10px;
    line-height: 28px;
    border-radius: 5px;
}

.function-item {
    .function-item-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        margin: 5px auto;

        .iconfont {
            font-size: 30px;
        }
    }
}

.sport-center-enter {
    position: fixed;
    bottom: 100px;
    right: 10px;
    background-color: rgba(92, 173, 255, .8);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    z-index: 9;
}

.subscribe-message-btn {
    line-height: 28px;
    border-radius: 5px;
}
</style>