<template>
    <view class="page bg-background">
        <view v-if="!id" class="bg-white p10 mb10">
            <year-month-switch :title="`${get_year || c_year}年${get_month || c_month}月步数`"
                               current="month" @yearMonthSwitch="lookYearStep"/>
        </view>

        <uni-calendar
            :date="get_date"
            :lunar="true"
            @change="calenderChange"
            @monthSwitch="monthSwitch"
            :showMonth="false"
            :selected="selected"
            :endDate="endDate"
            :startDate="startDate"
        />

        <view class="flex-kai bg-white">
            <view class="p10">
                <navigator v-if="!id && !popup_open" url="/pages/wechat-step/sports-poster/sports-poster"
                           class="to-encourage-poster flex-all-center">
                    <text class="color-sub font14">运动海报</text>
                    <text class="iconfont icon-more color-sub font14"></text>
                </navigator>
            </view>
            <view class="p10 font14" @click="uploadStep">
                <text class="color-content">获取最新运动步数，请点击</text>
                <text class="color-primary pl5">同步步数</text>
                <text class="iconfont icon-more color-primary font14"></text>
            </view>
        </view>


        <template v-if="!id && !greater_than_now">
            <month-step-cell-statistics v-if="all_step" :year="get_year" :month="get_month"
                                        :all-step="all_step" :step-list="selected" :report="report_data"
                                        @reportShow="popup_open = true" @reportHide="popup_open = false"/>


            <view class="bg-white">
                <view class="flex-center">
                    <view v-if="charts_data.today !== null && get_year === c_year && get_month === c_month">
                        <torus-progress-bar
                            :progress="charts_data.today.progress"
                            :textList="charts_data.today.textList"
                            :ringColor="stepColor"
                            :targetColor="targetColor"
                        />
                        <view class="text-center font12 color-sub" style="position: relative; top: -12px">
                            日目标 {{ daily_step_goal }}步 达标率{{Math.floor(charts_data.today.progress || 0) }}%
                        </view>
                    </view>

                    <view v-if="charts_data.month !== null">
                        <torus-progress-bar
                            :progress="charts_data.month.progress"
                            :textList="charts_data.month.textList"
                            :ringColor="stepColor"
                            :targetColor="targetColor"
                        />
                        <view class="text-center font12 color-sub" style="position: relative; top: -12px">
                            月目标 {{ month_day_count * daily_step_goal }}步
                            达标率{{ Math.floor(charts_data.month.progress || 0) }}%
                        </view>
                    </view>
                </view>
            </view>
        </template>


        <xwy-ad v-if="!popup_open" :activity_id="id || ''" :ad_type="4"/>

        <xwy-ad :activity_id="id || ''" :ad_type="3"/>


        <template v-if="!greater_than_now && !popup_open">
            <month-step-chart v-if="selected.length && !id" :step-list="selected"
                              :year="get_year" :month="get_month" :days="month_day_count"/>

            <step-distribution v-if="step_count_list.length && distributionStatisticsShow"
                               :active-id="id" :year="get_year" :month="get_month"
                               :list="step_count_list" :days="month_day_count"/>
        </template>


        <!-- <view v-if="!id && !greater_than_now" class="export-btn-view bg-white">
            <view
                class="export-btn text-center bg-primary color-white"
                hover-class="navigator-hover"
                @click="exportMonthData"
            >导出当月运动步数</view>
        </view> -->

        <uni-popup ref="one_day_popup" type="center">
            <view class="one-day bg-white">
                <view class="popup-close" @click="uniPopupClose('one_day_popup')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="text-center">
                    <view class="flex-all-center">
                        <view class="year-month-day text-center">
                            <view class="year-month bg-primary color-white font14">
                                {{ one_day_data.year }}-{{ one_day_data.month }}
                            </view>
                            <view class="day color-primary flex-all-center font34">{{ one_day_data.date }}
                            </view>
                        </view>
                    </view>
                    <view class="pb5 pt15">
                        <text class="color-sub font14">微信运动步数:</text>
                        <text class="color-sub font14 pl5">{{ one_day_data.wechat_num }}</text>
                        <text class="color-sub font12">步</text>
                    </view>
                    <view v-if="activity_max_num" class="pb5">
                        <text class="color-sub font14">每日最高可兑换:</text>
                        <text class="color-sub font14 pl5">{{ activity_max_num }}</text>
                        <text class="color-sub font12">步</text>
                    </view>
                    <view class="pb5">
                        <text class="color-sub font14">已兑换步数:</text>
                        <text class="color-sub font14 pl5">{{ one_day_data.exchange_num }}</text>
                        <text class="color-sub font12">步</text>
                    </view>
                    <view class="pb5" v-if="closed_sign_pass_day !== '1' || one_day_data.fulldate === today">
                        <text class="color-sub font14">
                            可{{ one_day_data.fulldate === today ? '兑换' : '补签' }}步数:
                        </text>
                        <text class="color-red pl5 font18">{{ one_day_data.extra_step }}</text>
                        <text class="color-sub font12">步</text>
                    </view>


                    <view v-if="one_day_data.extra_step">
                        <!-- 今日的话就不用积分补签了，直接兑换 -->
                        <view v-if="one_day_data.fulldate === today" class="pt15">
                            <view class="exchange-btn bg-info color-white" hover-class="navigator-hover" 
                                  @click="exchange">兑换</view>
                            <!-- <navigator
                                :url="'/pages/activity/user/road-map/road_map?is_joining=true&id=' + id"
                                @click="uniPopupClose('one_day_popup')"
                                class="exchange-btn bg-info color-white"
                            >去兑换</navigator> -->
                        </view>

                        <template v-else-if="closed_sign_pass_day !== '1'">
                            <view class="pt15">
                                <view v-if="exchange_integral.cost" class="font12 color-sub ptm5">
                                    补签需要{{ exchange_integral.cost }}积分(当前积分{{ user_integral }})
                                </view>
                                <template>
                                    <view
                                        v-if="!exchange_integral.cost || user_integral >= exchange_integral.cost"
                                        class="exchange-btn bg-info color-white"
                                        hover-class="navigator-hover"
                                        @click="exchange"
                                    >补签
                                    </view>
                                    <button
                                        v-else
                                        open-type="contact"
                                        class="exchange-btn bg-green color-white font16"
                                        @click="copy('积分')"
                                    >获取积分
                                    </button>
                                </template>
                            </view>
                        </template>
                    </view>
                </view>

            </view>
        </uni-popup>

        <uni-popup ref="success_popup" type="center">
            <view class="success_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('success_popup')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top bg-primary color-white" style="padding: 50px 20px 20px;">
                    <icon type="success" size="80" color="#ffffff"></icon>
                    <!-- <view class="font18">{{one_day_data.fulldate === today ? '兑换' : '补签'}}成功</view> -->
                    <view class="font18">
                        {{ exchange_success_info || (one_day_data.fulldate === today ? '兑换成功' : '补签成功') }}
                    </view>
                </view>
                <view class="bg-white color-info" style="padding: 20px 20px 50px;">
                    <view v-if="exchange_success_tip_text_list.length">
                        <view
                            v-for="(item, index) in exchange_success_tip_text_list"
                            :key="index"
                            :style="'color: ' + item.color"
                        >
                            <text v-if="item.is_redpack" class="iconfont icon-red-packet pr5"
                                  :style="'color: ' + item.color"></text>
                            <text>{{ item.text }}</text>
                            <view v-if="item.is_redpack && item.amount" class="pt5">
                                <text class="font14">￥</text>
                                <text class="font18">{{ item.amount }}</text>
                                <text class="font14">元</text>
                            </view>
                        </view>
                    </view>
                </view>
                <xwy-ad :activity_id="id || ''" :ad_type="4"></xwy-ad>
            </view>
        </uni-popup>

<!--        <uni-popup ref="export_month_tips" type="center">
            <view class="export_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('export_month_tips')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top" style="padding: 30px 10px 10px;">
                    <text class="iconfont icon-export-excel color-green" style="font-size: 80px;"></text>
                    <view class="font14 color-sub">导出运动步数数据需要{{ export_month_integral.cost }}积分
                    </view>
                    <view class="font14 color-sub">当前积分{{ user_integral }}</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <template>
                        <view
                            v-if="user_integral >= export_month_integral.cost"
                            class="export-btn bg-info color-white"
                            hover-class="navigator-hover"
                            @click="exportMonthDataAjax"
                        >导出
                        </view>
                        <button
                            v-else
                            open-type="contact"
                            class="export_ranking-btn bg-green color-white font16"
                            @click="copy('积分')"
                        >获取积分
                        </button>
                    </template>
                </view>
            </view>
        </uni-popup>

        <uni-popup ref="export_month_success_tips" type="center">
            <view class="export_popup text-center">
                <view class="popup-close" @click="uniPopupClose('export_month_success_tips')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top bg-primary" style="padding: 30px 10px 10px;">
                    <icon type="success" size="80" color="#ffffff"></icon>
                    <view class="font18 color-white">导出成功</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ export_month_success_tips }}
                    </view>
                    <view
                        class="export-btn bg-info color-white"
                        hover-class="navigator-hover"
                        @click="copyDownloadSrc(false)"
                    >复制下载地址
                    </view>
                </view>
            </view>
        </uni-popup>-->

    </view>
</template>

<script>
const STEP_COLOR = '#02b148'
const TARGET_COLOR = '#ff9900'

const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
// import my_storage from '@/utils/storage.js'
import torusProgressBar from './components/torus-progress-bar.vue'
import YearMonthSwitch from "./components/year-month-switch.vue"

import monthStepCellStatistics from './components/month-step-cell-statistics.vue'
import stepDistribution from './components/step-distribution.vue'
import monthStepChart from './components/month-step-chart.vue'

export default {
    components: {
        YearMonthSwitch,
        torusProgressBar,
        monthStepCellStatistics,
        stepDistribution,
        monthStepChart
    },
    data() {
        return {
            loading: true,
            id: '',
            selected: [],
            today: utils.getDay(0, true),
            endDate: utils.getDay(),
            startDate: '',
            show_data: null,
            step_count_list: [],
            month_day_count: 31,
            c_year: this.getYearMonthDay().year,
            c_month: this.getYearMonthDay().month,
            get_date: utils.getDay(0, true),   // 自定义当前时间，默认为今天，用于在年步数点击某月的步数返回本页使用
            get_year: null,
            get_month: null,
            greater_than_now: false,  // 当前日历的时间是否大于现在时间
            one_day_data: {},
            exchange_success_info: '',
            exchange_success_tip_text_list: [],
            user_integral: 0,
            exchange_integral: {
                cost: 0
            },
            export_month_integral: {
                cost: 0
            },
            export_month_success_tips: '',
            charts_data: {
                today: null,
                month: null
            },
            popup_open: false,
            activity_max_num: 0,
            closed_sign_pass_day: '0', //是否关闭补签 '1'：关闭 '0'开启
            daily_step_goal: Number(uni.getStorageSync('daily_step_goal') || this.xwy_config.daily_step_goal),
            stepColor: STEP_COLOR,
            targetColor: TARGET_COLOR,
            report_data: null,
            all_step: 0
        }
    },

    computed: {
        // 兑换日历是否显示兑换步数统计分布
        distributionStatisticsShow() {
            return !(this.id && this.xwy_config.exchangeCalendarNotShowDistributionStatistics(this.id))
        }
    },
    
    
    onLoad(e) {

        if (e.closed_sign_pass_day) this.closed_sign_pass_day = e.closed_sign_pass_day

        if (e.activity_max_num && e.activity_max_num !== '0') this.activity_max_num = Number(e.activity_max_num)
        if (e.id) {
            this.id = e.id
            uni.setNavigationBarTitle({
                title: '历史兑换步数'
            })
        }

        if (e['stime']) this.startDate = utils.unitTimeToDate(Number(e['stime']) * 1000)
        if (e['etime']) {
            const now_time = new Date().getTime()
            const end_time = Number(e['etime']) * 1000
            if (now_time > end_time) {
                console.log('活动结束，不允许补签')
                // 活动结束，不允许补签
                this.closed_sign_pass_day = '1'
            }
            if (end_time < now_time) {
                // 活动结束时间小于当前时间，日历结束时间为活动结束时间
                const end_date = this.getYearMonthDay(utils.unitTimeToDate(end_time))
                this.endDate = end_date.year + '-' + end_date.month + '-' + end_date.day
            }
        }

        // 开启30天自动兑换，关闭补签
        if (e['thirty_day_auto_exchange'] && e['thirty_day_auto_exchange'] === '1')
            this.closed_sign_pass_day = '1'

        if (e.year && e.month) {
            const get_year = Number(e.year),
                get_month = Number(e.month)
            if (get_year !== this.get_year || get_month !== this.get_month) {
                this.get_year = get_year
                this.get_month = get_month
                this.get_date = `${get_year}-${get_month.toString().padStart(2, '0')}-01`
            }
        }

        this.$uni.showLoading('数据加载中...')
        login.uniLogin(err => {
            if (err && err.errMsg) {
                this.loading = false
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }


            this.getNewIntegral()
            // 补签不需要积分
            if (!e['exchange_pass_day'] || e['exchange_pass_day'] !== '1') this.getCostRules()
            
            this.dateInit()
        })
    },
    
    onShow() {
        if (!this.loading) this.getNewIntegral()
    },
    
    methods: {
        lookYearStep() {
            const yearStepPage = 'pages/wechat-step/year_step'
            if (getCurrentPages().find(v => v.route === yearStepPage)) {
                return this.$uni.navigateBackPage(yearStepPage)
            }

            uni.navigateTo({
                url: `/${yearStepPage}`,
                events: {
                    monthSwitch: date => {
                        const {year, month} = date

                        // 当前查看年月一样，不切换
                        if (year === this.get_year && month === this.get_month) return false
                        
                        this.get_date = `${year}-${month >= 10 ? month : ('0' + month)}-01`
                        this.monthSwitch({year, month})
                    }
                }
            })
        },

        async uploadStep() {
            this.$uni.showLoading('刷新中...')
            const res = await xwy_api.getWeRunData()

            if (res.status !== 1) {
                uni.hideLoading()
                return this.$uni.showModal(res?.info || '步数刷新失败')
            }

            this.dateInit()
        },

        async getCostRules() {
            const integral_set = await xwy_api.getIntegralSet()
            if (!integral_set) return false


            const exchange_integral_data = integral_set.find(v => v.types === 1)
            if (exchange_integral_data && exchange_integral_data.cost) {
                this.exchange_integral = exchange_integral_data
            }
            const export_month_integral = integral_set.find(v => v.types === 2)
            if (export_month_integral && export_month_integral.cost) {
                this.export_month_integral = export_month_integral
            }

        },

        getYearMonthDay(date = utils.getDay()) {
            const date_arr = date.split('-')
            const year = Number(date_arr[0])
            const month = Number(date_arr[1])
            const day = Number(date_arr[2])
            return {year, month, day}
        },

        calenderChange(e) {
            if (Number(e.year) !== Number(this.get_year) || Number(e.month) !== Number(this.get_month)) {
                this.get_year = e.year
                this.get_month = e.month
                this.step_count_list.forEach(v => {
                    v.value = 0
                })
                
                return this.dateInit()
            }

            // 兑换日历的数据
            if (!this.id) return false
            
            const one_day_data = {
                year: e.year,
                month: e.month,
                date: e.date,
                wechat_num: e.extraInfo.data?.wechat_num || 0,
                exchange_num: e.extraInfo.info || 0,
                extra_step: 0,
                fulldate: e.fulldate
            }

            // 计算当日可补签步数,如果活动设置了每日最高兑换步数，可补签步数为最高步数减已兑换步数，否则可补签步数为微信步数减已兑换步数
            const max_num = this.activity_max_num && one_day_data.wechat_num > this.activity_max_num ? this.activity_max_num : one_day_data.wechat_num
            let extra_step = max_num - one_day_data.exchange_num
            if (extra_step < 0) extra_step = 0
            one_day_data.extra_step = extra_step

            this.one_day_data = one_day_data

            this.uniPopupOpen('one_day_popup')
        },
        monthSwitch(e) {
            this.greater_than_now = false

            this.step_count_list.forEach(v => {
                v.value = 0
            })

            
            this.charts_data = {
                today: null,
                month: null
            }

            const {year, month} = e
            const c_year = this.getYearMonthDay().year
            const c_month = this.getYearMonthDay().month
            // 大于结束时间的月份不加载数据，今年剩下的月份不加载数据
            if (year > c_year || year === c_year && month > c_month) {
                this.greater_than_now = true
                return false
            }

            // 查看活动兑换日历的，活动开始前和结束后的月份不加载数据
            if (this.id) {
                const s_year = this.getYearMonthDay(this.startDate).year
                const s_month = this.getYearMonthDay(this.startDate).month
                if (year < s_year || year === s_year && month < s_month) return false

                const e_year = this.getYearMonthDay(this.endDate).year
                const e_month = this.getYearMonthDay(this.endDate).month
                if (year > e_year || year === e_year && month > e_month) return false
            }

            this.selected = []
            this.$uni.showLoading('数据加载中...')

            this.get_year = year
            this.get_month = month

            this.dateInit()
        },

        dateInit(not_loading = false) {
            this.charts_data = {
                today: null,
                month: null
            }

            if (!this.get_year || !this.get_month) {
                this.get_year = this.getYearMonthDay().year
                this.get_month = this.getYearMonthDay().month
            }

            this.month_day_count = new Date(this.get_year, this.get_month, 0).getDate()

            if (!not_loading) this.$uni.showLoading('数据加载中...')
            this.getExchangeStepData()
        },

        getExchangeStepData() {
            if (!this.id) return this.getWechatStepData()

            xwy_api.ajax({
                url: 'front.flat.sport_step.exchange/exchange_step_list',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id,
                    year: this.get_year,
                    month: this.get_month,
                    page: 1,
                    perpage: this.month_day_count
                },
                success: res => {
                    const list = res?.data?.['exchange_list']?.data || []
                    list.forEach(item => {
                        item.exchange_date = this._utils.beijingTimestamp2Local(item.exchange_date)
                    })
                    this.getWechatStepData(list)
                }
            })


        },

        getWechatStepData(exchange_step_data = null) {

            xwy_api.ajax({
                url: 'front.flat.sport_step.step/user_mobile_step_list',
                data: {
                    access_token: app.globalData['access_token'],
                    year: this.get_year,
                    month: this.get_month,
                    page: 1,
                    perpage: this.month_day_count
                },
                success: res => {
                    // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
                    /** @namespace res.data.mobile_step_list */
                    /** @namespace res.data.report_list */
                    
                    const list = res?.data?.mobile_step_list?.data || []
                    list.forEach(item => {
                        item.date_time = this._utils.beijingTimestamp2Local(item.date_time)
                    })
                    this.setSelectedData(exchange_step_data, list)
                    
                    if (!this.id && res?.data?.report_list) this.report_data = res.data.report_list
                }
            })
        },

        setSelectedData(exchange_list, wechat_list) {

            const step_count = [
                {
                    title: '3万步以上',
                    rule: step => step >= 30000,
                    tips: '大于等于3万的步数',
                    value: 0
                },
                {
                    title: '2万步以上',
                    rule: step => step >= 20000 && step < 30000,
                    tips: '20000步 - 29999步',
                    value: 0
                },
                {
                    title: '1万步以上',
                    rule: step => step >= 10000 && step < 20000,
                    tips: '10000步 - 19999步',
                    value: 0
                },
                {
                    title: '8千步以上',
                    rule: step => step >= 8000 && step < 10000,
                    tips: '8000步 - 9999步',
                    value: 0
                },
                {
                    title: '5千步以上',
                    rule: step => step >= 5000 && step < 8000,
                    tips: '5000步 - 7999步',
                    value: 0
                },
                {
                    title: '1千步以上',
                    rule: step => step >= 1000 && step < 5000,
                    tips: '1000步 - 4999步',
                    value: 0
                },
                {
                    title: '1千步以下',
                    rule: step => step > 0 && step < 1000,
                    tips: '1步 - 999步',
                    value: 0
                }
            ]


            const selected = [] // 日历组件的数据

            let exchange_count = 0 // 当月已兑换步数的天数

            let all_step = 0, // 当月累计行走步数
                all_day = 0  // 当月累计行走天数，用于计算平均步数

            let today_step = 0

            wechat_list.forEach(wc => {
                const date_ = utils.unitTimeToDate(wc['date_time'] * 1000)

                const {year, month} = this.getYearMonthDay(date_)


                // 当前月份的记录
                if (Number(year) === Number(this.get_year) && Number(month) === Number(this.get_month)) {

                    const item = {
                        date: date_,
                        info: wc.num,
                    }


                    if (Number(this.getYearMonthDay(date_).day) === Number(this.getYearMonthDay().day)) today_step = item.info

                    if (this.id) {
                        item.info = ''
                        item.data = {wechat_num: wc.num}
                        if (exchange_list && exchange_list.length) {
                            exchange_list.forEach(ex => {
                                if (ex.exchange_date === wc['date_time']) item.info = ex.step
                            })
                        }
                    }

                    // 计算每个步数段的天数
                    step_count.forEach(v => {
                        if (v.rule(item.info)) {
                            v.value++
                            if (this.id) exchange_count++
                        }
                    })
                    
                    all_step += item.info
                    all_day += 1
                    

                    item.infoColor = item.info >= this.daily_step_goal ? TARGET_COLOR : STEP_COLOR

                    selected.push(item)
                }

            })

            this.all_step = all_step

            const step_count_list = step_count

            if (this.id) {
                let all_day = this.month_day_count
                const {get_year, get_month} = this
                
                if (this.startDate) {
                    // 如果是活动开始的月份，从活动开始的日期开始计算未兑换天数
                    let [year, month, day] = this.startDate.split('-')
                    year = Number(year)
                    month = Number(month)
                    if (year === get_year && month === get_month) {
                        day = Number(day)
                        all_day = all_day - day + 1
                    }
                }

                if (this.endDate) {
                    let [year, month, day] = this.endDate.split('-')
                    year = Number(year)
                    month = Number(month)
                    if (year === get_year && month === get_month) {
                        day = Number(day)
                        all_day = day
                    }
                }

                this.month_day_count = all_day

                // 活动兑换日历，不显示未兑换天数
                /*step_count_list.push({
                    title: '未兑换',
                    value: all_day - exchange_count
                })*/
            }

            // 计算本月微信步数数据
            if (!this.id) {
                // 当前月份才显示今日步数
                if (this.get_year === this.c_year && this.get_month === this.c_month) {
                    const today_progress = today_step / this.daily_step_goal * 100
                    this.charts_data.today = {
                        progress: today_progress,
                        textList: [
                            {
                                text: today_step || '0',
                                color: today_progress >= 100 ? TARGET_COLOR : STEP_COLOR,
                                fontSize: '24px'
                            },
                            {
                                text: '今日步数',
                                color: '#999999',
                                fontSize: '14px'
                            }
                        ]
                    }
                }


                const month_progress = all_step / (this.month_day_count * this.daily_step_goal) * 100
                this.charts_data.month = {
                    progress: month_progress,
                    textList: [
                        {
                            text: all_step || '0',
                            color: month_progress >= 100 ? TARGET_COLOR : STEP_COLOR,
                            fontSize: '24px'
                        },
                        {
                            text: this.get_month + '月步数',
                            color: '#999999',
                            fontSize: '14px'
                        }
                    ]
                }
            }

            this.selected = selected
            this.step_count_list = step_count_list

            uni.hideLoading()
        },


        copy(text) {
            uni.setClipboardData({
                data: text,
                success: () => uni.hideToast()
            })
        },


        exchange() {
            this.exchange_success_tip_text_list = []

            const {year, month, date} = this.one_day_data
            const exchange_date = `${year}/${month}/${date}`
            const exchange_time = new Date(exchange_date).getTime() / 1000

            uni.showLoading({
                title: this.one_day_data.fulldate === this.today ? '兑换中...' : '补签中...',
                mask: true
            })


            xwy_api.ajax({
                url: 'front.flat.sport_step.exchange/exchange_step',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id,
                    exchange_date: exchange_time
                },
                success: res => {
                    this.loading = false
                    uni.hideLoading()
                    if (!res.status) return this.$uni.showModal(res.info || '兑换失败', {title: '兑换失败'})

                    this.getNewIntegral()
                    this.exchange_success_info = res.info || '兑换成功'
                    if (res.data?.['exchange_result']?.['show_text']?.length) {
                        const show_text = res.data['exchange_result']['show_text']
                        show_text.forEach(v => {
                            if (v.text && v.text.includes('红包')) v.is_redpack = true
                        })
                        this.exchange_success_tip_text_list = show_text
                    }
                    this.uniPopupClose('one_day_popup')
                    this.uniPopupOpen('success_popup')
                    this.dateInit(true)
                }
            })
        },

        getNewIntegral() {
            xwy_api.getUserDetail(() => {
                this.user_integral = app.globalData['userinfo'].integral || 0
            })
        },

        /*exportMonthData() {
            if (!this.export_month_integral.cost) {
                this.exportMonthDataAjax()
                return false
            }

            this.uniPopupOpen('export_month_tips')
        },

        exportMonthDataAjax() {
            this.$uni.showLoading()

            xwy_api.ajax({
                url: 'front.flat.sport_step.export.user_step/export_user_step_one_month',
                data: {
                    access_token: app.globalData['access_token'],
                    year: this.get_year,
                    month: this.get_month
                },
                success: res => {
                    uni.hideLoading()
                    if (!res.status) return this.$uni.showModal(res.info || '导出失败', {title: '导出失败'})
                    
                    this.export_month_integral.cost && this.uniPopupClose('export_month_tips')

                    this.getNewIntegral()
                    this.export_month_success_tips = res.info || '导出成功'
                    this.export_month_src = res.data.url
                    this.copyDownloadSrc(true)
                    this.uniPopupOpen('export_month_success_tips')

                    my_storage.setExportExcelRecord({
                        url: res.data.url,
                        title: `导出 ${this.get_year}年${this.get_month}月 微信运动步数`
                    })
                }
            })

        },

        copyDownloadSrc(hide = false) {
            uni.setClipboardData({
                data: this.export_month_src,
                success: () => hide ? uni.hideToast() : this.$uni.showToast('复制成功', 'success', 1000)
            })
        },*/

        uniPopupOpen(ref) {
            this.popup_open = true
            this.$refs[ref].open()
        },

        uniPopupClose(ref) {
            this.$refs[ref].close()
            this.popup_open = false
        }
    }
}
</script>

<style>
.page {
    padding-bottom: 20px;
}

/* 当日及选中的日期样式修改 */
.uni-calendar-item--isDay.data-v-6097fd5b, .uni-calendar-item--checked.data-v-6097fd5b {
    background-color: #fff !important;
    /* color: #333 !important; */
    opacity: 1 !important;
    /* border-radius: 50%; */
}

.uni-calendar-item--isDay.data-v-6097fd5b .uni-calendar-item__weeks-box-item, .uni-calendar-item--checked.data-v-6097fd5b .uni-calendar-item__weeks-box-item {
    background-color: #5cadff !important;
    /* border: 2px solid #5cadff; */
    /* box-sizing: border-box; */
    /* color: #333 !important; */
    opacity: 1 !important;
    border-radius: 50%;
}

.uni-calendar-item--isDay.data-v-6097fd5b .uni-calendar-item--isDay, .uni-calendar-item--checked.data-v-6097fd5b .uni-calendar-item--checked {
    background-color: #5cadff !important;
}

.uni-calendar-item--isDay.data-v-6097fd5b .uni-calendar-item--extra.data-v-6097fd5b, .uni-calendar-item--checked.data-v-6097fd5b .uni-calendar-item--extra.data-v-6097fd5b {
    color: #fff !important;
}

/* 日历插入数据右上角圆点的样式 */
.uni-calendar-item__weeks-box-circle {
    display: none;
}

.uni-calendar__backtoday.data-v-0682a296 {
    display: none;
}

/*.export-btn-view {
    position: fixed;
    left: 0;
    bottom: 0;
    padding: 15px;
    width: 100vw;
    box-sizing: border-box;
    z-index: 99;
}

.export-btn {
    line-height: 40px;
    border-radius: 20px;
}*/



.one-day, .success_popup, .export_popup {
    position: relative;
    width: 240px;
    padding: 20px;
    border-radius: 10px;
    overflow: hidden;
}

.success_popup, .export_popup {
    width: 300px;
    padding: 0;
}

.exchange-btn {
    line-height: 40px;
    border-radius: 20px;
}

.exchange-btn::after {
    border: none;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}

.year-month-day {
    width: 100px;
    border: 2px solid #2d8cf0;
    border-radius: 10px;
    overflow: hidden;
}

.year-month-day .year-month {
    padding: 3px 0;
}

.year-month-day .day {
    height: 60px;
}
</style>
