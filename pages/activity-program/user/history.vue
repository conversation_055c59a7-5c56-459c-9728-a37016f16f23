<template>
    <view class="page bg-background">
        <view class="list">
            <view class="item bg-white p10" v-for="item in list" :key="item.id" @click="lookDetails(item.id)">
                <view class="title ellipsis--l3">
                    <text class="color-title" space="nbsp">{{ item.title }}</text>
                </view>
                <view class="color-sub font12 pt5">{{ item.create_time }}</view>
            </view>
        </view>
        
        <view v-if="loading" class="text-center">
            <view v-if="current_page === 1" style="padding-top: 30vh;"></view>
            <load-ani/>
            <view v-if="current_page === 1" class="font14 color-sub">加载中</view>
        </view>

        <view v-if="!loading && !list.length" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无活动方案</view>
        </view>
    </view>
</template>

<script>
import login from '@/utils/api/login.js'

export default {
    data() {
        return {
            loading: true,
            list: [],
            current_page: 1,
            is_last_page: false
        }
    },

    onLoad() {
        login.uniLogin(() => {
            this.init()
        })
    },
    
    onPullDownRefresh() {
        this.current_page = 1
        this.getList().finally(() => uni.stopPullDownRefresh())
    },
    
    async onReachBottom() {
        if (this.is_last_page || this.loading) return
        this.current_page++
        this.loading = true
        await this.getList()
        this.loading = false
    },

    methods: {
        async init () {
            await this.getList()
            this.loading = false
        },
        
        async getList() {
            if (this.current_page === 1) {
                this.list = []
                this.is_last_page = false
            }
            
            const res = await this.xwy_api.request({
                url: 'front.flat.active.chat.user.chat/chat_list',
                data: {
                    page: this.current_page,
                    page_size: 20
                }
            })
            
            const res_data = res?.data?.list
            if (!res_data) {
                this.is_last_page = true
                return
            }
            
            const list = this.listInit(res_data.data || [])
            this.list = this.list.concat(list)
            this.is_last_page = res_data.is_lastpage
        },

        listInit(list = []) {
            return list.map(item => {
                // 有的数据是数组，有的是对象，统一转为对象
                item.content = Array.isArray(item.content) ? item.content[0] : item.content
                
                this.addCompleteData(item)  // 缓存完整的数据
                
                return {
                    id: item.id,
                    title: item.content?.content || '',
                    create_time: item.create_time
                }
            })
        },

        // 完整的列表数据，不用于页面渲染，用于传递到详情页
        addCompleteData(item) {
            this.completeData ||= {}
            
            // if (this.completeData[item.id]) return // 阻止重复添加
            // if (this.completeData[item.id]) return 不能阻止重复添加  因为列表刷新数据可能有变
            // if (this.completeData[item.id]) return // 可以阻止重复添加 因为生成以后的数据无法人为更改
            
            this.completeData[item.id] = item
        },

        lookDetails(id) {
            const item = this.completeData[id]
            if (!item) return this.$uni.showToast('数据异常', 'error')
            const data = {
                title: item.content?.content || '',
                create_time: item.create_time,
                content: item.content?.result?.content || ''
            }
            this.$uni.navigateTo('./details', {
                success: res => res.eventChannel.emit('details', data)
            })
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 20px;
}
.list {
    padding: 20px 0;
    
    .item {
        margin: 0 10px 20px;
        border-radius: 10px;
        
        .title {
            word-break: break-all;
            white-space: pre-wrap;
        }
    }
}
</style>
