<template>
    <view class="page">
        <view class="line-feed p10">
            <text class="color-title" space="nbsp">{{ title }}</text>
        </view>
        <view class="flex-kai p10" style="padding-top: 0;">
            <view class="color-sub font12">{{ create_time }}</view>
            <view class="flex-row">
                <view class="pr10" @click="copy">
                    <text class="iconfont icon-copy color-light-primary"></text>
                    <text class="color-light-primary" style="padding-left: 2px;">复制</text>
                </view>
                <view class="pl10" @click="exportWord">
                    <text class="iconfont icon-word-file color-light-primary"></text>
                    <text class="color-light-primary" style="padding-left: 2px;">导出</text>
                </view>
            </view>
        </view>
        <view class="content line-feed p10">
            <text class="color-content" space="nbsp">{{ content }}</text>
        </view>
        
        <uni-popup ref="exportWord">
            <view class="export-word bg-white text-center">
                <view class="p10">
                    <text class="iconfont icon-word-file color-light-primary" style="font-size: 100px;"></text>
                </view>
                <view class="p10 color-content">{{ exportWordTips }}</view>
                <view class="flex-all-center p10">
                    <view class="copy-button bg-primary color-white" @click="copyDownloadSrc">复制下载地址</view>
                </view>
            </view>
            <view class="flex-all-center pt10" @click="$refs.exportWord.close()">
                <uni-icons type="close" color="#ffffff" size="28"/>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    data() {
        return {
            title: '',
            content: '',
            create_time: '',
            exportWordTips: ''
        }
    },

    onLoad() {
        this.getDetails()
    },

    methods: {
        getDetails() {
            this.getOpenerEventChannel().on('details', res => {
                let {title, content, create_time} = res
                this.title = title
                content = Array.isArray(content) ? content.join('\n\n') : content
                this.content = content
                this.create_time = create_time
            })
        },

        copy() {
            this.$uni.setClipboardData(`${this.title}\n\n\n\n${this.content}`, '复制成功', 'success')
        },
        
        async exportWord() {
            if (this.wordDownloadSrc) {
                this.$refs.exportWord.open()
                return this.$uni.setClipboardData(this.wordDownloadSrc)
            }
            this.$uni.showLoading('导出中...')
            const res = await this.xwy_api.request({
                url: 'front.user.export.word.exportWord/download',
                data: {
                    file_name: '活动方案',
                    content: `${this.title}\n\n\n\n${this.content}`.replace(/\n/g, '<br>')
                }
            })
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '导出失败')
            if (!res?.data?.url) return this.$uni.showModal('导出失败')
            this.exportWordTips = res.info || '导出成功'
            this.wordDownloadSrc = res.data.url
            this.$refs.exportWord.open()
            this.$uni.setClipboardData(this.wordDownloadSrc)
        },

        copyDownloadSrc() {
            this.$uni.setClipboardData(this.wordDownloadSrc, '复制成功', 'success')
            this.$refs.exportWord.close()
        }
    }
}
</script>

<style lang="scss">
.page {
    padding-bottom: 20px;
}

.line-feed {
    word-break: break-all;
    white-space: pre-wrap;
}

.copy-button {
    width: 200px;
    line-height: 44px;
    border-radius: 22px;
}

.export-word {
    width: 90vw;
    max-width: 400px;
    padding: 10px;
    border-radius: 10px;
}
</style>
