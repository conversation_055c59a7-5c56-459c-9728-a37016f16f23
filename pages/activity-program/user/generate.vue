<template>
    <view class="page bg-background">
        
        <view class="recommend p10">
            <view class="color-title font14">推荐</view>
            <view class="recommend-list">
                <view class="recommend-item" v-for="(item, index) in recommendList" :key="index" 
                      @click="useRecommend(item.title)">
                    <view class="recommend-item-text bg-white color-content font14">{{ item.title }}</view>
                </view>
            </view>
        </view>
        
        <view v-if="ai_loading" class="ai-loading text-center">
            <load-ani/>
            <view class="color-sub font14">生成中</view>
        </view>

        <view class="input-view bg-white">
            <view class="pb10 clearfix clear">
                <button class="button-none text-center font14 color-sub fl" open-type="contact">
                    <text>可用次数: {{ can_used_time }}次</text>
                    <text class="pl5 color-light-primary">(增加次数)</text>
                </button>
                <navigator url="./history" class="color-light-primary font14 fr">历史记录</navigator>
            </view>
            <view class="flex-row">
                <view style="width: 100%;">
                    <uni-easyinput
                        type="textarea"
                        v-model="content"
                        :maxlength="500"
                        auto-height
                        :disabled="ai_loading || can_used_time <= 0"
                        :placeholder="can_used_time <= 0 ? '当前无可用次数' : '请输入活动描述 500字以内'"
                    />
                </view>
                <view style="display: flex; flex-direction: column-reverse">

                    <view
                        :hover-class="content.length ? 'navigator-hover' : 'none'"
                        class="send flex-all-center"
                        :class="{
                        'bg-primary': content.length && !ai_loading,
                        'bg-disabled': !content.length || ai_loading || can_used_time <= 0
                    }"
                        @click="generate"
                    >
                        <text class="iconfont icon-send color-white font24"></text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import login from '@/utils/api/login.js'

export default {
    data() {
        return {
            content: '',
            ai_loading: false,
            can_used_time: 0,
            recommendList: []
        }
    },

    onLoad() {
        this.$uni.showLoading('加载中...')
        login.uniLogin(() => this.getChatSystemSet())
    },

    methods: {
        async getChatSystemSet() {
            const res = await this.xwy_api.request({
                url: 'front.flat.active.chat.user.shop/chat_system_set'
            })
            
            if (res?.status !== 1) {
                uni.hideLoading()
                return this.$uni.showModal(res?.info || '配置获取失败', {success: () => uni.navigateBack()})
            }
            
            if (res?.data?.can_used_time) this.can_used_time = res.data.can_used_time
            const role_id = res?.data?.['shop_set']?.['ChatGPT']?.['role_id']
            if (role_id) {
                this.role_id = role_id
                await this.getRoleDetails(role_id)
            }
            
            uni.hideLoading()
        },
        
        async getRoleDetails(id) {
            const res = await this.xwy_api.request({
                url: 'front.flat.active.chat.user.shop/role_details',
                data: {id}
            })
            const recommendList = res?.data?.['role_details']?.conf_json?.['eg_list']
            if (recommendList?.length) this.recommendList = recommendList
        },

        useRecommend(content) {
            if (!this.can_used_time || this.can_used_time <= 0) return this.$uni.showToast('当前无可用次数')
            
            if (!this.content) {
                this.content = content
                return
            }
            
            this.$uni.showModal('是否覆盖已输入的内容', {
                showCancel: true,
                success: res => {
                    if (res.confirm) this.content = content
                }
            })
        },
            
        async generate() {
            if (!this.content) return this.$uni.showToast('请输入活动描述')
            if (this.content.length > 500) return this.$uni.showToast('活动描述不能超过500字')
            
            const data = {content: this.content}
            if (this.role_id) data.role_id = this.role_id
            
            this.ai_loading = true
            const res = await this.xwy_api.request({
                url: 'front.flat.active.chat.user.chat/send_query',
                data
            })
            this.ai_loading = false
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '生成失败')
            this.can_used_time--
            
            const params = {
                title: this.content,
                content: res.data.result.content,
                create_time: this._utils.timestampToTime(new Date().getTime(), true)
            }
            this.$uni.navigateTo('./details', {
                success: res => {
                    res.eventChannel.emit('details', params)
                    this.content = ''
                    this.getChatSystemSet()
                }
            })
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 180px;
}

.recommend-item {
    margin: 10px 0;
    
    .recommend-item-text {
        display: inline-block;
        line-height: 32px;
        border-radius: 16px;
        padding: 0 10px;
    }
}

.line-feed {
    word-break: break-all;
    white-space: pre-wrap;
}

.ai-loading {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background-color: #fff;
    z-index: 100;
    padding-top: 30vh;
    box-sizing: border-box;
}

.input-view {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100vw;
    box-sizing: border-box;
    padding: 10px 10px 20px 10px;
    border-top: 1px solid #eee;
    z-index: 99;
    .send {
        width: 40px;
        min-width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-left: 10px;
    }
}
</style>
