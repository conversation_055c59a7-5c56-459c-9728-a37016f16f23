<template>
    <view class="page bg-background">
        <view class="top-view">
            <view class="search bg-white flex-kai p10">
                <view class="input-view">
                    <view class="search-icon left-icon flex-all-center">
                        <uni-icons type="search" size="20" color="#bbbec4"/>
                    </view>

                    <input
                        class="input bg-background"
                        type="text"
                        confirm-type="search"
                        v-model="search_keyword"
                        @confirm="search()"
                        :placeholder="`输入${search_user_key}搜索`"
                        placeholder-style="color:#bbbec4"
                    />
                    <view class="search-icon right-icon flex-all-center" @click="search_keyword = ''">
                        <uni-icons v-if="search_keyword" type="close" size="20" color="#bbbec4"/>
                    </view>
                </view>
                <view class="search-go color-info pl10" @click="search">搜索</view>
            </view>


            <view class="top-tab bg-white flex-row">
                <view
                    class="top-tab-item text-center color-sub"
                    :class="{'active-top-bar': item.id === list_type}"
                    :style="'width: calc(100% / ' + top_tab_list.length + ');'"
                    v-for="(item, index) in top_tab_list"
                    :key="index"
                    @click="listTypeChange(item.id)"
                >{{ item.title }}
                </view>
            </view>
        </view>

        <view v-if="total_count" class="color-sub text-center font14" style="padding-top: 10px;">
            共{{ total_count }}条记录
        </view>


        <view class="list">
            <view class="item bg-white" v-for="(item, index) in list" :key="index">

                <view class="flex-row">
                    <view class="pl10">
                        <image v-if="item.headimg" class="headimg" :src="item.headimg" mode="aspectFill"/>
                    </view>

                    <view class="middle">
                        <template v-for="(must, m_index) in item.must_submit">
                            <view v-if="must.value !== ''" class="flex-kai pb5 color-title"
                                  :key="m_index">
                                <view>{{ must.title }}: {{ must.value || ' ' }}</view>
                                <view v-if="m_index === 0" class="pl10" style="min-width: 60px;">
                                    <uni-tag :text="getTypeTitle(item.amount ? 1 : 2)" size="mini"
                                             :type="item.amount ? 'success' : 'default'" inverted/>
                                </view>
                            </view>
                        </template>
                        <view v-if="item.amount" class="pb5 color-title">支付金额: {{ item.amount }}元</view>


                        <view class="color-sub font12">
                            <uni-dateformat :date="item.create_time" format="yyyy-MM-dd hh:mm:ss"
                                            :threshold="[0, 0]"/>
                        </view>
                    </view>
                </view>

                <view v-if="is_admin" class="clear clearfix">
                   <view class="fr" @click="deleteRecord(item.id)">
                       <uni-icons type="trash" color="#80848f" size="14"/>
                       <text class="color-sub font14">删除</text>
                   </view> 
                </view>
            </view>
        </view>


        <view v-if="!list.length && !more_loading && !init_load" class="text-center"
              style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无记录</view>
        </view>

        <uni-load-more v-if="more_loading" status="loading"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && list.length > 5"
            status="noMore"
            :contentText="{contentnomore: '我是有底线的'}"
        ></uni-load-more>
        <uni-load-more v-if="!is_last_page && !more_loading && !init_load" status="more"></uni-load-more>


    </view>
</template>

<script>
const app = getApp()

export default {

    data() {
        return {
            id: '',
            top_tab_list: [
                {title: '所有记录', id: ''},
                {title: '已支付', id: 1},
                {title: '未支付', id: 2}
            ],
            list_type: '',
            total_count: 0,
            init_load: true,
            list: [],
            load_page: 1,
            is_last_page: false,
            more_loading: false,
            search_keyword: '',
            search_user_key: '用户名字',
            is_admin: false
        }
    },


    onLoad(e) {
        this.id = e.id
        if (e.myself) this.myself = true

        uni.showLoading({
            mask: true
        })
        this.$login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getActivityDetail()
        })
    },


    onPullDownRefresh() {
        if (this.init_load || this.more_loading) return uni.stopPullDownRefresh()
        this.load_page = 1
        this.getList()
        uni.stopPullDownRefresh()
    },
    onReachBottom() {
        if (!this.init_load && !this.more_loading && !this.is_last_page) this.getList()
    },
    methods: {
        getTypeTitle(id) {
            return this.top_tab_list.find(item => item.id === id)?.title || ''
        },

        async getActivityDetail() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id
                }
            })

            const details = res['data']['active_details']

            if (details?.conf?.must_submit?.[0]?.title)
                this.search_user_key = details.conf.must_submit[0].title

            this.is_admin = app.globalData['userid'] === details.userid
            this.activity_detail = details
            this.getList()
        },


        listTypeChange(id) {
            if (id === this.list_type) return
            this.list_type = id
            this.search()
        },

        search() {
            this.load_page = 1
            this.getList()
        },

        getList() {
            if (this.load_page === 1) {
                this.total_count = 0
                this.list = []
                this.is_last_page = false
                if (!this.init_load) this.init_load = true
                this.$uni.showLoading()

            } else {
                this.more_loading = true
            }

            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id,
                page: this.load_page,
                perpage: 20
            }

            if (this.list_type) data.pay_state = this.list_type
            if (this.search_keyword) data.truename = this.search_keyword
            if (this.myself) data.only_self = 1


            this.xwy_api.ajax({
                url: 'front.flat.sport_step.admin/active_attend_user_list',
                data,
                success: res => {
                    console.log('活动用户数据', res);
                    
                    if (this.load_page === 1) {
                        this.init_load = false
                        uni.hideLoading()
                    } else {
                        this.more_loading = false
                    }


                    this.load_page++

                    if (!res.status) {
                        this.is_last_page = true
                        return
                    }

                    const data = res.data.user_list

                    const list = data.data || []

                    this.list = [...this.list, ...list]
                    this.is_last_page = data.is_lastpage
                    this.total_count = data.total

                }
            })
        },

        async deleteRecord(id) {
            const modal = await this.$uni.showModal('确定删除该记录？', {showCancel: true})
            if (!modal.confirm) return
            
            this.$uni.showLoading('删除中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/batch_change_user',
                data: {
                    active_id: this.id,
                    ids: id,
                    act_types: 3
                }
            })
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '删除失败')
            this.$uni.showToast('已删除')
            this.search()
        }
    }
}
</script>

<style scoped lang="scss">
.page {
    min-height: 100vh;
    padding-top: 100px;
    padding-bottom: 100px;
    box-sizing: border-box;
}

.top-view {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 999;
}

.top-tab-item {
    height: 40px;
    line-height: 40px;
    box-sizing: border-box;
}

.active-top-bar {
    color: #2D8CF0;
    border-bottom: 2px solid #2D8CF0;
}

.search, .search .input {
    height: 40px;
    line-height: 40px;
}

.input-view {
    position: relative;
    width: 100%;
}

.search-icon {
    position: absolute;
    width: 30px;
    height: 40px;
    top: 0;
}

.left-icon {
    left: 0;
}

.right-icon {
    right: 0;
}

.search .input {
    /* width: calc(100% - 80px); */
    padding: 0 30px;
    border-radius: 20px;
    box-sizing: border-box;
}

.search-go {
    width: 60px;
    min-width: 60px;
    text-align: right;
}


/* #ifdef H5 */
.search-go {
    padding-right: 10px;
    box-sizing: border-box;
}

@media screen and (min-width: 500px) {
    .top-view {
        width: 500px;
        left: calc(((100vw - 500px) / 2) - 10px);
    }

    .search .input {
        width: 440px;
    }
}

/* #endif */


.item {
    margin: 10px;
    padding: 10px 10px 10px 0;
    border-radius: 10px;
}

.headimg {
    width: 50px;
    min-width: 50px;
    height: 50px;
    border-radius: 50%;
    display: block;
}

.middle {
    width: 100%;
    padding-left: 10px;
    overflow: hidden;
    box-sizing: border-box;
}

</style>
