<template>
    <view class="page bg-background">

        <screen-picture
            v-if="screen_pic_show"
            :hide="screen_pic_hide"
            :image-src="screen_pic"
            :time="screen_pic_count_down"
            :show-button="true"
            @skipScreen="skipScreen"
        />


        <activity-delete-tips v-if="error" :activeId="id"/>

        <view v-if="detail && detail.active_id">
            <button class="fixed-icon button-none" open-type="contact">
                <uni-icons type="chat" size="34" color="#ffffff"/>
            </button>


            <navigator v-if="is_my_activity" class="fixed-icon fixed-icon-manage" 
                       :url="'../admin/manage?id=' + id">
                <uni-icons type="gear" size="34" color="#ffffff"/>
            </navigator>


            <navigator class="fixed-icon fixed-icon-user-record" 
                       :url="'../submit-record?myself=1&id=' + id">
                <uni-icons type="list" size="30" color="#ffffff"/>
            </navigator>

            <template v-if="detail.conf && detail.conf.active && detail.conf.active.notice">
                <uni-notice-bar :text="detail.conf.active.notice" scrollable/>
            </template>

            <view v-if="detail.logo">
                <image :src="detail.logo" mode="widthFix" style="width: 100%; display: block;"/>
            </view>


            <view class="form-submit-view">
                <view v-if="buy_project_more" class="form-submit-item">
                    <view class="color-white pb5">项目</view>
                    <view class="form-submit-input-container flex-kai" @click="changeBuyProject">
                        <view>
                            <view v-if="buy_project_id">{{ buy_project_name || buy_project_id }}</view>
                            <view v-else class="form-submit-placeholder">请选择项目</view>
                        </view>
                        <view>
                            <uni-icons type="forward" size="16" color="#80848f"/>
                        </view>
                    </view>
                </view>

                <view class="form-submit-item" v-for="(item, index) in must_submit" :key="index">
                    <view class="color-white pb5">
                        <text>{{ item.title }}</text>
                        <text v-if="item.rules === 1" class="color-red" style="padding-left: 2px;">*</text>
                    </view>
                    <view class="form-submit-input-container">

                        <input
                            v-if="item.types === 1"
                            class="form-submit-input"
                            v-model="item.value"
                            :placeholder="`请输入 ${item.title}`"
                        />
                        <picker v-if="item.types === 2" :range="item.options" range-key="text"
                                @change="mustValueChange($event, index)">
                            <view v-if="item.value">{{ item.value }}</view>
                            <view v-else class="form-submit-placeholder">请选择 {{ item.title }}</view>
                        </picker>
                        <view v-if="item.types === 3">
                            <view v-if="item.value" class="form-submit-image-view">
                                <view class="form-submit-image-clear flex-all-center"
                                      @click="mustSubmitImageClear(index)">
                                    <uni-icons type="closeempty" color="#ececec"/>
                                </view>
                                <image class="form-submit-image" :src="item.value" mode="aspectFill"/>
                            </view>
                            <view v-else>
                                <view class="form-submit-image-upload flex-all-center"
                                      hover-class="navigator-hover"
                                      @click="mustSubmitUploadImage(index)">
                                    <uni-icons type="plusempty" size="50" color="#eeeeee"/>
                                </view>
                                <view class="form-submit-placeholder pt10">请上传 {{ item.title }}</view>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="flex-all-center p10">
                    <view class="sign-up-now-button color-white text-center" hover-class="navigator-hover"
                          @click="formSubmitSignUpCheck">立即申请
                    </view>
                </view>

                <view v-if="haveDeclaration" class="flex-all-center">
                    <view class="font14">

                        <checkbox :checked="read_and_agree_declaration"
                                  @click="read_and_agree_declaration = !read_and_agree_declaration"/>
                        <text class="color-title">我已阅读并同意</text>
                        <text class="color-red" @click="declarationPopupShow">{{ declarationTitle }}</text>
                    </view>
                </view>
            </view>


            <view v-if="detail.content || content_news_id" class="p10">
                <view v-if="detail.content && !content_news_id">
                    <text space="nbsp">{{ detail.content }}</text>
                </view>
                <form-submit-content v-if="content_news_id" :news-id="content_news_id"/>
            </view>


            <xwy-ad v-if="!password_dialog_show && !popup_open && !closed_AD" :ad_type="4"/>

            <view v-if="technology_support" class="flex-all-center" style="padding-top: 30px;">

                <navigator
                    v-if="technology_support.news_id"
                    :url="'/pages/news/preview?id=' + technology_support.news_id + '&type=support'"
                    class="text-center font14 color-sub p10"
                >{{ technology_support.button_text }}
                </navigator>
                <view v-else class="text-center font14 color-sub p10">
                    {{ technology_support.button_text }}
                </view>
            </view>

            <view v-if="!detail.rank_set || !detail.rank_set['closed_user_center']" class="flex-all-center">
                <navigator url="/pages/user/user" class="p10 color-sub font14">个人中心</navigator>
            </view>

            <xwy-ad v-if="!closed_AD && !screen_pic_show" :ad_type="3"/>

        </view>


        <active-share ref="activeShare"/>


        <template v-if="password_dialog_show">
            <uni-popup
                ref="input_password"
                type="dialog"
                mode="input"
                :is-mask-click="false"
                @maskClick="copy(id)"
            >
                <uni-popup-dialog
                    mode="input"
                    title="活动密码"
                    :value="password"
                    placeholder="请输入活动密码"
                    @confirm="passwordInputConfirm"
                    @close="passwordInputClose"
                ></uni-popup-dialog>
            </uni-popup>
        </template>


        <expiration-reminder ref="expirationReminder"/>


        <template v-if="haveDeclaration">
            <uni-popup ref="declarationPopup" type="bottom" :safe-area="false" @touchmove.stop.prevent=""
                       @maskClick="uniPopupClose('declarationPopup')">
                <view class="declaration-popup bg-white">
                    <view class="color-title font18 p10 text-center">{{ declarationTitle }}</view>

                    <scroll-view class="declaration-popup-content" :scroll-y="true">
                        <form-submit-content :news-id="detail.conf.active.active_details_notice.news_id"/>
                    </scroll-view>
                    <view class="flex-all-center pt15">
                        <view class="agree-button bg-light-primary color-white font14"
                              hover-class="navigator-hover" @click="readAndAgree">我已阅读并同意
                        </view>
                    </view>
                </view>
            </uni-popup>
        </template>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import my_storage from '@/utils/storage.js'
import base64 from '@/utils/base64.js'

import formSubmitContent from "../components/form-submit-content.vue"

export default {
    components: {formSubmitContent},
    data() {
        return {
            evn_version: app.globalData['evn_version'],
            loading: true,
            screen_pic: '',
            screen_pic_show: false,
            screen_pic_hide: false,
            screen_pic_count_down: null,
            id: '',
            userid: '',
            is_my_activity: false,
            detail: {},
            error: '',
            password: '',
            technology_support: false,
            must_submit: [],
            password_dialog_show: false,
            popup_open: false,
            platform: uni.getSystemInfoSync().platform,
            read_and_agree_declaration: false,
            buy_project_id: '',
            buy_project_name: '',
            buy_project_more: false
        }
    },

    computed: {
        content_news_id() {
            return this.detail?.conf?.active?.news?.news_id
        },

        haveDeclaration() {
            return !!(this.detail?.conf?.active?.active_details_notice?.open && this.detail.conf.active.active_details_notice.news_id)
        },

        declarationTitle() {
            return this.detail?.conf?.active?.active_details_notice?.news_title || '个人信息保护声明'
        },

        closed_AD() {
            return this.detail?.rank_set?.closed_AD || 0
        }
    },

    onUnload() {
        if (this.interval) clearInterval(this.interval)
    },

    onLoad(e) {
        console.log('活动详情页面路径参数', e)

        // #ifdef H5
        this.$uni.showModal('请在小程序内打开', {success: () => uni.navigateBack()})
        // #endif


        // #ifndef H5
        this.init(e)
        // #endif
    },


    onShareAppMessage() {
        this._utils.shareCopyActivityId(this.detail)

        let url = '/pages/form-submit/user/details?id=' + this.id;
        if (this.detail?.conf?.active?.screen_pic) url += `&screen_pic=${this.detail.conf.active.screen_pic}`

        return {
            title: this.detail.name,
            path: url,
            imageUrl: this.detail.logo || ''
        }
    },

    onShareTimeline() {
        return {
            title: this.detail.name,
            imageUrl: this.detail.logo || ''
        }
    },

    methods: {
        init(e) {
            if (uni.getLaunchOptionsSync().scene === 1154) return this.getSimpleDetail(e.id)

            e.screen_pic ? this.screenPicShow(e.screen_pic) : this.$uni.showLoading('数据加载中...')

            login.uniLogin(err => {
                if (err && err.errMsg) {
                    uni.hideLoading()
                    return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
                }
                
                if (e.scene) return this.analysisScene(e.scene)

                if (!e.id) {
                    this.loading = false
                    this.error = '请指定活动id'
                    return uni.hideLoading()
                }


                this.id = e.id

                this.userid = app.globalData['userid']

                this.getDetail()
            })
        },

        async getSimpleDetail(id) {
            this.$uni.showLoading('数据加载中...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details_simple',
                data: {
                    active_id: id
                }
            })
            this.loading = false
            uni.hideLoading()

            const details = res?.data?.active_details
            if (!details) {
                this.loading = false
                this.error = (res?.info || '活动详情获取失败。') + '活动id：' + this.id
                return uni.hideLoading()
            }


            if (res['data'].active_more_data) {
                const active_more_data = res['data']['active_more_data']
                this.active_more_data = active_more_data
                if (active_more_data['technology_support']) {
                    this.technology_support = res['data'].active_more_data.technology_support
                }
            }


            my_storage.setActivityCloaeAdStorage(this.id, details.rank_set?.closed_AD)

            details.conf ||= {}

            const conf = details.conf
            conf.active ||= {}

            if (!this.screen_pic && conf.active?.screen_pic) {
                this.screenPicShow(conf.active.screen_pic)
            }


            if (conf.must_submit) {
                const must_submit = conf.must_submit
                delete conf.must_submit
                if (must_submit.length) {
                    must_submit.forEach(v => v.value = v.value || '')
                    this.must_submit = must_submit
                }
            }

            this.detail = details
            
            this.addLookRecords()

            if (details.name) this.$uni.setNavigationBarTitle(details.name)
        },


        screenPicShow(src) {
            this.screen_pic = src
            this.screen_pic_count_down = 5
            this.screen_pic_show = true

            this.interval = null
            this.interval = setInterval(() => {
                this.screen_pic_count_down--
                if (this.screen_pic_count_down <= 0) this.skipScreen()
            }, 1000)
        },
        skipScreen() {
            clearInterval(this.interval)
            this.screen_pic_count_down = 0
            this.screen_pic_hide = true

            const timeout = setTimeout(() => {
                this.screen_pic_show = false
                this.screen_pic_hide = false
                if (this.loading) this.$uni.showLoading('数据加载中...')
                this.passwordDialogShow()
                clearTimeout(timeout)
            }, 500)
        },
        analysisScene(scene) {
            const sceneStr = decodeURIComponent(scene)
            console.log(sceneStr)
            const id = utils.getUrlParams('id', sceneStr)
            console.log('id===', id)
            if (!id) {
                uni.hideLoading()
                return this.$uni.showModal('从二维码获取id失败')
            }

            this.getActiveId(id)
        },


        async getActiveId(id) {
            const data = {id}

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/get_long_active_id_by_short_id',
                data
            })
            if (res?.['data']?.['long_active_id']) {
                this.id = res['data']['long_active_id']
                this.getDetail()
            } else {
                uni.hideLoading()
                xwy_api.alert(res && res['info'] || '长id获取失败')
            }
        },

        getDetail(just_update = false) {
            xwy_api.getActivityDetail(this.id, res => {
                const details = res?.data?.active_details
                if (!details) {
                    this.loading = false
                    this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                    return uni.hideLoading()
                }

                // #ifndef H5
                this.$refs.expirationReminder.open(res.data.active_details)
                // #endif

                if (res.data.active_more_data) {
                    const active_more_data = res.data.active_more_data
                    this.active_more_data = active_more_data
                    if (active_more_data.technology_support) {
                        this.technology_support = res.data.active_more_data.technology_support
                    }
                }


                app.globalData.activity_detail = details

                my_storage.setActivityCloaeAdStorage(this.id, details.rank_set?.closed_AD)

                details.conf ||= {}

                const conf = details.conf
                conf.active ||= {}

                if (conf.active) {
                    const active = conf.active
                    if (!this.screen_pic && active.screen_pic) {
                        this.screenPicShow(active.screen_pic)
                    }
                }

                if (conf.must_submit) {
                    const must_submit = conf.must_submit
                    delete conf.must_submit
                    if (must_submit.length) {
                        must_submit.forEach(v => v.value = v.value || '')
                        this.must_submit = must_submit
                    }
                }

                this.detail = details

                if (details.rank_set?.['shield_other']) this.$uni.hideHomeButton()

                this.addLookRecords()

                if (details.name) this.$uni.setNavigationBarTitle(details.name)

                if (!just_update && app.globalData['userid'] === details.userid) {
                    this.is_my_activity = true
                }
                
                if (details.rank_set?.['active_pay']) return this.getProject()

                uni.hideLoading()
            })
        },
        
        async getProject() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_pay.adminManage/active_price_list',
                data: {
                    active_id: this.id
                }
            })
            uni.hideLoading()
            
            const price_list = res?.data?.['price_list']
            if (price_list?.length) {
                this.buy_project_id = price_list[0].id
                this.buy_project_name = price_list[0].name
                if (price_list.length > 1) this.buy_project_more = true
            }
        },

        changeBuyProject() {
            this.$uni.navigateTo(`/pages/registration/admin/project/list?choose=1&id=${this.id}`, {
                events: {
                    chooseProject: data => {
                        this.buy_project_id = data.id
                        this.buy_project_name = data.name
                    }
                }
            })
        },


        addLookRecords() {
            const detail = this.detail
            const value = {
                active_id: detail.active_id,
                name: detail.name,
                types: detail.types,
                logo: detail.logo || this.xwy_config.active_default_logo,
                look_time: new Date().getTime()
            }

            if (detail.organizer) value.organizer = detail.organizer
            my_storage.addActivityLookRecords(value)
        },
        

        passwordDialogShow() {
            if (this.detail.conf.active.password && this.no_attend) {
                const passwordDialogShow = () => {
                    this.password_dialog_show = true
                    const password = my_storage.getActivityPassword(this.id)
                    if (password) {
                        this.password = password
                        this.checkActivityPassword(password)
                    }
                    const timeout = setTimeout(() => {
                        this.$refs.input_password.open()
                        clearTimeout(timeout)
                    }, 30)
                }

                // 体验版不需要输入密码
                if (app.globalData['evn_version'] === 'trial') {
                    return this.$uni.showModal('此活动设置了活动密码，请勿报名参与活动！！！', {
                        cancelText: '进入活动',
                        confirmText: '输入密码',
                        success: res => res.confirm && passwordDialogShow()
                    })
                }

                passwordDialogShow()
            }
        },

        passwordInputConfirm(val) {
            if (!val) {
                // 纯净版取消输入密码出现去个人中心选项
                if (this.detail?.rank_set?.['shield_other']) {
                    return this.$uni.showModal('请输入密码', {
                        cancelText: '个人中心',
                        confirmText: '重新输入',
                        success: res => {
                            if (res?.confirm) return this.$refs.input_password.open()
                            this.$uni.navigateTo('/pages/user/user')
                        }
                    })
                }

                return xwy_api.alert('请输入密码', {success: () => this.$refs.input_password.open()})
            }
            this.checkActivityPassword(val)
        },

        async checkActivityPassword(password) {
            this.$uni.showLoading('密码验证中...')

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/check_active_password',
                data: {
                    active_id: this.id,
                    password
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) {
                return this.$uni.showModal(res?.info || '密码错误', {
                    success: () => this.$refs.input_password.open()
                })
            }

            my_storage.rememberActivityPassword(this.id, password)
            this.$refs.input_password.close()
            this.$uni.showToast('密码正确', 'success')
        },

        passwordInputClose() {
            this.copy(this.id, true)

            if (getCurrentPages().length > 1) return uni.navigateBack()

            // 纯净版，重新弹出输入密码窗口
            if (app.globalData['shop_info']?.['extend_set']?.['shield_other_active']?.active_id) {
                return this.$uni.showModal('请输入活动密码', {success: () => this.$refs.input_password.open()})
            }

            this.$uni.reLaunch('/pages/index/index')
        },


        mustValueChange(e, index) {
            this.$set(this.must_submit[index], 'value', this.must_submit[index].options[e.detail.value].text)
        },

        mustSubmitUploadImage(index) {
            this.$uni.navigateTo(`/pages/other/image_upload_or_select?active_id=${this.id}`, {
                events: {
                    newImg: src => this.$set(this.must_submit[index], 'value', src)
                }
            })
        },

        mustSubmitImageClear(index) {
            this.$set(this.must_submit[index], 'value', '')
        },


        getMustSubmitData() {
            const must_submit = JSON.parse(JSON.stringify(this.must_submit))
            for (let i = 0; i < must_submit.length; i++) {
                const v = must_submit[i]
                v.options && delete v.options
                if (v.rules === 1 && v.value === '') {
                    let tips = '输入'
                    if (v.types === 2) tips = '选择'
                    uni.showToast({
                        title: `请${tips}${v.title}`,
                        mask: true,
                        icon: v.title.length <= 4 ? 'error' : 'none'
                    })
                    return false
                }
            }
            console.log(must_submit)
            let must_submit_str = JSON.stringify(must_submit)
            must_submit_str = must_submit_str.replaceAll('·', '-')
            return base64['encode'](must_submit_str)
        },

        showActiveSharePopup() {
            this.$refs.activeShare.open({
                page: 'pages/form-submit/user/details',
                scene: 'id=' + this.detail.id,
                qrcode_logo: this.closed_AD && this.detail.conf?.active?.qrcode_logo
            })
        },

        copy(data, hide_toast = false) {
            uni.setClipboardData({
                data,
                success: () => hide_toast ? uni.hideToast() : this.$uni.showToast('复制成功', 'none', 500)
            })
        },

        uniPopupClose(ref) {
            this.popup_open = false
            this.$refs[ref].close()
        },

        uniPopupOpen(ref) {
            this.popup_open = true
            this.$refs[ref].open()
        },


        formSubmitSignUpCheck() {
            let {begin_time, end_time} = this.detail
            begin_time *= 1000
            end_time *= 1000
            const now = new Date().getTime()
            if (now < begin_time) return this.$uni.showToast('未到报名时间')
            if (now > end_time) return this.$uni.showToast('报名已结束')


            for (let i = 0, len = this.must_submit.length; i < len; i++) {
                const item = this.must_submit[i]
                if (item.rules === 1 && !item.value) {
                    const tips = ['', '填写', '选择', '上传']
                    return this.$uni.showToast(`请${tips[item.types]} ${item.title}`)
                }
            }

            const {haveDeclaration: have, read_and_agree_declaration: agree, declarationTitle: title} = this
            if (have && !agree) return this.$uni.showToast(`请阅读并同意${title}`)
            
            this.formSubmitSignUp()
        },

        async formSubmitSignUp() {
            const data = {
                active_id: this.id
            }

            const must_submit = JSON.parse(JSON.stringify(this.must_submit))
            must_submit.forEach(item => {
                if (item.hasOwnProperty('options')) delete item.options
            })

            data.must_submit = base64['encode'](JSON.stringify(must_submit))

            this.$uni.showLoading('报名中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.user/submit_attend_active',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '报名失败')

            if (this.buy_project_id) return this.buyProject()
            this.submitSuccess()
        },

        clearMustSubmitValue() {
            this.must_submit.forEach(v => v.value = '')
        },


        async buyProject() {
            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_pay.userBuy/submit_for_buy_active',
                data: {
                    id: this.buy_project_id,
                    active_id: this.id,
                    num: 1
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '提交失败，请重试！')
            if (!res?.data?.details) return this.$uni.showModal('提交失败，请重试！')
            if (!res?.data?.details?.amount) return this.submitSuccess()
            await this.toPay(res.data.details)
        },

        async toPay(data) {
            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.user.wechat.pay.small_app/small_app_create_pay',
                data: {
                    shopid: app.globalData['who'],
                    openid: app.globalData['openid'],
                    amount: data.amount,
                    foreign_orderid: this.id,
                    profit_sharing: 1, // 【1】代表需要进行分账
                    types: 12,
                    name: '付费报名'
                }
            })
            uni.hideLoading()
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '下单失败！请重试！')
            if (!res?.data?.['create_res']) return this.$uni.showModal('下单失败！请重试！')

            this.requestPayment(res.data['create_res'])
        },

        requestPayment(data) {
            wx.requestPayment({
                timeStamp: data.timeStamp,
                nonceStr: data.nonceStr,
                package: data.package,
                signType: data.signType || 'MD5',
                paySign: data.paySign,
                success: () => this.submitSuccess(),
                fail: res => {
                    console.log('支付失败', res)
                    if (res?.errMsg && res.errMsg !== 'requestPayment:fail cancel') 
                        return this.$uni.showModal(res?.errMsg, {title: '支付失败'})
                }
            })
        },
        
        submitSuccess() {
            this.$uni.navigateTo(`./submit-success?id=${this.id}`)
            this.clearMustSubmitValue()
        },

        declarationPopupShow() {
            this.uniPopupOpen('declarationPopup')
        },

        readAndAgree() {
            this.read_and_agree_declaration = true
            this.uniPopupClose('declarationPopup')
        }

    }
}
</script>

<style scoped lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 80px;
}


.form-submit-view {
    padding: 10px 0;
    background-color: #3296F1;

    .form-submit-item {
        padding: 10px;

        .form-submit-input-container {
            padding: 10px;
            border-radius: 5px;
            background-color: #fff;

            .form-submit-placeholder {
                color: #808080;
            }

            .form-submit-image-view {
                position: relative;

                .form-submit-image-clear {
                    position: absolute;
                    top: 5px;
                    right: 5px;
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    background-color: rgba(255, 255, 255, .5);
                }

                .form-submit-image {
                    display: block;
                }
            }

            .form-submit-image-view, .form-submit-image, .form-submit-image-upload {
                width: 120px;
                height: 120px;
            }

            .form-submit-image-view, .form-submit-image-upload {
                border-radius: 5px;
                overflow: hidden;
            }

            .form-submit-image-upload {
                border: 1px solid #EBF1F8;
                box-sizing: border-box;
            }
        }
    }

    .sign-up-now-button {
        width: 200px;
        line-height: 44px;
        background-color: #CC0100;
        border-radius: 10px;
    }
}

.declaration-popup {
    padding: 10px 10px 20px;
    border-radius: 10px 10px 0 0;

    .declaration-popup-content {
        height: 70vh;
    }

    .agree-button {
        line-height: 40px;
        padding: 0 20px;
        border-radius: 5px;
    }
}


.fixed-icon {
    $size: 44px;
    position: fixed;
    z-index: 99;
    right: 10px;
    bottom: 80px;
    width: $size;
    height: $size;
    border-radius: 50%;
    text-align: center;
    line-height: $size;
    background-color: rgba(0, 0, 0, .5);
}

.fixed-icon-user-record {
    bottom: 130px;
}
.fixed-icon-manage {
    bottom: 180px;
}
</style>
