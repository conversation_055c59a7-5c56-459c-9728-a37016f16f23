<template>
    <view class="color-title">
        <u-parse v-if="newsContent" :content="newsContent"/>

        <!--<rich-text :nodes="newsContent" space="nbsp"/>-->
    </view>
</template>

<script>
export default {
    name: "form-submit-content",
    props: ['newsId'],
    data() {
        return {
            newsContent: ''
        }
    },

    mounted() {
        this.getNewsDetails()
    },

    methods: {
        async getNewsDetails() {
            const res = await this.xwy_api.request({
                url: 'front.news/news_details',
                data: {
                    news_id: this.newsId
                }
            })
            const content = res?.data?.['news_details']?.content
            if (content) {
                this.newsContent = this._utils.newsContentInit(content)
            }
        }
    }
}
</script>