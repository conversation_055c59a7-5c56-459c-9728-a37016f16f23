<template>
    <view class="page bg-background">
        <activity-delete-tips v-if="error" :activeId="id"/>

        <view v-if="detail && detail.active_id">
            <!-- #ifndef H5 -->
            <image v-if="detail.logo" class="logo" mode="widthFix" :src="detail.logo"/>
            <!-- #endif -->
            <view class="bg-white p10">
                <view class="color-title font18" @click="copy(id)">{{ detail.name }}</view>
                <view v-if="detail.organizer" class="color-content font16">
                    主办方：{{ detail.organizer }}
                </view>
            </view>

            <template v-if="!closed_AD">
                <xwy-ad v-if="!popup_show" :ad_type="4"></xwy-ad>
                <xwy-ad :ad_type="3"></xwy-ad>
            </template>


            <view class="tools bg-white">
                <view class="title color-title">活动管理</view>
                <view class="list flex-row flex-wrap text-center">

                    <navigator :url="'./add?id=' + id" class="item">
                        <text class="iconfont icon-edit color-primary font34"></text>
                        <view class="font14 color-content">活动修改</view>
                    </navigator>


                    <navigator v-if="detail.rank_set && detail.rank_set['active_pay']" class="item"
                               :url="'/pages/registration/admin/project/list?id=' + id">
                        <text class="iconfont icon-dating color-primary font34"></text>
                        <view class="font14 color-content">项目管理</view>
                    </navigator>
                    

                    <navigator :url="'../submit-record?id=' + id" class="item">
                        <uni-icons type="list" size="34" color="#2d8cf0"/>
                        <view class="font14 color-content">提交记录</view>
                    </navigator>


                    <navigator :url="'/pages/activity/admin/blacklist?id=' + id" class="item">
                        <text class="iconfont icon-blacklist color-primary font34"></text>
                        <view class="font14 color-content">黑名单用户</view>
                    </navigator>


                    <navigator class="item" :url="'/pages/news/list?type=user&vip=' + closed_AD">
                        <text class="iconfont icon-writing font34 color-primary"></text>
                        <view class="font14 color-content">文章管理</view>
                    </navigator>
                    
                    <navigator v-if="closed_AD" class="item" url="/pages/category/list?types=8">
                        <text class="iconfont icon-dating font34 color-primary"></text>
                        <view class="font14 color-content">文章分类</view>
                    </navigator>
                    
                    <view class="item" @click="copyActivityPages">
                        <text class="iconfont icon-copy color-primary font34"></text>
                        <view class="font14 color-content">复制路径</view>
                    </view>

                    <!-- #ifndef H5 -->
                    <view class="item" @click="showWebUrl">
                        <text class="iconfont icon-screen color-primary font34"></text>
                        <view class="font14 color-content">web端管理</view>
                    </view>
                    <!-- #endif -->

                    <navigator class="item" url="/pages/other/contact">
                        <uni-icons type="chatboxes" size="34" color="#2d8cf0"/>
                        <view class="font14 color-content">联系客服</view>
                    </navigator>

                    

                    <view class="item" @click="showActiveSharePopup">
                        <text class="iconfont icon-share color-primary font34"></text>
                        <view class="font14 color-content">转发分享</view>
                    </view>
                </view>
            </view>


            <view class="tools bg-white">
                <view class="title color-title">数据导出</view>
                <view class="list flex-row flex-wrap text-center">
                    <view class="item" @click="exportRankingDataAjax(21)">
                        <text class="iconfont icon-export-excel color-primary font34"></text>
                        <view class="font14 color-content">导出数据</view>
                    </view>

                    <navigator class="item" url="/pages/activity/admin/export_record">
                        <uni-icons type="list" size="34" color="#2d8cf0"/>
                        <view class="font14 color-content">导出记录</view>
                    </navigator>
                </view>

            </view>

        </view>


        <active-share ref="activeShare"/>


        <uni-popup ref="export_ranking_success" type="center">
            <view class="export_ranking_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('export_ranking_success')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top bg-primary color-white" style="padding: 50px 20px 20px;">
                    <icon type="success" size="80" color="#ffffff"></icon>
                    <view class="font18">导出成功</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ export_ranking_success_tips }}
                    </view>
                    <view
                        class="export_ranking-btn bg-info color-white"
                        hover-class="navigator-hover"
                        @click="copyDownloadSrc(false)"
                    >复制下载地址
                    </view>
                </view>
            </view>
        </uni-popup>


        <web-admin-src-copy-popup ref="web-admin-src-copy-popup"/>
    </view>
</template>

<script>
const app = getApp()
import login from '../../../utils/api/login.js'
import xwy_api from '../../../utils/api/xwy_api.js'
import my_storage from '../../../utils/storage.js'

export default {
    data() {
        return {
            loading: true,
            id: '',
            detail: {},
            error: '',
            export_ranking_success_tips: '',
            web_base_url: '',
            popup_show: false
        }
    },
    
    computed: {
        closed_AD() {
            return this.detail?.rank_set?.closed_AD || 0
        }
    },
    
    onShareAppMessage() {
        let path = '/pages/form-submit/user/details?id=' + this.id + '&userid=' + app.globalData['userid']
        if (this.detail.conf.active.screen_pic) path += `&screen_pic=` + (this.detail.conf.active.screen_pic || '')
        return {
            title: this.detail.name,
            path,
            imageUrl: this.detail.logo || ''
        }
    },
    onLoad(e) {
        console.log('活动详情页面路径参数', e)
        uni.showLoading({mask: true})
        if (!e.id) {
            this.loading = false
            this.error = '请指定活动id'
            return uni.hideLoading()
        }

        this.id = e.id
        login.uniLogin(err => {
            this.loading = false
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            if (app.globalData['shop_info']?.['shop_set']?.static_url) this.web_base_url = app.globalData['shop_info']['shop_set'].static_url


            this.getDetail()
        })
    },
    methods: {
        getDetail() {
            xwy_api.getActivityDetail(this.id, res => {
                uni.hideLoading()
                if (res.data?.['active_details']) {
                    const detail = res.data['active_details']
                    app.globalData.activity_detail = detail
                    my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)
                    this.detail = detail
                } else {
                    this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                }
            })
        },

        uniPopupOpen(ref) {
            this.popup_show = true
            this.$refs[ref].open()
        },

        uniPopupClose(ref) {
            this.popup_show = false
            this.$refs[ref].close()
        },


        exportRankingDataAjax(types = null) {

            this.$uni.showLoading('导出中...')

            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id
            }
            if (types) data.types = types

            xwy_api.ajax({
                url: 'front.flat.sport_step.export.admin_active/export_user_total_rank',
                data,
                success: res => {
                    uni.hideLoading()
                    if (res?.status !== 1) return this.$uni.showModal(res.info || '导出失败')

                    this.export_ranking_success_tips = res.info || '导出成功'
                    this.export_ranking_src = res.data.url
                    this.copyDownloadSrc(true)
                    this.uniPopupOpen('export_ranking_success')


                    let title = `导出活动【${this.detail.name}】排行榜`
                    if (types) {
                        const title_options = {
                            21: '活动用户名单'
                        }
                        title = `导出活动【${this.detail.name}】${title_options[types]}`
                    }
                    my_storage.setExportExcelRecord({url: res.data.url, title})
                }
            })
        },


        copyDownloadSrc(hide = false) {
            uni.setClipboardData({
                data: this.export_ranking_src,
                success: () => hide ? uni.hideToast() : this.$uni.showToast('复制成功', 'success', 1000)
            })
        },

        showActiveSharePopup() {
            this.$refs.activeShare.open({
                page: 'pages/form-submit/user/details',
                scene: 'id=' + this.detail.id + '&uid=' + app.globalData['userid'],
                qrcode_logo: this.closed_AD && this.detail.conf?.active?.qrcode_logo
            })
        },

        showWebUrl() {
            this.$refs['web-admin-src-copy-popup'].open({
                successCallback: () => this.popup_show = true,
                closeCallback: () => this.popup_show = false
            })
        },


        copyActivityPages() {
            let data = '活动小程序路径：pages/form-submit/user/details?id=' + this.id
            if (this.detail.screen_pic) data += `&screen_pic=` + this.detail.screen_pic
            if (app.globalData['shop_info']?.['extend_set']?.['conf_set']?.appid) {
                data = `小程序appid：${app.globalData['shop_info']['extend_set']['conf_set'].appid} \n ${data}`
            }
            
            uni.setClipboardData({
                data,
                success: () => {
                    uni.hideToast()
                    this.$uni.showModal('小程序路径地址复制成功，请粘贴到公众号自定义菜单设置里', {title: '复制成功'})
                }
            })
        },

        copy(data) {
            uni.setClipboardData({
                data,
                success: () => this.$uni.showToast('复制成功', 'none', 500)
            })
        }
    }
}
</script>

<style scoped>
.page {
    min-height: 100vh;
    padding-bottom: 15px;
    box-sizing: border-box;
}

.logo {
    display: block;
    width: 100vw;
    height: auto;
}


.tools {
    margin: 10px;
    border-radius: 10px;
    overflow: hidden;
}

.tools .title {
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.item {
    width: calc(100% / 4);
    padding: 10px 1px;
    box-sizing: border-box;
}


.export_ranking_popup {
    width: 280px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}

.export_ranking-btn {
    line-height: 40px;
    border-radius: 20px;
}

.export_ranking-btn::after {
    border: none;
}
</style>
