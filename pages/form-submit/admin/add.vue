<template>
    <view class="page">
        <view v-if="success" class="save-success bg-white">
            <view class="icon text-center">
                <uni-icons type="checkbox-filled" size="100" color="#19be6b"/>
                <view class="color-content font16">
                    活动{{ form_data.active_id ? '修改' : '创建' }}成功！
                </view>
            </view>
            <view class="flex-all-center">
                <button class="bg-success color-white" style="width: 200px;" @click="lookMyActivityList">
                    查看我的活动
                </button>
            </view>
        </view>

        <view class="type-list flex-row bg-white">
            <view
                class="type-item color-content"
                v-for="(item, index) in type_list"
                :key="index"
                :class="{'active-type': item.id === type_id}"
                :style="{width: 'calc(100% / ' + type_list.length + ')'}"
                @click="type_id = item.id"
            >
                {{ item.title }}
            </view>
        </view>

        <add-activity-tips ref="add_activity_tips"/>

        <view class="form">
            <template v-if="type_id === 1">
                <view class="form-item">
                    <view class="top color-content">
                        <text>活动名称</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" v-model="form_data.name" placeholder="请输入活动名称"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">主办方</view>
                    <view class="bottom font16">
                        <input class="input" v-model="form_data.organizer" placeholder="请输入主办方单位名称"/>
                    </view>
                </view>

                <template v-if="!form_data.active_id">
                    <view class="form-item">
                        <view class="top color-content">
                            <text>手机号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="number" v-model="form_data.mobile" 
                                   placeholder="请输入真实手机号"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <text>微信号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" v-model="form_data.wechat_num" 
                                   placeholder="请输入真实微信号"/>
                        </view>
                    </view>
                </template>

                <view class="form-item">
                    <view class="top color-content">活动开始时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.begin_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">活动结束时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.end_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view v-if="!conf.active.news.news_id" class="form-item">
                    <view class="top color-content">活动说明</view>
                    <view class="bottom font16">
                        <textarea
                            class="textarea"
                            maxlength="-1"
                            auto-height
                            v-model="form_data.content"
                            placeholder="请输入活动说明"
                        />
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>活动说明文章</view>
                        <view class="font12 color-sub">
                            绑定文章后，活动说明将会显示文章内容，不会显示上面填写的活动说明
                        </view>
                    </view>
                    <view class="bottom font16">
                        <view class="flex-kai" @click="toSelNews('news')">
                            <view class="view">
                                <view v-if="conf.active.news.news_id">
                                    {{ conf.active.news.news_title || conf.active.news.news_id }}
                                </view>
                                <view v-else class="color-sub">选择文章</view>
                            </view>
                            <view class="flex-all-center">
                                <view v-if="conf.active.news.news_id" class="color-sub font12" 
                                      style="width: 30px;" @click.stop="deleteNews('news')">解绑
                                </view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>
            </template>


            <template v-if="type_id === 3">

                <!--表单支付活动不能切换活动参与方式-->
                <!--<view class="form-item">
                    <view class="top color-content">活动参与方式</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.enter_types_list"
                                :value="form_options.enter_types_list.findIndex(v => v.value === conf.active.enter_types)"
                                range-key="title"
                                @change="conf.active.enter_types = form_options.enter_types_list[$event.detail.value].value"
                            >
                                {{
                                    form_options.enter_types_list.find(v => v.value === conf.active.enter_types).title
                                }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>-->

                <view v-if="conf.active.enter_types === 2" class="form-item">
                    <view class="top color-content">
                        <text>活动密码</text>
                        <text v-if="!have_password" class="color-error font16"> *</text>
                        <text class="color-sub font12 pl5">(3-20位)</text>
                    </view>
                    <view class="bottom font16">
                        <input
                            class="input"
                            v-model="conf.active.password"
                            :maxlength="20"
                            :placeholder="have_password ? '已设置密码，无需修改密码不用填写' : '请输入活动密码'"
                        />
                    </view>
                </view>


                <view class="form-item">
                    <view class="top color-content">
                        <view>参与活动需要填写的信息</view>
                        <view class="font12 color-error">注意: 第一项为排行榜显示的姓名, 无法删除</view>
                    </view>
                    <view class="bottom font16">
                        <view class="ptm5">
                            <view class="ptm5 must-submit-list" 
                                  v-for="(item, index) in conf.must_submit" :key="index">
                                <view class="flex-row">
                                    <view class="flex-row">
                                        <view style="width: 120px;">
                                            <uni-easyinput v-model="item.title" placeholder="请输入内容"/>
                                        </view>
                                        <template>
                                            <template v-if="index === 0">
                                                <picker :range="['选填', '必填']" :value="item.rules" 
                                                        @change="mustItemRulesChange($event, index)">
                                                    <view class="must-rules-picker">
                                                        <text class="color-content font14">
                                                            {{ item.rules === 0 ? '选填' : '必填' }}
                                                        </text>
                                                        <text class="iconfont icon-more color-sub font14"></text>
                                                    </view>
                                                </picker>
                                                <view class="must-rules-picker">
                                                    <text class="color-content font14">
                                                        {{ item.types === 1 ? '文本' : '单选' }}
                                                    </text>
                                                    <text class="iconfont icon-more color-white font14"></text>
                                                </view>
                                            </template>
                                            <template v-else>
                                                <picker :range="['选填', '必填']" :value="item.rules" 
                                                        @change="mustItemRulesChange($event, index)">
                                                    <view class="must-rules-picker">
                                                        <text class="color-content font14">
                                                            {{ item.rules === 0 ? '选填' : '必填' }}
                                                        </text>
                                                        <text class="iconfont icon-more color-sub font14"></text>
                                                    </view>
                                                </picker>
                                                <picker :range="form_options.must_submit_types" 
                                                        range-key="title" :value="item.types - 1" 
                                                        @change="mustItemTypesChange($event, index)">
                                                    <view class="must-rules-picker">
                                                        <text class="color-content font14">
                                                            {{
                                                                form_options.must_submit_types.find(v => v.value === item.types).title || ''
                                                            }}
                                                        </text>
                                                        <text class="iconfont icon-more color-sub font14"></text>
                                                    </view>
                                                </picker>
                                            </template>
                                        </template>
                                    </view>
                                    <view v-if="index !== 0" class="delete-rules font14 color-error" 
                                          @click="conf.must_submit.splice(index, 1)">删除
                                    </view>
                                </view>

                                <view v-if="item.types === 2" class="pl10">
                                    <view class="must-options-item flex-row" 
                                          v-for="(item_, index_) in item.options" :key="index_">
                                        <view class="color-sub delete-rules text-right" 
                                              style="width: 20px; padding: 0 5px 0 0;">{{ index_ + 1 }}:
                                        </view>
                                        <view style="width: 200px;">
                                            <uni-easyinput v-model="item_.text" placeholder="请输入内容"/>
                                        </view>
                                        <view class="delete-rules">
                                            <text class="color-error font14" 
                                                  @click="deleteOptionsItem(index, index_)">删除
                                            </text>
                                        </view>
                                    </view>
                                    <view class="flex-row">
                                        <view class="color-sub font14 ptm5" @click="addOption(index)">
                                            + 添加新选项
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <view class="flex-row">
                                <view class="color-primary font14 ptm5" @click="addMust">+ 添加新项</view>
                            </view>
                        </view>
                    </view>
                </view>

            </template>
            
            
            <template v-if="type_id === 5">
                <view class="form-item">
                    <view class="top color-content">公告</view>
                    <view class="bottom font16">
                        <input class="input" v-model="conf.active.notice" :maxlength="50" 
                               placeholder="请输入活动名称"/>
                    </view>
                </view>

                
                <template v-if="rank_set.active_details_notice">
                    <view class="form-item">
                        <view class="top color-content">
                            <view>是否开启活动阅读须知</view>
                            <view class="color-sub font14">开启后，用户需要阅读并同意才能进入报名</view>
                        </view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="['关闭', '开启']"
                                    :value="conf.active.active_details_notice.open"
                                    @change="conf.active.active_details_notice.open = Number($event.detail.value)"
                                >
                                    {{ conf.active.active_details_notice.open ? '开启' : '关闭' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-if="conf.active.active_details_notice.open" class="form-item">
                        <view class="top color-content">阅读须知文章</view>
                        <view class="bottom font16">
                            <view class="flex-kai" @click="toSelNews('active_details_notice')">
                                <view class="view">
                                    <view v-if="conf.active.active_details_notice.news_id">
                                        {{ conf.active.active_details_notice.news_title || conf.active.active_details_notice.news_id }}
                                    </view>
                                    <view v-else class="color-sub">选择文章</view>
                                </view>
                                <view class="flex-all-center">
                                    <view
                                        v-if="conf.active.active_details_notice.news_id"
                                        class="color-sub font12"
                                        style="width: 30px;"
                                        @click.stop="deleteNews('active_details_notice')"
                                    >解绑</view>
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>
                </template>
            </template>
            

            <template v-if="type_id === 2">
                <view class="form-item">
                    <view class="top color-title">
                        <text>活动主图</text>
                        <text class="font12 color-sub pl5">
                            (设置活动缩略图及活动详情顶部图片)
                        </text>
                        <view class="font12 color-sub">图片尺寸建议: 750*430</view>
                    </view>
                    <view style="padding-top: 5px;">
                        <view class="image-view" v-if="form_data.logo">
                            <image
                                class="image-item"
                                :src="form_data.logo"
                                mode="aspectFill"
                                @click="previewImage([form_data.logo])"
                            />
                            <view class="del-image-item" @click.stop="form_data.logo = ''">
                                <uni-icons type="closeempty" color="#e20f04"/>
                            </view>
                        </view>

                        <view v-else class="add-image text-center" @click="changeImage('logo')">
                            <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                        </view>
                    </view>
                </view>

                <!--付费表单活动不需要开屏大图   雷子-->
                <!--<view class="form-item">
                    <view class="top color-title">
                        <text>活动页面开屏大图</text>
                        <view class="font12 color-sub">图片尺寸建议: 780*1387</view>
                    </view>
                    <view style="padding-top: 5px;">
                        <view class="image-view" v-if="conf.active.screen_pic">
                            <image
                                class="image-item"
                                :src="conf.active.screen_pic"
                                mode="aspectFill"
                                @click="previewImage([conf.active.screen_pic])"
                            />
                            <view class="del-image-item" @click.stop="conf.active.screen_pic = ''">
                                <uni-icons type="closeempty" color="#e20f04"/>
                            </view>
                        </view>
                        <view v-else class="add-image text-center" @click="changeImage('screen_pic')">
                            <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                        </view>
                    </view>
                </view>-->

            </template>
        </view>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view class="login-btn color-white text-center font18 bg-primary" :disabled="loading" 
                  @click="save">{{ form_data.active_id ? '保存' : '创建活动' }}
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import base64 from '@/utils/base64.js'
import {pinyin} from 'pinyin-pro'


export default {
    data() {
        return {
            loading: true,
            success: false,
            type_list: [
                {title: '基本信息', id: 1},
                {title: '活动设置', id: 3},
                {title: '扩展设置', id: 5},
                {title: '活动图片', id: 2}
            ],
            type_id: 1,
            form_data: {
                name: '',
                wechat_num: '',
                mobile: '',
                organizer: '',
                content: '',
                begin_time: new Date(utils.getDay(0, true, '/') + ' 00:00:00').getTime(),
                end_time: new Date(utils.getDay(30, true, '/') + ' 23:59:59').getTime(),
                logo: ''
            },
            conf: {
                active: {
                    news: {
                        news_id: '',
                        news_title: ''
                    },
                    enter_types: 1,
                    password: '',
                    screen_pic: '',
                    notice: '',
                    active_details_notice: {
                        open: 0,
                        news_id: '',
                        news_title: '',
                        confirm_text: '确定'
                    },
                },
                must_submit: JSON.parse(JSON.stringify(this.xwy_config.defaultMustSubmit))
            },
            rank_set: {},
            have_password: false,
            form_options: {
                enter_types_list: [
                    {title: '自由报名参与活动', value: 1},
                    {title: '需要输入密码才能进入活动', value: 2},
                    {title: '报名需要审核通过才能参与活动', value: 3}
                ],
                must_submit_types: [
                    {value: 1, title: '文本'},
                    {value: 2, title: '单选'},
                    {value: 3, title: '图片'}
                ]
            },
            detail_icon_conf: [
                {
                    type: 0,
                    title: '文章详情'
                }
            ]
        }
    },
    onLoad(e) {
        if (e.id) {
            this.form_data.active_id = e.id
        } else {
            this.types = Number(e.types)
            this.$nextTick(() => this.$refs.add_activity_tips.open(this.types))
            this.form_data.logo = this.xwy_config.create_active_default_logo
        }
        
        this.$uni.setNavigationBarTitle(e.id ? '修改活动' : `创建${e.name || '活动'}`)
        this.$uni.showLoading()


        login.uniLogin(err => {

            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }


            if (e.id) return this.getDetail()

            const evn_version = app.globalData['evn_version']

            if (evn_version === 'develop') {
                this.form_data.mobile = 12345678910
                this.form_data.wechat_num = 12345678910
                this.form_data.organizer = '伟杰测试'
            }

            // 开发版和体验版不检查重复创建活动
            if (evn_version === 'develop' || evn_version === 'trial') return uni.hideLoading()

            this.checkUserCanCreateActive()
        })
    },
    methods: {
        checkUserCanCreateActive() {

            xwy_api.ajax({
                url: 'front.flat.sport_step.admin/check_user_can_create_active',
                data: {
                    access_token: app.globalData['access_token']
                },
                success: res => {
                    console.log('检查是否能创建活动', res)
                    uni.hideLoading()
                    this.loading = false
                    if (res?.status !== 1) this.$uni.showModal(res.info || '暂时不能创建活动', {
                        success: () => uni.navigateBack()
                    })
                }
            })

        },

        getDetail() {
            const errModal = content => {
                uni.hideLoading()
                this.$uni.showModal(content || '活动获取失败', {success: () => uni.navigateBack()})
            }

            xwy_api.getActivityDetail(this.form_data.active_id, res => {
                if (res.data?.['active_details']) {
                    const detail = res.data['active_details']

                    this.detailInit(detail)
                } else {
                    errModal(res.info)
                }
            })
        },


        detailInit(data) {
            this.types = data.types
            this.form_data.name = data.name
            this.form_data.begin_time = data.begin_time * 1000
            this.form_data.end_time = data.end_time * 1000
            if (data.organizer) this.form_data.organizer = data.organizer
            if (data.logo) this.form_data.logo = data.logo
            if (data.content) this.form_data.content = data.content

            const conf = data.conf
            const active = conf.active

            if (active.password) {
                this.old_password = active.password
                delete active.password
                this.have_password = true
            }
            
            active.notice ||= ''
            active.active_details_notice ||= {
                open: 0,
                news_id: '',
                news_title: '',
                confirm_text: '确定'
            }

            this.conf = conf

            if (data.rank_set) {
                const rank_set = data.rank_set

                this.rank_set = rank_set


                if (rank_set.closed_AD && rank_set.closed_AD === 1) {
                    this.detail_icon_conf.push({
                        type: 1,
                        title: '文章列表'
                    })
                }
            }

            uni.hideLoading()
        },


        toSelNews(key) {
            uni.navigateTo({
                url: '/pages/news/list?type=user&is_sel=true',
                events: {
                    selNews: data => {
                        this.conf.active[key].news_id = data.id
                        this.conf.active[key].news_title = data.title
                    }
                }
            })
        },


        deleteNews(key) {
            this.conf.active[key].news_id = ''
            this.conf.active[key].news_title = ''
        },


        mustItemRulesChange(e, index) {
            this.conf.must_submit[index].rules = Number(e.detail.value)
        },
        mustItemTypesChange(e, index) {
            const value = this.form_options.must_submit_types[e.detail.value].value
            if (value === 2) {
                this.conf.must_submit[index].options = this.conf.must_submit[index].options || [{text: ''}]
            }

            this.conf.must_submit[index].types = value
        },
        addMust() {
            this.conf.must_submit.push({
                name: '',
                rules: 0,
                title: '',
                types: 1
            })
        },

        addOption(index) {
            this.conf.must_submit[index].options.push({text: ''})
            this.$forceUpdate()
        },
        deleteOptionsItem(index, index_) {
            if (this.conf.must_submit[index].options.length === 1) 
                return this.$uni.showToast('请至少保留一个选项')
            
            this.conf.must_submit[index].options.splice(index_, 1)
            this.$forceUpdate()
        },


        changeImage(key) {
            if (key === 'screen_pic' && !this.rank_set.closed_AD)
                return this.$uni.showModal('无法设置开屏大图，请联系客服设置')

            let url = '/pages/other/image_upload_or_select'
            if (this.form_data.active_id) url += `?active_id=${this.form_data.active_id}`
            uni.navigateTo({
                url,
                events: {
                    newImg: src => {
                        switch (key) {
                            case 'logo':
                                this.form_data.logo = src
                                break
                            default:
                                this.$set(this.conf.active, key, src)
                        }
                    }
                }
            })
        },


        previewImage(urls, current = urls[0]) {
            uni.previewImage({
                urls,
                current
            })
        },


        dataCheck(data) {
            const showToast = title => {
                this.type_id = 1
                this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none')
            }

            if (!data.name) return showToast('请输入活动名称') && false

            if (!data.active_id) {
                if (!data.mobile) return showToast('请输入手机号') && false
                if (data.mobile.toString().length !== 11) return showToast('手机号长度有误') && false
                
                if (!data.wechat_num) return showToast('请输入微信号') && false
                const wechat_num = data.wechat_num.toString()
                if (wechat_num.length < 4 || wechat_num.length > 20) 
                    return showToast('微信号长度有误') && false
                if (this._utils.isChineseChar(wechat_num)) return showToast('微信号不能输入中文') && false
            }
            
            return true
        },

        setMustSubmitData() {
            const errModal = content => {
                this.type_id = 3
                return this.$uni.showModal(content) && false
            }


            for (let i = 0; i < this.conf.must_submit.length; i++) {
                const v = this.conf.must_submit[i]

                if (!v.title) return errModal('参与活动需要填写的信息选项填写不完整，请检查。')

                if (v.types === 2) {
                    if (!v.options || !v.options.length) return errModal(`${v.title} 至少需要添加一个选项。`)

                    for (let j = 0; j < v.options.length; j++) {
                        const v1 = v.options[j]
                        if (!v1 || !v1.text) return errModal(`${v.title} 有未填写的选项，请检查。`)
                    }
                }

                v.name = pinyin(v.title, {toneType: 'none', type: 'array'}).join('_')
            }

            return true
        },


        confCheck() {
            const showToast = title => {
                this.type_id = 3
                this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none')
            }
            const conf = JSON.parse(JSON.stringify(this.conf))
            const active = conf.active
            if (active.enter_types === 2) {
                const password = active.password
                if (!this.have_password && !password) return showToast('请输入活动密码') && false
                if (password && password.length < 3) return showToast('活动密码不得少于3位') && false
                if (password && password.length > 20) return showToast('活动密码不得大于20位') && false
                active.password ||= this.old_password
            } else {
                active.password = 1
            }
            
            if (!active.notice) delete active.notice
            if (!this.rank_set?.active_details_notice || !active.active_details_notice.open || !active.active_details_notice.news_id) {
                delete active.active_details_notice
            }

            let conf_str = JSON.stringify(conf)
            conf_str = conf_str.replace(/·/g, '-')
            return base64['encode'](conf_str)
        },

        save() {
            if (this.success) return false

            const data = JSON.parse(JSON.stringify(this.form_data))

            if (!this.dataCheck(data)) return
            if (!this.setMustSubmitData()) return

            const conf = this.confCheck()
            if (!conf) return
            data.conf = conf


            data.begin_time /= 1000
            data.end_time /= 1000

            this.saveAjax(data)
        },

        async saveAjax(data) {
            data.types = this.types

            this.loading = true
            this.$uni.showLoading('保存中...')

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.admin/create_active',
                data
            })

            uni.hideLoading()
            this.loading = false

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '保存失败')

            this.updatePageData()

            if (data.active_id) {
                if (this.id) {
                    this.$uni.showToast('保存成功', 'success')
                    return uni.navigateBack()
                }
            }

            this.success = true
        },

        // 活动修改/创建成功，如果页面栈有详情或列表页面，更新页面数据
        updatePageData() {
            const pages = getCurrentPages()

            const update_page_list = [
                {
                    route: 'pages/form-submit/user/details',
                    _function: page => {
                        page.$vm.getDetail(true)
                    }
                },
                {
                    route: 'pages/form-submit/admin/manage',
                    _function: page => {
                        page.$vm.getDetail()
                    }
                },
                {
                    route: 'pages/activity/user/index',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getActivityList()
                    }
                },
                {
                    route: 'pages/activity/user/activity_list',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getList()
                    }
                }
            ]

            update_page_list.forEach(v => {
                const page = pages.find(n => n.route === v.route)
                if (page) v._function(page)
            })
        },

        lookMyActivityList() {
            const list_page_route = 'pages/activity/user/activity_list'
            const pages = getCurrentPages()
            const list_page_index = pages.findIndex(v => {
                return v.route === list_page_route && v['options'].type && v['options'].type === 'create'
            })
            if (list_page_index === -1) return this.$uni.redirectTo(`/${list_page_route}?type=create`)

            this.$uni.navigateBack(pages.length - list_page_index - 1)
        }
    }
}
</script>

<style>
.page {
    padding-top: 40px;
    padding-bottom: 75px;
}

.type-list {
    position: fixed;
    z-index: 9;
    width: 100vw;
    top: 0;
    left: 0;
}

.type-item {
    height: 40px;
    line-height: 40px;
    text-align: center;
    box-sizing: border-box;
}

.active-type {
    color: #007AFF;
    border-bottom: 2px solid #007AFF;
}

.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}


.form-item .textarea {
    width: 100%;
    line-height: 16px;
    padding: 10px 0;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}

.must-submit-list {
    border-bottom: 1px dashed #eee;
}


.must-rules-picker {
    line-height: 34px;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    margin-left: 5px;
    padding: 0 15px;
}

.delete-rules {
    line-height: 36px;
    padding: 0 10px;
}

.must-options-item {
    padding: 3px 0;
}


.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
}

.add-image {
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
    line-height: calc((100vw - 50px) / 3);
    margin: 5px;
}

.image-item {
    width: calc(100vw - 20px);
    height: 50vw;
    border-radius: 5px;
}


.image-view {
    position: relative;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}


.top-rank-banner-item {
    padding: 5px;
    position: relative;
}

.top-rank-banner-item image {
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
}

.top-rank-banner-item .del-image-item {
    right: 8px;
}

.bottom-btn-view {
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.login-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}

.save-success {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 999999;
    box-sizing: border-box;
    padding-top: 10vh;
}

.save-success .icon {
    padding-bottom: 50px;
}


.uni-date-x {
    padding: 0 !important;
}

.uni-date__x-input {
    font-size: 16px !important;
    color: #000;
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .type-list {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }


    .image-item {
        width: 480px;
        height: 200px;
    }

    .top-rank-banner-item image, .add-image {
        width: 150px;
        height: 150px;
        line-height: 150px;
    }
}

/* #endif */
</style>
