<template>
    <view>
        <view v-if="total" class="color-sub font14 text-center">
            <template>共{{ total }}条记录</template>
            <template v-if="total_amount">
                <text>, 合计</text>
                <text class="color-warning">{{ total_amount.toFixed(2) }}</text>
                <text>元</text>
            </template>
        </view>

        <view>
            <view class="record-item" v-for="item in record_list" :key="item.id">
                <view class="flex-kai pb5">
                    <view class="color-title">
                        <text v-if="item.user_details && item.user_details.must_submit && item.user_details.must_submit && item.user_details.must_submit[0] && item.user_details.must_submit[0].value">
                            {{ item.user_details.must_submit[0].value }}
                        </text>
                        <text v-else class="color-sub">用户已删除</text>
                    </view>
                    <view v-if="item.amount">
                        <text class="color-warning">{{ item.amount.toFixed(2) }}</text>
                        <text class="color-sub font12">元</text>
                    </view>
                </view>
                <view v-if="item['price_details'] && item['price_details'].name" class="color-content">
                    报名项目: {{ item['price_details'].name }}
                </view>
                <view v-if="item.create_time" class="color-sub font14">{{ item.create_time }}</view>
            </view>
        </view>


        <view v-if="loading" class="text-center">
            <view v-if="load_page === 1" style="width: 1px; height: 30vh;"></view>
            <load-ani/>
        </view>

        <view v-if="!loading && !record_list.length" class="text-center" style="padding-top: 15vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub font14">暂无付费记录</view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
export default {
    data() {
        return {
            loading: true,
            record_list: [],
            load_page: 1,
            is_last_page: false,
            total: 0,
            total_amount: 0
        }
    },

    onLoad(e) {
        this.id = e.id
        login.uniLogin(() => {
            this.init()
        })
    },

    onReachBottom() {
        !this.loading && !this.is_last_page && this.loadRecordList()
    },

    methods: {
        async init() {
            await this.getRecordList()
            this.loading = false
        },

        async loadRecordList() {
            this.loading = true
            await this.getRecordList()
            this.loading = false
        },

        async getRecordList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_pay.adminManage/user_submit_buy_records_list',
                data: {
                    active_id: this.id,
                    page: this.load_page,
                    perpage: 10
                }
            })
            this.load_page++
            if (res?.data?.total_amount) this.total_amount = res.data.total_amount
            const data = res?.data?.list
            if (!data) {
                this.is_last_page = false
                return
            }
            this.record_list = this.record_list.concat(data.data || [])
            this.is_last_page = data.is_lastpage
            this.total = data.total
        }
    }
}
</script>

<style lang="scss">

.record-item {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
    box-shadow: 0 0 5px 2px #eee;
}
</style>
