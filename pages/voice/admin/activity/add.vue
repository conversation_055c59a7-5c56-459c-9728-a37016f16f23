<template>
    <view class="page">
        <view v-if="success" class="save-success bg-white">
            <view class="icon text-center">
                <uni-icons type="checkbox-filled" size="100" color="#19be6b"/>
                <view class="color-content font16">活动{{ form_data.active_id ? '修改' : '创建' }}成功！</view>
            </view>
            <view class="flex-all-center">
                <button class="bg-success color-white" style="width: 200px;" @click="lookMyActivityList">
                    查看我的活动
                </button>
            </view>
        </view>

        <view class="type-list flex-row bg-white">
            <view
                class="type-item color-content"
                v-for="(item, index) in type_list"
                :key="index"
                :class="{'active-type': item.id === type_id}"
                :style="{width: 'calc(100% / ' + type_list.length + ')'}"
                @click="type_id = item.id"
            >{{ item.title }}
            </view>
        </view>

        <add-activity-tips ref="add_activity_tips"/>

        <view class="form">
            <template v-if="type_id === 1">
                <view class="form-item">
                    <view class="top color-content">
                        <text>活动名称</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" v-model="form_data.name" placeholder="请输入活动名称"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">主办方</view>
                    <view class="bottom font16">
                        <input class="input" v-model="form_data.organizer" placeholder="请输入主办方单位名称"/>
                    </view>
                </view>

                <template v-if="!form_data.active_id">
                    <view class="form-item">
                        <view class="top color-content">
                            <text>手机号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="number" v-model="form_data.mobile"
                                   placeholder="请输入真实手机号"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <text>微信号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" v-model="form_data.wechat_num" placeholder="请输入真实微信号"/>
                        </view>
                    </view>
                </template>

                <view class="form-item">
                    <view class="top color-content">活动开始时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.begin_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">活动结束时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.end_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view v-if="!conf.active.news.news_id" class="form-item">
                    <view class="top color-content">活动说明</view>
                    <view class="bottom font16">
                        <textarea
                            class="textarea"
                            maxlength="1000"
                            auto-height="true"
                            v-model="form_data.content"
                            placeholder="请输入活动说明"
                        />
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>活动说明文章</view>
                        <view class="font12 color-sub">绑定文章后，活动说明将会显示文章内容，不会显示上面填写的活动说明
                        </view>
                    </view>
                    <view class="bottom font16">
                        <view class="flex-kai" @click="toSelNews('content')">
                            <view class="view">
                                <view v-if="conf.active.news.news_id">
                                    {{ conf.active.news.news_title || conf.active.news.news_id }}
                                </view>
                                <view v-else class="color-sub">选择文章</view>
                            </view>
                            <view class="flex-all-center">
                                <view
                                    v-if="conf.active.news.news_id"
                                    class="color-sub font12"
                                    style="width: 30px;"
                                    @click.stop="deleteNews('content')"
                                >解绑
                                </view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>
            </template>


            <template v-if="type_id === 3">
                <view class="form-item">
                    <view class="top color-content">活动参与方式</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                :range="form_options.enter_types_list"
                                :value="form_options.enter_types_list.findIndex(v => v.value === conf.active.enter_types)"
                                range-key="title"
                                @change="conf.active.enter_types = form_options.enter_types_list[$event.detail.value].value"
                            >
                                {{ form_options.enter_types_list.find(v => v.value === conf.active.enter_types).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="conf.active.enter_types === 2" class="form-item">
                    <view class="top color-content">
                        <text>活动密码</text>
                        <text v-if="!have_password" class="color-error font16"> *</text>
                        <text class="color-sub font12 pl5">(3-20位)</text>
                    </view>
                    <view class="bottom font16">

                        <input class="input" v-model="conf.active.password" maxlength="20"
                               :placeholder="have_password ? '已设置密码，无需修改密码不用填写' : '请输入活动密码'"/>
                    </view>
                </view>

                <view v-if="conf.active.enter_types === 4" class="form-item">
                    <view class="top color-content">
                        <text>报名密码</text>
                        <text class="color-error font16"> *</text>
                        <text class="color-sub font12 pl5">(3-20位)</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" v-model="conf.active.join_password" maxlength="20"
                               placeholder="请输入报名密码"/>
                    </view>
                </view>

                <must-submit-set :must-submit.sync="conf.must_submit"/>
            </template>

            <template v-if="type_id === 2">
                <active-image-set :active-id="form_data.active_id" :rankSet="rank_set"
                                  :logo.sync="form_data.logo"
                                  :screen-pic.sync="conf.active.screen_pic"
                                  :top-rank-banner.sync="conf.active.top_rank_banner"/>

                <view class="form-item">
                    <view class="top color-title">
                        <text>录制页面背景图</text>
                        <view class="font12 color-sub">图片尺寸建议: 780*1387</view>
                    </view>
                    <view style="padding-top: 5px;">
                        <view class="image-view" v-if="conf.active.recorder_bg_img">

                            <image class="image-item" :src="conf.active.recorder_bg_img" mode="aspectFill"
                                   @click="previewImage([conf.active.recorder_bg_img])"/>
                            <view class="del-image-item" @click.stop="conf.active.recorder_bg_img = ''">
                                <uni-icons type="closeempty" color="#e20f04"/>
                            </view>
                        </view>
                        <view v-else class="add-image text-center" @click="changeImage('recorder_bg_img')">
                            <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                        </view>
                    </view>
                </view>
            </template>

            <template v-if="type_id === 6">
                <view class="form-item">
                    <view class="top color-content">作品是否需要审核后才能显示在广场</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                :range="form_options.not_check_list"
                                :value="form_options.not_check_list.findIndex(v => v.value === conf.active.not_check)"
                                range-key="title"
                                @change="conf.active.not_check = form_options.not_check_list[$event.detail.value].value"
                            >
                                {{ form_options.not_check_list.find(v => v.value === conf.active.not_check).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>


                <view class="form-item">
                    <view class="top color-content">提交作品备注填写设置</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                :range="form_options.memo_required_list"
                                :value="form_options.memo_required_list.findIndex(v => v.value === conf.active.memo_required)"
                                range-key="title"
                                @change="conf.active.memo_required = form_options.memo_required_list[$event.detail.value].value"
                            >
                                {{ form_options.memo_required_list.find(v => v.value === conf.active.memo_required).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <template v-if="conf.active.memo_required && haveRankSet">
                    <view class="form-item">
                        <view class="top color-content">作品备注是否显示用户提交作品天数</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    :range="['不显示', '显示']"
                                    :value="conf.active.memo_show_day.show"
                                    @change="conf.active.memo_show_day.show = Number($event.detail.value)"
                                >
                                    {{ conf.active.memo_show_day.show ? '' : '不' }}显示
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-show="conf.active.memo_show_day.show" class="form-item">
                        <view class="top color-content">作品备注用户提交作品天数显示设置</view>
                        <view class="bottom font16 pt5 pb5">
                            <view class="flex-row">
                                <view>
                                    <uni-easyinput v-model="conf.active.memo_show_day.before"
                                                   maxlength="20"/>
                                </view>
                                <view class="pl5 pr5 flex-all-center">8</view>
                                <view>
                                    <uni-easyinput v-model="conf.active.memo_show_day.after" maxlength="20"/>
                                </view>
                            </view>
                        </view>
                    </view>
                </template>

                <view class="form-item">
                    <view class="top color-content">提交作品照片上传设置</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                :range="form_options.pic_required_list"
                                :value="form_options.pic_required_list.findIndex(v => v.value === conf.active.pic_required)"
                                range-key="title"
                                @change="conf.active.pic_required = form_options.pic_required_list[$event.detail.value].value"
                            >
                                {{ form_options.pic_required_list.find(v => v.value === conf.active.pic_required).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">每人作品提交数量限制</view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="conf.active.people_voice_count"
                               placeholder="0或不填不限制作品提交数量"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">每人每天作品提交数量限制</view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="conf.active.people_voice_count_oneday"
                               placeholder="0或不填不限制作品提交数量"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <text>作品录制时长限制</text>
                        <text class="color-error font16"> *</text>
                        <text class="pl5 color-sub font12">(单位: 秒)</text>
                    </view>
                    <view class="bottom font16 pt5 pb5">
                        <view class="flex-row">
                            <view>
                                <uni-easyinput
                                    type="digit"
                                    :clearable="false"
                                    :placeholder="form_options.voice_duration.min + '-' + form_options.voice_duration.max + '(不填默认' + form_options.voice_duration.min + ')'"
                                    v-model="conf.active.voice_duration.min"
                                    maxlength="3"
                                ></uni-easyinput>
                            </view>
                            <view class="pl5 pr5 color-sub flex-all-center">-</view>
                            <view>
                                <uni-easyinput
                                    type="digit"
                                    :clearable="false"
                                    :placeholder="form_options.voice_duration.min + '-' + form_options.voice_duration.max + '(不填默认' + form_options.voice_duration.max + ')'"
                                    v-model="conf.active.voice_duration.max"
                                    maxlength="3"
                                ></uni-easyinput>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">作品</view>
                    <view class="bottom font16 pt5 pb5">
                        <view class="article-item bg-background"
                              v-for="(item, index) in conf.active.article_list" :key="index">
                            <view class="flex-kai">
                                <view class="view">

                                    <navigator :url="`/pages/news/preview?id=${item.id}`"
                                               class="color-title">
                                        {{ item.title }}
                                    </navigator>
                                </view>
                                <view class="flex-row">
                                    <view class="flex-row" style="padding: 8px;">
                                        <view
                                            class="works-sort flex-all-center"
                                            :style="{borderColor: index === 0 ? '#bbbec4' : '#5cadff'}"
                                            @click.stop="sortArticle(index, 'up')"
                                        >
                                            <uni-icons type="arrow-up"
                                                       :color="index === 0 ? '#bbbec4' : '#5cadff'"/>
                                        </view>
                                        <view
                                            class="works-sort flex-all-center"
                                            :style="{borderColor: index === conf.active.article_list.length - 1 ? '#bbbec4' : '#5cadff'}"
                                            @click.stop="sortArticle(index, 'down')"
                                        >
                                            <uni-icons type="arrow-down"
                                                       :color="index === conf.active.article_list.length - 1 ? '#bbbec4' : '#5cadff'"/>
                                        </view>
                                    </view>
                                    <view class="flex-all-center">
                                        <view class="color-error font12" style="width: 30px;"
                                              @click.stop="conf.active.article_list.splice(index, 1)">
                                            删除
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                        <view class="color-primary font14 ptm5" @click="addArticle">+ 添加作品</view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">是否开启自由录制</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                :range="form_options.free_record_close_list"
                                :value="form_options.free_record_close_list.findIndex(v => v.value === conf.active.free_record_close)"
                                range-key="title"
                                @change="conf.active.free_record_close = form_options.free_record_close_list[$event.detail.value].value"
                            >
                                {{ form_options.free_record_close_list.find(v => v.value === conf.active.free_record_close).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">录制时是否播放背景音乐</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                :range="form_options.record_background_music"
                                :value="form_options.record_background_music.findIndex(v => v.value === conf.active.record_background_music)"
                                range-key="title"
                                @change="openBackgroundMusicChange($event)"
                            >
                                {{ form_options.record_background_music.find(v => v.value === conf.active.record_background_music).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">活动主页作品展示方式</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                :range="form_options.voice_show_type"
                                :value="form_options.voice_show_type.findIndex(v => v.value === conf.active.voice_show_type)"
                                range-key="title"
                                @change="conf.active.voice_show_type = form_options.voice_show_type[$event.detail.value].value"
                            >
                                {{ form_options.voice_show_type.find(v => v.value === conf.active.voice_show_type).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>


                <view class="form-item">
                    <view class="top color-content">
                        <text>每人每天可点赞总数</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="conf.active.vote_rules.daily_num"
                               placeholder="不填则不限制"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <text>每人每天可给相同一参赛者点赞次数</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="conf.active.vote_rules.daily_person"
                               placeholder="不填默认为1次"/>
                    </view>
                </view>

                <template v-if="rank_set.active_details_notice">
                    <view class="form-item">
                        <view class="top color-content">
                            <view>是否开启活动阅读须知</view>
                            <view class="color-sub font14">开启后，用户需要阅读并同意才能进入活动</view>
                        </view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    :range="form_options.active_details_notice_open_opt"
                                    :value="form_options.active_details_notice_open_opt.findIndex(v => v.value === conf.active.active_details_notice.open)"
                                    range-key="title"
                                    @change="conf.active.active_details_notice.open = form_options.active_details_notice_open_opt[$event.detail.value].value"
                                >
                                    {{ form_options.active_details_notice_open_opt.find(v => v.value === conf.active.active_details_notice.open).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-if="conf.active.active_details_notice.open" class="form-item">
                        <view class="top color-content">阅读须知文章</view>
                        <view class="bottom font16">
                            <view class="flex-kai" @click="toSelNews('notice')">
                                <view class="view">
                                    <view v-if="conf.active.active_details_notice.news_id">
                                        {{ conf.active.active_details_notice.news_title || conf.active.active_details_notice.news_id }}
                                    </view>
                                    <view v-else class="color-sub">选择文章</view>
                                </view>
                                <view class="flex-all-center">
                                    <view
                                        v-if="conf.active.active_details_notice.news_id"
                                        class="color-sub font12"
                                        style="width: 30px;"
                                        @click.stop="deleteNews('notice')"
                                    >解绑
                                    </view>
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-if="conf.active.active_details_notice.open" class="form-item">
                        <view class="top color-content">阅读须知确定按钮文字</view>
                        <view class="bottom font16">
                            <input class="input" maxlength="10" placeholder="不填默认为“确定”"
                                   v-model="conf.active.active_details_notice.confirm_text"/>
                        </view>
                    </view>
                </template>
            </template>
        </view>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view class="login-btn color-white text-center font18 bg-primary" :disabled="loading"
                  @click="save">
                {{ form_data.active_id ? '保存' : '创建活动' }}
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import {pinyin} from 'pinyin-pro'


export default {
    data() {
        return {
            loading: true,
            success: false,
            type_list: [
                {title: '基本信息', id: 1},
                {title: '活动设置', id: 3},
                {title: '扩展设置', id: 6},
                {title: '活动图片', id: 2}
            ],
            type_id: 1,
            form_data: {
                active_id: '',
                name: '',
                wechat_num: '',
                mobile: '',
                organizer: '',
                content: '',
                begin_time: new Date(this._utils.getDay(0, true, '/') + ' 00:00:00').getTime(),
                end_time: new Date(this._utils.getDay(30, true, '/') + ' 23:59:59').getTime(),
                logo: ''
            },
            conf: {
                active: {
                    news: {
                        news_id: '',
                        news_title: ''
                    },
                    enter_types: 1,
                    password: '',
                    screen_pic: '',
                    top_rank_banner: [],
                    voice_duration: {
                        min: '',
                        max: ''
                    },
                    not_check: 0,
                    memo_required: 2,
                    pic_required: 0,
                    sign_times_type: 0,
                    article_list: [],
                    recorder_bg_img: '',
                    people_voice_count: '',
                    people_voice_count_oneday: '',
                    active_details_notice: {
                        open: 0,
                        news_id: '',
                        news_title: '',
                        confirm_text: '确定'
                    },
                    free_record_close: 0,
                    record_background_music: 0,
                    voice_show_type: 0,
                    join_password: '',
                    vote_rules: {
                        daily_num: '',
                        daily_person: 1
                    },

                    memo_show_day: {
                        show: 0,
                        before: '【打卡第',
                        after: '天】'
                    }
                },
                must_submit: JSON.parse(JSON.stringify(this.xwy_config.defaultMustSubmit))
            },
            top_rank_banner_max_count: 6,
            rank_set: {},
            pic_list: [],
            have_password: false,
            form_options: {
                enter_types_list: [
                    {title: '自由报名参与活动', value: 1},
                    {title: '需要输入密码才能进入活动', value: 2},
                    {title: '报名需要审核通过才能参与活动', value: 3}
                ],
                not_check_list: [
                    {value: 0, title: '需要审核'},
                    {value: 1, title: '无需审核'}
                ],
                memo_required_list: [
                    {value: 0, title: '关闭填写备注'},
                    {value: 2, title: '开启填写备注'},
                    {value: 1, title: '必须填写备注'}
                ],
                pic_required_list: [
                    {value: 0, title: '关闭照片上传'},
                    {value: 2, title: '开启照片上传'},
                    {value: 1, title: '必须上传照片'}
                ],
                sign_times_list: [
                    {value: 0, title: '不限制作品上传次数'},
                    {value: 1, title: '只能上传一次作品'},
                    {value: 2, title: '每天只能上传一次作品'}
                ],
                records_not_edit_list: [
                    {value: 0, title: '是'},
                    {value: 1, title: '否'}
                ],
                voice_duration: {
                    min: 5,
                    max: 600
                },
                active_details_notice_open_opt: [
                    {value: 1, title: '开启'},
                    {value: 0, title: '关闭'}
                ],
                free_record_close_list: [
                    {value: 0, title: '开启'},
                    {value: 1, title: '关闭'}
                ],
                record_background_music: [
                    {value: 1, title: '开启'},
                    {value: 0, title: '关闭'}
                ],
                voice_show_type: [
                    {value: 0, title: '详细'},
                    {value: 1, title: '简约'}
                ]
            },
            detail_icon_conf: [
                {
                    type: 0,
                    title: '文章详情'
                }
            ]
        }
    },

    watch: {
        'conf.active.people_voice_count': {
            handler: function (val) {
                if (val || val === 0 || val === '0') {
                    this.conf.active.people_voice_count = Number(val)
                }
            }
        },
        'conf.active.people_voice_count_oneday': {
            handler: function (val) {
                if (val || val === 0 || val === '0') {
                    this.conf.active.people_voice_count_oneday = Number(val)
                }
            }
        }
    },

    computed: {
        haveRankSet() {
            const rank_set = this.rank_set
            if (!rank_set || rank_set === '0' || rank_set === 'null' || rank_set === 'undefined' || rank_set === '[]' || Array.isArray(rank_set)) return false

            return Object.keys(rank_set).length !== 0
        }
    },

    onLoad(e) {
        if (e.id) {
            this.form_data.active_id = e.id
        } else {
            this.types = Number(e.types)
            this.$nextTick(() => this.$refs.add_activity_tips.open(this.types))
            this.form_data.logo = this.xwy_config.create_active_default_logo
        }

        this.$uni.setNavigationBarTitle(e.id ? '修改活动' : `创建${e.name || '活动'}`)
        this.$uni.showLoading()

        this.addJoinType()

        this.$login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }


            if (e.id) return this.getDetail()

            const evn_version = app.globalData['evn_version']

            if (evn_version === 'develop') {
                this.form_data.mobile = 12345678910
                this.form_data.wechat_num = 12345678910
                this.form_data.organizer = '伟杰测试'
            }

            // 开发版和体验版不检查重复创建活动
            if (evn_version === 'develop' || evn_version === 'trial') return uni.hideLoading()

            this.checkUserCanCreateActive()
        })
    },
    methods: {
        addJoinType() {
            // 鲜繁朗诵活动独立小程序客户，进入活动不需要密码，报名时才需要密码
            if (app.globalData['who'] === 35211) {
                this.form_options.enter_types_list.push({title: '报名时需要密码', value: 4})
            }
        },


        async checkUserCanCreateActive() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/check_user_can_create_active'
            })

            uni.hideLoading()
            this.loading = false

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '暂时不能创建活动', {
                success: () => this.$uni.navigateBack()
            })
        },

        getDetail() {
            this.xwy_api.getActivityDetail(this.form_data.active_id, res => {
                const details = res.data['active_details']

                if (!details) {
                    uni.hideLoading()
                    this.$uni.showModal(res?.info || '活动获取失败', {success: () => this.$uni.navigateBack()})
                }

                this.detailInit(details)
            })
        },


        detailInit(data) {
            this.types = data.types
            this.form_data.name = data.name
            this.form_data.begin_time = data.begin_time * 1000
            this.form_data.end_time = data.end_time * 1000
            if (data.organizer) this.form_data.organizer = data.organizer
            if (data.logo) this.form_data.logo = data.logo
            if (data.content) this.form_data.content = data.content

            const conf = data.conf
            if (conf.must_submit?.length) this.conf.must_submit = conf.must_submit

            const active = conf.active

            this.conf.active.enter_types = active.enter_types || 1
            if (active.news) this.conf.active.news = active.news
            if (active.screen_pic) this.conf.active.screen_pic = active.screen_pic
            if (active.top_rank_banner) this.conf.active.top_rank_banner = active.top_rank_banner
            if (active.not_check) this.conf.active.not_check = active.not_check
            if (active.memo_required || active.memo_required === 0) this.conf.active.memo_required = active.memo_required
            if (active.pic_required) this.conf.active.pic_required = active.pic_required
            if (active.sign_times_type || active.sign_times_type === 0) this.conf.active.sign_times_type = active.sign_times_type
            if (active.article_list) this.conf.active.article_list = active.article_list
            if (active.voice_duration) this.conf.active.voice_duration = active.voice_duration
            if (active.recorder_bg_img) this.conf.active.recorder_bg_img = active.recorder_bg_img
            if (active.people_voice_count) this.conf.active.people_voice_count = active.people_voice_count
            if (active.people_voice_count_oneday) this.conf.active.people_voice_count_oneday = active.people_voice_count_oneday
            if (active.active_details_notice) this.conf.active.active_details_notice = active.active_details_notice
            if (active.free_record_close) this.conf.active.free_record_close = active.free_record_close
            if (active.record_background_music) this.conf.active.record_background_music = active.record_background_music
            if (active.voice_show_type) this.conf.active.voice_show_type = active.voice_show_type
            if (active.join_password) this.conf.active.join_password = active.join_password
            if (active.password) {
                this.old_password = active.password
                this.have_password = true
            }
            if (active.vote_rules) {
                const {daily_num, daily_person} = active.vote_rules
                if (daily_num) this.conf.active.vote_rules.daily_num = daily_num
                if (daily_person) this.conf.active.vote_rules.daily_person = daily_person
            }

            if (active.memo_show_day) this.conf.active.memo_show_day = active.memo_show_day

            if (data.rank_set) {
                const rank_set = data.rank_set

                this.rank_set = rank_set


                if (rank_set.closed_AD && rank_set.closed_AD === 1) {
                    this.detail_icon_conf.push({
                        type: 1,
                        title: '文章列表'
                    })
                }
            }

            uni.hideLoading()
        },


        toSelNews(type) {
            uni.navigateTo({
                url: '/pages/news/list?type=user&is_sel=true',
                events: {
                    selNews: data => {
                        switch (type) {
                            case 'content':
                                this.conf.active.news.news_id = data.id
                                this.conf.active.news.news_title = data.title
                                break
                            case 'notice':
                                this.conf.active.active_details_notice.news_id = data.id
                                this.conf.active.active_details_notice.news_title = data.title
                                break
                        }

                    }
                }
            })
        },

        deleteNews(type) {
            switch (type) {
                case 'content':
                    this.conf.active.news.news_id = ''
                    this.conf.active.news.news_title = ''
                    break
                case 'notice':
                    this.conf.active.active_details_notice.news_id = ''
                    this.conf.active.active_details_notice.news_title = ''
                    break
            }
        },

        addArticle() {
            uni.navigateTo({
                url: '/pages/news/list?type=user&is_sel=true',
                events: {
                    selNews: data => {
                        for (let i = 0; i < this.conf.active.article_list.length; i++) {
                            const item_id = this.conf.active.article_list[i].id
                            if (item_id === data.id) {
                                return setTimeout(() => this.$uni.showModal('此作品已添加，无需重复添加'), 100)
                            }
                        }

                        this.conf.active.article_list.push({
                            id: data.id,
                            title: data.title
                        })
                    }
                }
            })
        },

        sortArticle(index, type) {
            const list = this.conf.active.article_list
            const item = list[index]
            if ((type === 'ip' && index === 0) || (type === 'down' && index === list.length - 1)) return
            list.splice(index, 1)
            if (type === 'up') {
                list.splice(index - 1, 0, item)
            } else {
                list.splice(index + 1, 0, item)
            }
        },


        openBackgroundMusicChange(e) {
            const value = this.form_options.record_background_music[e.detail.value].value
            if (value && !this.rank_set?.closed_AD) return this.$uni.showModal('无法设置开屏大图，请联系客服设置')

            this.conf.active.record_background_music = value
        },

        changeImage(key) {
            if (!this.rank_set?.closed_AD) {
                const options = {
                    screen_pic: '无法设置开屏大图，请联系客服设置',
                    top_rank_banner: '无法设置排行榜轮播图，请联系客服设置'
                }
                if (options[key]) return this.$uni.showModal(options[key])
            }

            let url = '/pages/other/image_upload_or_select'
            if (this.form_data.active_id) url += `?active_id=${this.form_data.active_id}`
            uni.navigateTo({
                url,
                events: {
                    newImg: src => {
                        switch (key) {
                            case 'logo':
                                this.form_data.logo = src
                                break
                            case 'top_rank_banner':
                                this.conf.active.top_rank_banner.push(src)
                                break
                            default:
                                this.conf.active[key] = src
                        }
                    }
                }
            })
        },

        previewImage(urls, current = urls[0]) {
            uni.previewImage({
                urls,
                current
            })
        },


        dataCheck(data) {
            const showToast = title => {
                this.type_id = 1
                this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none')
            }

            if (!data.name) {
                showToast('请输入活动名称')
                return false
            }

            if (!data.active_id) {
                if (!data.mobile) {
                    showToast('请输入手机号')
                    return false
                }
                if (data.mobile.toString().length !== 11) {
                    showToast('手机号长度有误')
                    return false
                }
                if (!data.wechat_num) {
                    showToast('请输入微信号')
                    return false
                }
                const wechat_num = data.wechat_num.toString()
                if (wechat_num.length < 4 || wechat_num.length > 20) {
                    showToast('微信号长度有误')
                    return false
                }
                if (this._utils.isChineseChar(wechat_num)) {
                    showToast('微信号不能输入中文')
                    return false
                }
            }

            return true
        },

        setMustSubmitData() {
            const errModal = content => {
                this.type_id = 3
                this.$uni.showModal(content)
            }


            for (let i = 0; i < this.conf.must_submit.length; i++) {
                const v = this.conf.must_submit[i]

                if (!v.title) {
                    errModal('参与活动需要填写的信息选项填写不完整，请检查。')
                    return false
                }
                v.name = pinyin(v.title, {toneType: 'none', type: 'array'}).join('_')
                if (v.types === 2) {
                    if (!v.options || !v.options.length) {
                        errModal(`${v.title} 至少需要添加一个选项。`)
                        return false
                    }
                    console.log(v.options)
                    for (let j = 0; j < v.options.length; j++) {
                        const v1 = v.options[j]
                        if (!v1 || !v1.text) {
                            errModal(`${v.title} 有未填写的选项，请检查。`)
                            return false
                        }
                    }
                }
            }

            return true
        },

        confCheck() {
            const showToast = (title, type_id = 3) => {
                this.type_id = type_id
                this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none')
            }
            const conf = JSON.parse(JSON.stringify(this.conf))
            if (conf.active.enter_types === 2) {
                if (!this.have_password && !conf.active.password) {
                    showToast('请输入活动密码')
                    return false
                }
                if (conf.active.password && conf.active.password.length < 3) {
                    showToast('活动密码不得少于3位')
                    return false
                }
                if (conf.active.password && conf.active.password.length > 20) {
                    showToast('活动密码不得大于20位')
                    return false
                }
                conf.active.password = conf.active.password || this.old_password
            } else {
                conf.active.password = 1
            }

            if (conf.active.enter_types === 4) {
                if (!conf.active.join_password) {
                    showToast('请输入报名密码')
                    return false
                }
                if (conf.active.join_password && conf.active.join_password.length < 3) {
                    showToast('报名密码不得少于3位')
                    return false
                }
                if (conf.active.join_password && conf.active.join_password.length > 20) {
                    showToast('报名密码不得大于20位')
                    return false
                }
            }

            if (!conf.active.voice_duration.min) conf.active.voice_duration.min = this.form_options.voice_duration.min
            if (!conf.active.voice_duration.max) conf.active.voice_duration.max = this.form_options.voice_duration.max
            const min = Number(conf.active.voice_duration.min)
            const max = Number(conf.active.voice_duration.max)
            if (min < this.form_options.voice_duration.min) {
                this.type_id = 6
                this.$uni.showModal('作品录制时长限制不得小于' + this.form_options.voice_duration.min + '秒')
                return false
            }
            if (max > this.form_options.voice_duration.max) {
                this.type_id = 6
                this.$uni.showModal('作品录制时长限制不得大于' + this.form_options.voice_duration.max + '秒')
                return false
            }
            if (min > max) {
                this.type_id = 6
                this.$uni.showModal('作品录制时长限制填写有误，最短时间不得大于最长时间')
                return false
            }

            if (!this.rank_set?.active_details_notice) delete conf.active.active_details_notice

            if (!conf.active.free_record_close) delete conf.active.free_record_close

            const {memo_required, memo_show_day} = conf.active
            if (!memo_required || !memo_show_day.show || !this.haveRankSet) delete conf.active.memo_show_day

            let conf_str = JSON.stringify(conf)
            conf_str = conf_str.replace(/·/g, '-')
            return this._utils.base64['encode'](conf_str)
        },

        save() {
            if (this.success) return

            const data = JSON.parse(JSON.stringify(this.form_data))

            if (!this.dataCheck(data)) return
            if (!this.setMustSubmitData()) return

            const conf = this.confCheck()
            if (!conf) return
            data.conf = conf

            data.pic_list = this._utils.base64['encode'](JSON.stringify(this.pic_list))

            data.begin_time /= 1000
            data.end_time /= 1000

            this.saveAjax(data)
        },

        async saveAjax(data) {
            data.types = this.types

            this.loading = true
            this.$uni.showLoading('保存中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/create_active',
                data
            })
            uni.hideLoading()
            this.loading = false

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '保存失败')

            this.updatePageData()

            if (data.active_id) {
                this.$uni.showToast('保存成功', 'success')
                return this.$uni.navigateBack()
            }

            this.success = true
        },

        // 活动修改/创建成功，如果页面栈有详情或列表页面，更新页面数据
        updatePageData() {
            const pages = getCurrentPages()

            const update_page_list = [
                {
                    route: 'pages/voice/user/detail',
                    _function: page => {
                        page.$vm.getDetail(true)
                    }
                },
                {
                    route: 'pages/voice/admin/activity/manage',
                    _function: page => {
                        page.$vm.getDetail()
                    }
                },
                {
                    route: 'pages/activity/user/index',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getActivityList()
                    }
                },
                {
                    route: 'pages/activity/user/activity_list',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getList()
                    }
                }
            ]

            update_page_list.forEach(v => {
                const page = pages.find(n => n.route === v.route)
                if (page) v._function(page)
            })
        },

        lookMyActivityList() {
            const list_page_route = 'pages/activity/user/activity_list'
            const pages = getCurrentPages()
            const list_page_index = pages.findIndex(v => {
                return v.route === list_page_route && v['options'].type && v['options'].type === 'create'
            })
            if (list_page_index === -1) return this.$uni.redirectTo(`/${list_page_route}?type=create`)

            this.$uni.navigateBack(pages.length - list_page_index - 1)
        }
    }
}
</script>

<style>
.page {
    padding-top: 40px;
    padding-bottom: 75px;
}

.type-list {
    position: fixed;
    z-index: 9;
    width: 100vw;
    top: 0;
    left: 0;
}

.type-item {
    height: 40px;
    line-height: 40px;
    text-align: center;
    box-sizing: border-box;
}

.active-type {
    color: #007AFF;
    border-bottom: 2px solid #007AFF;
}

.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}


.form-item .textarea {
    width: 100%;
    line-height: 16px;
    padding: 10px 0;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}

.article-item {
    border-radius: 5px;
    padding-left: 10px;
    padding-right: 5px;
    margin: 3px 0;
}

.works-sort {
    width: 22px;
    height: 22px;
    border-radius: 11px;
    border: 1px solid #5cadff;
    box-sizing: border-box;
    margin: 0 2px;
}

.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
}

.add-image {
    width: calc((100vw - 50px) / 3);
    aspect-ratio: 1;
    border-radius: 5px;
    margin: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-item {
    width: calc(100vw - 20px);
    height: 50vw;
    border-radius: 5px;
}


.image-view {
    position: relative;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}

.bottom-btn-view {
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.login-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}

.save-success {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 999999;
    box-sizing: border-box;
    padding-top: 10vh;
}

.save-success .icon {
    padding-bottom: 50px;
}

.uni-date-x {
    padding: 0 !important;
}

.uni-date__x-input {
    font-size: 16px !important;
    color: #000;
}

/* #ifdef H5 */

@media screen and (min-width: 500px) {
    .type-list {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }

    .image-item {
        width: 480px;
        height: 200px;
    }

    .add-image {
        width: 150px;
    }
}

/* #endif */
</style>
