<template>
    <view class="page">
        <view class="form">
            <view class="form-item">
                <view class="top color-content">
                    <text>歌曲名称</text>
                    <text class="color-error font16"> *</text>
                </view>
                <view class="bottom font16">
                    <input class="input" type="text" v-model="name" maxlength="50" placeholder="请输入歌曲名称"/>
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">
                    <text>歌曲播放地址</text>
                    <text class="color-error font16"> *</text>
                </view>
                <view class="bottom font16">
                    <input class="input" type="text" v-model="mp3_url" maxlength="200" placeholder="请输入歌曲播放地址"/>
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">
                    <text>歌曲时长</text>
                    <text class="font12 pl5">(单位: 秒)</text>
                </view>
                <view class="bottom font16">
                    <input class="input" type="number" v-model="seconds" maxlength="5" placeholder="请输入歌曲时长"/>
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">歌曲排序</view>
                <view class="bottom font16">
                    <input class="input" type="number" v-model="sort_num" maxlength="5" placeholder="数字越小排在列表越前"/>
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">歌词</view>
                <view class="bottom font16">
                    <textarea class="textarea" v-model="lyric" maxlength="500" placeholder="请输入歌词"/>
                </view>
            </view>
        </view>

        <view class="save bg-white">
            <view class="save-btn text-center bg-primary color-white" @click="verify">保存</view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import base64 from '@/utils/base64.js'

export default {
    data() {
        return {
            name: '',
            mp3_url: '',
            seconds: '',
            sort_num: '',
            lyric: ''
        }
    },

    onLoad(e) {
        uni.showLoading({ mask: true })
        login.uniLogin(err => {
            this.loading = false
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.init(e)
        })
    },

    methods: {
        async init(e) {
            this.id = e.id
            if (e.music_id) {
                this.music_id = e.music_id
                await this.getMusicDetails()
            }
            uni.hideLoading()
        },

        async getMusicDetails() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.audio_online.admin_manage/music_details',
                data: {
                    access_token: app.globalData['access_token'],
                    music_id: this.music_id
                }
            })
            const details = res?.['data']?.['music_details']
            if (!details) return
            if (details.name) this.name = details.name
            if (details.mp3_url) this.mp3_url = details.mp3_url
            if (details.seconds) this.seconds = details.seconds
            if (details.sort_num) this.sort_num = details.sort_num
            if (details.conf_json?.lyric) this.lyric = details.conf_json.lyric
        },

        verify() {
            const { name, mp3_url, seconds, sort_num, lyric } = this
            const data = { name, mp3_url, seconds, sort_num }
            const errToast = (title, icon = 'none') => uni.showToast({ title, icon })
            if (!data.name) {
                errToast('请输入歌曲名称')
                return
            }
            if (!data.mp3_url) {
                errToast('请输入歌曲播放地址')
            }
            if (data.seconds) {
                const seconds = parseInt(data.seconds)
                if (isNaN(seconds)) {
                    errToast('请输入正确的歌曲时长')
                    return
                }
                data.seconds = seconds
            }
            if (lyric) {
                const conf_json = { lyric }
                data.conf_json = base64['encode'](JSON.stringify(conf_json))
            }

            data.access_token = app.globalData['access_token']
            data.active_id = this.id
            if (this.music_id) data.id = this.music_id

            this.save(data)
        },

        async save(data) {
            uni.showLoading({ title: '保存中...' })
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.audio_online.admin_manage/create_music',
                data
            })
            uni.hideLoading()
            if (!res['status'] || res['status'] !== 1) {
                xwy_api.alert(res?.['info'] || '保存失败')
                return
            }

            this.saveSuccess()
        },

        saveSuccess() {
            uni.showToast({
                title: '保存成功',
                icon: 'success'
            })
            const eventChannel = this.getOpenerEventChannel()
            eventChannel?.emit && eventChannel.emit('update')
            setTimeout(() => {
                uni.navigateBack()
            }, 1000)
        }
    }
}
</script>

<style lang="scss">
.page {
    padding-bottom: 80px;
}
.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}


.form-item .textarea {
    width: 100%;
    line-height: 16px;
    padding: 10px 0;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}

.save {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 15px;
    box-sizing: border-box;
    border-top: 1px solid #eee;
}
.save-btn {
    line-height: 44px;
    border-radius: 22px;
}
</style>
