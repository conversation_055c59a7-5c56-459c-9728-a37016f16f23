<template>
    <view>
        <view v-if="total" class="flex-kai">
            <view class="color-sub font14 p10">共{{total}}首歌曲</view>
            <view v-if="is_admin" class="p10" hover-class="navigator-hover" @click="addMusic(null)">
                <text class="color-primary font14">添加歌曲</text>
                <uni-icons type="forward" size="14" color="#2d8cf0"/>
            </view>
        </view>

        <view class="music-list">
            <view class="music-item" v-for="item in music_list" :key="item.id">
                <view class="flex-kai">
                    <view class="color-title">歌曲名称: {{item.name}}</view>
                    <view class="font14 pr10 color-light-primary" @click.stop="audioPlay(item)">
                        <text v-if="item.id === play_id">结束试听</text>
                        <text v-else>试听</text>
                    </view>
                </view>
                <view v-if="item.seconds" class="color-content">歌曲时长: {{item.seconds}}秒</view>
                <view v-if="is_admin" class="color-sub">添加时间: {{item.create_time}}</view>
                <view class="opt-bar flex-kai">
                    <view class="flex-row">
                        <view
                            v-if="item.id === play_id"
                            class="word-last-loading font12 color-light-primary"
                            style="line-height: 34px;"
                        >正在播放</view>
                    </view>
                    <view class="flex-row">
                        <template v-if="is_admin">
                            <view class="opt-item-icon" hover-class="navigator-hover" @click="addMusic(item.id)">
                                <text class="iconfont icon-edit color-sub"></text>
                            </view>
                            <view class="opt-item-icon" hover-class="navigator-hover" @click.stop="deleteMusic(item)">
                                <text class="iconfont icon-delete color-sub"></text>
                            </view>
                        </template>
                        <template v-else>
                            <view
                                hover-class="navigator-hover"
                                class="opt-item-text bg-primary"
                                @click="toRecorderCheck(item.id)"
                            >
                                <uni-icons type="mic" size="14" color="#ffffff"/>
                                <text class="color-white font14">录制</text>
                            </view>
                        </template>
                    </view>
                </view>
            </view>
        </view>

        <view v-if="loading" class="text-center">
            <view v-if="load_page === 1" style="padding-top: 30vh;"></view>
            <load-ani></load-ani>
        </view>

        <view v-if="!loading && !music_list.length" class="text-center" style="padding-top: 15vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub font14">暂无歌曲</view>

            <view
                v-if="is_admin"
                class="add-music-btn color-white bg-primary"
                hover-class="navigator-hover"
                @click="addMusic(null)"
            >添加歌曲</view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
export default {
    data() {
        return {
            is_admin: false,
            loading: true,
            music_list: [],
            load_page: 1,
            is_last_page: false,
            total: 0,
            play_id: null
        }
    },

    onLoad(e) {
        this.id = e.id
        if (e.is_admin) this.is_admin = true
        if (e.article_id) this.article_id = e.article_id
        login.uniLogin(err => {
            this.loading = false
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.init()
        })
    },

    beforeDestroy() {
        this.innerAudioContextDestroy()
    },

    onUnload() {
        this.innerAudioContextDestroy()
    },

    onShow() {
        if (!this.innerAudioContext) {
            this.createInnerAudioContext()
        }
    },

    onHide() {
        this.innerAudioContextDestroy()
    },

    onReachBottom() {
        !this.loading && !this.is_last_page && this.getMusicList()
    },

    methods: {
        createInnerAudioContext() {
            this.innerAudioContext = null
            let innerAudioContext = uni.createInnerAudioContext()
            innerAudioContext.autoplay = true

            innerAudioContext.onError(err => {
                console.log('播放错误', err)
                this.audioStop()
                xwy_api.alert('播放错误')
            })

            innerAudioContext.onPlay(() => {
                console.log('onPlay')
            })
            innerAudioContext.onCanplay(() => {
                console.log('onCanplay')
            })
            innerAudioContext.onEnded(() => {
                console.log('onEnded')
                this.play_id = null
            })
            this.innerAudioContext = innerAudioContext
        },

        innerAudioContextDestroy() {
            if (this.innerAudioContext != null) {
                this.play_id = null
                this.innerAudioContext.destroy()
                this.innerAudioContext = null
            }
        },

        audioPlay(item) {
            if (this.play_id) this.innerAudioContext.pause()
            if (item.id === this.play_id) {
                this.play_id = null
                return
            }
            this.play_id = item.id
            this.innerAudioContext.src = item.mp3_url
            this.innerAudioContext.play()
        },

        audioStop() {
            this.innerAudioContext.pause()
            this.play_id = null
        },

        async init() {
            !this.is_admin && await this.getActivityDetails()
            await this.getMusicList()
        },

        async getActivityDetails() {
            let activity_details = app.globalData['activity_detail'] || null
            if (!activity_details) {
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        access_token: app.globalData['access_token'],
                        active_id: this.id
                    }
                })

                activity_details = res.data.activity_details
            }
            this.activity_details = activity_details
        },

        async getMusicList() {
            this.loading = true
            if (this.load_page === 1) {
                this.music_list = []
                this.is_last_page = false
                this.total = 0
            }

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.audio_online.admin_manage/music_list',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id,
                    page: this.load_page,
                    perpage	: 10
                }
            })

            this.loading = false
            this.load_page++

            const res_data = res?.['data']?.music_list
            if (!res_data) {
                this.is_last_page = true
                return
            }

            const music_list = res_data.data || []
            this.music_list = this.music_list.concat(music_list)
            this.is_last_page = res_data.is_lastpage
            this.total = res_data.total
        },

        async toRecorderCheck(music_id) {
            if (this.activity_details.rank_set?.category_list) {
                let classify_list = this.classify_list
                if (!classify_list) {
                    uni.showLoading({ mask: true })
                    const classify_res = await xwy_api.getCategoryList({
                        active_id: this.id,
                        types: 16,
                        page: 1,
                        perpage: 6
                    })
                    uni.hideLoading()
                    classify_list = classify_res?.data?.category_list.data || []
                    this.classify_list = classify_list
                }

                if (!classify_list.length) {
                    uni.showToast({
                        title: '活动未添加作品分类，请联系管理员添加！',
                        icon: 'none'
                    })
                    return
                }

                const itemList = classify_list.map(item => item.name)
                uni.showActionSheet({
                    itemList,
                    success: res => this.toRecorder(music_id, classify_list[res.tapIndex].category_id)
                })

                return
            }

            this.toRecorder(music_id)
        },

        toRecorder(music_id, category_id = null) {
            let url = `/pages/voice/user/recorder?id=${this.id}&music_id=${music_id}`
            if (this.article_id) url += `&article_id=${this.article_id}`
            if (category_id) url += `&category_id=${category_id}`
            uni.navigateTo({
                url,
                events: {
                    submitSuccess: () => {
                        const eventChannel = this.getOpenerEventChannel()
                        eventChannel?.emit && eventChannel.emit('submitSuccess')
                    }
                }
            })
        },

        addMusic(id = null) {
            let url = './add?id=' + this.id
            if (id) url += `&music_id=${id}`
            uni.navigateTo({
                url,
                events: {
                    update: () => {
                        this.load_page = 1
                        this.getMusicList()
                    }
                }
            })
        },

        deleteMusic(item) {
            uni.showModal({
                title: '提示',
                content: `确定删除歌曲【${item.name}】?`,
                success: res => {
                    res.confirm && this.deleteMusicAjax(item.id)
                }
            })
        },

        async deleteMusicAjax(ids) {
            uni.showLoading({
                title: '删除中...'
            })
            const res = await xwy_api.deleteRecords(55, ids)
            uni.hideLoading()
            if (!res?.['status'] || res['status'] !== 1) {
                xwy_api.alert(res?.['info'] || '歌曲删除失败')
                return
            }

            uni.showToast({
                title: '歌曲删除成功',
                icon: 'success',
            })

            this.load_page = 1
            await this.getMusicList()
        }
    }
}
</script>

<style lang="scss">
.music-item {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
    box-shadow: 0 0 8px #eee;
    .opt-bar {
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px solid #eee;
        $opt-item-size: 32px;
        .opt-item-icon, .opt-item-text {
            height: $opt-item-size;
            line-height: $opt-item-size;
            text-align: center;
        }
        .opt-item-icon {
            width: $opt-item-size;
            border: 1px solid #eee;
            border-radius: 50%;
            margin-left: 10px;
        }
        .opt-item-text {
            width: 80px;
            border-radius: 16px;
        }
    }
}

.add-music-btn {
    margin: 30px auto;
    width: 250px;
    line-height: 44px;
    border-radius: 22px;
}
</style>
