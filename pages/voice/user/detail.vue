<template>
    <view class="page bg-background">

        <screen-picture
            v-if="screen_pic_show"
            :hide="screen_pic_hide"
            :image-src="screen_pic"
            :time="screen_pic_count_down"
            :show-button="true"
            @skipScreen="skipScreen"
        />


        <activity-delete-tips v-if="error" :activeId="id"/>

        <view v-if="detail && detail.active_id">

            <activity-logo-title-time :details="detail" :hide-share="detail.rank_set && detail.rank_set['share_closed']"/>

            <view class="icon-list flex-row flex-wrap text-center bdb-10 bg-white">

                <navigator
                    v-if="is_my_activity"
                    class="icon-item"
                    :url="'../admin/activity/manage?id=' + id"
                >
                    <text class="iconfont font24 color-primary icon-setting"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">活动管理</view>
                </navigator>
                <view
                    v-if="!is_joining"
                    class="icon-item"
                    @click="joinActivity"
                    hover-class="navigator-hover"
                >
                    <text class="iconfont font24 color-primary icon-users"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">参与活动</view>
                </view>
                <view
                    v-if="is_joining"
                    class="icon-item"
                    @click="uniPopupOpen('my_info')"
                    hover-class="navigator-hover"
                >
                    <text class="iconfont font24 color-primary icon-personal-data"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">报名信息</view>
                </view>
                <view
                    v-if="is_joining"
                    class="icon-item"
                    hover-class="navigator-hover"
                    @click="toSubmitVoice"
                >
                    <uni-icons type="mic" size="24" color="#2d8cf0"/>
                    <view class="color-sub font14" style="padding-top: 3px;">开始录制</view>
                </view>
                <view
                    class="icon-item"
                    hover-class="navigator-hover"
                    @click="lookVoiceList(true)"
                >
                    <text class="iconfont font24 color-primary icon-dating"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">我的作品</view>
                </view>

                <view
                    v-if="!detail.rank_set || !detail.rank_set['closed_top_rank']"
                    class="icon-item"
                    hover-class="navigator-hover"
                    @click="toTopList()"
                >
                    <text class="iconfont font24 color-primary icon-trophy"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">排行榜</view>
                </view>


                <view
                    class="icon-item"
                    @click="uniPopupOpen('activity_detail')"
                    hover-class="navigator-hover"
                >
                    <text class="iconfont font24 color-primary icon-feedback"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">活动说明</view>
                </view>


                <template v-if="headimg_plugin && headimg_plugin.length">
                    <template v-for="(item, index) in headimg_plugin">
                        <view class="icon-item" @click="toIdeaAvatar(item)" :key="index">

                            <image
                                v-if="item.key === 'chajian'"
                                :src="item.icon || 'https://apissl.xinweiyun.com/weixin/public/img/longmarch/icon_guajian.png'"
                                style="width: 24px; height: 24px;"
                            />

                            <image
                                v-if="item.key === 'beijing'"
                                :src="item.icon || 'https://apissl.xinweiyun.com/weixin/public/img/longmarch/icon_beijing.png'"
                                style="width: 24px; height: 24px;"
                            />
                            <view class="color-sub font14" style="padding-top: 1px;">{{ item.name }}</view>
                        </view>
                    </template>
                </template>



                <template v-if="detail.conf.active.detail_icon_list">

                    <navigator
                        v-for="(item, index) in detail.conf.active.detail_icon_list"
                        :key="index"
                        class="icon-item"
                        :url="item.type === 0 ? ('/pages/news/preview?id=' + item.id) : ('/pages/news/list?category_id=' + item.id)"
                    >
                        <text :class="'iconfont font24 color-primary ' + item.icon"></text>
                        <view class="color-sub font14" style="padding-top: 3px;">{{ item.title }}</view>
                    </navigator>
                </template>


                <view v-if="!detail.rank_set || !detail.rank_set['share_closed']" class="icon-item" hover-class="navigator-hover" @click="showActiveSharePopup">
                    <text class="iconfont font24 color-primary icon-share"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">分享</view>
                </view>
            </view>


            <view
                v-if="is_joining"
                class="to-submit-voice bg-primary flex-all-center"
                hover-class="navigator-hover"
                @click="toSubmitVoice"
            >
                <uni-icons type="mic" size="24" color="#ffffff"/>
            </view>


            <view v-if="!is_joining" class="flex-all-center pb10">
                <view class="join-btn bg-primary color-white" @click="joinActivity">参与活动</view>
            </view>


            <view class="bg-white" style="position: fixed; left: 0; bottom: 0; width: 100%; z-index: 99;">
                <view v-if="technology_support" class="flex-all-center">

                    <navigator
                        v-if="technology_support.news_id"
                        :url="'/pages/news/preview?id=' + technology_support.news_id + '&type=support'"
                        class="text-center font14 color-sub p10"
                    >{{ technology_support.button_text }}</navigator>
                    <view v-else class="text-center font14 color-sub p10">
                        {{ technology_support.button_text }}
                    </view>
                </view>

                <view v-if="!detail.rank_set || !detail.rank_set['closed_user_center']" class="flex-all-center pb10">
                    <navigator url="/pages/user/user" class="p5 color-sub font14">个人中心</navigator>
                </view>
            </view>

            <xwy-ad
                v-if="(!detail.rank_set || !detail.rank_set.closed_AD) && !screen_pic_show"
                :ad_type="3"
            />


            <xwy-ad :activity_id="id" :ad_type="66"/>


<!--            <view v-if="list_total" class="text-center p10 color-sub font14">共{{ list_total }}个作品</view>-->

            <view class="search-view flex-kai">
                <view class="search-input-view flex-row bg-white">
                    <view class="flex-all-center">
                        <input
                            type="text"
                            v-model="search_keyword"
                            placeholder="输入用户名字搜索"
                            confirm-type="search"
                            @confirm="search()"
                        />
                    </view>
                </view>
                <view
                    class="search-btn bg-primary color-white text-center"
                    hover-class="navigator-hover"
                    @click="search"
                >搜索
                </view>
            </view>

            <view v-if="category_list && category_list.length" class="category-list flex-center">
                <view
                    class="flex-all-center"
                    :style="{ width: 100 / category_list.length + '%' }"
                    v-for="item in category_list"
                    :key="item.category_id"
                    @click="changeCategory(item.category_id)"
                >
                    <view class="category-item" :class="{ 'category-active': item.category_id === category_id }">
                        {{ item.name }}
                    </view>
                </view>
            </view>

            <view
                v-if="detail.conf.active.voice_show_type && detail.conf.active.voice_show_type === 1"
                class="audio-list-new flex-row flex-wrap"
            >
                <template v-for="(item, index) in list">
                    <view
                        class="audio-item-new"
                        hover-class="navigator-hover"
                        :id="item._id"
                        :key="item.id"
                        @click="lookVoiceDetail(item)"
                    >
                        <image class="pic-1" :src="item.pic_1" mode="aspectFill"/>
                        <view class="bg-white p10">
                            <view class="text-center">{{ item['user_attend_details'].must_submit[0].value }}</view>
                            <view
                                v-if="item.conf_json && item.conf_json.audio_list && item.conf_json.audio_list[0] && item.conf_json.audio_list[0].music && item.conf_json.audio_list[0].music.name"
                                class="text-center pt5 color-content font14"
                            >
                                《{{ item.conf_json.audio_list[0].music.name }}》
                            </view>
                            <view class="audio-new-bottom-bar flex-kai pt10">
                                <view hover-stop-propagation @click.stop="like(index)">
                                    <text
                                        class="iconfont icon-love color-red"
                                        style="position: relative; top: 1px; left: 2px;"
                                    ></text>
                                    <text class="pl5 color-sub font14">{{ item.vote_num || 0 }}</text>
                                </view>

                                <view
                                    v-if="item.conf_json && item.conf_json.audio_list && item.conf_json.audio_list[0] && item.conf_json.audio_list[0].player_id && item.conf_json.audio_list[0].src"
                                    class="play-new flex-all-center"
                                    hover-stop-propagation
                                    @click.stop="audioPlay(item.conf_json.audio_list[0].player_id, item.conf_json.audio_list[0].src)"
                                >
                                    <text
                                        class="iconfont color-primary font18"
                                        :class="{
                                        'icon-play-filled': player_id !== item.conf_json.audio_list[0].player_id,
                                        'icon-pause-filled': player_id === item.conf_json.audio_list[0].player_id
                                    }"
                                    ></text>
                                </view>

                                <view
                                    class="font14 color-light-primary"
                                    hover-stop-propagation
                                    @click.stop="like(index)"
                                >点赞</view>
                            </view>
                        </view>
                    </view>

                    <view v-if="index !== 0 && ((index + 1) % 8 === 0)">
                        <xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>
                    </view>
                </template>
            </view>

            <view
                v-if="!detail.conf.active.voice_show_type || detail.conf.active.voice_show_type === '0'"
                class="voice-list-list"
            >
                <template v-for="(item, index) in list">
                    <view :key="item.id">
                        <view
                            class="voice-list-item bg-white"
                            :id="item._id"
                            hover-class="navigator-hover"
                            @click="lookVoiceDetail(item)"
                        >
                            <view class="flex-row">
                                <view class="pr10">
                                    <image
                                        class="voice-list-headimg"
                                        :src="item['user_attend_details'] && item['user_attend_details'].headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"
                                        mode="aspectFill"
                                    />
                                </view>
                                <view style="width: 100%;">
                                    <view
                                        v-if="item['user_attend_details'] && item['user_attend_details'].must_submit && item['user_attend_details'].must_submit.length"
                                        class="color-title"
                                    >{{ item['user_attend_details'].must_submit[0].value }}</view>
                                    <view v-if="item.memo" class="color-content pt5">{{ item.memo }}</view>

                                    <view
                                        v-if="item.conf_json && item.conf_json.pic_list && item.conf_json.pic_list && item.conf_json.pic_list.length"
                                        class="img-list flex-row flex-wrap pt5"
                                        :class="{
                                            'img-list-1': item.conf_json.pic_list.length === 1,
                                            'img-list-2': item.conf_json.pic_list.length === 2,
                                            'img-list-more': item.conf_json.pic_list.length >= 3
                                         }"
                                    >
                                        <image
                                            class="img-item"
                                            lazy-load
                                            v-for="(img_item, img_index) in item.conf_json.pic_list"
                                            :key="img_index"
                                            :src="img_item"
                                            :mode="item.conf_json.pic_list.length === 1 ? 'heightFix' : 'aspectFill'"
                                            @click.stop="previewImage(item.conf_json.pic_list, img_item)"
                                        />
                                    </view>

                                    <view
                                        v-if="item.conf_json && item.conf_json.audio_list && item.conf_json.audio_list.length"
                                        class="pt5"
                                    >
                                        <view
                                            class="voice-list-audio-item flex-row"
                                            v-for="(audio, idx) in item.conf_json.audio_list"
                                            :key="idx"
                                        >
                                            <view class="flex-all-center">
                                                <view
                                                    hover-class="navigator-hover"
                                                    class="voice-list-play-btn flex-all-center"
                                                    hover-stop-propagation
                                                    @click.stop="audioPlay(audio.player_id, audio.src)"
                                                >
                                                    <text
                                                        class="iconfont color-primary font18"
                                                        :class="{
                                                            'icon-play-filled': player_id !== audio.player_id,
                                                            'icon-pause-filled': player_id === audio.player_id
                                                        }"
                                                    ></text>
                                                </view>
                                            </view>
                                            <view class="pl10 voice-list-article-data">
                                                <view class="flex-kai">
                                                    <view
                                                        class="color-title ellipsis"
                                                        style="max-width: 150px;"
                                                    >
                                                        {{ (audio.music && audio.music.name) || (audio.article && audio.article.title) || default_article_title }}
                                                    </view>
                                                    <view class="color-sub font12">{{ audio.duration }}</view>
                                                </view>
                                                <view
                                                    v-if="audio.article && audio.article.content_preview"
                                                    class="color-content ellipsis font14"
                                                >{{ audio.article.content_preview }}</view>
                                            </view>
                                        </view>
                                    </view>

                                    <view class="flex-kai">
                                        <view class="color-sub font12 pt5">{{ item.create_time }}</view>
                                        <view class="flex-row">
                                            <view v-if="item['play_num']" class=" pr10 color-sub font14" 
                                                  style="padding-top: 3px;">
                                                播放{{ item['play_num'] }}次
                                            </view>
                                            <view hover-stop-propagation @click.stop="like(index)">
                                                <text
                                                    class="iconfont icon-love color-red"
                                                    style="position: relative; top: 1px; left: 2px;"
                                                ></text>
                                                <text class="pl5 color-sub font14">{{ item.vote_num || 0 }}</text>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view v-if="index !== 0 && ((index + 1) % 10 === 0)" style="padding-bottom: 20px;">
                            <xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>
                        </view>
                    </view>
                </template>
            </view>


            <uni-load-more v-if="loading && load_page !== 1" status="loading"></uni-load-more>
            <uni-load-more
                v-if="is_last_page && list.length > 5"
                status="noMore"
                :contentText="{contentnomore: '我是有底线的'}"
            ></uni-load-more>
            <uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>

            <view v-if="!loading && !list.length" class="text-center" style="padding: 20px 0;">
                <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
                <view class="color-sub font14">暂无作品</view>
            </view>

            <uni-popup ref="like_popup" type="center">
                <view class="voice-list-like_popup text-center bg-white">
                    <view class="voice-list-popup-close" @click="uniPopupClose('like_popup')">
                        <uni-icons type="close" size="24" color="#b2b3b7"/>
                    </view>
                    <view class="bg-primary color-white" style="padding: 30px 20px 20px;">
                        <icon
                            :type="like_status === 'success' ? 'success' : 'clear'"
                            size="80"
                            color="#ffffff"
                        ></icon>
                        <view class="font18">点赞{{ like_status === 'success' ? '成功' : '失败' }}</view>
                    </view>
                    <view class="bg-white color-info" style="padding: 20px;">
                        <view class="color-content text-center font14" style="padding-bottom: 20px;">
                            {{ like_info }}
                        </view>
                    </view>
                    <xwy-ad v-if="popup_open" :activity_id="id" :ad_type="3"></xwy-ad>
                </view>
                <view class="pt5">
                    <xwy-ad v-if="popup_open" :activity_id="id" :ad_type="66"></xwy-ad>
                </view>
            </uni-popup>
        </view>

        <view v-if="join_popup_show" class="join-popup flex-all-center bg-white" @touchmove.stop.prevent="">
            <view>
                <view class="join-popup-c bg-white">
                    <view class="text-center font18 color-content p10">
                        <template v-if="update_attend_details">修改报名信息</template>
                        <template v-else>参加活动</template>
                    </view>


                    <template v-if="must_submit.length">
                        <template v-for="(item, index) in must_submit">
                            <input
                                v-if="item.types === 1 || item.types === 3"
                                :key="index"
                                class="join-input"
                                v-model="item.value"
                                :placeholder="'请输入' + item.title + (item.rules === 1 ? ' (必填)' : '')"
                            />
                            <picker
                                v-if="item.types === 2"
                                :range="item.options"
                                range-key="text"
                                @change="mustValueChange($event, index)"
                            >
                                <view class="join-input flex-kai">
                                    <view v-if="!item.value" class="color-sub">
                                        请选择{{ item.title }}{{ item.rules === 1 ? ' (必选)' : '' }}
                                    </view>
                                    <view v-if="item.value">{{ item.value }}</view>
                                    <text class="iconfont icon-more color-disabled font18"/>
                                </view>
                            </picker>
                        </template>
                    </template>


                    <input
                        v-if="!update_attend_details && detail.conf.active.enter_types === 4"
                        class="join-input"
                        v-model="join_password"
                        placeholder="请输入报名密码"
                    />


                    <view class="join-popup-btns flex-row text-center font18">
                        <view class="join-popup-btn-cancel color-sub" @click="cancelJoin">取消</view>
                        <view class="join-popup-btn-confirm color-success" @click="joinAjax">确定</view>
                    </view>
                </view>

                <template v-if="!detail.rank_set || !detail.rank_set.closed_AD">
                    <view class="pt5">
                        <xwy-ad :ad_type="66"></xwy-ad>
                    </view>
                    <xwy-ad :ad_type="3"></xwy-ad>
                </template>
            </view>


        </view>

        <active-share ref="activeShare"/>

        <uni-popup ref="activity_detail" type="center" @touchmove.stop.prevent="">
            <view
                v-if="detail && detail.conf && detail.conf.active"
                class="uni-popup-info detail-popup bg-white"
            >
                <view class="popup-close" @click="uniPopupClose('activity_detail')">
                    <uni-icons type="close" size="28" color="#b2b3b7"/>
                </view>
                <scroll-view
                    scroll-y
                    class="detail-popup-detail"
                    style="max-height: calc(100vh - 200px); padding: 10px 0;"
                >
                    <view class="p10 bdb-10 bg-white">
                        <view class="text-center color-sub pb5">- 活动规则 -</view>

                        <view class="color-content font16">
                            活动参与方式：
                            <template v-if="detail.conf.active.enter_types === 1">
                                自由报名参与活动
                            </template>
                            <template v-if="detail.conf.active.enter_types === 2">
                                需要输入密码才能报名
                            </template>
                            <template v-if="detail.conf.active.enter_types === 3">
                                报名需要审核通过才能参与活动
                            </template>
                        </view>
                    </view>

                    <view v-if="detail.content || news_detail" class="p10 bdb-10 bg-white">
                        <view class="text-center color-sub pb5">- 活动说明 -</view>
                        <view class="color-content font16">
                            <template v-if="detail.content && !news_detail">
                                <rich-text :nodes="detail.content" space="nbsp" selectable></rich-text>
                            </template>
                            <template v-if="news_detail">
                                <template v-if="news_detail.content">
                                    <u-parse :content="news_detail.content"/>
                                </template>
                            </template>
                        </view>
                    </view>
                    <xwy-ad
                        v-if="!loading && (!detail.rank_set || !detail.rank_set.closed_AD)"
                        :ad_type="66"
                    />
                </scroll-view>
            </view>
        </uni-popup>

        <uni-popup ref="my_info" type="center" @touchmove.stop.prevent="">
            <view v-if="detail && detail.conf && detail.conf.active" class="uni-popup-info bg-white p20">
                <view class="popup-close" @click="uniPopupClose('my_info')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="text-center color-sub pb5">- 活动报名信息 -</view>
                <view class="text-center p10">
                    <image
                        class="headimg"
                        :src="headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"
                        mode="aspectFill"
                    />
                    <view>
                        <text class="color-primary" @click="updateHeadimg">更改头像</text>
                    </view>
                </view>


                <view
                    class="color-content font16 ptm5"
                    v-for="(item, index) in must_submit"
                    :key="index"
                    @click="updateAttendDetailShow"
                >
                    <text>
                        {{ item.title }}：
                        <template>
                            <template v-if="item.value">{{ item.value }}</template>
                            <template v-else>
                                <template v-if="item.types === 1">未填写</template>
                                <template v-if="item.types === 2">未选择</template>
                            </template>
                        </template>
                    </text>
                    <text v-if="is_joining" class="iconfont icon-edit color-sub pl5"></text>
                </view>

                <template v-if="popup_open && (!detail.rank_set || !detail.rank_set.closed_AD)">
                    <view style="position: relative; left: -10px;">
                        <xwy-ad :ad_type="66"></xwy-ad>
                    </view>
                    <xwy-ad :ad_type="3"></xwy-ad>
                </template>
            </view>
        </uni-popup>


        <template v-if="password_dialog_show">
            <uni-popup
                ref="input_password"
                type="dialog"
                mode="input"
                :is-mask-click="false"
                @maskClick="copy(id)"
            >
                <uni-popup-dialog
                    mode="input"
                    title="活动密码"
                    :value="password"
                    placeholder="请输入活动密码"
                    @confirm="passwordInputConfirm"
                    @close="passwordInputClose"
                ></uni-popup-dialog>
            </uni-popup>
        </template>


        <uni-popup ref="notice_popup" type="center" @touchmove.stop.prevent="" :is-mask-click="false">
            <view
                v-if="detail.conf.active.active_details_notice && detail.conf.active.active_details_notice.news_detail && detail.conf.active.active_details_notice.news_detail.id"
                class="bg-white"
                style="width: 320px; border-radius: 10px;"
            >
                <view class="text-center color-title p10" style="border-bottom: 1px solid #eee;">
                    {{ detail.conf.active.active_details_notice.news_detail.title || '活动须知' }}
                </view>
                <scroll-view
                    scroll-y
                    class="color-content"
                    style="height: calc(85vh - 170px); margin: 10px; width: 300px;"
                >
                    <u-parse :content="detail.conf.active.active_details_notice.news_detail.content || ''"/>
                </scroll-view>

                <view class="flex-row pt10 pl5" style="border-top: 1px solid #eee;">
                    <view @click="noticeAgreeChange">
                        <radio
                            :checked="detail.conf.active.active_details_notice.agree"
                            style="transform: scale(.7); position: relative; top: -3px; right: -3px;"
                        />
                    </view>
                    <view class="font14 color-sub">
                        我已阅读并同意《{{ detail.conf.active.active_details_notice.news_detail.title || '活动须知' }}》。
                    </view>
                </view>
                <view class="flex-all-center pb10">
                    <view
                        class="color-white text-center"
                        :class="{
                            'bg-primary': detail.conf.active.active_details_notice.agree,
                            'bg-disabled': !detail.conf.active.active_details_notice.agree
                        }"
                        style="line-height: 40px; border-radius: 20px; width: 200px;"
                        @click="noticeConfirm"
                    >{{ detail.conf.active.active_details_notice.confirm_text || "确定" }}</view>
                </view>
                <view v-if="detail.rank_set && detail.rank_set['shield_other']" class="flex-all-center pb10">
                    <navigator class="color-sub font14 pl10 pr10" url="/pages/user/user">个人中心</navigator>
                </view>
            </view>
        </uni-popup>

        <expiration-reminder ref="expirationReminder"/>

        
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import my_storage from '@/utils/storage.js'
import base64 from '@/utils/base64.js'
import activity_tool from '@/utils/acyivity_tool.js'
import aApi from '../api'


export default {
    data() {
        return {
            evn_version: app.globalData['evn_version'],
            loading: true,
            screen_pic: '',
            screen_pic_show: false,
            screen_pic_hide: false,
            screen_pic_count_down: null,
            id: '',
            userid: '',
            is_my_activity: false,
            detail: {},
            user_details: {},
            error: '',
            join_popup_show: false,
            update_attend_details: false,
            headimg: '',
            username: '',
            checked: 0,
            is_joining: false,
            password: '',
            technology_support: false,
            must_submit: [],
            headimg_plugin: [],
            password_dialog_show: false,
            popup_open: false,
            news_detail: null,
            platform: uni.getSystemInfoSync().platform,
            list: [],
            list_total: 0,
            load_page: 1,
            is_last_page: false,
            player_id: '',
            like_status: '',
            like_info: '',
            default_article_title: '自由录制',
            category_list: null,
            category_id: null,
            join_password: '',
            search_keyword: ''
        }
    },


    onLoad(e) {
        this.setDefaultArticleTitle(e.id)
        console.log('活动详情页面路径参数', e)

        // #ifdef H5
        const a = 'a' // 为了避免HbuilderX函数出现return，后面的代码会出现黄色波浪线
        if (a === 'a') {
            xwy_api.alert('请在小程序内打开', {
                success: () => uni.navigateBack()
            })
            return false
        }
        // #endif

        if (uni.getLaunchOptionsSync().scene === 1154) return this.getSimpleDetail(e.id)

        e.screen_pic ? this.screenPicShow(e.screen_pic) : this.$uni.showLoading('数据加载中...')

        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }


            if (e.scene) return this.analysisScene(e.scene)

            if (!e.id) {
                this.loading = false
                this.error = '请指定活动id'
                return uni.hideLoading()
            }


            this.id = e.id

            this.userid = app.globalData['userid']

            this.getDetail()


        })
    },
    onReachBottom() {
        !this.loading && !this.is_last_page && this.getList()
    },
    onUnload() {
        this.next_play_timeout && clearTimeout(this.next_play_timeout)
        this.player_id && this.innerAudioContext && this.innerAudioContext.stop()
        !this.is_ios && this.innerAudioContext && this.innerAudioContext.destroy()
        this.screenPicShowInterval && clearInterval(this.screenPicShowInterval)
    },

    onHide() {
        this.next_play_timeout && clearTimeout(this.next_play_timeout)
        if (this.player_id && this.innerAudioContext) {
            this.player_id = ''
            this.innerAudioContext.stop()
            !this.is_ios && this.innerAudioContext.destroy()
            this.innerAudioContext = null
        }
    },

    onShow() {
        if (!this.innerAudioContext) {
            this.audioContextOn()
        }
    },


    onShareAppMessage() {
        this._utils.shareCopyActivityId(this.detail)

        let url = '/pages/voice/user/detail?id=' + this.id;
        if (this.detail?.conf?.active?.screen_pic) url += `&screen_pic=${this.detail.conf.active.screen_pic}`
        
        return {
            title: this.detail.name,
            path: url,
            imageUrl: this.detail.logo || ''
        }
    },

    onShareTimeline() {
        return {
            title: this.detail.name,
            imageUrl: this.detail.logo || ''
        }
    },

    methods: {
        setDefaultArticleTitle(id) {
            if (id && id === 'adf261e6c0ee94145df58d7ab41bdb52') this.default_article_title = '自由歌曲录制'
        },


        lookVoiceDetail(item) {
            uni.navigateTo({
                url: './voice_detail?id=' + item.id + '&active_id=' + this.id,
                events: {
                    upDatePlayNum: () => this.addPlayNum(item.id.toString())
                }
            })
        },

        audioContextOn() {

            this.is_ios = false
            const {model, system} = uni.getSystemInfoSync()
            if (model.startsWith('iPhone') || system.startsWith('iOS')) {
                this.is_ios = true
                this.innerAudioContext = uni.getBackgroundAudioManager()
            } else {
                this.innerAudioContext = uni.createInnerAudioContext()
                this.innerAudioContext.obeyMuteSwitch = false
            }


            this.innerAudioContext.onPlay(() => {
                console.log('onPlay')
                this.addPlayNum(this.player_id)
            })
            this.innerAudioContext.onPause(() => {
                console.log('onPause')
            })
            this.innerAudioContext.onStop(() => {
                console.log('onStop')
            })
            this.innerAudioContext.onEnded(() => {
                console.log('onEnded')
                // 没有关闭广告的不自动播放下一个语音，因为要看广告才能播放
                if (!this.detail.rank_set?.closed_AD) {
                    this.player_id = ''
                    return false
                }
                const player_id = this.player_id
                this.playNext(player_id)
            })
            this.innerAudioContext.onError(err => {
                console.log('onError', err)
                this.player_id = ''
                const err_opt = {
                    '10001': '系统错误',
                    '10002': '网络错误',
                    '10003': '文件错误',
                    '10004': '格式错误',
                    '-1': '未知错误'
                }
                xwy_api.alert(`播放失败 (${err.errCode}: ${err_opt[err.errCode]})`)
            })



            this.innerAudioContext.onTimeUpdate(() => {
                console.log('onTimeUpdate', this.innerAudioContext.duration)
                if (this.innerAudioContext.duration && this.innerAudioContext.duration !== Infinity) {
                    this.setAudioDuration(this.innerAudioContext.duration)
                }
            })
        },

        setAudioDuration(duration) {
            console.log(duration)
            if (this.player_id) {
                const id_arr = this.player_id.split('_')
                const id = Number(id_arr[1]), src_index = Number(id_arr[3])
                const list_index = this.list.findIndex(v => v.id === id)
                if (list_index !== -1) {
                    duration = Math.floor(duration)
                    let mm = Math.floor(duration / 60)
                    if (mm < 10) mm = '0' + mm
                    let ss = duration % 60
                    if (ss < 10) ss = '0' + ss
                    console.log(mm, ss)
                    this.list[list_index].conf_json.audio_list[src_index].duration = mm + ':' + ss
                }
            }
        },

        async addPlayNum(player_id) {
            if (!player_id) return
            const id = player_id.match(/\d+/)[0]
            if (!id) return
            const res = await aApi.addPlayNum(this.id, id)
            if (res?.status) {
                const index = this.list.findIndex(v => v.id === Number(id))
                if (index !== -1) this.list[index]['play_num']++
            }
        },
        

        playNext(player_id) {
            let audio_list = []
            this.list.forEach(v => {
                if (v.conf_json?.audio_list?.length) {
                    audio_list = [...audio_list, ...v.conf_json.audio_list]
                }
            })
            const index = audio_list.findIndex(v => v.player_id === player_id)
            if (index === -1 || index === audio_list.length - 1) return false
            const nextAudio = audio_list[index + 1]

            this.next_play_timeout = setTimeout(() => {
                uni.pageScrollTo({
                    selector: '#' + nextAudio.parent_id,
                    fail: err => {
                        console.log('滚动失败', err)
                    }
                })
                this.audioPlay(nextAudio.player_id, nextAudio.src)
            }, 1000)
        },

        audioPlay(player_id, src) {
            this.next_play_timeout && clearTimeout(this.next_play_timeout)

            this.audio_src = src
            if (!this.player_id) {
                // 没有播放音频，播放点前点击音频
                this.player_id = player_id

                // 没有去广告的活动要看完激励视频广告才能听
                if (!this.detail.rank_set?.closed_AD) {
                    this.showRewardedVideoAd()
                    return false
                }

                this.AudioPlay()


                return false
            }

            if (player_id === this.player_id) {
                // 播放的是点击的音频，暂停音频播放
                this.player_id = ''
                this.innerAudioContext.pause()
                return false
            }

            // 切换音频播放
            this.innerAudioContext.stop()
            this.player_id = player_id

            // 没有去广告的活动要看完激励视频广告才能听
            if (!this.detail.rank_set?.closed_AD) {
                this.showRewardedVideoAd()
                return false
            }

            this.AudioPlay()
        },


        createRewardedVideoAd() {
            if (this.detail.rank_set?.closed_AD) return false
            let adid = null
            if (app.globalData['shop_info']?.['shop_set']?.ad_list?.length) {
                const ad_list = app.globalData['shop_info']?.['shop_set'].ad_list
                const RewardedVideoAd = ad_list.find(v => v['ad_types'] === 2)
                if (RewardedVideoAd && RewardedVideoAd['ad_id']) adid = RewardedVideoAd['ad_id']
            }

            if (adid === null) return false

            this.RewardedVideoAd = uni.createRewardedVideoAd({
                adUnitId: adid
            })

            this.RewardedVideoAd.onLoad(() => {
                uni.hideLoading()
                console.log('onload')
            })
            this.RewardedVideoAd.onError(err => {
                uni.hideLoading()
                console.log('onError', err)
                this.RewardedVideoAdError = true
            })
            this.RewardedVideoAd.onClose(res => {
                console.log('onClose', res)
                if (!res['isEnded']) {
                    this.player_id = ''
                    return false
                }
                this.AudioPlay()
            })
        },

        showRewardedVideoAd() {
            if (!this.RewardedVideoAd || this.RewardedVideoAdError) {
                this.AudioPlay()
                return false
            }
            this.RewardedVideoAd.show()
        },

        AudioPlay() {
            if (this.is_ios) {
                let title = this.default_article_title, singer = '微信用户',
                    coverImgUrl = 'https://bjetxgzv.cdn.bspapp.com/VKCEYUGU-uni-app-doc/7fbf26a0-4f4a-11eb-b680-7980c8a877b8.png'

                const list = this.list
                const play_id_arr = this.player_id.split('_')
                const item = list.find(v => v.id === Number(play_id_arr[1]))
                if (item) {
                    if (item['user_attend_details']?.must_submit?.length) singer = item['user_attend_details']['must_submit'][0].value
                    if (item['user_attend_details']?.headimg) coverImgUrl = item['user_attend_details'].headimg
                    const index = Number(play_id_arr[3])
                    if (item.conf_json.audio_list[index].article?.title) title = item.conf_json.audio_list[index].article.title
                    if (item.conf_json.audio_list[index].music?.name) title = item.conf_json.audio_list[index].music.name
                }

                this.innerAudioContext.title = title
                this.innerAudioContext.singer = singer
                this.innerAudioContext.coverImgUrl = coverImgUrl
                this.innerAudioContext.src = this.audio_src

                return false
            }
            this.innerAudioContext.src = this.audio_src
            this.innerAudioContext.play()
        },


        async showNoticePopup() {
            const active_details_notice = this.detail?.conf?.active?.active_details_notice
            if (!this.detail?.rank_set?.active_details_notice || !active_details_notice?.open || !active_details_notice?.news_id) return false
            if (this.user_details?.id) return false

            uni.showLoading({
                mask: app.globalData['evn_version'] !== 'trial'
            })
            const news_detail = await xwy_api.getNewsDetail(active_details_notice.news_id)
            this.detail.conf.active.active_details_notice.agree = false
            this.detail.conf.active.active_details_notice.news_detail = news_detail
            uni.hideLoading()
            this.uniPopupOpen('notice_popup')
        },

        noticeAgreeChange() {
            this.detail.conf.active.active_details_notice.agree = !this.detail.conf.active.active_details_notice.agree
            this.$forceUpdate()
        },

        noticeConfirm() {
            if (!this.detail.conf.active.active_details_notice.agree) {
                const news_title = this.detail.conf.active.active_details_notice.news_detail.title || '活动须知'
                uni.showToast({
                    title: `请勾选 我已阅读并同意《${news_title}》`,
                    icon: 'none'
                })
                return false
            }
            this.uniPopupClose('notice_popup')
        },

        changeCategory(category_id) {
            if (this.category_id === category_id) return
            this.category_id = category_id
            this.load_page = 1
            uni.showLoading({ mask: true })
            this.getList()
        },

        async getCategoryList() {
            if (this.detail.rank_set?.category_list) {
                let category_list = this.category_list
                if (!category_list) {
                    const res = await xwy_api.getCategoryList({
                        active_id: this.id,
                        types: 16,
                        page: 1,
                        perpage: 6
                    })
                    category_list = res?.['data']?.category_list.data || []
                }

                if (category_list.length) {
                    this.category_list = category_list
                    this.category_id = category_list[0].category_id
                }
            }
            await this.getList()
        },

        search() {
            this.load_page = 1
            this.$uni.showLoading()
            this.getList()
        },

        async getList() {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
            }
            const data = {
                active_id: this.id,
                page: this.load_page,
                perpage: 10
            }
            if (this.category_id) data.category_id = this.category_id
            if (this.search_keyword) data.name = this.search_keyword

            this.loading = true

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.audio_online.user_submit/audio_list',
                data
            })

            this.loading = false
            uni.hideLoading()

            this.load_page++

            if (res?.['data']?.audio_list) {
                const data_ = res['data'].audio_list
                this.list_total = data_.total
                this.is_last_page = data_.is_lastpage
                const list = data_.data || []

                if (list.length) {
                    list.forEach(v => {
                        const id = `id_${v.id}`
                        v._id = id
                        if (v.conf_json?.audio_list?.length) {
                            v.conf_json.audio_list.forEach((audio, i) => {
                                audio.player_id = `id_${v.id}_i_${i}`
                                audio.parent_id = id
                                audio.duration = ''
                            })
                        }
                        v.pic_1 = ''
                        if (v['user_attend_details']?.headimg) v.pic_1 = v['user_attend_details'].headimg
                        if (v.conf_json?.pic_list?.length) v.pic_1 = v.conf_json.pic_list[0]
                    })
                }
                this.list = [...this.list, ...list]
            } else {
                this.is_last_page = true
            }

        },

        async like(index) {
            const item = this.list[index]

            if (!item.checked) {
                xwy_api.alert('记录未审核通过，无法点赞。')
                return false
            }

            this.$uni.showLoading('点赞中...')

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/friend_agree_vote_record',
                data: {
                    access_token: app.globalData['access_token'],
                    id: item.id,
                    active_id: this.id,
                    act_types: 1
                }
            })

            uni.hideLoading()

            let like_status = 'success',
                like_info = '给好友点赞成功！'

            if (res && res['status']) {
                item.vote_num = item.vote_num || 0
                item.vote_num++
            } else {
                like_status = 'error'
                like_info = '给好友点赞失败！'
            }
            if (res && res['info']) {
                like_info = res['info'].replace(/投票/g, '点赞').replace(/票/g, '次')
            }
            this.like_status = like_status
            this.like_info = like_info

            this.uniPopupOpen('like_popup')

        },

        toTopList() {
            // uni.removeStorageSync('top_rank_banner')
            // if (this.detail.conf?.active?.top_rank_banner?.length) {
            //     uni.setStorageSync('top_rank_banner', this.detail.conf.active.top_rank_banner);
            // }
            //
            // let closed_AD = 0
            //
            // if (this.detail.rank_set) {
            //     const rank_set = this.detail.rank_set
            //     if (rank_set.closed_AD) closed_AD = rank_set.closed_AD
            // }
            //
            // let url = `./ranking_list?id=${this.id}&closed_AD=${closed_AD}`
            // if (this.detail?.rank_set?.category_list) url += `&works_ranking=1`
            //
            // uni.navigateTo({ url })

            uni.navigateTo({
                url: '/pages/ranking-list/ranking-list?id=' + this.id
            })
        },

        lookVoiceList(my_self = false) {
            let url = './voice_list?id=' + this.id
            if (my_self) url += '&is_myself=1'
            uni.navigateTo({
                url
            })
        },

        async getSimpleDetail(id) {
            this.$uni.showLoading('数据加载中...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details_simple',
                data: {
                    active_id: id
                }
            })
            this.loading = false
            uni.hideLoading()

            if (!res || !res['data'] || !res['data']['active_details']) {
                this.loading = false
                uni.hideLoading()
                this.error = (res['info'] || '活动详情获取失败。') + '活动id：' + this.id
                return false
            }


            if (res['data'].active_more_data) {
                const active_more_data = res['data']['active_more_data']
                this.active_more_data = active_more_data
                if (active_more_data['technology_support']) {
                    this.technology_support = res['data']['active_more_data'].technology_support
                }
                if (active_more_data['active_conf_set']) {
                    const active_conf_set = active_more_data['active_conf_set']
                    if (active_conf_set.headimg_plugin && active_conf_set.headimg_plugin.length) {
                        this.headimg_plugin = active_conf_set.headimg_plugin
                    }
                }
            }


            const detail = res['data']['active_details']

            this.detail = detail

            my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)


            if (detail.conf) {
                const conf = detail.conf

                if (!this.screen_pic && conf.active?.screen_pic) {
                    this.screenPicShow(conf.active.screen_pic)
                }


                if (conf.must_submit) {
                    const must_submit = conf.must_submit
                    delete conf.must_submit
                    if (must_submit.length) {
                        must_submit.forEach(v => v.value = v.value || '')
                        this.must_submit = must_submit
                    }
                }
            }



            this.addLookRecords()

            if (detail.name) {
                uni.setNavigationBarTitle({
                    title: detail.name
                })
            }
        },

        toIdeaAvatar(item) {
            if (!item.img_list || !item.img_list.length) {
                uni.showModal({
                    title: '提示',
                    content: '活动未配置头像图片列表，无法使用此功能',
                    showCancel: false
                })

                return false
            }

            let path = '/pages/idea_avatar/pendant/pendant'
            if (item.key === 'beijing') path = '/pages/idea_avatar/background/background'
            path += `?id=${this.id}`
            if (this.detail.rank_set?.closed_AD) path += '&closed_AD=1'

            this.$uni.navigateTo(path, {
                success: res => res.eventChannel.emit('img_list', item.img_list)
            })
        },


        screenPicShow(src) {
            uni.hideLoading()
            this.screen_pic = src
            this.screen_pic_count_down = 5
            this.screen_pic_show = true
            this.screenPicShowInterval = null
            this.screenPicShowInterval = setInterval(() => {
                this.screen_pic_count_down--
                if (this.screen_pic_count_down <= 0) {
                    this.skipScreen()
                }
            }, 1000)
        },
        skipScreen() {
            clearInterval(this.screenPicShowInterval)
            this.screen_pic_count_down = 0
            this.screen_pic_hide = true

            const timeout = setTimeout(() => {
                this.screen_pic_show = false
                this.screen_pic_hide = false
                if (this.loading) this.$uni.showLoading('数据加载中...')
                this.passwordDialogShow()
                clearTimeout(timeout)
            }, 500)
        },
        analysisScene(scene) {
            const sceneStr = decodeURIComponent(scene)
            console.log(sceneStr)
            const id = utils.getUrlParams('id', sceneStr)
            console.log('id===', id)
            if (!id) {
                uni.hideLoading()
                return this.$uni.showModal('从二维码获取id失败')
            }

            this.getActiveId(id)
            this.setDefaultArticleTitle(id)
        },


        async getActiveId(id) {
            const data = {
                access_token: app.globalData['access_token'],
                id
            }

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/get_long_active_id_by_short_id',
                data
            })
            if (res?.['data']?.['long_active_id']) {
                this.id = res['data']['long_active_id']
                this.getDetail()
            } else {
                uni.hideLoading()
                xwy_api.alert(res && res['info'] || '长id获取失败')
            }
        },

        getDetail(just_update = false) {
            xwy_api.getActivityDetail(this.id, res => {
                if (!res || !res.data || !res.data['active_details']) {
                    this.loading = false
                    uni.hideLoading()
                    this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                    return false
                }

                // #ifndef H5
                this.$refs.expirationReminder.open(res.data.active_details)
                // #endif

                if (res.data.active_more_data) {
                    const active_more_data = res.data.active_more_data
                    this.active_more_data = active_more_data
                    if (active_more_data.technology_support) {
                        this.technology_support = res.data.active_more_data.technology_support
                    }
                    if (active_more_data['active_conf_set']) {
                        const active_conf_set = active_more_data['active_conf_set']
                        if (active_conf_set.headimg_plugin && active_conf_set.headimg_plugin.length) {
                            this.headimg_plugin = active_conf_set.headimg_plugin
                        }
                    }
                }


                const detail = res.data['active_details']

                app.globalData.activity_detail = detail

                this.detail = detail

                my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)


                if (detail.conf) {
                    const conf = detail.conf

                    if (conf.active) {
                        const active = conf.active
                        if (!this.screen_pic && active.screen_pic) {
                            this.screenPicShow(active.screen_pic)
                        }
                        if (active.news?.news_id) this.changeDetailContent(active.news.news_id)
                    }

                    if (conf.must_submit) {
                        const must_submit = conf.must_submit
                        delete conf.must_submit
                        if (must_submit.length) {
                            must_submit.forEach(v => v.value = v.value || '')
                            this.must_submit = must_submit
                        }
                    }
                }
                
                // #ifdef MP-WEIXIN
                if (detail.rank_set?.['shield_other']) this.$uni.hideHomeButton()
                if (detail.rank_set?.['share_closed']) uni.hideShareMenu(undefined)
                // #endif


                this.addLookRecords()

                if (detail.name) {
                    uni.setNavigationBarTitle({
                        title: detail.name
                    })
                }

                if (!just_update && app.globalData['userid'] === detail.userid) {
                    this.is_my_activity = true
                }

                this.getUserStatus()

                this.load_page = 1
                this.is_last_page = false

                this.getCategoryList()

                this.createRewardedVideoAd()
            })
        },


        addLookRecords() {
            const detail = this.detail
            const value = {
                active_id: detail.active_id,
                name: detail.name,
                types: detail.types,
                logo: detail.logo || this.xwy_config.active_default_logo,
                look_time: new Date().getTime()
            }

            if (detail.organizer) value.organizer = detail.organizer
            my_storage.addActivityLookRecords(value)
        },


        async getUserStatus() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id
                }
            })

            if (res?.['data']?.user_details) {
                const attend_details = res['data'].user_details
                this.user_details = attend_details
                this.is_joining = true
                this.checked = attend_details.checked || 0

                this.loading = false
                uni.hideLoading()

                if (attend_details.nickname) this.username = attend_details.nickname
                if (attend_details.headimg) this.headimg = attend_details.headimg

                if (attend_details.must_submit) {
                    this.must_submit.forEach(v => {
                        attend_details.must_submit.forEach(vv => {
                            if (vv.title === v.title) v.value = vv.value
                        })
                    })
                    this.$forceUpdate()
                }

            } else {
                this.no_attend = true
                this.loading = false
                uni.hideLoading()

                if (this.screen_pic_show) return false

                this.passwordDialogShow()
            }

        },


        changeDetailContent(news_id) {
            xwy_api.ajax({
                url: "front.news/news_details",
                data: {
                    access_token: app.globalData['access_token'],
                    news_id
                },
                success: res => {
                    console.log(res);
                    uni.hideLoading();

                    uni.hideLoading()
                    if (!res.data || !res.data['news_details']) {
                        uni.showModal({
                            title: '提示',
                            content: res.info || '文章内容获取失败',
                            showCancel: false,
                            success: () => uni.navigateBack()
                        })
                        return false
                    }

                    const detail = res.data['news_details']


                    if (detail.video_url) {
                        let video_type = 'txv_id'
                        if (detail.video_url.startsWith('http://') || detail.video_url.startsWith('https://')) {
                            video_type = 'http'
                        }
                        detail.video_type = video_type
                    }


                    if (detail.content) {
                        detail.content = utils.newsContentInit(detail.content)
                    }

                    this.news_detail = detail
                }
            })
        },


        passwordDialogShow() {
            if (this.detail.conf.active.password && this.no_attend) {
                const passwordDialogShow = () => {
                    this.password_dialog_show = true
                    const password = my_storage.getActivityPassword(this.id)
                    if (password) {
                        this.password = password
                        this.checkActivityPassword(password)
                    }
                    const timeout = setTimeout(() => {
                        this.$refs.input_password.open()
                        clearTimeout(timeout)
                    }, 30)
                }

                // 体验版不需要输入密码
                if (app.globalData['evn_version'] === 'trial') {
                    return uni.showModal({
                        title: '提示',
                        content: '此活动设置了活动密码，请勿报名参与活动！！！',
                        cancelText: '进入活动',
                        confirmText: '输入密码',
                        success: res => {
                            res.confirm && passwordDialogShow()
                        }
                    })
                }


                passwordDialogShow()

                return false
            }

            this.showNoticePopup()
        },

        passwordInputConfirm(val) {
            if (!val) {
                // 纯净版取消输入密码出现去个人中心选项
                if (this.detail?.rank_set?.['shield_other']) {
                    return uni.showModal({
                        title: '提示',
                        content: '请输入密码',
                        cancelText: '个人中心',
                        confirmText: '重新输入',
                        success: res => {
                            if (res?.confirm) return this.$refs.input_password.open()
                            this.$uni.navigateTo('/pages/user/user')
                        }
                    })
                }

                xwy_api.alert('请输入密码', {
                    success: () => this.$refs.input_password.open()
                })

                return false
            }
            this.checkActivityPassword(val)
        },

        async checkActivityPassword(password) {
            this.$uni.showLoading('密码验证中...')

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/check_active_password',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id,
                    password
                }
            })
            uni.hideLoading()

            if (res?.['status']) {
                my_storage.rememberActivityPassword(this.id, password)
                this.$refs.input_password.close()
                uni.showToast({
                    title: '密码正确',
                    icon: 'success'
                })
                await this.showNoticePopup()
                return false
            }


            xwy_api.alert(res && res['info'] || '密码错误', {
                success: () => this.$refs.input_password.open()
            })
        },

        passwordInputClose() {
            this.copy(this.id, true)

            if (getCurrentPages().length > 1) return uni.navigateBack()

            // 纯净版并且没有上一页面，重新弹出输入密码窗口
            if (app.globalData['shop_info']?.['extend_set']?.['shield_other_active']?.active_id) {
                xwy_api.alert('请输入活动密码', {
                    success: () => this.$refs.input_password.open()
                })
                return false
            }

            this.$uni.reLaunch('/pages/index/index')
        },

        toSubmitVoice() {
            if (this.detail.conf.active.enter_types === 3 && this.checked === 0) {
                xwy_api.alert('需要管理员审核通过后才能开始录制哦~')
                return false
            }

            if (!activity_tool.actionCheck({
                is_joining: this.is_joining,
                checked: this.checked,
                begin_time: this.detail.begin_time,
                end_time: this.detail.end_time
            })) return false

            const {free_record_close, record_background_music, article_list} = this.detail.conf.active

            // 关闭了自由录制、关闭了背景音乐、没有配置朗读作品
            if (free_record_close && !record_background_music && !article_list?.length) {
                xwy_api.alert('活动没有设置作品并已关闭自由录制，无法进行录制。请联系活动管理员。')
                return false
            }

            let url = ''
            // 跳转到自由录制的场景： 开启了自由录制、关闭了背景音乐、没有配置朗读作品
            if (!free_record_close && !record_background_music && !article_list?.length) {
                url = './recorder?id=' + this.id
            }

            // 跳转到朗读作品的场景： 配置了朗读作品
            if (article_list && article_list.length) {
                url = './article_list?id=' + this.id
            }

            // 跳转到选择背景音乐的场景： 没有配置朗读作品、开启了背景音乐
            if (!article_list?.length && record_background_music) {
                url = '../admin/background-music/list?id=' + this.id
            }

            uni.navigateTo({
                url,
                events: {
                    submitSuccess: () => {
                        this.load_page = 1
                        this.is_last_page = false
                        this.getList()
                    }
                }
            })
        },

        async updateHeadimg() {
            uni.navigateTo({
                url: `/pages/other/headimg-list/headimg-list?active_id=${this.id}`,
                events: {
                    newImg: async obj => {
                        if (!obj?.src) return
                        this.$uni.showLoading('修改中...')
                        if (!obj.temp) return this.updateAttendDetail(obj.src)
                        const data = {
                            temp_data: {
                                path: obj.src
                            },
                            is_temp: 5
                        }
                        if (obj.size) data.temp_data.size = obj.size
                        const headimg = await xwy_api.uploadOneImage(data)
                        this.updateAttendDetail(headimg)
                    }
                }
            })
        },


        joinActivity() {
            if (this.loading) return
            this.join_popup_show = true
        },


        updateAttendDetailShow() {
            this.update_attend_details = true
            this.join_popup_show = true
        },

        mustValueChange(e, index) {
            this.must_submit[index].value = this.must_submit[index].options[e.detail.value].text
            this.$forceUpdate()
        },


        cancelJoin() {
            this.join_popup_show = false
            this.update_attend_details = false
        },

        joinAjax() {
            if (this.update_attend_details) {
                this.updateAttendDetail()
                return false
            }

            this.joining()
        },


        getMustSubmitData() {
            const must_submit = JSON.parse(JSON.stringify(this.must_submit))
            for (let i = 0; i < must_submit.length; i++) {
                const v = must_submit[i]
                v.options && delete v.options
                if (v.rules === 1 && v.value === '') {
                    let tips = '输入'
                    if (v.types === 2) tips = '选择'
                    uni.showToast({
                        title: `请${tips}${v.title}`,
                        mask: true,
                        icon: v.title.length <= 4 ? 'error' : 'none'
                    })
                    return false
                }
                if (v.rules === 3 && v.value && v.value.length !== 11) {
                    this.$uni.showToast(`请输入正确的${v.title}`)
                }
            }
            console.log(must_submit)
            let must_submit_str = JSON.stringify(must_submit)
            must_submit_str = must_submit_str.replace(/·/g, '-')
            return base64['encode'](must_submit_str)
        },


        updateAttendDetail(headimg) {
            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id
            }

            if (headimg) data.headimg = headimg

            const must_submit = this.getMustSubmitData()
            if (must_submit === false) return false
            data.must_submit = must_submit

            this.$uni.showLoading('修改中...')

            xwy_api.ajax({
                url: 'front.flat.sport_step.user/update_attend_details',
                data,
                success: res => {
                    if (res?.status !== 1) return this.$uni.showModal(res?.info || '修改失败')

                    this.$uni.showToast('修改成功', 'success')

                    this.cancelJoin()
                    this.getDetail()
                }
            })
        },

        joining() {
            if (this.detail.conf.active.enter_types === 4) {
                if (!this.join_password) {
                    uni.showToast({
                        title: '请输入报名密码',
                        icon: 'none'
                    })
                    return false
                }
                if (this.join_password !== this.detail.conf.active.join_password) {
                    uni.showToast({
                        title: '报名密码不正确',
                        icon: 'none'
                    })
                    return false
                }
            }

            const data = {
                active_id: this.id,
                access_token: app.globalData['access_token']
            }


            if (this.must_submit && this.must_submit.length) {
                const must_submit = this.getMustSubmitData()
                if (must_submit === false) return false
                data.must_submit = must_submit
            }

            data.nickname = this.username

            this.loading = true
            this.$uni.showLoading('报名中...')


            xwy_api.ajax({
                url: 'front.flat.sport_step.user/submit_attend_active',
                data,
                success: res => {
                    this.loading = false
                    uni.hideLoading()
                    console.log('报名活动', res)
                    if (!res.status) {
                        uni.showModal({
                            title: '报名失败',
                            content: res.info || '报名失败',
                            showCancel: false
                        })
                        return false
                    }

                    this.join_popup_show = false
                    uni.showToast({
                        title: res.info || '报名成功',
                        icon: 'success'
                    })

                    setTimeout(() => {
                        uni.showLoading({
                            mask: true
                        })
                        this.getDetail()
                    }, 1000)
                }
            })
        },


        showActiveSharePopup() {
            this.$refs.activeShare.open({
                page: 'pages/voice/user/detail',
                scene: 'id=' + this.detail.id,
                qrcode_logo: this.detail.rank_set?.closed_AD && this.detail.conf?.active?.qrcode_logo
            })
        },
        
        previewImage(urls, current = 0) {
            uni.previewImage({ urls, current })
        },


        copy(data, hide_toast = false) {
            uni.setClipboardData({
                data,
                success: () => hide_toast ? uni.hideToast() : this.$uni.showToast('复制成功', 'none', 500)
            })
        },

        uniPopupClose(ref) {
            this.popup_open = false
            this.$refs[ref].close()
        },

        uniPopupOpen(ref) {
            this.popup_open = true
            this.$refs[ref].open()
        }

    }
}
</script>

<style scoped lang="scss">

.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 80px;
}

.bdb-10 {
    border-bottom: 10px solid #f8f8f8;
}

.icon-list {
    /* position: sticky; */
    /* top: 0; */
    /* z-index: 9; */
    border-top: 1px solid #eee;
}

.icon-item {
    padding: 10px 0;
    width: calc(100% / 4);
    /* border-top: 1px solid #eee; */
    box-sizing: border-box;
}

.to-submit-voice {
    position: fixed;
    bottom: 100px;
    right: 20px;
    z-index: 9;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    opacity: .8;
}

.headimg {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}


.join-btn {
    width: 250px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 20px;
}

.join-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, .7);
    z-index: 9999;
}

.join-popup-c {
    border-radius: 10px;
    overflow: hidden;
}


.join-input {
    margin: 10px;
    padding: 10px;
    border: 1px solid #bbb;
    border-radius: 5px;
    width: 70vw;
    max-width: 400px;
}

.join-popup-btns {
    border-top: 1px solid #eee;
}

.join-popup-btn-cancel, .join-popup-btn-confirm {
    width: 50%;
    box-sizing: border-box;
    line-height: 44px;
}

.join-popup-btn-cancel {
    border-right: 1px solid #eee;
}




.uni-popup-info {
    position: relative;
    width: 280px;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    z-index: 999;
    left: 50%;
    bottom: -5px;
    margin-left: -12px;
}

.detail-popup {
    width: 95vw;
    max-width: 480px;
    padding-bottom: 15px;
}

.audio-list-new {
    padding: 5px;
}
$pic-1-size: calc((100vw - 30px) / 2);
.audio-item-new {
    width: $pic-1-size;
    margin: 5px;
    border-radius: 10px;
    overflow: hidden;
    .pic-1 {
        width: $pic-1-size;
        height: $pic-1-size;
        display: block;
    }
    .audio-new-bottom-bar {
        line-height: 36px;
    }
    .play-new {
        width: 36px;
        height: 36px;
        box-sizing: border-box;
        border-radius: 50%;
        background-color: #fff;
        box-shadow: 0 0 5px 2px #eee;
    }
}


.voice-list-list {
    padding: 10px;
}

.voice-list-item {
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.voice-list-headimg {
    width: 40px;
    min-width: 40px;
    height: 40px;
    display: block;
    border-radius: 50%;
}

.voice-list-audio-item {
    background-color: #f8f8f8;
    border-radius: 10px;
    padding: 10px;
    margin-bottom: 5px;
}

.voice-list-audio-item .voice-list-play-btn {
    width: 36px;
    min-width: 36px;
    height: 36px;
    box-sizing: border-box;
    border-radius: 50%;
    background-color: #fff;
    box-shadow: 0 0 5px 2px #eee;
}

.voice-list-article-data {
    width: calc(100vw - 156px);
    box-sizing: border-box;
}

.img-list {
    position: relative;
    left: -5px;
    padding-top: 5px;
}
.img-item {
    display: block;
    margin-left: 5px;
    margin-bottom: 5px;
    border-radius: 5px;
}
.img-list-1 .img-item {
    height: 120px;
    width: auto;
    max-width: calc(100vw - 130px);
}

.img-list-2 .img-item {
    width: 100px;
    height: 120px;
}
.img-list-more .img-item {
    width: calc((100vw - 150px) / 3);
    height: calc((100vw - 150px) / 3);
}

.voice-list-like_popup {
    width: 300px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.voice-list-popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}

.category-list {
    padding-bottom: 10px;
}
.category-item {
    padding: 0 5px;
    height: 34px;
    line-height: 34px;
    box-sizing: border-box;
    color: #666;
}
.category-active {
    color: #1e88e5;
    border-bottom: 2px solid #1e88e5;
}

.search-view {
    padding: 10px;
}

.search-input-view {
    width: calc(100% - 90px);
    border-radius: 22px;
    height: 44px;
}

.search-input-view input {
    width: 100%;
    padding: 10px;
}

.search-btn {
    width: 80px;
    line-height: 44px;
    border-radius: 22px;
}
</style>
