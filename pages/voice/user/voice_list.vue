<template>
    <view class="page bg-background">
        <view v-if="is_admin" class="top-view">
            <view class="search bg-white flex-kai p10">
                <view class="input-view">
                    <view class="search-icon left-icon flex-all-center">
                        <uni-icons type="search" size="20" color="#bbbec4"/>
                    </view>
                    <input
                        class="input bg-background"
                        type="text"
                        confirm-type="search"
                        v-model="search_keyword"
                        @confirm="search"
                        placeholder="输入用户名字搜索"
                        placeholder-style="color:#bbbec4"
                    />
                    <view class="search-icon right-icon flex-all-center" @click="clearSearchKeyword">
                        <uni-icons v-if="search_keyword" type="close" size="20" color="#bbbec4"/>
                    </view>
                </view>
                <view class="search-go color-info pl10" @click="search">搜索</view>
            </view>


            <view class="type-list flex-row bg-white">
                <view
                    class="type-item color-content"
                    v-for="(item, index) in checked_list"
                    :key="index"
                    :class="{'active-type': item.checked === checked}"
                    :style="{width: 'calc(100% / ' + checked_list.length + ')'}"
                    @click="checkedChange(item.checked)"
                >
                    {{ item.title }}
                </view>
            </view>
        </view>

        <view v-if="is_admin" style="height: 100px;"></view>

        <view v-if="list_total" class="text-center p10 color-sub font14">共{{ list_total }}个作品</view>

        <xwy-ad :activity_id="id" :ad_type="3"></xwy-ad>

        <view class="list">
            <view v-for="(item, index) in list" :key="item.id">
                <view v-if="index === 0" style="padding-bottom: 10px;">
                    <xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>
                </view>
                <view class="item bg-white" :id="item._id">
                    <view class="flex-row">
                        <view v-if="is_admin || is_myself" class="flex-all-center"
                              @click="checkChange(index)">
                            <radio :checked="item.check" style="transform:scale(0.8)"/>
                        </view>
                        <view class="pr10">
                            <image
                                class="headimg"
                                :src="item['user_attend_details'] && item['user_attend_details'].headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"
                                mode="aspectFill"
                            />
                        </view>
                        <view style="width: 100%;">
                            <view
                                v-if="item['user_attend_details'] && item['user_attend_details'].must_submit && item['user_attend_details'].must_submit.length"
                                class="color-title"
                            >{{ item['user_attend_details'].must_submit[0].value }}
                            </view>
                            <view v-if="item.memo" class="color-content pt5">{{ item.memo }}</view>


                            <view
                                v-if="item.img_video_count"
                                class="img-list flex-row flex-wrap pt5"
                                :class="{
										'img-list-1': item.img_video_count === 1,
										'img-list-2': item.img_video_count === 2,
										'img-list-more': item.img_video_count >= 3
									}"
                            >

                                <image
                                    class="img-item"
                                    :lazy-load="true"
                                    v-for="(img_item, img_index) in item.conf_json.pic_list"
                                    :key="img_index"
                                    :src="img_item"
                                    :mode="item.img_video_count === 1 ? 'heightFix' : 'aspectFill'"
                                    @click.stop="previewImage(img_item, item.conf_json.pic_list)"
                                />

                                <video
                                    class="img-item"
                                    :id="'video_' + item.id + '_' + video_index"
                                    v-for="(video_item, video_index) in item.conf_json.video_list"
                                    :key="video_index"
                                    :src="video_item"
                                    @play="videoPlay('video_' + item.id + '_' + video_index)"
                                ></video>
                            </view>

                            <view class="pt5">
                                <view
                                    class="audio-item flex-row"
                                    v-for="(audio, idx) in item.conf_json.audio_list"
                                    :key="idx"
                                >
                                    <view class="flex-all-center">
                                        <view
                                            hover-class="navigator-hover"
                                            class="play-btn flex-all-center"
                                            @click="audioPlay(audio.player_id, audio.src)"
                                        >
                                            <text
                                                class="iconfont color-primary font18"
                                                :class="{
														'icon-play-filled': player_id !== audio.player_id,
														'icon-pause-filled': player_id === audio.player_id
													}"
                                            ></text>
                                        </view>
                                    </view>
                                    <view class="pl10 article-data" v-if="audio.article">
                                        <view class="color-title ellipsis">{{ audio.article.title }}
                                        </view>
                                        <view class="color-content ellipsis font14">
                                            {{ audio.article.content_preview }}
                                        </view>
                                    </view>
                                </view>
                            </view>

                            <view v-if="!item.checked" class="pt5">
                                <uni-tag text="待审核" type="default" size="mini" :inverted="true"/>
                            </view>

                            <view class="flex-kai">
                                <view class="color-sub font14 pt5">{{ item.create_time }}</view>
                                <view class="flex-row">
                                    <view v-if="item['play_num']" class=" pr10 color-sub font12"
                                          style="padding-top: 3px;">
                                        播放{{ item['play_num'] }}次
                                    </view>

                                    <!--从排行榜进来查看某个人的不显示点赞量，不然可能和排行榜的点赞数对不上
                                    因为排行榜的点赞总数是算上已经删除的作品的点赞数-->
                                    <view v-if="!userid" @click="like(index)">
                                        <text
                                            class="iconfont icon-love color-red"
                                            style="position: relative; top: 1px; left: 2px;"
                                        ></text>
                                        <text class="pl5 color-sub font14">{{ item.vote_num || 0 }}</text>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>


                    <view v-if="is_admin || is_myself" class="item-bar flex-kai">
                        <view></view>
                        <view class="flex-row">
                            <view
                                v-if="is_admin"
                                class="font14 color-content pl10 pr10"
                                hover-class="navigator-hover"
                                @click="changeChecked(item)"
                            >
                                改为{{ item.checked ? '待' : '已' }}审核
                            </view>
                            <view
                                class="font14 color-content pl10"
                                hover-class="navigator-hover"
                                @click="delItem(item.id)"
                            >删除
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="index !== 0 && ((index + 1) % 10 === 0)" style="padding-bottom: 20px;">
                    <xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>
                </view>
            </view>
        </view>


        <uni-load-more v-if="loading && load_page !== 1" status="loading"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && list.length > 5"
            status="noMore"
            :contentText="{contentnomore: '我是有底线的'}"
        ></uni-load-more>
        <uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>

        <view v-if="!loading && !list.length" class="text-center" style="padding-top: 15vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub font14">暂无作品</view>
        </view>


        <template v-if="(is_admin || is_myself) && list.length">
            <view style="height: 70px;"></view>
            <view class="bottom-bar flex-kai bg-white">
                <view class="flex-all-center" @click="checkAllChange">

                    <radio :checked="check_all" style="transform:scale(0.8)"/>
                    <text class="font14 color-sub" style="position: relative; left: -4px; top: 1px;">全选
                    </text>
                </view>
                <view class="flex-row">
                    <view
                        v-if="is_admin"
                        class="all-change color-content font14"
                        hover-class="navigator-hover"
                        @click="changeCheckeds"
                    >修改状态
                    </view>
                    <view style="width: 10px;"></view>
                    <view
                        class="all-change color-content font14"
                        hover-class="navigator-hover"
                        @click="dels"
                    >删除
                    </view>
                </view>
            </view>
        </template>

        <uni-popup ref="like_popup" type="center">
            <view class="like_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('like_popup')">
                    <uni-icons type="close" size="24" color="#b2b3b7"/>
                </view>
                <view class="bg-primary color-white" style="padding: 30px 20px 20px;">

                    <icon :type="like_status === 'success' ? 'success' : 'clear'" size="80"
                          color="#ffffff"></icon>
                    <view class="font18">投票{{ like_status === 'success' ? '成功' : '失败' }}</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ like_info }}
                    </view>
                </view>
                <xwy-ad v-if="popup_show" :activity_id="id" :ad_type="3"></xwy-ad>
            </view>
            <view class="pt5">
                <xwy-ad v-if="popup_show" :activity_id="id" :ad_type="66"></xwy-ad>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import aApi from "@/pages/voice/api"

import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'

let innerAudioContext = null


export default {
    data() {
        return {
            id: '',
            is_admin: 0,
            is_myself: 0,
            userid: null,
            loading: true,
            checked: 1,
            list: [],
            list_total: 0,
            load_page: 1,
            is_last_page: false,
            checked_list: [
                {checked: 0, title: '所有记录'},
                {checked: 2, title: '待审核'},
                {checked: 1, title: '已审核'},
            ],
            check_all: false,
            player_id: '',
            popup_show: false,
            like_status: '',
            like_info: '',
            search_keyword: ''
        }
    },

    onLoad(e) {

        this.$uni.showLoading('加载中...')

        this.id = e.id

        if (e.is_admin) {
            this.$uni.setNavigationBarTitle('作品审核')
            this.is_admin = 1
            this.checked = 2
        } else if (e.is_myself) {
            this.$uni.setNavigationBarTitle('我的作品')
            this.is_myself = 1
            this.checked = 0
        } else if (e.userid) {
            this.$uni.setNavigationBarTitle(e.nickname ? `${e.nickname}的作品` : '作品')
            this.userid = Number(e.userid)
            this.checked = 1
        } else {
            this.$uni.setNavigationBarTitle('作品动态')
            this.checked = 1
        }

        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getList()
        })

    },

    onReachBottom() {
        !this.loading && !this.is_last_page && this.getList()
    },

    onUnload() {
        this.next_play_timeout && clearTimeout(this.next_play_timeout)
        this.player_id && innerAudioContext && innerAudioContext.stop()
        innerAudioContext && innerAudioContext.destroy()
    },

    onHide() {
        this.next_play_timeout && clearTimeout(this.next_play_timeout)
        if (this.player_id && innerAudioContext) {
            this.player_id = ''
            innerAudioContext.stop()
        }
    },

    methods: {
        audioContextOn() {
            if (!innerAudioContext) innerAudioContext = uni.createInnerAudioContext()

            innerAudioContext.onPlay(() => {
                console.log('onPlay')
                this.addPlayNum(this.player_id)
            })
            innerAudioContext.onPause(() => {
                console.log('onPause')
            })
            innerAudioContext.onStop(() => {
                console.log('onStop')
            })
            innerAudioContext.onEnded(() => {
                console.log('onEnded')
                const player_id = this.player_id
                this.player_id = ''
                this.playNext(player_id)
            })
            innerAudioContext.onError(err => {
                console.log('onError', err)
                this.player_id = ''
                const err_opt = {
                    '10001': '系统错误',
                    '10002': '网络错误',
                    '10003': '文件错误',
                    '10004': '格式错误',
                    '-1': '未知错误'
                }
                xwy_api.alert(`播放失败 (${err.errCode}: ${err_opt[err.errCode]})`)
            })
        },

        playNext(player_id) {
            let audio_list = []
            this.list.forEach(v => {
                if (v.conf_json?.audio_list?.length) {
                    audio_list = [...audio_list, ...v.conf_json.audio_list]
                }
            })
            const index = audio_list.findIndex(v => v.player_id === player_id)
            if (index === -1 || index === audio_list.length - 1) return false
            const nextAudio = audio_list[index + 1]

            this.next_play_timeout = setTimeout(() => {
                console.log('clearTineout')
                uni.pageScrollTo({
                    selector: '#' + nextAudio.parent_id,
                    fail: err => {
                        console.log('滚动失败', err)
                    }
                })
                this.audioPlay(nextAudio.player_id, nextAudio.src)
            }, 1000)
        },


        audioPlay(player_id, src) {
            this.next_play_timeout && clearTimeout(this.next_play_timeout)
            if (!this.player_id) {
                // 没有播放音频，播放点前点击音频
                this.player_id = player_id
                innerAudioContext.src = src
                return innerAudioContext.play()
            }

            if (player_id === this.player_id) {
                // 播放的是点击的音频，暂停音频播放
                this.player_id = ''
                return innerAudioContext.pause()
            }

            // 切换音频播放
            innerAudioContext.stop()
            this.player_id = player_id
            innerAudioContext.src = src
            innerAudioContext.play()
        },

        async addPlayNum(player_id) {
            if (!player_id) return
            const id = player_id.match(/\d+/)[0]
            if (!id) return
            const res = await aApi.addPlayNum(this.id, id)
            if (res?.status) {
                const index = this.list.findIndex(v => v.id === Number(id))
                if (index !== -1) this.list[index]['play_num']++
            }
        },

        videoPlay(id) {
            this.videoContext = this.videoContext || null
            if (this.previous_video_id) {
                if (id === this.previous_video_id) return false
                this.videoContext = uni.createVideoContext(this.previous_video_id)
                this.videoContext.pause()
            }
            this.previous_video_id = id
        },

        previewImage(src, list) {
            list = list || [src]
            uni.previewImage({
                urls: list,
                current: src
            })
        },


        checkedChange(checked) {
            if (checked === this.checked) return false
            this.checked = checked
            this.search()
        },

        search() {
            this.load_page = 1
            this.getList()
        },

        clearSearchKeyword() {
            if (!this.search_keyword) return
            this.search_keyword = ''
            this.search()
        },

        async getList() {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
            }
            const data = {
                active_id: this.id,
                page: this.load_page,
                perpage: 10
            }

            if (this.is_myself) data.my_self = 1
            if (this.is_admin) data.is_admin = 1
            if (this.checked) data.checked = this.checked
            if (this.search_keyword) data.name = this.search_keyword
            if (this.userid) data.userid = this.userid

            this.loading = true

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.audio_online.user_submit/audio_list',
                data
            })

            this.loading = false
            uni.hideLoading()

            this.load_page++

            if (res?.data?.audio_list) {
                const data_ = res.data.audio_list
                this.list_total = data_.total
                this.is_last_page = data_.is_lastpage
                const list = data_.data || []

                if (list.length) {
                    if (this.is_admin) this.check_all = false
                    list.forEach(v => {
                        if (this.is_admin) v.check = false
                        const id = `id_${v.id}`
                        v._id = id
                        if (v.conf_json?.audio_list?.length) {
                            v.conf_json.audio_list.forEach((audio, i) => {
                                audio.player_id = `id_${v.id}_i_${i}`
                                audio.parent_id = id
                            })
                        }
                        v.img_video_count = 0
                        if (v.conf_json) {
                            if (v.conf_json.pic_list && v.conf_json.pic_list.length) {
                                v.img_video_count += v.conf_json.pic_list.length
                            }
                            if (v.conf_json.video_list && v.conf_json.video_list.length) {
                                v.img_video_count += v.conf_json.video_list.length
                            }
                        }
                    })

                    if (data.page === 1) this.audioContextOn()
                }
                this.list = [...this.list, ...list]
            } else {
                this.is_last_page = true
            }

        },

        checkChange(index) {
            const item = this.list[index]
            item.check = !item.check
        },

        checkAllChange() {
            const check_all = !this.check_all
            this.check_all = check_all
            this.list.forEach(v => {
                v.check = check_all
            })
        },

        async changeChecked(item) {
            const res = await this.$uni.showModal(`确定将该记录的状态改为${item.checked ? '待' : '已'}审核?`, {showCancel: true})
            if (res.confirm) await this.changeCheckedAjax([item.id], item.checked ? '2' : '1')
        },

        getCheckList() {
            const ids = []
            this.list.forEach(v => {
                if (v.check) ids.push(v.id)
            })
            if (!ids.length) return this.$uni.showToast('未勾选记录')

            return ids
        },

        changeCheckeds() {
            const ids = this.getCheckList()
            if (!ids) return false

            const checked_list = [
                {checked: 1, title: '已审核'},
                {checked: 2, title: '待审核'}
            ]
            const itemList = []
            checked_list.forEach(v => {
                itemList.push(v.title)
            })

            uni.showActionSheet({
                title: '将记录状态修改为',
                itemList,
                success: res => {
                    const index = res.tapIndex
                    uni.showModal({
                        title: '提示',
                        content: `确定将选中的${ids.length}条记录状态修改为${checked_list[index].title}?`,
                        success: res => {
                            if (res.confirm) this.changeCheckedAjax([ids], checked_list[index].checked)
                        }
                    })
                }
            })
        },


        async delItem(id) {
            const res = await this.$uni.showModal('确定删除该记录?', {showCancel: true})
            if (res.confirm) await this.changeCheckedAjax([id], 'del')
        },

        async dels() {
            const ids = this.getCheckList()
            if (!ids) return

            const res = await this.$uni.showModal(`确定删除选中的${ids.length}条记录?`, {showCancel: true})
            if (res.confirm) await this.changeCheckedAjax([ids], 'del')
        },

        async changeCheckedAjax(id_list, act_types) {
            const act_text = act_types === 'del' ? '删除' : '修改'
            this.$uni.showLoading(act_text + '中...')

            const data = {
                active_id: this.id,
                ids: id_list.join(',')
            }
            if (act_types === 'del') {
                data.act_types = 1
            } else {
                data.checked = act_types
            }
            if (this.is_myself) data.is_user = 1

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.audio_online.admin_manage/admin_checked_user_audio',
                data
            })

            uni.hideLoading()

            if (!res || !res.status) return this.$uni.showModal(res?.info || `${act_text}失败`)

            const info = res.info || `${act_text}成功`
            this.$uni.showToast(info, info.length <= 7 ? 'error' : 'none')

            this.search()
        },

        async like(index) {
            const item = this.list[index]

            if (!item.checked) return this.$uni.showModal('记录未审核通过，无法投票。')

            this.$uni.showLoading('投票中...')

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/friend_agree_vote_record',
                data: {
                    id: item.id,
                    active_id: this.id,
                    act_types: 1
                }
            })

            uni.hideLoading()

            let like_status = 'success',
                like_info = '给好友投票成功！'

            if (res && res.status) {
                item.vote_num = item.vote_num || 0
                item.vote_num++
            } else {
                like_status = 'error'
                like_info = '给好友投票失败！'
            }
            if (res && res['info']) {
                like_info = res['info'].replace(/投票/g, '点赞').replace(/票/g, '次')
            }
            this.like_status = like_status
            this.like_info = like_info

            this.uniPopupOpen('like_popup')

        },

        uniPopupOpen(ref) {
            this.$refs[ref].open()
            this.popup_show = true
        },

        uniPopupClose(ref) {
            this.$refs[ref].close()
            this.popup_show = false
        },

    }
}
</script>

<style>
.page {
    min-height: 100vh;
    box-sizing: border-box;
}

.top-view {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 999;
}

.search, .search .input {
    height: 40px;
    line-height: 40px;
}

.input-view {
    position: relative;
    width: 100%;
}

.search-icon {
    position: absolute;
    width: 30px;
    height: 40px;
    top: 0;
}

.left-icon {
    left: 0;
}

.right-icon {
    right: 0;
}

.search .input {
    /* width: calc(100% - 80px); */
    padding: 0 30px;
    border-radius: 20px;
    box-sizing: border-box;
}

.search-go {
    width: 60px;
    min-width: 60px;
    text-align: right;
}


/* #ifdef H5 */
.search-go {
    padding-right: 10px;
    box-sizing: border-box;
}

@media screen and (min-width: 500px) {
    .top-view {
        width: 500px;
        left: calc(((100vw - 500px) / 2) - 10px);
    }

    .search .input {
        width: 440px;
    }
}

/* #endif */


.type-item {
    height: 40px;
    line-height: 40px;
    text-align: center;
    box-sizing: border-box;
}

.active-type {
    color: #007AFF;
    border-bottom: 2px solid #007AFF;
}


.list {
    padding: 0 10px 10px;
}

.item {
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.headimg {
    width: 40px;
    min-width: 40px;
    height: 40px;
    display: block;
    border-radius: 50%;
}

.audio-item {
    background-color: #f8f8f8;
    border-radius: 10px;
    padding: 10px;
    margin-bottom: 5px;
}

.audio-item .play-btn {
    width: 36px;
    min-width: 36px;
    height: 36px;
    box-sizing: border-box;
    border-radius: 50%;
    background-color: #fff;
    box-shadow: 0 0 5px 2px #eee;
}

.article-data {
    width: calc(100vw - 176px);
    box-sizing: border-box;
}

.img-list {
    position: relative;
    left: -5px;
    padding-top: 5px;
}

.img-item {
    display: block;
    margin-left: 5px;
    margin-bottom: 5px;
    border-radius: 5px;
}

.img-list-1 .img-item {
    height: 120px;
    width: auto;
    max-width: calc(100vw - 130px);
}

.img-list-1 video {
    width: 180px !important;
}

.img-list-2 .img-item {
    width: 100px;
    height: 120px;
}

.img-list-more .img-item {
    width: calc((100vw - 150px) / 3);
    height: calc((100vw - 150px) / 3);
}

.item-bar {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #eee;
}

.bottom-bar {
    position: fixed;
    left: 0;
    width: 100%;
    z-index: 9;
    border-top: 1px solid #eee;
    bottom: 0;
    padding: 5px 10px 15px;
    height: 61px;
    line-height: 40px;
    box-sizing: border-box;
}

.all-change {
    line-height: 38px;
    border-radius: 20px;
    padding: 0 15px;
    border: 1px solid #eee;
    text-align: center;
}

.like_popup {
    width: 300px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}
</style>
