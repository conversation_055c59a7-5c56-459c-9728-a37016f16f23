<template>
    <view class="page bg-background">

        <view class="flex-all-center" style="padding: 20px 0;">
            <view v-if="!free_record_close" class="diy bg-primary color-white text-center" @click="toRecorderCheck('')">
                <uni-icons type="mic" size="16" color="#ffffff"/>
                <text class="color-white">自由录制</text>
            </view>
        </view>

        <view>
            <view class="item bg-white" v-for="(item, index) in article_list" :key="index">
                <view class="color-title p10">{{ item.title }}</view>
                <view class="flex-kai p10">
                    <view></view>
                    <view hover-class="navigator-hover" class="to-recorder bg-primary" @click="toRecorderCheck(item.id)">
                        <uni-icons type="mic" size="14" color="#ffffff"/>
                        <text class="color-white font14">录制</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'


export default {
    data() {
        return {
            article_list: [],
            free_record_close: 0
        }
    },

    onLoad(e) {
        this.active_id = e.id
        uni.showLoading({
            title: '加载中...',
        })


        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }


            this.getDetail()
        })
    },

    methods: {
        async getDetail() {

            let activity_detail = app.globalData['activity_detail']

            if (!activity_detail) {
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        access_token: app.globalData['access_token'],
                        active_id: this.active_id
                    }
                })

                activity_detail = res['data']['active_details']

            }
            this.activity_details = activity_detail

            const {article_list, free_record_close, record_background_music} = activity_detail.conf.active
            this.article_list = article_list
            this.free_record_close = free_record_close || 0
            this.record_background_music = record_background_music || 0


            uni.hideLoading()
        },

        async toRecorderCheck(article_id) {
            if (this.record_background_music) {
                this.toRecorder(article_id)
                return
            }

            if (this.activity_details.rank_set?.category_list) {
                let classify_list = this.classify_list
                if (!classify_list) {
                    uni.showLoading({ mask: true })
                    const classify_res = await xwy_api.getCategoryList({
                        active_id: this.active_id,
                        types: 16,
                        page: 1,
                        perpage: 6
                    })
                    uni.hideLoading()
                    classify_list = classify_res?.data?.category_list.data || []
                    this.classify_list = classify_list
                }

                if (!classify_list.length) {
                    uni.showToast({
                        title: '活动未添加作品分类，请联系管理员添加！',
                        icon: 'none'
                    })
                    return
                }

                const itemList = classify_list.map(item => item.name)
                uni.showActionSheet({
                    itemList,
                    success: res => this.toRecorder(article_id, classify_list[res.tapIndex].category_id)
                })

                return
            }

            this.toRecorder(article_id)
        },

        toRecorder(article_id = '', category_id = null) {
            let path = this.record_background_music ? '../admin/background-music/list' : './recorder'
            let url = `${path}?id=${this.active_id}`
            if (article_id) url += `&article_id=${article_id}`
            if (category_id) url += `&category_id=${category_id}`

            uni.navigateTo({
                url,
                events: {
                    submitSuccess: () => {
                        const eventChannel = this.getOpenerEventChannel()
                        eventChannel?.emit && eventChannel.emit('submitSuccess')
                    }
                }
            })
        }
    }
}
</script>

<style>
.page {
    min-height: 100vh;
}

.diy {
    width: 150px;
    line-height: 40px;
    border-radius: 20px;
}

.item {
    margin: 0 10px 20px 10px;
    border-radius: 10px;
}

.to-recorder {
    height: 32px;
    line-height: 32px;
    width: 80px;
    border-radius: 16px;
    text-align: center;
}

</style>
