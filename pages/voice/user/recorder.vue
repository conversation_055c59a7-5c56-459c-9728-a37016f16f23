<template>
    <view class="page bg-background">
        <view v-if="activity_detail.conf.active.recorder_bg_img" class="bg-img">
            <image :src="activity_detail.conf.active.recorder_bg_img" mode="aspectFill"/>
        </view>

        <view
            v-if="article.title || article.content || music_name || music_lyric"
            class="article pt10"
            :class="{'transparent-bgc color-white': activity_detail.conf.active.recorder_bg_img}"
        >
            <view
                v-if="article.title || music_name"
                class="font24 text-center p10"
                :class="{'color-title': !activity_detail.conf.active.recorder_bg_img}"
            >
                <template v-if="music_name && music_lyric">{{ music_name }}</template>
                <template v-if="article.title && (!music_lyric || !music_name)">{{ article.title }}</template>
            </view>
            <view
                v-if="article.content || music_lyric"
                class="p10"
                :class="{'color-content': !activity_detail.conf.active.recorder_bg_img}"
            >
                <u-parse v-if="article.content && !music_lyric" :content="article.content"/>
                <view v-if="music_lyric" class="text-center">
                    <text>{{ music_lyric }}</text>
                </view>
            </view>
        </view>

        <view class="bottom-bar bg-white">
            <view v-if="have_background_music" class="flex-row">
                <view style="min-width: 16px; position: relative; left: 10px;">
                    <uni-icons type="sound" color="#999999"/>
                </view>
                <view style="width: 100%; position: relative; top: -8px;">
                    <slider :max="10" :value="volume" :block-size="16" @changing="changeVolume($event)"></slider>
                </view>
            </view>

            <view v-if="activity_detail.conf" class="color-sub font12 text-center pb10">
                请在{{ activity_detail.conf.active.voice_duration.max }}秒内完成录制
                ({{ activity_detail.conf.active.voice_duration.max - recorder_time }}s)
            </view>

            <uni-transition
                :mode-class="['fade', 'slide-left']"
                :show="recorder_status === 'waiting'"
            >
                <view class="flex-all-center" v-if="recorder_status === 'waiting'">
                    <view @click="startRecord">
                        <view class="big-btn flex-all-center">
                            <uni-icons type="mic" size="30" color="#2d8cf0"/>
                        </view>
                        <view class="font14 color-content text-center pt10">开始录制</view>
                    </view>
                </view>
            </uni-transition>

            <uni-transition :mode-class="['fade', 'slide-right']" :show="recorder_status !== 'waiting'">

                <view class="btns" v-if="recorder_status !== 'waiting'">

                    <view @click="rerecording">
                        <view class="flex-all-center small-btn">
                            <uni-icons type="refreshempty" size="24" color="#2d8cf0"/>
                        </view>
                        <view class="font14 color-content text-center pt10">重录</view>
                    </view>

                    <view v-if="recorder_status === 'pause'" @click="resumeRecord">
                        <view class="big-btn flex-all-center">
                            <uni-icons type="mic" size="30" color="#2d8cf0"/>
                        </view>
                        <view class="font14 color-content text-center pt10">继续录制</view>
                    </view>
                    <view v-if="recorder_status === 'start'">
                        <view class="big-btn flex-all-center" @click="pauseRecord">
                            <text class="iconfont icon-pause-filled color-primary font28"></text>
                        </view>
                        <view class="font14 color-content text-center pt10">{{ recorder_time_text }}</view>
                    </view>
                    <view @click="endRecord">
                        <view class="flex-all-center small-btn">
                            <uni-icons type="checkmarkempty" size="24" color="#2d8cf0"/>
                        </view>
                        <view class="font14 color-content text-center pt10">完成</view>
                    </view>

                </view>
            </uni-transition>
        </view>

        
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import base64 from '@/utils/base64.js'
import utils from '@/utils/utils.js'


export default {
    data() {
        return {
            activity_detail: {},
            recorder_status: 'waiting',  // waiting: 等待中   start: 录音中 pause: 暂停
            recorder_time: 0,
            recorder_time_text: '00:00',
            article: {
                title: '',
                content: ''
            },
            have_background_music: false,
            music_lyric: '',
            music_name: '',
            volume: 5
        }
    },
    watch: {
        recorder_time(val) {
            let mm = 0,
                ss = val

            if (val >= 60) {
                mm = Math.ceil(val / 60)
                ss = val % 60
            }

            if (mm < 10) mm = '0' + mm
            if (ss < 10) ss = '0' + ss

            this.recorder_time_text = mm + ':' + ss
        }
    },

    onLoad(e) {
        this.active_id = e.id

        // 这个活动默认背景音乐音量为20%
        if (e.id === '3f62ac69e000224f9dc81bfb80d19122') this.volume = 2

        if (e.article_id) this.article_id = e.article_id
        if (e.music_id) this.music_id = e.music_id
        if (e.category_id) this.category_id = e.category_id
        uni.showLoading({
            title: '加载中...',
        })


        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.init()

        })

        this.recorderManagerOn()
    },

    onUnload() {
        this.audioContextDestroy()
        if (this.recorder_status === 'waiting') return false
        this.not_submit = true
        this.recorderManager.stop()
    },

    beforeDestroy() {
        this.audioContextDestroy()
    },


    onShow() {
        if (this.have_background_music && !this.innerAudioContext) {
            this.createInnerAudioContext()
        }
    },

    onHide() {
        this.audioContextDestroy()
    },

    methods: {
        async init() {
            await this.getDetail()
            if (await this.getUserCoiceCount() === 'error') return
            await this.getArticleDetail()
            await this.getBackgroundMusic()
            uni.hideLoading()
        },

        async getDetail() {

            let activity_detail = app.globalData['activity_detail']

            if (!activity_detail) {
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        access_token: app.globalData['access_token'],
                        active_id: this.active_id
                    }
                })

                activity_detail = res['data']['active_details']

            }

            this.activity_detail = activity_detail
        },

        async getUserCoiceCount() {
            const people_voice_count = this.activity_detail?.conf?.active?.people_voice_count || 0
            const people_voice_count_oneday = this.activity_detail?.conf?.active?.people_voice_count_oneday || 0
            if (Number(people_voice_count) === 0 && Number(people_voice_count_oneday) === 0) {
                return false
            }

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.audio_online.user_submit/submit_count',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.active_id
                }
            })

            const user_voice_count = res?.['data']?.submit_count?.all || 0
            const user_voice_count_today = res?.['data']?.submit_count?.today || 0

            let tips = ''
            if (people_voice_count !== 0 && user_voice_count >= people_voice_count) {
                tips = `本活动每人最多只能提交${people_voice_count}份作品，你已提交${user_voice_count}份作品，无法继续提交。`
            }
            if (people_voice_count_oneday !== 0 && user_voice_count_today >= people_voice_count_oneday) {
                tips = `本活动每人每天最多只能提交${people_voice_count_oneday}份作品，你今天已提交${user_voice_count_today}份作品，无法继续提交。`
            }

            if (tips) {
                uni.hideLoading()
                uni.showModal({
                    title: '提示',
                    content: tips,
                    showCancel: false,
                    success: () => uni.navigateBack()
                })
                return 'error'
            }
        },

        async getArticleDetail() {
            if (!this.article_id) {
                uni.hideLoading()
                return false
            }

            const res = await xwy_api.request({
                url: "front.news/news_details",
                data: {
                    access_token: app.globalData['access_token'],
                    news_id: this.article_id
                }
            })

            uni.hideLoading()

            const detail = res?.['data']?.['news_details']
            if (!detail) return false
            if (detail.title) this.article.title = detail.title
            if (detail.content) this.initContent(detail.content)
        },

        initContent(content) {
            if (utils.isBase64(content)) content = base64['decode'](content)
            content = content.replace(/<br \/>/g, '');
            content = content.replace(/\r/g, '');
            content = content.replace(/\n/g, '');
            content = content.replace(/\t/g, '');
            // 这个会导致段落间的间距加大，一个没用的p标签
            content = content.replace(/<p class="MsoNormal"><a href="#point_\d+"><\/a> <\/p>/g, '');
            /*//  空的p标签是正常的换行
            content = content.replace(/<p>\s+<\/p>/g, '');// 空的p标签会导致换行*/
            content = content.replace(/<\/p>\s+<p>/g, '</p><p>');// p标签之间的空格会导致换行

            content = content.replace(/<img /g, `<img style="width: 100%; height: auto; display: block;" `);   //限制最大宽度，取消上下图片间距

            this.article.content = content

            this.getContentPreview(content)


        },


        // 提取文章内容预览，用于在列表上显示部分文章内容
        getContentPreview(content) {
            let value = content.replace(/\s*/g, "")
            value = value.replace(/<[^>]+>/g, ""); //去掉所有的html标记
            value = value.replace(/↵/g, "");     //去掉所有的↵符号
            value = value.replace(/[\r\n]/g, "") //去掉回车换行
            value = value.replace(/&nbsp;/g, "") //去掉空格
            this.content_preview = value.substr(0, 50)
        },


        async getBackgroundMusic() {
            if (!this.music_id) return

            const res = await xwy_api.request({
                url: "front.flat.sport_step.audio_online.admin_manage/music_details",
                data: {
                    access_token: app.globalData['access_token'],
                    music_id: this.music_id
                }
            })

            const music = res?.['data']?.['music_details']
            if (!music) return

            this.have_background_music = true
            this.background_music_src = music.mp3_url
            if (music.id) this.music_id = music.id
            if (music.name) this.music_name = music.name
            if (music.conf_json?.lyric) this.music_lyric = music.conf_json.lyric
            this.createInnerAudioContext()
        },

        createInnerAudioContext() {
            this.innerAudioContext = null
            let innerAudioContext = uni.createInnerAudioContext()
            innerAudioContext.autoplay = false
            innerAudioContext.volume = this.volume / 10
            innerAudioContext.src = this.background_music_src

            innerAudioContext.onError(err => {
                console.log('播放错误', err)
                this.audioStop()
                xwy_api.alert('播放错误')
            })

            innerAudioContext.onPlay(() => {
                console.log('onPlay')
            })
            innerAudioContext.onCanplay(() => {
                console.log('onCanplay')
            })
            innerAudioContext.onEnded(() => {
                console.log('onEnded')
                this.play_id = null
            })
            this.innerAudioContext = innerAudioContext
        },

        backgroundMusicPlay() {
            if (!this.innerAudioContext) return
            this.innerAudioContext.play()
        },

        backgroundMusicPause() {
            if (!this.innerAudioContext) return
            this.innerAudioContext.pause()
        },

        backgroundMusicStop() {
            if (!this.innerAudioContext) return
            this.innerAudioContext.stop()
        },

        audioContextDestroy() {
            if (!this.innerAudioContext) return
            this.innerAudioContext.destroy()
            this.innerAudioContext = null
        },

        setBackgroundMusicVolume(volume) {
            this.innerAudioContext.volume = volume
        },

        changeVolume(e) {
            this.volume = e.detail.value
            this.setBackgroundMusicVolume(this.volume / 10)
        },

        rerecording() {
            this.pauseRecord()
            uni.showModal({
                title: '提示',
                content: '确定重新录制？',
                success: res => {
                    if (res.confirm) {
                        this.not_submit = true
                        this.recorderManager.stop()
                    }
                }
            })
        },

        recorderTimeIntervalStart() {
            this.recorder_time_interval = null
            this.recorder_time_interval = setInterval(() => {
                this.recorder_time++
                if (this.recorder_time >= this.activity_detail.conf.active.voice_duration.max) {
                    this.timeOut()
                }
            }, 1000)
        },

        clearRecorderTimeInterval() {
            if (!this.recorder_time_interval) return
            clearInterval(this.recorder_time_interval)
        },

        timeOut() {
            this.clearRecorderTimeInterval()
            this.pauseRecord()
            this.recorder_status = 'pause'

            uni.showModal({
                title: '提示',
                content: '时间到，是否录制完成？录制完成请点击“录制完成”后提交，未录制完成可点“重新录制”进行重新录制',
                cancelText: '重新录制',
                confirmText: '录制完成',
                success: res => {
                    this.recorder_status = 'waiting'
                    res.confirm && this.endRecord()
                    if (res.cancel) {
                        this.not_submit = true
                        this.recorderManager.stop()
                    }
                }
            })
        },

        recorderManagerOn() {
            const recorderManager = uni.getRecorderManager()
            recorderManager.onStart(res => {
                uni.hideLoading()
                console.log('recorder Start', res)
                this.recorder_status = 'start'
                this.recorderTimeIntervalStart()
                this.backgroundMusicPlay()
                // #ifdef MP-WEIXIN
                wx.enableAlertBeforeUnload({
                    message: '退出后已录制的语音不会保存哦，是否确定退出？'
                })
                // #endif
            })
            recorderManager['onResume'](() => {
                console.log('recorder Resume')
                this.backgroundMusicPlay()
            })
            recorderManager.onPause(res => {
                console.log('recorder Pause', res)
                this.clearRecorderTimeInterval()
                this.recorder_status = 'pause'
                this.backgroundMusicPause()
            })
            recorderManager.onStop(res => {
                console.log('recorder Stop', res)
                this.clearRecorderTimeInterval()
                this.recorder_time = 0
                this.recorder_status = 'waiting'
                this.backgroundMusicStop()

                if (this.not_submit) {
                    this.not_submit = false
                    return false
                }

                this.audioContextDestroy()
                this.toSubmit(res)
            })
            recorderManager.onError(err => {
                uni.hideLoading()
                console.log('recorder Error', err)
                if (err && err.errMsg && err.errMsg === "operateRecorder:fail auth deny") {
                    this.getRecorderAuth()
                }
            })
            this.recorderManager = recorderManager
        },

        getRecorderAuth() {
            uni.authorize({
                scope: 'scope.record',
                success: () => {
                    this.startRecord()
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '需要授权录音功能才能录制哦',
                        confirmText: '去授权',
                        success: res => {
                            res.confirm && uni.openSetting()
                        }
                    })
                }
            })
        },

        startRecord() {
            /*uni.showLoading({
                title: '准备录制...',
                mask: true
            })*/
            this.recorderManager.start({
                // 因为在倒计时的定时器里做了到时间结束录音，所以这里不做限制
                // 指定录音的时长，单位 ms ，如果传入了合法的 duration ，在到达指定的 duration 后会自动停止录音，最大值 600000（10 分钟）,默认值 60000（1 分钟）
                duration: this.activity_detail.conf.active.voice_duration.max * 1000,
                format: 'mp3' // 音频格式，有效值 aac/mp3/wav/PCM。App默认值为mp3，小程序默认值aac
            })
        },
        resumeRecord() {
            console.log('继续录音')
            this.recorderManager.resume()
            this.recorder_status = 'start'
            this.recorderTimeIntervalStart()
        },
        pauseRecord() {
            console.log('暂停录音')
            this.recorderManager.pause()
        },
        endRecord() {
            if (this.recorder_status === 'start') this.pauseRecord()
            if (this.recorder_time < this.activity_detail.conf.active.voice_duration.min) {
                uni.showToast({
                    title: '录制时长不能少于' + this.activity_detail.conf.active.voice_duration.min + '秒哦~',
                    icon: 'none'
                })
                return false
            }
            console.log('录音结束')
            this.recorderManager.stop()
        },


        toSubmit(res) {
            app.globalData.voice_data = res
            if (this.content_preview) app.globalData.content_preview = this.content_preview
            let url = './submit_voice?id=' + this.active_id
            if (this.article_id) url += `&article_id=${this.article_id}`
            if (this.article.title) url += `&article_title=${this.article.title}`
            if (this.music_id) url += `&music_id=${this.music_id}`
            if (this.music_name) url += `&music_name=${this.music_name}`
            if (this.category_id) url += `&category_id=${this.category_id}`
            uni.navigateTo({
                url,
                events: {
                    submitSuccess: () => {
                        const eventChannel = this.getOpenerEventChannel()
                        eventChannel?.emit && eventChannel.emit('submitSuccess')
                    }
                }
            })
        }
    }
}
</script>

<style>
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 160px;
}

.bg-img, .article {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: calc(100vh - 140px);
}

.bg-img {
    z-index: 2;
}

.bg-img image {
    width: 100vw;
    height: calc(100vh - 140px);
    display: block;
}

.transparent-bgc {
    background-color: rgba(0, 0, 0, .6);
}

.article {
    z-index: 3;
    box-sizing: border-box;
    overflow: auto;
    padding-bottom: 30px;
}

.bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 99;
    width: 100vw;
    border-radius: 20px 20px 0 0;
    padding: 20px;
    /* box-shadow: 0 0 10px 3px #eee; */
    box-sizing: border-box;
}

.btns {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.big-btn, .small-btn {
    border-radius: 50%;
    box-shadow: 0 0 5px 2px #eee;
}

.big-btn {
    width: 60px;
    height: 60px;
}

.small-btn {
    margin-top: 5px;
    width: 50px;
    height: 50px;
}

</style>
