<template>
    <view class="page bg-background">
        <view v-if="fromTimeline" style="position: fixed; z-index: 9999;">

            <image :src="fromTimelineImageSrc" mode="aspectFill" style="width: 100vw; height: 100vh; display: block"/>
        </view>

        <view v-if="recorder_bg_img" class="bg-img">

            <image :src="recorder_bg_img" mode="aspectFill"/>
        </view>

        <view
            v-if="article.title || article.content || music_name || music_lyric"
            class="article pt10"
            :class="{'transparent-bgc color-white': recorder_bg_img}"
        >
            <view
                v-if="article.title || music_name"
                class="font24 text-center p10"
                :class="{'color-title': !recorder_bg_img}"
            >
                <template v-if="music_name && music_lyric">{{ music_name }}</template>
                <template v-if="article.title && (!music_lyric || !music_name)">{{ article.title }}</template>
            </view>
            <view
                v-if="article.content || music_lyric"
                class="p10"
                :class="{'color-content': !recorder_bg_img}"
            >
                <u-parse v-if="article.content && !music_lyric" :content="article.content"/>
                <view v-if="music_lyric" class="text-center">
                    <text>{{ music_lyric }}</text>
                </view>
            </view>
        </view>

        <view class="bottom-bar bg-white">
            <view style="position: relative;">
                <view>

                    <!--:max="duration_all.value" => :max="duration_all.value || 1"  解决在真机上，如果max和value都是0，value圆点会在中间问题-->
                    <!--disabled="!duration_all.value"  没有获取到总时长时，不允许被拖动-->

                    <slider
                        :max="duration_all.value || 1"
                        :value="duration_current.value"
                        :block-size="12"
                        :disabled="!duration_all.value"
                        activeColor="#2d8cf0"
                        block-color="#2d8cf0"
                        @change="seekTo($event)"
                        @changing="durationChanging()"
                    ></slider>
                </view>
                <view class="flex-kai">
                    <view class="color-sub font12 flex-all-center">
                        {{ duration_current.value_text }}
                    </view>
                    <view class="color-sub font12 flex-all-center">
                        {{ duration_all.value_text }}
                    </view>
                </view>


                <button class="share-btn flex-all-center" open-type="share" hover-class="navigator-hover">
                    <text class="iconfont icon-share color-disabled font24"></text>
                </button>
            </view>
            <view class="btns">
                <view>
                    <view class="flex-all-center small-btn" hover-class="navigator-hover" @click="back">
                        <uni-icons type="undo" size="24" color="#2d8cf0"/>
                    </view>
                    <view class="font14 color-content text-center pt10">{{ back_text }}</view>
                </view>

                <view v-if="play_status === 'pause'">
                    <view
                        class="big-btn flex-all-center"
                        hover-class="navigator-hover"
                        @click="audioPlay"
                    >
                        <text class="iconfont icon-play-filled color-primary font28"></text>
                    </view>
                    <view class="font14 color-content text-center pt10">播放</view>
                </view>
                <view v-if="play_status === 'play'">
                    <view
                        class="big-btn flex-all-center"
                        hover-class="navigator-hover"
                        @click="audioPause"
                    >
                        <text class="iconfont icon-pause-filled color-primary font28"></text>
                    </view>
                    <view class="font14 color-content text-center pt10">暂停</view>
                </view>
                <view>
                    <view
                        class="flex-all-center small-btn"
                        hover-class="navigator-hover"
                        @click="like"
                    >
                        <uni-icons type="heart" size="24" color="#2d8cf0"/>
                    </view>
                    <view class="font14 color-content text-center pt10">点赞</view>
                </view>

            </view>
        </view>

        <uni-popup ref="like_popup" type="center">
            <view class="like_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('like_popup')">
                    <uni-icons type="close" size="24" color="#b2b3b7"/>
                </view>
                <view class="bg-primary color-white" style="padding: 30px 20px 20px;">
                    <icon
                        :type="like_status === 'success' ? 'success' : 'clear'"
                        size="80"
                        color="#ffffff"
                    ></icon>
                    <view class="font18">点赞{{ like_status === 'success' ? '成功' : '失败' }}</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ like_info }}
                    </view>
                </view>
                <xwy-ad v-if="popup_show" :activity_id="activity_id" :ad_type="3"></xwy-ad>
            </view>
            <view class="pt5">
                <xwy-ad v-if="popup_show" :activity_id="activity_id" :ad_type="66"></xwy-ad>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import xwy_api from "@/utils/api/xwy_api";

const app = getApp()
import login from '@/utils/api/login.js'
import base64 from '@/utils/base64.js'

let innerAudioContext = null


export default {
    data() {
        return {
            imageWidth: uni.getSystemInfoSync().windowWidth - 20,
            play_status: 'pause',
            recorder_bg_img: '',
            article: {
                title: '',
                content: ''
            },
            duration_all: {
                value: 0,
                value_text: '00:00'
            },
            duration_current: {
                value: 0,
                value_text: '00:00'
            },
            like_status: '',
            like_info: '',
            popup_show: false,
            activity_id: '',
            back_text: getCurrentPages().length === 1 ? '返回活动' : '返回',
            music_lyric: '',
            music_name: '',
            fromTimeline: false,
            fromTimelineImageSrc: ''
        }
    },

    onLoad(e) {
        if (uni.getLaunchOptionsSync().scene === 1154) {
            this.fromTimeline = true
            this.getSimpleDetail(e.active_id)
            return false
        }

        this.id = e.id
        uni.showLoading({
            title: '加载中...',
            mask: true
        })

        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getDetail()
        })

    },


    onUnload() {
        if (!innerAudioContext) return false
        this.play_status === 'play' && innerAudioContext.stop()
        innerAudioContext.destroy()
    },

    beforeDestroy() {
        this.play_status === 'play' && innerAudioContext.stop()
        innerAudioContext.destroy()
    },

    onHide() {
        this.play_status === 'play' && innerAudioContext && innerAudioContext.pause()
    },

    methods: {
        async getSimpleDetail(id) {
            this.$uni.showLoading('数据加载中...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details_simple',
                data: {
                    active_id: id
                }
            })

            uni.hideLoading()

            if (!res || !res['data'] || !res['data']['active_details']) {
                uni.hideLoading()
                return false
            }

            const detail = res['data']['active_details']

            this.fromTimelineImageSrc = detail.conf?.active?.screen_pic ||
                detail.conf?.active?.recorder_bg_img ||
                detail.logo ||
                'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/shop/3/35211/2023/06/21/103306649261622b4e6_2245.jpg'


            if (detail.name) {
                uni.setNavigationBarTitle({
                    title: detail.name
                })
            }
        },

        async getDetail() {
            const voice_data = await this.xwy_api.request({
                url: 'front.flat.sport_step.audio_online.user_submit/audio_details',
                data: {
                    access_token: app.globalData['access_token'],
                    id: this.id
                }
            })

            if (!voice_data?.data?.details) {
                uni.hideLoading()
                this.xwy_api.alert(voice_data && voice_data.info || '获取失败', {
                    success: () => uni.navigateBack()
                })
                return false
            }

            this.voice_data = voice_data.data.details

            let activity_detail = app.globalData['activity_detail']
            if (!activity_detail) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        access_token: app.globalData['access_token'],
                        active_id: this.voice_data.active_id
                    }
                })

                if (!res?.data?.['active_details']) {
                    uni.hideLoading()
                    this.xwy_api.alert(res && res.info || '活动获取失败', {
                        success: () => uni.navigateBack()
                    })
                    return false
                }

                activity_detail = res.data['active_details']
            }

            this.activity_detail = activity_detail
            this.activity_id = this.activity_detail.active_id

            this.setNavigationBarTitle()
            if (this.activity_detail?.conf?.active?.recorder_bg_img) {
                this.recorder_bg_img = this.activity_detail.conf.active.recorder_bg_img
            }
        },

        setNavigationBarTitle() {
            let NavigationBarTitle = ''
            const audio_list = this.voice_data?.conf_json?.audio_list
            if (audio_list?.length) {
                const audio_data = audio_list[0]
                if (audio_data.src) this.audioContextOn()
                const article = audio_data.article
                NavigationBarTitle = article?.title || '自由录制'
                this.getArticleDetail(article?.id || false)

                const music = audio_data.music
                if (music?.name) {
                    NavigationBarTitle = music.name
                    this.music_name = music.name
                }
                if (music?.id) this.getMusicDetails(music.id)
            }
            const must_submit = this.voice_data?.['user_attend_details']?.must_submit
            if (must_submit?.length && must_submit[0].value) {
                NavigationBarTitle += ` - ${must_submit[0].value}`
            }
            this.NavigationBarTitle = NavigationBarTitle
            uni.setNavigationBarTitle({
                title: NavigationBarTitle
            })
        },

        async getArticleDetail(id) {
            if (!id) uni.hideLoading()

            const res = await this.xwy_api.request({
                url: "front.news/news_details",
                data: {
                    access_token: app.globalData['access_token'],
                    news_id: id
                }
            })

            uni.hideLoading()

            if (!res || !res.data || !res.data['news_details']) return false

            const detail = res.data['news_details']
            if (detail.title) this.article.title = detail.title

            if (detail.content) this.initContent(detail.content)
        },

        initContent(content) {
            if (this._utils.isBase64(content)) content = base64['decode'](content)
            content = content.replace(/<br \/>/g, '');
            content = content.replace(/\r/g, '');
            content = content.replace(/\n/g, '');
            content = content.replace(/\t/g, '');
            // 这个会导致段落间的间距加大，一个没用的p标签
            content = content.replace(/<p class="MsoNormal"><a href="#point_\d+"><\/a> <\/p>/g, '');
            /*//  空的p标签是正常的换行
            content = content.replace(/<p>\s+<\/p>/g, '');// 空的p标签会导致换行*/
            content = content.replace(/<\/p>\s+<p>/g, '</p><p>');// p标签之间的空格会导致换行

            content = content.replace(/<img /g, `<img style="width: 100%; height: auto; display: block;" `);   //限制最大宽度，取消上下图片间距

            this.article.content = content

        },


        async getMusicDetails(id) {
            const res = await xwy_api.request({
                url: "front.flat.sport_step.audio_online.admin_manage/music_details",
                data: {
                    access_token: app.globalData['access_token'],
                    music_id: id
                }
            })

            const music = res?.['data']?.['music_details']
            if (!music) return

            if (music.conf_json?.lyric) this.music_lyric = music.conf_json.lyric
        },

        audioContextOn() {
            innerAudioContext = uni.createInnerAudioContext()
            innerAudioContext.src = this.voice_data.conf_json.audio_list[0].src

            innerAudioContext.onCanplay(() => {
                console.log('onCanplay --- duration：', innerAudioContext.duration)
            })
            innerAudioContext.onPlay(() => {
                console.log('onPlay')
                this.play_status = 'play'
                uni.hideLoading()
                this.addPlayNum()
            })
            innerAudioContext.onPause(() => {
                console.log('onPause')
                this.play_status = 'pause'
            })
            innerAudioContext.onStop(() => {
                console.log('onStop')
                this.play_status = 'pause'
            })
            innerAudioContext.onEnded(() => {
                console.log('onEnded')
                this.play_status = 'pause'
            })
            innerAudioContext.onError(err => {
                uni.hideLoading()
                console.log('onError', err)
                this.play_status = 'pause'
                const err_opt = {
                    '10001': '系统错误',
                    '10002': '网络错误',
                    '10003': '文件错误',
                    '10004': '格式错误',
                    '-1': '未知错误'
                }
                this.xwy_api.alert(`播放失败 (${err.errCode}: ${err_opt[err.errCode]})`)
            })

            innerAudioContext.onTimeUpdate(() => {
                const duration = innerAudioContext.duration
                const currentTime = innerAudioContext.currentTime
                if (duration && duration !== Infinity) {
                    this.duration_all = {
                        value: Math.floor(duration),
                        value_text: this.initDuration(duration)
                    }
                }
                if (currentTime && currentTime !== Infinity) {
                    this.duration_current.value_text = this.initDuration(currentTime)
                    // 拖动的时候不改变进度条的值，不然进度条会一跳一跳
                    if (this.duration_changing) return false
                    this.duration_current.value = Math.floor(currentTime)
                }
            })
        },

        initDuration(duration) {
            duration = Math.floor(duration)
            let mm = Math.floor(duration / 60)
            if (mm < 10) mm = '0' + mm
            let ss = duration % 60
            if (ss < 10) ss = '0' + ss
            return mm + ':' + ss
        },

        audioPlay() {
            uni.showLoading({
                title: '准备播放...',
                mask: true
            })

            innerAudioContext.play()
        },

        audioPause() {
            innerAudioContext.pause()
        },

        seekTo(e) {
            this.duration_changing = false
            const {value} = e.detail
            this.duration_current = {
                value,
                value_text: this.initDuration(value)
            }
            innerAudioContext.seek(value)
        },

        durationChanging() {
            this.duration_changing = true
        },

        addPlayNum() {
            this.getOpenerEventChannel?.()?.emit?.('upDatePlayNum')
        },

        back() {
            if (getCurrentPages().length === 1) {
                uni.redirectTo({
                    url: '/pages/voice/user/detail?id=' + this.activity_id
                })
                return false
            }
            uni.navigateBack()
        },


        async like() {
            if (!this.voice_data?.checked) {
                this.xwy_api.alert('记录未审核通过，无法点赞。')
                return false
            }

            this.$uni.showLoading('点赞中...')

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/friend_agree_vote_record',
                data: {
                    access_token: app.globalData['access_token'],
                    id: this.voice_data.id,
                    active_id: this.activity_id,
                    act_types: 1
                }
            })

            uni.hideLoading()

            let like_status = 'success',
                like_info = '给好友点赞成功！'

            if (res && res.status) {

            } else {
                like_status = 'error'
                like_info = '给好友点赞失败！'
            }
            if (res && res['info']) {
                like_info = res['info'].replace(/投票/g, '点赞').replace(/票/g, '次')
            }
            this.like_status = like_status
            this.like_info = like_info

            this.uniPopupOpen('like_popup')

        },

        uniPopupOpen(ref) {
            this.$refs[ref].open()
            this.popup_show = true
        },

        uniPopupClose(ref) {
            this.$refs[ref].close()
            this.popup_show = false
        },

    },

    onShareAppMessage() {
        return {
            title: this.NavigationBarTitle || '分享一个作品',
            imageUrl: this.recorder_bg_img || '',
            path: '/pages/voice/user/voice_detail?id=' + this.id + '&active_id=' + this.activity_id
        }
    },

    onShareTimeline() {
        return {
            title: this.NavigationBarTitle || '分享一个作品',
            imageUrl: this.recorder_bg_img || ''
        }
    },
}
</script>

<style>
.bg-img, .article {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: calc(100vh - 160px);
}

.bg-img {
    z-index: 2;
}

.bg-img image {
    width: 100vw;
    height: calc(100vh - 160px);
    display: block;
}

.transparent-bgc {
    background-color: rgba(0, 0, 0, .6);
}

.article {
    z-index: 3;
    box-sizing: border-box;
    overflow: auto;
    padding-bottom: 30px;
}

.bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 99;
    width: 100vw;
    border-radius: 20px 20px 0 0;
    padding: 20px;
    /* box-shadow: 0 0 10px 3px #eee; */
    box-sizing: border-box;
}

.share-btn {
    position: absolute;
    top: -30px;
    right: -18px;
    background-color: rgba(0, 0, 0, 0);
    padding: 0;
    width: 40px;
    height: 40px;
}

.share-btn::after {
    content: "";
    border: none;
}

.btns {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.big-btn, .small-btn {
    border-radius: 50%;
    box-shadow: 0 0 5px 2px #eee;
}

.big-btn {
    width: 60px;
    height: 60px;
}

.small-btn {
    margin-top: 5px;
    width: 50px;
    height: 50px;
}

.like_popup {
    width: 300px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}
</style>
