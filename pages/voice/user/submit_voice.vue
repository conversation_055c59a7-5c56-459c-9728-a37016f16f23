<template>
    <view v-if="activity_detail">
        <view class="flex-all-center">
            <view v-if="is_ios" class="audio-view flex-row">
                <view class="left">
                    <image class="poster" :src="headimg"/>
                    <view class="controls flex-all-center" @click="play">
                        <view class="flex-all-center control">
                            <text v-if="!in_play" class="iconfont icon-play-filled color-white font14"></text>
                            <text v-if="in_play" class="iconfont icon-pause-filled color-white font14"></text>
                        </view>
                    </view>
                </view>
                <view class="right">
                    <view class="time font12 color-sub">{{ duration }}</view>
                    <view class="color-title font14">{{ music_name || article_title || '自由录制' }}</view>
                    <view class="color-content font12">{{ nickname || '' }}</view>
                </view>
            </view>
            

            <audio
                v-else
                :src="audio_src"
                :name="music_name || article_title || '自由录制'"
                :author="nickname || ' '"
                :poster="headimg || ''"
                controls="true"
                @error="audioError"
            ></audio>
        </view>


        <view class="p10" v-if="activity_detail.conf.active.memo_required !== 0">
            <uni-easyinput type="textarea" v-model="memo" :maxlength="800"
                           :placeholder="memo_placeholder"></uni-easyinput>
        </view>

        <view v-if="activity_detail.conf.active.pic_required !== 0" class="p10">
            <view>
                <text class="color-content">照片上传</text>
                <text v-if="activity_detail.conf.active.pic_required === 1" class="color-red">*</text>
            </view>
            <view style="padding-top: 5px;">
                <view class="flex-row flex-wrap">
                    <view class="top-rank-banner-item" v-for="(item, index) in pic_list" :key="index">
                        <image :src="item" mode="aspectFill" @click="previewImage(pic_list, item)"/>
                        <view class="del-image-item" @click.stop="pic_list.splice(index, 1)">
                            <uni-icons type="closeempty" color="#e20f04"/>
                        </view>
                    </view>
                    <view v-if="pic_list.length < pic_list_max_count" class="add-image text-center"
                          @click="changeImage()">
                        <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                    </view>
                </view>
            </view>
        </view>

        <view class="save-button flex-all-center">
            <view class="clock-in flex-all-center bg-primary color-white" hover-class="navigator-hover"
                  @click="save">提交
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()

let innerAudioContext = null

export default {
    data() {
        return {
            activity_detail: null,
            memo: '',
            audio_src: app.globalData['voice_data'].tempFilePath,
            article_title: '',
            nickname: app.globalData['userinfo']?.nickname || '',
            headimg: app.globalData['userinfo']?.headimg || '',
            is_ios: (function () {
                const {model, system} = uni.getSystemInfoSync()
                return model.startsWith('iPhone') || system.startsWith('iOS')
            }()),
            in_play: false,
            duration: '00:00',
            pic_list: [],
            pic_list_max_count: 1,
            music_name: '',
            memo_placeholder: ''
        }
    },

    onHide() {
        innerAudioContext && innerAudioContext.stop()
    },
    onUnload() {
        innerAudioContext && innerAudioContext.stop()
    },

    onLoad(e) {
        // #ifdef MP-WEIXIN
        wx.enableAlertBeforeUnload({
            message: '退出后已录制的语音不会保存哦，是否确定退出？'
        })
        // #endif

        this.active_id = e.id
        if (e.article_id) this.article_id = e.article_id
        if (e.article_title) this.article_title = e.article_title.replace(/·/g, '-')
        if (e.music_id) this.music_id = e.music_id
        if (e.music_name) this.music_name = e.music_name
        if (e.category_id) this.category_id = e.category_id
        // 这个活动的自由录制标题需要改为'自由歌曲录制'
        const other_id = 'adf261e6c0ee94145df58d7ab41bdb52'
        if (e.id === other_id && !e.article_title) this.article_title = '自由歌曲录制'
        
        this.$uni.showLoading('加载中...')

        this.memo = this.getMemoStorage()

        this.$login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }


            this.getDetail()
        })

        this.getBackgroundAudioManager()
    },

    methods: {
        async getDetail() {

            let activity_detail = app.globalData['activity_detail']

            if (!activity_detail || activity_detail.active_id !== this.active_id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {active_id: this.active_id}
                })

                activity_detail = res['data']['active_details']
            }

            this.activity_detail = activity_detail
            uni.hideLoading()

            this.setMemoPlaceholder()
        },

        setMemoPlaceholder() {
            const {memo_required} = this.activity_detail.conf.active
            if (memo_required === 0) return
            const required_tips = memo_required === 2 ? '非必填' : '必填'
            const memo_placeholder_list = {
                '35211': required_tips + ', 请填写表演人员姓名'
            }
            const default_placeholder = '请填写备注, 800字内, ' + required_tips
            this.memo_placeholder = memo_placeholder_list[app.globalData['who']] || default_placeholder
        },

        getBackgroundAudioManager() {
            if (!this.is_ios) return false

            innerAudioContext = uni.getBackgroundAudioManager()
            innerAudioContext.onPlay(() => {
                console.log('onPlay')
                this.in_play = true
            })
            innerAudioContext.onPause(() => {
                console.log('onPause')
                this.in_play = false
            })
            innerAudioContext.onStop(() => {
                console.log('onStop')
                this.in_play = false
            })
            innerAudioContext.onEnded(() => {
                console.log('onEnded')
                this.in_play = false

            })
            innerAudioContext.onError(err => {
                console.log('onError', err)
                this.player_id = ''
                const err_opt = {
                    '10001': '系统错误',
                    '10002': '网络错误',
                    '10003': '文件错误',
                    '10004': '格式错误',
                    '-1': '未知错误'
                }
                this.$uni.showModal(`播放失败 (${err.errCode}: ${err_opt[err.errCode]})`)
            })


            innerAudioContext.onTimeUpdate(() => {
                console.log('onTimeUpdate', innerAudioContext.duration)
                if (innerAudioContext.duration && innerAudioContext.duration !== Infinity) {
                    this.setAudioDuration(innerAudioContext.duration)
                }
            })
        },

        setAudioDuration(duration) {
            duration = Math.floor(duration)
            let mm = Math.floor(duration / 60)
            if (mm < 10) mm = '0' + mm
            let ss = duration % 60
            if (ss < 10) ss = '0' + ss
            this.duration = mm + ':' + ss
        },

        play() {
            if (this.in_play) {
                innerAudioContext.pause()
                this.in_play = false
                return false
            }

            this.in_play = true
            innerAudioContext.title = this.article_title || '自由录制'
            innerAudioContext.singer = this.nickname || '微信用户'
            innerAudioContext.coverImgUrl = this.headimg || 'https://bjetxgzv.cdn.bspapp.com/VKCEYUGU-uni-app-doc/7fbf26a0-4f4a-11eb-b680-7980c8a877b8.png'
            innerAudioContext.src = this.audio_src
        },


        previewImage(urls, current = urls[0]) {
            uni.previewImage({
                urls,
                current
            })
        },

        changeImage() {
            uni.navigateTo({
                url: '/pages/other/image_upload_or_select?active_id=' + this.active_id,
                events: {
                    newImg: src => {
                        this.pic_list.push(src)
                    }
                }
            })
        },

        async getConfJson() {
            const audio_src = await this.xwy_api.uploadAudio(app.globalData['voice_data'], this.active_id)
            
            const voice_item = {src: audio_src}
            
            if (this.article_id) {
                const article = {id: this.article_id}
                if (this.article_title) article.title = this.article_title
                const _preview = app.globalData['content_preview']
                if (_preview) article.content_preview = _preview.replace(/·/g, '-')
                
                voice_item.article = article
            }
            
            if (this.music_id) {
                voice_item.music = {id: this.music_id}
                if (this.music_name) voice_item.music.name = this.music_name
            }

            const conf_json = {
                audio_list: [voice_item]
            }
            
            if (this.pic_list.length) conf_json.pic_list = this.pic_list

            return this._utils.base64['encode'](JSON.stringify(conf_json))
        },

        async getSignDay() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: {active_id: this.active_id}
            })

            const {sign_day = 0, sign_latest_time = 0} = res?.data?.user_details || {}

            // 没有最后提交时间，说明是第一次提交，直接返回1
            if (!sign_latest_time) return 1

            // sign_latest_time是秒的时间戳，需要判断sign_latest_time是不是今天
            const today = new Date(this._utils.getDay()).getTime() / 1000
            // 是不是今天
            const isToday = sign_latest_time >= today

            // 最后提交时间是今天，说明今天已经提交过，直接返回sign_day
            // 最后提交时间不是今天，说明今天没有提交过返回sign_day + 1
            return isToday ? sign_day : sign_day + 1
        },

        async getMemo() {
            const {memo_required, memo_show_day} = this.activity_detail.conf.active
            if (!memo_required) return ''

            if (!memo_show_day) return this.memo

            const {show = 0, before = '', after = ''} = memo_show_day
            if (!show) return this.memo

            const day = await this.getSignDay()

            return `${before}${day}${after} ${this.memo}`
        },

        async save() {
            if (this.in_submit) return false

            const {memo_required, pic_required} = this.activity_detail.conf.active
            if (memo_required === 1 && !this.memo) return this.$uni.showToast('请填写备注')
            
            if (pic_required && pic_required === 1 && !this.pic_list.length) {
                return this.$uni.showToast('请上传照片')
            }

            this.in_submit = true
            this.$uni.showLoading('正在提交...')

            const data = {active_id: this.active_id}

            const memo = await this.getMemo()
            if (memo) data.memo = memo

            if (this.category_id) data.category_id = this.category_id
            
            const conf_json = await this.getConfJson()
            if (conf_json) data.conf_json = conf_json

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.audio_online.user_submit/submit_myself_audio',
                data
            })

            uni.hideLoading()
            this.in_submit = false
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '提交失败')

            this.saveMemoStorage()

            this.getOpenerEventChannel?.()?.emit?.('submitSuccess')

            const info = res['info'] || '提交成功'
            this.$uni.showToast(info, info.length <= 7 ? 'success' : 'none')

            // #ifdef MP-WEIXIN
            wx.disableAlertBeforeUnload()
            // #endif

            app.globalData.voice_data = null
            app.globalData.content_preview = ''

            this.$uni.navigateBack(2, {delay: 1000})
        },

        saveMemoStorage() {
            if (!this.memo) return
            uni.setStorageSync(`voice-memo-${this.active_id}`, this.memo)
        },
        getMemoStorage() {
            return uni.getStorageSync(`voice-memo-${this.active_id}`) || ''
        },

        audioError(e) {
            if (!e?.detail?.errMsg) return this.$uni.showModal('音频无法播放')

            if (typeof e.detail.errMsg === "string") {
                if (e.detail.errMsg === 'MEDIA_ERR_SRC_NOT_SUPPORTED') {
                    return this.$uni.showModal(`音频无法播放，音频地址不支持。${e.detail.errMsg}`)
                }

                return this.$uni.showModal(`音频无法播放。${e.detail.errMsg}`)
            }

            this.$uni.showModal(JSON.stringify(e.detail))
        }
    }
}
</script>

<style>
.audio-view {
    background-color: #fcfcfc;
    border: 1px solid #e0e0e0;
}

.left {
    position: relative;
}

.left, .poster, .controls {
    width: 65px;
    min-width: 65px;
    height: 65px;
}

.poster {
    display: block;
    background-color: #e6e6e6;
}

.controls {
    position: absolute;
    left: 0;
    top: 0;
}

.control {
    width: 24px;
    height: 24px;
    border: 2px solid #fff;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, .3);
}

.right {
    width: 200px;
    padding: 15px 15px 0;
    position: relative;
}

.time {
    position: absolute;
    top: 5px;
    right: 15px;
}


.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
    line-height: calc((100vw - 50px) / 3);
    margin: 5px;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}


.top-rank-banner-item {
    padding: 5px;
    position: relative;
}

.top-rank-banner-item image {
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
}

.top-rank-banner-item .del-image-item {
    right: 8px;
}

.save-button {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100vw;
    box-sizing: border-box;
    padding: 10px 10px 15px;
    border-top: 1px solid #eee;
}

.clock-in {
    width: 200px;
    height: 40px;
    border-radius: 20px;
}
</style>
