<template>
    <view class="page">
        <view class="record-list">
            <view class="item clear clearfix" v-for="(item, index) in recordList" :key="item.id">
                <view v-if="bodyFatRecord && item.height">身高: {{ item.height }}cm</view>
                <view v-if="(weightRecord || bodyFatRecord) && item.weight">体重: {{ item.weight }}kg</view>
                <view v-if="waistRecord && item.waist">腰围: {{ item.waist }}cm</view>
                <view v-if="bodyFatRecord && item.birthday">生日: {{ item.birthday }}</view>
                <view class="color-sub font14 fr pb10">{{ item.submit_date || item.create_time }}</view>

                <view class="operation-bar clear clearfix">
                    <view class="fr flex-row">
                        <view class="operation-bar__item" @click="editRecord(item, index)">
                            <text class="iconfont icon-edit color-sub font18"></text>
                        </view>
                        <view class="operation-bar__item" @click="deleteRecord(item.id)">
                            <text class="iconfont icon-delete color-sub font18"></text>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <view v-if="recordLoading" class="text-center" style="padding-top: 10vh;">
            <load-ani />
        </view>

        <view v-if="!recordLoading && !recordList.length" class="text-center"
              style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无记录</view>
        </view>

        <view v-if="!recordLoading" class="add-button-container flex-all-center">
            <view class="add-button bg-light-primary color-white text-center" hover-class="navigator-hover"
                  @click="submitData">添加记录
            </view>
        </view>

        <body-data-submit ref="bodyDataSubmit" :active-id="active_id" :show-ad="showAD"
                          @success="bodyDataSubmitSuccess"/>
    </view>
</template>

<script>
const app = getApp()

import bodyDataSubmit from '../components/body-data-submit.vue'

export default {
    components: {
        bodyDataSubmit
    },
    data() {
        return {
            active_id: '',
            recordLoading: true,
            recordList: [],
            showAD: false,
            weightRecord: false,
            bodyFatRecord: false,
            waistRecord: false
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.userid = params.userid
        if (params.nickname) this.$uni.setNavigationBarTitle(`${params.nickname}的身体数据记录`)

        this.init()
    },

    methods: {
        async init() {
            await this.getActiveDetails()
            await this.getRecordList()
        },

        async getActiveDetails() {
            let details = app.globalData.activity_detail

            if (!details || details.active_id !== this.active_id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.active_id
                    }
                })

                details = res?.data?.active_details
            }

            if (details) {
                if (!details.rank_set?.closed_AD) this.showAD = true
                this.checkTaskSet(details)
            }
        },

        checkTaskSet(details) {
            const task_list = details.conf?.active?.job_set?.job_list

            task_list.forEach(task => {
                if (task.types === 27) this.weightRecord = true
                if (task.types === 28) this.bodyFatRecord = true
                if (task.types === 29) this.waistRecord = true
            })
        },

        async getRecordList() {
            this.recordLoading = true
            this.recordList = []

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.lose_weight.user/user_weight_height_list',
                data: {
                    active_id: this.active_id,
                    userid: this.userid,
                    page: 1,
                    perpage: 10
                }
            })

            this.recordLoading = false

            const list = res?.data?.list?.data || []
            this.recordList = list.map(item => ({
                id: item.id,
                height: this._utils.formatDecimal(item.height / 1000),
                weight: this._utils.formatDecimal(item.weight / 1000),
                waist: this._utils.formatDecimal(item.waist / 1000),
                birthday: item.birthday,
                create_time: item.create_time,
                submit_date: this._utils.unitTimeToDate(item.submit_date * 1000)
            }))
        },

        async submitData() {
            const today = this._utils.getDay(0, true, '-')
            if (this.recordList.some(item => item.submit_date === today)) {
                const {confirm} = await this.$uni.showModal('今天已记录过数据，重新记录会覆盖今天记录的数据。是否重新记录？', {
                    showCancel: true,
                    confirmText: '重新记录'
                })
                if (!confirm) return
            }

            this.$refs.bodyDataSubmit.open({
                weightRecord: this.weightRecord,
                bodyFatRecord: this.bodyFatRecord,
                waistRecord: this.waistRecord,
                submitBirthday: !this.recordList.length,
                userid: this.userid
            })
        },

        bodyDataSubmitSuccess() {
            this.getRecordList()
        },

        editRecord(item, index) {
            this.$refs.bodyDataSubmit.open({
                weightRecord: this.weightRecord,
                bodyFatRecord: this.bodyFatRecord,
                waistRecord: this.waistRecord,
                submitBirthday: index === this.recordList.length - 1,
                userid: this.userid,
                weight: item.weight || '',
                height: item.height || '',
                waist: item.waist || '',
                birthday: item.birthday || '',
                submit_date: item.submit_date
            })
        },

        async deleteRecord(id) {
            const {confirm} = await this.$uni.showModal('确定删除该记录吗？', {showCancel: true})
            if (!confirm) return

            this.$uni.showLoading('删除中...')
            const res = await this.xwy_api.request({
                url: 'front.system.logsCommon/del_records',
                data: {
                    types: 88,
                    ids: id
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) {
                this.$uni.showModal(res?.info || '删除失败')
                return
            }

            this.$uni.showToast('已删除')
            this.getRecordList()
        }
    }
}
</script>

<style lang="scss" scoped>
.page {
    min-height: 100vh;
    box-sizing: border-box;
    background-color: #f8f8f8;
}

.record-list {
    padding: 10px 0;

    .item {
        background-color: #fff;
        padding: 10px;
        padding-bottom: 0;
        margin: 10px;
        border-radius: 10px;

        .operation-bar {
            border-top: 1px solid #e5e5e5;

            .operation-bar__item {
                padding: 10px;
            }
        }
    }
}

.add-button-container {
    padding: 50px 20px 20px;

    .add-button {
        width: 200px;
        line-height: 44px;
        border-radius: 22px;
    }
}
</style>