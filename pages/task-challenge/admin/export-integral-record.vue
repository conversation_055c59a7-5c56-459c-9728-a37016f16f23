<template>
    <view>
        <template v-if="total_page">
            <view class="font14 p10" style="color: #e19898;">
                活动用户共{{ count }}条积分记录，需要分{{ total_page }}次导出，每次导出{{ perpage }}条数据。
            </view>

            <view class="p10 font14 color-content">
                <text>每次导出</text>
                <text class="plr5">{{ perpage }}</text>
                <text>条数据</text>
                <text class="pl10 color-primary" @click="uniPopupOpen('perpage')">修改</text>
            </view>
        </template>


        <view class="pb10">
            <view class="flex-kai" v-for="(item, index) in total_page" :key="item">
                <view class="color-title p10">
                    <text class="color-sub">{{ index + 1 }}、</text>
                    <text>{{ index * perpage + 1 }}</text>
                    <text class="plr5">-</text>
                    <text>
                        <template v-if="index + 1 === total_page">{{ count }}</template>
                        <template v-else>{{ (index + 1) * perpage }}</template>
                    </text>
                </view>

                <view class="color-primary p10" @click="exportData(index + 1)">导出</view>
            </view>
        </view>


        <uni-popup ref="perpage" type="dialog" mode="input" :is-mask-click="false">
            <uni-popup-dialog
                mode="input"
                title="每次导出数据量"
                :value="perpage"
                :placeholder="'请输入 100 - ' + max_count + ' 内的数字'"
                @confirm="perpageInputConfirm"
            ></uni-popup-dialog>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
import openExcelFile from '@/utils/open-excel-file'

export default {
    data() {
        return {
            count: 0,
            total_page: 0,
            perpage: 1000,
            max_count: 1000,
            src_list: [],
            download_src: ''
        }
    },

    onLoad() {
        this.$uni.showLoading()
        this.getData()
    },

    methods: {
        getData() {
            this.getOpenerEventChannel().once('data', data => {
                this.id = data.id
                this.must_submit = data.must_submit
                this.white_list_open = data.white_list_open

                this.getOne()
            })
        },

        async getOne() {
            this.must_submit ||= await this.getActiveMustSubmit()
            const list = await this.getRecordList(1)
            uni.hideLoading()

            if (!list?.length) return this.$uni.showModal('暂无积分记录', {success: () => uni.navigateBack()})

            if (this.count <= this.perpage) {
                const tableData = this.excelDataProcessing(list, 1)
                openExcelFile.openDocument(tableData, '积分记录')
                return this.$uni.navigateBack()
            }

            this.setTotalPage()
        },

        async exportData(page) {
            this.$uni.showLoading('导出中...')
            const list = await this.getRecordList(page)
            const tableData = this.excelDataProcessing(list, page)
            openExcelFile.openDocument(tableData, `积分记录 - 第${page}页`)
            uni.hideLoading()
        },

        perpageInputConfirm(val) {
            const perpage = Number(val)
            if (isNaN(perpage) || perpage < 100) {
                return this.$uni.showModal(`输入错误，请输入 100 - ${this.max_count} 内的数字`, {
                    success: () => this.uniPopupOpen('perpage')
                })
            }
            this.perpage = perpage
            this.setTotalPage()
        },

        setTotalPage() {
            this.total_page = Math.ceil(this.count / this.perpage)
            this.src_list = []
        },

        excelDataProcessing(list, page) {
            const tHead = ['序号', '系统标识']
            this.must_submit.forEach(item => tHead.push(item.title))
            if (this.white_list_open) tHead.push('账号')
            tHead.push('积分', '说明', '队伍', '时间')

            const tBody = []

            console.log(page);
            console.log((page - 1) * this.perpage);

            list.forEach((item, index) => {
                const must_submit_value = this.must_submit.map(a => {
                    const user_must_submit = item.user_details?.must_submit || []
                    return user_must_submit.find(u => u.title === a.title)?.value || ''
                })

                const data = [
                    (page - 1) * this.perpage + index + 1,               // 序号
                    `w.${app.globalData['who']}u.${item.userid || 0}`, // 商户号+会员号
                    ...must_submit_value,    // must_submit
                ]

                // 白名单导入的账号
                if (this.white_list_open) data.push(item['user_white_details']?.username || '')

                data.push(
                    item['integral_num'] || 0,  // 积分
                    item.memo || '',         // 说明
                    item.user_details?.team_details?.name || '', // 队伍
                    item.create_time || ''   // 时间
                )

                tBody.push(data)
            })


            return [tHead, ...tBody]
        },


        async getActiveMustSubmit() {
            const details = app.globalData['activity_detail']
            if (details && details.active_id === this.id && details.conf?.must_submit) {
                return details.conf.must_submit
            }

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details',
                data: {
                    active_id: this.id
                }
            })

            return res?.data?.active_details?.conf?.must_submit
        },

        async getRecordList(page = 1) {
            const data = {
                active_id: this.id,
                all_active_user: 1, // 【1】指定查看整个活动用户的积分列表
                get_user_details: 1, // 【1】需要遍历输出用户的报名信息
                page,
                perpage: this.perpage
            }

            // 【1】获取白名单导入的名单信息
            if (this.white_list_open) data.get_white_list_details = 1


            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.userActiveIntegral/active_integral_list',
                data
            })

            if (page === 1) this.count = res?.data?.['integral_list']?.total || 0

            return res?.data?.['integral_list']?.data || []
        },

        uniPopupOpen(ref) {
            this.$refs[ref].open()
        },

        uniPopupClose(ref) {
            this.$refs[ref].close()
        },
    }
}
</script>

<style lang="scss">
.uni_popup {
    width: 280px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}

.uni_popup-btn {
    line-height: 40px;
    border-radius: 20px;
}

.uni_popup-btn::after {
    border: none;
}
</style>