<template>
    <view class="page">
        <view class="form">
            <view class="form-item">
                <view class="top color-content">
                    <text>关卡名称</text>
                    <text class="color-error font16"> *</text>
                </view>
                <view class="bottom font16">
                    <input class="input" type="text" v-model="name" maxlength="10"
                           placeholder="请输入关卡名称 10字内"/>
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">关卡排序</view>
                <view class="bottom font16">
                    <input class="input" type="number" v-model="sort_num" maxlength="5"/>
                </view>
            </view>

            <template v-if="clearance_type === 2">
                <view class="form-item">
                    <view class="top color-content">
                        <text>最小积分数</text>
                        <text class="color-error font16"> *</text>
                        <text class="color-sub font12 pl5">(不含设置的积分数)</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="digit" v-model="min_num" maxlength="8"
                               placeholder="请输入关卡最小积分数"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <text>最大积分数</text>
                        <text class="color-error font16"> *</text>
                        <text class="color-sub font12 pl5">(含设置的积分数)</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="digit" v-model="max_num" maxlength="8"
                               placeholder="请输入关卡最大积分数"/>
                    </view>
                </view>
            </template>

            <view class="form-item">
                <view class="top color-content">关卡任务</view>
                <view class="bottom font16">
                    <view class="flex-kai">

                        <picker class="view" :range="point_job_set_options" range-key="title"
                                :value="point_job_set_options.findIndex(item => item.value === conf.point_job_set_open)"
                                @change="pointJobSetOpenChange">
                            {{ point_job_set_options.find(item => item.value === conf.point_job_set_open).title }}
                        </picker>
                        <view class="flex-all-center">
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </view>
            </view>

            <view v-if="conf.point_job_set_open" class="form-item">
                <view class="top color-content">选择需要绑定到关卡的任务</view>
                <view class="bottom flex-row flex-wrap pt5">
                    <view v-for="(item, index) in job_list" :key="item.types"
                          class="job-item flex-all-center" :class="{'job-selected': item.selected}"
                          @click="jobSelectedChange(index)">
                        <view>{{ item.title }}</view>
                        <view v-show="item.selected" class="selected-tag">
                            <text class="iconfont icon-selected-mark color-light-primary font24"></text>
                        </view>
                    </view>
                </view>
            </view>

            <template v-if="job_open['types_4']">
                <view class="form-item">
                    <view class="top color-content">AI运动项目</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker class="view" :range="level_independent_ai_sport_options" range-key="title"
                                    :value="level_independent_ai_sport_options.findIndex(item => item.value === level_independent_ai_sport)"
                                    @change="levelIndependentAiSportChange">
                                {{ level_independent_ai_sport_options.find(item => item.value === level_independent_ai_sport).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-show="level_independent_ai_sport === 1" class="form-item">
                    <view class="top color-content">选择需要绑定到关卡的AI运动项目</view>
                    <view class="bottom flex-row flex-wrap pt5">
                        <view v-for="(item, index) in ai_sport_list" :key="item.types"
                              class="job-item flex-all-center" :class="{'job-selected': item.selected}"
                              @click="aiSportSelectedChange(index)">
                            <view>{{ item.name }}</view>
                            <view v-show="item.selected" class="selected-tag">
                                <text class="iconfont icon-selected-mark color-light-primary font24"></text>
                            </view>
                        </view>
                    </view>
                </view>
            </template>

            <view v-if="job_open['types_2']" class="form-item">
                <view class="top color-content">答题考试</view>
                <view class="bottom font16">
                    <view class="flex-kai" @click="toSelExam">
                        <view class="view">
                            <view v-if="exam_id">{{ exam_title || exam_id }}</view>
                            <view v-else class="color-sub">选择考卷</view>
                        </view>
                        <view class="flex-all-center">
                            <view v-if="exam_id" class="color-sub font12" style="width: 30px;"
                                  @click.stop="deleteExam">解绑
                            </view>
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </view>
            </view>

            <template v-if="job_open['types_9']">
                <view class="form-item">
                    <view class="top color-content">填词任务类型</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker class="view" :range="fill_words_options" range-key="title"
                                    :value="fill_words_options.findIndex(item => item.value === conf.fill_words.type)"
                                    @change="fillWordsTypeChange">
                                {{ fill_words_options.find(item => item.value === conf.fill_words.type).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">填词任务题库</view>
                    <view class="bottom font16">
                        <view class="flex-kai" @click="toSelectFillWordsCategory">
                            <view class="view">
                                <view v-if="conf.fill_words.category_id">
                                    {{ conf.fill_words.name || conf.fill_words.category_id }}
                                </view>
                                <view v-else class="color-sub">选择题库</view>
                            </view>
                            <view class="flex-all-center">
                                <view v-if="conf.fill_words.category_id" class="color-sub font12"
                                      style="width: 30px;" @click.stop="deleteFillWordsCategory">解绑
                                </view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>填词任务题目数</view>
                        <!--<view class="color-sub font12">
                            从题库随机出题数, 不得大于所选题库的题目数。选择公共题库时，题目数不得大于100。
                        </view>-->
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="digit" v-model="conf.fill_words.num" maxlength="8"
                               placeholder="请输入随机出题数"/>
                    </view>
                </view>
            </template>


            <view v-if="job_open['types_10']" class="form-item">
                <view class="top color-content">扫雷任务设置</view>
                <view class="bottom font16">
                    <level-mine-sweeper-set :set.sync="conf.mine_sweeper"/>
                </view>
            </view>

            <view v-if="job_open['types_11']" class="form-item">
                <view class="top color-content">连连看任务设置</view>
                <view class="bottom font16">
                    <lian-lian-kan-set :set.sync="conf.lian_lian_kan"/>
                </view>
            </view>

            <view v-if="job_open['types_13']" class="form-item">
                <view class="top color-content">贪吃蛇任务设置</view>
                <view class="bottom font16">
                    <gluttonous-snake-set :set.sync="conf.snake"/>
                </view>
            </view>

            <template v-if="job_open['types_14']">
                <view class="form-item">
                    <view class="top color-content">找茬关卡绑定</view>
                    <view class="bottom font16">
                        <view class="flex-kai" @click="toSelectPictureDiffLevel">
                            <view class="view">
                                <view v-if="conf.picture_diff.id">
                                    {{ conf.picture_diff.name || conf.picture_diff.id }}
                                </view>
                                <view v-else class="color-sub">选择关卡</view>
                            </view>
                            <view class="flex-all-center">
                                <view v-if="conf.picture_diff.id" class="color-sub font12"
                                      style="width: 30px;" @click.stop="deletePictureDiffLevel">解绑
                                </view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <text>找茬时间限制</text>
                        <text class="pl5 color-sub font12">(不限时不填或填0。单位: 秒)</text>
                    </view>
                    <view class="bottom font16">

                        <input class="input" type="number" :value="conf.picture_diff.time" maxlength="3"
                               @blur="pictureDiffTimeChange"/>
                    </view>
                </view>
            </template>

            <view class="form-item">
                <view class="top color-content">
                    <text>关卡图片</text>
                    <text class="color-error font16"> *</text>
                </view>
                <view style="padding-top: 5px;">
                    <view class="image-view" v-if="iconPath">

                        <image class="image-item" :src="iconPath" mode="aspectFill"
                               @click="previewImage([iconPath])"/>
                        <view class="del-image-item" @click.stop="iconPath = ''">
                            <uni-icons type="closeempty" color="#e20f04"/>
                        </view>
                    </view>

                    <navigator v-else class="add-image text-center"
                               :url="'/pages/other/image_upload_or_select?key=iconPath&active_id=' + active_id">
                        <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                    </navigator>
                </view>
            </view>

            <view class="form-item">
                <view class="top color-content">
                    <view>关卡图片是否全屏显示不可滚动</view>
                    <view class="color-sub font14">设置为【是】时，关卡图片全屏显示，无法滚动。并且点击图片无法触发到关卡或任务。</view>
                </view>
                <view class="bottom font16">
                    <view class="flex-kai">

                        <picker class="view" :range="['否', '是']" :value="conf.image_fixed"
                                @change="conf.image_fixed = Number($event.detail.value)">
                            {{ conf.image_fixed ? '是' : '否' }}
                        </picker>
                        <view class="flex-all-center">
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </view>
            </view>

            <view v-show="!conf.image_fixed">
                <view class="form-item">
                    <view class="top color-content">
                        <text>当前关卡与图片顶部的距离</text>
                        <text class="color-error font16"> *</text>
                        <text class="pl5 color-sub font12">(单位:px)</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="digit" v-model="margin_top" maxlength="5"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <text>当前关卡与图片左边的距离</text>
                        <text class="color-error font16"> *</text>
                        <text class="pl5 color-sub font12">(单位:px)</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="digit" v-model="conf.point_img_detail.margin_left"
                               maxlength="5"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <text>当前关卡的长度</text>
                        <text class="color-error font16"> *</text>
                        <text class="pl5 color-sub font12">(单位:px)</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="digit" v-model="conf.point_img_detail.point_width"
                               maxlength="5"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <text>当前关卡的高度</text>
                        <text class="color-error font16"> *</text>
                        <text class="pl5 color-sub font12">(单位:px)</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="digit" v-model="conf.point_img_detail.point_height"
                               maxlength="5"/>
                    </view>
                </view>
            </view>

        </view>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view class="save-btn color-white text-center font18 bg-primary" @click="save">保存</view>
        </view>


    </view>
</template>

<script>
const app = getApp()
import config from '../../config'

import levelMineSweeperSet from '../../components/level-mine-sweeper-set.vue'
import lianLianKanSet from '../../components/lian-lian-kan-set.vue'
import gluttonousSnakeSet from '../../components/gluttonous-snake-set.vue'

export default {
    components: {levelMineSweeperSet, lianLianKanSet, gluttonousSnakeSet},
    data() {
        return {
            active_id: '',
            id: '',
            //【1】按任务数过关  【2】按积分数过关
            clearance_type: 0,
            name: '',
            min_num: '',
            max_num: '',
            exam_id: '',
            exam_title: '',
            sort_num: '',
            iconPath: '',
            margin_top: '',
            margin_left: '',
            point_width: '',
            point_height: '',
            conf: {
                point_img_detail: {
                    margin_left: '',
                    point_height: '',
                    point_width: ''
                },
                fill_words: {
                    category_id: '',
                    name: '',
                    num: '',
                    type: 'fill_words'
                },
                mine_sweeper: {
                    row: 8,
                    col: 8,
                    boom_num: 10,
                    seconds: ''
                },
                lian_lian_kan: JSON.parse(JSON.stringify(config.lianLianKanDefaultSet)),
                snake: JSON.parse(JSON.stringify(config.gluttonousSnakeDefaultSet)),

                /**
                 * 关卡是否单独设置任务
                 * 0: 否，使用活动设置的任务，保存时不能保存job_set 默认
                 * 1: 是，使用单独设置的任务，保存时需要保存job_set，并且需要检查job_list不能为空
                 * */
                point_job_set_open: 0,

                // 找茬关卡绑定
                picture_diff: {
                    id: '',
                    name: '',
                    time: ''
                },

                ai_sport: {
                    types_list: []
                },

                image_fixed: 0
            },
            rank_set: {},

            point_job_set_options: [
                {value: 0, title: '使用活动设置的任务'},
                {value: 1, title: '关卡自定义选择任务'}
            ],
            job_list: [],

            fill_words_options: [
                {value: 'fill_words', title: '填词'},
                {value: 'fill_couplet', title: '填对联'}
            ],

            ai_sport_list: [],
            level_independent_ai_sport: 0,
            level_independent_ai_sport_options: [
                {value: 0, title: '使用活动设置的AI运动项目'},
                {value: 1, title: '关卡自定义选择AI运动项目'}
            ],
        }
    },

    computed: {
        job_open() {
            const job_open = {}
            this.job_list.forEach(item => {
                if (item.selected) job_open[`types_${item.types}`] = true
            })
            return job_open
        }
    },

    onLoad(e) {
        this.$uni.showLoading()
        this.active_id = e.active_id
        if (e.point_id) this.point_id = e.point_id

        this.$login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err['errTitle'] || '提示')
            }

            this.init()
        })
    },
    methods: {
        async init() {
            await this.getActivityDetail()
            if (this.point_id) await this.getPointDetail()
            this.pointJobSet()
            uni.hideLoading()
        },


        async getActivityDetail() {
            let activity_detail = app.globalData['activity_detail']

            if (!activity_detail || activity_detail.active_id !== this.active_id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {active_id: this.active_id}
                })
                activity_detail = res.data.active_details
            }

            this.activity_detail = activity_detail

            if (activity_detail.rank_set) this.rank_set = activity_detail.rank_set
            this.clearance_type = activity_detail.conf.active.job_set?.types || 1

            this.activeJobList = activity_detail.conf.active.job_set?.job_list || []

            const motion_list = activity_detail.conf?.AI_motion?.motion_list
            if (motion_list?.length) {
                this.ai_sport_list = motion_list.map(item => ({
                    name: item.name,
                    types: item.types,
                    selected: true
                }))
            }
        },


        async getPointDetail() {
            this.$uni.setNavigationBarTitle('编辑关卡')

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/map_point_details',
                data: {id: this.point_id}
            })

            uni.hideLoading()

            if (res?.status !== 1 || !res?.data?.['map_point_details']) {
                return this.$uni.showModal(res && res.info || '关卡数据获取失败', {
                    success: () => uni.navigateBack()
                })
            }

            const detail = res.data['map_point_details']

            this.name = detail.name
            this.min_num = detail.min_num || detail.min_num === 0 ? detail.min_num : ''
            this.max_num = detail.max_num || ''
            if (detail.exam_id) this.exam_id = detail.exam_id
            if (detail.exam_details?.exam_name) this.exam_title = detail.exam_details.exam_name
            if (detail.sort_num) this.sort_num = detail.sort_num
            if (detail.map_pic) this.iconPath = detail.map_pic
            if (detail.margin_top || detail.margin_top === 0) this.margin_top = detail.margin_top

            if (detail.conf) {
                const conf = detail.conf
                if (conf.point_img_detail) this.conf.point_img_detail = conf.point_img_detail
                if (conf.fill_words) {
                    conf.fill_words.type ||= 'fill_words'
                    this.conf.fill_words = conf.fill_words
                }
                if (conf.mine_sweeper) this.conf.mine_sweeper = conf.mine_sweeper
                if (conf.lian_lian_kan) this.conf.lian_lian_kan = conf.lian_lian_kan
                if (conf.snake) this.conf.snake = conf.snake
                if (conf.point_job_set_open) {
                    this.conf.point_job_set_open = conf.point_job_set_open
                    if (conf.job_set?.job_list?.length) this.pointJobListSet = conf.job_set.job_list
                }
                if (conf.picture_diff) this.conf.picture_diff = conf.picture_diff
                if (conf.ai_sport) {
                    this.conf.ai_sport = conf.ai_sport
                    this.level_independent_ai_sport = 1
                    const types_list = conf.ai_sport.types_list
                    if (types_list?.length) {
                        this.ai_sport_list.forEach((item, index) => {
                            this.$set(this.ai_sport_list[index], 'selected', types_list.includes(item.types))
                        })
                    }
                }

                if (conf.image_fixed) this.conf.image_fixed = conf.image_fixed

                if (conf.level_task_image_diy_set) this.level_task_image_diy_set = conf.level_task_image_diy_set
            }
        },

        pointJobSet() {
            let job_list = this.activeJobList.map(item => ({types: item.types}))
            if (this.pointJobListSet) job_list = this.pointJobListSet

            this.job_list = this.activeJobList.map(item => ({
                types: item.types,
                title: item.title || '',
                // logo: item.logo || '',
                selected: job_list.some(v => v.types === item.types)
            }))
        },

        pointJobSetOpenChange(e) {
            const open = this.point_job_set_options[e.detail.value].value
            this.conf.point_job_set_open = open
            if (!open) {
                this.ai_sport_list.forEach((item, index) => {
                    if (!item.selected) this.$set(this.ai_sport_list[index], 'selected', true)
                })
            }
        },

        jobSelectedChange(index) {
            this.$set(this.job_list[index], 'selected', !this.job_list[index].selected)
        },

        levelIndependentAiSportChange(e) {
            const open = this.level_independent_ai_sport_options[e.detail.value].value
            this.level_independent_ai_sport = open
            if (!open) {
                this.job_list.forEach((item, index) => {
                    if (!item.selected) this.$set(this.job_list[index], 'selected', true)
                })
            }
        },

        aiSportSelectedChange(index) {
            this.$set(this.ai_sport_list[index], 'selected', !this.ai_sport_list[index].selected)
        },


        toSelExam() {
            if (!this.rank_set.exam_open) return this.$uni.showModal('未开通答题功能，请联系客服开通。')

            this.$uni.navigateTo('/pages/likou_dati/pages/exam/exam_list/exam_list?in_select=true', {
                events: {
                    updateExam: data => {
                        this.exam_id = data.id
                        this.exam_title = data.title
                    }
                }
            })
        },

        deleteExam() {
            this.exam_id = ''
            this.exam_title = ''
        },

        fillWordsTypeChange(e) {
            this.conf.fill_words.type = this.fill_words_options[e.detail.value].value
            this.conf.fill_words.category_id = ''
            this.conf.fill_words.name = ''
        },

        async toSelectFillWordsCategory() {
            this.$uni.navigateTo('/pages/likou_dati/pages/question/category_list/category_list?select=1', {
                events: {
                    selectCategory: category => {
                        this.conf.fill_words.category_id = category.id
                        this.conf.fill_words.name = category.name
                    }
                }
            })


            /*const res = await this.$uni.showActionSheet(['公共题库', '个人题库'])
            if (res.tapIndex === 0) {
                this.conf.fill_words.category_id = this.xwy_config.fill_words_public_id
                this.conf.fill_words.name = '公共题库'
            } else if (res.tapIndex === 1) {
                this.$uni.navigateTo('/pages/likou_dati/pages/question/category_list/category_list?select=1', {
                    events: {
                        selectCategory: category => {
                            this.conf.fill_words.category_id = category.id
                            this.conf.fill_words.name = category.name
                        }
                    }
                })
            }*/
        },

        deleteFillWordsCategory() {
            this.conf.fill_words.category_id = ''
            this.conf.fill_words.name = ''
        },

        async toSelectPictureDiffLevel() {
            const res = await this.$uni.showActionSheet(['平台关卡', '个人关卡'])

            if (!res.hasOwnProperty('tapIndex') || res.errMsg === 'showActionSheet:fail cancel') return

            let id = this.xwy_config.picture_diff_public_id
            if (res.tapIndex === 1) {
                if (!this.rank_set.closed_AD && !this.rank_set.exam_open) {
                    return this.$uni.showModal('活动当前只能使用平台关卡，如需使用个人关卡，请联系客服', {
                        showCancel: true,
                        confirmText: '联系客服',
                        success: res => {
                            if (res.confirm) this.$uni.navigateTo('/pages/other/contact')
                        }
                    })
                }
                id = this.activity_detail?.conf?.active?.['pic_qb_cat_id']
                if (!id) return this.$uni.showModal('活动未添加个人关卡，请到 活动管理-找茬关卡 中添加关卡')
            }

            this.$uni.navigateTo(`/pages/treasure-hunt/user/level-list?id=${this.active_id}&cat_id=${id}&game_type=1&select=1`, {
                events: {
                    selectLevel: level => {
                        this.conf.picture_diff.id = level.id
                        this.conf.picture_diff.name = level.title
                    }
                }
            })
        },

        deletePictureDiffLevel() {
            this.conf.picture_diff.id = ''
            this.conf.picture_diff.name = ''
        },

        pictureDiffTimeChange(e) {
            const value = Math.floor(e.detail.value)
            if (isNaN(value) || value < 0) return this.$uni.showToast('请输入正确的找茬时间限制')
            this.$delete(this.conf.picture_diff, 'time')
            this.$set(this.conf.picture_diff, 'time', value)
        },


        previewImage(urls) {
            uni.previewImage({
                urls
            })
        },


        getConfSet() {
            const conf = JSON.parse(JSON.stringify(this.conf))

            function validateAndSet(key) {
                const numberValue = Number(conf.point_img_detail[key])
                if (isNaN(numberValue) || numberValue < 0) {
                    return conf.image_fixed ? 0 : false
                }
                return numberValue
            }

            // 开启了关卡图片全屏固定显示，这些参数相当于无效，如果设置了非法参数，自动改为0
            const keys = [
                {key: 'margin_left', name: '与图片左边的距离'},
                {key: 'point_width', name: '长度'},
                {key: 'point_height', name: '高度'}
            ]
            for (let i = 0, len = keys.length; i < len; i++) {
                const num = validateAndSet(keys[i].key)
                if (num === false) {
                    this.$uni.showToast(`当前关卡${keys[i].name} 填写不正确, 请重新填写`, 'none', 3000)
                    return false
                }
                conf.point_img_detail[keys[i].key] = num
            }


            if (this.job_open['types_9'] && conf.fill_words.category_id) {
                const num = Math.floor(conf.fill_words.num)
                if (isNaN(num) || num <= 0) {
                    this.$uni.showToast('填词题目数填写不正确, 请重新填写', 'none', 2000)
                    return false
                }
                if (conf.fill_words.category_id === this.xwy_config.fill_words_public_id && num > 100) {
                    this.$uni.showToast('填词题库选择公共题库时, 题目数不能大于100', 'none', 2000)
                    return false
                }
                conf.fill_words.num = num
            } else {
                delete conf.fill_words
            }

            if (!this.job_open['types_10']) delete conf.mine_sweeper

            if (this.job_open['types_11']) {
                if (JSON.stringify(conf.lian_lian_kan) === JSON.stringify(config.lianLianKanDefaultSet)) {
                    delete conf.lian_lian_kan
                }
            } else {
                delete conf.lian_lian_kan
            }

            if (this.job_open['types_13']) {
                if (JSON.stringify(conf.snake) === JSON.stringify(config.gluttonousSnakeDefaultSet)) {
                    delete conf.snake
                }
            } else {
                delete conf.snake
            }

            if (this.job_open['types_14']) {
                if (!conf.picture_diff?.id) {
                    return this.$uni.showToast('请绑定找茬关卡或删除找茬任务', 'none', 3000)
                }
            } else {
                if (conf.hasOwnProperty('picture_diff')) delete conf.picture_diff
            }

            if (conf.point_job_set_open) {
                const job_list = this.job_list.filter(item => item.selected).map(item => ({types: item.types}))

                // 默认必须勾选1个任务
                let min = 1
                // 如果活动设置为按任务数过关，最少任务数不能小于活动设置的过关任务数
                const {types, must_finished_num} = this.activity_detail?.conf?.active?.job_set
                if (types === 1 && must_finished_num) min = Math.floor(must_finished_num)
                if (job_list.length < min) {
                    this.$uni.showToast(`请至少勾选${min}个任务`, 'none', 2000)
                    return false
                }

                conf.job_set = {job_list}
            } else {
                delete conf.point_job_set_open
            }

            if (this.job_open['types_4'] && this.level_independent_ai_sport) {
                const select_list = this.ai_sport_list.filter(item => item.selected)
                if (select_list.length !== this.ai_sport_list.length) {
                    conf.ai_sport.types_list = select_list.map(item => item.types)
                }
            }

            if (!conf.image_fixed) delete conf.image_fixed

            if (this.level_task_image_diy_set) conf.level_task_image_diy_set = this.level_task_image_diy_set

            return this._utils.base64['encode'](JSON.stringify(conf))
        },

        save() {

            if (!this.name) return this.$uni.showToast('请输入关卡名称')

            if (this.clearance_type === 2) {
                const min_num = Number(this.min_num)
                if (isNaN(min_num) || min_num < 0) return this.$uni.showToast('最小积分数 填写不正确, 请重新填写')

                const max_num = Number(this.max_num)
                if (isNaN(max_num) || max_num < 0) return this.$uni.showToast('最大积分数 填写不正确, 请重新填写')

                if (min_num > max_num) return this.$uni.showToast('最小积分数 不能大于 最大积分数')
            }

            if (this.job_open['types_2'] && !this.exam_id) {
                return this.$uni.showToast('请绑定答题考试或删除答题任务', 'none', 3000)
            }

            let margin_top = Number(this.margin_top)
            if (isNaN(margin_top) || margin_top < 0) {
                if (this.conf.image_fixed) {
                    // 开启了关卡图片全屏固定显示，这个参数相当于无效，如果设置了非法参数，自动改为0
                    margin_top = 0
                } else {
                    return this.$uni.showToast('当前关卡与图片顶部的距离 填写不正确, 请重新填写', 'none', 3000)
                }
            }

            if (!this.iconPath) return this.$uni.showToast('请上传关卡图片')

            const conf = this.getConfSet()
            if (!conf) return

            const value = {
                name: this.name,
                min_num: this.min_num,
                max_num: this.max_num,
                active_id: this.active_id,
                map_pic: this.iconPath,
                sort_num: this.sort_num ? this.sort_num * 1 : 0,
                exam_id: this.exam_id || '',
                margin_top,
                conf
            }

            // 按任务数过关，关卡积分区间保存为0-0
            if (this.clearance_type === 1) {
                value.min_num = 0
                value.max_num = 0
            }

            if (this.point_id) value.id = this.point_id

            this.saveAjax(value)
        },

        async saveAjax(data) {
            const type_text = this.id ? '修改' : '添加'

            this.$uni.showLoading(`${type_text}中...`)
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/add_update_map_point',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res.info || `${type_text}失败`)

            this.$uni.showToast(`${type_text}成功`)
            this.getOpenerEventChannel?.()?.emit?.('reloadList')
            this.$uni.navigateBack(1, {delay: 1000})
        },


    }
}
</script>

<style scoped>
.page {
    padding-bottom: 100px;
    background-color: #fff;
}

.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}

.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
    width: 100px;
    height: 100px;
    border-radius: 5px;
    line-height: 95px;
    margin: 5px calc((100% - 20px - (100px * 3)) / 6);
}


.image-item {
    width: 100%;
    max-height: 200px;
    border-radius: 5px;
}

.image-view {
    width: 100%;
    position: relative;
    display: inline-block;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}


.bottom-btn-view {
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.save-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}


.job-item {
    width: calc(100% / 3 - 10px);
    height: 50px;
    margin: 5px;
    border-radius: 5px;
    overflow: hidden;
    border: 1px solid #eee;
    box-sizing: border-box;
}

.job-selected {
    position: relative;
    border-color: #5cadff;
}

.job-selected .selected-tag {
    position: absolute;
    right: -1px;
    bottom: -1px;
    text-align: right;
    display: flex;
    flex-direction: column-reverse;
}
</style>
