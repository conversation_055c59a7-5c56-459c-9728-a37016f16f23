<template>
    <view class="page bg-background">
        <template v-if="!loading">

            <template v-if="list.length">
                <view class="flex-kai bg-white">
                    <view class="color-sub p10 font14">共{{ list.length }}个关卡</view>
                    <view @click="toAddOrEdit(false)" class="color-primary p10">
                        <text>添加关卡</text>
                        <uni-icons type="forward" color="#2d8cf0"/>
                    </view>
                </view>

                <view class="list">
                    <view class="item bg-white" v-for="(item, index) in list" :key="index">
                        <view style="padding-bottom: 10px;">
                            <view class="color-title pb5">{{ item.name }}</view>
                            <view v-if="clearance_type === 2" class="color-content font14">
                                关卡积分: {{item.min_num}}(不含) - {{item.max_num}}(含)
                            </view>
                            <view class="color-content font14"
                                  v-if="activity_detail.rank_set && activity_detail.rank_set.redpack">
                                红包余额: {{ item.redpack_amount }}元
                            </view>
                            <view class="color-content font14">排序: {{ item.sort_num }}</view>
                        </view>
                        <view class="tools-bar clear clearfix">
                            <template v-if="is_to_bind">
                                <view class="fr p10 color-primary" @click="bindLevel(item)">绑定</view>
                            </template>
                            <template v-else>
                                <view class="flex-row fl">
                                    <navigator
                                        class="color-primary font14 pt5 pr10"
                                        v-if="activity_detail.rank_set && activity_detail.rank_set.redpack"
                                        :url="'/pages/wallet/redpack_list?is_admin=1&id=' + id + '&point_id=' + item.id"
                                    >红包记录
                                    </navigator>
                                    
                                    <view v-if="job_open['types_8']" class="color-primary font14 pt5 pr10"
                                          hover-class="navigator-hover" @click="toEventSet(item)">关卡事件
                                    </view>
                                </view>
                                <view class="flex-row fr">
                                    <view v-if="activity_detail.rank_set && activity_detail.rank_set['rushing_details_diy']"
                                          @click="imageDiyTaskSet(item.id)" class="edit">
                                        <text class="iconfont icon-image color-sub font20"></text>
                                    </view>
                                    <view @click="toAddOrEdit(item.id)" class="edit">
                                        <text class="iconfont icon-edit color-sub font20"></text>
                                    </view>
                                    <view class="delete" @click="deleteItem(item.id)">
                                        <text class="iconfont icon-delete color-sub font20"></text>
                                    </view>
                                </view>
                            </template>
                        </view>
                    </view>
                </view>
            </template>

            <view v-if="!list.length" class="text-center" style="padding-top: 15vh;">
                <text class="iconfont icon-map color-border" style="font-size: 100px;"></text>
                <view class="color-sub">该活动下暂无关卡</view>
                <view class="flex-all-center pt15">
                    <view class="add-team-btn color-white bg-primary text-center" @click="toAddOrEdit(false)">
                        创建关卡
                    </view>
                </view>
            </view>
        </template>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'


export default {
    data() {
        return {
            id: '',
            clearance_type: 0,
            loading: true,
            list: [],
            activity_detail: {},
            is_to_bind: false,
            job_open: {}
        }
    },
    onLoad(e) {
        this.$uni.showLoading()
        if (e['to_bind']) this.is_to_bind = true
        this.id = e.id
        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err['errTitle'] || '提示')
            }

            this.getActivityDetail()
        })
    },
    onPullDownRefresh() {
        if (!this.loading) this.getListInit()

        uni.stopPullDownRefresh()
    },
    methods: {
        async getActivityDetail() {
            let activity_detail = app.globalData['activity_detail']

            if (!activity_detail || activity_detail.active_id !== this.id) {
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {active_id: this.id}
                })
                activity_detail = res.data.active_details
            }
            this.clearance_type = activity_detail.conf.active.job_set?.types || 1

            this.activity_detail = activity_detail
            
            this.checkJobList()
            
            await this.getList()
        },

        
        // 检查添加的任务类型
        checkJobList() {
            const list = this.activity_detail?.conf?.active?.job_set?.job_list || []
            const job_open = {}
            list.forEach(item => {
                job_open[`types_${item.types}`] = true
            })
            this.job_open = job_open
        },

        getListInit() {
            this.loading = true
            this.$uni.showLoading()
            this.list = []
            this.getList()
        },

        async getList() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_map_point_list',
                data: {
                    active_id: this.id
                },
            })

            uni.hideLoading()
            this.loading = false

            if (!res.status) return

            const list = res.data?.map_point_list || []

            // 打卡点位增加字段类型 types  判断
            // 0   是之前的关卡 id及健步走活动点位，打卡活动的打卡点
            // 1 代表是任务闯关活动里的打卡点（闯关里需要进行打卡，打卡的记录）
            this.list = list.filter(item => item.types === 0)
        },

        toEventSet(item) {
            this.$uni.navigateTo(`/pages/historical-event-sort/admin/level-event-set?active_id=${this.id}&point_id=${item.id}&point_name=${item.name}`)
        },

        imageDiyTaskSet(id) {
            const {access_token, shop_info} = app.globalData
            const web_base_url = shop_info?.['shop_set']?.static_url
            if (!web_base_url) return this.$uni.showModal('没有配置【static_url】，无法使用')

            const url = `${web_base_url}web/wx-cloud-api/pages/admin_manage/task-challenge-level-task-diy-image-set/#/?access_token=${access_token}&active_id=${this.id}&point_id=${id}`

            let isH5 = false
            // #ifdef H5
            isH5 = true
            // #endif

            if (isH5) return window.open(url, "_blank")

            this.$uni.setClipboardData(url)
            this.$uni.showModal('设置地址已复制，请到电脑浏览器打开')
        },

        toAddOrEdit(id) {
            let url = `add?active_id=${this.id}`
            if (id) url += `&point_id=${id}`
            this.$uni.navigateTo(url, {
                events: {
                    reloadList: () => this.getListInit()
                }
            })
        },


        deleteItem(id) {
            this.deleteConfirm([id])
        },

        deleteConfirm(ids) {
            uni.showModal({
                title: '提示',
                content: `确定删除${ids.length > 1 ? '选中的' : '该'}关卡?`,
                confirmText: '删除',
                cancelColor: '#80848f',
                confirmColor: '#ed3f14',
                success: res => {
                    if (res.confirm) this.deleteAjax(ids)
                }
            })
        },

        async deleteAjax(ids) {
            uni.showLoading({
                title: '删除中...'
            })

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.admin/del_map_point',
                data: {
                    active_id: this.id,
                    ids: ids.join(',')
                }
            })

            uni.hideLoading()
            if (res?.status !== 1) return this.$uni.showModal(res.info || '删除失败')

            this.list = this.list.filter(item => {
                return !ids.find(v => item.id === v)
            })
            this.$uni.showToast('删除成功', 'success')
        },


        bindLevel(item) {
            const {id, name} = item
            this.getOpenerEventChannel?.()?.emit?.('changeLevel', {id, name})
            this.$uni.navigateBack()
        }
    }
}
</script>

<style scoped>
.page {
    min-height: 100vh;
    padding-bottom: 10px;
    box-sizing: border-box;
}

.list {

}

.item {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
}

.tools-bar {
    border-top: 1px solid #eee;
    padding-top: 10px;
}

.edit, .delete {
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 50%;
}

.edit {
    border: 1px solid #eee;
    margin-right: 10px;
}

.delete {
    border: 1px solid #eee;
}

.add-team-btn {
    width: 200px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
}
</style>
