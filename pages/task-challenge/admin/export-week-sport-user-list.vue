<template>
    <view>
        <uni-popup ref="dateSelectPopup" :is-mask-click="false">
            <view class="date-select-popup">
                <view class="text-center">请选择时间范围</view>
                <uni-forms label-position="top" label-width="200">
                    <uni-forms-item label="开始日期">
                        <uni-datetime-picker type="date" :value="startDate" :clear-icon="false"
                                             @change="dateChange($event, 'startDate')"/>
                    </uni-forms-item>
                    <uni-forms-item label="结束日期">
                        <uni-datetime-picker type="date" :value="endDate" :clear-icon="false"
                                             @change="dateChange($event, 'endDate')"/>
                    </uni-forms-item>
                </uni-forms>

                <view class="button-container flex-all-center">
                    <view class="button color-white bg-primary text-center" hover-class="navigator-hover"
                          @click="exportRecord">确定
                    </view>
                </view>

                <view class="flex-all-center">
                    <navigator open-type="navigateBack" class="p10 color-sub font14">取消</navigator>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
import openExcelFile from '@/utils/open-excel-file'
import utils from '@/utils/utils.js'
import config from '../config'

const weekDateList = utils.getWeekDate(utils.getDay(-7, true, '-'), true)

export default {
    data() {
        return {
            perpage: 10000,
            startDate: weekDateList[0],
            endDate: weekDateList[weekDateList.length - 1]
        }
    },

    onLoad(params) {
        this.tableTitle = '记录'
        if (params.title) {
            this.tableTitle = `${params.title}记录`
            this.$uni.setNavigationBarTitle(`导出${this.tableTitle}`)
        }
        this.active_id = params.active_id
        this.types = Number(params.types)

        this.getActiveDetails()
    },

    methods: {
        async getActiveDetails() {
            this.$uni.showLoading()

            let details = app.globalData.activity_detail
            if (!details || details.active_id !== this.active_id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.active_id
                    }
                })
                details = res?.data?.active_details
            }

            if (!details) {
                uni.hideLoading()
                this.$uni.showModal('活动获取失败', {success: () => uni.navigateBack()})
                return
            }

            this.must_submit = details.conf?.must_submit || []
            if (details.rank_set?.team_group_open) this.team_open = true

            uni.hideLoading()

            this.$refs.dateSelectPopup.open()
        },

        dateChange(e, type) {
            this[type] = e
        },

        async exportRecord() {
            this.$uni.showLoading('导出中...')
            const list = await this.getRecordList()
            uni.hideLoading()

            if (!list?.length) {
                this.$uni.showModal(`${this.startDate} - ${this.endDate} 无${this.tableTitle}`)
                return
            }


            const tableData = this.excelDataProcessing(list, 1)

            openExcelFile.openDocument(tableData, `${this.startDate} - ${this.endDate} ${this.tableTitle}`)
            this.$uni.navigateBack()
        },


        excelDataProcessing(list, page) {
            const tHead = ['序号', '系统标识']
            this.must_submit.forEach(item => tHead.push(item.title))
            tHead.push('积分', '说明')
            if (this.team_open) tHead.push('队伍')
            tHead.push('时间')

            const tBody = []

            list.forEach((item, index) => {
                const must_submit_value = this.must_submit.map(a => {
                    const user_must_submit = item.user_details?.must_submit || []
                    return user_must_submit.find(u => u.title === a.title)?.value || ''
                })

                const data = [
                    (page - 1) * this.perpage + index + 1,               // 序号
                    `w.${app.globalData['who']}u.${item.userid || 0}`,   // 商户号+会员号
                    ...must_submit_value,    // must_submit
                ]

                data.push(
                    item.integral_num || 0,  // 积分
                    item.memo || ''          // 说明
                )

                if (this.team_open) data.push(item.user_details?.team_details?.name || '') // 队伍
                data.push(item.create_time || '')   // 时间

                tBody.push(data)
            })


            return [tHead, ...tBody]
        },

        async getRecordList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.userActiveIntegral/active_integral_list',
                data: {
                    active_id: this.active_id,
                    all_active_user: 1, // 【1】指定查看整个活动用户的积分列表
                    get_user_details: 1, // 【1】需要遍历输出用户的报名信息
                    integral_types: config.getTaskIntegralTypes(this.types),
                    begin_time: `${this.startDate} 00:00:00`,
                    end_time: `${this.endDate} 23:59:59`,
                    page: 1,
                    perpage: this.perpage
                }
            })

            return res?.data?.integral_list?.data || []
        },
    }
}
</script>

<style lang="scss" scoped>
.date-select-popup {
    width: 80vw;
    max-width: 400px;
    padding: 20px;
    border-radius: 10px;
    background-color: #fff;
}

.button-container {
    padding: 10px;

    .button {
        width: 200px;
        line-height: 44px;
        border-radius: 22px;
    }
}
</style>