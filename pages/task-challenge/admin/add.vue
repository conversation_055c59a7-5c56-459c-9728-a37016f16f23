<template>
    <view class="page">
        <view v-if="success" class="save-success bg-white">
            <view class="icon text-center">
                <uni-icons type="checkbox-filled" size="100" color="#19be6b"/>
                <view class="color-content font16">活动{{ form_data.active_id ? '修改' : '创建' }}成功！</view>
            </view>
            <view class="flex-all-center">
                <button class="bg-success color-white" style="width: 200px;" @click="lookMyActivityList">
                    查看我的活动
                </button>
            </view>
        </view>

        <view class="type-list flex-row bg-white">
            <view
                class="type-item color-content font14"
                v-for="(item, index) in type_list"
                :key="index"
                :class="{'active-type': item.id === type_id}"
                :style="{width: 'calc(100% / ' + type_list.length + ')'}"
                @click="type_id = item.id"
            >{{ item.title }}
            </view>
        </view>

        <add-activity-tips ref="add_activity_tips"/>

        <view v-if="page_init_success" class="form">
            <view v-show="type_id === 1">
                <view class="form-item">
                    <view class="top color-content">
                        <text>活动名称</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="text" v-model="form_data.name"
                               placeholder="请输入活动名称"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">主办方</view>
                    <view class="bottom font16">
                        <input class="input" type="text" v-model="form_data.organizer"
                               placeholder="请输入主办方单位名称"/>
                    </view>
                </view>

                <template v-if="!form_data.active_id">
                    <view class="form-item">
                        <view class="top color-content">
                            <text>手机号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="number" v-model="form_data.mobile"
                                   placeholder="请输入真实手机号"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <text>微信号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="text" v-model="form_data.wechat_num"
                                   placeholder="请输入真实微信号"/>
                        </view>
                    </view>
                </template>

                <view class="form-item">
                    <view class="top color-content">活动开始时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.begin_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">活动结束时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.end_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view v-if="!conf.active.news.news_id" class="form-item">
                    <view class="top color-content">活动说明</view>
                    <view class="bottom font16">
                        <textarea
                            class="textarea"
                            maxlength="-1"
                            auto-height="true"
                            v-model="form_data.content"
                            placeholder="请输入活动说明"
                        />
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>活动说明文章</view>
                        <view class="font12 color-sub">
                            绑定文章后，活动说明将会显示文章内容，不会显示上面填写的活动说明
                        </view>
                    </view>
                    <view class="bottom font16">
                        <view class="flex-kai" @click="toSelNews('content')">
                            <view class="view">
                                <view v-if="conf.active.news.news_id">
                                    {{ conf.active.news.news_title || conf.active.news.news_id }}
                                </view>
                                <view v-else class="color-sub">选择文章</view>
                            </view>
                            <view class="flex-all-center">
                                <view
                                    v-if="conf.active.news.news_id"
                                    class="color-sub font12"
                                    style="width: 30px;"
                                    @click.stop="deleteNews('content')"
                                >解绑
                                </view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>
            </view>


            <view v-show="type_id === 3">
                <view class="form-item">
                    <view class="top color-content">活动参与方式</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.enter_types_list"
                                :value="form_options.enter_types_list.findIndex(v => v.value === conf.active.enter_types)"
                                range-key="title"
                                @change="enter_typesChange"
                            >
                                {{ form_options.enter_types_list.find(v => v.value === conf.active.enter_types).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="conf.active.enter_types === 2" class="form-item">
                    <view class="top color-content">
                        <text>活动密码</text>
                        <text v-if="!have_password" class="color-error font16"> *</text>
                        <text class="color-sub font12 pl5">(3-20位)</text>
                    </view>
                    <view class="bottom font16">

                        <input
                            class="input"
                            v-model="conf.active.password"
                            maxlength="20"
                            :placeholder="have_password ? '已设置密码，无需修改密码不用填写' : '请输入活动密码'"
                        />
                    </view>
                </view>


                <must-submit-set :must-submit.sync="conf.must_submit"/>


                <active-sex-set :sex-required.sync="conf.active.sex_required"
                                :sex-label.sync="conf.active.sex_label"/>


                <template v-if="rank_set.batch_import && conf.active.enter_types === 4">
                    <view class="form-item">
                        <view class="top color-content">参与活动账号输入提示</view>
                        <view class="bottom font16">
                            <input class="input" placeholder="默认为“请输入账号”"
                                   v-model="conf.active.batch_import_label.username"/>
                        </view>
                    </view>
                    <view class="form-item">
                        <view class="top color-content">参与活动密码输入提示</view>
                        <view class="bottom font16">
                            <input class="input" placeholder="默认为“请输入密码”"
                                   v-model="conf.active.batch_import_label.password"/>
                        </view>
                    </view>
                </template>


                <view class="form-item">
                    <view class="top color-content">报名开始时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="conf.active.submit.begin"
                                end="2038-12-31"
                                :border="false"
                                :clear-icon="true"
                                placeholder="选择报名开始时间"
                            />
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">报名截止时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="conf.active.submit.end"
                                end="2038-12-31"
                                :border="false"
                                :clear-icon="true"
                                placeholder="选择报名截止时间"
                            />
                        </view>
                    </view>
                </view>

                <view v-if="rank_set.team_group_open" class="form-item">
                    <view class="top color-content">报名时队伍是否必选</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker class="view" :range="['非必选', '必选']" :value="conf.active.team_required"
                                    @change="conf.active.team_required = Number($event.detail.value)">
                                {{ conf.active.team_required === 1 ? '' : '非' }}必选
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>


                <template v-if="rank_set.active_details_notice">
                    <view class="form-item">
                        <view class="top color-content">
                            <view>是否开启活动阅读须知</view>
                            <view class="color-sub font14">开启后，用户需要阅读并同意才能进入活动</view>
                        </view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="form_options.active_details_notice_open_opt"
                                    :value="form_options.active_details_notice_open_opt.findIndex(v => v.value === conf.active.active_details_notice.open)"
                                    range-key="title"
                                    @change="conf.active.active_details_notice.open = form_options.active_details_notice_open_opt[$event.detail.value].value"
                                >
                                    {{ form_options.active_details_notice_open_opt.find(v => v.value === conf.active.active_details_notice.open).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>
                    
                    <view v-if="conf.active.active_details_notice.open" class="form-item">
                        <view class="top color-content">阅读须知文章配置</view>

                        <view class="bottom">
                            <notice-list-set :list.sync="conf.active.active_details_notice.notice_list"/>
                        </view>
                    </view>
                </template>

            </view>


            <view v-show="type_id === 4">
                <view class="form-item">
                    <view class="top color-content">
                        <text>积分单位名称</text>
                        <text class="font12 color-sub pl5">(默认: {{ default_integral_unit }})</text>
                    </view>
                    <view class="bottom font16">

                        <input class="input" type="text" v-model="conf.active.integral.unit"
                               :placeholder="'不填默认为 ' + default_integral_unit"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">过关方式</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="['按任务数过关', `按${integralUnit}数过关`]"
                                :value="conf.active.job_set.types - 1"
                                @change="conf.active.job_set.types = Number($event.detail.value) + 1"
                            >
                                <template v-if="conf.active.job_set.types === 1">按任务数过关</template>
                                <template v-if="conf.active.job_set.types === 2">
                                    按{{ integralUnit }}数过关
                                </template>
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>


                <view v-if="conf.active.job_set.types === 1" class="form-item">
                    <view class="top color-content">
                        <text>完成多少个任务可以闯关成功</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="conf.active.job_set.must_finished_num"
                               placeholder="最小1, 最大不能超过设置的任务数量"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">每天任务开始时间</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <view class="view">
                                <picker-time :time.sync="conf.active.job_set.begin_time" 
                                             :text="conf.active.job_set.begin_time"/>
                            </view>
                            <view class="flex-all-center">
                                <view v-if="conf.active.job_set.begin_time !== '00:00:00'" 
                                      class="color-sub font12" style="width: 90px;" 
                                      @click.stop="conf.active.job_set.begin_time = '00:00:00'">
                                    设为 00:00:00
                                </view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">每天任务结束时间</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <view class="view">
                                <picker-time :time.sync="conf.active.job_set.end_time"
                                             :text="conf.active.job_set.end_time"/>
                            </view>
                            <view class="flex-all-center">
                                <view v-if="conf.active.job_set.end_time !== '23:59:59'" 
                                      class="color-sub font12" style="width: 90px;" 
                                      @click.stop="conf.active.job_set.end_time = '23:59:59'">
                                    设为 23:59:59
                                </view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top flex-kai">
                        <view class="color-content">任务设置</view>
                        <view v-if="conf.active.job_set.job_list.length" class="color-sub font14"
                              @click="clearJobList">清空任务
                        </view>
                    </view>

                    <view class="bottom font16">
                        <view class="ai-sport-list">
                            <view class="ai-sport-item"
                                  v-for="(item, index) in conf.active.job_set.job_list" :key="item.types">
                                <view class="item-controls flex-row">
                                    <view class="p5" @click="jobListSort('up', index)">
                                        <text class="iconfont icon-to-top font18"
                                              :class="index === 0 ? 'color-disabled' : 'color-sub'"></text>
                                    </view>
                                    <view class="p5" @click="jobListSort('down', index)">
                                        <text class="iconfont icon-to-down font18"
                                              :class="index === conf.active.job_set.job_list.length - 1 ? 'color-disabled' : 'color-sub'"></text>
                                    </view>
                                    <view class="p5" @click="editTaskItem(index)">
                                        <text class="iconfont icon-edit color-sub font18"></text>
                                    </view>
                                    <view class="p5" @click="deleteTaskItem(index)">
                                        <text class="iconfont icon-delete color-sub font18"></text>
                                    </view>
                                </view>

                                <view class="flex-row" style="padding-top: 20px;">
                                    <view class="task-item-logo">
                                        <image v-if="item.logo" class="task-item-logo-image"
                                               :src="item.logo" mode="aspectFill"/>
                                        <view v-else class="task-item-logo-image flex-all-center">
                                            <uni-icons type="image" color="#bbbec4" size="60"/>
                                        </view>
                                    </view>
                                    <view class="item-info">
                                        <view class="flex-row">
                                            <view class="item-info-label">任务类型:</view>
                                            <view>{{ getTaskTypesName(item.types) }}</view>
                                        </view>
                                        <view class="flex-row">
                                            <view class="item-info-label">任务名称:</view>
                                            <view>{{ item.title }}</view>
                                        </view>
                                        <view v-if="item.text" class="flex-row">
                                            <view class="item-info-label">任务描述:</view>
                                            <view>{{ item.text }}</view>
                                        </view>
                                        <view class="flex-row">
                                            <view class="item-info-label">奖励设置:</view>
                                            <view>{{ getTaskRewardDescription(item) }}</view>
                                        </view>
                                        <view
                                            v-if="conf.active.job_set.types === 2 && item.types === 1 && item.max"
                                            class="flex-row">
                                            <view class="item-info-label">上限设置:</view>
                                            <view>每日上限{{ item.max }}步</view>
                                        </view>
                                        <view v-if="need_count_types.includes(item.types)"
                                              class="flex-row">
                                            <view class="item-info-label">每关可完成任务次数:</view>
                                            <view>
                                                <template v-if="item.types === 2">以考卷设置为准</template>
                                                <template v-else>{{ item.count }}次</template>
                                            </view>
                                        </view>
                                        <view v-if="need_max_count_types.includes(item.types)"
                                              class="flex-row">
                                            <view class="item-info-label">数量上限:</view>
                                            <view>{{ item.max_count || '不限制' }}</view>
                                        </view>
                                        <view v-if="need_seconds_types.includes(item.types)" class="flex-row">
                                            <view class="item-info-label">时间上限:</view>
                                            <view>{{ item.seconds ? (item.seconds + '秒') : '不限制' }}</view>
                                        </view>
                                    </view>
                                </view>

                            </view>
                        </view>

                        <view v-if="conf.active.job_set.job_list.length < all_job_type_count"
                              class="flex-all-center pb5">
                            <view class="p5 color-light-primary font14" @click="editTaskItem(null)">
                                添加任务
                            </view>
                        </view>
                    </view>
                </view>


                <uni-popup ref="taskItemEditPopup" @touchmove.stop.prevent="">
                    <view class="bg-white ai-sport-item-edit">
                        <view class="text-center color-title font14 p10">任务设置</view>


                        <scroll-view :scroll-y="true" style="max-height: 70vh;">
                            <view class="flex-all-center">
                                <view class="task-item-logo">
                                    <view @click="changeImage('task_logo')">
                                        <image v-if="edit_task_data.logo" class="task-item-logo-image"
                                               :src="edit_task_data.logo" mode="aspectFill"/>
                                        <view v-else class="task-item-logo-image flex-all-center">
                                            <uni-icons type="image" color="#bbbec4" size="60"/>
                                        </view>
                                    </view>
                                    <view v-if="edit_task_data.logo" class="flex-all-center delete-task-logo"
                                          @click="edit_task_data.logo = ''">
                                        <uni-icons type="closeempty" color="#e20f04"/>
                                    </view>
                                </view>
                            </view>
                            <view class="flex-all-center pt5">
                                <view class="color-light-primary font14" @click="changeImage('task_logo')">
                                    {{ edit_task_data.logo ? '修改' : '设置' }}任务logo图
                                </view>
                            </view>

                            <uni-forms label-position="top" label-width="200" border="true">
                                <uni-forms-item label="任务类型" required="true">
                                    <view class="forms-picker flex-kai" @click="changeTaskTypes">
                                        <view style="width: 100%;">
                                            <text v-if="edit_task_data.types" style="color: #333;">
                                                {{ getTaskTypesName(edit_task_data.types) }}
                                            </text>
                                            <text v-else class="color-disabled">请选择任务类型</text>
                                        </view>
                                        <view>
                                            <uni-icons type="forward" size="16" color="#dddee1"/>
                                        </view>
                                    </view>
                                </uni-forms-item>

                                <uni-forms-item label="任务名称" required="true">
                                    <uni-easyinput v-model="edit_task_data.title" maxlength="20"/>
                                </uni-forms-item>

                                <uni-forms-item v-if="needSetCountTask" required="true" label="每关任务次数">
                                    <uni-easyinput type="number" v-model="edit_task_data.count" maxlength="3"/>
                                </uni-forms-item>

                                <uni-forms-item label="任务描述">
                                    <uni-easyinput v-model="edit_task_data.text" maxlength="100"/>
                                </uni-forms-item>

                                <uni-forms-item label="任务未完成按钮文字">
                                    <uni-easyinput v-model="edit_task_data.not_complete_button_text"
                                                   maxlength="5" placeholder="不填默认为“去完成”"/>
                                </uni-forms-item>

                                <uni-forms-item label="任务完成按钮文字">
                                    <uni-easyinput v-model="edit_task_data.complete_button_text"
                                                   maxlength="5" placeholder="不填默认为“已完成”"/>
                                </uni-forms-item>

                                <uni-forms-item v-if="edit_task_data.types === 9" label="奖励方式">

                                    <picker :range="[{type: 'integral', title: '全部答对奖励'}, {type: 'per_integral', title: '每答对一题奖励'}]"
                                            range-key="title"
                                            :value="edit_task_data.integral_type === 'integral' ? 0 : 1"
                                            @change="edit_task_data.integral_type = Number($event.detail.value) === 1 ? 'per_integral' : 'integral'">
                                        <view class="forms-picker flex-kai">
                                            <view style="width: 100%; color: #333;">
                                                <template v-if="edit_task_data.integral_type === 'per_integral'">每答对一题奖励</template>
                                                <template v-else>全部答对奖励</template>
                                            </view>
                                            <view>
                                                <uni-icons type="forward" size="16" color="#dddee1"/>
                                            </view>
                                        </view>
                                    </picker>
                                </uni-forms-item>

                                <uni-forms-item v-if="edit_task_data.types === 12" label="奖励方式">

                                    <picker :range="[{type: 'integral', title: '完成任务奖励'}, {type: 'per_integral', title: '每接一个奖励'}]"
                                            range-key="title"
                                            :value="edit_task_data.integral_type === 'integral' ? 0 : 1"
                                            @change="edit_task_data.integral_type = Number($event.detail.value) === 1 ? 'per_integral' : 'integral'">
                                        <view class="forms-picker flex-kai">
                                            <view style="width: 100%; color: #333;">
                                                <template v-if="edit_task_data.integral_type === 'per_integral'">每接一个奖励</template>
                                                <template v-else>完成任务奖励</template>
                                            </view>
                                            <view>
                                                <uni-icons type="forward" size="16" color="#dddee1"/>
                                            </view>
                                        </view>
                                    </picker>
                                </uni-forms-item>

                                <uni-forms-item v-if="edit_task_data.types === 18" label="奖励方式">

                                    <picker :range="[{type: 'integral', title: '全部答对奖励'}, {type: 'per_integral', title: '每答对一题奖励'}]"
                                            range-key="title"
                                            :value="edit_task_data.integral_type === 'integral' ? 0 : 1"
                                            @change="edit_task_data.integral_type = Number($event.detail.value) === 1 ? 'per_integral' : 'integral'">
                                        <view class="forms-picker flex-kai">
                                            <view style="width: 100%; color: #333;">
                                                <template v-if="edit_task_data.integral_type === 'per_integral'">每答对一题奖励</template>
                                                <template v-else>全部答对奖励</template>
                                            </view>
                                            <view>
                                                <uni-icons type="forward" size="16" color="#dddee1"/>
                                            </view>
                                        </view>
                                    </picker>
                                </uni-forms-item>
                                
                                <uni-forms-item v-if="needSetIntegralTask" label="奖励设置" required="true">
                                    <view v-if="edit_task_data.types" class="flex-row">
                                        <template v-if="edit_task_data.types === 1">
                                            <template v-if="conf.active.job_set.types === 1">
                                                <view class="input-word">步数达到</view>
                                                <view class="input-container">
                                                    <uni-easyinput v-model="edit_task_data.min" type="number"
                                                                   maxlength="8"/>
                                                </view>
                                                <view class="input-word">步奖励</view>
<!--                                                <view class="input-container">
                                                    <uni-easyinput v-model="edit_task_data.integral"
                                                                   type="number" maxlength="8"/>
                                                </view>-->
                                                <view class="input-word">1{{ integralUnit }}</view>
                                            </template>
                                            <template v-else-if="conf.active.job_set.types === 2">
                                                <view class="input-word">每</view>
                                                <view class="input-container">
                                                    <uni-easyinput v-model="edit_task_data.min" type="number"
                                                                   maxlength="8"/>
                                                </view>
                                                <view class="input-word">步奖励1{{ integralUnit }}</view>
                                            </template>
                                        </template>

                                        <template v-else-if="edit_task_data.types === 3">
                                            <view class="input-word">发布1条运动圈动态奖励</view>
                                            <view class="input-container">
                                                <uni-easyinput v-model="edit_task_data.integral"
                                                               type="digit" maxlength="8"/>
                                            </view>
                                            <view class="input-word">{{ integralUnit }}</view>
                                        </template>

                                        <template v-else-if="edit_task_data.types === 9">
                                            <template v-if="edit_task_data.integral_type === 'integral'">
                                                <view class="input-word">完成任务奖励</view>
                                                <view class="input-container">
                                                    <uni-easyinput v-model="edit_task_data.integral"
                                                                   type="digit" maxlength="8"/>
                                                </view>
                                                <view class="input-word">{{ integralUnit }}</view>
                                            </template>
                                            <template v-else>
                                                <view class="input-word">每答对一题奖励</view>
                                                <view class="input-container">
                                                    <uni-easyinput v-model="edit_task_data.per_integral"
                                                                   type="digit" maxlength="8"/>
                                                </view>
                                                <view class="input-word">{{ integralUnit }}</view>
                                            </template>
                                        </template>

                                        <template v-else-if="edit_task_data.types === 12">
                                            <template v-if="edit_task_data.integral_type === 'integral'">
                                                <view class="input-word">完成任务奖励</view>
                                                <view class="input-container">
                                                    <uni-easyinput v-model="edit_task_data.integral"
                                                                   type="digit" maxlength="8"/>
                                                </view>
                                                <view class="input-word">{{ integralUnit }}</view>
                                            </template>
                                            <template v-else>
                                                <view class="input-word">每接一个奖励</view>
                                                <view class="input-container">
                                                    <uni-easyinput v-model="edit_task_data.per_integral"
                                                                   type="digit" maxlength="8"/>
                                                </view>
                                                <view class="input-word">{{ integralUnit }}</view>
                                            </template>
                                        </template>

                                        <template v-else-if="edit_task_data.types === 15">
                                            <view class="input-word">接龙一次奖励</view>
                                            <view class="input-container">
                                                <uni-easyinput v-model="edit_task_data.per_integral"
                                                               type="digit" maxlength="8"/>
                                            </view>
                                            <view class="input-word">{{ integralUnit }}</view>
                                        </template>

                                        <template v-else-if="edit_task_data.types === 16">
                                            <view class="input-word">打中一只地鼠奖励</view>
                                            <view class="input-container">
                                                <uni-easyinput v-model="edit_task_data.per_integral"
                                                               type="digit" maxlength="8"/>
                                            </view>
                                            <view class="input-word">{{ integralUnit }}</view>
                                        </template>

                                        <template v-else-if="edit_task_data.types === 18">
                                            <template v-if="edit_task_data.integral_type === 'integral'">
                                                <view class="input-word">完成任务奖励</view>
                                                <view class="input-container">
                                                    <uni-easyinput v-model="edit_task_data.integral"
                                                                   type="digit" maxlength="8"/>
                                                </view>
                                                <view class="input-word">{{ integralUnit }}</view>
                                            </template>
                                            <template v-else>
                                                <view class="input-word">每答对一题奖励</view>
                                                <view class="input-container">
                                                    <uni-easyinput v-model="edit_task_data.per_integral"
                                                                   type="digit" maxlength="8"/>
                                                </view>
                                                <view class="input-word">{{ integralUnit }}</view>
                                            </template>
                                        </template>

                                        <template v-else-if="edit_task_data.types === 22">
                                            <view class="input-word">每连一个红包奖励</view>
                                            <view class="input-container">
                                                <uni-easyinput v-model="edit_task_data.per_integral"
                                                               type="digit" maxlength="8"/>
                                            </view>
                                            <view class="input-word">{{ integralUnit }}</view>
                                        </template>

                                        <template v-else-if="edit_task_data.types === 23">
                                            <view class="input-word">每打中一个年兽奖励</view>
                                            <view class="input-container">
                                                <uni-easyinput v-model="edit_task_data.per_integral"
                                                               type="digit" maxlength="8"/>
                                            </view>
                                            <view class="input-word">{{ integralUnit }}</view>
                                        </template>

                                        <template v-else-if="edit_task_data.types === 24">
                                            <view class="input-word">每击中一架飞机奖励</view>
                                            <view class="input-container">
                                                <uni-easyinput v-model="edit_task_data.per_integral"
                                                               type="digit" maxlength="8"/>
                                            </view>
                                            <view class="input-word">{{ integralUnit }}</view>
                                        </template>

                                        <template v-else-if="edit_task_data.types === 26">
                                            <view class="input-word">每消除一组奖励</view>
                                            <view class="input-container">
                                                <uni-easyinput v-model="edit_task_data.per_integral"
                                                               type="digit" maxlength="8"/>
                                            </view>
                                            <view class="input-word">{{ integralUnit }}</view>
                                        </template>

                                        <view class="w-100" v-else-if="edit_task_data.types === 27">
                                            <weight-task-reward-set ref="weightTaskRewardSet"
                                                                    :list.sync="edit_task_data.integral_list"/>
                                        </view>

                                        <template v-else-if="edit_task_data.types === 28">
                                            <view class="input-word">体脂每减少</view>
                                            <view class="input-container">
                                                <uni-easyinput v-model="edit_task_data.num" type="number"
                                                               maxlength="5"/>
                                            </view>
                                            <view class="input-word">%, 奖励</view>
                                            <view class="input-container">
                                                <uni-easyinput v-model="edit_task_data.integral"
                                                               type="number" maxlength="8"/>
                                            </view>
                                            <view class="input-word">{{ integralUnit }}</view>
                                        </template>

                                        <template v-else-if="edit_task_data.types === 29">
                                            <view class="input-word">腰围每减少</view>
                                            <view class="input-container">
                                                <uni-easyinput v-model="edit_task_data.num" type="number"
                                                               maxlength="5"/>
                                            </view>
                                            <view class="input-word">cm, 奖励</view>
                                            <view class="input-container">
                                                <uni-easyinput v-model="edit_task_data.integral"
                                                               type="number" maxlength="8"/>
                                            </view>
                                            <view class="input-word">{{ integralUnit }}</view>
                                        </template>

                                        <template v-else-if="edit_task_data.types === 36">
                                            <view class="input-word">每数1张钱奖励</view>
                                            <view class="input-container">
                                                <uni-easyinput v-model="edit_task_data.per_integral"
                                                               type="digit" maxlength="8"/>
                                            </view>
                                            <view class="input-word">{{ integralUnit }}</view>
                                        </template>

                                        <template v-else-if="edit_task_data.types === 38">
                                            <view class="input-word">每进1球奖励</view>
                                            <view class="input-container">
                                                <uni-easyinput v-model="edit_task_data.per_integral"
                                                            type="digit" maxlength="8"/>
                                            </view>
                                            <view class="input-word">{{ integralUnit }}</view>
                                        </template>

                                        <template v-else-if="edit_task_data.types === 39">
                                            <view class="input-word">每奔跑1米奖励</view>
                                            <view class="input-container">
                                                <uni-easyinput v-model="edit_task_data.per_integral"
                                                            type="digit" maxlength="8"/>
                                            </view>
                                            <view class="input-word">{{ integralUnit }}</view>
                                        </template>

                                        <template v-else-if="edit_task_data.types === 40">
                                            <view class="input-word">每消除1个元素奖励</view>
                                            <view class="input-container">
                                                <uni-easyinput v-model="edit_task_data.per_integral"
                                                            type="digit" maxlength="8"/>
                                            </view>
                                            <view class="input-word">{{ integralUnit }}</view>
                                        </template>

                                        <template v-else-if="edit_task_data.types === 41">
                                            <view class="input-word">每打爆1个气球奖励</view>
                                            <view class="input-container">
                                                <uni-easyinput v-model="edit_task_data.per_integral"
                                                            type="digit" maxlength="8"/>
                                            </view>
                                            <view class="input-word">{{ integralUnit }}</view>
                                        </template>

                                        <template v-else-if="edit_task_data.types === 42">
                                            <view class="input-word">每跳过1个平台奖励</view>
                                            <view class="input-container">
                                                <uni-easyinput v-model="edit_task_data.per_integral"
                                                            type="digit" maxlength="8"/>
                                            </view>
                                            <view class="input-word">{{ integralUnit }}</view>
                                        </template>

                                        <template v-else-if="edit_task_data.types === 45">
                                            <view class="input-word">发布1条动态圈动态奖励</view>
                                            <view class="input-container">
                                                <uni-easyinput v-model="edit_task_data.integral"
                                                               type="digit" maxlength="8"/>
                                            </view>
                                            <view class="input-word">{{ integralUnit }}</view>
                                        </template>

                                        <template v-else>
                                            <view class="input-word">完成任务奖励</view>
                                            <view class="input-container">
                                                <uni-easyinput v-model="edit_task_data.integral"
                                                               type="digit" maxlength="8"/>
                                            </view>
                                            <view class="input-word">{{ integralUnit }}</view>
                                        </template>
                                    </view>
                                    <view v-else class="color-sub font14">请选择任务类型</view>
                                </uni-forms-item>


                                <template v-if="edit_task_data.types === 1">
                                    <uni-forms-item v-if="conf.active.job_set.types === 2"
                                                    label="每日最低兑换步数">
                                        <view class="color-sub font12" style="position: relative; top: -6px;">
                                            每日步数未达到设置步数不可兑换，不限制不填写即可
                                        </view>
                                        <uni-easyinput type="number" v-model="edit_task_data.min_num"
                                                       maxlength="8"/>
                                    </uni-forms-item>


                                    <!--按积分数过关可设置每日步数兑换上限，因为按积分数过关需要按照是否到达上限来判断任务是否已完成-->
                                    <uni-forms-item v-if="conf.active.job_set.types === 2"
                                                    label="每日步数兑换上限">
                                        <view class="color-sub font12" style="position: relative; top: -6px;">
                                            每日兑换步数达到设置上限后不可再继续兑换，不限制不填写即可
                                        </view>
                                        <uni-easyinput type="number" v-model="edit_task_data.max"
                                                       maxlength="8"/>
                                    </uni-forms-item>
                                </template>

                                <uni-forms-item v-if="edit_task_data.types === 15" label="接龙数量上限">
                                    <view class="color-sub font12" style="position: relative; top: -6px;">
                                        超过设置上限后的接龙不获得奖励。
                                    </view>
                                    <uni-easyinput type="number" v-model="edit_task_data.max_count"
                                                   maxlength="8"/>
                                </uni-forms-item>

                                <uni-forms-item v-if="edit_task_data.types === 16" label="打地鼠数量上限(单次)">
                                    <view class="color-sub font12" style="position: relative; top: -6px;">
                                        单次任务中，打中的地鼠数量超过设置上限后，打中的地鼠不获得奖励。(如：设置打中一只地鼠奖励2{{ integralUnit }}，设置数量上限为10，则单次任务奖励的{{ integralUnit }}上限为2x10=20{{ integralUnit }}。)
                                    </view>
                                    <uni-easyinput type="number" v-model="edit_task_data.max_count"
                                                   maxlength="8"/>
                                </uni-forms-item>

                                <uni-forms-item v-if="need_seconds_types.includes(edit_task_data.types)"
                                                label="任务时间限制">
                                    <view class="color-sub font12" style="position: relative; top: -6px;">
                                        规定完成任务的时间，
                                        <template v-if="edit_task_data.types === 15">
                                            超过时间自动提交任务。不设置默认为120秒
                                        </template>
                                        <template v-else-if="edit_task_data.types === 22">时间到后提交任务。不设置不限时</template>
                                        <template v-else>时间到后提交任务。不设置默认为30秒</template>
                                        。(单位: 秒)
                                    </view>
                                    <uni-easyinput type="number" v-model="edit_task_data.seconds"
                                                   maxlength="8"/>
                                </uni-forms-item>

                                <uni-forms-item
                                    v-if="need_max_integral_types.includes(edit_task_data.types)"
                                    :label="`每个关卡${integralUnit}奖励上限`"
                                >
                                    <uni-easyinput type="number" v-model="edit_task_data['max_integral']"
                                                   maxlength="8"/>
                                </uni-forms-item>

                                <uni-forms-item v-if="need_week_count.includes(edit_task_data.types)"
                                                label="每周次数上限">
                                    <uni-easyinput type="number" v-model="edit_task_data['week_count']"
                                                   maxlength="8"/>
                                </uni-forms-item>

                                <uni-forms-item v-if="edit_task_data.types === 32"
                                                label="每周步数需要达到多少步才能奖励">
                                    <uni-easyinput type="number" v-model="edit_task_data['step_num']"
                                                   maxlength="8"/>
                                </uni-forms-item>

                                <uni-forms-item
                                    v-if="edit_task_data.types === 31 || edit_task_data.types === 33 || edit_task_data.types === 34"
                                                label="每周参与人数限制">
                                    <uni-easyinput type="number" v-model="edit_task_data.job_stock"
                                                   maxlength="8"/>
                                </uni-forms-item>
                            </uni-forms>

                        </scroll-view>
                        
                        <view class="ai-sport-item-edit-buttons flex-all-center font14">
                            <view class="bg-background color-sub" hover-class="navigator-hover"
                                  @click="taskItemEditClose">取消
                            </view>
                            <view class="bg-light-primary color-white" hover-class="navigator-hover"
                                  @click="taskItemEditConfirm">确定
                            </view>
                        </view>
                    </view>
                </uni-popup>

                <uni-popup ref="changeTaskTypesPopup">
                    <view class="ai-sport-types-change bg-white">
                        <view class="p10 text-center color-title">选择任务类型</view>
                        <view class="ai-sport-type-list flex-row flex-wrap">
                            <view class="ai-sport-type-item flex-all-center"
                                  :class="{selected: item.selected}"
                                  v-for="item in all_task_list" :key="item.types"
                                  @click="selectTaskTypes(item)">
                                <view>{{ item.name }}</view>
                            </view>
                        </view>
                    </view>
                    <view class="flex-all-center pt5" @click="$refs.changeTaskTypesPopup.close()">
                        <uni-icons type="close" color="#ffffff" size="28"/>
                    </view>
                </uni-popup>


                <view v-show="addedTasks['types_4']" class="form-item">
                    <view class="top flex-kai">
                        <view class="color-content">AI运动设置</view>
                        <view v-if="conf.AI_motion.motion_list.length" class="color-sub font14"
                              @click="clearAiMotionList">清空设置
                        </view>
                    </view>
                    <view class="bottom font16">
                        <view class="ai-sport-list">
                            <view class="ai-sport-item flex-row"
                                  v-for="(item, index) in conf.AI_motion.motion_list" :key="item.types">
                                <view class="item-controls flex-row">
                                    <view class="p5" @click="AiSportListSort('up', index)">
                                        <text class="iconfont icon-to-top font18"
                                              :class="index === 0 ? 'color-disabled' : 'color-sub'"></text>
                                    </view>
                                    <view class="p5" @click="AiSportListSort('down', index)">
                                        <text class="iconfont icon-to-down font18"
                                              :class="index === conf.AI_motion.motion_list.length - 1 ? 'color-disabled' : 'color-sub'"></text>
                                    </view>
                                    <view class="p5" @click="editAiSportItem(index)">
                                        <text class="iconfont icon-edit color-sub font18"></text>
                                    </view>
                                    <view class="p5" @click="deleteAiSportItem(index)">
                                        <text class="iconfont icon-delete color-sub font18"></text>
                                    </view>
                                </view>
                                <view class="ai-sport-item-logo">
                                    <image v-if="item.logo" class="ai-sport-item-logo-image"
                                           :src="item.logo" mode="aspectFill"/>
                                    <view v-else class="ai-sport-item-logo-image flex-all-center">
                                        <uni-icons type="image" color="#bbbec4" size="60"/>
                                    </view>
                                </view>
                                <view class="item-info">
                                    <view class="flex-row">
                                        <view class="item-info-label">运动类型:</view>
                                        <view>{{ getAiSportTypesName(item.types) }}</view>
                                    </view>
                                    <view class="flex-row">
                                        <view class="item-info-label">运动名称:</view>
                                        <view>{{ item.name || getAiSportTypesName(item.types) }}</view>
                                    </view>
                                    <view class="flex-row">
                                        <view class="item-info-label">奖励设置:</view>
                                        <view>{{ getAiSportRewardDescription(item) }}</view>
                                    </view>
                                    <view v-if="item.integral_reward.max_daily" class="flex-row">
                                        <view class="item-info-label">奖励上限:</view>
                                        <view>每日最高奖励{{ item.integral_reward.max_daily }}{{ integralUnit }}1</view>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view v-if="conf.AI_motion.motion_list.length < all_ai_sport_type_count"
                              class="flex-all-center pb5">
                            <view class="p5 color-light-primary font14" @click="editAiSportItem(null)">
                                添加运动类型
                            </view>
                        </view>
                    </view>
                </view>


                <uni-popup ref="aiSportItemEditPopup">
                    <view class="bg-white ai-sport-item-edit">
                        <view class="text-center color-title font14 p10">AI运动设置</view>
                        <view class="flex-all-center">
                            <view class="ai-sport-item-logo">
                                <view @click="changeImage('ai_sport_logo')">
                                    <image v-if="edit_ai_sport_item_data.logo"
                                           class="ai-sport-item-logo-image"
                                           :src="edit_ai_sport_item_data.logo" mode="aspectFill"/>
                                    <view v-else class="ai-sport-item-logo-image flex-all-center">
                                        <uni-icons type="image" color="#bbbec4" size="60"/>
                                    </view>
                                </view>
                                <view v-if="edit_ai_sport_item_data.logo"
                                      class="flex-all-center delete-task-logo"
                                      @click="edit_ai_sport_item_data.logo = ''">
                                    <uni-icons type="closeempty" color="#e20f04"/>
                                </view>
                            </view>
                        </view>
                        <view class="flex-all-center pt5">
                            <view class="color-light-primary font14" @click="changeImage('ai_sport_logo')">
                                {{ edit_ai_sport_item_data.logo ? '修改' : '设置' }}运动logo图
                            </view>
                        </view>

                        <uni-forms label-position="top" label-width="200" border="true">
                            <uni-forms-item label="运动类型" required="true">
                                <view class="forms-picker flex-kai" @click="changeAiSportTypes">
                                    <view style="width: 100%;">
                                        <text v-if="edit_ai_sport_item_data.types" style="color: #333;">
                                            {{ getAiSportTypesName(edit_ai_sport_item_data.types) }}
                                        </text>
                                        <text v-else class="color-disabled">请选择运动类型</text>
                                    </view>
                                    <view>
                                        <uni-icons type="forward" size="16" color="#dddee1"/>
                                    </view>
                                </view>
                            </uni-forms-item>

                            <uni-forms-item label="运动名称">
                                <uni-easyinput v-model="edit_ai_sport_item_data.name" maxlength="20"/>
                            </uni-forms-item>

                            <uni-forms-item label="运动描述">
                                <uni-easyinput v-model="edit_ai_sport_item_data.describe" maxlength="20"/>
                            </uni-forms-item>

                            <uni-forms-item label="奖励设置" required="true">
                                <view v-if="edit_ai_sport_item_data.types" class="flex-row">
                                    <view class="input-word">
                                        {{ getAiSportTypesName(edit_ai_sport_item_data.types) }}
                                    </view>
                                    <view class="input-container">
                                        <uni-easyinput v-model="edit_ai_sport_item_data.integral_reward.num"
                                                       type="number" maxlength="8"/>
                                    </view>
                                    <view class="input-word">
                                        {{ getAiSportTypesNumName(edit_ai_sport_item_data.types) }}
                                    </view>
                                    <view class="input-word">奖励</view>
                                    <view class="input-container">
                                        <uni-easyinput
                                            v-model="edit_ai_sport_item_data.integral_reward.integral"
                                            type="number" maxlength="8"/>
                                    </view>
                                    <view class="input-word">{{ integralUnit }}</view>
                                </view>
                                <view v-else class="color-sub font14">请选择运动类型</view>
                            </uni-forms-item>

                            <uni-forms-item v-if="edit_ai_sport_item_data.types"
                                            :label="`每日奖励${integralUnit}上限`">
                                <view class="color-sub font12" style="position: relative; top: -6px;">
                                    每日奖励{{ integralUnit }}达到设置上限后不再奖励{{ integralUnit }}。不限制不填写即可
                                </view>

                                <uni-easyinput type="number" maxlength="8"
                                               v-model="edit_ai_sport_item_data.integral_reward.max_daily"/>
                            </uni-forms-item>
                            
                        </uni-forms>

                        <view class="ai-sport-item-edit-buttons flex-all-center font14">
                            <view class="bg-background color-sub" hover-class="navigator-hover"
                                  @click="$refs.aiSportItemEditPopup.close()">取消
                            </view>
                            <view class="bg-light-primary color-white" hover-class="navigator-hover"
                                  @click="aiSportItemEditConfirm">确定
                            </view>
                        </view>
                    </view>
                </uni-popup>

                <uni-popup ref="changeAiSportTypesPopup">
                    <view class="ai-sport-types-change bg-white">
                        <view class="p10 text-center color-title">选择运动类型</view>
                        <view class="ai-sport-type-list flex-row flex-wrap">
                            <view class="ai-sport-type-item flex-all-center"
                                  :class="{selected: item.selected}"
                                  v-for="item in ai_sport_types" :key="item.types"
                                  @click="selectAiSportTypes(item)">
                                <view>{{ item.name }}</view>
                            </view>
                        </view>
                    </view>
                    <view class="flex-all-center pt5" @click="$refs.changeAiSportTypesPopup.close()">
                        <uni-icons type="close" color="#ffffff" size="28"/>
                    </view>
                </uni-popup>

                <template v-if="addedTasks['types_4']">
                    <view class="form-item">
                        <view class="top color-content">是否合并AI运动任务</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="['否', '是']"
                                    :value="conf.active.task_active_ai_sport_task_merge"
                                    @change="conf.active.task_active_ai_sport_task_merge = Number($event.detail.value)"
                                >
                                    {{ conf.active.task_active_ai_sport_task_merge ? '是' : '否' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">AI运动完成后是否显示'分享到微信运动'按钮</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker class="view" mode="selector" :range="['显示', '不显示']"
                                        :value="conf.active.we_run_share_button_hide"
                                        @change="conf.active.we_run_share_button_hide = Number($event.detail.value)">
                                    {{ conf.active.we_run_share_button_hide ? '不' : '' }}显示
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>
                </template>


                <template v-if="addedTasks['types_1'] && rank_set.exam_open">
                    <view class="form-item">
                        <view class="top color-content">运动步数任务是否需要答题</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker class="view" :range="['不需要', '需要']"
                                        :value="conf.active.exchange_step_answer.open"
                                        @change="conf.active.exchange_step_answer.open = Number($event.detail.value)">
                                    {{ conf.active.exchange_step_answer.open ? '' : '不' }}需要
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <template v-if="conf.active.exchange_step_answer.open">
                        <view class="form-item">
                            <view class="top color-content">运动步数任务答题考卷绑定</view>
                            <view class="bottom font16">
                                <view class="flex-kai" @click="exchangeStepAnswerBind">
                                    <view class="view">
                                        <view v-if="conf.active.exchange_step_answer.exam.id">
                                            {{ conf.active.exchange_step_answer.exam.name || conf.active.exchange_step_answer.exam.id }}
                                        </view>
                                        <view v-else class="color-sub">绑定考卷</view>
                                    </view>
                                    <view class="flex-all-center">
                                        <view v-if="conf.active.exchange_step_answer.exam.id"
                                              class="color-sub font12" style="width: 30px;"
                                              @click.stop="exchangeStepAnswerDeBind">
                                            解绑
                                        </view>
                                        <uni-icons type="forward" color="#80848f"/>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">
                                <view>运动步数任务答题最低分数</view>
                                <view class="color-sub font12">需要达到设置的分数才能完成任务</view>
                            </view>
                            <view class="bottom font16">
                                <input class="input" type="digit" placeholder="不填默认为0"
                                       v-model="conf.active.exchange_step_answer.min_score"/>
                            </view>
                        </view>
                    </template>
                </template>

                <view v-show="addedTasks['types_2']" class="form-item">
                    <view class="top color-content">
                        <view>答题任务最低分数</view>
                        <view class="color-sub font12">
                            需要达到设置的分数才能完成任务和奖励{{ integralUnit }}
                        </view>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="conf.active.integral.exam_min_score"
                               placeholder="不填默认为0"/>
                    </view>
                </view>

                <view v-show="addedTasks['types_2']" class="form-item">
                    <view class="top color-content">答题任务完成方式</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                :range="examJobCompleteTypesOptions"
                                range-key="title"
                                :value="examJobCompleteTypesPickerData.value"
                                @change="conf.active.exam_job_complete_types = examJobCompleteTypesOptions[$event.detail.value].types"
                            >
                                {{ examJobCompleteTypesPickerData.title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-show="addedTasks['types_2']" class="form-item">
                    <view class="top color-content">答题奖励{{ integralUnit }}规则</view>
                    <view class="bottom font16">
                        <view class="flex-row ptm5">
                            <view class="input-word">每获得</view>
                            <view class="input-container">
                                <uni-easyinput
                                    v-model="conf.active.integral.exam_reward"
                                    type="number" maxlength="3"/>
                            </view>
                            <view class="input-word">分奖励</view>
                            <view class="input-container">
                                <uni-easyinput
                                    v-model="conf.active.integral.exam_reward_num"
                                    type="number" maxlength="8"/>
                            </view>
                            <view class="input-word">{{ integralUnit }}</view>
                        </view>
                    </view>
                </view>
                
                
                <view v-show="addedTasks['types_12']">
                    <view class="form-item">
                        <view class="top color-content">
                            <text>抢粮食任务时间限制</text>
                            <text class="color-sub font12 pl5">(单位: 秒)</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="number" maxlength="5" placeholder="不填默认为60秒"
                                   v-model="conf.active.grab_grain_game_set.seconds"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">抢粮食任务完成的粮食数</view>
                        <view class="bottom font16">
                            <input class="input" type="number" maxlength="5" placeholder="不填默认为20"
                                   v-model="conf.active.grab_grain_game_set.target_grain"/>
                        </view>
                    </view>
                </view>

                <template v-if="addedTasks['types_15']">
                    <view class="form-item">
                        <view class="top color-content">
                            <view>成语接龙是否允许接同音字</view>
                            <view class="color-sub font14">如: "一心一意"可接"易如反掌"、"异想天开"。</view>
                        </view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="['不允许', '允许']"
                                    :value="conf.active.idiom_unison_open"
                                    @change="conf.active.idiom_unison_open = Number($event.detail.value)"
                                >
                                    {{ conf.active.idiom_unison_open ? '' : '不'}}允许
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <view>成语接龙提示次数</view>
                            <view class="color-sub font14">
                                如果用户无法想到接龙的成语，可以使用提示功能，系统会提供一个可接龙的成语作为提示。
                            </view>
                            <view class="color-sub font14">
                                请设置每次游戏的最大提示次数，未设置或设置为0时则不提供提示。
                            </view>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="number" maxlength="2" placeholder="不填默认为没有提示次数"
                                   v-model="conf.active.idiom_tips_count"/>
                        </view>
                    </view>
                </template>


                <view v-show="addedTasks['types_5']" class="form-item">
                    <view class="top flex-kai">
                        <view class="color-content">阅读奖励规则设置</view>
                        <view v-if="conf.active.reading_reward.rules" class="color-sub font14"
                              @click="clearReadRewardRule">清空设置
                        </view>
                    </view>
                    <view class="bottom font16">
                        <view class="reading-reward-rules">
                            <view class="reading-reward-rule ai-sport-item"
                                  v-for="(item, index) in conf.active.reading_reward.rules" :key="index">
                                <view class="item-controls flex-row">
                                    <view class="p5" @click="editReadRewardRule(index)">
                                        <text class="iconfont icon-edit color-sub font18"></text>
                                    </view>
                                    <view class="p5" @click="deleteReadRewardRule(index)">
                                        <text class="iconfont icon-delete color-sub font18"></text>
                                    </view>
                                </view>

                                <view>文章分类: {{ item.name || item.category_id }}</view>
                                <view>奖励规则: 阅读{{ item.seconds }}秒奖励{{ item.integral.min === item.integral.max ? item.integral.min : `${item.integral.min}到${item.integral.max}` }}{{ integralUnit }}</view>

                            </view>
                        </view>

                        <view class="flex-all-center pb5">
                            <view class="p5 color-light-primary font14" @click="editReadRewardRule(null)">
                                添加奖励规则
                            </view>
                        </view>
                    </view>
                </view>

                <uni-popup ref="readRewardEditPopup">
                    <view class="bg-white ai-sport-item-edit">
                        <view class="text-center color-title font14 p10">阅读奖励设置</view>

                        <uni-forms label-position="top" label-width="200" border="true">
                            <uni-forms-item label="文章分类" required="true">
                                <view class="forms-picker flex-kai" @click="changeNewsCategory">
                                    <view style="width: 100%;">
                                        <text v-if="read_reward_item_data.category_id" style="color: #333;">
                                            {{ read_reward_item_data.name ||
                                            read_reward_item_data.category_id }}
                                        </text>
                                        <text v-else class="color-disabled">请选择需要奖励的文章分类</text>
                                    </view>
                                    <view>
                                        <uni-icons type="forward" size="16" color="#dddee1"/>
                                    </view>
                                </view>
                            </uni-forms-item>

                            <uni-forms-item label="阅读时间 (秒)" required="true">
                                <uni-easyinput type="number" maxlength="5"
                                               v-model="read_reward_item_data.seconds"/>
                            </uni-forms-item>

                            <uni-forms-item :label="`奖励${integralUnit}`" required="true">
                                <view class="font12 color-sub" style="position:relative; top: -8px;">
                                    如需固定奖励{{ integralUnit }}, 两个输入框填写相同{{ integralUnit }}数。
                                </view>
                                <view class="flex-row">
                                    <view class="input-word">奖励</view>
                                    <view class="input-container">
                                        <uni-easyinput v-model="read_reward_item_data.integral.min"
                                                       type="number" maxlength="8"/>
                                    </view>
                                    <view class="input-word pl5 pr5">-</view>
                                    <view class="input-container">
                                        <uni-easyinput v-model="read_reward_item_data.integral.max"
                                                       type="number" maxlength="8"/>
                                    </view>
                                    <view class="input-word">{{ integralUnit }}</view>
                                </view>
                            </uni-forms-item>
                        </uni-forms>

                        <view class="ai-sport-item-edit-buttons flex-all-center font14">
                            <view class="bg-background color-sub" hover-class="navigator-hover"
                                  @click="$refs.readRewardEditPopup.close()">取消
                            </view>
                            <view class="bg-light-primary color-white" hover-class="navigator-hover"
                                  @click="readRewardEditConfirm">确定
                            </view>
                        </view>
                    </view>
                </uni-popup>


                <view v-show="addedTasks['types_5']" class="form-item">
                    <view class="top color-content">同一篇文章是否重复奖励</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="['不重复奖励', '重复奖励']"
                                :value="conf.active.reading_reward.can_repeat_reading"
                                @change="conf.active.reading_reward.can_repeat_reading = Number($event.detail.value)"
                            >
                                {{ conf.active.reading_reward.can_repeat_reading ? '重复奖励' : '不重复奖励' }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>


                <view v-show="addedTasks['types_6']">
                    <view class="form-item">
                        <view class="top color-content">每日签到类型设置</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    mode="selector"
                                    :range="dailySignSetOptions"
                                    :value="dailySignSetOptions.findIndex(v => v.value === conf.active.daily_sign.types)"
                                    range-key="title"
                                    @change="conf.active.daily_sign.types = dailySignSetOptions[$event.detail.value].value"
                                >
                                    {{ dailySignSetOptions.find(v => v.value === conf.active.daily_sign.types).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-show="conf.active.daily_sign.types === 1" class="form-item">
                        <view class="top color-content">
                            <text>每日签到奖励{{ integralUnit }}数</text>
                        </view>
                        <view class="bottom font16">
                            <input
                                class="input"
                                type="number"
                                v-model="conf.active.daily_sign.integral"
                                maxlength="8"
                            />
                        </view>
                    </view>

                    <view v-show="conf.active.daily_sign.types === 2" class="form-item">
                        <view class="top color-content">
                            <text>周期签到奖励{{ integralUnit }}数</text>
                        </view>
                        <view class="bottom font16">
                            <view
                                class="p5 mb5 flex-row"
                                v-for="(item, index) in conf.active.daily_sign.circle_set"
                                :key="index"
                                style="border: 1px solid #eee; border-radius: 5px;"
                            >
                                <view class="flex-row pr5">
                                    <view class="pr5 font14 color-content" style="line-height: 36px;">
                                        第{{ index + 1 }}天奖励:
                                    </view>
                                    <uni-easyinput v-model="item.integral" type="number" :maxlength="8"
                                                   style="width: 120px;"/>
                                    <view class="pl5 font14 color-content" style="line-height: 36px;">
                                        {{ integralUnit }}
                                    </view>
                                </view>
                                <view
                                    v-if="conf.active.daily_sign.circle_set.length > 1"
                                    class="font14 color-error"
                                    style="line-height: 36px;"
                                    @click="conf.active.daily_sign.circle_set.splice(index, 1)"
                                >删除</view>
                            </view>

                            <view
                                v-if="conf.active.daily_sign.circle_set.length < 7"
                                class="color-primary font14 pb5"
                                style="display: inline-block;"
                                @click="conf.active.daily_sign.circle_set.push({integral: ''})"
                            >添加</view>
                        </view>
                    </view>
                </view>

                <view v-if="addedTasks['types_18']" class="form-item">
                    <view class="top color-content">
                        <text>垃圾分类游戏获取的垃圾数量</text>
                        <text class="color-sub font12 pl5">(默认10)</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="conf.active.garbage_count"
                               placeholder="请输入垃圾数量"/>
                    </view>
                </view>

                <view v-if="addedTasks['types_20']" class="form-item">
                    <view class="top color-content">
                        <view>转发奖励文章设置</view>
                        <view class="font12 color-sub">设置转发奖励的文章</view>
                    </view>
                    <view class="bottom font16">
                        <view class="flex-kai" @click="toSelNews('share_reward_news')">
                            <view class="view">
                                <view v-if="conf.active.share_reward_news.id">
                                    {{ conf.active.share_reward_news.title || conf.active.share_reward_news.id }}
                                </view>
                                <view v-else class="color-sub">选择文章</view>
                            </view>
                            <view class="flex-all-center">
                                <view
                                    v-if="conf.active.news.news_id"
                                    class="color-sub font12"
                                    style="width: 30px;"
                                    @click.stop="deleteNews('share_reward_news')"
                                >解绑
                                </view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="addedTasks['types_22']" class="form-item">
                    <view class="top color-content">连红包任务设置</view>
                    <view class="bottom font16">
                        <one-stroke-line-set :set.sync="conf.active.one_stroke_line"/>
                    </view>
                </view>

                <view v-if="addedTasks['types_25']" class="form-item">
                    <view class="top color-content">
                        <text>翻翻乐任务记牌时间</text>
                        <text class="color-sub font14 pl5">(单位: 秒)</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="conf.active.fan_fan_le_memory_time"
                               placeholder="不设置默认5秒"/>
                    </view>
                </view>


                <tongue-twister-text-list-set
                    v-if="addedTasks['types_37']"
                    :set.sync="conf.active.tongue_twister"
                />



                <view v-if="addedTasks['types_31'] || addedTasks['types_32'] || addedTasks['types_33'] || addedTasks['types_34']" class="form-item">
                    <view class="top color-content">每周运动任务活动期间内{{ integralUnit }}奖励上限</view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="types_31_32_33_34_all_types_max"
                               :placeholder="`请输入${integralUnit}奖励上限`"/>
                    </view>
                </view>


                <template v-if="addedTasks['types_31'] || addedTasks['types_33'] || addedTasks['types_34']">
                    <view class="form-item">
                        <view class="top color-content">每周运动任务是否显示打卡广场入口</view>
                        <view class="bottom font16">
                            <view class="flex-kai">
                                <picker class="view" :range="['显示', '不显示']"
                                        :value="conf.active.week_sport_task_hide_sign_list_enter"
                                        @change="conf.active.week_sport_task_hide_sign_list_enter = Number($event.detail.value)">
                                    {{ conf.active.week_sport_task_hide_sign_list_enter ? '不' : '' }}显示
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <week-sport-task-news-bind v-if="addedTasks['types_31']" :news.sync="conf.active.types_31_news"
                        :title="addedTasks['types_31']"/>
                    <week-sport-task-news-bind v-if="addedTasks['types_33']" :news.sync="conf.active.types_33_news"
                        :title="addedTasks['types_33']"/>
                    <week-sport-task-news-bind v-if="addedTasks['types_34']" :news.sync="conf.active.types_34_news"
                        :title="addedTasks['types_34']"/>
                </template>

                <template v-if="bodyTaskNames.length >= 2">
                    <view class="form-item">
                        <view class="top color-content">是否合并显示{{ bodyTaskNames.join('、') }}任务</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker class="view" :range="['不合并', '合并']"
                                        :value="conf.active.body_data_submit_task_merge_set.merge"
                                        @change="conf.active.body_data_submit_task_merge_set.merge = Number($event.detail.value)">
                                    {{ conf.active.body_data_submit_task_merge_set.merge ? '' : '不' }}合并
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-show="conf.active.body_data_submit_task_merge_set.merge" class="form-item">
                        <view class="top color-content">{{ bodyTaskNames.join('、') }}任务显示设置</view>
                        <view class="bottom font16">
                            <task-list-show-shop-set :active-id="form_data.active_id" :button-set="false"
                                                     :set.sync="conf.active.body_data_submit_task_merge_set.set"/>
                        </view>
                    </view>
                </template>

                <template v-if="bodyTaskNames.length">
                    <view class="form-item">
                        <view class="top color-content">
                            <view>是否开启首次{{ bodyTaskNames.join('、') }}任务填写提示</view>
                            <view class="font12 color-sub">
                                用户未填写首次{{ bodyTaskNames.join('、') }}，每次查看任务时会提示用户填写。
                            </view>
                        </view>
                        <view class="bottom font16">
                            <body-data-submit-task-tips :set.sync="conf.active.body_data_submit_task_first_tips"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <view>是否开启最后一次{{ bodyTaskNames.join('、') }}任务填写提示</view>
                            <view class="font12 color-sub">
                                开启提示后，活动结束前，用户未填写最后一次{{ bodyTaskNames.join('、') }}，每次查看任务时会提示用户填写。用户填写首次{{ bodyTaskNames.join('、') }}后，任务状态会变成[已完成]，等到活动结束前多少天(天数由管理员设置)，任务状态变成[未完成]，用户可录入最后一次{{ bodyTaskNames.join('、') }}。
                            </view>
                        </view>
                        <view class="bottom font16">
                            <body-data-submit-task-tips :set.sync="conf.active.body_data_submit_task_last_tips" :is-set-day="true"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            {{ bodyTaskNames.join('、') }}任务填写方式
                        </view>
                        <view class="bottom font16">
                            <view class="flex-kai">
                                <picker class="view" :range="['用户自行填写', '管理员填写']"
                                        :value="conf.active.admin_submit_body_data"
                                        @change="conf.active.admin_submit_body_data = Number($event.detail.value)">
                                    {{ conf.active.admin_submit_body_data ? '管理员填写' : '用户自行填写' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>
                </template>
            </view>
            
            
            <view v-show="type_id === 5">
                <view class="form-item">
                    <view class="top color-content">活动右侧图标设置</view>
                    <view class="bottom">
                        <icons-set-list :list.sync="conf.active.task_active_icons_set"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">开始闯关按钮设置</view>
                    <view class="bottom">
                        <start-challenge-button-set :set.sync="conf.active.start_challenge_button_set"/>
                    </view>
                </view>

                 <view v-if="rank_set['export_top_rank_excel']" class="form-item">
                    <view class="top color-content">任务列表颜色设置</view>
                    <view class="bottom">
                        <task-popup-style-set :set.sync="conf.active.task_popup_style"/>
                    </view>
                </view>


                <template v-if="rank_set.gift_goods">
                    <view class="form-item">
                        <view class="top color-content">任务列表是否显示{{ integralUnit }}商城入口</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker class="view" :range="['隐藏', '显示']"
                                        :value="conf.active.task_list_show_shop.show"
                                        @change="conf.active.task_list_show_shop.show = Number($event.detail.value)">
                                    {{ conf.active.task_list_show_shop.show ? '显示' : '隐藏' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-show="conf.active.task_list_show_shop.show" class="form-item">
                        <view class="top color-content">任务列表{{ integralUnit }}商城入口显示设置</view>
                        <view class="bottom font16">
                            <task-list-show-shop-set :active-id="form_data.active_id"
                                                     :set.sync="conf.active.task_list_show_shop.set"/>
                        </view>
                    </view>
                </template>

                <view v-if="rank_set.gift_goods" class="form-item">
                    <view class="top color-content">用户报名信息是否显示剩余{{ integralUnit }}</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="['显示', '隐藏']"
                                :value="conf.active.integral_left_hide"
                                @change="conf.active.integral_left_hide = Number($event.detail.value)"
                            >
                                {{ conf.active.integral_left_hide ? '隐藏' : '显示' }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="rank_set.open_mp3" class="form-item">
                    <view class="top color-content">
                        <text>背景音乐</text>
                        <text class="color-sub font12 pl5">(格式: m4a、wav、mp3、aac)</text>
                    </view>
                    <view class="bottom font16">
                        <textarea class="textarea" maxlength="-1" auto-height="true"
                                  v-model="conf.active.audio_src" placeholder="进入活动会自动播放背景音乐"/>
                    </view>
                </view>

                <template v-if="addedTasks['types_3'] || addedTasks['types_45']">
                    <template v-if="addedTasks['types_3']">
                        <view class="form-item">
                            <view class="top color-content">运动圈名称</view>
                            <view class="bottom font16">
                                <input class="input"
                                       v-model="conf.active.sport_moment_name"
                                       placeholder="默认为“运动圈”"/>
                            </view>
                        </view>

                        <view class="form-item">
                            <view class="top color-content">运动圈内容输入提示语</view>
                            <view class="bottom font16">
                                <input class="input" v-model="conf.active.sport_moment_input_placeholder"
                                       placeholder="不填显示系统默认提示语"/>
                            </view>
                        </view>
                    </template>

                    <view class="form-item">
                        <view class="top color-content">
                            <template>是否允许用户删除已发布的</template>
                            <template>
                                <template v-if="addedTasks['types_3'] && addedTasks['types_45']">
                                    运动圈、动态圈
                                </template>
                                <template v-else-if="addedTasks['types_3']">运动圈</template>
                                <template v-else-if="addedTasks['types_45']">动态圈</template>
                            </template>
                        </view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker class="view" :range="['允许用户自己删除', '只能管理员删除']"
                                        :value="conf.active.sport_moment_user_cannot_delete"
                                        @change="conf.active.sport_moment_user_cannot_delete = Number($event.detail.value)"
                                >
                                    {{ conf.active.sport_moment_user_cannot_delete ? '只能管理员删除' : '允许用户自己删除' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <template>
                                <template v-if="addedTasks['types_3'] && addedTasks['types_45']">
                                    运动圈、动态圈
                                </template>
                                <template v-else-if="addedTasks['types_3']">运动圈</template>
                                <template v-else-if="addedTasks['types_45']">动态圈</template>
                            </template>
                            <template>图片限制</template>
                        </view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker class="view" :range="['允许用户拍照和相册选择照片', '只能拍照']"
                                        :value="conf.active.sport_moment_just_take_photo"
                                        @change="conf.active.sport_moment_just_take_photo = Number($event.detail.value)"
                                >
                                    {{ conf.active.sport_moment_just_take_photo ? '只能拍照' : '允许用户拍照和相册选择照片' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <view>
                                <template>
                                    <template v-if="addedTasks['types_3'] && addedTasks['types_45']">
                                        运动圈、动态圈
                                    </template>
                                    <template v-else-if="addedTasks['types_3']">运动圈</template>
                                    <template v-else-if="addedTasks['types_45']">动态圈</template>
                                </template>
                                <template>图片数量限制</template>
                            </view>
                            <view class="font12 color-sub">图片数量必须达到限制数量才能发布。例如：设置为3，则最少需要上传3张图片才能发布。(不填或填0不限制)</view>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="number" v-model="conf.active.sport_moment_min_pic"
                               placeholder="图片最低限制数量"/>
                        </view>
                    </view>
                </template>

                <view v-if="rank_set['lottery_open']" class="form-item">
                    <view class="top color-content">抽奖活动绑定</view>
                    <view class="bottom font16">
                        <view class="flex-kai" v-for="(item, index) in conf.active.task_lottery_list"
                              :key="item.lottery_id">
                            <view class="view">{{ item.title }}</view>
                            <view class="flex-all-center">
                                <view class="color-sub font12" style="width: 30px;"
                                      @click="deBindTaskLottery(index)">解绑
                                </view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>

                        <view class="flex-all-center">
                            <view class="p10 font14 color-light-primary" @click="bindTaskLottery">
                                绑定抽奖活动
                                <uni-icons type="forward" size="14" color="#5cadff"/>
                            </view>
                        </view>
                    </view>
                </view>

                <template v-if="rank_set['lottery_open'] && conf.active.task_lottery_list.length">
                    <view class="form-item">
                        <view class="top color-content">任务列表是否显示抽奖入口</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker class="view" :range="['隐藏', '显示']"
                                        :value="conf.active.task_list_show_lottery.show"
                                        @change="conf.active.task_list_show_lottery.show = Number($event.detail.value)">
                                    {{ conf.active.task_list_show_lottery.show ? '显示' : '隐藏' }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view v-show="conf.active.task_list_show_lottery.show" class="form-item">
                        <view class="top color-content">任务列表抽奖入口显示设置</view>
                        <view class="bottom font16">
                            <task-list-show-shop-set :active-id="form_data.active_id"
                                                     :set.sync="conf.active.task_list_show_lottery.set"/>
                        </view>
                    </view>
                </template>

                <template v-if="addedTasks['types_31'] || addedTasks['types_33'] || addedTasks['types_34']"
                          class="form-item">
                    <view class="form-item">
                        <view class="top color-content">健康打卡备注填写设置</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker class="view"
                                        :range="form_options.memo_required_list" range-key="title"
                                        :value="form_options.memo_required_list.findIndex(v => v.value === conf.active.memo_required)"
                                        @change="conf.active.memo_required = form_options.memo_required_list[$event.detail.value].value">
                                    {{ form_options.memo_required_list.find(v => v.value === conf.active.memo_required).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">健康打卡图片上传设置</view>
                        <view class="bottom font16">
                            <view class="flex-kai">

                                <picker
                                    class="view"
                                    :range="form_options.pic_list_required_list" range-key="title"
                                    :value="form_options.pic_list_required_list.findIndex(v => v.value === conf.active.pic_list_required)"
                                    @change="conf.active.pic_list_required = form_options.pic_list_required_list[$event.detail.value].value"
                                >
                                    {{ form_options.pic_list_required_list.find(v => v.value === conf.active.pic_list_required).title }}
                                </picker>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>
                        </view>
                    </view>
                    
                    <sport-check-in-submit-diy-form-set
                        v-if="rank_set['weeklySportsSubmitFormDIY']"
                        :diy-submit-form.sync="conf.active.sport_check_in_submit_diy_form"
                    />
                </template>


                <view class="form-item">
                    <view class="top color-content">
                        <view>文章阅读分类绑定</view>
                        <view class="font12 color-sub">
                            绑定分类后，活动右侧图标显示文章阅读图标，触发进去文章列表阅读文章。
                        </view>
                    </view>
                    <view class="bottom font16">
                        <view class="flex-kai" @click="articleReadingCategoryChange">
                            <view class="view">
                                <view v-if="conf.active.article_reading.category_id">
                                    {{ conf.active.article_reading.category_name || conf.active.article_reading.category_id }}
                                </view>
                                <view v-else class="color-sub">选择分类</view>
                            </view>
                            <view class="flex-all-center">
                                <view
                                    v-if="conf.active.article_reading.category_id"
                                    class="color-sub font12"
                                    style="width: 30px;"
                                    @click.stop="conf.active.article_reading = {category_id: '', category_name: ''}"
                                >解绑
                                </view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">活动说明弹窗是否显示活动规则</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                :range="['显示', '不显示']"
                                :value="conf.active.activity_rules_hide"
                                @change="conf.active.activity_rules_hide = Number($event.detail.value)"
                            >
                                <template v-if="conf.active.activity_rules_hide">不</template>
                                显示
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <medal3-set v-if="rank_set.medalSetting" :active-id="form_data.active_id"
                        :set.sync="conf.active.reward_medal"/>
                
                <active-share-set :active-id="form_data.active_id" :rank-set="rank_set"
                                  :qrcode-logo.sync="conf.active.qrcode_logo"
                                  :share-image.sync="conf.active.share_image"
                                  :share-title.sync="conf.active.share_title"/>
            </view>
            

            <view v-show="type_id === 2">
                <active-image-set :active-id="form_data.active_id" :rankSet="rank_set"
                                  :logo.sync="form_data.logo"
                                  :screen-pic.sync="conf.active.screen_pic"
                                  :top-rank-banner.sync="conf.active.top_rank_banner"/>


                <view v-if="addedTasks['types_19']" class="form-item">
                    <view class="top color-title">
                        <text>运动圈转发到朋友圈预览图</text>
                        <view class="font12 color-sub">图片尺寸建议: 780*1387</view>
                    </view>
                    <view style="padding-top: 5px;">
                        <view class="image-view" v-if="conf.active.timeline_preview_image">

                            <image class="image-item" mode="aspectFill"
                                   :src="conf.active.timeline_preview_image"
                                   @click="previewImage([conf.active.timeline_preview_image])"/>
                            <view class="del-image-item"
                                  @click.stop="conf.active.timeline_preview_image = ''">
                                <uni-icons type="closeempty" color="#e20f04"/>
                            </view>
                        </view>
                        <view v-else class="add-image text-center"
                              @click="changeImage('timeline_preview_image')">
                            <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                        </view>
                    </view>
                </view>
            </view>

            <template v-if="type_id === 6">
                <view class="form-item">
                    <view class="top color-content">任务列表是否显示打卡入口</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker class="view" :range="['隐藏', '显示']"
                                    :value="conf.active.task_list_show_sign.show"
                                    @change="conf.active.task_list_show_sign.show = Number($event.detail.value)">
                                {{ conf.active.task_list_show_sign.show ? '显示' : '隐藏' }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-show="conf.active.task_list_show_sign.show" class="form-item">
                    <view class="top color-content">任务列表打卡入口显示设置</view>
                    <view class="bottom font16">
                        <task-list-show-shop-set :active-id="form_data.active_id"
                                                 :set.sync="conf.active.task_list_show_sign.set"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>每周打卡次数上限</view>
                        <view class="font12 color-sub">
                            设置为0则不限制，设置为1则每周只能打卡1次，设置为2则每周只能打卡2次，以此类推
                        </view>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="conf.active.week_sign_limit"
                                placeholder="不填则不限制打卡次数"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">打卡点默认展示方式</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                :range="form_options.point_show_type_list"
                                :value="form_options.point_show_type_list.findIndex(v => v.value === conf.active.point_show_type)"
                                range-key="title"
                                @change="conf.active.point_show_type = form_options.point_show_type_list[$event.detail.value].value"
                            >
                                {{ form_options.point_show_type_list.find(v => v.value === conf.active.point_show_type).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">是否在打卡点附近才能打卡</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                :range="['否', '是']"
                                :value="conf.active.clock_in_on_site.open"
                                @change="conf.active.clock_in_on_site.open = Number($event.detail.value)"
                            >
                                {{ conf.active.clock_in_on_site.open ? '是' : '否' }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="conf.active.clock_in_on_site.open" class="form-item">
                    <view class="top color-content">
                        <text>打卡范围</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view class="bottom font16 view flex-row" style="line-height: 26px;">
                        <view class="font14 pr5">需在打卡点附近</view>
                        <uni-number-box :min="0.01" :max="9999" :step="0.01"
                                        v-model="conf.active.clock_in_on_site.distance"/>
                        <view class="font14 pl5">公里内才能打卡</view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">打卡次数</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                :range="form_options.sign_times_list"
                                :value="form_options.sign_times_list.findIndex(v => v.value === conf.active.sign_times_type)"
                                range-key="title"
                                @change="conf.active.sign_times_type = form_options.sign_times_list[$event.detail.value].value"
                            >
                                {{ form_options.sign_times_list.find(v => v.value === conf.active.sign_times_type).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-show="conf.active.sign_times_type === 2" class="form-item">
                    <view class="top color-content">
                        <view>每个打卡点每日打卡次数上限</view>
                        <view class="color-sub font12">
                            限制每个打卡点每日可以打卡的次数上限(不填或填0,默认为1次)。
                        </view>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="Number" v-model="conf.active.daily_submit_num"
                               placeholder="请输入次数上限"/>
                    </view>
                </view>


                <view class="form-item">
                    <view class="top color-content">打卡备注填写设置</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                :range="form_options.memo_required_list"
                                :value="form_options.memo_required_list.findIndex(v => v.value === conf.active.memo_required)"
                                range-key="title"
                                @change="conf.active.memo_required = form_options.memo_required_list[$event.detail.value].value"
                            >
                                {{
                                    form_options.memo_required_list.find(v => v.value === conf.active.memo_required).title
                                }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">打卡图片上传设置</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                :range="form_options.pic_list_required_list"
                                :value="form_options.pic_list_required_list.findIndex(v => v.value === conf.active.pic_list_required)"
                                range-key="title"
                                @change="conf.active.pic_list_required = form_options.pic_list_required_list[$event.detail.value].value"
                            >
                                {{ form_options.pic_list_required_list.find(v => v.value === conf.active.pic_list_required).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <text>每次打卡奖励{{ integralUnit }}数</text>
                        <text class="font12 color-sub pl5">(不填或填0不奖励)</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" v-model="conf.active.integral.per_point_reward"/>
                    </view>
                </view>
            </template>
        </view>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view class="login-btn color-white text-center font18 bg-primary"
                  :disabled="loading" @click="save">
                {{ form_data.active_id ? '保存' : '创建活动' }}
            </view>
        </view>
        
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import base64 from '@/utils/base64.js'
import {pinyin} from 'pinyin-pro'
import pickerTime from "@/components/picker-time.vue"
import iconsSetList from "../components/icons-set-list.vue"
import noticeListSet from "../components/notice-list-set.vue"
import startChallengeButtonSet from "../components/start-challenge-button-set.vue"
import taskListShowShopSet from "../components/task-list-show-shop-set.vue"
import taskPopupStyleSet from "../components/task-popup-style-set.vue"
import oneStrokeLineSet from '../components/one-stroke-line-set.vue'
import weightTaskRewardSet from '../components/weight-task-reward-set'
import bodyDataSubmitTaskTips from '../components/body-data-submit-task-tips'
import sportCheckInSubmitDiyFormSet from '../components/sport-check-in-submit-diy-form-set'
import tongueTwisterTextListSet from '../components/tongue-twister-text-list-set.vue'
import weekSportTaskNewsBind from '../components/week-sport-task-news-bind.vue'
import medal3Set from '../components/medal3-set.vue'

import config from '../config'

export default {
    components: {
        oneStrokeLineSet,
        pickerTime,
        iconsSetList,
        noticeListSet,
        startChallengeButtonSet,
        taskListShowShopSet,
        taskPopupStyleSet,
        weightTaskRewardSet,
        bodyDataSubmitTaskTips,
        sportCheckInSubmitDiyFormSet,
        tongueTwisterTextListSet,
        weekSportTaskNewsBind,
        medal3Set
    },

    data() {
        return {
            loading: true,
            page_init_success: false,
            success: false,
            type_list: [
                {title: '基本信息', id: 1},
                {title: '活动设置', id: 3},
                {title: '闯关设置', id: 4},
                {title: '扩展设置', id: 5},
                {title: '活动图片', id: 2}
            ],
            type_id: 1,
            form_data: {
                name: '',
                wechat_num: '',
                mobile: '',
                organizer: '',
                content: '',
                begin_time: new Date(utils.getDay(0, true, '/') + ' 00:00:00').getTime(),
                end_time: new Date(utils.getDay(30, true, '/') + ' 23:59:59').getTime(),
                logo: ''
            },
            conf: {
                active: {
                    news: {
                        news_id: '',
                        news_title: ''
                    },
                    enter_types: 1,
                    password: '',
                    screen_pic: '',
                    top_rank_banner: [],
                    share_title: '',
                    share_image: '',
                    qrcode_logo: '',
                    submit: {
                        begin: '',
                        end: ''
                    },
                    job_set: {
                        types: 1,  //  【1】完成任务数即可闯下一关  【2】需要按照设置的积分数额决定当前是闯第几关的
                        must_finished_num: '', // /指定是否完成一个关卡就可以闯下一关，还是必需完成所有任务才能继续下一关   1代表是完成一个任务即可 2代表是完成2个任务即可 以此类推
                        job_list: [],
                        begin_time: '00:00:00',
                        end_time: '23:59:59'
                    },
                    reading_reward: {
                        open: 1,
                        can_repeat_reading: 0,
                        rules: []
                    },
                    integral: {
                        unit: '积分',
                        exam_reward_num: '',
                        exam_reward: '',
                        exam_min_score: '',
                        exchange_step: '',
                        per_point_reward: ''
                    },
                    
                    
                    // 活动须知阅读
                    active_details_notice: {
                        open: 0,
                        notice_list: []
                    },
                    
                    
                    // 活动右侧图标设置
                    task_active_icons_set: config.getDefaultIconsSet(),

                    // 鲜繁客户定制，单独的文章阅读，不需要做任务，直接从活动右侧图标进入阅读
                    article_reading: {
                        category_id: '',
                        category_name: ''
                    },

                    start_challenge_button_set: config.getDefaultStartButtonSet(),
                    
                    // 任务闯关活动AI运动任务是否合并
                    task_active_ai_sport_task_merge: 0,

                    // 是否隐藏报名信息剩余积分
                    integral_left_hide: 0,

                    batch_import_label: {
                        username: '请输入账号',
                        password: '请输入密码'
                    },

                    daily_sign: {
                        types: 1,
                        integral: '',
                        circle_set: []
                    },
                    
                    grab_grain_game_set: {
                        seconds: 60,
                        target_grain: 20
                    },

                    // 运动圈名称
                    sport_moment_name: '',
                    sport_moment_input_placeholder: '',

                    // 是否不允许用户删除自己发布的运动圈 0: 允许  1: 不允许(默认)
                    sport_moment_user_cannot_delete: 1,

                    sport_moment_min_pic: '',
                    sport_moment_just_take_photo: 0,

                    task_list_show_shop: {
                        show: 0,
                        set: {
                            logo: '',
                            title: '积分商城',
                            text: '',
                            button_text: '去兑换'
                        }
                    },
                    task_list_show_lottery: {
                        show: 0,
                        set: {
                            logo: '',
                            title: '抽奖',
                            text: '',
                            button_text: '去抽奖'
                        }
                    },

                    task_popup_style: config.taskPopupDefaultStyle(),

                    // 找茬配置
                    pic_qb_cat_id: '',
                    game_type: '',

                    // 成语接龙是否允许接同音字
                    idiom_unison_open: 0,
                    idiom_tips_count: '',

                    // 转发文章奖励的文章设置
                    share_reward_news: {
                        id: '',
                        title: ''
                    },

                    one_stroke_line: {
                        row: 6,
                        col: 6,
                        obstacle: 3
                    },


                    // 运动圈转发到朋友圈的预览图，因为朋友圈打开小程序预览页面无法使用微信登录，所以接口不能用，获取不到页面内容，需要设置一张预览图用来显示在页面上
                    timeline_preview_image: '',

                    // 垃圾分类游戏每次获取的垃圾数量
                    garbage_count: '',

                    audio_src: '',

                    sex_required: 0,
                    sex_label: {
                        man: '',
                        woman: ''
                    },

                    task_lottery_list: [],

                    memo_required: 2,
                    pic_list_required: 2,
                    video_list_required: 0,

                    team_required: 0,

                    exchange_step_answer: {
                        open: 0,
                        exam: {
                            id: '',
                            name: ''
                        },
                        min_score: ''
                    },

                    body_data_submit_task_merge_set: {
                        merge: 0,
                        set: {
                            logo: '',
                            title: '身体数据录入',
                            text: ''
                        }
                    },

                    body_data_submit_task_first_tips: {
                        open: 0,
                        tips: '请录入首次身体数据'
                    },
                    body_data_submit_task_last_tips: {
                        open: 0,
                        days: 0,
                        tips: '请录入最终的身体数据'
                    },

                    admin_submit_body_data: 0,

                    we_run_share_button_hide: 0,
                    exam_job_complete_types: 0,
                    
                    sport_check_in_submit_diy_form: [],

                    fan_fan_le_memory_time: '',

                    tongue_twister: {
                        text_list: [
                            {text: '黑化肥发灰会挥发，灰化肥挥发会发黑。黑化肥挥发发灰会花飞，灰化肥挥发发黑会飞花'}
                        ],
                        accuracy: 80
                    },

                    activity_rules_hide: 0,

                    // 周运动任务是否隐藏打卡广场入口
                    week_sport_task_hide_sign_list_enter: 0,

                    types_31_news: {
                        news_id: '',
                        news_title: ''
                    },
                    types_33_news: {
                        news_id: '',
                        news_title: ''
                    },
                    types_34_news: {
                        news_id: '',
                        news_title: ''
                    },

                    // 雷子客户定制开发 - S
                    // 雷子客户定制开发，需要3种勋章
                    reward_medal: {
                        list: [
                            {types: 1, title: '答题勋章', pic: '', exam_min_score: ''},
                            {types: 2, title: '打卡勋章', pic: ''},
                            {types: 3, title: '步数勋章', pic: '', person: ''}
                        ]
                    },
                    // 每周限制打卡次数
                    week_sign_limit: '',

                    // 任务列表显示打卡入口
                    task_list_show_sign: {
                        show: 0,
                        set: {
                            logo: '',
                            title: '打卡',
                            text: '',
                            button_text: '去打卡'
                        }
                    },
                    point_show_type: 2,
                    clock_in_on_site: {
                        open: 0,
                        distance: 3
                    },
                    sign_times_type: 0,
                    daily_submit_num: 1,
                    // 雷子客户定制开发 - E
                },
                must_submit: JSON.parse(JSON.stringify(this.xwy_config.defaultMustSubmit)),
                AI_motion: {
                    motion_list: []
                }
            },
            top_rank_banner_max_count: 6,
            form_options: {
                enter_types_list: [
                    {title: '自由报名参与活动', value: 1},
                    {title: '需要输入密码才能进入活动', value: 2},
                    {title: '报名需要审核通过才能参与活动', value: 3}
                ],

                // 活动须知阅读
                active_details_notice_open_opt: [
                    {value: 1, title: '开启'},
                    {value: 0, title: '关闭'}
                ],

                memo_required_list: [
                    {value: 0, title: '关闭打卡填写备注'},
                    {value: 2, title: '开启打卡填写备注'},
                    {value: 1, title: '打卡必须填写备注'}
                ],

                pic_list_required_list: [
                    {value: 0, title: '关闭打卡上传图片'},
                    {value: 2, title: '开启打卡上传图片'},
                    {value: 1, title: '打卡必须上传图片'}
                ],
                point_show_type_list: [
                    {value: 1, title: '地图展示'},
                    {value: 2, title: '列表展示'}
                ],
                sign_times_list: [
                    {value: 0, title: '不限制打卡次数'},
                    {value: 1, title: '每个打卡点只能打卡一次'},
                    {value: 2, title: '限制每个打卡点每天打卡次数'}
                ],
            },
            rank_set: {},
            have_password: false,
            default_integral_unit: '积分',

            all_ai_sport_type_count: 0,
            edit_ai_sport_item_data: {},
            ai_sport_types: [],
            edit_task_data: {},
            all_task_list: [],
            read_reward_item_data: {
                name: '',
                category_id: '',
                seconds: '',
                integral: {min: '', max: ''}
            },
            
            need_integral_types: config.needIntegralTypes,
            need_count_types: config.needCountTypes,
            need_seconds_types: config.needSecondsTypes,
            need_max_count_types: config.needMaxCountTypes,
            need_max_integral_types: config.needMaxIntegralTypes,
            need_week_count: config.needWeekCount,
            // need_all_types_max: config.needAllTypesMax,

            active_job_list: [],


            // 31、32、33、34这4个任务活动期间内奖励的最大积分数，最后保存时需要循环保存到每个任务里面
            types_31_32_33_34_all_types_max: ''
        }
    },
    
    computed: {
        needSetIntegralTask() {
            const {types = 0} = this.edit_task_data
            return this.need_integral_types.includes(types)
        },

        needSetCountTask() {
            const {types = 0} = this.edit_task_data
            return this.need_count_types.includes(types)
        },

        addedTasks() {
            const job_list = this.conf?.active?.job_set?.job_list || []
            const addedTasks = {}
            job_list.forEach(item => {
                addedTasks[`types_${item.types}`] = item.title
            })
            return addedTasks
        },

        all_job_type_count() {
            return this.active_job_list.length
        },


        // 身体数据录入任务开启了多少个(名字)，达到两个或以上才显示合并
        bodyTaskNames() {
            const job_list = this.conf?.active?.job_set?.job_list || []
            return job_list.filter(item => [27, 28, 29].includes(item.types)).map(item => item.title)
        },

        examJobCompleteTypesPickerData() {
            const options = this.examJobCompleteTypesOptions
            let index = options.findIndex(item => item.types === this.conf.active.exam_job_complete_types)
            if (index === -1) index = 0

            return {
                value: index,
                title: options[index].title
            }
        },

        integralUnit() {
            return this.conf?.active?.integral?.unit || this.default_integral_unit
        },

        dailySignSetOptions() {
            return [
                // {value: 0, title: '关闭每日金币签到'},
                {value: 1, title: `每日签到奖励固定${this.integralUnit}`},
                {value: 2, title: `周期签到、连续签到，每日签到${this.integralUnit}奖励不同`}
            ]
        },

        examJobCompleteTypesOptions() {
            return [
                {types: 0, title: '单次答题达到设置的答题任务最低分数即完成任务'},
                {types: 1, title: `累计答题奖励${this.integralUnit}达到任务设置的${this.integralUnit}上限即完成任务`}
            ]
        }
    },


    watch: {
        'conf.active.submit.begin': function () {
            let {begin, end} = this.conf.active.submit
            if (begin && begin.length < 19) {
                begin += '00:00:00'
                this.conf.active.submit.begin = begin
            }
            if (end) {
                const begin_time = new Date(begin.replace(/-/g, '/')).getTime()
                const end_time = new Date(end.replace(/-/g, '/')).getTime()
                if (begin_time > end_time) {
                    this.$uni.showToast('报名开始时间不能大于报名截止时间')
                }
            }
        },
        'conf.active.submit.end': function () {
            let {begin, end} = this.conf.active.submit
            if (end && end.length < 19) {
                end += '23:59:59'
                this.conf.active.submit.end = end
            }
            if (begin) {
                const begin_time = new Date(begin.replace(/-/g, '/')).getTime()
                const end_time = new Date(end.replace(/-/g, '/')).getTime()
                if (end_time < begin_time) {
                    this.$uni.showToast('报名截止时间不能小于报名开始时间')
                }
            }
        }
    },
    onLoad(e) {
        if (e.id) {
            this.form_data.active_id = e.id
        } else {
            this.types = Number(e.types)
            this.$nextTick(() => this.$refs.add_activity_tips.open(this.types))
            this.form_data.logo = this.xwy_config.create_active_default_logo
        }
        uni.setNavigationBarTitle({
            title: e.id ? '修改活动' : `创建${e.name || '活动'}`
        })
        this.$uni.showLoading()


        login.uniLogin(err => {
            if (err && err.errMsg) {
                this.loading = false
                uni.hideLoading()
                return uni.showModal({
                    title: err['errTitle'] || '提示',
                    content: err.errMsg,
                    showCancel: false
                })
            }

            this.init()
        })
    },
    methods: {
        async init() {
            if (!this.form_data.active_id && !await this.checkUserCanCreateActive()) return

            await this.getAiSportTypeList()
            if (this.form_data.active_id) {
                await this.getDetail()
            } else {
                this.taskActiveIconsSetFilter()
            }

            this.getActiveJobList()

            this.loading = false
            this.page_init_success = true
            uni.hideLoading()
        },


        async checkUserCanCreateActive() {
            const evn_version = app.globalData['evn_version']

            if (evn_version === 'develop') {
                this.form_data.mobile = 12345678910
                this.form_data.wechat_num = 12345678910
                this.form_data.organizer = '伟杰测试'
            }

            // 开发版和体验版不检查重复创建活动
            if (evn_version === 'develop' || evn_version === 'trial') return true

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.admin/check_user_can_create_active'
            })
            if (res?.status === 1) return true

            uni.hideLoading()
            await this.$uni.showModal(res.info || '暂时不能创建活动', {success: () => uni.navigateBack()})
            return false
        },

        getTaskTypesName(types) {
            return this.active_job_list.find(v => v.types === types)?.name || types
        },

        getTaskRewardDescription(item) {
            const unit = this.integralUnit

            const options = {
                1: () => {
                    const types = this.conf.active.job_set.types
                    if (types === 1) return `步数达到${item.min}步, 奖励${item.integral}${unit}`
                    if (types === 2) return `每${item.min}步, 奖励${item.integral}${unit}`
                },
                2: () => '以关卡绑定的考卷设置为准',
                3: () => `发布1条动态奖励${item.integral}${unit}`,
                4: () => '以AI运动设置为准',
                5: () => '以文章阅读奖励设置为准',
                6: () => '以签到奖励设置为准',
                7: () => '以拼图设置为准',
                9: () => item.integral_type === 'per_integral' ? `答对1题奖励${item.per_integral}${unit}` : `完成任务奖励${item.integral}${unit}`,
                12: () => item.integral_type === 'per_integral' ? `接到1个奖励${item.per_integral}${unit}` : `完成任务奖励${item.integral}${unit}`,
                15: () => `接龙1次奖励${item.per_integral}${unit}`,
                16: () => `打中1只奖励${item.per_integral}${unit}`,
                18: () => item.integral_type === 'per_integral' ? `答对1题奖励${item.per_integral}${unit}` : `完成任务奖励${item.integral}${unit}`,
                22: () => `每连一个红包奖励${item.per_integral}${unit}`,
                23: () => `每打中一个年兽奖励${item.per_integral}${unit}`,
                24: () => `每击中一架飞机奖励${item.per_integral}${unit}`,
                26: () => `每消除一组奖励${item.per_integral}${unit}`,
                27: () => '以体重变化率奖励设置为准',
                28: () => {
                    let info = `体脂每减少${item.num}%, 奖励${item.integral}${unit}`
                    if (item.max_integral) info += `, 奖励上限${item.max_integral}${unit}`
                    return info
                },
                29: () => {
                    let info = `腰围每减少${item.num}cm, 奖励${item.integral}${unit}`
                    if (item.max_integral) info += `, 奖励上限${item.max_integral}${unit}`
                    return info
                },
                36: () => `每数1张钱奖励${item.per_integral}${unit}`,
                38: () => `每进1球奖励${item.per_integral}${unit}`,
                39: () => `每奔跑1米奖励${item.per_integral}${unit}`,
                40: () => `每消除1个元素奖励${item.per_integral}${unit}`,
                41: () => `每打爆1个气球奖励${item.per_integral}${unit}`,
                42: () => `每跳过1个平台奖励${item.per_integral}${unit}`,
                45: () => `发布1条动态奖励${item.integral}${unit}`,
            }

            const completeRewardTypes = [8, 10, 11, 13, 14, 19, 20, 21, 25, 30, 31, 32, 33, 34, 37]
            if (completeRewardTypes.includes(item.types)) return `完成任务奖励${item.integral}${unit}`
            return options[item.types]?.() || ''
        },

        deleteTaskItem(index) {
            this.conf.active.job_set.job_list.splice(index, 1)
        },

        clearJobList() {
            uni.showModal({
                title: '提示',
                content: '确定清空任务?',
                success: res => {
                    if (res.confirm) {
                        this.conf.active.job_set.job_list = []
                        this.$nextTick(() => this.$uni.showToast('任务已清空'))
                    }
                }
            })
        },

        jobListSort(type, index) {
            const list = JSON.parse(JSON.stringify(this.conf.active.job_set.job_list))
            if ((type === 'up' && index === 0) || (type === 'down' && index === list.length - 1)) return
            
            const item = list[index]
            if (type === 'up') {
                list[index] = list[index - 1]
                list[index - 1] = item
            } else {
                list[index] = list[index + 1]
                list[index + 1] = item
            }
            
            this.$set(this.conf.active.job_set, 'job_list', list)
        },

        editTaskItem(index) {
            this.task_change_index = index
            let item = null
            if (index !== null) {
                const list = JSON.parse(JSON.stringify(this.conf.active.job_set.job_list))
                item = list[index]
            }

            this.edit_task_data = {
                types: item?.types || '',
                logo: item?.logo || '',
                title: item?.title || '',
                text: item?.text || '',
                min_num: item?.min_num || '',
                min: item?.min || '',
                max: item?.max || '',
                integral_type: item?.integral_type || 'integral',
                per_integral: item?.per_integral || '',
                integral: item?.integral || '',
                count: item?.count || '',
                not_complete_button_text: item?.not_complete_button_text || '去完成',
                complete_button_text: item?.complete_button_text || '已完成',
                seconds: item?.seconds || '',
                max_count: item?.max_count || '',
                max_integral: item?.max_integral || '',
                num: item?.num || '',
                integral_list: item?.integral_list || [{min: 1, max: 10, integral: 10}],
                week_count: item?.week_count || 1,
                // all_types_max: item?.all_types_max || '',
                step_num: item?.step_num || '',
                job_stock: item?.job_stock || ''
            }
            this.$refs.taskItemEditPopup.open()
        },

        changeTaskTypes() {
            const all_task_list = JSON.parse(JSON.stringify(this.active_job_list))
            const job_list = this.conf.active.job_set.job_list

            // 如果是编辑的话，当前运动类型改为没有选择，不然选了其他运动类型后无法选回这个运动类型
            const index = this.task_change_index
            const current_types = job_list[index]?.types || null

            all_task_list.forEach(item => {
                item.selected = !!job_list.find(v => v.types === item.types && v.types !== current_types)
            })

            this.all_task_list = all_task_list
            this.$refs.changeTaskTypesPopup.open()
        },

        selectTaskTypes(item) {
            const need_exam_types = [2, 9]
            if (item.selected) return this.$uni.showToast(`${item.name}已选择，请选择其他任务类型`)
            if (need_exam_types.includes(item.types) && !this.rank_set?.exam_open)
                return this.$uni.showModal('活动未开启答题功能, 请联系客服开启。')
            if ([3, 19, 21, 45].includes(item.types) && !this.rank_set?.['open_sport_moment'])
                return this.$uni.showModal('活动未开启运动圈功能, 请联系客服开启。')
            if (item.types === 4 && !this.rank_set?.AI_motion)
                return this.$uni.showModal('活动未开启AI运动功能, 请联系客服开启。')
            if (item.types === 5 && !this.rank_set?.reading_reward)
                return this.$uni.showModal('活动未开启文章阅读奖励, 请联系客服开启。')
            if (item.types === 6 && !this.rank_set?.['morning_daily_sign'])
                return this.$uni.showModal('活动未开启每日签到功能, 请联系客服开启。')

            this.edit_task_data.types = item.types
            this.edit_task_data.title ||= item.name
            this.$refs.changeTaskTypesPopup.close()
        },

        taskItemEditClose() {
            this.edit_task_data = {}
            this.$refs.taskItemEditPopup.close()
        },

        taskItemEditConfirm() {
            const data = JSON.parse(JSON.stringify(this.edit_task_data))
            if (!data.types) return this.$uni.showToast('请选择任务类型')
            if (!data.title) return this.$uni.showToast('请输入任务名称')

            if (this.need_count_types.includes(data.types)) {
                data.count = Math.floor(data.count)
                if (isNaN(data.count) || data.count <= 0) return this.$uni.showToast('请输入正确的任务次数')
            } else {
                delete data.count
            }

            if (data.types === 1) {
                data.min = Math.floor(data.min)
                if (isNaN(data.min) || data.min <= 0) return this.$uni.showToast('请输入正确的运动步数')
                this.conf.active.integral.exchange_step = data.min  // 按积分数过关，兑换比例是存在这个地方的
                data.integral = 1

                data.min_num = Math.floor(data.min_num)
                if (isNaN(data.min_num) || data.min_num < 0) {
                    return this.$uni.showToast('请输入正确的每日最低兑换步数')
                }
                
                if (this.conf.active.job_set.types === 2) {
                    data.max = Math.floor(data.max)
                    if (isNaN(data.max) || data.max < 0) {
                        return this.$uni.showToast('请输入正确的每日步数兑换上限')
                    }
                } else {
                    data.max = 0
                }
            } else {
                delete data.min
                delete data.max
                delete data.min_num
            }

            if (data.types === 9 || data.types === 12 || data.types === 18) {
                if (data.integral_type === 'integral') {
                    data.integral = Number(data.integral)
                    if (isNaN(data.integral)) return this.$uni.showToast(`请输入正确的${this.integralUnit}数量`)
                } else {
                    data.per_integral = Number(data.per_integral)
                    if (isNaN(data.per_integral)) return this.$uni.showToast(`请输入正确的${this.integralUnit}数量`)
                }
            } else {
                if (this.need_integral_types.includes(data.types)) {
                    data.integral = Number(data.integral)
                    if (isNaN(data.integral)) return this.$uni.showToast(`请输入正确的${this.integralUnit}数量`)
                } else {
                    delete data.integral
                }
            }

            // 找茬、成语接龙、打地鼠任务次数固定为1次。如果不设置的话，接口会一直重复奖励
            // if (data.types === 14 || data.types === 15 || data.types === 16) data.count = 1

            if (data.types === 15 || data.types === 16) {
                delete data.integral
                data.per_integral = Number(data.per_integral)
                if (isNaN(data.per_integral)) return this.$uni.showToast(`请输入正确的${this.integralUnit}数量`)
            }

            if (this.need_seconds_types.includes(data.types)) {
                data.seconds = Math.floor(data.seconds)
                if (isNaN(data.seconds) || data.seconds < 0) return this.$uni.showToast('请输入正确的任务限时')
                // 22 一笔连福任务，不设置时间则不限制，
                if (data.seconds === 0 && data.types !== 22) {
                    // 成语接龙默认120秒，其它默认30秒
                    data.seconds = data.types === 15 ? 120 : 30
                }
            } else {
                delete data.seconds
            }

            if (this.need_max_count_types.includes(data.types)) {
                if (data.max_count) {
                    data.max_count = Math.floor(data.max_count)
                    if (isNaN(data.max_count)) return this.$uni.showToast('请输入正确的任务数量上限')
                }
            } else {
                delete data.max_count
            }

            if (this.need_max_integral_types.includes(data.types)) {
                const max_integral = Number(data.max_integral)
                if (isNaN(max_integral) || max_integral < 0) return this.$uni.showToast(`请输入正确的${this.integralUnit}奖励上限`)
                data.max_integral = max_integral
            } else {
                delete data.max_integral
            }

            if (data.types === 27) {
                const integral_list = this.$refs.weightTaskRewardSet.getListSet()
                if (integral_list === null) return
                data.integral_list = integral_list
            } else {
                delete data.integral_list
            }

            if (data.types === 28 || data.types === 29) {
                data.num = 1
            } else {
                delete data.num
            }


            if (this.need_week_count.includes(data.types)) {
                const week_count = Math.floor(data.week_count)
                if (isNaN(week_count)) return this.$uni.showToast('请输入正确的每周运动次数')
                if (week_count < 1) return this.$uni.showToast('每周运动次数至少设置1次')
                data.week_count = week_count
            } else {
                if (data.types !== 32) delete data.week_count
            }

            /*if (this.need_all_types_max.includes(data.types)) {
                const all_types_max = Math.floor(data.all_types_max)
                if (isNaN(all_types_max) || all_types_max < 0) return this.$uni.showToast('请输入正确的总积分上限')
                data.all_types_max = all_types_max
            } else {
                delete data.all_types_max
            }*/

            if (data.types === 32) {
                const step_num = Math.floor(data.step_num)
                if (isNaN(step_num) || step_num < 0) return this.$uni.showToast('请输入正确的每周最低步数')
                data.step_num = step_num
                // 每周次数必须设置，默认1次，不然已报名就会变成已完成
                data.week_count = 1
            } else {
                delete data.step_num
            }

            if ([31, 33, 34].includes(data.types)) {
                const job_stock = Math.floor(data.job_stock)
                if (isNaN(job_stock) || job_stock < 0) return this.$uni.showToast('请输入正确的每周参与人数限制')
                data.job_stock = job_stock
            } else {
                delete data.job_stock
            }

            const index = this.task_change_index
            const job_list = this.conf.active.job_set.job_list
            index === null ? job_list.push(data) : this.$set(job_list, index, data)

            this.$refs.taskItemEditPopup.close()
        },


        async getAiSportTypeList() {
            this.ai_sport_type_list = await xwy_api.getAiSportTypeList()
            this.all_ai_sport_type_count = this.ai_sport_type_list.length
        },


        getAiSportTypesName(types) {
            return this.ai_sport_type_list.find(v => v?.types === types)?.name || types
        },

        getAiSportTypesNumName(types) {
            const isCounts = this.xwy_config.AiSportNeedCountsTypes(types)
            return isCounts ? '个' : '秒'
        },

        getAiSportRewardDescription(item) {
            const sport_name = this.getAiSportTypesName(item.types)
            const num_name = this.getAiSportTypesNumName(item.types)   // 个 or 秒
            const {num, integral} = item.integral_reward
            return `${sport_name}${num}${num_name}奖励${integral}${this.integralUnit}`
        },
        

        clearAiMotionList() {
            uni.showModal({
                title: '提示',
                content: '确定清空AI运动设置?',
                success: res => {
                    if (res.confirm) {
                        this.conf.AI_motion.motion_list = []
                        this.$nextTick(() => this.$uni.showToast('AI运动设置已清空'))
                    }
                }
            })
        },

        deleteAiSportItem(index) {
            this.conf.AI_motion.motion_list.splice(index, 1)
        },

        AiSportListSort(type, index) {
            const list = JSON.parse(JSON.stringify(this.conf.AI_motion.motion_list))
            if ((type === 'up' && index === 0) || (type === 'down' && index === list.length - 1)) return

            const item = list[index]
            if (type === 'up') {
                list[index] = list[index - 1]
                list[index - 1] = item
            } else {
                list[index] = list[index + 1]
                list[index + 1] = item
            }

            this.$set(this.conf.AI_motion, 'motion_list', list)
        },

        editAiSportItem(index) {
            this.ai_sport_change_index = index
            
            const data = index === null ?
                JSON.parse(JSON.stringify(config.AI_SPORT_DEFAULT_DATA)) :
                JSON.parse(JSON.stringify(this.conf.AI_motion.motion_list[index]))
            
            data.integral_reward.max_daily ||= ''
            data.describe ||= ''

            this.edit_ai_sport_item_data = data
            
            this.$refs.aiSportItemEditPopup.open()
        },

        aiSportItemEditConfirm() {
            const data = this.edit_ai_sport_item_data
            if (!data.types) return this.$uni.showToast('请选择运动类型')
            
            const {integral, num, max_daily} = data.integral_reward
            
            if (isNaN(integral) || !integral || isNaN(num) || !num)
                return this.$uni.showToast('奖励设置填写不正确')
            
            if (max_daily && isNaN(max_daily)) return this.$uni.showToast('每日上限填写不正确')

            if (!data.name) data.name = this.getAiSportTypesName(data.types)

            const index = this.ai_sport_change_index
            const motion_list = this.conf.AI_motion.motion_list
            index === null ? motion_list.push(data) : this.$set(motion_list, index, data)

            this.$refs.aiSportItemEditPopup.close()
        },

        changeAiSportTypes() {
            const ai_sport_types = JSON.parse(JSON.stringify(this.ai_sport_type_list))
            const motion_list = this.conf.AI_motion.motion_list

            // 如果是编辑的话，当前运动类型改为没有选择，不然选了其他运动类型后无法选回这个运动类型
            const index = this.ai_sport_change_index
            let current_types = motion_list[index]?.types || null

            ai_sport_types.forEach(item => {
                item.selected = !!motion_list.find(v => v.types === item.types && v.types !== current_types)
            })

            this.ai_sport_types = ai_sport_types
            this.$refs.changeAiSportTypesPopup.open()
        },


        selectAiSportTypes(item) {
            if (item.selected) return this.$uni.showToast(`${item.name}已选择，请选择其他运动类型`)
            this.edit_ai_sport_item_data.types = item.types
            this.$refs.changeAiSportTypesPopup.close()
        },


        exchangeStepAnswerBind() {
            this.$uni.navigateTo('/pages/likou_dati/pages/exam/exam_list/exam_list?in_select=true', {
                events: {
                    updateExam: data => {
                        this.conf.active.exchange_step_answer.exam.id = data.id
                        this.conf.active.exchange_step_answer.exam.name = data.title
                    }
                }
            })
        },
        exchangeStepAnswerDeBind() {
            this.conf.active.exchange_step_answer.exam.id = ''
            this.conf.active.exchange_step_answer.exam.name = ''
        },


        editReadRewardRule(index) {
            this.read_reward_change_index = index
            let item = {}
            const data = this.conf.active.reading_reward.rules[index]
            if (data) item = JSON.parse(JSON.stringify(data))

            this.read_reward_item_data = {
                name: item.name || '',
                category_id: item.category_id || '',
                seconds: item.seconds || '',
                integral: item.integral || {min: '', max: ''}
            }

            this.$refs.readRewardEditPopup.open()
        },

        deleteReadRewardRule(index) {
            this.conf.active.reading_reward.rules.splice(index, 1)
        },

        clearReadRewardRule() {
            uni.showModal({
                title: '提示',
                content: '确定阅读奖励规则?',
                success: res => {
                    if (res.confirm) {
                        this.conf.active.reading_reward.rules = []
                        this.$nextTick(() => this.$uni.showToast('阅读奖励规则已清空'))
                    }
                }
            })
        },

        changeNewsCategory() {
            let url = '/pages/category/list?types=8&is_sel=1&just_look_active=1'
            if (this.form_data.active_id) url += `&active_id=${this.form_data.active_id}`
            this.$uni.navigateTo(url, {
                events: {
                    selCategory: data => {
                        const ids = this.conf.active.reading_reward.rules.map(v => v.category_id)
                        if (ids.includes(data.id)) {
                            return setTimeout(() => {
                                this.$uni.showModal('该分类已添加, 请选择新的分类', {
                                    showCancel: true,
                                    success: res => res.confirm && this.changeNewsCategory()
                                })
                            }, 100)
                        }
                        this.read_reward_item_data.category_id = data.id
                        this.read_reward_item_data.name = data.name
                    }
                }
            })
        },

        readRewardEditConfirm() {
            const data = this.read_reward_item_data
            if (!data.category_id) return this.$uni.showToast('请选择文章分类')
            const seconds = Math.floor(data.seconds)
            if (isNaN(seconds) || seconds <= 0) return this.$uni.showToast('请输入正确的阅读时间')
            const min = Math.floor(data.integral.min)
            const max = Math.floor(data.integral.max)
            if (isNaN(min) || min <= 0 || isNaN(max) || max <= 0)
                return this.$uni.showToast(`请输入正确的${this.integralUnit}数量`)
            if (min > max) return this.$uni.showToast(`最小${this.integralUnit}不能大于最大${this.integralUnit}`)

            const index = this.read_reward_change_index
            const rules = this.conf.active.reading_reward.rules
            index === null ? rules.push(data) : this.$set(rules, index, data)

            this.$refs.readRewardEditPopup.close()
        },


        articleReadingCategoryChange() {
            let url = '/pages/category/list?types=8&is_sel=1&just_look_active=1'
            if (this.form_data.active_id) url += `&active_id=${this.form_data.active_id}`
            this.$uni.navigateTo(url, {
                events: {
                    selCategory: data => {
                        this.conf.active.article_reading.category_id = data.id
                        this.conf.active.article_reading.category_name = data.name
                    }
                }
            })
        },


        enter_typesChange(e) {
            this.conf.active.enter_types = this.form_options.enter_types_list[e.detail.value].value
        },

        getActiveJobList() {
            this.active_job_list = config.getActiveJobListSet(this.activeDetails, this.active_conf_set)
        },

        async getDetail() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details',
                data: {
                    active_id: this.form_data.active_id
                }
            })

            const detail = res?.data?.active_details
            if (!detail) return this.$uni.showModal(res?.info || '活动获取失败', {
                success: () => uni.navigateBack()
            })

            this.activeDetails = detail
            this.detailInit(detail)

            const active_conf_set = res?.data?.active_more_data?.active_conf_set
            if (active_conf_set) this.active_conf_set = active_conf_set
        },


        detailInit(data) {
            this.types = data.types
            this.form_data.name = data.name
            this.form_data.begin_time = data.begin_time * 1000
            this.form_data.end_time = data.end_time * 1000
            if (data.organizer) this.form_data.organizer = data.organizer
            if (data.logo) this.form_data.logo = data.logo
            if (data.content) this.form_data.content = data.content

            const rank_set = data.rank_set
            if (rank_set) {
                this.rank_set = rank_set
                
                if (rank_set.batch_import) {
                    this.form_options.enter_types_list.push({
                        value: 4,
                        title: '只能使用管理员提前导入的名单报名'
                    })
                }

                if (rank_set.medalSetting) this.type_list.push({id: 6, title: '打卡设置'})
            }

            this.confDataInit(data.conf)
            
            
            // 为了解决开了白名单又关了的情况
            const enter_types = data.conf?.active?.enter_types
            if (enter_types) {
                if (!this.form_options.enter_types_list.find(v => v.value === enter_types)) {
                    this.conf.active.enter_types = this.form_options.enter_types_list[0].value
                }
            }

            uni.hideLoading()
        },

        confDataInit(conf) {
            if (conf.must_submit) this.conf.must_submit = conf.must_submit
            if (conf.AI_motion) this.conf.AI_motion = conf.AI_motion

            const active = conf.active
            this.conf.active.enter_types = active.enter_types

            const props = ['news', 'screen_pic', 'top_rank_banner', 'share_title', 'share_image', 'qrcode_logo', 'article_reading', 'start_challenge_button_set', 'task_active_ai_sport_task_merge', 'integral_left_hide', 'batch_import_label', 'daily_sign', 'grab_grain_game_set', 'sport_moment_name', 'sport_moment_input_placeholder', 'task_list_show_shop', 'task_list_show_lottery', 'task_popup_style', 'pic_qb_cat_id', 'game_type', 'idiom_unison_open', 'idiom_tips_count', 'share_reward_news', 'timeline_preview_image', 'garbage_count', 'audio_src', 'one_stroke_line', 'sex_required', 'sex_label', 'team_required', 'exchange_step_answer', 'body_data_submit_task_merge_set', 'body_data_submit_task_first_tips', 'body_data_submit_task_last_tips', 'we_run_share_button_hide', 'exam_job_complete_types', 'sport_check_in_submit_diy_form', 'fan_fan_le_memory_time', 'tongue_twister', 'activity_rules_hide', 'sport_moment_min_pic', 'sport_moment_just_take_photo', 'admin_submit_body_data', 'week_sport_task_hide_sign_list_enter', 'types_31_news', 'types_33_news', 'types_34_news', 'reward_medal', 'week_sign_limit', 'task_list_show_sign', 'point_show_type', 'clock_in_on_site', 'sign_times_type', 'daily_submit_num']
            props.forEach(prop => {
                if (active[prop]) this.conf.active[prop] = active[prop]
            })

            // if (active.news) this.conf.active.news = active.news
            if (active.password) {
                this.old_password = active.password
                this.have_password = true
            }

            if (active.submit) {
                if (active.submit.begin) this.conf.active.submit.begin = active.submit.begin
                if (active.submit.end) this.conf.active.submit.end = active.submit.end
            }

            if (active.job_set) {
                active.job_set.types ||= 1
                active.job_set.begin_time ||= '00:00:00'
                active.job_set.end_time ||= '23:59:59'

                let types_31_32_33_34_all_types_max = 0

                active.job_set.job_list.forEach(item => {
                    if (item.types === 9 || item.types === 12 || item.types === 18) {
                        if (item.hasOwnProperty('integral')) {
                            item.integral_type = 'integral'
                            item.per_integral = ''
                        } else {
                            item.integral_type = 'per_integral'
                            item.integral = ''
                        }
                    }
                    if (item.types === 15 || item.types === 16) item.integral_type = 'per_integral'

                    if ([31, 32, 33, 34].includes(item.types)) {
                        types_31_32_33_34_all_types_max = Math.max(types_31_32_33_34_all_types_max, item['all_types_max'] || 0)
                    }
                })

                if (types_31_32_33_34_all_types_max) {
                    this.types_31_32_33_34_all_types_max = types_31_32_33_34_all_types_max
                }

                this.conf.active.job_set = active.job_set
            }
            if (active.reading_reward) {
                active.reading_reward.can_repeat_reading ||= 0
                this.conf.active.reading_reward = active.reading_reward
            }
            if (active.integral) {
                active.integral.exam_reward_num ||= ''
                active.integral.exam_reward ||= ''
                active.integral.exam_min_score ||= ''
                active.integral.exchange_step ||= ''
                active.integral.per_point_reward ||= ''
                this.conf.active.integral = active.integral
            }
            
            // 阅读须知
            if (active.active_details_notice) {
                const set = active.active_details_notice
                if (!set.notice_list) {
                    set.notice_list = [{
                        news_id: set.news_id || '',
                        news_title: set.news_title || '',
                        confirm_text: set.confirm_text || '确定'
                    }]
                }
                if (set.hasOwnProperty('news_id')) delete set.news_id
                if (set.hasOwnProperty('news_title')) delete set.news_title
                if (set.hasOwnProperty('confirm_text')) delete set.confirm_text
                this.conf.active.active_details_notice = set
            }
            
            
            // 右侧图标自定义设置获取
            if (active.task_active_icons_set?.length) {
                this.conf.active.task_active_icons_set = config.getTaskActiveIconsSet(active.task_active_icons_set)
            }

            this.taskActiveIconsSetFilter()

            if (active.hasOwnProperty('sport_moment_user_cannot_delete')) {
                this.conf.active.sport_moment_user_cannot_delete = active.sport_moment_user_cannot_delete
            }

            if (conf.active.task_lottery_list?.length) {
                this.conf.active.task_lottery_list = conf.active.task_lottery_list
            }

            this.conf.active.memo_required = conf.active.memo_required || 0
            this.conf.active.pic_list_required = conf.active.pic_list_required || 0
        },

        // 过滤掉活动没有开启的功能
        taskActiveIconsSetFilter() {
            const task_active_icons_set = this.conf.active.task_active_icons_set
            const {exam_open, open_sport_moment, gift_goods, active_details_notice, lottery_open, diy_books} = this.rank_set

            if (!exam_open) {
                const index = task_active_icons_set.findIndex(item => item.name === 'exam')
                if (index !== -1) task_active_icons_set.splice(index, 1)
            }
            if (!open_sport_moment) {
                const index = task_active_icons_set.findIndex(item => item.name === 'sport_comment')
                if (index !== -1) task_active_icons_set.splice(index, 1)
            }
            if (!gift_goods) {
                const index = task_active_icons_set.findIndex(item => item.name === 'shopping')
                if (index !== -1) task_active_icons_set.splice(index, 1)
            }
            if (!active_details_notice) {
                const index = task_active_icons_set.findIndex(item => item.name === 'active_notice')
                if (index !== -1) task_active_icons_set.splice(index, 1)
            }
            if (!lottery_open) {
                const index = task_active_icons_set.findIndex(item => item.name === 'lottery')
                if (index !== -1) task_active_icons_set.splice(index, 1)
            }
            if (!diy_books) {
                const index = task_active_icons_set.findIndex(item => item.name === 'certificate')
                if (index !== -1) task_active_icons_set.splice(index, 1)
            }

            this.conf.active.task_active_icons_set = task_active_icons_set
        },

        toSelNews(type) {
            uni.navigateTo({
                url: '/pages/news/list?type=user&is_sel=true',
                events: {
                    selNews: data => {
                        switch (type) {
                            case 'content':
                                this.conf.active.news.news_id = data.id
                                this.conf.active.news.news_title = data.title
                                break

                            case 'share_reward_news':
                                this.conf.active.share_reward_news.id = data.id
                                this.conf.active.share_reward_news.title = data.title
                                break
                        }
                    }
                }
            })
        },

        deleteNews(type) {
            switch (type) {
                case 'content':
                    this.conf.active.news.news_id = ''
                    this.conf.active.news.news_title = ''
                    break

                case 'share_reward_news':
                    this.conf.active.share_reward_news.id = ''
                    this.conf.active.share_reward_news.title = ''
                    break
            }
        },


        changeImage(key) {
            let url = '/pages/other/image_upload_or_select'
            if (this.form_data.active_id) url += `?active_id=${this.form_data.active_id}`

            uni.navigateTo({
                url,
                events: {
                    newImg: src => {
                        const function_list = {
                            ai_sport_logo: () => this.edit_ai_sport_item_data.logo = src,
                            task_logo: () => this.edit_task_data.logo = src
                        }
                        const default_function = () => this.conf.active[key] = src
                        function_list[key] ? function_list[key]() : default_function()
                    }
                }
            })
        },

        previewImage(urls, current = urls[0]) {
            uni.previewImage({
                urls,
                current
            })
        },

        dataCheck(data) {
            const showToast = title => {
                this.type_id = 1
                this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none')
                return false
            }

            if (!data.name) return showToast('请输入活动名称')

            if (!data.active_id) {
                if (!data.mobile) return showToast('请输入手机号')
                if (data.mobile.toString().length !== 11) return showToast('手机号长度有误')
                
                if (!data.wechat_num) return showToast('请输入微信号')
                const wechat_num = data.wechat_num.toString()
                if (wechat_num.length < 4 || wechat_num.length > 20) return showToast('微信号长度有误')
                if (this._utils.isChineseChar(wechat_num)) return showToast('微信号不能输入中文')
            }

            return true
        },

        setMustSubmitData() {
            const errModal = content => {
                this.type_id = 3
                this.$uni.showModal(content)
                return false
            }


            for (let i = 0; i < this.conf.must_submit.length; i++) {
                const v = this.conf.must_submit[i]
                if (!v.title) return errModal('参与活动需要填写的信息选项填写不完整，请检查。')

                v.name = pinyin(v.title, {toneType: 'none', type: 'array'}).join('_')
                if (v.types === 2) {
                    if (!v.options || !v.options.length) return errModal(`${v.title} 至少需要添加一个选项。`)

                    for (let j = 0; j < v.options.length; j++) {
                        const v1 = v.options[j]
                        if (!v1 || !v1.text) return errModal(`${v.title} 有未填写的选项，请检查。`)
                    }
                }
            }

            return true
        },


        async confCheck() {
            const confErrorToast = (title, type_id = 3) => {
                this.type_id = type_id
                this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none')
                return false
            }
            const conf = JSON.parse(JSON.stringify(this.conf))
            if (conf.active.enter_types === 2) {
                if (!this.have_password && !conf.active.password) return confErrorToast('请输入活动密码')
                
                if (conf.active.password && conf.active.password.length < 3) 
                    return confErrorToast('活动密码不得少于3位')
                
                if (conf.active.password && conf.active.password.length > 20) 
                    return confErrorToast('活动密码不得大于20位')
                
                conf.active.password = conf.active.password || this.old_password
            } else {
                conf.active.password = 1
            }


            const {begin, end} = conf.active.submit
            if (begin || end) {
                if (begin && end) {
                    const begin_time = new Date(begin.replace(/-/g, '/')).getTime()
                    const end_time = new Date(end.replace(/-/g, '/')).getTime()
                    if (begin_time > end_time) return confErrorToast('报名开始时间不能大于报名结束时间')
                }
            } else {
                delete conf.active.submit
            }


            // OA没有开启文章阅读须知，不提交阅读须知设置
            if (!this.rank_set?.active_details_notice) {
                delete conf.active.active_details_notice
            }

            // 最低兑换步数
            conf.active.min_num = ''
            // 积分数过关，步数任务，每日步数上限
            conf.active.max_num = ''
            const {types, job_list} = conf.active.job_set || {}
            const step_task = job_list.find(v => v.types === 1)
            if (step_task) {
                if (step_task?.min_num) conf.active.min_num = step_task.min_num

                // 按积分数过关才能设置每日步数兑换上限，因为判断任务是否已完成需要按照兑换步数是否达到上限来判断
                if (types === 2) {
                    if (step_task?.max) conf.active.max_num = step_task.max
                }
            }

            
            if (!conf.active.article_reading.category_id) delete conf.active.article_reading
            if (!conf.active.task_active_ai_sport_task_merge) {
                delete conf.active.task_active_ai_sport_task_merge
            }
            if (!conf.active.integral_left_hide) delete conf.active.integral_left_hide
            if (this.rank_set.batch_import) {
                conf.active.batch_import_label.username ||= '请输入账号'
                conf.active.batch_import_label.password ||= '请输入密码'
            } else {
                delete conf.active.batch_import_label
            }
            
            if (this.addedTasks['types_6']) {
                const {circle_set, integral, types} = conf.active.daily_sign
                if (types === 1) {
                    const integral_ = Number(integral)
                    if (isNaN(integral_)) return confErrorToast(`请输入正确的每日签到奖励${this.integralUnit}数量`, 4)
                    conf.active.daily_sign.integral = integral_
                } else {
                    if (!circle_set.length) return confErrorToast(`请设置周期签到奖励${this.integralUnit}数`, 4)
                    for (let i = 0, len = circle_set.length; i < len; i++) {
                        const integral_ = Number(circle_set[i].integral)
                        if (isNaN(integral_)) return confErrorToast(`周期签到奖励第${i + 1}天${this.integralUnit}数设置有误`, 4)
                        conf.active.daily_sign.circle_set[i].integral = integral_
                    }
                }
            } else {
                delete conf.active.daily_sign
            }

            // 任务闯关活动需要将地图类型设置为手绘地图，不然的话会是无需地图，点位列表不会返回点位
            conf.active.map_types = 102

            const types_31_32_33_34_all_types_max = Math.floor(this.types_31_32_33_34_all_types_max)
            if (isNaN(types_31_32_33_34_all_types_max) || types_31_32_33_34_all_types_max < 0) {
                return confErrorToast(`每周运动任务活动期间内${this.integralUnit}奖励上限设置有误`, 4)
            }

            conf.active.job_set.job_list.forEach(item => {
                if (item.types === 9 || item.types === 12 || item.types === 18) {
                    if (item.integral_type === 'integral') {
                        delete item.per_integral
                    } else {
                        delete item.integral
                    }
                }
                delete item.integral_type
                
                if ([31, 32, 33, 34].includes(item.types)) {
                    if (types_31_32_33_34_all_types_max) {
                        item.all_types_max = types_31_32_33_34_all_types_max
                    } else {
                        delete item.all_types_max
                    }
                }

                // 固定为每周报名人数限制
                if ([31, 33, 34].includes(item.types) && item.job_stock) {
                    // 设定周期   job_period   是 一周能报这么多人 还是一个月能报这么多人 目前客户是要求  一周报这么多人
                    // 1 代表查询当天  7代表查询一周（自然周） 30 代表查询本月（自然月）
                    item.job_period = 7
                }
            })
            
            if (this.addedTasks['types_12']) {
                const seconds = Math.floor(conf.active.grab_grain_game_set.seconds)
                if (isNaN(seconds) || seconds < 0) return confErrorToast('请输入正确的抢粮食任务时间限制', 4)
                const target_grain = Math.floor(conf.active.grab_grain_game_set.target_grain)
                if (isNaN(target_grain) || target_grain < 0) return confErrorToast('请输入正确的抢粮食任务目标粮食', 4)
                
                if (seconds === 0 && target_grain === 0) {
                    delete conf.active.grab_grain_game_set
                } else {
                    conf.active.grab_grain_game_set.seconds = seconds || ''
                    conf.active.grab_grain_game_set.target_grain = target_grain || ''
                }
            } else {
                delete conf.active.grab_grain_game_set
            }

            if (!this.addedTasks['types_3']) {
                delete conf.active.sport_moment_name
                delete conf.active.sport_moment_input_placeholder
            } else {
                if (!conf.active.sport_moment_input_placeholder) delete conf.active.sport_moment_input_placeholder
                if (!conf.active.sport_moment_name) delete conf.active.sport_moment_name
            }

            if (!this.rank_set.gift_goods || !conf.active.task_list_show_shop.show) {
                delete conf.active.task_list_show_shop
            }
            if (!this.rank_set['lottery_open'] || !conf.active.task_list_show_lottery.show) {
                delete conf.active.task_list_show_lottery
            }

            // 没有开通数据导出功能不能自定义配置任务列表颜色  和默认配置一样，无需保存
            if (!this.rank_set['export_top_rank_excel'] || JSON.stringify(conf.active.task_popup_style) === JSON.stringify(config.taskPopupDefaultStyle())) {
                delete conf.active.task_popup_style
            }


            if (this.addedTasks['types_14']) {


                // 如果添加了找茬任务，并且没有添加找茬题库，需要添加找茬题库
                if (!conf.active.pic_qb_cat_id) {
                    const cat_id = await this.createPicQbCat()
                    if (!cat_id) return this.$uni.showToast('保存失败,请重试')
                    conf.active.pic_qb_cat_id = cat_id
                    conf.active.game_type = 2 // 1:寻宝 2:找茬
                }
            }

            if (this.addedTasks['types_15']) {
                if (!conf.active.idiom_unison_open) delete conf.active.idiom_unison_open
                if (conf.active.idiom_tips_count) {
                    const idiom_tips_count = Number(conf.active.idiom_tips_count)
                    if (isNaN(idiom_tips_count) || idiom_tips_count <= 0) delete conf.active.idiom_tips_count
                    conf.active.idiom_tips_count = idiom_tips_count
                }
            } else {
                delete conf.active.idiom_unison_open
                delete conf.active.idiom_tips_count
            }


            if (this.addedTasks['types_18']) {
                const garbage_count = Math.floor(conf.active.garbage_count)
                if (isNaN(garbage_count) || garbage_count < 0) return confErrorToast('请设置正确的垃圾分类游戏垃圾数量', 4)
                conf.active.garbage_count = garbage_count || 10
            } else {
                delete conf.active.garbage_count
            }

            if (!this.addedTasks['types_19']) delete conf.active.timeline_preview_image

            if (this.addedTasks['types_20']) {
                if (!conf.active.share_reward_news.id) return confErrorToast('请设置转发奖励的文章', 4)
            } else {
                delete conf.active.share_reward_news
            }

            if (!this.addedTasks['types_22']) delete conf.active.one_stroke_line

            if (!this.rank_set.open_mp3 || !conf.active.audio_src) delete conf.active.audio_src
            if (!this.rank_set.team_group_open || !conf.active.team_required) {
                delete conf.active.team_required
            }

            if (conf.active.sex_required === 0) delete conf.active.sex_required
            if (!conf.active.sex_label.man && !conf.active.sex_label.woman) {
                delete conf.active.sex_label
            }

            // 云祝福任务发布祝福语默认不需要审核 三种健康度打卡任务默认不需要审核
            if (this.addedTasks['types_30'] || this.addedTasks['types_31'] || this.addedTasks['types_33'] || this.addedTasks['types_34']) conf.active.not_check = 1

            if (!this.rank_set?.['lottery_open'] || !conf.active?.task_lottery_list.length) {
                delete conf.active.task_lottery_list
            }

            if (this.addedTasks['types_31'] || this.addedTasks['types_33'] || this.addedTasks['types_34']) {
                // 三种健康打卡任务默认不能修改打卡记录
                conf.active.records_not_edit = 1

                // 三种健康打卡任务默认不能传视频
                conf.active.video_list_required = 0

                // 三种健康打卡任务打卡记录不能点赞
                conf.active.closed_likes = 1

                if (!conf.active.week_sport_task_hide_sign_list_enter) {
                    delete conf.active.week_sport_task_hide_sign_list_enter
                }
            } else {
                delete conf.active.week_sport_task_hide_sign_list_enter
            }
            if (!this.addedTasks['types_31']) delete conf.active.types_31_news
            if (!this.addedTasks['types_33']) delete conf.active.types_33_news
            if (!this.addedTasks['types_34']) delete conf.active.types_34_news

            if (!this.rank_set.exam_open || !conf.active.exchange_step_answer.open || !conf.active.exchange_step_answer.exam.id) delete conf.active.exchange_step_answer

            if (this.bodyTaskNames.length < 2 || !conf.active.body_data_submit_task_merge_set.merge) {
                delete conf.active.body_data_submit_task_merge_set
            }

            if (!this.bodyTaskNames.length || !conf.active.body_data_submit_task_first_tips.open) {
                delete conf.active.body_data_submit_task_first_tips
            }
            if (!this.bodyTaskNames.length || !conf.active.body_data_submit_task_last_tips.open) {
                delete conf.active.body_data_submit_task_last_tips
            }
            if (!this.bodyTaskNames.length || !conf.active.admin_submit_body_data) {
                delete conf.active.admin_submit_body_data
            }

            if (!conf.active.we_run_share_button_hide) delete conf.active.we_run_share_button_hide

            if (conf.active.exam_job_complete_types && this.addedTasks['types_2']) {
                if (conf.active.exam_job_complete_types === 1) {
                    // 按答题任务设置的积分奖励上限来判断答题任务是否完成，必须设置答题任务的积分奖励上限
                    const examTaskSet = conf.active.job_set.job_list.find(item => item.types === 2)
                    if (examTaskSet) {
                        const maxIntegral = Number(examTaskSet.max_integral)
                        if (isNaN(maxIntegral) || maxIntegral <= 0) {
                            return confErrorToast(`答题任务${this.integralUnit}奖励上限请填写大于0的数字`, 4)
                        }
                    }
                }
            } else {
                delete conf.active.exam_job_complete_types
            }
            
            if (!conf.active.sport_check_in_submit_diy_form.length) delete conf.active.sport_check_in_submit_diy_form

            if (this.addedTasks['types_25'] && conf.active.fan_fan_le_memory_time) {
                const num = Math.floor(conf.active.fan_fan_le_memory_time)
                if (isNaN(num) || num <= 0) return confErrorToast('请设置正确的翻翻乐任务记忆时间', 4)
            } else {
                delete conf.active.fan_fan_le_memory_time
            }

            if (this.addedTasks['types_37']) {
                const text_list = conf.active.tongue_twister.text_list
                if (!text_list.length) return confErrorToast('请配置绕口令', 4)
                const accuracy = Number(conf.active.tongue_twister.accuracy)
                if (isNaN(accuracy) || accuracy < 0 || accuracy > 100) return confErrorToast('请设置正确的绕口令准确率', 4)
                conf.active.tongue_twister.accuracy = accuracy
            } else {
                delete conf.active.tongue_twister
            }

            if (!conf.active.activity_rules_hide) delete conf.active.activity_rules_hide

            if (!this.addedTasks['types_3'] && !this.addedTasks['types_45']) {
                delete conf.active.sport_moment_user_cannot_delete
                delete conf.active.sport_moment_min_pic
                delete conf.active.sport_moment_just_take_photo
            }

            if (this.rank_set?.medalSetting) {
                const limit = Math.floor(conf.active.week_sign_limit)
                if (isNaN(limit) || limit < 0) return confErrorToast('请输入正确的每周打卡次数上限', 6)
                conf.active.week_sign_limit = limit

                if (conf.active.sign_times_type === 2) {
                    const num = Math.floor(conf.active.daily_submit_num)
                    if (isNaN(num) || num <= 0) return confErrorToast('请输入正确的每日打卡次数上限', 6)
                    conf.active.daily_submit_num = num
                } else {
                    delete conf.active.daily_submit_num
                }

                if (conf.active.integral.per_point_reward) {
                    const num = Number(conf.active.integral.per_point_reward)
                    if (isNaN(num) || num < 0) {
                        return confErrorToast('每次打卡奖励设置不正确', 6)
                    }
                    conf.active.integral.per_point_reward = num
                } else {
                    delete conf.active.integral.per_point_reward
                }

                // 直接设置打卡不用审核，不然周次数那里不好判断本周打卡次数
                conf.active.not_check = 1
            } else {
                delete conf.active.reward_medal
                delete conf.active.week_sign_limit
                delete conf.active.task_list_show_sign
                delete conf.active.point_show_type
                delete conf.active.clock_in_on_site
                delete conf.active.sign_times_type
                delete conf.active.daily_submit_num
                delete conf.active.integral.per_point_reward
            }

            let conf_str = JSON.stringify(conf)
            conf_str = conf_str.replace(/·/g, '-')
            return base64['encode'](conf_str)
        },


        async createPicQbCat() {
            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.user.category/create_category',
                data: {
                    name: `党建寻宝活动-${this.form_data.name} 关卡题库`,
                    types: 4,
                    active_id: this.form_data.active_id
                }
            })
            uni.hideLoading()
            return res?.data?.['res']
        },


        JobCheck() {
            const errToast = title => {
                this.type_id = 4
                this.$uni.showToast(title)
            }

            const job_set = this.conf.active.job_set
            const job_list = job_set.job_list
            if (!job_list) {
                errToast('请设置任务')
                return false
            }

            if (this.conf.active.job_set.types === 1) {
                const must_finished_num = Math.floor(job_set.must_finished_num)
                if (isNaN(must_finished_num)) {
                    errToast('请输入正确的过关任务数')
                    return false
                }
                if (must_finished_num < 1) {
                    errToast('过关任务数不能小于1')
                    return false
                }
                if (must_finished_num > job_list.length) {
                    errToast('过关任务数不能大于任务数量')
                    return false
                }
            }

            if (job_list.find(v => v.types === 4) && !this.conf.AI_motion.motion_list.length) {
                errToast('任务选择了AI运动, 请添加AI运动', 'none', 2000)
                return false
            }

            if (job_list.find(v => v.types === 5) && !this.conf.active.reading_reward.rules.length) {
                errToast('任务选择了文章阅读奖励, 请添加奖励的文章分类', 'none', 3000)
                return false
            }

            return true
        },


        async save() {
            if (this.success) return false

            const data = JSON.parse(JSON.stringify(this.form_data))


            if (!this.dataCheck(data)) return false
            if (!this.setMustSubmitData()) return false
            if (!this.JobCheck()) return false

            const conf = await this.confCheck()
            if (!conf) return false
            data.conf = conf

            data.begin_time /= 1000
            data.end_time /= 1000

            await this.saveAjax(data)
        },

        async saveAjax(data) {
            data.types = this.types

            this.loading = true
            this.$uni.showLoading('保存中...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.admin/create_active',
                data
            })
            uni.hideLoading()
            this.loading = false

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '保存失败')

            this.updatePageData()

            if (data.active_id) {
                this.$uni.showToast('保存成功','success')
                return this.$uni.navigateBack(1, {delay: 1000})
            }

            this.success = true
        },

        // 活动修改/创建成功，如果页面栈有详情或列表页面，更新页面数据
        updatePageData() {
            const pages = getCurrentPages()

            const update_page_list = [
                {
                    route: 'pages/task-challenge/user/details',
                    _function: page => {
                        page.$vm.getDetail(true)
                    }
                },
                {
                    route: 'pages/task-challenge/admin/manage',
                    _function: page => {
                        page.$vm.getDetail()
                    }
                },
                {
                    route: 'pages/activity/user/index',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getActivityList()
                    }
                },
                {
                    route: 'pages/activity/user/activity_list',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getList()
                    }
                }
            ]

            update_page_list.forEach(v => {
                const page = pages.find(n => n.route === v.route)
                if (page) v._function(page)
            })
        },

        lookMyActivityList() {
            const list_page_route = 'pages/activity/user/activity_list'
            const pages = getCurrentPages()
            const list_page_index = pages.findIndex(v => {
                return v.route === list_page_route && v.options.type && v.options.type === 'create'
            })
            if (list_page_index === -1) return this.$uni.redirectTo(`/${list_page_route}?type=create`)

            this.$uni.navigateBack(pages.length - list_page_index - 1)
        },

        bindTaskLottery() {
            this.$uni.navigateTo(`/pages/lottery/admin/lottery/list?active_id=${this.form_data.active_id}&task_lottery=1&select_task=1`, {
                events: {
                    selectTaskLottery: lottery => {
                        if (this.conf.active.task_lottery_list.find(v => v.lottery_id === lottery.lottery_id)) {
                            return setTimeout(() => {
                                this.$uni.showToast('该抽奖活动已绑定，请勿重复绑定', 'none', 3000)
                            }, 300)
                        }
                        this.conf.active.task_lottery_list.push(lottery)
                    }
                }
            })
        },


        deBindTaskLottery(index) {
            this.conf.active.task_lottery_list.splice(index, 1)
        },
    }
}
</script>

<style lang="scss">
.page {
    padding-top: 40px;
    padding-bottom: 75px;
}

.type-list {
    position: fixed;
    z-index: 9;
    width: 100vw;
    top: 0;
    left: 0;
}

.type-item {
    height: 40px;
    line-height: 40px;
    text-align: center;
    box-sizing: border-box;
}

.active-type {
    color: #007AFF;
    border-bottom: 2px solid #007AFF;
}

.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}

.form-item .bottom {
    border-bottom: 1px solid #eee;
}

.form-item .textarea {
    width: 100%;
    line-height: 16px;
    padding: 10px 0;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}

.must-submit-list {
    border-bottom: 1px dashed #eee;
}

.must-rules-picker {
    line-height: 34px;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    margin-left: 5px;
    padding: 0 15px;
}

.delete-rules {
    line-height: 36px;
    padding: 0 10px;
}

.must-options-item {
    padding: 3px 0;
}


.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
    line-height: calc((100vw - 50px) / 3);
    margin: 5px;
}

.image-item {
    width: calc(100vw - 20px);
    height: 50vw;
    border-radius: 5px;
}


.image-view {
    position: relative;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}

.bottom-btn-view {
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.login-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}

.save-success {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 999999;
    box-sizing: border-box;
    padding-top: 10vh;
}

.save-success .icon {
    padding-bottom: 50px;
}

.uni-date-x {
    padding: 0 !important;
}

.uni-date__x-input {
    font-size: 16px !important;
    color: #000;
}

.forms-picker {
    border-radius: 4px;
    border: 1px solid #e5e5e5;
    padding: 0 5px;
    height: 36px;
    line-height: 36px;
}

.input-word {
    line-height: 37px;
    color: #666;
    font-size: 14px;
}

.input-container {
    width: 80px;
    margin: 0 2px;
}

/* #ifdef H5 */

@media screen and (min-width: 500px) {
    .type-list {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }


    .image-item {
        width: 480px;
        height: 200px;
    }
}

/* #endif */

.ai-sport-list {
    .ai-sport-item {
        position: relative;
        background-color: #f8f8f8;
        border-radius: 5px;
        margin: 5px;
        padding: 10px;

        .ai-sport-item-logo, .task-item-logo {
            margin-right: 10px;
        }
    }
}

.item-controls {
    position: absolute;
    right: 0;
    top: 0;
}

.item-info {
    font-size: 14px;
    color: #495060;
    .item-info-label {
        white-space: nowrap;
        padding-right: 3px;
    }
}

.ai-sport-item-logo, .task-item-logo {
    position: relative;
    
    image {
        display: block;
    }

    .ai-sport-item-logo-image, .task-item-logo-image {
        height: 60px;
        border-radius: 5px;
    }

    .ai-sport-item-logo-image {
        width: 90px;
        min-width: 90px;
    }

    .task-item-logo-image {
        width: 60px;
        min-width: 60px;
    }
    
    .delete-task-logo {
        position: absolute;
        top: 1px;
        right: 1px;
        width: 24px;
        height: 24px;
        border-radius: 12px;
        background-color: rgba(0, 0, 0, .7);
    }
}

.ai-sport-item-edit, .ai-sport-types-change {
    width: 95vw;
    padding: 0 10px;
    border-radius: 10px;
    box-sizing: border-box;
}

.ai-sport-item-edit {
    .ai-sport-item-edit-buttons {
        padding: 10px;
        border-top: 1px solid #eee;

        view {
            margin: 0 10px;
            width: 100px;
            line-height: 40px;
            border-radius: 20px;
            text-align: center;
        }
    }
}

.ai-sport-types-change {
    padding-bottom: 10px;

    .ai-sport-type-list {
        max-height: calc(100vh - 200px);
        overflow: auto;

        .ai-sport-type-item {
            margin: 5px;
            width: calc((100% - 30px) / 3);
            height: 60px;
            padding: 5px;
            box-sizing: border-box;
            border: 1px solid #eee;
            border-radius: 5px;
            text-align: center;
            font-size: 14px;
        }

        .selected {
            border-color: #4cd964;
            position: relative;
        }

        .selected::before {
            content: "已选";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 16px;
            line-height: 16px;
            font-size: 12px;
            text-align: center;
            color: #fff;
            border-radius: 0 0 5px 5px;
            background-color: #4cd964;
        }
    }
}


.reading-reward-rules {
    .reading-reward-rule {
        position: relative;
        background-color: #f8f8f8;
        border-radius: 5px;
        margin: 5px;
        padding: 10px;
        font-size: 14px;
        color: #495060;
    }
}

</style>
