<template>
    <view class="page bg-background">
        <view class="c-list">
            <view class="c-item flex-row" v-for="(item, index) in rankCategoryList" :key="index"
                  hover-class="navigator-hover" @click="exportItem(item)">
                <view class="icon">
                    <text class="iconfont icon-excel-file color-light-primary font34"></text>
                </view>
                <view class="pl10 w-100">
                    <view>{{ item.name }}排行榜</view>
                    <view class="clear clearfix">
                        <view class="fr color-primary">导出</view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import openExcelFile from '@/utils/open-excel-file'

export default {
    data() {
        return {
            rankCategoryList: []
        }
    },

    onLoad(params) {
        this.id = params.id
        
        // 其他活动过来导排行榜的
        if (params.just_export_type) {
            this.juset_export = true
            if (params.man_sex_label) this.man_sex_label = params.man_sex_label
            if (params.woman_sex_label) this.woman_sex_label = params.woman_sex_label
            return this.exportRanking(Number(params.just_export_type))
        }
        this.getRankCategorySet()
    },

    methods: {
        getRankCategorySet() {
            this.getOpenerEventChannel().on('rankingCategorySet', list => {
                if (list.find(v => v.id === 40)) list.push({name: '队内', id: 20, team_ranking: true})
                this.rankCategoryList = list
            })
        },

        exportItem(item) {
            if (item.team_ranking) return this.toSelectTeam(item.id)
            
            this.exportRanking(item.id)
        },

        async exportRanking(id, team_details = null) {
            this.$uni.showLoading('导出中...')
            
            const list = await this.getRankingList(id, team_details?.team_id)

            const tableData = this.excelDataProcessing(id, list)
            
            let tableName = '排行榜'
            if (this.juset_export) {
                const options = {
                    '0': '步数排行榜',
                    '7': '答题排行榜',
                    '46': '队伍排行榜',   // 跑步活动队伍排行榜
                    '15-1': `${this.man_sex_label || '男'}排行榜`,   // 跑步活动男排行榜
                    '15-2': `${this.woman_sex_label || '女'}女排行榜`,   // 跑步活动女排行榜
                }
                tableName = options[id] || '排行榜'
            } else {
                tableName = this.rankCategoryList?.find(v => v.id === id).name + '排行榜'
            }
            if (team_details?.team_name) tableName = `${team_details.team_name} 队内排行榜`

            uni.hideLoading()

            openExcelFile.openDocument(tableData, tableName)
            
            if (this.juset_export) this.$uni.navigateBack()
        },

        excelDataProcessing(id, list) {
            let unitHead = '积分'
            if (id === 0) unitHead = '步数'
            if (id === 7) unitHead = '分数'
            if (id === '15' || id === 15 || id === '46' || id === 46 || id === '15-1' || id === '15-2') {
                unitHead = '累计里程(公里)'
            }
            
            const tHead = ['排名', '系统标识', '', unitHead]
            const tBody = []
            
            list.forEach((item, index) => {
                const data = []
                data.push(index + 1)

                data.push(this.getSystemTag(id, item))
                
                if (!tHead[2]) {
                    if (id === 40 || id === '46' || id === 46) {
                        tHead[2] = '队伍'
                    } else {
                        const must_submit = item.must_submit || item.user_details?.must_submit
                        tHead[2] = must_submit?.[0]?.title || '姓名'
                    }
                }
                data.push(this.getNickname(item, id))

                data.push(this.getItemValue(item, id))
                
                tBody.push(data)
            })
            return [tHead, ...tBody]
        },

        getSystemTag(id, item) {
            if (id === 40) return `w.${app.globalData.who}t.${item.team_id || 0}`
            if (id === '46' || id === 46) return `w.${app.globalData.who}t.${item.id || 0}`
            return `w.${app.globalData.who}u.${item.userid || 0}`
        },

        getNickname(v, id) {
            if (id === 40) return v.team_details?.name || ''
            if (id === '46' || id === 46) return v.name || ''
            const must_submit = v.must_submit || v.user_details?.must_submit
            return must_submit?.[0]?.value || ''
        },

        
        getItemValue(v, id) {
            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace v.integral_all */
            /** @namespace v.allIntegral */
            /** @namespace v.ai_motion_integral_num */
            /** @namespace v.run_all_meter */
            /** @namespace v.run_avg_meter */
            
            if (id === '15-1' || id === '15-2') return this.m2km(v.meter) // ==== 跑步运动 男/女 排行榜

            const values = {
                0: () => v.exchange_step, // ================================== 步数
                7: () => v.score, // ========================================== 答题
                20: () => v.integral_all, // ================================== 个人总积分
                40: () => v.allIntegral, // =================================== 队伍积分排行榜
                41: () => v.ai_motion_integral_num, // ======================== AI运动积分排行榜
                '46': () => this.m2km(v.run_all_meter || v.run_avg_meter), // = 跑步运动队伍排行榜
            }
            return values[id]?.() || 0
        },

        m2km: (m = 0) => Math.floor(m / 10) / 100,

        async getRankingList(id, team_id = null) {
            let top_rank_types = id, sex = 0
            if (id === '15-1') {
                top_rank_types = 15
                sex = 1
            }
            if (id === '15-2') {
                top_rank_types = 15
                sex = 2
            }
            const data = {
                active_id: this.id,
                page: 1,
                perpage: 3000,
                top_rank_types
            }
            if (team_id) data.team_id = team_id
            if (sex) data.sex = sex
            
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.step/top_rank_list',
                data
            })
            
            return res?.data?.top_rank_list?.list?.data || []
        },

        toSelectTeam(id) {
            this.$uni.navigateTo(`/pages/activity/admin/team_list?selteam=1&is_manage=1&id=${this.id}`, {
                events: {
                    setTeam: data => this.exportRanking(id, data)
                }
            })
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
}

.c-list {
    padding-top: 1px;
    padding-bottom: 20px;
}

.c-item {
    padding: 15px;
    margin: 15px;
    background-color: #fff;
    border-radius: 5px;
}
</style>