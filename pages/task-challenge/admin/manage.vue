<template>
    <view class="page bg-background">
        <activity-delete-tips v-if="error" :activeId="id"/>


        <view v-if="detail && detail.active_id">
            <!-- #ifndef H5 -->
            <image v-if="detail.logo" class="logo" mode="widthFix" :src="detail.logo"/>
            <!-- #endif -->
            <view class="bg-white p10">
                <view class="color-title font18" @click="copy(id)">{{ detail.name }}</view>
                <view v-if="detail.organizer" class="color-content font16">主办方：{{detail.organizer }}</view>
            </view>

            <template v-if="!detail.rank_set || !detail.rank_set.closed_AD">
                <xwy-ad v-if="!shareShow && !popup_show" :ad_type="4"></xwy-ad>
                <xwy-ad :ad_type="3"></xwy-ad>
            </template>


            <view class="tools bg-white">
                <view class="title flex-kai">
                    <view class="color-title">活动管理</view>
                    <navigator v-if="document_show" url="/pages/other/activity-document?types=5">
                        <text class="iconfont icon-word-file color-light-primary font14"></text>
                        <text class="font14 color-light-primary" style="padding-left: 2px;">活动帮助文档</text>
                    </navigator>
                </view>

                <view class="list flex-row flex-wrap text-center">
                    <view class="item" hover-class="navigator-hover" @click="toEdit">
                        <text class="iconfont icon-edit color-primary font34"></text>
                        <view class="font14 color-content">活动修改</view>
                    </view>

                    <navigator :url="'/pages/activity/admin/activity_user_list?id=' + id" class="item">
                        <text class="iconfont icon-users color-primary font34"></text>
                        <view class="font14 color-content">活动用户</view>
                    </navigator>

                    <navigator :url="'/pages/activity/admin/blacklist?id=' + id" class="item">
                        <text class="iconfont icon-blacklist color-primary font34"></text>
                        <view class="font14 color-content">黑名单用户</view>
                    </navigator>

                    <template v-if="detail.rank_set && detail.rank_set['team_group_open']">

                        <navigator :url="`/pages/activity/admin/team_list?is_admin=1&id=${id}&active_types=${detail.types}`" class="item">
                            <text class="iconfont icon-team color-primary font34"></text>
                            <view class="font14 color-content">队伍管理</view>
                        </navigator>


                        <navigator class="item" v-if="detail.rank_set && detail.rank_set['team_category']"
                                   :url="'/pages/category/list?types=19&active_id=' + id">
                            <text class="iconfont icon-dating color-primary font34"></text>
                            <view class="font14 color-content">队伍分类</view>
                        </navigator>
                    </template>


                    <navigator class="item" v-if="detail.rank_set && detail.rank_set['sportsMomentCategory']"
                               :url="'/pages/category/list?types=43&active_id=' + id">
                        <text class="iconfont icon-wechat-movement color-primary font34"></text>
                        <view class="font14 color-content">运动圈分类</view>
                    </navigator>

                    <view class="item" hover-class="navigator-hover" @click="toLevelList">
                        <text class="iconfont icon-map color-primary font34"></text>
                        <view class="font14 color-content">关卡管理</view>
                    </view>

                    <navigator
                        class="item"
                        :url="`/pages/news/list?type=user&vip=${detail.rank_set && detail.rank_set.closed_AD || 0}&active_id=${id}&just_look_active=1`"
                    >
                        <text class="iconfont icon-writing font34 color-primary"/>
                        <view class="font14 color-content">文章管理</view>
                    </navigator>

                    <navigator
                        v-if="detail.rank_set && detail.rank_set.closed_AD"
                        class="item"
                        :url="`/pages/category/list?types=8&just_look_active=1&active_id=${id}`"
                    >
                        <text class="iconfont icon-dating font34 color-primary"/>
                        <view class="font14 color-content">文章分类</view>
                    </navigator>

                    <view v-if="job_open['types_7']" class="item" hover-class="navigator-hover"
                          @click="toPuzzleList">
                        <text class="iconfont icon-image color-primary font34"></text>
                        <view class="font14 color-content">拼图任务</view>
                    </view>

                    <view v-if="job_open['types_8']" class="item" hover-class="navigator-hover"
                          @click="toHistoricalEventList">
                        <text class="iconfont icon-sort color-primary font34"></text>
                        <view class="font14 color-content">事件管理</view>
                    </view>

                    <view v-if="job_open['types_14'] && detail.rank_set && (detail.rank_set.closed_AD || detail.rank_set.exam_open)"
                          class="item" hover-class="navigator-hover" @click="toPictureDiffManage">
                        <text class="iconfont icon-grid color-primary font34"></text>
                        <view class="font14 color-content">找茬关卡</view>
                    </view>


                    <navigator v-if="job_open['types_30']" class="item"
                               :url="'/pages/cloud_wish/wish_list?look_type=admin&id=' + id">
                        <uni-icons type="chat" size="34" color="#2d8cf0"/>
                        <view class="font14 color-content">祝福语管理</view>
                    </navigator>


                    <navigator v-if="job_open['types_31'] || job_open['types_33'] || job_open['types_34']"
                               :url="'/pages/clock_in/user/public_sign_list?is_admin=1&id=' + id"
                               class="item">
                        <text class="iconfont icon-8 color-primary font34"></text>
                        <view class="font14 color-content">运动打卡记录</view>
                    </navigator>
                    
                    <view class="item" @click="copyActivityPages">
                        <text class="iconfont icon-copy color-primary font34"></text>
                        <view class="font14 color-content">复制路径</view>
                    </view>

                    <!-- #ifndef H5 -->
                    <view class="item" @click="showWebUrl">
                        <text class="iconfont icon-screen color-primary font34"></text>
                        <view class="font14 color-content">web端管理</view>
                    </view>
                    <!-- #endif -->

                    <navigator class="item" url="/pages/other/contact">
                        <uni-icons type="chatboxes" size="34" color="#2d8cf0"/>
                        <view class="font14 color-content">联系客服</view>
                    </navigator>

                    

                    <view class="item" @click="shareShow = true">
                        <text class="iconfont icon-share color-primary font34"></text>
                        <view class="font14 color-content">转发分享</view>
                    </view>

                </view>
            </view>

            <view v-if="detail.rank_set && detail.rank_set['batch_import']" class="tools bg-white">
                <view class="title color-title">活动用户导入</view>
                <view class="list flex-row flex-wrap text-center">
                    <view hover-class="navigator-hover" class="item" @click="importUsers">
                        <text class="iconfont icon-import color-primary font34"></text>
                        <view class="font14 color-content">批量导入</view>
                    </view>


                    <navigator class="item" :url="'../../activity/admin/import_users/user_list?id=' + id">
                        <text class="iconfont icon-users color-primary font34"></text>
                        <view class="font14 color-content">用户名单</view>
                    </navigator>

                    <view hover-class="navigator-hover" class="item" @click="downloadImportUsersTemplate">
                        <text class="iconfont icon-export-excel color-primary font34"></text>
                        <view class="font14 color-content">模版下载</view>
                    </view>
                </view>

            </view>
            

            <view class="tools bg-white">
                <view class="title color-title">数据导出</view>
                <view class="list flex-row flex-wrap text-center">
                    <view class="item" @click="exportBackendRanking(48)">
                        <text class="iconfont icon-trophy color-primary font34"></text>
                        <view class="font14 color-content">个人排行榜</view>
                    </view>

                    <view v-if="rankingJsonConfig['types_58']" class="item" @click="exportDayIntegralRanking">
                        <text class="iconfont icon-trophy color-primary font34"></text>
                        <view class="font14 color-content">日{{ integralUnit }}排行榜</view>
                    </view>

                    <view v-if="job_open['types_1']" class="item" @click="exportStepRanking">
                        <text class="iconfont icon-footnotes color-primary font34"></text>
                        <view class="font14 color-content">步数排行榜</view>
                    </view>

                    <view v-if="job_open['types_27'] || job_open['types_28'] || job_open['types_29']"
                          class="item" @click="exportBackendRanking(36)">
                        <text class="iconfont icon-electronic-scale color-primary font34"></text>
                        <view class="font14 color-content">减重排行榜</view>
                    </view>

                    <view v-if="job_open['types_30']" class="item" @click="exportBackendRanking(16)">
                        <uni-icons type="chat" size="34" color="#2d8cf0"/>
                        <view class="font14 color-content">云祝福点赞排行榜</view>
                    </view>
                    
                    <view class="item" v-if="rank_category_list_open" @click="toExportOtherRanking">
                        <text class="iconfont icon-grid color-primary font34"></text>
                        <view class="font14 color-content">其他排行榜</view>
                    </view>

                    <view class="item" hover-class="navigator-hover" @click="exportRankingData(21)">
                        <text class="iconfont icon-users color-primary font34"></text>
                        <view class="font14 color-content">活动用户</view>
                    </view>

                    <view class="item" hover-class="navigator-hover" @click="exportIntegralRecord">
                        <text class="iconfont icon-integral color-primary font34"></text>
                        <view class="font14 color-content">{{ integralUnit }}记录</view>
                    </view>

                    <template v-if="detail.rank_set && detail.rank_set.open_sport_moment">
                        <view v-if="job_open.types_3" class="item" hover-class="navigator-hover"
                              @click="exportSportMoment(6)">
                            <text class="iconfont icon-wechat-movement color-primary font34"></text>
                            <view class="font14 color-content">运动圈数据</view>
                        </view>

                        <view v-if="job_open.types_45" class="item" hover-class="navigator-hover"
                              @click="exportSportMoment(17)">
                            <text class="iconfont icon-wechat-movement color-primary font34"></text>
                            <view class="font14 color-content">动态圈数据</view>
                        </view>
                    </template>

                    <template v-if="task_31_33_34.length">
                        <view v-for="item in task_31_33_34" :key="item.types" class="item"
                              hover-class="navigator-hover" @click="exportTask_31_33_34(item)">
                            <text class="iconfont icon-8 color-primary font34"></text>
                            <view class="font14 color-content">{{ item.title }}记录</view>
                        </view>
                    </template>

                    <view v-if="exportAiSportIntegralTopOpen" class="item" hover-class="navigator-hover"
                          @click="exportAiSportIntegralTop">
                        <text class="iconfont icon-trophy color-primary font34"></text>
                        <view class="font14 color-content">AI运动{{ integralUnit }}排行榜</view>
                    </view>

                    <template v-if="detail.rank_set && detail.rank_set.medalSetting">
                        <view class="item" @click="toFrontExportRanking(61)">
                            <text class="iconfont icon-integral color-primary font34"></text>
                            <view class="font14 color-content">{{ integralUnit }}勋章排行榜</view>
                        </view>

                        <template v-if="detail.conf.active.reward_medal && detail.conf.active.reward_medal.list && detail.conf.active.reward_medal.list.length">
                            <view v-for="item in detail.conf.active.reward_medal.list" :key="item.types"
                              class="item" hover-class="navigator-hover" @click="exportMedalCount(item)">
                            <text class="iconfont icon-medal color-primary font34"></text>
                            <view class="font14 color-content">{{ item.title }}勋章导出</view>
                        </view>
                        </template>
                    </template>

                    <navigator
                        class="item"
                        url="/pages/activity/admin/export_record"
                    >
                        <text class="iconfont icon-export-excel color-primary font34"></text>
                        <view class="font14 color-content">导出记录</view>
                    </navigator>
                </view>

            </view>


            <view v-if="detail.rank_set && detail.rank_set.medalSetting" class="tools bg-white">
                <view class="title color-title">打卡管理</view>

                <view class="list flex-row flex-wrap text-center">
                    <navigator
                        :url="'/pages/clock_in/user/public_sign_list?is_admin=1&id=' + id + '&type=' + detail.types"
                        class="item"
                    >
                        <text class="iconfont icon-sign-in-calendar color-primary font34"></text>
                        <view class="font14 color-content">打卡审核</view>
                    </navigator>

                    <view class="item" hover-class="navigator-hover" @click="toPointList">
                        <text class="iconfont icon-map color-primary font34"></text>
                        <view class="font14 color-content">点位管理</view>
                    </view>
                </view>
            </view>


            <view v-if="exam_open" class="tools bg-white">
                <view class="title color-title">答题管理</view>

                <view class="list flex-row flex-wrap text-center">
                    <navigator class="item" url="/pages/likou_dati/pages/exam/exam_list/exam_list">
                        <text class="iconfont icon-examination-paper color-primary font34"></text>
                        <view class="font14 color-content">我的考卷</view>
                    </navigator>

                    <navigator class="item"
                        url="/pages/likou_dati/pages/question/question_bank_list/question_bank_list">
                        <text class="iconfont icon-dictionary color-primary font34"></text>
                        <view class="font14 color-content">我的题库</view>
                    </navigator>

                    <navigator class="item"
                        url="/pages/likou_dati/pages/question/category_list/category_list">
                        <text class="iconfont icon-dating color-primary font34"></text>
                        <view class="font14 color-content">题库分类</view>
                    </navigator>

                    <navigator class="item"
                        url="/pages/likou_dati/pages/question/batch_import_question/explain/explain">
                        <text class="iconfont icon-import color-primary font34"></text>
                        <view class="font14 color-content">批量导题</view>
                    </navigator>

                </view>
            </view>


            <view
                v-if="detail.rank_set && (detail.rank_set['lottery_open'] || detail.rank_set['mystery_box'])"
                class="tools bg-white">
                <view class="title color-title">抽奖活动管理</view>
                <view class="list flex-row flex-wrap text-center">
                    <view class="item" hover-class="navigator-hover" @click="lotteryManage">
                        <text class="iconfont icon-dating font34 color-primary"></text>
                        <view class="font14 color-content">活动列表</view>
                    </view>

                    <template v-if="detail.rank_set['lottery_open']">

                        <navigator
                            class="item"
                            :url="'/pages/lottery/user/lottery_record_list?active_id=' + id + '&is_admin=1'"
                        >
                            <text class="iconfont icon-gift font34 color-primary"></text>
                            <view class="font14 color-content">抽奖记录</view>
                        </navigator>
                        <view class="item" hover-class="navigator-hover" @click="exportLotteryRecord">
                            <text class="iconfont icon-export-excel font34 color-primary"></text>
                            <view class="font14 color-content">导出抽奖记录</view>
                        </view>
                    </template>
                </view>
            </view>


            <view v-if="detail.rank_set && detail.rank_set['gift_goods']" class="tools bg-white">
                <view class="title color-title">商城管理</view>

                <view class="list flex-row flex-wrap text-center">
                    <view class="item" @click="toShopManage">
                        <uni-icons type="shop" size="34" color="#2d8cf0"/>
                        <view class="font14 color-content">商品管理</view>
                    </view>

                    <navigator class="item"
                        :url="'/pages/shop/order/order_list?is_admin=1&active_id=' + id + '&unit=' + (detail.conf.active.integral && detail.conf.active.integral.unit || '金币')">
                        <uni-icons type="list" size="34" color="#2d8cf0"/>
                        <view class="font14 color-content">订单记录</view>
                    </navigator>

                </view>
            </view>


            <view v-if="job_open['types_18']" class="tools bg-white">
                <view class="title color-title">垃圾分类游戏管理</view>

                <view class="list flex-row flex-wrap text-center">

                    <navigator class="item"
                               :url="`/pages/category/list?types=37&just_look_active=1&active_id=${id}`">
                        <text class="iconfont icon-delete color-primary font34"></text>
                        <view class="font14 color-content">垃圾桶管理</view>
                    </navigator>


                    <navigator class="item"
                               :url="`/pages/games/garbage-sorting/garbage-manage/list?active_id=${id}`">
                        <text class="iconfont icon-grid color-primary font34"></text>
                        <view class="font14 color-content">垃圾管理</view>
                    </navigator>

                </view>
            </view>


        </view>


        <canvas id="qrcode" canvas-id="qrcode"
                style="width: 500px; height: 500px; position: fixed; top: -999999px; left: -99999px;">
        </canvas>
        <view v-if="shareShow" class="share-popup" @click="shareShow = false">
            <view class="share-popup-main bg-white">
                <view class="flex-row">
                    <!-- #ifndef H5 -->
                    <button class="share-item" open-type="share">
                        <text class="iconfont icon-share color-primary font28"/>
                        <view class="color-sub font14">转发给微信好友</view>
                    </button>
                    <!-- #endif -->
                    <button class="share-item" @click="showShareImage">
                        <text class="iconfont icon-qr-code font28 color-warning"/>
                        <view class="color-sub font14">生成活动二维码</view>
                    </button>
                </view>
                <view class="share-cancel font16 color-sub text-center">取消</view>
            </view>
        </view>


        <uni-popup ref="not_export_ranking_tips" type="center">
            <view class="export_ranking_popup text-center bg-white">

                <view class="popup-close" @click="uniPopupClose('not_export_ranking_tips')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>

                <view class="top" style="padding: 50px 20px 20px;">
                    <text class="iconfont icon-export-excel color-green" style="font-size: 80px;"></text>
                    <view class="font14 color-sub">未开通导出数据功能</view>
                    <view class="font14 color-sub">请联系客服开通</view>
                </view>

                <view class="bg-white color-info" style="padding: 20px;">
                    <button
                        open-type="contact"
                        class="export_ranking-btn bg-green color-white font16"
                        @click="uniPopupClose('not_export_ranking_tips')"
                    >联系客服开通
                    </button>
                </view>

            </view>
        </uni-popup>

        <uni-popup ref="not_export_exam_tips" type="center">
            <view class="export_ranking_popup text-center bg-white">

                <view class="popup-close" @click="uniPopupClose('not_export_exam_tips')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>

                <view class="top" style="padding: 50px 20px 20px;">
                    <text class="iconfont icon-export-excel color-green" style="font-size: 80px;"></text>
                    <view class="font14 color-sub">未开通导出答题排行榜数据功能</view>
                    <view class="font14 color-sub">请联系客服开通</view>
                </view>

                <view class="bg-white color-info" style="padding: 20px;">
                    <button open-type="contact" class="export_ranking-btn bg-green color-white font16"
                            @click="uniPopupClose('not_export_exam_tips')">联系客服开通
                    </button>
                </view>

            </view>
        </uni-popup>


        <uni-popup ref="export_ranking_success" type="center">
            <view class="export_ranking_popup text-center bg-white">

                <view class="popup-close" @click="uniPopupClose('export_ranking_success')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>

                <view class="top bg-primary color-white" style="padding: 50px 20px 20px;">
                    <icon type="success" size="80" color="#ffffff"></icon>
                    <view class="font18">导出成功</view>
                </view>

                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ export_ranking_success_tips }}
                    </view>
                    <view class="export_ranking-btn bg-info color-white"
                        hover-class="navigator-hover" @click="copyDownloadSrc(false)">复制下载地址
                    </view>
                </view>

            </view>
        </uni-popup>

        <uni-popup ref="copy_web_src" type="center">
            <view class="export_ranking_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('copy_web_src')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="top bg-primary color-white" style="padding: 50px 20px 20px;">
                    <text class="iconfont icon-screen color-white" style="font-size: 80px;"></text>
                    <view class="font18">{{ copy_popup.title }}</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ copy_popup.content }}
                    </view>
                    <view class="export_ranking-btn bg-info color-white" hover-class="navigator-hover"
                          @click="copy(copy_popup.src)">{{ copy_popup.button }}
                    </view>
                </view>
            </view>
        </uni-popup>


        <web-admin-src-copy-popup ref="web-admin-src-copy-popup"/>

        
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import my_storage from '@/utils/storage.js'

export default {
    data() {
        return {
            loading: true,
            id: '',
            detail: {},
            error: '',
            shareShow: false,
            export_ranking_success_tips: '',
            exam_open: false,
            popup_show: false,
            document_show: this.xwy_config.showActivitySpecificationDocument(25),
            rank_category_list_open: false,
            copy_popup: {
                title: '',
                content: '',
                button: '',
                src: ''
            },
            rankingJsonConfig: {}
        }
    },

    computed: {
        job_open() {
            const job_open = {}
            this.detail?.conf?.active?.job_set?.job_list?.forEach(item => {
                job_open[`types_${item.types}`] = true
            })
            return job_open
        },

        task_31_33_34() {
            const job_list = this.detail?.conf?.active?.job_set?.job_list || []
            return job_list.filter(({types}) => [31, 33, 34].includes(types))
                .map(({types, title}) => ({types, title}))
        },

        exportAiSportIntegralTopOpen() {
            return !!this.detail?.rank_set?.AISportsIntegralTypesExport
        },

        integralUnit() {
            return this.detail?.conf?.active?.integral?.unit || '积分'
        }
    },

    onShareAppMessage() {
        let path = '/pages/task-challenge/user/details?id=' + this.id
        return {
            title: this.detail.conf?.active?.share_title || this.detail.name || '',
            path,
            imageUrl: this.detail.conf?.active?.share_image || this.detail.logo || ''
        }
    },

    onLoad(e) {
        console.log('活动详情页面路径参数', e)
        uni.showLoading({mask: true})
        if (!e.id) {
            this.loading = false
            uni.hideLoading()
            this.error = '请指定活动id'
            return false
        }

        this.id = e.id
        login.uniLogin(err => {
            this.loading = false
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            if (app.globalData['shop_info']?.['shop_set']?.static_url) {
                this.web_base_url = app.globalData['shop_info']['shop_set'].static_url
            }


            this.getDetail()
        })
    },

    methods: {
        getDetail() {
            xwy_api.getActivityDetail(this.id, res => {
                uni.hideLoading()
                if (res.data?.['active_details']) {
                    const detail = res.data['active_details']
                    my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)
                    this.detail = detail
                    if (detail.rank_set?.exam_open) this.exam_open = true
                    
                    this.getRankCategorySet(res.data.active_more_data?.active_conf_set?.['toprank_list'])
                } else {
                    this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                }
            })
        },

        toEdit() {
            uni.navigateTo({
                url: this.xwy_config.getActivityPath(this.detail.types, 'add') + '?id=' + this.id
            })
        },

        toLevelList() {
            this.$uni.navigateTo(`./level/list?id=${this.id}`)
        },

        toPuzzleList() {
            this.$uni.navigateTo(`/pages/puzzle/puzzle-list?active_id=${this.id}&admin=1`)  
        },

        toHistoricalEventList() {
            this.$uni.navigateTo(`/pages/historical-event-sort/admin/list?active_id=${this.id}`)
        },

        toPictureDiffManage() {
            const src = `${this.web_base_url}/web/wx-cloud-api/pages/admin_manage/picture-finder#/?access_token=${app.globalData['access_token']}&active_id=${this.id}`

            let isH5 = false
            // #ifdef H5
            isH5 = true
            // #endif
            if (isH5) return window.open(src, '_blank')

            this.copy_popup.title = '找茬关卡管理'
            this.copy_popup.content = '找茬关卡管理需要通过电脑浏览器管理，请复制管理地址并在电脑浏览器打开。'
            this.copy_popup.button = '复制管理地址'
            this.copy_popup.src = src

            this.uniPopupOpen('copy_web_src')

            uni.setClipboardData({
                data: src,
                success: () => uni.hideToast()
            })
        },

        uniPopupOpen(ref) {
            this.popup_show = true
            this.$refs[ref].open()
        },

        uniPopupClose(ref) {
            this.popup_show = false
            this.$refs[ref].close()
        },

        toPointList() {
            const rank_set = this.detail.rank_set || {}
            // 打卡点位增加字段类型 types  判断
            // 0   是之前的关卡 id及健步走活动点位，打卡活动的打卡点
            // 1 代表是任务闯关活动里的打卡点（闯关里需要进行打卡，打卡的记录）
            let url = '/pages/clock_in/admin/point/list?types=1&id=' + this.id + '&rank_set=' + JSON.stringify(rank_set) + '&active_small_id=' + this.detail.id
            
            this.$uni.navigateTo(url)
        },

        exportRankingData(types) {
            // 可免免费导出 2025-02-12 09:20:41
            // const export_top_rank_excel_open = this.detail.rank_set?.['export_top_rank_excel']
            // if (!export_top_rank_excel_open) return this.uniPopupOpen('not_export_ranking_tips')

            this.exportRankingDataAjax(types)
        },


        exportTask_31_33_34(item) {
            this.$uni.navigateTo(`./export-week-sport-user-list?active_id=${this.id}&types=${item.types}&title=${item.title}`)
        },

        exportMedalCount(item) {
            this.$uni.navigateTo(`./export-medal-count?active_id=${this.id}&types=${item.types}&title=${item.title}`)
        },

        exportAiSportIntegralTop() {
            this.$uni.navigateTo(`/pages/other/export-ai-sport-integral-list?active_id=${this.id}`)
        },

        async exportSportMoment(types) {
            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.user.reply/reply_view_list',
                data: {
                    page: 1,
                    perpage: 1000,
                    types,
                    pid: this.id,
                    is_long_id: 1,
                    need_excel: 1,
                    reply_id: 0     // 不导出评论
                }
            })
            uni.hideLoading()
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '导出失败')

            const count = res?.['data']?.all_count || 0
            if (count > 1000) {
                return this.$uni.navigateTo(`/pages/wallet/export_rank?active_id=${this.id}&name=${this.detail.name}&person_count=${count}&type=sport_moment`)
            }

            this.export_ranking_success_tips = res['info'] || '导出成功'
            this.export_ranking_src = res['data'].url
            this.copyDownloadSrc(true)
            this.uniPopupOpen('export_ranking_success')


            const title = `导出活动【${this.detail.name}】运动圈数据`
            my_storage.setExportExcelRecord({url: res['data'].url, title})
        },
        
        getRankCategorySet(list) {
            if (!list?.length) return

            const rankingJsonConfig = {}
            list.forEach(item => {
                rankingJsonConfig[`types_${item.id}`] = true
            })
            this.rankingJsonConfig = rankingJsonConfig

            // 答题排行榜、男女排行榜、队内排行榜、队伍排行榜 才可以去导出
            const types = [7, 15, 40, 46]
            const rank_category_list = list.filter(v => types.includes(v.id))
            if (rank_category_list.length) {
                this.rank_category_list_open = true
                this.rank_category_list = rank_category_list.map(v => ({name: v.name, id: v.id}))
            } 
        },

        exportBackendRanking(types) {
            this.$uni.navigateTo(`/pages/other/export-backend-ranking?active_id=${this.id}&types=${types}`)
        },

        exportDayIntegralRanking() {
            this.$uni.navigateTo(`/pages/other/export-day-integral-ranking?active_id=${this.id}`)
        },

        exportStepRanking() {
            this.$uni.navigateTo(`./export-step-ranking`, {
                success: res => res.eventChannel.emit('data', {
                    id: this.id,
                    must_submit: this.detail.conf?.must_submit
                })
            })
        },

        toExportOtherRanking() {
            this.$uni.navigateTo(`./export-other-ranking?id=${this.id}`, {
                success: res => res.eventChannel.emit('rankingCategorySet', this.rank_category_list)
            })  
        },

        toFrontExportRanking(types) {
            this.$uni.navigateTo(`/pages/other/front-export-ranking?active_id=${this.id}&types=${types}`)
        },

        exportIntegralRecord() {
            const {conf = {}, rank_set = {}} = this.detail || {}

            // 免费导出 2025-02-12 09:22:17
            // const export_top_rank_excel_open = rank_set?.['export_top_rank_excel']
            // if (!export_top_rank_excel_open) return this.uniPopupOpen('not_export_ranking_tips')

            this.$uni.navigateTo('./export-integral-record', {
                success: res => res.eventChannel.emit('data', {
                    id: this.id,
                    must_submit: conf?.must_submit,
                    white_list_open: !!rank_set?.batch_import
                })
            })
        },


        exportRankingDataAjax(types) {
            this.$uni.showLoading('导出中...')

            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id,
                types,
                page: 1,
                perpage: 3000
            }


            xwy_api.ajax({
                url: 'front.flat.sport_step.export.admin_active/export_user_total_rank',
                data,
                success: res => {
                    uni.hideLoading()
                    if (res?.status !== 1) return this.$uni.showModal(res.info || '导出失败')

                    this.export_ranking_success_tips = res.info || '导出成功'
                    this.export_ranking_src = res.data.url
                    this.copyDownloadSrc(true)
                    this.uniPopupOpen('export_ranking_success')


                    const title_options = {
                        48: `排行榜`,
                        21: '活动用户名单',
                        36: '减重排行榜'
                    }

                    const title = `导出活动【${this.detail.name}】${title_options[types]}`
                    my_storage.setExportExcelRecord({url: res.data.url, title})
                }
            })
        },


        copyDownloadSrc(hide = false) {
            uni.setClipboardData({
                data: this.export_ranking_src,
                success: () => hide ? uni.hideToast() : this.$uni.showToast('复制成功', 'success', 1000)
            })
        },

        showShareImage() {
            if (this.share_image) {
                this.previewShareImage()
                return false
            }

            this.getShareImage()
        },


        getShareImage() {
            this.$uni.showLoading('二维码生成中...')

            const qr_data = {
                page: 'pages/task-challenge/user/details',
                scene: 'id=' + this.detail.id,
                cb: qr_src => {
                    uni.hideLoading()
                    if (!qr_src) return uni.showModal({
                        title: '提示',
                        content: '活动海报生成失败',
                        showCancel: false
                    })

                    this.share_image = qr_src
                    this.previewShareImage()
                }
            }
            if (this.detail.rank_set?.closed_AD && this.detail.conf?.active?.qrcode_logo) {
                qr_data.qrcode_logo = this.detail.conf.active.qrcode_logo
                qr_data.canvas_id = 'qrcode'
                qr_data.canvas_this = this
            }

            xwy_api.getQrcode(qr_data)
        },

        previewShareImage() {
            uni.previewImage({
                urls: [this.share_image]
            })
        },



        downloadImportUsersTemplate() {
            this.importUsersTemplate('download-template')
        },
        importUsers() {
            this.importUsersTemplate('import-excel')
        },
        async importUsersTemplate(url) {
            let isH5 = false
            // #ifdef H5
            isH5 = true
            // #endif

            if (isH5) return this.$uni.navigateTo(`/pages/other/batch_import/${url}?id=${this.id}`)

            const res = await this.$uni.showModal('请在web端使用此功能')
            res.confirm && this.web_base_url && this.showWebUrl()
        },
        

        showWebUrl() {
            this.$refs['web-admin-src-copy-popup'].open({
                successCallback: () => this.popup_show = true,
                closeCallback: () => this.popup_show = false
            })
        },

        toShopManage() {
            // #ifndef H5
            uni.showModal({
                title: '提示',
                content: '请在web端管理商品',
                showCancel: false,
                success: res => {
                    if (res.confirm && this.web_base_url) this.showWebUrl()
                }
            })
            const flat = true
            if (flat) return
            // #endif

            window.open(this.web_base_url + '/web/wx-cloud-api/pages/admin_manage/goods#/pages/goods/list?access_token=' + app.globalData['access_token'])
        },

        copyActivityPages() {
            let data = 'pages/task-challenge/user/details?id=' + this.id
            if (this.detail.screen_pic) data += `&screen_pic=` + this.detail.screen_pic
            uni.setClipboardData({
                data,
                success() {
                    uni.hideToast()
                    uni.showModal({
                        title: '复制成功',
                        content: '小程序路径地址复制成功，请粘贴到公众号自定义菜单设置里',
                        showCancel: false
                    })
                }
            })
        },

        copy(data) {
            if (!data) return
            uni.setClipboardData({
                data,
                success: () => this.$uni.showToast('复制成功', 'none', 500)
            })
        },


        async exportLotteryRecord() {
            this.$uni.showLoading('导出中...')

            const res = await xwy_api.request({
                url: 'front.flat.active.lottery_active.user.lottery/lottery_records_list',
                data: {
                    is_admin: 1,
                    active_id: this.id,
                    need_excel: 1
                }
            })

            uni.hideLoading()

            this.export_ranking_success_tips = res['info'] || '导出成功'
            this.export_ranking_src = res['data'].url
            this.copyDownloadSrc(true)
            this.uniPopupOpen('export_ranking_success')


            const title = `导出活动【${this.detail.name}】抽奖记录`
            my_storage.setExportExcelRecord({url: res['data'].url, title})
        },

        lotteryManage() {
            this.$uni.navigateTo('/pages/lottery/admin/lottery/list?task_lottery=1')
        },
    }
}
</script>

<style scoped>
.page {
    min-height: 100vh;
    padding-bottom: 15px;
    box-sizing: border-box;
}

.logo {
    display: block;
    width: 100vw;
    height: auto;
}

.tools {
    margin: 10px;
    border-radius: 10px;
    overflow: hidden;
}

.tools .title {
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.item {
    width: calc(100% / 4);
    padding: 10px 5px;
    box-sizing: border-box;
}

.share-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, .7);
}

.share-popup-main {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100vw;
    border-radius: 20px 20px 0 0;
    overflow: hidden;
}

.share-item {
    padding: 0;
    margin: 10px 0;
    background-color: #fff;
    border: none;
    font-weight: normal;
    width: 50% !important;
    line-height: 1.5;
}

.share-item::after {
    border: none;
}

.share-cancel {
    line-height: 44px;
    padding-bottom: 10px;
    border-top: 1px solid #eee;
}

.export_ranking_popup {
    width: 280px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}

.export_ranking-btn {
    line-height: 40px;
    border-radius: 20px;
}

.export_ranking-btn::after {
    border: none;
}
</style>
