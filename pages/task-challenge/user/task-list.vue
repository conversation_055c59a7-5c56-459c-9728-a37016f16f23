<template>
    <view>
        <view v-if="loading" class="text-center" style="padding-top: 30vh;">
            <load-ani/>
        </view>


        <view v-if="!loading" class="container" :style="{backgroundColor: taskPopupStyle.bg_color}">
            <view class="task-list">
                <view v-for="(item, index) in task_list" :key="index">
                    <!--鲜繁客户定制活动，需要在这里跳转到运动圈列表
                    因为他这个客户开启了每日刷新任务rush_round_refresh_daily，所以通过这个来判断-->
                    <task-list-item v-if="!item.hidden" :item="item"
                                    :style="taskPopupStyle.task_item"
                                    :show-sport-moment="item.types === 3 && rush_round_refresh_daily"
                                    :sport-moment-name="sportMomentName"
                                    @toCompleteTask="toCompleteTask(item)"
                                    @toSportMomentList="toSportMomentList"/>
                </view>

                <task-list-item v-if="taskListShowShopSet" :show-sport-moment="false"
                                :item="taskListShowShopSet" :style="taskPopupStyle.task_item"
                                @toCompleteTask="toShop"/>
            </view>
        </view>
    </view>
</template>

<script>

import taskListItem from "../components/task-list-item.vue"
import config from "../config"

export default {
    components: {taskListItem},
    data() {
        return {
            loading: true,
            task_list: [],
            taskPopupStyle: {},
            rush_round_refresh_daily: false,
            sportMomentName: '',
            taskListShowShopSet: null
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.level_id = Number(params.level_id)

        this.getDetailsPageData()
    },

    methods: {
        getDetailsPageData() {
            this.getOpenerEventChannel().once('data', data => {
                this.activeDetails = data.activeDetails
                this.levelDetails = data.levelDetails
                this.AI_motion_list = data.AI_motion_list
                this.taskPopupStyle = data.taskPopupStyle || {}
                this.sportMomentName = data.sportMomentName
                this.taskListShowShopSet = data.taskListShowShopSet
                this.rush_round_set = data.rush_round_set

                this.rank_set = data.activeDetails?.rank_set || {}

                this.init()
            })
        },

        async init() {
            if (this.rank_set.rush_round_refresh_daily) this.rush_round_refresh_daily = true

            await this.getTaskList()
            this.loading = false
        },

        async getTaskList() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.jobState/job_finished_state_list',
                data: {
                    active_id: this.active_id,
                    point_id: this.level_id
                }
            })

            const errorModal = info => this.$uni.showModal(info, {success: () => uni.navigateBack()})
            if (res?.status !== 1) return errorModal(res?.info || '任务获取失败')
            const task_list = res?.data?.['job_finished_check']?.job_list || []
            if (!task_list?.length) return errorModal('任务获取失败')

            this.task_list = await this.taskListDataInit(task_list)
        },

        async taskListDataInit(task_list) {
            // 获取拼图任务奖励的积分，用来显示在任务列表上
            const puzzle = task_list.find(item => item.types === 7)
            if (puzzle) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.admin.puzzle/puzzle_image_list',
                    data: {
                        active_id: this.active_id,
                        point_id: this.level_id,
                        page: 1,
                        perpage: 10,
                    }
                })
                const list = res?.data?.list?.data
                if (list?.length) {
                    puzzle.integral = list[0].reward_integral_num
                    list.forEach(item => {
                        puzzle.integral = Math.min(puzzle.integral, item.reward_integral_num)
                    })
                }
            }

            const {
                job_set = {},
                integral = {},
                task_active_ai_sport_task_merge,
                daily_sign,
                reading_reward
            } = this.activeDetails.conf.active || {}

            const {unit: integral_unit = '积分', exam_reward_num, exam_reward} = integral

            const {AI_motion, rush_round_refresh_daily} = this.rank_set

            const level = this.levelDetails

            const {point_job_set_open = 0, job_set: {job_list = []} = {}} = level.conf || {}

            // 如果关卡设置了自定义任务，过滤掉点位没有选择的任务
            if (point_job_set_open && job_list.length) {
                task_list = task_list.filter(item => job_list.some(v => v.types === item.types))
            }

            const taskList = task_list.map(v => {
                // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
                /** @namespace v.finished_details.have_finished */

                // 鲜繁客户定制活动（判断开启了rush_round_refresh_daily， 每天刷新任务状态）。答题任务积分数显示
                if (rush_round_refresh_daily && v.types === 2 && Number(exam_reward_num)) {
                    v.integral = exam_reward_num
                }

                let is_finished = false
                if (v.finished_details) {
                    if (v.finished_details.have_finished) is_finished = true

                    // 运动步数按积分数闯关，如果有item.max，并且item.finished_details.today_have_exchange大于等于item.max，需要显示已完成,反之则显示未完成
                    if (job_set.types === 2 && v.types === 1 && v.max) {
                        const today_exchange_step = Number(v.finished_details.today_have_exchange || 0)
                        is_finished = today_exchange_step >= v.max
                    }
                }

                v.is_finished = is_finished

                // 是不是 按积分数过关(job_set.types === 2) 并且是 步数兑换任务(v.types === 1)
                const types_2_1 = (job_set.types === 2 && v.types === 1)

                /*// 按积分数过关，步数兑换未完成显示为"去运动"
                v.complete_button_text = is_finished ? '已完成' : (types_2_1 ? '去运动' : '去完成')*/

                // 按积分数过关，步数兑换未完成默认显示为"去运动"
                if (!v.not_complete_button_text && !is_finished && types_2_1) v.not_complete_button_text = '去运动'

                v.complete_button_text = is_finished ? (v.complete_button_text || '已完成') : (v.not_complete_button_text || '去完成')

                if (types_2_1 && v.max && v.min && v.integral) {
                    // 按积分数过关，步数兑换任务，如果有设置每日步数上限，页面显示的积分数为每日步数上限可获得的积分数
                    v.integral = Math.floor(v.max / v.min * v.integral)
                }

                if (v.types === 5 && reading_reward?.open && reading_reward?.rules?.length) {
                    const rules = reading_reward.rules
                    let min = Infinity, max = -Infinity
                    rules.forEach(item => {
                        min = Math.min(min, Number(item.integral.min))
                        max = Math.max(max, Number(item.integral.max))
                    })
                    v.integral = min === max ? min : `${min}到${max}`
                }

                if (v.types === 6 && daily_sign) {
                    const {circle_set, integral, types} = daily_sign
                    v.integral = types === 1 ? integral : circle_set[0].integral
                }

                // 填词任务，设置每题奖励积分
                if (v.types === 9) {
                    if (v.hasOwnProperty('per_integral')) v.integral = v.per_integral
                    if (!level?.conf?.fill_words?.category_id) v.hidden = true
                }
                if (v.finished_details?.['round_job_set']) delete v.finished_details['round_job_set']


                // 关卡没有绑定答题，不显示
                if (v.types === 2 && !level?.exam_id) if (!level?.exam_id) v.hidden = true

                // 关卡没有绑定找茬关卡，不显示
                if (v.types === 14 && !level?.conf?.picture_diff?.id) v.hidden = true


                const describeOptions = {
                    1: () => `当日步数达到${v.min}奖励${v.integral}${integral_unit}`,
                    2: () => {
                        if (exam_reward_num && exam_reward) {
                            return `答题每获得${exam_reward}分奖励${exam_reward_num}${integral_unit}`
                        }
                        return ''
                    },
                    3: () => `每发布1条动态奖励${v.integral}${integral_unit}`,
                    6: () => {
                        if (!daily_sign) return ''
                        const {circle_set, integral, types} = daily_sign
                        if (types === 1) return `每日签到奖励${integral}${integral_unit}`
                        return `按照连续签到天数奖励${circle_set[0].integral}到${circle_set[circle_set.length - 1].integral}${integral_unit}`
                    },
                    8: () => `完成任务奖励${v.integral}${integral_unit}`,
                    9: () => {
                        if (v.hasOwnProperty('per_integral')) {
                            return `每答对1题奖励${v.per_integral || 0}${integral_unit}`
                        }
                        return `完成任务奖励${v.integral || 0}${integral_unit}`
                    },
                    10: () => `完成任务奖励${v.integral}${integral_unit}`,
                    11: () => `完成任务奖励${v.integral}${integral_unit}`,
                    12: () => `完成任务奖励${v.integral}${integral_unit}`,
                    13: () => `完成任务奖励${v.integral}${integral_unit}`,
                    14: () => `完成任务奖励${v.integral}${integral_unit}`,
                    15: () => `接龙一次奖励${v.per_integral}积分`,
                    16: () => `打中一只奖励${v.per_integral}积分`,
                    18: () => {
                        if (v.hasOwnProperty('per_integral')) {
                            return `每答对1题奖励${v.per_integral || 0}${integral_unit}`
                        }
                        return `完成任务奖励${v.integral || 0}${integral_unit}`
                    },
                    19: () => `完成任务奖励${v.integral}${integral_unit}`,
                    20: () => `完成任务奖励${v.integral}${integral_unit}`,
                }
                v.describe = describeOptions[v.types]?.() || ''

                return v
            })

            /*
            * 鲜繁客户定制活动，需要把每一项AI运动显示在任务列表里面
            * */
            if (!AI_motion) return taskList  // 没有开启AI运动
            if (task_active_ai_sport_task_merge) return taskList  // 开启了合并AI运动任务

            const aiSportTaskIndex = taskList.findIndex(item => item.types === 4)
            if (aiSportTaskIndex === -1) return taskList  // 没有配置AI运动任务

            const aiSportTask = JSON.parse(JSON.stringify(taskList[aiSportTaskIndex]))
            taskList.splice(aiSportTaskIndex, 1) // 把原来的AI运动集合任务删除

            let AI_sport_list = JSON.parse(JSON.stringify(this.AI_motion_list))
            const types_list = level.conf?.ai_sport?.types_list
            if (types_list?.length) {
                // 关卡自定义选择AI运动
                AI_sport_list = AI_sport_list.filter(item => types_list.includes(item.types))
            }
            if (!AI_sport_list?.length) return taskList  // 没有配置AI运动

            const reward_integral_list = aiSportTask.finished_details?.['sport_reward_integral_list'] || []

            AI_sport_list.forEach((item, index) => {
                const task = {
                    types: 4,
                    title: item.name,
                    sport_types: item.types,
                    logo: item.logo || ''
                }

                let describe = '', is_finished = false

                if (item.integral_reward) {
                    const {integral, max_daily, num} = item.integral_reward
                    task.integral = max_daily || integral || 0
                    if (num && integral) {
                        const isCounts = this.xwy_config.AiSportNeedCountsTypes(item.types)
                        const sport_unit = isCounts ? '个' : '秒'
                        describe = `${item.name}${num}${sport_unit}奖励${integral}${integral_unit}`
                    }
                    if (max_daily) {
                        describe += `, 每日上限${max_daily}${integral_unit}`
                        const today_reward_integral = reward_integral_list.find(sport => sport.sport_types === item.types)?.['integral_num'] || 0
                        if (today_reward_integral >= Number(max_daily)) is_finished = true
                    }
                }

                task.describe = describe
                task.is_finished = is_finished
                task.complete_button_text = is_finished ? (aiSportTask.complete_button_text || '已完成') : (aiSportTask.not_complete_button_text || '去完成')

                const insert_index = aiSportTaskIndex + index
                taskList.splice(insert_index, 0, task)
            })

            return taskList
        },

        successCheck() {
            this.getTaskList()
            this.getOpenerEventChannel().emit('success')
        },

        toCompleteTask(item) {
            // 雷子的客户活动，任务已完成不能再进入
            const cannot_enter_after_completion = this.active_id === '5dc87ddf3ed74207006dc2772ad013c6'
            if (item.is_finished && cannot_enter_after_completion) return this.$uni.showToast('任务已完成')

            const point_id = this.level_id
            const level = this.levelDetails
            if (!level) return this.$uni.showToast('关卡获取失败', 'error')


            if (!this.activeTimeCheck()) return
            if (!this.taskTimeCheck()) return


            const {exam_open, open_sport_moment, AI_motion, reading_reward, closed_AD} = this.rank_set || {}

            const all_count = item.count
            const submit_count = item['finished_details']?.count
            if (all_count && submit_count && submit_count >= all_count) return this.$uni.showToast('任务已完成')

            const jump = (url, options) => {
                this.$uni.navigateTo(url, {
                    events: {
                        success: () => this.successCheck()
                    },
                    ...options
                })
            }

            const types = item.types

            if (types === 1) return this.stepExchange(item)

            if (types === 2) {
                // 去答题
                if (!exam_open) return this.$uni.showToast('活动未开启答题功能')
                const exam_id = level.exam_id
                if (!exam_id) return this.$uni.showToast('关卡未绑定答题')

                return jump(`/pages/likou_dati/pages/exam/exam_details/exam_details?exam_id=${exam_id}&active_id=${this.active_id}&point_id=${point_id}&rush_round=1`)
            }

            if (types === 3) {
                if (!open_sport_moment) return this.$uni.showToast('活动未开启运动圈功能')
                return jump(`/pages/comment/publish?active_id=${this.active_id}&point_id=${point_id}`)
            }

            if (types === 4) {
                if (!AI_motion) return this.$uni.showToast('活动未开启AI运动功能')

                if (item.sport_types) {
                    // 鲜繁客户定制的功能，所有AI运动项目显示在任务列表，如果有sport_types表示点击的是某个运动项目

                    let url = `/pages/ai_sport/pages/ai-sport-recognition?active_id=${this.active_id}&point_id=${point_id}&types=${item.sport_types}&name=${item.title}`
                    if (this.rank_set['ai_hand_start']) url += '&is_manual=1'

                    return jump(url)
                }

                let url = `/pages/ai_sport/pages/list?active_id=${this.active_id}&point_id=${point_id}`
                if (this.rank_set['ai_hand_start']) url += '&manual=1'
                return jump(url, {
                    success: res => {
                        let list = JSON.parse(JSON.stringify(this.AI_motion_list))
                        const types_list = level.conf?.ai_sport?.types_list
                        if (types_list?.length) {
                            // 关卡自定义选择AI运动
                            list = list.filter(item => types_list.includes(item.types))
                        }
                        res.eventChannel.emit('AI_motion_list', list || [])
                    }
                })
            }

            if (types === 5) {
                if (!reading_reward) return this.$uni.showToast('活动未开启阅读奖励功能')
                let url = `/pages/news/list?active_id=${this.active_id}&point_id=${point_id}`
                if (closed_AD) url += `&vip=1`
                const category_list = this.activeDetails.conf.active.reading_reward?.rules
                if (!category_list?.length) return this.$uni.showToast('活动未设置奖励文章')
                const list = category_list.map(v => {
                    return {
                        id: v.category_id,
                        n: v.name,
                        s: Math.floor(v.seconds),
                        i_min: Number(v.integral?.min || 0) || 0,
                        i_max: Number(v.integral?.max || 0) || 0
                    }
                })
                url += `&category_list=${JSON.stringify(list)}`
                return jump(url)
            }

            if (types === 6) return jump(`/pages/sign_in/sign_in?active_id=${this.active_id}`)

            if (types === 7) {
                let url = `/pages/puzzle/puzzle-list?active_id=${this.active_id}&point_id=${point_id}`
                if (!closed_AD) url += '&show_ad=1'
                const json_set = this.rush_round_set?.['puzzle_image']
                if (json_set) {
                    const {bg_img, navigation_bar} = json_set
                    if (bg_img) url += `&bg_img=${bg_img}`
                    if (navigation_bar) {
                        const {bg_color, font_color} = navigation_bar
                        if (bg_color) url += `&nav_bg_color=${bg_color}`
                        if (font_color) url += `&nav_font_color=${font_color}`
                    }
                }
                if (this.rush_round_set?.['puzzle_image']?.bg_img) url += `&bg_img=${this.rush_round_set['puzzle_image'].bg_img}`
                return jump(url)
            }

            if (types === 8) return jump(`/pages/historical-event-sort/user/sorting?active_id=${this.active_id}&point_id=${point_id}`)

            if (types === 9) {
                const {num = 0, category_id, type} = level.conf?.fill_words || {}

                let url = '/pages/fill-words/fill-words'
                if (type === 'fill_couplet') url = '/pages/fill-words/fill-couplet'
                url += `?active_id=${this.active_id}&point_id=${point_id}&category_id=${category_id}&num=${num}`
                if (!closed_AD) url += '&show_ad=1'
                if (item.title) url += `&title=${item.title}`

                if (type === 'fill_words' && this.rush_round_set?.fill_words?.bg_img) {
                    url += `&bg_img=${this.rush_round_set.fill_words.bg_img}`
                }
                return jump(url, {
                    success: res => {
                        const imageSet = this.rush_round_set?.fill_words
                        if (type === 'fill_couplet' && imageSet) res.eventChannel.emit('imageSet', imageSet)
                    }
                })
            }

            if (types === 10) {
                let url = `/pages/red-small-games/mine-sweeper/game?active_id=${this.active_id}&point_id=${point_id}`

                if (level.conf?.mine_sweeper) {
                    const {row, col, boom_num, seconds} = level.conf.mine_sweeper
                    url += `&row=${row}&col=${col}&boom_num=${boom_num}`
                    if (seconds) url += `&seconds=${seconds}`
                }

                if (!closed_AD) url += '&show_ad=1'
                if (this.rush_round_set?.['mine_clearing_bom']?.bg_img) url += `&bg_img=${this.rush_round_set['mine_clearing_bom'].bg_img}`

                if (item.title) url += `&title=${item.title}`
                return jump(url)
            }

            if (types === 11) {
                return jump('/pages/red-small-games/lian-lian-kan/game', {
                    success: res => {
                        const data = {
                            active_id: this.active_id,
                            point_id,
                            integral: item.integral || 0
                        }

                        if (!closed_AD) data.show_ad = 1
                        if (item.title) data.title = item.title

                        if (level.conf?.lian_lian_kan) {
                            const {row, col, img_count, seconds} = level.conf.lian_lian_kan
                            data.row = row
                            data.col = col
                            data.img_count = img_count
                            if (seconds) data.seconds = seconds
                        }

                        const json_set = this.rush_round_set?.['link_game']
                        if (json_set) {
                            const {bg_img, navigation_bar} = json_set
                            if (bg_img) data.bg_img = bg_img
                            if (navigation_bar) {
                                const {bg_color = '#ffffff', font_color = '#000000'} = navigation_bar
                                data.navigation_bar = {backgroundColor: bg_color, frontColor: font_color}
                            }
                        }

                        res.eventChannel.emit('data', data)
                    }
                })
            }

            if (types === 12) {
                let url = `/pages/red-small-games/grab-grain/game?active_id=${this.active_id}&point_id=${point_id}`

                if (this.activeDetails?.conf?.active?.grab_grain_game_set) {
                    const {target_grain, seconds} = this.activeDetails.conf.active.grab_grain_game_set
                    if (target_grain) url += `&target_grain=${target_grain}`
                    if (seconds) url += `&seconds=${seconds}`
                }

                if (!closed_AD) url += '&show_ad=1'

                if (item.title) url += `&title=${item.title}`

                return jump(url)
            }

            if (types === 13) {
                let {count, seconds, level: game_level} = config.gluttonousSnakeDefaultSet
                const level_set = level.conf?.snake
                if (level_set) {
                    if (level_set.count) count = level_set.count
                    if (level_set.seconds) seconds = level_set.seconds
                    if (level_set.level) game_level = level_set.level
                }

                let url = `/pages/games/gluttonous-snake/gluttonous-snake?active_id=${this.active_id}&point_id=${point_id}&count=${count}&seconds=${seconds}&level=${game_level}`

                if (!closed_AD) url += '&show_ad=1'
                if (item.title) url += `&title=${item.title}`

                return jump(url)
            }

            if (types === 14) {
                const {id, time} = level.conf.picture_diff
                let url = `/pages/treasure-hunt/user/look-for?active_id=${this.active_id}&point_id=${point_id}&id=${id}&unit=${this.activeDetails.conf.active.integral?.unit || '积分'}&game_type=1&from_task=1`
                if (time) url += `&time=${time}`

                return jump(url)
            }

            if (types === 15) {
                let url = `/pages/games/idiom-solitaire/idiom-solitaire?id=${this.active_id}&point_id=${point_id}&seconds=${item.seconds || 30}&per_integral=${item.per_integral}`
                if (item.max_count) url += `&max_count=${item.max_count}`
                if (!closed_AD) url += '&show_ad=1'
                if (item.title) url += `&title=${item.title}`

                const {idiom_unison_open, idiom_tips_count} = this.activeDetails.conf?.active || {}
                if (idiom_unison_open) url += '&unison=1'
                if (idiom_tips_count) url += `&idiom_tips_count=${idiom_tips_count}`

                return jump(url)
            }

            if (types === 16) {
                let url = `/pages/games/dds/dds?id=${this.active_id}&point_id=${point_id}&seconds=${item.seconds || 30}&from_task=1`
                if (!closed_AD) url += '&show_ad=1'
                if (item.title) url += `&title=${item.title}`

                return jump(url, {
                    success: res => {
                        const hit_mouse = this.rush_round_set?.['hit_mouse']
                        if (hit_mouse) res.eventChannel.emit('imageSet', hit_mouse)
                    }
                })
            }

            if (types === 18) {
                const garbage_count = this.activeDetails?.conf?.active?.garbage_count || 10

                return jump('/pages/games/garbage-sorting/garbage-sorting', {
                    success: res => {
                        const data = {
                            id: this.active_id,
                            point_id,
                            seconds: item.seconds || 30,
                            garbage_count
                        }

                        if (item.integral) data.integral = item.integral
                        if (item.per_integral) data.per_integral = item.per_integral
                        if (!closed_AD) data.show_ad = 1
                        if (item.title) data.title = item.title

                        const json_set = this.rush_round_set?.['garbage_sorting']
                        if (json_set) {
                            const {bg_img, navigation_bar} = json_set
                            if (bg_img) data.bg_img = bg_img
                            if (navigation_bar) {
                                const {bg_color = '#ffffff', font_color = '#000000'} = navigation_bar
                                data.navigation_bar = {backgroundColor: bg_color, frontColor: font_color}
                            }
                        }

                        res.eventChannel.emit('data', data)
                    }
                })
            }

            if (types === 19) {
                return jump(`/pages/comment/list?active_id=${this.active_id}&point_id=${point_id}&share_task=1&not_publish=1&sport_moment_name=${this.sportMomentName}`)
            }

            if (types === 20) {
                const id = this.activeDetails?.conf?.active?.share_reward_news?.id
                if (!id) return this.$uni.showToast('文章未配置')
                return jump(`/pages/news/preview?id=${id}&active_id=${this.active_id}&point_id=${point_id}&share_reward=1`)
            }
        },

        stepExchange(item) {
            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace item.finished_details.today_have_exchange */

            const task_type = this.activeDetails.conf.active.job_set?.types || 1
            const max_step = item.max || 0
            const today_finished_step = item.finished_details?.today_have_exchange || 0
            if (task_type === 2 && max_step && today_finished_step && today_finished_step >= max_step) {
                return this.$uni.showToast('任务已完成')
            }


            this.$uni.showLoading('获取步数中...')
            this.xwy_api.getWeRunData(res => {
                uni.hideLoading()
                if (res?.data?.['crypt_data']?.['stepInfoList']?.length) {
                    const step_list = res.data['crypt_data']['stepInfoList']
                    const today_step = step_list[step_list.length - 1].step || 0
                    this.stepExchangeAjax(item, today_step)
                } else {
                    this.$uni.showToast('获取步数失败', 'error')
                }
            })
        },

        async stepExchangeAjax(item, step) {
            const clearance_type = this.activeDetails.conf.active.job_set?.types || 1

            if (clearance_type === 1) {
                const need_step = item.min
                if (step < need_step) return this.$uni.showToast(`今日步数不足${need_step}步`)
            }

            const urls = {
                1: 'front.flat.sport_step.job_list.jobState/exchange_step_for_rush_round',
                2: 'front.flat.sport_step.exchange/exchange_step'
            }
            const data = {active_id: this.active_id}
            if (clearance_type === 1) data.point_id = this.level_id
            if (clearance_type === 2)
                data.exchange_date = Math.floor(new Date(this._utils.getDay(0, true, '/')).getTime() / 1000)


            this.$uni.showLoading('提交步数...')
            const res = await this.xwy_api.request({url: urls[clearance_type], data})
            uni.hideLoading()
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '步数提交失败')
            this.$uni.showToast('提交成功', 'success')
            this.successCheck()
        },


        taskTimeCheck() {
            let {begin_time, end_time} = this.activeDetails.conf?.active?.job_set || {}
            if (!begin_time && !end_time) return true

            begin_time ||= '00:00:00'
            end_time ||= '23:59:59'

            const now = new Date().getTime()
            const today = this._utils.getDay(0, true, '/')
            const begin = new Date(`${today} ${begin_time}`).getTime()
            const end = new Date(`${today} ${end_time}`).getTime()
            if (now < begin) {
                this.$uni.showToast(`${begin_time} 后才能开始任务`)
                return false
            }
            if (now > end) {
                this.$uni.showToast(`${end_time} 后不能做任务, 请明天 ${begin_time} 后再来`)
                return false
            }

            return true
        },

        activeTimeCheck() {
            const now_time = new Date().getTime()
            const {begin_time, end_time} = this.activeDetails

            if (begin_time * 1000 > now_time) {
                this.$uni.showToast('活动未开始')
                return false
            }
            if (end_time * 1000 < now_time) {
                this.$uni.showToast('活动已结束')
                return false
            }

            return true
        },


        toSportMomentList() {
            this.$uni.navigateTo(`/pages/comment/list?active_id=${this.active_id}&not_publish=1&sport_moment_name=${this.sportMomentName}`)
        },

        toShop() {
            app.globalData['tempData'].shop_integral_unit = this.activeDetails.conf.active.integral.unit || '积分'
            app.globalData['tempData'].shop_exchange_limit = this.rank_set.exchange_gift_once || 0
            this.$uni.navigateTo('/pages/shop/goods/list?active_id=' + this.active_id)
        },
    }
}
</script>

<style lang="scss">
.container {
    min-height: 100vh;
    background-color: #fff;
    padding: 10px;
    box-sizing: border-box;
}
</style>