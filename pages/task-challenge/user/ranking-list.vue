<template>
    <view class="page bg-background">

        <xwy-ad v-if="show_AD" :ad_type="3"></xwy-ad>
        
        <template v-if="category_list.length">
            <view class="type-bar bg-white flex-row">
                <view
                    class="type-item text-center font14 color-content"
                    :class="{'activate-type': item.id === type_id}"
                    :style="'width: calc(100% / ' + category_list.length + ');'"
                    v-for="item in category_list" :key="item.id"
                    @click="changeType(item)"
                >{{ item.name }}
                </view>
            </view>

            <view style="width: 100%; height: 40px;"></view>
        </template>

        <view v-if="top_rank_text" class="font12 p10" style="color: #e19898;" @click="copyId">
            {{ top_rank_text }}
        </view>

        <top-banner v-if="top_rank_banner.length" :list="top_rank_banner"></top-banner>


        <template v-if="podiumList.length">
            <ranking-podium :list="podiumList" :active-id="id"/>
        </template>


        <view v-if="list.length && total_count" class="color-sub font14 text-center pt5"
              @click="changeGodModel">
            <text v-if="!show_AD">我的排名: {{ my_position_num === -1 ? '未上榜' : my_position_num }}</text>
            <text class="pl5" v-if="total_count && id !== 'e500dedf72811dfd04d474c3ceb1ee5c'">
                <template v-if="!show_AD">(</template>
                共{{ total_count }}{{ type_id === 40 ? '队伍' : '人' }}
                <template v-if="!show_AD">)</template>
            </text>
        </view>

        <view class="list">
            <view v-for="(item, index) in list" :key="index">
                <xwy-ad v-if="show_AD && index === 0" :ad_type="66"></xwy-ad>

                <view v-if="!limit_show_num || index < limit_show_num" class="item flex-row bg-white">
                    <view class="index flex-all-center" @click.stop="copyUserid(item)">

                        <image class="top-3" v-if="index < 3"
                               :src="'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/no-' + (index + 1) + '.png'"/>
                        <view v-else class="color-sub font14">{{ index + 1 }}</view>
                    </view>

                    <view v-if="item.headimg" class="flex-all-center" @click="clickHeadimg(item)">
                        <image mode="aspectFill" class="headimg" :src="item.headimg"/>
                    </view>

                    <view class="middle">
                        <view class="name color-title"
                              :class="id === '98a78b836d8c42bba8cba6d1335ce62b' ? 'ellipsis--l2' : 'ellipsis'">
                            {{ item.nickname }}
                        </view>
                        <view v-if="item.other_info.length && other_show" class="color-sub font12 pt5"
                              @click="clickOther(item)">
                            <view v-for="info in item.other_info" :key="info">{{ info }}</view>
                        </view>
                        <view v-if="god_model" class="font12 color-sub">最后更新时间: {{ item.update_time || '' }}</view>
                    </view>

                    <view class="right flex-column flex-all-center color-red font14">
                        <view class="right-item text-right">
                            <view v-if="item.value !== 'hide'">
                                <text>{{ item.value }}</text>
                                <text class="font12" style="padding-left: 2px;">{{ unit }}</text>
                            </view>
                        </view>
                    </view>
                </view>

                <xwy-ad v-if="show_AD && index !== 0 && ((index + 1) % 10 === 0)" :ad_type="66"></xwy-ad>

            </view>
        </view>

        <view v-if="!list.length && !loading" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无排行数据</view>
        </view>

        <uni-load-more v-if="loading" status="loading"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && list.length > 5"
            status="noMore"
            :contentText="{contentnomore: '我是有底线的'}"
        ></uni-load-more>
        <uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>


    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'

export default {
    data() {
        return {
            id: '',
            
             // id 0: 累计步数  7: 答题总分  48: 个人总积分  40: 伍积分排行榜  41: AI运动积分排行榜
            category_list: [],
            
            type_id: 48,
            loading: true,
            total_count: 0,
            top_rank_text: '',
            my_position_num: -1,
            list: [],
            load_page: 1,
            is_last_page: false,
            top_rank_banner: [],
            show_AD: false,
            limit_show_num: null,
            
            team_id: 0,
            god_model: false,

            integralUnit: '积分'
        }
    },

    computed: {
        unit() {
            const type_id = this.type_id

            if (type_id === 0) return '步'
            if (type_id === 7) return '分'

            return this.integralUnit
        },

        podiumList() {
            const {list, show_AD, unit} = this
            if (!list.length || show_AD) return []
            return this.list.slice(0, 3).map(item => ({
                headimg: item.headimg,
                nickname: item.nickname,
                value: item.value === 'hide' ? '' : `${item.value}${unit}`
            }))
        },

        other_show() {
            const {type_id, god_model} = this
            if (type_id !== 7) return true
            return god_model
            
        }
    },

    onLoad(e) {
        this.id = e.id
        if (e.team_id) this.team_id = Number(e.team_id)
        
        this.$uni.showLoading()
        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.init()
        })
    },
    onPullDownRefresh() {
        this.reloadList()
    },
    onReachBottom() {
        this.loadList()
    },
    methods: {
        async init() {
            await this.getActivityDetails()
            this.setPageData()
            await this.getRankingCategoryList()
            await this.getList()
            this.loading = false
            uni.hideLoading()
        },

        async getActivityDetails() {
            let activity_details = app.globalData['activity_detail']
            if (!activity_details) {
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.id
                    }
                })
                activity_details = res?.['data']?.['active_details']
            }
            if (!activity_details) {
                return xwy_api.alert('活动获取失败', {
                    complete: () => {
                        uni.navigateBack()
                    }
                })
            }
            this.activity_details = activity_details

            this.integralUnit = activity_details?.conf?.active?.integral?.unit || '积分'
        },

        setPageData() {
            const details = this.activity_details
            if (details.conf?.active?.top_rank_banner?.length) {
                this.top_rank_banner = details.conf.active.top_rank_banner
            }
            if (!details.rank_set?.closed_AD) this.show_AD = true
            if (details.rank_set?.limit_show_num) this.limit_show_num = details.rank_set.limit_show_num
        },

        async getRankingCategoryList() {
            if (this.team_id) return
            
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.step/top_rank_category_list',
                data: {
                    active_id: this.id
                }
            })
            const list = res?.data?.top_rank_category_list || []
            if (list.length) {
                this.type_id = list[0].id
                if (list.length === 1) {
                    // JSON配置更改排行榜顶部提示语的
                    if (list[0].id === this.type_id && list[0].text) this.top_rank_text = list[0].text
                } else {
                    this.category_list = list
                }
            }
        },

        changeType(item) {
            if (item.id === this.type_id) return this.changeGodModel()
            this.type_id = item.id
            this.top_rank_text = item.text || ''
            this.reloadList()
        },

        changeGodModel() {
            this.god_model_timeout && clearTimeout(this.god_model_timeout)
            this.god_model_click_count = this.god_model_click_count || 0
            this.god_model_click_count++
            this.god_model_timeout = setTimeout(() => {
                this.god_model_click_count = 0
                console.log('定时器结束')
                clearTimeout(this.god_model_timeout)
            }, 2000)
            if (this.god_model_click_count >= 3) {
                this.god_model = !this.god_model
                this.god_model_click_count = 0
                clearTimeout(this.god_model_timeout)
            }
        },


        async loadList() {
            if (!this.loading && !this.is_last_page) {
                this.loading = true
                await this.getList()
                this.loading = false
            }
        },

        async reloadList() {
            if (this.loading) return uni.stopPullDownRefresh()
            this.load_page = 1
            this.loading = true
            await this.getList()
            this.loading = false
            uni.stopPullDownRefresh()
        },

        /**
         * @description 获取上周一的日期
         * @return {string} 上周一的日期 yyyy-MM-dd
         * */
        getLastMonday() {
            const today = new Date()
            const dayOfWeek = today.getDay()
            const diff = (dayOfWeek + 6) % 7
            today.setDate(today.getDate() - diff - 7)
            today.setHours(0, 0, 0, 0)

            return this._utils.unitTimeToDate(today.getTime())
        },

        async getList() {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
                this.my_position_num = -1
                this.total_count = 0
            }

            const data = {
                active_id: this.id,
                top_rank_types: this.type_id,
                page: this.load_page,
                perpage: 20
            }
            if (this.team_id) data.team_id = this.team_id

            // 查看某日的积分排行榜
            if (this.type_id === 58) {
                // 这里是查看昨日的
                data.date = this._utils.getDay(-1, true)
            }

            // 查看某周的积分排行榜
            if (this.type_id === 59) {
                // 这里是查看上周的
                // 获取上周周一的日期
                data.date = this.getLastMonday()
            }

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.step/top_rank_list',
                data
            })


            if (!res?.data) {
                this.is_last_page = true
                return
            }


            if (!this.top_rank_text && res.data?.rank_types_text?.rules) {
                this.top_rank_text = res.data.rank_types_text.rules
            }

            if (res.data.top_rank_list) {
                if (res.data.top_rank_list.position_num) this.my_position_num = res.data.top_rank_list.position_num

                const data = res.data.top_rank_list
                const list = data.list.data || []
                const new_list = this.listDataInit(list)

                this.list = [...this.list, ...new_list]
                this.is_last_page = data.list.is_lastpage
                this.total_count = data.list.total
                this.load_page++
            } else {
                this.is_last_page = true
            }
        },

        listDataInit(list) {
            return list.map(v => ({
                id: v.id,
                userid: v.userid || null,
                headimg: this.getHeadimg(v),
                nickname: this.getNickname(v),
                value: this.getItemValue(v),
                other_info: this.getItemOtherInfo(v),
                team_id: v.team_details?.id || 0,
                update_time: v.update_time || ''
            }))
        },

        getHeadimg(v) {
            if (this.type_id === 40) return v.team_details?.conf?.logo || ''
            return v.headimg || v.user_details?.headimg || ''
        },

        getNickname(v) {
            if (this.type_id === 40) return v.team_details?.name || ''
            const must_submit = v.must_submit || v.user_details?.must_submit
            return must_submit?.[0]?.value || ''
        },

        // 获取排行榜右边显示的值
        getItemValue(v) {
            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace v.integral_all */
            /** @namespace v.allIntegral */
            /** @namespace v.ai_motion_integral_num */

            // 最日、上周 积分排行榜
            if ([58, 59].includes(this.type_id)) return v.total_integral

            const values = {
                0: () => v.exchange_step, // ========================== 步数
                7: () => v.score, // ================================== 答题
                48: () => v.integral_all, // ========================== 个人总积分
                40: () => v.allIntegral, // =========================== 队伍积分排行榜
                41: () => v.ai_motion_integral_num, // ================ AI运动积分排行榜
                61: () => v.integral_all, // ========================== 勋章积分排行榜，先按积分排在按勋章数排
            }
            return values[this.type_id]?.() || 0
        },

        
        getItemOtherInfo(v) {
            const infos = {
                40: () => ['查看队内排名'],
                7: () => {
                    const seconds = v['score_seconds'] || 0
                    if (seconds < 60) return [`${seconds}秒`]
                    const m = Math.floor(seconds / 60)
                    const s = seconds % 60
                    const time_text = s === 0 ? `${m}分钟` : `${m}分${s}秒`
                    return [time_text]
                },
                61: () => [`勋章数: ${v.medal_num || 0}`]
            }
            return infos[this.type_id]?.() || []
        },

        clickHeadimg(item) {
            if (this.type_id !== 0) return
            
            let url = `/pages/activity/other/user_exchange_list?activity_id=${this.id}&userid=${item.userid}`
            if (item.nickname) url += `&nickname=${encodeURIComponent(item.nickname)}`
            if (item.headimg) url += `&headimg=${encodeURIComponent(item.headimg)}`
            this.$uni.navigateTo(url)
        },

        clickOther(item) {
            if (this.type_id !== 40 || !item.team_id) return
            this.$uni.navigateTo(`./ranking-list?id=${this.id}&team_id=${item.team_id}`)
        },

        copyUserid(item) {
            const id = this.type_id === 2 ? item.id : item.userid
            uni.setClipboardData({
                data: id.toString(),
                success: () => uni.hideToast()
            })
        },

        copyId() {
            uni.setClipboardData({
                data: this.id,
                success: () => uni.hideToast()
            })
        }
    }
}
</script>

<style scoped lang="scss">
@import "@/pages/ranking-list/ranking-list.scss";

.type-bar {
    position: fixed;
    z-index: 9999;
    top: 0;
    border: 0;
    width: 100vw;
    height: 40px;

    .type-item {
        height: 40px;
        line-height: 40px;
        box-sizing: border-box;
    }

    .activate-type {
        color: #2d8cf0;
        border-bottom: 2px solid #2d8cf0;
    }
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .type-bar {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }
}

/* #endif */
</style>
