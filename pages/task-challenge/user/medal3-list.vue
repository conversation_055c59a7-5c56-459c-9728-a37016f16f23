<template>
    <view class="page">
        <view class="user-info flex-row">
            <image class="headimg" :src="headimg" mode="aspectFill"/>
            <view class="pl10">
                <view>{{ username }}</view>
                <view>已获得{{ medalCounts }}枚勋章</view>
            </view>
        </view>

        <view class="list flex-row flex-wrap">
            <view class="item" v-for="(item, index) in medalList" :key="index">
                <image class="item-image" :class="{'no-get': !item.user_total_num}" :src="item.pic"
                       mode="aspectFit" @click="previewMedal(item.pic)"/>
                <view class="text-center">{{ item.title }}</view>
                <view class="medal-item-count font12" v-if="item.user_total_num">
                    {{item.user_total_num }}枚
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            username: '',
            headimg: '',
            medalList: []
        }
    },

    computed: {
        medalCounts() {
            return this.medalList.reduce((a, b) => a + b.user_total_num, 0)
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        if (params.username) this.username = params.username
        if (params.headimg) this.headimg = params.headimg

        this.init()
    },

    methods: {
        async init() {
            await this.getUserMedalCounts()
        },

        async getUserMedalCounts() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.medal/user_medal_list',
                data: {
                    active_id: this.active_id,
                    get_medal_total_num: 1,
                    page: 1,
                    perpage: this.medalList.length
                }
            })

            const list = res?.data?.total_num || []

            // 已点亮的勋章放在前面
            const lightList = [], darkList = []
            list.forEach(({title, pic, user_total_num}) => {
                const item = {title, pic, user_total_num}
                if (user_total_num) {
                    lightList.push(item)
                } else {
                    darkList.push(item)
                }
            })

            this.medalList = [...lightList, ...darkList]
        },

        previewMedal(current) {
            this.$uni.previewImage({
                urls: this.medalList.map(item => item.pic),
                current
            })
        }
    }
}
</script>

<style lang="scss">
.page {
    width: 100vw;
    min-height: 100vh;
    box-sizing: border-box;
    //background: radial-gradient(circle at 20px 20px, #AABEFA 5px, #B7C9FB 0);
    background-color: #f8f8f8;
    background-size: 30px 30px;
}

.user-info {
    padding: 15px;
    line-height: 30px;

    .headimg {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: block;
    }
}

.list {
    padding: 15rpx;

    .item {
        padding: 20rpx 15rpx;
        width: 210rpx;
        position: relative;

        .item-image {
            width: 210rpx;
            height: 210rpx;
        }

        .no-get {
            opacity: .1;
        }

        .medal-item-count {
            position: absolute;
            top: 0;
            right: 0;
            background-color: #FA670A;
            color: #fff;
            min-width: 24px;
            height: 12px;
            line-height: 12px;
            padding: 4px;
            border-radius: 10px 10px 10px 0;
            text-align: center;
        }
    }
}
</style>
