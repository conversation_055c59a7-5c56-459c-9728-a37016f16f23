<template>
    <view class="pt10 pb10">
        <view class="data-show bg-background">
            <view class="data-item flex-row">
                <view class="label">边框颜色:</view>
                <view class="color-block" :style="{backgroundColor: set.border_color}"></view>
            </view>

            <view class="data-item flex-row">
                <view class="label">背景颜色:</view>
                <view class="color-block" :style="{backgroundColor: set.bg_color}"></view>
            </view>

            <view class="data-item flex-row">
                <view class="label">过关描述颜色:</view>
                <view class="color-block" :style="{backgroundColor: set.text_color}"></view>
            </view>

            <view class="data-item flex-row">
                <view class="label">任务边框颜色:</view>
                <view class="color-block" :style="{backgroundColor: set.task_item.border_color}"></view>
            </view>

            <view class="data-item flex-row">
                <view class="label">任务背景颜色:</view>
                <view class="color-block" :style="{backgroundColor: set.task_item.bg_color}"></view>
            </view>

            <view class="data-item flex-row">
                <view class="label">任务标题文字颜色:</view>
                <view class="color-block" :style="{backgroundColor: set.task_item.name_color}"></view>
            </view>

            <view class="data-item flex-row">
                <view class="label">任务描述文字颜色:</view>
                <view class="color-block" :style="{backgroundColor: set.task_item.text_color}"></view>
            </view>

            <view class="data-item flex-row">
                <view class="label">任务积分颜色:</view>
                <view class="color-block" :style="{backgroundColor: set.task_item.integral_color}"></view>
            </view>

            <view class="data-item flex-row">
                <view class="label">任务完成按钮背景颜色:</view>
                <view class="color-block"
                      :style="{backgroundColor: set.task_item.button.completed.bg_color}"></view>
            </view>

            <view class="data-item flex-row">
                <view class="label">任务完成按钮文字颜色:</view>
                <view class="color-block"
                      :style="{backgroundColor: set.task_item.button.completed.text_color}"></view>
            </view>

            <view class="data-item flex-row">
                <view class="label">任务未完成按钮背景颜色:</view>
                <view class="color-block"
                      :style="{backgroundColor: set.task_item.button.uncompleted.bg_color}"></view>
            </view>

            <view class="data-item flex-row">
                <view class="label">任务未完成按钮文字颜色:</view>
                <view class="color-block"
                      :style="{backgroundColor: set.task_item.button.uncompleted.text_color}"></view>
            </view>

            <view class="control-icon flex-all-center bg-white" @click="$refs.popup.open()">
                <text class="iconfont icon-edit color-sub"></text>
            </view>

        </view>


        <uni-popup ref="popup" @touchmove.stop.prevent="">
            <view class="set-popup bg-white">
                <view class="text-center pb10">任务列表颜色设置</view>

                <scroll-view scroll-y="true" style="height: 50vh; max-height: calc(90vh - 150px);">
                    <uni-forms label-position="top" label-width="300">
                        <uni-forms-item label="边框颜色:">
                            <view class="flex-row"
                                  @click="changeColor('border_color', set_data.border_color)">
                                <view class="color-block"
                                      :style="{backgroundColor: set_data.border_color}"></view>
                                <view class="flex-all-center pl10">
                                    <text class="iconfont icon-edit color-sub"></text>
                                </view>
                            </view>
                        </uni-forms-item>

                        <uni-forms-item label="背景颜色:">
                            <view class="flex-row" @click="changeColor('bg_color', set_data.bg_color)">
                                <view class="color-block"
                                      :style="{backgroundColor: set_data.bg_color}"></view>
                                <view class="flex-all-center pl10">
                                    <text class="iconfont icon-edit color-sub"></text>
                                </view>
                            </view>
                        </uni-forms-item>

                        <uni-forms-item label="过关描述颜色:">
                            <view class="flex-row" @click="changeColor('text_color', set_data.text_color)">
                                <view class="color-block"
                                      :style="{backgroundColor: set_data.text_color}"></view>
                                <view class="flex-all-center pl10">
                                    <text class="iconfont icon-edit color-sub"></text>
                                </view>
                            </view>
                        </uni-forms-item>

                        <uni-forms-item label="任务边框颜色:">
                            <view class="flex-row"
                                  @click="changeColor('task_item.border_color', set_data.task_item.border_color)">
                                <view class="color-block"
                                      :style="{backgroundColor: set_data.task_item.border_color}"></view>
                                <view class="flex-all-center pl10">
                                    <text class="iconfont icon-edit color-sub"></text>
                                </view>
                            </view>
                        </uni-forms-item>

                        <uni-forms-item label="任务背景颜色:">
                            <view class="flex-row"
                                  @click="changeColor('task_item.bg_color', set_data.task_item.bg_color)">
                                <view class="color-block"
                                      :style="{backgroundColor: set_data.task_item.bg_color}"></view>
                                <view class="flex-all-center pl10">
                                    <text class="iconfont icon-edit color-sub"></text>
                                </view>
                            </view>
                        </uni-forms-item>

                        <uni-forms-item label="任务标题文字颜色:">
                            <view class="flex-row"
                                  @click="changeColor('task_item.name_color', set_data.task_item.name_color)">
                                <view class="color-block"
                                      :style="{backgroundColor: set_data.task_item.name_color}"></view>
                                <view class="flex-all-center pl10">
                                    <text class="iconfont icon-edit color-sub"></text>
                                </view>
                            </view>
                        </uni-forms-item>

                        <uni-forms-item label="任务描述文字颜色:">
                            <view class="flex-row"
                                  @click="changeColor('task_item.text_color', set_data.task_item.text_color)">
                                <view class="color-block"
                                      :style="{backgroundColor: set_data.task_item.text_color}"></view>
                                <view class="flex-all-center pl10">
                                    <text class="iconfont icon-edit color-sub"></text>
                                </view>
                            </view>
                        </uni-forms-item>

                        <uni-forms-item label="任务积分颜色:">
                            <view class="flex-row"
                                  @click="changeColor('task_item.integral_color', set_data.task_item.integral_color)">
                                <view class="color-block"
                                      :style="{backgroundColor: set_data.task_item.integral_color}"></view>
                                <view class="flex-all-center pl10">
                                    <text class="iconfont icon-edit color-sub"></text>
                                </view>
                            </view>
                        </uni-forms-item>

                        <uni-forms-item label="任务完成按钮背景颜色:">
                            <view class="flex-row"
                                  @click="changeColor('task_item.button.completed.bg_color', set_data.task_item.button.completed.bg_color)">
                                <view class="color-block"
                                      :style="{backgroundColor: set_data.task_item.button.completed.bg_color}"></view>
                                <view class="flex-all-center pl10">
                                    <text class="iconfont icon-edit color-sub"></text>
                                </view>
                            </view>
                        </uni-forms-item>

                        <uni-forms-item label="任务完成按钮文字颜色:">
                            <view class="flex-row"
                                  @click="changeColor('task_item.button.completed.text_color', set_data.task_item.button.completed.text_color)">
                                <view class="color-block"
                                      :style="{backgroundColor: set_data.task_item.button.completed.text_color}"></view>
                                <view class="flex-all-center pl10">
                                    <text class="iconfont icon-edit color-sub"></text>
                                </view>
                            </view>
                        </uni-forms-item>

                        <uni-forms-item label="任务未完成按钮背景颜色:">
                            <view class="flex-row"
                                  @click="changeColor('task_item.button.uncompleted.bg_color', set_data.task_item.button.uncompleted.bg_color)">
                                <view class="color-block"
                                      :style="{backgroundColor: set_data.task_item.button.uncompleted.bg_color}"></view>
                                <view class="flex-all-center pl10">
                                    <text class="iconfont icon-edit color-sub"></text>
                                </view>
                            </view>
                        </uni-forms-item>

                        <uni-forms-item label="任务未完成按钮文字颜色:">
                            <view class="flex-row"
                                  @click="changeColor('task_item.button.uncompleted.text_color', set_data.task_item.button.uncompleted.text_color)">
                                <view class="color-block"
                                      :style="{backgroundColor: set_data.task_item.button.uncompleted.text_color}"></view>
                                <view class="flex-all-center pl10">
                                    <text class="iconfont icon-edit color-sub"></text>
                                </view>
                            </view>
                        </uni-forms-item>
                    </uni-forms>
                </scroll-view>

                <view class="confirm-button color-white bg-light-primary text-center"
                      hover-class="navigator-hover" @click="confirm">确定
                </view>

                <view class="flex-all-center">
                    <view class="p5 color-sub font12" @click="$refs.popup.close()">取消</view>
                </view>
            </view>
        </uni-popup>

        <color-picker ref="colorPicker" :color="colorPickerColorData" @confirm="colorConfirm"/>
    </view>
</template>

<script>
// 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
/** @namespace set.border_color */
/** @namespace set.task_item */

import config from '../config'
import colorPicker from './t-color-picker.vue'

export default {
    name: "task-popup-style-set",
    components: {colorPicker},
    props: {
        set: {
            type: Object,
            default: () => config.taskPopupDefaultStyle()
        }
    },

    data() {
        return {
            set_data: config.taskPopupDefaultStyle(),
            colorPickerColorData: null
        }
    },

    watch: {
        set: {
            handler() {
                this.set_data = this.set
            },
            immediate: true
        }
    },

    methods: {
        // "rgba(0, 0, 0, 0)" => {r: 0, g: 0, b: 0, a: 0}
        rgbaStringToObject(rgbaString) {
            const rgbaRegex = /rgba?\((\d+),\s*(\d+),\s*(\d+),\s*(\d*\.?\d+)\)/
            const match = rgbaString.match(rgbaRegex)

            if (match) {
                return {
                    r: parseInt(match[1], 10),
                    g: parseInt(match[2], 10),
                    b: parseInt(match[3], 10),
                    a: parseFloat(match[4])
                }
            }

            return {r: 0, g: 0, b: 0, a: 0} // 如果字符串不符合 rgba 格式，则返回 {r: 0, g: 0, b: 0, a: 0}
        },

        changeColor(key, color) {
            this.change_color_key = key
            this.colorPickerColorData = this.rgbaStringToObject(color)
            this.$nextTick(() => this.$refs.colorPicker.open())
        },

        colorConfirm(e) {
            const {r, g, b, a} = e.rgba
            const color = `rgba(${r}, ${g}, ${b}, ${a})`

            const key = this.change_color_key
            const keys = key.split('.')

            if (keys.length === 1) return this.$set(this.set_data, key, color)
            if (keys.length === 2) return this.$set(this.set_data[keys[0]], keys[1], color)
            if (keys.length === 3) return this.$set(this.set_data[keys[0]][keys[1]], keys[2], color)
            if (keys.length === 4) return this.$set(this.set_data[keys[0]][keys[1]][keys[2]], keys[3], color)
        },

        confirm() {
            this.$emit('update:set', this.set_data)
            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.data-show {
    position: relative;
    padding: 10px 50px 10px 10px;
    box-sizing: border-box;
    border-radius: 10px;

    .data-item {
        line-height: 30px;

        .label {
            padding-right: 10px;
        }

        .color-block {
            margin-top: 2px;
            width: 24px;
            height: 24px;
            border: 1px solid #000;
            border-radius: 5px;
        }
    }

    .control-icon {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
    }
}

.set-popup {
    padding: 10px;
    border-radius: 10px;
    width: 300px;
    max-width: 400px;

    .color-block {
        margin-top: 2px;
        width: 60px;
        height: 30px;
        border: 1px solid #000;
        border-radius: 5px;
    }

    .confirm-button {
        width: 120px;
        line-height: 40px;
        border-radius: 20px;
        margin: 20px auto 0;
    }
}
</style>