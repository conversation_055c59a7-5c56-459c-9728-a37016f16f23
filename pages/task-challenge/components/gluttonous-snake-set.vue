<template>
    <view>
        <view class="show bg-background">
            <view class="info">任务难度: {{ setLevel }}</view>
            <view class="info">完成条件: 长度达到{{ set.count }}米</view>
            <view class="info">时间限制: {{ set.seconds }}秒</view>

            <view class="control-icon flex-all-center bg-white" @click="$refs.popup.open()">
                <text class="iconfont icon-edit color-sub"></text>
            </view>
        </view>

        <uni-popup ref="popup">
            <view class="set-popup bg-white">
                <view class="text-center pb10">贪吃蛇任务设置</view>

                <uni-forms label-position="top" label-width="200">
                    <uni-forms-item label="任务难度:">
                        <picker :range="levelList" range-key="title" :value="setDataLevelPickerValue"
                                @change="levelChange">
                            <view class="form-picker flex-kai">
                                <view>{{ setDataLevel }}</view>
                                <uni-icons type="forward" size="16" color="#c0c4cc"/>
                            </view>
                        </picker>
                    </uni-forms-item>

                    <uni-forms-item label="完成条件: (单位: 米)">
                        <view class="form-tips color-sub font12">长度达到多少米完成任务。</view>
                        <uni-easyinput type="number" v-model="set_edit.count" maxlength="3"/>
                    </uni-forms-item>

                    <uni-forms-item label="时间限制: (单位: 秒)">
                        <uni-easyinput type="number" v-model="set_edit.seconds" maxlength="3"/>
                    </uni-forms-item>
                </uni-forms>

                <view class="confirm-button color-white bg-light-primary text-center"
                      hover-class="navigator-hover" @click="confirm">确定
                </view>

                <view class="flex-all-center">
                    <view class="p5 color-sub font12" @click="$refs.popup.close()">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import config from '../config'

export default {
    name: "gluttonous-snake-set",
    props: {
        set: {
            type: Object,
            default: () => JSON.parse(JSON.stringify(config.lianLianKanDefaultSet))
        }
    },

    data() {
        return {
            set_edit: this.set,
            levelList: JSON.parse(JSON.stringify(config.gluttonousSnakeLevelList))
        }
    },

    watch: {
        set: {
            deep: true,
            handler(val) {
                this.set_edit = val
            }
        }
    },

    computed: {
        setLevel() {
            return this.levelList.find(item => item.value === this.set.level)?.title || this.levelList[0].title
        },
        setDataLevel() {
            return this.levelList.find(item => item.value === this.set_edit.level)?.title || this.levelList[0].title
        },
        setDataLevelPickerValue() {
            const index = this.levelList.findIndex(item => item.value === this.set_edit.level)
            return index < 0 ? 0 : index
        }
    },

    methods: {
        levelChange(e) {
            this.set_edit.level = this.levelList[e.detail.value].value
        },

        confirm() {
            const data = JSON.parse(JSON.stringify(this.set_edit))

            const count = Math.floor(data.count)
            if (isNaN(count)) return this.$uni.showToast('请输入正确的完成条件', 'none', 3000)
            if (count < 1) return this.$uni.showToast('完成条件需要大于1米', 'none', 3000)
            data.count = count

            const seconds = Math.floor(data.seconds)
            if (isNaN(seconds)) return this.$uni.showToast('请输入正确的时间限制', 'none', 3000)
            if (seconds < 10) return this.$uni.showToast('时间限制需要大于10秒', 'none', 3000)
            data.seconds = seconds

            this.$emit('update:set', data)
            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.show {
    margin: 5px 0;
    padding: 10px;
    border-radius: 5px;
    position: relative;

    .info {
        line-height: 25px;
    }

    .control-icon {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
    }
}

.set-popup {
    padding: 10px;
    border-radius: 10px;
    width: 80vw;
    max-width: 400px;

    .form-picker {
        border: 1px solid #e5e5e5;
        border-radius: 4px;
        height: 34px;
        line-height: 34px;
        padding: 0 10px;
    }

    .form-tips {
        position: relative;
        top: -5px;
    }

    .confirm-button {
        width: 120px;
        line-height: 40px;
        border-radius: 20px;
        margin: 20px auto 0;
    }
}
</style>