<template>
    <view>
        <uni-popup ref="noticePopup" type="center" @touchmove.stop.prevent="" :is-mask-click="false">
            <view class="bg-white notice-popup">
                <view class="news-title text-center color-title ellipsis">{{ news_title }}</view>


                <scroll-view :scroll-y="true" class="news-content color-content">
                    <u-parse :content="news_content"/>
                </scroll-view>

                <template v-if="!isJoin">
                    <view class="flex-row pt10 pl5" style="border-top: 1px solid #eee;">
                        <view @click="agree = !agree">

                            <radio class="agree-radio" :checked="agree"/>
                        </view>
                        <view class="font14 color-sub">我已阅读并同意《{{ news_title }}》。</view>
                    </view>
                    <view class="flex-all-center pb10">
                        <view class="notice-confirm-button color-white text-center"
                              :class="{'bg-primary': agree, 'bg-disabled': !agree}"
                              @click="confirm">{{ confirm_text }}
                        </view>
                    </view>
                    <view v-if="showUserCenter" class="flex-all-center pb10">
                        <navigator class="color-sub font14 pl10 pr10" url="/pages/user/user">
                            个人中心
                        </navigator>
                    </view>
                </template>

                <view v-if="isJoin" class="flex-all-center pb10">
                    <view class="notice-confirm-button color-white text-center bg-primary" @click="confirm">
                        {{ confirm_text }}
                    </view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "notice-popup",
    props: {
        noticeList: {
            type: Array,
            default: () => []
        },
        isJoin: {
            type: Boolean,
            default: false
        },
        showUserCenter: {
            type: Boolean,
            default: false
        }
    },

    data() {
        return {
            showIndex: null,
            news_title: '活动须知',
            news_content: '',
            agree: false,
            confirm_text: '确定'
        }
    },

    methods: {
        open() {
            this.showIndex = -1
            this.showNext()
        },

        async showNext() {
            this.showIndex++
            
            this.news_title = ''
            this.news_content = '<p></p>'
            this.confirm_text = ''
            
            const set = this.noticeList[this.showIndex]
            const news_id = set.news_id

            const news_detail = await this.getNewsDetail(news_id)

            this.news_title = news_detail.title || '活动须知'
            this.news_content = news_detail.content || '活动须知'
            this.confirm_text = set.confirm_text || '确定'
            this.agree = false

            this.showIndex === 0 && this.$refs.noticePopup.open()
        },
        
        async getNewsDetail(news_id) {
            this.newsCache ||= {}
            if (this.newsCache[news_id]) return this.newsCache[news_id]
            
            this.$uni.showLoading('加载中...')
            const news_detail = await this.xwy_api.getNewsDetail(news_id)
            uni.hideLoading()
            
            this.newsCache[news_id] = news_detail
            return news_detail
        },

        confirm() {
            const {agree, isJoin, news_title, showIndex: index, noticeList: list} = this
            if (!agree && !isJoin) return this.$uni.showToast(`请勾选 我已阅读并同意《${news_title}》`)
            if (index === list.length - 1) return this.$refs.noticePopup.close()
            
            this.showNext()
        },
    }
}
</script>

<style lang="scss">
.notice-popup {
    width: 90vw;
    max-width: 400px;
    border-radius: 10px;
    
    .news-title {
        height: 40px;
        line-height: 40px;
        padding: 0 10px;
        border-bottom: 1px solid #eee;
    }

    .news-content {
        height: calc(85vh - 170px);
        margin: 10px;
        width: calc(100% - 20px);
    }

    .agree-radio {
        transform: scale(.7);
        position: relative;
        top: -3px;
        right: -3px;
    }
    
    .notice-confirm-button {
        height: 40px;
        line-height: 40px;
        border-radius: 20px;
        width: 200px;
    }
}
</style>