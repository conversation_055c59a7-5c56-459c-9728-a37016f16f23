<template>
    <view>
        <view class="form-item">
            <view class="top color-content">
                <view>{{ title }}任务绑定说明文章</view>
                <view class="font12 color-sub">绑定文章后，完成任务前需要查看绑定的文章。</view>
            </view>
            <view class="bottom font16">
                <view class="flex-kai" @click="toSelNews">
                    <view class="view">
                        <view v-if="news_id">
                            {{ news_title || news_id }}
                        </view>
                        <view v-else class="color-sub">选择文章</view>
                    </view>
                    <view class="flex-all-center">
                        <view v-if="news_id" class="color-sub font12" style="width: 30px;" @click.stop="deleteNews">
                            解绑
                        </view>
                        <uni-icons type="forward" color="#80848f" />
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "week-sport-task-news-bind",
    emits: ['update:news'],
    props: {
        news: {
            type: Object,
            default: () => ({ news_id: '', news_title: '' })
        },
        title: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            news_id: '',
            news_title: ''
        }
    },

    watch: {
        news: {
            handler(newVal) {
                this.news_id = newVal.news_id
                this.news_title = newVal.news_title
            },
            deep: true,
            immediate: true
        }
    },

    methods: {
        toSelNews() {
            this.$uni.navigateTo('/pages/news/list?type=user&is_sel=true', {
                events: {
                    selNews: ({id, title}) => {
                        this.news_id = id
                        this.news_title = title
                        this.$emit('update:news', { news_id: id, news_title: title })
                    }
                }
            })
        },

        deleteNews() {
            this.news_id = ''
            this.news_title = ''
            this.$emit('update:news', { news_id: '', news_title: '' })
        }
    }
}
</script>

<style lang="scss" scoped></style>