<template>
    <view class="music-container flex-all-center" :class="{'rotate': playing}" :style="containerStyle"
          @click="musicPlayChange">
        <text class="iconfont icon-background-music-play" :style="iconStyle"></text>
    </view>
</template>

<script>
export default {
    name: "bg-music",
    props: {
        src: {
            type: String,
            default: ''
        },
        autoplay: {
            type: Boolean,
            default: true
        },
        iconSize: {
            type: String,
            default: '24px'
        },
        iconColor: {
            type: String,
            default: '#ffffff'
        },
        border: {
            type: Boolean,
            default: true
        },
        borderSize: {
            type: String,
            default: '1px'
        },
        borderColor: {
            type: String,
            default: '#ffffff'
        },
    },
    data() {
        return {
            playing: false
        }
    },

    computed: {
        iconStyle() {
            const {iconSize: size, iconColor: color} = this
            return `font-size: ${size}; color: ${color};`
        },
        containerStyle() {
            if (!this.border) return ''
            const {borderSize: size, borderColor: color} = this
            return `border: ${size} solid ${color};`
        }
    },

    mounted() {
        this.backgroundMusicInit()
    },

    methods: {
        backgroundMusicInit() {
            this.musicContext ||= uni.createInnerAudioContext()
            this.musicContext.autoplay = this.autoplay
            this.musicContext.src = this.src

            this.musicContext.onError(err => {
                console.error('背景音频播放失败 - ', err)
                this.playing = false
            })
            this.musicContext.onPlay(() => this.playing = true)
            this.musicContext.onPause(() => this.playing = false)
            this.musicContext.onStop(() => this.playing = false)
        },
        
        musicPlayChange() {
            if (!this.musicContext) return
            this.playing ? this.musicContext.pause() : this.musicContext.play()
        }
    }
}
</script>

<style lang="scss">
.music-container {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
    box-sizing: border-box;
}

.rotate {
    animation: rotate 2s linear infinite;
}

@keyframes rotate {
    100% {
        transform: rotate(360deg);
    }
}
</style>