<template>
    <view>
        <view class="show bg-background">
            <view class="info">格子数量: {{ set.col }} x {{ set.row }}个</view>
            <view class="info">空白格子: {{ set.obstacle }}个</view>

            <view class="control-icon flex-all-center bg-white" @click="$refs.popup.open()">
                <text class="iconfont icon-edit color-sub"></text>
            </view>
        </view>

        <uni-popup ref="popup">
            <view class="set-popup bg-white">
                <view class="text-center pb10">任务设置</view>

                <uni-forms label-position="top" label-width="200">
                    <uni-forms-item label="横向格子数量:">
                        <uni-easyinput type="number" v-model="set.col" maxlength="2"
                                       placeholder="请输入4-10的整数"/>
                    </uni-forms-item>

                    <uni-forms-item label="竖向格子数量:">
                        <view class="form-item-tips">提示: 竖向格子数量不建议设置超过横向格子数量的两倍。</view>
                        <uni-easyinput type="number" v-model="set.row" maxlength="2"
                                       placeholder="请输入4-20的整数"/>
                    </uni-forms-item>

                    <uni-forms-item label="空白格子:">
                        <view class="form-item-tips">提示: 空白格子数量建议设置为格子总数的十分之一。</view>
                        <uni-easyinput type="number" v-model="set.obstacle" maxlength="2"
                                       placeholder="请输入空白格子数量"/>
                    </uni-forms-item>
                </uni-forms>

                <view class="confirm-button color-white bg-light-primary text-center"
                      hover-class="navigator-hover" @click="confirm">确定</view>

                <view class="flex-all-center">
                    <view class="p5 color-sub font12" @click="$refs.popup.close()">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>

export default {
    name: "one-stroke-line-set",
    props: {

        // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
        /** @namespace set.obstacle */
        set: {
            type: Object,
            default: () => ({
                row: 6,
                col: 6,
                obstacle: 3
            })
        }
    },
    
    data() {
        return {
            set_edit: this.set
        }
    },
    
    watch: {
        set: {
            deep: true,
            handler(val) {
                this.set_edit = val
            }
        }
    },

    methods: {
        confirm() {
            const data = JSON.parse(JSON.stringify(this.set_edit))
            
            const col = Math.floor(data.col)
            if (isNaN(col)) return this.$uni.showToast('请输入正确的横向格子数量', 'none', 3000)
            if (col < 4) return this.$uni.showToast('横向格子数量不能小于4', 'none', 3000)
            if (col > 10) return this.$uni.showToast('横向格子数量不能大于10', 'none', 3000)
            data.col = col
            
            const row = Math.floor(data.row)
            if (isNaN(row)) return this.$uni.showToast('请输入正确的竖向格子数量', 'none', 3000)
            if (row < 4) return this.$uni.showToast('竖向格子数量不能小于4', 'none', 3000)
            if (row > 20) return this.$uni.showToast('横向格子数量不能大于20', 'none', 3000)
            data.row = row
            
            const obstacle = Math.floor(data.obstacle)
            if (isNaN(obstacle)) return this.$uni.showToast('请输入正确的空白格子数量', 'none', 3000)
            data.obstacle = obstacle

            this.$emit('update:set', data)
            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.show {
    margin: 5px 0;
    padding: 10px;
    border-radius: 5px;
    position: relative;

    .info {
        line-height: 25px;
    }

    .control-icon {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
    }
}

.set-popup {
    padding: 10px;
    border-radius: 10px;
    width: 80vw;
    max-width: 400px;

    .form-item-tips {
        color: #80848f;
        font-size: 12px;
        transform: translateY(-6px);
    }
    
    .confirm-button {
        width: 120px;
        line-height: 40px;
        border-radius: 20px;
        margin: 20px auto 0;
    }
}
</style>