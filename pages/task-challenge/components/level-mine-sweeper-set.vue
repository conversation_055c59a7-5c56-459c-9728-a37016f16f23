<template>
    <view>
        <view class="show bg-background">
            <view class="info">雷区格子数量: {{ set.row }} x {{ set.col }}</view>
            <view class="info">雷区地雷数量: {{ set.boom_num }}个</view>
            <view class="info">扫雷时间限制: {{ set.seconds ? (set.seconds + '秒') : '不限时' }}</view>

            <view class="control-icon flex-all-center bg-white" @click="$refs.popup.open()">
                <text class="iconfont icon-edit color-sub"></text>
            </view>
        </view>

        <uni-popup ref="popup">
            <view class="set-popup bg-white">
                <view class="text-center pb10">扫雷任务设置</view>

                <uni-forms label-position="top" label-width="200">
                    <uni-forms-item label="雷区横向格子:">
                        <uni-easyinput type="number" v-model="set.row" maxlength="2"
                                       placeholder="请输入4-20的整数"/>
                    </uni-forms-item>

                    <uni-forms-item label="雷区竖向格子:">
                        <uni-easyinput type="number" v-model="set.col" maxlength="2"
                                       placeholder="请输入4-30的整数"/>
                    </uni-forms-item>

                    <uni-forms-item label="雷区地雷数量:">
                        <uni-easyinput type="number" v-model="set.boom_num" maxlength="3"
                                       placeholder="请输入雷区地雷的数量"/>
                    </uni-forms-item>

                    <uni-forms-item label="扫雷时间限制 (单位: 秒):">
                        <uni-easyinput type="number" v-model="set.seconds" maxlength="5"
                                       placeholder="不输入或输入0不限制时间"/>
                    </uni-forms-item>
                </uni-forms>

                <view class="confirm-button color-white bg-light-primary text-center"
                      hover-class="navigator-hover" @click="confirm">确定</view>

                <view class="flex-all-center">
                    <view class="p5 color-sub font12" @click="$refs.popup.close()">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "level-mine-sweeper-set",
    props: {
        
        // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
        /** @namespace set.row */
        /** @namespace set.col */
        /** @namespace set.boom_num */
        /** @namespace set.seconds */
        set: {
            type: Object,
            default: () => ({
                row: 8,
                col: 8,
                boom_num: 10,
                seconds: ''
            })
        }
    },
    
    data() {
        return {
            set_edit: this.set
        }
    },
    
    watch: {
        set: {
            deep: true,
            handler(val) {
                this.set_edit = val
            }
        }
    },

    methods: {
        confirm() {
            const data = JSON.parse(JSON.stringify(this.set_edit))
            
            const row = Math.floor(data.row)
            if (isNaN(row)) return this.$uni.showToast('请输入正确的雷区横向格子数量', 'none', 3000)
            if (row < 4) return this.$uni.showToast('雷区横向格子数量不能小于4', 'none', 3000)
            if (row > 20) return this.$uni.showToast('雷区横向格子数量不能大于20', 'none', 3000)
            data.row = row
            
            const col = Math.floor(data.col)
            if (isNaN(col)) return this.$uni.showToast('请输入正确的雷区竖向格子数量', 'none', 3000)
            if (col < 4) return this.$uni.showToast('雷区竖向格子数量不能小于4', 'none', 3000)
            if (col > 30) return this.$uni.showToast('雷区竖向格子数量不能大于30', 'none', 3000)
            data.col = col
            
            const boomNum = Math.floor(data.boom_num)
            if (isNaN(boomNum)) return this.$uni.showToast('请输入正确的雷区地雷数量', 'none', 3000)
            if (boomNum < 1) return this.$uni.showToast('请至少设置1个地雷', 'none', 3000)
            if (boomNum > data.row * data.col) return this.$uni.showToast(`雷区地雷数量不能大于雷区格子数量${data.row * data.col}(${data.row}x${data.col})`, 'none', 3000)
            data.boom_num = boomNum
            
            if (data.seconds) {
                const seconds = Math.floor(data.seconds)
                if (isNaN(seconds)) return this.$uni.showToast('请输入正确的扫雷时间限制', 'none', 3000)
                if (seconds < 0) return this.$uni.showToast('扫雷时间限制不能小于0', 'none', 3000)
                data.seconds = seconds
            }
           
            this.$emit('update:set', data)
            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.show {
    margin: 5px 0;
    padding: 10px;
    border-radius: 5px;
    position: relative;

    .info {
        line-height: 25px;
    }

    .control-icon {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
    }
}

.set-popup {
    padding: 10px;
    border-radius: 10px;
    width: 80vw;
    max-width: 400px;
    
    .confirm-button {
        width: 120px;
        line-height: 40px;
        border-radius: 20px;
        margin: 20px auto 0;
    }
}
</style>