<template>
    <view class="pt10 pb10">
        <view class="data-show bg-background">
            
            <view class="data-item flex-row">
                <view class="label">按钮文字:</view>
                <view>{{ set.text }}</view>
            </view>
            
            <view class="data-item flex-row">
                <view class="label">文字颜色:</view>
                <view class="color-block" :style="{backgroundColor: set.text_color}"></view>
            </view>
            
            <view class="data-item flex-row">
                <view class="label">按钮颜色:</view>
                <view class="color-block" :style="{backgroundColor: set.bg_color}"></view>
            </view>

            <view class="control-icon flex-all-center bg-white" @click="$refs.popup.open()">
                <text class="iconfont icon-edit color-sub"></text>
            </view>
            
        </view>
        
        
        <uni-popup ref="popup">
            <view class="set-popup bg-white">
                <view class="text-center pb10">开始闯关按钮设置</view>
                
                <uni-forms>
                    
                    <uni-forms-item label="按钮文字:">
                        <uni-easyinput v-model="set_data.text" maxlength="10"/>
                    </uni-forms-item>
                    
                    <uni-forms-item label="文字颜色:">
                        <view class="flex-row" @click="changeColor('text_color')">
                            <view class="color-block" :style="{backgroundColor: set_data.text_color}"></view>
                            <view class="flex-all-center pl10">
                                <text class="iconfont icon-edit color-sub"></text>
                            </view>
                        </view>
                    </uni-forms-item>
                    
                    <uni-forms-item label="按钮颜色:">
                        <view class="flex-row" @click="changeColor('bg_color')">
                            <view class="color-block" :style="{backgroundColor: set_data.bg_color}"></view>
                            <view class="flex-all-center pl10">
                                <text class="iconfont icon-edit color-sub"></text>
                            </view>
                        </view>
                    </uni-forms-item>
                    
                </uni-forms>
                
                <view class="confirm-button color-white bg-light-primary text-center"
                      hover-class="navigator-hover" @click="confirm">确定</view>
                
                <view class="flex-all-center">
                    <view class="p5 color-sub font12" @click="$refs.popup.close()">取消</view>
                </view>
            </view>
        </uni-popup>

        <color-picker ref="colorPicker" :color="colorPickerColorData" @confirm="colorConfirm"/>
    </view>
</template>

<script>
import config from '../config'
import colorPicker from './t-color-picker.vue'

export default {
    name: "start-challenge-button-set",
    components: {colorPicker},
    props: {

        // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
        /** @namespace set.text */
        /** @namespace set.text_color */
        /** @namespace set.bg_color */
        
        set: {
            type: Object,
            default: () => config.getDefaultStartButtonSet()
        }
    },
    
    data() {
        return {
            set_data: config.getDefaultStartButtonSet(),
            colorPickerColorData: null
        }
    },
    
    watch: {
        set: {
            handler() {
                this.set_data = this.set
            },
            immediate: true
        }
    },
   
    methods: {
        // "rgba(0, 0, 0, 0)" => {r: 0, g: 0, b: 0, a: 0}
        rgbaStringToObject(rgbaString) {
            const rgbaRegex = /rgba?\((\d+),\s*(\d+),\s*(\d+),\s*(\d*\.?\d+)\)/
            const match = rgbaString.match(rgbaRegex)

            if (match) {
                return {
                    r: parseInt(match[1], 10),
                    g: parseInt(match[2], 10),
                    b: parseInt(match[3], 10),
                    a: parseFloat(match[4])
                }
            }

            return {r: 0, g: 0, b: 0, a: 0} // 如果字符串不符合 rgba 格式，则返回 {r: 0, g: 0, b: 0, a: 0}
        },

        changeColor(key) {
            this.change_color_key = key
            const color = this.set_data[key]
            this.colorPickerColorData = this.rgbaStringToObject(color)
            this.$nextTick(() => this.$refs.colorPicker.open())
        },
        
        colorConfirm(e) {
            console.log(e);
            const {r, g, b, a} = e.rgba
            this.$set(this.set_data, this.change_color_key, `rgba(${r}, ${g}, ${b}, ${a})`)
        },

        confirm() {
            this.$emit('update:set', this.set_data)
            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.data-show {
    position: relative;
    padding: 10px 50px 10px 10px;
    box-sizing: border-box;
    border-radius: 10px;
    
    .data-item {
        line-height: 30px;
        
        .label {
            width: 75px;
            min-width: 75px;
        }
        
        .color-block {
            margin-top: 2px;
            width: 24px;
            height: 24px;
            border: 1px solid #000;
            border-radius: 5px;
        }
    }

    .control-icon {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
    }
}

.set-popup {
    padding: 10px;
    border-radius: 10px;
    width: 80vw;
    max-width: 400px;

    .color-block {
        margin-top: 2px;
        width: 60px;
        height: 30px;
        border: 1px solid #000;
        border-radius: 5px;
    }
    
    .confirm-button {
        width: 120px;
        line-height: 40px;
        border-radius: 20px;
        margin: 20px auto 0;
    }
}
</style>