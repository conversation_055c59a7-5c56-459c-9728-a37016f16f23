<template>
    <view>
        <view class="task-item flex-kai"
              :style="{borderColor: style.border_color, backgroundColor: style.bg_color}">
            <view class="task-item-info flex-row">
                <view v-if="item.logo" class="task-item-logo">
                    <image class="task-item-logo-image" :src="item.logo" mode="aspectFill"/>
                </view>
                <view>
                    <view class="pt5" @click="clickTitle">
                        <text :style="{color: style.name_color}">{{ item.title }}</text>
                        <template v-if="item.integral || item.per_integral">
                            <text class="iconfont icon-integral pl10"
                                  :style="{color: style.integral_color}"></text>
                            <text :style="{color: style.integral_color}">
                                +{{ item.integral || item.per_integral }}
                            </text>
                        </template>
                    </view>
                    <view v-if="item.text || item.describe" class="font12 pt5"
                          :style="{color: style.text_color}">
                        {{ item.text || item.describe }}
                    </view>

                    <view v-if="item.types === 30" class="pt5" @click="toCloudWish">
                        <text class="color-light-primary font12">查看{{ item.title }}</text>
                        <uni-icons type="forward" size="12" color="#5cadff"/>
                    </view>

                    <view v-if="signListEnterShow" class="pt5" @click="toCheckInSquare">
                        <text class="color-light-primary font12">打卡广场</text>
                        <uni-icons type="forward" size="12" color="#5cadff"/>
                    </view>
                </view>
            </view>

            <view class="task-item-button" :style="buttonStyle"
                  hover-class="navigator-hover" @click="toCompleteTask">
                {{ item.complete_button_text }}
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "task-list-item",
    emits: ['toCompleteTask', 'toSportMomentList', 'lookWeekTaskNews', 'clickTitle'],
    props: ['item', 'style', 'activeId', 'showSportMoment', 'sportMomentName', 'hideSignListEnter'],

    computed: {
        buttonStyle() {
            const {completed, uncompleted} = this.style.button
            const {bg_color, text_color} = this.item.is_finished ? completed : uncompleted
            return `background-color: ${bg_color}; color: ${text_color}`
        },

        signListEnterShow() {
            if (this.hideSignListEnter) return false
            return [31, 33, 34].includes(this.item.types)
        }
    },

    methods: {
        clickTitle() {
            if (this.item.types === 3 || this.item.types === 45) this.toSportMomentList()
            if (this.item.types === 31 || this.item.types === 33 || this.item.types === 34) this.lookWeekTaskNews()
            this.$emit('clickTitle')
        },

        toSportMomentList() {
            this.$emit('toSportMomentList', this.item.types)
        },

        lookWeekTaskNews() {
            this.$emit('lookWeekTaskNews', this.item.types)
        },

        toCompleteTask() {
            this.$emit('toCompleteTask')
        },

        toCloudWish() {
            this.$uni.navigateTo(`/pages/cloud_wish/user/detail?id=${this.activeId}&task_active=1&not_show_publish_button=1`)
        },

        toCheckInSquare() {
            this.$uni.navigateTo(`/pages/sport_clock_in/user/public_sign_list?id=${this.activeId}&types=${this.item.types}`)
        }
    }
}
</script>

<style lang="scss">
.task-item {
    margin: 10px 0;
    padding: 10px;
    border-radius: 10px;
    border: 1px solid #feedcd;
    background-color: #fffdfa;

    .task-item-info {
        .task-item-logo {
            padding-right: 10px;

            .task-item-logo-image {
                width: 60px;
                min-width: 60px;
                height: 60px;
                display: block;
            }
        }
    }

    .task-item-button {
        width: 70px;
        height: 34px;
        line-height: 34px;
        border-radius: 17px;
        text-align: center;
        min-width: 70px;
        margin: auto 0 auto 10px;
        font-size: 14px;
    }
}
</style>