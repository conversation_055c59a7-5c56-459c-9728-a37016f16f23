<template>
    <view>
        <view class="show flex-row">
            <view class="control-icon flex-all-center bg-white" @click="setPopupOpen">
                <text class="iconfont icon-edit color-sub"></text>
            </view>

            <view class="info">
                <view class="flex-row">
                    <view class="info-label">开启提示:</view>
                    <view>{{ open ? '' : '不' }}开启</view>
                </view>
                <view class="flex-row">
                    <view class="info-label">提示描述:</view>
                    <view>{{ tips || '' }}</view>
                </view>
                <view v-if="isSetDay" class="flex-row">
                    <view class="info-label">提示时间:</view>
                    <view>活动结束{{ days === 0 ? '当天' : `前${days}天`}}提示</view>
                </view>
            </view>
        </view>

        <uni-popup ref="popup">
            <view class="set-popup bg-white">
                <view class="text-center pb10">设置</view>

                <uni-forms>
                    <uni-forms-item label="开启提示:">

                        <picker :range="['不开启提示', '开启提示']" :value="editData.open"
                                @change="editData.open = Number($event.detail.value)">
                            <view class="picker-form flex-kai">
                                <view>{{ editData.open ? '' : '不' }}开启提示</view>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" color="#80848f"/>
                                </view>
                            </view>

                        </picker>
                    </uni-forms-item>
                    <uni-forms-item label="提示描述:">
                        <uni-easyinput v-model="editData.tips" maxlength="100"/>
                    </uni-forms-item>
                    <uni-forms-item v-if="isSetDay" label="提示时间:">
                        <uni-easyinput type="number" v-model="editData.days" maxlength="5"/>
                        <view class="font12 color-sub">如需设置活动结束当天提示，请填0或不填</view>
                    </uni-forms-item>
                </uni-forms>

                <view class="confirm-button color-white bg-light-primary text-center"
                      hover-class="navigator-hover" @click="confirm">确定
                </view>

                <view class="flex-all-center">
                    <view class="p5 color-sub font12" @click="$refs.popup.close()">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "body-data-submit-task-tips",
    props: {
        set: {
            type: Object,
            default: () => {}
        },

        isSetDay: {
            type: Boolean,
            default: false
        }
    },

    data() {
        return {
            open: 0,
            days: 0,
            tips: '',
            editData: {
                open: 0,
                days: 0,
                tips: '',
            }
        }
    },

    watch: {
        set: {
            handler() {
                this.open = this.set.open || 0
                this.days = this.set.days || 0
                this.tips = this.set.tips || ''
            },
            immediate: true
        }
    },

    methods: {
        setPopupOpen() {
            this.editData = {
                open: this.open,
                days: this.days,
                tips: this.tips
            }

            this.$nextTick(() => this.$refs.popup.open())
        },


        confirm() {
            const data = {
                open: this.editData.open,
                tips: this.editData.tips
            }
            if (this.isSetDay) {
                const days = Math.floor(this.editData.days)
                if (isNaN(days) || days < 0) return this.$uni.showToast('请输入正确的提示时间')
                data.days = days
            }

            this.$emit('update:set', data)
            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.show {
    position: relative;
    background-color: #f8f8f8;
    border-radius: 5px;
    margin: 5px;
    padding: 10px;

    .control-icon {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
    }

    .info {
        font-size: 14px;
        color: #495060;

        .info-label {
            white-space: nowrap;
            padding-right: 3px;
        }
    }
}

.set-popup {
    padding: 10px;
    border-radius: 10px;
    width: 80vw;
    max-width: 400px;

    .picker-form {
        height: 34px;
        line-height: 34px;
        padding-left: 10px;
        border: 1px solid #DCDFE6;
        border-radius: 4px;
    }

    .confirm-button {
        width: 120px;
        line-height: 40px;
        border-radius: 20px;
        margin: 20px auto 0;
    }
}
</style>