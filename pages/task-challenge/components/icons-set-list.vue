<template>
    <view>
        <view class="icons-set-list">
            <view class="icons-set-item flex-kai bg-background" 
                  v-for="item in icons_list" :key="item.name">
                <view class="info color-content font14">
                    <view>
                        <text>图标功能: {{ item.title }}</text>
                        <text v-show="item.hide" class="pl5 color-sub font12">(已隐藏)</text>
                    </view>
                    <view>图标图片: {{ item.type === 'system' ? '系统默认' : '自定义' }}</view>
                </view>
                <view class="pl10 flex-row">
                    <view v-show="item.image" class="pr10">
                        <image class="item-icon-image" :src="item.image" mode="aspectFill"/>
                    </view>
                    <view class="control-icons clear clearfix">
                        <template v-if="item.name !== 'open_more' && item.name !== 'close_more'">
                            <view class="control-icon flex-all-center bg-white fl" style="margin-right: 5px;"
                                  @click="moveUp(item)">
                                <uni-icons type="arrow-up" size="16" color="#80848f"/>
                            </view>
                            <view class="control-icon flex-all-center bg-white fl" @click="moveDown(item)">
                                <uni-icons type="arrow-down" size="16" color="#80848f"/>
                            </view>
                        </template>
                        <view class="control-icon flex-all-center bg-white fr" @click="showItem(item)">
                            <text class="iconfont icon-edit color-sub"></text>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <uni-popup ref="popup" @touchmove.stop.prevent="">
            <view class="edit-popup bg-white p10">
                <view class="text-center">{{ edit_item.title }}图标设置</view>
                
                <view class="edit-popup-forms">
                    <uni-forms label-position="top" label-width="200" border>
                        <uni-forms-item v-show="canSetHide" label="是否显示">
                            <view class="flex-row">
                                <view @click="edit_item.hide = 0">

                                    <radio class="radio" :checked="edit_item.hide === 0"
                                           color="#5cadff"/>
                                    <text style="padding-right: 20px;">显示</text>
                                </view>
                                <view @click="edit_item.hide = 1">

                                    <radio class="radio" :checked="edit_item.hide === 1"
                                           color="#5cadff"/>
                                    <text style="padding-right: 20px;">隐藏</text>
                                </view>
                            </view>
                        </uni-forms-item>
                        
                        <uni-forms-item label="图标类型">
                            <view class="flex-row">
                                <view v-for="item in type_list" :key="item.type" 
                                      @click="typeChange(item.type)">

                                    <radio class="radio" :checked="item.type === edit_item.type" 
                                           color="#5cadff"/>
                                    <text style="padding-right: 20px;">{{ item.title }}</text>
                                </view>
                            </view>
                        </uni-forms-item>

                        <uni-forms-item v-show="edit_item.type === 'diy'" label="自定义图标" required>
                            <view class="edit-popup-image-container" v-if="edit_item.image">
                                <view class="edit-popup-image-clear flex-all-center"
                                      @click="edit_item.image = ''">
                                    <uni-icons type="closeempty" size="16" color="#e20f04"/>
                                </view>

                                <image class="edit-popup-image" :src="edit_item.image" mode="aspectFill"
                                       @click="previewImage"/>
                            </view>

                            <view v-else hover-class="navigator-hover" @click="imageChange"
                                  class="edit-popup-add-image flex-all-center bg-background color-disabled">
                                <uni-icons type="plusempty" size="38" color="#bbbec4"/>
                            </view>
                        </uni-forms-item>
                    </uni-forms>
                </view>

                <view class="edit-popup-buttons flex-all-center font14">
                    <view class="edit-popup-button bg-background color-sub" hover-class="navigator-hover"
                          @click="$refs.popup.close()">取消
                    </view>
                    <view class="edit-popup-button bg-light-primary color-white" hover-class="navigator-hover"
                          @click="editConfirm">确定
                    </view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
// 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
/** @namespace edit_item */

export default {
    name: "icons-set-list",
    props: {
        list: {
            type: Array,
            default: () => []
        }
    },

    data() {
        return {
            icons_list: [],
            edit_item: {},
            type_list: [
                {type: 'system', title: '系统默认'},
                {type: 'diy', title: '自定义'}
            ]
        }
    },
    
    computed: {
        sortLastIndex() {
            return this.icons_list.filter(item => item.name !== 'open_more' && item.name !== 'close_more').length - 1
        },

        canSetHide() {
            const name = this.edit_item.name
            if (!name) return false
            
            // 活动设置不能隐藏，避免手贱把活动设置隐藏了无法设置活动了
            // 展开更多和收起更多不能手动隐藏，界面会自动根据显示的图标数自动判断是否需要显示 展开更多和收起更多
            const noSetHideNames = ['set', 'open_more', 'close_more']
            
            return !noSetHideNames.includes(name)
        }
    },

    watch: {
        list: {
            handler() {
                this.icons_list = this.list
            },
            immediate: true
        },
        
        icons_list: {
            handler() {
                this.$emit('update:list', this.icons_list)
            },
            immediate: true
        }
    },

    methods: {
        moveUp(item) {
            // 因为v-for的index是不会跟着item的位置改变的，所以不能使用v-for的index
            const index = this.icons_list.findIndex(v => v.name === item.name)
            if (index === 0) return this.$uni.showToast('已经是第一个了')
            const previous = this.icons_list[index - 1]

            this.$set(this.icons_list, index, previous)
            this.$set(this.icons_list, index - 1, item)
        },

        moveDown(item) {
            // 因为v-for的index是不会跟着item的位置改变的，所以不能使用v-for的index
            const index = this.icons_list.findIndex(v => v.name === item.name)
            if (index === this.sortLastIndex) return this.$uni.showToast('不能往下移了')
            const next = this.icons_list[index + 1]

            this.$set(this.icons_list, index, next)
            this.$set(this.icons_list, index + 1, item)
        },
        
        
        typeChange(type) {
            this.edit_item.type = type
            if (this.edit_item.type === 'system') this.edit_item.image = ''
        },

        imageChange() {
            this.$uni.navigateTo('/pages/other/image_upload_or_select', {
                events: {
                    newImg: src => {
                        this.edit_item.image = src
                    }
                }
            })
        },

        previewImage() {
            this.$uni.previewImage(this.edit_item.image)
        },

        showItem(item) {
            const data = JSON.parse(JSON.stringify(item))
            data.hide ||= 0
            this.edit_item = data
            this.$refs.popup.open()
        },

        editConfirm() {
            const edit_item = JSON.parse(JSON.stringify(this.edit_item))
            if (edit_item.type === 'diy' && !edit_item.image) return this.$uni.showToast('请上传自定义图标')
            const index = this.icons_list.findIndex(v => v.name === edit_item.name)
            this.$set(this.icons_list, index, edit_item)
            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.icons-set-item {
    margin: 5px;
    padding: 10px;
    border-radius: 5px;

    .info {
        line-height: 25px;
    }

    .item-icon-image {
        display: block;
        width: 50px;
        min-width: 50px;
        height: 50px;
    }
    
    .control-icons {
        margin: 10px 0;
        width: 100px;
        
        .control-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
        }
    }
}

.edit-popup {
    border-radius: 10px;
    width: 85vw;
    max-width: 400px;
    
    .edit-popup-forms {
        height: 360px;
        
        .radio {
            transform: scale(.8);
            position: relative;
            top: -2px;
        }
    }

    .edit-popup-image-container, .edit-popup-image, .edit-popup-add-image {
        width: 100px;
        height: 100px;
        border-radius: 5px;
    }

    .edit-popup-image-container {
        position: relative;

        .edit-popup-image-clear {
            position: absolute;
            right: 5px;
            top: 5px;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, .7);
        }

        .edit-popup-image {
            display: block;
        }
    }

    .edit-popup-buttons {
        padding: 10px;

        .edit-popup-button {
            margin: 0 10px;
            width: 100px;
            line-height: 40px;
            border-radius: 20px;
            text-align: center;
        }
    }
}
</style>