<template>
    <view>
        <view class="show flex-row">
            <view class="control-icon flex-all-center bg-white" @click="setPopupOpen">
                <text class="iconfont icon-edit color-sub"></text>
            </view>

            <view class="logo">
                <image v-if="set.logo" class="logo-image" :src="set.logo" mode="aspectFill"/>
                <view v-else class="logo-image flex-all-center">
                    <uni-icons type="image" color="#bbbec4" size="60"/>
                </view>
            </view>
            <view class="info">
                <view class="flex-row">
                    <view class="info-label">名称:</view>
                    <view>{{ set.title }}</view>
                </view>
                <view class="flex-row">
                    <view class="info-label">描述:</view>
                    <view>{{ set.text || '不显示描述' }}</view>
                </view>
                <view v-if="buttonSet" class="flex-row">
                    <view class="info-label">按钮文字:</view>
                    <view>{{ set.button_text }}</view>
                </view>
            </view>
        </view>

        <uni-popup ref="popup">
            <view class="set-popup bg-white">
                <view class="text-center">设置</view>


                <view style="padding: 20px 0;">
                    <view class="flex-all-center">
                        <view class="set-logo" @click="changeLogo">

                            <image v-if="set_data.logo" class="set-logo-image"
                                   :src="set_data.logo" mode="aspectFill"/>
                            <view v-else class="set-logo-image flex-all-center">
                                <uni-icons type="image" color="#bbbec4" size="60"/>
                            </view>
                        </view>
                    </view>
                    <view class="flex-all-center pt5">
                        <view class="color-light-primary font14" @click="changeLogo">
                            {{ set_data.logo ? '修改' : '设置' }}logo图
                        </view>
                    </view>
                </view>

                <uni-forms>
                    <uni-forms-item label="名称:">
                        <uni-easyinput v-model="set_data.title" maxlength="20"/>
                    </uni-forms-item>
                    <uni-forms-item label="描述:">
                        <uni-easyinput v-model="set_data.text" maxlength="100"/>
                    </uni-forms-item>
                    <uni-forms-item v-if="buttonSet" label="按钮文字:">
                        <uni-easyinput v-model="set_data.button_text" maxlength="5"/>
                    </uni-forms-item>
                </uni-forms>

                <view class="confirm-button color-white bg-light-primary text-center"
                      hover-class="navigator-hover" @click="confirm">确定
                </view>

                <view class="flex-all-center">
                    <view class="p5 color-sub font12" @click="$refs.popup.close()">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "task-list-show-shop-set",
    props: {
        set: {
            type: Object
        },
        activeId: {
            type: String
        },
        buttonSet: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            set_data: {
                title: '',
                logo: '',
                text: '',
                button_text: ''
            }
        }
    },

    watch: {
        set: {
            handler() {
                this.set_data = this.set
            },
            immediate: true
        }
    },

    methods: {
        setPopupOpen() {
            this.$refs.popup.open()
        },

        changeLogo() {
            this.$uni.navigateTo(`/pages/other/image_upload_or_select?active_id=${this.activeId}`, {
                events: {
                    newImg: src => {
                        this.set_data.logo = src
                    }
                }
            })
        },

        confirm() {
            this.$emit('update:set', this.set_data)
            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.show {
    position: relative;
    background-color: #f8f8f8;
    border-radius: 5px;
    margin: 5px;
    padding: 10px;

    .control-icon {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
    }

    .logo {
        margin-right: 10px;

        image {
            display: block;
        }

        .logo-image {
            width: 60px;
            min-width: 60px;
            height: 60px;
            border-radius: 5px;
        }
    }

    .info {
        font-size: 14px;
        color: #495060;

        .info-label {
            white-space: nowrap;
            padding-right: 3px;
        }
    }
}

.set-popup {
    padding: 10px;
    border-radius: 10px;
    width: 80vw;
    max-width: 400px;

    .set-logo {
        image {
            display: block;
        }

        .set-logo-image {
            width: 60px;
            height: 60px;
            border-radius: 5px;
        }
    }

    .confirm-button {
        width: 120px;
        line-height: 40px;
        border-radius: 20px;
        margin: 20px auto 0;
    }
}
</style>