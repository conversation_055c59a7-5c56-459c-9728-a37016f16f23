<template>
    <view>
        <view class="list">
            <view class="item flex-kai bg-background" v-for="(item, index) in noticeList" :key="index">
                <view class="color-content font14">
                    <view>阅读须知文章: {{ item.news_title || item.news_id }}</view>
                    <view class="pt5">确定按钮文字: {{ item.confirm_text }}</view>
                </view>
                <view class="controls flex-kai">
                    <view class="control flex-all-center" @click="editSet(index)">
                        <text class="iconfont icon-edit color-sub"></text>
                    </view>
                    <view class="control flex-all-center" @click="deleteSet(index)">
                        <text class="iconfont icon-delete color-sub"></text>
                    </view>
                </view>
            </view>
        </view>

        <view class="flex-center">
            <view class="color-light-primary p10 font14" @click="addSet">添加阅读须知文章</view>
        </view>

        <uni-popup ref="setPopup" @touchmove.stop.prevent="">
            <view class="edit-popup bg-white p10">
                <view class="text-center">阅读须知文章配置</view>

                <view class="edit-popup-forms">
                    <uni-forms label-position="top" label-width="200" border>
                        <uni-forms-item label="阅读须知文章" required>
                            <view class="false-input flex-kai" @click="chooseNews">
                                <template>
                                    <view v-if="setData.news_id" class="pr10">
                                        {{ setData.news_title || setData.news_id }}
                                    </view>
                                    <view v-else style="color: #808080">选择文章</view>
                                </template>
                                <view class="flex-all-center">
                                    <uni-icons type="forward" size="16" color="#bbbec4"/>
                                </view>
                            </view>
                        </uni-forms-item>

                        <uni-forms-item label="确认按钮文字">
                            <uni-easyinput v-model="setData.confirm_text" maxlength="10"
                                           placeholder="不填默认为“确定”"/>
                        </uni-forms-item>
                    </uni-forms>
                </view>

                <view class="edit-popup-buttons flex-all-center font14">
                    <view class="edit-popup-button bg-background color-sub" hover-class="navigator-hover"
                          @click="$refs.setPopup.close()">取消
                    </view>
                    <view class="edit-popup-button bg-light-primary color-white" hover-class="navigator-hover"
                          @click="editConfirm">确定
                    </view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const getDefaultSet = () => {
    return {
        news_id: '',
        news_title: '',
        confirm_text: '确定'
    }
}

export default {
    name: "notice-list-set",
    props: {
        list: {
            type: Array,
            default: () => []
        }
    },

    data() {
        return {
            noticeList: [],
            setData: getDefaultSet()
        }
    },

    watch: {
        list: {
            handler() {
                this.noticeList = this.list
            },
            immediate: true
        },

        noticeList: {
            handler() {
                this.$emit('update:list', this.noticeList)
            },
            immediate: true
        }
    },

    mounted() {

    },

    methods: {
        addSet() {
            this.editIndex = null
            this.setData = getDefaultSet()

            this.$refs.setPopup.open()
        },

        editSet(index) {
            this.editIndex = index
            this.setData = {...this.noticeList[index]}

            this.$refs.setPopup.open()
        },

        editConfirm() {
            const set = {...this.setData}
            if (!set.news_id) return this.$uni.showToast('请选择文章')
            set.confirm_text ||= '确定'

            if (this.editIndex) return this.$set(this.noticeList, this.editIndex, set)
            this.noticeList.push(set)
            this.$refs.setPopup.close()
        },

        chooseNews() {
            this.$uni.navigateTo('/pages/news/list?type=user&is_sel=true', {
                events: {
                    selNews: news => {
                        this.setData.news_id = news.id
                        this.setData.news_title = news.title
                    }
                }
            })
        },

        async deleteSet(index) {
            const res = await this.$uni.showModal('确定删除？', {showCancel: true})
            if (res.confirm) this.noticeList.splice(index, 1)
        }
    }
}
</script>

<style lang="scss" scoped>
.list {
    .item {
        margin: 10px 0;
        padding: 10px;
        border-radius: 5px;

        .controls {
            width: 70px;
            min-width: 70px;
            padding-left: 10px;
            align-items: center;

            .control {
                width: 30px;
                height: 30px;
                border-radius: 50%;
                background-color: #fff;
            }
        }
    }
}

.edit-popup {
    border-radius: 10px;
    width: 85vw;
    max-width: 400px;

    .false-input {
        width: 100%;
        box-sizing: border-box;
        border: 1px solid #e5e5e5;
        border-radius: 4px;
        min-height: 36px;
        line-height: 22px;
        padding: 7px 10px;
    }

    .edit-popup-buttons {
        padding: 10px;

        .edit-popup-button {
            margin: 0 10px;
            width: 100px;
            line-height: 40px;
            border-radius: 20px;
            text-align: center;
        }
    }
}
</style>