<template>
    <view>
        <uni-popup ref="sharePopup" type="bottom" :safe-area="false">
            <view class="share-popup">
                <view class="share-popup-title">转发分享</view>
                <view class="popup-container">
                    <view class="flex-row">
                        <!-- #ifndef H5 -->
                        <button class="share-item" open-type="share" @click="close">
                            <text class="iconfont icon-share color-primary font28"/>
                            <view class="color-sub font14">转发给微信好友</view>
                        </button>
                        <!-- #endif -->
                        <button class="share-item" @click="showShareImage">
                            <text class="iconfont icon-qr-code font28 color-warning"/>
                            <view class="color-sub font14">生成活动二维码</view>
                        </button>
                    </view>
                    <view class="share-cancel font16 color-sub text-center" @click="close">取消</view>
                </view>
            </view>
        </uni-popup>

        <canvas v-if="qrcodeLogo" id="qrcode" canvas-id="qrcode"
                style="width: 500px; height: 500px; position: fixed; top: -999999px; left: -99999px;">
        </canvas>
    </view>
</template>

<script>
export default {
    name: "share-popup",
    props: {
        shortId: {
            type: Number,
            default: 0
        },
        qrcodeLogo: {
            type: String,
            default: ''
        }
    },

    methods: {
        open() {
            this.$refs.sharePopup.open()
        },

        close() {
            this.$refs.sharePopup.close()
        },

        showShareImage() {
            this.share_image ? this.previewShareImage() : this.getShareImage()
            this.close()
        },


        async getShareImage() {
            const data = {
                page: 'pages/task-challenge/user/details',
                scene: 'id=' + this.shortId,
                cb: share_image => {
                    if (!share_image) return this.$uni.showModal('活动二维码生成失败')

                    this.share_image = share_image
                    this.previewShareImage()
                }
            }
            if (this.qrcodeLogo) {
                data.qrcode_logo = this.qrcodeLogo
                data.canvas_id = 'qrcode'
                data.canvas_this = this
            }

            this.$uni.showLoading('二维码生成中...')
            await this.xwy_api.getQrcode(data)
            uni.hideLoading()
        },

        previewShareImage() {
            this.$uni.previewImage(this.share_image)
        },
    }
}
</script>

<style lang="scss" scoped>
.share-popup {
    .share-item {
        padding: 0;
        margin: 10px 0;
        background-color: rgba(0, 0, 0, 0);
        border: none;
        font-weight: normal;
        width: 50% !important;
        line-height: 1.5;
    }

    .share-item::after, .share-btn::after {
        border: none;
    }

    .share-cancel {
        line-height: 44px;
        border-top: 1px solid #eee;
    }
}
</style>