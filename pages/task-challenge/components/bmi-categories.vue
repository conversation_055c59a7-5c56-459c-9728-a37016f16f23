<template>
    <view class="bmi-categories-table">
        <view class="table-header">
            <view class="table-cell">BMI</view>
            <view class="table-cell">体重情况</view>
        </view>
        <view class="table-row" v-for="(item, index) in categories" :key="index"
              :style="{ backgroundColor: item.bgColor }">
            <view class="table-cell">{{ item.range }}</view>
            <view class="table-cell">{{ item.status }}</view>
        </view>
    </view>
</template>

<script>
export default {
    name: "bmi-categories",
    data() {
        return {
            categories: [
                /*{ range: "BMI<18.5", status: "体重过轻", bgColor: '#E0F2F7' },
                { range: "18.5≤BMI<24", status: "体重正常", bgColor: '#D5E8D4' },
                { range: "24≤BMI<28", status: "超重", bgColor: '#FFF2CC' },
                { range: "28≤BMI<32.5", status: "轻度肥胖", bgColor: '#F8CECC' },
                { range: "32.5≤BMI<37.5", status: "中度肥胖", bgColor: '#F5C6CB' },
                { range: "37.5≤BMI<50", status: "重度肥胖", bgColor: '#E6B8B7' },
                { range: "BMI≥50", status: "极重度肥胖", bgColor: '#DAE8FC' }*/

                {range: "BMI < 18.5", status: "偏瘦", bgColor: '#57AEFE'},
                {range: "18.5 ≤ BMI < 24", status: "正常", bgColor: '#2CD994'},
                {range: "24 ≤ BMI < 28", status: "偏胖", bgColor: '#FDB64C'},
                {range: "BMI ≥ 28", status: "肥胖", bgColor: '#FF5E5E'}
            ]
        };
    }
}
</script>

<style lang="scss" scoped>
.bmi-categories-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    font-size: 14px;
}

.table-header,
.table-row {
    display: flex;
    border-bottom: 1px solid #E0E0E0;
}

.table-header {
    color: #333;
}

.table-row {
    font-weight: 500;
}

.table-header .table-cell {
    font-weight: bold;
    background-color: #f5f5f5;
}

.table-cell {
    flex: 1;
    padding: 8px;
    text-align: center;
    border-left: 1px solid #E0E0E0;
}

.table-cell:first-child {
    border-left: none;
}

.table-row:last-child {
    border-bottom: none;
}
</style>