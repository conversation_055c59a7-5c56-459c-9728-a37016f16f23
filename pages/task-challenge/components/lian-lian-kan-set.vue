<template>
    <view>
        <view class="show bg-background">
            <view class="info">图标数量: {{ set.col }} x {{ set.row }}</view>
            <view class="info">图标种类: {{ set.img_count }}个</view>
            <view class="info">时间限制: {{ set.seconds ? (set.seconds + '秒') : '不限时' }}</view>

            <view class="control-icon flex-all-center bg-white" @click="$refs.popup.open()">
                <text class="iconfont icon-edit color-sub"></text>
            </view>
        </view>

        <uni-popup ref="popup">
            <view class="set-popup bg-white">
                <view class="text-center pb10">连连看任务设置</view>

                <uni-forms label-position="top" label-width="200">
                    <uni-forms-item label="横向图标数量:">
                        <uni-easyinput type="number" v-model="set.col" maxlength="2"
                                       placeholder="请输入4-10的整数"/>
                    </uni-forms-item>

                    <uni-forms-item label="竖向图标数量:">
                        <uni-easyinput type="number" v-model="set.row" maxlength="2"
                                       placeholder="请输入4-15的整数"/>
                    </uni-forms-item>

                    <uni-forms-item label="图标种类:">
                        <uni-easyinput type="number" v-model="set.img_count" maxlength="2"
                                       placeholder="请输入图标种类数量"/>
                    </uni-forms-item>

                    <uni-forms-item label="时间限制 (单位: 秒):">
                        <uni-easyinput type="number" v-model="set.seconds" maxlength="5"
                                       placeholder="不输入或输入0不限制时间"/>
                    </uni-forms-item>
                </uni-forms>

                <view class="confirm-button color-white bg-light-primary text-center"
                      hover-class="navigator-hover" @click="confirm">确定</view>

                <view class="flex-all-center">
                    <view class="p5 color-sub font12" @click="$refs.popup.close()">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import config from '../config'

export default {
    name: "lian-lian-kan-set",
    props: {
        
        // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
        /** @namespace set.row */
        /** @namespace set.col */
        /** @namespace set.img_count */
        /** @namespace set.seconds */
        set: {
            type: Object,
            default: () => JSON.parse(JSON.stringify(config.lianLianKanDefaultSet))
        }
    },
    
    data() {
        return {
            set_edit: this.set
        }
    },
    
    watch: {
        set: {
            deep: true,
            handler(val) {
                this.set_edit = val
            }
        }
    },

    methods: {
        confirm() {
            const data = JSON.parse(JSON.stringify(this.set_edit))
            
            const col = Math.floor(data.col)
            if (isNaN(col)) return this.$uni.showToast('请输入正确的横向图标数量', 'none', 3000)
            if (col < 4) return this.$uni.showToast('横向图标数量不能小于4', 'none', 3000)
            if (col > 10) return this.$uni.showToast('横向图标数量不能大于10', 'none', 3000)
            data.col = col
            
            const row = Math.floor(data.row)
            if (isNaN(row)) return this.$uni.showToast('请输入正确的竖向图标数量', 'none', 3000)
            if (row < 4) return this.$uni.showToast('竖向图标数量不能小于4', 'none', 3000)
            if (row > 15) return this.$uni.showToast('横向图标数量不能大于15', 'none', 3000)
            data.row = row
            
            if (col * row % 2 !== 0) return this.$uni.showToast('横向图标数量与竖向图标数量相乘的结果必须为2的整倍数', 'none', 3000)
            
            const img_count = Math.floor(data.img_count)
            if (isNaN(img_count)) return this.$uni.showToast('请输入正确的图标种类数量', 'none', 3000)
            if (img_count < 2) return this.$uni.showToast('请至少设置2种图标种类', 'none', 3000)
            data.boom_num = img_count
            
            if (data.seconds) {
                const seconds = Math.floor(data.seconds)
                if (isNaN(seconds)) return this.$uni.showToast('请输入正确的时间限制', 'none', 3000)
                if (seconds < 0) return this.$uni.showToast('时间限制不能小于0', 'none', 3000)
                data.seconds = seconds
            }
           
            this.$emit('update:set', data)
            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.show {
    margin: 5px 0;
    padding: 10px;
    border-radius: 5px;
    position: relative;

    .info {
        line-height: 25px;
    }

    .control-icon {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
    }
}

.set-popup {
    padding: 10px;
    border-radius: 10px;
    width: 80vw;
    max-width: 400px;
    
    .confirm-button {
        width: 120px;
        line-height: 40px;
        border-radius: 20px;
        margin: 20px auto 0;
    }
}
</style>