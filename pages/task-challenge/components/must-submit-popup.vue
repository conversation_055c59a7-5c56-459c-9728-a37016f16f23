<template>
    <view>
        <uni-popup class="my-uni-popup" ref="popup" :is-mask-click="false" @touchmove.stop.prevent="">

            <view class="join-popup-c">
                <view class="join-popup-c-title">{{ update ? '修改报名信息' : '参加活动' }}</view>

                <view class="popup-container" style="padding-top: 30px;">
                    <template>
                        <template v-if="batchImportJoin">
                            <view>
                                <input class="join-input" v-model="username"
                                       :placeholder="batchImportLabel.username"/>
                            </view>
                            <view>
                                <input class="join-input" v-model="password"
                                       :placeholder="batchImportLabel.password"/>
                            </view>
                        </template>

                        <template v-else>
                            <template v-if="must_submit.length">
                                <view v-for="(item, index) in must_submit" :key="index">
                                    <picker
                                        v-if="item.types === 2"
                                        :range="item.options"
                                        range-key="text"
                                        @change="mustValueChange($event, index)"
                                    >
                                        <view class="join-input flex-kai">
                                            <view v-if="!item.value" class="color-sub">
                                                请选择{{ item.title }}{{ item.rules === 1 ? ' (必选)' : '' }}
                                            </view>
                                            <view v-if="item.value">{{ item.value }}</view>
                                            <text class="iconfont icon-more color-disabled font18"/>
                                        </view>
                                    </picker>


                                    <input v-else :key="index" class="join-input" v-model="item.value"
                                           :placeholder="`请输入${item.title}${item.rules === 1 ? ' (必填)' : ''}`"/>
                                </view>

                            </template>


                            <picker v-if="sexRequire !== -1" :range="sexLabel" range-key="title"
                                    :value="sexPickerValue" @change="sexChange">
                                <view class="join-input flex-kai">
                                    <view>
                                        <text v-if="sex">{{ sexPickerText }}</text>
                                        <text v-else class="color-sub">
                                            请选择性别
                                            <template v-if="sexRequire === 1"> (必选)</template>
                                        </text>
                                    </view>
                                    <text class="iconfont icon-more color-disabled font18"/>
                                </view>
                            </picker>

                            <view v-if="showTeam && !teamId" class="join-input flex-kai" @click="changeTeam">
                                <template>
                                    <view v-if="team_id">{{ team_name || team_id }}</view>
                                    <view v-else class="color-sub">
                                        <text>请选择队伍</text>
                                        <text v-if="teamRequired" class="pl5">(必选)</text>
                                    </view>
                                </template>
                                <text class="iconfont icon-more color-disabled font18"/>
                            </view>
                        </template>
                    </template>


                    <view class="join-popup-buttons">
                        <view class="join-popup-cancel-button" @click="close">取消</view>
                        <view class="join-popup-confirm-button" @click="confirm">确定</view>
                    </view>


                    <view v-if="evn_version === 'trial' || evn_version === 'develop'" class="flex-all-center">
                        <view class="p10 color-sub font14" @click="$emit('trialNoJoinJustGoTask')">
                            直接进入
                        </view>
                    </view>
                </view>
            </view>

            <template v-if="showAd">
                <view class="pt5">
                    <xwy-ad :ad_type="66" :activity_id="activeId"/>
                </view>
                <xwy-ad :ad_type="3" :activity_id="activeId"/>
            </template>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "must-submit-popup",
    emits: ['close', 'mustSubmitUpdate'],
    props: {
        activeId: {
            type: String,
            default: ''
        },
        mustSubmit: {
            type: Array,
            default: []
        },
        update: {
            type: Boolean,
            default: false
        },
        showAd: {
            type: Boolean,
            default: true
        },
        showTeam: {
            type: Boolean,
            default: false
        },
        teamName: {
            type: String,
            default: ''
        },
        teamRequired: {
            type: Boolean,
            default: false
        },
        teamId: {
            type: Number,
            default: 0
        },
        
        // 是否使用白名单导入报名
        batchImport: {
            type: Boolean,
            default: false
        },

        // 白名单导入时报名表单提示
        batchImportLabel: {
            type: Object,
            default: () => ({username: '请输入账号', password: '请输入密码'})
        },

        sexRequire: {
            type: Number,
            default: 0
        },
        sexLabel: {
            type: Array,
            default: () => [{value: 1, title: '男'}, {value: 2, title: '女'}]
        },
        userSex: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            must_submit: [],
            team_name: '',
            team_id: 0,
            username: '',
            password: '',
            evn_version: wx?.getAccountInfoSync?.()?.miniProgram?.envVersion || 'release',
            sex: 0
        }
    },
    
    computed: {
        batchImportJoin() {
            return this.batchImport && !this.update
        },

        sexPickerValue() {
            const index = this.sexLabel.findIndex(item => item.value === this.sex)
            return index === -1 ? 0 : index
        },
        sexPickerText() {
            return this.sexLabel.find(item => item.value === this.sex)?.title || ''
        }
    },

    watch: {
        mustSubmit: {
            handler(newVal) {
                this.must_submit = newVal
            },
            immediate: true
        },

        teamName(newVal) {
            this.team_name = newVal
        },
        teamId(newVal) {
            this.team_id = newVal
        },

        userSex(val) {
            console.log(val);
            this.sex = val
        }
    },

    mounted() {

    },

    methods: {
        open() {
            this.$refs.popup.open()
        },

        close() {
            this.$refs.popup.close()
            this.$emit('close')
        },

        mustValueChange(e, index) {
            const item = this.must_submit[index]
            item.value = item.options[e.detail.value].text
            this.$set(this.must_submit, index, item)
        },

        sexChange(e) {
            this.sex = this.sexLabel[e.detail.value].value
        },

        changeTeam() {
            this.$uni.navigateTo(`/pages/activity/admin/team_list?selteam=1&id=${this.activeId}`, {
                events: {
                    setTeam: team => {
                        this.team_id = team.team_id
                        this.team_name = team.team_name
                    }
                }
            })  
        },

        confirm() {
            if (this.batchImportJoin) return this.checkUsernamePassword()
            if (this.update) return this.updateMustSubmit()
            this.joining()
        },

        async checkUsernamePassword() {
            const username = this.username
            const password = this.password
            if (!username) return this.$uni.showToast(this.batchImportLabel.username)
            if (!password) return this.$uni.showToast(this.batchImportLabel.password)

            this.$uni.showLoading()

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.adminUser/check_acitve_username',
                data: {
                    active_id: this.activeId,
                    username,
                    password
                }
            })

            uni.hideLoading()

            if (res?.status !== 1 || !res?.data?.user_details) {
                return this.$uni.showModal(res?.info || '验证失败')
            }


            await this.usernamePasswordJoin(res.data.user_details)
        },

        async usernamePasswordJoin(user_details) {
            this.$uni.showLoading('报名中...')

            let must_submit = JSON.stringify(user_details.conf_json)
            must_submit = must_submit.replace(/·/g, '-')
            const data = {
                active_id: this.activeId,
                must_submit: this._utils.base64['encode'](must_submit)
            }
            if (user_details['logs_types']) data.team_id = user_details['logs_types']
            
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.user/submit_attend_active',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '报名失败')

            this.$uni.showToast('报名成功', 'success')
            this.$emit('mustSubmitUpdate')
        },


        getMustSubmitData() {
            const must_submit = JSON.parse(JSON.stringify(this.must_submit))
            for (let i = 0; i < must_submit.length; i++) {
                const v = must_submit[i]
                v.options && delete v.options
                if (v.rules === 1 && v.value === '') {
                    this.$uni.showToast(`请${v.types === 2 ? '选择' : '输入'}${v.title}`)
                    return false
                }
                if (v.types === 3 && v.value && v.value.length !== 11) {
                    this.$uni.showToast('请输入正确的手机号')
                    return false
                }
            }

            let must_submit_str = JSON.stringify(must_submit)
            must_submit_str = must_submit_str.replace(/·/g, '-')
            return this._utils.base64['encode'](must_submit_str)
        },

        uploadHeadimg(src) {
            this.updateMustSubmit(src)
        },

        async updateMustSubmit(headimg = '') {
            const data = {
                active_id: this.activeId
            }

            const must_submit = this.getMustSubmitData()
            if (must_submit === false) return
            data.must_submit = must_submit

            if (headimg) data.headimg = headimg
            if (this.team_id) data.team_id = this.team_id

            if (this.sexRequire === 1 && !this.sex) return this.$uni.showToast('请选择性别')
            if (this.sexRequire !== -1 && this.sex) data.sex = this.sex

            this.$uni.showLoading('修改中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.user/update_attend_details',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '修改失败')
            this.$uni.showToast('修改成功', 'success')
            this.$emit('mustSubmitUpdate')

        },

        async joining() {
            const data = {
                active_id: this.activeId
            }

            if (this.must_submit && this.must_submit.length) {
                const must_submit = this.getMustSubmitData()
                if (must_submit === false) return
                data.must_submit = must_submit
            }

            if (this.sexRequire === 1 && !this.sex) return this.$uni.showToast('请选择性别')
            if (this.sexRequire !== -1 && this.sex) data.sex = this.sex


            if (this.showTeam && this.teamRequired && !this.team_id) return this.$uni.showToast('请选择队伍')
            if (this.team_id) data.team_id = this.team_id

            this.$uni.showLoading('报名中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.user/submit_attend_active',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '报名失败')

            this.$uni.showToast('报名成功', 'success')
            this.$emit('mustSubmitUpdate')
        },
    }
}
</script>

<style lang="scss">
.my-uni-popup {
    position: relative;
    z-index: 999;
}

.join-popup-c {
    .join-input {
        margin: 0 20px 10px;
        padding: 10px;
        border: 1px solid #feedcd;
        background-color: #fffdfa;
        border-radius: 5px;
        width: 70vw;
        max-width: 400px;
    }
}
</style>