<template>
    <view class="medal3-set__">
        <view class="form-item">
            <view class="top color-content">勋章设置</view>
            <view class="bottom">
                <view class="medal3-set__medal-list">
                    <view class="medal3-set__medal-item flex-row" v-for="item in list" :key="item.types">
                        <view class="medal3-set__medal-logo flex-all-center">
                            <image v-if="item.pic" class="medal3-set__medal-logo-img" :src="item.pic"
                                mode="aspectFill" />
                            <uni-icons v-else type="image" size="50" color="#bbbec4" />
                        </view>
                        <view class="med3-set__medal-info">
                            <view>勋章类型: {{ getMedalTypeTitle(item.types) }}</view>
                            <view>勋章名称: {{ item.title }}</view>
                            <view v-if="item.types === 1">答题分数: {{ item.exam_min_score || '不限' }}</view>
                            <view v-if="item.types === 3">每日人数: {{ item.person || '不限' }}</view>
                        </view>

                        <view class="medal3-set__control-icons flex-row">
                            <view class="medal3-set__control-icon flex-all-center bg-white"
                                  @click="moveUp(item)">
                                <uni-icons type="arrow-up" size="16" color="#80848f"/>
                            </view>
                            <view class="medal3-set__control-icon flex-all-center bg-white"
                                  @click="moveDown(item)">
                                <uni-icons type="arrow-down" size="16" color="#80848f"/>
                            </view>
                            <view class="medal3-set__control-icon flex-all-center bg-white"
                                  @click="editMedal(item)">
                                <text class="iconfont icon-edit color-sub"></text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <uni-popup ref="popup">
            <view class="set-popup bg-white">
                <view class="text-center">{{ setTyppesTitle }}设置</view>

                <view style="padding: 20px 0;">
                    <view class="flex-all-center">
                        <view class="set-logo" @click="changeLogo">

                            <image v-if="set_data.pic" class="set-logo-image" :src="set_data.pic" mode="aspectFill" />
                            <view v-else class="set-logo-image flex-all-center">
                                <uni-icons type="image" color="#bbbec4" size="60" />
                            </view>
                        </view>
                    </view>
                    <view class="flex-all-center pt5">
                        <view class="color-light-primary font14" @click="changeLogo">
                            {{ set_data.pic ? '修改' : '设置' }}勋章图
                        </view>
                    </view>
                </view>

                <uni-forms>
                    <uni-forms-item label="勋章名称:">
                        <uni-easyinput v-model="set_data.title" maxlength="20" />
                    </uni-forms-item>
                    <uni-forms-item v-if="set_data.types === 1" label="答题分数:">
                        <uni-easyinput type="digit" v-model="set_data.exam_min_score" maxlength="5"/>
                        <view class="font12 color-sub">
                            可以获得勋章的最低分数，如设置80，则答题分数需要大于等于80分才能获得勋章。不限制分数则留空。
                        </view>
                    </uni-forms-item>
                    <uni-forms-item v-if="set_data.types === 3" label="每日人数:">
                        <uni-easyinput type="number" v-model="set_data.person" maxlength="8"/>
                        <view class="font12 color-sub">
                            可以获得勋章的每日人数，如设置10，则每日只有前10名达标用户获得勋章。不限制人数则留空。
                        </view>
                    </uni-forms-item>
                </uni-forms>

                <view class="confirm-button color-white bg-light-primary text-center" hover-class="navigator-hover"
                    @click="confirm">确定
                </view>

                <view class="flex-all-center">
                    <view class="p5 color-sub font12" @click="$refs.popup.close()">取消</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "medal3-set",
    emits: ['update:set'],
    props: ['set', 'activeId'],
    data() {
        return {
            list: {},
            typesOptions: [
                { types: 1, title: '答题勋章' },
                { types: 2, title: '打卡勋章' },
                { types: 3, title: '步数勋章' }
            ],
            set_data: {},
            set_index: 0
        }
    },

    computed: {
        setTyppesTitle() {
            return this.typesOptions.find(item => item.types === this.set_data.types)?.title || ''
        }
    },

    watch: {
        set: {
            handler(val) {
                const set = JSON.parse(JSON.stringify(val || {}))
                if (set.list?.length) this.list = set.list
            },
            immediate: true,
            deep: true
        }
    },

    methods: {
        getMedalTypeTitle(types) {
            return this.typesOptions.find(item => item.types === types)?.title || ''
        },

        moveUp(item) {
            // 因为v-for的index是不会跟着item的位置改变的，所以不能使用v-for的index
            const index = this.list.findIndex(v => v.types === item.types)
            if (index === 0) return this.$uni.showToast('已经是第一个了')
            const previous = this.list[index - 1]

            this.$set(this.list, index, previous)
            this.$set(this.list, index - 1, item)

            this.updateSet()
        },

        moveDown(item) {
            // 因为v-for的index是不会跟着item的位置改变的，所以不能使用v-for的index
            const index = this.list.findIndex(v => v.types === item.types)
            if (index === this.list.length - 1) return this.$uni.showToast('不能往下移了')
            const next = this.list[index + 1]

            this.$set(this.list, index, next)
            this.$set(this.list, index + 1, item)

            this.updateSet()
        },

        editMedal(item) {
            this.set_data = JSON.parse(JSON.stringify(item))
            // 因为v-for的index是不会跟着item的位置改变的，所以不能使用v-for的index
            this.set_index = this.list.findIndex(v => v.types === item.types)
            this.setPopupOpen()
        },

        setPopupOpen() {
            this.$refs.popup.open()
        },

        changeLogo() {
            this.$uni.navigateTo(`/pages/other/image_upload_or_select?active_id=${this.activeId}`, {
                events: {
                    newImg: src => {
                        this.set_data.pic = src
                    }
                }
            })
        },

        confirm() {
            const {types, person, exam_min_score, title, pic} = this.set_data

            if (!pic) {
                this.$uni.showToast('请设置勋章图')
                return
            }

            if (!title) {
                this.$uni.showToast('请输入勋章名称')
                return
            }

            if (types === 1) {
                if (!exam_min_score) {
                    this.$uni.showToast('请输入答题分数')
                    return
                }

                const score = Number(exam_min_score)
                if (score < 0) {
                    this.$uni.showToast('答题分数不能小于0')
                    return
                }

                this.set_data.exam_min_score = score
            }
            

            if (types === 3) {
                if (!person) {
                    this.$uni.showToast('请输入每日人数')
                    return
                }

                const personNum = Math.floor(person)
                if (personNum < 0) {
                    this.$uni.showToast('每日人数不能小于0')
                    return
                }

                this.set_data.person = personNum
            }

            this.$set(this.list, this.set_index, this.set_data)
            this.$refs.popup.close()

            this.updateSet()
        },

        // 为什么不能再watch里面监听执行，因为排序的时候会更新两次list，所以会导致watch到两次导致这里执行两次
        updateSet() {
            const list = this.list.map(item => {
                // 不知道为什么，排序调换位置后，各自的属性会叠加在一起，所以这里要删掉
                if (item.types !== 1 && item.hasOwnProperty('exam_min_score')) delete item.exam_min_score
                if (item.types !== 3 && item.hasOwnProperty('person')) delete item.person
                return item
            })
            const set = {list}
            console.log(set);
            this.$emit('update:set', set)
        }
    }
}
</script>

<style lang="scss" scoped>
.medal3-set__ {
    .medal3-set__medal-list {
        .medal3-set__medal-item {
            margin: 5px;
            padding: 10px;
            border-radius: 10px;
            background-color: #f8f8f8;
            position: relative;

            .medal3-set__medal-logo {
                $size: 60px;
                width: $size;
                height: $size;

                .medal3-set__medal-logo-img {
                    width: $size;
                    height: $size;
                    box-sizing: border-box;
                    border-radius: 5px;
                }
            }

            .med3-set__medal-info {
                margin-left: 10px;
                font-size: 14px;
                color: #495060;
            }

            .medal3-set__control-icons {
                position: absolute;
                top: 5px;
                right: 5px;
                width: 100px;

                .medal3-set__control-icon {
                    margin-left: 5px;
                    width: 30px;
                    height: 30px;
                    border-radius: 50%;
                }
    }
        }
    }

    .set-popup {
        padding: 10px;
        border-radius: 10px;
        width: 80vw;
        max-width: 400px;

        .set-logo {
            image {
                display: block;
            }

            .set-logo-image {
                width: 60px;
                height: 60px;
                border-radius: 5px;
            }
        }

        .confirm-button {
            width: 120px;
            line-height: 40px;
            border-radius: 20px;
            margin: 20px auto 0;
        }
    }
}
</style>