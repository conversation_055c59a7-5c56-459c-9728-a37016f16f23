<template>
    <view>
        <uni-popup class="body-data-submit__" ref="popup" :is-mask-click="false" @touchmove.stop.prevent="">

            <view class="body-data-submit__container">
                <view class="body-data-submit__title">身体数据提交</view>


                <view v-if="heightSubmit" class="body-data-submit__input flex-row">
                    <view class="color-sub pr5">身高:</view>
                    <input v-model="height" placeholder="单位: cm" maxlength="5"/>
                </view>
                <view v-if="weightSubmit" class="body-data-submit__input flex-row">
                    <view class="color-sub pr5">体重:</view>
                    <input v-model="weight" placeholder="单位: kg" maxlength="5"/>
                </view>
                <view v-if="waistSubmit" class="body-data-submit__input flex-row">
                    <view class="color-sub pr5">腰围:</view>
                    <input v-model="waist" placeholder="单位: cm" maxlength="5"/>
                </view>


                <picker v-if="birthdaySubmit" mode="date" :value="birthday" :end="today" @change="birthdayChange">
                    <view class="body-data-submit__input flex-kai">
                        <view class="flex-row">
                            <view class="color-sub pr5">生日:</view>
                            <view>{{ birthday }}</view>
                        </view>
                        <text class="iconfont icon-more color-disabled font18"/>
                    </view>
                </picker>

                <!--                    <picker mode="date" :value="submitDate" @change="submitDateChange">
                                        <view class="join-input flex-kai">
                                            <view class="flex-row">
                                                <view class="color-sub pr5">日期: </view>
                                                <view>{{ submitDate }}</view>
                                            </view>
                                            <text class="iconfont icon-more color-disabled font18"/>
                                        </view>
                                    </picker>-->


                <view class="body-data-submit__buttons">
                    <view class="body-data-submit__cancel-button" @click="close">取消</view>
                    <view class="body-data-submit__confirm-button" @click="confirm">确定</view>
                </view>
            </view>

            <template v-if="showAd">
                <view class="pt5">
                    <xwy-ad :ad_type="66" :activity_id="activeId"/>
                </view>
                <xwy-ad :ad_type="3" :activity_id="activeId"/>
            </template>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "body-data-submit",
    props: {
        showAd: {
            type: Boolean,
            default: false
        },

        activeId: {
            type: String,
            default: ''
        }
    },

    computed: {
        heightSubmit() {
            return this.bodyFatRecord
        },

        weightSubmit() {
            return this.weightRecord || this.bodyFatRecord
        },

        waistSubmit() {
            return this.waistRecord
        },

        birthdaySubmit() {
            return this.bodyFatRecord && this.submitBirthday
        }
    },

    data() {
        return {
            weight: '',
            height: '',
            today: this._utils.getDay(0, true),
            birthday: this._utils.getDay(0, true),
            waist: '',
            weightRecord: false,
            waistRecord: false,
            bodyFatRecord: false,
            submitDate: this._utils.getDay(0, true),
            submitBirthday: false
        }
    },

    mounted() {

    },

    methods: {
        open(openData) {
            this.weightRecord = openData.weightRecord
            this.waistRecord = openData.waistRecord
            this.bodyFatRecord = openData.bodyFatRecord
            if (openData.submitBirthday) this.submitBirthday = true

            if (openData.weight) this.weight = openData.weight
            if (openData.height) this.height = openData.height
            if (openData.waist) this.waist = openData.waist
            if (openData.birthday) this.birthday = openData.birthday
            if (openData.submit_date) this.submitDate = openData.submit_date

            if (openData.userid) this.userid = openData.userid

            this.$nextTick(() => this.$refs.popup.open())
        },

        birthdayChange(e) {
            this.birthday = e.detail.value
        },

        submitDateChange(e) {
            this.submitDate = e.detail.value
        },


        async confirm() {
            const data = {active_id: this.activeId}

            if (this.heightSubmit) {
                const height = this._utils.formatDecimal(this.height)
                if (isNaN(height)) return this.$uni.showToast('请输入正确的身高')
                if (height < 100) return this.$uni.showToast('身高不能小于100cm')
                data.height = height
            }

            if (this.weightSubmit) {
                const weight = this._utils.formatDecimal(this.weight)
                if (isNaN(weight) || weight <= 0) return this.$uni.showToast('请输入正确的体重')
                data.weight = weight
            }

            if (this.waistSubmit) {
                const waist = this._utils.formatDecimal(this.waist)
                if (isNaN(waist) || waist <= 0) return this.$uni.showToast('请输入正确的腰围')
                data.waist = waist
            }

            if (this.birthdaySubmit) {
                if (!this.birthday) return this.$uni.showToast('请选择生日')
                data.birthday = this.birthday
            }

            if (this.submitDate) data.submit_date = this.submitDate

            if (this.userid) data.userid = this.userid

            this.$uni.showLoading('提交中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.lose_weight.user/submit_weight_height',
                data
            })
            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '提交失败')

            this.$uni.showToast('已提交')
            this.$refs.popup.close()

            this.$emit('success')
        },

        close() {
            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.body-data-submit__ {
    position: relative;
    z-index: 999;
}

.body-data-submit__container {
    background-color: #fff;
    border-radius: 10px;
    padding: 10px;
    box-sizing: border-box;

    .body-data-submit__title {
        text-align: center;
        padding: 10px;
    }

    .body-data-submit__input {
        margin: 0 20px 10px;
        padding: 10px;
        border: 1px solid #eee;
        background-color: #fffdfa;
        border-radius: 5px;
        width: 70vw;
        max-width: 400px;
    }

    .body-data-submit__buttons {
        display: flex;
        justify-content: center;
        align-items: center;
        padding-bottom: 10px;

        .body-data-submit__cancel-button, .body-data-submit__confirm-button {
            width: 100px;
            line-height: 40px;
            border-radius: 20px;
            text-align: center;
            margin: 10px;
            font-size: 18px;
        }
    }

    .body-data-submit__confirm-button {
        background-color: #5cadff;
        color: #fff;
    }

    .body-data-submit__cancel-button {
        background-color: #bbbec4;
        color: #fff;
    }
}
</style>