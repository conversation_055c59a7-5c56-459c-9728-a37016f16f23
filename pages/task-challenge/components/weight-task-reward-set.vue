<template>
    <view class="w-100">
        <view class="rule-item flex-kai w-100" v-for="(item, index) in list_" :key="index">
            <view class="flex-row">
                <view class="word-container">减重</view>
                <view class="input-container">
                    <uni-easyinput type="digit" v-model="item.min" maxlength="5" :clearable="false"/>
                </view>
                <view class="word-container">%到</view>
                <view class="input-container">
                    <uni-easyinput type="digit" v-model="item.max" maxlength="5" :clearable="false"/>
                </view>
                <view class="word-container">%, 奖励</view>
                <view class="input-container">
                    <uni-easyinput type="number" v-model="item.integral" maxlength="8" :clearable="false"/>
                </view>
                <view class="word-container">积分</view>
            </view>

            <view class="del-icon text-center" @click="deleteRule(index)">
                <text class="iconfont icon-delete color-sub"></text>
            </view>
        </view>

        <view class="flex-all-center">
            <view class="color-light-primary p10" @click="addRewardRule">添加奖励规则</view>
        </view>
    </view>
</template>

<script>
export default {
    name: "weight-task-reward-set",
    props: ['list'],
    data() {
        return {
            list_: []
        }
    },

    mounted() {
        this.list_ = this.list
    },

    methods: {
        getListSet() {
            const list = this.list_

            if (!list.length) return this.$uni.showToast('请添加奖励规则')

            for (let i = 0, len = list.length; i < len; i++) {
                const item = list[i]
                const min = Number(item.min),
                    max = Number(item.max),
                    integral = Number(item.integral)

                if (isNaN(min) || isNaN(max) || isNaN(integral) || min > max) {
                    this.$uni.showToast(`第${i+1}个奖励规则有误，请检查`, 'none', 3000)
                    return null
                }

                item.min = min
                item.max = max
                item.integral = integral
            }

            return list
        },

        addRewardRule() {
            this.list_.push({
                min: '',
                max: '',
                integral: ''
            })
            console.log(this.list_);
        },

        deleteRule(index) {
            this.list_.splice(index, 1)
        }
    }
}
</script>

<style lang="scss" scoped>
.rule-item {
    &:not(:first-child) {
        padding-top: 10px;
    }
}

.input-container {
    width: 50px;
}
.word-container {
    line-height: 36px;
    color: #333;
    font-size: 14px;
}

.del-icon {
    width: 36px;
    line-height: 36px;
}
</style>