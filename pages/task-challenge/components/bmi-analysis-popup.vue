<template>
    <view>
        <uni-popup ref="uniPopup" @close="popupClose">
            <view class="popup-content bg-white">
                <view class="close" @click="popupClose">
                    <uni-icons type="close" size="24" color="#bbbec4"/>
                </view>
                <view class="popup-title text-center color-title">BMI指数</view>
                <bmi-progress-bar :bmi="bmiValue" class="bmi-progress-bar-container"/>
                <view class="bmi-result-text">
                    你的BMI为
                    <text class="color-light-primary font-bold">{{ bmiValue }}</text>
                    属于
                    <text class="font-bold">{{ bmiText }}</text>
                </view>
                <bmi-categories class="bmi-categories-container"/>
                <view class="bmi-formula-container">
                    <view class="bmi-formula-title">BMI 计算公式:</view>
                    <view class="bmi-formula-text">体重 (kg) / 身高² (m²)</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import bmiProgressBar from './bmi-progress-bar.vue';
import BmiCategories from './bmi-categories.vue';

export default {
    name: "bmi-analysis-popup",
    components: {bmiProgressBar, BmiCategories},
    props: ['bmiValue', 'bmiText'],
    data() {
        return {}
    },
    methods: {
        open() {
            this.$refs.uniPopup.open()
        },
        popupClose() {
            this.$refs.uniPopup.close()
            this.$emit('close')
        }
    }
}
</script>

<style lang="scss">
.popup-content {
    width: 90vw;
    max-width: 400px;
    padding: 20px;
    border-radius: 10px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    .close {
        position: absolute;
        top: 0;
        right: 0;
        padding: 5px;
    }
}

.popup-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
}

.bmi-progress-bar-container {
    width: 100%;
    margin-bottom: 15px;
}

.bmi-result-text {
    font-size: 14px;
    color: #333;
    margin-bottom: 15px;
    text-align: center;
}

.bmi-categories-container {
    width: 100%;
    margin-bottom: 20px;
}

.bmi-formula-container {
    margin-top: 10px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 8px;
    width: 100%;
    box-sizing: border-box;
    text-align: center;
}

.bmi-formula-title {
    font-size: 14px;
    font-weight: bold;
    color: #555;
    margin-bottom: 5px;
}

.bmi-formula-text {
    font-size: 13px;
    color: #666;
}
</style>