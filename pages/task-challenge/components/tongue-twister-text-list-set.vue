<template>
    <view>
        <view class="form-item">
            <view class="top color-content">
                <view>绕口令口令设置</view>
                <view class="color-sub font14">
                    进行绕口令任务时，随机从下列口令配置中获取一条口令作为任务内容。
                </view>
            </view>
            <view class="bottom font16">
                <view class="tongue-twister-list">
                    <view class="tongue-twister-item ai-sport-item"
                          v-for="(item, index) in localSet.text_list" :key="index">
                        <view class="item-controls flex-row">
                            <view class="p5" @click="editTongueTwisterItem(index)">
                                <text class="iconfont icon-edit color-sub font18"></text>
                            </view>
                            <view class="p5" @click="deleteTongueTwisterItem(index)">
                                <text class="iconfont icon-delete color-sub font18"></text>
                            </view>
                        </view>

                        <view class="flex-row" style="padding-top: 15px;">
                            <view class="item-info">
                                <view>{{ item.text }}</view>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="flex-all-center pb5">
                    <view class="p5 color-light-primary font14" @click="editTongueTwisterItem(null)">
                        添加口令
                    </view>
                </view>
            </view>
        </view>

        <view class="form-item">
            <view class="top color-content">
                <view>绕口令任务准确率设置</view>
                <view class="color-sub font14">
                    准确率需要达到设置值才能获得奖励，请输入0-100的整数。
                </view>
            </view>
            <view class="bottom font16">
                <input class="input" type="number" v-model="localSet.accuracy"/>
            </view>
        </view>

        <uni-popup ref="tongueTwisterEditPopup" @touchmove.stop.prevent="">
            <view class="bg-white ai-sport-item-edit">
                <view class="text-center color-title font14 p10">绕口令设置</view>

                <uni-forms label-position="top" label-width="200" :border="true">
                    <uni-forms-item label="口令内容" :required="true">
                        <uni-easyinput 
                            v-model="edit_tongue_twister_data.text" 
                            type="textarea" 
                            :maxlength="200"
                            placeholder="请输入绕口令内容"
                        />
                    </uni-forms-item>
                </uni-forms>

                <view class="ai-sport-item-edit-buttons flex-all-center font14">
                    <view class="bg-background color-sub" hover-class="navigator-hover"
                          @click="tongueTwisterEditClose">取消
                    </view>
                    <view class="bg-light-primary color-white" hover-class="navigator-hover"
                          @click="tongueTwisterEditConfirm">确定
                    </view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
export default {
    name: "tongue-twister-text-list-set",
    props: {
        set: {
            type: Object,
            default: () => ({
                text_list: [
                    {text: '黑化肥发灰会挥发，灰化肥挥发会发黑。黑化肥挥发发灰会花飞，灰化肥挥发发黑会飞花'}
                ],
                accuracy: 80
            })
        }
    },
    data() {
        return {
            localSet: {
                text_list: [],
                accuracy: 80
            },
            edit_tongue_twister_data: {
                text: ''
            },
            tongue_twister_change_index: null
        }
    },

    mounted() {
        this.localSet = JSON.parse(JSON.stringify(this.set))
    },

    watch: {
        set: {
            handler(newVal) {
                this.localSet = JSON.parse(JSON.stringify(newVal))
            },
            deep: true
        },
        localSet: {
            handler(newVal) {
                this.$emit('update:set', newVal)
            },
            deep: true
        }
    },

    methods: {
        editTongueTwisterItem(index) {
            this.tongue_twister_change_index = index
            let item = {text: ''}
            
            if (index !== null) {
                const list = this.localSet.text_list
                if (list[index]) {
                    item = JSON.parse(JSON.stringify(list[index]))
                }
            }

            this.edit_tongue_twister_data = {
                text: item.text || ''
            }
            
            this.$refs.tongueTwisterEditPopup.open()
        },

        tongueTwisterEditClose() {
            this.edit_tongue_twister_data = {text: ''}
            this.tongue_twister_change_index = null
            this.$refs.tongueTwisterEditPopup.close()
        },

        tongueTwisterEditConfirm() {
            const data = JSON.parse(JSON.stringify(this.edit_tongue_twister_data))
            if (!data.text || !data.text.trim()) {
                return this.$uni.showToast('请输入口令内容')
            }

            const index = this.tongue_twister_change_index
            const text_list = this.localSet.text_list
            
            if (index === null) {
                // 添加新口令
                text_list.push(data)
            } else {
                // 修改现有口令
                this.$set(text_list, index, data)
            }

            this.tongueTwisterEditClose()
        },

        deleteTongueTwisterItem(index) {
            uni.showModal({
                title: '提示',
                content: '确定删除该口令?',
                success: res => {
                    if (res.confirm) {
                        this.localSet.text_list.splice(index, 1)
                        this.$nextTick(() => this.$uni.showToast('口令已删除'))
                    }
                }
            })
        }
    }
}
</script>

<style lang="scss">
.form-item {
    padding: 10px;
}

.form-item .bottom {
    border-bottom: 1px solid #eee;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.tongue-twister-list {
    .tongue-twister-item {
        position: relative;
        background-color: #f8f8f8;
        border-radius: 5px;
        margin: 5px;
        padding: 10px;
        font-size: 14px;
        color: #495060;
    }
}

.item-controls {
    position: absolute;
    right: 0;
    top: 0;
}

.item-info {
    font-size: 14px;
    color: #495060;
}

.ai-sport-item-edit {
    width: 95vw;
    padding: 0 10px;
    border-radius: 10px;
    box-sizing: border-box;

    .ai-sport-item-edit-buttons {
        padding: 10px;
        border-top: 1px solid #eee;

        view {
            margin: 0 10px;
            width: 100px;
            line-height: 40px;
            border-radius: 20px;
            text-align: center;
        }
    }
}
</style>