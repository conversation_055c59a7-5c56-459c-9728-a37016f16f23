<template>
    <view class="form-item">
        <view class="top color-content">健康打卡需要提交的表单信息</view>

        <view class="bottom font16">
            <view class="ptm5">
                <view class="ptm5 must-submit-list" v-for="(item, index) in diy_submit_form" :key="index">
                    <view class="flex-row">
                        <view class="flex-row">
                            <view style="width: 120px;">
                                <uni-easyinput v-model="item.title" placeholder="请输入内容"/>
                            </view>
                            <template>

                                <picker :range="options.rules" range-key="name"
                                        :value="getRulesValue(item.rules)"
                                        @change="mustItemRulesChange($event, index)">
                                    <view class="must-rules-picker">
                                        <text class="color-content font14">
                                            {{ getRulesName(item.rules) }}
                                        </text>
                                        <text class="iconfont icon-more color-sub font14"></text>
                                    </view>
                                </picker>


                                <picker :range="options.types" range-key="name"
                                        :value="getTypesValue(item.types)"
                                        @change="mustItemTypesChange($event, index)">
                                    <view class="must-rules-picker">
                                        <text class="color-content font14">
                                            {{ getTypesName(item.types) }}
                                        </text>
                                        <text class="iconfont icon-more color-sub font14"></text>
                                    </view>
                                </picker>
                            </template>
                        </view>
                        <view class="delete-rules font14 color-error"
                              @click="diy_submit_form.splice(index, 1)">删除
                        </view>
                    </view>

                    <view v-if="item.types === 2" class="pl10">
                        <view class="must-options-item flex-row" v-for="(item_, index_) in item.options"
                              :key="index_">
                            <view class="color-sub delete-rules text-right"
                                  style="width: 20px; padding: 0 5px 0 0;">{{ index_ + 1 }}:
                            </view>
                            <view style="width: 200px;">
                                <uni-easyinput :value="item_.text" placeholder="请输入内容" @input="mustItemOptionsTextChange($event, index, index_)"/>
                            </view>
                            <view class="delete-rules">
                                <text class="color-error font14"
                                      @click="deleteOptionsItem(index, index_)">删除
                                </text>
                            </view>
                        </view>
                        <view class="flex-row">
                            <view class="color-sub font14 ptm5" @click="addOption(index)">+ 添加新选项</view>
                        </view>
                    </view>
                </view>
                <view class="flex-row">
                    <view class="color-primary font14 ptm5" @click="addMust">+ 添加新项</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "sport-check-in-submit-diy-form-set",
    props: ['diySubmitForm'],
    data() {
        return {
            diy_submit_form: [],
            options: {
                rules: [
                    {rules: 0, name: '选填'},
                    {rules: 1, name: '必填'}
                ],
                types: [
                    {types: 1, name: '文本'},
                    {types: 2, name: '单选'},
                    {types: 3, name: '日期'}
                ]
            }
        }
    },
    watch: {
        diy_submit_form: {
            handler(val) {
                this.$emit('update:diySubmitForm', val)
            },
            deep: true
        }
    },

    mounted() {
        this.diy_submit_form = this.diySubmitForm
    },

    methods: {
        getRulesValue(rules) {
            const index = this.options.rules.findIndex(v => v.rules === rules)
            return index === -1 ? 0 : index
        },
        getRulesName(rules) {
            const item = this.options.rules.find(v => v.rules === rules)
            return item?.name || ''
        },
        mustItemRulesChange(e, index) {
            this.diy_submit_form[index].rules = this.options.rules[e.detail.value].rules
        },


        getTypesValue(types) {
            const index = this.options.types.findIndex(v => v.types === types)
            return index === -1 ? 0 : index
        },
        getTypesName(types) {
            const item = this.options.types.find(v => v.types === types)
            return item?.name || ''
        },
        mustItemTypesChange(e, index) {
            const value = Number(e.detail.value) + 1
            this.diy_submit_form[index].types = value
            if (value === 2) this.diy_submit_form[index].options ||= [{text: ''}]
        },

        addMust() {
            this.diy_submit_form.push({
                rules: 0,
                title: '',
                types: 1
            })
        },

        addOption(index) {
            const item = this.diy_submit_form[index]
            item.options.push({text: ''})
            this.$set(this.diy_submit_form, index, item)
        },
        deleteOptionsItem(index, index_) {
            const item = this.diy_submit_form[index]
            if (item.options.length === 1) {
                this.$uni.showToast('请至少保留一个选项')
                return
            }
            item.options.splice(index_, 1)
            this.$set(this.diy_submit_form, index, item)
        },

        mustItemOptionsTextChange(e, index, index_) {
            const item = this.diy_submit_form[index]
            item.options[index_].text = e
            this.$set(this.diy_submit_form, index, item)
        }
    }
}
</script>

<style lang="scss" scoped>
.must-submit-list {
    border-bottom: 1px dashed #eee;
}

.must-rules-picker {
    line-height: 34px;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    margin-left: 5px;
    padding: 0 15px;
}

.delete-rules {
    line-height: 36px;
    padding: 0 10px;
}

.must-options-item {
    padding: 3px 0;
}
</style>