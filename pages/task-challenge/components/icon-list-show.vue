<template>
    <view class="icon-list">
        <view v-for="(item, index) in icon_list" :key="item.name">
            <view v-show="icon_list_show_more || index < icon_list_show_count">
                <view v-if="item.name === 'set'" :class="item.className"
                      @click="$emit('iconItemClick', item.name)">
                    <image v-if="item.image" class="icon-item-image"
                           :src="item.image" mode="aspectFill"/>
                    <text v-else class="iconfont icon-setting"></text>
                </view>

                <view v-if="item.name === 'user_info' && isJoining" :class="item.className"
                      hover-class="navigator-hover" @click="$emit('iconItemClick', item.name)">
                    <image v-if="item.image" class="icon-item-image"
                           :src="item.image" mode="aspectFill"/>
                    <text v-else class="iconfont icon-personal-data"></text>
                </view>

                <view v-if="item.name === 'ranking_list'" :class="item.className"
                      hover-class="navigator-hover" @click="$emit('iconItemClick', item.name)">
                    <image v-if="item.image" class="icon-item-image"
                           :src="item.image" mode="aspectFill"/>
                    <text v-else class="iconfont icon-trophy"></text>
                </view>

                <view v-if="item.name === 'exam'"
                      :class="item.className" hover-class="navigator-hover" 
                      @click="$emit('iconItemClick', item.name)">
                    <image v-if="item.image" class="icon-item-image"
                           :src="item.image" mode="aspectFill"/>
                    <text v-else class="iconfont icon-examination-paper"></text>
                </view>

                <view v-if="item.name === 'sport_comment'"
                      :class="item.className"
                      hover-class="navigator-hover" @click="$emit('iconItemClick', item.name)">
                    <image v-if="item.image" class="icon-item-image"
                           :src="item.image" mode="aspectFill"/>
                    <text v-else class="iconfont icon-wechat-movement"></text>
                </view>

                <view v-if="item.name === 'active_rule'" :class="item.className"
                      hover-class="navigator-hover" @click="$emit('iconItemClick', item.name)">
                    <image v-if="item.image" class="icon-item-image"
                           :src="item.image" mode="aspectFill"/>
                    <text v-else class="iconfont icon-feedback"></text>
                </view>

                <view v-if="item.name === 'shopping'"
                      :class="item.className" hover-class="navigator-hover" 
                      @click="$emit('iconItemClick', item.name)">
                    <image v-if="item.image" class="icon-item-image"
                           :src="item.image" mode="aspectFill"/>
                    <uni-icons color="#ffffff" size="24" v-else type="shop"/>
                </view>

                <view v-if="item.name === 'lottery'"
                      :class="item.className" hover-class="navigator-hover"
                      @click="$emit('iconItemClick', item.name)">
                    <image v-if="item.image" class="icon-item-image"
                           :src="item.image" mode="aspectFill"/>
                    <text v-else class="iconfont icon-prize-draw"></text>
                </view>

                <view v-if="item.name === 'certificate'"
                      :class="item.className" hover-class="navigator-hover"
                      @click="$emit('iconItemClick', item.name)">
                    <image v-if="item.image" class="icon-item-image"
                           :src="item.image" mode="aspectFill"/>
                    <text v-else class="iconfont icon-certificate"></text>
                </view>

                <view v-if="item.name === 'article_reading' && articleReadingShow"
                      :class="item.className" hover-class="navigator-hover"
                      @click="$emit('iconItemClick', item.name)">
                    <image v-if="item.image" class="icon-item-image"
                           :src="item.image" mode="aspectFill"/>
                    <text v-else class="iconfont icon-news"></text>
                </view>

                <view v-if="item.name === 'active_notice' && noticeShow" :class="item.className"
                      hover-class="navigator-hover" @click="$emit('iconItemClick', item.name)">
                    <image v-if="item.image" class="icon-item-image"
                           :src="item.image" mode="aspectFill"/>
                    <text v-else class="iconfont icon-book"></text>
                </view>

                <navigator v-if="item.name === 'user_center'" :class="item.className"
                           url="/pages/user/user">
                    <image v-if="item.image" class="icon-item-image"
                           :src="item.image" mode="aspectFill"/>
                    <uni-icons color="#ffffff" size="24" v-else type="person"/>
                </navigator>

                <view v-if="item.name === 'share'" :class="item.className"
                      hover-class="navigator-hover" @click="$emit('iconItemClick', item.name)">
                    <image v-if="item.image" class="icon-item-image"
                           :src="item.image" mode="aspectFill"/>
                    <text v-else class="iconfont icon-share"></text>
                </view>
            </view>


            <view v-if="moreIconShow && (item.name === 'open_more' || item.name === 'close_more')"
                  class="pt5">
                <view v-if="item.name === 'close_more'" :class="item.className"
                      v-show="icon_list_show_more" @click="icon_list_show_more = false">
                    <image v-if="item.image" class="icon-item-image"
                           :src="item.image" mode="aspectFill"/>
                    <uni-icons color="#ffffff" size="24" v-else type="up"/>
                </view>

                <view v-if="item.name === 'open_more'" :class="item.className"
                      v-show="!icon_list_show_more" @click="icon_list_show_more = true">
                    <image v-if="item.image" class="icon-item-image"
                           :src="item.image" mode="aspectFill"/>
                    <uni-icons color="#ffffff" size="24" v-else type="down"/>
                </view>
            </view>

        </view>
    </view>
</template>

<script>
export default {
    name: "icon-list-show",
    props: {
        iconList: {
            type: Array,
            default: () => []
        },
        isJoining: {
            type: Boolean,
            default: false
        },
        activeSet: {
            type: Object,
            default: () => ({})
        },
        noticeShow: {
            type: Boolean,
            default: false
        }
    },
    
    data() {
        return {
            icon_list_show_more: false,
            icon_list_show_count: 4
        }
    },
    
    computed: {
        moreIconShow() {
            // 需要减2是因为 展开和收缩的图标也在列表里面
            return this.icon_list.length - 2 > this.icon_list_show_count
        },
        
        articleReadingShow() {
            return !!this.activeSet.article_reading?.category_id
        },

        icon_list() {
            const needJoin = ['user_info', 'exam', 'shopping', 'sport_comment']
            return this.iconList.filter(item => {
                if (!this.isJoining) {
                    if (needJoin.includes(item.name)) return false
                }
                return true
            })
        }
    }
}
</script>

<style lang="scss">
.icon-list {
    position: fixed;
    top: 110px;
    right: 3vw;
    display: flex;
    flex-direction: column;

    .icon-item, .icon-item-image-container, .icon-item-image {
        width: 40px;
        height: 40px;
    }

    .icon-item, .icon-item-image-container {
        margin-bottom: 5px;
    }

    .icon-item {
        line-height: 40px;
        border-radius: 50%;
        text-align: center;
        background-color: rgba(0, 0, 0 , .5);

        .iconfont, .uni-icons {
            font-size: 24px !important;
            color: #fff !important;
        }
    }

    .icon-item-image-container {
        .icon-item-image {
            display: block;
        }
    }
}
</style>