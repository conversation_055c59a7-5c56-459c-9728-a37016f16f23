<template>
    <view class="page">
        <view v-if="success" class="save-success bg-white">
            <view class="icon text-center">
                <uni-icons type="checkbox-filled" size="100" color="#19be6b"/>
                <view class="color-content font16">活动{{ form_data.active_id ? '修改' : '创建' }}成功！</view>
            </view>
            <view class="flex-all-center">
                <button class="bg-success color-white" style="width: 200px;" @click="lookMyActivityList">
                    查看我的活动
                </button>
            </view>
        </view>

        <view class="type-list flex-row bg-white">
            <view
                class="type-item color-content"
                v-for="(item, index) in type_list"
                :key="index"
                :class="{'active-type': item.id === type_id}"
                :style="{width: 'calc(100% / ' + type_list.length + ')'}"
                @click="type_id = item.id"
            >{{ item.title }}
            </view>
        </view>

        <add-activity-tips ref="add_activity_tips"/>

        <view class="form">
            <template v-if="type_id === 1">
                <view class="form-item">
                    <view class="top color-content">
                        <text>活动名称</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" v-model="form_data.name" placeholder="请输入活动名称"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">主办方</view>
                    <view class="bottom font16">
                        <input class="input" v-model="form_data.organizer" placeholder="请输入主办方单位名称"/>
                    </view>
                </view>

                <template v-if="!form_data.active_id">
                    <view class="form-item">
                        <view class="top color-content">
                            <text>手机号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="number" v-model="form_data.mobile"
                                   placeholder="请输入真实手机号"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <text>微信号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" v-model="form_data.wechat_num"
                                   placeholder="请输入真实微信号"/>
                        </view>
                    </view>
                </template>

                <view class="form-item">
                    <view class="top color-content">活动开始时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.begin_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">活动结束时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.end_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view v-if="!conf.active.news.news_id" class="form-item">
                    <view class="top color-content">活动说明</view>
                    <view class="bottom font16">
				        <textarea class="textarea" maxlength="-1" auto-height v-model="form_data.content"
                          placeholder="请输入活动说明"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>活动说明文章</view>
                        <view class="font12 color-sub">
                            绑定文章后，活动说明将会显示文章内容，不会显示上面填写的活动说明
                        </view>
                    </view>
                    <view class="bottom font16">
                        <view class="flex-kai" @click="toSelNews">
                            <view class="view">
                                <view v-if="conf.active.news.news_id">
                                    {{ conf.active.news.news_title || conf.active.news.news_id }}
                                </view>
                                <view v-else class="color-sub">选择文章</view>
                            </view>
                            <view class="flex-all-center">
                                <view v-if="conf.active.news.news_id" class="color-sub font12"
                                      style="width: 30px;" @click.stop="deleteNews">解绑
                                </view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>
            </template>


            <template v-if="type_id === 3">
                <view class="form-item">
                    <view class="top color-content">活动参与方式</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.enter_types_list"
                                :value="form_options.enter_types_list.findIndex(v => v.value === conf.active.enter_types)"
                                range-key="title"
                                @change="conf.active.enter_types = form_options.enter_types_list[$event.detail.value].value"
                            >
                                {{ form_options.enter_types_list.find(v => v.value === conf.active.enter_types).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="conf.active.enter_types === 2" class="form-item">
                    <view class="top color-content">
                        <text>活动密码</text>
                        <text v-if="!have_password" class="color-error font16"> *</text>
                        <text class="color-sub font12 pl5">(3-20位)</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" v-model="conf.active.password" maxlength="20"
                               :placeholder="have_password ? '已设置密码，无需修改密码不用填写' : '请输入活动密码'"
                        />
                    </view>
                </view>

                <must-submit-set :must-submit.sync="conf.must_submit"/>

            </template>

            <template v-if="type_id === 2">
                <active-image-set :active-id="form_data.active_id" :rankSet="rank_set"
                                  :logo.sync="form_data.logo"
                                  :screen-pic.sync="conf.active.screen_pic"
                                  :top-rank-banner.sync="conf.active.top_rank_banner"/>

                <view v-if="rank_set.big_screen" class="form-item">
                    <view class="top color-title">PC大屏背景图</view>
                    <view style="padding-top: 5px;">
                        <view class="image-view" v-if="conf.active.vote_big_screen_bg_img">

                            <image class="image-item" mode="aspectFill"
                                   :src="conf.active.vote_big_screen_bg_img"
                                   @click="previewImage([conf.active.vote_big_screen_bg_img])"/>
                            <view class="del-image-item"
                                  @click.stop="conf.active.vote_big_screen_bg_img = ''">
                                <uni-icons type="closeempty" color="#e20f04"/>
                            </view>
                        </view>

                        <view v-else class="add-image text-center"
                              @click="changeImage('vote_big_screen_bg_img')">
                            <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                        </view>
                    </view>
                </view>
            </template>

            <template v-if="type_id === 6">

                <view class="form-item">
                    <view class="top color-content">提交参赛作品备注填写设置</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.memo_required_list"
                                :value="form_options.memo_required_list.findIndex(v => v.value === conf.active.memo_required)"
                                range-key="title"
                                @change="conf.active.memo_required = form_options.memo_required_list[$event.detail.value].value"
                            >
                                {{ form_options.memo_required_list.find(v => v.value === conf.active.memo_required).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">提交参赛作品图片上传设置</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.pic_list_required_list"
                                :value="form_options.pic_list_required_list.findIndex(v => v.value === conf.active.pic_list_required)"
                                range-key="title"
                                @change="conf.active.pic_list_required = form_options.pic_list_required_list[$event.detail.value].value"
                            >
                                {{ form_options.pic_list_required_list.find(v => v.value === conf.active.pic_list_required).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">提交参赛作品视频上传设置</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.video_list_required_list"
                                :value="form_options.video_list_required_list.findIndex(v => v.value === conf.active.video_list_required)"
                                range-key="title"
                                @change="videoListRequiredChange"
                            >
                                {{ form_options.video_list_required_list.find(v => v.value === conf.active.video_list_required).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">作品是否需要审核才能显示在广场</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.not_check_list"
                                :value="form_options.not_check_list.findIndex(v => v.value === conf.active.not_check)"
                                range-key="title"
                                @change="conf.active.not_check = form_options.not_check_list[$event.detail.value].value"
                            >
                                {{ form_options.not_check_list.find(v => v.value === conf.active.not_check).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">是否允许修改已提交的参赛作品</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.records_not_edit_list"
                                :value="form_options.records_not_edit_list.findIndex(v => v.value === conf.active.records_not_edit)"
                                range-key="title"
                                @change="conf.active.records_not_edit = form_options.records_not_edit_list[$event.detail.value].value"
                            >
                                {{ form_options.records_not_edit_list.find(v => v.value === conf.active.records_not_edit).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <text>每人总共可投票总数</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="conf.active.vote_rules.all_num"
                               placeholder="不填则不限制"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <text>每人每天可投票总数</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="conf.active.vote_rules.daily_num"
                               placeholder="不填则不限制"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <text>每人每天可给相同一参赛者投票数</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="conf.active.vote_rules.daily_person"
                               placeholder="不填默认为1次"/>
                    </view>
                </view>

                <view v-if="rank_set.vote_together_submit" class="form-item">
                    <view class="top color-content">
                        <text>投票批量选择</text>
                    </view>
                    <view class="bottom font16 flex-kai">
                        <picker
                            class="view"
                            :range="['关闭', '开启']"
                            :value="conf.active.must_person.open"
                            @change="conf.active.must_person.open = Number($event.detail.value)"
                        >
                            {{ conf.active.must_person.open ? '开启' : '关闭' }}
                        </picker>
                        <view class="flex-all-center">
                            <uni-icons type="forward" color="#80848f"/>
                        </view>
                    </view>
                </view>
                
                <view v-show="conf.active.must_person.open" class="form-item">
                    <view class="top color-content">
                        <text>每次投票必须选择的人数</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="number" v-model="conf.active.must_person.min"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">投票是否需要输入验证码</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                :range="['无需输入验证码', '需要输入验证码']"
                                :value="conf.active.vote_need_verify_code"
                                @change="conf.active.vote_need_verify_code = Number($event.detail.value)"
                            >
                                {{ conf.active.vote_need_verify_code ? '需要输入验证码' : '无需输入验证码' }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <active-share-set :active-id="form_data.active_id" :rank-set="rank_set"
                                  :qrcode-logo.sync="conf.active.qrcode_logo"
                                  :share-image.sync="conf.active.share_image"
                                  :share-title.sync="conf.active.share_title"/>
            </template>
        </view>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view class="login-btn color-white text-center font18 bg-primary" :disabled="loading"
                  @click="save">{{ form_data.active_id ? '保存' : '创建活动' }}
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import base64 from '@/utils/base64.js'
import {pinyin} from 'pinyin-pro'


export default {
    data() {
        return {
            loading: true,
            success: false,
            type_list: [
                {title: '基本信息', id: 1},
                {title: '活动设置', id: 3},
                {title: '扩展设置', id: 6},
                {title: '活动图片', id: 2}
            ],
            type_id: 1,
            form_data: {
                name: '',
                wechat_num: '',
                mobile: '',
                organizer: '',
                content: '',
                begin_time: new Date(utils.getDay(0, true, '/') + ' 00:00:00').getTime(),
                end_time: new Date(utils.getDay(30, true, '/') + ' 23:59:59').getTime(),
                logo: ''
            },
            conf: {
                active: {
                    news: {
                        news_id: '',
                        news_title: ''
                    },
                    enter_types: 1,
                    password: '',
                    screen_pic: '',
                    top_rank_banner: [],
                    memo_required: 2,
                    pic_list_required: 2,
                    video_list_required: 0,
                    records_not_edit: 0,
                    not_check: 0,
                    vote_rules: {
                        daily_num: '',
                        daily_person: 1,
                        all_num: ''
                    },
                    must_person: {
                        open: 0,
                        min: '',
                        max: ''
                    },
                    vote_need_verify_code: 0,
                    vote_big_screen_bg_img: '',

                    share_title: '',
                    share_image: '',
                    qrcode_logo: '',
                },
                must_submit: JSON.parse(JSON.stringify(this.xwy_config.defaultMustSubmit))
            },
            top_rank_banner_max_count: 6,
            rank_set: {},
            pic_list: [],
            have_password: false,
            form_options: {
                enter_types_list: [
                    {title: '自由报名参与活动', value: 1},
                    {title: '需要输入密码才能进入活动', value: 2},
                    {title: '报名需要审核通过才能参与活动', value: 3},
                    {title: '不允许用户报名', value: 0}
                ],
                memo_required_list: [
                    {value: 0, title: '关闭填写备注'},
                    {value: 2, title: '开启填写备注'},
                    {value: 1, title: '必须填写备注'}
                ],
                pic_list_required_list: [
                    {value: 0, title: '关闭上传图片'},
                    {value: 2, title: '开启上传图片'},
                    {value: 1, title: '必须上传图片'}
                ],
                video_list_required_list: [
                    {value: 0, title: '关闭上传视频'},
                    {value: 2, title: '开启上传视频'},
                    {value: 1, title: '必须上传视频'}
                ],
                records_not_edit_list: [
                    {value: 0, title: '是'},
                    {value: 1, title: '否'}
                ],
                not_check_list: [
                    {value: 0, title: '需要审核'},
                    {value: 1, title: '无需审核'}
                ]
            },
            detail_icon_conf: [
                {
                    type: 0,
                    title: '文章详情'
                }
            ],
        }
    },
    onLoad(e) {
        if (e.id) {
            this.form_data.active_id = e.id
        } else {
            this.types = Number(e.types)
            this.$nextTick(() => this.$refs.add_activity_tips.open(this.types))
            this.form_data.logo = this.xwy_config.create_active_default_logo
        }
        uni.setNavigationBarTitle({
            title: e.id ? '修改活动' : `创建${e.name || '活动'}`
        })
        uni.showLoading({
            mask: true
        })


        login.uniLogin(err => {

            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }


            if (e.id) return this.getDetail()

            const evn_version = app.globalData['evn_version']

            if (evn_version === 'develop') {
                this.form_data.mobile = 12345678910
                this.form_data.wechat_num = 12345678910
                this.form_data.organizer = '伟杰测试'
            }

            // 开发版和体验版不检查重复创建活动
            if (evn_version === 'develop' || evn_version === 'trial') {
                uni.hideLoading()
                return false
            }

            this.checkUserCanCreateActive()
        })
    },
    methods: {
        checkUserCanCreateActive() {

            xwy_api.ajax({
                url: 'front.flat.sport_step.admin/check_user_can_create_active',
                data: {
                    access_token: app.globalData['access_token']
                },
                success: res => {
                    console.log('检查是否能创建活动', res)
                    uni.hideLoading()
                    this.loading = false
                    if (!res.status) {
                        uni.showModal({
                            title: '提示',
                            content: res.info || '暂时不能创建活动',
                            showCancel: false,
                            success: () => {
                                uni.navigateBack()
                            }
                        })
                    }
                }
            })

        },

        getDetail() {
            const errModal = content => {
                uni.hideLoading()
                uni.showModal({
                    title: '提示',
                    content: content || '活动获取失败',
                    showCancel: false,
                    success: () => {
                        uni.navigateBack()
                    }
                })
            }

            xwy_api.getActivityDetail(this.form_data.active_id, res => {
                if (res.data?.active_details) {
                    const detail = res.data.active_details

                    this.detailInit(detail)
                } else {
                    errModal(res.info)
                }
            })
        },


        detailInit(data) {
            this.types = data.types
            this.form_data.name = data.name
            this.form_data.begin_time = data.begin_time * 1000
            this.form_data.end_time = data.end_time * 1000
            if (data.organizer) this.form_data.organizer = data.organizer
            if (data.logo) this.form_data.logo = data.logo
            if (data.content) this.form_data.content = data.content

            const conf = data.conf
            if (conf.must_submit?.length) this.conf.must_submit = conf.must_submit

            const active = conf.active

            this.conf.active.enter_types = active.enter_types
            this.conf.active.memo_required = active.memo_required
            this.conf.active.pic_list_required = active.pic_list_required
            this.conf.active.video_list_required = active.video_list_required
            if (active.news) this.conf.active.news = active.news
            if (active.screen_pic) this.conf.active.screen_pic = active.screen_pic
            if (active.top_rank_banner) this.conf.active.top_rank_banner = active.top_rank_banner
            if (active.not_check) this.conf.active.not_check = active.not_check
            if (active.records_not_edit) this.conf.active.records_not_edit = active.records_not_edit
            if (active.password) {
                this.old_password = active.password
                this.have_password = true
            }

            if (active.vote_rules) {
                const {daily_num, daily_person, all_num} = active.vote_rules
                if (daily_num) this.conf.active.vote_rules.daily_num = daily_num
                if (daily_person) this.conf.active.vote_rules.daily_person = daily_person
                if (all_num) this.conf.active.vote_rules.all_num = all_num
            }
            
            if (active.must_person) this.conf.active.must_person = active.must_person

            if (active.vote_need_verify_code) this.conf.active.vote_need_verify_code = active.vote_need_verify_code
            
            if (active.vote_big_screen_bg_img) this.conf.active.vote_big_screen_bg_img = active.vote_big_screen_bg_img

            if (active.qrcode_logo) this.conf.active.qrcode_logo = active.qrcode_logo
            if (active.share_image) this.conf.active.share_image = active.share_image
            if (active.share_title) this.conf.active.share_title = active.share_title

            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace data.rank_set.vote_together_submit */
            /** @namespace data.rank_set.big_screen */
            
            if (data.rank_set) {
                const rank_set = data.rank_set

                this.rank_set = rank_set


                if (rank_set.closed_AD && rank_set.closed_AD === 1) {
                    this.detail_icon_conf.push({
                        type: 1,
                        title: '文章列表'
                    })
                }
            }

            uni.hideLoading()
        },

        videoListRequiredChange(e) {
            const value = this.form_options.video_list_required_list[e.detail.value].value
            if (!this.rank_set['upload_video'] && value !== 0)
                return xwy_api.alert('未开通视频上传功能，请联系客服开通')

            this.conf.active.video_list_required = value
        },

        toSelNews() {
            uni.navigateTo({
                url: '/pages/news/list?type=user&is_sel=true',
                events: {
                    selNews: data => {
                        this.conf.active.news.news_id = data.id
                        this.conf.active.news.news_title = data.title
                    }
                }
            })
        },


        deleteNews() {
            this.conf.active.news.news_id = ''
            this.conf.active.news.news_title = ''
        },

        changeImage(key) {
            if (!this.rank_set?.closed_AD) {
                const options = {
                    screen_pic: '无法设置开屏大图，请联系客服设置',
                    top_rank_banner: '无法设置排行榜轮播图，请联系客服设置'
                }
                if (options[key]) return this.$uni.showModal(options[key])
            }

            let url = '/pages/other/image_upload_or_select'
            if (this.form_data.active_id) url += `?active_id=${this.form_data.active_id}`
            uni.navigateTo({
                url,
                events: {
                    newImg: src => {
                        switch (key) {
                            case 'logo':
                                this.form_data.logo = src
                                break
                            case 'top_rank_banner':
                                this.conf.active.top_rank_banner.push(src)
                                break
                            default:
                                this.conf.active[key] = src
                        }
                    }
                }
            })
        },

        previewImage(urls, current = urls[0]) {
            uni.previewImage({
                urls,
                current
            })
        },


        dataCheck(data) {
            const showToast = title => {
                this.type_id = 1
                this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none')
            }

            if (!data.name) {
                showToast('请输入活动名称')
                return false
            }

            if (!data.active_id) {
                if (!data.mobile) {
                    showToast('请输入手机号')
                    return false
                }
                if (data.mobile.toString().length !== 11) {
                    showToast('手机号长度有误')
                    return false
                }
                if (!data.wechat_num) {
                    showToast('请输入微信号')
                    return false
                }
                const wechat_num = data.wechat_num.toString()
                if (wechat_num.length < 4 || wechat_num.length > 20) {
                    showToast('微信号长度有误')
                    return false
                }
                if (this._utils.isChineseChar(wechat_num)) {
                    showToast('微信号不能输入中文')
                    return false
                }
            }

            return true
        },

        setMustSubmitData() {
            const errModal = content => {
                this.type_id = 3
                uni.showModal({
                    title: '提示',
                    content,
                    showCancel: false
                })
            }


            for (let i = 0; i < this.conf.must_submit.length; i++) {
                const v = this.conf.must_submit[i]
                console.log(v)
                if (!v.title) {
                    errModal('参与活动需要填写的信息选项填写不完整，请检查。')
                    return false
                }
                v.name = pinyin(v.title, {toneType: 'none', type: 'array'}).join('_')
                if (v.types === 2) {
                    if (!v.options || !v.options.length) {
                        errModal(`${v.title} 至少需要添加一个选项。`)
                        return false
                    }
                    console.log(v.options)
                    for (let j = 0; j < v.options.length; j++) {
                        const v1 = v.options[j]
                        if (!v1 || !v1.text) {
                            errModal(`${v.title} 有未填写的选项，请检查。`)
                            return false
                        }
                    }
                }
            }

            return true
        },

        confCheck() {
            const showToast = title => {
                this.$uni.showToast(title, title.length <= 7 ? 'error' : 'none')
                this.type_id = 3
                return false
            }

            const conf = JSON.parse(JSON.stringify(this.conf))
            if (conf.active.enter_types === 2) {
                if (!this.have_password && !conf.active.password) return showToast('请输入活动密码')
                if (conf.active.password && conf.active.password.length < 3) {
                    return showToast('活动密码不得少于3位')
                }
                if (conf.active.password && conf.active.password.length > 20) {
                    return showToast('活动密码不得大于20位')
                }
                conf.active.password = conf.active.password || this.old_password
            } else {
                conf.active.password = 1
            }
            
            if (!this.rank_set.vote_together_submit || !conf.active.must_person.open) delete conf.active.must_person
            
            if (conf.active.must_person?.open) {
                const min = Math.floor(conf.active.must_person.min)
                if (isNaN(min) || min === 0) return showToast('每次投票必须选择的人数填写不正确')
                conf.active.must_person.min = min
                conf.active.must_person.max = min
                // 暂时不考虑设置区间的
                /*if (conf.active.must_person.max) {
                    const max = Math.floor(conf.active.must_person.max)
                    if (isNaN(max) || max === 0) return showToast('每次投票必须选择的人数填写不正确')
                    conf.active.must_person.max = max
                } else {
                    conf.active.must_person.max = min
                }*/
            }

            if (!conf.active.qrcode_logo) delete conf.active.qrcode_logo
            if (!conf.active.share_image) delete conf.active.share_image
            if (!conf.active.share_title) delete conf.active.share_title

            let conf_str = JSON.stringify(conf)
            conf_str = conf_str.replace(/·/g, '-')
            return base64['encode'](conf_str)
        },

        save() {
            if (this.success) return false

            const data = JSON.parse(JSON.stringify(this.form_data))

            if (!this.dataCheck(data)) return false
            if (!this.setMustSubmitData()) return false

            const conf = this.confCheck()
            if (!conf) return false
            data.conf = conf

            data.pic_list = base64['encode'](JSON.stringify(this.pic_list))

            data.begin_time /= 1000
            data.end_time /= 1000

            this.saveAjax(data)
        },

        async saveAjax(data) {
            data.types = this.types

            this.loading = true
            uni.showLoading({
                title: '保存中...',
                mask: app.globalData['evn_version'] !== 'trial'
            })

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.admin/create_active',
                data
            })

            uni.hideLoading()
            this.loading = false

            if (!res || !res.status) {
                xwy_api.alert(res && res.info || '保存失败')
                return false
            }

            this.updatePageData()

            if (this.form_data.active_id) {
                this.$uni.showToast('保存成功', 'success')
                return this.$uni.navigateBack(1, {delay: 1000})
            }

            this.success = true
        },

        // 活动修改/创建成功，如果页面栈有详情或列表页面，更新页面数据
        updatePageData() {
            const pages = getCurrentPages()

            const update_page_list = [
                {
                    route: 'pages/vote/user/detail',
                    _function: page => {
                        page.$vm.getDetail(true)
                    }
                },
                {
                    route: 'pages/vote/admin/manage',
                    _function: page => {
                        page.$vm.getDetail()
                    }
                },
                {
                    route: 'pages/activity/user/index',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getActivityList()
                    }
                },
                {
                    route: 'pages/activity/user/activity_list',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getList()
                    }
                }
            ]

            update_page_list.forEach(v => {
                const page = pages.find(n => n.route === v.route)
                if (page) v._function(page)
            })
        },

        lookMyActivityList() {
            const list_page_route = 'pages/activity/user/activity_list'
            const pages = getCurrentPages()
            const list_page_index = pages.findIndex(v => {
                return v.route === list_page_route && v.options.type && v.options.type === 'create'
            })
            if (list_page_index === -1) return this.$uni.redirectTo(`/${list_page_route}?type=create`)

            this.$uni.navigateBack(pages.length - list_page_index - 1)
        }
    }
}
</script>

<style>
.page {
    padding-top: 40px;
    padding-bottom: 75px;
}

.type-list {
    position: fixed;
    z-index: 9;
    width: 100vw;
    top: 0;
    left: 0;
}

.type-item {
    height: 40px;
    line-height: 40px;
    text-align: center;
    box-sizing: border-box;
}

.active-type {
    color: #007AFF;
    border-bottom: 2px solid #007AFF;
}

.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}


.form-item .textarea {
    width: 100%;
    line-height: 16px;
    padding: 10px 0;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}

.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
    line-height: calc((100vw - 50px) / 3);
    margin: 5px;
}

.image-item {
    width: calc(100vw - 20px);
    height: 50vw;
    border-radius: 5px;
}


.image-view {
    position: relative;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}

.bottom-btn-view {
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.login-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}

.save-success {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 999999;
    box-sizing: border-box;
    padding-top: 10vh;
}

.save-success .icon {
    padding-bottom: 50px;
}

.uni-date-x {
    padding: 0 !important;
}

.uni-date__x-input {
    font-size: 16px !important;
    color: #000;
}

/* #ifdef H5 */

@media screen and (min-width: 500px) {
    .type-list {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }

    .image-item {
        width: 480px;
        height: 200px;
    }
}

/* #endif */
</style>
