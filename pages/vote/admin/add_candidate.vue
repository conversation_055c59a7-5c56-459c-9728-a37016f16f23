<template>
	<view>
		<view class="form">
			<view class="form-item" v-for="(item, index) in must_submit" :key="index">
				<template v-if="item.types === 1">
					<view class="top color-content">
						<text>{{item.title}}</text>
						<text v-if="item.rules === 1" class="color-red pl5">*</text>
					</view>
					<view class="bottom font16">
					    <input class="input" type="text" v-model="item.value" :placeholder="'请输入' + item.title"/>
					</view>
				</template>

				<template v-if="item.types === 2">
					<view class="top color-content">
						<text>{{item.title}}</text>
						<text v-if="item.rules === 1" class="color-red pl5">*</text>
					</view>
					<view class="bottom font16">
					    <view class="flex-kai">
					        <picker
					            class="view"
					            mode="selector"
					            :range="item.options"
                      :value="item.options.findIndex(v => v.text === item.value) === -1 ? 0 : item.options.findIndex(v => v.text === item.value)"
                      range-key="text"
                      @change="optionChange($event, item)"
					        >
								<view v-if="item.value">{{item.value}}</view>
								<view class="color-sub" v-else>请选择{{item.title}}</view>
					        </picker>
					        <view class="flex-all-center">
					            <uni-icons type="forward" color="#80848f"/>
					        </view>
					    </view>
					</view>
				</template>
			</view>

			<view class="form-item">
				<view class="top color-content">候选人头像</view>
				<view class="bottom font16">
					<view class="image-view" v-if="headimg">

						<image class="image-item" :src="headimg" mode="aspectFill" @click="previewImage([headimg])"/>
						<view class="del-image-item" @click.stop="headimg = ''">
							<uni-icons type="closeempty" color="#e20f04"/>
						</view>
					</view>

					<navigator
						v-else
						class="add-image bg-background text-center flex-all-center"
						:url="'/pages/other/image_upload_or_select?key=headimg&active_id=' + active_id"
					>
						<uni-icons type="plusempty" size="48" color="#eeeeee"/>
					</navigator>
				</view>
			</view>
		</view>

		<view class="save flex-all-center">
			<button class="save-btn bg-primary color-white" :disabled="loading" @click="save">{{id ? '修改' : '添加'}}</button>
		</view>

		<view v-if="success_show" class="add-success bg-white">
			<view class="icon text-center">
			    <uni-icons type="checkbox-filled" size="100" color="#19be6b"/>
			    <view class="color-content font16">{{success_info}}</view>
			</view>
			<view class="flex-all-center">
			    <button class="success-btn bg-success color-white font16" @click="success_show = false">继续添加</button>
			</view>
			<view class="flex-all-center p10">

			    <navigator
					class="success-btn bg-success color-white text-center"
					open-type="redirect"
					:url="'/pages/activity/admin/activity_user_list?id=' + active_id">
				查看候选人</navigator>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp()
	import xwy_api from '@/utils/api/xwy_api.js'
	import base64 from '@/utils/base64.js'

	export default {
		data() {
			return {
				id: null,
				active_id: '',
				must_submit: [],
				loading: true,
				success_show: false,
				success_info: '',
				headimg: ''
			}
		},
		onLoad(e) {
			this.active_id = e.active_id
			if (e.id) this.id = e.id
			uni.showLoading()
			this.getActivityDetail()
		},
		methods: {

			optionChange(e, item) {
				item.value = item.options[e.detail.value].text
				this.$forceUpdate()
			},
			async getActivityDetail() {

				let activity_detail = app.globalData.activity_detail

				if (!activity_detail) {
					const res = await xwy_api.request({
						url: 'front.flat.sport_step.active_list/active_details',
						data: {
							access_token: app.globalData.access_token,
							active_id: this.active_id
						}
					})

					activity_detail = res.data.active_details

				}
				console.log(activity_detail)
				const must_submit = activity_detail.conf?.must_submit
				if (must_submit) {
					must_submit.forEach(v => v.value = '')
					this.must_submit = must_submit
				}

				if (this.id) this.getDetails()

				this.loading = false
				uni.hideLoading()
			},


			getDetails() {
				const details = app.globalData.tempData?.candidateInfo
				if (!details) {
					xwy_api.alert('详情获取失败', {
						success: () => {
							uni.navigateBack()
						}
					})
					return
				}

				this.headimg = details.headimg || ''
				if (details.must_submit?.length) {
					this.must_submit.forEach(v => {
						details.must_submit.forEach(u => {
							if (v.name === u.name) v.value = u.value
						})
					})
				}
			},

			previewImage(urls) {
				uni.previewImage({
					urls
				})
			},


			check() {
				const list = this.must_submit
				for (let i = 0; i < list.length; i++) {
					const item = list[i]
					console.log(item.value)
					if (item.rules === 1 && (item.value === '' || item.value === undefined)) {
						let opt = '输入'
						if (item.types === 2) opt = '选择'
						xwy_api.alert(`请${opt}${item.title}`)
						return false
					}
				}

				return true
			},

			async save() {
				if (!this.check()) return false


				this.loading = true
				uni.showLoading({
					title: `正在${this.id ? '修改' : '添加'}...`,
					mask: true
				})
				let must_submit_str = JSON.stringify(this.must_submit)
				must_submit_str = must_submit_str.replace(/·/g, '-')
				const data = {
					access_token: app.globalData.access_token,
					active_id: this.active_id,
					must_submit: base64.encode(must_submit_str)
				}
				if (this.headimg) data.headimg = this.headimg
				if (this.id) data.id = this.id
				const res = await xwy_api.request({
					url: 'front.flat.sport_step.user/active_admin_add_user_details',
					data
				})

				this.loading = false
				uni.hideLoading()

				if (!res || !res.status) {
					xwy_api.alert(res && res.info || '失败')
					return false
				}

				if (this.id) {
					const eventChannel = this.getOpenerEventChannel()
					eventChannel && eventChannel.emit && eventChannel.emit('updateList')
					return uni.navigateBack()
				}

				this.success_info = res.info
				this.success_show = true

                this.must_submit.forEach(item => {
                    item.value = ''
                })
                this.headimg = ''
			}
		}
	}
</script>

<style>
.form {
    padding: 10px 0;
	padding-bottom: 70px;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}




.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}

.image-item, .image-view, .add-image {
	width: 120px;
	height: 120px;
}

.add-image {
	margin: 5px 0;
	border-radius: 5px;
}

.image-item {
	border-radius: 5px;
}

.image-view {
	position: relative;
	display: inline-block;
}

.del-image-item {
	position: absolute;
	right: 10px;
	top: 8px;
	width: 30px;
	height: 30px;
	line-height: 30px;
	text-align: center;
	background-color: rgba(0, 0, 0, .5);
	border-radius: 50%;
}

.save {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	padding: 10px 10px 15px 10px;
	border-top: 1px solid #eee;
}
.save-btn {
	width: 250px;
	line-height: 40px;
	border-radius: 20px;
}
.save-btn::after, .success-btn::after{
	border: none;
}

.add-success {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	z-index: 999999;
	box-sizing: border-box;
	padding-top: 10vh;
}
.add-success .icon {
    padding-bottom: 50px;
}
.success-btn {
	width: 200px;
	height: 40px;
	line-height: 40px;
	border-radius: 20px;
}

</style>
