<template>
    <view class="page bg-background">
        <template v-if="is_admin && !user_record_id">
            <view class="type-list flex-row bg-white">
                <view
                    class="type-item color-content"
                    v-for="(item, index) in checked_list"
                    :key="index"
                    :class="{'active-type': item.checked === checked}"
                    :style="{width: 'calc(100% / ' + checked_list.length + ')'}"
                    @click="checkedChange(item.checked)"
                >
                    {{ item.title }}
                </view>
            </view>

            <view style="height: 40px;"></view>
        </template>


        <view v-if="list_total" class="text-center p10 color-sub font14">共{{ list_total }}个作品</view>

        <xwy-ad :activity_id="id" :ad_type="3"></xwy-ad>

        <view class="list">
            <template v-for="(item, index) in list">
                <view :key="index">
                    <view v-if="index === 0" style="padding-bottom: 10px;">
                        <xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>
                    </view>
                    <view class="item bg-white" @click="lookWorksDetail(item)">
                        <view class="flex-row">
                            <view v-if="is_admin" class="flex-all-center" @click.stop="checkChange(index)">
                                <radio :checked="item.check" style="transform:scale(0.8)"/>
                            </view>
                            <view class="pr10">
                                <image
                                    class="headimg"
                                    :src="item['user_attend_details'] && item['user_attend_details'].headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"
                                    mode="aspectFill"
                                />
                            </view>
                            <view style="width: 100%;">
                                <view
                                    v-if="item['user_attend_details'] && item['user_attend_details'].must_submit && item['user_attend_details'].must_submit.length"
                                    class="color-title"
                                >{{ item['user_attend_details'].must_submit[0].value }}</view>
                                <view v-if="item.sort_num" class="color-content pt5">编号: {{ item.sort_num }}</view>
                                <view v-if="item.memo" class="color-content pt5">{{ item.memo }}</view>


                                <view
                                    v-if="item.img_video_count"
                                    class="img-list flex-row flex-wrap pt5"
                                    :class="{
                                        'img-list-1': item.img_video_count === 1,
                                        'img-list-2': item.img_video_count === 2,
                                        'img-list-more': item.img_video_count >= 3
                                    }"
                                >
                                    <image
                                        class="img-item"
                                        lazy-load="true"
                                        v-for="img_item in item.conf_json.pic_list"
                                        :src="img_item"
                                        :mode="item.img_video_count === 1 ? 'heightFix' : 'aspectFill'"
                                        @click.stop="previewImage(img_item, item.conf_json.pic_list)"
                                    />

                                    <video
                                        class="img-item"
                                        :id="'video_' + item.id + '_' + video_index"
                                        v-for="(video_item, video_index) in item.conf_json.video_list"
                                        :src="video_item"
                                        @play="videoPlay('video_' + item.id + '_' + video_index)"
                                    ></video>
                                </view>


                                <view v-if="!item.checked" class="pt5">
                                    <uni-tag text="待审核" type="default" size="mini" :inverted="true"/>
                                </view>

                                <view class="flex-kai">
                                    <view class="color-sub font14 pt5">{{ item.create_time }}</view>
                                    <view @click.stop="like(index)">
                                        <text
                                            class="iconfont icon-love color-red"
                                            style="position: relative; top: 1px; left: 2px;"
                                        ></text>
                                        <text class="pl5 color-sub font14">{{ item.vote_num || 0 }}</text>
                                    </view>
                                </view>
                            </view>
                        </view>

                        <view v-if="is_myself" class="item-bar flex-kai">
                            <view></view>
                            <view class="flex-row">
                                <view
                                    class="font14 color-primary pl10 pr10"
                                    hover-class="navigator-hover"
                                    @click.stop="toEdit(item)"
                                >修改</view>
                            </view>
                        </view>

                        <view v-if="is_admin" class="item-bar flex-kai">
                            <view></view>
                            <view class="flex-row">
                                <view v-if="user_record_id" class="flex-row">
                                    <view
                                        class="font14 color-content pl10 pr10"
                                        hover-class="navigator-hover"
                                        @click.stop="toEdit(item)"
                                    >作品修改</view>
                                </view>
                                <view
                                    v-if="!user_record_id"
                                    class="font14 color-content pl10 pr10"
                                    hover-class="navigator-hover"
                                    @click.stop="changeNum(item)"
                                >修改编号</view>
                                <view
                                    class="font14 color-content pl10 pr10"
                                    hover-class="navigator-hover"
                                    @click.stop="changeChecked(item)"
                                >
                                    改为{{ item.checked ? '待' : '已' }}审核
                                </view>
                                <view
                                    class="font14 color-content pl10"
                                    hover-class="navigator-hover"
                                    @click.stop="delItem(item.id)"
                                >删除</view>
                            </view>
                        </view>
                    </view>

                    <view v-if="index !== 0 && ((index + 1) % 10 === 0)" style="padding-bottom: 20px;">
                        <xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>
                    </view>
                </view>
            </template>
        </view>


        <uni-load-more v-if="loading && load_page !== 1" status="loading"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && list.length > 5"
            status="noMore"
            :contentText="{contentnomore: '我是有底线的'}"
        ></uni-load-more>
        <uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>

        <view v-if="!loading && !list.length" class="text-center" style="padding-top: 15vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub font14">暂无作品</view>
        </view>


        <template v-if="is_admin && list.length">
            <view style="height: 70px;"></view>
            <view class="bottom-bar flex-kai bg-white">
                <view class="flex-all-center" @click="checkAllChange">
                    <radio :checked="check_all" style="transform:scale(0.8)"/>
                    <text class="font14 color-sub" style="position: relative; left: -4px; top: 1px;">全选</text>
                </view>
                <view class="flex-row">
                    <view
                        class="all-change color-content font14"
                        hover-class="navigator-hover"
                        @click="changeCheckeds"
                    >修改状态</view>
                    <view style="width: 10px;"></view>
                    <view
                        class="all-change color-content font14"
                        hover-class="navigator-hover"
                        @click="dels"
                    >删除</view>
                </view>
            </view>
        </template>

        <uni-popup ref="like_popup" type="center">
            <view class="like_popup text-center bg-white">
                <view class="popup-close" @click="uniPopupClose('like_popup')">
                    <uni-icons type="close" size="24" color="#b2b3b7"/>
                </view>
                <view class="bg-primary color-white" style="padding: 30px 20px 20px;">
                    <icon :type="like_status === 'success' ? 'success' : 'clear'" size="80" color="#ffffff"></icon>
                    <view class="font18">投票{{ like_status === 'success' ? '成功' : '失败' }}</view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14" style="padding-bottom: 20px;">
                        {{ like_info }}
                    </view>
                </view>
                <xwy-ad v-if="popup_show" :activity_id="id" :ad_type="3"></xwy-ad>
            </view>
            <view class="pt5">
                <xwy-ad v-if="popup_show" :activity_id="id" :ad_type="66"></xwy-ad>
            </view>
        </uni-popup>
        
        <verify-code-input-popup v-if="vote_need_verify_code" ref="verify-code-input-popup"/>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import base64 from '@/utils/base64.js'


export default {
    data() {
        return {
            id: '',
            is_admin: 0,
            is_myself: 0,
            loading: true,
            checked: 1,
            list: [],
            list_total: 0,
            load_page: 1,
            is_last_page: false,
            checked_list: [
                {checked: 0, title: '所有记录'},
                {checked: 2, title: '待审核'},
                {checked: 1, title: '已审核'},
            ],
            check_all: false,
            popup_show: false,
            like_status: '',
            like_info: '',
            user_record_id: '',
            vote_need_verify_code: false
        }
    },

    onLoad(e) {

        uni.showLoading({
            title: '加载中...',
        })

        this.id = e.id
        if (e.user_record_id) this.user_record_id = e.user_record_id

        if (e.is_admin) {
            uni.setNavigationBarTitle({
                title: e.user_record_id ? '修改作品' : '作品审核'
            })
            this.is_admin = 1
            this.checked = e.user_record_id ? 1 : 2
        } else if (e.is_myself) {
            uni.setNavigationBarTitle({
                title: '我的作品'
            })
            this.is_myself = 1
            this.checked = 0
        } else {
            uni.setNavigationBarTitle({
                title: '作品动态'
            })
            this.checked = 1
        }

        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.init()
        })

    },

    onReachBottom() {
        !this.loading && !this.is_last_page && this.getList()
    },


    methods: {
        async init() {
            await this.getActiveDetails()
            await this.getList()  
        },

        async getActiveDetails() {
            let active_details = app.globalData['activity_detail']
            if (!active_details || active_details.active_id !== this.id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {active_id: this.id}
                })
                active_details = res?.data?.active_details
            }

            if (!active_details) return
            if (active_details.conf?.active?.vote_need_verify_code) this.vote_need_verify_code = true
        },

        videoPlay(id) {
            this.videoContext = this.videoContext || null
            if (this.previous_video_id) {
                if (id === this.previous_video_id) return false
                this.videoContext = uni.createVideoContext(this.previous_video_id)
                this.videoContext.pause()
            }
            this.previous_video_id = id
        },


        previewImage(src, list) {
            list = list || [src]
            uni.previewImage({
                urls: list,
                current: src
            })
        },


        checkedChange(checked) {
            if (checked === this.checked) return false
            this.checked = checked
            this.load_page = 1
            this.getList()
        },


        async getList() {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
            }

            this.loading = true

            if (this.user_record_id) {
                await this.getAdminAddUserWorksList()
                return false
            }

            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id,
                page: this.load_page,
                perpage: 10
            }

            if (this.is_myself) data.my_self = 1
            if (this.is_admin) data.is_admin = 1
            if (this.checked) data.checked = this.checked


            const res = await xwy_api.request({
                url: 'front.flat.sport_step.audio_online.user_submit/audio_list',
                data
            })


            const data_ = res?.['data']?.audio_list
            if (!data_) this.is_last_page = true
            this.list_total = data_.total
            this.is_last_page = data_.is_lastpage
            const list = data_.data || []

            this.listDataInit(list)
        },


        // 管理员给手动添加的候选人发布的作品
        async getAdminAddUserWorksList() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.audio_online.user_submit/audio_user_attend_submit_details',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id,
                    attend_id: this.user_record_id
                }
            })

            this.is_last_page = true
            const list = res?.['data']?.['user_audio_details']?.['submit_audio_details'] || []
            this.list_total = list.length
            this.listDataInit(list)
        },

        listDataInit(list) {
            if (list.length) {
                if (this.is_admin) this.check_all = false
                list.forEach(v => {
                    if (this.is_admin) v.check = false

                    v.img_video_count = 0
                    if (v.conf_json) {
                        if (v.conf_json.pic_list && v.conf_json.pic_list.length) {
                            v.img_video_count += v.conf_json.pic_list.length
                        }
                        if (v.conf_json.video_list && v.conf_json.video_list.length) {
                            v.img_video_count += v.conf_json.video_list.length
                        }
                    }
                })
            }

            this.loading = false
            uni.hideLoading()

            this.load_page++

            this.list = [...this.list, ...list]
        },


        toEdit(item) {
            let url = `/pages/vote/user/submit?active_id=${this.id}&id=${item.id}`
            if (this.is_admin) url += `&is_admin=1`
            uni.navigateTo({
                url
            })
        },

        lookWorksDetail(item) {
            uni.navigateTo({
                url: './works_detail?id=' + item.id,
                events: {
                    addLike: vote_num => {
                        item.vote_num = vote_num
                    }
                }
            })
        },

        checkChange(index) {
            const item = this.list[index]
            item.check = !item.check
        },

        checkAllChange() {
            const check_all = !this.check_all
            this.check_all = check_all
            this.list.forEach(v => {
                v.check = check_all
            })
        },

        changeNum(item) {
            uni.showModal({
                title: '修改编号',
                content: item.sort_num ? item.sort_num.toString() : '',
                editable: true,
                placeholderText: '请输入作品编号，编号越小展示越靠前',
                success: res => {
                    if (res.confirm) {
                        const errToast = title => {
                            uni.showToast({
                                title,
                                icon: 'none'
                            })
                            this.changeNum(item)
                        }
                        const sort_num = parseInt(res.content)
                        if (isNaN(sort_num)) {
                            errToast('请输入数字')
                            return
                        }
                        if (sort_num < 1) {
                            errToast('编号不能小于1')
                            return
                        }
                        if (sort_num > 99999) {
                            errToast('编号不能大于99999')
                            return
                        }
                        if (item.sort_num && Math.floor(item.sort_num) === sort_num) {
                            errToast('编号相同, 无需修改')
                            return
                        }

                        uni.showModal({
                            title: '修改编号',
                            content: `确定将作品的编号${item.sort_num ? `从${item.sort_num}` : ''}改为${sort_num}?`,
                            success: res => {
                                if (res.confirm) this.changeNumAjax(item, sort_num)
                            }
                        })
                    }
                }
            })
        },

        async changeNumAjax(item, sort_num) {
            const data = {
                active_id: item.active_id,
                id: item.id,
                memo: item.memo,
                conf_json: base64['encode'](JSON.stringify(item.conf_json)),
                sort_num
            }
            if (item.attend_id && !item.userid) {
                data.attend_id = item.attend_id
                data.is_admin = 1
            }
            if (this.is_admin) data.is_admin = 1
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.audio_online.user_submit/submit_myself_audio',
                data
            })

            if (res?.['status'] && res['status'] === 1) {
                item.sort_num = sort_num
                this.$forceUpdate()
                uni.showToast({
                    title: '编号修改成功',
                    icon: 'success'
                })
                return
            }

            xwy_api.alert(res?.['info'] || '编号修改失败')
        },

        changeChecked(item) {
            uni.showModal({
                title: '提示',
                content: `确定将该记录的状态改为${item.checked ? '待' : '已'}审核?`,
                success: res => {
                    if (res.confirm) this.changeCheckedAjax([item.id], item.checked ? '2' : '1')
                }
            })
        },

        getCheckList() {
            const ids = []
            this.list.forEach(v => {
                if (v.check) ids.push(v.id)
            })
            if (!ids.length) {
                uni.showToast({
                    title: '未勾选记录',
                    icon: 'error'
                })
                return false
            }

            return ids
        },

        changeCheckeds() {
            const ids = this.getCheckList()
            if (!ids) return false

            const checked_list = [
                {checked: 1, title: '已审核'},
                {checked: 2, title: '待审核'}
            ]
            const itemList = []
            checked_list.forEach(v => {
                itemList.push(v.title)
            })

            uni.showActionSheet({
                title: '将记录状态修改为',
                itemList,
                success: res => {
                    const index = res.tapIndex
                    uni.showModal({
                        title: '提示',
                        content: `确定将选中的${ids.length}条记录状态修改为${checked_list[index].title}?`,
                        success: res => {
                            if (res.confirm) this.changeCheckedAjax([ids], checked_list[index].checked)
                        }
                    })
                }
            })
        },


        delItem(id) {
            uni.showModal({
                title: '提示',
                content: '确定删除该记录?',
                success: res => {
                    if (res.confirm) this.changeCheckedAjax([id], 'del')
                }
            })
        },

        dels() {
            const ids = this.getCheckList()
            if (!ids) return false

            uni.showModal({
                title: '提示',
                content: `确定删除选中的${ids.length}条记录?`,
                success: res => {
                    if (res.confirm) this.changeCheckedAjax([ids], 'del')
                }
            })
        },

        async changeCheckedAjax(id_list, act_types) {
            const act_text = act_types === 'del' ? '删除' : '修改'
            uni.showLoading({
                title: act_text + '中...',
                mask: true
            })

            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id,
                ids: id_list.join(',')
            }
            if (act_types === 'del') {
                data.act_types = 1
            } else {
                data.checked = act_types
            }

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.audio_online.admin_manage/admin_checked_user_audio',
                data
            })

            uni.hideLoading()

            if (!res || !res['status']) {
                xwy_api.alert(res && res['info'] || `${act_text}失败`)
                return false
            }

            const info = res['info'] || `${act_text}成功`
            uni.showToast({
                title: info,
                icon: info.length <= 7 ? 'error' : 'none'
            })

            this.load_page = 1
            await this.getList()
        },

        like(index) {
            const item = this.list[index]
            if (!item.checked) return this.$uni.showModal('记录未审核通过，无法投票。')
            if (!this.vote_need_verify_code) return this.likeSubmit(item)
            this.$refs['verify-code-input-popup'].show(codeData => this.likeSubmit(item, codeData))
        },
        
        async likeSubmit(item, codeData = null) {
            const data = {
                id: item.id,
                active_id: this.id,
                act_types: 2,
                ...(codeData && { ...codeData })
            }

            this.$uni.showLoading('投票中...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/friend_agree_vote_record',
                data
            })
            uni.hideLoading()

            let like_status = 'success',
                like_info = '给好友投票成功！'

            if (res && res['status']) {
                item.vote_num = item.vote_num || 0
                item.vote_num++
            } else {
                like_status = 'error'
                like_info = '给好友投票失败！'
            }
            if (res && res['info']) like_info = res['info']
            this.like_status = like_status
            this.like_info = like_info

            this.uniPopupOpen('like_popup')
        },
        
        

        uniPopupOpen(ref) {
            this.$refs[ref].open()
            this.popup_show = true
        },

        uniPopupClose(ref) {
            this.$refs[ref].close()
            this.popup_show = false
        },

    }
}
</script>

<style>
.page {
    min-height: 100vh;
    box-sizing: border-box;
}


.type-list {
    position: fixed;
    z-index: 9;
    width: 100vw;
    top: 0;
    left: 0;
}

.type-item {
    height: 40px;
    line-height: 40px;
    text-align: center;
    box-sizing: border-box;
}

.active-type {
    color: #007AFF;
    border-bottom: 2px solid #007AFF;
}


.list {
    padding: 0 10px 10px;
}

.item {
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.headimg {
    width: 40px;
    min-width: 40px;
    height: 40px;
    display: block;
    border-radius: 50%;
}

.img-list {
    position: relative;
    left: -5px;
    padding-top: 5px;
}

.img-item {
    display: block;
    margin-left: 5px;
    margin-bottom: 5px;
    border-radius: 5px;
}

.img-list-1 .img-item {
    height: 120px;
    width: auto;
    max-width: calc(100vw - 130px);
}

.img-list-1 video {
    width: 180px !important;
}

.img-list-2 .img-item {
    width: 100px;
    height: 120px;
}

.img-list-more .img-item {
    width: calc((100vw - 150px) / 3);
    height: calc((100vw - 150px) / 3);
}

.item-bar {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #eee;
}

.bottom-bar {
    position: fixed;
    left: 0;
    width: 100%;
    z-index: 9;
    border-top: 1px solid #eee;
    bottom: 0;
    padding: 5px 10px 15px;
    height: 61px;
    line-height: 40px;
    box-sizing: border-box;
}

.all-change {
    line-height: 38px;
    border-radius: 20px;
    padding: 0 15px;
    border: 1px solid #eee;
    text-align: center;
}

.like_popup {
    width: 300px;
    padding: 0;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    right: 0;
    top: 0;
}
</style>
