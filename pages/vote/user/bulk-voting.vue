<template>
    <view class="page bg-background">
        <view class="top-tips bg-white color-sub text-center font14">
            请勾选{{ must_person_count }}个作品投票
        </view>
        <view class="works-list flex-row flex-wrap pl5 pr5">
            <template v-for="(item, index) in list">
                <view :key="index" class="works-item bg-white" @click="lookWorksDetail(item)">
                    <view class="works-item-image bg-divider">
                        <image
                            v-if="item.conf_json && item.conf_json.pic_list && item.conf_json.pic_list.length"
                            class="works-item-image"
                            :src="item.conf_json.pic_list[0]"
                            mode="aspectFill"
                        />
                        <view v-else class="works-item-image flex-all-center" style="display: flex;">
                            <uni-icons type="image" size="40" color="#bfc5d9"/>
                        </view>
                    </view>
                    <view class="works-item-user-detail">
                        <view class="text-center color-title pt5">
                            <text class="font14 color-content" v-if="item.sort_num"
                                  style="padding-right: 5px;">{{ item.sort_num }}号
                            </text>
                            <text>{{ item['user_attend_details'].must_submit[0].value }}</text>
                        </view>
                    </view>
                    <view class="flex-kai p10" @click.stop="">
                        <view class="color-content font14">{{ item.vote_num }}票</view>
                        <view @click.stop="check(index)">
                            <radio :checked="item.checked" :disabled="enough && !item.checked"/>
                        </view>
                    </view>
                </view>
                <view v-if="index !== 0 && ((index + 1) % 6 === 0)" style="width: 100%;">
                    <xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>
                </view>
            </template>
        </view>

        <view v-if="loading" class="text-center">
            <view v-if="current_page === 1" style="width: 100%; height: 25vh;"></view>
            <load-ani/>
            <view class="color-sub font14">加载中</view>
        </view>
        
        
        <view class="bottom-bar flex-kai bg-white">
            <view class="color-content">已选 {{ checkIds.length }}/{{ must_person_count }}</view>
            <view class="vote-button color-white" :class="enough ? 'bg-light-primary' : 'bg-disabled'"
                  @click="vote">投票
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
export default {
    data() {
        return {
            id: '',
            loading: true,
            list: [],
            current_page: 1,
            is_last_page: false,
            must_person_count: 0
        }
    },
    
    computed: {
        checkIds() {
            return this.list.filter(item => item.checked).map(item => item.id)
        },

        enough() {
            return this.checkIds.length === this.must_person_count
        }
    },

    onLoad(params) {
        this.id = params.id

        this.init()
    },

    onReachBottom() {
        if (!this.loading && !this.is_last_page) {
            this.current_page++
            this.getList()
        }
    },

    methods: {
        async init() {
            await this.getActiveDetails()
            await this.reloadList()
            this.loading = false
        },

        async getActiveDetails() {
            let activity_detail = app.globalData.activity_detail

            if (!activity_detail || activity_detail.active_id !== this.id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.id
                    }
                })


                activity_detail = res?.data?.active_details
            }

            const must_person_count = activity_detail?.conf?.active?.must_person?.min || 0
            if (!must_person_count) return this.$uni.showModal('活动配置获取出错', {
                success: () => this.$uni.navigateBack()
            })
            this.must_person_count = must_person_count
        },

        async reloadList() {
            this.current_page = 1
            await this.getList()
        },

        async getList() {
            if (this.current_page === 1) {
                this.list = []
                this.is_last_page = false
            }
            const data = {
                active_id: this.id,
                checked: 1,
                page: this.current_page,
                perpage: 20
            }

            this.loading = true

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.audio_online.user_submit/audio_list',
                data
            })

            this.loading = false


            if (res?.['data']?.audio_list) {
                const data_ = res.data.audio_list
                this.list_total = data_.total
                this.is_last_page = data_.is_lastpage
                const list = data_.data || []

                list.forEach(item => {
                    item.checked = false
                })

                this.list = [...this.list, ...list]
            } else {
                this.is_last_page = true
            }
        },

        check(index) {
            const item = this.list[index]
            const checked = item.checked
            if (this.enough && !checked) {
                return this.$uni.showToast(`最多只能选择${this.must_person_count}个作品`)
            }
            item.checked = !checked
            this.$set(this.list, index, item)
        },

        async vote() {
            if (!this.enough) return this.$uni.showToast(`请选择${this.must_person_count}个作品`)
            
            this.$uni.showLoading('投票中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.vote.voteList/vote_together_submit',
                data: {
                    active_id: this.id,
                    ids: this.checkIds.join(',')
                }
            })
            uni.hideLoading()
            
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '投票失败')
            
            this.$uni.showToast('投票完成', 'success')
            this.$uni.navigateBack(1, {delay: 1000})
        },

        lookWorksDetail(item) {
            this.$uni.navigateTo(`./works_detail?id=${item.id}&not_vote=1`)
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-top: 40px;
    padding-bottom: 65px;
}

.top-tips, .bottom-bar {
    position: fixed;
    left: 0;
    z-index: 99;
}

.top-tips {
    top: 0;
    width: 100%;
    height: 40px;
    line-height: 40px;
}

.bottom-bar {
    bottom: 0;
    width: 100%;
    padding: 10px 10px 15px;
    line-height: 40px;
    box-sizing: border-box;
    
    .vote-button {
        width: 200px;
        height: 40px;
        border-radius: 20px;
        text-align: center;
    }
}

.works-list {
    padding-top: 1px;

    .works-item {
        width: calc((100% - 20px) / 2);
        margin: 10px 5px;
        border-radius: 10px;
        overflow: hidden;

        .works-item-image {
            width: 100%;
            height: 200px;
            display: block;
        }
    }
}
</style>