<template>
    <view class="page bg-background" v-if="detail">
        <xwy-ad :activity_id="detail.active_id" :ad_type="3"></xwy-ad>

        <view class="flex-kai" style="padding-top: 20px;">
            <view></view>
            <view class="look-rank" hover-class="navigator-hover" @click="toTopList">排行榜</view>
        </view>
        <view
            class="user-info bg-white"
            :class="{'have-headimg': detail['user_attend_details'] && detail['user_attend_details'].headimg}"
        >
            <template v-if="detail['user_attend_details']">
                <view v-if="detail['user_attend_details'].headimg" class="headimg">
                    <image mode="aspectFill" :src="detail['user_attend_details'].headimg"/>
                </view>


                <view class="sort-num" v-if="detail.sort_num">
                    <text class="color-sub font12" style="padding-right: 2px;">编号:</text>
                    <text class="color-primary font14">{{ detail.sort_num }}</text>
                </view>

                <view
                    class="user-name color-title"
                    v-if="detail['user_attend_details'].must_submit && detail['user_attend_details'].must_submit[0] && detail['user_attend_details'].must_submit[0].value"
                >{{ detail['user_attend_details'].must_submit[0].value }}</view>

                <view class="infos flex-center text-center">
                    <view class="info-item" v-if="detail.my_num" hover-class="navigator-hover" @click="toTopList">
                        <view class="color-primary font18">{{ detail.my_num }}</view>
                        <view class="color-content font14">排名</view>
                    </view>
                    <view class="info-item">
                        <view class="color-primary font18">{{ detail.vote_num || 0 }}</view>
                        <view class="color-content font14">票数</view>
                    </view>
                    <view class="info-item" v-if="detail.my_num !== 1 && detail.prev_num">
                        <view class="color-primary font18">{{ detail.prev_num }}</view>
                        <view class="color-content font14">距前一名</view>
                    </view>
                </view>
            </template>
        </view>


        <view v-if="!popup_open" class="pt10">
            <xwy-ad :activity_id="detail.active_id" :ad_type="4"></xwy-ad>
        </view>

        <!--鲜繁客户定制开发活动，视频需要显示在这里-->
        <template v-if="detail.active_id === 'e7516c12aac9d3700df8bbcb2d85b38d'">
            <view v-if="detail.conf_json && detail.conf_json.video_list && detail.conf_json.video_list.length"
                  class="p10">
                <video v-for="(item, index) in detail.conf_json.video_list" :key="index" class="w-100"
                       :src="item"/>
            </view>
        </template>

        <view v-if="detail.memo || memo_article" class="memo bg-white p10">
            <view class="text-center">· 参赛介绍 ·</view>
            <view class="color-content pt5">
                <u-parse v-if="memo_article" :content="memo_article"/>
                <text v-else space="nbsp">{{ detail.memo }}</text>
            </view>
        </view>

        <!--鲜繁客户定制开发活动，视频需要显示在最上方，所以这里不能显示视频-->
        <template v-if="detail.active_id !== 'e7516c12aac9d3700df8bbcb2d85b38d'">
            <view v-if="detail.conf_json && detail.conf_json.video_list && detail.conf_json.video_list.length"
                  class="p10">
                <video v-for="(item, index) in detail.conf_json.video_list" :key="index"
                       class="w-100" :src="item"/>
            </view>
        </template>


        <!--鲜繁客户定制开发活动，作品详情不显示图片，因为作品有视频。但是作品其实是有传图片的，因为列表那里需要有图片的预览图-->
        <template v-if="detail.active_id !== 'e7516c12aac9d3700df8bbcb2d85b38d'">
            <view v-if="detail.conf_json && detail.conf_json.pic_list && detail.conf_json.pic_list.length"
                  class="img-list p10">
                <image class="img-item" v-for="(item, index) in detail.conf_json.pic_list" :key="index"
                       :src="item" mode="widthFix" @click="previewImage(item)"/>
            </view>
        </template>

        <view v-if="!not_vote" class="bottom-bar flex-kai color-white text-center">
            <view
                class="like-btn-mimi bg-primary  flex-all-center"
                hover-class="navigator-hover"
                @click="backActivity"
            >
                <view>
                    <text class="iconfont icon-go-back color-white"></text>
                    <view class="font12">返回</view>
                </view>
            </view>
            <view
                class="like-btn bg-primary"
                hover-class="navigator-hover"
                @click="like"
            >立即投票</view>
            <view
                class="like-btn-mimi bg-primary flex-all-center"
                hover-class="navigator-hover"
                @click="showActiveSharePopup"
            >
                <view>
                    <text class="iconfont icon-share color-white"></text>
                    <view class="font12">分享</view>
                </view>
            </view>
        </view>

        <uni-popup ref="like_popup" type="center">
            <view class="uni-popup-info text-center bg-white">

                <view class="bg-primary color-white" style="padding: 30px 20px 20px;">
                    <icon
                        :type="like_status === 'success' ? 'success' : 'clear'"
                        size="80"
                        color="#ffffff"
                    ></icon>
                    <view class="font18">
                        投票{{ like_status === 'success' ? '成功' : '失败' }}
                    </view>
                </view>
                <view class="bg-white color-info" style="padding: 20px;">
                    <view class="color-content text-center font14">
                        {{ like_info }}
                    </view>
                </view>
                <xwy-ad :activity_id="detail.active_id" :ad_type="3"></xwy-ad>
                <view class="pt5">
                    <xwy-ad :activity_id="detail.active_id" :ad_type="66"></xwy-ad>
                </view>
                <view class="popup-close text-center p10 pt0">
                    <uni-icons
                        type="close"
                        size="24"
                        color="#b2b3b7"
                        @click="uniPopupClose('like_popup')"
                    />
                </view>
            </view>
        </uni-popup>

        <active-share ref="activeShare" wechat-share-title="邀请微信好友投票" qr-code-title="生成拉票二维码"/>

        <verify-code-input-popup v-if="vote_need_verify_code" ref="verify-code-input-popup"/>
    </view>

</template>

<script>
import utils from "@/utils/utils";

const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'

export default {
    data() {
        return {
            detail: null,
            like_status: '',
            like_info: '',
            popup_open: false,
            vote_need_verify_code: false,
            not_vote: false,
            memo_article: ''
        }
    },
    onLoad(e) {
        if (e.not_vote) this.not_vote = true
        this.getId(e)
        uni.showLoading({
            title: '加载中...',
            mask: true
        })
        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.init()
        })
    },
    onShareAppMessage() {
        return {
            title: '投我一票',
            path: '/pages/vote/user/works_detail?id=' + this.id,
            imageUrl: this.detail.conf_json?.pic_list?.length ? this.detail.conf_json.pic_list[0] : ''
        }
    },
    methods: {
        getId(e) {
            if (e.scene) {
                const sceneStr = decodeURIComponent(e.scene)
                this.id = utils.getUrlParams('id', sceneStr)
            }
            if (e.id) this.id = e.id
        },
        
        async init() {
            await this.getDetail()
            await this.getActiveDetails()
            uni.hideLoading()
        },
        
        async getActiveDetails() {
            let active_details = app.globalData['activity_detail']
            if (!active_details || active_details.active_id !== this.detail.active_id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {active_id: this.detail.active_id}
                })
                active_details = res?.data?.active_details
            }

            if (!active_details) return
            this.active_details = active_details
            if (active_details.conf?.active?.vote_need_verify_code) this.vote_need_verify_code = true
        },
        
        
        async getDetail() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.vote.voteList/vote_details',
                data: {
                    id: this.id,
                    access_token: app.globalData['access_token']
                }
            })

            const details = res.data.details
            if (details) {
                if (res.data['user_position']) {
                    const user = res.data['user_position']
                    details.my_num = user.my_num || null
                    details.prev_num = user.prev_num || null
                }
                this.detail = details

                await this.getMemoArticle()
            }
        },

        async getMemoArticle() {
            const id = this.detail?.conf_json?.memo_article?.id
            if (!id) return
            const res = await this.xwy_api.getNewsDetail(id)

            const content = res?.content
            if (content) this.memo_article = content
        },

        previewImage(src) {
            uni.previewImage({
                urls: this.detail.conf_json.pic_list,
                current: src
            })
        },

        backActivity() {
            const pages = getCurrentPages()
            const detail_route = 'pages/vote/user/detail'
            const detail_page_index = pages.findIndex(v => v.route === detail_route)
            if (detail_page_index === -1) {
                uni.reLaunch({
                    url: './detail?id=' + this.detail.active_id
                })
                return
            }
            uni.navigateBack({
                delta: pages.length - detail_page_index - 1
            })
        },


        async like() {
            if (!this.detail.checked) return this.$uni.showModal('记录未审核通过，无法投票。')
            if (!this.vote_need_verify_code) return this.likeSubmit()
            this.$refs['verify-code-input-popup'].show(codeData => this.likeSubmit(codeData))
        },

        async likeSubmit(codeData = null) {
            const data = {
                id: this.id,
                active_id: this.detail.active_id,
                act_types: 2,
                ...(codeData && { ...codeData })
            }

            this.$uni.showLoading('投票中...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/friend_agree_vote_record',
                data
            })
            uni.hideLoading()

            let like_status = 'success',
                like_info = '给好友投票成功！'

            if (res && res['status']) {
                this.detail.vote_num = this.detail.vote_num || 0
                this.detail.vote_num++

                const eventChannel = this.getOpenerEventChannel()
                eventChannel && eventChannel.emit && eventChannel.emit('addLike', this.detail.vote_num)

            } else {
                like_status = 'error'
                like_info = '给好友投票失败！'
            }
            if (res && res['info']) like_info = res['info']
            this.like_status = like_status
            this.like_info = like_info

            this.uniPopupOpen('like_popup')
        },
        

        uniPopupClose(ref) {
            this.popup_open = false
            this.$refs[ref].close()
        },

        uniPopupOpen(ref) {
            this.popup_open = true
            this.$refs[ref].open()
        },


        async toTopList() {
            this.$uni.navigateTo('./ranking-list?id=' + this.detail.active_id)
        },


        showActiveSharePopup() {
            this.$refs.activeShare.open({
                page: 'pages/vote/user/works_detail',
                scene: 'id=' + this.id,
                qrcode_logo: this.detail.rank_set?.closed_AD && this.detail.conf?.active?.qrcode_logo
            })
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 80px;
}

.look-rank {
    font-size: 12px;
    color: #fff;
    background-color: rgba(0, 0, 0, .5);
    line-height: 24px;
    border-radius: 12px 0 0 12px;
    padding-left: 10px;
    padding-right: 5px;
}

.user-info {
    position: relative;
    margin: 20px 10px 10px;
    padding: 10px;
    border-radius: 10px;
}

.sort-num {
    position: absolute;
    top: 10px;
    right: 10px;
}


.headimg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    position: absolute;
    left: 10px;
    top: -40px;
    border: 10px solid #fff;
}
.headimg image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
}
.have-headimg .user-name {
    margin-left: 80px;
}

.infos {
    padding-top: 20px;
}
.info-item {
    width: 33%;
}

.memo {
    margin: 10px;
    border-radius: 10px;
}

.img-item {
    width: 100%;
    height: auto;
    border-radius: 5px;
}

.bottom-bar {
    position: fixed;
    z-index: 9;
    bottom: 15px;
    left: 0;
    width: 100%;
    box-sizing: border-box;
}

$like-btn-size: 44px;
.like-btn, .like-btn-mimi {
    height: $like-btn-size;
    border-radius: calc(#{$like-btn-size} / 2);
}

.like-btn {
    width: 100%;
    line-height: $like-btn-size;
}

.like-btn-mimi {
    margin: 0 20px;
    width: $like-btn-size;
    min-width: $like-btn-size;
    line-height: 16px;
}

.uni-popup-info {
    width: 300px;
    border-radius: 10px;
    overflow: hidden;
}
</style>
