<template>
    <view class="page bg-background">

        <screen-picture
            v-if="screen_pic_show"
            :hide="screen_pic_hide"
            :image-src="screen_pic"
            :time="screen_pic_count_down"
            :show-button="true"
            @skipScreen="skipScreen"
        />


        <activity-delete-tips v-if="error" :activeId="id"/>

        <view v-if="detail && detail.active_id">
            <template v-if="detail.rank_set && detail.rank_set['barrage']">
                <lff-barrage ref="lffBarrage" :activeid="id"></lff-barrage>
            </template>
            
            <activity-logo-title-time :details="detail"/>

            <view class="icon-list flex-row flex-wrap text-center bdb-10 bg-white">
                <navigator v-if="is_my_activity" class="icon-item" :url="'../admin/manage?id=' + id">
                    <text class="iconfont font24 color-primary icon-setting"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">活动管理</view>
                </navigator>
                <view
                    v-if="!is_joining && detail.conf.active.enter_types !== 0"
                    class="icon-item"
                    @click="joinActivity"
                    hover-class="navigator-hover"
                >
                    <text class="iconfont font24 color-primary icon-users"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">参与活动</view>
                </view>
                <view
                    v-if="is_joining"
                    class="icon-item"
                    @click="uniPopupOpen('my_info')"
                    hover-class="navigator-hover"
                >
                    <text class="iconfont font24 color-primary icon-personal-data"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">报名信息</view>
                </view>
                <view
                    v-if="is_joining"
                    class="icon-item"
                    hover-class="navigator-hover"
                    @click="toSubmit"
                >
                    <uni-icons type="plus" size="24" color="#2d8cf0"/>
                    <view class="color-sub font14" style="padding-top: 3px;">发布作品</view>
                </view>
                <view
                    v-if="is_joining"
                    class="icon-item"
                    hover-class="navigator-hover"
                    @click="lookVoiceList(true)"
                >
                    <text class="iconfont font24 color-primary icon-dating"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">我的作品</view>
                </view>

                <view
                    v-if="!detail.rank_set || !detail.rank_set['closed_top_rank']"
                    class="icon-item"
                    hover-class="navigator-hover"
                    @click="toTopList()"
                >
                    <text class="iconfont font24 color-primary icon-trophy"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">排行榜</view>
                </view>


                <view
                    class="icon-item"
                    @click="uniPopupOpen('activity_detail')"
                    hover-class="navigator-hover"
                >
                    <text class="iconfont font24 color-primary icon-feedback"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">活动说明</view>
                </view>


                <template v-if="headimg_plugin && headimg_plugin.length">
                    <view class="icon-item" v-for="(item, index) in headimg_plugin" :key="index"
                          @click="toIdeaAvatar(item)">
                        <image v-if="item.key === 'chajian'" style="width: 24px; height: 24px;"
                               :src="item.icon || 'https://apissl.xinweiyun.com/weixin/public/img/longmarch/icon_guajian.png'"/>
                        <image v-if="item.key === 'beijing'" style="width: 24px; height: 24px;"
                               :src="item.icon || 'https://apissl.xinweiyun.com/weixin/public/img/longmarch/icon_beijing.png'"/>
                        <view class="color-sub font14" style="padding-top: 1px;">
                            {{ item.name }}
                        </view>
                    </view>
                </template>


                <view
                    v-if="detail.rank_set && detail.rank_set['barrage']"
                    hover-class="navigator-hover"
                    class="icon-item"
                    @click="toBulletScreen()"
                >
                    <uni-icons type="chatboxes" size="24" color="#2d8cf0"/>
                    <!-- <text class="iconfont font24 color-primary icon-empty-state"></text> -->
                    <view class="color-sub font14" style="padding-top: 3px;">活动弹幕</view>
                </view>


                <template v-if="detail.conf.active.detail_icon_list">
                    <navigator
                        v-for="(item, index) in detail.conf.active.detail_icon_list"
                        :key="index"
                        class="icon-item"
                        :url="item.type === 0 ? ('/pages/news/preview?id=' + item.id) : ('/pages/news/list?category_id=' + item.id)"
                    >
                        <text :class="'iconfont font24 color-primary ' + item.icon"></text>
                        <view class="color-sub font14" style="padding-top: 3px;">{{ item.title }}</view>
                    </navigator>
                </template>


                <view class="icon-item" hover-class="navigator-hover" @click="showActiveSharePopup">
                    <text class="iconfont font24 color-primary icon-share"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">分享</view>
                </view>
            </view>


            <xwy-ad
                v-if="(!detail.rank_set || !detail.rank_set.closed_AD) && !screen_pic_show"
                :ad_type="3"
            ></xwy-ad>


            <xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>


            <view class="search-view flex-kai">
                <view class="search-input-view flex-row bg-white">
                    <picker
                        class="flex-all-center"
                        style="border-right: 1px solid #eee;"
                        :range="search_model"
                        range-key="title"
                        :value="search_model.findIndex(v => v.value === search_key)"
                        @change="search_key = search_model[$event.detail.value].value"
                    >
                        <view class="pl10 pr5">
                            <text class="color-sub font14">
                                {{search_model.find(v => v.value === search_key).title}}
                            </text>
                            <uni-icons type="forward" color="#999999" size="14"/>
                        </view>
                    </picker>
                    <view class="flex-all-center">
                        <input v-model="search_keyword" confirm-type="search" @confirm="search()"
                               :placeholder="`输入${search_model.find(v => v.value === search_key).title}搜索`"/>
                    </view>
                </view>
                <view
                    class="search-btn bg-primary color-white text-center"
                    hover-class="navigator-hover"
                    @click="search"
                >搜索
                </view>
            </view>

            <view v-if="is_search_result && !category_list.length" class="flex-all-center">
                <view class="color-sub font14" hover-class="navigator-hover" @click="showAllWorks">
                    <text>查看全部</text>
                    <uni-icons type="forward" size="14" color="#80848f"/>
                </view>
            </view>

            <!-- <view v-if="list_total" class="text-center p10 color-sub font14">共{{list_total}}个参赛作品</view> -->

            <template v-if="category_list.length">
                <view v-if="category_list.length <= 4" class="category-list flex-row">
                    <view
                        class="category-item text-center font14 color-content"
                        :class="{'category-active': item.category_id === category_id }"
                        :style="'width: calc(100% / ' + category_list.length + ');'"
                        v-for="item in category_list"
                        :key="item.category_id"
                        @click="changeCategory(item.category_id)"
                    >{{ item.name }}
                    </view>
                </view>

                <view v-else class="category-list">
                    <scroll-view scroll-x="true" style="width: 100vw;">
                        <view class="flex-row">
                            <view
                                class="category-item text-center font14 color-content no-wrap"
                                :class="{'category-active': item.category_id === category_id }"
                                style="padding: 0 20px;"
                                v-for="item in category_list"
                                :key="item.category_id"
                                @click="changeCategory(item.category_id)"
                            >{{ item.name }}
                            </view>
                        </view>
                    </scroll-view>
                </view>
            </template>

            <view class="works-list flex-row flex-wrap pl5 pr5">
                <template v-for="(item, index) in list">
                    <view class="works-item bg-white" hover-class="navigator-hover"
                          @click="lookWorksDetail(item)" :key="index">
                        <view class="works-item-image bg-divider">
                            <image
                                v-if="item.conf_json && item.conf_json.pic_list && item.conf_json.pic_list.length"
                                class="works-item-image"
                                :src="item.conf_json.pic_list[0]"
                                mode="aspectFill"
                            />
                            <view
                                v-else
                                class="works-item-image flex-all-center"
                                style="display: flex;"
                            >
                                <uni-icons
                                    type="image"
                                    size="40"
                                    color="#bfc5d9"
                                />
                            </view>
                        </view>
                        <view>
                            <view class="text-center color-title pt5">
                                <text
                                    class="font14 color-content"
                                    v-if="item.sort_num"
                                    style="padding-right: 5px;"
                                >{{ item.sort_num }}号</text>
                                <text>{{ item['user_attend_details'].must_submit[0].value }}</text>
                            </view>
                        </view>
                        <view class="flex-kai p10">
                            <view class="color-content font14">{{ item.vote_num }}票</view>
                            <view
                                hover-class="navigator-hover"
                                class="color-primary font14 pl10"
                                @click.stop="like(index)"
                            >立即投票</view>
                        </view>
                    </view>
                    <view v-if="index !== 0 && ((index + 1) % 6 === 0)" style="width: 100%;">
                        <xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>
                    </view>
                </template>
            </view>


            <uni-load-more v-if="loading && load_page !== 1" status="loading"/>
            <uni-load-more
                v-if="is_last_page && list.length > 5"
                status="noMore"
                :contentText="{contentnomore: '我是有底线的'}"
            />
            <uni-load-more v-if="!is_last_page && !loading" status="more"/>

            <view v-if="!loading && !list.length" class="text-center" style="padding-top: 15vh;">
                <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
                <view class="color-sub font14">暂无作品</view>
            </view>

            <view class="bg-white" style="position: fixed; left: 0; bottom: 0; width: 100%; z-index: 99;">
                <view v-if="technology_support" class="flex-all-center">
                    <navigator v-if="technology_support.news_id" class="text-center font14 color-sub p10"
                               :url="'/pages/news/preview?id=' + technology_support.news_id + '&type=support'">
                        {{ technology_support.button_text }}
                    </navigator>
                    <view v-else class="text-center font14 color-sub p10">
                        {{ technology_support.button_text }}
                    </view>
                </view>

                <view v-if="!detail.rank_set || !detail.rank_set['closed_user_center']" class="flex-all-center pb10">
                    <navigator url="/pages/user/user" class="p5 color-sub font14">个人中心</navigator>
                </view>
            </view>

            <uni-popup ref="like_popup" type="center">
                <view class="uni-popup-info text-center bg-white">
                    <view class="popup-close" @click="uniPopupClose('like_popup')">
                        <uni-icons type="close" size="24" color="#b2b3b7"/>
                    </view>
                    <view class="bg-primary color-white" style="padding: 30px 20px 20px;">
                        <icon :type="like_status === 'success' ? 'success' : 'clear'" size="80"
                              color="#ffffff"></icon>
                        <view class="font18">投票{{ like_status === 'success' ? '成功' : '失败' }}</view>
                    </view>
                    <view class="bg-white color-info" style="padding: 20px;">
                        <view class="color-content text-center font14" style="padding-bottom: 20px;">
                            {{ like_info }}
                        </view>
                    </view>
                    <xwy-ad v-if="popup_open" :activity_id="id" :ad_type="3"></xwy-ad>
                </view>
                <view class="pt5">
                    <xwy-ad v-if="popup_open" :activity_id="id" :ad_type="66"></xwy-ad>
                </view>
            </uni-popup>
        </view>

        <view
            v-if="join_popup_show"
            class="join-popup flex-all-center bg-white"
            @touchmove.stop.prevent=""
        >
            <view>
                <view class="join-popup-c bg-white">
                    <view class="text-center font18 color-content p10">
                        <template v-if="update_attend_details">修改报名信息</template>
                        <template v-else>参加活动</template>
                    </view>


                    <template v-if="must_submit.length">
                        <template v-for="(item, index) in must_submit">
                            <input v-if="item.types === 1" :key="index" class="join-input" v-model="item.value"
                                   :placeholder="'请输入' + item.title + (item.rules === 1 ? ' (必填)' : '')"/>
                            <picker
                                v-if="item.types === 2"
                                :range="item.options"
                                range-key="text"
                                @change="mustValueChange($event, index)"
                            >
                                <view class="join-input flex-kai">
                                    <view v-if="!item.value" class="color-sub">
                                        请选择{{ item.title }}{{ item.rules === 1 ? ' (必选)' : '' }}
                                    </view>
                                    <view v-if="item.value">{{ item.value }}</view>
                                    <text class="iconfont icon-more color-disabled font18"/>
                                </view>
                            </picker>
                            <input v-if="item.types === 3" type="number" maxlength="11" :key="index"
                                   class="join-input" v-model="item.value"
                                   :placeholder="'请输入' + item.title + (item.rules === 1 ? ' (必填)' : '')"/>
                        </template>

                    </template>


                    <view class="join-popup-btns flex-row text-center font18">
                        <view class="join-popup-btn-cancel color-sub" @click="cancelJoin">
                            取消
                        </view>
                        <view
                            class="join-popup-btn-confirm color-success"
                            @click="joinAjax"
                        >确定</view>
                    </view>
                </view>

                <template v-if="!detail.rank_set || !detail.rank_set.closed_AD">
                    <view class="pt5">
                        <xwy-ad :ad_type="66"></xwy-ad>
                    </view>
                    <xwy-ad :ad_type="3"></xwy-ad>
                </template>
            </view>


        </view>

        <active-share ref="activeShare"/>

        <uni-popup ref="activity_detail" type="center" @touchmove.stop.prevent="">
            <view
                v-if="detail && detail.conf && detail.conf.active"
                class="uni-popup-info detail-popup bg-white"
            >
                <view class="popup-close" @click="uniPopupClose('activity_detail')">
                    <uni-icons type="close" size="28" color="#b2b3b7"/>
                </view>
                <scroll-view scroll-y="true" class="detail-popup-detail"
                             style="max-height: calc(100vh - 200px); padding: 10px 0;">
                    <view class="p10 bdb-10 bg-white">
                        <view class="text-center color-sub pb5">- 活动规则 -</view>

                        <view class="color-content font16">
                            活动参与方式：
                            <template v-if="detail.conf.active.enter_types === 1">
                                自由报名参与活动
                            </template>
                            <template v-if="detail.conf.active.enter_types === 2">
                                需要输入密码才能报名
                            </template>
                            <template v-if="detail.conf.active.enter_types === 3">
                                报名需要审核通过才能参与活动
                            </template>
                        </view>
                        <view class="color-content font16">
                            <text>用户单日投票总数：</text>
                            <text>
                                <template v-if="detail.conf.active.vote_rules && detail.conf.active.vote_rules.daily_num">
                                    {{ detail.conf.active.vote_rules.daily_num }}票
                                </template>
                                <template v-else>无限制</template>
                            </text>
                        </view>
                        <view class="color-content font16">
                            <text>用户单日对同一{{who === 45898 ? '景区' : '参赛者'}}投票上限：</text>
                            <text>
                                {{ detail.conf.active.vote_rules && detail.conf.active.vote_rules.daily_person || 1 }}票
                            </text>
                        </view>
                    </view>

                    <view v-if="detail.content || news_detail" class="p10 bdb-10 bg-white">
                        <view class="text-center color-sub pb5">- 活动说明 -</view>
                        <view class="color-content font16">
                            <template v-if="detail.content && !news_detail">
                                <rich-text :nodes="detail.content" space="nbsp" selectable="true"></rich-text>
                            </template>
                            <template v-if="news_detail">
                                <template v-if="news_detail.content">
                                    <u-parse :content="news_detail.content"/>
                                </template>
                            </template>
                        </view>
                    </view>
                    <xwy-ad
                        v-if="!loading && (!detail.rank_set || !detail.rank_set.closed_AD)"
                        :ad_type="66"
                    ></xwy-ad>
                </scroll-view>
            </view>
        </uni-popup>

        <uni-popup ref="my_info" type="center" @touchmove.stop.prevent="">
            <view
                v-if="detail && detail.conf && detail.conf.active"
                class="uni-popup-info bg-white p20"
            >
                <view class="popup-close" @click="uniPopupClose('my_info')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="text-center color-sub pb5">- 活动报名信息 -</view>
                <view class="text-center p10">
                    <image class="headimg" mode="aspectFill"
                           :src="headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"/>
                    <view>
                        <text class="color-primary" @click="updateHeadimg">更改头像</text>
                    </view>
                </view>


                <view
                    class="color-content font16 ptm5"
                    v-for="(item, index) in must_submit"
                    :key="index"
                    @click="updateAttendDetailShow"
                >
                    <text>
                        {{ item.title }}：
                        <template>
                            <template v-if="item.value">{{ item.value }}</template>
                            <template v-else>
                                <template v-if="item.types === 1">未填写</template>
                                <template v-if="item.types === 2">未选择</template>
                            </template>
                        </template>
                    </text>
                    <text v-if="is_joining" class="iconfont icon-edit color-sub pl5"></text>
                </view>

                <template v-if="popup_open && (!detail.rank_set || !detail.rank_set.closed_AD)">
                    <view style="position: relative; left: -10px;">
                        <xwy-ad :ad_type="66"></xwy-ad>
                    </view>
                    <xwy-ad :ad_type="3"></xwy-ad>
                </template>
            </view>
        </uni-popup>


        <template v-if="password_dialog_show">
            <uni-popup
                ref="input_password"
                type="dialog"
                mode="input"
                :is-mask-click="false"
                @maskClick="copy(id)"
            >
                <uni-popup-dialog
                    mode="input"
                    title="活动密码"
                    :value="password"
                    placeholder="请输入活动密码"
                    @confirm="passwordInputConfirm"
                    @close="passwordInputClose"
                ></uni-popup-dialog>
            </uni-popup>
        </template>
        

        <expiration-reminder ref="expirationReminder"/>

        <verify-code-input-popup 
            ref="verify-code-input-popup" 
            v-if="detail && detail.conf && detail.conf.active && detail.conf.active.vote_need_verify_code"
        />
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import my_storage from '@/utils/storage.js'
import base64 from '@/utils/base64.js'
import bs_socket from '@/utils/bullet_screen_socket.js'


import lffBarrage from '@/components/lff-barrage.vue'

let interval

export default {
    components: {
        lffBarrage
    },
    data() {
        return {
            evn_version: app.globalData['evn_version'],
            show_tab: false,
            from_tab: false,
            loading: true,
            screen_pic: '',
            screen_pic_show: false,
            screen_pic_hide: false,
            screen_pic_count_down: null,
            id: '',
            userid: '',
            is_my_activity: false,
            detail: {},
            user_details: {},
            error: '',
            join_popup_show: false,
            update_attend_details: false,
            headimg: '',
            username: '',
            checked: 0,
            is_joining: false,
            password: '',
            technology_support: false,
            must_submit: [],
            headimg_plugin: [],
            password_dialog_show: false,
            popup_open: false,
            news_detail: null,
            platform: uni.getSystemInfoSync().platform,
            list: [],
            list_total: 0,
            load_page: 1,
            is_last_page: false,
            like_status: '',
            like_info: '',
            is_search_result: false,
            search_model: [
                { value: 'name', title: '名称' },
                { value: 'sort_num', title: '编号' }
            ],
            search_key: 'name',
            search_keyword: '',
            who: app.globalData['who'],
            category_list: [],
            category_id: ''
        }
    },
    
    computed: {
        mustPerson() {
            const {rank_set, conf} = this.detail
            return !!(rank_set?.vote_together_submit && conf?.active?.must_person?.open)
        }
    },
    
    onLoad(e) {
        console.log('活动详情页面路径参数', e)

        let isH5 = false
        // #ifdef H5
        isH5 = true
        // #endif
        if (isH5) return this.$uni.showModal('请在小程序内打开', {success: () => uni.navigateBack()})

        if (e.from && e.from === 'tab') {
            this.from_tab = true
            this.$uni.hideHomeButton()
        }

        if (uni.getLaunchOptionsSync().scene === 1154) return this.getSimpleDetail(e.id)

        e.screen_pic ? this.screenPicShow(e.screen_pic) : this.$uni.showLoading('数据加载中...')


        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            if (this.from_tab) this.show_tab = true

            if (e.scene) return this.analysisScene(e.scene)

            if (!e.id) {
                this.loading = false
                this.error = '请指定活动id'
                return uni.hideLoading()
            }


            this.id = e.id

            this.userid = app.globalData['userid']

            this.getDetail()


        })
    },
    onReachBottom() {
        !this.loading && !this.is_last_page && this.getList()
    },
    onUnload() {
        if (bs_socket.socketTask) {
            this.unload = true
            bs_socket.socketTask.close()
        }
    },


    onShareAppMessage() {
        this._utils.shareCopyActivityId(this.detail)

        let url = '/pages/vote/user/detail?id=' + this.id;
        if (this.detail?.conf?.active?.screen_pic) url += `&screen_pic=${this.detail.conf.active.screen_pic}`

        return {
            title: this.detail?.conf?.active?.share_title || this.detail?.name || '',
            path: url,
            imageUrl: this.detail?.conf?.active?.share_image || this.detail?.logo || ''
        }
    },

    onShareTimeline() {
        return {
            title: this.detail.name,
            imageUrl: this.detail.logo || ''
        }
    },

    methods: {

        search() {
            this.load_page = 1
            this.loading = true
            uni.showLoading({
                title: '搜索中...'
            })
            this.getList()
        },

        showAllWorks() {
            this.search_keyword = ''
            this.loading = true
            this.load_page = 1
            uni.showLoading({title: ''})
            this.getList()
        },

        changeCategory(category_id) {
            if (this.category_id === category_id) return
            this.category_id = category_id
            this.load_page = 1
            this.getList()
        },

        /**
         * @description 获取作品列表
         * @param {boolean} first  是否进入页面第一次加载，默认false，只有进入页面第一次调用的时候才传true。第一页并且为false的时候页面会自动滚动到列表位置
         * */
        async getList(first = false) {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
            }
            const data = {
                active_id: this.id,
                checked: 1,
                page: this.load_page,
                perpage: 10
            }
            if (this.search_keyword) data[this.search_key] = this.search_keyword
            if (this.category_id) data.category_id = this.category_id

            this.loading = true

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.audio_online.user_submit/audio_list',
                data
            })

            this.loading = false
            uni.hideLoading()
            this.is_search_result = data[this.search_key] !== ''
            this.load_page++

            if (res?.['data']?.audio_list) {
                const data_ = res.data.audio_list
                this.list_total = data_.total
                this.is_last_page = data_.is_lastpage
                const list = data_.data || []


                this.list = [...this.list, ...list]

                if (data.page === 1 && !first) {
                    this.$nextTick(() => {
                        uni.pageScrollTo({
                            selector: '.search-view'
                        })
                    })
                }
            } else {
                this.is_last_page = true
            }

        },


        lookWorksDetail(item) {
            let url = `./works_detail?id=${item.id}`
            if (this.mustPerson) url += '&not_vote=1'
            this.$uni.navigateTo(url, {
                events: {
                    addLike: vote_num => {
                        item.vote_num = vote_num
                    }
                }
            })
        },

        like(index) {
            if (this.mustPerson) return this.$uni.navigateTo(`./bulk-voting?id=${this.id}`)
                        
            const item = this.list[index]
            if (!item.checked) return this.$uni.showModal('记录未审核通过，无法投票。')
            if (!this.detail.conf?.active?.vote_need_verify_code) return this.likeSubmit(item)
            this.$refs['verify-code-input-popup'].show(codeData => this.likeSubmit(item, codeData))
        },

        async likeSubmit(item, codeData = null) {
            const data = {
                id: item.id,
                active_id: this.id,
                act_types: 2,
                ...(codeData && { ...codeData })
            }
            
            this.$uni.showLoading('投票中...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.sign_location.userSign/friend_agree_vote_record',
                data
            })
            uni.hideLoading()

            let like_status = 'success',
                like_info = '给好友投票成功！'

            if (res && res['status']) {
                item.vote_num = item.vote_num || 0
                item.vote_num++
            } else {
                like_status = 'error'
                like_info = '给好友投票失败！'
            }
            if (res && res['info']) like_info = res['info']
            this.like_status = like_status
            this.like_info = like_info

            this.uniPopupOpen('like_popup')
        },

        toTopList() {
            this.$uni.navigateTo('./ranking-list?id=' + this.id)
        },

        lookVoiceList(my_self = false) {
            let url = './works_list?id=' + this.id
            if (my_self) url += '&is_myself=1'
            this.$uni.navigateTo(url)
        },

        toBulletScreen() {
            let nickname = '匿名用户',
                headimg = ''

            if (app.globalData['userinfo'].nickname) {
                nickname = app.globalData['userinfo'].nickname
            }
            if (app.globalData['userinfo'].headimg) {
                headimg = app.globalData['userinfo'].headimg
            }
            if (this.must_submit && this.must_submit.length && this.must_submit[0].value) {
                nickname = this.must_submit[0].value
            }
            if (this.headimg) headimg = this.headimg

            let url = `/pages/activity/other/bullet_screen?active_id=${this.id}&nickname=${nickname}&headimg=${headimg}`
            console.log(this.active_more_data)
            if (this.active_more_data?.['active_conf_set']?.['barrage']) {
                const barrage = this.active_more_data['active_conf_set']['barrage']
                if (barrage.background) url += `&bgi=${barrage.background}`
                if (barrage['submit_button_text']) {
                    url += `&send_text=${barrage['submit_button_text']}`
                }
                if (barrage['navigation_bar']) {
                    url += `&navigation_bar=${JSON.stringify(barrage['navigation_bar'])}`
                }
            }
            this.$uni.navigateTo(url)
        },

        webSocketInit(reconnect = false) {
            bs_socket.webSocketInit(() => {
                bs_socket.socketTask.onMessage(res => {
                    console.log('【WEBSOCKET】收到消息', res.data)
                    this.receiveMessages(res.data)
                })
                bs_socket.socketTask.onOpen(res => {
                    console.log('【WEBSOCKET】', '链接成功！', res)
                    uni.hideLoading()
                    if (!reconnect) this.joinSocket()
                })
                bs_socket.socketTask.onClose(res => {
                    console.log('【WEBSOCKET】链接关闭！', res)
                    uni.hideLoading()
                    !this.unload && this.webSocketInit(true)
                })
            })
        },

        joinSocket() {
            let nickname = '匿名用户',
                headimg = ''

            if (app.globalData?.['userinfo']?.nickname) nickname = app.globalData.userinfo.nickname
            if (app.globalData?.['userinfo']?.headimg) headimg = app.globalData.userinfo.headimg
            if (this.must_submit && this.must_submit.length && this.must_submit[0].value) {
                nickname = this.must_submit[0].value
            }
            if (this.headimg) headimg = this.headimg
            const data = {
                active_id: this.id,
                userid: this.userid,
                nickname,
                headimg,
                message: '进入活动'
            }
            bs_socket.socketTask.send({
                data: JSON.stringify(data)
            })
        },

        receiveMessages(message) {
            message = JSON.parse(message)
            if (message.active_id !== this.id) return false
            this.$refs.lffBarrage.add({item: message})
        },


        async getSimpleDetail(id) {
            this.$uni.showLoading('数据加载中...')
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details_simple',
                data: {
                    active_id: id
                }
            })
            this.loading = false
            uni.hideLoading()

            if (!res || !res['data'] || !res['data']['active_details']) {
                this.loading = false
                uni.hideLoading()
                this.error = (res['info'] || '活动详情获取失败。') + '活动id：' + this.id
                return false
            }


            if (res['data']['active_more_data']) {
                const active_more_data = res['data']['active_more_data']
                this.active_more_data = active_more_data
                if (active_more_data.technology_support) {
                    this.technology_support = res['data']['active_more_data'].technology_support
                }
                if (active_more_data['active_conf_set']) {
                    const active_conf_set = active_more_data['active_conf_set']
                    if (active_conf_set.headimg_plugin && active_conf_set.headimg_plugin.length) {
                        this.headimg_plugin = active_conf_set.headimg_plugin
                    }
                }
            }


            const detail = res['data']['active_details']

            this.detail = detail

            my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)


            if (detail.conf) {
                const conf = detail.conf

                if (!this.screen_pic && conf.active?.screen_pic) {
                    this.screenPicShow(conf.active.screen_pic)
                }


                if (conf.must_submit) {
                    const must_submit = JSON.parse(JSON.stringify(conf.must_submit))
                    if (must_submit.length) {
                        must_submit.forEach(v => v.value = v.value || '')
                        this.must_submit = must_submit
                    }
                }
            }

            if (detail.rank_set) {
                const rank_set = detail.rank_set
                // 是否纯净版，纯净版显示tab栏，隐藏返回首页按钮
                if (rank_set['shield_other'] && !this.show_tab) {
                    this.show_tab = true
                }
            }


            this.addLookRecords()

            if (detail.name) {
                uni.setNavigationBarTitle({
                    title: detail.name
                })
            }

            if (app.globalData['userid'] === detail.userid) this.is_my_activity = true
        },

        toIdeaAvatar(item) {
            if (!item.img_list || !item.img_list.length) {
                uni.showModal({
                    title: '提示',
                    content: '活动未配置头像图片列表，无法使用此功能',
                    showCancel: false
                })

                return false
            }

            let path = '/pages/idea_avatar/pendant/pendant'
            if (item.key === 'beijing') path = '/pages/idea_avatar/background/background'
            path += `?id=${this.id}`
            if (this.detail.rank_set?.closed_AD) path += '&closed_AD=1'

            this.$uni.navigateTo(path, {
                success: res => res.eventChannel.emit('img_list', item.img_list)
            })
        },


        screenPicShow(src) {
            let isH5 = false
            // #ifdef H5
            isH5 = true
            // #endif
            if (isH5) return

            uni.hideLoading()
            this.screen_pic = src
            this.screen_pic_count_down = 5
            this.screen_pic_show = true
            interval = setInterval(() => {
                this.screen_pic_count_down--
                if (this.screen_pic_count_down <= 0) {
                    this.skipScreen()
                }
            }, 1000)
        },
        skipScreen() {
            clearInterval(interval)
            this.screen_pic_count_down = 0
            this.screen_pic_hide = true

            const timeout = setTimeout(() => {
                this.screen_pic_show = false
                this.screen_pic_hide = false
                if (this.loading) this.$uni.showLoading('数据加载中...')
                this.passwordDialogShow()
                if (this.from_tab) this.show_tab = true
                clearTimeout(timeout)
            }, 500)
        },
        analysisScene(scene) {
            const sceneStr = decodeURIComponent(scene)
            console.log(sceneStr)
            const id = utils.getUrlParams('id', sceneStr)
            console.log('id===', id)
            if (!id) {
                uni.hideLoading()
                return this.$uni.showModal('从二维码获取id失败')
            }

            this.getActiveId(id)
        },


        async getActiveId(id) {
            const data = {
                access_token: app.globalData['access_token'],
                id
            }

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/get_long_active_id_by_short_id',
                data
            })
            if (res?.['data']?.['long_active_id']) {
                this.id = res['data']['long_active_id']
                this.getDetail()
            } else {
                uni.hideLoading()
                xwy_api.alert(res && res['info'] || '长id获取失败')
            }
        },

        getDetail(just_update = false) {
            xwy_api.getActivityDetail(this.id, res => {
                if (!res || !res.data || !res.data['active_details']) {
                    this.loading = false
                    uni.hideLoading()
                    this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                    return false
                }

                // #ifndef H5
                this.$refs.expirationReminder.open(res.data.active_details)
                // #endif

                if (res.data.active_more_data) {
                    const active_more_data = res.data.active_more_data
                    this.active_more_data = active_more_data
                    if (active_more_data.technology_support) {
                        this.technology_support = res.data.active_more_data.technology_support
                    }
                    if (active_more_data['active_conf_set']) {
                        const active_conf_set = active_more_data['active_conf_set']
                        if (active_conf_set.headimg_plugin && active_conf_set.headimg_plugin.length) {
                            this.headimg_plugin = active_conf_set.headimg_plugin
                        }
                    }
                }


                const detail = res.data['active_details']

                app.globalData.activity_detail = detail

                this.detail = detail

                my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)


                if (detail.conf) {
                    const conf = detail.conf


                    if (conf.active) {
                        const active = conf.active
                        if (!this.screen_pic && active.screen_pic) {
                            this.screenPicShow(active.screen_pic)
                        }

                        if (active.news?.news_id) this.changeDetailContent(active.news.news_id)
                    }

                    if (conf.must_submit) {
                        const must_submit = JSON.parse(JSON.stringify(conf.must_submit))
                        if (must_submit.length) {
                            must_submit.forEach(v => v.value = v.value || '')
                            this.must_submit = must_submit
                        }
                    }
                }

                if (detail.rank_set) {
                    const rank_set = detail.rank_set
                    // 是否纯净版，纯净版显示tab栏，隐藏返回首页按钮
                    if (rank_set['shield_other'] && !this.show_tab) {
                        this.show_tab = true
                        this.$uni.hideHomeButton()

                        // 更新纯净版缓存信息
                        utils.updateShieldOtherInfo(this.detail)
                    }
                }


                this.addLookRecords()

                if (detail.name) {
                    uni.setNavigationBarTitle({
                        title: detail.name
                    })
                }

                if (!just_update && app.globalData['userid'] === detail.userid) {
                    this.is_my_activity = true
                }

                this.getUserStatus()


                this.getCategoryList()
            })
        },

        async getCategoryList() {
            if (this.detail.rank_set?.category_list) {
                let category_list = this.category_list
                if (!category_list.length) {
                    const res = await xwy_api.getCategoryList({
                        active_id: this.id,
                        types: 16,
                        page: 1,
                        perpage: 100
                    })
                    category_list = res?.['data']?.category_list.data || []
                }

                if (category_list.length) {
                    category_list.unshift({category_id: '', name: '全部'})
                    this.category_list = category_list.map(item => ({
                        category_id: item.category_id,
                        name: item.name
                    }))
                    this.category_id = category_list[0].category_id
                }
            }

            this.load_page = 1
            this.is_last_page = false
            await this.getList(true)
        },


        addLookRecords() {
            const detail = this.detail
            const value = {
                active_id: detail.active_id,
                name: detail.name,
                types: detail.types,
                logo: detail.logo || this.xwy_config.active_default_logo,
                look_time: new Date().getTime()
            }

            if (detail.organizer) value.organizer = detail.organizer
            my_storage.addActivityLookRecords(value)
        },


        async getUserStatus() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id
                }
            })

            if (res?.['data']?.user_details) {
                const attend_details = res.data.user_details
                this.user_details = attend_details
                this.is_joining = true
                this.checked = attend_details.checked || 0

                this.loading = false
                uni.hideLoading()

                if (attend_details.nickname) this.username = attend_details.nickname
                if (attend_details.headimg) this.headimg = attend_details.headimg

                if (attend_details.must_submit) {
                    this.must_submit.forEach((v, i) => {
                        attend_details.must_submit.forEach(vv => {
                            if (vv.title === v.title) this.$set(this.must_submit[i], 'value', vv.value)
                        })
                    })
                }

            } else {
                this.no_attend = true
                this.loading = false
                uni.hideLoading()

                if (this.screen_pic_show) return false

                this.passwordDialogShow()
            }

            this.detail?.rank_set?.['barrage'] && this.webSocketInit()
        },


        changeDetailContent(news_id) {
            xwy_api.ajax({
                url: "front.news/news_details",
                data: {
                    access_token: app.globalData['access_token'],
                    news_id
                },
                success: res => {
                    console.log(res);
                    uni.hideLoading();

                    uni.hideLoading()
                    if (!res.data || !res.data['news_details']) {
                        uni.showModal({
                            title: '提示',
                            content: res.info || '文章内容获取失败',
                            showCancel: false,
                            success: () => uni.navigateBack()
                        })
                        return false
                    }

                    const detail = res.data['news_details']


                    if (detail.video_url) {
                        let video_type = 'txv_id'
                        if (detail.video_url.startsWith('http://') || detail.video_url.startsWith('https://')) {
                            video_type = 'http'
                        }
                        detail.video_type = video_type
                    }


                    if (detail.content) {
                        detail.content = utils.newsContentInit(detail.content)
                    }

                    this.news_detail = detail
                }
            })
        },


        passwordDialogShow() {
            if (this.detail.conf.active.password && this.no_attend) {
                const passwordDialogShow = () => {
                    this.password_dialog_show = true
                    const password = my_storage.getActivityPassword(this.id)
                    if (password) {
                        this.password = password
                        this.checkActivityPassword(password)
                    }
                    const timeout = setTimeout(() => {
                        this.$refs.input_password.open()
                        clearTimeout(timeout)
                    }, 30)
                }

                // 体验版不需要输入密码
                if (app.globalData['evn_version'] === 'trial') {
                    return uni.showModal({
                        title: '提示',
                        content: '此活动设置了活动密码，请勿报名参与活动！！！',
                        cancelText: '进入活动',
                        confirmText: '输入密码',
                        success: res => {
                            res.confirm && passwordDialogShow()
                        }
                    })
                }


                passwordDialogShow()
            }
        },

        passwordInputConfirm(val) {
            if (!val) {
                // 纯净版取消输入密码出现去个人中心选项
                if (this.detail?.rank_set?.['shield_other']) {
                    return uni.showModal({
                        title: '提示',
                        content: '请输入密码',
                        cancelText: '个人中心',
                        confirmText: '重新输入',
                        success: res => {
                            if (res?.confirm) return this.$refs.input_password.open()
                            this.$uni.navigateTo('/pages/user/user')
                        }
                    })
                }

                return xwy_api.alert('请输入密码', {success: () => this.$refs.input_password.open()})
            }
            this.checkActivityPassword(val)
        },

        async checkActivityPassword(password) {
            this.$uni.showLoading('密码验证中...')

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/check_active_password',
                data: {
                    access_token: app.globalData['access_token'],
                    active_id: this.id,
                    password
                }
            })
            uni.hideLoading()

            if (res?.['status']) {
                my_storage.rememberActivityPassword(this.id, password)
                this.$refs.input_password.close()
                uni.showToast({
                    title: '密码正确',
                    icon: 'success'
                })

                return false
            }


            xwy_api.alert(res && res['info'] || '密码错误', {
                success: () => this.$refs.input_password.open()
            })
        },

        passwordInputClose() {
            this.copy(this.id, true)

            if (getCurrentPages().length > 1) return uni.navigateBack()

            // 纯净版并且只有当前页面栈，重新弹出输入密码窗口
            if (app.globalData['shop_info']?.['extend_set']?.['shield_other_active']?.active_id) {
                xwy_api.alert('请输入活动密码', {
                    success: () => this.$refs.input_password.open()
                })
                return false
            }

            this.$uni.reLaunch('/pages/index/index')
        },


        toSubmit() {
            if (this.detail.conf.active.enter_types === 3 && this.checked === 0) {
                xwy_api.alert('需要管理员审核通过后才能发布作品哦~')
                return false
            }

            uni.navigateTo({
                url: './submit?active_id=' + this.id,
                events: {
                    uploadData: () => {
                        this.getDetail()
                    }
                }
            })
        },

        async updateHeadimg() {
            uni.navigateTo({
                url: `/pages/other/headimg-list/headimg-list?active_id=${this.id}`,
                events: {
                    newImg: async obj => {
                        if (!obj?.src) return
                        this.$uni.showLoading('修改中...')
                        if (!obj.temp) return this.updateAttendDetail(obj.src)
                        const data = {
                            temp_data: {
                                path: obj.src
                            },
                            is_temp: 5
                        }
                        if (obj.size) data.temp_data.size = obj.size
                        const headimg = await xwy_api.uploadOneImage(data)
                        this.updateAttendDetail(headimg)
                    }
                }
            })
        },

        joinActivity() {
            if (this.loading) return
            let tips = ''
            if (this.is_joining) tips = '已报名活动，无需重复报名。'
            if (this.detail.conf.active.enter_types === 0) tips = '活动不允许用户报名。'
            if (tips !== '') {
                xwy_api.alert(tips)
                return
            }
            this.join_popup_show = true
        },

        updateAttendDetailShow() {
            this.update_attend_details = true
            this.join_popup_show = true
        },

        mustValueChange(e, index) {
            const item = this.must_submit[index]
            item.value = item.options[e.detail.value].text
            this.$set(this.must_submit[index], 'value', item.value)
        },


        cancelJoin() {
            this.join_popup_show = false
            this.update_attend_details = false
        },

        joinAjax() {
            if (this.update_attend_details) {
                this.updateAttendDetail()
                return false
            }


            this.joining()
        },


        getMustSubmitData() {
            const errToast = title => {
                this.$uni.showToast(title)
                return false
            }

            const must_submit = JSON.parse(JSON.stringify(this.must_submit))
            for (let i = 0; i < must_submit.length; i++) {
                const v = must_submit[i]
                v.options && delete v.options
                if (v.rules === 1 && v.value === '') {
                    return errToast(`请${v.types === 2 ? '选择' : '输入'} ${v.title}`)
                }
                if (v.types === 3 && v.value && v.value.toString().length !== 11) {
                    return errToast(`请输入正确的 ${v.title}`)
                }
            }

            let must_submit_str = JSON.stringify(must_submit)
            must_submit_str = must_submit_str.replace(/·/g, '-')
            return base64['encode'](must_submit_str)
        },

        updateAttendDetail(headimg) {
            const data = {
                access_token: app.globalData['access_token'],
                active_id: this.id
            }

            if (headimg) data.headimg = headimg

            const must_submit = this.getMustSubmitData()
            if (must_submit === false) return false
            data.must_submit = must_submit

            this.$uni.showLoading('修改中...')

            xwy_api.ajax({
                url: 'front.flat.sport_step.user/update_attend_details',
                data,
                success: res => {
                    if (res?.status !== 1) return this.$uni.showModal(res?.info || '修改失败')

                    this.$uni.showToast('修改成功', 'success')

                    this.cancelJoin()
                    this.getDetail()
                }
            })
        },

        joining() {

            const data = {
                active_id: this.id,
                access_token: app.globalData['access_token']
            }


            if (this.must_submit && this.must_submit.length) {
                const must_submit = this.getMustSubmitData()
                if (must_submit === false) return false
                data.must_submit = must_submit
            }

            data.nickname = this.username

            this.loading = true
            this.$uni.showLoading('报名中...')


            xwy_api.ajax({
                url: 'front.flat.sport_step.user/submit_attend_active',
                data,
                success: res => {
                    this.loading = false
                    uni.hideLoading()
                    console.log('报名活动', res)
                    if (!res.status) {
                        uni.showModal({
                            title: '报名失败',
                            content: res.info || '报名失败',
                            showCancel: false
                        })
                        return false
                    }

                    this.join_popup_show = false
                    uni.showToast({
                        title: res.info || '报名成功',
                        icon: 'success'
                    })

                    setTimeout(() => {
                        uni.showLoading({
                            mask: true
                        })
                        this.getDetail()
                    }, 1000)
                }
            })
        },


        showActiveSharePopup() {
            this.$refs.activeShare.open({
                page: 'pages/vote/user/detail',
                scene: 'id=' + this.detail.id,
                qrcode_logo: this.detail.rank_set?.closed_AD && this.detail.conf?.active?.qrcode_logo
            })
        },
        

        copy(data, hide_toast = false) {
            uni.setClipboardData({
                data,
                success: () => hide_toast ? uni.hideToast() : this.$uni.showToast('复制成功', 'none', 500)
            })
        },

        uniPopupClose(ref) {
            this.popup_open = false
            this.$refs[ref].close()
        },

        uniPopupOpen(ref) {
            this.popup_open = true
            this.$refs[ref].open()
        }

    }
}
</script>

<style scoped lang="scss">

.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 80px;
}

.bdb-10 {
    border-bottom: 10px solid #f8f8f8;
}

.icon-list {
    border-top: 1px solid #eee;
}

.icon-item {
    padding: 10px 0;
    width: calc(100% / 4);
    /* border-top: 1px solid #eee; */
    box-sizing: border-box;
}


.headimg {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}


.join-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, .7);
    z-index: 9999;
}

.join-popup-c {
    border-radius: 10px;
    overflow: hidden;
}


.join-input {
    margin: 10px;
    padding: 10px;
    border: 1px solid #bbb;
    border-radius: 5px;
    width: 70vw;
    max-width: 400px;
}

.join-popup-btns {
    border-top: 1px solid #eee;
}

.join-popup-btn-cancel, .join-popup-btn-confirm {
    width: 50%;
    box-sizing: border-box;
    line-height: 44px;
}

.join-popup-btn-cancel {
    border-right: 1px solid #eee;
}


.uni-popup-info {
    position: relative;
    width: 280px;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    z-index: 999;
    left: 50%;
    bottom: -5px;
    margin-left: -12px;
}

.detail-popup {
    width: 95vw;
    padding-bottom: 15px;
}


.works-item {
    width: calc(50% - 10px);
    margin: 10px 5px;
    border-radius: 10px;
    overflow: hidden;
}

.works-item-image {
    width: 100%;
    height: 200px;
    display: block;
}

.search-view {
    padding: 10px;
}

.search-input-view {
    width: calc(100% - 90px);
    border-radius: 22px;
    height: 44px;
}

.search-input-view input {
    width: 100%;
    padding: 10px;
}

.search-btn {
    width: 80px;
    line-height: 44px;
    border-radius: 22px;
}

.category-list {
    padding-bottom: 10px;

    .category-item {
        padding: 0 5px;
        height: 34px;
        line-height: 34px;
        box-sizing: border-box;
        color: #666;
    }

    .category-active {
        color: #1e88e5;
        border-bottom: 2px solid #1e88e5;
    }
}
</style>
