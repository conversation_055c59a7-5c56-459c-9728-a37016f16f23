<template>
    <view>
        <view class="p10">
            <uni-forms label-position="top" label-width="300">
                <uni-forms-item label="作品备注" :required="submitRequired.memo === 1">
                    <uni-easyinput type="textarea" v-model="memo" :maxlength="800"
                                   :placeholder="memoPlaceholder"/>
                </uni-forms-item>

                <uni-forms-item v-if="is_admin" label="作品备注文章绑定">
                    <view class="font12 color-sub" style="position: relative; top: -8px;">
                        绑定文章后，用户作品备注显示为文章的内容。
                    </view>
                    <view class="form-picker flex-kai" @click="toBindMemoArticle">
                        <view>
                            <text v-if="memo_article_id">{{ memo_article_title || memo_article_id }}</text>
                            <text v-else class="color-sub font14">请选择文章</text>
                        </view>
                        <view class="flex-row">
                            <view v-if="memo_article_id" class="color-sub" @click.stop="debindMemoArticle">
                                解绑
                            </view>
                            <uni-icons type="forward" size="16" color="#c0c4cc"/>
                        </view>
                    </view>
                </uni-forms-item>

                <uni-forms-item v-if="categoryOpen" label="作品分类">
                    <picker :range="category_list" range-key="name" :value="categoryValue" @change="categoryChange">
                        <view class="form-picker flex-kai">
                            <view>
                                <text v-if="categoryName">{{ categoryName }}</text>
                                <text v-else class="color-sub font14">请选择分类</text>
                            </view>
                            <uni-icons type="forward" size="16" color="#c0c4cc"/>
                        </view>
                    </picker>
                </uni-forms-item>

                <uni-forms-item v-if="submitRequired.pic !== 0" label="展示相册"
                                :required="submitRequired.pic === 1">
                    <view class="flex-row flex-wrap">
                        <view class="image-item" v-for="(item, index) in pic_list" :key="index">
                            <image :src="item" mode="aspectFill" @click="previewImage(pic_list, item)"/>
                            <view class="del-image-item" @click.stop="pic_list.splice(index, 1)">
                                <uni-icons type="closeempty" color="#e20f04"/>
                            </view>
                        </view>
                        <view
                            v-if="pic_list.length < pic_list_max_count"
                            class="add-image text-center"
                            @click="changeImage('pic_list')"
                        >
                            <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                        </view>
                    </view>
                </uni-forms-item>

                <uni-forms-item v-if="submitRequired.video !== 0" label="展示视频"
                                :required="submitRequired.video === 1">
                    <view class="pt5">
                        <view v-if="video_list.length" class="video-item-view">
                            <video class="video-item" :src="video_list[0]"></video>
                            <view class="del-image-item" @click.stop="video_list = []">
                                <uni-icons type="closeempty" color="#e20f04"/>
                            </view>
                        </view>
                        <view v-else class="choose-video flex-all-center" @click="addVideo">
                            <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                        </view>
                    </view>
                </uni-forms-item>

                <uni-forms-item label="作品编号">
                    <uni-easyinput v-model="sort_num" type="number" placeholder="编号越小展示越靠前"/>
                </uni-forms-item>
            </uni-forms>

        </view>

        <view style="height: 80px;"></view>

        <view class="save-button flex-all-center bg-white">
            <view class="clock-in flex-all-center bg-primary color-white" hover-class="navigator-hover"
                  @click="save">提交
            </view>
        </view>


    </view>
</template>

<script>
const app = getApp()

export default {
    data() {
        return {
            activity_detail: {},
            memo: '',
            pic_list: [],
            pic_list_max_count: 6,
            video_list: [],
            is_admin: 0,
            sort_num: '',
            category_list: [],
            category_id: '',
            memo_article_id: '',
            memo_article_title: ''
        }
    },

    computed: {
        submitRequired() {
            const {
                memo_required: memo = 0,
                pic_list_required: pic = 0,
                video_list_required: video = 0
            } = this.activity_detail?.conf?.active || {}

            return {memo, pic, video}
        },

        memoPlaceholder() {
            return `请填写备注,800字内,${this.submitRequired.memo === 2 ? '非' : ''}必填`
        },

        categoryOpen() {
            return !!this.activity_detail?.rank_set?.category_list
        },

        categoryValue() {
            if (!this.category_id || !this.category_list) return 0
            const index = this.category_list.findIndex(item => item.category_id === this.category_id)
            return index === -1 ? 0 : index
        },

        categoryName() {
            if (!this.category_id || !this.category_list) return ''
            return this.category_list.find(item => item.category_id === this.category_id)?.name || this.category_id
        }
    },

    onLoad(e) {
        if (e.active_id) this.active_id = e.active_id
        if (e.id) {
            this.$uni.setNavigationBarTitle('修改作品')
            this.id = e.id
        }
        if (e.user_record_id) this.user_record_id = e.user_record_id
        if (e.is_admin) this.is_admin = e.is_admin

        this.$login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err.errTitle || '提示'})
            }

            this.init()
        })
    },

    methods: {
        async init() {
            await this.getDetail()
            await this.getCategoryList()
            await this.getWorksDetail()

            uni.hideLoading()
        },

        async getDetail() {

            let details = app.globalData['activity_detail']

            if (!details || details.active_id !== this.active_id) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.active_id
                    }
                })

                details = res.data.active_details

            }

            this.activity_detail = details
        },

        async getCategoryList() {
            if (!this.activity_detail?.rank_set?.category_list) return

            const res = await this.xwy_api.getCategoryList({
                active_id: this.active_id,
                types: 16,
                page: 1,
                perpage: 100
            })

            const list = res?.data?.category_list?.data || []
            this.category_list = list.map(item => ({
                category_id: item.category_id,
                name: item.name
            }))
        },

        categoryChange(e) {
            this.category_id = this.category_list[e.detail.value].category_id
        },

        toBindMemoArticle() {
              this.$uni.navigateTo('/pages/news/list?type=user&is_sel=true', {
                events: {
                    selNews: data => {
                        this.memo_article_id = data.id
                        this.memo_article_title = data.title
                    }
                }
            })
        },

        debindMemoArticle() {
            this.memo_article_id = ''
            this.memo_article_title = ''
        },

        async getWorksDetail() {
            if (!this.id) return

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.vote.voteList/vote_details',
                data: {
                    id: this.id
                }
            })
            if (res?.data?.details) {
                const detail = res.data.details
                if (detail.memo) this.memo = detail.memo
                if (detail.sort_num) this.sort_num = detail.sort_num
                if (detail.conf_json) {
                    const conf_json = detail.conf_json
                    if (conf_json.pic_list?.length) this.pic_list = conf_json.pic_list
                    if (conf_json.video_list?.length) this.video_list = conf_json.video_list
                    if (conf_json['memo_article']) {
                        const {id, title} = conf_json['memo_article']
                        if (id && title) {
                            this.memo_article_title = title
                            this.memo_article_id = id
                        }
                    }
                }
                if (detail.category_id) this.category_id = detail.category_id
            }

            uni.hideLoading()
        },

        changeImage(key) {
            uni.navigateTo({
                url: '/pages/other/image_upload_or_select?active_id=' + this.active_id,
                events: {
                    newImg: src => {
                        switch (key) {
                            case 'pic_list':
                                this.pic_list.push(src)
                                break
                        }
                    }
                }
            })
        },

        async addVideo() {
            const res = await this.$uni.showActionSheet(['上传视频', '输入视频地址'])
            if (!res.hasOwnProperty('tapIndex')) return

            if (res.tapIndex === 0) {
                await this.chooseVideo()
                return
            }

            if (res.tapIndex === 1) {
                const res = await this.$uni.showModal('', {
                    showCancel: true,
                    editable: true,
                    placeholderText: '请输入视频地址'
                })

                if (!res.content) return

                this.video_list.push(res.content)
                // return
            }
        },

        async chooseVideo() {
            const video_data = await new Promise(resolve => {
                uni.chooseVideo({
                    success: res => {
                        return resolve(res)
                    }
                })
            })

            this.$uni.showLoading('视频上传中...')

            const video_src = await this.xwy_api.uploadVideo(video_data, this.active_id)

            uni.hideLoading()

            if (!video_src) return this.$uni.showModal('视频上传失败，请重试')

            this.video_list.push(video_src)
        },

        previewImage(urls, current = urls[0]) {
            uni.previewImage({
                urls,
                current
            })
        },

        async save() {
            const {
                memo: memo_required,
                pic: pic_list_required,
                video: video_list_required
            } = this.submitRequired

            const haveMemoArticle = this.memo_article_id && this.memo_article_title

            if (memo_required === 1 && !this.memo && !haveMemoArticle) return this.$uni.showToast('请填写备注')

            if (pic_list_required === 1 && !this.pic_list.length) return this.$uni.showToast('请上传相册')

            if (video_list_required === 1 && !this.video_list.length) {
                return this.$uni.showToast('请上传视频')
            }

            this.$uni.showLoading('正在提交...')

            const data = {
                active_id: this.active_id
            }
            if (this.id) data.id = this.id
            if (this.memo) data.memo = this.memo
            if (this.user_record_id) data.attend_id = this.user_record_id
            if (this.is_admin) data.is_admin = this.is_admin
            if (this.category_id) data.category_id = this.category_id

            if (this.sort_num) {
                const sort_num = parseInt(this.sort_num)
                if (isNaN(sort_num)) return this.$uni.showToast('作品编号请输入数字')

                if (sort_num < 1) return this.$uni.showToast('作品编号不能小于1')

                if (sort_num > 99999) return this.$uni.showToast('作品编号不能大于99999')

                data.sort_num = sort_num
            }

            const conf_json = {}
            if (this.pic_list.length) conf_json.pic_list = this.pic_list
            if (this.video_list.length) conf_json.video_list = this.video_list
            if (haveMemoArticle) {
                conf_json.memo_article = {
                    id: this.memo_article_id,
                    title: this.memo_article_title
                }
            }

            data.conf_json = this._utils.base64['encode'](JSON.stringify(conf_json))

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.audio_online.user_submit/submit_myself_audio',
                data
            })

            uni.hideLoading()

            if (res?.status !== 1) return this.$uni.showModal(res?.info || '提交失败')

            this.$uni.showToast('已提交', 'success')

            await this.checked(res?.data?.res)

            const eventChannel = this.getOpenerEventChannel()
            eventChannel && eventChannel.emit('uploadData')

            this.$uni.navigateBack(1, {delay: 1000})
        },


        // 管理员为添加的候选人发布完作品后，自动审核
        async checked(id) {
            if (!this.is_admin) return false
            await this.xwy_api.request({
                url: 'front.flat.sport_step.audio_online.admin_manage/admin_checked_user_audio',
                data: {
                    active_id: this.active_id,
                    ids: id,
                    checked: 1
                }
            })
        }

    }
}
</script>

<style lang="scss">
.save-button {
    position: fixed;
    z-index: 999999;
    bottom: 0;
    left: 0;
    width: 100vw;
    box-sizing: border-box;
    padding: 10px 10px 15px;
    border-top: 1px solid #eee;
}

.form-picker {
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    height: 34px;
    line-height: 34px;
    padding: 0 10px;
}

.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
    line-height: calc((100vw - 50px) / 3);
    margin: 5px;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}


.image-item {
    padding: 5px;
    position: relative;

    image {
        width: calc((100vw - 50px) / 3);
        height: calc((100vw - 50px) / 3);
        border-radius: 5px;
    }

    .del-image-item {
        right: 8px;
    }
}

.video-item-view {
    position: relative;

    .video-item {
        width: 100%;
    }
}

.choose-video {
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
    margin: 5px;
    border: 1px solid #eee;
}


.clock-in {
    width: 200px;
    height: 40px;
    border-radius: 20px;
}

</style>
