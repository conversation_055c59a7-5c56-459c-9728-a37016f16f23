<template>
    <view class="page bg-background">
        <template v-if="type_list.length > 1">
            <view v-if="type_list.length <= 4" class="type-bar bg-white flex-row">
                <view
                    class="type-item text-center font14 color-content"
                    :class="{'activate-type': item.id === type_id}"
                    :style="'width: calc(100% / ' + type_list.length + ');'"
                    v-for="item in type_list"
                    :key="item.id"
                    @click="changeType(item)"
                >{{ item.name }}
                </view>
            </view>

            <view v-else class="bg-white type-bar">
                <scroll-view scroll-x="true" style="width: 100vw;">
                    <view class="flex-row">
                        <view
                            class="type-item text-center font14 color-content no-wrap"
                            :class="{'activate-type': item.id === type_id}"
                            style="padding: 0 20px;"
                            v-for="item in type_list"
                            :key="item.id"
                            @click="changeType(item)"
                        >{{ item.name }}
                        </view>
                    </view>
                </scroll-view>
            </view>
        </template>

        <xwy-ad v-if="show_AD" :ad_type="3"></xwy-ad>

        <view v-if="top_rank_text" class="font12 p10" style="color: #e19898;"
              @click="copyId">{{ top_rank_text }}
        </view>

        <top-banner v-if="top_rank_banner.length" :list="top_rank_banner"></top-banner>


        <template v-if="podiumList.length">
            <ranking-podium :list="podiumList"/>
        </template>


        <view v-if="list.length && total_count && !is_category" class="color-sub font14 text-center pt5">
            <text v-if="!show_AD">我的排名: {{ my_position_num === -1 ? '未上榜' : my_position_num }}</text>
            <text class="pl5" v-if="total_count">
                <template v-if="!show_AD">(</template>
                共{{ total_count }}人
                <template v-if="!show_AD">)</template>
            </text>
        </view>

        <view class="list">
            <view v-for="(item, index) in list" :key="index">
                <xwy-ad v-if="show_AD && index === 0" :ad_type="66"></xwy-ad>

                <view v-if="!limit_show_num || index < limit_show_num" class="item flex-row bg-white">
                    <view class="index flex-all-center" @click.stop="copyUserid(item)">

                        <image class="top-3" v-if="index < 3"
                               :src="'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/no-' + (index + 1) + '.png'"/>
                        <view v-else class="color-sub font14">{{ index + 1 }}</view>
                    </view>

                    <view class="flex-all-center">
                        <view>
                            <image v-if="item.headimg" mode="aspectFill" class="headimg"
                                   :src="item.headimg"/>
                            <view v-else class="headimg bg-background color-sub font24 flex-all-center">
                                {{ item.nickname[0] }}
                            </view>
                        </view>
                    </view>

                    <view class="middle">
                        <view class="name color-title ellipsis">{{ item.nickname }}</view>
                    </view>

                    <view v-if="item.value !== 'hide' || show_like"
                          class="right flex-column flex-all-center color-red font14">
                        <view class="right-item text-right">
                            <view v-if="item.value !== 'hide'">
                                <text>{{ item.value }}</text>
                                <text class="font12" style="padding-left: 2px;">{{ unit }}</text>
                            </view>
                        </view>

                    </view>
                </view>

                <xwy-ad v-if="show_AD && index !== 0 && ((index + 1) % 10 === 0)" :ad_type="66"></xwy-ad>

            </view>
        </view>

        <view v-if="!list.length && !loading" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无排行数据</view>
        </view>

        <uni-load-more v-if="loading" status="loading"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && list.length > 5"
            status="noMore"
            :contentText="{contentnomore: '我是有底线的'}"
        ></uni-load-more>
        <uni-load-more v-if="!is_last_page && !loading" status="more"></uni-load-more>
    </view>
</template>

<script>
const app = getApp()
import xwy_api from '@/utils/api/xwy_api.js'

export default {
    data() {
        return {
            id: '',
            loading: true,
            total_count: 0,
            top_rank_text: '',
            my_position_num: -1,
            list: [],
            load_page: 1,
            is_last_page: false,
            top_rank_banner: [],
            show_AD: false,
            unit: '票',
            show_like: false,
            like_status: '',
            like_info: '',
            popup_show: false,
            limit_show_num: null,
            type_id: 10,
            type_list: [],
            is_category: false
        }
    },

    computed: {
        podiumList() {
            const {list, show_AD, unit} = this
            if (!list.length || show_AD) return []
            return this.list.slice(0, 3).map(item => ({
                headimg: item.headimg,
                nickname: item.nickname,
                value: item.value === 'hide' ? '' : `${item.value}${unit}`
            }))
        }
    },

    onLoad(e) {
        this.id = e.id

        this.$uni.showLoading()
        this.$login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.init()
        })
    },

    onPullDownRefresh() {
        this.reloadList().finally(() => uni.stopPullDownRefresh())
    },

    onReachBottom() {
        if (this.loading || this.is_last_page) return
        this.load_page++
        this.loadList()
    },

    methods: {
        async init() {
            await this.getActivityDetails()
            this.setPageData()
            await this.getRankingCategoryList()
            await this.getList()
            this.loading = false
            uni.hideLoading()
        },

        async getActivityDetails() {
            let activity_details = app.globalData['activity_detail']
            if (!activity_details || activity_details?.['avtive_id'] !== this.id) {
                const res = await xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {
                        active_id: this.id
                    }
                })
                activity_details = res?.['data']?.['active_details']
            }
            if (!activity_details) {
                return this.$uni.showModal('活动获取失败', {success: () => uni.navigateBack()})
            }
            this.activity_details = activity_details
        },

        setPageData() {
            const details = this.activity_details
            if (details.conf?.active?.top_rank_banner?.length) {
                this.top_rank_banner = details.conf.active.top_rank_banner
            }
            if (!details.rank_set?.closed_AD) this.show_AD = true
            if (details.rank_set?.limit_show_num) this.limit_show_num = details.rank_set.limit_show_num
        },

        async getRankingCategoryList() {
            const type_list = [{id: 10, name: '投票排行榜'}]
            if (this.activity_details?.rank_set?.category_list) {
                const category_list = await this.getCategoryList()
                type_list.push(...category_list.map(item => ({
                    id: item.category_id,
                    name: item.name,
                    is_category: true
                })))
            }
            this.type_list = type_list
        },

        async getCategoryList() {
            const res = await xwy_api.getCategoryList({
                active_id: this.id,
                types: 16,
                page: 1,
                perpage: 100
            })
            return res?.['data']?.category_list.data || []
        },

        changeType(item) {
            if (this.type_id === item.id) return
            this.type_id = item.id
            this.is_category = !!item.is_category

            this.reloadList()
        },

        async loadList() {
            if (!this.loading && !this.is_last_page) {
                this.loading = true
                await this.getList()
                this.loading = false
            }
        },

        async reloadList() {
            this.load_page = 1
            this.loading = true
            await this.getList()
            this.loading = false
        },

        async getList() {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
                this.my_position_num = -1
                this.total_count = 0
                this.top_rank_text = ''
            }

            if (this.is_category) return this.categoryRanking()

            const data = {
                active_id: this.id,
                top_rank_types: this.type_id,
                page: this.load_page,
                perpage: 20
            }


            const res = await xwy_api.request({
                url: 'front.flat.sport_step.step/top_rank_list',
                data
            })

            if (data.page === 1 && res.data?.rank_types_text?.rules) {
                this.top_rank_text = res.data.rank_types_text.rules
            }

            if (!res?.data) {
                this.is_last_page = true
                return false
            }

            if (res.data.top_rank_list) {
                if (res.data.top_rank_list.position_num) this.my_position_num = res.data.top_rank_list.position_num

                const data = res.data.top_rank_list
                const list = data.list.data || []
                const new_list = this.listDataInit(list)

                this.list = [...this.list, ...new_list]
                this.is_last_page = data.list.is_lastpage
                this.total_count = data.list.total
            } else {
                this.is_last_page = true
            }
        },

        listDataInit(list) {
            const new_list = []
            list.forEach(v => {
                const must_submit = v.must_submit || v.user_details?.must_submit
                const item = {
                    id: v.id,
                    userid: v.userid || null,
                    headimg: v.headimg || v.user_details?.headimg || '',
                    nickname: must_submit?.[0]?.value || ' ',
                    value: this.getItemValue(v)
                }

                new_list.push(item)
            })
            return new_list
        },

        // 获取排行榜右边显示的值
        getItemValue(v) {
            const values = {
                10: () => v.water, // ===================================== 投票活动
            }
            return values[this.type_id]?.() || 0
        },


        async categoryRanking() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.audio_online.user_submit/audio_list',
                data: {
                    active_id: this.id,
                    get_data_types: 1,  //	【1】按照投票活动的票数 从高到低排序
                    category_id: this.type_id,
                    page: this.load_page,
                    perpage: 20
                }
            })

            const data = res?.data?.audio_list
            if (!data) {
                this.is_last_page = true
                return
            }


            const list = data.data || []
            const new_list = this.categoryListDataInit(list)

            this.list = [...this.list, ...new_list]
            this.is_last_page = data.is_lastpage
            this.total_count = data.total
            this.load_page++
        },

        categoryListDataInit(list) {
            return list.map(item => ({
                id: item.id,
                userid: item.userid || null,
                headimg: item.user_attend_details?.headimg || '',
                nickname: item.user_attend_details?.must_submit?.[0]?.value || ' ',
                value: item.vote_num || 0
            }))
        },


        uniPopupOpen(ref) {
            this.$refs[ref].open()
            this.popup_show = true
        },

        uniPopupClose(ref) {
            this.$refs[ref].close()
            this.popup_show = false
        },

        copyUserid(item) {
            this.$uni.setClipboardData(item.userid.toString())
        },

        copyId() {
            uni.setClipboardData({
                data: this.id,
                success: () => uni.hideToast()
            })
        }
    }
}
</script>

<style scoped lang="scss">
@import "@/pages/ranking-list/ranking-list.scss";

.page {
    padding-top: 40px;
}

.type-bar {
    position: fixed;
    z-index: 9999;
    top: 0;
    left: 0;
    width: 100vw;
    height: 40px;
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .type-bar {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }
}
/* #endif */

.type-item {
    height: 40px;
    line-height: 40px;
    box-sizing: border-box;
}

.activate-type {
    color: #2d8cf0;
    border-bottom: 2px solid #2d8cf0;
}
</style>
