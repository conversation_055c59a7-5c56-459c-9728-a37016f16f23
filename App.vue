<script>

let ext_conf = {}
// #ifndef H5
if (uni.getExtConfigSync) {
    ext_conf = uni.getExtConfigSync()
    console.log('获取第三方平台自定义的数据字段', uni.getExtConfigSync() || {})
}
// #endif


if (!ext_conf.who) {
    ext_conf = {
        // who: 6,
        // who: 215, //wx01a523a94b068167
        // who: 42279, //wxc7415d92c6863bbd
        // who: 300, //wxfbaa3acf80597988
        who: 288, //wx0921894817922c7d
        // who: 35211, //wx91744674845494e1
        // apihost: "https://apissl.xinweiyun.com",
        apihost: "https://ja.xwykj.com",
        template_id: 'test'
    }
}

ext_conf.who = Number(ext_conf.who)

ext_conf.apihost = "https://sport.xwykj.com"

// 万步有约域名配置
if (ext_conf.who === 45813) ext_conf.apihost = 'https://vote.xwykj.com'

let userinfo = null,
    userid = null,
    access_token = null

//#ifdef H5
function getUrlParams(name, url) {
    const reg = new RegExp("(^|\\?|&)" + name + "=([^&]*)(\\s|&|$)", "i")
    url ||= decodeURIComponent(location.href)
    if (reg.test(url)) return unescape(RegExp.$2.replace(/\+/g, " "))
    return ""
}

let who = sessionStorage.getItem('who')
if (!who) {
    who = getUrlParams('who')
    sessionStorage.setItem('who', who)
}
ext_conf.who = who
ext_conf.template_id = 'h5'
ext_conf.apihost = 'https://wx-cloud-api-1755522-**********.ap-shanghai.run.tcloudbase.com/crossdomain?url=https://wx-cloud-api-1755522-**********.ap-shanghai.run.tcloudbase.com'
if (sessionStorage.getItem('apihost')) ext_conf.apihost = sessionStorage.getItem('apihost')

if (sessionStorage.getItem('userinfo')) userinfo = JSON.parse(sessionStorage.getItem('userinfo'))
if (sessionStorage.getItem('userid')) userid = sessionStorage.getItem('userid')
if (sessionStorage.getItem('access_token')) access_token = sessionStorage.getItem('access_token')
//#endif

const evn_version = wx?.getAccountInfoSync?.()?.miniProgram?.envVersion || 'release'
if (evn_version === 'develop') access_token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************.xlieIOf8F4FAhziRqZHn0DOPkYrcbJoUmK80W-tnbxg'


console.log('最终ext_conf', ext_conf)

export default {
    globalData: {
        who: parseInt(ext_conf.who),
        apihost: ext_conf.apihost,
        template_id: ext_conf.template_id,
        userInfo: {
            nickName: '微信用户',
            avatarUrl: 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg',
        },
        evn_version,
        userinfo,
        userid,
        access_token,
        tempData: {}
    },


    onLaunch: function () {
        // #ifdef MP-WEIXIN
        /*const updateManager = wx.getUpdateManager()

        updateManager.onCheckForUpdate(function (res) {
            // 请求完新版本信息的回调
            console.log('小程序是否需要更新', res.hasUpdate)
        })

        updateManager.onUpdateReady(function () {
            wx.showModal({
                title: '更新提示',
                content: '新版本已经准备好，是否重启应用？',
                success: function (res) {
                    if (res.confirm) {
                        // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
                        updateManager.applyUpdate()
                    }
                }
            })
        })*/

        wx.cloud.init()

        uni.onCopyUrl(() => {
            if (!this.xwy_config.isAllowCopyUrl()) {
                setTimeout(() => {
                    uni.setClipboardData({
                        data: '本页面短链接不可复制，请使用转发功能',
                        complete() {
                            uni.showToast({
                                title: '本页面短链接不可复制，请使用转发功能',
                                icon: 'none'
                            })
                        }
                    })
                }, 500)
                return
            }

            const pages = getCurrentPages()
            const currentPage = pages[pages.length - 1]
            const options = currentPage['options']
            const query = Object.keys(options).map(v => `${v}=${options[v]}`).join('&')
            console.log(query);
            if (query) return { query }
        })
        // #endif

        // 初始化清除不需要的缓存
        const removeStorageKeys = [
            'MOVE_NET_lightning',  // AI运动模型缓存
            'MOVE_NET_thunder'     // AI运动模型缓存
        ]
        removeStorageKeys.forEach(v => {
            if (uni.getStorageSync(v)) uni.removeStorageSync(v)
        })

        
        // 删除利口答题代码我这边用不到的缓存
        try {
            const res = uni.getStorageInfoSync()
            res.keys.forEach(v => v.match(/^answer_result-\d+$/) && uni.removeStorageSync(v))
        } catch (e) {}
    },
    onShow: function () {
        // #ifdef H5
        /* window.onbeforeunload = () => {
          return false
        } */
        // #endif
    },
    onHide: function () {

    }
}
</script>

<style lang="scss">
@import '@/uni_modules/uni-scss';
/*每个页面公共css */
@import url("/static/style/public.css");
</style>
