import uniApi from "@/utils/api/uniapp_api"

export default {
    getImageInfo(src) {
        return uniApi.getImageInfo(src)
    },
    
	showLoading(title = '', mask) {
		if (mask === undefined) {
			// #ifdef H5
			mask = true
			// #endif
			// #ifdef MP-WEIXIN
			const envVersion = uni.getAccountInfoSync().miniProgram.envVersion
			mask = envVersion === 'release'
			// #endif
		}
		uni.showLoading({
			title,
			mask
		})
	},

    
    /**
     * @description uni.showModal(OBJECT) 封装
     *
     * @param {String} title - 提示框的标题内容
     * @param {'success' | 'loading' | 'error' | 'none' | 'fail' | 'exception' | undefined} [icon='none'] - 提示框的图标类型
     * @param {Number} [duration=1500] - 提示框显示的时长，单位为毫秒，默认1500ms
     * @param {Object} [options={}] 其他参数
     */
	showToast: (title, icon = 'none', duration = 1500, options = {}) => {
        return uni.showToast({
            title,
            icon,
            duration,
            fail: err => console.error(err),
            ...options
        })
    },

	/**
	 * @description uni.showModal(OBJECT) 封装
	 * 
	 * @param {string} content - 弹窗的内容文本
	 * @param {Object} [options={}] - 配置项
	 * @param {string} [options.title='提示'] - 弹窗标题
	 * @param {boolean} [options.showCancel=false] - 是否显示取消按钮
	 * @param {string} [options.cancelText='取消'] - 取消按钮的文本
	 * @param {string} [options.cancelColor='#000000'] - 取消按钮的颜色
	 * @param {string} [options.confirmText='确定'] - 确认按钮的文本
	 * @param {string} [options.confirmColor='#576B95'] - 确认按钮的颜色
	 * @param {boolean} [options.editable=false] - 是否可编辑
	 * @param {string} [options.placeholderText='placeholderText'] - 输入框的占位符文本
	 * @param {Function} [options.success] - 弹窗成功回调
	 * @param {Function} [options.fail] - 弹窗失败回调
	 * @param {Function} [options.complete] - 弹窗完成回调
	 * 
	 * @returns {Promise} 返回一个 Promise，包含弹窗的结果对象
	 */
	showModal(content, options = {}) {
		if ((options.cancelText || options.cancelColor) && !options.showCancel) options.showCancel = true
		return new Promise((resolve, reject) => {
			uni.showModal({
				title: options.title || '提示',
				content,
				showCancel: options.showCancel || false,
				cancelText: options.cancelText || '取消',
				cancelColor: options.cancelColor || '#000000',
				confirmText: options.confirmText || '确定',
				confirmColor: options.confirmColor || '#576B95',
				editable: options.editable || false,
				placeholderText: options.placeholderText || 'placeholderText',
                success: res => {
                    options.success && options.success(res)
                    resolve(res)
                },
                fail: err => {
                    options.fail && options.fail(err)
                    reject(err)
                },
                complete: res => {
                    options.complete && options.complete(res)
                }
			})
		})
	},

    showActionSheet(itemList, options = {}) {
        const obj = { itemList: itemList }
        if (options.title) obj.title = options.title
        if (options.alertText) obj.alertText = options.alertText
        if (options.itemColor) obj.itemColor = options.itemColor
        if (options.popover) obj.popover = options.popover
        return new Promise(resolve => {
            uni.showActionSheet({
                ...obj,
                success: res => {
                    options.success && options.success(res)
                    resolve(res)
                },
                fail: err => {
                    options.fail && options.fail(err)
                    resolve(err)
                }
            })
        })
    },


	setNavigationBarTitle(title = '') {
		uni.setNavigationBarTitle({
			title,
			fail: err => {
				console.error('页面标题设置失败，【'+ title + '】', JSON.stringify(err))
			}
		})
	},

	navigateOptionsInit(url, options) {
		options.url = url
        options.delay = options.delay || 0
		if (!options.fail) {
			options.fail = err => {
				console.error('页面跳转失败，页面地址：【', url, '】 - 错误信息：', err)
			}
		}
		return options
	},

    navigate(options) {
        const timeout = setTimeout(() => {
            uni[options.method](options)
            clearTimeout(timeout)
        }, options.delay)
    },

	navigateTo(url, options = {}) {
		options = this.navigateOptionsInit(url, options)
        options.method = 'navigateTo'
        this.navigate(options)
	},
	redirectTo(url, options = {}) {
		options = this.navigateOptionsInit(url, options)
        options.method = 'redirectTo'
        this.navigate(options)
	},
	reLaunch(url, options = {}) {
		options = this.navigateOptionsInit(url, options)
        options.method = 'reLaunch'
        this.navigate(options)
	},
	navigateBack(delta = 1, options = {}) {
		options.delta = delta
        options.delay = options.delay || 0
        options.method = 'navigateBack'
        this.navigate(options)
	},
	navigateBackPage(path, options = {}) {
        const pages = getCurrentPages()
        const route = path.split('?')[0]
        const path_index = pages.findIndex(v => v.route === route)
        console.log(path_index);
        if (path_index === -1) {
			this.reLaunch('/' + path, options)
			return false
		}
		this.navigateBack(pages.length - path_index - 1, options)
	},


	// 获取页面栈 star
	pages() {
		return getCurrentPages()
	},
	page() {
		return this.pages()[this.pages().length - 1]
	},
	route() {
		return this.page().route
	},
	options() {
		return this.page().options
	},
	// 获取页面栈 end

	setClipboardData(data, tips = '', icon = 'none') {
		uni.setClipboardData({
			data,
			success: () => {
                tips ? this.showToast(tips, icon) : uni.hideToast()
			}
		})
	},

    previewImage(params) {
        if (typeof params === 'string') return uni.previewImage({urls: [params]})
        uni.previewImage({
            ...params,
            fail: err => console.error('图片预览失败', err)
        })
    },

    hideHomeButton() {
        // #ifdef MP-WEIXIN
        uni.hideHomeButton()
        // #endif
    }
}
