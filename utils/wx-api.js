export default {
    
    
    /**
     * @description 分享运动数据到微信运动
     * @param {Object} options - 包含分享所需数据和回调函数的对象
     * @param {Array} options.recordList - 运动数据列表 { typeId, time | distance | calorie }
     * @param {Function} [options.success] - 分享成功时的回调函数
     * @param {Function} [options.fail] - 分享失败时的回调函数
     * @param {Function} [options.complete] - 分享操作完成时的回调函数，无论成功还是失败都会执行
     */
    shareToWeRun(options) {
        if (!options?.recordList?.length) return
        console.log('分享的运动数据', options.recordList)
        
        // API文档地址: https://developers.weixin.qq.com/miniprogram/dev/api/open-api/werun/wx.shareToWeRun.html
        // 相关文档: https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-werun.html
        wx.shareToWeRun({
            recordList: options.recordList, // 分享的运动数据记录列表
            success: res => {
                console.log('分享到微信运动成功', res)
                options?.success?.()
            },
            fail: err => {
                console.log('分享到微信运动失败', err)
                options?.fail?.()
            },
            complete: res => options?.complete?.(res)
        })
    }
}