import xlsx from "@/utils/lib/xlsx.mini.min"

// 每次生成新文件要把上次的文件删掉，并把这次的文件路径存到缓存里面，便于后续删除
const removeSavedFile = (fs, filePath) => {
    const storageKey = 'removeSavedFile'
    const storage = wx.getStorageSync(storageKey)
    if (storage) fs.removeSavedFile({
        filePath: storage,
        fail: err => console.error(storage, err)
    })
    wx.setStorageSync(storageKey, filePath)
}

module.exports = {
    openDocument(data, tableName) {
        const {utils, writeFile} = xlsx
        const worksheet = utils.aoa_to_sheet(data)
        const workbook = utils.book_new()
        utils.book_append_sheet(workbook, worksheet, tableName.substring(0, 10))
        
        let env = 'weChat'
        // #ifdef H5
        env = 'H5'
        // #endif
        
        if (env === 'H5') return writeFile(workbook, `${tableName}.xlsx`)
        if (env === 'weChat') return this.weChatOpenDocument(workbook, tableName)
    },
    
    weChatOpenDocument(workbook, tableName) {
        uni.showLoading({
            title: '打开文件...'
        })
        
        const {write} = xlsx
        const fileData = write(workbook, {bookType: "xlsx", type: 'base64'})
        const filePath = `${wx.env.USER_DATA_PATH}/${tableName}-${new Date().getTime()}.xlsx`
        const fs = wx.getFileSystemManager()
        
        removeSavedFile(fs, filePath)
        
        fs.writeFile({
            filePath: filePath,
            data: fileData,
            encoding: 'base64',
            success: () => {
                const errToast = () => uni.showToast({
                    title: '导出失败，请重试',
                    icon: 'none'
                })
                
                const sysInfo = wx.getSystemInfoSync()
                
                if (sysInfo.platform.toLowerCase().indexOf('windows') >= 0) {
                    wx.saveFileToDisk({
                        filePath: filePath,
                        fail: () => errToast(),
                        complete: () => uni.hideLoading()
                    })
                } else {
                    wx.openDocument({
                        filePath: filePath,
                        showMenu: true, // 需要添加showMenu允许用户导出
                        fail: () => errToast(),
                        complete: () => uni.hideLoading()
                    })
                }
            },
            fail: e => {
                console.error(e)
                if (e.errMsg.indexOf('locked')) {
                    wx.showModal({
                        title: '提示',
                        content: '文档已打开，请先关闭',
                    })
                }
            },
            complete: () => uni.hideLoading()
        })
    }
}