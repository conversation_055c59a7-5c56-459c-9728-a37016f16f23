module.exports = {
	// 设置活动去除广告的缓存
	setActivityCloaeAdStorage(activity_id, close_ad) {
		const KEY = 'activity_close_ad_list'
		let activity_close_ad_list = uni.getStorageSync(KEY) || []

		// 把去除广告的活动id保存
		if (close_ad && close_ad !== '0') activity_close_ad_list.push(activity_id)




		// 数组去重
		activity_close_ad_list = Array.from(new Set(activity_close_ad_list))

		// 如果活动没有去除广告，并且去广告的缓存里面有此活动id，去除
		if (!close_ad || close_ad === 0 || close_ad === '0') {
			const id_index = activity_close_ad_list.findIndex(v => v === activity_id)
			if (id_index !== -1) activity_close_ad_list.splice(id_index, 1)
		}

		uni.setStorageSync(KEY, activity_close_ad_list)
	},


	activityIsCloseAd(activity_id) {
		const KEY = 'activity_close_ad_list'
		let activity_close_ad_list = uni.getStorageSync(KEY) || []
		const id_index = activity_close_ad_list.findIndex(v => v === activity_id)
		return id_index !== -1


	},

	activityLookRecords() {
        return uni.getStorageSync('activity_look_records') || []
	},

	addActivityLookRecords(detail) {
		const records = this.activityLookRecords()
		const is_exist = records.findIndex(v => v.active_id === detail.active_id)
		if (is_exist >= 0) records.splice(is_exist, 1)
		records.unshift(detail)
		if (records.length > 100) records.length = 100
		uni.setStorage({
			key: 'activity_look_records',
			data: records,
			fail: err => {
				console.log('浏览记录缓存设置失败', err)
			}
		})
	},


	setExportExcelRecord(data) {
		const record_list = uni.getStorageSync('export_excel_record') || [];
		record_list.unshift({
			url: data.url,
			add_time: new Date().getTime(),
			title: data.title
		})
		uni.setStorageSync('export_excel_record', record_list)
	},

	getExportExcelRecord() {
		const record_list = uni.getStorageSync('export_excel_record') || [];
		const current_time = new Date().getTime();
		const validity = 2 * 60 * 60 * 1000;  // 有效期2小时
		// 删除超时记录
		record_list.forEach((v, i) => {
			if (current_time - v.add_time > validity) record_list.splice(i, 1)
		})
		uni.setStorageSync('export_excel_record', record_list);
		return record_list;
	},


	rememberActivityPassword(id, password) {
		const KEY = 'activity_password_list'
		let activity_password_list = uni.getStorageSync(KEY) || []
		activity_password_list.forEach((v, i) => {
			if (v.id === id) activity_password_list.splice(i, 1)
		})
		activity_password_list.push({
			id, password
		})
		uni.setStorageSync(KEY, activity_password_list)
	},


	getActivityPassword(id) {
		if (!id) return ''
		const KEY = 'activity_password_list'
		let activity_password_list = uni.getStorageSync(KEY) || []
		if (!activity_password_list.length) return ''
		const activity_password = activity_password_list.find(v => v.id === id)
		if (!activity_password || !activity_password.password) return ''
		return activity_password.password
	},

	getCartCount(active_id) {
		const storage_key = 'shop_cart_' + active_id
		const storage_cart_data = uni.getStorageSync(storage_key) || []
		let count = 0
		storage_cart_data.forEach(v => {
			count += v.buy_count
		})
		return count
	}
}
