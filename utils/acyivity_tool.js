export default {
    actionCheck(data) {
        if (!data.is_joining) {
            uni.showToast({
                title: '还未参与活动哦',
                icon: 'error'
            })
            return false
        }
        if (!data.checked) {
            uni.showToast({
                title: '报名未审核通过',
                icon: 'error'
            })
            return false
        }
        const now_time = new Date().getTime()
        const begin_time = data.begin_time * 1000
        if (now_time < begin_time) {
            uni.showToast({
                title: '活动未开始',
                icon: 'error'
            })
            return false
        }
        const end_time = data.end_time * 1000
        if (now_time > end_time) {
            uni.showToast({
                title: '活动已结束',
                icon: 'error'
            })
            return false
        }
        return true
    },


    // 更新shopconf接口缓存的纯净版活动信息
    updateShieldOtherInfo(data) {
        const shop_info = getApp().globalData['shop_info'] || {}
        shop_info.extend_set ||= {}
        shop_info.extend_set.shield_other_active = {
            active_id: data.active_id,
            active_name: data.name,
            id: data.id,
            types: data.types
        }
    }
}
