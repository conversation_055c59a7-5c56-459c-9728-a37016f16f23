// const app = getApp();

import base64 from './base64.js'
import activity_tool from './acyivity_tool.js'
import activityPagePathConfig from "@/config/activity-page-path"

let utils = {
    ...activity_tool,
    base64,

    /**
     * 根据日期获取对应的周几
     * @param {string|Date} date - 日期字符串或 Date 对象
     * @returns {string}
     */
	getWeek(date = new Date()) {
		const weekList = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
		const weekIndex = new Date(date).getDay()
		return weekList[weekIndex]
	},


    /**
     * 随机打乱数组
     * @param {Array} array 需要打乱的数组
     * @return {Array} 打乱后的数组
     */
    shuffleArray(array) {
        const newArray = array.slice() // 创建原数组的副本，避免修改原数组
        for (let i = newArray.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [newArray[i], newArray[j]] = [newArray[j], newArray[i]]
        }
        return newArray
    },
    
    
    /**
     * @description: 输入某一天日期，获取该日所在周的所有日期
     * @param {String} input_day 某一天的日期
     * @param {Boolean} fill0 月日是否需要补0，默认不补0
     * @param {String} type 年月日连接符，默认"-"。如需返回"yyyy年mm月dd日"，type请传"年月日"
     * @return {Array} ['yyyy-m-d', 'yyyy-m-d'...]
     */
    getWeekDate(input_day = this.getDay(0, true, '/'), fill0 = false, type = '-') {
        // 一天里一共的毫秒数
        let oneDayTime = 1000 * 60 * 60 * 24
        let today = new Date(input_day)
        // 若那一天是周末时，则强制赋值为7
        let todayDay = today.getDay() || 7
        let startDate = new Date(
            today.getTime() - oneDayTime * (todayDay - 1)
        )
        let dateList = []
        for (let i = 0; i < 7; i++) {
            const temp = new Date(startDate.getTime() + i * oneDayTime)
            let year = temp.getFullYear()
            let month = temp.getMonth() + 1
            let day = temp.getDate()
            if (fill0) month = this.doHandleMonth(month);
            if (fill0) day = this.doHandleMonth(day);
            year = year.toString()
            month = month.toString()
            day = day.toString()
            dateList[i] = this.dateConnect(year, month, day, type)
        }
        return dateList
    },
    
    
    /**
     * 日期函数
     * @param {number}  day     相对于今天的哪一天（昨天传-1，今天传0，明天传1）
     * @param {boolean} fill0   月日是否补0，默认不补0 (补0: "xxxx-xx-xx", 不补0: "xxxx-x-x")
     * @param {string}  type
     * @returns {string} 'xxxx-xx-xx'
     */
    getDay(day = 0, fill0 = false, type = "-") {
        let today = new Date();
        
        let targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;
        
        today.setTime(targetday_milliseconds); //注意，这行是关键代码
        
        let tYear = today.getFullYear();
        let tMonth = today.getMonth() + 1;
        let tDate = today.getDate();
        if (fill0) tMonth = this.doHandleMonth(tMonth);
        if (fill0) tDate = this.doHandleMonth(tDate);
        
        return this.dateConnect(tYear, tMonth, tDate, type)
    },
    
    dateConnect(yy, mm, dd, type = '-') {
        if (type === '年月日') return yy + "年" + mm + "月" + dd + "日";
        return yy + type + mm + type + dd;
    },
    
    doHandleMonth(month) {
        let m = month;
        if (month.toString().length === 1) {
            m = "0" + month;
        }
        return m;
    },
    
    
    unitTimeToDate(timestamp, returnTime = false) {
        const date = this.timestampToTime(timestamp, true)
        return returnTime ? date : date.split(' ')[0]
    },
    
    // 时间戳转换成日期
    timestampToTime(timestamp, is_milli = false) {
        if (!timestamp) return ''
        if (!is_milli) timestamp *= 1000 // 不是毫秒要转成毫秒
        const date = new Date(timestamp)
        const YYYY = date.getFullYear(),
            MM = (date.getMonth() + 1).toString().padStart(2, '0'),
            DD = date.getDate().toString().padStart(2, '0'),
            hh = date.getHours().toString().padStart(2, '0'),
            mm = date.getMinutes().toString().padStart(2, '0'),
            ss = date.getSeconds().toString().padStart(2, '0')
        return `${YYYY}-${MM}-${DD} ${hh}:${mm}:${ss}`
    },
    
    
    // 时间返回年月日
    getYearMonthDay(date = this.getDay()) {
        date.replace(/\//g, '-')
        const date_arr = date.split('-')
        const year = Number(date_arr[0])
        const month = Number(date_arr[1])
        const day = Number(date_arr[2])
        return {year, month, day}
    },
    
    /**
     * 计算两个日期范围内包含的天数
     * @param first
     * @param second
     * @returns {number}
     */
    dateDiff(first, second) {
        let firstDate = first,
            secondDate = second
        if (typeof first === 'string') {
            first = first.replace(/-/g, '/')
            firstDate = new Date(first).getTime()
        }
        if (typeof second === 'string') {
            second = second.replace(/-/g, '/')
            secondDate = new Date(second).getTime()
        }
        const diff = Math.abs(firstDate - secondDate)
        const result = Math.floor(diff / (1000 * 60 * 60 * 24))
        return result + 1
    },
    
    seconds2time(seconds = 0) {
        const hh = Math.floor(seconds / 3600).toString().padStart(2, '0')
        seconds %= 3600
        const mm = Math.floor(seconds / 60).toString().padStart(2, '0')
        seconds %= 60
        const ss = seconds.toString().padStart(2, '0')
        
        if (Number(hh) > 0) return `${hh}:${mm}'${ss}"`
        /*if (Number(mm) > 0) return `${mm}'${ss}"`
        return `${ss}"`*/
        return `${Number(mm)}'${ss}"`
    },
    
    /**
     * @description 计算时间差，返回相差天数或小时数
     *
     * @param startTime 开始时间，单位毫秒
     * @param endTime 结束时间，单位毫秒
     * @returns 返回相差天数或小时数，格式如："1天" 或 "1.5小时"
     */
    timeDifference(startTime, endTime) {
        const timeDifference = endTime - startTime
        const oneDay = 24 * 60 * 60 * 1000
        if (timeDifference >= oneDay) return Math.ceil(timeDifference / oneDay) + '天'
        const oneHouse = 60 * 60 * 1000
        return Math.ceil(timeDifference / oneHouse * 10) / 10 + '小时'  // 只保留一位小数
    },
    
    
    /**
	 * 获取时区差值
	 * @returns {int} 小时
	 */
	getTimezoneOffsetHours() {
		const mins = -new Date().getTimezoneOffset()  // 本地相对 UTC 的分钟数
    	return mins / 60
	},
    
    /**
     * @description 把当地时间戳改为北京时区时间戳
     *
     * @param timestamp {number} 当地时间戳 单位 秒
     * @returns {number} 北京时区时间戳，单位 秒
     */
    timestamp2Beijing(timestamp) {
        const time_zone_hours = this.getTimezoneOffsetHours()
        const beijing_utc = 8
        if (time_zone_hours === beijing_utc) {
            console.log('⏰ 北京时区不需要转换', timestamp, ' ==> ', this.unitTimeToDate(timestamp * 1000, true))
        }
        console.log('⏰ 转换前时间戳', timestamp, ' ==> ', this.unitTimeToDate(timestamp * 1000, true))
        const timezone_offset_hours_beijing = timestamp + ((time_zone_hours - beijing_utc) * 60 * 60)
        console.log('⏰ 转换后时间戳', timezone_offset_hours_beijing, ' ==> ', this.unitTimeToDate(timezone_offset_hours_beijing * 1000, true))
        return timezone_offset_hours_beijing
    },
    
    /**
     * @description 把当地时间戳改为北京时区时间戳
     *
     * @param timestamp {number} 当地时间戳 单位 秒
     * @returns {number} 北京时区时间戳，单位 秒
     */
    beijingTimestamp2Local(timestamp) {
        const time_zone_hours = this.getTimezoneOffsetHours()
        const beijing_utc = 8
        if (time_zone_hours === beijing_utc) {
            console.log('⏰ 北京时区不需要转换', timestamp, ' ==> ', this.unitTimeToDate(timestamp * 1000, true))
        }
        console.log('⏰ 转换前时间戳', timestamp, ' ==> ', this.unitTimeToDate(timestamp * 1000, true))
        const timezone_offset_hours_local = timestamp + ((beijing_utc - time_zone_hours) * 60 * 60)
        console.log('⏰ 转换后时间戳', timezone_offset_hours_local, ' ==> ', this.unitTimeToDate(timezone_offset_hours_local * 1000, true))
        return timezone_offset_hours_local
    },
    
    
    headimgHD(imageUrl) {
        if (!imageUrl) return '';
        
        imageUrl = imageUrl.split('/'); //把头像的路径切成数组
        
        //把大小数值为 '46' || '64' || '96' || '132' 的转换为0
        const size_list = ['46', '64', '96', '132']
        if (size_list.includes(imageUrl[imageUrl.length - 1])) imageUrl[imageUrl.length - 1] = '0'
        imageUrl = imageUrl.join('/'); //重新拼接为字符串
        return imageUrl
    },
    
    
    /**
     * 获取路径的参数
     * @param name  获取的参数
     * @param url   自定义获取参数的链接
     */
    getUrlParams(name, url) {
        //URL GET 获取值
        const reg = new RegExp("(^|\\?|&)" + name + "=([^&]*)(\\s|&|$)", "i");
        if (reg.test(url)) return unescape(RegExp.$2.replace(/\+/g, " "));
        return "";
    },
    
    accSub(arg1, arg2) {
        let r1, r2, m, n;
        try {
            r1 = arg1.toString().split(".")[1].length;
        } catch (e) {
            r1 = 0;
        }
        try {
            r2 = arg2.toString().split(".")[1].length;
        } catch (e) {
            r2 = 0;
        }
        m = Math.pow(10, Math.max(r1, r2)); //last modify by deeka //动态控制精度长度
        n = (r1 >= r2) ? r1 : r2;
        return ((arg1 * m - arg2 * m) / m).toFixed(n);
    },
    
    
    // 过滤emoji
    replaceEmoji(str) {
        if (!str) return ''
        return str.replace(/\uD83C[\uDF00-\uDFFF]|\uD83D[\uDC00-\uDE4F]|\uD83E[\uDD00-\uDD80]/g, "");
    },
    
    
    checkImageSize(img_list, size = 1048576) {
        const is_big = img_list.find(v => v.size > size)
        return !is_big;
        
    },
    
    isBase64(str) {
        try {
            return base64['decode'](str)
        } catch (e) {
            return false
        }
    },
    
    
    // 生成随机数
    randomNum(minNum, maxNum) {
        switch (arguments.length) {
            case 1:
                return parseInt((Math.random() * minNum + 1).toString(), 10);
            case 2:
                return parseInt((Math.random() * (maxNum - minNum + 1) + minNum).toString(), 10);
            default:
                return 0;
        }
    },
    
    
    /**
     * 计算角度
     */
    rad(d) {
        return d * Math.PI / 180.0;
    },
    
    /**
     * 计算两点之间的直线距离
     * @param latFrom 起点纬度
     * @param lngFrom 起点经度
     * @param latTo 终点纬度
     * @param lngTo 终点经度
     * @return {Number} 返回的是距离，单位m
     */
    getDistance(latFrom, lngFrom, latTo, lngTo) {
        const radLatFrom = this.rad(latFrom);
        const radLatTo = this.rad(latTo);
        const a = radLatFrom - radLatTo;
        const b = this.rad(lngFrom) - this.rad(lngTo);
        let distance = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLatFrom) * Math.cos(radLatTo) * Math.pow(Math.sin(b / 2), 2)));
        distance = distance * 6378136.49;
        distance = Math.round(distance * 10000) / 10000;
        return parseFloat(distance.toFixed(0));
    },
    
    
    /**
     * @description 计算多个经纬度坐标的中心点
     * @param {Array} points 经纬度坐标数组 [{latitude: xx, longitude: xx}, ...]
     * @returns {Object} {latitude: xx, longitude: xx}
     */
    getCenterLonLat(points) {
        // 计算纬度的总和
        const totalLatitude = points.reduce((sum, point) => sum + point.latitude, 0)
        
        // 计算经度的总和
        const totalLongitude = points.reduce((sum, point) => sum + point.longitude, 0)
        
        // 计算平均纬度
        const averageLatitude = totalLatitude / points.length
        
        // 计算平均经度
        const averageLongitude = totalLongitude / points.length
        
        return {
            latitude: averageLatitude,
            longitude: averageLongitude
        };
    },
    
    
    /**
     * @description 从一堆经纬度中计算最大最小经纬度，然后计算出地图缩放等级
     * @param {Array} points 经纬度坐标数组 [{latitude: xx, longitude: xx}, ...]
     * @returns {Number} 地图缩放等级
     */
    getMapScale(points) {
        const scale_list = [
            {scale: 3, distance: 1000000},
            {scale: 4, distance: 500000},
            {scale: 5, distance: 200000},
            {scale: 6, distance: 100000},
            {scale: 7, distance: 50000},
            {scale: 8, distance: 50000},
            {scale: 9, distance: 20000},
            {scale: 10, distance: 10000},
            {scale: 11, distance: 5000},
            {scale: 12, distance: 2000},
            {scale: 13, distance: 1000},
            {scale: 14, distance: 500},
            {scale: 15, distance: 200},
            {scale: 16, distance: 100},
            {scale: 17, distance: 50},
            {scale: 18, distance: 50},
            {scale: 19, distance: 20},
            {scale: 20, distance: 10}
        ]
        
        // 找出最小和最大的经度
        const longitudes = points.map(point => point.longitude);
        const minLongitude = Math.min(...longitudes);
        const maxLongitude = Math.max(...longitudes);
        
        // 找出最小和最大的纬度
        const latitudes = points.map(point => point.latitude);
        const minLatitude = Math.min(...latitudes);
        const maxLatitude = Math.max(...latitudes);
        
        const lngDistance = this.getDistance(minLatitude, minLongitude, minLatitude, maxLongitude)
        const latDistance = this.getDistance(minLatitude, minLongitude, maxLatitude, minLongitude)
        
        const distance = Math.max(lngDistance, latDistance)
        
        const one_scale_distance = distance / 4.5
        if (one_scale_distance >= scale_list[0].distance) return scale_list[0].scale
        if (one_scale_distance <= scale_list[scale_list.length - 1].distance) return scale_list[scale_list.length - 1].scale
        for (let i = 0; i < scale_list.length; i++) {
            if (one_scale_distance <= scale_list[i].distance && one_scale_distance >= scale_list[i + 1].distance) {
                return scale_list[i].scale
            }
        }
        
    },
    
    // 获取地址中的省市区信息
    extractRegionInfo(address) {
        const regex = /(.*?[省市区县])(.*?[市区县])(.*?[区县])?(.*)/
        // const directMunicipalities = ['北京市', '天津市', '上海市', '重庆市']
        
        const matches = address.match(regex)
        if (matches && matches.length >= 4) {
            let province = matches[1].trim()
            let city = matches[2].trim()
            // 直辖市，如果省市一样，如“北京市 北京市”，直接显示城市“北京市”
            if (province === city) province = ''
            let district = (matches[3] || '').trim()
            const rest = matches[4] || ''
            
            // if (directMunicipalities.includes(province)) {
            //     city = province
            //     province = province.substring(0, province.length - 1);
            // }
            
            return {
                province,
                city,
                district,
                rest: rest.trim()
            }
        }
        
        return null // 未匹配到省市区信息
    },
    
    
    toActivityDetail(data) {
        let url = activityPagePathConfig.getActivityPath(data.types, 'details') || '/pages/activity/user/detail'
        url += `?id=${data.active_id}`
        if (data.conf?.active?.screen_pic) url += `&screen_pic=${data.conf.active.screen_pic}`
        if (data.active_details?.conf?.active?.screen_pic) {
            url += `&screen_pic=${data.active_details.conf.active.screen_pic}`
        }
        
        uni.navigateTo({
            url
        })
    },
    
    /**
     * 生成随机字母
     *
     * @param {Number} n 生成的数量
     * @param {Boolean} isLowerCase 是否返回小写字母，默认返回大写字母
     * @returns {String} 生成的字母
     */
    randomCoding(n = 1, isLowerCase = false) {
        const arr = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
        let value = ''
        for (let i = 0; i < n; i++) {
            value += arr[Math.floor(Math.random() * 26)]
        }
        if (isLowerCase) value = value.toLowerCase()
        return value
    },
    
    showRunExplain(type) {
        const options = {
            speed: {
                title: '什么是配速',
                content: '配速，每公里所需要的时间，是马拉松运动的训练中使用的概念。马拉松运动讲究匀速，在匀速的状态下才能更好地发挥自己的实力。'
            },
            calories: {
                title: '消耗热量',
                content: '跑步热量（大卡）= 体重（kg）× 距离（公里）× 1.036'
            },
            marathon: {
                title: '马拉松',
                content: '一次全程马拉松≈42.19km, 一次半程马拉松≈21.09km, 一次四分马拉松≈10.54km。'
            }
        }
        return uni.showModal({
            ...options[type],
            showCancel: false,
            confirmText: '知道了'
        })
    },
    
    // 字符串是否包含中文
    isChineseChar(str) {
        const reg = new RegExp("[\\u4E00-\\u9FFF]+", "g")
        return reg.test(str)
    },
    
    
    newsContentInit(content) {
        if (this.isBase64(content)) content = base64['decode'](content)
        content = content.replace(/<br \/>/g, '');
        content = content.replace(/\r/g, '');
        content = content.replace(/\n/g, '');
        content = content.replace(/\t/g, '');
        // 这个会导致段落间的间距加大，一个没用的p标签
        content = content.replace(/<p class="MsoNormal"><a href="#point_\d+"><\/a> <\/p>/g, '');
        /*//  空的p标签是正常的换行
        content = content.replace(/<p>\s+<\/p>/g, '');// 空的p标签会导致换行*/
        content = content.replace(/<\/p>\s+<p>/g, '</p><p>');// p标签之间的空格会导致换行
        
        content = content.replace(/<img /g, `<img style="width: 100% !important; height: auto; display: block;" `);   //限制最大宽度，取消上下图片间距
        
        // content = content.replace(/<p>/g, `<p style="text-indent: 2em;">`);   //首行缩进
        // content = content.replace(/<p style="/g, `<p style="text-indent: 2em; `);   //首行缩进
        // content = content.replace(/<h1>/g, `<h1 style="text-indent: 2em;">`);   //首行缩进
        // content = content.replace(/<h1 style="/g, `<h1 style="text-indent: 2em; `);   //首行缩进
        // content = content.replace(/<h2>/g, `<h2 style="text-indent: 2em;">`);   //首行缩进
        // content = content.replace(/<h2 style="/g, `<h2 style="text-indent: 2em; `);   //首行缩进
        // content = content.replace(/<h3>/g, `<h3 style="text-indent: 2em;">`);   //首行缩进
        // content = content.replace(/<h3 style="/g, `<h3 style="text-indent: 2em; `);   //首行缩进
        // content = content.replace(/<h4>/g, `<h4 style="text-indent: 2em;">`);   //首行缩进
        // content = content.replace(/<h4 style="/g, `<h4 style="text-indent: 2em; `);   //首行缩进
        // content = content.replace(/<h5>/g, `<h5 style="text-indent: 2em;">`);   //首行缩进
        // content = content.replace(/<h5 style="/g, `<h5 style="text-indent: 2em; `);   //首行缩进
        // content = content.replace(/<h6>/g, `< style="text-indent: 2em;">`);   //首行缩进
        // content = content.replace(/<h6 style="/g, `<h6 style="text-indent: 2em; `);   //首行缩进
        
        return content
    },
    
    // 步数转千米
    step2kilometer(step, precision = 0) {
        if (!step || isNaN(step)) return 0
        return Number((step / 1700).toFixed(precision))
    },
    
    // 步数转热量(千卡)
    step2calorie(step, precision = 0) {
        if (!step || isNaN(step)) return 0
        return Number((step * 0.045).toFixed(precision))
    },
    
    // 活动页面转发复制活动id，有的活动把活动名称去掉了，排行榜也去掉了，无法复制到活动id
    shareCopyActivityId(details) {
        if (!details?.rank_set?.['closed_top_rank']) return
        if (!details?.active_id) return
        
        uni.setClipboardData({
            data: details.active_id,
            success: () => uni.hideToast()
        })
    },
    
    // 四舍五入最多保留1位小数
    formatDecimal: num => Math.round(num * 10) / 10
}


module.exports = utils
