// const app = getApp()
import xwy_api from './xwy_api.js'
import base64 from '@/utils/base64.js'

export default {
	login_success: false,

	uniLogin(cb = () => {}, userid = null, active_id = null) {
		if (this.login_success) {
            if (uni.getSystemInfoSync().osName === 'windows') {
                // 解决页面加载框一直不消失的问题
                const timeout = setTimeout(() => {
                    cb && cb()
                    clearTimeout(timeout)
                }, 100)
            } else {
                cb && cb()
            }

			return false
		}

		//#ifdef H5
        const is_h5 = true
        if (is_h5) return this.h5Login(cb)
		//#endif


		uni.login({
			success: res => {
				this.getOpenid(res.code, cb, userid, active_id)
			},
			fail: err => {
				console.log('uni.login: ', err)
			}
		})
	},
    
    async h5Login(cb) {
        if (sessionStorage.getItem('access_token')) return await this.getUserDetail(cb)
        const register_user = uni.getStorageSync('register_user') || {}
        const {auto_login, username, password} = register_user
        if (!auto_login || !username || !password) return this.toH5LoginPage()
        
        const res = await xwy_api.request({
            url: 'front.user.login/user_login',
            data: {username, password: base64['decode'](password)},
        })
        if (res?.status !== 1 || !res?.data?.access_token)  return this.toH5LoginPage()
        this.h5LoginSuccess(res.data)

        await this.getUserDetail(cb)
    },
    
    toH5LoginPage() {
        const current_page = getCurrentPages()[getCurrentPages().length - 1]
        let back_path = '/' + current_page.route
        const options = current_page.options
        const query = Object.keys(options).map(key => `${key}=${options[key]}`).join('&')
        back_path += `?${query}`
        back_path = encodeURIComponent(back_path)

        return uni.reLaunch({
            url: '/pages/H5/login?back_path=' + back_path
        })
    },

    h5LoginSuccess(data) {
        const app = getApp()
        const user_details = data.user_details || {}

        if (user_details.shopid) app.globalData.who = user_details.shopid
        app.globalData.userinfo = user_details
        app.globalData.userid = user_details.userid
        app.globalData.access_token = data.access_token

        // #ifdef H5
        sessionStorage.setItem('who', user_details.shopid)
        sessionStorage.setItem('userinfo', JSON.stringify(user_details))
        sessionStorage.setItem('userid', user_details.userid)
        sessionStorage.setItem('access_token', data.access_token)
        // #endif
    },

	getOpenid(code, cb, userid, active_id) {
		xwy_api.code2session(code, res => {
			if (res === 'error') return false
			getApp().globalData.openid = res.data.wechat_res.openid
			getApp().globalData.access_token = res.data.access_token
			this.getUserDetail(cb)
		}, userid, active_id)

	},



	async getUserDetail(cb) {
		await xwy_api.getActivityTypeConfSet()
		xwy_api.getUserDetail(() => {
			this.saveUserDetail(cb)
		})
	},

	is_authorization_userInfo() {
		if (
			!getApp().globalData.userinfo ||
			!getApp().globalData.userinfo.nickname ||
			getApp().globalData.userinfo.nickname === '微信用户' ||
			getApp().globalData.userinfo.nickname === '微信昵称' ||
			!getApp().globalData.userinfo.headimg ||
			getApp().globalData.userinfo.headimg === 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg' ||
			getApp().globalData.userinfo.headimg === 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132'
		) return false

		return true

	},

	async saveUserDetail(cb) {
		if (!this.is_authorization_userInfo()) {
			const userinfo = getApp().globalData.userinfo || {nickname: '', headimg: ''}
			let {nickname, headimg} = userinfo
			if (!nickname || nickname === '微信昵称' || nickname === '微信用户') nickname = xwy_api.getRandomNickname()
			if (!headimg || headimg === 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132') headimg = await xwy_api.getRandomHeadimg()
			const data = {
				nickname,
				headimg
			}
			xwy_api.saveUserDetail(data, () => {
				this.getUserDetail(cb)
			})
			return false
		}
		this.getShopMsg(cb)
	},

	getShopMsg(cb) {
		xwy_api.ajax({
			url: 'front.user.shop/shopconf',
			data: {
				shopid: getApp().globalData.who,
				access_token: getApp().globalData.access_token,
				types: 2 // 2：健步走
			},
			success: res => {
				console.log('===== 店铺参数 =====', res)
				getApp().globalData.shop_info = res && res.data || {}

				cb && cb()
				this.login_success = true
			}
		})
	},


}
