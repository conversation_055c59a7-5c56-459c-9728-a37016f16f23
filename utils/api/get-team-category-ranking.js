import xwy_api from './xwy_api'
import openExcelFile from '@/utils/open-excel-file'

const getActiveDetails = async active_id => {
    let details = getApp()?.globalData?.['activity_detail']
    if (!details || details.active_id !== active_id) {
        const res = await xwy_api.request({
            url: 'front.flat.sport_step.active_list/active_details',
            data: {active_id}
        })
        details = res?.data?.active_details
    }
    
    return details
}

const getCategoryList = async active_id => {
    const res = await xwy_api.getCategoryList({
        types: 19,
        active_id,
        page: 1,
        perpage: 100
    })
    
    const list = res?.data?.category_list?.data || []
    return list.map(item => ({
        category_id: item.category_id,
        name: item.name,
        headimg: item.logo,
        integral: 0,
        team_count: 0
    }))
}

const getTeamRanking = async active_id => {
    const res = await xwy_api.request({
        url: 'front.flat.sport_step.step/top_rank_list',
        data: {
            active_id,
            top_rank_types: 40,
            page: 1,
            perpage: 1000
        }
    })
    
    const list = res?.data?.top_rank_list?.list?.data || []
    return list.filter(item => item.allIntegral && item.team_details?.category_id).map(item => ({
        team_id: item.team_id,
        integral: item.allIntegral,
        category_id: item.team_details.category_id
    }))
}

/*const getMyTeamRanking = async (active_id, categoryList) => {
    const res = await xwy_api.request({
        url: 'front.flat.sport_step.user/user_attend_details',
        data: {
            active_id
        }
    })
    
    const category_id = res?.data?.user_details?.team_details?.category_id
    if (!category_id) return -1
    return categoryList.findIndex(item => item.category_id === category_id) + 1
}*/


const excelDataProcessing = (list, unit) => {
    const tHead = ['序号', '系统标识', '名称', unit]
    
    const tBody = []
    
    list.forEach((item, index) => {
        const data = [
            index + 1,               // 序号
            `w.${getApp().globalData['who']}c.${item.category_id || 0}`, // 商户号+分类ID
            item.name || 0,  // 名称
            item.integral || 0  // 积分
        ]
        
        tBody.push(data)
    })
    
    return [tHead, ...tBody]
}

export default {
    async getRankingList(active_id) {
        const activeDetails = await getActiveDetails(active_id)
        const categoryList = await getCategoryList(active_id)
        const teamRanking = await getTeamRanking(active_id)
        
        // 通过 Map 将 categoryList 转化为键值对，提升查找效率
        const categoryMap = new Map(categoryList.map(item => [item.category_id, item]))
        teamRanking.forEach(team => {
            const category = categoryMap.get(team.category_id)
            if (category) {
                category.integral += team.integral
                category.team_count++
            }
        })
        
        categoryList.forEach(item => {
            if (activeDetails?.rank_set?.['team_average']) item.integral /= item.team_count
            item.integral = Number(item.integral.toFixed(2))
        })
        const sortedCategoryList = categoryList.sort((a, b) => b.integral - a.integral)
        
        // const myTeamRanking = await getMyTeamRanking(active_id, sortedCategoryList)
        
        return {
            list: sortedCategoryList,
            // position_num: myTeamRanking
        }
    },
    
    async exportRanking(active_id, unit) {
        const {list} = await this.getRankingList(active_id)
        if (!list.length) return uni.showToast({title: '没有数据', icon: 'none'})
        
        const tableData = excelDataProcessing(list, unit)
        openExcelFile.openDocument(tableData, `队伍分类${unit}排行榜`)
    }
}