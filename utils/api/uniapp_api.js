module.exports = {
	async getImageInfo(src) {
		return await new Promise(resolve => {
			uni.getImageInfo({
				src,
				success: res => resolve(res),
                fail: err => {
                    console.log(src, err)
                    resolve('error')
                }
			})
		})
	},
	
	showLoading(data) {
		if (!data.mask) {
			const evn_version = wx.getAccountInfoSync() && wx.getAccountInfoSync().miniProgram && wx.getAccountInfoSync().miniProgram.envVersion || 'release'
			data.mask = evn_version === 'release' ? true : false
		}
		uni.showLoading(data)
	}
}