import utils from '../utils.js'
import base64 from '../base64.js'
import uniapp_api from "@/utils/api/uniapp_api";

const cloud_evn_id = 'prod-0g479j60184f120d'

module.exports = {

	alert(Content = '', param = {}) {

		if (param.cancelText) {
			param.showCancel = true;
		}

		uni.showModal({
			title: param.Title || param.title || '提示',
			content: Content || '提示的内容为空（empty）',
			showCancel: param.showCancel || false,
			cancelText: param.cancelText || '取消',
			cancelColor: param.cancelColor || '#000000',
			confirmText: param.confirmText || '确定',
			confirmColor: param.confirmColor || '#3CC51F',
			success: function(res) {

				typeof param.success == 'function' && param.success(res);

			},
			fail: function(res) {
				typeof param.fail == 'function' && param.fail(res);

				//console.log(res);

			},
			complete: function(res) {
				typeof param.complete == 'function' && param.complete(res);
			}
		})

	},

	ajax(opt) {
		const self = this;
		const opts = opt || {};

		if (!opts.url) {
			self.alert('API接口地址为空');
			return false;
		}

		const Method = opts.method || 'POST',
			Header = opts.header || {
				'content-type': 'application/x-www-form-urlencoded'
			};

		let Data = opts.data || {}

		// 兑换接口时区时间戳转化
		Data = this.timestampConversion(opt.url, Data)

		//请求的次数
		let ajaxCountName = 'ajaxCou_' + opt.url.substr(-10);
		if (!this[ajaxCountName]) {
			this[ajaxCountName] = 0;

		}

		let url = opt.url
		//拼接请求域名和api路径
		if (opt.url && (!opt.url.startsWith('http://') && !opt.url.startsWith('https://'))) {
			let api_url = getApp().globalData.apihost
			if (getApp().shopMsg && getApp().shopMsg.request_url) api_url = getApp().shopMsg.request_url
			url = api_url + '/' + opt.url
		}

		console.log('🔗提交参数：', url, Data)

		let requestData = {
			url,
			method: Method,
			data: Data,
			header: Header,
			success: function(res) {
				console.log('🔗返回参数：', url, res)


				if (res.statusCode === 200) {

					self[ajaxCountName] = 0;

					typeof opts.success === 'function' && opts.success(res.data, res);

				} else {
					requestData.fail(res);

				}


			},
			fail: (res) => {
					console.log('请求错误：', url, res)

				self[ajaxCountName]++;


				if (self[ajaxCountName] > 2) {

					if (opts.fail == 'function') {
						opts.fail(res);
					} else {
						uni.hideLoading()
						self.alert(`很抱歉，网络状态不佳，请切换网络后重新再试试哦！[${res.statusCode || '404'}:${url}]`);


						opts.fail && opts.fail();

					}
					self[ajaxCountName] = 0;

					console.log('hide');
				} else {
					self.ajax(opts);
				}


			},
			complete: (res) => {
				//console.log(res);

				typeof opts.complete == 'function' && opts.complete(res);
			}
		};

		uni.request(requestData)


	},

	// 兑换接口时区处理，如果不是北京时区，要把当地时区的时间戳转换成北京时区时间戳
	timestampConversion(url = '', data) {
		if (url === 'front.flat.sport_step.exchange/exchange_step' && data?.exchange_date) {
			data.exchange_date = utils.timestamp2Beijing(data.exchange_date)
		}
		return data
	},


	request(opt) {
		if (!opt.url) {
			self.alert('API接口地址为空')
			return false
		}
		
        const globalData = getApp().globalData
		const self = this
		const opts = opt || {}
		const Method = opts.method || 'POST',
			Header = opts.header || {
				'content-type': 'application/x-www-form-urlencoded'
			}

		let Data = opts.data || {}
        if (!opts['not_token'] && !Data.access_token) Data.access_token = globalData['access_token']

		// 兑换接口时区时间戳转化
		Data = this.timestampConversion(opt.url, Data)

		//请求的次数
		let ajaxCountName = 'ajaxCou_' + opt.url.substr(-10);
		if (!this[ajaxCountName]) {
			this[ajaxCountName] = 0
		}

		let url = opt.url
		//拼接请求域名和api路径
		if (opt.url && (!opt.url.startsWith('http://') && !opt.url.startsWith('https://'))) {
			let api_url = globalData.apihost
			if (globalData.shop_info?.request_url) api_url = globalData.shop_info.request_url
			url = api_url + '/' + opt.url
		}

        const consoleData = JSON.parse(JSON.stringify(Data))
        // 上传的接口，base64的数据太大了，不打印出来
        if (opt.url === 'front.upload/upload_file') delete consoleData.pic
		console.log('🔗提交参数：', url, consoleData)

		return new Promise((resolve, reject) => {
			uni.request({
				url,
				method: Method,
				data: Data,
				header: Header,
				success: function(res) {
					console.log('🔗返回参数：', url, res)
					if (res.statusCode === 200) {
						self[ajaxCountName] = 0
						resolve(res.data)
					} else {
						reject(res)
					}
				}
			})
		}).then().catch(err => {
			console.log('请求错误：', url, err)
			self[ajaxCountName]++
			if (self[ajaxCountName] > 2) {
				uni.hideLoading()
				self.alert(`很抱歉，网络状态不佳，请切换网络后重新再试试哦！[${err.statusCode || '404'}:${url}]`)

				self[ajaxCountName] = 0
			} else {
				self.request(opts)
			}
		})
	},


	code2session(code, cb, userid, active_id) {
		const data = {
			shopid: getApp().globalData.who,
			js_code: code,
			needsessionkey: 1
		}

		if (userid) {
			data.share_user = userid
			if (active_id) data.active_id = active_id
		}

		console.log('code2session传入的参数', data)

		this.ajax({
			url: 'front.user.wechat.login/code2session',
			data,
			success: res => {
				console.log(res)
				if (res.status == 1) {
					cb && cb(res)
				} else {
					uni.hideLoading()
					uni.showModal({
						title: '提示',
						content: res.info,
						showCancel: false
					})
					cb && cb('error')
				}
			},
		    fail: error => {
				uni.hideLoading()
		        wx.showModal({
		            title: '提示',
		            content: JSON.stringify(error)
		        })
				cb && cb('error')
		    }
		})
	},

	async getWeRunData(cb) {
        const login_res = await new Promise(resolve => {
            uni.login({
                success: res => resolve(res),
                fail: err => {
                    console.log('uni.login error: ', err)
                    resolve('err')
                    cb && cb('err')
                }
            })
        })

        if (login_res === 'err') return 'err'
        
        const se_res = await this.request({
            url: 'front.user.wechat.login/code2session',
            data: {
                shopid: getApp().globalData['who'],
                js_code: login_res.code,
                needsessionkey: 1
            }
        })
        if (se_res.status !== 1) return 'err'
        
        const we_res = await new Promise(resolve => {
            wx.getWeRunData({
                success: res => {
                    resolve(res)
                },
                fail: err => {
                    console.log('wx.getWeRunData error: ', err)
                    resolve('err')
                    cb && cb('err')
                }
            })
        })
        
        if (we_res === 'err') return 'err'
        
        const res = await this.request({
            url: 'front.user.wechat.login/get_wechat_crypt_data',
            data: {
                encryptedData: we_res.encryptedData,
                iv: we_res.iv
            }
        })

        const step_list = res?.data?.['crypt_data']?.['stepInfoList']
        if (!step_list?.length) {
            cb && cb(null)
            return null
        }
        
        const today_step = step_list[step_list.length - 1].step
        if (step_list && step_list.length) getApp().globalData.step_list = step_list
        if (today_step) getApp().globalData.today_step = today_step
        cb && cb(res)
        return res
        
        
		/*uni.login({
			success: login_res => {
				this.code2session(login_res.code, se_res => {
					wx.getWeRunData({
						success: we_res => {
							this.ajax({
								url: 'front.user.wechat.login/get_wechat_crypt_data',
								data: {
									access_token: getApp().globalData.access_token,
									encryptedData: we_res.encryptedData,
									iv: we_res.iv
								},
								success: res => {
									console.log(res)
									if (!res || !res.data || !res.data.crypt_data || !res.data.crypt_data.stepInfoList || !res.data.crypt_data.stepInfoList.length) {
										cb && cb(null)
										return false
									}
									const step_list = res.data.crypt_data.stepInfoList
									const today_step = step_list[step_list.length - 1].step
									if (step_list && step_list.length) getApp().globalData.step_list = step_list
									if (today_step) getApp().globalData.today_step = today_step
									cb && cb(res)
								}
							})
						},
						fail: err => {
							cb && cb('err')
						}
					})
				})
			}
		})*/
	},

	getUserDetail(cb) {
		this.ajax({
			url: 'front.user.user/get_user_details',
			data: {
				access_token: getApp().globalData.access_token
			},
			success: res => {
		        console.log('个人信息', res)

		        if (res.data && res.data.user_details) {
		            getApp().globalData.userinfo = res.data.user_details
					if (res.data.user_details.id) getApp().globalData.userid = res.data.user_details.id
		        }
		       cb && cb(res)
		    },
		})
	},

	saveUserDetail(data, cb, fail) {
		const nickname = utils.replaceEmoji(data.nickname)
		if (data.nickname && !nickname) {
			cb && cb(false, '昵称有误，昵称不能有表情符号')
			return false
		}
		if (!data.nickname) {
			cb && cb(false, '昵称不能为空')
			return false
		}
		data.nickname = nickname

		if (!data.shopid) data.shopid = getApp().globalData.who
		if (!data.access_token) data.access_token = getApp().globalData.access_token

		this.ajax({
			url: 'front.user.user/update_user_details',
			data,
			success: res => {
				if (!res.status) {
					cb && cb(false, res.info)
					return false;
				}

				cb && cb(true)
			},
			fail: () => {
				fail && fail()
			}
		})
	},


	getActivityDetail(id, cb) {
		this.ajax({
			url: 'front.flat.sport_step.active_list/active_details',
			data: {
				access_token: getApp().globalData.access_token,
				active_id: id
			},
			success: res => {
				console.log(id, ' 活动详情：', res);
				cb && cb(res)
			}
		})
	},

	async getNewsDetail(id, text_indent = false) {
		const res = await this.request({
			url: "front.news/news_details",
			data: {
				access_token: getApp().globalData.access_token,
				news_id: id
			}
		})

		const detail = res?.data?.news_details || {}

		if (detail.content) {
			detail.content = utils.newsContentInit(detail.content)
		}

		return detail
	},

	async getImageInfo_h5(file) {
		let {base64, img_type} = await new Promise(resolve => {
		    let reader = new FileReader()
		    reader.readAsDataURL(file)
		    reader.onload = () => {
				return resolve({
					base64: reader.result.replace(/^data:image\/\w+;base64,/, ""),
					img_type: reader.result.match(/data:image\/(\S*);base64,/)[1]
				})
		    }
		})

		if (img_type === 'jpeg') img_type = 'jpg'

		return {img_type, base64}
	},

	async getImageInfo(data) {
		// #ifdef H5
		return await this.getImageInfo_h5(data.file)
		// #endif


		let img_type = await new Promise(resolve => {
			uni.getImageInfo({
				src: data.path,
				success: res => {
					return resolve(res.type)
				}
			})
		})

		const base64 = await new Promise(resolve => {
		    //获取全局唯一的文件管理器
		    uni.getFileSystemManager().readFile({ //读取本地文件内容
				filePath: data.path, // 文件路径
				encoding: 'base64', // 返回格式
				success: ({data}) => {
					return resolve(data)
				}
		    })
		})


		if (img_type === 'jpeg') img_type = 'jpg'

		return {img_type, base64}
	},

	//上传图片
	//temp_list  数组，待上传的本地临时图片路径
	//i 数字，当前上传第几张
	//src_list 数组，上传完的图片路径


    /**
     * 上传图片
     * @param options  object
     * @param options.i  number  当前上传第几张
     * @param options.temp_list  array  本地图片信息
     * @param options.temp_list[i].path  string  本地图片路径
     * @param options.temp_list[i].size  number  图片大小
     * @param options.active_id  string  活动id
     * @param options.success  function  上传成功回调
     * @param src_list  array  上传完的图片路径
     */
	async uploadImage(options, src_list = []) {
		options.i = options.i || 0
		const img_item = options.temp_list[options.i]

		const data = {
			temp_data: img_item
		}
		if (options.active_id) data.active_id = options.active_id
		const src = await this.uploadOneImage(data)
		src_list.push(src)

		if (options.i === options.temp_list.length - 1) {
			options.success && options.success(src_list)
			return false;
		}

		options.i++
		await this.uploadImage(options, src_list)
	},

    /**
     * 上传单张图片
     * @param opt  object
     * @param opt.temp_data  object  本地图片信息
     * @param opt.temp_data.path  string  本地图片路径
     * @param opt.temp_data.size  string  图片大小
     * @param opt.active_id  string  活动id
     * @param opt.is_temp  number  临时图片的类型
     */
	async uploadOneImage(opt) {
		const {img_type, base64} = await this.getImageInfo(opt.temp_data)
		const data = {
			pic: base64,
			ext: img_type
		}

		if (opt.temp_data?.size) data.size = opt.temp_data.size / 1024
		// #ifdef H5
		if (!data.size) {
			// 计算base64字符串的大小（以KB为单位）
			// base64字符串长度 * 0.75 是近似的字节大小（因为base64编码会将3字节数据编码为4字符）
			data.size = (base64.length * 0.75) / 1024
		}
		// #endif

		if (opt.active_id) data.active_id = opt.active_id
		if (opt.is_temp) data.is_temp = opt.is_temp
		const res = await this.request({url: 'front.upload/upload_file', data})
		return res?.data?.pic_url || ''
	},

	getFileType(src) {
		let type = ''
		const src2arr = src.split('.')
		if (src2arr.length) type = src2arr[src2arr.length - 1]
		return type
	},

	async uploadVideo(video_data, active_id = '') {
		const base64 = await new Promise(resolve => {
		    //获取全局唯一的文件管理器
		    uni.getFileSystemManager().readFile({ //读取本地文件内容
				filePath: video_data.tempFilePath, // 文件路径
				encoding: 'base64', // 返回格式
				success: ({data}) => {
					return resolve(data)
				}
		    })
		})

		const data = {
			access_token: getApp().globalData.access_token,
			pic: base64,
			size: video_data.size / 1024,
			ext: this.getFileType(video_data.tempFilePath),
			is_temp: 4
		}
		if (active_id) data.active_id = active_id

		const res = await this.request({url: 'front.upload/upload_file', data})

		if (res && res.data && res.data.pic_url) {
			return res.data.pic_url
		}

		return ''
	},

	async uploadAudio(audio_data, active_id = '') {
		const base64 = await new Promise(resolve => {
		    //获取全局唯一的文件管理器
		    uni.getFileSystemManager().readFile({ //读取本地文件内容
				filePath: audio_data.tempFilePath, // 文件路径
				encoding: 'base64', // 返回格式
				success: ({data}) => {
					return resolve(data)
				}
		    })
		})

		const data = {
			access_token: getApp().globalData.access_token,
			pic: base64,
			size: audio_data.fileSize / 1024,
			ext: this.getFileType(audio_data.tempFilePath),
			is_temp: 3
		}
		if (active_id) data.active_id = active_id

		const res = await this.request({url: 'front.upload/upload_file', data})

		if (res && res.data && res.data.pic_url) {
			return res.data.pic_url
		}

		return ''
	},


	getIntegralSet() {
		if (getApp().globalData.integral_set) return getApp().globalData.integral_set

		return new Promise((resolve, reject) => {
			this.ajax({
				url: 'front.flat.sport_step.system/get_shop_flat_integral_set',
				data: {
					access_token: getApp().globalData.access_token
				},
				success: res => {
					if (res?.data?.shop_set?.length) {
						const set = res.data.shop_set
						getApp().globalData.integral_set = set
						resolve(set)
					} else {
						reject(false)
					}
				},
				fail: () => reject(false)
			})
		})
	},

	async getActivityTypeConfSet() {
		if (this.have_activity_type_conf_set) {
			return this.activity_type_conf_set
		}

		const res = await this.request({
			url: 'front.system.system_conf/json_conf_set',
			data: {
				access_token: getApp().globalData.access_token,
				name: 'create_sport_active_page_set'
			}
		})
		this.have_activity_type_conf_set = true

		if (res?.data?.conf_set) {
			const active_goods_list = res?.data?.conf_set?.active_goods_list || []
			const category_list_set = res?.data?.conf_set?.category_list_set || []
			const data = {active_goods_list, category_list_set}
			this.activity_type_conf_set = data
			return data
		}

		this.activity_type_conf_set = null
		return null
	},

	async getUsedStorage() {
		const res = await this.request({
			url: 'front.user.userStorage/user_used_storage',
			data: {
				access_token: getApp().globalData.access_token
			}
		})

		return {max_storage: res?.data?.max_storage || 0, used_storage: res?.data?.user_used_details?.used_storage || 0}
	},


	closed_create_active() {
		return !!getApp().globalData['shop_info']?.extend_set?.conf_set.closed_create_active
	},

	getRandomNickname() {
		const adj_list = [
			'优雅',
			'美丽',
			'漂亮',
			'忧伤',
			'锲而不舍',
			'废寝忘食',
			'光明磊落',
			'料事如神',
			'足智多谋',
			'才华横溢',
			'文质彬彬',
			'风度翩翩',
			'威风凛凛',
			'呆若木鸡',
			'喜出望外',
			'能说会道',
			'口是心非',
			'手无缚鸡之力',
			'一身正气',
			'眼观六路',
			'耳听八方',
			'一表人才',
			'眉清目秀',
			'出类拔萃',
			'见多识广',
			'文武双全',
			'国色天香',
			'花容月貌',
			'怜香惜玉',
			'风趣幽默',
			'出神入化',
			'力挽狂澜',
			'大公无私',
			'一尘不染',
			'一鸣惊人',
			'横扫千军',
			'惊天动地',
			'日理万机',
			'霸气外露'
		]

		const noun_list = [
			'王子',
			'公主',
			'苹果',
			'梨',
			'葡萄',
			'西瓜',
			'荔枝',
			'柠檬',
			'香蕉',
			'芒果',
			'菠萝',
			'哈密瓜',
			'石榴',
			'椰子',
			'草莓',
			'木瓜',
			'猕猴桃',
			'建筑师',
			'化学工程师',
			'土木工程师',
			'会计师',
			'快递员',
			'总裁',
			'厨师',
			'汽车修护工',
			'铁匠',
			'程序员',
			'司机'
		]


		return adj_list[utils.randomNum(0, adj_list.length - 1)] + '的' + noun_list[utils.randomNum(0, noun_list.length - 1)]
	},
	async getRandomHeadimg() {
		if (this.headimgList?.length) return this.headimgList[utils.randomNum(0, this.headimgList.length - 1)].url
		const res = await this.request({
			url: 'front.system.flat_pic/pic_list',
			data: {
				access_token: getApp().globalData.access_token,
				category_id: 1389,
				page: 1,
				perpage: 100
			}
		})

		if (res?.data?.pic_list?.data?.length) {
			const list = res.data.pic_list.data
			this.headimgList = list
			return list[utils.randomNum(0, list.length - 1)].url
		}

		return ''
	},


	async gameSubmit(id, types, score) {
		const game_data_obj = { score }
		const game_data_str = JSON.stringify(game_data_obj)
		const game_data_base64 = base64.encode(game_data_str)
		const game_data = utils.randomCoding() + game_data_base64
		const res = await this.request({
			url: 'front.flat.sport_step.game_center.userGameCenter/submit_game_result',
			data: {
				access_token: getApp().globalData.access_token,
				active_id: id,
				types,
				game_data
			}
		})
		return res
	},

    
    async getWxAppCode(options) {
        const data = {...options}
        data.scene ||= 'a=a'  // 只是为了解决接口必须传scene
        
        data.env_version = 'release'
        // #ifdef MP-WEIXIN
        const {evn_version, who} = getApp().globalData
        const enableDebug = wx?.getAppBaseInfo()?.enableDebug
        if (evn_version !== 'release' && enableDebug && who === 288) data.env_version = 'trial'
        // #endif
        
        const res = await this.request({
            url: 'front.user.wechat.qrcode/create_qr_code',
            data
        })
        const src = res?.data?.['qr_code_pic']
        return src ? `${src}?time=${new Date().getTime()}` : null
    },
    
    async getQrcode(options) {
        const qr_data = {page: options.page}
        if (options.scene) qr_data.scene = options.scene
        const qr_src = await this.getWxAppCode(qr_data)
        if (!qr_src) return options?.cb('')

        let is_h5 = false
        // #ifdef H5
        is_h5 = true
        // #endif
        if (is_h5) return options?.cb(qr_src)
        
        const {qrcode_logo: logo, canvas_id, canvas_this: _this} = options

        if (!logo || !canvas_id || !_this) return options?.cb(qr_src)

        const qr_info = await uniapp_api.getImageInfo(qr_src)
        const qr_path = qr_info?.path
        if (!qr_info || qr_info === 'error' || !qr_path) return options?.cb(qr_src)

        const logo_info = await uniapp_api.getImageInfo(logo)
        const logo_path = logo_info?.path
        if (!logo_info || logo_info === 'error' || !logo_path) return options?.cb(qr_src)

        const ctx = uni.createCanvasContext(canvas_id, _this)
        const qr_size = options.size || 500
        const center_point = qr_size / 2
        const logo_size = qr_size * 0.4
        const logo_point = (qr_size - logo_size) / 2
        ctx.drawImage(qr_path, 0, 0, qr_size, qr_size)
        ctx.beginPath()
        ctx.arc(center_point, center_point, logo_size / 2, 0, Math.PI * 2, false)
        ctx.clip()
        ctx.drawImage(logo_path, logo_point, logo_point, logo_size, logo_size)
        ctx.restore()
        _this['$nextTick'](() => {
            ctx.draw(true, () => {
                _this['$nextTick'](() => {
                    this.canvasToTempFilePath(canvas_id, _this, path => options?.cb(path))
                })
            })
        })
    },

    canvasToTempFilePath(canvas_id, _this, cb) {
        uni.canvasToTempFilePath({
            canvasId: canvas_id,
            success: res => {
                console.log(res);
                cb?.(res.tempFilePath)
            },
            fail: err => {
                console.log('重新生成', err);
                setTimeout(() => this.canvasToTempFilePath(canvas_id, cb), 300)
            }
        }, _this)
    },
    
    async getAiSportTypeList() {
        let list = []
        if (this.aiSportTypeList?.length) list = this.aiSportTypeList
        if (!list?.length) {
            const res = await this.request({
                url: 'front.flat.sport_step.ai_motion.admin/flat_support_motion_list'
            })
            list = res?.data?.motion_list || []
            this.aiSportTypeList = list
        }
        
        return JSON.parse(JSON.stringify(list))
    },
    
    
    setUrlLinkUrlSchemeDefaultOptions(options) {
        // 默认类型为失效天数
        options.expire_type ??= 0
        // 如果类型是时效时间，且没有设置时效时间，则默认30天后
        if (options.expire_type === 0 && !options.hasOwnProperty('expire_time')) {
            options.expire_time = Date.now() + (30 * 24 * 60 * 60 * 1000)
        }
        // 如果类型是失效天数，且没有设置失效天数，则默认30天
        if (options.expire_type === 1 && !options.hasOwnProperty('expire_interval')) {
            options.expire_interval = 30
        }
        if (!options.env_version) {
            const {who, evn_version} = getApp().globalData
            options.env_version = who === 288 ? evn_version : 'release'
        }
        return options
    },
    
    
    /**
    * 获取加密URLLink
    * @param options {Object} 参数
    * @param options.path {String} 通过 URL Link 进入的小程序页面路径，必须是已经发布的小程序存在的页面，不可携带 query 。path 为空时会跳转小程序主页
    * @param options.query {String} 通过 URL Link 进入小程序时的query，最大1024个字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~%
    * @param options.expire_type {0 | 1} 默认值0.小程序 URL Link 失效类型，失效时间：0，失效间隔天数：1
    * @param options.expire_time {Number} 到期失效的 URL Link 的失效时间，为 Unix 时间戳。生成的到期失效 URL Link 在该时间前有效。最长有效期为30天。expire_type 为 0 必填
    * @param options.expire_interval {Number} 到期失效的URL Link的失效间隔天数。生成的到期失效URL Link在该间隔时间到达前有效。最长间隔天数为30天。expire_type 为 1 必填
    * @param options.env_version {String} 默认值"release"。要打开的小程序版本。正式版为 "release"，体验版为"trial"，开发版为"develop"，仅在微信外打开时生效。
    * */
    async generateUrlLink(options) {
        const res = await this.request({
            url: 'front.user.wechat.shortLink/get_url_link',
            data: this.setUrlLinkUrlSchemeDefaultOptions(options)
        })
        
        return res?.data?.res?.url_link || ''
    },
    
    /**
    * 获取加密URLScheme
    * @param options {Object} 参数
    * @param options.path {String} 通过 URL Scheme 进入的小程序页面路径，必须是已经发布的小程序存在的页面，不可携带 query 。path 为空时会跳转小程序主页
    * @param options.query {String} 通过 URL Scheme 进入小程序时的query，最大1024个字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~%
    * @param options.expire_type {0 | 1} 默认值0.小程序 URL Scheme 失效类型，失效时间：0，失效间隔天数：1
    * @param options.expire_time {Number} 到期失效的 URL Scheme 的失效时间，为 Unix 时间戳。生成的到期失效 URL Scheme 在该时间前有效。最长有效期为30天。expire_type 为 0 必填
    * @param options.expire_interval {Number} 到期失效的URL Scheme的失效间隔天数。生成的到期失效URL Scheme在该间隔时间到达前有效。最长间隔天数为30天。expire_type 为 1 必填
    * @param options.env_version {String} 默认值"release"。要打开的小程序版本。正式版为 "release"，体验版为"trial"，开发版为"develop"，仅在微信外打开时生效。
    * */
    async generateUrlScheme(options) {
        const res = await this.request({
            url: 'front.user.wechat.shortLink/url_scheme',
            data: this.setUrlLinkUrlSchemeDefaultOptions(options)
        })
        
        return res?.data?.res?.openlink || ''
    },
    
    /**
    * 获取加密NFC Scheme
    * @param options {Object} 参数
    * @param options.path {String} 通过 Scheme 进入的小程序页面路径，必须是已经发布的小程序存在的页面，不可携带 query 。path 为空时会跳转小程序主页
    * @param options.query {String} 通过 Scheme 进入小程序时的query，最大1024个字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~%
    * @param options.env_version {String} 默认值"release"。要打开的小程序版本。正式版为 "release"，体验版为"trial"，开发版为"develop"，仅在微信外打开时生效。
    * */
    async generateNFCScheme(options) {
        let {path, query = '', env_version, model_id, sn} = options
        
        if (!env_version) {
            const {who, evn_version: evn} = getApp().globalData
            env_version = who === 288 ? evn : 'release'
        }
        
        if (!path) return uni.showModal({title: '提示', content: '请指定页面地址', showCancel: false})
        if (!model_id) return uni.showModal({title: '提示', content: '请指定model_id', showCancel: false})
        if (!sn) return uni.showModal({title: '提示', content: 'sn', showCancel: false})
        
        const res = await this.request({
            url: 'front.user.wechat.shortLink/get_nfc_scheme',
            data: {path, query, env_version, model_id, sn}
        })
        
        return this.getSchemeLink(res)
    },
    
    getSchemeLink(res) {
        const {openlink, errcode, errmsg} = res?.data?.res || {}
        if (openlink) return openlink
        
        if (errcode === 9800010) {
            const arr = errmsg.split(' ')
            const schema = arr.find(item => item.startsWith('weixin://dl/business/?t='))
            if (schema) return schema
        }
        
        return ''
    },
    
    
    deleteRecords(types, ids) {
        return this.request({
            url: 'front.system.logsCommon/del_records',
            data: {types, ids}
        })
    },
    
    /**
    * 获取分类列表
    * @param data {Object} 参数
    * @param data.types {Number} 查询的分类类型
    * @param data.my_self {Number} 【1】只查看会员自己创建的分类，默认是查看整个商户平台的分类列表
    * @param data.name {String} 根据名称搜索
    * @param data.is_hidden {Number} 【1】只查看已隐藏的分类
    * @param data.active_id {String} 搜索某个活动下的分类列表
    * @param data.page {Number} 分页参数，当前请求第几页的数据，默认显示第一页
    * @param data.perpage {Number} 分页参数，每页显示多少条记录，默认显示10条
    * */
    getCategoryList(data) {
        return this.request({
            url: 'front.user.category/category_list',
            data
        })
    }
}
