import xwy_api from './xwy_api'
import openExcelFile from '@/utils/open-excel-file'

const getActiveDetails = async active_id => {
    let details = getApp()?.globalData?.['activity_detail']
    if (!details || details.active_id !== active_id) {
        const res = await xwy_api.request({
            url: 'front.flat.sport_step.active_list/active_details',
            data: {active_id}
        })
        details = res?.data?.active_details
    }
    
    return details
}

const getTeamRanking = async active_id => {
    const res = await xwy_api.request({
        url: 'front.flat.sport_step.step/top_rank_list',
        data: {
            active_id,
            top_rank_types: 40,
            page: 1,
            perpage: 1000
        }
    })
    
    return res?.data?.top_rank_list?.list?.data || []
}

const excelDataProcessing = (list, unit, showCategory) => {
    const tHead = ['序号', '系统标识', '队伍名称']
    if (showCategory) tHead.push('所属分类')
    tHead.push(unit)
    
    const tBody = []
    
    list.forEach((item, index) => {
        const data = [
            index + 1,               // 序号
            `w.${getApp().globalData['who']}c.${item.team_id || 0}`, // 商户号+分类ID
            item.team_details?.name || '',  // 名称
        ]
        
        if (showCategory) data.push(item.team_details?.category_details?.name || '') // 队伍所属分类名称
        
        data.push(item.allIntegral || 0)   // 积分
        
        tBody.push(data)
    })
    
    return [tHead, ...tBody]
}

export default {
    async exportRanking(active_id) {
        const list = await getTeamRanking(active_id)
        if (!list.length) return uni.showToast({title: '没有数据', icon: 'none'})
        
        const activeDetails = await getActiveDetails(active_id)
        const unit = activeDetails?.conf?.active?.integral?.unit || '积分'
        const showCategory = !!activeDetails?.rank_set?.['team_category']
        
        const tableData = excelDataProcessing(list, unit, showCategory)
        openExcelFile.openDocument(tableData, `队伍${unit}排行榜`)
    }
}