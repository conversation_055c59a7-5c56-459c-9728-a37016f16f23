@import url(@/static/style/iconfont/iconfont.css);
@import url(@/components/u-parse/u-parse.css);

.uni-app--maxwidth {
    max-width: 500px;
    margin: 0 auto;
    position: relative;
}


/*==== flex  ===*/

.flex-center {
    display: flex;
    justify-content: space-around;
    align-items: center;
}


.flex-kai {
    display: flex;
    justify-content: space-between;
}

.flex-all-center {
    display: flex;
    justify-content: center;
    align-items: center;
}


.flex-column {
    display: flex;
    flex-direction: column;
}

.flex-row {
    display: flex;
    flex-direction: row;
}


.flex-wrap {
    flex-wrap: wrap;
}

.fl {
    float: left;
}


.fr {
    float: right;
}


.m5 {
    margin-top: 5px;
}

.m10 {
    margin: 10px;
}

.mt15 {
    margin-top: 15px;
}

.mt20 {
    margin-top: 20px;
}

.mt10 {
    margin-top: 10px;
}

.mt0 {
    margin-top: 0;
}

.mb5 {
    margin-bottom: 5px;
}

.mb10 {
    margin-bottom: 10px;
}

.ml5 {
    margin-left: 5px;
}

.p0 {
    padding: 0;
}

.p15 {
    padding: 15px;
}

.p20 {
    padding: 20px;
}

.p10 {
    padding: 10px;
}

.p5 {
    padding: 5px;
}

.pt5 {
    padding-top: 5px;
}

.pt10 {
    padding-top: 10px;
}

.pt15 {
    padding-top: 15px;
}

.pb5 {
    padding-bottom: 5px;
}

.pb10 {
    padding-bottom: 10px;
}

.pt0 {
    padding-top: 0;
}


.pl5 {
    padding-left: 5px;
}

.pl10 {
    padding-left: 10px;
}

.pr5 {
    padding-right: 5px;

}

.pr10 {
    padding-right: 10px;
}

.plr5 {
    padding-left: 5px;
    padding-right: 5px;
}

.ptm5 {
    padding-top: 5px;
    padding-bottom: 5px;
}


.font-bold {
    font-weight: bold;
}

.italic-text {
    font-style: italic;
}

.clearfix:after {
    content: " ";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

.clear {
    clear: both;
}


.font12 {
    font-size: 12px;
}

.font14 {
    font-size: 14px;
}

.font16 {
    font-size: 16px;
}

.font18 {
    font-size: 18px;
}

.font20 {
    font-size: 20px;
}

.font24 {
    font-size: 24px;
}

.font28 {
    font-size: 28px;
}

.font34 {
    font-size: 34px;
}

.color888 {
    color: #888;
}

.color444 {
    color: #444;
}

.color666 {
    color: #666;
}

.color999 {
    color: #999;
}

.color-white {
    color: #fff;
}


.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-center {
    text-align: center;
}

.bg-white {
    background: #fff;
}

.ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.ellipsis--l2 {
    -webkit-line-clamp: 2;
}

.ellipsis--l2, .ellipsis--l3 {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
}

.ellipsis--l3 {
    -webkit-line-clamp: 3;
}


.wrap {
    word-wrap: break-word;
    word-break: break-all;
}

.no-wrap {
    white-space: nowrap;
}

/*颜色*/

.color-green {
    color: #02b148;
}

.bg-green {
    background-color: #02b148;
}

.color-a {
    color: #72ace3;
}

.color-red {
    color: #e20f04;
}

.bg-red {
    background-color: #e20f04;
}


.color-pink {
    color: #ff86aa;
}


/*主色*/
/*使用较为安全的蓝色作为主色调，其中 Light Primary 常用于 hover，Dark Primary 常用于 active。*/
.color-primary {
    color: #2d8cf0;
}

.bg-primary {
    background-color: #2d8cf0;
}

.color-light-primary {
    color: #5cadff;
}

.bg-light-primary {
    background-color: #5cadff;
}

.color-dark-primary {
    color: #2b85e4;
}

.bg-dark-primary {
    background-color: #2b85e4;
}

/*主色*/


/*辅助色*/
/*辅助色是具有代表性的颜色，常用于信息提示，比如成功、警告和失败*/
.color-info {
    color: #2d8cf0;
}

.bg-info {
    background-color: #2d8cf0;
}

.color-success {
    color: #19be6b;
}

.bg-success {
    background-color: #19be6b;
}

.color-warning {
    color: #ff9900;
}

.bg-warning {
    background-color: #ff9900;
}

.color-error {
    color: #ed3f14;
}

.bg-error {
    background-color: #ed3f14;
}

/*辅助色*/


/*中性色 */
/*中性色常用于文本、背景、边框、阴影等，可以体现出页面的层次结构。*/

/*标题 Title*/
.color-title {
    color: #1c2438;
}

.bg-title {
    background-color: #1c2438;
}

/*正文 Content*/
.color-content {
    color: #495060;
}

.bg-content {
    background-color: #495060;
}

/*辅助/图标 Sub Color*/
.color-sub {
    color: #80848f;
}

.bg-sub {
    background-color: #80848f;
}

/*失效 Disabled*/
.color-disabled {
    color: #bbbec4;
}

.bg-disabled {
    background-color: #bbbec4;
}

/*边框 Border*/
.color-border {
    color: #dddee1;
}

.bg-border {
    background-color: #dddee1;
}

/*分割线 Divider*/
.color-divider {
    color: #e9eaec;
}

.bg-divider {
    background-color: #e9eaec;
}

/*背景 Background*/
.color-background {
    color: #f8f8f8;
}

.bg-background {
    background-color: #f8f8f8;
}

/*中性色 */


.color-ff8e1e {
    color: #ff8e1e;
}


.button-none {
    line-height: 1;
    padding: 0;
    margin: 0;
    font-size: 14px;
    background-color: rgba(0, 0, 0, 0);
    text-align: left;
}

.button-none:after {
    content: none;
}


.no-list {
    padding-top: 10vh;
}

.no-list .iconfont {
    font-size: 80px;
}


.word-last-loading {
    position: relative;
}

.word-last-loading::after {
    position: absolute;
    left: 100%;
    content: "";
    width: 20px;
    animation: word-loading 1.5s infinite;
}

@keyframes word-loading {
    0%, 100% {
        content: ".";
    }
    33% {
        content: "..";
    }
    66% {
        content: "...";
    }
}


.uni-noticebar {
    /*移除<uni-notice-bar>组件底部间距*/
    margin-bottom: 0 !important;
}

.radius10 {
    border-radius: 10px;
}

.w-100 {
    width: 100%;
}