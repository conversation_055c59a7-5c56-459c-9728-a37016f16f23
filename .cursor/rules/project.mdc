---
description: 
globs: 
alwaysApply: true
---
# UniApp Vue2 项目规则

## 项目概述
这是一个基于 UniApp 框架和 Vue2 的跨平台移动应用开发项目。UniApp 是一个使用 Vue.js 开发所有前端应用的框架，开发者编写一套代码，可发布到 iOS、Android、Web（响应式）、以及各种小程序（微信/支付宝/百度/头条/飞书/QQ/快手/钉钉/淘宝）、快应用等多个平台。

## 技术栈
- **框架**: UniApp
- **前端框架**: Vue2
- **样式**: SCSS/CSS
- **路由**: UniApp 内置路由
- **UI组件库**: uni-ui 或其他 UniApp 兼容组件库
- **构建工具**: HBuilderX 或 Vue CLI

## 开发标准
### 代码风格
1. **命名规范**
    - 组件名采用小驼峰命名法，例如 `myComponent.vue`。
    - 变量和函数名采用小驼峰命名法，例如 `myVariable`、`myFunction`。
    - 常量名全部大写，单词间用下划线分隔，例如 `MAX_COUNT`。
    - 模版语法中的 class 类名请使用中横线分割，例如 `class="class-name"`。
2. **缩进**：使用 4 个空格进行缩进。
3. **注释**
    - 组件和模块开头应添加注释，说明其功能和使用方法。
    - 关键代码行和复杂逻辑处应添加注释，解释代码的意图。
4. **字符串**
    - 模板语法中的属性请使用双引号，例如 `class="class-name"`、`v-modal="name"`。
    - js 代码中尽量使用单引号，例如 'string'。
    - 字符串拼接请使用模板字符串，例如 `string${name}string`。

### Vue2 语法
1. **模板语法**
    - 优先使用 `v-bind` 和 `v-on` 的缩写形式，例如 `:prop` 和 `@event`。
    - 避免在模板中使用复杂的表达式，将复杂逻辑移到计算属性或方法中。
2. **计算属性和方法**
    - 计算属性用于处理依赖于数据的逻辑，方法用于处理用户交互和其他操作。
    - 计算属性应具有描述性的名称，且避免在计算属性中进行异步操作。
3. **生命周期钩子**
    - 合理使用生命周期钩子，例如在 `created` 钩子中进行数据初始化，在 `mounted` 钩子中进行 DOM 操作。
    - 避免在生命周期钩子中进行过于复杂的逻辑处理。

## 性能优化
1. **图片优化**
    - 使用合适的图片格式，如 WebP，以减小图片体积。
    - 对图片进行懒加载，避免一次性加载过多图片。
2. **组件优化**
    - 合理使用 `v-if` 和 `v-show`，`v-if` 适用于不经常切换的场景，`v-show` 适用于频繁切换的场景。
    - 对组件进行按需加载，提高应用的初始加载速度。

## 错误处理和日志记录
- 在关键代码处进行错误捕获和处理，避免应用崩溃。
- 记录重要的操作和错误信息，方便后续的调试和排查问题。

## 代码审查清单
- [ ] 代码是否实现了预期功能
- [ ] 是否处理了所有边界情况
- [ ] 错误处理是否完善
- [ ] 是否有内存泄漏风险

## 代码规范

### Vue 组件规范

#### 组件结构顺序

```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
// 脚本内容
</script>

<style lang="scss" scoped>
/* 样式内容 */
</style>
```

#### 组件选项顺序

```javascript
export default {
  name: 'ComponentName',
  components: {},
  mixins: [],
  props: {},
  data() {
    return {}
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {},
  filters: {}
}
```


## UniApp 特定规范

### 页面配置

每个页面都应该有对应的页面配置：

```javascript
// pages.json
{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "首页",
        "enablePullDownRefresh": false
      }
    }
  ]
}
```

### 生命周期使用

```javascript
export default {
  // 页面生命周期
  onLoad(options) {
    // 页面加载时触发
  },
  onShow() {
    // 页面显示时触发
  },
  onReady() {
    // 页面初次渲染完成时触发
  },
  onHide() {
    // 页面隐藏时触发
  },
  onUnload() {
    // 页面卸载时触发
  },
  
  // 下拉刷新
  onPullDownRefresh() {
    // 处理下拉刷新
    setTimeout(() => {
      uni.stopPullDownRefresh();
    }, 1000);
  },
  
  // 上拉加载
  onReachBottom() {
    // 处理上拉加载更多
  }
}
```

### 条件编译

使用条件编译适配不同平台：

```javascript
// #ifdef APP-PLUS
// App 平台特有代码
// #endif

// #ifdef H5
// H5 平台特有代码
// #endif

// #ifdef MP-WEIXIN
// 微信小程序特有代码
// #endif

// #ifndef H5
// 除了 H5 平台的代码
// #endif
```

### API 调用规范

```javascript
// 网络请求
uni.request({
  url: 'https://api.example.com/data',
  method: 'GET',
  data: {},
  success: (res) => {
    console.log(res.data);
  },
  fail: (err) => {
    console.error(err);
  }
});

// 页面跳转
uni.navigateTo({
  url: '/pages/detail/detail?id=123'
});

// 显示提示
uni.showToast({
  title: '操作成功',
  icon: 'success'
});
```

## 组件开发规范

### 组件设计原则

1. **单一职责**: 每个组件只负责一个功能
2. **可复用性**: 组件应该是可复用的
3. **可配置性**: 通过 props 提供配置选项
4. **事件通信**: 使用事件进行父子组件通信

### 组件模板

```vue
<template>
  <view class="component-name">
    <view class="component-name__header" v-if="showHeader">
      <text class="component-name__title">{{ title }}</text>
    </view>
    <view class="component-name__content">
      <slot></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ComponentName',
  props: {
    title: {
      type: String,
      default: ''
    },
    showHeader: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // 组件内部状态
    };
  },
  methods: {
    handleClick() {
      this.$emit('click', {
        // 事件数据
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.component-name {
  &__header {
    // 头部样式
  }
  
  &__title {
    // 标题样式
  }
  
  &__content {
    // 内容样式
  }
}
</style>
```

### Props 规范

```javascript
props: {
  // 基础类型
  title: String,
  count: Number,
  isVisible: Boolean,
  
  // 带默认值和验证
  size: {
    type: String,
    default: 'medium',
    validator(value) {
      return ['small', 'medium', 'large'].includes(value);
    }
  },
  
  // 对象类型
  user: {
    type: Object,
    default() {
      return {};
    }
  },
  
  // 数组类型
  items: {
    type: Array,
    default() {
      return [];
    }
  }
}
```

## 样式规范

### CSS 类命名

使用 BEM 命名规范：

```scss
// Block
.user-card {
  // Element
  &__avatar {
    // Modifier
    &--large {
      
    }
  }
  
  &__name {
    
  }
  
  &__info {
    
  }
}
```

### 响应式设计

```scss
// 使用 UniApp 的 rpx 单位
.container {
  width: 750rpx;
  padding: 20rpx;
}

// 媒体查询
@media screen and (min-width: 768px) {
  .container {
    max-width: 1200px;
    margin: 0 auto;
  }
}
```


## 性能优化规范

### 图片优化

```javascript
// 图片懒加载
<image 
  :src="imageSrc" 
  lazy-load 
  mode="aspectFill"
  @load="onImageLoad"
  @error="onImageError"
/>

// 图片压缩
const compressImage = (src, quality = 0.8) => {
  return new Promise((resolve) => {
    uni.compressImage({
      src,
      quality,
      success: (res) => {
        resolve(res.tempFilePath)
      }
    })
  })
}
```

### 列表优化

```vue
<template>
  <scroll-view 
    scroll-y 
    @scrolltolower="loadMore"
    :refresher-enabled="true"
    @refresherrefresh="onRefresh"
  >
    <view 
      v-for="item in list" 
      :key="item.id"
      class="list-item"
    >
      <!-- 列表项内容 -->
    </view>
  </scroll-view>
</template>

<script>
export default {
  data() {
    return {
      list: [],
      page: 1,
      pageSize: 20,
      loading: false,
      hasMore: true
    }
  },
  methods: {
    async loadMore() {
      if (this.loading || !this.hasMore) return
      
      this.loading = true
      try {
        const res = await api.getList({
          page: this.page,
          pageSize: this.pageSize
        })
        
        this.list.push(...res.data)
        this.page++
        this.hasMore = res.data.length === this.pageSize
      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>
```

## 代码审查清单

### 功能性检查

- [ ] 代码是否实现了预期功能
- [ ] 是否处理了所有边界情况
- [ ] 错误处理是否完善
- [ ] 是否有内存泄漏风险

### 代码质量检查

- [ ] 代码是否遵循项目规范
- [ ] 变量和函数命名是否清晰
- [ ] 是否有重复代码
- [ ] 注释是否充分且准确

### 性能检查

- [ ] 是否有不必要的重复渲染
- [ ] 图片是否进行了优化
- [ ] 是否使用了合适的数据结构
- [ ] 网络请求是否进行了优化

### 安全性检查

- [ ] 用户输入是否进行了验证
- [ ] 敏感信息是否进行了保护
- [ ] API 调用是否安全
- [ ] 是否有 XSS 风险

## 最佳实践建议

### 开发效率

1. **使用代码片段**: 创建常用代码的 snippet
2. **组件库**: 建立项目专用的组件库
3. **工具函数**: 封装常用的工具函数
4. **自动化**: 使用脚本自动化重复任务

### 代码质量

1. **代码审查**: 建立代码审查流程
2. **单元测试**: 为关键功能编写测试
3. **文档**: 维护完善的项目文档
4. **重构**: 定期重构优化代码

### 性能优化

1. **懒加载**: 对图片和组件进行懒加载
2. **缓存策略**: 合理使用缓存
3. **代码分割**: 按需加载代码
4. **资源优化**: 压缩图片和代码

## 常见问题解决

### 跨平台兼容性

```javascript
// 平台判断
const platform = uni.getSystemInfoSync().platform

if (platform === 'ios') {
  // iOS 特有处理
} else if (platform === 'android') {
  // Android 特有处理
}

// 条件编译
// #if
(Content truncated due to size limit. Use line ranges to read in chunks)