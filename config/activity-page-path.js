export default {
    /**
    *  获取活动页面路径
    * @param {Number} activity_types 活动类型
    * @param {String} path_type 路径类型  add: 添加活动  manage: 活动管理  details: 活动详情
    * */
    getActivityPath(activity_types, path_type) {
        const defaultPath = {  // 默认健步走
            add: '/pages/activity/admin/add_or_edit',
            manage: '/pages/activity/admin/manage',
            details: '/pages/activity/user/detail'
        }

        const path_options = {
            2: defaultPath, // 健步走
            4: {  // 朗诵
                add: '/pages/voice/admin/activity/add',
                manage: '/pages/voice/admin/activity/manage',
                details: '/pages/voice/user/detail'
            },
            5: {  // 在线打卡
                add: '/pages/clock_in/admin/activity/add',
                manage: '/pages/clock_in/admin/activity/manage',
                details: '/pages/clock_in/user/detail'
            },
            6: {  // 运动打卡
                add: '/pages/sport_clock_in/admin/activity/add',
                manage: '/pages/sport_clock_in/admin/activity/manage',
                details: '/pages/sport_clock_in/user/detail'
            },
            7: {  // 投票
                add: '/pages/vote/admin/add',
                manage: '/pages/vote/admin/manage',
                details: '/pages/vote/user/detail'
            },
            8: {  // 集字
                add: '/pages/words/admin/add',
                manage: '/pages/words/admin/manage',
                details: '/pages/words/user/detail'
            },
            9: {  // AI运动
                add: '/pages/ai_sport/pages/admin/add',
                manage: '/pages/ai_sport/pages/admin/manage',
                details: '/pages/ai_sport/pages/user/detail'
            },
            12: {  // 运动轨迹
                add: '/pages/running/admin/add',
                manage: '/pages/running/admin/manage',
                details: '/pages/running/user/detail'
            },
            13: {  // 云祝福
                add: '/pages/cloud_wish/admin/add',
                manage: '/pages/cloud_wish/admin/manage',
                details: '/pages/cloud_wish/user/detail'
            },
            15: {  // 猜歌
                add: '/pages/song/admin/activity/add',
                manage: '/pages/song/admin/activity/manage',
                details: '/pages/song/user/detail'
            },
            16: {  // 朗诵红包
                add: '/pages/voice-red-pack/admin/add',
                manage: '/pages/voice-red-pack/admin/manage',
                details: '/pages/voice-red-pack/user/detail'
            },
            17: {  // 报名活动
                add: '/pages/registration/admin/add',
                manage: '/pages/registration/admin/manage',
                details: '/pages/registration/user/detail'
            },
            18: {  // 党建猜图答题
                add: '/pages/guess-picture/admin/add',
                manage: '/pages/guess-picture/admin/manage',
                details: '/pages/guess-picture/user/detail'
            },
            20: {  // 党建寻宝
                add: '/pages/treasure-hunt/admin/add',
                manage: '/pages/treasure-hunt/admin/manage',
                details: '/pages/treasure-hunt/user/detail'
            },
            21: {   // 集卡活动
                add: '/pages/card-collecting/admin/add',
                manage: '/pages/clock_in/admin/activity/manage',
                details: '/pages/clock_in/user/detail'
            },
            22: { // 拼起红色记忆碎片活动
                add: '/pages/text-stitching/admin/add',
                manage: '/pages/text-stitching/admin/manage',
                details: '/pages/text-stitching/user/detail'
            },
            24: { // 赛事比分
                add: '/pages/match-score/admin/add',
                manage: '/pages/match-score/admin/manage',
                details: '/pages/match-score/user/details'
            },
            25: { // 任务闯关
                add: '/pages/task-challenge/admin/add',
                manage: '/pages/task-challenge/admin/manage',
                details: '/pages/task-challenge/user/details'
            },
            29: { // 长征游戏
                add: '/pages/long-march-game/admin/add',
                manage: '/pages/long-march-game/admin/manage',
                details: '/pages/long-march-game/user/details'
            },
            30: { // 体重身高
                add: '/pages/weightHeight/admin/add',
                manage: '/pages/weightHeight/admin/manage',
                details: '/pages/weightHeight/user/details'
            },
            31: { // 报名信息收集-财务代理记账申报 可重复申报 不判断是否重复提交
                add: '/pages/form-submit/admin/add',
                manage: '/pages/form-submit/admin/manage',
                details: '/pages/form-submit/user/details'
            },
            33: { // 点亮中国地图
                add: '/pages/light-up-map/admin/add',
                manage: '/pages/light-up-map/admin/manage',
                details: '/pages/light-up-map/user/details'
            }
        }

        return path_options[activity_types]?.[path_type] || defaultPath[path_type] || ''
    }
}
