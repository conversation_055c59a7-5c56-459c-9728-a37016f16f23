import activityPagePath from "./activity-page-path"
export default {
    ...activityPagePath,
    treasure_hunt_public_id: 6506,  // 寻宝活动寻宝类型公共题库分类id
    picture_diff_public_id: 15242,  // 寻宝活动找茬类型公共题库分类id
    text_stitching_public_id: 6632, // 拼记忆碎片动公共题库分类id
    fill_words_public_id: -1,       // 填词任务公共题库分类id
    object_storage_url: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/',
    active_default_logo: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/shop/6/6/2022/08/04/62eba1fdd4d9c_4692.jpg',  // 活动默认的logo图片，用于活动列表显示没有logo的活动
    create_active_default_logo: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/public_image/main_figure/jbz.jpg',  // 创建活动默认的logo图片
    
    // 长征地图未兑换提示窗默认提示内容
    long_march_no_exchange_default_tips_set: {
        text: '    同志们，战友们！我们即将携手踏上一段意义非凡的旅程——云上重走长征路。\n    让我们心怀敬仰，步履坚定地追随先辈的足迹，重温那一段波澜壮阔、艰苦卓绝的革命岁月。在这段征程中，我们将耳闻目睹长征途中那些感人至深的传奇故事，深刻领悟伟大长征精神的丰富内涵，让红色血脉在我们心中流淌不息，让初心在我们行动中熠熠生辉。\n    长征的号角已经吹响，让我们携手并进，共同启程！长征，出发！',
        text_set: {
            color: 'rgba(235, 127, 44, 1)'  
        },
        background: {
            image: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/cz_pop.jpg',
            color: 'rgba(255, 255, 255, 1)'
        },
        button: {
            text: '出发',
            text_color: 'rgba(255, 255, 255, 1)',
            bg_color: 'rgba(255, 153, 0, 1)'
        }
    },

    // 判断活动是否开启出差公里数兑换
    openBusinessKilometers(id) {
        if (!id) return false
        const activity_id_list = [
            '24e6c2bd9b8c300885fde3b8a79213db', // 我自己的活动
            'b38ae240920b664dc9926ad68981b505', // 我自己的活动
            'a1ba5c597229936f0d701104d7f4a07d', // 雷子的活动
            '32ea0f8906e5033a5cb80687a94881e6', // 雷子的客户的
        ]
        return activity_id_list.includes(id)
    },

    // 判断用户兑换记录是否需要显示团队名称
    exchangeRecordsShowTeamName(id) {
        if (!id) return false
        const activity_id_list = [
            // '24e6c2bd9b8c300885fde3b8a79213db', // 我自己的活动
            '32ea0f8906e5033a5cb80687a94881e6', // 雷子的航空里程兑换的客户的
        ]
        return activity_id_list.includes(id)
    },

    // 判断个人中心是否进入了纯净版活动也显示活动模块，在下面添加了userid就可以显示
    userCenterActivityModuleShow() {
        const user_id_list = [
            132, // 李狗蛋
            162, // 雷子
            164, // 晓阳
            171, // 鲜繁
            
            1420929, 1190320, 147908,  // 这三个是晓阳让我加的
        ]
        const userid = getApp().globalData['userid']
        return user_id_list.includes(userid)
    },


    // 健步走活动tab栏联动
    activityTabBarInterconnect(id) {
        const ids = [
            // '24e6c2bd9b8c300885fde3b8a79213db', // 我自己的活动
            'a1ba5c597229936f0d701104d7f4a07d', // 雷子的活动
            '32ea0f8906e5033a5cb80687a94881e6', // 雷子的航空里程兑换的客户的
        ]
        return ids.includes(id)
    },

    // 检查当前页面是否允许复制短链
    isAllowCopyUrl() {
        const pages = getCurrentPages()
        const currentPage = pages[pages.length - 1]
        const { route } = currentPage

        // 允许复制短链的页面
        // 如果给成配置不能复制短链的页面地址，那么会出现如果有的页面忘了配置，那么所有用户都能通过短链进入
        const allowCopyUrlPages = [
            'pages/index/index',                  // 小程序启动页
            'pages/diy-page/diy-index',           // 小程序首页
            'pages/activity/user/detail',         // 健步走活动详情
            'pages/voice/user/detail',            // 朗诵活动详情
            'pages/clock_in/user/detail',         // 在线打卡活动详情、集卡活动详情
            'pages/sport_clock_in/user/detail',   // 运动打卡活动详情
            'pages/vote/user/detail',             // 投票活动详情
            'pages/words/user/detail',            // 集字活动详情
            'pages/ai_sport/user/detail',         // AI运动活动详情
            'pages/running/user/detail',          // 运动轨迹活动详情
            'pages/cloud_wish/user/detail',       // 云祝福活动详情
            'pages/song/user/detail',             // 猜歌活动详情
            'pages/voice-red-pack/user/detail',   // 朗诵红包活动详情
            'pages/registration/user/detail',     // 报名活动详情
            'pages/guess-picture/user/detail',    // 猜图活动详情
            'pages/treasure-hunt/user/detail',    // 寻宝活动详情
            'pages/task-challenge/user/details',  // 任务闯关活动
        ]

        return allowCopyUrlPages.includes(route)
    },


    // 活动用户上传单张图片大小限制
    activeUserUploadImageSizeLimit(id) {
        const default_size = 1
        if (!id) return default_size
        const set = {
            '5301a298e445c6f771959289fbc548fa': 10,  // 晓阳客户的活动，限制10M，晓阳说1个月后活动关闭吧图片删了
            '24e6c2bd9b8c300885fde3b8a79213db': 10,  // 我自己的活动，限制10M
            '5209727001eb3864e2c9b9607e33681e': 2,   // 鲜繁客户的活动
            '5a56536807e0970200f7954a6a0e11cd': 5,   // 雷子客户的打卡活动
        }
        return set[id] || default_size
    },

    // 地图页面是否显示兑换成功弹窗的关闭icon图标
    mapPageExchangeSuccessPopupCloseIconShow(id) {
        if (!id) return true
        const close_id_list = [
            '4754faf00bb9771058b5ef08d08a205e'
        ]
        return !close_id_list.includes(id)
    },

    // 活动管理是否显示活动规则文档
    showActivitySpecificationDocument (types) {
        if (!types) return false
        const show_types_list = [
            2, // 健步走
        ]
        return show_types_list.includes(types)
    },

    daily_step_goal: 10000, // 今日步数目标
    
    AiSportNeedCountsTypes(types) {
        const needCountTypes = [1, 2, 3, 4, 6, 7, 8, 9]
        return needCountTypes.includes(types)
    },
    
    // 兑换日历不显示兑换步数统计分布
    exchangeCalendarNotShowDistributionStatistics(id) {
        const ids = [
            '8d216b3a33667da7550b9e3babf6e341',  // 晓阳客户的活动
        ]
        return ids.includes(id)
    },
    
    
    teamSloganOpen(id) {
        // 用户进入活动，选择自己所在队伍提交保存成功后，弹窗提示当前是属于哪个队伍以及显示出本队伍的口号
        const ids = [
            '60340917365fbfab6cad8b21ddd1257f',  // 我的活动 (用来测试)
            '79e0836e3dad59acaa76130fd08f57ab',  // 雷子客户 (第一个)
        ]
        return ids.includes(id)
    },
    
    reachTheEndPopupBackgroundImage(id) {
        const _default = {
            img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/end/end.png',
            class_name: 'finished-main'
        }
        
        const _config = {
            '79e0836e3dad59acaa76130fd08f57ab': {
                img: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/longmarch/xsyjl/mapwanhua/end1.png',
                class_name: 'finished-main-2'
            }
        }
        
        return _config[id] || _default
    },
    
    reachTheEndPopupDataShow(id) {
        const _config = {
            headimg: true,
            nickname: true,
            finished_num: true,
            kilo: true,
            signdays: true,
            finished_time: true
        }
        
        const active_configs = {
            '79e0836e3dad59acaa76130fd08f57ab': {
                headimg: false,
                nickname: false,
                signdays: false,
                finished_time: false,
            }
        }
        
        if (active_configs[id]) {
            const active_config = active_configs[id]
            Object.keys(active_config).forEach(key => {
                _config[key] = active_config[key]
            })
        }
        
        return _config
    },
    
    examResultScoreText(id, score, success_score) {
        const _config = {
            '79e0836e3dad59acaa76130fd08f57ab': {
                success_text: '通关',
                fail_text: '通关失败'
            }
        }
        
        if (_config[id]) {
            const a_config = _config[id]
            const success = Number(score) >= Number(success_score)
            return success ? a_config.success_text : a_config.fail_text
        }
        
        return false
    },
    
    answerResultBackButtonText(id) {
        const _default = '返回活动'
        const _config = {
            '79e0836e3dad59acaa76130fd08f57ab': '返回兑换步数'
        }
        return _config[id] || _default
    },
    
    defaultMustSubmit: [{name: 'xing_ming', title: '姓名', rules: 1, types: 1}]
}
