---
description: 
globs:
alwaysApply: true
---
# project-rule

本文件约束本项目的工程规范、目录职责、开发/构建流程、代码风格与最佳实践，帮助成员与 AI 在统一约定下高效协作。

## 1. 项目概览与技术栈

- 框架与平台
    - 基于 uni-app，当前主运行时为 Vue 2（main.js 使用了 #ifndef VUE3/#ifdef VUE3 条件编译）。
    - 目标平台包含微信小程序（mp-weixin）与 H5。
- 依赖与工具
    - vue@2.6.x
    - fetch-wechat（小程序端 fetch 风格请求）
    - pinyin-pro（拼音工具）
- 全局注入（main.js）
    - this.xwy_api —— 统一接口封装（utils/api/xwy_api.js）
    - this._utils —— 工具函数集合（utils/utils.js）
    - this.$uni —— uni API 二次封装（utils/uni-api.js）
    - this.$wx —— 微信相关 API 封装（utils/wx-api.js）
    - this.xwy_config —— 全局配置与业务开关（config/config.js）
    - this.$login —— 登录逻辑封装（utils/api/login.js）
    - 利口答题库：this.xwyLib、this.base64、this.imgImport

说明：所有页面/组件优先通过注入对象调用对应能力，避免直接散落调用底层 API，保证可替换性与一致性。

## 2. 目录结构与职责

- components/ 自定义业务组件（每个组件自带独立样式与注释，名称使用短横线命名，如 ranking-search）
- pages/ 业务页面（每个功能独立子目录，页面路由源于 pages.json）
- config/ 运行时/编译期配置（如接口域名、功能开关，详见第 8 节）
- utils/ 工具与 API
    - utils/api/ 接口封装与鉴权（xwy_api.js、login.js 等）
    - utils/uni-api.js 封装 uni.* 能力（兼容、节流、错误统一处理）
    - utils/wx-api.js 微信相关封装
- uni_modules/ 通用第三方组件库（如 uni-*、qiun-data-charts）
- static/ 静态资源（图片、字体等）
- uni.scss 全局样式/变量
- App.vue 应用根组件
- main.js 入口与全局注入
- pages.json 路由/页面/分包/导航配置
- manifest.json 应用配置（小程序 appid、图标、权限等）
- webpack.config.js 仅供 WebStorm 识别别名（见第 3 节）

约束：新增通用能力进 utils，新增业务页面进 pages 对应域，新增可复用 UI 进 components。

## 3. 路径别名与 IDE 配置

- webpack.config.js 仅用于 WebStorm 识别别名，不参与实际构建。
- 当前配置为：
    - 别名 '@' 指向项目根目录。
- 约定与用法：
    - 代码中如需使用别名引用，形如：
      ```javascript
      // 中文注释：使用别名 @ 指向项目根目录
      import MyComp from '@/components/top-banner/top-banner.vue'
      ```
    - 修改此文件仅影响 IDE 跳转/补全，不会影响打包产物。实际构建由 uni-app 工具链处理。

## 4. 运行与构建

- 推荐 npm scripts（如 package.json 中缺失请补充至 scripts）
```json
{
    "scripts": {
        "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch --minimize",
        "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build --minimize",
        "dev:h5": "cross-env UNI_PLATFORM=h5 vue-cli-service uni-serve",
        "build:h5": "cross-env UNI_PLATFORM=h5 vue-cli-service uni-build"
    }
}
```
- 小程序调试：使用 HBuilderX 或微信开发者工具导入 /dist/build/mp-weixin
- H5 调试：npm run dev:h5 后浏览器访问
- 注意：避免直接修改 dist 产物；环境差异通过 config/config.js 和条件编译控制。

## 5. 代码风格与规范

- 统一约定
    - 缩进：4 个空格；编码：UTF-8（无 BOM）
    - 文件/组件命名：kebab-case；组件内 name 用 PascalCase
    - 注释：中文，解释意图优先，关键逻辑与接口需标注入参/返回
- Vue 书写规范（Vue2）
    - data 返回对象函数；props 标注类型与默认值；computed 纯函数；watch 做防抖/节流
    - 生命周期勿用箭头函数；避免在 created 中做重 IO，优先 onLoad/onShow 分流
- 引入顺序：第三方依赖 -> 项目工具与配置 -> 本地组件
- 错误处理：所有异步请求 try/catch；业务错误统一提示（this.$uni.toast 或弹窗组件）

## 6. Vue2/3 条件编译与全局组件注册

- main.js 使用了条件编译：
    - Vue2 分支：#ifndef VUE3
    - Vue3 分支：#ifdef VUE3
- 全局组件注册注意：
    - Vue2 请使用 Vue.component 并放在 Vue2 分支内或加条件编译保护。
    - Vue3 请在 createApp 内使用 app.component。
    - 示例：
      ```javascript
      // #ifndef VUE3
      import Vue from 'vue'
      import pickerTime from '@/components/picker-time.vue'
      Vue.component('picker-time', pickerTime)
      // #endif

      // #ifdef VUE3
      import { createSSRApp } from 'vue'
      import pickerTime from '@/components/picker-time.vue'
      export function createApp() {
          const app = createSSRApp(App)
          app.component('picker-time', pickerTime)
          return { app }
      }
      // #endif
      ```
    - 说明：避免在 Vue3 构建下直接调用 Vue.component 导致未定义错误。

## 7. 组件规范（components/）

- 组件文件结构
    - 仅暴露必要 props / events；内部 state 最小化
    - props 指定 type / default / required；事件命名遵循 uni-app 习惯（@confirm/@change）
- 可复用性：UI 组件不依赖页面 this；与后端交互通过外部注入回调或数据
- 文档化：文件头写用法示例与 props 说明（中文）

## 8. 全局配置与业务开关（config/config.js）

- 静态配置（示例）
    - object_storage_url：对象存储基础地址（当前为腾讯云云开发 TCB 域名）
    - active_default_logo / create_active_default_logo：默认活动 Logo
    - long_march_no_exchange_default_tips_set：长征地图未兑换提示窗默认配置（含文本/样式/按钮）
    - daily_step_goal：今日步数目标（默认 10000）
    - defaultMustSubmit：默认必填项配置数组（如姓名字段）
- 行为/开关函数（仅列核心，实际以文件为准）
    - openBusinessKilometers(id)：是否开启出差公里数兑换（按活动 id 白名单）
    - exchangeRecordsShowTeamName(id)：兑换记录是否显示团队名称（按活动 id 白名单）
    - userCenterActivityModuleShow()：个人中心是否显示活动模块（按用户 id 白名单，依赖 getApp().globalData.userid）
    - activityTabBarInterconnect(id)：健步走活动 tab 栏联动（按活动 id 白名单）
    - isAllowCopyUrl()：当前页面是否允许复制短链（按 route 白名单，使用 getCurrentPages）
    - activeUserUploadImageSizeLimit(id)：用户单图上传大小限制（默认 1M，部分活动提升）
    - mapPageExchangeSuccessPopupCloseIconShow(id)：兑换成功弹窗是否显示关闭图标
    - showActivitySpecificationDocument(types)：活动管理是否显示活动规则文档（按活动类型，如 2=健步走）
    - AiSportNeedCountsTypes(types)：AI 运动需要计数的活动类型集合
    - exchangeCalendarNotShowDistributionStatistics(id)：兑换日历是否隐藏分布统计
    - teamSloganOpen(id)：进入活动选择队伍后弹出队伍口号（按活动 id 白名单）
    - reachTheEndPopupBackgroundImage(id)：到达终点弹窗背景图与样式类名（按活动 id 覆盖）
    - reachTheEndPopupDataShow(id)：到达终点弹窗数据项显示控制（默认全开，按活动 id 关闭部分）
    - examResultScoreText(id, score, success_score)：考试结果文案（按活动 id 自定义通过/失败文案）
    - answerResultBackButtonText(id)：答题结果返回按钮文案（按活动 id 自定义）
- 统一使用规范
    - 页面/组件不要散落硬编码 id 或类型判断，统一调用 xwy_config 中的方法或字段。
    - 需要新增开关时，优先在 config/config.js 增加函数或配置映射，并补充中文注释。
    - 涉及路由白名单（如 isAllowCopyUrl）的页面新增/变更，必须同步维护该白名单。
- 使用示例
    ```javascript
    // 中文注释：限制上传大小并提示
    const limitMB = this.xwy_config.activeUserUploadImageSizeLimit(this.activity_id) // 返回 MB
    if (file.size / (1024 * 1024) > limitMB) {
        this.$uni.toast(`单张图片不能超过 ${limitMB}M`)
        return
    }

    // 中文注释：到达终点弹窗样式与数据控制
    const bgConf = this.xwy_config.reachTheEndPopupBackgroundImage(this.activity_id)
    const dataShow = this.xwy_config.reachTheEndPopupDataShow(this.activity_id)
    // 使用 bgConf.img / bgConf.class_name 与 dataShow.xxx 渲染
    ```

## 9. 平台 API 使用注意（getApp / getCurrentPages）

- config/config.js 内部使用了 getApp() 与 getCurrentPages()：
    - 这些 API 在小程序端可用；在 H5 端行为不同或不可用。
- 约束与建议：
    - 仅在小程序相关逻辑路径调用此类函数；或使用条件编译保护：
      ```javascript
      // #ifdef MP-WEIXIN
      const canCopy = this.xwy_config.isAllowCopyUrl()
      // #endif
      ```
    - 若需跨端复用，可在 this.$uni 中封装安全访问方法（如安全获取当前路由），H5 端提供降级实现，避免直接访问原生 API。

## 10. 网络与接口调用

- 首选 this.xwy_api（utils/api/xwy_api.js）
    - 统一 baseURL、header（token/tenant/appid）、错误码处理、超时/重试
    - 所有接口在 xwy_api 内定义方法导出，页面仅调用方法
- fetch-wechat 使用说明
    - 小程序端若需 fetch 风格，统一在封装内引入；页面层不直接使用 fetch-wechat
- 响应约定
    - 建议使用 { code, msg, data }，code===0 视为成功；异常统一转换为用户可理解提示

示例：
```javascript
/**
 * 中文注释：获取用户详情
 * @param {Object} payload { userId: string }
 */
export async function getUserDetail(payload) {
    return await request('/user/detail', { method: 'GET', data: payload });
}
```

## 11. 跨端与条件编译

- 平台差异 API
    - 小程序：订阅消息、NFC、文件系统等 —— 放入 this.$wx 封装
    - H5：DOM/浏览器特性 —— 仅在 #ifdef H5 范围使用
- 资源与路径
    - 使用相对路径或 '@' 别名；避免硬编码绝对路径
- 组件兼容
    - 优先使用 uni_modules 官方组件，减少平台差异

## 12. 样式与资源

- 统一样式
    - 全局变量/混入于 uni.scss；组件内尽量局部样式，避免全局污染
    - 单位：小程序端 rpx 优先；H5 可结合 vw，但保持一致性
- 图片与图标
    - static/ 存放静态资源；尽量使用压缩图；必要时使用 webp（H5）
- 文本解析
    - 使用 components/u-parse 进行富文本解析，注意样式隔离与 XSS 过滤

## 13. 存储与权限

- 存储
    - 使用 this.$uni.storage 封装，key 统一前缀（如 xwy_），设置过期策略
- 权限
    - 登录态、用户信息统一通过 this.$login 管理；需要授权的页面在 onLoad 先校验
- 安全
    - 禁止在前端硬编码密钥、内部域名；涉及鉴权签名由后端/云函数完成

## 14. 性能与可维护性

- 列表与图片
    - 分页/懒加载；图片 lazy-load；避免大图同步解码
- 计算与渲染
    - 复杂计算放 computed，频繁触发事件做节流/防把（utils 提供）
- 首屏优化
    - 减少 onLoad 并行请求；非关键请求延后到 onShow/交互后
- 包体控制
    - 按需引入 uni_modules；公共模块抽离，合理分包

## 15. 提交与分支

- Git 分支
    - main：稳定发布分支
    - develop：日常开发分支
    - feature/*：功能分支
    - hotfix/*：紧急修复
- 提交信息
    - 格式：type(scope): message
    - 示例：feat(activity): 新增排行榜搜索组件与接口绑定

## 16. 常见坑与约束

- package.json 脚本
    - 构建命令必须位于 scripts 下，否则无法被 npm 识别
- 生命周期差异
    - 小程序 onLoad/onShow 与 H5 路由钩子行为不同，避免将平台相关逻辑混写
- 条件编译
    - #ifdef/#ifndef 分支中不要引用另一分支专属模块，防止构建失败
- 组件通信
    - 避免深层级 $parent/$children；改用 props/emit 或全局总线封装
- 全局组件注册
    - 严格区分 Vue2 的 Vue.component 与 Vue3 的 app.component，使用条件编译包裹

## 17. 模板片段

- 标准请求模板
```javascript
// 中文注释：标准请求模板（含错误提示与加载态）
async function fetchDataSafely(params) {
    this.loading = true;
    try {
        const { data } = await this.xwy_api.xxx(params);
        this.list = data || [];
    } catch (e) {
        this.$uni.toast(e.message || '网络异常，请稍后重试');
    } finally {
        this.loading = false;
    }
}
```

- 路由跳转模板
```javascript
// 中文注释：统一通过封装跳转，自动带上必要公共参数
this.$uni.navTo('/pages/activity/user/detail', { id: userId });
```

- 条件编译模板
```javascript
// #ifdef MP-WEIXIN
// 小程序专属逻辑（中文注释：如订阅消息）
await this.$wx.requestSubscribeMessage({ tmplIds: [...] });
// #endif

// #ifdef H5
// H5 专属逻辑（中文注释：如分享 API）
await this._utils.shareToBrowser();
// #endif
```

## 18. 变更流程

1. 评审：新增页面/组件/接口需在 PR 中列出变更点与影响范围
2. 文档：组件/页面顶部注释用途、入参、事件
3. 验证：本地跑通 mp-weixin 与 H5 至关键路径
4. 上线：按标准提审（小程序）或部署（H5）

## 19. 附录：全局注入对象一览（来源 main.js）

- this.xwy_api：接口调用封装，统一 header/错误处理/超时
- this._utils：通用工具（节流防抖、格式化、校验等）
- this.$uni：uni 能力二次封装（路由、toast、storage、request 等）
- this.$wx：微信专属能力封装（订阅、支付、NFC 等）
- this.xwy_config：全局配置与业务开关（对象存储、默认图、活动功能白名单、日目标等）
- this.$login：登录流程封装（确保鉴权）
- this.xwyLib / this.base64 / this.imgImport：利口答题相关公共库

遵循以上规范进行开发。新增全局能力时，请先在 utils 内封装并通过 main.js 注入，再在本规则中登记用途与约束；新增/变更业务开关请集中维护于 config/config.js 并补充注释。